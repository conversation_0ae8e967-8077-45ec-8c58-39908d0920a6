/* Admin Search and Filter Styles */

/* Search Container */
.search-container {
  position: relative;
  min-width: 250px;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: 10px 15px;
  padding-right: 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  color: var(--text-dark);
  background-color: var(--bg-white);
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(94, 53, 177, 0.2);
  outline: none;
}

.search-button {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  height: 30px;
  width: 30px;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--primary);
  font-size: 1.1rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.search-button:hover {
  background-color: rgba(94, 53, 177, 0.1);
}

/* Filter Dropdowns */
.status-filter,
.gender-filter,
.verified-filter,
.premium-filter {
  padding: 10px 15px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  color: var(--text-dark);
  background-color: var(--bg-white);
  cursor: pointer;
  min-width: 140px;
  transition: border-color 0.2s, box-shadow 0.2s;
  appearance: auto; /* Show dropdown arrow */
}

.status-filter:focus,
.gender-filter:focus,
.verified-filter:focus,
.premium-filter:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(94, 53, 177, 0.2);
  outline: none;
}

/* Action Buttons */
.refresh-button,
.export-button {
  padding: 10px 15px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.refresh-button {
  background-color: var(--secondary);
  color: white;
}

.refresh-button:hover {
  background-color: var(--secondary-dark);
}

.export-button {
  background-color: var(--success);
  color: white;
}

.export-button:hover {
  background-color: #388e3c;
}

/* Bulk Actions */
.bulk-actions {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  width: 100%;
  position: relative;
}

.bulk-action-controls {
  display: flex;
  gap: 10px;
}

.bulk-action-select {
  padding: 10px 15px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  color: var(--text-dark);
  background-color: var(--bg-white);
  min-width: 180px;
  cursor: pointer;
  transition: border-color 0.2s, box-shadow 0.2s;
  appearance: auto; /* Show dropdown arrow */
}

.bulk-action-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(94, 53, 177, 0.2);
  outline: none;
}

.bulk-apply-btn {
  padding: 10px 15px;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: background-color 0.2s;
}

.bulk-apply-btn:hover:not(:disabled) {
  background-color: var(--primary-dark);
}

.bulk-apply-btn:disabled {
  background-color: #b39ddb;
  cursor: not-allowed;
}

.selected-count {
  font-size: 14px;
  color: var(--primary);
  background-color: #f5f5f5;
  padding: 5px 10px;
  border-radius: 4px;
  white-space: nowrap;
  font-weight: 500;
}

/* Dark Mode Styles */
body.dark-mode .search-input,
body.dark-mode .status-filter,
body.dark-mode .gender-filter,
body.dark-mode .verified-filter,
body.dark-mode .premium-filter,
body.dark-mode .bulk-action-select {
  background-color: #2d3748;
  color: var(--text-dark);
  border-color: #4a5568;
}

body.dark-mode .selected-count {
  background-color: #2d3748;
  color: var(--primary-light);
}

/* Header Actions */
.header-actions {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    width: 100%;
  }
}
