import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { nanoid } from 'nanoid';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  // Check if user is authenticated
  const session = await getServerSession(req, res, authOptions);
  
  if (!session || !session.user) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  const userId = session.user.id;

  // Only allow GET method
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Get active referral program
    const activeProgram = await prisma.referralProgram.findFirst({
      where: {
        status: 'active',
        startDate: {
          lte: new Date()
        },
        OR: [
          { endDate: null },
          { endDate: { gte: new Date() } }
        ]
      },
      orderBy: {
        startDate: 'desc'
      }
    });

    if (!activeProgram) {
      return res.status(404).json({ 
        success: false, 
        message: 'No active referral program found' 
      });
    }

    // Check if user already has a referral code for this program
    let referral = await prisma.referral.findFirst({
      where: {
        referrerId: userId,
        referralProgramId: activeProgram.id
      }
    });

    // If user doesn't have a referral code, generate one
    if (!referral) {
      // Generate a unique referral code
      const userInitials = await getUserInitials(userId);
      let referralCode = generateReferralCode(userInitials);
      
      // Check if code already exists
      let codeExists = await prisma.referral.findUnique({
        where: { referralCode }
      });
      
      // If code exists, generate a new one
      while (codeExists) {
        referralCode = generateReferralCode(userInitials);
        codeExists = await prisma.referral.findUnique({
          where: { referralCode }
        });
      }
      
      // Generate referral link
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://vaivahik.com';
      const referralLink = `${baseUrl}/register?ref=${referralCode}`;
      
      // Create referral record
      referral = await prisma.referral.create({
        data: {
          referralProgramId: activeProgram.id,
          referrerId: userId,
          referralCode,
          referralLink,
          status: 'pending'
        }
      });
    }

    // Check if user has reached max referrals limit
    let canRefer = true;
    let remainingReferrals = null;
    
    if (activeProgram.maxReferralsPerUser) {
      const userReferralCount = await prisma.referral.count({
        where: {
          referrerId: userId,
          referralProgramId: activeProgram.id,
          refereeId: { not: null } // Only count completed referrals
        }
      });
      
      remainingReferrals = activeProgram.maxReferralsPerUser - userReferralCount;
      canRefer = remainingReferrals > 0;
    }

    // Get user's referral history
    const referralHistory = await prisma.referral.findMany({
      where: {
        referrerId: userId,
        refereeId: { not: null } // Only include completed referrals
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10,
      include: {
        referralProgram: {
          select: {
            name: true,
            referrerRewardType: true,
            referrerRewardAmount: true
          }
        }
      }
    });

    // Get total rewards earned
    const totalRewards = await prisma.referralReward.aggregate({
      where: {
        userId,
        rewardType: 'cash'
      },
      _sum: {
        rewardAmount: true
      }
    });

    return res.status(200).json({
      success: true,
      referralCode: referral.referralCode,
      referralLink: referral.referralLink,
      program: {
        id: activeProgram.id,
        name: activeProgram.name,
        referrerRewardType: activeProgram.referrerRewardType,
        referrerRewardAmount: activeProgram.referrerRewardAmount,
        refereeRewardType: activeProgram.refereeRewardType,
        refereeRewardAmount: activeProgram.refereeRewardAmount,
        termsAndConditions: activeProgram.termsAndConditions
      },
      canRefer,
      remainingReferrals,
      referralHistory: referralHistory.map(ref => ({
        id: ref.id,
        date: ref.createdAt,
        status: ref.status,
        rewardStatus: ref.referrerRewardStatus,
        programName: ref.referralProgram.name,
        rewardType: ref.referralProgram.referrerRewardType,
        rewardAmount: ref.referralProgram.referrerRewardAmount
      })),
      totalRewardsEarned: totalRewards._sum?.rewardAmount || 0
    });
  } catch (error) {
    console.error('Error generating referral code:', error);
    return res.status(500).json({ success: false, message: 'Failed to generate referral code' });
  }
}

// Helper function to get user initials
async function getUserInitials(userId) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { name: true }
    });
    
    if (!user || !user.name) return 'VV';
    
    // Extract initials from name
    const nameParts = user.name.split(' ');
    if (nameParts.length === 1) {
      return nameParts[0].substring(0, 2).toUpperCase();
    }
    
    return (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase();
  } catch (error) {
    console.error('Error getting user initials:', error);
    return 'VV';
  }
}

// Helper function to generate a referral code
function generateReferralCode(initials) {
  // Define characters to use (excluding ambiguous ones like 0/O, 1/I)
  const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
  
  // Generate a 6-character random string
  const randomPart = nanoid(6).toUpperCase();
  
  // Combine initials with random part
  return `${initials}${randomPart}`;
}
