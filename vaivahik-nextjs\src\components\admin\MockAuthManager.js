import { useState, useEffect } from 'react';
import {
  Box,
  <PERSON>ton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Tooltip,
  Typography,
  Chip
} from '@mui/material';
import {
  AccountCircle as AccountIcon,
  Login as LoginIcon,
  Logout as LogoutIcon,
  Security as SecurityIcon
} from '@mui/icons-material';

/**
 * Mock Authentication Manager Component
 *
 * This component provides a UI for managing mock authentication in development mode.
 * It allows developers to log in with different mock user roles.
 */
const MockAuthManager = () => {
  const [open, setOpen] = useState(false);
  const [mockUsers, setMockUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState('');
  const [credentials, setCredentials] = useState({ email: '', password: '' });
  const [currentUser, setCurrentUser] = useState(null);
  const [error, setError] = useState('');

  // Load current user from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedUser = localStorage.getItem('mockAuthUser');
      if (storedUser) {
        try {
          setCurrentUser(JSON.parse(storedUser));
        } catch (e) {
          console.error('Error parsing stored user:', e);
          localStorage.removeItem('mockAuthUser');
        }
      }
    }
  }, []);

  // Fetch mock users when dialog opens
  const handleOpen = async () => {
    setOpen(true);
    try {
      const response = await fetch('/api/auth/mock-admin-users');
      if (response.ok) {
        const data = await response.json();
        setMockUsers(data.users || []);
      } else {
        console.error('Failed to fetch mock admin users');
        // Fallback to hardcoded admin users if API fails
        setMockUsers([
          {
            id: 'admin-1',
            email: '<EMAIL>',
            name: 'Admin User',
            role: 'ADMIN'
          },
          {
            id: 'moderator-1',
            email: '<EMAIL>',
            name: 'Moderator User',
            role: 'MODERATOR'
          }
        ]);
      }
    } catch (error) {
      console.error('Error fetching mock admin users:', error);
      // Fallback to hardcoded admin users if API fails
      setMockUsers([
        {
          id: 'admin-1',
          email: '<EMAIL>',
          name: 'Admin User',
          role: 'ADMIN'
        },
        {
          id: 'moderator-1',
          email: '<EMAIL>',
          name: 'Moderator User',
          role: 'MODERATOR'
        }
      ]);
    }
  };

  const handleClose = () => {
    setOpen(false);
    setError('');
  };

  // Handle user selection
  const handleUserSelect = (event) => {
    const userId = event.target.value;
    setSelectedUser(userId);

    // Find selected user and set credentials
    const user = mockUsers.find(u => u.id === userId);
    if (user) {
      setCredentials({
        email: user.email,
        password: user.id.includes('admin') ? 'admin123' :
                 user.id.includes('moderator') ? 'moderator123' : 'user123'
      });
    }
  };

  // Handle login
  const handleLogin = async () => {
    try {
      setError('');

      // Use the admin login API
      const response = await fetch('/api/auth/admin-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(credentials)
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // Store user in localStorage (only in browser environment)
        if (data.user && typeof window !== 'undefined') {
          localStorage.setItem('mockAuthUser', JSON.stringify(data.user));
          localStorage.setItem('adminName', data.user.name);
          localStorage.setItem('adminRole', data.user.role);

          // Store token
          localStorage.setItem('mockAuthToken', data.token);
          localStorage.setItem('adminAccessToken', data.token);
        }

        // Update state
        setCurrentUser(data.user);
        setOpen(false);

        // Reload page to apply authentication (only in browser environment)
        if (typeof window !== 'undefined') {
          window.location.reload();
        }
      } else {
        setError(data.message || 'Authentication failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('An error occurred during login');
    }
  };

  // Handle logout
  const handleLogout = () => {
    // Only access localStorage in the browser environment
    if (typeof window !== 'undefined') {
      // Clear auth data
      localStorage.removeItem('mockAuthUser');
      localStorage.removeItem('mockAuthToken');
      localStorage.removeItem('adminName');
      localStorage.removeItem('adminRole');
      localStorage.removeItem('adminAccessToken');

      // Reload page to apply changes
      window.location.reload();
    }

    // Update state
    setCurrentUser(null);
  };

  // Only show in development mode
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <>
      {/* Toggle Button */}
      <Tooltip title="Mock Authentication">
        <IconButton
          color="inherit"
          onClick={handleOpen}
          sx={{ position: 'relative' }}
        >
          <SecurityIcon />
          {currentUser && (
            <Chip
              label={currentUser.role}
              color="primary"
              size="small"
              sx={{
                position: 'absolute',
                top: -2,
                right: -2,
                height: '18px',
                fontSize: '0.6rem',
                fontWeight: 'bold'
              }}
            />
          )}
        </IconButton>
      </Tooltip>

      {/* Mock Auth Dialog */}
      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SecurityIcon sx={{ mr: 1 }} />
            <Typography variant="h6">Mock Authentication</Typography>
          </Box>
        </DialogTitle>

        <DialogContent>
          {currentUser ? (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Currently logged in as:
              </Typography>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                p: 2,
                bgcolor: 'background.paper',
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 1
              }}>
                <AccountIcon sx={{ mr: 2, color: 'primary.main' }} />
                <Box>
                  <Typography variant="body1" fontWeight="medium">
                    {currentUser.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {currentUser.email} ({currentUser.role})
                  </Typography>
                </Box>
              </Box>
            </Box>
          ) : (
            <>
              <Typography variant="body2" color="text.secondary" paragraph>
                Select a mock user to authenticate as:
              </Typography>

              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Select User</InputLabel>
                <Select
                  value={selectedUser}
                  onChange={handleUserSelect}
                  label="Select User"
                >
                  {mockUsers.map(user => (
                    <MenuItem key={user.id} value={user.id}>
                      {user.name} ({user.role})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <TextField
                label="Email"
                fullWidth
                margin="normal"
                value={credentials.email}
                onChange={(e) => setCredentials({ ...credentials, email: e.target.value })}
              />

              <TextField
                label="Password"
                type="password"
                fullWidth
                margin="normal"
                value={credentials.password}
                onChange={(e) => setCredentials({ ...credentials, password: e.target.value })}
              />

              {error && (
                <Typography color="error" variant="body2" sx={{ mt: 1 }}>
                  {error}
                </Typography>
              )}
            </>
          )}
        </DialogContent>

        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          {currentUser ? (
            <Button
              variant="contained"
              color="error"
              startIcon={<LogoutIcon />}
              onClick={handleLogout}
            >
              Logout
            </Button>
          ) : (
            <Button
              variant="contained"
              color="primary"
              startIcon={<LoginIcon />}
              onClick={handleLogin}
              disabled={!credentials.email || !credentials.password}
            >
              Login
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </>
  );
};

export default MockAuthManager;
