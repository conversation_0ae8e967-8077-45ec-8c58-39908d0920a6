// src/services/notification.service.js

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const emailService = require('./email.service');

/**
 * Create a new notification for a user
 * @param {Object} notification - Notification object
 * @param {string} notification.userId - User ID
 * @param {string} notification.title - Notification title
 * @param {string} notification.message - Notification message
 * @param {string} notification.type - Notification type
 * @param {string} notification.actionUrl - URL to navigate to when notification is clicked
 * @returns {Promise<Object>} Created notification
 */
async function createNotification(notification) {
    try {
        const { userId, title, message, type, actionUrl } = notification;
        
        // Create notification in database
        const newNotification = await prisma.notification.create({
            data: {
                userId,
                title,
                message,
                type,
                actionUrl,
                isRead: false
            }
        });
        
        console.log(`Notification created for user ${userId}: ${title}`);
        return newNotification;
    } catch (error) {
        console.error('Error creating notification:', error);
        throw error;
    }
}

/**
 * Send verification reminders to users who haven't verified their profiles
 * @param {number} daysThreshold - Number of days since registration to send reminder
 * @returns {Promise<number>} Number of reminders sent
 */
async function sendVerificationReminders(daysThreshold = 3) {
    try {
        // Find users who registered more than X days ago but aren't verified
        const unverifiedUsers = await prisma.user.findMany({
            where: {
                isVerified: false,
                profileStatus: { not: 'PENDING_APPROVAL' },
                createdAt: { lt: new Date(Date.now() - daysThreshold * 24 * 60 * 60 * 1000) }
            },
            select: {
                id: true,
                email: true,
                phone: true,
                profile: {
                    select: { fullName: true }
                },
                // Check if they already received a reminder in the last 7 days
                notifications: {
                    where: {
                        type: 'VERIFICATION_REMINDER',
                        createdAt: { gt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
                    }
                }
            }
        });
        
        console.log(`Found ${unverifiedUsers.length} unverified users registered more than ${daysThreshold} days ago`);
        
        let remindersSent = 0;
        
        // Send reminders to users who haven't received one recently
        for (const user of unverifiedUsers) {
            // Skip if user already received a reminder in the last 7 days
            if (user.notifications && user.notifications.length > 0) {
                console.log(`Skipping reminder for user ${user.id}: already received one recently`);
                continue;
            }
            
            // Create in-app notification
            await createNotification({
                userId: user.id,
                title: "Verify Your Profile",
                message: "Get a verification badge and unlock all features by verifying your profile.",
                type: "VERIFICATION_REMINDER",
                actionUrl: "/verification"
            });
            
            // Send email reminder if email is available
            if (user.email) {
                await emailService.sendEmail({
                    to: user.email,
                    subject: "Verify Your Vaivahik Profile",
                    template: "verification-reminder",
                    data: {
                        name: user.profile?.fullName || "User",
                        verificationLink: `${process.env.FRONTEND_URL}/verification`
                    }
                });
            }
            
            // Send SMS reminder if needed (implementation would go here)
            
            remindersSent++;
        }
        
        console.log(`Sent ${remindersSent} verification reminders`);
        return remindersSent;
    } catch (error) {
        console.error('Error sending verification reminders:', error);
        throw error;
    }
}

/**
 * Mark a notification as read
 * @param {string} notificationId - Notification ID
 * @param {string} userId - User ID (for security check)
 * @returns {Promise<Object>} Updated notification
 */
async function markNotificationAsRead(notificationId, userId) {
    try {
        // Verify the notification belongs to the user
        const notification = await prisma.notification.findUnique({
            where: { id: notificationId },
            select: { userId: true }
        });
        
        if (!notification || notification.userId !== userId) {
            throw new Error('Notification not found or does not belong to user');
        }
        
        // Mark as read
        const updatedNotification = await prisma.notification.update({
            where: { id: notificationId },
            data: { isRead: true, readAt: new Date() }
        });
        
        return updatedNotification;
    } catch (error) {
        console.error('Error marking notification as read:', error);
        throw error;
    }
}

/**
 * Get all notifications for a user
 * @param {string} userId - User ID
 * @param {boolean} unreadOnly - Whether to get only unread notifications
 * @returns {Promise<Array>} List of notifications
 */
async function getUserNotifications(userId, unreadOnly = false) {
    try {
        const notifications = await prisma.notification.findMany({
            where: {
                userId,
                ...(unreadOnly ? { isRead: false } : {})
            },
            orderBy: { createdAt: 'desc' }
        });
        
        return notifications;
    } catch (error) {
        console.error('Error getting user notifications:', error);
        throw error;
    }
}

module.exports = {
    createNotification,
    sendVerificationReminders,
    markNotificationAsRead,
    getUserNotifications
};
