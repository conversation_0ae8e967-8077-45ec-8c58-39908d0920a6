/**
 * AI Phase Management - Admin Panel
 * Manages AI algorithm phases and transitions for the matrimony platform
 */

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  LinearProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  PlayArrow as StartIcon,
  Pause as PauseIcon,
  Stop as StopIcon,
  Settings as SettingsIcon,
  Analytics as AnalyticsIcon,
  Timeline as TimelineIcon,
  CheckCircle as CompleteIcon,
  Warning as WarningIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { ToastContainer, toast } from 'react-toastify';

// Import EnhancedAdminLayout with SSR disabled
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);

export default function AIPhaseManagement() {
  const [currentPhase, setCurrentPhase] = useState('v1.0');
  const [phaseStatus, setPhaseStatus] = useState('active');
  const [phases, setPhases] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedPhase, setSelectedPhase] = useState(null);
  const [transitionData, setTransitionData] = useState({
    targetPhase: '',
    reason: '',
    scheduledTime: ''
  });

  useEffect(() => {
    fetchPhaseData();
  }, []);

  const fetchPhaseData = async () => {
    try {
      setLoading(true);
      
      // Mock data for AI phases
      const mockPhases = [
        {
          id: 'v1.0',
          name: 'Basic Matching Algorithm',
          version: '1.0',
          status: 'active',
          description: 'Rule-based matching with basic preferences',
          accuracy: 72.5,
          performance: 85.2,
          dataRequirement: 'Basic profile data',
          userCount: 1250,
          matchesGenerated: 15420,
          successRate: 3.8,
          startDate: '2024-01-15',
          features: ['Age matching', 'Location proximity', 'Basic preferences']
        },
        {
          id: 'v2.0',
          name: 'ML-Enhanced Matching',
          version: '2.0',
          status: 'ready',
          description: 'Machine learning with user behavior analysis',
          accuracy: 84.3,
          performance: 78.9,
          dataRequirement: 'Profile + interaction data',
          userCount: 0,
          matchesGenerated: 0,
          successRate: 0,
          estimatedStartDate: '2024-07-01',
          features: ['ML predictions', 'Behavior analysis', 'Advanced preferences', 'Compatibility scoring']
        },
        {
          id: 'v3.0',
          name: 'AI-Powered Deep Matching',
          version: '3.0',
          status: 'development',
          description: 'Deep learning with personality and lifestyle analysis',
          accuracy: 92.1,
          performance: 71.4,
          dataRequirement: 'Complete profile + behavioral + interaction data',
          userCount: 0,
          matchesGenerated: 0,
          successRate: 0,
          estimatedStartDate: '2024-12-01',
          features: ['Deep learning', 'Personality analysis', 'Lifestyle matching', 'Predictive compatibility', 'Real-time optimization']
        }
      ];

      setPhases(mockPhases);
      setCurrentPhase(mockPhases.find(p => p.status === 'active')?.id || 'v1.0');
      
    } catch (error) {
      console.error('Error fetching phase data:', error);
      toast.error('Failed to load phase data');
    } finally {
      setLoading(false);
    }
  };

  const handlePhaseTransition = async (targetPhaseId) => {
    try {
      // Simulate API call for phase transition
      console.log('Transitioning to phase:', targetPhaseId);
      
      // Update phase status
      setPhases(prev => prev.map(phase => ({
        ...phase,
        status: phase.id === targetPhaseId ? 'active' : 
                phase.id === currentPhase ? 'inactive' : phase.status
      })));
      
      setCurrentPhase(targetPhaseId);
      toast.success(`Successfully transitioned to ${targetPhaseId}`);
      setOpenDialog(false);
      
    } catch (error) {
      console.error('Error transitioning phase:', error);
      toast.error('Failed to transition phase');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'ready': return 'warning';
      case 'development': return 'info';
      case 'inactive': return 'default';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active': return <CompleteIcon />;
      case 'ready': return <WarningIcon />;
      case 'development': return <SettingsIcon />;
      case 'inactive': return <StopIcon />;
      default: return <ErrorIcon />;
    }
  };

  if (loading) {
    return (
      <EnhancedAdminLayout title="AI Phase Management">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
          <LinearProgress sx={{ width: '50%' }} />
        </Box>
      </EnhancedAdminLayout>
    );
  }

  return (
    <EnhancedAdminLayout title="AI Phase Management">
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            AI Phase Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage AI algorithm phases and transitions for optimal matching performance
          </Typography>
        </Box>

        {/* Current Phase Overview */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Current Active Phase
            </Typography>
            {phases.find(p => p.id === currentPhase) && (
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h5" sx={{ mr: 2 }}>
                      {phases.find(p => p.id === currentPhase).name}
                    </Typography>
                    <Chip 
                      label={phases.find(p => p.id === currentPhase).status}
                      color={getStatusColor(phases.find(p => p.id === currentPhase).status)}
                      icon={getStatusIcon(phases.find(p => p.id === currentPhase).status)}
                    />
                  </Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {phases.find(p => p.id === currentPhase).description}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Accuracy</Typography>
                      <Typography variant="h6">{phases.find(p => p.id === currentPhase).accuracy}%</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Performance</Typography>
                      <Typography variant="h6">{phases.find(p => p.id === currentPhase).performance}%</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Active Users</Typography>
                      <Typography variant="h6">{phases.find(p => p.id === currentPhase).userCount}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Success Rate</Typography>
                      <Typography variant="h6">{phases.find(p => p.id === currentPhase).successRate}%</Typography>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            )}
          </CardContent>
        </Card>

        {/* Phase Management Table */}
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">
                All Algorithm Phases
              </Typography>
              <Button
                variant="contained"
                startIcon={<TimelineIcon />}
                onClick={() => setOpenDialog(true)}
              >
                Manage Transition
              </Button>
            </Box>

            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Phase</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Accuracy</TableCell>
                    <TableCell>Performance</TableCell>
                    <TableCell>Users</TableCell>
                    <TableCell>Success Rate</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {phases.map((phase) => (
                    <TableRow key={phase.id}>
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2">{phase.name}</Typography>
                          <Typography variant="caption" color="text.secondary">
                            {phase.version}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={phase.status}
                          color={getStatusColor(phase.status)}
                          size="small"
                          icon={getStatusIcon(phase.status)}
                        />
                      </TableCell>
                      <TableCell>{phase.accuracy}%</TableCell>
                      <TableCell>{phase.performance}%</TableCell>
                      <TableCell>{phase.userCount}</TableCell>
                      <TableCell>{phase.successRate}%</TableCell>
                      <TableCell>
                        <Tooltip title="View Analytics">
                          <IconButton size="small">
                            <AnalyticsIcon />
                          </IconButton>
                        </Tooltip>
                        {phase.status === 'ready' && (
                          <Tooltip title="Activate Phase">
                            <IconButton 
                              size="small" 
                              color="primary"
                              onClick={() => {
                                setSelectedPhase(phase);
                                setTransitionData(prev => ({ ...prev, targetPhase: phase.id }));
                                setOpenDialog(true);
                              }}
                            >
                              <StartIcon />
                            </IconButton>
                          </Tooltip>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        {/* Phase Transition Dialog */}
        <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Phase Transition</DialogTitle>
          <DialogContent>
            <Box sx={{ pt: 2 }}>
              <FormControl fullWidth sx={{ mb: 3 }}>
                <InputLabel>Target Phase</InputLabel>
                <Select
                  value={transitionData.targetPhase}
                  onChange={(e) => setTransitionData(prev => ({ ...prev, targetPhase: e.target.value }))}
                >
                  {phases.filter(p => p.status === 'ready').map(phase => (
                    <MenuItem key={phase.id} value={phase.id}>
                      {phase.name} ({phase.version})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              
              <TextField
                fullWidth
                label="Transition Reason"
                multiline
                rows={3}
                value={transitionData.reason}
                onChange={(e) => setTransitionData(prev => ({ ...prev, reason: e.target.value }))}
                sx={{ mb: 3 }}
              />
              
              <TextField
                fullWidth
                label="Scheduled Time"
                type="datetime-local"
                value={transitionData.scheduledTime}
                onChange={(e) => setTransitionData(prev => ({ ...prev, scheduledTime: e.target.value }))}
                InputLabelProps={{ shrink: true }}
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
            <Button 
              variant="contained" 
              onClick={() => handlePhaseTransition(transitionData.targetPhase)}
              disabled={!transitionData.targetPhase}
            >
              Execute Transition
            </Button>
          </DialogActions>
        </Dialog>

        <ToastContainer position="top-right" />
      </Box>
    </EnhancedAdminLayout>
  );
}
