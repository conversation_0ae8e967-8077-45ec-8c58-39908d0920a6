"""
Embedding Service for Matrimony Matching

This module provides embedding precomputation and caching functionality
to optimize the matrimony matching system for production.
"""

import os
import json
import logging
import asyncio
import torch
import numpy as np
from datetime import datetime, timedelta
from prisma.client import PrismaClient

from .enhanced_tower_model_pytorch import EnhancedMatrimonyMatchingModel
from .feature_processor import FeatureProcessor
from .redis_cache import RedisCache

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmbeddingService:
    """Embedding service for matrimony matching"""
    
    def __init__(self, model=None, redis_cache=None, config=None):
        """
        Initialize the embedding service
        
        Args:
            model: An instance of EnhancedMatrimonyMatchingModel
            redis_cache: Redis cache instance
            config (dict): Configuration parameters
        """
        # Default configuration
        self.default_config = {
            'embedding_ttl': 604800,  # 7 days in seconds
            'batch_size': 64,
            'precompute_interval': 86400,  # 24 hours in seconds
            'precompute_limit': 1000,  # Maximum number of users to precompute at once
            'model_path': os.path.join(os.path.dirname(__file__), '../../models/production_model.pt'),
            'use_quantized_model': True
        }
        
        # Use provided config or default
        self.config = config if config else self.default_config
        
        # Set device
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {self.device}")
        
        # Initialize Prisma client
        self.prisma = PrismaClient()
        
        # Initialize feature processor
        stats_path = os.path.join(os.path.dirname(__file__), '../../models/feature_stats.json')
        self.feature_processor = FeatureProcessor(stats_path)
        
        # Initialize Redis cache
        self.redis_cache = redis_cache
        
        # Initialize model
        self.model = model
        if not self.model:
            self.load_model()
    
    def load_model(self):
        """Load the model from disk"""
        try:
            model_path = self.config['model_path']
            
            # Check if model exists
            if not os.path.exists(model_path):
                logger.error(f"Model not found: {model_path}")
                return False
            
            # Load model
            if self.config['use_quantized_model']:
                # Load quantized model
                self.model = torch.jit.load(model_path, map_location=self.device)
                logger.info(f"Quantized model loaded from {model_path}")
            else:
                # Load regular model
                checkpoint = torch.load(model_path, map_location=self.device)
                
                if 'config' in checkpoint:
                    config = checkpoint['config']
                    self.model = EnhancedMatrimonyMatchingModel(config)
                    self.model.build_model()
                    
                    # Load state dict
                    self.model.model.load_state_dict(checkpoint['model_state_dict'])
                    self.model.model.to(self.device)
                    self.model.model.eval()  # Set to evaluation mode
                    
                    logger.info(f"Model loaded from {model_path}")
                else:
                    logger.error(f"Invalid model checkpoint: {model_path}")
                    return False
            
            return True
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            return False
    
    async def compute_user_embedding(self, user_profile, user_preferences):
        """
        Compute embedding for a user
        
        Args:
            user_profile (dict): User profile data
            user_preferences (dict): User preferences
            
        Returns:
            torch.Tensor: User embedding
        """
        try:
            # Process features
            user_features, _ = self.feature_processor.process_match_pair(
                user_profile, user_preferences, {}  # Empty match profile
            )
            
            # Convert to tensor
            user_tensor = self._features_to_tensor([user_features])
            
            # Move to device
            user_tensor = user_tensor.to(self.device)
            
            # Compute embedding
            with torch.no_grad():
                if self.config['use_quantized_model']:
                    # For quantized model
                    embedding = self.model.user_tower(user_tensor)
                else:
                    # For regular model
                    embedding = self.model.model.user_tower(user_tensor)
            
            return embedding.cpu()
        except Exception as e:
            logger.error(f"Error computing user embedding: {str(e)}")
            return None
    
    async def compute_match_embedding(self, match_profile):
        """
        Compute embedding for a match
        
        Args:
            match_profile (dict): Match profile data
            
        Returns:
            torch.Tensor: Match embedding
        """
        try:
            # Process features
            _, match_features = self.feature_processor.process_match_pair(
                {}, {}, match_profile  # Empty user profile and preferences
            )
            
            # Convert to tensor
            match_tensor = self._features_to_tensor([match_features])
            
            # Move to device
            match_tensor = match_tensor.to(self.device)
            
            # Compute embedding
            with torch.no_grad():
                if self.config['use_quantized_model']:
                    # For quantized model
                    embedding = self.model.match_tower(match_tensor)
                else:
                    # For regular model
                    embedding = self.model.model.match_tower(match_tensor)
            
            return embedding.cpu()
        except Exception as e:
            logger.error(f"Error computing match embedding: {str(e)}")
            return None
    
    def _features_to_tensor(self, features_list):
        """
        Convert feature dictionaries to tensor
        
        Args:
            features_list (list): List of feature dictionaries
            
        Returns:
            torch.Tensor: Feature tensor
        """
        # Get all feature keys
        all_keys = set()
        for features in features_list:
            all_keys.update(features.keys())
        
        # Sort keys for consistent ordering
        sorted_keys = sorted(all_keys)
        
        # Create tensor
        tensor_data = []
        for features in features_list:
            feature_vector = [features.get(key, 0.0) for key in sorted_keys]
            tensor_data.append(feature_vector)
        
        return torch.tensor(tensor_data, dtype=torch.float32)
    
    async def get_user_embedding(self, user_id):
        """
        Get embedding for a user, either from cache or by computing it
        
        Args:
            user_id (str): User ID
            
        Returns:
            torch.Tensor: User embedding
        """
        # Check cache if available
        if self.redis_cache and self.redis_cache.is_connected():
            cached_embedding = await self.redis_cache.get_user_embedding(user_id)
            if cached_embedding is not None:
                logger.info(f"Using cached embedding for user {user_id}")
                return cached_embedding
        
        # Get user profile and preferences
        user = await self.prisma.user.find_unique(
            where={'id': user_id},
            include={
                'profile': True,
                'preference': True
            }
        )
        
        if not user or not user.profile:
            logger.error(f"User {user_id} not found or has no profile")
            return None
        
        # Convert to dictionaries
        user_profile = {
            'id': user.id,
            'name': user.name,
            'age': user.profile.age,
            'gender': user.profile.gender,
            'height': user.profile.height,
            'religion': user.profile.religion,
            'caste': user.profile.caste,
            'subCaste': user.profile.subCaste,
            'gotra': user.profile.gotra,
            'education': user.profile.education,
            'occupation': user.profile.occupation,
            'income': user.profile.income,
            'city': user.profile.city,
            'state': user.profile.state,
            'maritalStatus': user.profile.maritalStatus
        }
        
        user_preferences = {}
        if user.preference:
            user_preferences = {
                'minAge': user.preference.minAge,
                'maxAge': user.preference.maxAge,
                'minHeight': user.preference.minHeight,
                'maxHeight': user.preference.maxHeight,
                'religion': user.preference.religion,
                'caste': user.preference.caste,
                'subCaste': user.preference.subCaste,
                'education': user.preference.education,
                'occupation': user.preference.occupation,
                'minIncome': user.preference.minIncome,
                'city': user.preference.city,
                'state': user.preference.state,
                'maritalStatus': user.preference.maritalStatus
            }
        
        # Compute embedding
        embedding = await self.compute_user_embedding(user_profile, user_preferences)
        
        # Cache embedding if available
        if embedding is not None and self.redis_cache and self.redis_cache.is_connected():
            await self.redis_cache.cache_user_embedding(user_id, embedding)
        
        return embedding
    
    async def precompute_embeddings(self):
        """
        Precompute embeddings for active users
        
        Returns:
            int: Number of embeddings computed
        """
        try:
            # Get active users
            active_users = await self.prisma.user.find_many(
                where={
                    'profileStatus': 'ACTIVE',
                    'lastActive': {
                        'gte': datetime.now() - timedelta(days=30)  # Active in the last 30 days
                    }
                },
                include={
                    'profile': True,
                    'preference': True
                },
                take=self.config['precompute_limit']
            )
            
            logger.info(f"Precomputing embeddings for {len(active_users)} active users")
            
            # Process in batches
            batch_size = self.config['batch_size']
            count = 0
            
            for i in range(0, len(active_users), batch_size):
                batch = active_users[i:i+batch_size]
                
                # Process batch
                for user in batch:
                    if not user.profile:
                        continue
                    
                    # Convert to dictionaries
                    user_profile = {
                        'id': user.id,
                        'name': user.name,
                        'age': user.profile.age,
                        'gender': user.profile.gender,
                        'height': user.profile.height,
                        'religion': user.profile.religion,
                        'caste': user.profile.caste,
                        'subCaste': user.profile.subCaste,
                        'gotra': user.profile.gotra,
                        'education': user.profile.education,
                        'occupation': user.profile.occupation,
                        'income': user.profile.income,
                        'city': user.profile.city,
                        'state': user.profile.state,
                        'maritalStatus': user.profile.maritalStatus
                    }
                    
                    user_preferences = {}
                    if user.preference:
                        user_preferences = {
                            'minAge': user.preference.minAge,
                            'maxAge': user.preference.maxAge,
                            'minHeight': user.preference.minHeight,
                            'maxHeight': user.preference.maxHeight,
                            'religion': user.preference.religion,
                            'caste': user.preference.caste,
                            'subCaste': user.preference.subCaste,
                            'education': user.preference.education,
                            'occupation': user.preference.occupation,
                            'minIncome': user.preference.minIncome,
                            'city': user.preference.city,
                            'state': user.preference.state,
                            'maritalStatus': user.preference.maritalStatus
                        }
                    
                    # Compute embedding
                    embedding = await self.compute_user_embedding(user_profile, user_preferences)
                    
                    # Cache embedding if available
                    if embedding is not None and self.redis_cache and self.redis_cache.is_connected():
                        await self.redis_cache.cache_user_embedding(user.id, embedding)
                        count += 1
                
                # Sleep to avoid overloading the server
                await asyncio.sleep(1)
            
            logger.info(f"Precomputed {count} embeddings")
            return count
        except Exception as e:
            logger.error(f"Error precomputing embeddings: {str(e)}")
            return 0
    
    async def start_precomputation_scheduler(self):
        """Start the embedding precomputation scheduler"""
        while True:
            try:
                # Precompute embeddings
                await self.precompute_embeddings()
                
                # Sleep until next precomputation
                await asyncio.sleep(self.config['precompute_interval'])
            except Exception as e:
                logger.error(f"Error in precomputation scheduler: {str(e)}")
                await asyncio.sleep(60)  # Sleep for a minute before retrying
