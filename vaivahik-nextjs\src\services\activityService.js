import axios from 'axios';
import { API_BASE_URL } from '@/config';

const API_URL = `${API_BASE_URL}/activities`;

/**
 * Get user activities
 * @param {Object} options - Query options
 * @returns {Promise} Promise with activities data
 */
export const getUserActivities = async (options = {}) => {
  try {
    const token = localStorage.getItem('token');
    
    const queryParams = new URLSearchParams();
    
    if (options.type) {
      queryParams.append('type', options.type);
    }
    
    if (options.page) {
      queryParams.append('page', options.page);
    }
    
    if (options.limit) {
      queryParams.append('limit', options.limit);
    }
    
    if (options.isRead !== undefined) {
      queryParams.append('isRead', options.isRead);
    }
    
    if (options.startDate) {
      queryParams.append('startDate', options.startDate);
    }
    
    if (options.endDate) {
      queryParams.append('endDate', options.endDate);
    }
    
    const response = await axios.get(`${API_URL}?${queryParams.toString()}`, {
      headers: {
        'x-auth-token': token
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error fetching activities:', error);
    throw error;
  }
};

/**
 * Get activity counts by type
 * @returns {Promise} Promise with activity counts
 */
export const getActivityCounts = async () => {
  try {
    const token = localStorage.getItem('token');
    
    const response = await axios.get(`${API_URL}/counts`, {
      headers: {
        'x-auth-token': token
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error fetching activity counts:', error);
    throw error;
  }
};

/**
 * Mark activities as read
 * @param {Array} activityIds - Activity IDs to mark as read (optional)
 * @returns {Promise} Promise with result
 */
export const markActivitiesAsRead = async (activityIds = null) => {
  try {
    const token = localStorage.getItem('token');
    
    const response = await axios.put(
      `${API_URL}/read`,
      { activityIds },
      {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      }
    );
    
    return response.data;
  } catch (error) {
    console.error('Error marking activities as read:', error);
    throw error;
  }
};

/**
 * Delete activities
 * @param {Array} activityIds - Activity IDs to delete
 * @returns {Promise} Promise with result
 */
export const deleteActivities = async (activityIds) => {
  try {
    const token = localStorage.getItem('token');
    
    const response = await axios.delete(
      API_URL,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        data: { activityIds }
      }
    );
    
    return response.data;
  } catch (error) {
    console.error('Error deleting activities:', error);
    throw error;
  }
};

/**
 * Get another user's activities
 * @param {String} userId - User ID
 * @param {Object} options - Query options
 * @returns {Promise} Promise with activities data
 */
export const getUserPublicActivities = async (userId, options = {}) => {
  try {
    const token = localStorage.getItem('token');
    
    const queryParams = new URLSearchParams();
    
    if (options.type) {
      queryParams.append('type', options.type);
    }
    
    if (options.page) {
      queryParams.append('page', options.page);
    }
    
    if (options.limit) {
      queryParams.append('limit', options.limit);
    }
    
    const response = await axios.get(`${API_URL}/user/${userId}?${queryParams.toString()}`, {
      headers: {
        'x-auth-token': token
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error fetching user public activities:', error);
    throw error;
  }
};

/**
 * Format activity timestamp
 * @param {Date} timestamp - Activity timestamp
 * @returns {String} Formatted timestamp
 */
export const formatActivityTime = (timestamp) => {
  const now = new Date();
  const activityTime = new Date(timestamp);
  const diffMs = now - activityTime;
  
  // Convert to seconds
  const diffSec = Math.floor(diffMs / 1000);
  
  if (diffSec < 60) {
    return 'Just now';
  }
  
  // Convert to minutes
  const diffMin = Math.floor(diffSec / 60);
  
  if (diffMin < 60) {
    return `${diffMin} minute${diffMin !== 1 ? 's' : ''} ago`;
  }
  
  // Convert to hours
  const diffHour = Math.floor(diffMin / 60);
  
  if (diffHour < 24) {
    return `${diffHour} hour${diffHour !== 1 ? 's' : ''} ago`;
  }
  
  // Convert to days
  const diffDay = Math.floor(diffHour / 24);
  
  if (diffDay < 7) {
    return `${diffDay} day${diffDay !== 1 ? 's' : ''} ago`;
  }
  
  // Format as date for older activities
  return activityTime.toLocaleDateString();
};
