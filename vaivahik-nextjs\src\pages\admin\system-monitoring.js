/**
 * Admin System Monitoring Dashboard
 * Integrates all the new features: Analytics, Performance, Security, Errors
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Tabs,
  Tab,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel,
  LinearProgress
} from '@mui/material';
import {
  Security as SecurityIcon,
  Speed as PerformanceIcon,
  Analytics as AnalyticsIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);
import AdvancedAnalytics from '@/components/analytics/AdvancedAnalytics';

const SystemMonitoring = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [systemHealth, setSystemHealth] = useState({});
  const [errorData, setErrorData] = useState({});
  const [performanceData, setPerformanceData] = useState({});
  const [securityData, setSecurityData] = useState({});
  const [loading, setLoading] = useState(true);

  // Fetch system data
  useEffect(() => {
    const fetchSystemData = async () => {
      setLoading(true);
      try {
        // Mock error monitoring data
        setErrorData({
          totalErrors: 45,
          errors24h: 12,
          errors7d: 89,
          errorsBySeverity: {
            error: 15,
            warning: 25,
            info: 5
          },
          recentErrors: [
            { timestamp: new Date().toISOString(), error: { name: 'ValidationError', message: 'Invalid email format' }, severity: 'warning' },
            { timestamp: new Date().toISOString(), error: { name: 'APIError', message: 'Database connection timeout' }, severity: 'error' },
            { timestamp: new Date().toISOString(), error: { name: 'AuthError', message: 'Invalid token' }, severity: 'warning' }
          ]
        });

        // Mock performance data
        setPerformanceData({
          metrics: {
            LCP: { value: 1200 },
            FID: { value: 85 },
            CLS: { value: 0.08 }
          },
          recommendations: [
            { type: 'LCP', severity: 'medium', message: 'Consider optimizing images for faster loading' },
            { type: 'FID', severity: 'low', message: 'JavaScript execution time is acceptable' }
          ]
        });

        // Mock security data
        setSecurityData({
          rateLimitStatus: {
            totalRequests: 15420,
            blockedRequests: 23,
            activeIPs: 1250
          },
          securityEvents: [
            { type: 'RATE_LIMIT_EXCEEDED', count: 15, severity: 'medium' },
            { type: 'INVALID_CSRF_TOKEN', count: 3, severity: 'high' },
            { type: 'SUSPICIOUS_FILE_UPLOAD', count: 1, severity: 'high' }
          ]
        });

        // Overall system health
        setSystemHealth({
          status: 'healthy',
          uptime: '99.9%',
          responseTime: '245ms',
          errorRate: '0.1%',
          lastUpdated: new Date().toISOString()
        });

      } catch (error) {
        console.error('Error fetching system data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSystemData();
    const interval = setInterval(fetchSystemData, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // System Health Overview
  const SystemHealthOverview = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography color="text.secondary" gutterBottom>
                  System Status
                </Typography>
                <Typography variant="h5">
                  {systemHealth.status === 'healthy' ? 'Healthy' : 'Issues'}
                </Typography>
              </Box>
              <CheckCircleIcon sx={{ fontSize: 40, color: 'success.main' }} />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography color="text.secondary" gutterBottom>
                  Uptime
                </Typography>
                <Typography variant="h5">{systemHealth.uptime}</Typography>
              </Box>
              <PerformanceIcon sx={{ fontSize: 40, color: 'primary.main' }} />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography color="text.secondary" gutterBottom>
                  Response Time
                </Typography>
                <Typography variant="h5">{systemHealth.responseTime}</Typography>
              </Box>
              <AnalyticsIcon sx={{ fontSize: 40, color: 'info.main' }} />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography color="text.secondary" gutterBottom>
                  Error Rate
                </Typography>
                <Typography variant="h5">{systemHealth.errorRate}</Typography>
              </Box>
              <ErrorIcon sx={{ fontSize: 40, color: 'error.main' }} />
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  // Error Monitoring Tab
  const ErrorMonitoringTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Error Summary
            </Typography>
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Total Errors: {errorData.totalErrors || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Last 24h: {errorData.errors24h || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Last 7d: {errorData.errors7d || 0}
              </Typography>
            </Box>
            
            <Typography variant="subtitle2" gutterBottom>
              By Severity
            </Typography>
            {Object.entries(errorData.errorsBySeverity || {}).map(([severity, count]) => (
              <Box key={severity} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Chip
                  label={severity}
                  size="small"
                  color={severity === 'error' ? 'error' : severity === 'warning' ? 'warning' : 'info'}
                />
                <Typography variant="body2">{count}</Typography>
              </Box>
            ))}
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={8}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Recent Errors
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Time</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Message</TableCell>
                    <TableCell>Severity</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {(errorData.recentErrors || []).slice(0, 10).map((error, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        {new Date(error.timestamp).toLocaleTimeString()}
                      </TableCell>
                      <TableCell>{error.error.name}</TableCell>
                      <TableCell>{error.error.message}</TableCell>
                      <TableCell>
                        <Chip
                          label={error.severity}
                          size="small"
                          color={error.severity === 'error' ? 'error' : 'warning'}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  // Performance Monitoring Tab
  const PerformanceMonitoringTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Core Web Vitals
            </Typography>
            {Object.entries(performanceData.metrics || {}).map(([metric, data]) => (
              <Box key={metric} sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">{metric}</Typography>
                  <Typography variant="body2">{data.value?.toFixed(2)}ms</Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={Math.min((data.value / 1000) * 100, 100)}
                  color={data.value < 500 ? 'success' : data.value < 1000 ? 'warning' : 'error'}
                />
              </Box>
            ))}
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Performance Recommendations
            </Typography>
            {(performanceData.recommendations || []).map((rec, index) => (
              <Alert
                key={index}
                severity={rec.severity}
                sx={{ mb: 1 }}
              >
                <Typography variant="body2">
                  <strong>{rec.type}:</strong> {rec.message}
                </Typography>
              </Alert>
            ))}
            {(!performanceData.recommendations || performanceData.recommendations.length === 0) && (
              <Alert severity="success">
                No performance issues detected. System is running optimally.
              </Alert>
            )}
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  // Security Monitoring Tab
  const SecurityMonitoringTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Rate Limiting Status
            </Typography>
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Total Requests: {securityData.rateLimitStatus?.totalRequests || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Blocked Requests: {securityData.rateLimitStatus?.blockedRequests || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active IPs: {securityData.rateLimitStatus?.activeIPs || 0}
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={8}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Security Events
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Event Type</TableCell>
                    <TableCell>Count</TableCell>
                    <TableCell>Severity</TableCell>
                    <TableCell>Action</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {(securityData.securityEvents || []).map((event, index) => (
                    <TableRow key={index}>
                      <TableCell>{event.type}</TableCell>
                      <TableCell>{event.count}</TableCell>
                      <TableCell>
                        <Chip
                          label={event.severity}
                          size="small"
                          color={event.severity === 'high' ? 'error' : 'warning'}
                        />
                      </TableCell>
                      <TableCell>
                        <Tooltip title="View Details">
                          <IconButton size="small">
                            <AnalyticsIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  if (loading) {
    return (
      <AdminLayout>
        <Box sx={{ p: 3 }}>
          <LinearProgress />
          <Typography sx={{ mt: 2 }}>Loading system monitoring data...</Typography>
        </Box>
      </AdminLayout>
    );
  }

  return (
    <EnhancedAdminLayout>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" gutterBottom>
            System Monitoring
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Refresh Data">
              <IconButton onClick={() => window.location.reload()}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Export Report">
              <IconButton>
                <DownloadIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* System Health Overview */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            System Health Overview
          </Typography>
          <SystemHealthOverview />
        </Box>

        {/* Tabs */}
        <Card>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={activeTab} onChange={handleTabChange}>
              <Tab
                icon={<AnalyticsIcon />}
                label="Advanced Analytics"
                iconPosition="start"
              />
              <Tab
                icon={<ErrorIcon />}
                label="Error Monitoring"
                iconPosition="start"
              />
              <Tab
                icon={<PerformanceIcon />}
                label="Performance"
                iconPosition="start"
              />
              <Tab
                icon={<SecurityIcon />}
                label="Security"
                iconPosition="start"
              />
            </Tabs>
          </Box>

          <CardContent>
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              {activeTab === 0 && <AdvancedAnalytics />}
              {activeTab === 1 && <ErrorMonitoringTab />}
              {activeTab === 2 && <PerformanceMonitoringTab />}
              {activeTab === 3 && <SecurityMonitoringTab />}
            </motion.div>
          </CardContent>
        </Card>
      </Box>
    </EnhancedAdminLayout>
  );
};

export default SystemMonitoring;
