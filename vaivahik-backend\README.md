# Vaivahik Backend

This is the backend server for the Vaivahik Matrimony platform, designed specifically for the Maratha community.

## Directory Structure

This project uses a structured organization for better maintainability:

### Main Directories
- `/src` - Core application code
  - `/src/api` - API endpoints and handlers
  - `/src/controllers` - Controller logic for handling requests
  - `/src/middleware` - Middleware functions for authentication, validation, etc.
  - `/src/models` - Data models and schemas
  - `/src/routes` - Route definitions
  - `/src/services` - Business logic and services
  - `/src/utils` - Utility functions and helpers
  - `/src/validators` - Input validation logic
- `/prisma` - Prisma ORM schema and migrations
- `/config` - Configuration files
- `/public` - Static files
- `/uploads` - User uploaded files
- `/tests` - Test files

### Key Features
- **Admin Panel API**: Located in `/src/routes/admin`
- **User API**: Located in `/src/routes/user.routes.js`
- **Authentication**: JWT-based authentication with middleware
- **Feature Flags**: Toggle between mock and real data
- **Redis Integration**: For caching and real-time features
- **Matching Algorithm**: AI-powered matching for matrimony profiles

## Getting Started

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. Run database migrations:
```bash
npx prisma migrate dev
```

4. Start the development server:
```bash
npm run dev
```

## API Documentation

The API is organized into the following main sections:

- `/api/auth` - Authentication endpoints
- `/api/users` - User management
- `/api/profiles` - Profile management
- `/api/matches` - Match recommendations
- `/api/admin` - Admin panel endpoints
- `/api/verification` - User verification

## Development Guidelines

1. **Code Organization**:
   - Keep routes in the `/src/routes` directory
   - Implement business logic in services
   - Use controllers to handle request/response

2. **API Standards**:
   - Use consistent naming conventions
   - Implement proper error handling
   - Validate all inputs

3. **Testing**:
   - Write unit tests for critical functionality
   - Use mock data for development

4. **Security**:
   - Validate all user inputs
   - Use middleware for authentication
   - Implement rate limiting for sensitive endpoints

## Production Deployment

For production deployment:

1. Set NODE_ENV=production
2. Configure proper database credentials
3. Set up Redis for production
4. Enable security features (CORS, Helmet, etc.)
5. Set up proper logging

## Maintenance

Regular maintenance tasks:

1. Update dependencies
2. Run security audits
3. Monitor performance
4. Back up database regularly
