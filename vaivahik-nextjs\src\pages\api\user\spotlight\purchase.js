import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  // Check if user is authenticated
  const session = await getServerSession(req, res, authOptions);

  if (!session || !session.user) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { spotlightId, paymentMethod, transactionId, quantity = 1 } = req.body;
    const userId = session.user.id;

    if (!spotlightId) {
      return res.status(400).json({ success: false, message: 'Spotlight ID is required' });
    }

    // Check if spotlight feature exists
    const spotlight = await prisma.spotlightFeature.findUnique({
      where: { id: spotlightId }
    });

    if (!spotlight) {
      return res.status(404).json({ success: false, message: 'Spotlight feature not found' });
    }

    // Check if spotlight feature is active
    if (!spotlight.isActive) {
      return res.status(400).json({ success: false, message: 'This spotlight feature is currently unavailable' });
    }

    // Calculate price and total count
    const unitPrice = spotlight.discountedPrice || spotlight.price;
    const totalPrice = unitPrice * quantity;

    // Calculate total spotlight count (quantity * defaultCount)
    const defaultCount = spotlight.defaultCount || 1;
    const totalCount = quantity * defaultCount;

    // Create or update purchase record
    const existingPurchase = await prisma.userSpotlight.findFirst({
      where: {
        userId,
        spotlightId
      }
    });

    let userSpotlight;

    if (existingPurchase) {
      // Update existing purchase
      userSpotlight = await prisma.userSpotlight.update({
        where: { id: existingPurchase.id },
        data: {
          availableCount: existingPurchase.availableCount + totalCount,
          pricePaid: existingPurchase.pricePaid + totalPrice
        }
      });
    } else {
      // Create new purchase
      userSpotlight = await prisma.userSpotlight.create({
        data: {
          userId,
          spotlightId,
          purchaseDate: new Date(),
          pricePaid: totalPrice,
          availableCount: totalCount,
          usedCount: 0,
          transactionId: transactionId || `MANUAL-${Date.now()}`
        }
      });
    }

    // TODO: In a real implementation, you would integrate with a payment gateway
    // and only create the purchase record after successful payment

    return res.status(201).json({
      success: true,
      message: 'Spotlight purchased successfully',
      userSpotlight: {
        ...userSpotlight,
        spotlight: {
          name: spotlight.name
        },
        availableCount: userSpotlight.availableCount,
        usedCount: userSpotlight.usedCount
      }
    });
  } catch (error) {
    console.error('Error purchasing spotlight:', error);
    return res.status(500).json({ success: false, message: 'Failed to purchase spotlight' });
  }
}
