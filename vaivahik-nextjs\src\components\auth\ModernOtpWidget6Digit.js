import React, { useState, useRef, useEffect } from 'react';
import { Box, Typography, TextField, Button, Paper, Grid, CircularProgress } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  borderRadius: 16,
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
  position: 'relative',
  overflow: 'hidden',
  background: 'linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%)',
}));

const OtpInput = styled(TextField)(({ theme }) => ({
  width: '40px',
  '& input': {
    textAlign: 'center',
    fontSize: '1.2rem',
    padding: '12px 0',
  },
}));

/**
 * Modern 6-digit OTP input widget
 * 
 * @param {Object} props - Component props
 * @param {string} props.phone - Phone number the OTP was sent to
 * @param {Function} props.onVerify - Function to call when verifying OTP
 * @param {Function} props.onResendOtp - Function to call when resending OTP
 * @param {Function} props.onChangePhone - Function to call when changing phone number
 * @param {boolean} props.loading - Whether verification is in progress
 * @param {string} props.error - Error message to display
 * @param {string} props.success - Success message to display
 */
const ModernOtpWidget6Digit = ({ 
  phone, 
  onVerify, 
  onResendOtp, 
  onChangePhone,
  loading = false,
  error = '',
  success = ''
}) => {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [resendTimer, setResendTimer] = useState(30);
  const [resendCount, setResendCount] = useState(0);
  const [resendLoading, setResendLoading] = useState(false);
  const MAX_RESEND_ATTEMPTS = 3;
  const inputRefs = useRef([]);

  // Initialize timer when component mounts
  useEffect(() => {
    setResendTimer(30); // 30 seconds cooldown for resend
  }, []);

  // Timer countdown
  useEffect(() => {
    let interval;
    if (resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer((prev) => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [resendTimer]);

  // Handle input change
  const handleChange = (index, value) => {
    // Only allow digits
    if (value && !/^\d+$/.test(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1].focus();
    }

    // Auto-submit when all digits are entered
    if (value && index === 5 && newOtp.every(digit => digit)) {
      handleVerify();
    }
  };

  // Handle key press
  const handleKeyDown = (index, e) => {
    // Move to previous input on backspace
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1].focus();
    }
  };

  // Handle paste
  const handlePaste = (e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');
    
    // Check if pasted content is a 6-digit number
    if (/^\d{6}$/.test(pastedData)) {
      const digits = pastedData.split('');
      setOtp(digits);
      
      // Focus the last input
      inputRefs.current[5].focus();
    }
  };

  // Handle OTP verification
  const handleVerify = () => {
    const otpString = otp.join('');
    if (otpString.length === 6) {
      onVerify(otpString);
    }
  };

  // Handle OTP resend
  const handleResend = async () => {
    if (resendTimer > 0 || resendCount >= MAX_RESEND_ATTEMPTS) return;
    
    setResendLoading(true);
    
    try {
      await onResendOtp();
      setResendCount(prev => prev + 1);
      setResendTimer(30); // Reset timer
    } finally {
      setResendLoading(false);
    }
  };

  // Format phone number for display
  const formatPhoneForDisplay = (phone) => {
    if (!phone) return '';
    
    // Remove any non-digit characters
    const cleaned = phone.replace(/\D/g, '');
    
    // Format based on length
    if (cleaned.length === 10) {
      return `+91 ${cleaned.substring(0, 5)} ${cleaned.substring(5)}`;
    } else if (cleaned.length > 10) {
      // Assume country code is present
      const countryCode = cleaned.substring(0, cleaned.length - 10);
      const number = cleaned.substring(cleaned.length - 10);
      return `+${countryCode} ${number.substring(0, 5)} ${number.substring(5)}`;
    }
    
    return phone;
  };

  return (
    <StyledPaper>
      <Box sx={{ textAlign: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2" fontWeight="bold" gutterBottom>
          Verify Your Phone
        </Typography>
        <Typography variant="body1" color="text.secondary">
          We've sent a 6-digit OTP to {formatPhoneForDisplay(phone)}
        </Typography>
        <Button 
          variant="text" 
          size="small" 
          onClick={onChangePhone}
          sx={{ mt: 1 }}
        >
          Change Phone Number
        </Button>
      </Box>

      {/* OTP Input Fields */}
      <Box sx={{ mb: 3 }}>
        <Grid container spacing={1} justifyContent="center">
          {otp.map((digit, index) => (
            <Grid item key={index}>
              <OtpInput
                inputRef={(el) => (inputRefs.current[index] = el)}
                value={digit}
                onChange={(e) => handleChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                onPaste={index === 0 ? handlePaste : null}
                inputProps={{ maxLength: 1 }}
                autoFocus={index === 0}
              />
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Error and Success Messages */}
      {error && (
        <Typography color="error" variant="body2" textAlign="center" sx={{ mb: 2 }}>
          {error}
        </Typography>
      )}
      
      {success && (
        <Typography color="success.main" variant="body2" textAlign="center" sx={{ mb: 2 }}>
          {success}
        </Typography>
      )}

      {/* Verify Button */}
      <Box sx={{ mb: 2 }}>
        <Button
          variant="contained"
          fullWidth
          size="large"
          onClick={handleVerify}
          disabled={loading || otp.some(digit => !digit)}
          sx={{ 
            borderRadius: 2,
            py: 1.5,
            background: 'linear-gradient(90deg, #1976d2 0%, #2196f3 100%)',
            '&:hover': {
              background: 'linear-gradient(90deg, #1565c0 0%, #1976d2 100%)',
            }
          }}
        >
          {loading ? <CircularProgress size={24} color="inherit" /> : 'Verify OTP'}
        </Button>
      </Box>

      {/* Resend OTP */}
      <Box sx={{ textAlign: 'center' }}>
        {resendCount < MAX_RESEND_ATTEMPTS ? (
          <Typography variant="body2" color="text.secondary">
            {resendTimer > 0 ? (
              <>Didn't receive the OTP? Resend in <b>{resendTimer}s</b></>
            ) : (
              <Button
                variant="text"
                onClick={handleResend}
                disabled={resendLoading}
                sx={{ textTransform: 'none' }}
              >
                {resendLoading ? <CircularProgress size={16} color="inherit" /> : 'Resend OTP'}
              </Button>
            )}
          </Typography>
        ) : (
          <Typography variant="body2" color="error">
            Maximum resend attempts reached. Please try again later.
          </Typography>
        )}
        
        {resendCount > 0 && resendCount < MAX_RESEND_ATTEMPTS && (
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            {MAX_RESEND_ATTEMPTS - resendCount} resend attempts remaining
          </Typography>
        )}
      </Box>

      {/* OTP Expiry Notice */}
      <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block', textAlign: 'center' }}>
        This OTP will expire in 15 minutes
      </Typography>
    </StyledPaper>
  );
};

export default ModernOtpWidget6Digit;
