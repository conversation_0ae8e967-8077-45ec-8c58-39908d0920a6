/**
 * Gamification utility functions for encouraging profile completion
 */

// Calculate profile completion percentage
export const calculateProfileCompletion = (userData) => {
  if (!userData) return 0;

  // Define all fields that contribute to profile completion
  const sections = {
    basic: {
      fields: ['fullName', 'gender', 'dateOfBirth', 'email', 'phone', 'maritalStatus', 'height', 'bloodGroup', 'religion', 'caste', 'subCaste'],
      weight: 20, // 20% of total
      required: ['fullName', 'gender', 'dateOfBirth', 'phone']
    },
    education: {
      fields: ['education', 'educationField', 'occupation', 'workingWith', 'incomeRange'],
      weight: 15, // 15% of total
      required: ['education', 'occupation']
    },
    location: {
      fields: ['city', 'state', 'pincode', 'birthPlace', 'birthTime', 'gotra'],
      weight: 15, // 15% of total
      required: ['city', 'state']
    },
    photos: {
      fields: ['profilePhoto'],
      weight: 10, // 10% of total
      required: ['profilePhoto']
    },
    about: {
      fields: ['aboutMe'],
      weight: 5, // 5% of total
      required: []
    },
    family: {
      fields: ['familyType', 'familyStatus', 'fatherName', 'fatherOccupation', 'motherName', 'motherOccupation', 'siblings', 'uncleName', 'familyContact', 'motherTongue', 'marathiProficiency', 'kul', 'maharashtrianOrigin', 'nativePlace', 'nativeDistrict'],
      weight: 15, // 15% of total
      required: []
    },
    lifestyle: {
      fields: ['diet', 'smoking', 'drinking', 'hobbies', 'interests'],
      weight: 10, // 10% of total
      required: ['diet']
    },
    preferences: {
      fields: ['ageMin', 'ageMax', 'heightMin', 'heightMax', 'educationLevel', 'occupations', 'incomeMin', 'preferredCities', 'preferredStates', 'acceptSubCastes', 'gotraPreference', 'dietPreference', 'hobbiesPreference', 'interestsPreference', 'otherPreferences'],
      weight: 10, // 10% of total
      required: []
    }
  };

  // Calculate completion for each section
  const sectionCompletions = {};
  let totalCompletion = 0;

  Object.entries(sections).forEach(([sectionName, sectionConfig]) => {
    const { fields, weight, required } = sectionConfig;

    // Count completed fields
    let completedFields = 0;
    let totalFields = fields.length;

    fields.forEach(field => {
      // Handle nested fields (e.g., preferences.ageMin)
      if (field.includes('.')) {
        const [parent, child] = field.split('.');
        if (userData[parent] && userData[parent][child]) {
          completedFields++;
        }
      }
      // Handle array fields
      else if (Array.isArray(userData[field])) {
        if (userData[field].length > 0) {
          completedFields++;
        }
      }
      // Handle regular fields
      else if (userData[field]) {
        completedFields++;
      }
    });

    // Calculate section completion percentage
    const sectionPercentage = totalFields > 0 ? (completedFields / totalFields) * 100 : 0;
    sectionCompletions[sectionName] = Math.round(sectionPercentage);

    // Add weighted contribution to total completion
    totalCompletion += (sectionPercentage * weight) / 100;
  });

  return {
    overall: Math.round(totalCompletion),
    sections: sectionCompletions
  };
};

// Get profile level based on completion percentage
export const getProfileLevel = (completionPercentage) => {
  if (completionPercentage >= 80) return 'Premium';
  if (completionPercentage >= 60) return 'Advanced';
  if (completionPercentage >= 40) return 'Standard';
  return 'Basic';
};

// Get profile visibility percentage based on completion
export const getVisibilityPercentage = (completionPercentage) => {
  return Math.min(100, completionPercentage + 20);
};

// Get earned badges based on completion percentage
export const getEarnedBadges = (completionPercentage) => {
  const badges = [];
  if (completionPercentage >= 25) badges.push('bronze');
  if (completionPercentage >= 50) badges.push('silver');
  if (completionPercentage >= 75) badges.push('gold');
  if (completionPercentage === 100) badges.push('platinum');
  return badges;
};

// Get locked features based on completion percentage
export const getLockedFeatures = (completionPercentage) => {
  const locked = [];
  if (completionPercentage < 40) locked.push('advanced_search');
  if (completionPercentage < 60) locked.push('contact_info');
  if (completionPercentage < 80) locked.push('spotlight');
  return locked;
};

// Get unlocked features based on completion percentage
export const getUnlockedFeatures = (completionPercentage) => {
  const unlocked = ['basic_search', 'basic_matching'];
  if (completionPercentage >= 40) unlocked.push('advanced_search');
  if (completionPercentage >= 60) unlocked.push('contact_info');
  if (completionPercentage >= 80) unlocked.push('spotlight');
  if (completionPercentage >= 100) unlocked.push('premium_matching');
  return unlocked;
};

// Get next milestone based on completion percentage
export const getNextMilestone = (completionPercentage) => {
  if (completionPercentage < 40) {
    return {
      name: 'Advanced Search Visibility',
      description: 'Your profile appears in advanced search results',
      requiredPercentage: 40,
      remaining: 40 - completionPercentage
    };
  }
  if (completionPercentage < 60) {
    return {
      name: 'Contact Information Sharing',
      description: 'Share contact details with interested matches',
      requiredPercentage: 60,
      remaining: 60 - completionPercentage
    };
  }
  if (completionPercentage < 80) {
    return {
      name: 'Spotlight Feature Eligibility',
      description: 'Highlight your profile to get more attention',
      requiredPercentage: 80,
      remaining: 80 - completionPercentage
    };
  }
  if (completionPercentage < 100) {
    return {
      name: 'Premium Match Algorithm',
      description: 'Get highest quality matches based on compatibility',
      requiredPercentage: 100,
      remaining: 100 - completionPercentage
    };
  }
  return null;
};

// Get recommendations for improving profile
export const getProfileRecommendations = (userData) => {
  const { overall, sections } = calculateProfileCompletion(userData);

  // Sort sections by completion percentage (ascending)
  const sortedSections = Object.entries(sections)
    .sort(([, a], [, b]) => a - b)
    .map(([name, percentage]) => ({ name, percentage }));

  // Get the 3 least complete sections
  const leastCompleteSections = sortedSections.slice(0, 3);

  // Generate recommendations
  const recommendations = leastCompleteSections.map(section => {
    switch (section.name) {
      case 'basic':
        return {
          section: 'Basic Details',
          message: 'Complete your basic details to appear in more search results',
          route: '/profile/edit/basic'
        };
      case 'photos':
        return {
          section: 'Profile Photos',
          message: 'Add a profile photo to get 10x more responses',
          route: '/profile/edit/photos'
        };
      case 'family':
        return {
          section: 'Family Details',
          message: 'Add family details to improve match quality by 40%',
          route: '/profile/edit/family'
        };
      case 'education':
        return {
          section: 'Education & Career',
          message: 'Complete your education and career details to match with compatible professionals',
          route: '/profile/edit/education'
        };
      case 'location':
        return {
          section: 'Location Details',
          message: 'Add your location details to find matches in your preferred locations',
          route: '/profile/edit/location'
        };
      case 'preferences':
        return {
          section: 'Partner Preferences',
          message: 'Specify your partner preferences to receive more relevant match suggestions',
          route: '/website/pages/profile/edit/preferences'
        };
      case 'lifestyle':
        return {
          section: 'Lifestyle & Habits',
          message: 'Share your lifestyle preferences to find compatible partners',
          route: '/website/pages/profile/edit/lifestyle'
        };
      case 'about':
        return {
          section: 'About Me',
          message: 'Write about yourself to express your personality and attract better matches',
          route: '/profile/edit/about'
        };
      default:
        return null;
    }
  }).filter(Boolean);

  return recommendations;
};
