// src/routes/admin/index.js

const express = require('express');
const router = express.Router();

// Import admin route modules
const photoModerationRoutes = require('./photoModeration.routes');
const promotionsRoutes = require('./promotions.routes');
const textModerationRoutes = require('./textModeration.routes');
const chatSettingsRoutes = require('./chatSettings.routes');
const biodataRoutes = require('./biodata.routes');
const spotlightRoutes = require('./spotlight.routes');
const phasesRoutes = require('./phases');
const dataAnalyticsRoutes = require('./data-analytics');
const blogRoutes = require('./blog.routes');
const successStoriesRoutes = require('./successStories.routes');
const subscriptionsRoutes = require('./subscriptions.routes');
const transactionsRoutes = require('./transactions.routes');
// Import other admin routes as needed

// Mount admin route modules
router.use('/photo-moderation', photoModerationRoutes);
router.use('/promotions', promotionsRoutes);
router.use('/text-moderation', textModerationRoutes);
router.use('/chat-settings', chatSettingsRoutes);
router.use('/biodata-templates', biodataRoutes); // Fixed path to match frontend expectations
router.use('/spotlight', spotlightRoutes);
router.use('/phases', phasesRoutes);
router.use('/data-analytics', dataAnalyticsRoutes);
router.use('/blog-posts', blogRoutes); // Mount blog routes
router.use('/success-stories', successStoriesRoutes); // Mount success stories routes
router.use('/subscriptions', subscriptionsRoutes); // Mount subscription routes
router.use('/transactions', transactionsRoutes); // Mount transaction routes
// Mount other admin routes as needed

module.exports = router;
