// prisma/seed-features.js

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

exports.main = async function() {
    console.log('Starting to seed features and access rules...');

    // Define initial features
    const features = [
        {
            name: 'browse',
            displayName: 'Browse Profiles',
            description: 'Browse through profiles of potential matches',
            category: 'BASIC',
            isActive: true
        },
        {
            name: 'search',
            displayName: 'Search Profiles',
            description: 'Search for profiles using filters',
            category: 'BASIC',
            isActive: true
        },
        {
            name: 'view-profile',
            displayName: 'View Profile Details',
            description: 'View detailed information about a profile',
            category: 'BASIC',
            isActive: true
        },
        {
            name: 'edit-profile',
            displayName: 'Edit Profile',
            description: 'Edit your own profile information',
            category: 'BASIC',
            isActive: true
        },
        {
            name: 'upload-photo',
            displayName: 'Upload Photos',
            description: 'Upload photos to your profile',
            category: 'BASIC',
            isActive: true
        },
        {
            name: 'matches',
            displayName: 'View Matches',
            description: 'View profiles that match your preferences',
            category: 'MATCHING',
            isActive: true
        },
        {
            name: 'connections',
            displayName: 'Manage Connections',
            description: 'View and manage your connections',
            category: 'COMMUNICATION',
            isActive: true
        },
        {
            name: 'send-message',
            displayName: 'Send Messages',
            description: 'Send messages to other users',
            category: 'COMMUNICATION',
            isActive: true
        },
        {
            name: 'receive-message',
            displayName: 'Receive Messages',
            description: 'Receive messages from other users',
            category: 'COMMUNICATION',
            isActive: true
        },
        {
            name: 'advanced-search',
            displayName: 'Advanced Search',
            description: 'Use advanced filters to find your perfect match',
            category: 'PREMIUM',
            isActive: true
        },
        {
            name: 'contact-details',
            displayName: 'View Contact Details',
            description: 'View contact information of other users',
            category: 'PREMIUM',
            isActive: true
        },
        {
            name: 'priority-matching',
            displayName: 'Priority Matching',
            description: 'Get matched with the most compatible profiles first',
            category: 'PREMIUM',
            isActive: true
        },
        {
            name: 'horoscope-matching',
            displayName: 'Horoscope Matching',
            description: 'View detailed horoscope compatibility',
            category: 'PREMIUM',
            isActive: true
        },
        {
            name: 'profile-boost',
            displayName: 'Profile Boost',
            description: 'Boost your profile visibility',
            category: 'PREMIUM',
            isActive: true
        },
        {
            name: 'incognito-browsing',
            displayName: 'Incognito Browsing',
            description: 'Browse profiles without appearing in their visitor list',
            category: 'PREMIUM',
            isActive: true
        }
    ];

    // Define subscription plans
    const subscriptionPlans = [
        {
            name: 'Monthly Premium',
            planType: 'PREMIUM',
            price: 999,
            currency: 'INR',
            duration: 30,
            description: 'Premium features for 30 days',
            features: JSON.stringify([
                'Unlimited messaging',
                'View contact details',
                'Advanced search filters',
                'Priority in search results'
            ]),
            isActive: true
        },
        {
            name: 'Quarterly Premium',
            planType: 'PREMIUM',
            price: 2499,
            currency: 'INR',
            duration: 90,
            description: 'Premium features for 90 days with 17% savings',
            features: JSON.stringify([
                'All Monthly features',
                'Profile boost every month',
                '17% savings compared to monthly'
            ]),
            isActive: true
        },
        {
            name: 'Annual Premium',
            planType: 'PREMIUM',
            price: 7999,
            currency: 'INR',
            duration: 365,
            description: 'Premium features for 365 days with 33% savings',
            features: JSON.stringify([
                'All Quarterly features',
                'Dedicated relationship manager',
                '33% savings compared to monthly'
            ]),
            isActive: true
        }
    ];

    // Create subscription plans
    for (const plan of subscriptionPlans) {
        const existingPlan = await prisma.subscriptionPlan.findFirst({
            where: { name: plan.name }
        });

        if (!existingPlan) {
            await prisma.subscriptionPlan.create({
                data: plan
            });
            console.log(`Created subscription plan: ${plan.name}`);
        } else {
            console.log(`Subscription plan already exists: ${plan.name}`);
        }
    }

    // Create features and access rules
    for (const feature of features) {
        // Check if feature already exists
        const existingFeature = await prisma.feature.findUnique({
            where: { name: feature.name }
        });

        let featureId;

        if (!existingFeature) {
            // Create the feature
            const createdFeature = await prisma.feature.create({
                data: feature
            });

            featureId = createdFeature.id;
            console.log(`Created feature: ${feature.name}`);
        } else {
            featureId = existingFeature.id;
            console.log(`Feature already exists: ${feature.name}`);
        }

        // Define access rules based on feature
        const accessRules = [];

        // Basic tier access rules
        accessRules.push({
            featureId,
            userTier: 'BASIC',
            isEnabled: ['browse', 'search', 'view-profile', 'edit-profile', 'upload-photo', 'receive-message'].includes(feature.name),
            dailyLimit: getBasicLimit(feature.name),
            limitPeriod: getBasicLimit(feature.name) ? 'DAILY' : null,
            allowedFilters: feature.name === 'search' ? JSON.stringify(['age', 'gender', 'city']) : null,
            upgradeMessage: getUpgradeMessage(feature.name, 'BASIC')
        });

        // Verified tier access rules
        accessRules.push({
            featureId,
            userTier: 'VERIFIED',
            isEnabled: !['advanced-search', 'contact-details', 'priority-matching', 'horoscope-matching', 'profile-boost', 'incognito-browsing'].includes(feature.name),
            dailyLimit: getVerifiedLimit(feature.name),
            limitPeriod: getVerifiedLimit(feature.name) ? (feature.name === 'send-message' ? 'TOTAL' : 'DAILY') : null,
            allowedFilters: feature.name === 'search' ? JSON.stringify(['age', 'gender', 'city', 'education']) : null,
            upgradeMessage: getUpgradeMessage(feature.name, 'VERIFIED')
        });

        // Premium tier access rules
        accessRules.push({
            featureId,
            userTier: 'PREMIUM',
            isEnabled: true,
            dailyLimit: null,
            limitPeriod: null,
            allowedFilters: feature.name === 'search' ? JSON.stringify(['age', 'gender', 'city', 'education', 'income', 'occupation', 'religion', 'caste', 'subcaste', 'maritalStatus', 'height', 'diet']) : null
        });

        // Create access rules
        for (const rule of accessRules) {
            // Check if rule already exists
            const existingRule = await prisma.featureAccess.findFirst({
                where: {
                    featureId: rule.featureId,
                    userTier: rule.userTier,
                    subscriptionPlanId: null
                }
            });

            if (!existingRule) {
                await prisma.featureAccess.create({
                    data: rule
                });
                console.log(`Created access rule: ${feature.name} - ${rule.userTier}`);
            } else {
                console.log(`Access rule already exists: ${feature.name} - ${rule.userTier}`);
            }
        }
    }

    // Create system configuration
    const verificationBenefits = [
        "Verification badge on your profile",
        "Send up to 5 messages",
        "Connect with up to 5 profiles",
        "View up to 10 matches per day"
    ];

    const existingConfig = await prisma.systemConfig.findUnique({
        where: { configKey: 'VERIFICATION_BENEFITS' }
    });

    if (!existingConfig) {
        await prisma.systemConfig.create({
            data: {
                configKey: 'VERIFICATION_BENEFITS',
                configValue: JSON.stringify(verificationBenefits),
                description: 'Benefits shown to users when encouraging verification'
            }
        });
        console.log('Created system configuration: VERIFICATION_BENEFITS');
    } else {
        console.log('System configuration already exists: VERIFICATION_BENEFITS');
    }

    console.log('Seeding completed successfully!');
}

// Helper function to get limit for basic tier
function getBasicLimit(featureName) {
    switch (featureName) {
        case 'browse':
            return 10;
        case 'view-profile':
            return 5;
        case 'upload-photo':
            return 3;
        default:
            return null;
    }
}

// Helper function to get limit for verified tier
function getVerifiedLimit(featureName) {
    switch (featureName) {
        case 'browse':
            return 30;
        case 'view-profile':
            return 15;
        case 'upload-photo':
            return 6;
        case 'matches':
            return 10;
        case 'connections':
            return 5;
        case 'send-message':
            return 5;
        default:
            return null;
    }
}

// Helper function to get upgrade message
function getUpgradeMessage(featureName, tier) {
    if (tier === 'BASIC') {
        switch (featureName) {
            case 'matches':
                return 'Verify your profile to view matches.';
            case 'connections':
                return 'Verify your profile to connect with other users.';
            case 'send-message':
                return 'Verify your profile to send messages.';
            default:
                return null;
        }
    } else if (tier === 'VERIFIED') {
        switch (featureName) {
            case 'advanced-search':
                return 'Advanced search helps you find your perfect match faster. Upgrade to Premium to access all filters.';
            case 'contact-details':
                return 'Contact details are available only for Premium members. Upgrade now to connect directly.';
            case 'priority-matching':
                return 'Get matched with the most compatible profiles first. Upgrade to Premium for priority matching.';
            case 'horoscope-matching':
                return 'Detailed horoscope compatibility is a Premium feature. Upgrade now to see your astrological compatibility.';
            case 'profile-boost':
                return 'Get 5x more profile views with Profile Boost. Upgrade to Premium to stand out from the crowd.';
            case 'incognito-browsing':
                return 'Browse profiles without appearing in their visitor list. Upgrade to Premium for incognito browsing.';
            case 'send-message':
                return "You've used all your free messages. Upgrade to Premium for unlimited messaging.";
            case 'matches':
                return "You've reached your daily match limit. Upgrade to Premium for unlimited matches.";
            case 'connections':
                return "You've reached your connection limit. Upgrade to Premium for unlimited connections.";
            default:
                return null;
        }
    }

    return null;
}

// Run the main function if this script is executed directly
if (require.main === module) {
    exports.main()
        .catch((e) => {
            console.error(e);
            process.exit(1);
        })
        .finally(async () => {
            await prisma.$disconnect();
        });
}
