/**
 * Razorpay Payment Service
 * Handles all payment operations for Vaivahik matrimony app
 */

const Razorpay = require('razorpay');
const crypto = require('crypto');
const { PrismaClient } = require('@prisma/client');
const autoTriggers = require('../notification/auto-notification-triggers');

const prisma = new PrismaClient();

// Initialize Razorpay instance
const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID,
  key_secret: process.env.RAZORPAY_KEY_SECRET,
});

/**
 * Create a payment order for subscription
 */
const createSubscriptionOrder = async (userId, planType, planDuration) => {
  try {
    // Define subscription plans
    const plans = {
      PREMIUM: {
        monthly: { amount: 99900, name: 'Premium Monthly' }, // ₹999
        quarterly: { amount: 249900, name: 'Premium Quarterly' }, // ₹2499
        annual: { amount: 799900, name: 'Premium Annual' } // ₹7999
      }
    };

    const plan = plans[planType]?.[planDuration];
    if (!plan) {
      throw new Error('Invalid plan type or duration');
    }

    // Create Razorpay order
    const orderOptions = {
      amount: plan.amount, // Amount in paise
      currency: 'INR',
      receipt: `sub_${userId.slice(-8)}_${Date.now().toString().slice(-8)}`,
      notes: {
        userId,
        planType,
        planDuration,
        planName: plan.name
      }
    };

    const order = await razorpay.orders.create(orderOptions);

    // Store order in database
    const paymentOrder = await prisma.paymentOrder.create({
      data: {
        orderId: order.id,
        userId,
        amount: plan.amount / 100, // Store in rupees
        currency: 'INR',
        status: 'CREATED',
        planType,
        planDuration,
        razorpayOrderId: order.id,
        receipt: order.receipt
      }
    });

    return {
      success: true,
      order: {
        id: order.id,
        amount: order.amount,
        currency: order.currency,
        receipt: order.receipt,
        planName: plan.name
      },
      paymentOrderId: paymentOrder.id
    };
  } catch (error) {
    console.error('Error creating subscription order:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Verify payment signature
 */
const verifyPaymentSignature = (orderId, paymentId, signature) => {
  try {
    const body = orderId + '|' + paymentId;
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
      .update(body.toString())
      .digest('hex');

    return expectedSignature === signature;
  } catch (error) {
    console.error('Error verifying payment signature:', error);
    return false;
  }
};

/**
 * Process successful payment
 */
const processSuccessfulPayment = async (paymentData) => {
  try {
    const { orderId, paymentId, signature, userId } = paymentData;

    // Verify signature
    if (!verifyPaymentSignature(orderId, paymentId, signature)) {
      throw new Error('Invalid payment signature');
    }

    // Get payment order from database
    const paymentOrder = await prisma.paymentOrder.findUnique({
      where: { razorpayOrderId: orderId }
    });

    if (!paymentOrder) {
      throw new Error('Payment order not found');
    }

    // Update payment order status
    await prisma.paymentOrder.update({
      where: { id: paymentOrder.id },
      data: {
        status: 'COMPLETED',
        razorpayPaymentId: paymentId,
        razorpaySignature: signature,
        completedAt: new Date()
      }
    });

    // Calculate subscription end date
    const startDate = new Date();
    let endDate = new Date();

    switch (paymentOrder.planDuration) {
      case 'monthly':
        endDate.setMonth(endDate.getMonth() + 1);
        break;
      case 'quarterly':
        endDate.setMonth(endDate.getMonth() + 3);
        break;
      case 'annual':
        endDate.setFullYear(endDate.getFullYear() + 1);
        break;
    }

    // Create or update subscription
    const subscription = await prisma.subscription.upsert({
      where: { userId: paymentOrder.userId },
      update: {
        planType: paymentOrder.planType,
        planDuration: paymentOrder.planDuration,
        status: 'ACTIVE',
        startDate,
        endDate,
        autoRenew: true,
        paymentOrderId: paymentOrder.id
      },
      create: {
        userId: paymentOrder.userId,
        planType: paymentOrder.planType,
        planDuration: paymentOrder.planDuration,
        status: 'ACTIVE',
        startDate,
        endDate,
        autoRenew: true,
        paymentOrderId: paymentOrder.id
      }
    });

    // Update user premium status
    await prisma.user.update({
      where: { id: paymentOrder.userId },
      data: { isPremium: true }
    });

    // Trigger subscription notification
    await autoTriggers.onSubscriptionChange(paymentOrder.userId, {
      status: 'ACTIVE',
      planType: paymentOrder.planType,
      planDuration: paymentOrder.planDuration,
      endDate
    });

    return {
      success: true,
      subscription,
      message: 'Payment processed successfully'
    };
  } catch (error) {
    console.error('Error processing successful payment:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Handle failed payment
 */
const processFailedPayment = async (orderId, reason) => {
  try {
    // Update payment order status
    await prisma.paymentOrder.updateMany({
      where: { razorpayOrderId: orderId },
      data: {
        status: 'FAILED',
        failureReason: reason,
        completedAt: new Date()
      }
    });

    return { success: true, message: 'Failed payment recorded' };
  } catch (error) {
    console.error('Error processing failed payment:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Create order for one-time features
 */
const createFeatureOrder = async (userId, featureType, quantity = 1) => {
  try {
    // Define feature prices
    const features = {
      PROFILE_BOOST: { amount: 19900, name: 'Profile Boost' }, // ₹199
      SUPER_LIKES: { amount: 9900, name: 'Super Likes (10 pack)' }, // ₹99
      CONTACT_REVEAL: { amount: 4900, name: 'Contact Details Reveal' }, // ₹49
      HOROSCOPE_MATCH: { amount: 29900, name: 'Horoscope Matching' }, // ₹299
      BACKGROUND_VERIFY: { amount: 99900, name: 'Background Verification' } // ₹999
    };

    const feature = features[featureType];
    if (!feature) {
      throw new Error('Invalid feature type');
    }

    const totalAmount = feature.amount * quantity;

    // Create Razorpay order
    const orderOptions = {
      amount: totalAmount,
      currency: 'INR',
      receipt: `feat_${userId.slice(-8)}_${Date.now().toString().slice(-8)}`,
      notes: {
        userId,
        featureType,
        quantity,
        featureName: feature.name
      }
    };

    const order = await razorpay.orders.create(orderOptions);

    // Store order in database
    const paymentOrder = await prisma.paymentOrder.create({
      data: {
        orderId: order.id,
        userId,
        amount: totalAmount / 100,
        currency: 'INR',
        status: 'CREATED',
        featureType,
        quantity,
        razorpayOrderId: order.id,
        receipt: order.receipt
      }
    });

    return {
      success: true,
      order: {
        id: order.id,
        amount: order.amount,
        currency: order.currency,
        receipt: order.receipt,
        featureName: feature.name
      },
      paymentOrderId: paymentOrder.id
    };
  } catch (error) {
    console.error('Error creating feature order:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get user's payment history
 */
const getPaymentHistory = async (userId, limit = 10, offset = 0) => {
  try {
    const payments = await prisma.paymentOrder.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
      include: {
        subscription: true
      }
    });

    return {
      success: true,
      payments: payments.map(payment => ({
        id: payment.id,
        amount: payment.amount,
        currency: payment.currency,
        status: payment.status,
        planType: payment.planType,
        planDuration: payment.planDuration,
        featureType: payment.featureType,
        createdAt: payment.createdAt,
        completedAt: payment.completedAt
      }))
    };
  } catch (error) {
    console.error('Error getting payment history:', error);
    return { success: false, error: error.message };
  }
};

module.exports = {
  createSubscriptionOrder,
  createFeatureOrder,
  verifyPaymentSignature,
  processSuccessfulPayment,
  processFailedPayment,
  getPaymentHistory
};
