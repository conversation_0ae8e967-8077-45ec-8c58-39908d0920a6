/**
 * <PERSON><PERSON><PERSON> to list all mock data files
 */

const fs = require('fs');
const path = require('path');

// Base directory for mock data
const MOCK_DATA_DIR = path.join(__dirname, '..', 'public', 'mock-data');

// Function to recursively scan a directory
function scanDirectory(dir, fileList = []) {
  try {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        scanDirectory(filePath, fileList);
      } else if (stat.isFile() && file.endsWith('.json')) {
        // Get the relative path from the mock-data directory
        const relativePath = path.relative(MOCK_DATA_DIR, filePath);
        fileList.push(relativePath);
      }
    });
  } catch (error) {
    console.error(`Error scanning directory ${dir}:`, error);
  }
  
  return fileList;
}

// Main function
function main() {
  console.log('Scanning mock data directory...');
  console.log(`Base directory: ${MOCK_DATA_DIR}`);
  
  try {
    // Check if the directory exists
    if (!fs.existsSync(MOCK_DATA_DIR)) {
      console.error(`Directory does not exist: ${MOCK_DATA_DIR}`);
      return;
    }
    
    const files = scanDirectory(MOCK_DATA_DIR);
    
    console.log(`\nFound ${files.length} mock data files:`);
    files.forEach(file => {
      console.log(`  ${file}`);
    });
  } catch (error) {
    console.error('Error in main function:', error);
  }
}

// Run the script
main();
