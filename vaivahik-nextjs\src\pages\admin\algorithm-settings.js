import { useState, useEffect } from 'react';
import EnhancedAdminLayout from '@/components/admin/EnhancedAdminLayout';
import { adminGet, adminPut, adminPost } from '@/services/apiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';
import mcpClient from '@/services/mcpClient';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  Button,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Switch,
  FormControlLabel,
  IconButton,
  Tooltip,
  Snackbar,
  Chip
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import SaveIcon from '@mui/icons-material/Save';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import BarChartIcon from '@mui/icons-material/BarChart';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import SettingsIcon from '@mui/icons-material/Settings';
import SmartToyIcon from '@mui/icons-material/SmartToy';

export default function AlgorithmSettings() {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [mcpStatus, setMcpStatus] = useState({ isConnected: false, isRunning: false });
  const [aiTestResults, setAiTestResults] = useState(null);
  const [testingAI, setTestingAI] = useState(false);
  const [settings, setSettings] = useState({
    // General settings
    matchingAlgorithmVersion: 'v1.0',
    enableAIMatching: true,
    matchingModel: 'TWO_TOWER', // TWO_TOWER, CONTENT_BASED, HYBRID

    // Weights
    weights: {
      ageWeight: 8,
      heightWeight: 6,
      educationWeight: 7,
      occupationWeight: 7,
      locationWeight: 8,
      casteWeight: 9,
      subCasteWeight: 5,
      gotraWeight: 6,
      incomeWeight: 5,
      lifestyleWeight: 4
    },

    // Thresholds
    minimumMatchScore: 65,
    highQualityMatchThreshold: 80,

    // A/B Testing
    abTestingEnabled: false,
    abTestingVariant: 'A',
    abTestingDistribution: 50, // percentage for variant B

    // Advanced settings
    maxDistanceKm: 100,
    maxAgeDifference: 10,
    considerUserActivity: true,
    boostNewProfiles: true,
    boostNewProfilesDays: 7,
    boostVerifiedProfiles: true,
    boostVerifiedProfilesAmount: 10, // percentage
    boostPremiumProfiles: true,
    boostPremiumProfilesAmount: 15, // percentage,

    // Two-Tower Model settings
    twoTower: {
      embeddingSize: 128,
      learningRate: 0.001,
      batchSize: 64,
      epochs: 10,
      userTowerLayers: [128, 64],
      matchTowerLayers: [128, 64],
      dropoutRate: 0.2,
      similarityMetric: 'cosine' // cosine, dot, euclidean
    },

    // Phase-specific settings
    phaseSettings: {
      // Phase 1: Flexibility Enhancements
      flexibility: {
        enabled: false,
        defaultFlexibilityLevel: 'MODERATE',
        allowUserFlexibilityControl: true,
        religionFlexibilityEnabled: false,
        casteFlexibilityEnabled: false,
        ageFlexibilityRange: 5,
        heightFlexibilityRange: 0.5
      },

      // Phase 2: Personalization
      personalization: {
        enabled: false,
        behavioralLearningEnabled: false,
        dynamicPreferencesEnabled: false,
        userAdaptationEnabled: false,
        minimumInteractionsForLearning: 10
      },

      // Phase 3: Intelligent Features
      intelligentFeatures: {
        enabled: false,
        smartExplanationsEnabled: false,
        predictiveScoringEnabled: false,
        aiRecommendationsEnabled: false,
        conversationAnalysisEnabled: false
      },

      // Phase 4: Advanced AI
      advancedAI: {
        enabled: false,
        multiModalLearningEnabled: false,
        graphNetworksEnabled: false,
        reinforcementLearningEnabled: false,
        realTimeAdaptationEnabled: false
      }
    }
  });

  const [abTestResults, setAbTestResults] = useState({
    variantA: {
      matches: 245,
      conversations: 156,
      successRate: 63.7
    },
    variantB: {
      matches: 267,
      conversations: 182,
      successRate: 68.2
    }
  });

  const [metrics, setMetrics] = useState({
    totalMatches: 5842,
    successfulMatches: 2156,
    averageMatchScore: 72.4,
    matchDistribution: [12, 18, 25, 30, 15],
    monthlyTrend: [120, 145, 160, 178, 195, 210]
  });

  useEffect(() => {
    // Fetch algorithm settings
    const fetchSettings = async () => {
      setLoading(true);
      try {
        // Use the admin API service which handles mock/real data toggle
        const data = await adminGet(ADMIN_ENDPOINTS.ALGORITHM_SETTINGS, {
          includeABTestResults: true,
          includeMetrics: true
        });

        if (data.success) {
          // Update settings with data from API
          if (data.settings) {
            setSettings(data.settings);
          }

          // Update A/B test results if available
          if (data.abTestResults) {
            setAbTestResults(data.abTestResults);
          }

          // Update metrics if available
          if (data.metrics) {
            setMetrics(data.metrics);
          }
        } else {
          throw new Error(data.message || 'Failed to load settings');
        }

        setLoading(false);
      } catch (err) {
        console.error('Error fetching algorithm settings:', err);
        setError('Failed to load algorithm settings. Please try again.');
        setLoading(false);
      }
    };

    fetchSettings();
    initializeMCP();
  }, []);

  // MCP Integration Functions
  const initializeMCP = async () => {
    try {
      // Check MCP server status
      const mcpServerStatus = await adminGet('/mcp/status');
      if (mcpServerStatus.success) {
        setMcpStatus({
          isConnected: true,
          isRunning: mcpServerStatus.data.isRunning
        });
      }

      // Initialize MCP client if server is running
      if (mcpServerStatus.data?.isRunning) {
        try {
          await mcpClient.connect();
          setMcpStatus(prev => ({ ...prev, isConnected: true }));
        } catch (error) {
          console.warn('MCP client connection failed:', error);
        }
      }
    } catch (error) {
      console.warn('MCP server not available:', error);
    }
  };

  const testAIAlgorithm = async (algorithmName, testData = {}) => {
    setTestingAI(true);
    try {
      let result;

      if (mcpStatus.isConnected) {
        // Use MCP client for AI testing
        result = await mcpClient.callTool(algorithmName, testData);
      } else {
        // Fallback to direct API call
        result = await adminPost('/mcp/tools/test', {
          toolName: algorithmName,
          arguments: testData
        });
      }

      setAiTestResults({
        algorithm: algorithmName,
        result: result,
        timestamp: new Date().toISOString()
      });

      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (error) {
      setError(`AI test failed: ${error.message}`);
      setTimeout(() => setError(null), 5000);
    } finally {
      setTestingAI(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleSettingChange = (category, setting, value) => {
    if (category) {
      setSettings(prev => ({
        ...prev,
        [category]: {
          ...prev[category],
          [setting]: value
        }
      }));
    } else {
      setSettings(prev => ({
        ...prev,
        [setting]: value
      }));
    }
  };

  // Phase information helper function
  const getPhaseInfo = (version) => {
    const phaseInfo = {
      'v1.0': {
        name: 'Current Rule-Based',
        description: 'Traditional rule-based matching with fixed preferences',
        features: ['Basic compatibility scoring', 'Fixed preference weights', 'Simple filtering'],
        status: 'ACTIVE',
        color: 'success'
      },
      'v1.5': {
        name: 'Flexible Matching',
        description: 'Enhanced matching with flexible preferences and compatibility groups',
        features: ['Flexible age ranges', 'Religion compatibility groups', 'Caste flexibility', 'Gradual scoring'],
        status: 'READY',
        color: 'info'
      },
      'v2.0': {
        name: 'Personalized AI',
        description: 'AI-powered personalization based on user behavior',
        features: ['Behavioral learning', 'Dynamic preferences', 'User adaptation', 'Interaction analysis'],
        status: 'PLANNED',
        color: 'warning'
      },
      'v2.5': {
        name: 'Intelligent Features',
        description: 'Advanced AI features for enhanced user experience',
        features: ['Smart explanations', 'Predictive scoring', 'AI recommendations', 'Conversation analysis'],
        status: 'PLANNED',
        color: 'warning'
      },
      'v3.0': {
        name: 'Advanced AI',
        description: 'Cutting-edge AI algorithms for superior matching',
        features: ['Multi-modal learning', 'Graph networks', 'Reinforcement learning', 'Real-time adaptation'],
        status: 'PLANNED',
        color: 'error'
      }
    };

    const info = phaseInfo[version];
    if (!info) return null;

    return (
      <Box>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mr: 2 }}>
            {info.name}
          </Typography>
          <Chip
            label={info.status}
            color={info.color}
            size="small"
            variant={info.status === 'ACTIVE' ? 'filled' : 'outlined'}
          />
        </Box>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {info.description}
        </Typography>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>
          Features:
        </Typography>
        <Box component="ul" sx={{ m: 0, pl: 2 }}>
          {info.features.map((feature, index) => (
            <Typography component="li" variant="body2" key={index} sx={{ mb: 0.5 }}>
              {feature}
            </Typography>
          ))}
        </Box>
        {info.status === 'PLANNED' && (
          <Alert severity="info" sx={{ mt: 2 }}>
            This phase is planned for future implementation. Selecting it will prepare the system but won't activate the features yet.
          </Alert>
        )}
        {info.status === 'READY' && (
          <Alert severity="success" sx={{ mt: 2 }}>
            This phase is ready for activation. All features have been implemented and tested.
          </Alert>
        )}
      </Box>
    );
  };

  const handleSaveSettings = async () => {
    setLoading(true);
    try {
      // Use the admin API service which handles mock/real data toggle
      const data = await adminPut(ADMIN_ENDPOINTS.ALGORITHM_SETTINGS, { settings });

      if (data.success) {
        setSuccess(true);

        // Update settings if returned from API
        if (data.settings) {
          setSettings(data.settings);
        }
      } else {
        throw new Error(data.message || 'Failed to save settings');
      }

      setLoading(false);
    } catch (err) {
      console.error('Error saving algorithm settings:', err);
      setError('Failed to save algorithm settings. Please try again.');
      setLoading(false);
    }
  };

  const handleResetSettings = () => {
    // In a real implementation, this would reset to default values from the API
    // For now, we'll just reload the page
    window.location.reload();
  };

  const handleCloseSnackbar = () => {
    setSuccess(false);
    setError(null);
  };

  return (
    <EnhancedAdminLayout title="Algorithm Settings">
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Algorithm Settings
        </Typography>

        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab icon={<SettingsIcon />} label="General Settings" />
            <Tab icon={<SettingsIcon />} label="Two-Tower Model" />
            <Tab icon={<CompareArrowsIcon />} label="A/B Testing" />
            <Tab icon={<BarChartIcon />} label="Performance Metrics" />
            <Tab icon={<SmartToyIcon />} label="AI Testing" />
          </Tabs>
        </Paper>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            {/* General Settings Tab */}
            {activeTab === 0 && (
              <Box>
                <Grid container spacing={3}>
                  {/* Algorithm Version */}
                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardHeader title="Algorithm Version" />
                      <Divider />
                      <CardContent>
                        <FormControl fullWidth margin="normal">
                          <InputLabel>Matching Algorithm Version</InputLabel>
                          <Select
                            value={settings.matchingAlgorithmVersion}
                            label="Matching Algorithm Version"
                            onChange={(e) => handleSettingChange(null, 'matchingAlgorithmVersion', e.target.value)}
                          >
                            <MenuItem value="v1.0">Version 1.0 (Current - Rule-Based)</MenuItem>
                            <MenuItem value="v1.5">Version 1.5 (Phase 1 - Flexible Matching)</MenuItem>
                            <MenuItem value="v2.0">Version 2.0 (Phase 2 - Personalized AI)</MenuItem>
                            <MenuItem value="v2.5">Version 2.5 (Phase 3 - Intelligent Features)</MenuItem>
                            <MenuItem value="v3.0">Version 3.0 (Phase 4 - Advanced AI)</MenuItem>
                          </Select>
                        </FormControl>

                        {/* Phase Information Display */}
                        {settings.matchingAlgorithmVersion && (
                          <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #e0e0e0' }}>
                            <Typography variant="h6" gutterBottom>
                              Phase Information
                            </Typography>
                            {getPhaseInfo(settings.matchingAlgorithmVersion)}
                          </Box>
                        )}

                        <FormControlLabel
                          control={
                            <Switch
                              checked={settings.enableAIMatching}
                              onChange={(e) => handleSettingChange(null, 'enableAIMatching', e.target.checked)}
                            />
                          }
                          label="Enable AI-Powered Matching"
                        />
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* Matching Thresholds */}
                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardHeader title="Matching Thresholds" />
                      <Divider />
                      <CardContent>
                        <Typography gutterBottom>
                          Minimum Match Score (%)
                          <Tooltip title="Minimum score required to consider a match">
                            <IconButton size="small">
                              <InfoIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Typography>
                        <Slider
                          value={settings.minimumMatchScore}
                          onChange={(e, newValue) => handleSettingChange(null, 'minimumMatchScore', newValue)}
                          valueLabelDisplay="auto"
                          step={5}
                          marks
                          min={0}
                          max={100}
                        />

                        <Typography gutterBottom sx={{ mt: 2 }}>
                          High Quality Match Threshold (%)
                          <Tooltip title="Threshold for highlighting high-quality matches">
                            <IconButton size="small">
                              <InfoIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Typography>
                        <Slider
                          value={settings.highQualityMatchThreshold}
                          onChange={(e, newValue) => handleSettingChange(null, 'highQualityMatchThreshold', newValue)}
                          valueLabelDisplay="auto"
                          step={5}
                          marks
                          min={0}
                          max={100}
                        />
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* Attribute Weights */}
                  <Grid item xs={12}>
                    <Card>
                      <CardHeader
                        title="Attribute Weights"
                        subheader="Set the importance of each attribute in the matching algorithm (0-10)"
                      />
                      <Divider />
                      <CardContent>
                        <Grid container spacing={3}>
                          {Object.entries(settings.weights).map(([key, value]) => (
                            <Grid item xs={12} sm={6} md={4} lg={3} key={key}>
                              <Typography gutterBottom>
                                {key.replace('Weight', '')}
                              </Typography>
                              <Slider
                                value={value}
                                onChange={(e, newValue) => handleSettingChange('weights', key, newValue)}
                                valueLabelDisplay="auto"
                                step={1}
                                marks
                                min={0}
                                max={10}
                              />
                            </Grid>
                          ))}
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* Advanced Settings */}
                  <Grid item xs={12}>
                    <Card>
                      <CardHeader title="Advanced Settings" />
                      <Divider />
                      <CardContent>
                        <Grid container spacing={3}>
                          <Grid item xs={12} sm={6} md={4}>
                            <TextField
                              fullWidth
                              label="Maximum Distance (km)"
                              type="number"
                              value={settings.maxDistanceKm}
                              onChange={(e) => handleSettingChange(null, 'maxDistanceKm', parseInt(e.target.value))}
                              InputProps={{ inputProps: { min: 0, max: 500 } }}
                            />
                          </Grid>
                          <Grid item xs={12} sm={6} md={4}>
                            <TextField
                              fullWidth
                              label="Maximum Age Difference"
                              type="number"
                              value={settings.maxAgeDifference}
                              onChange={(e) => handleSettingChange(null, 'maxAgeDifference', parseInt(e.target.value))}
                              InputProps={{ inputProps: { min: 0, max: 20 } }}
                            />
                          </Grid>
                          <Grid item xs={12} sm={6} md={4}>
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={settings.considerUserActivity}
                                  onChange={(e) => handleSettingChange(null, 'considerUserActivity', e.target.checked)}
                                />
                              }
                              label="Consider User Activity"
                            />
                          </Grid>
                          <Grid item xs={12} sm={6} md={4}>
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={settings.boostNewProfiles}
                                  onChange={(e) => handleSettingChange(null, 'boostNewProfiles', e.target.checked)}
                                />
                              }
                              label="Boost New Profiles"
                            />
                          </Grid>
                          {settings.boostNewProfiles && (
                            <Grid item xs={12} sm={6} md={4}>
                              <TextField
                                fullWidth
                                label="Boost New Profiles Days"
                                type="number"
                                value={settings.boostNewProfilesDays}
                                onChange={(e) => handleSettingChange(null, 'boostNewProfilesDays', parseInt(e.target.value))}
                                InputProps={{ inputProps: { min: 1, max: 30 } }}
                              />
                            </Grid>
                          )}
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                  <Button
                    variant="outlined"
                    startIcon={<RestartAltIcon />}
                    onClick={handleResetSettings}
                  >
                    Reset to Defaults
                  </Button>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<SaveIcon />}
                    onClick={handleSaveSettings}
                    disabled={loading}
                  >
                    Save Settings
                  </Button>
                </Box>
              </Box>
            )}

            {/* Two-Tower Model Tab */}
            {activeTab === 1 && (
              <Box>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardHeader title="Two-Tower Model Configuration" />
                      <Divider />
                      <CardContent>
                        <FormControl fullWidth margin="normal">
                          <InputLabel>Similarity Metric</InputLabel>
                          <Select
                            value={settings.twoTower.similarityMetric}
                            label="Similarity Metric"
                            onChange={(e) => handleSettingChange('twoTower', 'similarityMetric', e.target.value)}
                          >
                            <MenuItem value="cosine">Cosine Similarity</MenuItem>
                            <MenuItem value="dot">Dot Product</MenuItem>
                            <MenuItem value="euclidean">Euclidean Distance</MenuItem>
                          </Select>
                        </FormControl>

                        <Typography gutterBottom sx={{ mt: 2 }}>
                          Embedding Size
                          <Tooltip title="Size of the embedding vectors in the two-tower model">
                            <IconButton size="small">
                              <InfoIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Typography>
                        <Slider
                          value={settings.twoTower.embeddingSize}
                          onChange={(_, newValue) => handleSettingChange('twoTower', 'embeddingSize', newValue)}
                          valueLabelDisplay="auto"
                          step={32}
                          marks
                          min={32}
                          max={512}
                        />

                        <Typography gutterBottom sx={{ mt: 2 }}>
                          Dropout Rate
                          <Tooltip title="Dropout rate for regularization in the two-tower model">
                            <IconButton size="small">
                              <InfoIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Typography>
                        <Slider
                          value={settings.twoTower.dropoutRate}
                          onChange={(_, newValue) => handleSettingChange('twoTower', 'dropoutRate', newValue)}
                          valueLabelDisplay="auto"
                          step={0.05}
                          marks
                          min={0}
                          max={0.5}
                        />
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardHeader title="Training Parameters" />
                      <Divider />
                      <CardContent>
                        <Typography gutterBottom>
                          Learning Rate
                          <Tooltip title="Learning rate for training the two-tower model">
                            <IconButton size="small">
                              <InfoIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Typography>
                        <Slider
                          value={settings.twoTower.learningRate}
                          onChange={(_, newValue) => handleSettingChange('twoTower', 'learningRate', newValue)}
                          valueLabelDisplay="auto"
                          step={0.0001}
                          min={0.0001}
                          max={0.01}
                        />

                        <Grid container spacing={2} sx={{ mt: 1 }}>
                          <Grid item xs={6}>
                            <TextField
                              fullWidth
                              label="Batch Size"
                              type="number"
                              value={settings.twoTower.batchSize}
                              onChange={(e) => handleSettingChange('twoTower', 'batchSize', parseInt(e.target.value))}
                              inputProps={{ min: 8, max: 256 }}
                            />
                          </Grid>
                          <Grid item xs={6}>
                            <TextField
                              fullWidth
                              label="Epochs"
                              type="number"
                              value={settings.twoTower.epochs}
                              onChange={(e) => handleSettingChange('twoTower', 'epochs', parseInt(e.target.value))}
                              inputProps={{ min: 1, max: 100 }}
                            />
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12}>
                    <Card>
                      <CardHeader title="Neural Network Architecture" />
                      <Divider />
                      <CardContent>
                        <Grid container spacing={3}>
                          <Grid item xs={12} md={6}>
                            <Typography variant="h6" gutterBottom>
                              User Tower Layers
                              <Tooltip title="Hidden layer sizes for the user tower">
                                <IconButton size="small">
                                  <InfoIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </Typography>
                            <TextField
                              fullWidth
                              label="Layer Sizes (comma-separated)"
                              value={settings.twoTower.userTowerLayers.join(', ')}
                              onChange={(e) => {
                                const layerSizes = e.target.value.split(',').map(size => parseInt(size.trim())).filter(size => !isNaN(size));
                                handleSettingChange('twoTower', 'userTowerLayers', layerSizes);
                              }}
                              helperText="Example: 128, 64"
                            />
                          </Grid>
                          <Grid item xs={12} md={6}>
                            <Typography variant="h6" gutterBottom>
                              Match Tower Layers
                              <Tooltip title="Hidden layer sizes for the match tower">
                                <IconButton size="small">
                                  <InfoIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </Typography>
                            <TextField
                              fullWidth
                              label="Layer Sizes (comma-separated)"
                              value={settings.twoTower.matchTowerLayers.join(', ')}
                              onChange={(e) => {
                                const layerSizes = e.target.value.split(',').map(size => parseInt(size.trim())).filter(size => !isNaN(size));
                                handleSettingChange('twoTower', 'matchTowerLayers', layerSizes);
                              }}
                              helperText="Example: 128, 64"
                            />
                          </Grid>
                        </Grid>

                        <Alert severity="info" sx={{ mt: 3 }}>
                          The two-tower model consists of two separate neural networks (towers) that process user and match data separately. The outputs of both towers are compared to calculate a match score.
                        </Alert>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                  <Button
                    variant="outlined"
                    startIcon={<RestartAltIcon />}
                    onClick={handleResetSettings}
                  >
                    Reset to Defaults
                  </Button>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<SaveIcon />}
                    onClick={handleSaveSettings}
                    disabled={loading}
                  >
                    Save Settings
                  </Button>
                </Box>
              </Box>
            )}

            {/* A/B Testing Tab */}
            {activeTab === 2 && (
              <Box>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardHeader title="A/B Testing Configuration" />
                      <Divider />
                      <CardContent>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={settings.abTestingEnabled}
                              onChange={(e) => handleSettingChange(null, 'abTestingEnabled', e.target.checked)}
                            />
                          }
                          label="Enable A/B Testing"
                        />

                        {settings.abTestingEnabled && (
                          <>
                            <FormControl fullWidth margin="normal">
                              <InputLabel>Active Variant</InputLabel>
                              <Select
                                value={settings.abTestingVariant}
                                label="Active Variant"
                                onChange={(e) => handleSettingChange(null, 'abTestingVariant', e.target.value)}
                              >
                                <MenuItem value="A">Variant A (Current)</MenuItem>
                                <MenuItem value="B">Variant B (Test)</MenuItem>
                                <MenuItem value="BOTH">Both Variants</MenuItem>
                              </Select>
                            </FormControl>

                            <Typography gutterBottom sx={{ mt: 2 }}>
                              Distribution (% of users for Variant B)
                            </Typography>
                            <Slider
                              value={settings.abTestingDistribution}
                              onChange={(e, newValue) => handleSettingChange(null, 'abTestingDistribution', newValue)}
                              valueLabelDisplay="auto"
                              step={5}
                              marks
                              min={0}
                              max={100}
                            />

                            <Alert severity="info" sx={{ mt: 2 }}>
                              A/B testing allows you to compare different algorithm configurations with real users to determine which performs better.
                            </Alert>
                          </>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardHeader title="A/B Testing Results" />
                      <Divider />
                      <CardContent>
                        <Grid container spacing={2}>
                          <Grid item xs={6}>
                            <Typography variant="h6" align="center">Variant A</Typography>
                            <Box sx={{ textAlign: 'center', mt: 2 }}>
                              <Typography variant="body2">Matches</Typography>
                              <Typography variant="h5">{abTestResults.variantA.matches}</Typography>
                            </Box>
                            <Box sx={{ textAlign: 'center', mt: 2 }}>
                              <Typography variant="body2">Conversations</Typography>
                              <Typography variant="h5">{abTestResults.variantA.conversations}</Typography>
                            </Box>
                            <Box sx={{ textAlign: 'center', mt: 2 }}>
                              <Typography variant="body2">Success Rate</Typography>
                              <Typography variant="h5">{abTestResults.variantA.successRate}%</Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="h6" align="center">Variant B</Typography>
                            <Box sx={{ textAlign: 'center', mt: 2 }}>
                              <Typography variant="body2">Matches</Typography>
                              <Typography variant="h5">{abTestResults.variantB.matches}</Typography>
                            </Box>
                            <Box sx={{ textAlign: 'center', mt: 2 }}>
                              <Typography variant="body2">Conversations</Typography>
                              <Typography variant="h5">{abTestResults.variantB.conversations}</Typography>
                            </Box>
                            <Box sx={{ textAlign: 'center', mt: 2 }}>
                              <Typography variant="body2">Success Rate</Typography>
                              <Typography variant="h5">{abTestResults.variantB.successRate}%</Typography>
                            </Box>
                          </Grid>
                        </Grid>

                        <Alert
                          severity="success"
                          sx={{ mt: 3 }}
                        >
                          Variant B is performing 7.1% better than Variant A. Consider making it the default.
                        </Alert>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<SaveIcon />}
                    onClick={handleSaveSettings}
                    disabled={loading}
                  >
                    Save Settings
                  </Button>
                </Box>
              </Box>
            )}

            {/* Performance Metrics Tab */}
            {activeTab === 3 && (
              <Box>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={4}>
                    <Card>
                      <CardHeader title="Match Statistics" />
                      <Divider />
                      <CardContent>
                        <Box sx={{ textAlign: 'center', mb: 2 }}>
                          <Typography variant="body2">Total Matches</Typography>
                          <Typography variant="h4">{metrics.totalMatches}</Typography>
                        </Box>
                        <Box sx={{ textAlign: 'center', mb: 2 }}>
                          <Typography variant="body2">Successful Matches</Typography>
                          <Typography variant="h4">{metrics.successfulMatches}</Typography>
                        </Box>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="body2">Average Match Score</Typography>
                          <Typography variant="h4">{metrics.averageMatchScore}%</Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} md={8}>
                    <Card>
                      <CardHeader title="Match Score Distribution" />
                      <Divider />
                      <CardContent sx={{ height: 300 }}>
                        <Typography variant="body2" color="textSecondary" align="center">
                          Chart visualization would go here in a real implementation
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12}>
                    <Card>
                      <CardHeader title="Monthly Match Trends" />
                      <Divider />
                      <CardContent sx={{ height: 300 }}>
                        <Typography variant="body2" color="textSecondary" align="center">
                          Chart visualization would go here in a real implementation
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* AI Testing Tab */}
            {activeTab === 4 && (
              <Box>
                <Grid container spacing={3}>
                  {/* MCP Server Status */}
                  <Grid item xs={12} md={4}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          MCP Server Status
                        </Typography>
                        <Stack spacing={2}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="body2">Server:</Typography>
                            <Chip
                              label={mcpStatus.isRunning ? 'Running' : 'Stopped'}
                              color={mcpStatus.isRunning ? 'success' : 'error'}
                              size="small"
                            />
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="body2">Connection:</Typography>
                            <Chip
                              label={mcpStatus.isConnected ? 'Connected' : 'Disconnected'}
                              color={mcpStatus.isConnected ? 'success' : 'warning'}
                              size="small"
                            />
                          </Box>
                        </Stack>
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* AI Algorithm Testing */}
                  <Grid item xs={12} md={8}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          AI Algorithm Testing
                        </Typography>
                        <Grid container spacing={2}>
                          <Grid item xs={12} sm={6}>
                            <Button
                              fullWidth
                              variant="outlined"
                              onClick={() => testAIAlgorithm('user_matching', { userId: 'test123', limit: 5 })}
                              disabled={testingAI}
                              startIcon={testingAI ? <CircularProgress size={20} /> : <SmartToyIcon />}
                            >
                              Test User Matching
                            </Button>
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <Button
                              fullWidth
                              variant="outlined"
                              onClick={() => testAIAlgorithm('profile_analysis', { userId: 'test123' })}
                              disabled={testingAI}
                              startIcon={testingAI ? <CircularProgress size={20} /> : <SmartToyIcon />}
                            >
                              Test Profile Analysis
                            </Button>
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <Button
                              fullWidth
                              variant="outlined"
                              onClick={() => testAIAlgorithm('compatibility_score', { userId1: 'test123', userId2: 'test456' })}
                              disabled={testingAI}
                              startIcon={testingAI ? <CircularProgress size={20} /> : <SmartToyIcon />}
                            >
                              Test Compatibility Score
                            </Button>
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <Button
                              fullWidth
                              variant="outlined"
                              onClick={() => testAIAlgorithm('smart_recommendations', { userId: 'test123', type: 'profile_improvement' })}
                              disabled={testingAI}
                              startIcon={testingAI ? <CircularProgress size={20} /> : <SmartToyIcon />}
                            >
                              Test Smart Recommendations
                            </Button>
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <Button
                              fullWidth
                              variant="outlined"
                              onClick={() => testAIAlgorithm('fraud_detection', { userId: 'test123', checkType: 'profile' })}
                              disabled={testingAI}
                              startIcon={testingAI ? <CircularProgress size={20} /> : <SmartToyIcon />}
                            >
                              Test Fraud Detection
                            </Button>
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <Button
                              fullWidth
                              variant="outlined"
                              onClick={() => testAIAlgorithm('success_prediction', { userId1: 'test123', userId2: 'test456' })}
                              disabled={testingAI}
                              startIcon={testingAI ? <CircularProgress size={20} /> : <SmartToyIcon />}
                            >
                              Test Success Prediction
                            </Button>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* Test Results */}
                  {aiTestResults && (
                    <Grid item xs={12}>
                      <Card>
                        <CardContent>
                          <Typography variant="h6" gutterBottom>
                            Test Results - {aiTestResults.algorithm}
                          </Typography>
                          <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
                            Tested at: {new Date(aiTestResults.timestamp).toLocaleString()}
                          </Typography>
                          <Paper sx={{ p: 2, bgcolor: 'grey.100', mt: 2 }}>
                            <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap', fontFamily: 'monospace' }}>
                              {JSON.stringify(aiTestResults.result, null, 2)}
                            </Typography>
                          </Paper>
                        </CardContent>
                      </Card>
                    </Grid>
                  )}
                </Grid>
              </Box>
            )}
          </>
        )}

        <Snackbar
          open={success}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          message="Settings saved successfully"
        />

        <Snackbar
          open={!!error}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
        >
          <Alert onClose={handleCloseSnackbar} severity="error">
            {error}
          </Alert>
        </Snackbar>
      </Box>
    </EnhancedAdminLayout>
  );
}

