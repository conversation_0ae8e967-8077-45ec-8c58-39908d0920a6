/**
 * Website-Admin Integration Verification
 * Ensures all user-facing features have corresponding admin controls
 */

const fs = require('fs');
const path = require('path');

// User-facing features and their corresponding admin controls
const FEATURE_INTEGRATION_MAP = {
  // User Registration & Profile Management
  'User Registration': {
    userFeatures: ['/register', '/profile/complete'],
    adminControls: ['/admin/users', '/admin/verification-queue'],
    integration: 'User registration data flows to admin user management and verification queue'
  },
  
  // Interest Management System
  'Interest Management': {
    userFeatures: ['/interests'],
    adminControls: ['/admin/users', '/admin/chat-management'],
    integration: 'User interests are tracked and manageable through admin user controls'
  },
  
  // Shortlist Feature
  'Shortlist Management': {
    userFeatures: ['/shortlist'],
    adminControls: ['/admin/users', '/admin/advanced-analytics'],
    integration: 'User shortlists are visible in admin analytics and user profiles'
  },
  
  // Search & Matching
  'Profile Search': {
    userFeatures: ['/search'],
    adminControls: ['/admin/algorithm-settings', '/admin/ai-phase-management'],
    integration: 'Search algorithms are configurable through admin algorithm settings'
  },
  
  // Contact Management
  'Contact Management': {
    userFeatures: ['/contacts'],
    adminControls: ['/admin/contact-security', '/admin/privacy-controls'],
    integration: 'Contact reveals are controlled by admin security settings'
  },
  
  // Profile Interactions
  'Profile Interactions': {
    userFeatures: ['/interactions'],
    adminControls: ['/admin/advanced-analytics', '/admin/success-analytics'],
    integration: 'User interactions feed into admin analytics and success metrics'
  },
  
  // Premium Features
  'Premium Subscriptions': {
    userFeatures: ['/premium', '/billing'],
    adminControls: ['/admin/premium-plans', '/admin/subscriptions', '/admin/transactions'],
    integration: 'User subscriptions are fully manageable through admin billing controls'
  },
  
  // Communication
  'Messaging System': {
    userFeatures: ['/messages'],
    adminControls: ['/admin/chat-management', '/admin/text-moderation'],
    integration: 'User messages are monitored and moderated through admin controls'
  },
  
  // Content Management
  'Photo Management': {
    userFeatures: ['/profile/photos'],
    adminControls: ['/admin/photo-moderation'],
    integration: 'User photos are reviewed through admin moderation system'
  },
  
  // Verification
  'Profile Verification': {
    userFeatures: ['/verification'],
    adminControls: ['/admin/verification-queue'],
    integration: 'User verification requests flow to admin verification queue'
  },
  
  // Notifications
  'Notifications': {
    userFeatures: ['/notifications'],
    adminControls: ['/admin/notifications', '/admin/email-templates'],
    integration: 'User notifications are controlled through admin notification management'
  },
  
  // Biodata & Templates
  'Biodata Generation': {
    userFeatures: ['/biodata'],
    adminControls: ['/admin/biodata-templates'],
    integration: 'User biodata uses templates managed through admin controls'
  },
  
  // Spotlight Features
  'Profile Spotlight': {
    userFeatures: ['/spotlight'],
    adminControls: ['/admin/spotlight-features'],
    integration: 'User spotlight features are managed through admin spotlight controls'
  },
  
  // Reports & Safety
  'Report System': {
    userFeatures: ['/report'],
    adminControls: ['/admin/reported-profiles', '/admin/security-settings'],
    integration: 'User reports flow to admin moderation and security systems'
  },
  
  // Success Stories
  'Success Stories': {
    userFeatures: ['/success-stories'],
    adminControls: ['/admin/success-stories'],
    integration: 'User success stories are managed through admin content management'
  }
};

// Test results
const integrationResults = {
  fullyIntegrated: [],
  partiallyIntegrated: [],
  missingIntegration: [],
  adminOnlyFeatures: [],
  userOnlyFeatures: []
};

/**
 * Check if a file exists (handles both direct files and index files)
 */
function checkFileExists(routePath) {
  const basePath = path.join(__dirname, '../src/pages');
  const directPath = path.join(basePath, `${routePath}.js`);
  const indexPath = path.join(basePath, routePath, 'index.js');
  
  return fs.existsSync(directPath) || fs.existsSync(indexPath);
}

/**
 * Verify integration between user features and admin controls
 */
function verifyFeatureIntegration(featureName, config) {
  const result = {
    feature: featureName,
    userFeaturesExist: [],
    userFeaturesMissing: [],
    adminControlsExist: [],
    adminControlsMissing: [],
    integrationDescription: config.integration,
    integrationLevel: 'none'
  };
  
  // Check user features
  config.userFeatures.forEach(feature => {
    if (checkFileExists(feature)) {
      result.userFeaturesExist.push(feature);
    } else {
      result.userFeaturesMissing.push(feature);
    }
  });
  
  // Check admin controls
  config.adminControls.forEach(control => {
    if (checkFileExists(control)) {
      result.adminControlsExist.push(control);
    } else {
      result.adminControlsMissing.push(control);
    }
  });
  
  // Determine integration level
  const userComplete = result.userFeaturesMissing.length === 0;
  const adminComplete = result.adminControlsMissing.length === 0;
  
  if (userComplete && adminComplete) {
    result.integrationLevel = 'full';
    integrationResults.fullyIntegrated.push(result);
  } else if (result.userFeaturesExist.length > 0 && result.adminControlsExist.length > 0) {
    result.integrationLevel = 'partial';
    integrationResults.partiallyIntegrated.push(result);
  } else if (result.userFeaturesExist.length > 0 && result.adminControlsExist.length === 0) {
    result.integrationLevel = 'user_only';
    integrationResults.userOnlyFeatures.push(result);
  } else if (result.userFeaturesExist.length === 0 && result.adminControlsExist.length > 0) {
    result.integrationLevel = 'admin_only';
    integrationResults.adminOnlyFeatures.push(result);
  } else {
    result.integrationLevel = 'missing';
    integrationResults.missingIntegration.push(result);
  }
  
  return result;
}

/**
 * Run comprehensive website-admin integration test
 */
function runIntegrationTest() {
  console.log('🔗 Starting Website-Admin Integration Verification...\n');
  
  const results = [];
  
  // Test each feature integration
  Object.entries(FEATURE_INTEGRATION_MAP).forEach(([featureName, config]) => {
    const result = verifyFeatureIntegration(featureName, config);
    results.push(result);
    
    // Log progress
    const statusIcon = {
      'full': '✅',
      'partial': '⚠️',
      'user_only': '🔵',
      'admin_only': '🟡',
      'missing': '❌'
    }[result.integrationLevel];
    
    console.log(`${statusIcon} ${featureName} - ${result.integrationLevel.toUpperCase()}`);
    
    if (result.userFeaturesMissing.length > 0) {
      console.log(`   Missing User Features: ${result.userFeaturesMissing.join(', ')}`);
    }
    if (result.adminControlsMissing.length > 0) {
      console.log(`   Missing Admin Controls: ${result.adminControlsMissing.join(', ')}`);
    }
  });
  
  // Generate integration summary
  console.log('\n📊 WEBSITE-ADMIN INTEGRATION SUMMARY');
  console.log('=' .repeat(50));
  console.log(`Total Features Tested: ${results.length}`);
  console.log(`✅ Fully Integrated: ${integrationResults.fullyIntegrated.length}`);
  console.log(`⚠️  Partially Integrated: ${integrationResults.partiallyIntegrated.length}`);
  console.log(`🔵 User Features Only: ${integrationResults.userOnlyFeatures.length}`);
  console.log(`🟡 Admin Controls Only: ${integrationResults.adminOnlyFeatures.length}`);
  console.log(`❌ Missing Integration: ${integrationResults.missingIntegration.length}`);
  
  // Calculate integration score
  const integrationScore = (
    (integrationResults.fullyIntegrated.length * 100) +
    (integrationResults.partiallyIntegrated.length * 70) +
    (integrationResults.userOnlyFeatures.length * 40) +
    (integrationResults.adminOnlyFeatures.length * 60)
  ) / (results.length * 100) * 100;
  
  console.log(`\n📈 Integration Score: ${integrationScore.toFixed(1)}%`);
  
  // Critical missing features
  if (integrationResults.missingIntegration.length > 0) {
    console.log('\n🚨 CRITICAL MISSING INTEGRATIONS:');
    integrationResults.missingIntegration.forEach(feature => {
      console.log(`   - ${feature.feature}: No user features or admin controls found`);
    });
  }
  
  // User-only features (need admin controls)
  if (integrationResults.userOnlyFeatures.length > 0) {
    console.log('\n🔵 USER FEATURES NEEDING ADMIN CONTROLS:');
    integrationResults.userOnlyFeatures.forEach(feature => {
      console.log(`   - ${feature.feature}: Has user features but missing admin controls`);
      console.log(`     Missing: ${feature.adminControlsMissing.join(', ')}`);
    });
  }
  
  // Admin-only features (need user interfaces)
  if (integrationResults.adminOnlyFeatures.length > 0) {
    console.log('\n🟡 ADMIN CONTROLS NEEDING USER FEATURES:');
    integrationResults.adminOnlyFeatures.forEach(feature => {
      console.log(`   - ${feature.feature}: Has admin controls but missing user features`);
      console.log(`     Missing: ${feature.userFeaturesMissing.join(', ')}`);
    });
  }
  
  // Production readiness assessment
  if (integrationScore >= 90 && integrationResults.missingIntegration.length === 0) {
    console.log('\n🎉 WEBSITE-ADMIN INTEGRATION IS PRODUCTION READY!');
  } else if (integrationScore >= 80) {
    console.log('\n✅ Integration is mostly complete with minor gaps');
  } else {
    console.log('\n⚠️  Integration needs significant work before production');
  }
  
  return {
    results: results,
    summary: integrationResults,
    integrationScore: integrationScore,
    productionReady: integrationScore >= 90 && integrationResults.missingIntegration.length === 0
  };
}

// Run the test if this script is executed directly
if (require.main === module) {
  const integrationResults = runIntegrationTest();
  
  // Save detailed results to file
  const reportPath = path.join(__dirname, '../website-admin-integration-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(integrationResults, null, 2));
  console.log(`\n📄 Detailed report saved to: ${reportPath}`);
}

module.exports = { runIntegrationTest, FEATURE_INTEGRATION_MAP };
