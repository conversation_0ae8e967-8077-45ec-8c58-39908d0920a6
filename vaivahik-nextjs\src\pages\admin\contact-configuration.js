/**
 * Contact Configuration Admin Page
 * Dynamic configuration of all contact details (email, WhatsApp, phone)
 */

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  Alert,
  Divider,
  IconButton,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Phone as PhoneIcon,
  Email as EmailIcon,
  WhatsApp as WhatsAppIcon,
  Business as BusinessIcon,
  Support as SupportIcon,
  Edit as EditIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { ToastContainer, toast } from 'react-toastify';
import axios from 'axios';

// Import EnhancedAdminLayout with SSR disabled
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);

export default function ContactConfiguration() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [contactConfig, setContactConfig] = useState({
    primary: {
      businessEmail: '<EMAIL>',
      supportEmail: '<EMAIL>',
      businessPhone: '+91-XXXX-XXXXXX',
      supportPhone: '+91-XXXX-XXXXXX',
      whatsappBusiness: '+91-XXXX-XXXXXX',
      whatsappSupport: '+91-XXXX-XXXXXX'
    },
    hours: {
      support: {
        enabled: true,
        monday: { start: '09:00', end: '18:00', enabled: true },
        tuesday: { start: '09:00', end: '18:00', enabled: true },
        wednesday: { start: '09:00', end: '18:00', enabled: true },
        thursday: { start: '09:00', end: '18:00', enabled: true },
        friday: { start: '09:00', end: '18:00', enabled: true },
        saturday: { start: '09:00', end: '18:00', enabled: true },
        sunday: { start: '10:00', end: '16:00', enabled: false }
      }
    },
    autoResponses: {
      email: {
        enabled: true,
        subject: 'Thank you for contacting Vaivahik',
        message: 'We have received your message and will respond within 24 hours.'
      },
      whatsapp: {
        enabled: true,
        message: 'Hello! Thanks for contacting Vaivahik. Our team will assist you shortly.'
      }
    },
    integrations: {
      whatsappBusiness: {
        enabled: false,
        apiKey: '',
        phoneNumberId: '',
        accessToken: ''
      },
      emailService: {
        provider: 'brevo',
        apiKey: '',
        fromEmail: '<EMAIL>',
        fromName: 'Vaivahik Team'
      }
    }
  });

  const [editDialog, setEditDialog] = useState(false);
  const [editField, setEditField] = useState(null);

  useEffect(() => {
    fetchContactConfiguration();
  }, []);

  const fetchContactConfiguration = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/admin/contact-configuration');
      
      if (response.data.success) {
        setContactConfig(response.data.configuration);
      }
    } catch (error) {
      console.error('Error fetching contact configuration:', error);
      toast.error('Failed to load contact configuration');
    } finally {
      setLoading(false);
    }
  };

  const saveConfiguration = async () => {
    try {
      setSaving(true);
      const response = await axios.post('/api/admin/contact-configuration', contactConfig);
      
      if (response.data.success) {
        toast.success('Contact configuration saved successfully');
      } else {
        throw new Error(response.data.message);
      }
    } catch (error) {
      console.error('Error saving configuration:', error);
      toast.error('Failed to save contact configuration');
    } finally {
      setSaving(false);
    }
  };

  const handleFieldUpdate = (section, field, value) => {
    setContactConfig(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const handleNestedFieldUpdate = (section, subsection, field, value) => {
    setContactConfig(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [subsection]: {
          ...prev[section][subsection],
          [field]: value
        }
      }
    }));
  };

  const testContact = async (type, value) => {
    try {
      const response = await axios.post('/api/admin/test-contact', { type, value });
      
      if (response.data.success) {
        toast.success(`${type} test successful`);
      } else {
        toast.error(`${type} test failed: ${response.data.message}`);
      }
    } catch (error) {
      console.error('Error testing contact:', error);
      toast.error(`Failed to test ${type}`);
    }
  };

  const getContactIcon = (type) => {
    switch (type) {
      case 'email': return <EmailIcon />;
      case 'phone': return <PhoneIcon />;
      case 'whatsapp': return <WhatsAppIcon />;
      default: return <BusinessIcon />;
    }
  };

  if (loading) {
    return (
      <EnhancedAdminLayout title="Contact Configuration">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
          <Typography>Loading contact configuration...</Typography>
        </Box>
      </EnhancedAdminLayout>
    );
  }

  return (
    <EnhancedAdminLayout title="Contact Configuration">
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            Contact Configuration
          </Typography>
          <Box>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchContactConfiguration}
              sx={{ mr: 2 }}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={<SaveIcon />}
              onClick={saveConfiguration}
              loading={saving}
            >
              Save Changes
            </Button>
          </Box>
        </Box>

        <Grid container spacing={3}>
          {/* Primary Contact Details */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Primary Contact Details
                </Typography>
                
                <Box sx={{ mb: 3 }}>
                  <TextField
                    fullWidth
                    label="Business Email"
                    value={contactConfig.primary.businessEmail}
                    onChange={(e) => handleFieldUpdate('primary', 'businessEmail', e.target.value)}
                    InputProps={{
                      startAdornment: <EmailIcon sx={{ mr: 1, color: 'action.active' }} />
                    }}
                    sx={{ mb: 2 }}
                  />
                  
                  <TextField
                    fullWidth
                    label="Support Email"
                    value={contactConfig.primary.supportEmail}
                    onChange={(e) => handleFieldUpdate('primary', 'supportEmail', e.target.value)}
                    InputProps={{
                      startAdornment: <SupportIcon sx={{ mr: 1, color: 'action.active' }} />
                    }}
                    sx={{ mb: 2 }}
                  />
                  
                  <TextField
                    fullWidth
                    label="Business Phone"
                    value={contactConfig.primary.businessPhone}
                    onChange={(e) => handleFieldUpdate('primary', 'businessPhone', e.target.value)}
                    InputProps={{
                      startAdornment: <PhoneIcon sx={{ mr: 1, color: 'action.active' }} />
                    }}
                    sx={{ mb: 2 }}
                  />
                  
                  <TextField
                    fullWidth
                    label="Support Phone"
                    value={contactConfig.primary.supportPhone}
                    onChange={(e) => handleFieldUpdate('primary', 'supportPhone', e.target.value)}
                    InputProps={{
                      startAdornment: <PhoneIcon sx={{ mr: 1, color: 'action.active' }} />
                    }}
                    sx={{ mb: 2 }}
                  />
                  
                  <TextField
                    fullWidth
                    label="WhatsApp Business"
                    value={contactConfig.primary.whatsappBusiness}
                    onChange={(e) => handleFieldUpdate('primary', 'whatsappBusiness', e.target.value)}
                    InputProps={{
                      startAdornment: <WhatsAppIcon sx={{ mr: 1, color: 'action.active' }} />
                    }}
                    sx={{ mb: 2 }}
                  />
                  
                  <TextField
                    fullWidth
                    label="WhatsApp Support"
                    value={contactConfig.primary.whatsappSupport}
                    onChange={(e) => handleFieldUpdate('primary', 'whatsappSupport', e.target.value)}
                    InputProps={{
                      startAdornment: <WhatsAppIcon sx={{ mr: 1, color: 'action.active' }} />
                    }}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Support Hours */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Support Hours
                </Typography>
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={contactConfig.hours.support.enabled}
                      onChange={(e) => handleNestedFieldUpdate('hours', 'support', 'enabled', e.target.checked)}
                    />
                  }
                  label="Enable Support Hours Display"
                  sx={{ mb: 2 }}
                />

                {contactConfig.hours.support.enabled && (
                  <Box>
                    {Object.entries(contactConfig.hours.support).filter(([key]) => key !== 'enabled').map(([day, hours]) => (
                      <Box key={day} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="body2" sx={{ minWidth: 100, textTransform: 'capitalize' }}>
                          {day}:
                        </Typography>
                        <FormControlLabel
                          control={
                            <Switch
                              size="small"
                              checked={hours.enabled}
                              onChange={(e) => handleNestedFieldUpdate('hours', 'support', day, { ...hours, enabled: e.target.checked })}
                            />
                          }
                          label=""
                          sx={{ mr: 1 }}
                        />
                        {hours.enabled && (
                          <>
                            <TextField
                              type="time"
                              size="small"
                              value={hours.start}
                              onChange={(e) => handleNestedFieldUpdate('hours', 'support', day, { ...hours, start: e.target.value })}
                              sx={{ mr: 1, width: 120 }}
                            />
                            <Typography variant="body2" sx={{ mx: 1 }}>to</Typography>
                            <TextField
                              type="time"
                              size="small"
                              value={hours.end}
                              onChange={(e) => handleNestedFieldUpdate('hours', 'support', day, { ...hours, end: e.target.value })}
                              sx={{ width: 120 }}
                            />
                          </>
                        )}
                      </Box>
                    ))}
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Auto Responses */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Auto Responses
                </Typography>
                
                <Box sx={{ mb: 3 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={contactConfig.autoResponses.email.enabled}
                        onChange={(e) => handleNestedFieldUpdate('autoResponses', 'email', 'enabled', e.target.checked)}
                      />
                    }
                    label="Email Auto Response"
                    sx={{ mb: 2 }}
                  />
                  
                  {contactConfig.autoResponses.email.enabled && (
                    <>
                      <TextField
                        fullWidth
                        label="Email Subject"
                        value={contactConfig.autoResponses.email.subject}
                        onChange={(e) => handleNestedFieldUpdate('autoResponses', 'email', 'subject', e.target.value)}
                        sx={{ mb: 2 }}
                      />
                      <TextField
                        fullWidth
                        multiline
                        rows={3}
                        label="Email Message"
                        value={contactConfig.autoResponses.email.message}
                        onChange={(e) => handleNestedFieldUpdate('autoResponses', 'email', 'message', e.target.value)}
                        sx={{ mb: 2 }}
                      />
                    </>
                  )}
                  
                  <FormControlLabel
                    control={
                      <Switch
                        checked={contactConfig.autoResponses.whatsapp.enabled}
                        onChange={(e) => handleNestedFieldUpdate('autoResponses', 'whatsapp', 'enabled', e.target.checked)}
                      />
                    }
                    label="WhatsApp Auto Response"
                    sx={{ mb: 2 }}
                  />
                  
                  {contactConfig.autoResponses.whatsapp.enabled && (
                    <TextField
                      fullWidth
                      multiline
                      rows={3}
                      label="WhatsApp Message"
                      value={contactConfig.autoResponses.whatsapp.message}
                      onChange={(e) => handleNestedFieldUpdate('autoResponses', 'whatsapp', 'message', e.target.value)}
                    />
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Integration Settings */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Integration Settings
                </Typography>
                
                <Alert severity="info" sx={{ mb: 2 }}>
                  Configure third-party integrations for automated contact management
                </Alert>
                
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    WhatsApp Business API
                  </Typography>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={contactConfig.integrations.whatsappBusiness.enabled}
                        onChange={(e) => handleNestedFieldUpdate('integrations', 'whatsappBusiness', 'enabled', e.target.checked)}
                      />
                    }
                    label="Enable WhatsApp Business API"
                    sx={{ mb: 2 }}
                  />
                  
                  {contactConfig.integrations.whatsappBusiness.enabled && (
                    <>
                      <TextField
                        fullWidth
                        label="API Key"
                        type="password"
                        value={contactConfig.integrations.whatsappBusiness.apiKey}
                        onChange={(e) => handleNestedFieldUpdate('integrations', 'whatsappBusiness', 'apiKey', e.target.value)}
                        sx={{ mb: 2 }}
                      />
                      <TextField
                        fullWidth
                        label="Phone Number ID"
                        value={contactConfig.integrations.whatsappBusiness.phoneNumberId}
                        onChange={(e) => handleNestedFieldUpdate('integrations', 'whatsappBusiness', 'phoneNumberId', e.target.value)}
                        sx={{ mb: 2 }}
                      />
                    </>
                  )}
                </Box>

                <Divider sx={{ my: 2 }} />

                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Email Service Configuration
                  </Typography>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Email Provider</InputLabel>
                    <Select
                      value={contactConfig.integrations.emailService.provider}
                      onChange={(e) => handleNestedFieldUpdate('integrations', 'emailService', 'provider', e.target.value)}
                    >
                      <MenuItem value="brevo">Brevo (Sendinblue)</MenuItem>
                      <MenuItem value="sendgrid">SendGrid</MenuItem>
                      <MenuItem value="mailgun">Mailgun</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <TextField
                    fullWidth
                    label="From Email"
                    value={contactConfig.integrations.emailService.fromEmail}
                    onChange={(e) => handleNestedFieldUpdate('integrations', 'emailService', 'fromEmail', e.target.value)}
                    sx={{ mb: 2 }}
                  />
                  
                  <TextField
                    fullWidth
                    label="From Name"
                    value={contactConfig.integrations.emailService.fromName}
                    onChange={(e) => handleNestedFieldUpdate('integrations', 'emailService', 'fromName', e.target.value)}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <ToastContainer position="top-right" />
      </Box>
    </EnhancedAdminLayout>
  );
}
