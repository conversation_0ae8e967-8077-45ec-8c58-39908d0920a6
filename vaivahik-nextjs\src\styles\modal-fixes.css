/* Modal Fixes CSS */

/* Force body to show modal */
body.modal-open {
  overflow: hidden;
}

/* Force modal to display */
.modal-overlay {
  display: flex !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  z-index: 2000 !important;
  padding: 20px !important;
  box-sizing: border-box !important;
}

/* Force display for modal elements */
.modal-overlay,
.modal,
.modal-header,
.modal-body,
.modal-footer,
.user-profile-section,
.user-profile-header,
.user-avatar,
.user-profile-info,
.tab-section,
.user-details-grid,
.report-info-grid,
.documents-grid,
.evidence-grid,
.document-card,
.evidence-item,
.document-preview,
.evidence-preview,
.document-info,
.evidence-info,
.detail-item,
.action-buttons-container,
.action-buttons-row {
  display: block !important;
}

/* Flex exceptions */
.modal-overlay,
.modal-header,
.modal-footer,
.user-profile-header,
.document-preview,
.evidence-preview,
.action-buttons-row {
  display: flex !important;
}

/* Modal container */
.modal {
  display: flex !important;
  flex-direction: column !important;
  background-color: white !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  width: 100% !important;
  max-width: 800px !important;
  max-height: 90vh !important;
  margin: 0 auto !important;
  position: relative !important;
  overflow: hidden !important;
}

/* Modal header */
.modal-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 15px 20px !important;
  border-bottom: 1px solid #eee !important;
  position: sticky !important;
  top: 0 !important;
  background-color: white !important;
  z-index: 10 !important;
}

/* Modal title */
.modal-title {
  margin: 0 !important;
  font-size: 1.5rem !important;
  color: #333 !important;
}

/* Close button */
.modal-close-button {
  background: none !important;
  border: none !important;
  font-size: 24px !important;
  cursor: pointer !important;
  color: #999 !important;
}

.modal-close-button:hover {
  color: #333 !important;
}

/* Modal body */
.modal-body {
  padding: 20px !important;
  overflow-y: auto !important;
  max-height: calc(90vh - 130px) !important;
  flex: 1 !important;
}

/* Modal footer */
.modal-footer {
  display: flex !important;
  justify-content: flex-end !important;
  gap: 10px !important;
  padding: 15px 20px !important;
  border-top: 1px solid #eee !important;
  position: sticky !important;
  bottom: 0 !important;
  background-color: white !important;
  z-index: 10 !important;
}

/* Grid exceptions */
.user-details-grid,
.report-info-grid,
.documents-grid,
.evidence-grid {
  display: grid !important;
}

/* Ensure images are displayed correctly */
.user-avatar img,
.document-preview img,
.evidence-preview img,
.document-full-image {
  max-width: 100% !important;
  height: auto !important;
  object-fit: cover !important;
}

/* Ensure document modal has proper dimensions */
.document-modal {
  max-width: 90vw !important;
}

/* Ensure document preview has proper dimensions */
.document-full-image {
  max-height: 70vh !important;
}
