import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Verify JWT token
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ success: false, message: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const userId = decoded.userId;

    // Get current user with profile and preferences
    const currentUser = await prisma.user.findUnique({
      where: { id: userId },
      include: { 
        profile: true,
        preference: true
      }
    });

    if (!currentUser || !currentUser.profile) {
      return res.status(400).json({
        success: false,
        message: 'User profile not found'
      });
    }

    // Base query for potential matches
    const baseQuery = {
      AND: [
        { id: { not: userId } },
        { profileStatus: 'ACTIVE' },
        { profile: { gender: { not: currentUser.profile.gender } } }
      ]
    };

    // Get user interactions for personalization
    const userInteractions = await prisma.userInteraction.findMany({
      where: { userId: userId },
      include: { targetUser: { include: { profile: true } } }
    });

    const userLikes = await prisma.profileLike.findMany({
      where: { userId: userId },
      include: { targetUser: { include: { profile: true } } }
    });

    const userShortlist = await prisma.shortlist.findMany({
      where: { userId: userId }
    });

    const sentInterests = await prisma.interest.findMany({
      where: { userId: userId }
    });

    const receivedInterests = await prisma.interest.findMany({
      where: { targetUserId: userId, status: 'ACCEPTED' }
    });

    // Helper function to format user data
    const formatUser = (user) => ({
      id: user.id,
      firstName: user.profile?.firstName || 'Unknown',
      lastName: user.profile?.lastName || '',
      age: user.profile?.age,
      height: user.profile?.height,
      city: user.profile?.city,
      state: user.profile?.state,
      education: user.profile?.education,
      occupation: user.profile?.occupation,
      profilePicture: user.profile?.profilePicUrl,
      isVerified: user.isVerified,
      isPremium: user.isPremium,
      lastActive: user.lastActiveAt,
      compatibility: Math.floor(Math.random() * 30) + 70, // Mock compatibility for now
      aiInsight: generateAIInsight(user.profile, currentUser.profile),
      isLiked: userLikes.some(like => like.targetUserId === user.id),
      isShortlisted: userShortlist.some(item => item.targetUserId === user.id)
    });

    // 1. AI-Powered Matches (using ML service)
    const aiMatches = await getAIPoweredMatches(currentUser, baseQuery, 12);

    // 2. Verified User Matches
    const verifiedMatches = await prisma.user.findMany({
      where: {
        ...baseQuery,
        isVerified: true
      },
      include: { profile: true },
      take: 12,
      orderBy: { lastActiveAt: 'desc' }
    });

    // 3. Premium Matches
    const premiumMatches = await prisma.user.findMany({
      where: {
        ...baseQuery,
        isPremium: true
      },
      include: { profile: true },
      take: 12,
      orderBy: { createdAt: 'desc' }
    });

    // 4. Recent Activity Matches
    const recentMatches = await prisma.user.findMany({
      where: {
        ...baseQuery,
        lastActiveAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        }
      },
      include: { profile: true },
      take: 12,
      orderBy: { lastActiveAt: 'desc' }
    });

    // 5. Mutual Interest Matches
    const mutualInterestUserIds = receivedInterests.map(interest => interest.userId);
    const mutualMatches = await prisma.user.findMany({
      where: {
        ...baseQuery,
        id: { in: mutualInterestUserIds }
      },
      include: { profile: true },
      take: 12
    });

    // 6. Location-Based Matches
    const locationMatches = await prisma.user.findMany({
      where: {
        ...baseQuery,
        profile: {
          OR: [
            { city: currentUser.profile.city },
            { state: currentUser.profile.state }
          ]
        }
      },
      include: { profile: true },
      take: 12,
      orderBy: { lastActiveAt: 'desc' }
    });

    // 7. High Compatibility Matches (90%+)
    // For now, we'll simulate this by selecting users with similar profiles
    const highCompatibilityMatches = await prisma.user.findMany({
      where: {
        ...baseQuery,
        profile: {
          education: currentUser.profile.education
        }
      },
      include: { profile: true },
      take: 12,
      orderBy: { createdAt: 'desc' }
    });

    // Format all matches
    const categorizedMatches = {
      ai_powered: aiMatches.map(formatUser),
      verified: verifiedMatches.map(formatUser),
      premium: premiumMatches.map(formatUser),
      recent_activity: recentMatches.map(formatUser),
      mutual_interest: mutualMatches.map(formatUser),
      location_based: locationMatches.map(formatUser),
      high_compatibility: highCompatibilityMatches.map(formatUser)
    };

    // Calculate stats
    const stats = {
      ai_powered: categorizedMatches.ai_powered.length,
      verified: categorizedMatches.verified.length,
      premium: categorizedMatches.premium.length,
      recent_activity: categorizedMatches.recent_activity.length,
      mutual_interest: categorizedMatches.mutual_interest.length,
      location_based: categorizedMatches.location_based.length,
      high_compatibility: categorizedMatches.high_compatibility.length
    };

    return res.status(200).json({
      success: true,
      matches: categorizedMatches,
      stats: stats,
      userProfile: {
        isPremium: currentUser.isPremium,
        isVerified: currentUser.isVerified
      }
    });

  } catch (error) {
    console.error('Error fetching categorized matches:', error);
    return res.status(500).json({
      success: false,
      message: 'Error fetching matches',
      error: error.message
    });
  }
}

// Helper function to get AI-powered matches
async function getAIPoweredMatches(currentUser, baseQuery, limit) {
  try {
    // Try to use ML matching service if available
    const mlMatchingService = require('../../../services/mlMatchingService');
    const mlMatches = await mlMatchingService.getMatches(
      currentUser.id,
      limit,
      0,
      70 // minimum score
    );
    
    if (mlMatches && mlMatches.matches) {
      return mlMatches.matches;
    }
  } catch (error) {
    console.log('ML service not available, using fallback matching');
  }

  // Fallback to database query
  return await prisma.user.findMany({
    where: baseQuery,
    include: { profile: true },
    take: limit,
    orderBy: [
      { isVerified: 'desc' },
      { isPremium: 'desc' },
      { lastActiveAt: 'desc' }
    ]
  });
}

// Helper function to generate AI insights
function generateAIInsight(targetProfile, currentProfile) {
  const insights = [
    "Strong compatibility in career goals and life values",
    "Similar educational background suggests good intellectual match",
    "Complementary personalities based on profile analysis",
    "Shared interests in family values and traditions",
    "Geographic proximity enhances relationship potential",
    "Compatible lifestyle preferences and future plans",
    "Strong foundation for long-term relationship success",
    "Aligned values in family and cultural traditions"
  ];

  // Simple logic to make insights more relevant
  if (targetProfile.education === currentProfile.education) {
    return "Similar educational background suggests excellent intellectual compatibility";
  }
  
  if (targetProfile.city === currentProfile.city) {
    return "Geographic proximity and shared local culture enhance compatibility";
  }
  
  if (targetProfile.occupation && currentProfile.occupation) {
    return "Professional backgrounds suggest compatible lifestyle and goals";
  }

  return insights[Math.floor(Math.random() * insights.length)];
}
