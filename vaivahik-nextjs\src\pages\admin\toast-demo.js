import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  TextField,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Divider,
  Card,
  CardContent,
  CardHeader
} from '@mui/material';
import dynamic from 'next/dynamic';
import { useToast } from '@/contexts/ToastContext';
import FormErrorHandler from '@/components/common/FormErrorHandler';
import { getErrorResolution } from '@/utils/errorResolutionDatabase';
import withAuth from '@/hoc/withAuth';
import Head from 'next/head';

// Import EnhancedAdminLayout with <PERSON> disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);

const ToastDemoPage = () => {
  const toast = useToast();
  const [errorCode, setErrorCode] = useState('AUTH_001');
  const [customMessage, setCustomMessage] = useState('');
  const [formErrors, setFormErrors] = useState({});

  // Get all error codes from the database
  const errorCodes = [
    'AUTH_001', 'AUTH_002', 'AUTH_003',
    'PROF_001', 'PROF_002', 'PROF_003',
    'PAY_001', 'PAY_002',
    'NET_001', 'NET_002',
    'VAL_001', 'VAL_002', 'VAL_003',
    'PERM_001',
    'GEN_001'
  ];

  // Show a regular toast
  const showRegularToast = () => {
    toast.showToast('This is a regular toast notification');
  };

  // Show a toast with undo functionality
  const showUndoToast = () => {
    toast.showUndoToast(
      'Action completed successfully',
      () => {
        toast.showToast('Action undone!');
      }
    );
  };

  // Show a success toast
  const showSuccessToast = () => {
    toast.showSuccess('Operation completed successfully');
  };

  // Show an error toast
  const showErrorToast = () => {
    toast.showError('An error occurred');
  };

  // Show a warning toast
  const showWarningToast = () => {
    toast.showWarning('Warning: This action cannot be undone');
  };

  // Show an info toast
  const showInfoToast = () => {
    toast.showInfo('Did you know? You can customize these notifications');
  };

  // Show a guided error toast
  const showGuidedErrorToast = () => {
    const resolution = getErrorResolution(errorCode);

    if (resolution) {
      toast.showGuidedError(
        customMessage || resolution.message,
        {
          errorCode: resolution.code,
          type: resolution.type,
          steps: resolution.steps,
          onHelp: resolution.helpUrl ? () => {
            window.open(resolution.helpUrl, '_blank');
          } : undefined
        }
      );
    } else {
      toast.showError('Error resolution not found for this code');
    }
  };

  // Simulate a form submission with validation errors
  const simulateFormErrors = () => {
    setFormErrors({
      email: { type: 'pattern', message: 'Invalid email format' },
      password: { type: 'minLength', message: 'Password must be at least 8 characters' },
      confirmPassword: { type: 'validate', message: 'Passwords do not match' }
    });
  };

  // Clear form errors
  const clearFormErrors = () => {
    setFormErrors({});
  };

  return (
    <>
      <Head>
        <title>Toast Notifications Demo | Vaivahik Admin</title>
      </Head>
      <EnhancedAdminLayout title="Toast Demo">
        <Box sx={{ p: 3 }}>
          <Typography variant="h4" gutterBottom>
            Toast Notifications & Guided Error Resolution Demo
          </Typography>

          <Typography variant="body1" paragraph>
            This page demonstrates the different types of toast notifications and guided error resolution available in the application.
          </Typography>

          <Grid container spacing={3}>
            {/* Basic Toast Types */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Basic Toast Types" />
                <CardContent>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <Button variant="contained" onClick={showRegularToast}>
                      Show Regular Toast
                    </Button>

                    <Button variant="contained" onClick={showUndoToast}>
                      Show Toast with Undo
                    </Button>

                    <Button variant="contained" color="success" onClick={showSuccessToast}>
                      Show Success Toast
                    </Button>

                    <Button variant="contained" color="error" onClick={showErrorToast}>
                      Show Error Toast
                    </Button>

                    <Button variant="contained" color="warning" onClick={showWarningToast}>
                      Show Warning Toast
                    </Button>

                    <Button variant="contained" color="info" onClick={showInfoToast}>
                      Show Info Toast
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Guided Error Resolution */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Guided Error Resolution" />
                <CardContent>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <FormControl fullWidth>
                      <InputLabel id="error-code-label">Error Code</InputLabel>
                      <Select
                        labelId="error-code-label"
                        value={errorCode}
                        label="Error Code"
                        onChange={(e) => setErrorCode(e.target.value)}
                      >
                        {errorCodes.map((code) => (
                          <MenuItem key={code} value={code}>
                            {code}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>

                    <TextField
                      label="Custom Error Message (optional)"
                      value={customMessage}
                      onChange={(e) => setCustomMessage(e.target.value)}
                      fullWidth
                    />

                    <Button
                      variant="contained"
                      color="primary"
                      onClick={showGuidedErrorToast}
                      sx={{ mt: 2 }}
                    >
                      Show Guided Error Toast
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Form Error Handler */}
            <Grid item xs={12}>
              <Card>
                <CardHeader title="Form Error Handler" />
                <CardContent>
                  <Typography variant="body2" paragraph>
                    This demonstrates how form validation errors can be displayed with guided resolution steps.
                  </Typography>

                  <FormErrorHandler
                    errors={formErrors}
                    title="Form Validation Errors"
                    showGuide={true}
                    onHelp={() => window.open('/help/form-validation', '_blank')}
                  />

                  <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={simulateFormErrors}
                    >
                      Simulate Form Errors
                    </Button>

                    <Button
                      variant="outlined"
                      onClick={clearFormErrors}
                    >
                      Clear Form Errors
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </EnhancedAdminLayout>
    </>
  );
};

export default withAuth(ToastDemoPage, ['ADMIN', 'SUPER_ADMIN']);
