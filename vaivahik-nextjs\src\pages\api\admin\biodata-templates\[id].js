import { withAuth } from '@/utils/authHandler';
import { handleApiError } from '@/utils/errorHandler';
import { createRouter } from 'next-connect';

// Mock data for biodata templates
const mockBiodataTemplates = [
  {
    id: 1,
    name: "Traditional Maratha Style",
    description: "Classic Maratha style biodata with traditional elements and design",
    previewImage: "/images/biodata-templates/traditional-maratha.jpg",
    designFile: "/templates/biodata/traditional-maratha.html",
    price: 499,
    discountPercent: 10,
    discountedPrice: 449.1,
    isActive: true,
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(),
    purchaseCount: 45,
    downloadCount: 38,
    revenue: 20205
  },
  {
    id: 2,
    name: "Modern Professional",
    description: "Clean and professional biodata template with modern design elements",
    previewImage: "/images/biodata-templates/modern-professional.jpg",
    designFile: "/templates/biodata/modern-professional.html",
    price: 599,
    discountPercent: null,
    discountedPrice: null,
    isActive: true,
    createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(),
    purchaseCount: 32,
    downloadCount: 28,
    revenue: 19168
  },
  {
    id: 3,
    name: "Elegant Floral",
    description: "Beautiful floral-themed biodata template with elegant typography",
    previewImage: "/images/biodata-templates/elegant-floral.jpg",
    designFile: "/templates/biodata/elegant-floral.html",
    price: 649,
    discountPercent: 15,
    discountedPrice: 551.65,
    isActive: true,
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(),
    purchaseCount: 28,
    downloadCount: 25,
    revenue: 15446.2
  },
  {
    id: 4,
    name: "Royal Heritage",
    description: "Premium template inspired by royal Maratha heritage and traditions",
    previewImage: "/images/biodata-templates/royal-heritage.jpg",
    designFile: "/templates/biodata/royal-heritage.html",
    price: 799,
    discountPercent: 5,
    discountedPrice: 759.05,
    isActive: true,
    createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(),
    purchaseCount: 18,
    downloadCount: 15,
    revenue: 13662.9
  }
];

// Create a router
const router = createRouter();

// Middleware to check admin authentication (simplified for development)
async function checkAdminAuth(req, res, next) {
  // In a real app, we would check the session here
  // For now, we'll just allow all requests
  next();
}

// Apply middleware
router.use(checkAdminAuth);

// Handle GET request to fetch a specific template
router.get(async (req, res) => {
  try {
    const { id } = req.query;

    // Find the template in our mock data
    const template = mockBiodataTemplates.find(t => t.id === parseInt(id));

    if (!template) {
      return res.status(404).json({ success: false, message: 'Template not found' });
    }

    return res.status(200).json({ success: true, template });
  } catch (error) {
    console.error('Error fetching biodata template:', error);
    return res.status(500).json({ success: false, message: 'Failed to fetch biodata template' });
  }
});

// Handle PUT request to update a template
router.put(async (req, res) => {
  try {
    const { id } = req.query;
    const {
      name,
      description,
      price,
      discountPercent,
      isActive
    } = req.body;

    // Find the template in our mock data
    const templateIndex = mockBiodataTemplates.findIndex(t => t.id === parseInt(id));

    if (templateIndex === -1) {
      return res.status(404).json({ success: false, message: 'Template not found' });
    }

    const existingTemplate = mockBiodataTemplates[templateIndex];

    // Prepare update data
    const updateData = {
      ...existingTemplate,
      name: name || existingTemplate.name,
      description: description || existingTemplate.description,
      isActive: isActive === 'true' || isActive === true,
      updatedAt: new Date()
    };

    // Update price and discount if provided
    if (price) {
      updateData.price = parseFloat(price);

      // Calculate discounted price if discount is provided
      if (discountPercent && parseInt(discountPercent) > 0) {
        const discount = parseInt(discountPercent);
        updateData.discountPercent = discount;
        updateData.discountedPrice = updateData.price - (updateData.price * (discount / 100));
      } else {
        updateData.discountPercent = null;
        updateData.discountedPrice = null;
      }
    }

    // Update the template in our mock data
    mockBiodataTemplates[templateIndex] = updateData;

    return res.status(200).json({ success: true, template: updateData });
  } catch (error) {
    console.error('Error updating biodata template:', error);
    return res.status(500).json({ success: false, message: 'Failed to update biodata template' });
  }
});

// Handle DELETE request to delete a template
router.delete(async (req, res) => {
  try {
    const { id } = req.query;

    // Find the template in our mock data
    const templateIndex = mockBiodataTemplates.findIndex(t => t.id === parseInt(id));

    if (templateIndex === -1) {
      return res.status(404).json({ success: false, message: 'Template not found' });
    }

    const existingTemplate = mockBiodataTemplates[templateIndex];

    // Check if template has been purchased
    if (existingTemplate.purchaseCount > 0) {
      // Instead of deleting, mark as inactive
      mockBiodataTemplates[templateIndex] = {
        ...existingTemplate,
        isActive: false,
        updatedAt: new Date()
      };

      return res.status(200).json({
        success: true,
        message: 'Template has been purchased by users and has been marked as inactive instead of deleted'
      });
    }

    // Remove the template from our mock data
    mockBiodataTemplates.splice(templateIndex, 1);

    return res.status(200).json({ success: true, message: 'Template deleted successfully' });
  } catch (error) {
    console.error('Error deleting biodata template:', error);
    return res.status(500).json({ success: false, message: 'Failed to delete biodata template' });
  }
});

// Export the router with authentication middleware
export default withAuth(router.handler({
  onError: (err, req, res) => {
    console.error(err.stack);
    res.status(500).json({ success: false, message: err.message });
  },
  onNoMatch: (req, res) => {
    res.status(405).json({ success: false, message: `Method '${req.method}' not allowed` });
  },
}), 'ADMIN');
