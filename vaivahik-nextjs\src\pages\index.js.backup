import { useState, useEffect } from "react";
import Head from "next/head";
import Image from "next/image";
import Link from "next/link";
import styles from "@/styles/Landing.module.css";

export default function Home() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isNavActive, setIsNavActive] = useState(false);
  const [activePricingTab, setActivePricingTab] = useState('monthly');

  // Handle scroll event to change header style
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);

    // Create floating hearts
    const createHearts = () => {
      const heartsContainer = document.getElementById('floating-hearts-container');
      if (heartsContainer) {
        for (let i = 0; i < 15; i++) {
          createHeart(heartsContainer);
        }
      }
    };

    const createHeart = (container) => {
      const heart = document.createElement('div');
      heart.classList.add(styles.heart);

      // Random positioning
      heart.style.left = `${Math.random() * 100}%`;
      heart.style.animationDuration = `${15 + Math.random() * 10}s`;
      heart.style.animationDelay = `${Math.random() * 5}s`;
      heart.style.opacity = `${0.3 + Math.random() * 0.5}`;

      container.appendChild(heart);

      // Remove heart after animation completes
      setTimeout(() => {
        heart.remove();
        createHeart(container);
      }, 25000);
    };

    createHearts();

    // Add spin-back animation to logo hearts
    const setupLogoHeartAnimations = () => {
      const logoHearts = document.querySelectorAll(`.${styles.logoSymbol}`);

      logoHearts.forEach(heart => {
        const parentLink = heart.closest('a');

        if (parentLink) {
          parentLink.addEventListener('mouseleave', () => {
            // Remove the class first to reset the animation
            heart.classList.remove(styles.spinBack);

            // Force a reflow to ensure the class removal takes effect
            void heart.offsetWidth;

            // Add the class back to trigger the animation
            heart.classList.add(styles.spinBack);

            // Remove the class after animation completes
            setTimeout(() => {
              heart.classList.remove(styles.spinBack);
            }, 600);
          });
        }
      });
    };

    // Call the function after a short delay to ensure DOM is ready
    setTimeout(setupLogoHeartAnimations, 500);

    return () => {
      window.removeEventListener('scroll', handleScroll);

      // Clean up event listeners
      const logoHearts = document.querySelectorAll(`.${styles.logoSymbol}`);
      logoHearts.forEach(heart => {
        const parentLink = heart.closest('a');
        if (parentLink) {
          parentLink.removeEventListener('mouseleave', () => {});
        }
      });
    };
  }, []);

  // Toggle mobile navigation
  const toggleNav = () => {
    setIsNavActive(!isNavActive);
  };

  // Switch pricing tabs
  const switchPricingTab = (tab) => {
    setActivePricingTab(tab);
  };

  return (
    <>
      <Head>
        <title>Vaivahik - AI-Powered Matrimony for Indian Families</title>
        <meta name="description" content="Discover meaningful connections with Vaivahik, where modern AI meets timeless Indian traditions to help families find the perfect match." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className={styles.page}>
        {/* Header */}
        <header className={`${styles.header} ${isScrolled ? styles.headerScrolled : ''}`} id="main-header">
          <div className={styles.headerContainer}>
            <Link href="/" className={styles.logo}>
              <svg className={styles.logoSymbol} viewBox="0 0 512 512" width="24" height="24">
                <defs>
                  <linearGradient id="heartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#FF5F6D" />
                    <stop offset="100%" stopColor="#FFC371" />
                  </linearGradient>
                </defs>
                <path d="M462.3 62.6C407.5 15.9 326 24.3 275.7 76.2L256 96.5l-19.7-20.3C186.1 24.3 104.5 15.9 49.7 62.6c-62.8 53.6-66.1 149.8-9.9 207.9l193.5 199.8c12.5 12.9 32.8 12.9 45.3 0l193.5-199.8c56.3-58.1 53-154.3-9.8-207.9z" fill="url(#heartGradient)"/>
              </svg>
              <span>Vaivahik</span>
            </Link>
            <nav className={`${styles.nav} ${isNavActive ? styles.navActive : ''}`}>
              <ul>
                <li><Link href="#features">Features</Link></li>
                <li><Link href="#how-it-works">How It Works</Link></li>
                <li><Link href="#testimonials">Testimonials</Link></li>
                <li><Link href="#pricing">Pricing</Link></li>
                <li><Link href="/register" className={`${styles.btn} ${styles.navCta}`}>Sign In / Sign Up</Link></li>
              </ul>
            </nav>
            <button
              className={`${styles.hamburger} ${isNavActive ? styles.hamburgerActive : ''}`}
              onClick={toggleNav}
            >
              <div className={styles.line1}></div>
              <div className={styles.line2}></div>
              <div className={styles.line3}></div>
            </button>
          </div>
        </header>

        {/* Hero Section */}
        <section id="hero" className={styles.hero}>
          <div className={styles.floatingHearts} id="floating-hearts-container"></div>
          <div className={styles.container}>
            <div className={styles.heroContent}>
              <h1>Begin Your Journey to Forever, Together.</h1>
              <p>Discover meaningful connections with Vaivahik, where modern AI meets timeless Indian traditions to help families find the perfect match.</p>
              <div className={styles.heroButtons}>
                <Link href="#pricing" className={`${styles.btn} ${styles.btnPrimary}`}>Explore Membership Plans</Link>
                <Link href="#features" className={`${styles.btn} ${styles.btnOutline}`}>Learn How Vaivahik Works</Link>
              </div>
            </div>
          </div>
        </section>
