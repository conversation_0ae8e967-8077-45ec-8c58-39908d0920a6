/**
 * Mobile Responsiveness Hook
 * Provides responsive design utilities and device detection
 */

import { useState, useEffect } from 'react';
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';

export const useResponsive = () => {
  const theme = useTheme();
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });

  // MUI breakpoints
  const isXs = useMediaQuery(theme.breakpoints.only('xs'));
  const isSm = useMediaQuery(theme.breakpoints.only('sm'));
  const isMd = useMediaQuery(theme.breakpoints.only('md'));
  const isLg = useMediaQuery(theme.breakpoints.only('lg'));
  const isXl = useMediaQuery(theme.breakpoints.only('xl'));

  // Common responsive queries
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.between('md', 'lg'));
  const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  // Device orientation
  const [orientation, setOrientation] = useState('portrait');

  // Touch device detection
  const [isTouchDevice, setIsTouchDevice] = useState(false);

  // Network information
  const [networkInfo, setNetworkInfo] = useState({
    effectiveType: '4g',
    downlink: 10,
    rtt: 100
  });

  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });

      // Update orientation
      setOrientation(window.innerWidth > window.innerHeight ? 'landscape' : 'portrait');
    };

    const handleOrientationChange = () => {
      setTimeout(() => {
        setOrientation(window.innerWidth > window.innerHeight ? 'landscape' : 'portrait');
      }, 100);
    };

    // Touch device detection
    const checkTouchDevice = () => {
      setIsTouchDevice(
        'ontouchstart' in window ||
        navigator.maxTouchPoints > 0 ||
        navigator.msMaxTouchPoints > 0
      );
    };

    // Network information
    const updateNetworkInfo = () => {
      if ('connection' in navigator) {
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
        setNetworkInfo({
          effectiveType: connection.effectiveType || '4g',
          downlink: connection.downlink || 10,
          rtt: connection.rtt || 100
        });
      }
    };

    // Initial setup
    handleResize();
    checkTouchDevice();
    updateNetworkInfo();

    // Event listeners
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);
    
    if ('connection' in navigator) {
      navigator.connection.addEventListener('change', updateNetworkInfo);
    }

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
      
      if ('connection' in navigator) {
        navigator.connection.removeEventListener('change', updateNetworkInfo);
      }
    };
  }, []);

  // Responsive values helper
  const getResponsiveValue = (values) => {
    if (typeof values === 'object') {
      if (isXs && values.xs !== undefined) return values.xs;
      if (isSm && values.sm !== undefined) return values.sm;
      if (isMd && values.md !== undefined) return values.md;
      if (isLg && values.lg !== undefined) return values.lg;
      if (isXl && values.xl !== undefined) return values.xl;
      
      // Fallback logic
      if (isMobile && values.mobile !== undefined) return values.mobile;
      if (isTablet && values.tablet !== undefined) return values.tablet;
      if (isDesktop && values.desktop !== undefined) return values.desktop;
      
      return values.default || values;
    }
    return values;
  };

  // Grid columns helper
  const getGridColumns = (defaultCols = 12) => {
    if (isXs) return Math.min(defaultCols, 1);
    if (isSm) return Math.min(defaultCols, 2);
    if (isMd) return Math.min(defaultCols, 3);
    if (isLg) return Math.min(defaultCols, 4);
    return defaultCols;
  };

  // Spacing helper
  const getSpacing = (base = 2) => {
    if (isXs) return base * 0.5;
    if (isSm) return base * 0.75;
    return base;
  };

  // Font size helper
  const getFontSize = (base = 16) => {
    if (isXs) return base * 0.875;
    if (isSm) return base * 0.9375;
    return base;
  };

  // Container max width
  const getContainerMaxWidth = () => {
    if (isXs) return '100%';
    if (isSm) return '600px';
    if (isMd) return '960px';
    if (isLg) return '1280px';
    return '1920px';
  };

  // Performance optimization for mobile
  const shouldReduceAnimations = () => {
    return isMobile || networkInfo.effectiveType === 'slow-2g' || networkInfo.effectiveType === '2g';
  };

  // Image quality based on device and network
  const getImageQuality = () => {
    if (networkInfo.effectiveType === 'slow-2g' || networkInfo.effectiveType === '2g') {
      return 'low';
    }
    if (networkInfo.effectiveType === '3g' || isMobile) {
      return 'medium';
    }
    return 'high';
  };

  // Adaptive loading strategy
  const getLoadingStrategy = () => {
    if (networkInfo.effectiveType === 'slow-2g' || networkInfo.effectiveType === '2g') {
      return 'minimal';
    }
    if (networkInfo.effectiveType === '3g') {
      return 'progressive';
    }
    return 'full';
  };

  return {
    // Window dimensions
    windowSize,
    
    // Breakpoint booleans
    isXs,
    isSm,
    isMd,
    isLg,
    isXl,
    isMobile,
    isTablet,
    isDesktop,
    isSmallScreen,
    
    // Device info
    orientation,
    isTouchDevice,
    networkInfo,
    
    // Helper functions
    getResponsiveValue,
    getGridColumns,
    getSpacing,
    getFontSize,
    getContainerMaxWidth,
    shouldReduceAnimations,
    getImageQuality,
    getLoadingStrategy,
    
    // Responsive styles
    responsiveStyles: {
      container: {
        maxWidth: getContainerMaxWidth(),
        margin: '0 auto',
        padding: getSpacing(2)
      },
      text: {
        fontSize: getFontSize(),
        lineHeight: isMobile ? 1.4 : 1.6
      },
      spacing: {
        section: getSpacing(4),
        component: getSpacing(2),
        element: getSpacing(1)
      }
    }
  };
};

// HOC for responsive components
export const withResponsive = (Component) => {
  return function ResponsiveComponent(props) {
    const responsive = useResponsive();
    return <Component {...props} responsive={responsive} />;
  };
};

// Responsive Grid Component
export const ResponsiveGrid = ({ children, columns = 12, spacing = 2, ...props }) => {
  const { getGridColumns, getSpacing } = useResponsive();
  
  const responsiveColumns = getGridColumns(columns);
  const responsiveSpacing = getSpacing(spacing);
  
  return (
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: `repeat(${responsiveColumns}, 1fr)`,
        gap: `${responsiveSpacing * 8}px`,
        ...props.style
      }}
      {...props}
    >
      {children}
    </div>
  );
};

// Responsive Text Component
export const ResponsiveText = ({ variant = 'body1', children, ...props }) => {
  const { getFontSize, isMobile } = useResponsive();
  
  const fontSizes = {
    h1: isMobile ? 24 : 32,
    h2: isMobile ? 20 : 28,
    h3: isMobile ? 18 : 24,
    h4: isMobile ? 16 : 20,
    h5: isMobile ? 14 : 18,
    h6: isMobile ? 12 : 16,
    body1: isMobile ? 14 : 16,
    body2: isMobile ? 12 : 14,
    caption: isMobile ? 10 : 12
  };
  
  return (
    <span
      style={{
        fontSize: `${fontSizes[variant]}px`,
        lineHeight: isMobile ? 1.4 : 1.6,
        ...props.style
      }}
      {...props}
    >
      {children}
    </span>
  );
};

// Responsive Image Component
export const ResponsiveImage = ({ src, alt, ...props }) => {
  const { getImageQuality, isMobile } = useResponsive();
  
  const quality = getImageQuality();
  const qualitySuffix = quality === 'low' ? '_low' : quality === 'medium' ? '_med' : '';
  
  // Modify src based on quality if using a CDN with quality parameters
  const optimizedSrc = src.includes('?') 
    ? `${src}&q=${quality === 'low' ? 30 : quality === 'medium' ? 60 : 90}`
    : src;
  
  return (
    <img
      src={optimizedSrc}
      alt={alt}
      loading="lazy"
      style={{
        maxWidth: '100%',
        height: 'auto',
        ...props.style
      }}
      {...props}
    />
  );
};

export default useResponsive;
