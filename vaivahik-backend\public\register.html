<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign In/Sign Up - Vaivahik</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/material_pink.css">
    <style>
        :root {
            --primary-color: #FF5F6D;
            --primary-light: #FFC371;
            --secondary-color: #8A2BE2;
            --dark-color: #2D3047;
            --light-color: #F8F9FA;
            --white: #FFFFFF;
            --error-color: #dc3545;
            --success-color: #28a745;
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Montserrat', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .registration-container {
            background: var(--white);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 600px;
            padding: 40px;
            position: relative;
            overflow: hidden;
        }

        .progress-bar {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: #eee;
        }

        .progress-indicator {
            height: 100%;
            background: var(--primary-color);
            width: 0;
            transition: width 0.3s ease;
        }

        .auth-step {
            display: none;
        }

        .auth-step-1 {
            display: block;
        }

        h2 {
            color: var(--dark-color);
            margin-bottom: 30px;
            text-align: center;
            font-size: 28px;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--dark-color);
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #eee;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background-color: white;
        }

        .form-group input:focus,
        .form-group select:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(255, 95, 109, 0.1);
        }

        button {
            width: 100%;
            padding: 15px;
            background: var(--primary-color);
            color: var(--white);
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        button:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }

        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 8px;
            color: var(--white);
            font-weight: 500;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s ease;
        }

        .toast.show {
            opacity: 1;
            transform: translateY(0);
        }

        .toast-success {
            background: var(--success-color);
        }

        .toast-error {
            background: var(--error-color);
        }

        .step-title {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
        }

        .step-number {
            width: 40px;
            height: 40px;
            background: var(--primary-color);
            color: var(--white);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 15px;
        }

        .otp-inputs {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }

        .otp-input {
            width: 50px;
            height: 50px;
            text-align: center;
            font-size: 24px;
            border: 2px solid #eee;
            border-radius: 8px;
        }

        .timer {
            text-align: center;
            margin: 15px 0;
            color: var(--dark-color);
        }

        .resend-btn {
            background: none;
            color: var(--primary-color);
            text-decoration: underline;
            padding: 5px;
            margin-top: 10px;
        }

        .resend-btn:hover {
            background: none;
            color: var(--secondary-color);
            transform: none;
        }

        .welcome-text {
            text-align: center;
            margin-bottom: 30px;
            color: var(--text-color-medium);
        }

        /* Enhanced Form Styles */
        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--dark-color);
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #eee;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background-color: white;
        }

        .form-group input:focus,
        .form-group select:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(255, 95, 109, 0.1);
        }

        /* Date and Time Picker Styles */
        .datetime-wrapper {
            display: flex;
            gap: 20px;
        }

        .datetime-wrapper .form-group {
            flex: 1;
        }

        .flatpickr-input {
            background-color: white !important;
        }

        .flatpickr-calendar {
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border: none;
        }

        .flatpickr-day.selected {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        /* Grid Layout for Form */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .form-grid .form-group.full-width {
            grid-column: 1 / -1;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .datetime-wrapper {
                flex-direction: column;
                gap: 10px;
            }
        }

        .location-details {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .location-details p {
            margin: 0 0 8px 0;
            font-weight: 500;
            color: var(--dark-color);
        }
        .location-details span {
            color: var(--primary-color);
        }
        .error-message {
            color: var(--error-color);
            font-size: 14px;
            margin-top: 5px;
        }
        .pac-container {
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-top: 5px;
        }
        .pac-item {
            padding: 8px 15px;
        }
        .pac-item:hover {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="registration-container">
        <div class="progress-bar">
            <div class="progress-indicator"></div>
        </div>

        <!-- Step 1: Phone Number -->
        <div class="auth-step auth-step-1">
            <div class="step-title">
                <div class="step-number">1</div>
                <h2>Welcome to Vaivahik</h2>
            </div>
            <p class="welcome-text">Enter your phone number to sign in or create a new account</p>
            <form id="phoneForm">
                <div class="form-group">
                    <label for="phone">Phone Number</label>
                    <input type="tel" id="phone" name="phone" required
                           pattern="[0-9]{10}"
                           placeholder="Enter your 10-digit phone number"
                           maxlength="10">
                </div>
                <button type="submit">Get OTP</button>
            </form>
        </div>

        <!-- Step 2: OTP Verification -->
        <div class="auth-step auth-step-2">
            <div class="step-title">
                <div class="step-number">2</div>
                <h2>Verify Your Number</h2>
            </div>
            <p class="welcome-text">We've sent a verification code to your phone</p>
            <div id="userStatus" class="welcome-text" style="color: var(--primary-color); margin-top: -15px; display: none;">
                Welcome back! Please verify to continue.
            </div>
            <form id="otpForm">
                <div class="otp-inputs">
                    <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" required>
                    <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" required>
                    <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" required>
                    <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" required>
                    <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" required>
                    <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" required>
                </div>
                <div class="timer">Resend OTP in <span id="timer">02:00</span></div>
                <button type="submit">Verify OTP</button>
                <button type="button" class="resend-btn" id="resendOtp" disabled>Resend OTP</button>
            </form>
        </div>

        <!-- Step 3: Profile Information -->
        <div class="auth-step auth-step-3">
            <div class="step-title">
                <div class="step-number">3</div>
                <h2>Complete Your Profile</h2>
            </div>
            <form id="profileForm" class="form-grid">
                <div class="form-group full-width">
                    <label for="fullName">Full Name*</label>
                    <input type="text" id="fullName" required placeholder="Enter your full name">
                </div>

                <div class="form-group">
                    <label for="profileFor">Profile For*</label>
                    <select id="profileFor" required>
                        <option value="">Select profile for</option>
                        <option value="SELF">Self</option>
                        <option value="SON">Son</option>
                        <option value="DAUGHTER">Daughter</option>
                        <option value="BROTHER">Brother</option>
                        <option value="SISTER">Sister</option>
                        <option value="RELATIVE">Relative</option>
                        <option value="FRIEND">Friend</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="gender">Gender*</label>
                    <select id="gender" required>
                        <option value="">Select gender</option>
                        <option value="MALE">Male</option>
                        <option value="FEMALE">Female</option>
                        <option value="OTHER">Other</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="maritalStatus">Marital Status*</label>
                    <select id="maritalStatus" required>
                        <option value="">Select marital status</option>
                        <option value="NEVER_MARRIED">Never Married</option>
                        <option value="DIVORCED">Divorced</option>
                        <option value="WIDOWED">Widowed</option>
                        <option value="AWAITING_DIVORCE">Awaiting Divorce</option>
                    </select>
                </div>

                <div class="datetime-wrapper full-width">
                    <div class="form-group">
                        <label for="dateOfBirth">Date of Birth*</label>
                        <input type="text" id="dateOfBirth" required placeholder="DD/MM/YYYY">
                    </div>
                    <div class="form-group">
                        <label for="birthTime">Birth Time*</label>
                        <input type="text" id="birthTime" required placeholder="Select your birth time">
                    </div>
                </div>

                <div class="form-group">
                    <label for="birthPlace">Birth Place*</label>
                    <input type="text" id="birthPlace" required placeholder="Enter your birth place">
                </div>

                <div class="form-group">
                    <label for="height">Height (cm)*</label>
                    <input type="number" id="height" required min="100" max="250" placeholder="Enter your height in cm">
                </div>

                <div class="form-group">
                    <label for="religion">Religion*</label>
                    <select id="religion" required>
                        <option value="">Select religion</option>
                        <option value="HINDU">Hindu</option>
                        <option value="MUSLIM">Muslim</option>
                        <option value="CHRISTIAN">Christian</option>
                        <option value="SIKH">Sikh</option>
                        <option value="BUDDHIST">Buddhist</option>
                        <option value="JAIN">Jain</option>
                        <option value="OTHER">Other</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="caste">Caste</label>
                    <input type="text" id="caste" placeholder="Enter your caste (optional)">
                </div>

                <div class="form-group">
                    <label for="city">Current City*</label>
                    <input type="text" id="city" required placeholder="Enter your current city">
                </div>

                <div class="form-group">
                    <label for="state">State*</label>
                    <select id="state" required>
                        <option value="">Select state</option>
                        <!-- Add Indian states here -->
                    </select>
                </div>

                <div class="form-group">
                    <label for="pincode">Pincode*</label>
                    <input type="text" id="pincode" required pattern="[0-9]{6}" maxlength="6" placeholder="Enter 6-digit pincode">
                </div>

                <div class="form-group full-width">
                    <div id="locationDetails" class="location-details" style="display: none;">
                        <p>Selected Location:</p>
                        <span id="selectedAddress"></span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="education">Education*</label>
                    <input type="text" id="education" required placeholder="Your highest education">
                </div>

                <div class="form-group">
                    <label for="occupation">Occupation*</label>
                    <input type="text" id="occupation" required placeholder="Your current occupation">
                </div>

                <div class="form-group">
                    <label for="incomeRange">Annual Income Range*</label>
                    <select id="incomeRange" required>
                        <option value="">Select income range</option>
                        <option value="0-300000">Below 3 Lakhs</option>
                        <option value="300000-600000">3-6 Lakhs</option>
                        <option value="600000-1000000">6-10 Lakhs</option>
                        <option value="1000000-2000000">10-20 Lakhs</option>
                        <option value="2000000+">Above 20 Lakhs</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="employmentType">Employment Type*</label>
                    <select id="employmentType" required>
                        <option value="">Select employment type</option>
                        <option value="PRIVATE">Private Job</option>
                        <option value="GOVERNMENT">Government Job</option>
                        <option value="BUSINESS">Business</option>
                        <option value="SELF_EMPLOYED">Self Employed</option>
                        <option value="OTHER">Other</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="fatherName">Father's Name*</label>
                    <input type="text" id="fatherName" required placeholder="Enter your father's name">
                </div>

                <div class="form-group">
                    <label for="motherName">Mother's Name*</label>
                    <input type="text" id="motherName" required placeholder="Enter your mother's name">
                </div>

                <div class="form-group">
                    <label for="totalSiblings">Total Siblings*</label>
                    <input type="number" id="totalSiblings" required min="0" placeholder="Number of siblings">
                </div>

                <div class="form-group">
                    <label for="marriedSiblings">Married Siblings*</label>
                    <input type="number" id="marriedSiblings" required min="0" placeholder="Number of married siblings">
                </div>

                <div class="form-group full-width">
                    <label for="familyContact">Family Contact Number*</label>
                    <input type="tel" id="familyContact" required pattern="[0-9]{10}" placeholder="Alternative family contact">
                </div>

                <div class="form-group full-width">
                    <button type="submit" class="btn btn-primary">Complete Profile</button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <!-- Add Google Places API -->
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyXXXXXXXXXXXXXXXXXXXXXXXXXXXXX&libraries=places"></script>
    <script src="/js/auth.js"></script>
    <script>
        // Global variable to track API status
        let placesAPILoaded = false;

        // Function to handle Places API loading error
        function handlePlacesAPIError() {
            console.error('Google Places API failed to load');
            const cityInput = document.getElementById('city');
            cityInput.placeholder = 'Enter city name (API unavailable)';

            // Show error message to user
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = 'Location service is currently unavailable. Please enter location details manually.';
            cityInput.parentNode.appendChild(errorDiv);

            // Enable manual input as fallback
            enableManualLocationInput();
        }

        // Function to enable manual location input
        function enableManualLocationInput() {
            const cityInput = document.getElementById('city');
            const pincodeInput = document.getElementById('pincode');
            const stateSelect = document.getElementById('state');

            // Remove any existing autocomplete
            if (window.google && window.google.maps && window.google.maps.places) {
                google.maps.event.clearInstanceListeners(cityInput);
            }

            // Enable manual input
            cityInput.removeAttribute('readonly');
            pincodeInput.removeAttribute('readonly');
            stateSelect.removeAttribute('disabled');
        }

        // Initialize Google Places Autocomplete with enhanced features
        function initPlacesAutocomplete() {
            try {
                const cityInput = document.getElementById('city');
                const pincodeInput = document.getElementById('pincode');
                const locationDetails = document.getElementById('locationDetails');
                const selectedAddress = document.getElementById('selectedAddress');

                const options = {
                    types: ['(cities)', 'sublocality', 'postal_code'],
                    componentRestrictions: { country: 'IN' },
                    fields: ['address_components', 'formatted_address', 'geometry', 'name']
                };

                const autocomplete = new google.maps.places.Autocomplete(cityInput, options);

                autocomplete.addListener('place_changed', function() {
                    const place = autocomplete.getPlace();

                    if (!place.geometry) {
                        cityInput.value = '';
                        showError(cityInput, 'Please select a valid location from the dropdown');
                        return;
                    }

                    // Reset any previous error messages
                    clearError(cityInput);

                    let city = '';
                    let state = '';
                    let pincode = '';
                    let district = '';
                    let sublocality = '';

                    place.address_components.forEach(component => {
                        const types = component.types;

                        if (types.includes('locality')) {
                            city = component.long_name;
                        }
                        if (types.includes('administrative_area_level_1')) {
                            state = component.long_name;
                        }
                        if (types.includes('postal_code')) {
                            pincode = component.long_name;
                        }
                        if (types.includes('administrative_area_level_2')) {
                            district = component.long_name;
                        }
                        if (types.includes('sublocality_level_1')) {
                            sublocality = component.long_name;
                        }
                    });

                    // Update form fields
                    cityInput.value = city || sublocality;
                    if (pincode) {
                        pincodeInput.value = pincode;
                    }

                    // Update state dropdown
                    const stateSelect = document.getElementById('state');
                    const stateOption = Array.from(stateSelect.options).find(option =>
                        option.text.toUpperCase() === state.toUpperCase()
                    );

                    if (stateOption) {
                        stateSelect.value = stateOption.value;
                    }

                    // Show full location details for disambiguation
                    locationDetails.style.display = 'block';
                    selectedAddress.textContent = `${city}${sublocality ? ', ' + sublocality : ''}${district ? ', ' + district : ''}, ${state}${pincode ? ' - ' + pincode : ''}`;
                });

                placesAPILoaded = true;

            } catch (error) {
                console.error('Error initializing Places Autocomplete:', error);
                handlePlacesAPIError();
            }
        }

        // Utility functions for error handling
        function showError(element, message) {
            clearError(element);
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = message;
            element.parentNode.appendChild(errorDiv);
        }

        function clearError(element) {
            const existingError = element.parentNode.querySelector('.error-message');
            if (existingError) {
                existingError.remove();
            }
        }

        // Modified form validation to include location validation
        document.getElementById('profileForm').addEventListener('submit', function(e) {
            const cityInput = document.getElementById('city');
            const pincodeInput = document.getElementById('pincode');

            // Validate pincode
            if (!/^[0-9]{6}$/.test(pincodeInput.value)) {
                e.preventDefault();
                showError(pincodeInput, 'Please enter a valid 6-digit pincode');
                return;
            }

            // If Places API is loaded but no location is selected from dropdown
            if (placesAPILoaded && !document.getElementById('locationDetails').style.display === 'block') {
                e.preventDefault();
                showError(cityInput, 'Please select a location from the dropdown');
                return;
            }
        });

        // Load Places API with error handling
        function loadPlacesAPI() {
            const script = document.createElement('script');
            script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyXXXXXXXXXXXXXXXXXXXXXXXXXXXXX&libraries=places&callback=initPlacesAutocomplete';
            script.async = true;
            script.defer = true;
            script.onerror = handlePlacesAPIError;
            document.head.appendChild(script);
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            loadPlacesAPI();
            // ... rest of your existing DOMContentLoaded code ...
        });

        // Initialize date picker with Indian format (DD/MM/YYYY)
        flatpickr("#dateOfBirth", {
            dateFormat: "d/m/Y",
            maxDate: new Date().fp_incr(-18 * 365), // Must be at least 18 years old
            disableMobile: "true",
            locale: {
                firstDayOfWeek: 1 // Monday as first day (Indian preference)
            }
        });

        // Initialize time picker
        flatpickr("#birthTime", {
            enableTime: true,
            noCalendar: true,
            dateFormat: "H:i",
            time_24hr: true,
            disableMobile: "true"
        });

        // Populate states dropdown
        const states = [
            "Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat",
            "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh",
            "Maharashtra", "Manipur", "Meghalaya", "Mizoram", "Nagaland", "Odisha", "Punjab",
            "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "Tripura", "Uttar Pradesh",
            "Uttarakhand", "West Bengal"
        ];

        const stateSelect = document.getElementById('state');
        states.forEach(state => {
            const option = document.createElement('option');
            option.value = state.toUpperCase().replace(/\s+/g, '_');
            option.textContent = state;
            stateSelect.appendChild(option);
        });

        // OTP Input Handling
        document.querySelectorAll('.otp-input').forEach((input, index) => {
            input.addEventListener('keyup', (e) => {
                if (e.key >= 0 && e.key <= 9) {
                    if (index < 5) {
                        document.querySelectorAll('.otp-input')[index + 1].focus();
                    }
                } else if (e.key === 'Backspace') {
                    if (index > 0) {
                        document.querySelectorAll('.otp-input')[index - 1].focus();
                    }
                }
            });
        });

        // Handle plan selection from URL parameters
        document.addEventListener('DOMContentLoaded', function() {
            initPlacesAutocomplete();

            const urlParams = new URLSearchParams(window.location.search);
            const selectedPlan = urlParams.get('plan');

            if (selectedPlan) {
                // Store the selected plan in localStorage to use after registration
                localStorage.setItem('selectedPlan', selectedPlan);

                // Add a message to inform the user about their plan selection
                const welcomeText = document.querySelector('.welcome-text');
                if (welcomeText) {
                    const planName = selectedPlan.charAt(0).toUpperCase() + selectedPlan.slice(1);
                    welcomeText.innerHTML = `Welcome! You've selected the ${planName} plan.<br>Please enter your phone number to continue.`;
                }
            }
        });

        // Timer for OTP resend
        function startTimer(duration) {
            let timer = duration;
            const display = document.getElementById('timer');
            const resendBtn = document.getElementById('resendOtp');
            resendBtn.disabled = true;

            const interval = setInterval(() => {
                const minutes = parseInt(timer / 60, 10);
                const seconds = parseInt(timer % 60, 10);

                display.textContent = minutes.toString().padStart(2, '0') + ':' +
                                   seconds.toString().padStart(2, '0');

                if (--timer < 0) {
                    clearInterval(interval);
                    display.textContent = '00:00';
                    resendBtn.disabled = false;
                }
            }, 1000);

            return interval;
        }
    </script>
</body>
</html>