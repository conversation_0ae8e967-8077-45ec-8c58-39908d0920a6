import { useState, useEffect } from 'react';
import { <PERSON>, Chip, Typo<PERSON>, <PERSON>ltip, IconButton, Button } from '@mui/material';
import {
  Clear as ClearIcon,
  FilterAlt as FilterIcon,
  CalendarMonth as CalendarIcon,
  Height as HeightIcon,
  LocationOn as LocationIcon,
  School as EducationIcon,
  Work as WorkIcon,
  AttachMoney as MoneyIcon,
  Favorite as HeartIcon,
  Restaurant as FoodIcon,
  Photo as PhotoIcon,
  AccessTime as TimeIcon
} from '@mui/icons-material';
import { formatTotalInches } from '@/utils/heightUtils';

/**
 * FilterChips Component
 *
 * Displays active search filters as removable chips
 *
 * @param {Object} props
 * @param {Object} props.searchParams - The current search parameters
 * @param {Function} props.onRemoveFilter - Function to call when a filter is removed
 * @param {Function} props.onClearAllFilters - Function to call when all filters are cleared
 */
const FilterChips = ({ searchParams, onRemoveFilter, onClearAllFilters }) => {
  const [activeFilters, setActiveFilters] = useState([]);

  // Process search params to extract active filters
  useEffect(() => {
    if (!searchParams) return;

    const filters = [];

    // Process age range
    if (searchParams.ageFrom && searchParams.ageTo) {
      filters.push({
        id: 'age',
        label: `Age: ${searchParams.ageFrom}-${searchParams.ageTo} years`,
        value: { ageFrom: searchParams.ageFrom, ageTo: searchParams.ageTo }
      });
    }

    // Process height range
    if (searchParams.heightFrom && searchParams.heightTo) {
      const heightFromFeet = Math.floor(searchParams.heightFrom / 12);
      const heightFromInches = searchParams.heightFrom % 12;
      const heightToFeet = Math.floor(searchParams.heightTo / 12);
      const heightToInches = searchParams.heightTo % 12;

      filters.push({
        id: 'height',
        label: `Height: ${heightFromFeet}'${heightFromInches}" - ${heightToFeet}'${heightToInches}"`,
        value: { heightFrom: searchParams.heightFrom, heightTo: searchParams.heightTo }
      });
    }

    // Process location
    if (searchParams.location) {
      filters.push({
        id: 'location',
        label: `Location: ${searchParams.location}`,
        value: searchParams.location
      });
    }

    // Process religion
    if (searchParams.religion && searchParams.religion !== 'ANY') {
      filters.push({
        id: 'religion',
        label: `Religion: ${formatEnumValue(searchParams.religion)}`,
        value: searchParams.religion
      });
    }

    // Process caste
    if (searchParams.caste && searchParams.caste !== 'ANY') {
      filters.push({
        id: 'caste',
        label: `Caste: ${formatEnumValue(searchParams.caste)}`,
        value: searchParams.caste
      });
    }

    // Process education
    if (searchParams.education && searchParams.education.length > 0) {
      filters.push({
        id: 'education',
        label: `Education: ${searchParams.education.length} selected`,
        value: searchParams.education
      });
    }

    // Process occupation
    if (searchParams.occupation && searchParams.occupation.length > 0) {
      filters.push({
        id: 'occupation',
        label: `Occupation: ${searchParams.occupation.length} selected`,
        value: searchParams.occupation
      });
    }

    // Process income range
    if (searchParams.incomeRange && searchParams.incomeRange !== 'ANY') {
      filters.push({
        id: 'incomeRange',
        label: `Income: ${formatEnumValue(searchParams.incomeRange)}`,
        value: searchParams.incomeRange
      });
    }

    // Process marital status
    if (searchParams.maritalStatus && searchParams.maritalStatus.length > 0) {
      filters.push({
        id: 'maritalStatus',
        label: `Marital Status: ${searchParams.maritalStatus.length} selected`,
        value: searchParams.maritalStatus
      });
    }

    // Process diet
    if (searchParams.diet && searchParams.diet !== 'ANY') {
      filters.push({
        id: 'diet',
        label: `Diet: ${formatEnumValue(searchParams.diet)}`,
        value: searchParams.diet
      });
    }

    // Process photo filter
    if (searchParams.withPhoto === true) {
      filters.push({
        id: 'withPhoto',
        label: 'With Photo',
        value: true
      });
    }

    // Process profile creation date filter
    if (searchParams.profileCreatedWithin) {
      filters.push({
        id: 'profileCreatedWithin',
        label: `Created: ${formatEnumValue(searchParams.profileCreatedWithin)}`,
        value: searchParams.profileCreatedWithin
      });
    }

    setActiveFilters(filters);
  }, [searchParams]);

  // Format enum values for display
  const formatEnumValue = (value) => {
    if (!value) return '';

    return value
      .replace(/_/g, ' ')
      .toLowerCase()
      .replace(/\b\w/g, char => char.toUpperCase());
  };

  // Get icon for filter type
  const getFilterIcon = (filterId) => {
    switch (filterId) {
      case 'age':
        return <CalendarIcon fontSize="small" />;
      case 'height':
        return <HeightIcon fontSize="small" />;
      case 'location':
        return <LocationIcon fontSize="small" />;
      case 'religion':
        return <HeartIcon fontSize="small" />;
      case 'caste':
        return <HeartIcon fontSize="small" />;
      case 'education':
        return <EducationIcon fontSize="small" />;
      case 'occupation':
        return <WorkIcon fontSize="small" />;
      case 'incomeRange':
        return <MoneyIcon fontSize="small" />;
      case 'maritalStatus':
        return <HeartIcon fontSize="small" />;
      case 'diet':
        return <FoodIcon fontSize="small" />;
      case 'withPhoto':
        return <PhotoIcon fontSize="small" />;
      case 'profileCreatedWithin':
        return <TimeIcon fontSize="small" />;
      default:
        return <FilterIcon fontSize="small" />;
    }
  };

  // Handle removing a filter
  const handleRemoveFilter = (filterId) => {
    if (onRemoveFilter) {
      onRemoveFilter(filterId);
    }
  };

  // If no active filters, don't render anything
  if (activeFilters.length === 0) {
    return null;
  }

  return (
    <Box sx={{ mb: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
        <FilterIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
        <Typography variant="body2" color="text.secondary" sx={{ mr: 2 }}>
          Active Filters:
        </Typography>
        <Button
          size="small"
          variant="text"
          color="primary"
          startIcon={<ClearIcon />}
          onClick={onClearAllFilters}
        >
          Clear All
        </Button>
      </Box>
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
        {activeFilters.map((filter) => (
          <Chip
            key={filter.id}
            label={filter.label}
            icon={getFilterIcon(filter.id)}
            onDelete={() => handleRemoveFilter(filter.id)}
            color="primary"
            variant="outlined"
            size="medium"
            sx={{
              borderRadius: 2,
              '& .MuiChip-label': {
                px: 1,
                fontWeight: 500
              }
            }}
          />
        ))}
      </Box>
    </Box>
  );
};

export default FilterChips;
