/**
 * Validation Utilities
 * 
 * This module provides utility functions for validating data on the server side.
 * These functions match the client-side validation to ensure consistent validation
 * between client and server.
 */

const { VALIDATION } = require('./validationConstants');

/**
 * Validates a string against minimum and maximum length constraints
 * @param {string} value - The string to validate
 * @param {number} minLength - Minimum length
 * @param {number} maxLength - Maximum length
 * @returns {boolean} - Whether the string is valid
 */
const validateStringLength = (value, minLength, maxLength) => {
  if (typeof value !== 'string') return false;
  
  if (minLength !== undefined && value.length < minLength) {
    return false;
  }
  
  if (maxLength !== undefined && value.length > maxLength) {
    return false;
  }
  
  return true;
};

/**
 * Validates a string against a regular expression pattern
 * @param {string} value - The string to validate
 * @param {RegExp} pattern - The pattern to match
 * @returns {boolean} - Whether the string matches the pattern
 */
const validatePattern = (value, pattern) => {
  if (typeof value !== 'string') return false;
  return pattern.test(value);
};

/**
 * Validates a value against a list of allowed options
 * @param {any} value - The value to validate
 * @param {Array} options - The list of allowed options
 * @returns {boolean} - Whether the value is in the list of options
 */
const validateOptions = (value, options) => {
  return options.includes(value);
};

/**
 * Validates a number against minimum and maximum constraints
 * @param {number} value - The number to validate
 * @param {number} min - Minimum value
 * @param {number} max - Maximum value
 * @returns {boolean} - Whether the number is valid
 */
const validateNumber = (value, min, max) => {
  const num = parseFloat(value);
  if (isNaN(num)) return false;
  
  if (min !== undefined && num < min) {
    return false;
  }
  
  if (max !== undefined && num > max) {
    return false;
  }
  
  return true;
};

/**
 * Validates a date of birth based on gender and age constraints
 * @param {string|Date} dateOfBirth - The date of birth to validate
 * @param {string} gender - The gender (Male or Female)
 * @returns {boolean} - Whether the date of birth is valid
 */
const validateDateOfBirth = (dateOfBirth, gender) => {
  if (!dateOfBirth) return false;
  
  // Parse the date if it's a string
  const dob = typeof dateOfBirth === 'string' ? new Date(dateOfBirth) : dateOfBirth;
  
  // Check if the date is valid
  if (isNaN(dob.getTime())) {
    return false;
  }
  
  // Calculate age
  const today = new Date();
  const age = today.getFullYear() - dob.getFullYear();
  const monthDiff = today.getMonth() - dob.getMonth();
  
  // Adjust age if birthday hasn't occurred yet this year
  const adjustedAge = (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) 
    ? age - 1 
    : age;
  
  // Get minimum age based on gender
  const minAge = VALIDATION.DATE_OF_BIRTH.minAge[gender] || VALIDATION.DATE_OF_BIRTH.minAge.Male;
  
  // Check minimum and maximum age
  if (adjustedAge < minAge) {
    return false;
  }
  
  if (adjustedAge > VALIDATION.DATE_OF_BIRTH.maxAge) {
    return false;
  }
  
  return true;
};

/**
 * Validates a field based on validation rules
 * @param {string} fieldName - The name of the field
 * @param {any} value - The value to validate
 * @param {Object} formData - The entire form data (for cross-field validation)
 * @returns {string|null} - Error message or null if valid
 */
const validateField = (fieldName, value, formData = {}) => {
  // Skip validation if value is undefined or null
  if (value === undefined || value === null || value === '') {
    return null;
  }
  
  // Get validation rules for the field
  const rules = getValidationRules(fieldName);
  if (!rules) {
    return null;
  }
  
  // Validate based on field type
  switch (fieldName) {
    case 'fullName':
      if (!validateStringLength(value, rules.minLength, rules.maxLength)) {
        return `Full name must be between ${rules.minLength} and ${rules.maxLength} characters`;
      }
      if (!validatePattern(value, rules.pattern)) {
        return 'Full name can only contain letters, spaces, and characters like . \' -';
      }
      break;
      
    case 'gender':
      if (!validateOptions(value, rules.options)) {
        return `Gender must be one of: ${rules.options.join(', ')}`;
      }
      break;
      
    case 'dateOfBirth':
      if (!validateDateOfBirth(value, formData.gender)) {
        const minAge = VALIDATION.DATE_OF_BIRTH.minAge[formData.gender] || VALIDATION.DATE_OF_BIRTH.minAge.Male;
        return `Minimum age for ${formData.gender?.toLowerCase() || 'male'} is ${minAge} years`;
      }
      break;
      
    case 'height':
      if (!validateNumber(value, rules.min, rules.max)) {
        return `Height must be between ${rules.min} and ${rules.max} feet`;
      }
      break;
      
    // Add more field validations as needed
  }
  
  return null;
};

/**
 * Gets validation rules for a field
 * @param {string} fieldName - The name of the field
 * @returns {Object|null} - Validation rules or null if not found
 */
const getValidationRules = (fieldName) => {
  // Map field names to validation rule keys
  const fieldToRuleMap = {
    fullName: 'FULL_NAME',
    gender: 'GENDER',
    dateOfBirth: 'DATE_OF_BIRTH',
    height: 'HEIGHT',
    email: 'EMAIL',
    phone: 'PHONE',
    pincode: 'PINCODE',
    birthPlace: 'BIRTH_PLACE',
    familyType: 'FAMILY_TYPE',
    familyStatus: 'FAMILY_STATUS',
    fatherName: 'FATHER_NAME',
    motherName: 'MOTHER_NAME',
    brothers: 'SIBLINGS',
    sisters: 'SIBLINGS',
    marriedBrothers: 'SIBLINGS',
    marriedSisters: 'SIBLINGS',
    educationField: 'EDUCATION_FIELD',
    workingWith: 'WORKING_WITH',
    incomeRange: 'INCOME_RANGE',
    diet: 'DIET',
    smoking: 'SMOKING',
    drinking: 'DRINKING',
    aboutMe: 'ABOUT_ME',
    aboutPartner: 'ABOUT_PARTNER'
  };
  
  const ruleKey = fieldToRuleMap[fieldName];
  return ruleKey ? VALIDATION[ruleKey] : null;
};

module.exports = {
  validateStringLength,
  validatePattern,
  validateOptions,
  validateNumber,
  validateDateOfBirth,
  validateField,
  getValidationRules
};
