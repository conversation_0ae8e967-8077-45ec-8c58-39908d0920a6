// Import necessary modules
import { withAuth } from '@/utils/authHandler';
import { handleApiError } from '@/utils/errorHandler';

// In production, we would import Prisma
let prisma;
if (process.env.NODE_ENV === 'production') {
  try {
    const { PrismaClient } = require('@prisma/client');
    prisma = new PrismaClient();
  } catch (error) {
    console.error('Failed to initialize Prisma client:', error);
  }
}

// Mock data for development
const mockSpotlightFeatures = [
  {
    id: 1,
    name: "24-Hour Spotlight",
    description: "Get your profile highlighted at the top of search results for 24 hours",
    durationHours: 24,
    price: 499,
    discountPercent: 10,
    discountedPrice: 449.1,
    defaultCount: 1,
    isActive: true,
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(),
    purchaseCount: 45,
    activeCount: 12,
    revenue: 20205
  },
  {
    id: 2,
    name: "Premium Spotlight Pack",
    description: "Get 3 spotlight features at a discounted price",
    durationHours: 24,
    price: 1299,
    discountPercent: 15,
    discountedPrice: 1104.15,
    defaultCount: 3,
    isActive: true,
    createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(),
    purchaseCount: 28,
    activeCount: 8,
    revenue: 30916.2
  },
  {
    id: 3,
    name: "Weekend Spotlight",
    description: "Get your profile highlighted for the entire weekend (48 hours)",
    durationHours: 48,
    price: 899,
    discountPercent: null,
    discountedPrice: null,
    defaultCount: 1,
    isActive: true,
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(),
    purchaseCount: 32,
    activeCount: 5,
    revenue: 28768
  },
  {
    id: 4,
    name: "Special Offer Spotlight",
    description: "Limited time offer - Get 5 spotlights at a heavily discounted price",
    durationHours: 24,
    price: 1999,
    discountPercent: 25,
    discountedPrice: 1499.25,
    defaultCount: 5,
    isActive: false,
    createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(),
    purchaseCount: 15,
    activeCount: 0,
    revenue: 22488.75
  }
];

// Mock active users for a spotlight feature
const mockActiveUsers = [
  {
    spotlightId: 1,
    userId: 101,
    name: "Rahul Sharma",
    gender: "Male",
    age: 28,
    email: "<EMAIL>",
    phone: "+91 98765 43210",
    profilePic: null,
    startTime: new Date(Date.now() - 12 * 60 * 60 * 1000),
    endTime: new Date(Date.now() + 12 * 60 * 60 * 1000),
    timeRemaining: "12 hours"
  },
  {
    spotlightId: 2,
    userId: 102,
    name: "Priya Patel",
    gender: "Female",
    age: 26,
    email: "<EMAIL>",
    phone: "+91 87654 32109",
    profilePic: null,
    startTime: new Date(Date.now() - 6 * 60 * 60 * 1000),
    endTime: new Date(Date.now() + 18 * 60 * 60 * 1000),
    timeRemaining: "18 hours"
  },
  {
    spotlightId: 3,
    userId: 103,
    name: "Amit Kumar",
    gender: "Male",
    age: 30,
    email: "<EMAIL>",
    phone: "+91 76543 21098",
    profilePic: null,
    startTime: new Date(Date.now() - 24 * 60 * 60 * 1000),
    endTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
    timeRemaining: "24 hours"
  }
];

async function handler(req, res) {
  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getSpotlightFeatures(req, res);
      case 'POST':
        return await createSpotlightFeature(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Error in spotlight-features API:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
}

// Export the handler with authentication middleware
// In development mode, skip authentication for easier testing
export default process.env.NODE_ENV === 'development' ? handler : withAuth(handler, 'ADMIN');

// Get all spotlight features
async function getSpotlightFeatures(req, res) {
  try {
    // In development mode, return mock data
    if (process.env.NODE_ENV === 'development') {
      return res.status(200).json({ success: true, features: mockSpotlightFeatures });
    }

    // In production, use Prisma to fetch data from the database
    const features = await prisma.spotlightFeature.findMany({
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        _count: {
          select: { userSpotlights: true }
        }
      }
    });

    // Enhance features with additional stats
    const enhancedFeatures = await Promise.all(features.map(async (feature) => {
      // Get active spotlights count
      const now = new Date();
      const activeCount = await prisma.userSpotlight.count({
        where: {
          spotlightId: feature.id,
          isActive: true,
          endTime: {
            gt: now
          }
        }
      });

      // Get total revenue
      const revenue = await prisma.userSpotlight.aggregate({
        where: {
          spotlightId: feature.id
        },
        _sum: {
          pricePaid: true
        }
      });

      return {
        ...feature,
        purchaseCount: feature._count.userSpotlights,
        activeCount,
        revenue: revenue._sum?.pricePaid || 0
      };
    }));

    return res.status(200).json({ success: true, features: enhancedFeatures });
  } catch (error) {
    console.error('Error fetching spotlight features:', error);

    // If there's an error in production, return mock data in development mode
    if (process.env.NODE_ENV === 'development') {
      return res.status(200).json({ success: true, features: mockSpotlightFeatures });
    }

    return res.status(500).json({ success: false, message: 'Failed to fetch spotlight features' });
  }
}

// Create a new spotlight feature
async function createSpotlightFeature(req, res) {
  try {
    const {
      name,
      description,
      durationHours = 24,
      price,
      discountPercent,
      defaultCount,
      isActive
    } = req.body;

    // Validate required fields
    if (!name || !description || !price) {
      return res.status(400).json({
        success: false,
        message: 'Name, description, and price are required'
      });
    }

    // Parse numeric values
    const parsedDuration = parseInt(durationHours);
    const parsedPrice = parseFloat(price);

    // Calculate discounted price if discount is provided
    let discountedPrice = null;
    let parsedDiscountPercent = null;

    if (discountPercent && parseInt(discountPercent) > 0) {
      parsedDiscountPercent = parseInt(discountPercent);
      discountedPrice = parsedPrice - (parsedPrice * (parsedDiscountPercent / 100));
    }

    // Parse default count
    const parsedDefaultCount = defaultCount ? parseInt(defaultCount) : 1;

    // In development mode, return a mock created feature
    if (process.env.NODE_ENV === 'development') {
      const mockFeature = {
        id: mockSpotlightFeatures.length + 1,
        name,
        description,
        durationHours: parsedDuration,
        price: parsedPrice,
        discountPercent: parsedDiscountPercent,
        discountedPrice,
        defaultCount: parsedDefaultCount,
        isActive: isActive === true || isActive === 'true',
        createdAt: new Date(),
        updatedAt: new Date(),
        purchaseCount: 0,
        activeCount: 0,
        revenue: 0
      };

      // Add to mock data for future requests
      mockSpotlightFeatures.push(mockFeature);

      return res.status(201).json({ success: true, feature: mockFeature });
    }

    // In production, use Prisma to create in the database
    const feature = await prisma.spotlightFeature.create({
      data: {
        name,
        description,
        durationHours: parsedDuration,
        price: parsedPrice,
        discountPercent: parsedDiscountPercent,
        discountedPrice,
        defaultCount: parsedDefaultCount,
        isActive: isActive === true || isActive === 'true'
      }
    });

    return res.status(201).json({ success: true, feature });
  } catch (error) {
    console.error('Error creating spotlight feature:', error);

    // If there's an error in production, return a mock success in development mode
    if (process.env.NODE_ENV === 'development') {
      const mockFeature = {
        id: mockSpotlightFeatures.length + 1,
        name: req.body.name || 'New Spotlight Feature',
        description: req.body.description || 'Description',
        durationHours: parseInt(req.body.durationHours) || 24,
        price: parseFloat(req.body.price) || 999,
        discountPercent: req.body.discountPercent ? parseInt(req.body.discountPercent) : null,
        discountedPrice: null,
        defaultCount: parseInt(req.body.defaultCount) || 1,
        isActive: req.body.isActive === true || req.body.isActive === 'true',
        createdAt: new Date(),
        updatedAt: new Date(),
        purchaseCount: 0,
        activeCount: 0,
        revenue: 0
      };

      // Calculate discounted price if discount percent is provided
      if (mockFeature.discountPercent) {
        mockFeature.discountedPrice = mockFeature.price - (mockFeature.price * (mockFeature.discountPercent / 100));
      }

      // Add to mock data for future requests
      mockSpotlightFeatures.push(mockFeature);

      return res.status(201).json({ success: true, feature: mockFeature });
    }

    return res.status(500).json({ success: false, message: 'Failed to create spotlight feature' });
  }
}
