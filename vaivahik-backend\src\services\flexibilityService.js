/**
 * Flexibility Service
 * Handles flexible matching preferences and compatibility groups
 */

const { PrismaClient } = require('@prisma/client');

class FlexibilityService {
  constructor() {
    this.prisma = new PrismaClient();
    
    // Default flexibility settings
    this.defaultSettings = {
      ageFlexibility: {
        enabled: true,
        minRange: 2,
        maxRange: 5,
        preferredRange: 3
      },
      heightFlexibility: {
        enabled: true,
        tolerance: 2, // inches
        preferredTolerance: 1
      },
      locationFlexibility: {
        enabled: true,
        maxDistance: 50, // km
        preferredDistance: 25
      },
      educationFlexibility: {
        enabled: true,
        allowLowerEducation: false,
        allowHigherEducation: true
      },
      incomeFlexibility: {
        enabled: true,
        tolerance: 20, // percentage
        preferredTolerance: 10
      },
      casteFlexibility: {
        enabled: false,
        allowSimilarCastes: false,
        allowInterCaste: false
      },
      religionFlexibility: {
        enabled: false,
        allowSimilarReligions: false,
        allowInterReligion: false
      }
    };

    // Compatibility groups for religion
    this.religionCompatibilityGroups = {
      'Hindu': ['Hindu', 'Jain', 'Buddhist'],
      'Muslim': ['Muslim'],
      'Christian': ['Christian', 'Catholic'],
      'Sikh': ['Sikh', 'Hindu'],
      'Jain': ['Jain', 'Hindu'],
      'Buddhist': ['Buddhist', 'Hindu'],
      'Parsi': ['Parsi'],
      'Jewish': ['Jewish'],
      'Other': ['Other']
    };

    // Maratha subcaste compatibility groups
    this.marathaSubcasteGroups = {
      '96K': ['96K', 'Maratha'],
      'Maratha': ['Maratha', '96K'],
      'Kunbi': ['Kunbi', 'Maratha'],
      'Dhangar': ['Dhangar'],
      'Other': ['Other', 'Maratha']
    };
  }

  /**
   * Create default flexibility settings for a user
   */
  async createDefaultSettings(userId) {
    try {
      const existingSettings = await this.prisma.userFlexibilitySettings.findUnique({
        where: { userId }
      });

      if (existingSettings) {
        return existingSettings;
      }

      const settings = await this.prisma.userFlexibilitySettings.create({
        data: {
          userId,
          settings: this.defaultSettings,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      return settings;
    } catch (error) {
      console.error('Error creating default flexibility settings:', error);
      throw error;
    }
  }

  /**
   * Get user's flexibility settings
   */
  async getUserFlexibilitySettings(userId) {
    try {
      let settings = await this.prisma.userFlexibilitySettings.findUnique({
        where: { userId }
      });

      if (!settings) {
        settings = await this.createDefaultSettings(userId);
      }

      return settings;
    } catch (error) {
      console.error('Error getting user flexibility settings:', error);
      return { settings: this.defaultSettings, isActive: true };
    }
  }

  /**
   * Update user's flexibility settings
   */
  async updateFlexibilitySettings(userId, newSettings) {
    try {
      const settings = await this.prisma.userFlexibilitySettings.upsert({
        where: { userId },
        update: {
          settings: newSettings,
          updatedAt: new Date()
        },
        create: {
          userId,
          settings: newSettings,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      return settings;
    } catch (error) {
      console.error('Error updating flexibility settings:', error);
      throw error;
    }
  }

  /**
   * Check if two users are compatible based on flexibility settings
   */
  async checkFlexibilityCompatibility(user1Id, user2Id, user1Profile, user2Profile) {
    try {
      const user1Settings = await this.getUserFlexibilitySettings(user1Id);
      const user2Settings = await this.getUserFlexibilitySettings(user2Id);

      const compatibility = {
        isCompatible: true,
        score: 100,
        details: {},
        flexibilityApplied: []
      };

      // Check age flexibility
      const ageCompatibility = this.checkAgeFlexibility(
        user1Profile, user2Profile, user1Settings.settings, user2Settings.settings
      );
      compatibility.details.age = ageCompatibility;
      if (!ageCompatibility.isCompatible) {
        compatibility.isCompatible = false;
        compatibility.score -= 20;
      } else if (ageCompatibility.flexibilityApplied) {
        compatibility.flexibilityApplied.push('age');
        compatibility.score -= 5;
      }

      // Check height flexibility
      const heightCompatibility = this.checkHeightFlexibility(
        user1Profile, user2Profile, user1Settings.settings, user2Settings.settings
      );
      compatibility.details.height = heightCompatibility;
      if (!heightCompatibility.isCompatible) {
        compatibility.score -= 10;
      } else if (heightCompatibility.flexibilityApplied) {
        compatibility.flexibilityApplied.push('height');
        compatibility.score -= 3;
      }

      // Check location flexibility
      const locationCompatibility = this.checkLocationFlexibility(
        user1Profile, user2Profile, user1Settings.settings, user2Settings.settings
      );
      compatibility.details.location = locationCompatibility;
      if (!locationCompatibility.isCompatible) {
        compatibility.score -= 15;
      } else if (locationCompatibility.flexibilityApplied) {
        compatibility.flexibilityApplied.push('location');
        compatibility.score -= 5;
      }

      // Check caste flexibility
      const casteCompatibility = this.checkCasteFlexibility(
        user1Profile, user2Profile, user1Settings.settings, user2Settings.settings
      );
      compatibility.details.caste = casteCompatibility;
      if (!casteCompatibility.isCompatible) {
        compatibility.isCompatible = false;
        compatibility.score -= 30;
      } else if (casteCompatibility.flexibilityApplied) {
        compatibility.flexibilityApplied.push('caste');
        compatibility.score -= 10;
      }

      // Check religion flexibility
      const religionCompatibility = this.checkReligionFlexibility(
        user1Profile, user2Profile, user1Settings.settings, user2Settings.settings
      );
      compatibility.details.religion = religionCompatibility;
      if (!religionCompatibility.isCompatible) {
        compatibility.isCompatible = false;
        compatibility.score -= 25;
      } else if (religionCompatibility.flexibilityApplied) {
        compatibility.flexibilityApplied.push('religion');
        compatibility.score -= 8;
      }

      compatibility.score = Math.max(0, compatibility.score);
      return compatibility;

    } catch (error) {
      console.error('Error checking flexibility compatibility:', error);
      return {
        isCompatible: false,
        score: 0,
        details: {},
        flexibilityApplied: [],
        error: error.message
      };
    }
  }

  /**
   * Check age flexibility between two profiles
   */
  checkAgeFlexibility(profile1, profile2, settings1, settings2) {
    const age1 = this.calculateAge(profile1.dateOfBirth);
    const age2 = this.calculateAge(profile2.dateOfBirth);
    
    const ageDiff = Math.abs(age1 - age2);
    
    // Check strict preferences first
    const strictCompatible = this.isAgeStrictlyCompatible(age1, age2, profile1.preferences, profile2.preferences);
    
    if (strictCompatible) {
      return {
        isCompatible: true,
        flexibilityApplied: false,
        ageDifference: ageDiff,
        reason: 'Within strict age preferences'
      };
    }

    // Apply flexibility if enabled
    const flex1 = settings1.ageFlexibility;
    const flex2 = settings2.ageFlexibility;
    
    if (flex1?.enabled && flex2?.enabled) {
      const maxFlexRange = Math.max(flex1.maxRange || 5, flex2.maxRange || 5);
      
      if (ageDiff <= maxFlexRange) {
        return {
          isCompatible: true,
          flexibilityApplied: true,
          ageDifference: ageDiff,
          reason: `Within flexible age range (${maxFlexRange} years)`
        };
      }
    }

    return {
      isCompatible: false,
      flexibilityApplied: false,
      ageDifference: ageDiff,
      reason: 'Age difference too large even with flexibility'
    };
  }

  /**
   * Check height flexibility between two profiles
   */
  checkHeightFlexibility(profile1, profile2, settings1, settings2) {
    const height1 = profile1.height || 0;
    const height2 = profile2.height || 0;
    
    if (!height1 || !height2) {
      return {
        isCompatible: true,
        flexibilityApplied: false,
        reason: 'Height information not available'
      };
    }

    const heightDiff = Math.abs(height1 - height2);
    
    // Check if within flexible range
    const flex1 = settings1.heightFlexibility;
    const flex2 = settings2.heightFlexibility;
    
    if (flex1?.enabled && flex2?.enabled) {
      const maxTolerance = Math.max(flex1.tolerance || 2, flex2.tolerance || 2);
      
      if (heightDiff <= maxTolerance) {
        return {
          isCompatible: true,
          flexibilityApplied: heightDiff > 1,
          heightDifference: heightDiff,
          reason: heightDiff <= 1 ? 'Within preferred height range' : `Within flexible height range (${maxTolerance} inches)`
        };
      }
    }

    return {
      isCompatible: false,
      flexibilityApplied: false,
      heightDifference: heightDiff,
      reason: 'Height difference too large'
    };
  }

  /**
   * Check location flexibility between two profiles
   */
  checkLocationFlexibility(profile1, profile2, settings1, settings2) {
    if (!profile1.city || !profile2.city) {
      return {
        isCompatible: true,
        flexibilityApplied: false,
        reason: 'Location information not complete'
      };
    }

    // Same city is always compatible
    if (profile1.city.toLowerCase() === profile2.city.toLowerCase()) {
      return {
        isCompatible: true,
        flexibilityApplied: false,
        reason: 'Same city'
      };
    }

    // Check state compatibility
    if (profile1.state && profile2.state) {
      if (profile1.state.toLowerCase() === profile2.state.toLowerCase()) {
        return {
          isCompatible: true,
          flexibilityApplied: true,
          reason: 'Same state (location flexibility applied)'
        };
      }
    }

    // For now, different states are considered incompatible
    // In future, we can add distance calculation
    return {
      isCompatible: false,
      flexibilityApplied: false,
      reason: 'Different states'
    };
  }

  /**
   * Check caste flexibility between two profiles
   */
  checkCasteFlexibility(profile1, profile2, settings1, settings2) {
    const caste1 = profile1.caste || profile1.subcaste;
    const caste2 = profile2.caste || profile2.subcaste;

    if (!caste1 || !caste2) {
      return {
        isCompatible: true,
        flexibilityApplied: false,
        reason: 'Caste information not available'
      };
    }

    // Exact match
    if (caste1.toLowerCase() === caste2.toLowerCase()) {
      return {
        isCompatible: true,
        flexibilityApplied: false,
        reason: 'Same caste'
      };
    }

    // Check Maratha subcaste compatibility
    const marathaCompatible = this.checkMarathaSubcasteCompatibility(caste1, caste2);
    if (marathaCompatible) {
      return {
        isCompatible: true,
        flexibilityApplied: true,
        reason: 'Compatible Maratha subcastes'
      };
    }

    // Check flexibility settings
    const flex1 = settings1.casteFlexibility;
    const flex2 = settings2.casteFlexibility;

    if (flex1?.enabled && flex2?.enabled && flex1?.allowInterCaste && flex2?.allowInterCaste) {
      return {
        isCompatible: true,
        flexibilityApplied: true,
        reason: 'Inter-caste flexibility enabled'
      };
    }

    return {
      isCompatible: false,
      flexibilityApplied: false,
      reason: 'Different castes and no flexibility'
    };
  }

  /**
   * Check religion flexibility between two profiles
   */
  checkReligionFlexibility(profile1, profile2, settings1, settings2) {
    const religion1 = profile1.religion;
    const religion2 = profile2.religion;

    if (!religion1 || !religion2) {
      return {
        isCompatible: true,
        flexibilityApplied: false,
        reason: 'Religion information not available'
      };
    }

    // Exact match
    if (religion1.toLowerCase() === religion2.toLowerCase()) {
      return {
        isCompatible: true,
        flexibilityApplied: false,
        reason: 'Same religion'
      };
    }

    // Check compatibility groups
    const compatibleReligions = this.religionCompatibilityGroups[religion1] || [];
    if (compatibleReligions.includes(religion2)) {
      return {
        isCompatible: true,
        flexibilityApplied: true,
        reason: 'Compatible religions'
      };
    }

    // Check flexibility settings
    const flex1 = settings1.religionFlexibility;
    const flex2 = settings2.religionFlexibility;

    if (flex1?.enabled && flex2?.enabled && flex1?.allowInterReligion && flex2?.allowInterReligion) {
      return {
        isCompatible: true,
        flexibilityApplied: true,
        reason: 'Inter-religion flexibility enabled'
      };
    }

    return {
      isCompatible: false,
      flexibilityApplied: false,
      reason: 'Different religions and no flexibility'
    };
  }

  /**
   * Helper methods
   */
  calculateAge(dateOfBirth) {
    if (!dateOfBirth) return 0;
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  }

  isAgeStrictlyCompatible(age1, age2, preferences1, preferences2) {
    // Check if ages fall within each other's strict preferences
    const minAge1 = preferences1?.minAge || 18;
    const maxAge1 = preferences1?.maxAge || 50;
    const minAge2 = preferences2?.minAge || 18;
    const maxAge2 = preferences2?.maxAge || 50;

    return (age2 >= minAge1 && age2 <= maxAge1) && (age1 >= minAge2 && age1 <= maxAge2);
  }

  checkMarathaSubcasteCompatibility(caste1, caste2) {
    const normalizedCaste1 = caste1.toLowerCase();
    const normalizedCaste2 = caste2.toLowerCase();

    for (const [group, compatibleCastes] of Object.entries(this.marathaSubcasteGroups)) {
      const normalizedCompatible = compatibleCastes.map(c => c.toLowerCase());
      if (normalizedCompatible.includes(normalizedCaste1) && normalizedCompatible.includes(normalizedCaste2)) {
        return true;
      }
    }

    return false;
  }
}

module.exports = FlexibilityService;
