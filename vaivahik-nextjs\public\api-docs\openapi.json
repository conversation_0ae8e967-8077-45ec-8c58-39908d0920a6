{"openapi": "3.0.0", "info": {"title": "Vaivahik AI API", "version": "1.0.0", "description": "API documentation for Vaivahik AI Platform", "contact": {"name": "Vaivahik Support", "email": "<EMAIL>", "url": "https://vaivahik.com/contact"}}, "servers": [{"url": "http://localhost:8000", "description": "Development server"}, {"url": "https://api.vaivahik.com", "description": "Production server"}], "paths": {"/api/users": {"get": {"summary": "Get all users", "description": "Retrieve a list of all users", "tags": ["Users"], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "1"}, "name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}}}}}}}}}}}}, "/api/admin/dashboard": {"get": {"summary": "Get dashboard data", "description": "Retrieve dashboard statistics and data", "tags": ["Admin"], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "stats": {"type": "object", "properties": {"totalUsers": {"type": "integer", "example": 1000}, "activeUsers": {"type": "integer", "example": 500}, "premiumUsers": {"type": "integer", "example": 200}}}}}}}}}}}}}