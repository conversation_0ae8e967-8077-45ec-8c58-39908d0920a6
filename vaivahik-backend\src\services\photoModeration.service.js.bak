// src/services/photoModeration.service.js

const fs = require('fs').promises;
const path = require('path');
const tf = require('@tensorflow/tfjs-node');
const nsfw = require('nsfwjs');
const faceapi = require('@vladmandic/face-api');
const { PrismaClient } = require('@prisma/client');

// Initialize Prisma client
const prisma = new PrismaClient();

// Path to models
const MODELS_DIR = path.join(__dirname, '../../models');
const FACE_API_MODELS_DIR = path.join(MODELS_DIR, 'face-api');

// Moderation models
let nsfwModel = null;
let faceDetectionInitialized = false;
let isInitializing = false;
let initializationError = null;

/**
 * Initialize the AI moderation models
 * @returns {Promise<boolean>} True if initialization successful
 */
async function initializeModels() {
    if (nsfwModel && faceDetectionInitialized) {
        return true; // Already initialized
    }
    
    if (isInitializing) {
        // Wait for initialization to complete
        while (isInitializing) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        return !initializationError;
    }
    
    isInitializing = true;
    initializationError = null;
    
    try {
        console.log('Initializing AI photo moderation models...');
        
        // Create models directory if it doesn't exist
        try {
            await fs.mkdir(MODELS_DIR, { recursive: true });
            await fs.mkdir(FACE_API_MODELS_DIR, { recursive: true });
        } catch (err) {
            console.log('Models directories already exist or could not be created:', err.message);
        }
        
        // Load NSFW detection model
        console.log('Loading NSFW detection model...');
        nsfwModel = await nsfw.load();
        
        // Load face detection models
        console.log('Loading face detection models...');
        await faceapi.tf.setBackend('tensorflow');
        await faceapi.tf.enableProdMode();
        await faceapi.tf.ENV.set('DEBUG', false);
        await faceapi.tf.ready();
        
        // Check if models exist, if not, they will be downloaded automatically
        const modelPathSSD = path.join(FACE_API_MODELS_DIR, 'ssd_mobilenetv1_model-weights_manifest.json');
        try {
            await fs.access(modelPathSSD);
            console.log('Face detection models found, loading from disk...');
        } catch (err) {
            console.log('Face detection models not found, will download automatically...');
        }
        
        // Load face detection models
        await faceapi.nets.ssdMobilenetv1.loadFromDisk(FACE_API_MODELS_DIR);
        await faceapi.nets.faceLandmark68Net.loadFromDisk(FACE_API_MODELS_DIR);
        
        faceDetectionInitialized = true;
        console.log('AI photo moderation models initialized successfully');
        
        isInitializing = false;
        return true;
    } catch (error) {
        console.error('Error initializing AI photo moderation models:', error);
        initializationError = error;
        isInitializing = false;
        return false;
    }
}

/**
 * Get the current moderation settings from the database
 * @returns {Promise<Object>} Moderation settings
 */
async function getModerationSettings() {
    try {
        // Try to get settings from database
        const settings = await prisma.systemConfig.findFirst({
            where: { configKey: 'photoModeration' }
        });
        
        if (settings) {
            return JSON.parse(settings.configValue);
        }
        
        // Default settings if none found
        return {
            operationMode: 0, // 0: Manual, 1: Shadow, 2: Limited Auto, 3: Full Auto
            automationPercentage: 0,
            autoApprovalConfidence: 95,
            autoRejectionConfidence: 98,
            requireFaceDetection: true,
            allowMultipleFaces: false,
            minFaceSize: 15, // Percentage of image
            rejectExplicitContent: true,
            rejectViolentContent: true,
            flagSuggestiveContent: true,
            minResolution: 400, // Minimum width/height in pixels
            maxFileSize: 5 * 1024 * 1024 // 5MB
        };
    } catch (error) {
        console.error('Error getting moderation settings:', error);
        // Return default settings on error
        return {
            operationMode: 0,
            automationPercentage: 0,
            autoApprovalConfidence: 95,
            autoRejectionConfidence: 98,
            requireFaceDetection: true,
            allowMultipleFaces: false,
            minFaceSize: 15,
            rejectExplicitContent: true,
            rejectViolentContent: true,
            flagSuggestiveContent: true,
            minResolution: 400,
            maxFileSize: 5 * 1024 * 1024
        };
    }
}

/**
 * Moderate a photo using AI
 * @param {string} imagePath - Path to the image file
 * @param {string} photoId - Optional photo ID if already in database
 * @returns {Promise<Object>} Moderation result
 */
async function moderatePhoto(imagePath, photoId = null) {
    // Initialize models if not already done
    const initialized = await initializeModels();
    if (!initialized) {
        return {
            decision: 'PENDING',
            flags: ['ai_initialization_failed'],
            confidence: 0,
            details: { error: 'AI models failed to initialize' }
        };
    }
    
    try {
        // Get current moderation settings
        const settings = await getModerationSettings();
        
        // Read image file
        const imageBuffer = await fs.readFile(imagePath);
        
        // Get image dimensions
        const imageInfo = await sharp(imageBuffer).metadata();
        const { width, height } = imageInfo;
        
        // Check resolution
        if (width < settings.minResolution || height < settings.minResolution) {
            return {
                decision: 'REJECTED',
                flags: ['low_resolution'],
                confidence: 100,
                details: {
                    width,
                    height,
                    minRequired: settings.minResolution
                }
            };
        }
        
        // Convert buffer to tensor for NSFW detection
        const image = await tf.node.decodeImage(imageBuffer, 3);
        
        // Run NSFW detection
        const nsfwPredictions = await nsfwModel.classify(image);
        
        // Run face detection
        const imageForFace = await canvas.loadImage(imageBuffer);
        const faceDetections = await faceapi.detectAllFaces(imageForFace)
            .withFaceLandmarks();
        
        // Clean up tensor to prevent memory leaks
        tf.dispose(image);
        
        // Process results
        return analyzeResults(nsfwPredictions, faceDetections, settings, { width, height });
    } catch (error) {
        console.error('Error in photo moderation:', error);
        return {
            decision: 'PENDING', // Default to manual review on error
            flags: ['moderation_error'],
            confidence: 0,
            details: { error: error.message }
        };
    }
}

/**
 * Analyze moderation results and make a decision
 * @param {Array} nsfwPredictions - Results from NSFW detection
 * @param {Array} faceDetections - Results from face detection
 * @param {Object} settings - Moderation settings
 * @param {Object} imageInfo - Image information (width, height)
 * @returns {Object} Decision and flags
 */
function analyzeResults(nsfwPredictions, faceDetections, settings, imageInfo) {
    const result = {
        decision: 'PENDING',
        flags: [],
        confidence: 0,
        details: {
            nsfw: nsfwPredictions,
            faces: faceDetections.length,
            imageInfo
        }
    };
    
    // Check for inappropriate content
    const pornPrediction = nsfwPredictions.find(p => p.className === 'Porn');
    const sexyPrediction = nsfwPredictions.find(p => p.className === 'Sexy');
    const violencePrediction = nsfwPredictions.find(p => p.className === 'Gore');
    
    // Calculate overall safety score (higher is safer)
    const safePrediction = nsfwPredictions.find(p => p.className === 'Neutral');
    const safetyScore = safePrediction ? safePrediction.probability * 100 : 0;
    result.details.safetyScore = safetyScore;
    
    // Check explicit content
    if (settings.rejectExplicitContent && pornPrediction && pornPrediction.probability > 0.7) {
        result.decision = 'REJECTED';
        result.flags.push('explicit_content');
        result.confidence = pornPrediction.probability * 100;
        return result;
    }
    
    // Check violent content
    if (settings.rejectViolentContent && violencePrediction && violencePrediction.probability > 0.7) {
        result.decision = 'REJECTED';
        result.flags.push('violent_content');
        result.confidence = violencePrediction.probability * 100;
        return result;
    }
    
    // Flag suggestive content
    if (settings.flagSuggestiveContent && sexyPrediction && sexyPrediction.probability > 0.7) {
        result.flags.push('suggestive_content');
        // Don't auto-reject, just flag for review
    }
    
    // Check face detection
    if (settings.requireFaceDetection) {
        if (faceDetections.length === 0) {
            result.decision = 'REJECTED';
            result.flags.push('no_face_detected');
            result.confidence = 95;
            return result;
        }
        
        // Check for multiple faces
        if (faceDetections.length > 1 && !settings.allowMultipleFaces) {
            result.flags.push('multiple_faces');
            // Don't auto-reject, just flag for review
        }
        
        // Check face size
        const { width, height } = imageInfo;
        const imageArea = width * height;
        
        for (const face of faceDetections) {
            const faceBox = face.detection.box;
            const faceArea = faceBox.width * faceBox.height;
            const facePercentage = (faceArea / imageArea) * 100;
            
            if (facePercentage < settings.minFaceSize) {
                result.flags.push('face_too_small');
                break;
            }
        }
    }
    
    // If we have any flags, set to PENDING for manual review
    if (result.flags.length > 0) {
        result.decision = 'PENDING';
        result.confidence = 70;
        return result;
    }
    
    // If we get here, the image is probably appropriate
    result.decision = 'APPROVED';
    result.confidence = safetyScore;
    result.flags.push('face_detected');
    
    return result;
}

/**
 * Process a photo through the moderation system
 * This is the main entry point for the moderation service
 * @param {string} imagePath - Path to the image file
 * @param {string} photoId - Optional photo ID if already in database
 * @returns {Promise<Object>} Processing result with decision
 */
async function processPhoto(imagePath, photoId = null) {
    try {
        // Get current moderation settings
        const settings = await getModerationSettings();
        
        // If in manual mode (0), skip AI processing
        if (settings.operationMode === 0) {
            return {
                decision: 'PENDING', // Always manual review in manual mode
                flags: ['manual_mode'],
                confidence: 0,
                details: { mode: 'Manual' }
            };
        }
        
        // In shadow mode (1), always return PENDING but still do analysis
        if (settings.operationMode === 1) {
            const aiResult = await moderatePhoto(imagePath, photoId);
            return {
                decision: 'PENDING', // Always manual review in shadow mode
                flags: aiResult.flags,
                confidence: aiResult.confidence,
                details: { 
                    ...aiResult.details,
                    aiDecision: aiResult.decision,
                    mode: 'Shadow'
                }
            };
        }
        
        // For limited auto mode (2) and full auto mode (3)
        const aiResult = await moderatePhoto(imagePath, photoId);
        
        // In limited auto mode, apply automation percentage
        if (settings.operationMode === 2) {
            // Randomly determine if this photo should be auto-processed
            const useAutomation = Math.random() * 100 < settings.automationPercentage;
            
            if (!useAutomation) {
                return {
                    decision: 'PENDING',
                    flags: aiResult.flags,
                    confidence: aiResult.confidence,
                    details: {
                        ...aiResult.details,
                        aiDecision: aiResult.decision,
                        mode: 'Limited Auto (Manual Selected)'
                    }
                };
            }
        }
        
        // Apply confidence thresholds for auto-approval/rejection
        if (aiResult.decision === 'APPROVED' && aiResult.confidence >= settings.autoApprovalConfidence) {
            return {
                decision: 'APPROVED',
                flags: aiResult.flags,
                confidence: aiResult.confidence,
                details: {
                    ...aiResult.details,
                    mode: settings.operationMode === 2 ? 'Limited Auto' : 'Full Auto'
                }
            };
        } else if (aiResult.decision === 'REJECTED' && aiResult.confidence >= settings.autoRejectionConfidence) {
            return {
                decision: 'REJECTED',
                flags: aiResult.flags,
                confidence: aiResult.confidence,
                details: {
                    ...aiResult.details,
                    mode: settings.operationMode === 2 ? 'Limited Auto' : 'Full Auto'
                }
            };
        }
        
        // Default to manual review for borderline cases
        return {
            decision: 'PENDING',
            flags: aiResult.flags,
            confidence: aiResult.confidence,
            details: {
                ...aiResult.details,
                aiDecision: aiResult.decision,
                mode: settings.operationMode === 2 ? 'Limited Auto (Borderline)' : 'Full Auto (Borderline)'
            }
        };
    } catch (error) {
        console.error('Error in photo processing:', error);
        return {
            decision: 'PENDING', // Default to manual review on error
            flags: ['processing_error'],
            confidence: 0,
            details: { error: error.message }
        };
    }
}

/**
 * Log moderation result to database for analytics
 * @param {string} photoId - Photo ID
 * @param {Object} result - Moderation result
 * @returns {Promise<void>}
 */
async function logModerationResult(photoId, result) {
    try {
        await prisma.moderationLog.create({
            data: {
                photoId,
                aiDecision: result.decision,
                aiFlags: result.flags.join(','),
                aiConfidence: result.confidence,
                details: JSON.stringify(result.details),
                createdAt: new Date()
            }
        });
    } catch (error) {
        console.error('Error logging moderation result:', error);
    }
}

module.exports = {
    initializeModels,
    moderatePhoto,
    processPhoto,
    getModerationSettings,
    logModerationResult
};
