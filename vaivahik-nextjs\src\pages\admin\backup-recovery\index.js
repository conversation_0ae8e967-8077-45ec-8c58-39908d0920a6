import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  Alert,
  CircularProgress,
  Switch,
  FormControlLabel,
  Tooltip,
  Stack,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Backup as BackupIcon,
  Restore as RestoreIcon,
  CloudDownload as CloudDownloadIcon,
  CloudUpload as CloudUploadIcon,
  Schedule as ScheduleIcon,
  Storage as StorageIcon,
  Security as SecurityIcon,
  Assessment as AssessmentIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  Save as SaveIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  PlayArrow as PlayArrowIcon,
  Stop as StopIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';

// Dynamic import for EnhancedAdminLayout to avoid SSR issues
const EnhancedAdminLayout = dynamic(() => import('@/components/admin/EnhancedAdminLayout'), {
  ssr: false
});

export default function BackupRecovery() {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [backupStats, setBackupStats] = useState({});
  const [backups, setBackups] = useState([]);
  const [schedules, setSchedules] = useState([]);
  const [backupInProgress, setBackupInProgress] = useState(false);
  const [restoreDialogOpen, setRestoreDialogOpen] = useState(false);
  const [selectedBackup, setSelectedBackup] = useState(null);
  const [settings, setSettings] = useState({
    autoBackup: true,
    backupFrequency: 'daily',
    retentionDays: 30,
    compressionEnabled: true,
    encryptionEnabled: true,
    cloudStorage: true,
    storageProvider: 'AWS_S3',
    maxBackupSize: 10, // GB
    includeMedia: true,
    includeUserData: true,
    includeLogs: false
  });

  useEffect(() => {
    fetchBackupData();
  }, []);

  const fetchBackupData = async () => {
    try {
      setLoading(true);
      
      // Mock backup stats
      setBackupStats({
        totalBackups: 45,
        successfulBackups: 43,
        failedBackups: 2,
        totalSize: 2.4, // GB
        lastBackup: new Date(Date.now() - 86400000).toISOString(),
        nextScheduled: new Date(Date.now() + 86400000).toISOString(),
        storageUsed: 75.2, // GB
        storageLimit: 100 // GB
      });

      // Mock backup history
      setBackups([
        {
          id: '1',
          name: 'Daily Backup - 2024-01-15',
          type: 'automatic',
          status: 'completed',
          size: 2.1,
          duration: 45,
          createdAt: new Date(Date.now() - 86400000).toISOString(),
          location: 's3://vaivahik-backups/daily/2024-01-15.tar.gz',
          checksum: 'sha256:abc123...',
          includes: ['database', 'media', 'config']
        },
        {
          id: '2',
          name: 'Weekly Backup - 2024-01-14',
          type: 'scheduled',
          status: 'completed',
          size: 8.5,
          duration: 180,
          createdAt: new Date(Date.now() - 172800000).toISOString(),
          location: 's3://vaivahik-backups/weekly/2024-01-14.tar.gz',
          checksum: 'sha256:def456...',
          includes: ['database', 'media', 'config', 'logs']
        },
        {
          id: '3',
          name: 'Manual Backup - 2024-01-13',
          type: 'manual',
          status: 'failed',
          size: 0,
          duration: 0,
          createdAt: new Date(Date.now() - 259200000).toISOString(),
          location: null,
          error: 'Storage quota exceeded',
          includes: ['database', 'media']
        }
      ]);

      // Mock backup schedules
      setSchedules([
        {
          id: '1',
          name: 'Daily Database Backup',
          frequency: 'daily',
          time: '02:00',
          enabled: true,
          includes: ['database', 'config'],
          retention: 7,
          lastRun: new Date(Date.now() - 86400000).toISOString(),
          nextRun: new Date(Date.now() + 86400000).toISOString()
        },
        {
          id: '2',
          name: 'Weekly Full Backup',
          frequency: 'weekly',
          time: '01:00',
          day: 'sunday',
          enabled: true,
          includes: ['database', 'media', 'config', 'logs'],
          retention: 30,
          lastRun: new Date(Date.now() - 604800000).toISOString(),
          nextRun: new Date(Date.now() + 86400000).toISOString()
        }
      ]);

    } catch (error) {
      console.error('Error fetching backup data:', error);
      toast.error('Error fetching backup data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateBackup = async () => {
    try {
      setBackupInProgress(true);
      
      // Mock backup creation
      console.log('Creating manual backup...');
      
      // Simulate backup progress
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      toast.success('Backup created successfully');
      fetchBackupData();
    } catch (error) {
      toast.error('Error creating backup');
    } finally {
      setBackupInProgress(false);
    }
  };

  const handleRestoreBackup = async () => {
    try {
      if (!selectedBackup) return;
      
      // Mock restore process
      console.log('Restoring backup:', selectedBackup.id);
      toast.success('Backup restore initiated');
      setRestoreDialogOpen(false);
      setSelectedBackup(null);
    } catch (error) {
      toast.error('Error restoring backup');
    }
  };

  const handleSaveSettings = async () => {
    try {
      // Mock save settings
      console.log('Saving backup settings:', settings);
      toast.success('Backup settings saved successfully');
    } catch (error) {
      toast.error('Error saving backup settings');
    }
  };

  const getStatusChip = (status) => {
    const config = {
      completed: { color: 'success', label: 'Completed', icon: <CheckCircleIcon /> },
      failed: { color: 'error', label: 'Failed', icon: <ErrorIcon /> },
      running: { color: 'warning', label: 'Running', icon: <CircularProgress size={16} /> }
    };
    
    const statusConfig = config[status] || config.completed;
    return (
      <Chip 
        icon={statusConfig.icon} 
        label={statusConfig.label} 
        color={statusConfig.color} 
        size="small" 
      />
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  const formatSize = (sizeInGB) => {
    if (sizeInGB < 1) {
      return `${(sizeInGB * 1024).toFixed(0)} MB`;
    }
    return `${sizeInGB.toFixed(1)} GB`;
  };

  const TabPanel = ({ children, value, index, ...other }) => (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`backup-tabpanel-${index}`}
      aria-labelledby={`backup-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );

  return (
    <EnhancedAdminLayout title="Backup & Recovery">
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Backup & Recovery
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchBackupData}
            >
              Refresh
            </Button>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={() => toast.info('Download backup functionality')}
            >
              Download
            </Button>
            <Button
              variant="contained"
              startIcon={<BackupIcon />}
              onClick={handleCreateBackup}
              disabled={backupInProgress}
            >
              {backupInProgress ? 'Creating...' : 'Create Backup'}
            </Button>
          </Box>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Total Backups
                    </Typography>
                    <Typography variant="h4" component="div">
                      {backupStats.totalBackups || 0}
                    </Typography>
                  </Box>
                  <BackupIcon color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Successful
                    </Typography>
                    <Typography variant="h4" component="div">
                      {backupStats.successfulBackups || 0}
                    </Typography>
                  </Box>
                  <CheckCircleIcon color="success" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Failed
                    </Typography>
                    <Typography variant="h4" component="div">
                      {backupStats.failedBackups || 0}
                    </Typography>
                  </Box>
                  <ErrorIcon color="error" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Total Size
                    </Typography>
                    <Typography variant="h4" component="div">
                      {formatSize(backupStats.totalSize || 0)}
                    </Typography>
                  </Box>
                  <StorageIcon color="info" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Storage Used
                    </Typography>
                    <Typography variant="h4" component="div">
                      {formatSize(backupStats.storageUsed || 0)}
                    </Typography>
                  </Box>
                  <CloudDownloadIcon color="warning" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Last Backup
                    </Typography>
                    <Typography variant="body2" component="div">
                      {backupStats.lastBackup ? new Date(backupStats.lastBackup).toLocaleDateString() : 'Never'}
                    </Typography>
                  </Box>
                  <ScheduleIcon color="secondary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Storage Usage Progress */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Storage Usage
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <LinearProgress
                variant="determinate"
                value={(backupStats.storageUsed / backupStats.storageLimit) * 100}
                sx={{ flexGrow: 1, height: 8, borderRadius: 4 }}
                color={(backupStats.storageUsed / backupStats.storageLimit) > 0.8 ? 'error' : 'primary'}
              />
              <Typography variant="body2">
                {formatSize(backupStats.storageUsed || 0)} / {formatSize(backupStats.storageLimit || 0)}
              </Typography>
            </Box>
          </CardContent>
        </Card>

        {/* Tabs */}
        <Paper sx={{ mb: 3 }}>
          <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
            <Tab label="Backup History" icon={<BackupIcon />} />
            <Tab label="Schedules" icon={<ScheduleIcon />} />
            <Tab label="Settings" icon={<SettingsIcon />} />
            <Tab label="Recovery" icon={<RestoreIcon />} />
          </Tabs>
        </Paper>

        {/* Tab Panels */}
        <TabPanel value={activeTab} index={0}>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Backup Name</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Size</TableCell>
                  <TableCell>Duration</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell>Includes</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {backups.map((backup) => (
                  <TableRow key={backup.id}>
                    <TableCell>
                      <Typography variant="body1" fontWeight="600">
                        {backup.name}
                      </Typography>
                      {backup.error && (
                        <Typography variant="caption" color="error">
                          Error: {backup.error}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={backup.type} 
                        size="small" 
                        variant="outlined"
                        color={backup.type === 'manual' ? 'primary' : 'default'}
                      />
                    </TableCell>
                    <TableCell>{getStatusChip(backup.status)}</TableCell>
                    <TableCell>{backup.size > 0 ? formatSize(backup.size) : '-'}</TableCell>
                    <TableCell>{backup.duration > 0 ? `${backup.duration}s` : '-'}</TableCell>
                    <TableCell>{formatDate(backup.createdAt)}</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {backup.includes?.map((item, index) => (
                          <Chip key={index} label={item} size="small" variant="outlined" />
                        ))}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        {backup.status === 'completed' && (
                          <>
                            <Tooltip title="Download">
                              <IconButton size="small" color="primary">
                                <DownloadIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Restore">
                              <IconButton 
                                size="small" 
                                color="success"
                                onClick={() => {
                                  setSelectedBackup(backup);
                                  setRestoreDialogOpen(true);
                                }}
                              >
                                <RestoreIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </>
                        )}
                        <Tooltip title="Delete">
                          <IconButton size="small" color="error">
                            <ErrorIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Schedule Name</TableCell>
                  <TableCell>Frequency</TableCell>
                  <TableCell>Time</TableCell>
                  <TableCell>Includes</TableCell>
                  <TableCell>Retention</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Last Run</TableCell>
                  <TableCell>Next Run</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {schedules.map((schedule) => (
                  <TableRow key={schedule.id}>
                    <TableCell>
                      <Typography variant="body1" fontWeight="600">
                        {schedule.name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip label={schedule.frequency} size="small" variant="outlined" />
                    </TableCell>
                    <TableCell>
                      {schedule.time}
                      {schedule.day && ` (${schedule.day})`}
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {schedule.includes?.map((item, index) => (
                          <Chip key={index} label={item} size="small" variant="outlined" />
                        ))}
                      </Box>
                    </TableCell>
                    <TableCell>{schedule.retention} days</TableCell>
                    <TableCell>
                      <Chip 
                        label={schedule.enabled ? 'Enabled' : 'Disabled'} 
                        color={schedule.enabled ? 'success' : 'error'} 
                        size="small" 
                      />
                    </TableCell>
                    <TableCell>{formatDate(schedule.lastRun)}</TableCell>
                    <TableCell>{formatDate(schedule.nextRun)}</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        <Tooltip title={schedule.enabled ? 'Disable' : 'Enable'}>
                          <IconButton size="small" color={schedule.enabled ? 'error' : 'success'}>
                            {schedule.enabled ? <StopIcon fontSize="small" /> : <PlayArrowIcon fontSize="small" />}
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit">
                          <IconButton size="small" color="primary">
                            <SettingsIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={activeTab} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Backup Settings
                  </Typography>
                  <Stack spacing={3}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.autoBackup}
                          onChange={(e) => setSettings({ ...settings, autoBackup: e.target.checked })}
                        />
                      }
                      label="Enable Automatic Backups"
                    />
                    <FormControl fullWidth>
                      <InputLabel>Backup Frequency</InputLabel>
                      <Select
                        value={settings.backupFrequency}
                        label="Backup Frequency"
                        onChange={(e) => setSettings({ ...settings, backupFrequency: e.target.value })}
                      >
                        <MenuItem value="hourly">Hourly</MenuItem>
                        <MenuItem value="daily">Daily</MenuItem>
                        <MenuItem value="weekly">Weekly</MenuItem>
                        <MenuItem value="monthly">Monthly</MenuItem>
                      </Select>
                    </FormControl>
                    <TextField
                      label="Retention Period (days)"
                      type="number"
                      value={settings.retentionDays}
                      onChange={(e) => setSettings({ ...settings, retentionDays: parseInt(e.target.value) })}
                      helperText="How long to keep backups"
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Storage & Security
                  </Typography>
                  <Stack spacing={3}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.compressionEnabled}
                          onChange={(e) => setSettings({ ...settings, compressionEnabled: e.target.checked })}
                        />
                      }
                      label="Enable Compression"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.encryptionEnabled}
                          onChange={(e) => setSettings({ ...settings, encryptionEnabled: e.target.checked })}
                        />
                      }
                      label="Enable Encryption"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.cloudStorage}
                          onChange={(e) => setSettings({ ...settings, cloudStorage: e.target.checked })}
                        />
                      }
                      label="Use Cloud Storage"
                    />
                    <FormControl fullWidth>
                      <InputLabel>Storage Provider</InputLabel>
                      <Select
                        value={settings.storageProvider}
                        label="Storage Provider"
                        onChange={(e) => setSettings({ ...settings, storageProvider: e.target.value })}
                        disabled={!settings.cloudStorage}
                      >
                        <MenuItem value="AWS_S3">AWS S3</MenuItem>
                        <MenuItem value="GOOGLE_CLOUD">Google Cloud</MenuItem>
                        <MenuItem value="AZURE">Azure Blob</MenuItem>
                      </Select>
                    </FormControl>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Backup Content
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={3}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={settings.includeUserData}
                            onChange={(e) => setSettings({ ...settings, includeUserData: e.target.checked })}
                          />
                        }
                        label="Include User Data"
                      />
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={settings.includeMedia}
                            onChange={(e) => setSettings({ ...settings, includeMedia: e.target.checked })}
                          />
                        }
                        label="Include Media Files"
                      />
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={settings.includeLogs}
                            onChange={(e) => setSettings({ ...settings, includeLogs: e.target.checked })}
                          />
                        }
                        label="Include System Logs"
                      />
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <TextField
                        fullWidth
                        label="Max Backup Size (GB)"
                        type="number"
                        value={settings.maxBackupSize}
                        onChange={(e) => setSettings({ ...settings, maxBackupSize: parseInt(e.target.value) })}
                      />
                    </Grid>
                  </Grid>
                  <Box sx={{ mt: 3 }}>
                    <Button variant="contained" onClick={handleSaveSettings}>
                      Save Settings
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={activeTab} index={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Disaster Recovery
              </Typography>
              <Alert severity="warning" sx={{ mb: 2 }}>
                Recovery operations should be performed with caution. Always test recovery procedures in a staging environment first.
              </Alert>
              <Typography variant="body2" color="text.secondary">
                Disaster recovery tools and procedures would be displayed here,
                including point-in-time recovery, selective restore options, and recovery testing tools.
              </Typography>
            </CardContent>
          </Card>
        </TabPanel>

        {/* Restore Confirmation Dialog */}
        <Dialog open={restoreDialogOpen} onClose={() => setRestoreDialogOpen(false)}>
          <DialogTitle>Confirm Backup Restore</DialogTitle>
          <DialogContent>
            <Alert severity="warning" sx={{ mb: 2 }}>
              This will restore the system to the state of the selected backup. Current data may be overwritten.
            </Alert>
            {selectedBackup && (
              <Box>
                <Typography variant="body1" gutterBottom>
                  <strong>Backup:</strong> {selectedBackup.name}
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>Created:</strong> {formatDate(selectedBackup.createdAt)}
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>Size:</strong> {formatSize(selectedBackup.size)}
                </Typography>
                <Typography variant="body2">
                  <strong>Includes:</strong> {selectedBackup.includes?.join(', ')}
                </Typography>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setRestoreDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleRestoreBackup} variant="contained" color="warning">
              Restore Backup
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </EnhancedAdminLayout>
  );
}
