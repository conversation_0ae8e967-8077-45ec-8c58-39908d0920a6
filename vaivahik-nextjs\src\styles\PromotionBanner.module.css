/* PromotionBanner.module.css */

.promotionBanner {
  position: relative;
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.promotionContent {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.promotionTitle {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.promotionDescription {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

.promotionCountdown {
  font-size: 12px;
  font-weight: 600;
  margin-top: 5px;
  display: inline-block;
  padding: 3px 8px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
}

.closeButton {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  color: inherit;
  font-size: 20px;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.closeButton:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.1);
}

/* Responsive styles */
@media (max-width: 768px) {
  .promotionBanner {
    border-radius: 0;
    margin-bottom: 15px;
  }
}
