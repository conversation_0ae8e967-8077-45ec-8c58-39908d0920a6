/**
 * Route Standardization Script
 * 
 * This script updates all existing routes to follow the new API standards.
 * It adds proper JSDoc comments, validation, error handling, and caching.
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');
const { execSync } = require('child_process');

// Configuration
const config = {
  routesDir: path.join(__dirname, '../src/routes'),
  controllersDir: path.join(__dirname, '../src/controllers'),
  templateDir: path.join(__dirname, '../src/templates'),
  backupDir: path.join(__dirname, '../backups', `routes-${new Date().toISOString().replace(/:/g, '-')}`)
};

// Create backup directory
if (!fs.existsSync(config.backupDir)) {
  fs.mkdirSync(config.backupDir, { recursive: true });
}

// Create template directory if it doesn't exist
if (!fs.existsSync(config.templateDir)) {
  fs.mkdirSync(config.templateDir, { recursive: true });
}

/**
 * Get all route files
 * @returns {Array<string>} Array of route file paths
 */
function getRouteFiles() {
  return glob.sync('**/*.js', { cwd: config.routesDir, absolute: true });
}

/**
 * Backup a file
 * @param {string} filePath - Path to the file
 */
function backupFile(filePath) {
  const relativePath = path.relative(path.join(__dirname, '..'), filePath);
  const backupPath = path.join(config.backupDir, relativePath);
  
  // Create directory if it doesn't exist
  const backupDir = path.dirname(backupPath);
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  // Copy file
  fs.copyFileSync(filePath, backupPath);
  console.log(`Backed up ${relativePath}`);
}

/**
 * Check if a file has JSDoc comments
 * @param {string} content - File content
 * @returns {boolean} Whether the file has JSDoc comments
 */
function hasJSDocComments(content) {
  return content.includes('/**') && content.includes('@route');
}

/**
 * Check if a file has validation
 * @param {string} content - File content
 * @returns {boolean} Whether the file has validation
 */
function hasValidation(content) {
  return content.includes('express-validator') && 
         (content.includes('body(') || content.includes('param(') || content.includes('query('));
}

/**
 * Check if a file has error handling
 * @param {string} content - File content
 * @returns {boolean} Whether the file has error handling
 */
function hasErrorHandling(content) {
  return content.includes('next(error)') || content.includes('return next(error)');
}

/**
 * Check if a file has caching
 * @param {string} content - File content
 * @returns {boolean} Whether the file has caching
 */
function hasCaching(content) {
  return content.includes('cache(') || content.includes('clearCache(');
}

/**
 * Update a route file
 * @param {string} filePath - Path to the route file
 */
function updateRouteFile(filePath) {
  // Read file
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Skip if file is already updated
  if (hasJSDocComments(content) && hasValidation(content) && hasErrorHandling(content)) {
    console.log(`Skipping ${path.relative(path.join(__dirname, '..'), filePath)} (already updated)`);
    return;
  }
  
  // Backup file
  backupFile(filePath);
  
  // Parse file to extract routes
  const routes = extractRoutes(content);
  
  // Generate updated content
  let updatedContent = generateUpdatedContent(content, routes, filePath);
  
  // Write updated content
  fs.writeFileSync(filePath, updatedContent);
  
  console.log(`Updated ${path.relative(path.join(__dirname, '..'), filePath)}`);
}

/**
 * Extract routes from file content
 * @param {string} content - File content
 * @returns {Array<Object>} Array of route objects
 */
function extractRoutes(content) {
  const routes = [];
  const routeRegex = /router\.(get|post|put|patch|delete)\(\s*['"]([^'"]+)['"]\s*,\s*([^)]+)\)/g;
  
  let match;
  while ((match = routeRegex.exec(content)) !== null) {
    const method = match[1];
    const path = match[2];
    const handlers = match[3].split(',').map(h => h.trim());
    
    routes.push({
      method,
      path,
      handlers,
      original: match[0]
    });
  }
  
  return routes;
}

/**
 * Generate updated content
 * @param {string} content - Original file content
 * @param {Array<Object>} routes - Array of route objects
 * @param {string} filePath - Path to the route file
 * @returns {string} Updated content
 */
function generateUpdatedContent(content, routes, filePath) {
  let updatedContent = content;
  
  // Add imports if needed
  if (!updatedContent.includes('express-validator')) {
    updatedContent = updatedContent.replace(
      'const express = require(\'express\');',
      'const express = require(\'express\');\nconst { body, param, query } = require(\'express-validator\');'
    );
  }
  
  if (!updatedContent.includes('validator')) {
    updatedContent = updatedContent.replace(
      'const express = require(\'express\');',
      'const express = require(\'express\');\nconst { validate } = require(\'../middleware/validator\');'
    );
  }
  
  if (!updatedContent.includes('cacheMiddleware')) {
    updatedContent = updatedContent.replace(
      'const express = require(\'express\');',
      'const express = require(\'express\');\nconst { cache } = require(\'../middleware/cacheMiddleware\');'
    );
  }
  
  // Add JSDoc comments and validation for each route
  for (const route of routes) {
    // Skip if route already has JSDoc comments
    if (updatedContent.includes(`@route   ${route.method.toUpperCase()} ${route.path}`)) {
      continue;
    }
    
    // Generate JSDoc comment
    const jsdoc = generateJSDocComment(route);
    
    // Generate validation
    const validation = generateValidation(route);
    
    // Replace original route with updated route
    const updatedRoute = generateUpdatedRoute(route, validation);
    
    // Add JSDoc comment and updated route
    updatedContent = updatedContent.replace(
      route.original,
      `${jsdoc}\n${updatedRoute}`
    );
  }
  
  return updatedContent;
}

/**
 * Generate JSDoc comment for a route
 * @param {Object} route - Route object
 * @returns {string} JSDoc comment
 */
function generateJSDocComment(route) {
  const method = route.method.toUpperCase();
  const path = route.path;
  const resourceName = getResourceName(path);
  const action = getActionName(method, path);
  
  return `/**
 * @swagger
 * /api/v1${path}:
 *   ${route.method}:
 *     summary: ${action} ${resourceName}
 *     description: ${action} ${resourceName}
 *     tags: [${getTagName(path)}]
 *     parameters:
 *       ${generateSwaggerParameters(route)}
 *     responses:
 *       ${generateSwaggerResponses(route)}
 */`;
}

/**
 * Generate validation for a route
 * @param {Object} route - Route object
 * @returns {string} Validation code
 */
function generateValidation(route) {
  const method = route.method.toLowerCase();
  const path = route.path;
  
  // Skip validation for simple GET routes
  if (method === 'get' && !path.includes(':')) {
    return '';
  }
  
  // Generate validation based on path parameters
  const pathParams = [];
  const pathRegex = /:([^/]+)/g;
  let match;
  
  while ((match = pathRegex.exec(path)) !== null) {
    pathParams.push(match[1]);
  }
  
  // Generate validation code
  let validation = '[';
  
  // Add path parameter validation
  if (pathParams.length > 0) {
    for (const param of pathParams) {
      validation += `
    param('${param}')
      .isString()
      .trim()
      .notEmpty()
      .withMessage('${param} is required'),`;
    }
  }
  
  // Add body validation for POST, PUT, PATCH
  if (method === 'post' || method === 'put' || method === 'patch') {
    validation += `
    body('*')
      .optional()
      .trim(),`;
  }
  
  // Add query validation for GET
  if (method === 'get') {
    validation += `
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),`;
  }
  
  // Close validation array
  validation += `
  ],
  validate,`;
  
  return validation;
}

/**
 * Generate updated route
 * @param {Object} route - Route object
 * @param {string} validation - Validation code
 * @returns {string} Updated route
 */
function generateUpdatedRoute(route, validation) {
  const method = route.method;
  const path = route.path;
  const handlers = route.handlers;
  
  // Add caching for GET routes
  let caching = '';
  if (method === 'get') {
    caching = `cache(60), // Cache for 60 seconds\n  `;
  }
  
  // Generate updated route
  return `router.${method}(
  '${path}',
  ${validation}
  ${caching}${handlers.join(',\n  ')}
)`;
}

/**
 * Get resource name from path
 * @param {string} path - Route path
 * @returns {string} Resource name
 */
function getResourceName(path) {
  const parts = path.split('/').filter(Boolean);
  return parts[parts.length - 1].replace(/:[^/]+/, 'item');
}

/**
 * Get action name from method and path
 * @param {string} method - HTTP method
 * @param {string} path - Route path
 * @returns {string} Action name
 */
function getActionName(method, path) {
  const resource = getResourceName(path);
  
  switch (method) {
    case 'GET':
      return path.includes(':') ? `Get ${resource}` : `Get all ${resource}s`;
    case 'POST':
      return `Create ${resource}`;
    case 'PUT':
      return `Update ${resource}`;
    case 'PATCH':
      return `Partially update ${resource}`;
    case 'DELETE':
      return `Delete ${resource}`;
    default:
      return `${method} ${resource}`;
  }
}

/**
 * Get tag name from path
 * @param {string} path - Route path
 * @returns {string} Tag name
 */
function getTagName(path) {
  const parts = path.split('/').filter(Boolean);
  return parts[0].charAt(0).toUpperCase() + parts[0].slice(1);
}

/**
 * Generate Swagger parameters
 * @param {Object} route - Route object
 * @returns {string} Swagger parameters
 */
function generateSwaggerParameters(route) {
  const method = route.method.toLowerCase();
  const path = route.path;
  
  // Generate parameters based on path parameters
  const pathParams = [];
  const pathRegex = /:([^/]+)/g;
  let match;
  
  while ((match = pathRegex.exec(path)) !== null) {
    pathParams.push(match[1]);
  }
  
  // Generate Swagger parameters
  let parameters = '';
  
  // Add path parameter documentation
  if (pathParams.length > 0) {
    for (const param of pathParams) {
      parameters += `- name: ${param}
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: ${param} parameter\n *       `;
    }
  }
  
  // Add query parameter documentation for GET
  if (method === 'get') {
    parameters += `- name: page
 *         in: query
 *         required: false
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - name: limit
 *         in: query
 *         required: false
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Items per page`;
  }
  
  return parameters;
}

/**
 * Generate Swagger responses
 * @param {Object} route - Route object
 * @returns {string} Swagger responses
 */
function generateSwaggerResponses(route) {
  const method = route.method.toLowerCase();
  
  // Generate Swagger responses
  let responses = '';
  
  // Add success response
  switch (method) {
    case 'get':
      responses += `200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'`;
      break;
    case 'post':
      responses += `201:
 *         description: Created
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'`;
      break;
    case 'put':
    case 'patch':
    case 'delete':
      responses += `200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'`;
      break;
  }
  
  // Add error responses
  responses += `
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       422:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'`;
  
  return responses;
}

/**
 * Main function
 */
function main() {
  console.log('Updating routes to follow new API standards...');
  
  // Get all route files
  const routeFiles = getRouteFiles();
  
  // Update each route file
  for (const filePath of routeFiles) {
    updateRouteFile(filePath);
  }
  
  console.log('Done!');
}

// Run main function
main();
