/**
 * Advanced SEO Optimization System
 * Features: Meta tags, structured data, sitemap generation, social media optimization
 */

import Head from 'next/head';
import { useRouter } from 'next/router';

// SEO Configuration
export const seoConfig = {
  defaultTitle: 'Vaivahik - Premium Maratha Matrimony Platform',
  titleTemplate: '%s | Vaivahik Matrimony',
  defaultDescription: 'Find your perfect life partner in the Maratha community. Advanced AI-powered matching, verified profiles, and trusted by thousands of families.',
  siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://vaivahik.com',
  defaultImage: '/images/og-image.jpg',
  twitterHandle: '@VaivahikApp',
  facebookAppId: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID,
  googleSiteVerification: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION,
  bingSiteVerification: process.env.NEXT_PUBLIC_BING_SITE_VERIFICATION,
  organization: {
    name: 'Vaivahik Matrimony',
    url: 'https://vaivahik.com',
    logo: 'https://vaivahik.com/images/logo.png',
    contactPoint: {
      telephone: '+91-9876543210',
      contactType: 'customer service',
      email: '<EMAIL>'
    },
    sameAs: [
      'https://www.facebook.com/vaivahik',
      'https://www.twitter.com/vaivahikapp',
      'https://www.instagram.com/vaivahik',
      'https://www.linkedin.com/company/vaivahik'
    ]
  }
};

// SEO Hook
export const useSEO = (seoData = {}) => {
  const router = useRouter();
  
  const {
    title,
    description,
    image,
    url,
    type = 'website',
    publishedTime,
    modifiedTime,
    author,
    tags = [],
    noindex = false,
    nofollow = false,
    canonical,
    alternateLanguages = [],
    structuredData = null
  } = seoData;

  const fullTitle = title 
    ? `${title} | ${seoConfig.defaultTitle}`
    : seoConfig.defaultTitle;
  
  const fullDescription = description || seoConfig.defaultDescription;
  const fullImage = image || seoConfig.defaultImage;
  const fullUrl = url || `${seoConfig.siteUrl}${router.asPath}`;
  const canonicalUrl = canonical || fullUrl;

  return {
    title: fullTitle,
    description: fullDescription,
    image: fullImage,
    url: fullUrl,
    canonical: canonicalUrl,
    type,
    publishedTime,
    modifiedTime,
    author,
    tags,
    noindex,
    nofollow,
    alternateLanguages,
    structuredData
  };
};

// SEO Head Component
export const SEOHead = ({ seoData = {} }) => {
  const seo = useSEO(seoData);

  const robotsContent = [
    seo.noindex ? 'noindex' : 'index',
    seo.nofollow ? 'nofollow' : 'follow'
  ].join(', ');

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{seo.title}</title>
      <meta name="description" content={seo.description} />
      <meta name="robots" content={robotsContent} />
      <link rel="canonical" href={seo.canonical} />
      
      {/* Viewport */}
      <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      
      {/* Open Graph */}
      <meta property="og:type" content={seo.type} />
      <meta property="og:title" content={seo.title} />
      <meta property="og:description" content={seo.description} />
      <meta property="og:image" content={seo.image} />
      <meta property="og:url" content={seo.url} />
      <meta property="og:site_name" content={seoConfig.organization.name} />
      <meta property="og:locale" content="en_IN" />
      
      {seo.publishedTime && (
        <meta property="article:published_time" content={seo.publishedTime} />
      )}
      {seo.modifiedTime && (
        <meta property="article:modified_time" content={seo.modifiedTime} />
      )}
      {seo.author && (
        <meta property="article:author" content={seo.author} />
      )}
      {seo.tags.map((tag, index) => (
        <meta key={index} property="article:tag" content={tag} />
      ))}
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content={seoConfig.twitterHandle} />
      <meta name="twitter:creator" content={seoConfig.twitterHandle} />
      <meta name="twitter:title" content={seo.title} />
      <meta name="twitter:description" content={seo.description} />
      <meta name="twitter:image" content={seo.image} />
      
      {/* Facebook */}
      {seoConfig.facebookAppId && (
        <meta property="fb:app_id" content={seoConfig.facebookAppId} />
      )}
      
      {/* Site Verification */}
      {seoConfig.googleSiteVerification && (
        <meta name="google-site-verification" content={seoConfig.googleSiteVerification} />
      )}
      {seoConfig.bingSiteVerification && (
        <meta name="msvalidate.01" content={seoConfig.bingSiteVerification} />
      )}
      
      {/* Alternate Languages */}
      {seo.alternateLanguages.map((lang, index) => (
        <link
          key={index}
          rel="alternate"
          hrefLang={lang.hrefLang}
          href={lang.href}
        />
      ))}
      
      {/* Structured Data */}
      {seo.structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(seo.structuredData)
          }}
        />
      )}
      
      {/* Organization Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": seoConfig.organization.name,
            "url": seoConfig.organization.url,
            "logo": seoConfig.organization.logo,
            "contactPoint": {
              "@type": "ContactPoint",
              "telephone": seoConfig.organization.contactPoint.telephone,
              "contactType": seoConfig.organization.contactPoint.contactType,
              "email": seoConfig.organization.contactPoint.email
            },
            "sameAs": seoConfig.organization.sameAs
          })
        }}
      />
    </Head>
  );
};

// Structured Data Generators
export const generatePersonStructuredData = (profile) => {
  return {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": profile.fullName,
    "gender": profile.gender,
    "birthDate": profile.dateOfBirth,
    "height": profile.height,
    "address": {
      "@type": "PostalAddress",
      "addressLocality": profile.city,
      "addressRegion": profile.state,
      "addressCountry": "IN"
    },
    "jobTitle": profile.occupation,
    "alumniOf": profile.education,
    "religion": profile.religion,
    "image": profile.profilePhoto,
    "url": `${seoConfig.siteUrl}/profile/${profile.id}`
  };
};

export const generateWebsiteStructuredData = () => {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": seoConfig.organization.name,
    "url": seoConfig.siteUrl,
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${seoConfig.siteUrl}/search?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    }
  };
};

export const generateBreadcrumbStructuredData = (breadcrumbs) => {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": `${seoConfig.siteUrl}${crumb.url}`
    }))
  };
};

export const generateServiceStructuredData = () => {
  return {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Matrimony Matching Service",
    "description": "AI-powered matrimony matching service for the Maratha community",
    "provider": {
      "@type": "Organization",
      "name": seoConfig.organization.name,
      "url": seoConfig.organization.url
    },
    "areaServed": {
      "@type": "Country",
      "name": "India"
    },
    "serviceType": "Matrimony",
    "audience": {
      "@type": "Audience",
      "audienceType": "Maratha Community"
    }
  };
};

// Meta Tags for Different Page Types
export const getHomePageSEO = () => ({
  title: 'Premium Maratha Matrimony - Find Your Perfect Life Partner',
  description: 'Join India\'s most trusted Maratha matrimony platform. Advanced AI matching, verified profiles, and personalized matchmaking for the Maratha community.',
  structuredData: generateWebsiteStructuredData()
});

export const getProfilePageSEO = (profile) => ({
  title: `${profile.fullName} - Maratha Matrimony Profile`,
  description: `View ${profile.fullName}'s matrimony profile. ${profile.age} years, ${profile.occupation}, from ${profile.city}. Connect now on Vaivahik.`,
  image: profile.profilePhoto,
  structuredData: generatePersonStructuredData(profile),
  type: 'profile'
});

export const getSearchPageSEO = (filters) => {
  const location = filters.city || filters.state || 'India';
  const ageRange = filters.minAge && filters.maxAge ? `${filters.minAge}-${filters.maxAge}` : '';
  
  return {
    title: `Maratha Matrimony Profiles in ${location} ${ageRange ? `- Age ${ageRange}` : ''}`,
    description: `Find verified Maratha matrimony profiles in ${location}. ${ageRange ? `Age ${ageRange}, ` : ''}Browse profiles by education, occupation, and more.`,
    noindex: Object.keys(filters).length > 3 // Don't index complex filter combinations
  };
};

export const getBlogPostSEO = (post) => ({
  title: post.title,
  description: post.excerpt,
  image: post.featuredImage,
  type: 'article',
  publishedTime: post.publishedAt,
  modifiedTime: post.updatedAt,
  author: post.author,
  tags: post.tags,
  structuredData: {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": post.title,
    "description": post.excerpt,
    "image": post.featuredImage,
    "datePublished": post.publishedAt,
    "dateModified": post.updatedAt,
    "author": {
      "@type": "Person",
      "name": post.author
    },
    "publisher": {
      "@type": "Organization",
      "name": seoConfig.organization.name,
      "logo": seoConfig.organization.logo
    }
  }
});

// Sitemap Generation
export const generateSitemap = async () => {
  const staticPages = [
    '',
    '/about',
    '/contact',
    '/privacy-policy',
    '/terms-of-service',
    '/success-stories',
    '/blog',
    '/pricing'
  ];

  // Fetch dynamic pages (profiles, blog posts, etc.)
  const profiles = await fetch('/api/sitemap/profiles').then(res => res.json());
  const blogPosts = await fetch('/api/sitemap/blog-posts').then(res => res.json());

  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${staticPages.map(page => `
    <url>
      <loc>${seoConfig.siteUrl}${page}</loc>
      <lastmod>${new Date().toISOString()}</lastmod>
      <changefreq>weekly</changefreq>
      <priority>${page === '' ? '1.0' : '0.8'}</priority>
    </url>
  `).join('')}
  
  ${profiles.map(profile => `
    <url>
      <loc>${seoConfig.siteUrl}/profile/${profile.id}</loc>
      <lastmod>${profile.updatedAt}</lastmod>
      <changefreq>monthly</changefreq>
      <priority>0.6</priority>
    </url>
  `).join('')}
  
  ${blogPosts.map(post => `
    <url>
      <loc>${seoConfig.siteUrl}/blog/${post.slug}</loc>
      <lastmod>${post.updatedAt}</lastmod>
      <changefreq>monthly</changefreq>
      <priority>0.7</priority>
    </url>
  `).join('')}
</urlset>`;

  return sitemap;
};

// Robots.txt Generation
export const generateRobotsTxt = () => {
  return `User-agent: *
Allow: /

# Disallow admin and private pages
Disallow: /admin/
Disallow: /api/
Disallow: /dashboard/
Disallow: /profile/edit/
Disallow: /messages/

# Allow important pages
Allow: /profile/*/public
Allow: /search
Allow: /blog/

# Sitemap
Sitemap: ${seoConfig.siteUrl}/sitemap.xml

# Crawl delay
Crawl-delay: 1`;
};

// SEO Analytics
export const trackSEOEvent = (eventName, parameters = {}) => {
  if (typeof gtag !== 'undefined') {
    gtag('event', eventName, {
      event_category: 'SEO',
      ...parameters
    });
  }
};

// Page Speed Optimization
export const optimizePageSpeed = () => {
  if (typeof window === 'undefined') return;

  // Preload critical resources
  const preloadCriticalResources = () => {
    const criticalResources = [
      '/fonts/primary-font.woff2',
      '/images/hero-background.webp'
    ];

    criticalResources.forEach(resource => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = resource;
      link.as = resource.includes('.woff') ? 'font' : 'image';
      if (resource.includes('.woff')) {
        link.crossOrigin = 'anonymous';
      }
      document.head.appendChild(link);
    });
  };

  // Lazy load non-critical resources
  const lazyLoadResources = () => {
    const lazyImages = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src;
          img.removeAttribute('data-src');
          imageObserver.unobserve(img);
        }
      });
    });

    lazyImages.forEach(img => imageObserver.observe(img));
  };

  preloadCriticalResources();
  lazyLoadResources();
};

export default {
  SEOHead,
  useSEO,
  seoConfig,
  generatePersonStructuredData,
  generateWebsiteStructuredData,
  generateBreadcrumbStructuredData,
  generateServiceStructuredData,
  getHomePageSEO,
  getProfilePageSEO,
  getSearchPageSEO,
  getBlogPostSEO,
  generateSitemap,
  generateRobotsTxt,
  trackSEOEvent,
  optimizePageSpeed
};
