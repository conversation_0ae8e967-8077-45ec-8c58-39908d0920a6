/**
 * Mock Login API Endpoint
 *
 * This endpoint is only available in development mode and provides
 * a way to authenticate with mock credentials.
 * It supports both email and phone number login.
 */

import { authenticateMockUser } from '@/utils/mockAuth';

export default function handler(req, res) {
  // Only allow this endpoint in development mode
  if (process.env.NODE_ENV !== 'development') {
    return res.status(404).json({
      success: false,
      message: 'This endpoint is only available in development mode'
    });
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    // Get credentials from request body
    const { emailOrPhone, password } = req.body;

    // Validate required fields
    if (!emailOrPhone || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email/phone and password are required'
      });
    }

    // Authenticate user - try both as email and phone
    const authResult = authenticateMockUser(emailOrPhone, password);

    // If authentication failed
    if (!authResult) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Generate refresh token
    const refreshToken = `refresh-${authResult.user.id}-${Math.random().toString(36).substring(2)}`;

    // Set mock auth token cookie
    res.setHeader('Set-Cookie', [
      `mockAuthToken=${authResult.token}; Path=/; HttpOnly; SameSite=Strict; Max-Age=86400`,
      `mockRefreshToken=${refreshToken}; Path=/; HttpOnly; SameSite=Strict; Max-Age=604800`
    ]);

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Authentication successful',
      user: authResult.user,
      token: authResult.token,
      refreshToken: refreshToken,
      expiresIn: 86400 // 24 hours in seconds
    });
  } catch (error) {
    console.error('Mock login error:', error);
    return res.status(500).json({
      success: false,
      message: 'Authentication error',
      error: error.message
    });
  }
}
