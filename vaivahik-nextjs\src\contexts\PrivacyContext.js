import React, { createContext, useState, useEffect, useContext } from 'react';
import { getPrivacySettings, updatePrivacySettings } from '@/services/privacyService';
import { DEFAULT_PRIVACY_SETTINGS } from '@/config';
import { AuthContext } from './AuthContext';

// Create the context
export const PrivacyContext = createContext();

/**
 * Privacy Context Provider
 * 
 * Provides privacy settings and related functions to the application
 */
export const PrivacyProvider = ({ children }) => {
  const { isAuthenticated, user } = useContext(AuthContext);
  const [privacySettings, setPrivacySettings] = useState(DEFAULT_PRIVACY_SETTINGS);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Load privacy settings when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      loadPrivacySettings();
    }
  }, [isAuthenticated, user]);
  
  // Load privacy settings from API
  const loadPrivacySettings = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const settings = await getPrivacySettings();
      setPrivacySettings(settings);
    } catch (err) {
      setError('Failed to load privacy settings');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };
  
  // Save privacy settings to API
  const savePrivacySettings = async (settings) => {
    try {
      setLoading(true);
      setError(null);
      
      const updatedSettings = await updatePrivacySettings(settings);
      setPrivacySettings(updatedSettings);
      
      return { success: true };
    } catch (err) {
      setError('Failed to save privacy settings');
      console.error(err);
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  };
  
  // Check if a user can view specific content
  const canViewContent = (contentType, targetUser) => {
    if (!isAuthenticated || !user || !targetUser) {
      return false;
    }
    
    // Users can always view their own content
    if (user.id === targetUser.id) {
      return true;
    }
    
    const privacySetting = targetUser.privacySettings?.[`${contentType}Privacy`];
    
    switch (privacySetting) {
      case 'ALL_USERS':
        return true;
        
      case 'PREMIUM_USERS':
        return user.isPremium;
        
      case 'ACCEPTED_INTEREST':
        // Check if there's an accepted interest between the users
        const hasAcceptedInterest = user.acceptedInterests?.includes(targetUser.id) ||
                                   targetUser.acceptedInterests?.includes(user.id);
        return user.isPremium && hasAcceptedInterest;
        
      case 'HIDDEN':
        return false;
        
      default:
        return false;
    }
  };
  
  return (
    <PrivacyContext.Provider
      value={{
        privacySettings,
        loading,
        error,
        loadPrivacySettings,
        savePrivacySettings,
        canViewContent
      }}
    >
      {children}
    </PrivacyContext.Provider>
  );
};

// Custom hook to use the privacy context
export const usePrivacy = () => useContext(PrivacyContext);
