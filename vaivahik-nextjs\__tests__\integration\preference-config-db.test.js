/**
 * Integration tests for preference configuration database operations
 */
import { PrismaClient } from '@prisma/client';

// Create a test-specific Prisma client
const prisma = new PrismaClient();

// Test data
const testCategory = {
  name: 'test_category',
  displayName: 'Test Category',
  description: 'Test category for integration tests',
  displayOrder: 999,
  icon: 'test',
  isActive: true,
  isRequired: false,
};

const testField = {
  name: 'test_field',
  displayName: 'Test Field',
  description: 'Test field for integration tests',
  fieldType: 'TEXT',
  displayOrder: 999,
  isActive: true,
  isRequired: false,
  isSearchable: true,
  isMatchCriteria: true,
  defaultValue: '""',
};

const testOption = {
  value: 'test_option',
  displayText: 'Test Option',
  description: 'Test option for integration tests',
  displayOrder: 999,
  isActive: true,
};

const testImportance = {
  importanceLevel: 5.0,
  description: 'Test importance for integration tests',
  isActive: true,
  gender: 'ALL',
};

// Store created IDs for cleanup
let categoryId;
let fieldId;
let optionId;
let importanceId;

describe('Preference Configuration Database Operations', () => {
  // Connect to the database before tests
  beforeAll(async () => {
    await prisma.$connect();
  });

  // Disconnect from the database after tests
  afterAll(async () => {
    // Clean up test data
    if (importanceId) {
      await prisma.preferenceImportance.delete({ where: { id: importanceId } }).catch(() => {});
    }
    if (optionId) {
      await prisma.preferenceOption.delete({ where: { id: optionId } }).catch(() => {});
    }
    if (fieldId) {
      await prisma.preferenceField.delete({ where: { id: fieldId } }).catch(() => {});
    }
    if (categoryId) {
      await prisma.preferenceCategory.delete({ where: { id: categoryId } }).catch(() => {});
    }

    await prisma.$disconnect();
  });

  describe('Category operations', () => {
    test('should create a category', async () => {
      const category = await prisma.preferenceCategory.create({
        data: testCategory,
      });

      categoryId = category.id;

      expect(category).toHaveProperty('id');
      expect(category.name).toBe(testCategory.name);
      expect(category.displayName).toBe(testCategory.displayName);
    });

    test('should retrieve categories', async () => {
      const categories = await prisma.preferenceCategory.findMany({
        where: { id: categoryId },
      });

      expect(categories).toHaveLength(1);
      expect(categories[0].id).toBe(categoryId);
    });

    test('should update a category', async () => {
      const updatedCategory = await prisma.preferenceCategory.update({
        where: { id: categoryId },
        data: { displayName: 'Updated Test Category' },
      });

      expect(updatedCategory.displayName).toBe('Updated Test Category');
    });
  });

  describe('Field operations', () => {
    test('should create a field', async () => {
      const field = await prisma.preferenceField.create({
        data: {
          ...testField,
          categoryId,
        },
      });

      fieldId = field.id;

      expect(field).toHaveProperty('id');
      expect(field.name).toBe(testField.name);
      expect(field.categoryId).toBe(categoryId);
    });

    test('should retrieve fields', async () => {
      const fields = await prisma.preferenceField.findMany({
        where: { id: fieldId },
      });

      expect(fields).toHaveLength(1);
      expect(fields[0].id).toBe(fieldId);
    });

    test('should update a field', async () => {
      const updatedField = await prisma.preferenceField.update({
        where: { id: fieldId },
        data: { displayName: 'Updated Test Field' },
      });

      expect(updatedField.displayName).toBe('Updated Test Field');
    });
  });

  describe('Option operations', () => {
    test('should create an option', async () => {
      const option = await prisma.preferenceOption.create({
        data: {
          ...testOption,
          fieldId,
        },
      });

      optionId = option.id;

      expect(option).toHaveProperty('id');
      expect(option.value).toBe(testOption.value);
      expect(option.fieldId).toBe(fieldId);
    });

    test('should retrieve options', async () => {
      const options = await prisma.preferenceOption.findMany({
        where: { id: optionId },
      });

      expect(options).toHaveLength(1);
      expect(options[0].id).toBe(optionId);
    });

    test('should update an option', async () => {
      const updatedOption = await prisma.preferenceOption.update({
        where: { id: optionId },
        data: { displayText: 'Updated Test Option' },
      });

      expect(updatedOption.displayText).toBe('Updated Test Option');
    });
  });

  describe('Importance operations', () => {
    test('should create an importance setting', async () => {
      const importance = await prisma.preferenceImportance.create({
        data: {
          ...testImportance,
          fieldId,
        },
      });

      importanceId = importance.id;

      expect(importance).toHaveProperty('id');
      expect(importance.importanceLevel).toBe(testImportance.importanceLevel);
      expect(importance.fieldId).toBe(fieldId);
    });

    test('should retrieve importance settings', async () => {
      const importanceSettings = await prisma.preferenceImportance.findMany({
        where: { id: importanceId },
      });

      expect(importanceSettings).toHaveLength(1);
      expect(importanceSettings[0].id).toBe(importanceId);
    });

    test('should update an importance setting', async () => {
      const updatedImportance = await prisma.preferenceImportance.update({
        where: { id: importanceId },
        data: { importanceLevel: 7.5 },
      });

      expect(updatedImportance.importanceLevel).toBe(7.5);
    });
  });

  describe('Cascade delete operations', () => {
    test('should delete a field and its related options and importance settings', async () => {
      // First verify that the option and importance setting exist
      const optionsBefore = await prisma.preferenceOption.findMany({
        where: { id: optionId },
      });
      const importanceBefore = await prisma.preferenceImportance.findMany({
        where: { id: importanceId },
      });

      expect(optionsBefore).toHaveLength(1);
      expect(importanceBefore).toHaveLength(1);

      // Delete the field
      await prisma.preferenceField.delete({
        where: { id: fieldId },
      });

      // Verify that the option and importance setting are also deleted
      const optionsAfter = await prisma.preferenceOption.findMany({
        where: { id: optionId },
      });
      const importanceAfter = await prisma.preferenceImportance.findMany({
        where: { id: importanceId },
      });

      expect(optionsAfter).toHaveLength(0);
      expect(importanceAfter).toHaveLength(0);

      // Clear the IDs since they've been deleted
      optionId = null;
      importanceId = null;
      fieldId = null;
    });

    test('should delete a category and its related fields', async () => {
      // First create a new field for the category
      const field = await prisma.preferenceField.create({
        data: {
          ...testField,
          name: 'test_field_2',
          categoryId,
        },
      });

      // Verify that the field exists
      const fieldsBefore = await prisma.preferenceField.findMany({
        where: { id: field.id },
      });

      expect(fieldsBefore).toHaveLength(1);

      // Delete the category
      await prisma.preferenceCategory.delete({
        where: { id: categoryId },
      });

      // Verify that the field is also deleted
      const fieldsAfter = await prisma.preferenceField.findMany({
        where: { id: field.id },
      });

      expect(fieldsAfter).toHaveLength(0);

      // Clear the ID since it's been deleted
      categoryId = null;
    });
  });
});
