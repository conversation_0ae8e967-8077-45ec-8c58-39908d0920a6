// API endpoint for admin users
import { handleApiError } from '@/utils/errorHandler';
import { withAuth } from '@/utils/authHandler';

async function handler(req, res) {
  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getAdminUsers(req, res);
      case 'POST':
        return await createAdminUser(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    return handleApiError(error, res, 'Admin Users API');
  }
}

// GET /api/admin/admin-users
async function getAdminUsers(req, res) {
  try {
    // Get query parameters
    const { page = 1, limit = 10, search = '', role = '' } = req.query;
    
    // Mock data for admin users
    const allAdminUsers = [
      {
        id: 1,
        name: 'Super Admin',
        email: '<EMAIL>',
        role: 'SUPER_ADMIN',
        permissions: ['all'],
        lastLogin: '2023-08-01T10:15:00Z',
        createdAt: '2023-01-01T00:00:00Z',
        status: 'active',
        avatar: null
      },
      {
        id: 2,
        name: 'Content Manager',
        email: '<EMAIL>',
        role: 'CONTENT_MANAGER',
        permissions: ['blog_posts', 'success_stories', 'biodata_templates'],
        lastLogin: '2023-07-28T14:30:00Z',
        createdAt: '2023-02-15T00:00:00Z',
        status: 'active',
        avatar: null
      },
      {
        id: 3,
        name: 'User Manager',
        email: '<EMAIL>',
        role: 'USER_MANAGER',
        permissions: ['users', 'verification', 'reports'],
        lastLogin: '2023-07-30T09:45:00Z',
        createdAt: '2023-03-10T00:00:00Z',
        status: 'active',
        avatar: null
      },
      {
        id: 4,
        name: 'Finance Admin',
        email: '<EMAIL>',
        role: 'FINANCE_ADMIN',
        permissions: ['subscriptions', 'transactions', 'revenue_reports'],
        lastLogin: '2023-07-25T16:20:00Z',
        createdAt: '2023-04-05T00:00:00Z',
        status: 'active',
        avatar: null
      },
      {
        id: 5,
        name: 'Support Staff',
        email: '<EMAIL>',
        role: 'SUPPORT_STAFF',
        permissions: ['users', 'verification', 'reports', 'notifications'],
        lastLogin: '2023-07-31T11:10:00Z',
        createdAt: '2023-05-20T00:00:00Z',
        status: 'active',
        avatar: null
      },
      {
        id: 6,
        name: 'Test Admin',
        email: '<EMAIL>',
        role: 'CONTENT_MANAGER',
        permissions: ['blog_posts', 'success_stories'],
        lastLogin: null,
        createdAt: '2023-06-15T00:00:00Z',
        status: 'inactive',
        avatar: null
      }
    ];
    
    // Filter admin users based on search and role
    let filteredAdminUsers = [...allAdminUsers];
    
    if (search) {
      const searchLower = search.toLowerCase();
      filteredAdminUsers = filteredAdminUsers.filter(user => 
        user.name.toLowerCase().includes(searchLower) || 
        user.email.toLowerCase().includes(searchLower) ||
        user.role.toLowerCase().includes(searchLower)
      );
    }
    
    if (role) {
      filteredAdminUsers = filteredAdminUsers.filter(user => user.role === role);
    }
    
    // Calculate pagination
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedAdminUsers = filteredAdminUsers.slice(startIndex, endIndex);
    
    // Get unique roles for filtering
    const roles = [...new Set(allAdminUsers.map(user => user.role))];
    
    return res.status(200).json({
      success: true,
      adminUsers: paginatedAdminUsers,
      pagination: {
        total: filteredAdminUsers.length,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(filteredAdminUsers.length / parseInt(limit))
      },
      roles
    });
  } catch (error) {
    return handleApiError(error, res, 'Get admin users');
  }
}

// POST /api/admin/admin-users
async function createAdminUser(req, res) {
  try {
    const { name, email, role, permissions, password } = req.body;
    
    // Validate required fields
    if (!name || !email || !role || !password) {
      return res.status(400).json({
        success: false,
        message: 'Name, email, role, and password are required'
      });
    }
    
    // Check if email is valid
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email format'
      });
    }
    
    // Check if password is strong enough
    if (password.length < 8) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 8 characters long'
      });
    }
    
    // In a real implementation, this would create a new admin user in the database
    // For now, we'll just return a success response with mock data
    
    return res.status(201).json({
      success: true,
      message: 'Admin user created successfully',
      adminUser: {
        id: Math.floor(Math.random() * 1000) + 7,
        name,
        email,
        role,
        permissions: permissions || [],
        lastLogin: null,
        createdAt: new Date().toISOString(),
        status: 'active',
        avatar: null
      }
    });
  } catch (error) {
    return handleApiError(error, res, 'Create admin user');
  }
}

export default handler;
