import { useState, useEffect } from 'react';
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Typography,
  Paper,
  Divider,
  Alert
} from '@mui/material';
import axios from 'axios';
import { toast } from 'react-toastify';
import { useRouter } from 'next/router';

/**
 * Payment form component using Razorpay
 * @param {Object} props - Component props
 * @param {string} props.productType - Type of product (BIODATA, SPOTLIGHT)
 * @param {string} props.productId - ID of the product
 * @param {number} props.quantity - Quantity (for spotlight)
 * @param {Function} props.onSuccess - Callback on successful payment
 * @param {Function} props.onCancel - Callback on payment cancellation
 * @param {Function} props.onError - Callback on payment error
 */
export default function PaymentForm({
  productType,
  productId,
  quantity = 1,
  onSuccess,
  onCancel,
  onError
}) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [orderData, setOrderData] = useState(null);
  const [error, setError] = useState(null);
  const [showDialog, setShowDialog] = useState(false);
  const [processingPayment, setProcessingPayment] = useState(false);

  useEffect(() => {
    if (productType && productId) {
      createOrder();
    }
  }, [productType, productId, quantity]);

  const createOrder = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.post('/api/user/payments/create-order', {
        productType,
        productId,
        quantity
      });

      if (response.data.success) {
        setOrderData(response.data.order);
        setShowDialog(true);
      } else {
        setError(response.data.message || 'Failed to create payment order');
        if (onError) onError(response.data.message || 'Failed to create payment order');
      }

      setLoading(false);
    } catch (error) {
      console.error('Error creating payment order:', error);
      setError(error.response?.data?.message || 'Failed to create payment order');
      if (onError) onError(error.response?.data?.message || 'Failed to create payment order');
      setLoading(false);
    }
  };

  const handlePayment = () => {
    if (!orderData) return;

    setProcessingPayment(true);

    const options = {
      key: orderData.keyId,
      amount: orderData.amount * 100, // Amount in paise
      currency: orderData.currency,
      name: 'Vaivahik',
      description: `Payment for ${orderData.productDetails.name}`,
      order_id: orderData.orderId,
      prefill: {
        name: orderData.prefillData.name,
        email: orderData.prefillData.email,
        contact: orderData.prefillData.contact
      },
      notes: orderData.notes,
      theme: {
        color: '#9c27b0'
      },
      handler: function(response) {
        verifyPayment(response);
      },
      modal: {
        ondismiss: function() {
          setProcessingPayment(false);
          if (onCancel) onCancel();
        }
      }
    };

    // Load Razorpay script if not already loaded
    if (!window.Razorpay) {
      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.async = true;
      script.onload = () => {
        const razorpay = new window.Razorpay(options);
        razorpay.open();
      };
      document.body.appendChild(script);
    } else {
      const razorpay = new window.Razorpay(options);
      razorpay.open();
    }
  };

  const verifyPayment = async (response) => {
    try {
      setProcessingPayment(true);

      const verifyResponse = await axios.post('/api/user/payments/verify', {
        orderId: response.razorpay_order_id,
        paymentId: response.razorpay_payment_id,
        signature: response.razorpay_signature
      });

      if (verifyResponse.data.success) {
        toast.success('Payment successful!');
        setShowDialog(false);
        if (onSuccess) onSuccess(verifyResponse.data.result);
      } else {
        toast.error(verifyResponse.data.message || 'Payment verification failed');
        if (onError) onError(verifyResponse.data.message || 'Payment verification failed');
      }

      setProcessingPayment(false);
    } catch (error) {
      console.error('Error verifying payment:', error);
      toast.error(error.response?.data?.message || 'Payment verification failed');
      if (onError) onError(error.response?.data?.message || 'Payment verification failed');
      setProcessingPayment(false);
    }
  };

  const handleCancel = () => {
    setShowDialog(false);
    if (onCancel) onCancel();
  };

  return (
    <>
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ my: 2 }}>
          {error}
        </Alert>
      )}

      <Dialog
        open={showDialog}
        onClose={!processingPayment ? handleCancel : undefined}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Confirm Payment
        </DialogTitle>
        <DialogContent>
          {orderData && (
            <>
              <DialogContentText paragraph>
                You are about to purchase:
              </DialogContentText>

              <Paper variant="outlined" sx={{ p: { xs: 1.5, sm: 2 }, mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  {orderData.productDetails.name}
                </Typography>

                {orderData.productDetails.description && (
                  <Typography variant="body2" color="text.secondary" paragraph>
                    {orderData.productDetails.description}
                  </Typography>
                )}

                {productType === 'SPOTLIGHT' && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2">
                      Quantity: {quantity}
                    </Typography>
                    {orderData.productDetails.defaultCount > 1 && (
                      <Typography variant="body2">
                        Spotlights per purchase: {orderData.productDetails.defaultCount}
                      </Typography>
                    )}
                    {orderData.productDetails.totalCount > 1 && (
                      <Typography variant="body2" fontWeight="medium" color="secondary">
                        Total spotlights: {orderData.productDetails.totalCount}
                      </Typography>
                    )}
                  </Box>
                )}

                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  flexDirection: { xs: 'column', sm: 'row' },
                  alignItems: { xs: 'flex-start', sm: 'center' }
                }}>
                  {orderData.productDetails.discountedPrice ? (
                    <>
                      <Typography variant="body2" color="text.secondary" sx={{
                        textDecoration: 'line-through',
                        mr: { xs: 0, sm: 1 },
                        mb: { xs: 0.5, sm: 0 }
                      }}>
                        ₹{orderData.productDetails.price}
                      </Typography>
                      <Typography variant="h6" color="primary" fontWeight="bold">
                        ₹{orderData.productDetails.discountedPrice}
                      </Typography>
                    </>
                  ) : (
                    <Typography variant="h6" color="primary" fontWeight="bold">
                      ₹{orderData.productDetails.price}
                    </Typography>
                  )}
                </Box>
              </Paper>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body1">Total Amount:</Typography>
                <Typography variant="body1" fontWeight="bold">₹{orderData.amount}</Typography>
              </Box>

              <Alert severity="info" sx={{ mt: 2 }}>
                You will be redirected to Razorpay's secure payment gateway to complete your payment.
              </Alert>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleCancel}
            disabled={processingPayment}
          >
            Cancel
          </Button>
          <Button
            onClick={handlePayment}
            variant="contained"
            color="primary"
            disabled={processingPayment || !orderData}
          >
            {processingPayment ? <CircularProgress size={24} /> : 'Proceed to Payment'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
