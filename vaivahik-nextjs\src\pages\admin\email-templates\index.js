import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with <PERSON> disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);
import { adminGet, adminPost, adminPut, adminDelete } from '@/services/adminApiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';
import { toast } from 'react-toastify';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Pagination,
  Select,
  Switch,
  TextField,
  Typography,
  Tabs,
  Tab,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Send as SendIcon,
  ContentCopy as ContentCopyIcon,
  Email as EmailIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  FilterList as FilterListIcon,
  Preview as PreviewIcon,
  Code as CodeIcon,
  Palette as PaletteIcon,
  Schedule as ScheduleIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';

// Import the editor component dynamically to avoid SSR issues
const ReactQuill = dynamic(() => import('react-quill'), { ssr: false });
import 'react-quill/dist/quill.snow.css';

export default function EmailTemplates() {
  const router = useRouter();
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [search, setSearch] = useState('');
  const [category, setCategory] = useState('');
  const [categories, setCategories] = useState([]);

  // Template dialog state
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState('create');
  const [currentTemplate, setCurrentTemplate] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    content: '',
    category: '',
    variables: [],
    isActive: true
  });
  const [variableInput, setVariableInput] = useState('');
  const [dialogTab, setDialogTab] = useState(0);
  const [errors, setErrors] = useState({});

  // Preview dialog state
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewHtml, setPreviewHtml] = useState('');

  // Delete dialog state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [templateToDelete, setTemplateToDelete] = useState(null);

  useEffect(() => {
    fetchTemplates();
  }, [page, limit, search, category]);

  const fetchTemplates = async () => {
    setLoading(true);
    try {
      const response = await adminGet(ADMIN_ENDPOINTS.EMAIL_TEMPLATES, {
        page,
        limit,
        search,
        category
      });

      if (response.success) {
        setTemplates(response.templates);
        setTotal(response.pagination.total);
        setTotalPages(response.pagination.totalPages);
        setCategories(response.categories);
      } else {
        toast.error(response.message || 'Failed to fetch email templates');
      }
    } catch (error) {
      console.error('Error fetching email templates:', error);
      toast.error('Error fetching email templates');
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (event, value) => {
    setPage(value);
  };

  const handleLimitChange = (event) => {
    setLimit(event.target.value);
    setPage(1); // Reset to first page when changing limit
  };

  const handleSearchChange = (event) => {
    setSearch(event.target.value);
  };

  const handleCategoryChange = (event) => {
    setCategory(event.target.value);
  };

  const handleSearchSubmit = (event) => {
    event.preventDefault();
    setPage(1); // Reset to first page when searching
    fetchTemplates();
  };

  // Dialog handlers
  const handleAddTemplate = () => {
    setDialogMode('create');
    setCurrentTemplate(null);
    setFormData({
      name: '',
      subject: '',
      content: '',
      category: '',
      variables: [],
      isActive: true
    });
    setErrors({});
    setDialogTab(0);
    setDialogOpen(true);
  };

  const handleEditTemplate = (template) => {
    setDialogMode('edit');
    setCurrentTemplate(template);
    setFormData({
      name: template.name,
      subject: template.subject,
      content: template.content,
      category: template.category,
      variables: template.variables || [],
      isActive: template.isActive
    });
    setErrors({});
    setDialogTab(0);
    setDialogOpen(true);
  };

  const handleViewTemplate = (template) => {
    setDialogMode('view');
    setCurrentTemplate(template);
    setFormData({
      name: template.name,
      subject: template.subject,
      content: template.content,
      category: template.category,
      variables: template.variables || [],
      isActive: template.isActive
    });
    setDialogTab(0);
    setDialogOpen(true);
  };

  const handlePreviewTemplate = (template) => {
    // Replace variables with sample values
    let previewContent = template.content;

    if (template.variables && template.variables.length > 0) {
      template.variables.forEach(variable => {
        const regex = new RegExp(`{{${variable}}}`, 'g');
        let sampleValue = '';

        // Generate sample values based on variable name
        switch (variable) {
          case 'name':
            sampleValue = 'Rahul Sharma';
            break;
          case 'email':
            sampleValue = '<EMAIL>';
            break;
          case 'match_name':
            sampleValue = 'Priya Patel';
            break;
          case 'match_age':
            sampleValue = '26';
            break;
          case 'match_location':
            sampleValue = 'Mumbai';
            break;
          case 'compatibility_score':
            sampleValue = '85';
            break;
          case 'amount':
            sampleValue = '2,999';
            break;
          case 'expiry_date':
            sampleValue = '15 August 2024';
            break;
          case 'sender_name':
            sampleValue = 'Neha Gupta';
            break;
          case 'reset_link':
          case 'match_profile_link':
          case 'sender_profile_link':
          case 'profile_link':
            sampleValue = 'https://vaivahik.com/profile/123';
            break;
          case 'plan_name':
            sampleValue = 'Premium Gold';
            break;
          default:
            sampleValue = `[Sample ${variable}]`;
        }

        previewContent = previewContent.replace(regex, sampleValue);
      });
    }

    // Add email wrapper
    const wrappedHtml = `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333;">
        <div style="background-color: #5e35b1; padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">Vaivahik</h1>
        </div>
        <div style="padding: 20px; border: 1px solid #ddd; border-top: none;">
          <h2 style="color: #5e35b1;">${template.subject}</h2>
          <div>${previewContent}</div>
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #777; text-align: center;">
            <p>© 2023 Vaivahik. All rights reserved.</p>
            <p>This is a preview of an email template. No emails were sent.</p>
          </div>
        </div>
      </div>
    `;

    setPreviewHtml(wrappedHtml);
    setPreviewOpen(true);
  };

  const handleDeleteClick = (template) => {
    setTemplateToDelete(template);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      const response = await adminDelete(ADMIN_ENDPOINTS.EMAIL_TEMPLATE_DETAILS(templateToDelete.id));

      if (response.success) {
        toast.success('Email template deleted successfully');
        fetchTemplates(); // Refresh the list
      } else {
        toast.error(response.message || 'Failed to delete email template');
      }
    } catch (error) {
      console.error('Error deleting email template:', error);
      toast.error('Error deleting email template');
    } finally {
      setDeleteDialogOpen(false);
      setTemplateToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setTemplateToDelete(null);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
  };

  const handleDialogTabChange = (event, newValue) => {
    setDialogTab(newValue);
  };

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when field is updated
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const handleContentChange = (content) => {
    setFormData(prev => ({ ...prev, content }));

    // Clear error when content is updated
    if (errors.content) {
      setErrors(prev => ({ ...prev, content: null }));
    }
  };

  const handleStatusChange = (e) => {
    setFormData(prev => ({ ...prev, isActive: e.target.checked }));
  };

  const handleVariableInputChange = (e) => {
    setVariableInput(e.target.value);
  };

  const handleVariableInputKeyDown = (e) => {
    if (e.key === 'Enter' && variableInput.trim()) {
      e.preventDefault();
      addVariable(variableInput.trim());
    }
  };

  const addVariable = (variable) => {
    if (variable && !formData.variables.includes(variable)) {
      setFormData(prev => ({
        ...prev,
        variables: [...prev.variables, variable]
      }));
    }
    setVariableInput('');
  };

  const removeVariable = (variableToRemove) => {
    setFormData(prev => ({
      ...prev,
      variables: prev.variables.filter(variable => variable !== variableToRemove)
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.subject.trim()) {
      newErrors.subject = 'Subject is required';
    }

    if (!formData.content.trim()) {
      newErrors.content = 'Content is required';
    }

    if (!formData.category.trim()) {
      newErrors.category = 'Category is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (dialogMode === 'view') {
      handleDialogClose();
      return;
    }

    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    setLoading(true);
    try {
      let response;
      if (dialogMode === 'edit') {
        response = await adminPut(ADMIN_ENDPOINTS.EMAIL_TEMPLATE_DETAILS(currentTemplate.id), formData);
      } else {
        response = await adminPost(ADMIN_ENDPOINTS.EMAIL_TEMPLATES, formData);
      }

      if (response.success) {
        toast.success(dialogMode === 'edit' ? 'Email template updated successfully' : 'Email template created successfully');
        fetchTemplates(); // Refresh the list
        handleDialogClose();
      } else {
        toast.error(response.message || 'Failed to save email template');
      }
    } catch (error) {
      console.error('Error saving email template:', error);
      toast.error('Error saving email template');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <EnhancedAdminLayout title="Email Templates">
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Email Templates
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleAddTemplate}
          >
            Create Template
          </Button>
        </Box>

        {/* Filters */}
        <Box sx={{ mb: 3, p: { xs: 1.5, sm: 2 }, bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>
          <Grid container spacing={{ xs: 1, sm: 2 }} alignItems="center">
            <Grid item xs={12} sm={12} md={4}>
              <form onSubmit={handleSearchSubmit}>
                <TextField
                  fullWidth
                  label="Search Templates"
                  value={search}
                  onChange={handleSearchChange}
                  size="small"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton type="submit" edge="end" size="small">
                          <SearchIcon />
                        </IconButton>
                      </InputAdornment>
                    )
                  }}
                />
              </form>
            </Grid>
            <Grid item xs={6} sm={6} md={4}>
              <FormControl fullWidth size="small">
                <InputLabel>Category</InputLabel>
                <Select
                  value={category}
                  onChange={handleCategoryChange}
                  label="Category"
                >
                  <MenuItem value="">All Categories</MenuItem>
                  {categories.map((cat) => (
                    <MenuItem key={cat} value={cat}>{cat}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6} sm={6} md={4}>
              <FormControl fullWidth size="small">
                <InputLabel>Show</InputLabel>
                <Select
                  value={limit}
                  onChange={handleLimitChange}
                  label="Show"
                >
                  <MenuItem value={5}>5</MenuItem>
                  <MenuItem value={10}>10</MenuItem>
                  <MenuItem value={25}>25</MenuItem>
                  <MenuItem value={50}>50</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Box>

        {/* Templates List */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 5 }}>
            <div className="loading-spinner"></div>
          </Box>
        ) : templates.length === 0 ? (
          <Box sx={{ textAlign: 'center', my: 5, p: 3, bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>
            <Typography variant="h6" gutterBottom>
              No email templates found
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Try changing your search criteria or create a new template.
            </Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddTemplate}
              sx={{ mt: 2 }}
            >
              Create Template
            </Button>
          </Box>
        ) : (
          <Grid container spacing={{ xs: 2, md: 3 }}>
            {templates.map((template) => (
              <Grid item xs={12} sm={6} key={template.id}>
                <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <CardContent sx={{ flexGrow: 1, p: { xs: 2, sm: 3 } }}>
                    <Box sx={{
                      display: 'flex',
                      flexDirection: { xs: 'column', sm: 'row' },
                      justifyContent: 'space-between',
                      alignItems: { xs: 'flex-start', sm: 'center' },
                      gap: { xs: 1, sm: 0 },
                      mb: 2
                    }}>
                      <Typography
                        variant="h6"
                        component="div"
                        sx={{
                          fontWeight: 'bold',
                          fontSize: { xs: '1rem', sm: '1.25rem' },
                          lineHeight: 1.2
                        }}
                      >
                        {template.name}
                      </Typography>
                      <Chip
                        label={template.isActive ? 'Active' : 'Inactive'}
                        color={template.isActive ? 'success' : 'default'}
                        size="small"
                        sx={{ alignSelf: { xs: 'flex-start', sm: 'center' } }}
                      />
                    </Box>
                    <Typography
                      variant="subtitle2"
                      color="text.secondary"
                      gutterBottom
                      sx={{
                        display: '-webkit-box',
                        overflow: 'hidden',
                        WebkitBoxOrient: 'vertical',
                        WebkitLineClamp: 2,
                        fontSize: { xs: '0.75rem', sm: '0.875rem' }
                      }}
                    >
                      {template.subject}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                      Category: <Chip label={template.category} size="small" />
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                      {template.variables && template.variables.slice(0, 3).map((variable) => (
                        <Chip
                          key={variable}
                          label={`{{${variable}}}`}
                          size="small"
                          variant="outlined"
                          sx={{
                            '& .MuiChip-label': {
                              fontSize: { xs: '0.625rem', sm: '0.75rem' },
                              px: { xs: 0.5, sm: 1 }
                            }
                          }}
                        />
                      ))}
                      {template.variables && template.variables.length > 3 && (
                        <Chip
                          label={`+${template.variables.length - 3} more`}
                          size="small"
                          variant="outlined"
                          sx={{
                            '& .MuiChip-label': {
                              fontSize: { xs: '0.625rem', sm: '0.75rem' },
                              px: { xs: 0.5, sm: 1 }
                            }
                          }}
                        />
                      )}
                    </Box>
                    <Divider sx={{ my: 2 }} />
                    <Box sx={{
                      display: 'flex',
                      flexDirection: { xs: 'column', sm: 'row' },
                      justifyContent: 'space-between',
                      gap: { xs: 0.5, sm: 0 }
                    }}>
                      <Typography variant="caption" color="text.secondary">
                        Last sent: {formatDate(template.lastSent)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Sent {template.sendCount} times
                      </Typography>
                    </Box>
                  </CardContent>
                  <Box sx={{
                    display: 'flex',
                    justifyContent: { xs: 'space-between', sm: 'flex-end' },
                    p: { xs: 1, sm: 1.5 },
                    gap: { xs: 0, sm: 0.5 }
                  }}>
                    <IconButton onClick={() => handleViewTemplate(template)} color="primary" title="View Template" size="small">
                      <VisibilityIcon fontSize="small" />
                    </IconButton>
                    <IconButton onClick={() => handlePreviewTemplate(template)} color="primary" title="Preview Email" size="small">
                      <SendIcon fontSize="small" />
                    </IconButton>
                    <IconButton onClick={() => handleEditTemplate(template)} color="primary" title="Edit Template" size="small">
                      <EditIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      onClick={() => handleDeleteClick(template)}
                      color="error"
                      title="Delete Template"
                      disabled={template.id <= 3} // Prevent deleting system templates
                      size="small"
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <Pagination
              count={totalPages}
              page={page}
              onChange={handlePageChange}
              color="primary"
              showFirstButton
              showLastButton
            />
          </Box>
        )}

        {/* Template Dialog */}
        <Dialog open={dialogOpen} onClose={handleDialogClose} maxWidth="md" fullWidth>
          <DialogTitle>
            {dialogMode === 'create' ? 'Create Email Template' :
             dialogMode === 'edit' ? 'Edit Email Template' : 'View Email Template'}
          </DialogTitle>
          <DialogContent>
            <Tabs value={dialogTab} onChange={handleDialogTabChange} sx={{ mb: 2 }}>
              <Tab label="Basic Info" />
              <Tab label="Content" />
              <Tab label="Variables" />
            </Tabs>

            {/* Basic Info Tab */}
            {dialogTab === 0 && (
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Template Name"
                    name="name"
                    value={formData.name}
                    onChange={handleFormChange}
                    disabled={dialogMode === 'view'}
                    error={!!errors.name}
                    helperText={errors.name}
                    required
                    margin="normal"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Email Subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleFormChange}
                    disabled={dialogMode === 'view'}
                    error={!!errors.subject}
                    helperText={errors.subject}
                    required
                    margin="normal"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth margin="normal" error={!!errors.category}>
                    <InputLabel>Category</InputLabel>
                    <Select
                      name="category"
                      value={formData.category}
                      onChange={handleFormChange}
                      label="Category"
                      disabled={dialogMode === 'view'}
                      required
                    >
                      {categories.map((cat) => (
                        <MenuItem key={cat} value={cat}>{cat}</MenuItem>
                      ))}
                      <MenuItem value="Other">Other</MenuItem>
                    </Select>
                    {errors.category && (
                      <Typography color="error" variant="caption">
                        {errors.category}
                      </Typography>
                    )}
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={formData.isActive}
                          onChange={handleStatusChange}
                          disabled={dialogMode === 'view'}
                        />
                      }
                      label={`Status: ${formData.isActive ? 'Active' : 'Inactive'}`}
                    />
                  </Box>
                </Grid>
              </Grid>
            )}

            {/* Content Tab */}
            {dialogTab === 1 && (
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Email Content
                </Typography>
                {errors.content && (
                  <Typography color="error" variant="caption">
                    {errors.content}
                  </Typography>
                )}
                <Box sx={{ border: errors.content ? '1px solid #d32f2f' : 'none', borderRadius: 1 }}>
                  {dialogMode === 'view' ? (
                    <Box
                      sx={{
                        border: '1px solid #ddd',
                        borderRadius: 1,
                        p: 2,
                        minHeight: '300px',
                        '& img': { maxWidth: '100%' }
                      }}
                      dangerouslySetInnerHTML={{ __html: formData.content }}
                    />
                  ) : (
                    <ReactQuill
                      value={formData.content}
                      onChange={handleContentChange}
                      style={{ height: '300px', marginBottom: '50px' }}
                      modules={{
                        toolbar: [
                          [{ 'header': [1, 2, 3, false] }],
                          ['bold', 'italic', 'underline', 'strike', 'blockquote'],
                          [{'list': 'ordered'}, {'list': 'bullet'}, {'indent': '-1'}, {'indent': '+1'}],
                          ['link', 'image'],
                          ['clean']
                        ],
                      }}
                    />
                  )}
                </Box>
              </Box>
            )}

            {/* Variables Tab */}
            {dialogTab === 2 && (
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Template Variables
                </Typography>
                <Typography variant="body2" color="textSecondary" paragraph>
                  Variables will be replaced with actual values when the email is sent. Use the format {{variable_name}} in your template content.
                </Typography>

                {dialogMode !== 'view' && (
                  <TextField
                    fullWidth
                    label="Add Variable"
                    value={variableInput}
                    onChange={handleVariableInputChange}
                    onKeyDown={handleVariableInputKeyDown}
                    placeholder="Type and press Enter"
                    margin="normal"
                    helperText="Press Enter to add a variable"
                  />
                )}

                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 2 }}>
                  {formData.variables.map((variable) => (
                    <Chip
                      key={variable}
                      label={`{{${variable}}}`}
                      onDelete={dialogMode !== 'view' ? () => removeVariable(variable) : undefined}
                      color="primary"
                      variant="outlined"
                    />
                  ))}
                  {formData.variables.length === 0 && (
                    <Typography variant="body2" color="textSecondary">
                      No variables defined
                    </Typography>
                  )}
                </Box>

                {dialogMode !== 'view' && (
                  <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Common Variables
                    </Typography>
                    <Grid container spacing={1}>
                      {['name', 'email', 'profile_link', 'match_name', 'match_profile_link', 'amount', 'expiry_date'].map((commonVar) => (
                        <Grid item key={commonVar}>
                          <Chip
                            label={commonVar}
                            onClick={() => addVariable(commonVar)}
                            color="default"
                            variant="outlined"
                            size="small"
                            icon={<ContentCopyIcon fontSize="small" />}
                          />
                        </Grid>
                      ))}
                    </Grid>
                  </Box>
                )}
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleDialogClose}>
              {dialogMode === 'view' ? 'Close' : 'Cancel'}
            </Button>
            {dialogMode !== 'view' && (
              <Button
                onClick={handleSubmit}
                color="primary"
                variant="contained"
                disabled={loading}
              >
                {loading ? 'Saving...' : dialogMode === 'edit' ? 'Update Template' : 'Create Template'}
              </Button>
            )}
          </DialogActions>
        </Dialog>

        {/* Preview Dialog */}
        <Dialog open={previewOpen} onClose={() => setPreviewOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>Email Preview</DialogTitle>
          <DialogContent>
            <Box
              sx={{
                border: '1px solid #ddd',
                borderRadius: 1,
                p: 0,
                '& img': { maxWidth: '100%' }
              }}
              dangerouslySetInnerHTML={{ __html: previewHtml }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setPreviewOpen(false)}>Close</Button>
          </DialogActions>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={deleteDialogOpen}
          onClose={handleDeleteCancel}
        >
          <DialogTitle>Delete Email Template</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete the email template "{templateToDelete?.name}"? This action cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleDeleteCancel}>Cancel</Button>
            <Button onClick={handleDeleteConfirm} color="error" autoFocus>
              Delete
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </EnhancedAdminLayout>
  );
}
