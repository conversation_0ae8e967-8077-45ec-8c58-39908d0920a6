// test-document-upload.js
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const FormData = require('form-data');
require('dotenv').config();

// Configuration
const API_URL = 'http://localhost:8000/api';
const USER_ID = 'cma4ztj0z000086fw6mzvg9dc'; // Replace with your test user ID
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJjbWE0enRqMHowMDAwODZmdzZtenZnOWRjIiwicGhvbmUiOiI5OTk5OTk5OTk5IiwiaWF0IjoxNzQ2MDgxNDYyLCJleHAiOjE3NDYwODUwNjJ9.MFOY2WnTR-0vwXsmO_sCC6Qqm7Awk49Uemv_Lk7yQ2s'; // Replace with your JWT token

// Create a sample document for testing
async function createSampleDocument() {
  const sampleDir = path.join(__dirname, 'test-samples');
  const sampleFilePath = path.join(sampleDir, 'sample-id.jpg');
  
  // Create directory if it doesn't exist
  if (!fs.existsSync(sampleDir)) {
    fs.mkdirSync(sampleDir, { recursive: true });
  }
  
  // Create a simple image if it doesn't exist
  if (!fs.existsSync(sampleFilePath)) {
    // Create a simple colored rectangle as a sample ID
    const sharp = require('sharp');
    await sharp({
      create: {
        width: 800,
        height: 500,
        channels: 4,
        background: { r: 255, g: 255, b: 255, alpha: 1 }
      }
    })
    .composite([
      {
        input: Buffer.from(
          '<svg width="800" height="500">' +
          '<rect x="0" y="0" width="800" height="500" fill="#f0f0f0" />' +
          '<text x="50" y="50" font-family="Arial" font-size="30" fill="black">SAMPLE ID CARD</text>' +
          '<text x="50" y="100" font-family="Arial" font-size="20" fill="black">Name: Test User</text>' +
          '<text x="50" y="150" font-family="Arial" font-size="20" fill="black">ID: 123456789</text>' +
          '<text x="50" y="200" font-family="Arial" font-size="20" fill="black">DOB: 01/01/1990</text>' +
          '<rect x="500" y="50" width="200" height="200" fill="#d0d0d0" />' +
          '<text x="550" y="150" font-family="Arial" font-size="20" fill="black">PHOTO</text>' +
          '</svg>'
        ),
        top: 0,
        left: 0
      }
    ])
    .toFormat('jpeg')
    .toFile(sampleFilePath);
    
    console.log(`Sample ID created at: ${sampleFilePath}`);
  } else {
    console.log(`Sample ID already exists at: ${sampleFilePath}`);
  }
  
  return sampleFilePath;
}

// Upload the document
async function uploadDocument(filePath) {
  try {
    const formData = new FormData();
    formData.append('document', fs.createReadStream(filePath));
    formData.append('documentType', 'AADHAR_CARD');
    
    const response = await axios.post(
      `${API_URL}/users/verification/documents`,
      formData,
      {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${JWT_TOKEN}`
        }
      }
    );
    
    console.log('Document uploaded successfully:');
    console.log(JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('Error uploading document:');
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    } else {
      console.error(error.message);
    }
    throw error;
  }
}

// Check user profile status
async function checkUserProfile() {
  try {
    const response = await axios.get(
      `${API_URL}/users/profile`,
      {
        headers: {
          'Authorization': `Bearer ${JWT_TOKEN}`
        }
      }
    );
    
    console.log('User profile:');
    console.log(`ID: ${response.data.id}`);
    console.log(`Verification Status: ${response.data.isVerified ? 'Verified' : 'Not Verified'}`);
    console.log(`Profile Status: ${response.data.profileStatus}`);
    return response.data;
  } catch (error) {
    console.error('Error checking user profile:');
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    } else {
      console.error(error.message);
    }
    throw error;
  }
}

// Get user's verification documents
async function getVerificationDocuments() {
  try {
    const response = await axios.get(
      `${API_URL}/users/verification/documents`,
      {
        headers: {
          'Authorization': `Bearer ${JWT_TOKEN}`
        }
      }
    );
    
    console.log('Verification documents:');
    console.log(JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('Error getting verification documents:');
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    } else {
      console.error(error.message);
    }
    throw error;
  }
}

// Run the test
async function runTest() {
  try {
    console.log('=== VERIFICATION FLOW TEST ===');
    
    // Step 1: Check initial user profile status
    console.log('\n1. Checking initial user profile status...');
    await checkUserProfile();
    
    // Step 2: Create and upload a sample document
    console.log('\n2. Creating and uploading a sample document...');
    const sampleFilePath = await createSampleDocument();
    await uploadDocument(sampleFilePath);
    
    // Step 3: Check user profile status after document upload
    console.log('\n3. Checking user profile status after document upload...');
    await checkUserProfile();
    
    // Step 4: Get verification documents
    console.log('\n4. Getting verification documents...');
    await getVerificationDocuments();
    
    console.log('\n=== TEST COMPLETED SUCCESSFULLY ===');
  } catch (error) {
    console.error('\n=== TEST FAILED ===');
    console.error(error);
  }
}

// Install required packages if not already installed
async function installDependencies() {
  const { execSync } = require('child_process');
  try {
    console.log('Installing required dependencies...');
    execSync('npm install axios form-data sharp --no-save', { stdio: 'inherit' });
    console.log('Dependencies installed successfully.');
  } catch (error) {
    console.error('Error installing dependencies:', error.message);
    process.exit(1);
  }
}

// Run the test
installDependencies().then(runTest);
