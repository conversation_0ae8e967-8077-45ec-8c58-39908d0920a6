.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.paper {
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.title {
  text-align: center;
  margin-bottom: 2rem;
  color: #6200ea;
  font-weight: 600;
}

.stepper {
  margin-bottom: 2rem;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.input:focus {
  border-color: #6200ea;
  outline: none;
  box-shadow: 0 0 0 2px rgba(98, 0, 234, 0.2);
}

.error {
  color: #d32f2f;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.buttonContainer {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

.button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.buttonPrimary {
  background-color: #6200ea;
  color: white;
}

.buttonPrimary:hover {
  background-color: #5000d3;
}

.buttonSecondary {
  background-color: #f5f5f5;
  color: #333;
}

.buttonSecondary:hover {
  background-color: #e0e0e0;
}

.buttonDisabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.radioGroup {
  display: flex;
  gap: 1rem;
}

.radioLabel {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.radio {
  margin-right: 0.5rem;
}

.select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  background-color: white;
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
  padding-right: 2.5rem;
}

.select:focus {
  border-color: #6200ea;
  outline: none;
  box-shadow: 0 0 0 2px rgba(98, 0, 234, 0.2);
}

.gridContainer {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

@media (max-width: 768px) {
  .gridContainer {
    grid-template-columns: 1fr;
  }
  
  .paper {
    padding: 1.5rem;
  }
}

/* Birth Details Specific Styles */
.birthDetailsContainer {
  background-color: #f9f9ff;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.birthDetailsTitle {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  color: #6200ea;
}

.birthDetailsIcon {
  margin-right: 0.5rem;
}

.dateTimeContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.locationContainer {
  position: relative;
}

.locationSuggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 0 0 8px 8px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 10;
}

.locationItem {
  padding: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.locationItem:hover {
  background-color: #f5f5f5;
}

.helpText {
  font-size: 0.875rem;
  color: #666;
  margin-top: 0.25rem;
}

.timeFormatToggle {
  font-size: 0.875rem;
  color: #6200ea;
  cursor: pointer;
  margin-left: auto;
}

.timeFormatToggle:hover {
  text-decoration: underline;
}
