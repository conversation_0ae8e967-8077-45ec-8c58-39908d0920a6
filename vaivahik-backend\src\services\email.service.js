// src/services/email.service.js

const nodemailer = require('nodemailer');

// Create a test account if no SMTP settings are provided
let transporter;

/**
 * Initialize the email transporter
 * @returns {Promise<void>}
 */
async function initTransporter() {
    if (process.env.SMTP_HOST && process.env.SMTP_PORT) {
        // Use provided SMTP settings
        transporter = nodemailer.createTransport({
            host: process.env.SMTP_HOST,
            port: process.env.SMTP_PORT,
            secure: process.env.SMTP_SECURE === 'true',
            auth: {
                user: process.env.SMTP_USER,
                pass: process.env.SMTP_PASS
            }
        });
    } else {
        // Create a test account for development
        console.log('No SMTP settings found. Using test account for email.');
        const testAccount = await nodemailer.createTestAccount();
        
        transporter = nodemailer.createTransport({
            host: 'smtp.ethereal.email',
            port: 587,
            secure: false,
            auth: {
                user: testAccount.user,
                pass: testAccount.pass
            }
        });
    }
}

// Initialize the transporter
initTransporter().catch(console.error);

/**
 * Send an email
 * @param {Object} options - Email options
 * @param {string} options.to - Recipient email
 * @param {string} options.subject - Email subject
 * @param {string} options.template - Template name (not implemented yet)
 * @param {Object} options.data - Template data
 * @returns {Promise<Object>} Email info
 */
async function sendEmail(options) {
    try {
        if (!transporter) {
            await initTransporter();
        }
        
        const { to, subject, template, data } = options;
        
        // Simple template handling (in a real app, you'd use a template engine)
        let html = '';
        
        if (template === 'verification-reminder') {
            html = `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2 style="color: #7e57c2;">Verify Your Vaivahik Profile</h2>
                    <p>Hello ${data.name},</p>
                    <p>We noticed that you haven't verified your profile yet. Verified profiles get more responses and have access to premium features.</p>
                    <div style="margin: 20px 0;">
                        <a href="${data.verificationLink}" style="background-color: #7e57c2; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">Verify Now</a>
                    </div>
                    <p>Benefits of verification:</p>
                    <ul>
                        <li>Get a verification badge on your profile</li>
                        <li>Increase your profile visibility</li>
                        <li>Access premium features</li>
                        <li>Build trust with other members</li>
                    </ul>
                    <p>If you have any questions, please contact our support team.</p>
                    <p>Best regards,<br>The Vaivahik Team</p>
                </div>
            `;
        } else {
            // Default email template
            html = `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2 style="color: #7e57c2;">Vaivahik</h2>
                    <p>Hello ${data.name || 'User'},</p>
                    <p>${data.message || 'This is a notification from Vaivahik.'}</p>
                    <p>Best regards,<br>The Vaivahik Team</p>
                </div>
            `;
        }
        
        const info = await transporter.sendMail({
            from: process.env.EMAIL_FROM || '"Vaivahik" <<EMAIL>>',
            to,
            subject,
            html
        });
        
        console.log(`Email sent: ${info.messageId}`);
        
        // Log preview URL in development
        if (info.messageId && info.messageId.includes('ethereal.email')) {
            console.log(`Email preview URL: ${nodemailer.getTestMessageUrl(info)}`);
        }
        
        return info;
    } catch (error) {
        console.error('Error sending email:', error);
        throw error;
    }
}

module.exports = {
    sendEmail
};
