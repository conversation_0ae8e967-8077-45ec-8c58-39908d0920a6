import { useState, useEffect, useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';

/**
 * Custom hook for WebSocket notifications
 * 
 * @param {Object} options - Options for the WebSocket connection
 * @param {boolean} options.autoReconnect - Whether to automatically reconnect (default: true)
 * @param {number} options.reconnectInterval - Interval in ms between reconnect attempts (default: 3000)
 * @param {number} options.maxReconnectAttempts - Maximum number of reconnect attempts (default: 5)
 * @returns {Object} WebSocket state and methods
 */
export default function useNotificationSocket(options = {}) {
  const { 
    autoReconnect = true, 
    reconnectInterval = 3000,
    maxReconnectAttempts = 5
  } = options;
  
  const { data: session } = useSession();
  const [isConnected, setIsConnected] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [error, setError] = useState(null);
  
  const socketRef = useRef(null);
  const reconnectAttemptsRef = useRef(0);
  const reconnectTimeoutRef = useRef(null);
  
  // Connect to WebSocket
  const connect = useCallback(() => {
    if (!session?.user?.token) return;
    
    try {
      // Close existing connection if any
      if (socketRef.current && socketRef.current.readyState !== WebSocket.CLOSED) {
        socketRef.current.close();
      }
      
      // Create new WebSocket connection
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = process.env.NEXT_PUBLIC_WS_HOST || window.location.host;
      const wsUrl = `${protocol}//${host}/ws?token=${session.user.token}`;
      
      const socket = new WebSocket(wsUrl);
      socketRef.current = socket;
      
      // Connection opened
      socket.addEventListener('open', () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        setError(null);
        reconnectAttemptsRef.current = 0;
        
        // Set up ping interval to keep connection alive
        const pingInterval = setInterval(() => {
          if (socket.readyState === WebSocket.OPEN) {
            socket.send(JSON.stringify({ type: 'PING' }));
          } else {
            clearInterval(pingInterval);
          }
        }, 30000); // Send ping every 30 seconds
        
        // Store interval ID for cleanup
        socketRef.current.pingInterval = pingInterval;
      });
      
      // Listen for messages
      socket.addEventListener('message', (event) => {
        try {
          const data = JSON.parse(event.data);
          
          switch (data.type) {
            case 'NOTIFICATION':
              // Add new notification to the list
              setNotifications(prev => [data.notification, ...prev]);
              // Increment unread count
              setUnreadCount(prev => prev + 1);
              break;
              
            case 'NOTIFICATIONS':
              // Replace notifications list
              setNotifications(data.notifications);
              break;
              
            case 'UNREAD_COUNT':
              // Update unread count
              setUnreadCount(data.count);
              break;
              
            case 'NOTIFICATION_READ':
              // Mark notification as read
              setNotifications(prev => 
                prev.map(notification => 
                  notification.id === data.notificationId 
                    ? { ...notification, isRead: true } 
                    : notification
                )
              );
              // Decrement unread count
              setUnreadCount(prev => Math.max(0, prev - 1));
              break;
              
            case 'ALL_NOTIFICATIONS_READ':
              // Mark all notifications as read
              setNotifications(prev => 
                prev.map(notification => ({ ...notification, isRead: true }))
              );
              // Reset unread count
              setUnreadCount(0);
              break;
              
            case 'PONG':
              // Ping response, do nothing
              break;
              
            case 'ERROR':
              console.error('WebSocket error:', data.message);
              setError(data.message);
              break;
              
            default:
              console.log('Unknown message type:', data.type);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      });
      
      // Connection closed
      socket.addEventListener('close', (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        
        // Clear ping interval
        if (socketRef.current?.pingInterval) {
          clearInterval(socketRef.current.pingInterval);
        }
        
        // Attempt to reconnect if enabled
        if (autoReconnect && reconnectAttemptsRef.current < maxReconnectAttempts) {
          console.log(`Reconnecting in ${reconnectInterval}ms (attempt ${reconnectAttemptsRef.current + 1}/${maxReconnectAttempts})...`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttemptsRef.current += 1;
            connect();
          }, reconnectInterval);
        } else if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
          setError('Maximum reconnection attempts reached');
        }
      });
      
      // Connection error
      socket.addEventListener('error', (event) => {
        console.error('WebSocket error:', event);
        setError('WebSocket connection error');
      });
      
    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      setError('Failed to create WebSocket connection');
    }
  }, [session, autoReconnect, reconnectInterval, maxReconnectAttempts]);
  
  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.close();
      socketRef.current = null;
    }
    
    // Clear reconnect timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    setIsConnected(false);
  }, []);
  
  // Mark notification as read
  const markAsRead = useCallback((notificationId) => {
    if (socketRef.current?.readyState === WebSocket.OPEN) {
      socketRef.current.send(JSON.stringify({
        type: 'MARK_READ',
        notificationId
      }));
    }
  }, []);
  
  // Mark all notifications as read
  const markAllAsRead = useCallback(() => {
    if (socketRef.current?.readyState === WebSocket.OPEN) {
      socketRef.current.send(JSON.stringify({
        type: 'MARK_ALL_READ'
      }));
    }
  }, []);
  
  // Fetch notifications
  const fetchNotifications = useCallback((options = {}) => {
    if (socketRef.current?.readyState === WebSocket.OPEN) {
      socketRef.current.send(JSON.stringify({
        type: 'GET_NOTIFICATIONS',
        ...options
      }));
    }
  }, []);
  
  // Connect when session is available
  useEffect(() => {
    if (session?.user?.token) {
      connect();
    }
    
    return () => {
      disconnect();
    };
  }, [session, connect, disconnect]);
  
  return {
    isConnected,
    notifications,
    unreadCount,
    error,
    markAsRead,
    markAllAsRead,
    fetchNotifications,
    connect,
    disconnect
  };
}
