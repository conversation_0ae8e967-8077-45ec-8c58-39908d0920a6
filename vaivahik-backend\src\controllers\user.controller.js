// src/controllers/user.controller.js

// Required Modules
const jwt = require('jsonwebtoken');
const fs = require('fs/promises'); // Use promises version of fs for async file operations
const path = require('path');
const sharp = require('sharp'); // For image processing
const bcrypt = require('bcryptjs'); // For password hashing (used in registerWithPassword)

// OTP Service (adjust path if needed)
const { generateOTP, setOTP, verifyOTP, deleteOTP, getOTP } = require('../../redis/otpService');

// Redis Cache Service
const { getOrSetCache, invalidateUserCache, CACHE_PREFIXES } = require('../../redis/cacheService');

// SMS Service for OTP
const msg91Service = require('../services/sms/msg91.service');

// Enhanced OTP Service
const { sendOtpEnhanced, verifyOtpEnhanced, resendOtpEnhanced, getOtpStatus } = require('../services/otp/enhancedOtpService');

// Photo Moderation Service
const photoModerationService = require('../services/photoModeration.service');

// Field Locking Utilities
const { getCriticalFieldStatus } = require('../utils/fieldLocking');

// Constants
const PROFILE_PHOTO_DIR = path.join(__dirname, '../../uploads/profile_photos');
const MAX_PHOTOS_PER_USER = 3;

// --- OTP & Sign In/Up Functions ---

/**
 * @description Request an OTP for phone number verification/signup.
 * @route POST /api/users/request-otp
 */
exports.sendOtp = async (req, res, next) => {
  // Access the shared Prisma client instance from the request object
  const prisma = req.prisma;
  const { phone } = req.body;

  // Validate phone number
  if (!phone) {
    const error = new Error("Phone number is required.");
    error.status = 400;
    return next(error);
  }

  if (!/^\+?[1-9]\d{1,14}$/.test(phone)) {
    const error = new Error("Invalid phone number format.");
    error.status = 400;
    return next(error);
  }

  try {
    // Use enhanced OTP service with fallback mechanisms
    const result = await sendOtpEnhanced(phone);

    if (result.success) {
      // Log success with provider information
      console.log(`✅ OTP sent successfully to ${phone} via ${result.provider}`);

      // Return appropriate response
      const response = {
        success: true,
        message: result.message,
        phone: result.phone,
        expiresIn: result.expirySeconds
      };

      // Include warning if using fallback
      if (result.warning) {
        response.warning = result.warning;
      }

      // In development, include OTP for testing
      if (process.env.NODE_ENV === 'development' && result.otp) {
        response.otp = result.otp;
        console.log(`🔐 Development OTP for ${phone}: ${result.otp}`);
      }

      return res.status(200).json(response);
    } else {
      // Handle different error types appropriately
      let statusCode = 500;

      if (result.errorType === 'RATE_LIMITED') {
        statusCode = 429; // Too Many Requests
      } else if (result.errorType === 'VALIDATION_ERROR') {
        statusCode = 400; // Bad Request
      }

      console.error(`❌ Failed to send OTP to ${phone}: ${result.message}`);

      const error = new Error(result.message);
      error.status = statusCode;
      error.details = {
        errorType: result.errorType,
        waitTime: result.waitTime
      };
      return next(error);
    }
  } catch (error) {
    console.error('Error in sendOtp:', error);
    const err = new Error('Failed to send OTP');
    err.status = 500;
    return next(err);
  }
};

/**
 * @description Verify OTP and Sign Up/Sign In the user, creating Profile record for new users.
 * @route POST /api/users/verify-otp
 */
exports.verifyOtpAndSignIn = async (req, res, next) => {
  const { phone, otp } = req.body;
  const prisma = req.prisma;

  // Validate inputs
  if (!phone || !otp) {
    const error = new Error("Phone number and OTP are required.");
    error.status = 400;
    return next(error);
  }

  try {
    // Use enhanced OTP verification service
    const verificationResult = await verifyOtpEnhanced(phone, otp);

    if (!verificationResult.success) {
      console.error(`❌ OTP verification failed for ${phone}: ${verificationResult.message}`);

      let statusCode = 401;
      if (verificationResult.errorType === 'INVALID_OTP') {
        statusCode = 401; // Unauthorized
      } else if (verificationResult.errorType === 'EXPIRED_OTP') {
        statusCode = 410; // Gone
      }

      const error = new Error(verificationResult.message);
      error.status = statusCode;
      return next(error);
    }

    console.log(`✅ OTP verified successfully for ${phone} via ${verificationResult.provider}`);
    const formattedPhone = verificationResult.phone;

    // Find user by phone number
    let user = await prisma.user.findUnique({
      where: { phone: formattedPhone },
      include: {
        profile: {
          select: {
            id: true
          }
        }
      }
    });

    let isNewUser = false;

    // If user doesn't exist, create a new user
    if (!user) {
      user = await prisma.user.create({
        data: {
          phone: formattedPhone,
          isVerified: true,
          profileStatus: "INCOMPLETE",
          profile: { create: {} }
        },
        include: {
          profile: {
            select: {
              id: true
            }
          }
        }
      });
      isNewUser = true;
      console.log(`New user and profile created with phone: ${formattedPhone}, User ID: ${user.id}`);
    }
    // If user exists but is not verified, update verification status
    else if (!user.isVerified) {
      user = await prisma.user.update({
        where: { phone: formattedPhone },
        data: { isVerified: true },
        include: {
          profile: {
            select: {
              id: true
            }
          }
        }
      });
      console.log(`Existing user verified: ${formattedPhone}, ID: ${user.id}`);
    }
    // User exists and is already verified
    else {
      console.log(`Existing verified user signed in: ${formattedPhone}, ID: ${user.id}`);
    }

    // Delete OTP from Redis after successful verification
    await deleteOTP(formattedPhone);

    // Validate JWT secrets
    if (!process.env.JWT_SECRET || !process.env.JWT_REFRESH_SECRET) {
      console.error("JWT secrets are not defined in .env file!");
      const error = new Error("Server configuration error: JWT secrets missing.");
      error.status = 500;
      return next(error);
    }

    // Generate access token
    const accessTokenPayload = {
      userId: user.id,
      phone: user.phone,
      profileId: user.profile?.id
    };
    const accessToken = jwt.sign(
      accessTokenPayload,
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRY || '1h' }
    );

    // Generate refresh token
    const refreshTokenPayload = { userId: user.id };
    const refreshToken = jwt.sign(
      refreshTokenPayload,
      process.env.JWT_REFRESH_SECRET,
      { expiresIn: process.env.REFRESH_TOKEN_EXPIRY || '7d' }
    );

    // Set refresh token as HTTP-only cookie
    res.cookie('refreshToken', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
    });

    // Return success response
    return res.status(200).json({
      success: true,
      message: isNewUser ? "OTP verified. Welcome!" : "OTP verified. Welcome back!",
      userId: user.id,
      accessToken: accessToken,
      isNewUser: isNewUser,
      profileStatus: user.profileStatus
    });
  } catch (error) {
    console.error("Error in verifyOtpAndSignIn:", error);
    next(error);
  }
};

/**
 * @description Resend OTP for phone verification
 * @route POST /api/users/resend-otp
 */
exports.resendOtp = async (req, res, next) => {
  const prisma = req.prisma;
  const { phone, retryType = 'text' } = req.body;

  // Validate phone number
  if (!phone) {
    const error = new Error("Phone number is required.");
    error.status = 400;
    return next(error);
  }

  if (!/^\+?[1-9]\d{1,14}$/.test(phone)) {
    const error = new Error("Invalid phone number format.");
    error.status = 400;
    return next(error);
  }

  // Validate retry type
  if (retryType !== 'text' && retryType !== 'voice') {
    const error = new Error("Invalid retry type. Must be 'text' or 'voice'.");
    error.status = 400;
    return next(error);
  }

  try {
    // Use enhanced OTP resend service
    const result = await resendOtpEnhanced(phone, retryType);

    if (result.success) {
      console.log(`✅ OTP resent successfully to ${phone} via ${result.provider} (${retryType})`);

      const response = {
        success: true,
        message: result.message,
        phone: result.phone,
        retryType: result.retryType || retryType,
        expiresIn: result.expirySeconds
      };

      // Include warning if using fallback
      if (result.warning) {
        response.warning = result.warning;
      }

      // In development, include OTP for testing
      if (process.env.NODE_ENV === 'development' && result.otp) {
        response.otp = result.otp;
        console.log(`🔐 Development OTP for ${phone}: ${result.otp}`);
      }

      return res.status(200).json(response);
    } else {
      // Handle different error types appropriately
      let statusCode = 500;

      if (result.errorType === 'RESEND_COOLDOWN') {
        statusCode = 429; // Too Many Requests
      } else if (result.errorType === 'RATE_LIMITED') {
        statusCode = 429; // Too Many Requests
      } else if (result.errorType === 'VALIDATION_ERROR') {
        statusCode = 400; // Bad Request
      }

      console.error(`❌ Failed to resend OTP to ${phone}: ${result.message}`);

      const error = new Error(result.message);
      error.status = statusCode;
      error.details = {
        errorType: result.errorType,
        waitTime: result.waitTime
      };
      return next(error);
    }
  } catch (error) {
    console.error('Error in resendOtp:', error);
    const err = new Error('Failed to resend OTP');
    err.status = 500;
    return next(err);
  }
};

/**
 * @description Register a new user with email, password, and profile details.
 * @route POST /api/users/register
 */
exports.registerWithPassword = async (req, res, next) => {
    const prisma = req.prisma;
    const { email, password, phone, fullName, gender, dateOfBirth, birthTime, birthPlace, incomeRange, height, city, education, occupation, nativePlace, fatherName, motherName, uncleName, totalSiblings, marriedSiblings, familyContact, latitude, longitude // Added lat/lng
    } = req.body;

    // --- Input Validation (Essential) ---
    if (!email || !password || !phone) { const error = new Error("Email, password, and phone number are required for registration."); error.status = 400; return next(error); }
    if (!/\S+@\S+\.\S+/.test(email)) { const error = new Error('Invalid email format provided.'); error.status = 400; return next(error); }
    if (password.length < 6) { const error = new Error('Password must be at least 6 characters long.'); error.status = 400; return next(error); }
    if (!/^\+?[1-9]\d{1,14}$/.test(phone)) { const error = new Error("Invalid phone number format."); error.status = 400; return next(error); }
    if ((latitude !== undefined && typeof latitude !== 'number') || (longitude !== undefined && typeof longitude !== 'number')) {
         const error = new Error("Latitude and Longitude must be numbers."); error.status = 400; return next(error);
    }
    // ------------------------------------

    try {
        const existingUser = await prisma.user.findFirst({ where: { OR: [{ email: email }, { phone: phone }] } });
        if (existingUser) { const field = existingUser.email === email ? 'Email' : 'Phone number'; const error = new Error(`${field} is already associated with an account.`); error.status = 409; return next(error); }

        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(password, saltRounds);

        const profileData = {};
        if (fullName !== undefined) profileData.fullName = fullName;
        if (gender !== undefined) profileData.gender = gender;
        if (dateOfBirth) profileData.dateOfBirth = new Date(dateOfBirth); // Validate format before this
        if (birthTime !== undefined) profileData.birthTime = birthTime;
        if (birthPlace !== undefined) profileData.birthPlace = birthPlace;
        if (height !== undefined) profileData.height = height;
        if (city !== undefined) profileData.city = city;
        if (nativePlace !== undefined) profileData.nativePlace = nativePlace;
        if (education !== undefined) profileData.education = education;
        if (occupation !== undefined) profileData.occupation = occupation;
        if (incomeRange !== undefined) profileData.incomeRange = incomeRange;
        if (fatherName !== undefined) profileData.fatherName = fatherName;
        if (motherName !== undefined) profileData.motherName = motherName;
        if (uncleName !== undefined) profileData.uncleName = uncleName;
        if (totalSiblings !== undefined) profileData.totalSiblings = parseInt(totalSiblings) || null;
        if (marriedSiblings !== undefined) profileData.marriedSiblings = parseInt(marriedSiblings) || null;
        if (familyContact !== undefined) profileData.familyContact = familyContact;
        // Add lat/lng if provided
        if (latitude !== undefined) profileData.latitude = latitude;
        if (longitude !== undefined) profileData.longitude = longitude;


        const newUser = await prisma.user.create({
            data: {
                email: email, phone: phone, password: hashedPassword, isVerified: false, // Start as unverified
                profileStatus: Object.keys(profileData).length > 0 ? "PENDING_APPROVAL" : "INCOMPLETE",
                profile: { create: profileData },
            },
        });
        console.log(`New user registered via password: ${email}, ID: ${newUser.id}`);

        if (!process.env.JWT_SECRET || !process.env.JWT_REFRESH_SECRET) { console.error("JWT secrets are not defined!"); throw new Error("Server configuration error."); }
        const accessTokenPayload = { userId: newUser.id, phone: newUser.phone, email: newUser.email };
        const accessToken = jwt.sign(accessTokenPayload, process.env.JWT_SECRET, { expiresIn: '1h' });
        const refreshTokenPayload = { userId: newUser.id };
        const refreshToken = jwt.sign(refreshTokenPayload, process.env.JWT_REFRESH_SECRET, { expiresIn: '7d' });
        res.cookie('refreshToken', refreshToken, { httpOnly: true, secure: process.env.NODE_ENV === 'production', sameSite: 'strict', maxAge: 7 * 24 * 60 * 60 * 1000 });
        res.status(201).json({ message: 'Registration successful. Please verify your account.', userId: newUser.id, accessToken: accessToken });
    } catch (error) { console.error("Error during registration:", error); next(error); }
};


/**
 * @description Get the profile information for the currently authenticated user.
 * @route GET /api/users/profile
 */
exports.getUserProfile = async (req, res, next) => {
    const prisma = req.prisma; const userId = req.user?.userId;
    if (!userId) { const error = new Error('Authentication error: User ID missing from token.'); error.status = 401; return next(error); }
    try {
        // Use cache for user profile
        const cacheKey = `${CACHE_PREFIXES.PROFILE}${userId}`;

        const userWithProfileAndPhotos = await getOrSetCache(
            cacheKey,
            async () => {
                const userData = await prisma.user.findUnique({
                    where: { id: userId },
                    select: {
                        id: true, phone: true, email: true, isVerified: true, profileStatus: true,
                        isPremium: true, createdAt: true, updatedAt: true,
                        profile: {
                            select: {
                                id: true, fullName: true, gender: true, dateOfBirth: true, birthTime: true,
                                birthPlace: true, height: true, city: true, education: true, occupation: true,
                                incomeRange: true, fatherName: true, motherName: true, uncleName: true, nativePlace: true,
                                totalSiblings: true, marriedSiblings: true, familyContact: true,
                                latitude: true, longitude: true, createdAt: true, updatedAt: true
                            }
                        },
                        photos: {
                            select: { id: true, url: true, visibility: true, isProfilePic: true, uploadedAt: true, status: true }, // Include photo status
                            orderBy: { isProfilePic: 'desc', }
                        }
                    }
                });

                if (!userData) {
                    const error = new Error("User profile not found.");
                    error.status = 404;
                    throw error;
                }

                if (!userData.profile) {
                    userData.profile = null;
                }

                return userData;
            },
            3600 // Cache for 1 hour
        );

        res.status(200).json(userWithProfileAndPhotos);
    } catch (error) {
        console.error("Error fetching user profile:", error);
        next(error);
    }
};

/**
 * @description Refresh the Access Token using the Refresh Token from the cookie.
 * @route POST /api/users/refresh-token
 */
exports.refreshToken = async (req, res, next) => {
    const prisma = req.prisma; const refreshToken = req.cookies?.refreshToken;
    if (!refreshToken) { const error = new Error("Access denied. No refresh token provided."); error.status = 401; return next(error); }
    try {
        if (!process.env.JWT_REFRESH_SECRET) { console.error("JWT_REFRESH_SECRET is not defined!"); const error = new Error("Server configuration error."); error.status = 500; return next(error); }
        const decodedPayload = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);
        const userId = decodedPayload.userId;
        const user = await prisma.user.findUnique({ where: { id: userId }, select: { id: true, phone: true } });
        if (!user) { const error = new Error("User not found."); error.status = 401; res.clearCookie('refreshToken', { httpOnly: true, secure: process.env.NODE_ENV === 'production', sameSite: 'strict' }); return next(error); }
        const newAccessTokenPayload = { userId: user.id, phone: user.phone };
        const newAccessToken = jwt.sign(newAccessTokenPayload, process.env.JWT_SECRET, { expiresIn: '1h' });
        res.status(200).json({ accessToken: newAccessToken });
    } catch (err) { console.error("Error verifying refresh token:", err); const error = new Error("Invalid or expired refresh token."); error.status = 403; res.clearCookie('refreshToken', { httpOnly: true, secure: process.env.NODE_ENV === 'production', sameSite: 'strict' }); return next(error); }
};


/**
 * @description Update detailed profile information, requiring at least one photo before status changes from INCOMPLETE.
 * @route PUT /api/users/profile
 */
exports.updateUserProfile = async (req, res, next) => {
    const prisma = req.prisma; const userId = req.user?.userId;
    const { email, fullName, gender, dateOfBirth, birthTime, birthPlace, height, city, nativePlace, education, occupation, incomeRange, fatherName, motherName, uncleName, totalSiblings, marriedSiblings, familyContact, latitude, longitude } = req.body;
    if (!userId) { const error = new Error('Authentication error: User ID missing.'); error.status = 401; return next(error); }

    // --- Input Validation ---
    if (email && typeof email === 'string' && !/\S+@\S+\.\S+/.test(email)) { const error = new Error('Invalid email format provided.'); error.status = 400; return next(error); }
    if (dateOfBirth && isNaN(Date.parse(dateOfBirth))) { const error = new Error('Invalid dateOfBirth format. Use ISO 8601 format.'); error.status = 400; return next(error); }
    const siblingFields = { totalSiblings, marriedSiblings };
    for (const field in siblingFields) { if (req.body[field] !== undefined && req.body[field] !== null && isNaN(parseInt(req.body[field]))) { const error = new Error(`Invalid number format for ${field}.`); error.status = 400; return next(error); } }
    if ((latitude !== undefined && typeof latitude !== 'number') || (longitude !== undefined && typeof longitude !== 'number')) { const error = new Error("Latitude and Longitude must be numbers."); error.status = 400; return next(error); }
    if (latitude !== undefined && (latitude < -90 || latitude > 90)) { const error = new Error("Latitude must be between -90 and 90."); error.status = 400; return next(error); }
    if (longitude !== undefined && (longitude < -180 || longitude > 180)) { const error = new Error("Longitude must be between -180 and 180."); error.status = 400; return next(error); }
    // -----------------------------

    try {
        // Prepare update data objects
        const profileDataToUpdate = {}; const userDataToUpdate = {};
        if (email !== undefined) userDataToUpdate.email = email;
        if (fullName !== undefined) profileDataToUpdate.fullName = fullName;
        if (gender !== undefined) profileDataToUpdate.gender = gender;
        if (dateOfBirth !== undefined) profileDataToUpdate.dateOfBirth = new Date(dateOfBirth);
        if (birthTime !== undefined) profileDataToUpdate.birthTime = birthTime;
        if (birthPlace !== undefined) profileDataToUpdate.birthPlace = birthPlace;
        if (height !== undefined) profileDataToUpdate.height = height;
        if (city !== undefined) profileDataToUpdate.city = city;
        if (nativePlace !== undefined) profileDataToUpdate.nativePlace = nativePlace;
        if (education !== undefined) profileDataToUpdate.education = education;
        if (occupation !== undefined) profileDataToUpdate.occupation = occupation;
        if (incomeRange !== undefined) profileDataToUpdate.incomeRange = incomeRange;
        if (fatherName !== undefined) profileDataToUpdate.fatherName = fatherName;
        if (motherName !== undefined) profileDataToUpdate.motherName = motherName;
        if (uncleName !== undefined) profileDataToUpdate.uncleName = uncleName;
        if (totalSiblings !== undefined) profileDataToUpdate.totalSiblings = parseInt(totalSiblings) || null;
        if (marriedSiblings !== undefined) profileDataToUpdate.marriedSiblings = parseInt(marriedSiblings) || null;
        if (familyContact !== undefined) profileDataToUpdate.familyContact = familyContact;
        if (latitude !== undefined) profileDataToUpdate.latitude = latitude;
        if (longitude !== undefined) profileDataToUpdate.longitude = longitude;

        let profileUpdateMessage = ""; // Optional message for frontend

        // Determine if profileStatus needs update
        if (Object.keys(profileDataToUpdate).length > 0) {
            const currentUser = await prisma.user.findUnique({ where: { id: userId }, select: { profileStatus: true } });
            if (currentUser?.profileStatus === "INCOMPLETE") {
                // *** ADDED: Check for photo before changing status ***
                const photoCount = await prisma.photo.count({ where: { userId: userId } });
                if (photoCount > 0) {
                    // AUTO-APPROVE: Skip manual approval for faster onboarding
                    userDataToUpdate.profileStatus = "ACTIVE";
                    userDataToUpdate.isVerified = true;
                    console.log(`Profile status for user ${userId} auto-approved to ACTIVE.`);
                } else {
                    // Profile details updated, but photos missing. Keep status INCOMPLETE.
                    console.log(`Profile details updated for user ${userId}, but status remains INCOMPLETE (photos required).`);
                    profileUpdateMessage = "Profile details saved. Please upload at least one photo to complete your profile.";
                }
            }
        }

        // Check if there's actually anything to update across User or Profile
        if (Object.keys(userDataToUpdate).length === 0 && Object.keys(profileDataToUpdate).length === 0) {
            // If nothing to update, just return current profile data
            const currentUserProfile = await prisma.user.findUnique({ where: { id: userId }, select: { /* ... full select from getUserProfile ... */ } });
            return res.status(200).json(currentUserProfile || {});
        }

        // --- Perform Update using a nested write ---
        const updatedUser = await prisma.user.update({
            where: { id: userId },
            data: { ...userDataToUpdate, profile: { update: { ...profileDataToUpdate } } },
            select: { /* Replicate select from getUserProfile, ensure lat/lng included */
                id: true, phone: true, email: true, isVerified: true, profileStatus: true,
                isPremium: true, createdAt: true, updatedAt: true,
                profile: {
                    select: {
                        id: true, fullName: true, gender: true, dateOfBirth: true, birthTime: true,
                        birthPlace: true, height: true, city: true, education: true, occupation: true,
                        incomeRange: true, fatherName: true, motherName: true, uncleName: true, nativePlace: true,
                        totalSiblings: true, marriedSiblings: true, familyContact: true,
                        latitude: true, longitude: true, createdAt: true, updatedAt: true
                    }
                },
                 photos: {
                    select: { id: true, url: true, visibility: true, isProfilePic: true, uploadedAt: true, status: true },
                    orderBy: { isProfilePic: 'desc', }
                }
             }
        });

        console.log(`User profile updated for ID: ${userId}`);

        // Invalidate user cache after update
        await invalidateUserCache(userId);

        // Include the optional message if photos are still needed
        res.status(200).json({
             ...(profileUpdateMessage && { notice: profileUpdateMessage }), // Add notice if applicable
             ...updatedUser // Spread the updated user data
        });

    } catch (error) {
         if (error.code === 'P2002' && error.meta?.target?.includes('email')) { const err = new Error("Email address is already in use."); err.status = 409; return next(err); }
         if (error.code === 'P2025') { const err = new Error("Profile record not found for update."); err.status = 404; return next(err); }
         console.error("Error updating user profile:", error); next(error);
    }
};

// --- logout function ---
exports.logout = (req, res, next) => {
    const userId = req.user?.userId;
    try {
        res.clearCookie('refreshToken', { httpOnly: true, secure: process.env.NODE_ENV === 'production', sameSite: 'strict' });
        console.log(`User logged out: ${userId || 'Unknown user'}`);
        res.status(204).send();
    } catch (error) { console.error(`Error during logout for user ${userId}:`, error); next(error); }
};


// --- handleProfilePhotoUpload function ---
exports.handleProfilePhotoUpload = async (req, res, next) => {
    const prisma = req.prisma; const userId = req.user?.userId;
    if (!userId) { const error = new Error('Authentication error: User ID missing.'); error.status = 401; return next(error); }
    if (!req.files || req.files.length === 0) { const error = new Error('No files were uploaded.'); error.status = 400; return next(error); }
    try {
        const currentPhotoCount = await prisma.photo.count({ where: { userId: userId } });
        const availableSlots = MAX_PHOTOS_PER_USER - currentPhotoCount;
        if (availableSlots <= 0) { const error = new Error(`Cannot upload more photos. Limit of ${MAX_PHOTOS_PER_USER} reached.`); error.status = 400; return next(error); }
        const filesToProcess = req.files.slice(0, availableSlots);
        const uploadedPhotosData = [];
        await fs.mkdir(PROFILE_PHOTO_DIR, { recursive: true }); // Ensure directory exists

        for (const file of filesToProcess) {
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const filename = `user-${userId}-${uniqueSuffix}.webp`;
            const outputPath = path.join(PROFILE_PHOTO_DIR, filename);
            const publicUrlPath = `/uploads/profile_photos/${filename}`; // Relative URL path

            // Process and save the image with Sharp
            await sharp(file.buffer)
                .resize({ width: 800, height: 800, fit: 'inside', withoutEnlargement: true })
                .webp({ quality: 80 })
                .toFile(outputPath);

            console.log(`Processed and saved photo: ${filename}`);

            // Prepare photo data with default values
            const photoData = {
                userId: userId,
                url: publicUrlPath,
                // status defaults to PENDING in schema
            };

            try {
                // Process the photo through AI moderation (non-blocking)
                // This won't affect the upload flow but will prepare for later moderation
                photoModerationService.processPhoto(outputPath)
                    .then(moderationResult => {
                        // Log the result for debugging
                        console.log(`AI moderation for ${filename}: ${moderationResult.decision} (${moderationResult.confidence}%)`);

                        // We'll update the photo record with AI results after creation
                        // This happens asynchronously and doesn't block the upload
                    })
                    .catch(err => {
                        console.error(`Error in AI moderation for ${filename}:`, err);
                        // Continue with upload even if moderation fails
                    });
            } catch (moderationError) {
                // Log but don't fail the upload if moderation service has an error
                console.error(`Error calling moderation service for ${filename}:`, moderationError);
            }

            // Add to upload data array
            uploadedPhotosData.push(photoData);
        }

        // Create all photos in the database
        const newPhotos = await prisma.photo.createMany({
            data: uploadedPhotosData,
            skipDuplicates: true
        });

        // Set first photo as profile pic if this is the user's first upload
        if (currentPhotoCount === 0 && newPhotos.count > 0) {
            const firstPhoto = await prisma.photo.findFirst({
                where: { userId: userId },
                orderBy: { uploadedAt: 'asc'}
            });

            if(firstPhoto) {
                await prisma.photo.update({
                    where: { id: firstPhoto.id },
                    data: { isProfilePic: true }
                });
                console.log(`Set first uploaded photo ${firstPhoto.id} as primary.`);
            }
        }

        console.log(`${newPhotos.count} photo(s) saved to database for user ID: ${userId}`);

        // Get the created photos to return to the client
        const createdPhotos = await prisma.photo.findMany({
            where: {
                userId: userId,
                url: { in: uploadedPhotosData.map(p => p.url) }
            },
            select: {
                id: true,
                url: true,
                visibility: true,
                isProfilePic: true,
                uploadedAt: true,
                status: true,
                aiFlags: true,
                aiConfidence: true
            }
        });

        // Process each photo through moderation service and update database
        // This happens after we've already responded to the user
        for (const photo of createdPhotos) {
            const photoPath = path.join(process.cwd(), photo.url);

            // Use a self-executing async function to handle each photo
            (async (photo, photoPath) => {
                try {
                    // Process the photo through AI moderation
                    const moderationResult = await photoModerationService.processPhoto(photoPath, photo.id);

                    // Update the photo record with AI results
                    await prisma.photo.update({
                        where: { id: photo.id },
                        data: {
                            aiFlags: moderationResult.flags.join(','),
                            aiConfidence: moderationResult.confidence,
                            // Only update status if AI is configured to auto-approve/reject
                            ...(moderationResult.decision !== 'PENDING' && { status: moderationResult.decision })
                        }
                    });

                    // Log the moderation result for analytics
                    await photoModerationService.logModerationResult(photo.id, moderationResult);

                    console.log(`Updated photo ${photo.id} with AI moderation results: ${moderationResult.decision}`);
                } catch (err) {
                    console.error(`Error in post-upload moderation for photo ${photo.id}:`, err);
                }
            })(photo, photoPath);
        }

        // Return success response to user
        res.status(201).json({
            message: `${newPhotos.count} photo(s) uploaded successfully.`,
            photos: createdPhotos
        });
    } catch (error) {
        console.error("Error processing or saving photos:", error);
        next(error);
    }
};

/**
 * @description Update family details for the authenticated user
 * @route PUT /api/users/family-details
 */
exports.updateFamilyDetails = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Extract family details from request body
        const {
            familyType,
            familyStatus,
            fatherName,
            fatherOccupation,
            motherName,
            motherOccupation,
            uncleName,
            siblings,
            familyLocation,
            aboutFamily,
            brothers,
            marriedBrothers,
            sisters,
            marriedSisters,
            gotra
        } = req.body;

        // Check if user exists
        const user = await prisma.user.findUnique({
            where: { id: userId },
            include: { profile: true }
        });

        if (!user) {
            const error = new Error('User not found.');
            error.status = 404;
            return next(error);
        }

        // Prepare data for update
        const familyDetailsToUpdate = {
            familyType,
            familyStatus,
            fatherName,
            fatherOccupation,
            motherName,
            motherOccupation,
            uncleName,
            siblings: JSON.stringify({
                brothers: brothers || 0,
                marriedBrothers: marriedBrothers || 0,
                sisters: sisters || 0,
                marriedSisters: marriedSisters || 0
            }),
            familyContact: familyLocation, // Map to existing field
            gotra // Add if schema supports it
        };

        // Filter out undefined values
        Object.keys(familyDetailsToUpdate).forEach(key =>
            familyDetailsToUpdate[key] === undefined && delete familyDetailsToUpdate[key]
        );

        // Update profile with family details
        const updatedProfile = await prisma.profile.update({
            where: { userId },
            data: familyDetailsToUpdate
        });

        // Check if profile is more complete and update status if needed
        if (user.profileStatus === 'INCOMPLETE') {
            // Check if basic profile fields are filled
            const hasBasicInfo = user.profile &&
                user.profile.fullName &&
                user.profile.gender &&
                user.profile.dateOfBirth;

            // Check if user has at least one photo
            const hasPhoto = await prisma.photo.findFirst({
                where: { userId, status: 'APPROVED' }
            });

            if (hasBasicInfo && hasPhoto) {
                await prisma.user.update({
                    where: { id: userId },
                    data: { profileStatus: 'PENDING_APPROVAL' }
                });
            }
        }

        res.status(200).json({
            success: true,
            message: 'Family details updated successfully',
            data: {
                familyDetails: updatedProfile
            }
        });
    } catch (error) {
        console.error('Error updating family details:', error);
        next(error);
    }
};

/**
 * @description Update partner preferences for the authenticated user
 * @route PUT /api/users/partner-preferences
 */
exports.updatePartnerPreferences = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Extract partner preferences from request body
        const {
            ageRange,
            heightRange,
            maritalStatus,
            education,
            occupation,
            incomeRange,
            location,
            diet,
            subCaste,
            manglik,
            aboutPartner
        } = req.body;

        // Check if user exists
        const user = await prisma.user.findUnique({
            where: { id: userId }
        });

        if (!user) {
            const error = new Error('User not found.');
            error.status = 404;
            return next(error);
        }

        // Check if preference exists
        const existingPreference = await prisma.preference.findUnique({
            where: { userId }
        });

        // Prepare data for update or create
        const preferenceData = {
            ageMin: ageRange ? ageRange[0] : undefined,
            ageMax: ageRange ? ageRange[1] : undefined,
            heightMin: heightRange ? String(heightRange[0]) : undefined,
            heightMax: heightRange ? String(heightRange[1]) : undefined,
            educationLevel: education || [],
            occupations: occupation || [],
            incomeMin: incomeRange && incomeRange.length > 0 ? incomeRange[0] : undefined,
            preferredCities: location || [],
            acceptSubCastes: subCaste || [],
            dietPreference: diet,
            gotraPreference: manglik,
            otherPreferences: aboutPartner
        };

        // Filter out undefined values
        Object.keys(preferenceData).forEach(key =>
            preferenceData[key] === undefined && delete preferenceData[key]
        );

        let updatedPreference;

        if (existingPreference) {
            // Update existing preference
            updatedPreference = await prisma.preference.update({
                where: { userId },
                data: preferenceData
            });
        } else {
            // Create new preference
            updatedPreference = await prisma.preference.create({
                data: {
                    ...preferenceData,
                    user: { connect: { id: userId } }
                }
            });
        }

        // Also update partnerPreferences field in Profile if it exists
        try {
            await prisma.profile.update({
                where: { userId },
                data: {
                    partnerPreferences: JSON.stringify(req.body)
                }
            });
        } catch (profileError) {
            console.warn('Could not update profile.partnerPreferences:', profileError);
            // Continue execution even if this fails
        }

        res.status(200).json({
            success: true,
            message: 'Partner preferences updated successfully',
            data: {
                preferences: updatedPreference
            }
        });
    } catch (error) {
        console.error('Error updating partner preferences:', error);
        next(error);
    }
};

/**
 * @description Update basic details for the authenticated user
 * @route PUT /api/users/basic-details
 */
exports.updateBasicDetails = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Extract basic details from request body
        const {
            fullName,
            gender,
            dateOfBirth,
            maritalStatus,
            height,
            religion,
            caste,
            subCaste,
            bloodGroup
        } = req.body;

        // Check if user exists
        const user = await prisma.user.findUnique({
            where: { id: userId },
            include: { profile: true }
        });

        if (!user) {
            const error = new Error('User not found.');
            error.status = 404;
            return next(error);
        }

        // Prepare data for update
        let basicDetailsToUpdate = {
            fullName,
            maritalStatus,
            height,
            religion,
            caste,
            subCaste,
            bloodGroup
        };

        // Only include gender and dateOfBirth if they're not already set
        // This is a fallback in case the middleware fails
        if (!user.profile.gender) {
            basicDetailsToUpdate.gender = gender;
        }

        if (!user.profile.dateOfBirth) {
            basicDetailsToUpdate.dateOfBirth = dateOfBirth ? new Date(dateOfBirth) : undefined;
        }

        // Filter out undefined values
        Object.keys(basicDetailsToUpdate).forEach(key =>
            basicDetailsToUpdate[key] === undefined && delete basicDetailsToUpdate[key]
        );

        // Update profile with basic details
        const updatedProfile = await prisma.profile.update({
            where: { userId },
            data: basicDetailsToUpdate
        });

        // Check if profile is more complete and update status if needed
        if (user.profileStatus === 'INCOMPLETE') {
            // Check if basic profile fields are filled
            const hasBasicInfo = updatedProfile &&
                updatedProfile.fullName &&
                updatedProfile.gender &&
                updatedProfile.dateOfBirth;

            // Check if user has at least one photo
            const hasPhoto = await prisma.photo.findFirst({
                where: { userId, status: 'APPROVED' }
            });

            if (hasBasicInfo && hasPhoto) {
                await prisma.user.update({
                    where: { id: userId },
                    data: {
                        profileStatus: 'ACTIVE',
                        isVerified: true
                    }
                });
                console.log(`User ${userId} auto-approved after completing basic profile.`);
            }
        }

        res.status(200).json({
            success: true,
            message: 'Basic details updated successfully',
            data: {
                basicDetails: updatedProfile
            }
        });
    } catch (error) {
        console.error('Error updating basic details:', error);
        next(error);
    }
};

/**
 * @description Update education and career details for the authenticated user
 * @route PUT /api/users/education-career
 */
exports.updateEducationCareer = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Extract education and career details from request body
        const {
            highestEducation,
            educationField,
            occupation,
            workingWith,
            annualIncome,
            workLocation
        } = req.body;

        // Check if user exists
        const user = await prisma.user.findUnique({
            where: { id: userId },
            include: { profile: true }
        });

        if (!user) {
            const error = new Error('User not found.');
            error.status = 404;
            return next(error);
        }

        // Prepare data for update
        const educationCareerToUpdate = {
            highestEducation,
            educationField,
            occupation,
            workingWith,
            annualIncome,
            workLocation
        };

        // Filter out undefined values
        Object.keys(educationCareerToUpdate).forEach(key =>
            educationCareerToUpdate[key] === undefined && delete educationCareerToUpdate[key]
        );

        // Update profile with education and career details
        const updatedProfile = await prisma.profile.update({
            where: { userId },
            data: educationCareerToUpdate
        });

        res.status(200).json({
            success: true,
            message: 'Education and career details updated successfully',
            data: {
                educationCareer: updatedProfile
            }
        });
    } catch (error) {
        console.error('Error updating education and career details:', error);
        next(error);
    }
};

/**
 * @description Update location details for the authenticated user
 * @route PUT /api/users/location-details
 */
exports.updateLocationDetails = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Extract location details from request body
        const {
            city,
            state,
            country,
            pincode,
            birthPlace,
            birthTime,
            currentLocation,
            permanentLocation
        } = req.body;

        // Check if user exists
        const user = await prisma.user.findUnique({
            where: { id: userId },
            include: { profile: true }
        });

        if (!user) {
            const error = new Error('User not found.');
            error.status = 404;
            return next(error);
        }

        // Prepare data for update
        let locationDetailsToUpdate = {
            city,
            state,
            country,
            pincode,
            currentLocation,
            permanentLocation
        };

        // Only include birthPlace and birthTime if they're not already set
        // This is a fallback in case the middleware fails
        if (!user.profile.birthPlace) {
            locationDetailsToUpdate.birthPlace = birthPlace;
        }

        if (!user.profile.birthTime) {
            locationDetailsToUpdate.birthTime = birthTime ? new Date(birthTime) : undefined;
        }

        // Filter out undefined values
        Object.keys(locationDetailsToUpdate).forEach(key =>
            locationDetailsToUpdate[key] === undefined && delete locationDetailsToUpdate[key]
        );

        // Update profile with location details
        const updatedProfile = await prisma.profile.update({
            where: { userId },
            data: locationDetailsToUpdate
        });

        res.status(200).json({
            success: true,
            message: 'Location details updated successfully',
            data: {
                locationDetails: updatedProfile
            }
        });
    } catch (error) {
        console.error('Error updating location details:', error);
        next(error);
    }
};

/**
 * @description Update lifestyle and habits for the authenticated user
 * @route PUT /api/users/lifestyle-habits
 */
exports.updateLifestyleHabits = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Extract lifestyle and habits from request body
        const {
            diet,
            smoking,
            drinking,
            hobbies,
            interests,
            languages,
            pets,
            lifestyle
        } = req.body;

        // Check if user exists
        const user = await prisma.user.findUnique({
            where: { id: userId },
            include: { profile: true }
        });

        if (!user) {
            const error = new Error('User not found.');
            error.status = 404;
            return next(error);
        }

        // Prepare data for update
        const lifestyleHabitsToUpdate = {
            diet,
            smoking,
            drinking,
            hobbies: Array.isArray(hobbies) ? hobbies.join(', ') : hobbies,
            interests: Array.isArray(interests) ? interests.join(', ') : interests,
            languages: Array.isArray(languages) ? languages.join(', ') : languages,
            pets,
            lifestyle: typeof lifestyle === 'object' ? JSON.stringify(lifestyle) : lifestyle
        };

        // Filter out undefined values
        Object.keys(lifestyleHabitsToUpdate).forEach(key =>
            lifestyleHabitsToUpdate[key] === undefined && delete lifestyleHabitsToUpdate[key]
        );

        // Update profile with lifestyle and habits
        const updatedProfile = await prisma.profile.update({
            where: { userId },
            data: lifestyleHabitsToUpdate
        });

        res.status(200).json({
            success: true,
            message: 'Lifestyle and habits updated successfully',
            data: {
                lifestyleHabits: updatedProfile
            }
        });
    } catch (error) {
        console.error('Error updating lifestyle and habits:', error);
        next(error);
    }
};

/**
 * @description Get critical field status for the authenticated user
 * @route GET /api/users/critical-fields
 */
exports.getCriticalFieldStatus = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Get user profile
        const user = await prisma.user.findUnique({
            where: { id: userId },
            include: { profile: true }
        });

        if (!user) {
            const error = new Error('User not found.');
            error.status = 404;
            return next(error);
        }

        // Get critical field status
        const fieldStatus = getCriticalFieldStatus(user.profile);

        // Return the status
        res.status(200).json({
            success: true,
            message: 'Critical field status retrieved successfully',
            data: {
                criticalFields: fieldStatus,
                lockedFields: Object.keys(fieldStatus).filter(field => fieldStatus[field])
            }
        });
    } catch (error) {
        console.error('Error getting critical field status:', error);
        next(error);
    }
};

/**
 * @description Update about me for the authenticated user
 * @route PUT /api/users/about-me
 */
exports.updateAboutMe = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Extract about me from request body
        const { aboutMe } = req.body;

        // Check if user exists
        const user = await prisma.user.findUnique({
            where: { id: userId },
            include: { profile: true }
        });

        if (!user) {
            const error = new Error('User not found.');
            error.status = 404;
            return next(error);
        }

        // Update profile with about me
        const updatedProfile = await prisma.profile.update({
            where: { userId },
            data: { aboutMe }
        });

        res.status(200).json({
            success: true,
            message: 'About me updated successfully',
            data: {
                aboutMe: updatedProfile.aboutMe
            }
        });
    } catch (error) {
        console.error('Error updating about me:', error);
        next(error);
    }
};

// --- Photo Management Functions ---
exports.setPrimaryPhoto = async (req, res, next) => {
    const prisma = req.prisma; const userId = req.user?.userId; const { photoId } = req.params;
    if (!userId) { const error = new Error('Authentication error: User ID missing.'); error.status = 401; return next(error); }
    if (!photoId) { const error = new Error('Photo ID parameter is required.'); error.status = 400; return next(error); }
    try {
        // Use a transaction to ensure atomicity
        const [, updatedPhoto] = await prisma.$transaction([
            prisma.photo.updateMany({ where: { userId: userId, isProfilePic: true, NOT: { id: photoId } }, data: { isProfilePic: false } }),
            prisma.photo.update({ where: { id: photoId, userId: userId }, data: { isProfilePic: true }, select: { id: true, url: true, isProfilePic: true } })
        ]);
        console.log(`Set photo ${photoId} as primary for user ${userId}`);
        res.status(200).json({ message: "Primary profile photo updated successfully.", primaryPhoto: updatedPhoto });
    } catch (error) {
        if (error.code === 'P2025') { const err = new Error("Photo not found or you do not own this photo."); err.status = 404; return next(err); }
        console.error("Error setting primary photo:", error); next(error);
    }
};
exports.updatePhotoVisibility = async (req, res, next) => {
    const prisma = req.prisma; const userId = req.user?.userId; const { photoId } = req.params; const { visibility } = req.body;
    if (!userId) { const error = new Error('Authentication error: User ID missing.'); error.status = 401; return next(error); }
    if (!photoId) { const error = new Error('Photo ID parameter is required.'); error.status = 400; return next(error); }
    if (!visibility || !['PUBLIC', 'PAID', 'CONNECTIONS_ONLY'].includes(visibility)) { const error = new Error('Invalid or missing visibility value. Must be PUBLIC, PAID, or CONNECTIONS_ONLY.'); error.status = 400; return next(error); }
    try {
        const updatedPhoto = await prisma.photo.update({ where: { id: photoId, userId: userId }, data: { visibility: visibility }, select: { id: true, url: true, visibility: true } });
        console.log(`Updated visibility for photo ${photoId} to ${visibility} for user ${userId}`);
        res.status(200).json({ message: "Photo visibility updated successfully.", photo: updatedPhoto });
    } catch (error) {
        if (error.code === 'P2025') { const err = new Error("Photo not found or you do not own this photo."); err.status = 404; return next(err); }
        console.error("Error updating photo visibility:", error); next(error);
    }
};
exports.deletePhoto = async (req, res, next) => {
    const prisma = req.prisma; const userId = req.user?.userId; const { photoId } = req.params;
    if (!userId) { const error = new Error('Authentication error: User ID missing.'); error.status = 401; return next(error); }
    if (!photoId) { const error = new Error('Photo ID parameter is required.'); error.status = 400; return next(error); }
    try {
        const photoToDelete = await prisma.photo.findUnique({ where: { id: photoId, userId: userId }, select: { url: true, isProfilePic: true } });
        if (!photoToDelete) { const error = new Error("Photo not found or you do not own this photo."); error.status = 404; return next(error); }
        if (photoToDelete.isProfilePic) {
            const photoCount = await prisma.photo.count({ where: { userId } });
             if (photoCount <= 1) { const error = new Error("Cannot delete the primary photo when it's the only one."); error.status = 400; return next(error); }
        }
        await prisma.photo.delete({ where: { id: photoId } });
        if (photoToDelete.url) {
            const filePath = path.join(PROFILE_PHOTO_DIR, path.basename(photoToDelete.url));
            try { await fs.unlink(filePath); console.log(`Deleted photo file: ${filePath}`); }
            catch (fileError) { console.error(`Error deleting photo file ${filePath}: ${fileError.message}`); }
        }
        console.log(`Deleted photo ${photoId} for user ${userId}`);
        res.status(204).send();
    } catch (error) { console.error("Error deleting photo:", error); next(error); }
};

// Add other controller functions here later

