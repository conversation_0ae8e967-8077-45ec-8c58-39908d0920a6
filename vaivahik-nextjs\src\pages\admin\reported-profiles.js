import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);

export default function ReportedProfiles() {
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentReport, setCurrentReport] = useState(null);
  const [showReportModal, setShowReportModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmAction, setConfirmAction] = useState('');
  const [selectedAction, setSelectedAction] = useState('');
  const [actionDetails, setActionDetails] = useState('');
  const [dismissReason, setDismissReason] = useState('');
  const [suspensionDays, setSuspensionDays] = useState(7);
  const [restrictedFeatures, setRestrictedFeatures] = useState([]);

  useEffect(() => {
    fetchReportedProfiles();
  }, [currentPage, searchQuery, selectedStatus, itemsPerPage]);

  // Generate mock reported profiles data
  const generateMockReportedProfiles = () => {
    return [
      {
        id: 1,
        reportedUser: {
          id: 101,
          name: 'Rahul Sharma',
          age: 28,
          location: 'Mumbai',
          registrationDate: '2023-01-15',
          photo: '/img/user-placeholder.svg',
          email: '<EMAIL>',
          phone: '+91 9876543210',
          occupation: 'Software Engineer',
          education: 'B.Tech in Computer Science',
          maritalStatus: 'Never Married'
        },
        reporter: {
          id: 201,
          name: 'Ananya Gupta',
          age: 27,
          location: 'Delhi',
          email: '<EMAIL>',
          phone: '+91 9876543220'
        },
        reason: 'Fake Profile',
        additionalInfo: 'This person is using someone else\'s photos',
        status: 'pending',
        reportDate: '2023-07-10',
        reportCount: 3,
        evidence: [
          { id: 1, type: 'Screenshot', url: '/img/document-placeholder.svg', uploadedAt: '2023-07-10' },
          { id: 2, type: 'Chat Evidence', url: '/img/document-placeholder.svg', uploadedAt: '2023-07-10' }
        ]
      },
      {
        id: 2,
        reportedUser: {
          id: 102,
          name: 'Priya Patel',
          age: 26,
          location: 'Pune',
          registrationDate: '2023-02-20',
          photo: '/img/user-placeholder.svg',
          email: '<EMAIL>',
          phone: '+91 9876543211',
          occupation: 'Doctor',
          education: 'MBBS',
          maritalStatus: 'Never Married'
        },
        reporter: {
          id: 202,
          name: 'Vikram Singh',
          age: 30,
          location: 'Mumbai',
          email: '<EMAIL>',
          phone: '+91 9876543221'
        },
        reason: 'Inappropriate Content',
        additionalInfo: 'Profile contains inappropriate photos',
        status: 'pending',
        reportDate: '2023-07-12',
        reportCount: 1
      },
      {
        id: 3,
        reportedUser: {
          id: 103,
          name: 'Amit Desai',
          age: 30,
          location: 'Bangalore',
          registrationDate: '2023-03-05',
          photo: '/img/user-placeholder.svg',
          email: '<EMAIL>',
          phone: '+91 9876543212',
          occupation: 'Business Analyst',
          education: 'MBA in Finance',
          maritalStatus: 'Never Married'
        },
        reporter: {
          id: 203,
          name: 'Neha Sharma',
          age: 28,
          location: 'Bangalore',
          email: '<EMAIL>',
          phone: '+91 9876543222'
        },
        reason: 'Harassment',
        additionalInfo: 'This person is sending inappropriate messages',
        status: 'resolved',
        reportDate: '2023-07-08',
        actionDate: '2023-07-09',
        actionTaken: 'User warned and content removed',
        actionBy: 'Admin User',
        reportCount: 2
      }
    ];
  };

  const fetchReportedProfiles = async () => {
    setLoading(true);
    try {
      // Construct the API URL with query parameters
      let apiUrl = `/api/admin/reported-profiles?page=${currentPage}&limit=${itemsPerPage}`;

      // Add optional query parameters if they exist
      if (selectedStatus !== 'all') apiUrl += `&status=${encodeURIComponent(selectedStatus)}`;
      if (searchQuery) apiUrl += `&search=${encodeURIComponent(searchQuery)}`;

      try {
        // Call the API endpoint
        const response = await fetch(apiUrl);

        if (!response.ok) {
          console.warn(`API returned status: ${response.status}. Using mock data instead.`);
          throw new Error(`Failed to fetch reported profiles: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
          // Check if the data has the expected structure
          if (data.reports) {
            setReports(data.reports);
            setTotalPages(data.pagination?.totalPages || 1);
          } else if (data.reportedProfiles) {
            // Handle alternative API response structure
            setReports(data.reportedProfiles);
            setTotalPages(data.pagination?.totalPages || 1);
          } else {
            console.warn('Unexpected API response structure:', data);
            // Fallback to mock data
            throw new Error('Unexpected API response structure');
          }
        } else {
          console.warn('API returned error:', data.message);
          throw new Error(data.message || 'API returned unsuccessful response');
        }
      } catch (error) {
        console.warn('Using mock data as fallback due to API error:', error.message);
        // Always use mock data in development or when API fails
        const mockData = generateMockReportedProfiles();
        setReports(mockData);
        setTotalPages(1);
      }

      setLoading(false);
    } catch (error) {
      console.error('Error in fetchReportedProfiles:', error);
      // Fallback to mock data if API fails
      console.log('Using mock data as fallback');
      const mockData = generateMockReportedProfiles();
      setReports(mockData);
      setTotalPages(1);
      setLoading(false);
    }
  };

  // View report details
  const handleViewReport = (report) => {
    setCurrentReport(report);
    setShowReportModal(true);
    document.body.classList.add('modal-open');
  };

  // Handle resolve report
  const handleResolveReport = (report) => {
    setCurrentReport(report);
    setConfirmAction('resolve');
    setShowConfirmModal(true);
    document.body.classList.add('modal-open');
  };

  // Handle dismiss report
  const handleDismissReport = (report) => {
    setCurrentReport(report);
    setConfirmAction('dismiss');
    setShowConfirmModal(true);
    document.body.classList.add('modal-open');
  };

  // Confirm report action
  const confirmReportAction = async () => {
    try {
      // Validate input for resolve action
      if (confirmAction === 'resolve') {
        if (!selectedAction) {
          alert('Please select an action to take.');
          return;
        }

        if (!actionDetails) {
          alert('Please provide details about the action being taken.');
          return;
        }

        if (selectedAction === 'RESTRICT_FEATURES' && restrictedFeatures.length === 0) {
          alert('Please select at least one feature to restrict.');
          return;
        }
      } else if (confirmAction === 'dismiss') {
        if (!dismissReason) {
          alert('Please provide a reason for dismissing this report.');
          return;
        }
      }

      // Prepare request body
      const requestBody = confirmAction === 'resolve'
        ? {
            actionType: selectedAction,
            actionDetails: actionDetails,
            suspensionDays: selectedAction === 'TEMP_SUSPENSION' ? suspensionDays : null,
            restrictedFeatures: selectedAction === 'RESTRICT_FEATURES' ? restrictedFeatures : null
          }
        : {
            dismissReason: dismissReason
          };

      // Call the API endpoint
      const response = await fetch(`/api/admin/reported-profiles/${currentReport.id}?action=${confirmAction}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`Failed to ${confirmAction} report: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Update local state with a more detailed action
        const updatedReports = reports.map(r => {
          if (r.id === currentReport.id) {
            let actionTakenText = '';

            if (confirmAction === 'resolve') {
              switch (selectedAction) {
                case 'WARNING':
                  actionTakenText = `Warning issued: ${actionDetails}`;
                  break;
                case 'CONTENT_REMOVAL':
                  actionTakenText = `Content removed: ${actionDetails}`;
                  break;
                case 'REVOKE_VERIFICATION':
                  actionTakenText = `Verification badge revoked: ${actionDetails}`;
                  break;
                case 'RESTRICT_FEATURES':
                  actionTakenText = `Features restricted (${restrictedFeatures.join(', ')}): ${actionDetails}`;
                  break;
                case 'TEMP_SUSPENSION':
                  actionTakenText = `Account suspended for ${suspensionDays} days: ${actionDetails}`;
                  break;
                case 'PERMANENT_BAN':
                  actionTakenText = `Account permanently banned: ${actionDetails}`;
                  break;
                default:
                  actionTakenText = actionDetails;
              }
            }

            return {
              ...r,
              status: confirmAction === 'resolve' ? 'resolved' : 'dismissed',
              actionDate: new Date().toISOString().split('T')[0],
              actionBy: 'Admin User',
              ...(confirmAction === 'resolve' ? {
                actionTaken: actionTakenText,
                actionType: selectedAction,
                // Store additional action details for potential future use
                actionDetails: {
                  selectedAction,
                  actionDetails,
                  suspensionDays: selectedAction === 'TEMP_SUSPENSION' ? suspensionDays : null,
                  restrictedFeatures: selectedAction === 'RESTRICT_FEATURES' ? restrictedFeatures : null
                }
              } : {})
            };
          }
          return r;
        });

        setReports(updatedReports);
        setShowConfirmModal(false);
        setSelectedAction('');
        setActionDetails('');
        setDismissReason('');
        setSuspensionDays(7);
        setRestrictedFeatures([]);
        document.body.classList.remove('modal-open');

        // Show success message with more details
        alert(data.message || (confirmAction === 'resolve'
          ? `Report resolved successfully. Action taken: ${selectedAction}`
          : 'Report dismissed successfully.'));
      } else {
        console.error('API returned error:', data.message);
        alert(data.message || `Failed to ${confirmAction} report. Please try again.`);
      }
    } catch (error) {
      console.error(`Error ${confirmAction}ing report:`, error);
      alert(`Failed to ${confirmAction} report. Please try again.`);
    }
  };

  // Close report modal
  const closeReportModal = () => {
    setShowReportModal(false);
    setCurrentReport(null);
    document.body.classList.remove('modal-open');
  };

  // Close confirmation modal
  const closeConfirmModal = () => {
    setShowConfirmModal(false);
    document.body.classList.remove('modal-open');
  };

  // View evidence/document in full size
  const viewEvidence = (url, title = 'Evidence') => {
    // Create a modal for evidence viewing
    const modal = document.createElement('div');
    modal.className = 'modal-overlay document-viewer-modal';
    modal.style.zIndex = '3000';

    // Use a fallback image if the URL is missing or invalid
    const evidenceUrl = url || '/img/document-placeholder.svg';

    // Determine if the evidence is a PDF
    const isPdf = url && url.toLowerCase().endsWith('.pdf');

    // Create modal content
    if (isPdf) {
      modal.innerHTML = `
        <div class="modal document-modal">
          <div class="modal-header">
            <h2 class="modal-title">${title} Preview</h2>
            <button class="modal-close-button" onclick="this.closest('.modal-overlay').remove(); document.body.classList.remove('modal-open');">&times;</button>
          </div>
          <div class="modal-body document-modal-body">
            <iframe src="${evidenceUrl}" width="100%" height="500px" style="border: none;"></iframe>
          </div>
          <div class="modal-footer">
            <button class="btn btn-primary" onclick="window.open('${evidenceUrl}', '_blank')">Open in New Tab</button>
            <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove(); document.body.classList.remove('modal-open');">Close</button>
          </div>
        </div>
      `;
    } else {
      modal.innerHTML = `
        <div class="modal document-modal">
          <div class="modal-header">
            <h2 class="modal-title">${title} Preview</h2>
            <button class="modal-close-button" onclick="this.closest('.modal-overlay').remove(); document.body.classList.remove('modal-open');">&times;</button>
          </div>
          <div class="modal-body document-modal-body">
            <img
              src="${evidenceUrl}"
              alt="${title}"
              class="document-full-image"
              style="max-width: 100%; height: auto; display: block; margin: 0 auto;"
              onerror="this.onerror=null; this.src='/img/document-placeholder.svg';"
            >
          </div>
          <div class="modal-footer">
            <button class="btn btn-primary" onclick="window.open('${evidenceUrl}', '_blank')">Open in New Tab</button>
            <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove(); document.body.classList.remove('modal-open');">Close</button>
          </div>
        </div>
      `;
    }

    // Add modal to document
    document.body.appendChild(modal);
    document.body.classList.add('modal-open');
  };


  return (
    <EnhancedAdminLayout title="Reported Profiles">
      <div className="content-header">
        <h2 className="page-title">Reported Profiles</h2>
        <div className="header-actions">
          <div className="search-container">
            <input
              type="text"
              className="search-input"
              placeholder="Search reports..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <button className="search-button" title="Search">
              🔍
            </button>
          </div>
          <select
            className="status-filter"
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="resolved">Resolved</option>
            <option value="dismissed">Dismissed</option>
          </select>
          <button
            className="refresh-button"
            onClick={fetchReportedProfiles}
          >
            ↻ Refresh
          </button>
        </div>
      </div>

      {loading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading reported profiles...</p>
        </div>
      ) : (
        <div className="table-container">
          <table className="data-table">
            <thead>
              <tr>
                <th>User</th>
                <th>Report Reason</th>
                <th>Report Count</th>
                <th>Reported By</th>
                <th>Report Date</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {reports.map(report => (
                <tr key={report.id}>
                  <td>
                    <div className="user-cell">
                      <div className="user-avatar">
                        {report.reportedUser.photo ? (
                          <img
                            src={report.reportedUser.photo}
                            alt={report.reportedUser.name}
                            onError={(e) => {
                              e.target.onerror = null;
                              e.target.src = '/img/user-placeholder.svg';
                            }}
                          />
                        ) : (
                          <img
                            src="/img/user-placeholder.svg"
                            alt={report.reportedUser.name}
                          />
                        )}
                      </div>
                      <div className="user-info">
                        <div className="user-name">{report.reportedUser.name}</div>
                        <div className="user-age">{report.reportedUser.age} years</div>
                      </div>
                    </div>
                  </td>
                  <td>{report.reason}</td>
                  <td>
                    <span className={`status-badge ${(report.reportCount || 1) > 2 ? 'high' : 'low'}`}>
                      {report.reportCount || 1}
                    </span>
                  </td>
                  <td>
                    <div className="user-cell">
                      <div className="user-avatar">
                        {(report.reporter?.photo || report.reportedBy?.photo) ? (
                          <img
                            src={report.reporter?.photo || report.reportedBy?.photo}
                            alt={(report.reporter?.name || report.reportedBy?.name)}
                            onError={(e) => {
                              e.target.onerror = null;
                              e.target.src = '/img/user-placeholder.svg';
                            }}
                          />
                        ) : (
                          <img
                            src="/img/user-placeholder.svg"
                            alt={(report.reporter?.name || report.reportedBy?.name || 'Unknown')}
                          />
                        )}
                      </div>
                      <div className="user-info">
                        <div className="user-name">{report.reporter?.name || report.reportedBy?.name || 'Unknown'}</div>
                        <div className="user-location">{report.reporter?.location || report.reportedBy?.email || ''}</div>
                      </div>
                    </div>
                  </td>
                  <td>{new Date(report.reportDate || report.reportedOn || Date.now()).toLocaleDateString()}</td>
                  <td>
                    <div className="action-buttons">
                      <button
                        className="action-btn view-btn"
                        title="View Details"
                        onClick={() => handleViewReport(report)}
                      >
                        👁️
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          <div className="table-footer">
            <div className="entries-info">
              Showing {reports.length > 0 ? 1 : 0} to {reports.length} of {reports.length} entries
            </div>
            <div className="pagination">
              <button
                className="pagination-btn"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              >
                Previous
              </button>
              <span className="pagination-info">Page {currentPage} of {totalPages}</span>
              <button
                className="pagination-btn"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Report Details Modal */}
      {showReportModal && currentReport && (
        <div className="modal-overlay" style={{ zIndex: 2000, position: 'fixed', left: 0, top: 0, width: '100%', height: '100%', backgroundColor: 'rgba(0, 0, 0, 0.5)', display: 'flex', justifyContent: 'center', alignItems: 'center', padding: '20px' }}>
          <div className="modal" style={{ margin: '0 auto', maxWidth: '800px', maxHeight: '90vh', overflowY: 'auto', backgroundColor: 'white', borderRadius: '12px', boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)', width: '100%', display: 'flex', flexDirection: 'column', position: 'relative' }}>
            <div className="modal-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '15px 20px', borderBottom: '1px solid #eee' }}>
              <h2 className="modal-title" style={{ margin: 0, fontSize: '1.5rem' }}>Report Details</h2>
              <button
                className="modal-close-button"
                onClick={closeReportModal}
                style={{ background: 'none', border: 'none', fontSize: '24px', cursor: 'pointer', color: '#999' }}
              >
                &times;
              </button>
            </div>
            <div className="modal-body" style={{ maxHeight: 'calc(90vh - 130px)', overflowY: 'auto', padding: '20px', flex: 1 }}>
              <div className="report-details" style={{ display: 'block' }}>
                <div className="user-profile-section" style={{ marginBottom: '20px' }}>
                  <div className="user-profile-header" style={{ display: 'flex', alignItems: 'center', gap: '15px', paddingBottom: '15px', borderBottom: '1px solid #eee' }}>
                    <div className="user-avatar large" style={{ width: '80px', height: '80px', borderRadius: '50%', overflow: 'hidden', display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#5e35b1', color: 'white', fontSize: '2rem' }}>
                      {currentReport.reportedUser.photo ? (
                        <img
                          src={currentReport.reportedUser.photo}
                          alt={currentReport.reportedUser.name}
                          style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src = '/img/user-placeholder.svg';
                          }}
                        />
                      ) : (
                        <img
                          src="/img/user-placeholder.svg"
                          alt={currentReport.reportedUser.name}
                          style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                        />
                      )}
                    </div>
                    <div className="user-profile-info" style={{ flex: 1 }}>
                      <h3 className="user-name" style={{ margin: '0 0 5px 0', fontSize: '1.5rem' }}>{currentReport.reportedUser.name}</h3>
                      <div className="user-meta" style={{ display: 'flex', gap: '8px', color: '#666', marginBottom: '8px' }}>
                        <span>{currentReport.reportedUser.age} years</span>
                        <span>•</span>
                        <span>{currentReport.reportedUser.location}</span>
                      </div>
                      <div className="report-status" style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                        <span className={`status-badge ${currentReport.status}`} style={{ padding: '4px 10px', borderRadius: '4px', fontSize: '0.85rem', fontWeight: '500', backgroundColor: currentReport.status === 'pending' ? '#ff9800' : currentReport.status === 'resolved' ? '#4caf50' : '#f44336', color: 'white' }}>
                          {currentReport.status.charAt(0).toUpperCase() + currentReport.status.slice(1)}
                        </span>
                        {currentReport.status !== 'pending' && (
                          <span className="report-date" style={{ fontSize: '0.85rem', color: '#666' }}>
                            on {new Date(currentReport.actionDate).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="report-tabs" style={{ display: 'block' }}>
                  <div className="tab-content" style={{ display: 'block' }}>
                    <div className="tab-section" style={{ marginBottom: '20px' }}>
                      <h4 className="section-title" style={{ marginBottom: '15px', fontSize: '1.2rem', fontWeight: '600' }}>Report Information</h4>
                      <div className="report-info-grid" style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '15px' }}>
                        <div className="detail-item" style={{ padding: '10px', backgroundColor: '#f9f9f9', borderRadius: '8px' }}>
                          <div className="detail-label" style={{ fontSize: '0.9rem', color: '#666', marginBottom: '5px' }}>Report Reason</div>
                          <div className="detail-value" style={{ fontWeight: '500' }}>{currentReport.reason}</div>
                        </div>
                        <div className="detail-item" style={{ padding: '10px', backgroundColor: '#f9f9f9', borderRadius: '8px' }}>
                          <div className="detail-label" style={{ fontSize: '0.9rem', color: '#666', marginBottom: '5px' }}>Additional Information</div>
                          <div className="detail-value" style={{ fontWeight: '500' }}>{currentReport.additionalInfo || 'None provided'}</div>
                        </div>
                        <div className="detail-item" style={{ padding: '10px', backgroundColor: '#f9f9f9', borderRadius: '8px' }}>
                          <div className="detail-label" style={{ fontSize: '0.9rem', color: '#666', marginBottom: '5px' }}>Report Date</div>
                          <div className="detail-value" style={{ fontWeight: '500' }}>{new Date(currentReport.reportDate).toLocaleDateString()}</div>
                        </div>
                        <div className="detail-item" style={{ padding: '10px', backgroundColor: '#f9f9f9', borderRadius: '8px' }}>
                          <div className="detail-label" style={{ fontSize: '0.9rem', color: '#666', marginBottom: '5px' }}>Report Count</div>
                          <div className="detail-value" style={{ fontWeight: '500' }}>{currentReport.reportCount}</div>
                        </div>
                        {currentReport.status !== 'pending' && (
                          <>
                            <div className="detail-item" style={{ padding: '10px', backgroundColor: '#f9f9f9', borderRadius: '8px' }}>
                              <div className="detail-label" style={{ fontSize: '0.9rem', color: '#666', marginBottom: '5px' }}>Action Date</div>
                              <div className="detail-value" style={{ fontWeight: '500' }}>{new Date(currentReport.actionDate).toLocaleDateString()}</div>
                            </div>
                            <div className="detail-item" style={{ padding: '10px', backgroundColor: '#f9f9f9', borderRadius: '8px' }}>
                              <div className="detail-label" style={{ fontSize: '0.9rem', color: '#666', marginBottom: '5px' }}>Action By</div>
                              <div className="detail-value" style={{ fontWeight: '500' }}>{currentReport.actionBy}</div>
                            </div>
                            {currentReport.status === 'resolved' && currentReport.actionTaken && (
                              <div className="detail-item" style={{ padding: '10px', backgroundColor: '#f9f9f9', borderRadius: '8px', gridColumn: '1 / -1' }}>
                                <div className="detail-label" style={{ fontSize: '0.9rem', color: '#666', marginBottom: '5px' }}>Action Taken</div>
                                <div className="detail-value" style={{ fontWeight: '500' }}>{currentReport.actionTaken}</div>
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    </div>

                    <div className="tab-section">
                      <h4 className="section-title">Reported User Information</h4>
                      <div className="user-details-grid">
                        <div className="detail-item">
                          <div className="detail-label">Full Name</div>
                          <div className="detail-value">{currentReport.reportedUser.name}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Age</div>
                          <div className="detail-value">{currentReport.reportedUser.age} years</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Gender</div>
                          <div className="detail-value">{currentReport.reportedUser.gender || 'Not specified'}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Location</div>
                          <div className="detail-value">{currentReport.reportedUser.location}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Email</div>
                          <div className="detail-value">{currentReport.reportedUser.email}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Phone</div>
                          <div className="detail-value">{currentReport.reportedUser.phone}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Occupation</div>
                          <div className="detail-value">{currentReport.reportedUser.occupation || 'Not specified'}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Education</div>
                          <div className="detail-value">{currentReport.reportedUser.education || 'Not specified'}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Marital Status</div>
                          <div className="detail-value">{currentReport.reportedUser.maritalStatus || 'Not specified'}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Registration Date</div>
                          <div className="detail-value">{new Date(currentReport.reportedUser.registrationDate).toLocaleDateString()}</div>
                        </div>
                      </div>
                    </div>

                    <div className="tab-section">
                      <h4 className="section-title">Reported User Community Details</h4>
                      <div className="user-details-grid">
                        <div className="detail-item">
                          <div className="detail-label">Sub-Caste</div>
                          <div className="detail-value">{currentReport.reportedUser.communityDetails?.subCaste || 'Not specified'}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Gotra</div>
                          <div className="detail-value">{currentReport.reportedUser.communityDetails?.gotra || 'Not specified'}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Rashi</div>
                          <div className="detail-value">{currentReport.reportedUser.communityDetails?.rashi || 'Not specified'}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Nakshatra</div>
                          <div className="detail-value">{currentReport.reportedUser.communityDetails?.nakshatra || 'Not specified'}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Manglik Status</div>
                          <div className="detail-value">{currentReport.reportedUser.communityDetails?.manglik ? 'Yes' : 'No'}</div>
                        </div>
                      </div>
                    </div>

                    <div className="tab-section">
                      <h4 className="section-title">Reported User Family Details</h4>
                      <div className="user-details-grid">
                        <div className="detail-item">
                          <div className="detail-label">Father's Name</div>
                          <div className="detail-value">{currentReport.reportedUser.familyDetails?.fatherName || 'Not specified'}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Father's Occupation</div>
                          <div className="detail-value">{currentReport.reportedUser.familyDetails?.fatherOccupation || 'Not specified'}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Mother's Name</div>
                          <div className="detail-value">{currentReport.reportedUser.familyDetails?.motherName || 'Not specified'}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Mother's Occupation</div>
                          <div className="detail-value">{currentReport.reportedUser.familyDetails?.motherOccupation || 'Not specified'}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Family Type</div>
                          <div className="detail-value">{currentReport.reportedUser.familyDetails?.familyType || 'Not specified'}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Family Values</div>
                          <div className="detail-value">{currentReport.reportedUser.familyDetails?.familyValues || 'Not specified'}</div>
                        </div>
                      </div>
                    </div>

                    <div className="tab-section">
                      <h4 className="section-title">Reporter Information</h4>
                      <div className="user-details-grid">
                        <div className="detail-item">
                          <div className="detail-label">Full Name</div>
                          <div className="detail-value">{currentReport.reporter.name}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Age</div>
                          <div className="detail-value">{currentReport.reporter.age} years</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Gender</div>
                          <div className="detail-value">{currentReport.reporter.gender || 'Not specified'}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Location</div>
                          <div className="detail-value">{currentReport.reporter.location}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Email</div>
                          <div className="detail-value">{currentReport.reporter.email}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Phone</div>
                          <div className="detail-value">{currentReport.reporter.phone}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Occupation</div>
                          <div className="detail-value">{currentReport.reporter.occupation || 'Not specified'}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Education</div>
                          <div className="detail-value">{currentReport.reporter.education || 'Not specified'}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Marital Status</div>
                          <div className="detail-value">{currentReport.reporter.maritalStatus || 'Not specified'}</div>
                        </div>
                        <div className="detail-item">
                          <div className="detail-label">Registration Date</div>
                          <div className="detail-value">{currentReport.reporter.registrationDate ? new Date(currentReport.reporter.registrationDate).toLocaleDateString() : 'Not available'}</div>
                        </div>
                      </div>
                    </div>

                    {/* Evidence Section - Add this if you have evidence images */}
                    <div className="tab-section">
                      <h4 className="section-title">Evidence</h4>
                      <div className="evidence-grid">
                        {currentReport.evidence && currentReport.evidence.length > 0 ? (
                          currentReport.evidence.map((item, index) => (
                            <div key={index} className="evidence-item" onClick={() => viewEvidence(item.url, item.type || 'Evidence')}>
                              <div className="evidence-preview">
                                <img
                                  src={item.url || '/img/document-placeholder.svg'}
                                  alt={item.type || 'Evidence'}
                                  onError={(e) => {
                                    e.target.onerror = null;
                                    e.target.src = '/img/document-placeholder.svg';
                                  }}
                                />
                              </div>
                              <div className="evidence-info">
                                <div className="evidence-type">{item.type || 'Evidence'}</div>
                                <div className="evidence-date">{new Date(item.uploadedAt || currentReport.reportDate).toLocaleDateString()}</div>
                              </div>
                            </div>
                          ))
                        ) : (
                          <div className="no-evidence">No evidence images provided</div>
                        )}
                      </div>
                    </div>

                    {/* Enhanced action section inside the modal body */}
                    {currentReport.status === 'pending' && (
                      <div className="tab-section action-section">
                        <h4 className="section-title">Admin Actions</h4>
                        <div className="action-buttons-container">
                          <div className="action-description">
                            <p>Please review the report details carefully before taking action.</p>
                          </div>
                          <div className="action-options">
                            <div className="action-option">
                              <h5>Resolve Report</h5>
                              <p>Take action against the reported user if the report is valid.</p>
                              <div className="action-inputs">
                                <select
                                  className="form-control"
                                  id="actionType"
                                  value={selectedAction}
                                  onChange={(e) => setSelectedAction(e.target.value)}
                                >
                                  <option value="">Select Action</option>
                                  <option value="WARNING">Issue Warning</option>
                                  <option value="CONTENT_REMOVAL">Remove Content</option>
                                  <option value="REVOKE_VERIFICATION">Revoke Verification</option>
                                  <option value="RESTRICT_FEATURES">Restrict Features</option>
                                  <option value="TEMP_SUSPENSION">Temporary Suspension</option>
                                  <option value="PERMANENT_BAN">Permanent Ban</option>
                                </select>

                                {selectedAction === 'TEMP_SUSPENSION' && (
                                  <div className="suspension-duration mt-2">
                                    <label htmlFor="suspensionDays">Suspension Duration (days):</label>
                                    <input
                                      type="number"
                                      id="suspensionDays"
                                      className="form-control"
                                      min="1"
                                      max="90"
                                      value={suspensionDays}
                                      onChange={(e) => setSuspensionDays(e.target.value)}
                                    />
                                  </div>
                                )}

                                {selectedAction === 'RESTRICT_FEATURES' && (
                                  <div className="feature-restrictions mt-2">
                                    <label>Features to Restrict:</label>
                                    <div className="feature-checkboxes">
                                      {['messaging', 'photo_upload', 'search', 'contact_view'].map(feature => (
                                        <div className="feature-checkbox" key={feature}>
                                          <input
                                            type="checkbox"
                                            id={`feature-${feature}`}
                                            value={feature}
                                            checked={restrictedFeatures.includes(feature)}
                                            onChange={(e) => {
                                              if (e.target.checked) {
                                                setRestrictedFeatures([...restrictedFeatures, feature]);
                                              } else {
                                                setRestrictedFeatures(restrictedFeatures.filter(f => f !== feature));
                                              }
                                            }}
                                          />
                                          <label htmlFor={`feature-${feature}`}>
                                            {feature.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                          </label>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}

                                <textarea
                                  className="form-control mt-2"
                                  placeholder="Add notes about this action (required)"
                                  rows="2"
                                  value={actionDetails}
                                  onChange={(e) => setActionDetails(e.target.value)}
                                ></textarea>
                              </div>
                              <button
                                className="btn btn-success btn-lg mt-3"
                                onClick={() => handleResolveReport(currentReport)}
                                disabled={!selectedAction || (selectedAction === 'RESTRICT_FEATURES' && restrictedFeatures.length === 0) || !actionDetails}
                              >
                                <span className="action-icon">✓</span> Resolve Report
                              </button>
                            </div>

                            <div className="action-option">
                              <h5>Dismiss Report</h5>
                              <p>Dismiss the report if it is invalid or does not violate our policies.</p>
                              <div className="action-inputs">
                                <textarea
                                  className="form-control"
                                  placeholder="Reason for dismissal (required)"
                                  rows="2"
                                  value={dismissReason}
                                  onChange={(e) => setDismissReason(e.target.value)}
                                ></textarea>
                              </div>
                              <button
                                className="btn btn-danger btn-lg mt-3"
                                onClick={() => handleDismissReport(currentReport)}
                                disabled={!dismissReason}
                              >
                                <span className="action-icon">✗</span> Dismiss Report
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="modal-footer" style={{ display: 'flex', justifyContent: 'flex-end', gap: '10px', padding: '15px 20px', borderTop: '1px solid #eee' }}>
              {currentReport.status === 'pending' && (
                <>
                  <button
                    className="btn btn-success"
                    onClick={() => handleResolveReport(currentReport)}
                    style={{ padding: '8px 16px', backgroundColor: '#4caf50', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer', fontWeight: '500', display: 'flex', alignItems: 'center', gap: '5px' }}
                  >
                    <span className="action-icon">✓</span> Resolve
                  </button>
                  <button
                    className="btn btn-danger"
                    onClick={() => handleDismissReport(currentReport)}
                    style={{ padding: '8px 16px', backgroundColor: '#f44336', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer', fontWeight: '500', display: 'flex', alignItems: 'center', gap: '5px' }}
                  >
                    <span className="action-icon">✗</span> Dismiss
                  </button>
                </>
              )}
              <button
                className="btn btn-secondary"
                onClick={closeReportModal}
                style={{ padding: '8px 16px', backgroundColor: '#f0f0f0', color: '#333', border: 'none', borderRadius: '4px', cursor: 'pointer', fontWeight: '500' }}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmModal && currentReport && (
        <div className="modal-overlay" style={{ zIndex: 2100, position: 'fixed', left: 0, top: 0, width: '100%', height: '100%', backgroundColor: 'rgba(0, 0, 0, 0.5)', display: 'flex', justifyContent: 'center', alignItems: 'center', padding: '20px' }}>
          <div className="modal confirmation-modal" style={{ margin: '0 auto', maxWidth: '500px', backgroundColor: 'white', borderRadius: '12px', boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)', width: '100%', display: 'flex', flexDirection: 'column', position: 'relative' }}>
            <div className="modal-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '15px 20px', borderBottom: '1px solid #eee' }}>
              <h2 className="modal-title" style={{ margin: 0, fontSize: '1.5rem' }}>
                {confirmAction === 'resolve' ? 'Resolve Report' : 'Dismiss Report'}
              </h2>
              <button
                className="modal-close-button"
                onClick={closeConfirmModal}
                style={{ background: 'none', border: 'none', fontSize: '24px', cursor: 'pointer', color: '#999' }}
              >
                &times;
              </button>
            </div>
            <div className="modal-body" style={{ padding: '20px' }}>
              <div className="confirmation-message" style={{ textAlign: 'center' }}>
                <div className="confirmation-icon" style={{ fontSize: '3rem', marginBottom: '15px', color: confirmAction === 'resolve' ? '#4caf50' : '#f44336' }}>
                  {confirmAction === 'resolve' ? '✓' : '✗'}
                </div>
                <p style={{ fontSize: '1.1rem', marginBottom: '20px' }}>
                  Are you sure you want to <strong>{confirmAction}</strong> the report against <strong>{currentReport.reportedUser.name}</strong>?
                </p>
                {confirmAction === 'resolve' && (
                  <div className="action-details">
                    <label htmlFor="actionType">Action to Take:</label>
                    <select
                      id="actionType"
                      value={selectedAction}
                      onChange={(e) => setSelectedAction(e.target.value)}
                      className="action-select"
                    >
                      <option value="">Select an action...</option>
                      <option value="WARNING">Issue Warning</option>
                      <option value="CONTENT_REMOVAL">Remove Content</option>
                      <option value="REVOKE_VERIFICATION">Revoke Verification</option>
                      <option value="RESTRICT_FEATURES">Restrict Features</option>
                      <option value="TEMP_SUSPENSION">Temporary Suspension</option>
                      <option value="PERMANENT_BAN">Permanent Ban</option>
                    </select>

                    {selectedAction === 'TEMP_SUSPENSION' && (
                      <div className="suspension-duration">
                        <label htmlFor="suspensionDays">Suspension Duration (days):</label>
                        <input
                          type="number"
                          id="suspensionDays"
                          min="1"
                          max="90"
                          value={suspensionDays}
                          onChange={(e) => setSuspensionDays(e.target.value)}
                        />
                      </div>
                    )}

                    {selectedAction === 'RESTRICT_FEATURES' && (
                      <div className="feature-restrictions">
                        <label>Features to Restrict:</label>
                        <div className="feature-checkboxes">
                          {['messaging', 'photo_upload', 'search', 'contact_view'].map(feature => (
                            <label key={feature}>
                              <input
                                type="checkbox"
                                value={feature}
                                checked={restrictedFeatures.includes(feature)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setRestrictedFeatures([...restrictedFeatures, feature]);
                                  } else {
                                    setRestrictedFeatures(restrictedFeatures.filter(f => f !== feature));
                                  }
                                }}
                              />
                              {feature.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </label>
                          ))}
                        </div>
                      </div>
                    )}

                    <label htmlFor="actionTaken">Reason and Details:</label>
                    <textarea
                      id="actionTaken"
                      placeholder="Describe the reason for this action and any specific details"
                      rows="3"
                      value={actionDetails}
                      onChange={(e) => setActionDetails(e.target.value)}
                    ></textarea>
                  </div>
                )}
              </div>
            </div>
            <div className="modal-footer" style={{ display: 'flex', justifyContent: 'flex-end', gap: '10px', padding: '15px 20px', borderTop: '1px solid #eee' }}>
              <button
                className="btn btn-secondary"
                onClick={closeConfirmModal}
                style={{ padding: '8px 16px', backgroundColor: '#f0f0f0', color: '#333', border: 'none', borderRadius: '4px', cursor: 'pointer', fontWeight: '500' }}
              >
                Cancel
              </button>
              <button
                className={`btn ${confirmAction === 'resolve' ? 'btn-success' : 'btn-danger'}`}
                onClick={confirmReportAction}
                style={{
                  padding: '8px 16px',
                  backgroundColor: confirmAction === 'resolve' ? '#4caf50' : '#f44336',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontWeight: '500'
                }}
              >
                {confirmAction === 'resolve' ? 'Resolve' : 'Dismiss'}
              </button>
            </div>
          </div>
        </div>
      )}

      <style jsx global>{`
        /* Force body to show modal */
        body.modal-open {
          overflow: hidden;
        }

        /* Force modal to display */
        .modal-overlay {
          display: flex !important;
        }
        /* Header Actions */
        .header-actions {
          display: flex;
          gap: 15px;
          align-items: center;
        }

        .search-container {
          position: relative;
        }

        .search-input {
          padding: 10px 15px;
          padding-right: 40px;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 0.9rem;
          width: 250px;
          transition: all 0.3s ease;
        }

        .search-input:focus {
          border-color: var(--secondary);
          box-shadow: 0 0 0 2px rgba(255, 87, 34, 0.2);
          outline: none;
        }

        .search-button {
          position: absolute;
          right: 0;
          top: 0;
          height: 100%;
          width: 40px;
          background: none;
          border: none;
          cursor: pointer;
          color: #666;
          font-size: 1.1rem;
        }

        .search-button:hover {
          color: var(--secondary);
        }

        .status-filter {
          padding: 10px 15px;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 0.9rem;
          background-color: white;
          min-width: 140px;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .status-filter:focus {
          border-color: var(--secondary);
          box-shadow: 0 0 0 2px rgba(255, 87, 34, 0.2);
          outline: none;
        }

        .refresh-button {
          padding: 10px 15px;
          background-color: var(--secondary);
          color: white;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-size: 0.9rem;
          display: flex;
          align-items: center;
          gap: 5px;
          transition: background-color 0.3s ease;
        }

        .refresh-button:hover {
          background-color: #e64a19;
        }

        /* Table Styles */
        .table-container {
          background-color: white;
          border-radius: 12px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          overflow: hidden;
          margin-bottom: 30px;
        }

        .data-table {
          width: 100%;
          border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
          padding: 15px;
          text-align: left;
          border-bottom: 1px solid #eee;
        }

        .data-table th {
          background-color: #f9f9f9;
          font-weight: 600;
          color: #555;
        }

        .data-table tr:last-child td {
          border-bottom: none;
        }

        .data-table tr:hover {
          background-color: #f5f5f5;
        }

        /* User Cell */
        .user-cell {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .user-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background-color: var(--primary-light);
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 1.1rem;
          overflow: hidden;
        }

        .user-avatar img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .user-info {
          display: flex;
          flex-direction: column;
        }

        .user-name {
          font-weight: 600;
          color: var(--text-dark);
        }

        .user-age, .user-location {
          font-size: 0.8rem;
          color: #666;
        }

        /* Status Badge */
        .status-badge {
          display: inline-block;
          padding: 5px 10px;
          border-radius: 12px;
          font-size: 0.75rem;
          font-weight: 500;
        }

        .status-badge.pending {
          background-color: #fff8e1;
          color: #ff8f00;
        }

        .status-badge.resolved {
          background-color: #e8f5e9;
          color: #2e7d32;
        }

        .status-badge.dismissed {
          background-color: #f5f5f5;
          color: #757575;
        }

        .status-badge.high {
          background-color: #ffebee;
          color: #c62828;
        }

        .status-badge.low {
          background-color: #e8f5e9;
          color: #2e7d32;
        }

        /* Action Buttons */
        .action-buttons {
          display: flex;
          gap: 8px;
        }

        .action-btn {
          background: none;
          border: none;
          cursor: pointer;
          font-size: 1rem;
          padding: 4px;
          border-radius: 4px;
          transition: background-color 0.2s ease;
        }

        .action-btn:hover {
          background-color: #f0f0f0;
        }

        .view-btn {
          color: var(--primary);
        }

        .resolve-btn {
          color: var(--success);
        }

        .dismiss-btn {
          color: var(--danger);
        }

        /* Table Footer */
        .table-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 15px;
          border-top: 1px solid #eee;
        }

        .entries-info {
          color: #666;
          font-size: 0.9rem;
        }

        .pagination {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .pagination-btn {
          background-color: #f5f5f5;
          border: 1px solid #ddd;
          border-radius: 4px;
          padding: 5px 10px;
          cursor: pointer;
          font-size: 0.9rem;
          transition: all 0.2s ease;
        }

        .pagination-btn:hover:not(:disabled) {
          background-color: var(--primary-light);
          color: white;
          border-color: var(--primary-light);
        }

        .pagination-btn:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .pagination-info {
          color: #666;
          font-size: 0.9rem;
        }

        /* Modal Styles */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex !important;
          justify-content: center;
          align-items: center;
          z-index: 2000; /* Higher than sidebar z-index (1000) */
          overflow-y: auto;
          padding: 20px;
        }

        .modal {
          background-color: white;
          border-radius: 12px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
          width: 100%;
          max-width: 800px;
          max-height: 90vh;
          overflow-y: auto;
          display: flex !important;
          flex-direction: column;
          margin-left: auto;
          margin-right: auto;
          position: relative;
        }

        .confirmation-modal {
          max-width: 500px;
        }

        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 15px 20px;
          border-bottom: 1px solid #eee;
        }

        .modal-title {
          font-size: 1.2rem;
          font-weight: 600;
          color: var(--text-dark);
          margin: 0;
        }

        .modal-close-button {
          background: none;
          border: none;
          font-size: 1.5rem;
          cursor: pointer;
          color: #999;
          transition: color 0.2s ease;
        }

        .modal-close-button:hover {
          color: var(--text-dark);
        }

        .modal-body {
          padding: 20px;
          overflow-y: auto;
          flex: 1;
          display: block !important;
        }

        .modal-footer {
          display: flex;
          justify-content: flex-end;
          gap: 10px;
          padding: 15px 20px;
          border-top: 1px solid #eee;
        }

        /* Report Details Styles */
        .report-details {
          display: flex !important;
          flex-direction: column;
          gap: 20px;
        }

        .user-profile-section {
          margin-bottom: 20px;
        }

        .user-profile-header {
          display: flex;
          align-items: center;
          gap: 20px;
          padding-bottom: 20px;
          border-bottom: 1px solid #eee;
        }

        .user-avatar.large {
          width: 80px;
          height: 80px;
          font-size: 2rem;
        }

        .user-profile-info {
          flex: 1;
        }

        .user-profile-info .user-name {
          font-size: 1.5rem;
          margin-bottom: 5px;
        }

        .user-meta {
          display: flex;
          gap: 8px;
          color: #666;
          margin-bottom: 10px;
        }

        .report-status {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .report-date {
          font-size: 0.85rem;
          color: #666;
        }

        .report-tabs {
          display: flex;
          flex-direction: column;
        }

        .tab-section {
          margin-bottom: 30px;
        }

        .section-title {
          font-size: 1.1rem;
          font-weight: 600;
          margin-bottom: 15px;
          padding-bottom: 8px;
          border-bottom: 1px solid #eee;
          color: var(--text-dark);
        }

        .report-info-grid,
        .user-details-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 15px;
        }

        /* Evidence Grid Styles */
        .evidence-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 15px;
          margin-bottom: 20px;
        }

        .evidence-item {
          border: 1px solid #eee;
          border-radius: 8px;
          overflow: hidden;
          cursor: pointer;
          transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .evidence-item:hover {
          transform: translateY(-3px);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .evidence-preview {
          height: 150px;
          overflow: hidden;
        }

        .evidence-preview img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .evidence-info {
          padding: 10px;
          background-color: #f9f9f9;
        }

        .evidence-type {
          font-weight: 600;
          color: var(--text-dark);
          margin-bottom: 5px;
        }

        .evidence-date {
          font-size: 0.8rem;
          color: #666;
        }

        .no-evidence {
          grid-column: 1 / -1;
          padding: 15px;
          text-align: center;
          color: #666;
          background-color: #f9f9f9;
          border-radius: 8px;
        }

        /* Document Modal Styles */
        .document-modal-body {
          padding: 20px;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #f5f5f5;
          min-height: 300px;
        }

        .document-full-image {
          max-width: 100%;
          max-height: 70vh;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .document-viewer-modal {
          z-index: 3000;
        }

        /* Action Section Styles */
        .action-section {
          background-color: #f5f5f5;
          border-radius: 8px;
          padding: 20px;
          margin-top: 20px;
        }

        .action-buttons-container {
          display: flex;
          flex-direction: column;
          gap: 15px;
        }

        .action-description {
          margin-bottom: 10px;
        }

        .action-options {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;
        }

        @media (max-width: 768px) {
          .action-options {
            grid-template-columns: 1fr;
          }
        }

        .action-option {
          background-color: white;
          border-radius: 8px;
          padding: 15px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .action-option h5 {
          font-size: 1.1rem;
          margin-top: 0;
          margin-bottom: 10px;
          color: var(--text-dark);
        }

        .action-option p {
          color: #666;
          margin-bottom: 15px;
          font-size: 0.9rem;
        }

        .action-inputs {
          display: flex;
          flex-direction: column;
          gap: 10px;
          margin-bottom: 15px;
        }

        .form-control {
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 0.9rem;
        }

        .form-control:focus {
          border-color: var(--primary);
          outline: none;
          box-shadow: 0 0 0 2px rgba(103, 58, 183, 0.1);
        }

        .mt-2 {
          margin-top: 10px;
        }

        .mt-3 {
          margin-top: 15px;
        }

        .feature-checkboxes {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 8px;
          margin-top: 8px;
        }

        .feature-checkbox {
          display: flex;
          align-items: center;
          gap: 5px;
        }

        .btn {
          padding: 10px 15px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-weight: 500;
          transition: background-color 0.2s ease;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
        }

        .btn-lg {
          padding: 12px 20px;
          font-size: 1rem;
        }

        .btn-primary {
          background-color: var(--primary);
          color: white;
        }

        .btn-primary:hover {
          background-color: #5c35a4;
        }

        .btn-success {
          background-color: #4caf50;
          color: white;
        }

        .btn-success:hover {
          background-color: #43a047;
        }

        .btn-danger {
          background-color: #f44336;
          color: white;
        }

        .btn-danger:hover {
          background-color: #e53935;
        }

        .btn-secondary {
          background-color: #757575;
          color: white;
        }

        .btn-secondary:hover {
          background-color: #616161;
        }

        .btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .action-icon {
          font-size: 1.1rem;
        }
        .action-section {
          background-color: #f9f9f9;
          border-radius: 8px;
          padding: 20px;
          margin-top: 20px;
        }

        .action-buttons-container {
          display: flex;
          flex-direction: column;
          gap: 15px;
        }

        .action-description {
          margin-bottom: 10px;
        }

        .action-description p {
          color: #555;
          font-size: 0.95rem;
          margin: 0;
        }

        .action-options {
          display: flex;
          gap: 20px;
          margin-top: 15px;
        }

        .action-option {
          flex: 1;
          background-color: #f9f9f9;
          border-radius: 8px;
          padding: 15px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .action-option h5 {
          margin-top: 0;
          margin-bottom: 8px;
          color: var(--text-dark);
          font-size: 1rem;
          font-weight: 600;
        }

        .action-option p {
          color: #666;
          font-size: 0.9rem;
          margin-bottom: 15px;
        }

        .action-inputs {
          margin-bottom: 15px;
        }

        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 0.9rem;
          margin-bottom: 10px;
        }

        .form-control:focus {
          border-color: var(--primary);
          outline: none;
          box-shadow: 0 0 0 2px rgba(103, 58, 183, 0.1);
        }

        .mt-2 {
          margin-top: 10px;
        }

        .mt-3 {
          margin-top: 15px;
        }

        .feature-checkbox {
          display: flex;
          align-items: center;
          gap: 5px;
        }

        .feature-checkbox label {
          font-size: 0.85rem;
          color: #555;
          cursor: pointer;
        }

        .action-buttons-row {
          display: flex;
          gap: 15px;
          justify-content: center;
        }

        .btn-lg {
          padding: 12px 24px;
          font-size: 1rem;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          min-width: 200px;
        }

        .action-icon {
          font-size: 1.2rem;
          font-weight: bold;
        }

        .detail-item {
          display: flex;
          flex-direction: column;
          gap: 5px;
        }

        .detail-label {
          font-size: 0.85rem;
          color: #666;
        }

        .detail-value {
          font-weight: 500;
          color: var(--text-dark);
        }

        /* Confirmation Modal Styles */
        .confirmation-message {
          text-align: center;
          padding: 20px 0;
        }

        .confirmation-icon {
          font-size: 3rem;
          margin-bottom: 20px;
          display: inline-block;
          width: 80px;
          height: 80px;
          line-height: 80px;
          border-radius: 50%;
          background-color: #f5f5f5;
          color: var(--primary);
        }

        .action-details {
          margin-top: 20px;
          text-align: left;
        }

        .action-details label {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
        }

        .action-details textarea {
          width: 100%;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          resize: vertical;
        }

        .action-select {
          width: 100%;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          margin-bottom: 15px;
          background-color: white;
          font-size: 0.95rem;
        }

        .suspension-duration {
          margin: 15px 0;
        }

        .suspension-duration input {
          width: 100%;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
        }

        .feature-restrictions {
          margin: 15px 0;
        }

        .feature-checkboxes {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 10px;
          margin-top: 8px;
        }

        .feature-checkboxes label {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: normal;
          cursor: pointer;
        }

        .feature-checkboxes input[type="checkbox"] {
          width: 16px;
          height: 16px;
        }

        /* Button Styles */
        .btn {
          padding: 8px 16px;
          border-radius: 4px;
          font-weight: 500;
          cursor: pointer;
          border: none;
          transition: background-color 0.2s ease;
        }

        .btn-primary {
          background-color: var(--primary);
          color: white;
        }

        .btn-primary:hover {
          background-color: var(--primary-dark);
        }

        .btn-secondary {
          background-color: #f5f5f5;
          color: var(--text-dark);
        }

        .btn-secondary:hover {
          background-color: #e0e0e0;
        }

        .btn-success {
          background-color: var(--success);
          color: white;
        }

        .btn-success:hover {
          background-color: #388e3c;
        }

        .btn-danger {
          background-color: var(--danger);
          color: white;
        }

        .btn-danger:hover {
          background-color: #d32f2f;
        }

        /* Responsive Modal */
        @media (max-width: 768px) {
          .modal {
            width: 95%;
            max-height: 80vh;
          }

          .user-profile-header {
            flex-direction: column;
            text-align: center;
          }

          .report-info-grid,
          .user-details-grid,
          .evidence-grid {
            grid-template-columns: 1fr;
          }

          .action-options {
            flex-direction: column;
          }

          .action-option {
            margin-bottom: 15px;
          }
        }
      `}</style>
    </EnhancedAdminLayout>
  );
}


