// API endpoint for individual blog posts
import { handleApiError } from '@/utils/errorHandler';
import { withAuth } from '@/utils/authHandler';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';

async function handler(req, res) {
  // Check if user is authenticated and is an admin
  const session = await getServerSession(req, res, authOptions);

  if (!session || !session.user || !session.user.isAdmin) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }
  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getBlogPost(req, res);
      case 'PUT':
        return await updateBlogPost(req, res);
      case 'DELETE':
        return await deleteBlogPost(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    return handleApiError(error, res, 'Blog Post API');
  }
}

// Mock data for blog posts
const blogPosts = [
  {
    id: 1,
    title: 'Finding Your Perfect Match: Tips for Creating an Attractive Profile',
    slug: 'finding-your-perfect-match-tips',
    excerpt: 'Learn how to create a profile that stands out and attracts the right matches.',
    content: 'Creating an attractive profile is the first step towards finding your perfect match. Here are some tips to help you create a profile that stands out...',
    featuredImage: '/images/blog/profile-tips.jpg',
    category: 'Profile Tips',
    tags: ['profile', 'tips', 'matching'],
    author: 'Priya Sharma',
    authorId: 1,
    status: 'published',
    publishedAt: '2023-05-15T10:30:00Z',
    createdAt: '2023-05-10T08:15:00Z',
    updatedAt: '2023-05-15T10:30:00Z',
    viewCount: 1250,
    commentCount: 18
  },
  {
    id: 2,
    title: 'The Importance of Family Values in Maratha Marriages',
    slug: 'importance-of-family-values-maratha-marriages',
    excerpt: 'Discover why family values play a crucial role in successful Maratha marriages.',
    content: 'In Maratha culture, family values are the foundation of a successful marriage. This article explores the importance of these values...',
    featuredImage: '/images/blog/family-values.jpg',
    category: 'Culture',
    tags: ['family', 'values', 'culture', 'maratha'],
    author: 'Rajesh Patil',
    authorId: 2,
    status: 'published',
    publishedAt: '2023-06-02T14:45:00Z',
    createdAt: '2023-05-28T11:20:00Z',
    updatedAt: '2023-06-02T14:45:00Z',
    viewCount: 980,
    commentCount: 12
  },
  {
    id: 3,
    title: 'Modern Dating vs Traditional Matchmaking: Finding the Balance',
    slug: 'modern-dating-vs-traditional-matchmaking',
    excerpt: 'Explore the pros and cons of modern dating and traditional matchmaking approaches.',
    content: 'In today\'s world, many young Marathas are caught between modern dating approaches and traditional matchmaking. This article explores how to find the right balance...',
    featuredImage: '/images/blog/modern-traditional.jpg',
    category: 'Relationships',
    tags: ['dating', 'matchmaking', 'tradition', 'modern'],
    author: 'Ananya Desai',
    authorId: 3,
    status: 'published',
    publishedAt: '2023-06-20T09:15:00Z',
    createdAt: '2023-06-15T16:30:00Z',
    updatedAt: '2023-06-20T09:15:00Z',
    viewCount: 1560,
    commentCount: 25
  },
  {
    id: 4,
    title: 'Preparing for Your First Meeting: Do\'s and Don\'ts',
    slug: 'first-meeting-dos-and-donts',
    excerpt: 'Essential tips for making a great impression during your first meeting with a potential match.',
    content: 'The first meeting with a potential match can be nerve-wracking. Here are some do\'s and don\'ts to help you make a great impression...',
    featuredImage: '/images/blog/first-meeting.jpg',
    category: 'Dating Tips',
    tags: ['meeting', 'first impression', 'dating'],
    author: 'Vikram Singh',
    authorId: 4,
    status: 'draft',
    publishedAt: null,
    createdAt: '2023-07-05T13:45:00Z',
    updatedAt: '2023-07-05T13:45:00Z',
    viewCount: 0,
    commentCount: 0
  },
  {
    id: 5,
    title: 'Understanding Compatibility: Beyond Horoscopes',
    slug: 'understanding-compatibility-beyond-horoscopes',
    excerpt: 'Learn about the various factors that contribute to compatibility in a successful marriage.',
    content: 'While horoscopes are important in traditional matchmaking, there are many other factors that contribute to compatibility. This article explores these factors...',
    featuredImage: '/images/blog/compatibility.jpg',
    category: 'Compatibility',
    tags: ['compatibility', 'horoscope', 'matching'],
    author: 'Neha Gupta',
    authorId: 5,
    status: 'published',
    publishedAt: '2023-07-18T11:30:00Z',
    createdAt: '2023-07-10T09:20:00Z',
    updatedAt: '2023-07-18T11:30:00Z',
    viewCount: 850,
    commentCount: 9
  }
];

// GET /api/admin/blog-posts/[id]
async function getBlogPost(req, res) {
  try {
    const { id } = req.query;

    // Find the post by ID
    const post = blogPosts.find(post => post.id === parseInt(id));

    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Blog post not found'
      });
    }

    return res.status(200).json({
      success: true,
      post
    });
  } catch (error) {
    return handleApiError(error, res, 'Get blog post');
  }
}

// PUT /api/admin/blog-posts/[id]
async function updateBlogPost(req, res) {
  try {
    const { id } = req.query;
    const { title, excerpt, content, category, tags, status } = req.body;

    // Find the post by ID
    const postIndex = blogPosts.findIndex(post => post.id === parseInt(id));

    if (postIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Blog post not found'
      });
    }

    // Update the post
    const updatedPost = {
      ...blogPosts[postIndex],
      title: title || blogPosts[postIndex].title,
      excerpt: excerpt || blogPosts[postIndex].excerpt,
      content: content || blogPosts[postIndex].content,
      category: category || blogPosts[postIndex].category,
      tags: tags || blogPosts[postIndex].tags,
      status: status || blogPosts[postIndex].status,
      updatedAt: new Date().toISOString(),
      publishedAt: status === 'published' && !blogPosts[postIndex].publishedAt ? new Date().toISOString() : blogPosts[postIndex].publishedAt
    };

    // In a real implementation, this would update the post in the database
    // For now, we'll just return a success response with the updated post

    return res.status(200).json({
      success: true,
      message: 'Blog post updated successfully',
      post: updatedPost
    });
  } catch (error) {
    return handleApiError(error, res, 'Update blog post');
  }
}

// DELETE /api/admin/blog-posts/[id]
async function deleteBlogPost(req, res) {
  try {
    const { id } = req.query;

    // Find the post by ID
    const postIndex = blogPosts.findIndex(post => post.id === parseInt(id));

    if (postIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Blog post not found'
      });
    }

    // In a real implementation, this would delete the post from the database
    // For now, we'll just return a success response

    return res.status(200).json({
      success: true,
      message: 'Blog post deleted successfully'
    });
  } catch (error) {
    return handleApiError(error, res, 'Delete blog post');
  }
}

export default handler;
