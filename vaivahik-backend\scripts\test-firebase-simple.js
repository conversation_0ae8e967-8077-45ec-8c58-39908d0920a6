#!/usr/bin/env node

/**
 * Simple Firebase Test
 */

require('dotenv').config();

console.log('🔔 Simple Firebase Test...\n');

// Check environment variables
console.log('Environment Variables:');
console.log(`FIREBASE_PROJECT_ID: ${process.env.FIREBASE_PROJECT_ID ? '✅ Set' : '❌ Missing'}`);
console.log(`FIREBASE_CLIENT_EMAIL: ${process.env.FIREBASE_CLIENT_EMAIL ? '✅ Set' : '❌ Missing'}`);
console.log(`FIREBASE_PRIVATE_KEY: ${process.env.FIREBASE_PRIVATE_KEY ? '✅ Set' : '❌ Missing'}`);

if (process.env.FIREBASE_PRIVATE_KEY) {
    console.log(`Private Key Length: ${process.env.FIREBASE_PRIVATE_KEY.length} characters`);
    console.log(`Private Key Start: ${process.env.FIREBASE_PRIVATE_KEY.substring(0, 50)}...`);
}

try {
    console.log('\nTesting Firebase Admin import...');
    const admin = require('firebase-admin');
    console.log('✅ Firebase Admin imported successfully');
    
    console.log(`Current apps: ${admin.apps.length}`);
    
    if (admin.apps.length === 0) {
        console.log('\nTesting Firebase initialization...');
        
        // Simple test with minimal config
        const testConfig = {
            projectId: process.env.FIREBASE_PROJECT_ID || 'test-project'
        };
        
        console.log(`Using project ID: ${testConfig.projectId}`);
        
        // Try to initialize without credentials first
        admin.initializeApp(testConfig);
        console.log('✅ Firebase initialized (without credentials)');
    }
    
    console.log('\n🎉 Firebase Admin SDK is working!');
    
} catch (error) {
    console.log('\n❌ Firebase test failed:');
    console.log(`Error: ${error.message}`);
    console.log(`Code: ${error.code || 'N/A'}`);
}

process.exit(0);
