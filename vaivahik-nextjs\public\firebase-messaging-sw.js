// Firebase Messaging Service Worker
// This file must be in the public directory to work properly

// Import and configure the Firebase SDK
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

// Firebase configuration
// These values will be replaced by the actual values during build
firebase.initializeApp({
  apiKey: "FIREBASE_API_KEY",
  authDomain: "FIREBASE_AUTH_DOMAIN",
  projectId: "FIREBASE_PROJECT_ID",
  storageBucket: "FIREBASE_STORAGE_BUCKET",
  messagingSenderId: "FIREBASE_MESSAGING_SENDER_ID",
  appId: "FIREBASE_APP_ID"
});

const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message:', payload);

  // Customize notification here
  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    icon: '/logo.png',
    badge: '/badge-icon.png',
    image: payload.notification.image,
    vibrate: [200, 100, 200],
    data: payload.data,
    actions: [
      {
        action: 'view',
        title: 'View',
      },
    ],
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification click
self.addEventListener('notificationclick', (event) => {
  console.log('[firebase-messaging-sw.js] Notification clicked:', event);

  event.notification.close();

  // Get the notification data
  const data = event.notification.data;
  
  // Handle click based on notification type
  let url = '/';
  
  if (data) {
    // Handle different notification types
    switch (data.type) {
      case 'NEW_MATCH':
        url = `/profile/${data.matchId}`;
        break;
      case 'PROFILE_VIEW':
        url = `/profile/${data.viewerId}`;
        break;
      case 'INTEREST_RECEIVED':
        url = '/interests/received';
        break;
      case 'INTEREST_ACCEPTED':
        url = `/profile/${data.acceptorId}`;
        break;
      case 'NEW_MESSAGE':
        url = `/messages/${data.conversationId}`;
        break;
      case 'PROMOTION':
        url = data.webLink || '/promotions';
        break;
      case 'VERIFICATION_STATUS':
        url = data.status === 'APPROVED' ? '/profile' : '/profile/verification';
        break;
      default:
        // Use webLink if provided, otherwise go to home
        url = data.webLink || '/';
    }
  }

  // Open the URL
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true })
      .then((windowClients) => {
        // Check if there is already a window/tab open with the target URL
        for (let i = 0; i < windowClients.length; i++) {
          const client = windowClients[i];
          // If so, focus it
          if (client.url.includes(url) && 'focus' in client) {
            return client.focus();
          }
        }
        
        // If not, open a new window/tab
        if (clients.openWindow) {
          return clients.openWindow(url);
        }
      })
  );
});

// Service worker installation
self.addEventListener('install', (event) => {
  console.log('[firebase-messaging-sw.js] Service worker installed');
  self.skipWaiting();
});

// Service worker activation
self.addEventListener('activate', (event) => {
  console.log('[firebase-messaging-sw.js] Service worker activated');
  return self.clients.claim();
});
