/**
 * Unauthorized Page
 * 
 * This page is displayed when a user tries to access a page they don't have permission for.
 */

import { useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { Box, Container, Typography, Button, Paper } from '@mui/material';
import { Warning as WarningIcon, Home as HomeIcon } from '@mui/icons-material';
import { useAuth } from '@/contexts/AuthContext';

export default function Unauthorized() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();

  // If user is not authenticated, redirect to login
  useEffect(() => {
    if (!isAuthenticated()) {
      router.push('/login');
    }
  }, [isAuthenticated, router]);

  // Determine appropriate home page based on user role
  const getHomePage = () => {
    if (!user) return '/';
    
    switch (user.role) {
      case 'ADMIN':
        return '/admin/dashboard';
      case 'MODERATOR':
        return '/admin/dashboard';
      default:
        return '/dashboard';
    }
  };

  return (
    <>
      <Head>
        <title>Unauthorized Access - Vaivahik</title>
        <meta name="description" content="You don't have permission to access this page" />
      </Head>

      <Container maxWidth="md" sx={{ py: 8 }}>
        <Paper 
          elevation={3} 
          sx={{ 
            p: 4, 
            textAlign: 'center',
            borderRadius: 2,
            border: '1px solid #f44336'
          }}
        >
          <Box sx={{ mb: 3 }}>
            <WarningIcon color="error" sx={{ fontSize: 64 }} />
          </Box>

          <Typography variant="h4" component="h1" gutterBottom color="error">
            Access Denied
          </Typography>

          <Typography variant="body1" paragraph>
            You don't have permission to access this page. This area may require higher privileges.
          </Typography>

          <Typography variant="body2" color="text.secondary" paragraph>
            If you believe this is an error, please contact the administrator.
          </Typography>

          <Box sx={{ mt: 4 }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<HomeIcon />}
              component={Link}
              href={getHomePage()}
            >
              Return to Home
            </Button>
          </Box>
        </Paper>
      </Container>
    </>
  );
}
