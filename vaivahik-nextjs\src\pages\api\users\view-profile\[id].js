/**
 * API Route: View User Profile
 * 
 * This endpoint handles viewing a specific user's profile by ID.
 * It supports both mock and real data based on feature flags.
 */

import { isUsingRealBackend } from '@/utils/featureFlags';

// Mock profile data for development
const MOCK_PROFILES = {
  '1': {
    id: '1',
    fullName: '<PERSON><PERSON>',
    age: 26,
    gender: 'FEMALE',
    city: 'Mumbai',
    state: 'Maharashtra',
    occupation: 'Software Engineer',
    education: 'B.Tech Computer Science',
    maritalStatus: 'NEVER_MARRIED',
    religion: 'Hindu',
    caste: '<PERSON><PERSON>',
    subCaste: 'Kunbi',
    height: 165,
    profilePicture: '/api/placeholder/400/400',
    photos: [
      '/api/placeholder/400/400',
      '/api/placeholder/400/400',
      '/api/placeholder/400/400'
    ],
    aboutMe: 'I am a software engineer working in Mumbai. I love traveling, reading books, and spending time with family. Looking for a life partner who shares similar values and interests.',
    interests: ['Reading', 'Traveling', 'Cooking', 'Music'],
    isVerified: true,
    isPremium: false,
    lastActive: '2024-01-15T10:30:00Z',
    compatibility: 94,
    isOnline: true,
    privacySettings: {
      showFullName: true,
      showContactInfo: false,
      showPhotos: true
    }
  },
  '2': {
    id: '2',
    fullName: 'Rahul Patil',
    age: 29,
    gender: 'MALE',
    city: 'Pune',
    state: 'Maharashtra',
    occupation: 'Business Analyst',
    education: 'MBA Finance',
    maritalStatus: 'NEVER_MARRIED',
    religion: 'Hindu',
    caste: 'Maratha',
    subCaste: 'Kunbi',
    height: 175,
    profilePicture: '/api/placeholder/400/400',
    photos: [
      '/api/placeholder/400/400',
      '/api/placeholder/400/400'
    ],
    aboutMe: 'Working as a business analyst in Pune. I enjoy sports, music, and spending time with friends and family. Looking for a caring and understanding life partner.',
    interests: ['Sports', 'Music', 'Travel', 'Photography'],
    isVerified: true,
    isPremium: true,
    lastActive: '2024-01-15T08:45:00Z',
    compatibility: 87,
    isOnline: false,
    privacySettings: {
      showFullName: true,
      showContactInfo: true,
      showPhotos: true
    }
  }
};

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    const { id } = req.query;

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'Profile ID is required'
      });
    }

    // Check authentication (basic check for demo)
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    if (isUsingRealBackend()) {
      // Call real backend API
      try {
        const backendUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:8000/api';
        const response = await fetch(`${backendUrl}/users/view-profile/${id}`, {
          method: 'GET',
          headers: {
            'Authorization': authHeader,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`Backend API error: ${response.status}`);
        }

        const data = await response.json();
        return res.status(200).json(data);
      } catch (error) {
        console.error('Real backend error, falling back to mock:', error);
        // Fall back to mock data if real backend fails
      }
    }

    // Use mock data
    const profile = MOCK_PROFILES[id];
    
    if (!profile) {
      return res.status(404).json({
        success: false,
        message: 'Profile not found'
      });
    }

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    return res.status(200).json({
      success: true,
      message: 'Profile fetched successfully',
      profile,
      source: 'mock'
    });

  } catch (error) {
    console.error('Profile view API error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
}
