// API endpoint for activating a feature
import { generateMockFeatures } from '@/utils/mockData';

export default function handler(req, res) {
  // Set proper content type header
  res.setHeader('Content-Type', 'application/json');

  try {
    // Only allow PUT method
    if (req.method !== 'PUT') {
      return res.status(405).json({
        success: false,
        message: 'Method not allowed'
      });
    }

    // Get the feature ID from the URL
    const { id } = req.query;

    // In a real implementation, this would update the database
    // For now, we'll just return a success response
    return res.status(200).json({
      success: true,
      message: 'Feature activated successfully',
      data: {
        id,
        isActive: true,
        activatedAt: new Date().toISOString(),
        activatedBy: 'Admin User'
      }
    });
  } catch (error) {
    console.error('Error activating feature:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to activate feature'
    });
  }
}
