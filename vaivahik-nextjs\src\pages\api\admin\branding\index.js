// Frontend API endpoint - delegates to backend
import axios from 'axios';
import multer from 'multer';
import { createRouter } from 'next-connect';
import path from 'path';
import fs from 'fs';

const BACKEND_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

// Configure multer for file uploads
const upload = multer({
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'branding');
      
      // Create directory if it doesn't exist
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }
      
      cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
      const ext = path.extname(file.originalname);
      cb(null, file.fieldname + '-' + uniqueSuffix + ext);
    }
  }),
  limits: {
    fileSize: 2 * 1024 * 1024, // 2MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept images
    const filetypes = /jpeg|jpg|png|gif|svg/;
    const mimetype = filetypes.test(file.mimetype);
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    
    if (mimetype && extname) {
      return cb(null, true);
    }
    
    cb(new Error('Only image files are allowed'));
  }
});

// Create a router
const router = createRouter();

// Middleware to check admin authentication
async function checkAdminAuth(req, res, next) {
  const session = await getServerSession(req, res, authOptions);
  
  if (!session || !session.user || session.user.role !== 'ADMIN') {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }
  
  next();
}

// Apply middleware
router.use(checkAdminAuth);

// Handle GET request to fetch branding settings
router.get(async (req, res) => {
  try {
    // Get the branding settings
    const branding = await prisma.appSettings.findFirst({
      where: {
        key: 'branding'
      }
    });
    
    if (!branding) {
      // Return default values if no branding settings exist
      return res.status(200).json({
        success: true,
        branding: {
          brandName: 'Vaivahik',
          brandTagline: 'Find your perfect match',
          brandLogo: '/logo.png'
        }
      });
    }
    
    // Parse the value from JSON
    const brandingData = JSON.parse(branding.value);
    
    return res.status(200).json({
      success: true,
      branding: brandingData
    });
  } catch (error) {
    console.error('Error fetching branding settings:', error);
    return res.status(500).json({ success: false, message: 'Failed to fetch branding settings' });
  }
});

// Handle POST request to update branding settings
router.post(upload.single('brandLogo'), async (req, res) => {
  try {
    const { brandName, brandTagline } = req.body;
    
    // Validate required fields
    if (!brandName) {
      return res.status(400).json({ success: false, message: 'Brand name is required' });
    }
    
    // Get existing branding settings
    const existingBranding = await prisma.appSettings.findFirst({
      where: {
        key: 'branding'
      }
    });
    
    // Parse existing data or use default
    let brandingData = existingBranding ? JSON.parse(existingBranding.value) : {
      brandName: 'Vaivahik',
      brandTagline: 'Find your perfect match',
      brandLogo: '/logo.png'
    };
    
    // Update with new values
    brandingData.brandName = brandName;
    brandingData.brandTagline = brandTagline || brandingData.brandTagline;
    
    // Update logo if a new one was uploaded
    if (req.file) {
      // Delete old logo if it exists and is not the default
      if (existingBranding && brandingData.brandLogo && 
          brandingData.brandLogo !== '/logo.png' && 
          brandingData.brandLogo.startsWith('/uploads/')) {
        const oldPath = path.join(process.cwd(), 'public', brandingData.brandLogo);
        if (fs.existsSync(oldPath)) {
          fs.unlinkSync(oldPath);
        }
      }
      
      // Set new logo path
      brandingData.brandLogo = `/uploads/branding/${req.file.filename}`;
    }
    
    // Save to database
    if (existingBranding) {
      await prisma.appSettings.update({
        where: {
          id: existingBranding.id
        },
        data: {
          value: JSON.stringify(brandingData),
          updatedAt: new Date()
        }
      });
    } else {
      await prisma.appSettings.create({
        data: {
          key: 'branding',
          value: JSON.stringify(brandingData),
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }
    
    return res.status(200).json({
      success: true,
      message: 'Branding settings updated successfully',
      branding: brandingData
    });
  } catch (error) {
    console.error('Error updating branding settings:', error);
    return res.status(500).json({ success: false, message: 'Failed to update branding settings' });
  }
});

// Export the router
export default router.handler({
  onError: (err, req, res) => {
    console.error(err.stack);
    res.status(500).json({ success: false, message: err.message });
  },
  onNoMatch: (req, res) => {
    res.status(405).json({ success: false, message: `Method '${req.method}' not allowed` });
  },
});

// Configure Next.js to handle file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};
