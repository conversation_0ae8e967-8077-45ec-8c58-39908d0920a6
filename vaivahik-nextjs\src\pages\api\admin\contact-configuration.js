/**
 * Contact Configuration API
 * Handles dynamic contact configuration management
 */

import { authenticateAdmin } from '@/utils/adminAuth';

// Mock contact configuration storage
let contactConfig = {
  primary: {
    businessEmail: '<EMAIL>',
    supportEmail: '<EMAIL>',
    businessPhone: '+91-XXXX-XXXXXX',
    supportPhone: '+91-XXXX-XXXXXX',
    whatsappBusiness: '+91-XXXX-XXXXXX',
    whatsappSupport: '+91-XXXX-XXXXXX'
  },
  hours: {
    support: {
      enabled: true,
      monday: { start: '09:00', end: '18:00', enabled: true },
      tuesday: { start: '09:00', end: '18:00', enabled: true },
      wednesday: { start: '09:00', end: '18:00', enabled: true },
      thursday: { start: '09:00', end: '18:00', enabled: true },
      friday: { start: '09:00', end: '18:00', enabled: true },
      saturday: { start: '09:00', end: '18:00', enabled: true },
      sunday: { start: '10:00', end: '16:00', enabled: false }
    }
  },
  autoResponses: {
    email: {
      enabled: true,
      subject: 'Thank you for contacting Vaivahik',
      message: 'We have received your message and will respond within 24 hours.'
    },
    whatsapp: {
      enabled: true,
      message: 'Hello! Thanks for contacting Vaivahik. Our team will assist you shortly.'
    }
  },
  integrations: {
    whatsappBusiness: {
      enabled: false,
      apiKey: '',
      phoneNumberId: '',
      accessToken: ''
    },
    emailService: {
      provider: 'brevo',
      apiKey: '',
      fromEmail: '<EMAIL>',
      fromName: 'Vaivahik Team'
    }
  }
};

export default async function handler(req, res) {
  try {
    // Authenticate admin
    const adminData = await authenticateAdmin(req);
    if (!adminData.success) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized access'
      });
    }

    switch (req.method) {
      case 'GET':
        return handleGet(req, res);
      case 'POST':
        return handlePost(req, res, adminData);
      default:
        return res.status(405).json({
          success: false,
          message: 'Method not allowed'
        });
    }
  } catch (error) {
    console.error('Contact configuration API error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}

async function handleGet(req, res) {
  try {
    return res.status(200).json({
      success: true,
      configuration: contactConfig
    });
  } catch (error) {
    console.error('Error fetching contact configuration:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch contact configuration'
    });
  }
}

async function handlePost(req, res, adminData) {
  try {
    const newConfig = req.body;

    if (!newConfig) {
      return res.status(400).json({
        success: false,
        message: 'Configuration data is required'
      });
    }

    // Update the configuration
    contactConfig = {
      ...contactConfig,
      ...newConfig,
      lastUpdated: new Date().toISOString(),
      updatedBy: adminData.admin.name
    };

    return res.status(200).json({
      success: true,
      message: 'Contact configuration updated successfully',
      configuration: contactConfig
    });
  } catch (error) {
    console.error('Error updating contact configuration:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update contact configuration'
    });
  }
}
