import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import axios from 'axios';
import { useRouter } from 'next/router';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);

// Dynamically import SwaggerUI to avoid SSR issues
const SwaggerUI = dynamic(
  () => import('swagger-ui-react').then((mod) => mod.default),
  { ssr: false }
);

export default function TestingTools() {
  const router = useRouter();

  // State for each testing tool
  const [adminFunctionalityResults, setAdminFunctionalityResults] = useState(null);
  const [apiEndpointResults, setApiEndpointResults] = useState(null);
  const [projectStructureResults, setProjectStructureResults] = useState(null);
  const [runningTest, setRunningTest] = useState(null);
  const [testOutput, setTestOutput] = useState('');

  // State for API tools
  const [activeTab, setActiveTab] = useState('testing'); // 'testing', 'viewer', 'discovery'
  const [authToken, setAuthToken] = useState('');
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [baseUrl, setBaseUrl] = useState('http://localhost:8000');
  const [endpoints, setEndpoints] = useState([]);
  const [categorizedEndpoints, setCategorizedEndpoints] = useState({});
  const [discovering, setDiscovering] = useState(false);
  const [selectedEndpoint, setSelectedEndpoint] = useState(null);
  const [selectedMethod, setSelectedMethod] = useState('GET');
  const [testPayload, setTestPayload] = useState('');
  const [testResult, setTestResult] = useState(null);
  const [testing, setTesting] = useState(false);

  // Load auth token from localStorage on component mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedToken = localStorage.getItem('api_auth_token');
      if (savedToken) {
        setAuthToken(savedToken);
      }
    }
  }, []);

  // Function to run admin functionality test
  const runAdminFunctionalityTest = async () => {
    setRunningTest('admin');
    setTestOutput('Starting Admin Functionality Test...\n');

    try {
      const response = await axios.post('/api/admin/testing/admin-functionality');
      setAdminFunctionalityResults(response.data);
      setTestOutput(prev => prev + JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.error('Error running admin functionality test:', error);
      setTestOutput(prev => prev + `\nError: ${error.message}`);
    } finally {
      setRunningTest(null);
    }
  };

  // Function to run API endpoint test
  const runApiEndpointTest = async () => {
    setRunningTest('api');
    setTestOutput('Starting API Endpoint Test...\n');

    try {
      const response = await axios.post('/api/admin/testing/api-endpoints');
      setApiEndpointResults(response.data);
      setTestOutput(prev => prev + JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.error('Error running API endpoint test:', error);
      setTestOutput(prev => prev + `\nError: ${error.message}`);
    } finally {
      setRunningTest(null);
    }
  };

  // Function to run project structure analysis
  const runProjectStructureAnalysis = async () => {
    setRunningTest('structure');
    setTestOutput('Starting Project Structure Analysis...\n');

    try {
      const response = await axios.post('/api/admin/testing/project-structure');
      setProjectStructureResults(response.data);
      setTestOutput(prev => prev + JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.error('Error running project structure analysis:', error);
      setTestOutput(prev => prev + `\nError: ${error.message}`);
    } finally {
      setRunningTest(null);
    }
  };

  // Function to set auth token
  const setApiAuthToken = () => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('api_auth_token', authToken);
    }
    setShowAuthModal(false);
  };

  // Function to clear auth token
  const clearAuth = () => {
    setAuthToken('');
    if (typeof window !== 'undefined') {
      localStorage.removeItem('api_auth_token');
    }
  };

  // Custom request interceptor for Swagger UI
  const requestInterceptor = (req) => {
    if (authToken) {
      req.headers.Authorization = `Bearer ${authToken}`;
    }
    return req;
  };

  // Function to discover API endpoints
  const discoverEndpoints = async () => {
    setDiscovering(true);
    try {
      const response = await fetch(`/api/api-discovery?baseUrl=${encodeURIComponent(baseUrl)}`);
      const data = await response.json();

      if (data.success) {
        setEndpoints(data.endpoints || []);
        setCategorizedEndpoints(data.categorizedEndpoints || {});
      } else {
        alert(`Error: ${data.message}`);
      }
    } catch (error) {
      console.error('Error discovering endpoints:', error);
      alert(`Error discovering endpoints: ${error.message}`);
    } finally {
      setDiscovering(false);
    }
  };

  // Function to test an endpoint
  const testEndpoint = async () => {
    if (!selectedEndpoint) return;

    setTesting(true);
    setTestResult(null);

    try {
      // Prepare headers
      const headers = {
        'Content-Type': 'application/json'
      };

      // Add auth token if available
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
      }

      // Parse payload if provided
      let parsedPayload = null;
      if (testPayload) {
        try {
          parsedPayload = JSON.parse(testPayload);
        } catch (e) {
          alert('Invalid JSON payload. Please check your syntax.');
          setTesting(false);
          return;
        }
      }

      // Make the request to the API discovery endpoint
      const response = await fetch('/api/api-discovery', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          baseUrl,
          path: selectedEndpoint.path,
          method: selectedMethod,
          payload: parsedPayload
        })
      });

      const data = await response.json();

      if (data.success) {
        setTestResult(data.result);
      } else {
        alert(`Error: ${data.message}`);
      }
    } catch (error) {
      console.error('Error testing endpoint:', error);
      alert(`Error testing endpoint: ${error.message}`);
    } finally {
      setTesting(false);
    }
  };

  return (
    <EnhancedAdminLayout title="Testing Tools">
      <div className="admin-card">
        <div className="admin-card-header">
          <h2>Testing Tools Dashboard</h2>
          <p>Run automated tests and analyze your application from this dashboard.</p>

          <div className="tabs-container">
            <div
              className={`tab ${activeTab === 'testing' ? 'active' : ''}`}
              onClick={() => setActiveTab('testing')}
            >
              Automated Testing
            </div>
            <div
              className={`tab ${activeTab === 'viewer' ? 'active' : ''}`}
              onClick={() => setActiveTab('viewer')}
            >
              API Viewer
            </div>
            <div
              className={`tab ${activeTab === 'discovery' ? 'active' : ''}`}
              onClick={() => setActiveTab('discovery')}
            >
              API Discovery
            </div>
          </div>
        </div>

        {/* Auth Token Modal */}
        {showAuthModal && (
          <div className="modal-overlay">
            <div className="modal">
              <div className="modal-header">
                <h3>Set API Authentication Token</h3>
                <button className="modal-close" onClick={() => setShowAuthModal(false)}>×</button>
              </div>
              <div className="modal-body">
                <p>Enter your API authentication token below:</p>
                <input
                  type="text"
                  className="auth-input"
                  value={authToken}
                  onChange={(e) => setAuthToken(e.target.value)}
                  placeholder="Bearer token"
                />
                <p className="modal-note">
                  This token will be stored in your browser's localStorage and used for all API requests.
                </p>
              </div>
              <div className="modal-footer">
                <button className="cancel-btn" onClick={() => setShowAuthModal(false)}>Cancel</button>
                <button className="save-btn" onClick={setApiAuthToken}>Save Token</button>
              </div>
            </div>
          </div>
        )}

        {/* Automated Testing Tab */}
        {activeTab === 'testing' && (
          <>
            <div className="testing-tools-grid">
              {/* Admin Functionality Testing */}
              <div className="tool-card">
                <div className="tool-header">
                  <h3>Admin Functionality Testing</h3>
                  <div className="tool-icon">🖥️</div>
                </div>
                <p className="tool-description">
                  Automatically tests admin pages by taking screenshots and verifying content.
                </p>
                <div className="tool-actions">
                  <button
                    className="run-test-btn"
                    onClick={runAdminFunctionalityTest}
                    disabled={runningTest !== null}
                  >
                    {runningTest === 'admin' ? 'Running...' : 'Run Test'}
                  </button>
                  <button className="view-docs-btn" onClick={() => setActiveTab('viewer')}>
                    View API Docs
                  </button>
                </div>
                {adminFunctionalityResults && (
                  <div className="test-results">
                    <h4>Test Results</h4>
                    <div className="results-summary">
                      <div className="result-item">
                        <span className="result-label">Total Tests:</span>
                        <span className="result-value">{adminFunctionalityResults.total || 0}</span>
                      </div>
                      <div className="result-item">
                        <span className="result-label">Passed:</span>
                        <span className="result-value passed">{adminFunctionalityResults.passed || 0}</span>
                      </div>
                      <div className="result-item">
                        <span className="result-label">Failed:</span>
                        <span className="result-value failed">{adminFunctionalityResults.failed || 0}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* API Endpoint Testing */}
              <div className="tool-card">
                <div className="tool-header">
                  <h3>API Endpoint Testing</h3>
                  <div className="tool-icon">🔌</div>
                </div>
                <p className="tool-description">
                  Tests all API endpoints by making HTTP requests and verifying responses.
                </p>
                <div className="tool-actions">
                  <button
                    className="run-test-btn"
                    onClick={runApiEndpointTest}
                    disabled={runningTest !== null}
                  >
                    {runningTest === 'api' ? 'Running...' : 'Run Test'}
                  </button>
                  <button className="view-docs-btn" onClick={() => setActiveTab('discovery')}>
                    API Discovery
                  </button>
                </div>
                {apiEndpointResults && (
                  <div className="test-results">
                    <h4>Test Results</h4>
                    <div className="results-summary">
                      <div className="result-item">
                        <span className="result-label">Total Endpoints:</span>
                        <span className="result-value">{apiEndpointResults.total || 0}</span>
                      </div>
                      <div className="result-item">
                        <span className="result-label">Passed:</span>
                        <span className="result-value passed">{apiEndpointResults.passed || 0}</span>
                      </div>
                      <div className="result-item">
                        <span className="result-label">Failed:</span>
                        <span className="result-value failed">{apiEndpointResults.failed || 0}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Project Structure Analysis */}
              <div className="tool-card">
                <div className="tool-header">
                  <h3>Project Structure Analysis</h3>
                  <div className="tool-icon">🔍</div>
                </div>
                <p className="tool-description">
                  Analyzes your codebase for duplicates, naming issues, and unused files.
                </p>
                <div className="tool-actions">
                  <button
                    className="run-test-btn"
                    onClick={runProjectStructureAnalysis}
                    disabled={runningTest !== null}
                  >
                    {runningTest === 'structure' ? 'Running...' : 'Run Analysis'}
                  </button>
                  <a href="/vaivahik-project-test-checklist.md" className="view-docs-btn" target="_blank" rel="noopener noreferrer">
                    Testing Checklist
                  </a>
                </div>
                {projectStructureResults && (
                  <div className="test-results">
                    <h4>Analysis Results</h4>
                    <div className="results-summary">
                      <div className="result-item">
                        <span className="result-label">Total Files:</span>
                        <span className="result-value">{projectStructureResults.totalFiles || 0}</span>
                      </div>
                      <div className="result-item">
                        <span className="result-label">Duplicates:</span>
                        <span className="result-value warning">{projectStructureResults.duplicates?.length || 0}</span>
                      </div>
                      <div className="result-item">
                        <span className="result-label">Naming Issues:</span>
                        <span className="result-value warning">{projectStructureResults.namingIssues?.length || 0}</span>
                      </div>
                      <div className="result-item">
                        <span className="result-label">Unused Files:</span>
                        <span className="result-value warning">{projectStructureResults.potentialUnused?.length || 0}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Test Output Console */}
            <div className="test-output-section">
              <h3>Test Output</h3>
              <div className="test-console">
                <pre>{testOutput || 'Run a test to see output here...'}</pre>
              </div>
            </div>
          </>
        )}

        {/* API Viewer Tab */}
        {activeTab === 'viewer' && (
          <div className="api-viewer-container">
            <div className="api-viewer-header">
              <h3>API Documentation</h3>
              <div className="api-viewer-actions">
                <button
                  className="auth-button"
                  onClick={() => setShowAuthModal(true)}
                >
                  {authToken ? 'Update Auth Token' : 'Set Auth Token'}
                </button>

                {authToken && (
                  <button
                    className="clear-auth-button"
                    onClick={clearAuth}
                  >
                    Clear Auth Token
                  </button>
                )}
              </div>
            </div>

            <div className="swagger-container">
              <SwaggerUI
                url="/api/openapi"
                docExpansion="list"
                defaultModelsExpandDepth={1}
                requestInterceptor={requestInterceptor}
                onComplete={() => console.log('Swagger UI loaded')}
              />
            </div>
          </div>
        )}

        {/* API Discovery Tab */}
        {activeTab === 'discovery' && (
          <div className="api-discovery-container">
            <div className="api-discovery-header">
              <h3>API Discovery Tool</h3>
              <div className="api-discovery-actions">
                <button
                  className="auth-button"
                  onClick={() => setShowAuthModal(true)}
                >
                  {authToken ? 'Update Auth Token' : 'Set Auth Token'}
                </button>

                {authToken && (
                  <button
                    className="clear-auth-button"
                    onClick={clearAuth}
                  >
                    Clear Auth Token
                  </button>
                )}
              </div>
            </div>

            <div className="discovery-content">
              <div className="discovery-form">
                <div className="form-group">
                  <label htmlFor="baseUrl">API Base URL:</label>
                  <div className="input-with-button">
                    <input
                      type="text"
                      id="baseUrl"
                      value={baseUrl}
                      onChange={(e) => setBaseUrl(e.target.value)}
                      placeholder="http://localhost:8000"
                    />
                    <button
                      className="discover-btn"
                      onClick={discoverEndpoints}
                      disabled={discovering}
                    >
                      {discovering ? 'Discovering...' : 'Discover Endpoints'}
                    </button>
                  </div>
                </div>

                {endpoints.length > 0 && (
                  <div className="endpoints-container">
                    <div className="endpoints-list">
                      <h4>Discovered Endpoints</h4>
                      <div className="endpoints-scroll">
                        {endpoints.map((endpoint, index) => (
                          <div
                            key={index}
                            className={`endpoint-item ${selectedEndpoint === endpoint ? 'selected' : ''}`}
                            onClick={() => setSelectedEndpoint(endpoint)}
                          >
                            <div className="endpoint-path">{endpoint.path}</div>
                            <div className="endpoint-methods">
                              {endpoint.methods?.map(method => (
                                <span key={method} className={`method-badge ${method.toLowerCase()}`}>
                                  {method}
                                </span>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {selectedEndpoint && (
                      <div className="endpoint-details">
                        <h4>Test Endpoint</h4>
                        <div className="endpoint-info">
                          <div className="endpoint-url">{selectedEndpoint.path}</div>
                          <div className="endpoint-description">{selectedEndpoint.description}</div>
                        </div>

                        <div className="test-form">
                          <div className="form-group">
                            <label>Method:</label>
                            <select
                              value={selectedMethod}
                              onChange={(e) => setSelectedMethod(e.target.value)}
                            >
                              {selectedEndpoint.methods?.map(method => (
                                <option key={method} value={method}>{method}</option>
                              ))}
                            </select>
                          </div>

                          {(selectedMethod === 'POST' || selectedMethod === 'PUT') && (
                            <div className="form-group">
                              <label>Request Payload (JSON):</label>
                              <textarea
                                value={testPayload}
                                onChange={(e) => setTestPayload(e.target.value)}
                                placeholder='{"key": "value"}'
                                rows={5}
                              />
                            </div>
                          )}

                          <button
                            className="test-endpoint-btn"
                            onClick={testEndpoint}
                            disabled={testing}
                          >
                            {testing ? 'Testing...' : 'Test Endpoint'}
                          </button>
                        </div>

                        {testResult && (
                          <div className="test-result-container">
                            <h4>Test Result</h4>
                            <div className="result-header">
                              <div className={`status-badge ${testResult.status >= 200 && testResult.status < 300 ? 'success' : 'error'}`}>
                                {testResult.status} {testResult.statusText}
                              </div>
                              <div className="timing">{testResult.timing}ms</div>
                            </div>
                            <div className="result-body">
                              <div className="result-section">
                                <h5>Response Headers</h5>
                                <pre>{JSON.stringify(testResult.headers, null, 2)}</pre>
                              </div>
                              <div className="result-section">
                                <h5>Response Body</h5>
                                <pre>{JSON.stringify(testResult.data, null, 2)}</pre>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        /* Tabs Styling */
        .tabs-container {
          display: flex;
          margin-top: 20px;
          border-bottom: 1px solid #ddd;
        }

        .tab {
          padding: 10px 20px;
          cursor: pointer;
          border-bottom: 3px solid transparent;
          font-weight: 500;
          transition: all 0.2s ease;
        }

        .tab:hover {
          background-color: #f5f5f5;
        }

        .tab.active {
          border-bottom-color: #5e35b1;
          color: #5e35b1;
        }

        /* Modal Styling */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .modal {
          background-color: white;
          border-radius: 8px;
          width: 90%;
          max-width: 500px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 15px 20px;
          border-bottom: 1px solid #eee;
        }

        .modal-header h3 {
          margin: 0;
          font-size: 1.2rem;
        }

        .modal-close {
          background: none;
          border: none;
          font-size: 1.5rem;
          cursor: pointer;
          color: #666;
        }

        .modal-body {
          padding: 20px;
        }

        .modal-footer {
          padding: 15px 20px;
          border-top: 1px solid #eee;
          display: flex;
          justify-content: flex-end;
          gap: 10px;
        }

        .auth-input {
          width: 100%;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 1rem;
        }

        .modal-note {
          font-size: 0.8rem;
          color: #666;
          margin-top: 10px;
        }

        .cancel-btn, .save-btn {
          padding: 8px 15px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 0.9rem;
        }

        .cancel-btn {
          background-color: #f5f5f5;
          border: 1px solid #ddd;
          color: #333;
        }

        .save-btn {
          background-color: #5e35b1;
          border: none;
          color: white;
        }

        /* Testing Tools Grid */
        .testing-tools-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 20px;
          margin-bottom: 30px;
        }

        .tool-card {
          background-color: white;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          padding: 20px;
          transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .tool-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .tool-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;
        }

        .tool-header h3 {
          margin: 0;
          color: #333;
          font-size: 1.2rem;
        }

        .tool-icon {
          font-size: 1.8rem;
          background-color: #f5f5f5;
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
        }

        .tool-description {
          color: #666;
          margin-bottom: 20px;
          min-height: 40px;
        }

        .tool-actions {
          display: flex;
          gap: 10px;
          margin-bottom: 15px;
        }

        .run-test-btn, .view-docs-btn {
          padding: 8px 15px;
          border-radius: 4px;
          font-size: 0.9rem;
          cursor: pointer;
          text-align: center;
          text-decoration: none;
          display: inline-block;
        }

        .run-test-btn {
          background-color: #5e35b1;
          color: white;
          border: none;
          flex: 1;
        }

        .run-test-btn:hover {
          background-color: #4527a0;
        }

        .run-test-btn:disabled {
          background-color: #9e9e9e;
          cursor: not-allowed;
        }

        .view-docs-btn {
          background-color: #f5f5f5;
          color: #333;
          border: 1px solid #ddd;
          flex: 1;
        }

        .view-docs-btn:hover {
          background-color: #e0e0e0;
        }

        .test-results {
          background-color: #f9f9f9;
          border-radius: 4px;
          padding: 15px;
        }

        .test-results h4 {
          margin-top: 0;
          margin-bottom: 10px;
          font-size: 1rem;
          color: #333;
        }

        .results-summary {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
          gap: 10px;
        }

        .result-item {
          display: flex;
          flex-direction: column;
        }

        .result-label {
          font-size: 0.8rem;
          color: #666;
        }

        .result-value {
          font-size: 1.2rem;
          font-weight: 600;
          color: #333;
        }

        .result-value.passed {
          color: #2e7d32;
        }

        .result-value.failed, .result-value.warning {
          color: #c62828;
        }

        .result-value.warning {
          color: #f57c00;
        }

        .test-output-section {
          margin-top: 30px;
        }

        .test-console {
          background-color: #1e1e1e;
          color: #f8f8f8;
          border-radius: 8px;
          padding: 15px;
          max-height: 300px;
          overflow-y: auto;
          font-family: monospace;
        }

        .test-console pre {
          margin: 0;
          white-space: pre-wrap;
          word-wrap: break-word;
        }

        /* API Viewer Styling */
        .api-viewer-container {
          margin-top: 20px;
        }

        .api-viewer-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
        }

        .api-viewer-header h3 {
          margin: 0;
          font-size: 1.2rem;
        }

        .api-viewer-actions {
          display: flex;
          gap: 10px;
        }

        .auth-button, .clear-auth-button {
          padding: 8px 15px;
          border-radius: 4px;
          font-size: 0.9rem;
          cursor: pointer;
        }

        .auth-button {
          background-color: #5e35b1;
          color: white;
          border: none;
        }

        .clear-auth-button {
          background-color: #f5f5f5;
          color: #333;
          border: 1px solid #ddd;
        }

        .swagger-container {
          border: 1px solid #ddd;
          border-radius: 8px;
          overflow: hidden;
          min-height: 600px;
        }

        /* API Discovery Styling */
        .api-discovery-container {
          margin-top: 20px;
        }

        .api-discovery-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
        }

        .api-discovery-header h3 {
          margin: 0;
          font-size: 1.2rem;
        }

        .api-discovery-actions {
          display: flex;
          gap: 10px;
        }

        .discovery-form {
          margin-bottom: 20px;
        }

        .form-group {
          margin-bottom: 15px;
        }

        .form-group label {
          display: block;
          margin-bottom: 5px;
          font-weight: 500;
        }

        .input-with-button {
          display: flex;
          gap: 10px;
        }

        .input-with-button input {
          flex: 1;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 0.9rem;
        }

        .discover-btn {
          background-color: #5e35b1;
          color: white;
          border: none;
          border-radius: 4px;
          padding: 0 15px;
          cursor: pointer;
        }

        .discover-btn:disabled {
          background-color: #9e9e9e;
          cursor: not-allowed;
        }

        .endpoints-container {
          display: grid;
          grid-template-columns: 1fr 2fr;
          gap: 20px;
          margin-top: 20px;
        }

        .endpoints-list {
          background-color: #f9f9f9;
          border-radius: 8px;
          padding: 15px;
        }

        .endpoints-list h4 {
          margin-top: 0;
          margin-bottom: 15px;
        }

        .endpoints-scroll {
          max-height: 400px;
          overflow-y: auto;
        }

        .endpoint-item {
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 5px;
          cursor: pointer;
          transition: background-color 0.2s ease;
        }

        .endpoint-item:hover {
          background-color: #f0f0f0;
        }

        .endpoint-item.selected {
          background-color: #e8eaf6;
          border-left: 3px solid #5e35b1;
        }

        .endpoint-path {
          font-size: 0.9rem;
          font-weight: 500;
          margin-bottom: 5px;
          word-break: break-all;
        }

        .endpoint-methods {
          display: flex;
          gap: 5px;
        }

        .method-badge {
          font-size: 0.7rem;
          padding: 2px 6px;
          border-radius: 3px;
          font-weight: 600;
        }

        .method-badge.get {
          background-color: #e3f2fd;
          color: #1565c0;
        }

        .method-badge.post {
          background-color: #e8f5e9;
          color: #2e7d32;
        }

        .method-badge.put {
          background-color: #fff8e1;
          color: #f57f17;
        }

        .method-badge.delete {
          background-color: #ffebee;
          color: #c62828;
        }

        .endpoint-details {
          background-color: white;
          border-radius: 8px;
          padding: 20px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .endpoint-details h4 {
          margin-top: 0;
          margin-bottom: 15px;
        }

        .endpoint-info {
          margin-bottom: 20px;
        }

        .endpoint-url {
          font-family: monospace;
          background-color: #f5f5f5;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 10px;
          word-break: break-all;
        }

        .endpoint-description {
          color: #666;
          font-size: 0.9rem;
        }

        .test-form {
          margin-bottom: 20px;
        }

        .test-form select {
          width: 100%;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 0.9rem;
        }

        .test-form textarea {
          width: 100%;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 0.9rem;
          font-family: monospace;
        }

        .test-endpoint-btn {
          background-color: #5e35b1;
          color: white;
          border: none;
          border-radius: 4px;
          padding: 10px 15px;
          cursor: pointer;
          width: 100%;
          margin-top: 10px;
        }

        .test-endpoint-btn:disabled {
          background-color: #9e9e9e;
          cursor: not-allowed;
        }

        .test-result-container {
          margin-top: 20px;
          background-color: #f9f9f9;
          border-radius: 8px;
          padding: 15px;
        }

        .test-result-container h4 {
          margin-top: 0;
          margin-bottom: 15px;
        }

        .result-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;
        }

        .status-badge {
          padding: 5px 10px;
          border-radius: 4px;
          font-weight: 500;
          font-size: 0.9rem;
        }

        .status-badge.success {
          background-color: #e8f5e9;
          color: #2e7d32;
        }

        .status-badge.error {
          background-color: #ffebee;
          color: #c62828;
        }

        .timing {
          font-size: 0.9rem;
          color: #666;
        }

        .result-body {
          display: flex;
          flex-direction: column;
          gap: 15px;
        }

        .result-section h5 {
          margin-top: 0;
          margin-bottom: 10px;
          font-size: 0.9rem;
          color: #333;
        }

        .result-section pre {
          background-color: #f0f0f0;
          padding: 10px;
          border-radius: 4px;
          font-family: monospace;
          font-size: 0.9rem;
          overflow-x: auto;
          margin: 0;
        }

        @media (max-width: 768px) {
          .testing-tools-grid {
            grid-template-columns: 1fr;
          }

          .endpoints-container {
            grid-template-columns: 1fr;
          }

          .tabs-container {
            flex-direction: column;
          }

          .tab {
            text-align: center;
          }
        }
      `}</style>
    </EnhancedAdminLayout>
  );
}
