/* Verification Queue Page Specific Styles */

/* Verification Details Modal */
.verification-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.user-profile-section {
  margin-bottom: 20px;
}

.user-profile-header {
  display: flex;
  align-items: center;
  gap: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.user-avatar.large {
  width: 80px;
  height: 80px;
  font-size: 2rem;
  background-color: #7c4dff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.user-avatar.large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.user-profile-info {
  flex: 1;
}

.user-profile-info h3 {
  margin: 0 0 5px 0;
  font-size: 1.3rem;
  color: #333;
}

.user-meta {
  display: flex;
  gap: 8px;
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 8px;
}

.verification-status {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 5px;
}

.status-badge {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-badge.pending {
  background-color: #fff3e0;
  color: #e65100;
  border-left: 3px solid #ff9800;
}

.status-badge.approved {
  background-color: #e8f5e9;
  color: #2e7d32;
  border-left: 3px solid #4caf50;
}

.status-badge.rejected {
  background-color: #ffebee;
  color: #c62828;
  border-left: 3px solid #f44336;
}

/* Tab Content Styles */
.verification-tabs {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tab-content {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.tab-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.user-details-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.detail-item {
  margin-bottom: 10px;
}

.detail-label {
  font-weight: 500;
  color: #666;
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.detail-value {
  color: #333;
}

/* Documents Grid */
.documents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 15px;
}

.document-card {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.document-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.document-preview {
  height: 140px;
  overflow: hidden;
}

.document-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.document-info {
  padding: 10px;
  background-color: #f9f9f9;
}

.document-type {
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.document-name {
  font-size: 0.8rem;
  color: #666;
}

.no-documents {
  grid-column: 1 / -1;
  padding: 15px;
  text-align: center;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 8px;
}

/* Action Section */
.action-section {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.action-buttons-container {
  display: flex;
  justify-content: center;
  gap: 20px;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .user-details-grid {
    grid-template-columns: 1fr;
  }
  
  .user-profile-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .documents-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  }
  
  .action-buttons-container {
    flex-direction: column;
  }
}
