/**
 * Modern Profile Dashboard Page
 *
 * This page displays the modern profile completion dashboard with gamification elements.
 * It guides users through completing their profile after registration.
 */

import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Container,
  Box,
  Alert,
  CircularProgress,
  Typography,
  Paper
} from '@mui/material';
import ModernProfileCompletionDashboard from '@/components/profile/ModernProfileCompletionDashboard';
import { useAuth } from '@/contexts/AuthContext';
import { isUsingRealBackend } from '@/utils/apiUtils';
import api from '@/services/api';

const ModernProfileDashboardPage = () => {
  const router = useRouter();
  const { userData, setUserData } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Fetch user data if not available in context
  useEffect(() => {
    const fetchUserData = async () => {
      if (!userData) {
        try {
          const response = await api.get('/api/users/profile');
          setUserData(response.data);
        } catch (err) {
          setError('Failed to load user data. Please try again later.');
        }
      }
      setLoading(false);
    };

    fetchUserData();
  }, [userData, setUserData]);

  // Handle section update
  const handleUpdateSection = (sectionId) => {
    // Navigate to the appropriate edit page based on section ID
    switch (sectionId) {
      case 'basic':
        router.push('/profile/edit/basic');
        break;
      case 'photos':
        router.push('/profile/edit/photos');
        break;
      case 'family':
        router.push('/website/pages/profile/edit/modern-family');
        break;
      case 'education':
        router.push('/profile/edit/education');
        break;
      case 'location':
        router.push('/profile/edit/location');
        break;
      case 'preferences':
        router.push('/website/pages/profile/edit/modern-preferences');
        break;
      case 'lifestyle':
        router.push('/website/pages/profile/edit/lifestyle');
        break;
      case 'about':
        router.push('/profile/edit/about');
        break;
      default:
        router.push('/profile/edit/basic');
    }
  };

  return (
    <>
      <Head>
        <title>Complete Your Profile | Vaivahik</title>
        <meta name="description" content="Complete your profile on Vaivahik matrimony to find your perfect match" />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
            <CircularProgress />
          </Box>
        ) : (
          <ModernProfileCompletionDashboard
            userData={userData}
            onUpdateSection={handleUpdateSection}
          />
        )}
      </Container>
    </>
  );
};

export default ModernProfileDashboardPage;
