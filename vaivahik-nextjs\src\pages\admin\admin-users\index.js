import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);
import { adminGet, adminDelete } from '@/services/apiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';
import { toast } from 'react-toastify';
import {
  Box,
  Button,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
  Avatar
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Lock as LockIcon,
  LockOpen as LockOpenIcon
} from '@mui/icons-material';
import AdminUserDialog from '@/components/admin/AdminUserDialog';

export default function AdminUsers() {
  const [adminUsers, setAdminUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  const [search, setSearch] = useState('');
  const [role, setRole] = useState('');
  const [roles, setRoles] = useState([]);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);
  const [userDialogOpen, setUserDialogOpen] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [dialogMode, setDialogMode] = useState('create');

  useEffect(() => {
    fetchAdminUsers();
  }, [page, rowsPerPage, search, role]);

  const fetchAdminUsers = async () => {
    setLoading(true);
    try {
      const response = await adminGet(ADMIN_ENDPOINTS.ADMIN_USERS, {
        page: page + 1, // API uses 1-based indexing
        limit: rowsPerPage,
        search,
        role
      });

      if (response.success) {
        setAdminUsers(response.adminUsers);
        setTotal(response.pagination.total);
        setRoles(response.roles);
      } else {
        toast.error(response.message || 'Failed to fetch admin users');
      }
    } catch (error) {
      console.error('Error fetching admin users:', error);
      toast.error('Error fetching admin users');
    } finally {
      setLoading(false);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event) => {
    setSearch(event.target.value);
  };

  const handleRoleChange = (event) => {
    setRole(event.target.value);
  };

  const handleSearchSubmit = (event) => {
    event.preventDefault();
    setPage(0); // Reset to first page when searching
    fetchAdminUsers();
  };

  const handleAddUser = () => {
    setCurrentUser(null);
    setDialogMode('create');
    setUserDialogOpen(true);
  };

  const handleEditUser = (user) => {
    setCurrentUser(user);
    setDialogMode('edit');
    setUserDialogOpen(true);
  };

  const handleViewUser = (user) => {
    setCurrentUser(user);
    setDialogMode('view');
    setUserDialogOpen(true);
  };

  const handleDeleteClick = (user) => {
    setUserToDelete(user);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      const response = await adminDelete(ADMIN_ENDPOINTS.ADMIN_USER_DETAILS(userToDelete.id));

      if (response.success) {
        toast.success('Admin user deleted successfully');
        fetchAdminUsers(); // Refresh the list
      } else {
        toast.error(response.message || 'Failed to delete admin user');
      }
    } catch (error) {
      console.error('Error deleting admin user:', error);
      toast.error('Error deleting admin user');
    } finally {
      setDeleteDialogOpen(false);
      setUserToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setUserToDelete(null);
  };

  const handleUserDialogClose = () => {
    setUserDialogOpen(false);
    setCurrentUser(null);
  };

  const handleUserSave = () => {
    fetchAdminUsers(); // Refresh the list after save
    setUserDialogOpen(false);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <EnhancedAdminLayout title="Admin Users">
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Admin Users
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleAddUser}
          >
            Add Admin User
          </Button>
        </Box>

        {/* Filters */}
        <Box sx={{ mb: 3, p: { xs: 1.5, sm: 2 }, bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>
          <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, flexWrap: 'wrap', gap: { xs: 1, sm: 2 } }}>
            <form onSubmit={handleSearchSubmit} style={{ flex: 1, minWidth: '100%', maxWidth: { sm: '60%' } }}>
              <TextField
                fullWidth
                label="Search Admin Users"
                value={search}
                onChange={handleSearchChange}
                size="small"
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton type="submit" edge="end" size="small">
                        <SearchIcon />
                      </IconButton>
                    </InputAdornment>
                  )
                }}
              />
            </form>
            <FormControl sx={{ minWidth: { xs: '100%', sm: '200px' } }} size="small">
              <InputLabel>Role</InputLabel>
              <Select
                value={role}
                onChange={handleRoleChange}
                label="Role"
              >
                <MenuItem value="">All Roles</MenuItem>
                {roles.map((roleOption) => (
                  <MenuItem key={roleOption} value={roleOption}>{roleOption.replace('_', ' ')}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </Box>

        {/* Admin Users Table */}
        <TableContainer component={Paper} className="responsive-table">
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell sx={{ display: { xs: 'none', md: 'table-cell' } }}>Email</TableCell>
                <TableCell>Role</TableCell>
                <TableCell sx={{ display: { xs: 'none', sm: 'table-cell' } }}>Status</TableCell>
                <TableCell sx={{ display: { xs: 'none', lg: 'table-cell' } }}>Last Login</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                      <div className="loading-spinner"></div>
                    </Box>
                  </TableCell>
                </TableRow>
              ) : adminUsers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                    <Typography variant="body1">No admin users found</Typography>
                  </TableCell>
                </TableRow>
              ) : (
                adminUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{
                          mr: 1,
                          width: { xs: 30, sm: 40 },
                          height: { xs: 30, sm: 40 },
                          fontSize: { xs: '0.8rem', sm: '1rem' },
                          bgcolor: user.status === 'active' ? 'primary.main' : 'grey.500'
                        }}>
                          {user.name.charAt(0)}
                        </Avatar>
                        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>{user.name}</Typography>
                          <Typography
                            variant="caption"
                            sx={{
                              display: { xs: 'block', md: 'none' },
                              color: 'text.secondary'
                            }}
                          >
                            {user.email}
                          </Typography>
                          <Chip
                            label={user.status}
                            color={user.status === 'active' ? 'success' : 'default'}
                            size="small"
                            sx={{
                              display: { xs: 'flex', sm: 'none' },
                              height: 20,
                              '& .MuiChip-label': { px: 1, fontSize: '0.625rem' },
                              mt: 0.5,
                              width: 'fit-content'
                            }}
                          />
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell sx={{ display: { xs: 'none', md: 'table-cell' } }}>{user.email}</TableCell>
                    <TableCell>
                      <Chip
                        label={user.role.replace('_', ' ')}
                        color={user.role === 'SUPER_ADMIN' ? 'secondary' : 'primary'}
                        variant={user.role === 'SUPER_ADMIN' ? 'filled' : 'outlined'}
                        size="small"
                        sx={{
                          '& .MuiChip-label': {
                            fontSize: { xs: '0.625rem', sm: '0.75rem' },
                            px: { xs: 1, sm: 1.5 }
                          }
                        }}
                      />
                    </TableCell>
                    <TableCell sx={{ display: { xs: 'none', sm: 'table-cell' } }}>
                      <Chip
                        label={user.status}
                        color={user.status === 'active' ? 'success' : 'default'}
                        icon={user.status === 'active' ? <LockOpenIcon fontSize="small" /> : <LockIcon fontSize="small" />}
                        size="small"
                      />
                    </TableCell>
                    <TableCell sx={{ display: { xs: 'none', lg: 'table-cell' } }}>{formatDate(user.lastLogin)}</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: { xs: 0, sm: 1 } }}>
                        <IconButton onClick={() => handleViewUser(user)} color="primary" size="small">
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                        <IconButton onClick={() => handleEditUser(user)} color="primary" size="small">
                          <EditIcon fontSize="small" />
                        </IconButton>
                        <IconButton
                          onClick={() => handleDeleteClick(user)}
                          color="error"
                          size="small"
                          disabled={user.role === 'SUPER_ADMIN'} // Prevent deleting super admin
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={total}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            labelRowsPerPage={<Typography sx={{ display: { xs: 'none', sm: 'block' } }}>Rows per page:</Typography>}
            labelDisplayedRows={({ from, to, count }) => (
              <Typography variant="body2">{from}-{to} of {count}</Typography>
            )}
          />
        </TableContainer>

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={deleteDialogOpen}
          onClose={handleDeleteCancel}
        >
          <DialogTitle>Delete Admin User</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete the admin user "{userToDelete?.name}"? This action cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleDeleteCancel}>Cancel</Button>
            <Button onClick={handleDeleteConfirm} color="error" autoFocus>
              Delete
            </Button>
          </DialogActions>
        </Dialog>

        {/* Admin User Dialog */}
        <AdminUserDialog
          open={userDialogOpen}
          onClose={handleUserDialogClose}
          onSave={handleUserSave}
          user={currentUser}
          mode={dialogMode}
          roles={roles}
        />
      </Box>
    </EnhancedAdminLayout>
  );
}
