"""
Matching Service

This module provides services for matching users in the matrimony app.
It uses different matching algorithms, including the two-tower model.
"""

import os
import json
import logging
from datetime import datetime
import numpy as np
from .two_tower_model_pytorch import MatrimonyMatchingModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MatchingService:
    """Service for matching users in the matrimony app"""

    def __init__(self, prisma_client, settings=None):
        """
        Initialize the matching service

        Args:
            prisma_client: Prisma client for database access
            settings (dict): Settings for the matching service
        """
        self.prisma = prisma_client
        self.settings = settings or {}
        self.models = {}

        # Load default settings if not provided
        if not self.settings:
            self._load_default_settings()

        # Initialize models
        self._initialize_models()

    async def _load_default_settings(self):
        """Load default settings from the database"""
        try:
            # Get all algorithm settings
            db_settings = await self.prisma.algorithmSetting.findMany(
                where={
                    'isActive': True
                }
            )

            # Process settings
            for setting in db_settings:
                # Convert value based on dataType
                value = setting.value
                if setting.dataType == 'NUMBER':
                    value = float(value)
                elif setting.dataType == 'BOOLEAN':
                    value = value.lower() == 'true'
                elif setting.dataType == 'JSON':
                    try:
                        value = json.loads(value)
                    except:
                        pass

                # Organize settings by category
                category = setting.category.lower()
                if category not in self.settings:
                    self.settings[category] = {}

                self.settings[category][setting.key] = value

            logger.info(f"Loaded {len(db_settings)} settings from database")
        except Exception as e:
            logger.error(f"Error loading settings: {str(e)}")
            # Use hardcoded defaults
            self.settings = {
                'general': {
                    'matchingAlgorithmVersion': 'v1.0',
                    'enableAIMatching': True,
                    'matchingModel': 'TWO_TOWER'
                },
                'weights': {
                    'ageWeight': 8,
                    'heightWeight': 6,
                    'educationWeight': 7,
                    'occupationWeight': 7,
                    'locationWeight': 8,
                    'casteWeight': 9,
                    'subCasteWeight': 5,
                    'gotraWeight': 6,
                    'incomeWeight': 5,
                    'lifestyleWeight': 4
                },
                'thresholds': {
                    'minimumMatchScore': 65,
                    'highQualityMatchThreshold': 80
                },
                'advanced': {
                    'maxDistanceKm': 100,
                    'maxAgeDifference': 10,
                    'considerUserActivity': True,
                    'boostNewProfiles': True,
                    'boostNewProfilesDays': 7,
                    'boostVerifiedProfiles': True,
                    'boostVerifiedProfilesAmount': 10,
                    'boostPremiumProfiles': True,
                    'boostPremiumProfilesAmount': 15
                }
            }

    async def _initialize_models(self):
        """Initialize matching models"""
        try:
            # Get active models from database
            db_models = await self.prisma.algorithmModel.findMany(
                where={
                    'isActive': True
                }
            )

            for model in db_models:
                # Parse config
                config = None
                if model.config:
                    try:
                        config = json.loads(model.config)
                    except:
                        logger.warning(f"Failed to parse config for model {model.name}")

                # Initialize model based on type
                if model.type == 'TWO_TOWER':
                    self.models[model.id] = MatrimonyMatchingModel(config)

                    # Try to load model if it exists
                    model_path = model.modelPath
                    if model_path and os.path.exists(model_path):
                        try:
                            self.models[model.id].load(model_path)
                            logger.info(f"Loaded model {model.name} from {model_path}")
                        except Exception as e:
                            logger.warning(f"Error loading model {model.name}: {str(e)}, building new model")
                            # Build model if loading fails
                            self.models[model.id].build_model()
                            logger.info(f"Built new model {model.name} successfully")
                    else:
                        # Build model if it doesn't exist
                        self.models[model.id].build_model()
                        logger.info(f"Built new model {model.name} (no saved model found)")

                # Set default model
                if model.isDefault:
                    self.default_model_id = model.id

            logger.info(f"Initialized {len(self.models)} models")
        except Exception as e:
            logger.error(f"Error initializing models: {str(e)}")

            # Initialize default two-tower model
            self.models['default'] = MatrimonyMatchingModel()
            self.models['default'].build_model()
            self.default_model_id = 'default'

    async def get_matches(self, user_id, limit=10, offset=0, min_score=None):
        """
        Get matches for a user

        Args:
            user_id (str): User ID
            limit (int): Maximum number of matches to return
            offset (int): Offset for pagination
            min_score (float): Minimum match score

        Returns:
            list: List of matches
        """
        try:
            # Get user profile and preferences
            user = await self.prisma.user.findUnique(
                where={'id': user_id},
                include={
                    'profile': True,
                    'preference': True
                }
            )

            if not user:
                logger.error(f"User {user_id} not found")
                return []

            # Get potential matches
            # This is a simplified query - in a real implementation, you would
            # filter based on user preferences
            potential_matches = await self.prisma.user.findMany(
                where={
                    'id': {'not': user_id},
                    'profileStatus': 'ACTIVE'
                },
                include={
                    'profile': True
                },
                take=100  # Get more than needed for scoring
            )

            if not potential_matches:
                logger.info(f"No potential matches found for user {user_id}")
                return []

            # Get match scores
            matches_with_scores = await self._score_matches(user, potential_matches)

            # Filter by minimum score
            if min_score is not None:
                matches_with_scores = [m for m in matches_with_scores if m['score'] >= min_score]
            else:
                # Use threshold from settings
                threshold = self.settings.get('thresholds', {}).get('minimumMatchScore', 65)
                matches_with_scores = [m for m in matches_with_scores if m['score'] >= threshold]

            # Sort by score (descending)
            matches_with_scores.sort(key=lambda x: x['score'], reverse=True)

            # Apply pagination
            paginated_matches = matches_with_scores[offset:offset+limit]

            # Save matches to database
            await self._save_matches(user_id, paginated_matches)

            return paginated_matches
        except Exception as e:
            logger.error(f"Error getting matches: {str(e)}")
            return []

    async def _score_matches(self, user, potential_matches):
        """
        Score potential matches for a user

        Args:
            user (dict): User profile and preferences
            potential_matches (list): List of potential matches

        Returns:
            list: List of matches with scores
        """
        # Get matching model
        model_type = self.settings.get('general', {}).get('matchingModel', 'TWO_TOWER')

        if model_type == 'TWO_TOWER':
            # Use two-tower model
            return await self._score_matches_two_tower(user, potential_matches)
        else:
            # Use rule-based matching
            return await self._score_matches_rule_based(user, potential_matches)

    async def _score_matches_two_tower(self, user, potential_matches):
        """
        Score potential matches using the two-tower model

        Args:
            user (dict): User profile and preferences
            potential_matches (list): List of potential matches

        Returns:
            list: List of matches with scores
        """
        # Get model
        model = self.models.get(self.default_model_id)
        if not model:
            logger.error(f"Model {self.default_model_id} not found")
            return []

        # Prepare data
        user_profile = user.profile
        user_preferences = user.preference.values if user.preference else {}

        # Get scores
        scores = model.predict(
            [user_profile],
            [user_preferences],
            [m.profile for m in potential_matches]
        )

        # Create result
        matches_with_scores = []
        for i, match in enumerate(potential_matches):
            # Scale score to 0-100
            score = int(scores[i] * 100)

            matches_with_scores.append({
                'userId': match.id,
                'name': match.profile.fullName,
                'age': self._calculate_age(match.profile.birthDate) if match.profile.birthDate else None,
                'city': match.profile.city,
                'occupation': match.profile.occupation,
                'education': match.profile.highestEducation,
                'score': score,
                'isPremium': match.isPremium,
                'isVerified': match.isVerified,
                'profilePictureUrl': match.profile.profilePictureUrl
            })

        return matches_with_scores

    async def _score_matches_rule_based(self, user, potential_matches):
        """
        Score potential matches using rule-based matching

        Args:
            user (dict): User profile and preferences
            potential_matches (list): List of potential matches

        Returns:
            list: List of matches with scores
        """
        # Get weights
        weights = self.settings.get('weights', {})

        # User profile and preferences
        user_profile = user.profile
        user_preferences = user.preference.values if user.preference else {}

        # Parse preferences
        try:
            preferences = json.loads(user_preferences) if isinstance(user_preferences, str) else user_preferences
        except:
            preferences = {}

        # Calculate scores
        matches_with_scores = []
        for match in potential_matches:
            match_profile = match.profile

            # Initialize score components
            score_components = {}

            # Age compatibility
            if user_profile.birthDate and match_profile.birthDate:
                user_age = self._calculate_age(user_profile.birthDate)
                match_age = self._calculate_age(match_profile.birthDate)

                age_min = preferences.get('ageMin', user_age - 5)
                age_max = preferences.get('ageMax', user_age + 5)

                if age_min <= match_age <= age_max:
                    score_components['age'] = 10
                else:
                    # Partial score based on how far outside the range
                    distance = min(abs(match_age - age_min), abs(match_age - age_max))
                    score_components['age'] = max(0, 10 - distance)

            # TODO: Add more compatibility calculations
            # - Height
            # - Education
            # - Occupation
            # - Location
            # - Caste
            # - Sub-caste
            # - Gotra
            # - Income
            # - Lifestyle

            # Calculate weighted score
            total_weight = sum(weights.values())
            weighted_score = 0

            for component, score in score_components.items():
                weight_key = f"{component}Weight"
                weight = weights.get(weight_key, 5)
                weighted_score += (score * weight)

            # Normalize to 0-100
            if total_weight > 0:
                final_score = int((weighted_score / (total_weight * 10)) * 100)
            else:
                final_score = 50  # Default score

            matches_with_scores.append({
                'userId': match.id,
                'name': match_profile.fullName,
                'age': self._calculate_age(match_profile.birthDate) if match_profile.birthDate else None,
                'city': match_profile.city,
                'occupation': match_profile.occupation,
                'education': match_profile.highestEducation,
                'score': final_score,
                'isPremium': match.isPremium,
                'isVerified': match.isVerified,
                'profilePictureUrl': match_profile.profilePictureUrl
            })

        return matches_with_scores

    async def _save_matches(self, user_id, matches):
        """
        Save matches to database

        Args:
            user_id (str): User ID
            matches (list): List of matches with scores
        """
        try:
            # Save each match
            for match in matches:
                match_data = {
                    'user1Id': user_id,
                    'user2Id': match['userId'],
                    'compatibilityScore': match['score'] / 100.0,  # Store as 0-1
                    'matchReason': f"Compatibility score: {match['score']}%"
                }

                # Upsert match
                await self.prisma.match.upsert(
                    where={
                        'user1Id_user2Id': {
                            'user1Id': user_id,
                            'user2Id': match['userId']
                        }
                    },
                    update=match_data,
                    create=match_data
                )
        except Exception as e:
            logger.error(f"Error saving matches: {str(e)}")

    def _calculate_age(self, birth_date):
        """
        Calculate age from birth date

        Args:
            birth_date (datetime): Birth date

        Returns:
            int: Age in years
        """
        today = datetime.now()
        age = today.year - birth_date.year

        # Adjust age if birthday hasn't occurred yet this year
        if (today.month, today.day) < (birth_date.month, birth_date.day):
            age -= 1

        return age
