import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Avatar,
  Chip,
  Button,
  CircularProgress,
  Alert,
  Pa<PERSON>ation,
  Slider,
  FormControlLabel,
  Switch
} from '@mui/material';
import VerifiedIcon from '@mui/icons-material/Verified';
import WorkIcon from '@mui/icons-material/Work';
import SchoolIcon from '@mui/icons-material/School';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import StarIcon from '@mui/icons-material/Star';
import { useRouter } from 'next/router';

const MatchList = ({ userId }) => {
  const router = useRouter();
  const [matches, setMatches] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(1);
  const [minScore, setMinScore] = useState(50);
  const [applyMinScore, setApplyMinScore] = useState(false);
  const matchesPerPage = 10;

  useEffect(() => {
    fetchMatches();
  }, [userId, page, applyMinScore, minScore]);

  const fetchMatches = async () => {
    setLoading(true);
    setError(null);

    try {
      const offset = (page - 1) * matchesPerPage;
      let url = `/api/matching?limit=${matchesPerPage}&offset=${offset}`;
      
      if (userId) {
        url += `&userId=${userId}`;
      }
      
      if (applyMinScore) {
        url += `&minScore=${minScore}`;
      }
      
      const response = await fetch(url);
      const data = await response.json();

      if (data.success) {
        setMatches(data.matches);
      } else {
        setError(data.message || 'Failed to fetch matches');
      }
    } catch (err) {
      console.error('Error fetching matches:', err);
      setError('An error occurred while fetching matches');
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (event, value) => {
    setPage(value);
  };

  const handleMinScoreChange = (event, newValue) => {
    setMinScore(newValue);
  };

  const handleApplyMinScoreChange = (event) => {
    setApplyMinScore(event.target.checked);
  };

  const handleViewProfile = (userId) => {
    router.push(`/profile/${userId}`);
  };

  const getScoreColor = (score) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'primary';
    if (score >= 40) return 'warning';
    return 'error';
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ my: 2 }}>
        {error}
      </Alert>
    );
  }

  if (matches.length === 0) {
    return (
      <Alert severity="info" sx={{ my: 2 }}>
        No matches found. Try adjusting your preferences.
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ mb: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
        <Typography variant="h6" gutterBottom>
          Filter Matches
        </Typography>
        
        <Box sx={{ px: 2 }}>
          <Typography gutterBottom>
            Minimum Match Score: {minScore}%
          </Typography>
          <Slider
            value={minScore}
            onChange={handleMinScoreChange}
            aria-labelledby="match-score-slider"
            valueLabelDisplay="auto"
            step={5}
            marks
            min={0}
            max={100}
            disabled={!applyMinScore}
          />
          <FormControlLabel
            control={
              <Switch
                checked={applyMinScore}
                onChange={handleApplyMinScoreChange}
                name="applyMinScore"
                color="primary"
              />
            }
            label="Apply Minimum Score Filter"
          />
        </Box>
      </Box>

      <Grid container spacing={3}>
        {matches.map((match) => (
          <Grid item xs={12} key={match.userId}>
            <Card 
              sx={{ 
                display: 'flex', 
                cursor: 'pointer',
                '&:hover': {
                  boxShadow: 6
                }
              }}
              onClick={() => handleViewProfile(match.userId)}
            >
              <Avatar
                src={match.profilePictureUrl}
                alt={match.name}
                sx={{ width: 120, height: 120, m: 2, borderRadius: 2 }}
                variant="rounded"
              />
              <Box sx={{ display: 'flex', flexDirection: 'column', flexGrow: 1 }}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="h6" component="div">
                      {match.name}, {match.age}
                      {match.isVerified && (
                        <VerifiedIcon color="primary" fontSize="small" sx={{ ml: 1 }} />
                      )}
                      {match.isPremium && (
                        <StarIcon color="warning" fontSize="small" sx={{ ml: 1 }} />
                      )}
                    </Typography>
                    <Chip
                      label={`${match.score}% Match`}
                      color={getScoreColor(match.score)}
                      size="small"
                    />
                  </Box>
                  
                  <Grid container spacing={1}>
                    {match.occupation && (
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <WorkIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            {match.occupation}
                          </Typography>
                        </Box>
                      </Grid>
                    )}
                    
                    {match.education && (
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <SchoolIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            {match.education}
                          </Typography>
                        </Box>
                      </Grid>
                    )}
                    
                    {match.city && (
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <LocationOnIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            {match.city}
                          </Typography>
                        </Box>
                      </Grid>
                    )}
                  </Grid>
                </CardContent>
                
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', p: 1 }}>
                  <Button 
                    variant="outlined" 
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleViewProfile(match.userId);
                    }}
                  >
                    View Profile
                  </Button>
                </Box>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>
      
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <Pagination
          count={Math.ceil(matches.length / matchesPerPage)}
          page={page}
          onChange={handlePageChange}
          color="primary"
        />
      </Box>
    </Box>
  );
};

export default MatchList;
