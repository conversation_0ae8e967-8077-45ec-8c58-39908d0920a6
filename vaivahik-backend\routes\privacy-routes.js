/**
 * Privacy Settings Routes
 * Handles user privacy preferences and display name settings
 */

const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../src/middleware/auth.middleware');

const prisma = new PrismaClient();

/**
 * @route GET /api/privacy/settings
 * @desc Get user's privacy settings
 * @access Private
 */
router.get('/settings', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;

    const profile = await prisma.profile.findUnique({
      where: { userId },
      select: {
        displayNamePreference: true,
        showNameInNotifications: true,
        showNameInSearch: true,
        showNameInMatches: true,
        showNameInMessages: true,
        allowProfileViews: true,
        showOnlineStatus: true,
        showLastSeen: true,
        allowDirectMessages: true,
        showContactInfo: true
      }
    });

    if (!profile) {
      return res.status(404).json({
        success: false,
        message: 'Profile not found'
      });
    }

    res.json({
      success: true,
      privacySettings: profile
    });
  } catch (error) {
    console.error('Error fetching privacy settings:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

/**
 * @route PUT /api/privacy/settings
 * @desc Update user's privacy settings
 * @access Private
 */
router.put('/settings', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const {
      displayNamePreference,
      showNameInNotifications,
      showNameInSearch,
      showNameInMatches,
      showNameInMessages,
      allowProfileViews,
      showOnlineStatus,
      showLastSeen,
      allowDirectMessages,
      showContactInfo
    } = req.body;

    // Validate displayNamePreference
    const validDisplayPreferences = ['FULL_NAME', 'FIRST_NAME', 'PROFILE_ID', 'ANONYMOUS'];
    if (displayNamePreference && !validDisplayPreferences.includes(displayNamePreference)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid display name preference. Must be one of: FULL_NAME, FIRST_NAME, PROFILE_ID, ANONYMOUS'
      });
    }

    // Update privacy settings
    const updatedProfile = await prisma.profile.update({
      where: { userId },
      data: {
        ...(displayNamePreference && { displayNamePreference }),
        ...(typeof showNameInNotifications === 'boolean' && { showNameInNotifications }),
        ...(typeof showNameInSearch === 'boolean' && { showNameInSearch }),
        ...(typeof showNameInMatches === 'boolean' && { showNameInMatches }),
        ...(typeof showNameInMessages === 'boolean' && { showNameInMessages }),
        ...(typeof allowProfileViews === 'boolean' && { allowProfileViews }),
        ...(typeof showOnlineStatus === 'boolean' && { showOnlineStatus }),
        ...(typeof showLastSeen === 'boolean' && { showLastSeen }),
        ...(typeof allowDirectMessages === 'boolean' && { allowDirectMessages }),
        ...(typeof showContactInfo === 'boolean' && { showContactInfo })
      },
      select: {
        displayNamePreference: true,
        showNameInNotifications: true,
        showNameInSearch: true,
        showNameInMatches: true,
        showNameInMessages: true,
        allowProfileViews: true,
        showOnlineStatus: true,
        showLastSeen: true,
        allowDirectMessages: true,
        showContactInfo: true
      }
    });

    res.json({
      success: true,
      message: 'Privacy settings updated successfully',
      privacySettings: updatedProfile
    });
  } catch (error) {
    console.error('Error updating privacy settings:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

/**
 * @route GET /api/privacy/display-name-options
 * @desc Get available display name options
 * @access Private
 */
router.get('/display-name-options', authenticateToken, async (req, res) => {
  try {
    const options = [
      {
        value: 'FULL_NAME',
        label: 'Full Name',
        description: 'Show your complete name (e.g., "Rahul Sharma")',
        privacy: 'Low',
        recommended: false
      },
      {
        value: 'FIRST_NAME',
        label: 'First Name Only',
        description: 'Show only your first name (e.g., "Rahul")',
        privacy: 'Medium',
        recommended: true
      },
      {
        value: 'PROFILE_ID',
        label: 'Profile ID',
        description: 'Show profile ID based on gender (e.g., "Profile M1234")',
        privacy: 'High',
        recommended: false
      },
      {
        value: 'ANONYMOUS',
        label: 'Anonymous',
        description: 'Show as "Someone" for maximum privacy',
        privacy: 'Maximum',
        recommended: false
      }
    ];

    res.json({
      success: true,
      options
    });
  } catch (error) {
    console.error('Error fetching display name options:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

/**
 * @route GET /api/privacy/preview-name
 * @desc Preview how user's name will appear with different settings
 * @access Private
 */
router.get('/preview-name', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const { preference } = req.query;

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { profile: true }
    });

    if (!user || !user.profile) {
      return res.status(404).json({
        success: false,
        message: 'Profile not found'
      });
    }

    // Import the privacy function
    const { getPrivacySafeName, getProfileId, getAnonymousName } = require('../services/notification/auto-notification-triggers');

    const previews = {};
    const preferences = preference ? [preference] : ['FULL_NAME', 'FIRST_NAME', 'PROFILE_ID', 'ANONYMOUS'];

    preferences.forEach(pref => {
      // Temporarily set the preference
      const tempUser = {
        ...user,
        profile: {
          ...user.profile,
          displayNamePreference: pref,
          showNameInNotifications: true
        }
      };

      switch (pref) {
        case 'FULL_NAME':
          previews[pref] = tempUser.profile.fullName || 'Full Name Not Set';
          break;
        case 'FIRST_NAME':
          if (tempUser.profile.fullName) {
            previews[pref] = tempUser.profile.fullName.split(' ')[0];
          } else {
            previews[pref] = 'First Name Not Set';
          }
          break;
        case 'PROFILE_ID':
          const gender = tempUser.profile.gender?.toLowerCase();
          const idSuffix = tempUser.id.slice(-4);
          if (gender === 'female') {
            previews[pref] = `Profile F${idSuffix}`;
          } else if (gender === 'male') {
            previews[pref] = `Profile M${idSuffix}`;
          } else {
            previews[pref] = `Profile ${idSuffix}`;
          }
          break;
        case 'ANONYMOUS':
          previews[pref] = 'Someone';
          break;
        default:
          previews[pref] = 'Unknown';
      }
    });

    res.json({
      success: true,
      previews: preference ? previews[preference] : previews,
      currentSetting: user.profile.displayNamePreference || 'FIRST_NAME'
    });
  } catch (error) {
    console.error('Error generating name preview:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

/**
 * @route GET /api/privacy/recommendations
 * @desc Get privacy recommendations based on user profile
 * @access Private
 */
router.get('/recommendations', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { profile: true }
    });

    if (!user || !user.profile) {
      return res.status(404).json({
        success: false,
        message: 'Profile not found'
      });
    }

    const gender = user.profile.gender?.toLowerCase();
    const recommendations = [];

    // Gender-based recommendations
    if (gender === 'female') {
      recommendations.push({
        type: 'DISPLAY_NAME',
        recommendation: 'FIRST_NAME',
        reason: 'For enhanced privacy and security, we recommend showing only your first name.',
        priority: 'HIGH'
      });
      recommendations.push({
        type: 'PROFILE_VIEWS',
        recommendation: false,
        reason: 'Consider limiting who can view your profile for added security.',
        priority: 'MEDIUM'
      });
    } else {
      recommendations.push({
        type: 'DISPLAY_NAME',
        recommendation: 'FIRST_NAME',
        reason: 'First name provides a good balance between privacy and personalization.',
        priority: 'MEDIUM'
      });
    }

    // General recommendations
    recommendations.push({
      type: 'CONTACT_INFO',
      recommendation: false,
      reason: 'Keep contact information private until you establish trust.',
      priority: 'HIGH'
    });

    recommendations.push({
      type: 'ONLINE_STATUS',
      recommendation: false,
      reason: 'Hiding online status gives you more control over your availability.',
      priority: 'LOW'
    });

    res.json({
      success: true,
      recommendations,
      userProfile: {
        gender: user.profile.gender,
        currentSettings: {
          displayNamePreference: user.profile.displayNamePreference,
          showNameInNotifications: user.profile.showNameInNotifications,
          allowProfileViews: user.profile.allowProfileViews,
          showContactInfo: user.profile.showContactInfo,
          showOnlineStatus: user.profile.showOnlineStatus
        }
      }
    });
  } catch (error) {
    console.error('Error generating privacy recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
