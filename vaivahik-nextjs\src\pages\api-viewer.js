import { useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Link from 'next/link';

export default function ApiViewer() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the enhanced API viewer
    router.replace('/api-viewer-enhanced');
  }, [router]);

  return (
    <div className="container">
      <Head>
        <title>Redirecting to Enhanced API Viewer - Vaivahik Admin</title>
        <meta name="description" content="Redirecting to Enhanced API Viewer" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="main">
        <h1 className="title">Redirecting to Enhanced API Viewer</h1>

        <div className="loading-spinner"></div>

        <p className="redirect-message">
          You are being redirected to our new and improved API documentation tool.
        </p>

        <div className="manual-link">
          If you are not redirected automatically, please <Link href="/api-viewer-enhanced">click here</Link>.
        </div>
      </main>

      <style jsx>{`
        .container {
          min-height: 100vh;
          padding: 0 2rem;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background-color: #f5f5f5;
        }

        .main {
          padding: 4rem 0;
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 100%;
          max-width: 600px;
          text-align: center;
        }

        .title {
          margin: 0 0 2rem;
          line-height: 1.15;
          font-size: 2rem;
          text-align: center;
          color: #5e35b1;
        }

        .loading-spinner {
          width: 50px;
          height: 50px;
          border: 5px solid #f3f3f3;
          border-top: 5px solid #5e35b1;
          border-radius: 50%;
          margin: 1rem 0 2rem;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .redirect-message {
          font-size: 1.1rem;
          color: #333;
          margin-bottom: 2rem;
        }

        .manual-link {
          font-size: 0.9rem;
          color: #666;
        }

        .manual-link a {
          color: #5e35b1;
          text-decoration: none;
          font-weight: bold;
        }

        .manual-link a:hover {
          text-decoration: underline;
        }
      `}</style>
    </div>
  );
}
