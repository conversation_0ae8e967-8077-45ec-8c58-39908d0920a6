#!/usr/bin/env node

/**
 * Test Email Service
 * Tests the email functionality using Ethereal test account
 */

// Load environment variables
require('dotenv').config();

// Import email service
const { sendEmail } = require('../src/services/email.service');

// Test email data
const testEmail = {
    to: '<EMAIL>',
    subject: 'Test Email from Vaivahik',
    template: 'welcome',
    data: {
        name: 'Test User',
        message: 'This is a test email to verify the email service is working correctly.'
    }
};

async function testEmailService() {
    console.log('🧪 Testing Email Service...\n');
    
    try {
        console.log('📧 Sending test email...');
        console.log(`To: ${testEmail.to}`);
        console.log(`Subject: ${testEmail.subject}`);
        console.log(`Template: ${testEmail.template}`);
        
        const result = await sendEmail(testEmail);
        
        console.log('\n✅ Email sent successfully!');
        console.log(`Message ID: ${result.messageId}`);
        
        // If using Ethereal test account, show preview URL
        if (result.messageId && result.messageId.includes('ethereal.email')) {
            console.log('\n🔗 Email Preview URL:');
            console.log('You can view the sent email at the URL shown in the logs above.');
            console.log('This is a test email service - no real email was sent.');
        }
        
        return true;
        
    } catch (error) {
        console.log('\n❌ Email test failed:');
        console.log(`Error: ${error.message}`);
        
        if (error.message.includes('SMTP')) {
            console.log('\n💡 Solutions:');
            console.log('1. Check your SMTP configuration in .env file');
            console.log('2. For testing, leave SMTP settings empty to use test account');
            console.log('3. For production, add your email provider SMTP details');
        }
        
        return false;
    }
}

// Run the test
testEmailService().then(success => {
    if (success) {
        console.log('\n🎉 Email service is working correctly!');
        console.log('\n📝 Next steps:');
        console.log('1. For production, configure real SMTP settings in .env');
        console.log('2. Popular providers: Gmail, SendGrid, Mailgun, AWS SES');
        console.log('3. Update EMAIL_FROM with your domain email');
    } else {
        console.log('\n❌ Email service needs configuration.');
    }
    
    process.exit(success ? 0 : 1);
});
