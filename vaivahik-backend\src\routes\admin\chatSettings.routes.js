// src/routes/admin/chatSettings.routes.js

const express = require('express');
const router = express.Router();
const chatSettingsController = require('../../controllers/admin/chatSettings.controller');
const { authenticateAdmin } = require('../../middleware/auth.middleware');

// Get chat settings
router.get('/', authenticateAdmin, chatSettingsController.getChatSettings);

// Update moderation settings
router.put('/moderation', authenticateAdmin, chatSettingsController.updateModerationSettings);

module.exports = router;
