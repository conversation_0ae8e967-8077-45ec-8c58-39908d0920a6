import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import UserLayout from '@/components/user/UserLayout';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  Grid,
  IconButton,
  Typography,
  Chip,
  Tooltip,
  Paper,
  Stepper,
  Step,
  StepLabel,
  TextField,
  InputAdornment,
  Alert,
  LinearProgress,
  Stack
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  ShoppingCart as ShoppingCartIcon,
  FlashOn as FlashOnIcon,
  History as HistoryIcon,
  CheckCircle as CheckCircleIcon,
  Timer as TimerIcon,
  Add as AddIcon,
  Remove as RemoveIcon
} from '@mui/icons-material';
import axios from 'axios';
import { toast } from 'react-toastify';

export default function SpotlightPage() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [spotlightOptions, setSpotlightOptions] = useState([]);
  const [purchasedSpotlights, setPurchasedSpotlights] = useState([]);
  const [activeSpotlight, setActiveSpotlight] = useState(null);
  const [spotlightHistory, setSpotlightHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openPurchaseModal, setOpenPurchaseModal] = useState(false);
  const [openActivateModal, setOpenActivateModal] = useState(false);
  const [currentSpotlight, setCurrentSpotlight] = useState(null);
  const [currentPurchasedSpotlight, setCurrentPurchasedSpotlight] = useState(null);
  const [paymentProcessing, setPaymentProcessing] = useState(false);
  const [activationProcessing, setActivationProcessing] = useState(false);
  const [quantity, setQuantity] = useState(1);

  useEffect(() => {
    if (status === 'authenticated') {
      fetchSpotlightStatus();
    }
  }, [status]);

  const fetchSpotlightStatus = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/user/spotlight/status');
      if (response.data.success) {
        setSpotlightOptions(response.data.availableSpotlights);
        setPurchasedSpotlights(response.data.purchasedSpotlights);
        setActiveSpotlight(response.data.activeSpotlight);
        setSpotlightHistory(response.data.spotlightHistory);
      }
      setLoading(false);
    } catch (error) {
      console.error('Error fetching spotlight status:', error);
      toast.error('Failed to fetch spotlight status');
      setLoading(false);
    }
  };

  const handlePurchase = (spotlight) => {
    setCurrentSpotlight(spotlight);
    setQuantity(1);
    setOpenPurchaseModal(true);
  };

  const handleActivate = (purchasedSpotlight) => {
    setCurrentPurchasedSpotlight(purchasedSpotlight);
    setOpenActivateModal(true);
  };

  const handleConfirmPurchase = async () => {
    try {
      setPaymentProcessing(true);

      // In a real implementation, you would integrate with a payment gateway here
      // For now, we'll simulate a successful payment

      const response = await axios.post('/api/user/spotlight/purchase', {
        spotlightId: currentSpotlight.id,
        paymentMethod: 'credit_card',
        transactionId: `DEMO-${Date.now()}`,
        quantity
      });

      if (response.data.success) {
        toast.success('Spotlight purchased successfully!');
        fetchSpotlightStatus();
        setOpenPurchaseModal(false);
      } else {
        toast.error(response.data.message || 'Failed to purchase spotlight');
      }

      setPaymentProcessing(false);
    } catch (error) {
      console.error('Error purchasing spotlight:', error);
      toast.error(error.response?.data?.message || 'Failed to purchase spotlight');
      setPaymentProcessing(false);
    }
  };

  const handleConfirmActivation = async () => {
    try {
      setActivationProcessing(true);

      const response = await axios.post('/api/user/spotlight/activate', {
        userSpotlightId: currentPurchasedSpotlight.id
      });

      if (response.data.success) {
        toast.success('Spotlight activated successfully! Your profile will be highlighted for the next 24 hours.');
        fetchSpotlightStatus();
        setOpenActivateModal(false);
      } else {
        toast.error(response.data.message || 'Failed to activate spotlight');
      }

      setActivationProcessing(false);
    } catch (error) {
      console.error('Error activating spotlight:', error);
      toast.error(error.response?.data?.message || 'Failed to activate spotlight');
      setActivationProcessing(false);
    }
  };

  const handleQuantityChange = (value) => {
    const newQuantity = Math.max(1, Math.min(10, value));
    setQuantity(newQuantity);
  };

  // Calculate time remaining for active spotlight
  const getTimeRemaining = (endTime) => {
    const now = new Date();
    const end = new Date(endTime);
    const diffMs = end - now;

    if (diffMs <= 0) return { text: 'Expired', percentage: 0 };

    const totalDuration = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    const elapsedMs = totalDuration - diffMs;
    const percentage = Math.max(0, Math.min(100, (elapsedMs / totalDuration) * 100));

    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    return {
      text: `${diffHours}h ${diffMinutes}m remaining`,
      percentage: 100 - percentage
    };
  };

  return (
    <UserLayout title="Spotlight Feature">
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Spotlight Your Profile
          </Typography>
        </Box>

        <Alert severity="info" sx={{ mb: 3 }}>
          Get more visibility! Activate the Spotlight feature to make your profile stand out in search results for 24 hours.
        </Alert>

        {/* Active Spotlight Section */}
        {activeSpotlight ? (
          <Paper sx={{ p: 3, mb: 4, bgcolor: '#f9f4ff', border: '1px solid #d4b3ff' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <FlashOnIcon sx={{ color: 'secondary.main', fontSize: 32, mr: 2 }} />
              <Typography variant="h5" component="h2">
                Your Profile is in the Spotlight!
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body1" paragraph>
                Your profile is currently highlighted in search results. You're getting more visibility!
              </Typography>

              {activeSpotlight && (
                <>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <TimerIcon sx={{ mr: 1, color: 'secondary.main' }} />
                    <Typography variant="body1" fontWeight="medium">
                      {getTimeRemaining(activeSpotlight.endTime).text}
                    </Typography>
                  </Box>

                  <LinearProgress
                    variant="determinate"
                    value={getTimeRemaining(activeSpotlight.endTime).percentage}
                    color="secondary"
                    sx={{ height: 10, borderRadius: 5, mb: 2 }}
                  />

                  <Typography variant="body2" color="text.secondary">
                    Started: {new Date(activeSpotlight.startTime).toLocaleString()}
                    <br />
                    Ends: {new Date(activeSpotlight.endTime).toLocaleString()}
                  </Typography>
                </>
              )}
            </Box>
          </Paper>
        ) : (
          <Paper sx={{ p: 3, mb: 4, bgcolor: '#f5f5f5' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <VisibilityIcon sx={{ color: 'text.secondary', fontSize: 32, mr: 2 }} />
              <Typography variant="h5" component="h2">
                Your Profile is Not Spotlighted
              </Typography>
            </Box>

            <Typography variant="body1" paragraph>
              Activate the Spotlight feature to make your profile stand out in search results for 24 hours.
              {purchasedSpotlights.length > 0 ? ' You have spotlights available to activate!' : ' Purchase a spotlight package to get started.'}
            </Typography>

            {purchasedSpotlights.length > 0 && (
              <Button
                variant="contained"
                color="secondary"
                startIcon={<FlashOnIcon />}
                onClick={() => handleActivate(purchasedSpotlights[0])}
                sx={{ mt: 1 }}
              >
                Activate Spotlight Now
              </Button>
            )}
          </Paper>
        )}

        {/* Available Spotlights Section */}
        {purchasedSpotlights.length > 0 && (
          <Box sx={{ mb: 4 }}>
            <Typography variant="h5" sx={{ mb: 2 }}>
              Your Available Spotlights
            </Typography>

            <Grid container spacing={3}>
              {purchasedSpotlights.map(spotlight => (
                <Grid item xs={12} sm={6} md={4} key={spotlight.id}>
                  <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <CardContent sx={{ flexGrow: 1 }}>
                      <Typography gutterBottom variant="h6" component="h2">
                        {spotlight.spotlight.name}
                      </Typography>

                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Chip
                          label={`${spotlight.remainingCount} remaining`}
                          color="primary"
                          variant="outlined"
                        />
                        <Typography variant="body2" color="text.secondary">
                          Purchased: {new Date(spotlight.purchaseDate).toLocaleDateString()}
                        </Typography>
                      </Box>

                      <Typography variant="body2" color="text.secondary" paragraph>
                        {spotlight.spotlight.description}
                      </Typography>
                    </CardContent>
                    <Divider />
                    <Box sx={{ p: 2 }}>
                      <Button
                        variant="contained"
                        color="secondary"
                        fullWidth
                        startIcon={<FlashOnIcon />}
                        onClick={() => handleActivate(spotlight)}
                        disabled={!!activeSpotlight || spotlight.remainingCount <= 0}
                      >
                        {activeSpotlight ? 'Already Active' : 'Activate Now'}
                      </Button>
                    </Box>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        )}

        {/* Purchase Options Section */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h5" sx={{ mb: 2 }}>
            Purchase Spotlight Packages
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Grid container spacing={3}>
              {spotlightOptions.map(option => (
                <Grid item xs={12} sm={6} md={4} key={option.id}>
                  <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <Box sx={{
                      bgcolor: 'secondary.main',
                      color: 'white',
                      p: 2,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      flexDirection: 'column'
                    }}>
                      <Typography variant="h5" component="h2" align="center">
                        {option.name}
                      </Typography>
                    </Box>
                    <CardContent sx={{ flexGrow: 1 }}>
                      <Typography variant="body1" paragraph>
                        {option.description}
                      </Typography>

                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                        {option.discountedPrice ? (
                          <>
                            <Typography variant="body2" color="text.secondary" sx={{ textDecoration: 'line-through', mr: 1 }}>
                              ₹{option.price}
                            </Typography>
                            <Typography variant="h4" color="secondary" fontWeight="bold">
                              ₹{option.discountedPrice}
                            </Typography>
                            <Chip
                              label={`-${option.discountPercent}%`}
                              size="small"
                              color="secondary"
                              sx={{ ml: 1 }}
                            />
                          </>
                        ) : (
                          <Typography variant="h4" color="secondary" fontWeight="bold">
                            ₹{option.price}
                          </Typography>
                        )}
                      </Box>

                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="body2" color="text.secondary">
                          24 hours of increased visibility
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Priority placement in search results
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Higher match recommendations
                        </Typography>
                        <Typography variant="body2" color="secondary" fontWeight="medium" sx={{ mt: 1 }}>
                          {option.defaultCount > 1 ? `${option.defaultCount} spotlights per purchase` : '1 spotlight per purchase'}
                        </Typography>
                      </Box>
                    </CardContent>
                    <Divider />
                    <Box sx={{ p: 2 }}>
                      <Button
                        variant="contained"
                        color="secondary"
                        fullWidth
                        startIcon={<ShoppingCartIcon />}
                        onClick={() => handlePurchase(option)}
                      >
                        Purchase
                      </Button>
                    </Box>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </Box>

        {/* Spotlight History Section */}
        {spotlightHistory.length > 0 && (
          <Box sx={{ mb: 4 }}>
            <Typography variant="h5" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
              <HistoryIcon sx={{ mr: 1 }} /> Spotlight History
            </Typography>

            <Paper sx={{ overflow: 'auto' }}>
              <Box sx={{ minWidth: 650 }}>
                <Box sx={{ display: 'table', width: '100%' }}>
                  <Box sx={{ display: 'table-header-group', bgcolor: 'background.paper' }}>
                    <Box sx={{ display: 'table-row' }}>
                      <Box sx={{ display: 'table-cell', p: 2, fontWeight: 'bold' }}>Spotlight Type</Box>
                      <Box sx={{ display: 'table-cell', p: 2, fontWeight: 'bold' }}>Start Time</Box>
                      <Box sx={{ display: 'table-cell', p: 2, fontWeight: 'bold' }}>End Time</Box>
                      <Box sx={{ display: 'table-cell', p: 2, fontWeight: 'bold' }}>Status</Box>
                    </Box>
                  </Box>
                  <Box sx={{ display: 'table-row-group' }}>
                    {spotlightHistory.map((history, index) => (
                      <Box key={index} sx={{ display: 'table-row', '&:nth-of-type(odd)': { bgcolor: 'action.hover' } }}>
                        <Box sx={{ display: 'table-cell', p: 2 }}>{history.spotlight.name}</Box>
                        <Box sx={{ display: 'table-cell', p: 2 }}>{new Date(history.startTime).toLocaleString()}</Box>
                        <Box sx={{ display: 'table-cell', p: 2 }}>{new Date(history.endTime).toLocaleString()}</Box>
                        <Box sx={{ display: 'table-cell', p: 2 }}>
                          <Chip
                            label={new Date(history.endTime) > new Date() ? 'Active' : 'Expired'}
                            color={new Date(history.endTime) > new Date() ? 'success' : 'default'}
                            size="small"
                          />
                        </Box>
                      </Box>
                    ))}
                  </Box>
                </Box>
              </Box>
            </Paper>
          </Box>
        )}
      </Box>

      {/* Purchase Modal */}
      <Dialog open={openPurchaseModal} onClose={() => !paymentProcessing && setOpenPurchaseModal(false)}>
        <DialogTitle>Purchase Spotlight</DialogTitle>
        <DialogContent>
          <DialogContentText paragraph>
            You are about to purchase the "{currentSpotlight?.name}" spotlight package.
          </DialogContentText>

          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom>
              Quantity:
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <IconButton
                onClick={() => handleQuantityChange(quantity - 1)}
                disabled={quantity <= 1}
              >
                <RemoveIcon />
              </IconButton>
              <TextField
                value={quantity}
                onChange={(e) => {
                  const value = parseInt(e.target.value);
                  if (!isNaN(value)) {
                    handleQuantityChange(value);
                  }
                }}
                inputProps={{
                  min: 1,
                  max: 10,
                  style: { textAlign: 'center' }
                }}
                sx={{ width: '80px', mx: 1 }}
              />
              <IconButton
                onClick={() => handleQuantityChange(quantity + 1)}
                disabled={quantity >= 10}
              >
                <AddIcon />
              </IconButton>
            </Box>
          </Box>

          <Box sx={{ bgcolor: '#f5f5f5', p: 2, borderRadius: 1, mb: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              Order Summary:
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2">
                Price per spotlight:
              </Typography>
              <Typography variant="body2" fontWeight="medium">
                ₹{currentSpotlight?.discountedPrice || currentSpotlight?.price}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2">
                Quantity:
              </Typography>
              <Typography variant="body2" fontWeight="medium">
                {quantity}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2">
                Spotlights per purchase:
              </Typography>
              <Typography variant="body2" fontWeight="medium">
                {currentSpotlight?.defaultCount || 1}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2">
                Total spotlights:
              </Typography>
              <Typography variant="body2" fontWeight="medium" color="secondary">
                {quantity * (currentSpotlight?.defaultCount || 1)}
              </Typography>
            </Box>
            <Divider sx={{ my: 1 }} />
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="subtitle2">
                Total:
              </Typography>
              <Typography variant="subtitle2" fontWeight="bold" color="secondary">
                ₹{((currentSpotlight?.discountedPrice || currentSpotlight?.price) * quantity).toFixed(2)}
              </Typography>
            </Box>
          </Box>

          <Typography variant="body2" color="text.secondary">
            Each spotlight can be activated for a 24-hour period. You can purchase multiple spotlights now and activate them whenever you want.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setOpenPurchaseModal(false)}
            disabled={paymentProcessing}
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirmPurchase}
            color="secondary"
            variant="contained"
            disabled={paymentProcessing}
          >
            {paymentProcessing ? <CircularProgress size={24} /> : 'Confirm Purchase'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Activation Modal */}
      <Dialog open={openActivateModal} onClose={() => !activationProcessing && setOpenActivateModal(false)}>
        <DialogTitle>Activate Spotlight</DialogTitle>
        <DialogContent>
          <DialogContentText paragraph>
            You are about to activate the "{currentPurchasedSpotlight?.spotlight.name}" spotlight feature.
          </DialogContentText>

          <Alert severity="info" sx={{ mb: 2 }}>
            Your profile will be highlighted in search results for the next 24 hours.
          </Alert>

          <Typography variant="body2" paragraph>
            You have <strong>{currentPurchasedSpotlight?.remainingCount}</strong> spotlights remaining from this package.
          </Typography>

          <Typography variant="body2" color="text.secondary">
            Once activated, the spotlight will be active for exactly 24 hours and cannot be paused or extended.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setOpenActivateModal(false)}
            disabled={activationProcessing}
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirmActivation}
            color="secondary"
            variant="contained"
            disabled={activationProcessing}
          >
            {activationProcessing ? <CircularProgress size={24} /> : 'Activate Spotlight'}
          </Button>
        </DialogActions>
      </Dialog>
    </UserLayout>
  );
}
