/**
 * Cache Optimization Script
 * 
 * This script analyzes the codebase and adds caching to frequently accessed endpoints.
 * It identifies endpoints that should be cached and adds the cache middleware.
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Configuration
const config = {
  routesDir: path.join(__dirname, '../src/routes'),
  controllersDir: path.join(__dirname, '../src/controllers'),
  backupDir: path.join(__dirname, '../backups', `cache-${new Date().toISOString().replace(/:/g, '-')}`)
};

// Cache configuration for different types of endpoints
const cacheConfig = {
  // Endpoints that should be cached for a long time (rarely change)
  longCache: {
    patterns: [
      '/users/:id/profile',
      '/profiles/:id',
      '/admin/settings',
      '/admin/dashboard/stats',
      '/admin/ai/settings'
    ],
    duration: 3600 // 1 hour
  },
  
  // Endpoints that should be cached for a medium time (change occasionally)
  mediumCache: {
    patterns: [
      '/users',
      '/profiles',
      '/matches',
      '/search',
      '/admin/users',
      '/admin/verification-queue'
    ],
    duration: 300 // 5 minutes
  },
  
  // Endpoints that should be cached for a short time (change frequently)
  shortCache: {
    patterns: [
      '/notifications',
      '/activities',
      '/admin/dashboard'
    ],
    duration: 60 // 1 minute
  },
  
  // Endpoints that should never be cached (real-time data)
  noCache: {
    patterns: [
      '/auth',
      '/messages',
      '/payments',
      '/users/:id/status'
    ]
  }
};

// Create backup directory
if (!fs.existsSync(config.backupDir)) {
  fs.mkdirSync(config.backupDir, { recursive: true });
}

/**
 * Get all route files
 * @returns {Array<string>} Array of route file paths
 */
function getRouteFiles() {
  return glob.sync('**/*.js', { cwd: config.routesDir, absolute: true });
}

/**
 * Backup a file
 * @param {string} filePath - Path to the file
 */
function backupFile(filePath) {
  const relativePath = path.relative(path.join(__dirname, '..'), filePath);
  const backupPath = path.join(config.backupDir, relativePath);
  
  // Create directory if it doesn't exist
  const backupDir = path.dirname(backupPath);
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  // Copy file
  fs.copyFileSync(filePath, backupPath);
  console.log(`Backed up ${relativePath}`);
}

/**
 * Extract routes from file content
 * @param {string} content - File content
 * @returns {Array<Object>} Array of route objects
 */
function extractRoutes(content) {
  const routes = [];
  const routeRegex = /router\.(get|post|put|patch|delete)\(\s*['"]([^'"]+)['"]\s*,\s*([^)]+)\)/g;
  
  let match;
  while ((match = routeRegex.exec(content)) !== null) {
    const method = match[1];
    const path = match[2];
    const handlers = match[3].split(',').map(h => h.trim());
    
    routes.push({
      method,
      path,
      handlers,
      original: match[0]
    });
  }
  
  return routes;
}

/**
 * Check if a route should be cached
 * @param {Object} route - Route object
 * @param {string} routeFile - Route file path
 * @returns {Object|null} Cache configuration or null if no caching needed
 */
function shouldCache(route, routeFile) {
  // Only cache GET requests
  if (route.method.toLowerCase() !== 'get') {
    return null;
  }
  
  // Get API path
  const apiPath = getApiPath(routeFile, route.path);
  
  // Check if route matches any cache configuration
  for (const [cacheType, config] of Object.entries(cacheConfig)) {
    if (cacheType === 'noCache') {
      // Check if route should not be cached
      if (config.patterns.some(pattern => matchPattern(apiPath, pattern))) {
        return null;
      }
    } else {
      // Check if route should be cached
      if (config.patterns.some(pattern => matchPattern(apiPath, pattern))) {
        return {
          duration: config.duration,
          type: cacheType
        };
      }
    }
  }
  
  // Default to short cache for GET requests
  return {
    duration: 30, // 30 seconds
    type: 'defaultCache'
  };
}

/**
 * Match a path against a pattern
 * @param {string} path - API path
 * @param {string} pattern - Pattern to match
 * @returns {boolean} Whether the path matches the pattern
 */
function matchPattern(path, pattern) {
  // Convert pattern to regex
  const regexPattern = pattern
    .replace(/\//g, '\\/') // Escape slashes
    .replace(/:[^/]+/g, '[^/]+'); // Replace :param with regex
  
  const regex = new RegExp(`^${regexPattern}$`);
  
  return regex.test(path);
}

/**
 * Get API path from route file and route path
 * @param {string} routeFile - Route file path
 * @param {string} routePath - Route path
 * @returns {string} Full API path
 */
function getApiPath(routeFile, routePath) {
  const relativePath = path.relative(config.routesDir, routeFile);
  const parts = relativePath.split(path.sep);
  
  // Remove file extension
  parts[parts.length - 1] = parts[parts.length - 1].replace('.js', '');
  
  // Handle index.js files
  if (parts[parts.length - 1] === 'index') {
    parts.pop();
  }
  
  return '/' + parts.join('/') + routePath;
}

/**
 * Add caching to a route file
 * @param {string} filePath - Path to the route file
 */
function addCaching(filePath) {
  // Read file
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Skip if file already has caching
  if (content.includes('cache(') || content.includes('clearCache(')) {
    console.log(`Skipping ${path.relative(path.join(__dirname, '..'), filePath)} (already has caching)`);
    return;
  }
  
  // Extract routes
  const routes = extractRoutes(content);
  
  // Skip if no routes found
  if (routes.length === 0) {
    console.log(`Skipping ${path.relative(path.join(__dirname, '..'), filePath)} (no routes found)`);
    return;
  }
  
  // Check if any route should be cached
  const routesToCache = routes.filter(route => shouldCache(route, filePath));
  
  if (routesToCache.length === 0) {
    console.log(`Skipping ${path.relative(path.join(__dirname, '..'), filePath)} (no routes to cache)`);
    return;
  }
  
  // Backup file
  backupFile(filePath);
  
  // Add cache middleware import if needed
  let updatedContent = content;
  if (!updatedContent.includes('cacheMiddleware')) {
    updatedContent = updatedContent.replace(
      'const express = require(\'express\');',
      'const express = require(\'express\');\nconst { cache, clearCache } = require(\'../middleware/cacheMiddleware\');'
    );
  }
  
  // Add caching to routes
  for (const route of routesToCache) {
    const cacheConfig = shouldCache(route, filePath);
    
    if (!cacheConfig) {
      continue;
    }
    
    // Generate updated route with caching
    const updatedRoute = generateCachedRoute(route, cacheConfig);
    
    // Replace original route with updated route
    updatedContent = updatedContent.replace(route.original, updatedRoute);
  }
  
  // Add cache invalidation for POST, PUT, PATCH, DELETE routes
  const routesToInvalidate = routes.filter(route => {
    const method = route.method.toLowerCase();
    return method === 'post' || method === 'put' || method === 'patch' || method === 'delete';
  });
  
  for (const route of routesToInvalidate) {
    // Generate updated route with cache invalidation
    const updatedRoute = generateCacheInvalidationRoute(route, filePath);
    
    // Replace original route with updated route
    updatedContent = updatedContent.replace(route.original, updatedRoute);
  }
  
  // Write updated content
  fs.writeFileSync(filePath, updatedContent);
  
  console.log(`Added caching to ${path.relative(path.join(__dirname, '..'), filePath)}`);
}

/**
 * Generate cached route
 * @param {Object} route - Route object
 * @param {Object} cacheConfig - Cache configuration
 * @returns {string} Updated route with caching
 */
function generateCachedRoute(route, cacheConfig) {
  const method = route.method;
  const path = route.path;
  const handlers = route.handlers;
  
  // Add cache middleware
  return `router.${method}(
  '${path}',
  cache(${cacheConfig.duration}), // Cache for ${cacheConfig.duration} seconds (${cacheConfig.type})
  ${handlers.join(',\n  ')}
)`;
}

/**
 * Generate route with cache invalidation
 * @param {Object} route - Route object
 * @param {string} routeFile - Route file path
 * @returns {string} Updated route with cache invalidation
 */
function generateCacheInvalidationRoute(route, routeFile) {
  const method = route.method;
  const path = route.path;
  const handlers = route.handlers;
  
  // Get API path for cache invalidation
  const apiPath = getApiPath(routeFile, '');
  
  // Add cache invalidation middleware
  return `router.${method}(
  '${path}',
  clearCacheMiddleware('${apiPath}*'), // Clear cache for this resource
  ${handlers.join(',\n  ')}
)`;
}

/**
 * Main function
 */
function main() {
  console.log('Optimizing caching for frequently accessed endpoints...');
  
  // Get all route files
  const routeFiles = getRouteFiles();
  
  // Add caching to each route file
  for (const filePath of routeFiles) {
    addCaching(filePath);
  }
  
  console.log('Done!');
}

// Run main function
main();
