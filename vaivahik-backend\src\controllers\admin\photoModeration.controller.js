// src/controllers/admin/photoModeration.controller.js

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const photoModerationService = require('../../services/photoModeration.service');
const path = require('path');

/**
 * @description Get photo moderation settings
 * @route GET /api/admin/photo-moderation/settings
 */
exports.getModerationSettings = async (req, res, next) => {
    try {
        const settings = await photoModerationService.getModerationSettings();
        
        res.status(200).json({
            success: true,
            settings
        });
    } catch (error) {
        console.error('Error getting moderation settings:', error);
        next(error);
    }
};

/**
 * @description Update photo moderation settings
 * @route PUT /api/admin/photo-moderation/settings
 */
exports.updateModerationSettings = async (req, res, next) => {
    const { settings } = req.body;
    
    if (!settings) {
        const error = new Error('Settings object is required');
        error.status = 400;
        return next(error);
    }
    
    try {
        // Validate settings
        if (settings.operationMode !== undefined && ![0, 1, 2, 3].includes(settings.operationMode)) {
            const error = new Error('Operation mode must be 0, 1, 2, or 3');
            error.status = 400;
            return next(error);
        }
        
        if (settings.automationPercentage !== undefined && (settings.automationPercentage < 0 || settings.automationPercentage > 100)) {
            const error = new Error('Automation percentage must be between 0 and 100');
            error.status = 400;
            return next(error);
        }
        
        // Update settings in database
        await prisma.systemConfig.upsert({
            where: { configKey: 'photoModeration' },
            update: {
                configValue: JSON.stringify(settings),
                updatedAt: new Date()
            },
            create: {
                configKey: 'photoModeration',
                configValue: JSON.stringify(settings),
                description: 'Photo moderation settings',
                isActive: true
            }
        });
        
        res.status(200).json({
            success: true,
            message: 'Photo moderation settings updated successfully',
            settings
        });
    } catch (error) {
        console.error('Error updating moderation settings:', error);
        next(error);
    }
};

/**
 * @description Get moderation statistics
 * @route GET /api/admin/photo-moderation/stats
 */
exports.getModerationStats = async (req, res, next) => {
    try {
        // Get counts for different statuses
        const [pendingCount, approvedCount, rejectedCount, totalCount] = await Promise.all([
            prisma.photo.count({ where: { status: 'PENDING' } }),
            prisma.photo.count({ where: { status: 'APPROVED' } }),
            prisma.photo.count({ where: { status: 'REJECTED' } }),
            prisma.photo.count()
        ]);
        
        // Get AI moderation stats
        const aiModeratedCount = await prisma.photo.count({
            where: {
                aiFlags: { not: null }
            }
        });
        
        // Get recent moderation logs
        const recentLogs = await prisma.moderationLog.findMany({
            take: 10,
            orderBy: { createdAt: 'desc' },
            include: {
                photo: {
                    select: {
                        id: true,
                        url: true,
                        userId: true,
                        user: {
                            select: {
                                profile: {
                                    select: { fullName: true }
                                }
                            }
                        }
                    }
                }
            }
        });
        
        // Calculate percentages
        const stats = {
            total: totalCount,
            pending: {
                count: pendingCount,
                percentage: totalCount > 0 ? (pendingCount / totalCount) * 100 : 0
            },
            approved: {
                count: approvedCount,
                percentage: totalCount > 0 ? (approvedCount / totalCount) * 100 : 0
            },
            rejected: {
                count: rejectedCount,
                percentage: totalCount > 0 ? (rejectedCount / totalCount) * 100 : 0
            },
            aiModerated: {
                count: aiModeratedCount,
                percentage: totalCount > 0 ? (aiModeratedCount / totalCount) * 100 : 0
            },
            recentActivity: recentLogs.map(log => ({
                id: log.id,
                photoId: log.photoId,
                photoUrl: log.photo.url,
                userName: log.photo.user?.profile?.fullName || 'Unknown',
                decision: log.aiDecision,
                confidence: log.aiConfidence,
                flags: log.aiFlags ? log.aiFlags.split(',') : [],
                timestamp: log.createdAt
            }))
        };
        
        res.status(200).json({
            success: true,
            stats
        });
    } catch (error) {
        console.error('Error getting moderation stats:', error);
        next(error);
    }
};

/**
 * @description Process a batch of pending photos
 * @route POST /api/admin/photo-moderation/batch-process
 */
exports.batchProcessPhotos = async (req, res, next) => {
    const { limit = 50 } = req.body;
    
    try {
        // Get pending photos
        const pendingPhotos = await prisma.photo.findMany({
            where: { status: 'PENDING' },
            take: parseInt(limit),
            orderBy: { uploadedAt: 'asc' }
        });
        
        if (pendingPhotos.length === 0) {
            return res.status(200).json({
                success: true,
                message: 'No pending photos to process',
                results: {
                    processed: 0,
                    approved: 0,
                    rejected: 0,
                    pending: 0
                }
            });
        }
        
        console.log(`Processing ${pendingPhotos.length} pending photos with AI`);
        
        let processed = 0;
        let approved = 0;
        let rejected = 0;
        let stillPending = 0;
        
        // Process each photo
        for (const photo of pendingPhotos) {
            try {
                const photoPath = path.join(process.cwd(), photo.url);
                
                // Process the photo
                const moderationResult = await photoModerationService.processPhoto(photoPath, photo.id);
                
                // Update the photo with AI results
                await prisma.photo.update({
                    where: { id: photo.id },
                    data: {
                        aiFlags: moderationResult.flags.join(','),
                        aiConfidence: moderationResult.confidence,
                        status: moderationResult.decision
                    }
                });
                
                // Log the moderation result
                await photoModerationService.logModerationResult(photo.id, moderationResult);
                
                processed++;
                if (moderationResult.decision === 'APPROVED') approved++;
                else if (moderationResult.decision === 'REJECTED') rejected++;
                else stillPending++;
                
                console.log(`Processed photo ${photo.id}: ${moderationResult.decision}`);
            } catch (error) {
                console.error(`Error processing photo ${photo.id}:`, error);
                // Continue with next photo
            }
        }
        
        res.status(200).json({
            success: true,
            message: `Processed ${processed} photos: ${approved} approved, ${rejected} rejected, ${stillPending} need manual review.`,
            results: {
                processed,
                approved,
                rejected,
                pending: stillPending
            }
        });
    } catch (error) {
        console.error('Error in batch processing:', error);
        next(error);
    }
};

/**
 * @description Get photos with a specific status
 * @route GET /api/admin/photo-moderation/photos
 */
exports.getPhotosByStatus = async (req, res, next) => {
    const { status = 'PENDING', page = 1, limit = 20 } = req.query;
    
    try {
        // Validate status
        if (!['PENDING', 'APPROVED', 'REJECTED', 'ALL'].includes(status)) {
            const error = new Error('Invalid status. Must be PENDING, APPROVED, REJECTED, or ALL');
            error.status = 400;
            return next(error);
        }
        
        // Build where clause
        const where = status !== 'ALL' ? { status } : {};
        
        // Get total count
        const totalCount = await prisma.photo.count({ where });
        
        // Calculate pagination
        const pageNum = parseInt(page);
        const pageSize = parseInt(limit);
        const skip = (pageNum - 1) * pageSize;
        
        // Get photos
        const photos = await prisma.photo.findMany({
            where,
            skip,
            take: pageSize,
            orderBy: { uploadedAt: 'desc' },
            include: {
                user: {
                    select: {
                        id: true,
                        phone: true,
                        profile: {
                            select: {
                                fullName: true,
                                gender: true,
                                dateOfBirth: true,
                                city: true
                            }
                        }
                    }
                }
            }
        });
        
        // Format response
        const formattedPhotos = photos.map(photo => {
            // Calculate age if dateOfBirth is available
            let age = null;
            if (photo.user?.profile?.dateOfBirth) {
                const birthDate = new Date(photo.user.profile.dateOfBirth);
                const today = new Date();
                age = today.getFullYear() - birthDate.getFullYear();
                const m = today.getMonth() - birthDate.getMonth();
                if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
                    age--;
                }
            }
            
            return {
                id: photo.id,
                url: photo.url,
                status: photo.status,
                uploadedAt: photo.uploadedAt,
                isProfilePic: photo.isProfilePic,
                aiFlags: photo.aiFlags ? photo.aiFlags.split(',') : [],
                aiConfidence: photo.aiConfidence,
                user: {
                    id: photo.user.id,
                    name: photo.user.profile?.fullName || 'Unknown',
                    phone: photo.user.phone,
                    gender: photo.user.profile?.gender || 'Unknown',
                    age: age,
                    location: photo.user.profile?.city || 'Unknown'
                }
            };
        });
        
        res.status(200).json({
            success: true,
            photos: formattedPhotos,
            pagination: {
                total: totalCount,
                page: pageNum,
                pageSize,
                pages: Math.ceil(totalCount / pageSize)
            }
        });
    } catch (error) {
        console.error('Error getting photos by status:', error);
        next(error);
    }
};

/**
 * @description Update a photo's moderation status
 * @route PUT /api/admin/photo-moderation/photos/:photoId/status
 */
exports.updatePhotoStatus = async (req, res, next) => {
    const { photoId } = req.params;
    const { status, adminNotes } = req.body;
    
    if (!photoId) {
        const error = new Error('Photo ID is required');
        error.status = 400;
        return next(error);
    }
    
    if (!status || !['APPROVED', 'REJECTED', 'PENDING'].includes(status)) {
        const error = new Error('Invalid status. Must be APPROVED, REJECTED, or PENDING');
        error.status = 400;
        return next(error);
    }
    
    try {
        // Update the photo
        const updatedPhoto = await prisma.photo.update({
            where: { id: photoId },
            data: { status },
            include: {
                user: {
                    select: {
                        id: true,
                        profile: {
                            select: { fullName: true }
                        }
                    }
                }
            }
        });
        
        // Log the manual moderation
        await prisma.moderationLog.create({
            data: {
                photoId,
                aiDecision: status,
                aiFlags: 'manual_review',
                aiConfidence: 100,
                details: JSON.stringify({
                    adminNotes: adminNotes || 'Manual review by admin',
                    adminId: req.user?.adminId || 'unknown'
                })
            }
        });
        
        res.status(200).json({
            success: true,
            message: `Photo status updated to ${status}`,
            photo: {
                id: updatedPhoto.id,
                url: updatedPhoto.url,
                status: updatedPhoto.status,
                user: {
                    id: updatedPhoto.user.id,
                    name: updatedPhoto.user.profile?.fullName || 'Unknown'
                }
            }
        });
    } catch (error) {
        if (error.code === 'P2025') {
            const err = new Error('Photo not found');
            err.status = 404;
            return next(err);
        }
        
        console.error('Error updating photo status:', error);
        next(error);
    }
};
