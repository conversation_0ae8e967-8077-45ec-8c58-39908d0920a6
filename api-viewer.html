<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>API Viewer</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2 {
      text-align: center;
      color: #333;
    }
    .container {
      display: flex;
      gap: 20px;
    }
    .sidebar {
      flex: 1;
      max-width: 300px;
      border-right: 1px solid #ddd;
      padding-right: 20px;
    }
    .content {
      flex: 3;
    }
    .endpoint-list {
      list-style-type: none;
      padding: 0;
    }
    .endpoint-item {
      padding: 10px;
      margin: 5px 0;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    .endpoint-item:hover {
      background-color: #f0f0f0;
    }
    .endpoint-item.active {
      background-color: #e0e0e0;
      font-weight: bold;
    }
    .method {
      display: inline-block;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 0.8em;
      font-weight: bold;
      margin-right: 8px;
    }
    .get {
      background-color: #61affe;
      color: white;
    }
    .post {
      background-color: #49cc90;
      color: white;
    }
    .put {
      background-color: #fca130;
      color: white;
    }
    .delete {
      background-color: #f93e3e;
      color: white;
    }
    .endpoint-details {
      background-color: #f9f9f9;
      border-radius: 4px;
      padding: 20px;
    }
    .url-display {
      background-color: #333;
      color: white;
      padding: 10px;
      border-radius: 4px;
      font-family: monospace;
      margin-bottom: 20px;
    }
    .response-container {
      margin-top: 20px;
    }
    .response-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
    .response-body {
      background-color: #f0f0f0;
      padding: 10px;
      border-radius: 4px;
      font-family: monospace;
      white-space: pre-wrap;
      max-height: 400px;
      overflow-y: auto;
    }
    .status {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-weight: bold;
    }
    .status-200 {
      background-color: #49cc90;
      color: white;
    }
    .status-404 {
      background-color: #f93e3e;
      color: white;
    }
    .status-500 {
      background-color: #f93e3e;
      color: white;
    }
    .nav-links {
      display: flex;
      justify-content: space-between;
      margin: 20px 0;
    }
    .nav-links a {
      text-decoration: none;
      color: #2196F3;
    }
    button {
      padding: 8px 16px;
      background-color: #2196F3;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #0b7dda;
    }
    .input-group {
      margin-bottom: 10px;
    }
    .input-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    .input-group input, .input-group textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .input-group textarea {
      height: 100px;
      font-family: monospace;
    }
  </style>
</head>
<body>
  <h1>API Viewer</h1>
  <p style="text-align: center;">Explore and test the API endpoints for your application</p>

  <div class="nav-links">
    <a href="api-discovery.html">Back to API Discovery</a>
    <a href="dashboard.html">Back to Dashboard</a>
  </div>

  <div class="container">
    <div class="sidebar">
      <h2>Endpoints</h2>
      <ul class="endpoint-list" id="endpointList">
        <!-- Endpoints will be populated here -->
      </ul>
    </div>
    <div class="content">
      <div class="endpoint-details" id="endpointDetails">
        <h2>Select an endpoint</h2>
        <p>Click on an endpoint from the list to view details and test it.</p>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const endpointList = document.getElementById('endpointList');
      const endpointDetails = document.getElementById('endpointDetails');

      // Define API endpoints
      const endpoints = [
        { method: 'GET', path: '/api/hello', description: 'Basic API health check' },
        { method: 'GET', path: '/api/admin/premium-plans', description: 'Get all premium plans' },
        { method: 'GET', path: '/api/admin/verification-queue', description: 'Get verification queue' },
        { method: 'GET', path: '/api/admin/reported-profiles', description: 'Get reported profiles' },
        { method: 'POST', path: '/api/admin/verification-queue/1/approve', description: 'Approve a verification request',
          body: { reason: 'Documents verified' } },
        { method: 'POST', path: '/api/admin/verification-queue/1/reject', description: 'Reject a verification request',
          body: { reason: 'Invalid documents' } },
        { method: 'POST', path: '/api/admin/reported-profiles/1/resolve', description: 'Resolve a reported profile',
          body: { action: 'warning', message: 'Please follow community guidelines' } },
        { method: 'POST', path: '/api/admin/reported-profiles/1/dismiss', description: 'Dismiss a reported profile',
          body: { reason: 'No violation found' } }
      ];

      // Populate endpoint list
      endpoints.forEach((endpoint, index) => {
        const li = document.createElement('li');
        li.className = 'endpoint-item';
        li.innerHTML = `
          <span class="method ${endpoint.method.toLowerCase()}">${endpoint.method}</span>
          <span>${endpoint.path}</span>
        `;
        li.addEventListener('click', () => showEndpointDetails(endpoint, index));
        endpointList.appendChild(li);
      });

      function showEndpointDetails(endpoint, index) {
        // Update active state in sidebar
        document.querySelectorAll('.endpoint-item').forEach(item => item.classList.remove('active'));
        document.querySelectorAll('.endpoint-item')[index].classList.add('active');

        // Create endpoint details view
        const apiBaseUrl = window.location.origin !== 'file://' ? window.location.origin : 'http://localhost:3001';
        const fullUrl = `${apiBaseUrl}${endpoint.path}`;

        let detailsHTML = `
          <h2>${endpoint.description}</h2>
          <div class="url-display">${endpoint.method} ${fullUrl}</div>

          <div>
            <h3>Request</h3>
        `;

        if (endpoint.method === 'GET') {
          detailsHTML += `
            <p>This endpoint doesn't require a request body.</p>
            <button id="sendRequestBtn">Send Request</button>
          `;
        } else {
          const bodyJson = JSON.stringify(endpoint.body || {}, null, 2);
          detailsHTML += `
            <div class="input-group">
              <label for="requestBody">Request Body (JSON):</label>
              <textarea id="requestBody">${bodyJson}</textarea>
            </div>
            <button id="sendRequestBtn">Send Request</button>
          `;
        }

        detailsHTML += `
          </div>

          <div class="response-container" id="responseContainer" style="display: none;">
            <h3>Response</h3>
            <div class="response-header">
              <span>Status: <span id="responseStatus" class="status"></span></span>
              <span>Time: <span id="responseTime"></span></span>
            </div>
            <div class="response-body" id="responseBody"></div>
          </div>
        `;

        endpointDetails.innerHTML = detailsHTML;

        // Add event listener to send request button
        document.getElementById('sendRequestBtn').addEventListener('click', () => {
          sendRequest(endpoint, fullUrl);
        });
      }

      async function sendRequest(endpoint, url) {
        const responseContainer = document.getElementById('responseContainer');
        const responseStatus = document.getElementById('responseStatus');
        const responseTime = document.getElementById('responseTime');
        const responseBody = document.getElementById('responseBody');

        // Show loading state
        responseContainer.style.display = 'block';
        responseStatus.textContent = 'Loading...';
        responseStatus.className = 'status';
        responseBody.textContent = 'Sending request...';

        const startTime = new Date();

        try {
          const options = {
            method: endpoint.method,
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            },
            mode: 'cors'
          };

          // Add request body for non-GET requests
          if (endpoint.method !== 'GET') {
            const requestBodyElem = document.getElementById('requestBody');
            if (requestBodyElem) {
              try {
                const jsonBody = JSON.parse(requestBodyElem.value);
                options.body = JSON.stringify(jsonBody);
                console.log('Request body:', options.body);
              } catch (e) {
                responseStatus.textContent = 'Error';
                responseStatus.className = 'status status-500';
                responseBody.textContent = 'Invalid JSON in request body';
                return;
              }
            }
          }

          console.log(`Sending ${endpoint.method} request to ${url}`, options);

          const response = await fetch(url, options);
          const endTime = new Date();
          const duration = endTime - startTime;

          console.log(`Response received:`, response);

          // Update response status
          responseStatus.textContent = response.status;
          responseStatus.className = `status status-${response.status}`;
          responseTime.textContent = `${duration}ms`;

          // Update response body
          try {
            const data = await response.json();
            console.log('Response data:', data);
            responseBody.textContent = JSON.stringify(data, null, 2);
          } catch (e) {
            console.error('Error parsing response:', e);
            const text = await response.text();
            responseBody.textContent = text || '(No response body)';
          }
        } catch (error) {
          console.error('Fetch error:', error);
          const endTime = new Date();
          const duration = endTime - startTime;

          responseStatus.textContent = 'Error';
          responseStatus.className = 'status status-500';
          responseTime.textContent = `${duration}ms`;
          responseBody.textContent = `Error: ${error.message}`;
        }
      }

      // Show first endpoint by default
      if (endpoints.length > 0) {
        showEndpointDetails(endpoints[0], 0);
      }
    });
  </script>
</body>
</html>
