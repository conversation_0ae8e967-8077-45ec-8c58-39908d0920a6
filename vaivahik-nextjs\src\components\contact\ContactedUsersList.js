/**
 * Contacted Users List Component
 * Shows users whose contact details have been accessed
 * Helps users track their contact reveals and calling history
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Typography,
  Box,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
  Divider,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Phone as PhoneIcon,
  History as HistoryIcon,
  Person as PersonIcon,
  AccessTime as AccessTimeIcon,
  Security as SecurityIcon,
  Visibility as VisibilityIcon,
  Close as CloseIcon,
  CallMade as CallMadeIcon
} from '@mui/icons-material';
import { contactApi, contactUtils } from '@/services/contactApiService';

const ContactedUsersList = ({ 
  userId, 
  showTitle = true,
  maxItems = 10,
  showCallButton = true 
}) => {
  const [contactHistory, setContactHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedContact, setSelectedContact] = useState(null);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadContactHistory();
  }, [userId]);

  const loadContactHistory = async () => {
    try {
      setLoading(true);
      const result = await contactApi.getAccessHistory(maxItems);
      
      if (result.success) {
        setContactHistory(result.history);
      } else {
        setError('Failed to load contact history');
      }
    } catch (error) {
      console.error('Error loading contact history:', error);
      setError('Unable to load contact history');
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetails = (contact) => {
    setSelectedContact(contact);
    setShowDetailsDialog(true);
  };

  const handleCallAgain = async (contact) => {
    try {
      // Re-reveal contact and open dialer
      const result = await contactApi.revealContact(contact.otherUser.id);
      if (result.success) {
        contactUtils.openDialer(result.contactNumber);
      }
    } catch (error) {
      console.error('Error calling user:', error);
    }
  };

  const formatAccessTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffHours < 1) return 'Just now';
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  const getAccessTypeIcon = (accessType) => {
    switch (accessType) {
      case 'CONTACT_REVEAL': return <VisibilityIcon />;
      case 'CALL_ATTEMPT': return <CallMadeIcon />;
      default: return <PhoneIcon />;
    }
  };

  const getAccessTypeColor = (accessType) => {
    switch (accessType) {
      case 'CONTACT_REVEAL': return 'primary';
      case 'CALL_ATTEMPT': return 'success';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent>
          <Alert severity="error">{error}</Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        {showTitle && (
          <CardHeader
            avatar={<HistoryIcon />}
            title="Contacted Users"
            subheader={`${contactHistory.length} contact${contactHistory.length !== 1 ? 's' : ''} accessed`}
          />
        )}
        
        <CardContent>
          {contactHistory.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <PersonIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                No contacts accessed yet
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Contact details you access will appear here
              </Typography>
            </Box>
          ) : (
            <List>
              {contactHistory.map((contact, index) => (
                <React.Fragment key={contact.id}>
                  <ListItem
                    sx={{
                      px: 0,
                      '&:hover': {
                        backgroundColor: 'grey.50',
                        borderRadius: 1
                      }
                    }}
                  >
                    <ListItemAvatar>
                      <Avatar
                        src={contact.otherUser.profilePic}
                        sx={{ bgcolor: 'primary.main' }}
                      >
                        {contact.otherUser.name?.charAt(0) || 'U'}
                      </Avatar>
                    </ListItemAvatar>
                    
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="subtitle1">
                            {contact.otherUser.name}
                          </Typography>
                          <Chip
                            icon={getAccessTypeIcon(contact.accessType)}
                            label={contact.accessType.replace('_', ' ')}
                            size="small"
                            color={getAccessTypeColor(contact.accessType)}
                          />
                        </Box>
                      }
                      secondary={
                        <Box sx={{ mt: 0.5 }}>
                          <Typography variant="body2" color="text.secondary">
                            Accessed {formatAccessTime(contact.accessedAt)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Platform: {contact.platform} • Reason: {contact.accessReason}
                          </Typography>
                        </Box>
                      }
                    />
                    
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          onClick={() => handleViewDetails(contact)}
                        >
                          <VisibilityIcon />
                        </IconButton>
                      </Tooltip>
                      
                      {showCallButton && (
                        <Tooltip title="Call Again">
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => handleCallAgain(contact)}
                          >
                            <PhoneIcon />
                          </IconButton>
                        </Tooltip>
                      )}
                    </Box>
                  </ListItem>
                  
                  {index < contactHistory.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          )}
        </CardContent>
      </Card>

      {/* Contact Details Dialog */}
      <Dialog
        open={showDetailsDialog}
        onClose={() => setShowDetailsDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <HistoryIcon />
            <Typography variant="h6">Contact Access Details</Typography>
          </Box>
          <IconButton onClick={() => setShowDetailsDialog(false)} size="small">
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent>
          {selectedContact && (
            <Box>
              {/* User Info */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                <Avatar
                  src={selectedContact.otherUser.profilePic}
                  sx={{ width: 64, height: 64 }}
                >
                  {selectedContact.otherUser.name?.charAt(0) || 'U'}
                </Avatar>
                <Box>
                  <Typography variant="h6">
                    {selectedContact.otherUser.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Profile ID: {selectedContact.otherUser.id}
                  </Typography>
                </Box>
              </Box>

              {/* Access Details */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Access Information
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <Chip
                    icon={getAccessTypeIcon(selectedContact.accessType)}
                    label={`${selectedContact.accessType.replace('_', ' ')}`}
                    color={getAccessTypeColor(selectedContact.accessType)}
                    size="small"
                  />
                  <Typography variant="body2">
                    <strong>Accessed:</strong> {new Date(selectedContact.accessedAt).toLocaleString()}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Platform:</strong> {selectedContact.platform}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Access Reason:</strong> {selectedContact.accessReason}
                  </Typography>
                </Box>
              </Box>

              {/* Security Info */}
              <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <SecurityIcon color="info" />
                  <Typography variant="subtitle2">Security & Privacy</Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  This contact access was logged for security purposes. 
                  All contact reveals are tracked to prevent abuse and protect user privacy.
                </Typography>
              </Box>
            </Box>
          )}
        </DialogContent>

        <DialogActions>
          {selectedContact && showCallButton && (
            <Button
              variant="contained"
              startIcon={<PhoneIcon />}
              onClick={() => {
                handleCallAgain(selectedContact);
                setShowDetailsDialog(false);
              }}
            >
              Call Again
            </Button>
          )}
          <Button onClick={() => setShowDetailsDialog(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ContactedUsersList;
