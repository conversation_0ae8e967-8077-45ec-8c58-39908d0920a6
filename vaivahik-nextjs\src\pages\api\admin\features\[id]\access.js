// API endpoint for feature access rules
import { generateMockFeatures } from '@/utils/mockData';

export default function handler(req, res) {
  // Get the feature ID from the URL
  const { id } = req.query;
  
  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getFeatureAccessRules(req, res, id);
    case 'PUT':
      return updateFeatureAccessRules(req, res, id);
    default:
      return res.status(405).json({ message: 'Method not allowed' });
  }
}

// GET /api/admin/features/[id]/access
function getFeatureAccessRules(req, res, id) {
  try {
    // In a real implementation, this would fetch data from a database
    // For now, we'll use mock data
    const mockFeatures = generateMockFeatures();
    
    // Find the feature by ID
    const feature = mockFeatures.find(f => f.id.toString() === id);
    
    // If feature not found, return 404
    if (!feature) {
      return res.status(404).json({ 
        success: false, 
        message: 'Feature not found' 
      });
    }
    
    // Return the feature access rules
    return res.status(200).json({
      success: true,
      featureId: id,
      accessRules: feature.accessRules
    });
  } catch (error) {
    console.error('Error fetching feature access rules:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch feature access rules' 
    });
  }
}

// PUT /api/admin/features/[id]/access
function updateFeatureAccessRules(req, res, id) {
  try {
    // Get access rules from request body
    const accessRules = req.body;
    
    // Validate access rules
    if (!accessRules || !accessRules.basic || !accessRules.verified || !accessRules.premium) {
      return res.status(400).json({ 
        success: false, 
        message: 'Access rules for basic, verified, and premium tiers are required' 
      });
    }
    
    // In a real implementation, this would update the feature access rules in the database
    // For now, we'll just return a success response
    
    return res.status(200).json({
      success: true,
      message: 'Feature access rules updated successfully',
      featureId: id,
      accessRules
    });
  } catch (error) {
    console.error('Error updating feature access rules:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to update feature access rules' 
    });
  }
}
