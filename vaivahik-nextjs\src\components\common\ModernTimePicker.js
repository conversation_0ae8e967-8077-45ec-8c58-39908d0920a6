import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  IconButton,
  InputAdornment,
  Popover,
  Paper,
  Typography,
  Button,
  styled,
  useTheme
} from '@mui/material';
import {
  AccessTime as TimeIcon,
  KeyboardArrowUp as ArrowUpIcon,
  KeyboardArrowDown as ArrowDownIcon
} from '@mui/icons-material';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  borderRadius: 16,
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  border: `1px solid ${theme.palette.divider}`,
  minWidth: 280
}));

const TimeButton = styled(Button)(({ theme }) => ({
  minWidth: 60,
  height: 48,
  borderRadius: 12,
  fontSize: '1.1rem',
  fontWeight: 600,
  border: `2px solid ${theme.palette.divider}`,
  backgroundColor: theme.palette.background.paper,
  color: theme.palette.text.primary,
  '&:hover': {
    backgroundColor: theme.palette.primary.light,
    color: theme.palette.primary.contrastText,
    borderColor: theme.palette.primary.main
  },
  '&.selected': {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    borderColor: theme.palette.primary.main
  }
}));

const AmPmButton = styled(Button)(({ theme }) => ({
  minWidth: 50,
  height: 40,
  borderRadius: 8,
  fontSize: '0.9rem',
  fontWeight: 600,
  border: `2px solid ${theme.palette.divider}`,
  backgroundColor: theme.palette.background.paper,
  color: theme.palette.text.primary,
  '&:hover': {
    backgroundColor: theme.palette.primary.light,
    color: theme.palette.primary.contrastText,
    borderColor: theme.palette.primary.main
  },
  '&.selected': {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    borderColor: theme.palette.primary.main
  }
}));

const ModernTimePicker = ({ 
  value, 
  onChange, 
  placeholder = "Select time",
  fullWidth = true,
  error = false,
  helperText = "",
  ...props 
}) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedHour, setSelectedHour] = useState(12);
  const [selectedMinute, setSelectedMinute] = useState(0);
  const [selectedAmPm, setSelectedAmPm] = useState('AM');

  // Initialize from value
  useEffect(() => {
    if (value && value instanceof Date && !isNaN(value.getTime())) {
      const hours = value.getHours();
      const minutes = value.getMinutes();

      setSelectedHour(hours === 0 ? 12 : hours > 12 ? hours - 12 : hours);
      setSelectedMinute(minutes);
      setSelectedAmPm(hours >= 12 ? 'PM' : 'AM');
    } else if (!value) {
      // Reset to default values when value is cleared
      setSelectedHour(12);
      setSelectedMinute(0);
      setSelectedAmPm('AM');
    }
  }, [value]);

  const formatTime = (hour, minute, ampm) => {
    const formattedHour = hour.toString().padStart(2, '0');
    const formattedMinute = minute.toString().padStart(2, '0');
    return `${formattedHour}:${formattedMinute} ${ampm}`;
  };

  const handleOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleTimeSelect = () => {
    try {
      const hour24 = selectedAmPm === 'AM'
        ? (selectedHour === 12 ? 0 : selectedHour)
        : (selectedHour === 12 ? 12 : selectedHour + 12);

      const newDate = new Date();
      newDate.setHours(hour24, selectedMinute, 0, 0);

      // Validate the created date
      if (!isNaN(newDate.getTime())) {
        onChange(newDate);
        handleClose();
      } else {
        console.error('Invalid time created');
      }
    } catch (error) {
      console.error('Error creating time:', error);
    }
  };

  const displayValue = value 
    ? formatTime(selectedHour, selectedMinute, selectedAmPm)
    : '';

  const hours = Array.from({ length: 12 }, (_, i) => i + 1);
  const minutes = Array.from({ length: 60 }, (_, i) => i);

  return (
    <>
      <TextField
        value={displayValue}
        placeholder={placeholder}
        fullWidth={fullWidth}
        error={error}
        helperText={helperText}
        onClick={handleOpen}
        readOnly
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <IconButton onClick={handleOpen} edge="end">
                <TimeIcon />
              </IconButton>
            </InputAdornment>
          ),
          sx: {
            cursor: 'pointer',
            '& .MuiOutlinedInput-root': {
              borderRadius: 12,
            }
          }
        }}
        sx={{
          '& .MuiOutlinedInput-input': {
            cursor: 'pointer'
          }
        }}
        {...props}
      />

      <Popover
        open={Boolean(anchorEl)}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
      >
        <StyledPaper>
          <Typography variant="h6" gutterBottom align="center">
            Select Time
          </Typography>
          
          <Grid container spacing={1} alignItems="flex-start">
            {/* Hours */}
            <Grid item xs={4}>
              <Typography variant="subtitle2" align="center" gutterBottom>
                Hour
              </Typography>
              <Box sx={{ maxHeight: 200, overflowY: 'auto' }}>
                <Grid container spacing={1}>
                  {hours.map((hour) => (
                    <Grid item xs={6} key={hour}>
                      <TimeButton
                        size="small"
                        className={selectedHour === hour ? 'selected' : ''}
                        onClick={() => setSelectedHour(hour)}
                      >
                        {hour}
                      </TimeButton>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            </Grid>

            {/* Minutes */}
            <Grid item xs={4}>
              <Typography variant="subtitle2" align="center" gutterBottom>
                Minute
              </Typography>
              <Box sx={{ maxHeight: 250, overflowY: 'auto' }}>
                <Grid container spacing={0.5}>
                  {minutes.map((minute) => (
                    <Grid item xs={4} key={minute}>
                      <TimeButton
                        size="small"
                        className={selectedMinute === minute ? 'selected' : ''}
                        onClick={() => setSelectedMinute(minute)}
                        sx={{
                          minWidth: '32px',
                          height: '32px',
                          fontSize: '0.75rem',
                          padding: '4px'
                        }}
                      >
                        {minute.toString().padStart(2, '0')}
                      </TimeButton>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            </Grid>

            {/* AM/PM */}
            <Grid item xs={4}>
              <Typography variant="subtitle2" align="center" gutterBottom>
                Period
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <AmPmButton
                  className={selectedAmPm === 'AM' ? 'selected' : ''}
                  onClick={() => setSelectedAmPm('AM')}
                >
                  AM
                </AmPmButton>
                <AmPmButton
                  className={selectedAmPm === 'PM' ? 'selected' : ''}
                  onClick={() => setSelectedAmPm('PM')}
                >
                  PM
                </AmPmButton>
              </Box>
            </Grid>
          </Grid>

          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between' }}>
            <Button onClick={handleClose} color="inherit">
              Cancel
            </Button>
            <Button 
              onClick={handleTimeSelect} 
              variant="contained"
              sx={{ borderRadius: 2 }}
            >
              Select
            </Button>
          </Box>
        </StyledPaper>
      </Popover>
    </>
  );
};

export default ModernTimePicker;
