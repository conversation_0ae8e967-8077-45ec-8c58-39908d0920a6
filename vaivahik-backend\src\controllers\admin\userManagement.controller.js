/**
 * Admin User Management Controller
 *
 * This controller handles admin operations for managing users,
 * including updating locked fields that regular users cannot change.
 */

const { LOCKED_FIELDS, getLockedFieldChanges } = require('../../utils/fieldLocking');

/**
 * Get all users with pagination and filtering
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.getAllUsers = async (req, res, next) => {
  const prisma = req.prisma;
  const { page = 1, limit = 10, search, gender, status } = req.query;

  try {
    // Convert page and limit to numbers
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Build filter conditions
    const where = {};

    if (search) {
      where.OR = [
        { email: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search } },
        { profile: { fullName: { contains: search, mode: 'insensitive' } } }
      ];
    }

    if (gender) {
      where.profile = {
        ...where.profile,
        gender
      };
    }

    if (status) {
      where.status = status;
    }

    // Get users with pagination
    const [users, totalCount] = await Promise.all([
      prisma.user.findMany({
        where,
        include: {
          profile: true
        },
        skip,
        take: limitNum,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where })
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limitNum);

    res.status(200).json({
      success: true,
      message: 'Users retrieved successfully',
      data: {
        users,
        pagination: {
          total: totalCount,
          page: pageNum,
          limit: limitNum,
          totalPages
        }
      }
    });
  } catch (error) {
    console.error('Error getting users:', error);
    next(error);
  }
};

/**
 * Get a specific user by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.getUserById = async (req, res, next) => {
  const prisma = req.prisma;
  const { userId } = req.params;

  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        profile: true,
        photos: true,
        documents: true
      }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'User retrieved successfully',
      data: { user }
    });
  } catch (error) {
    console.error('Error getting user:', error);
    next(error);
  }
};

/**
 * Update a user's basic details (including locked fields)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.updateUserBasicDetails = async (req, res, next) => {
  const prisma = req.prisma;
  const { userId } = req.params;
  const adminId = req.admin.adminId;

  try {
    // Get the user's existing profile
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { profile: true }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if any locked fields are being changed
    const lockedChanges = getLockedFieldChanges(user.profile, req.body);

    // Extract all fields from the request body
    const {
      // Basic details
      fullName, gender, dateOfBirth, maritalStatus, height,
      // Community/Background
      religion, caste, subCaste, gotra, kul, motherTongue, marathiProficiency,
      // Physical
      diet, bloodGroup,
      // Other fields
      profileFor
    } = req.body;

    // Prepare data for update
    const basicDetailsToUpdate = {
      // Basic details
      fullName,
      gender,
      dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
      maritalStatus,
      height,

      // Community/Background
      religion,
      caste,
      subCaste,
      gotra,
      kul,
      motherTongue,
      marathiProficiency,

      // Physical
      diet,
      bloodGroup,

      // Other fields
      profileFor
    };

    // Remove undefined values
    Object.keys(basicDetailsToUpdate).forEach(key => {
      if (basicDetailsToUpdate[key] === undefined) {
        delete basicDetailsToUpdate[key];
      }
    });

    // Update the user's profile
    const updatedProfile = await prisma.profile.update({
      where: { userId },
      data: basicDetailsToUpdate
    });

    // Log all changes made by admin
    const oldValues = {};
    const newValues = {};

    Object.keys(basicDetailsToUpdate).forEach(key => {
      if (user.profile && user.profile[key] !== basicDetailsToUpdate[key]) {
        oldValues[key] = user.profile[key];
        newValues[key] = basicDetailsToUpdate[key];
      }
    });

    // Create audit log entry if changes were made
    if (Object.keys(newValues).length > 0) {
      await prisma.adminAuditLog.create({
        data: {
          adminId,
          userId,
          action: 'UPDATE_USER_PROFILE',
          details: {
            section: 'basicDetails',
            oldValues,
            newValues,
            lockedFieldsChanged: Object.keys(lockedChanges).length > 0 ? Object.keys(lockedChanges) : []
          }
        }
      });
    }

    res.status(200).json({
      success: true,
      message: 'User basic details updated successfully',
      data: { profile: updatedProfile }
    });
  } catch (error) {
    console.error('Error updating user basic details:', error);
    next(error);
  }
};

/**
 * Update a user's location details (including locked fields)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.updateUserLocationDetails = async (req, res, next) => {
  const prisma = req.prisma;
  const { userId } = req.params;
  const adminId = req.admin.adminId;

  try {
    // Get the user's existing profile
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { profile: true }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if any locked fields are being changed
    const lockedChanges = getLockedFieldChanges(user.profile, req.body);

    // Extract all location fields from the request body
    const {
      // Basic location
      city, state, country, pincode,
      // Birth details (locked fields)
      birthPlace, birthTime,
      // Additional location details
      currentLocation, permanentLocation, nativePlace, nativeDistrict,
      // Coordinates
      latitude, longitude,
      // Other fields
      maharashtrianOrigin
    } = req.body;

    // Prepare data for update
    const locationDetailsToUpdate = {
      // Basic location
      city,
      state,
      country,
      pincode,

      // Birth details (locked fields)
      birthPlace,
      birthTime: birthTime ? new Date(birthTime) : undefined,

      // Additional location details
      currentLocation,
      permanentLocation,
      nativePlace,
      nativeDistrict,

      // Coordinates
      latitude: latitude ? parseFloat(latitude) : undefined,
      longitude: longitude ? parseFloat(longitude) : undefined,

      // Other fields
      maharashtrianOrigin: maharashtrianOrigin !== undefined ? Boolean(maharashtrianOrigin) : undefined
    };

    // Remove undefined values
    Object.keys(locationDetailsToUpdate).forEach(key => {
      if (locationDetailsToUpdate[key] === undefined) {
        delete locationDetailsToUpdate[key];
      }
    });

    // Update the user's profile
    const updatedProfile = await prisma.profile.update({
      where: { userId },
      data: locationDetailsToUpdate
    });

    // Log all changes made by admin
    const oldValues = {};
    const newValues = {};

    Object.keys(locationDetailsToUpdate).forEach(key => {
      if (user.profile && user.profile[key] !== locationDetailsToUpdate[key]) {
        oldValues[key] = user.profile[key];
        newValues[key] = locationDetailsToUpdate[key];
      }
    });

    // Create audit log entry if changes were made
    if (Object.keys(newValues).length > 0) {
      await prisma.adminAuditLog.create({
        data: {
          adminId,
          userId,
          action: 'UPDATE_USER_PROFILE',
          details: {
            section: 'locationDetails',
            oldValues,
            newValues,
            lockedFieldsChanged: Object.keys(lockedChanges).length > 0 ? Object.keys(lockedChanges) : []
          }
        }
      });
    }

    res.status(200).json({
      success: true,
      message: 'User location details updated successfully',
      data: { profile: updatedProfile }
    });
  } catch (error) {
    console.error('Error updating user location details:', error);
    next(error);
  }
};

/**
 * Update a user's critical fields (specifically for locked fields)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.updateUserCriticalFields = async (req, res, next) => {
  const prisma = req.prisma;
  const { userId } = req.params;
  const adminId = req.admin.adminId;
  const { gender, dateOfBirth, birthTime, birthPlace } = req.body;

  try {
    // Get the user's existing profile
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { profile: true }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if any locked fields are being changed
    const lockedChanges = getLockedFieldChanges(user.profile, req.body);

    // Prepare data for update (only include fields that are provided)
    const criticalFieldsToUpdate = {};

    if (gender !== undefined) criticalFieldsToUpdate.gender = gender;
    if (dateOfBirth !== undefined) criticalFieldsToUpdate.dateOfBirth = new Date(dateOfBirth);
    if (birthTime !== undefined) criticalFieldsToUpdate.birthTime = new Date(birthTime);
    if (birthPlace !== undefined) criticalFieldsToUpdate.birthPlace = birthPlace;

    // Update the user's profile
    const updatedProfile = await prisma.profile.update({
      where: { userId },
      data: criticalFieldsToUpdate
    });

    // Log the changes to locked fields
    await prisma.adminAuditLog.create({
      data: {
        adminId,
        userId,
        action: 'UPDATE_CRITICAL_FIELDS',
        details: {
          fields: Object.keys(criticalFieldsToUpdate),
          changes: lockedChanges,
          formType: 'criticalFields'
        }
      }
    });

    res.status(200).json({
      success: true,
      message: 'User critical fields updated successfully',
      data: { profile: updatedProfile }
    });
  } catch (error) {
    console.error('Error updating user critical fields:', error);
    next(error);
  }
};

/**
 * Update a user's family details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.updateUserFamilyDetails = async (req, res, next) => {
  const prisma = req.prisma;
  const { userId } = req.params;
  const adminId = req.admin.adminId;

  try {
    // Get the user's existing profile
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { profile: true }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Extract all family fields from the request body
    const {
      familyType, familyStatus,
      fatherName, fatherOccupation,
      motherName, motherOccupation,
      uncleName, siblings, familyContact
    } = req.body;

    // Prepare data for update
    const familyDetailsToUpdate = {
      familyType,
      familyStatus,
      fatherName,
      fatherOccupation,
      motherName,
      motherOccupation,
      uncleName,
      siblings,
      familyContact
    };

    // Remove undefined values
    Object.keys(familyDetailsToUpdate).forEach(key => {
      if (familyDetailsToUpdate[key] === undefined) {
        delete familyDetailsToUpdate[key];
      }
    });

    // Update the user's profile
    const updatedProfile = await prisma.profile.update({
      where: { userId },
      data: familyDetailsToUpdate
    });

    // Log all changes made by admin
    const oldValues = {};
    const newValues = {};

    Object.keys(familyDetailsToUpdate).forEach(key => {
      if (user.profile && user.profile[key] !== familyDetailsToUpdate[key]) {
        oldValues[key] = user.profile[key];
        newValues[key] = familyDetailsToUpdate[key];
      }
    });

    // Create audit log entry if changes were made
    if (Object.keys(newValues).length > 0) {
      await prisma.adminAuditLog.create({
        data: {
          adminId,
          userId,
          action: 'UPDATE_USER_PROFILE',
          details: {
            section: 'familyDetails',
            oldValues,
            newValues
          }
        }
      });
    }

    res.status(200).json({
      success: true,
      message: 'User family details updated successfully',
      data: { profile: updatedProfile }
    });
  } catch (error) {
    console.error('Error updating user family details:', error);
    next(error);
  }
};

/**
 * Update a user's education and career details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.updateUserEducationCareer = async (req, res, next) => {
  const prisma = req.prisma;
  const { userId } = req.params;
  const adminId = req.admin.adminId;

  try {
    // Get the user's existing profile
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { profile: true }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Extract all education and career fields from the request body
    const {
      highestEducation, occupation, annualIncome
    } = req.body;

    // Prepare data for update
    const educationCareerToUpdate = {
      highestEducation,
      occupation,
      annualIncome
    };

    // Remove undefined values
    Object.keys(educationCareerToUpdate).forEach(key => {
      if (educationCareerToUpdate[key] === undefined) {
        delete educationCareerToUpdate[key];
      }
    });

    // Update the user's profile
    const updatedProfile = await prisma.profile.update({
      where: { userId },
      data: educationCareerToUpdate
    });

    // Log all changes made by admin
    const oldValues = {};
    const newValues = {};

    Object.keys(educationCareerToUpdate).forEach(key => {
      if (user.profile && user.profile[key] !== educationCareerToUpdate[key]) {
        oldValues[key] = user.profile[key];
        newValues[key] = educationCareerToUpdate[key];
      }
    });

    // Create audit log entry if changes were made
    if (Object.keys(newValues).length > 0) {
      await prisma.adminAuditLog.create({
        data: {
          adminId,
          userId,
          action: 'UPDATE_USER_PROFILE',
          details: {
            section: 'educationCareer',
            oldValues,
            newValues
          }
        }
      });
    }

    res.status(200).json({
      success: true,
      message: 'User education and career details updated successfully',
      data: { profile: updatedProfile }
    });
  } catch (error) {
    console.error('Error updating user education and career details:', error);
    next(error);
  }
};

/**
 * Update a user's lifestyle and interests
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.updateUserLifestyleInterests = async (req, res, next) => {
  const prisma = req.prisma;
  const { userId } = req.params;
  const adminId = req.admin.adminId;

  try {
    // Get the user's existing profile
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { profile: true }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Extract all lifestyle and interests fields from the request body
    const {
      hobbies, interests, aboutMe
    } = req.body;

    // Prepare data for update
    const lifestyleInterestsToUpdate = {
      hobbies,
      interests,
      aboutMe
    };

    // Remove undefined values
    Object.keys(lifestyleInterestsToUpdate).forEach(key => {
      if (lifestyleInterestsToUpdate[key] === undefined) {
        delete lifestyleInterestsToUpdate[key];
      }
    });

    // Update the user's profile
    const updatedProfile = await prisma.profile.update({
      where: { userId },
      data: lifestyleInterestsToUpdate
    });

    // Log all changes made by admin
    const oldValues = {};
    const newValues = {};

    Object.keys(lifestyleInterestsToUpdate).forEach(key => {
      if (user.profile && user.profile[key] !== lifestyleInterestsToUpdate[key]) {
        oldValues[key] = user.profile[key];
        newValues[key] = lifestyleInterestsToUpdate[key];
      }
    });

    // Create audit log entry if changes were made
    if (Object.keys(newValues).length > 0) {
      await prisma.adminAuditLog.create({
        data: {
          adminId,
          userId,
          action: 'UPDATE_USER_PROFILE',
          details: {
            section: 'lifestyleInterests',
            oldValues,
            newValues
          }
        }
      });
    }

    res.status(200).json({
      success: true,
      message: 'User lifestyle and interests updated successfully',
      data: { profile: updatedProfile }
    });
  } catch (error) {
    console.error('Error updating user lifestyle and interests:', error);
    next(error);
  }
};

/**
 * Update a user's partner preferences
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.updateUserPartnerPreferences = async (req, res, next) => {
  const prisma = req.prisma;
  const { userId } = req.params;
  const adminId = req.admin.adminId;

  try {
    // Get the user's existing preferences
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { preference: true }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Extract all partner preference fields from the request body
    const {
      ageMin, ageMax, heightMin, heightMax,
      educationLevel, occupations, incomeMin,
      preferredCities, preferredStates,
      acceptSubCastes, gotraPreference,
      dietPreference, hobbiesPreference, interestsPreference,
      otherPreferences
    } = req.body;

    // Prepare data for update or create
    const preferencesToUpdate = {
      ageMin: ageMin ? parseInt(ageMin) : undefined,
      ageMax: ageMax ? parseInt(ageMax) : undefined,
      heightMin,
      heightMax,
      educationLevel: educationLevel ? (Array.isArray(educationLevel) ? educationLevel : [educationLevel]) : undefined,
      occupations: occupations ? (Array.isArray(occupations) ? occupations : [occupations]) : undefined,
      incomeMin,
      preferredCities: preferredCities ? (Array.isArray(preferredCities) ? preferredCities : [preferredCities]) : undefined,
      preferredStates: preferredStates ? (Array.isArray(preferredStates) ? preferredStates : [preferredStates]) : undefined,
      acceptSubCastes: acceptSubCastes ? (Array.isArray(acceptSubCastes) ? acceptSubCastes : [acceptSubCastes]) : undefined,
      gotraPreference,
      dietPreference,
      hobbiesPreference,
      interestsPreference,
      otherPreferences
    };

    // Remove undefined values
    Object.keys(preferencesToUpdate).forEach(key => {
      if (preferencesToUpdate[key] === undefined) {
        delete preferencesToUpdate[key];
      }
    });

    // Update or create the user's preferences
    let updatedPreferences;

    if (user.preference) {
      // Update existing preferences
      updatedPreferences = await prisma.preference.update({
        where: { userId },
        data: preferencesToUpdate
      });
    } else {
      // Create new preferences
      updatedPreferences = await prisma.preference.create({
        data: {
          ...preferencesToUpdate,
          user: { connect: { id: userId } }
        }
      });
    }

    // Log all changes made by admin
    const oldValues = {};
    const newValues = {};

    Object.keys(preferencesToUpdate).forEach(key => {
      if (user.preference && JSON.stringify(user.preference[key]) !== JSON.stringify(preferencesToUpdate[key])) {
        oldValues[key] = user.preference[key];
        newValues[key] = preferencesToUpdate[key];
      } else if (!user.preference) {
        newValues[key] = preferencesToUpdate[key];
      }
    });

    // Create audit log entry if changes were made
    if (Object.keys(newValues).length > 0) {
      await prisma.adminAuditLog.create({
        data: {
          adminId,
          userId,
          action: 'UPDATE_USER_PREFERENCES',
          details: {
            oldValues,
            newValues
          }
        }
      });
    }

    res.status(200).json({
      success: true,
      message: 'User partner preferences updated successfully',
      data: { preferences: updatedPreferences }
    });
  } catch (error) {
    console.error('Error updating user partner preferences:', error);
    next(error);
  }
};

/**
 * Update a user's account details (email, phone, etc.)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.updateUserAccount = async (req, res, next) => {
  const prisma = req.prisma;
  const { userId } = req.params;
  const adminId = req.admin.adminId;
  const { email, phone, isVerified, isPremium } = req.body;

  try {
    // Get the user
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check for email uniqueness if changing email
    if (email && email !== user.email) {
      const existingUserWithEmail = await prisma.user.findUnique({
        where: { email }
      });

      if (existingUserWithEmail) {
        return res.status(409).json({
          success: false,
          message: 'Email is already in use by another user'
        });
      }
    }

    // Check for phone uniqueness if changing phone
    if (phone && phone !== user.phone) {
      const existingUserWithPhone = await prisma.user.findUnique({
        where: { phone }
      });

      if (existingUserWithPhone) {
        return res.status(409).json({
          success: false,
          message: 'Phone number is already in use by another user'
        });
      }
    }

    // Prepare data for update
    const accountToUpdate = {
      email,
      phone,
      isVerified: isVerified !== undefined ? Boolean(isVerified) : undefined,
      isPremium: isPremium !== undefined ? Boolean(isPremium) : undefined
    };

    // Remove undefined values
    Object.keys(accountToUpdate).forEach(key => {
      if (accountToUpdate[key] === undefined) {
        delete accountToUpdate[key];
      }
    });

    // Update the user's account
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: accountToUpdate
    });

    // Log all changes made by admin
    const oldValues = {};
    const newValues = {};

    Object.keys(accountToUpdate).forEach(key => {
      if (user[key] !== accountToUpdate[key]) {
        oldValues[key] = user[key];
        newValues[key] = accountToUpdate[key];
      }
    });

    // Create audit log entry if changes were made
    if (Object.keys(newValues).length > 0) {
      await prisma.adminAuditLog.create({
        data: {
          adminId,
          userId,
          action: 'UPDATE_USER_ACCOUNT',
          details: {
            oldValues,
            newValues
          }
        }
      });
    }

    res.status(200).json({
      success: true,
      message: 'User account details updated successfully',
      data: { user: updatedUser }
    });
  } catch (error) {
    console.error('Error updating user account details:', error);
    next(error);
  }
};

/**
 * Update a user's account status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.updateUserStatus = async (req, res, next) => {
  const prisma = req.prisma;
  const { userId } = req.params;
  const adminId = req.admin.adminId;
  const { status, reason } = req.body;

  try {
    // Get the user
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Validate status
    const validStatuses = ['INCOMPLETE', 'PENDING_APPROVAL', 'ACTIVE', 'SUSPENDED', 'INACTIVE'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: `Invalid status. Must be one of: ${validStatuses.join(', ')}`
      });
    }

    // Update the user's status
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { profileStatus: status }
    });

    // Log the status change
    await prisma.adminAuditLog.create({
      data: {
        adminId,
        userId,
        action: 'UPDATE_USER_STATUS',
        details: {
          oldStatus: user.profileStatus,
          newStatus: status,
          reason: reason || 'No reason provided'
        }
      }
    });

    // If status is SUSPENDED, create a notification for the user
    if (status === 'SUSPENDED') {
      await prisma.notification.create({
        data: {
          userId,
          title: 'Account Suspended',
          body: reason
            ? `Your account has been suspended. Reason: ${reason}`
            : 'Your account has been suspended. Please contact support for more information.',
          data: {
            type: 'ACCOUNT_STATUS',
            status: 'SUSPENDED'
          }
        }
      });
    }

    // If status is changed from SUSPENDED to ACTIVE, create a notification
    if (user.profileStatus === 'SUSPENDED' && status === 'ACTIVE') {
      await prisma.notification.create({
        data: {
          userId,
          title: 'Account Reactivated',
          body: 'Your account has been reactivated. You can now use all features of the platform.',
          data: {
            type: 'ACCOUNT_STATUS',
            status: 'ACTIVE'
          }
        }
      });
    }

    res.status(200).json({
      success: true,
      message: `User status updated to ${status} successfully`,
      data: { user: updatedUser }
    });
  } catch (error) {
    console.error('Error updating user status:', error);
    next(error);
  }
};

/**
 * Get audit logs for user changes
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.getAuditLogs = async (req, res, next) => {
  const prisma = req.prisma;
  const { page = 1, limit = 10, userId, action } = req.query;

  try {
    // Convert page and limit to numbers
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Build filter conditions
    const where = {};

    if (userId) {
      where.userId = userId;
    }

    if (action) {
      where.action = action;
    }

    // Get audit logs with pagination
    const [logs, totalCount] = await Promise.all([
      prisma.adminAuditLog.findMany({
        where,
        include: {
          admin: {
            select: { email: true, name: true }
          },
          user: {
            select: { email: true, phone: true, profile: { select: { fullName: true } } }
          }
        },
        skip,
        take: limitNum,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.adminAuditLog.count({ where })
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limitNum);

    res.status(200).json({
      success: true,
      message: 'Audit logs retrieved successfully',
      data: {
        logs,
        pagination: {
          total: totalCount,
          page: pageNum,
          limit: limitNum,
          totalPages
        }
      }
    });
  } catch (error) {
    console.error('Error getting audit logs:', error);
    next(error);
  }
};
