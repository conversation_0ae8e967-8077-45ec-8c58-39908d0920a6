/**
 * MCP Server Manager
 * Manages the lifecycle of the MCP server
 */

const MCPServer = require('./mcpServer');
const logger = require('../../utils/logger');

class MCPServerManager {
  constructor() {
    this.server = null;
    this.isRunning = false;
    this.config = {
      port: process.env.MCP_PORT || 8001,
      autoRestart: true,
      maxRestartAttempts: 5,
      restartDelay: 5000
    };
    this.restartAttempts = 0;
  }

  /**
   * Start the MCP server
   */
  async start() {
    try {
      if (this.isRunning) {
        logger.warn('MCP Server is already running');
        return;
      }

      logger.info('Starting MCP Server...');
      
      this.server = new MCPServer({
        port: this.config.port
      });

      // Set up event listeners
      this.server.on('started', () => {
        this.isRunning = true;
        this.restartAttempts = 0;
        logger.info(`MCP Server started successfully on port ${this.config.port}`);
      });

      this.server.on('stopped', () => {
        this.isRunning = false;
        logger.info('MCP Server stopped');
        
        if (this.config.autoRestart && this.restartAttempts < this.config.maxRestartAttempts) {
          this.scheduleRestart();
        }
      });

      this.server.on('error', (error) => {
        logger.error('MCP Server error:', error);
        
        if (this.config.autoRestart && this.restartAttempts < this.config.maxRestartAttempts) {
          this.scheduleRestart();
        }
      });

      await this.server.start();

    } catch (error) {
      logger.error('Failed to start MCP Server:', error);
      throw error;
    }
  }

  /**
   * Stop the MCP server
   */
  async stop() {
    try {
      if (!this.isRunning || !this.server) {
        logger.warn('MCP Server is not running');
        return;
      }

      logger.info('Stopping MCP Server...');
      await this.server.stop();
      this.server = null;
      this.isRunning = false;

    } catch (error) {
      logger.error('Failed to stop MCP Server:', error);
      throw error;
    }
  }

  /**
   * Restart the MCP server
   */
  async restart() {
    try {
      logger.info('Restarting MCP Server...');
      await this.stop();
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
      await this.start();
    } catch (error) {
      logger.error('Failed to restart MCP Server:', error);
      throw error;
    }
  }

  /**
   * Schedule a restart attempt
   */
  scheduleRestart() {
    this.restartAttempts++;
    const delay = this.config.restartDelay * this.restartAttempts;

    logger.info(`Scheduling MCP Server restart in ${delay}ms (attempt ${this.restartAttempts}/${this.config.maxRestartAttempts})`);

    setTimeout(async () => {
      try {
        await this.start();
      } catch (error) {
        logger.error('Auto-restart failed:', error);
      }
    }, delay);
  }

  /**
   * Get server status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      port: this.config.port,
      restartAttempts: this.restartAttempts,
      maxRestartAttempts: this.config.maxRestartAttempts,
      autoRestart: this.config.autoRestart,
      serverStatus: this.server ? this.server.getStatus() : null
    };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    logger.info('MCP Server configuration updated:', this.config);
  }

  /**
   * Register a new tool
   */
  registerTool(name, tool) {
    if (this.server) {
      this.server.registerTool(name, tool);
      logger.info(`Registered MCP tool: ${name}`);
    } else {
      logger.warn('Cannot register tool: MCP Server not running');
    }
  }

  /**
   * Register a new resource
   */
  registerResource(name, resource) {
    if (this.server) {
      this.server.registerResource(name, resource);
      logger.info(`Registered MCP resource: ${name}`);
    } else {
      logger.warn('Cannot register resource: MCP Server not running');
    }
  }

  /**
   * Register a new prompt
   */
  registerPrompt(name, prompt) {
    if (this.server) {
      this.server.registerPrompt(name, prompt);
      logger.info(`Registered MCP prompt: ${name}`);
    } else {
      logger.warn('Cannot register prompt: MCP Server not running');
    }
  }

  /**
   * Get connected clients
   */
  getConnectedClients() {
    if (this.server) {
      return Array.from(this.server.clients.values()).map(client => ({
        id: client.id,
        ip: client.ip,
        connectedAt: client.connectedAt,
        capabilities: client.capabilities
      }));
    }
    return [];
  }

  /**
   * Get registered tools
   */
  getRegisteredTools() {
    if (this.server) {
      return Array.from(this.server.tools.values());
    }
    return [];
  }

  /**
   * Get registered resources
   */
  getRegisteredResources() {
    if (this.server) {
      return Array.from(this.server.resources.values());
    }
    return [];
  }

  /**
   * Get registered prompts
   */
  getRegisteredPrompts() {
    if (this.server) {
      return Array.from(this.server.prompts.values());
    }
    return [];
  }

  /**
   * Health check
   */
  async healthCheck() {
    try {
      const status = this.getStatus();
      
      return {
        healthy: status.isRunning,
        status: status.isRunning ? 'running' : 'stopped',
        port: status.port,
        connectedClients: status.serverStatus ? status.serverStatus.connectedClients : 0,
        uptime: status.serverStatus ? status.serverStatus.uptime : 0,
        tools: status.serverStatus ? status.serverStatus.registeredTools : 0,
        resources: status.serverStatus ? status.serverStatus.registeredResources : 0,
        prompts: status.serverStatus ? status.serverStatus.registeredPrompts : 0,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Health check failed:', error);
      return {
        healthy: false,
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Create singleton instance
const mcpServerManager = new MCPServerManager();

module.exports = mcpServerManager;
