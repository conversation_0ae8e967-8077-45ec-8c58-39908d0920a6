import { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControlLabel,
  IconButton,
  List,
  ListItem,
  ListItemText,
  Paper,
  Switch,
  Tooltip,
  Typography
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  Storage as StorageIcon,
  Code as CodeIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  DarkMode as DarkModeIcon,
  LightMode as LightModeIcon
} from '@mui/icons-material';
import { 
  getAllFeatureFlags, 
  setFeatureFlag, 
  toggleFeatureFlag, 
  resetFeatureFlags,
  isUsingRealBackend
} from '@/utils/featureFlags';
import { toast } from 'react-toastify';

/**
 * Feature Flag Toggle Component
 * 
 * This component provides a UI for toggling between real and mock data,
 * as well as managing other feature flags.
 */
const FeatureFlagToggle = () => {
  const [open, setOpen] = useState(false);
  const [flags, setFlags] = useState({});
  const [needsReload, setNeedsReload] = useState(false);

  // Load feature flags on mount
  useEffect(() => {
    setFlags(getAllFeatureFlags());
  }, []);

  // Handle dialog open/close
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  // Handle toggle for real/mock backend
  const handleToggleBackend = () => {
    const newValue = toggleFeatureFlag('useRealBackend');
    setFlags(prev => ({ ...prev, useRealBackend: newValue }));
    setNeedsReload(true);
    toast.info(`Switched to ${newValue ? 'real' : 'mock'} backend. Reload required for changes to take effect.`);
  };

  // Handle toggle for other feature flags
  const handleToggleFlag = (flagName) => {
    const newValue = toggleFeatureFlag(flagName);
    setFlags(prev => ({ ...prev, [flagName]: newValue }));
    
    // Some flags require page reload
    const reloadFlags = ['enableDarkMode', 'enableAnimations'];
    if (reloadFlags.includes(flagName)) {
      setNeedsReload(true);
      toast.info(`Changed ${flagName} to ${newValue}. Reload required for changes to take effect.`);
    } else {
      toast.success(`Changed ${flagName} to ${newValue}`);
    }
  };

  // Handle reset all flags
  const handleResetFlags = () => {
    resetFeatureFlags();
    setFlags(getAllFeatureFlags());
    setNeedsReload(true);
    toast.info('All feature flags reset to defaults. Reload required for changes to take effect.');
  };

  // Handle page reload
  const handleReload = () => {
    window.location.reload();
  };

  return (
    <>
      {/* Toggle Button */}
      <Tooltip title="Data Source & Feature Flags">
        <IconButton 
          color="inherit" 
          onClick={handleOpen}
          sx={{ position: 'relative' }}
        >
          <SettingsIcon />
          <Chip
            label={isUsingRealBackend() ? 'Real' : 'Mock'}
            color={isUsingRealBackend() ? 'success' : 'warning'}
            size="small"
            sx={{
              position: 'absolute',
              top: -2,
              right: -2,
              height: '18px',
              fontSize: '0.6rem',
              fontWeight: 'bold'
            }}
          />
        </IconButton>
      </Tooltip>

      {/* Feature Flags Dialog */}
      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">Feature Flags & Data Source</Typography>
            <Tooltip title="Reset All Flags">
              <IconButton onClick={handleResetFlags} size="small">
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </DialogTitle>
        <DialogContent>
          {/* Data Source Toggle */}
          <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <StorageIcon sx={{ mr: 1, color: isUsingRealBackend() ? 'success.main' : 'warning.main' }} />
                <Box>
                  <Typography variant="subtitle1">Data Source</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {isUsingRealBackend() 
                      ? 'Using real backend API' 
                      : 'Using mock data (for development)'}
                  </Typography>
                </Box>
              </Box>
              <Switch
                checked={flags.useRealBackend || false}
                onChange={handleToggleBackend}
                color="primary"
              />
            </Box>
          </Paper>

          {/* Feature Flags List */}
          <Typography variant="subtitle2" gutterBottom>Feature Flags</Typography>
          <List disablePadding>
            <Divider />
            {Object.entries(flags)
              .filter(([key]) => key !== 'useRealBackend')
              .map(([key, value]) => (
                typeof value === 'boolean' && (
                  <Box key={key}>
                    <ListItem>
                      <ListItemText
                        primary={key}
                        secondary={getFlagDescription(key)}
                        primaryTypographyProps={{ 
                          variant: 'body2',
                          sx: { fontWeight: 'medium' }
                        }}
                        secondaryTypographyProps={{ 
                          variant: 'caption',
                          sx: { fontSize: '0.7rem' }
                        }}
                      />
                      <Switch
                        edge="end"
                        checked={value}
                        onChange={() => handleToggleFlag(key)}
                        color="primary"
                      />
                    </ListItem>
                    <Divider />
                  </Box>
                )
              ))}
          </List>
        </DialogContent>
        <DialogActions>
          {needsReload && (
            <Button 
              color="warning" 
              startIcon={<RefreshIcon />}
              onClick={handleReload}
            >
              Reload Page
            </Button>
          )}
          <Button onClick={handleClose}>Close</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

// Helper function to get flag descriptions
const getFlagDescription = (flagName) => {
  const descriptions = {
    enableBiodataTemplates: 'Enable biodata template features',
    enableSpotlightFeatures: 'Enable spotlight features',
    enablePaymentGateway: 'Enable payment gateway integration',
    enableNotifications: 'Enable notification system',
    enableMatchingAlgorithm: 'Enable AI matching algorithm',
    enableDarkMode: 'Enable dark mode support',
    enableAnimations: 'Enable UI animations',
    showMockDataIndicator: 'Show indicator when using mock data',
    enableDebugLogging: 'Enable debug logging'
  };
  
  return descriptions[flagName] || 'No description available';
};

export default FeatureFlagToggle;
