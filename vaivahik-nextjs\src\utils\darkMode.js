/**
 * Dark Mode Utility
 * This utility handles dark mode toggling and persistence
 */

// Initialize dark mode based on user preference or system preference
export const initDarkMode = () => {
  if (typeof window === 'undefined') return false;
  
  // Check if user has a preference stored
  const storedPreference = localStorage.getItem('darkMode');
  
  if (storedPreference !== null) {
    // Use stored preference
    return storedPreference === 'true';
  } else {
    // Check system preference
    const systemPreference = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    // Store the system preference
    localStorage.setItem('darkMode', systemPreference.toString());
    
    return systemPreference;
  }
};

// Toggle dark mode
export const toggleDarkMode = (currentState) => {
  if (typeof window === 'undefined') return currentState;
  
  const newState = !currentState;
  
  // Update body class
  if (newState) {
    document.body.classList.add('dark-mode');
  } else {
    document.body.classList.remove('dark-mode');
  }
  
  // Store preference
  localStorage.setItem('darkMode', newState.toString());
  
  return newState;
};

// Apply dark mode to the document
export const applyDarkMode = (isDarkMode) => {
  if (typeof window === 'undefined') return;
  
  if (isDarkMode) {
    document.body.classList.add('dark-mode');
  } else {
    document.body.classList.remove('dark-mode');
  }
};

// Listen for system preference changes
export const listenForSystemPreferenceChanges = (setDarkMode) => {
  if (typeof window === 'undefined') return () => {};
  
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  
  const handleChange = (e) => {
    // Only update if user hasn't set a preference
    if (localStorage.getItem('darkMode') === null) {
      setDarkMode(e.matches);
      applyDarkMode(e.matches);
    }
  };
  
  // Add event listener
  if (mediaQuery.addEventListener) {
    mediaQuery.addEventListener('change', handleChange);
  } else {
    // Fallback for older browsers
    mediaQuery.addListener(handleChange);
  }
  
  // Return cleanup function
  return () => {
    if (mediaQuery.removeEventListener) {
      mediaQuery.removeEventListener('change', handleChange);
    } else {
      // Fallback for older browsers
      mediaQuery.removeListener(handleChange);
    }
  };
};
