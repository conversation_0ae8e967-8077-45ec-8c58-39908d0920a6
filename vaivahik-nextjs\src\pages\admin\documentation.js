import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);
import { toast } from 'react-toastify';
import ReactMarkdown from 'react-markdown';
import {
  Box,
  Button,
  Card,
  CardContent,
  Divider,
  Grid,
  Paper,
  Tab,
  Tabs,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton
} from '@mui/material';
import {
  MenuBook as MenuBookIcon,
  Code as CodeIcon,
  Api as ApiIcon,
  Download as DownloadIcon,
  Search as SearchIcon,
  Help as HelpIcon
} from '@mui/icons-material';

export default function Documentation() {
  const [activeTab, setActiveTab] = useState(0);
  const [markdownContent, setMarkdownContent] = useState('');
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [tableOfContents, setTableOfContents] = useState([]);

  const tabs = [
    { label: 'User Guide', icon: <MenuBookIcon />, file: '/docs/admin-guide.md' },
    { label: 'Developer Guide', icon: <CodeIcon />, file: '/docs/admin-developer-guide.md' },
    { label: 'API Documentation', icon: <ApiIcon />, file: '/docs/admin-api-docs.md' }
  ];

  useEffect(() => {
    fetchDocumentation(tabs[activeTab].file);
  }, [activeTab]);

  const fetchDocumentation = async (filePath) => {
    setLoading(true);
    try {
      const response = await fetch(filePath);
      if (!response.ok) {
        throw new Error('Failed to fetch documentation');
      }
      
      const content = await response.text();
      setMarkdownContent(content);
      
      // Extract table of contents
      const headings = content.match(/^#{1,3} (.+)$/gm) || [];
      const toc = headings.map(heading => {
        const level = (heading.match(/^#+/) || [''])[0].length;
        const text = heading.replace(/^#+\s+/, '');
        const anchor = text.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-');
        return { level, text, anchor };
      });
      
      setTableOfContents(toc);
    } catch (error) {
      console.error('Error fetching documentation:', error);
      toast.error('Error loading documentation');
      setMarkdownContent('# Documentation Not Available\n\nSorry, the requested documentation could not be loaded.');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
  };

  const handleDownloadPdf = () => {
    // In a real implementation, this would generate and download a PDF
    toast.info('PDF download functionality will be implemented in the future');
  };

  const filteredToc = tableOfContents.filter(item => 
    item.text.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <EnhancedAdminLayout title="Documentation">
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Documentation
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<DownloadIcon />}
            onClick={handleDownloadPdf}
          >
            Download PDF
          </Button>
        </Box>

        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
          indicatorColor="primary"
          textColor="primary"
          sx={{ mb: 3, bgcolor: 'background.paper', borderRadius: 1 }}
        >
          {tabs.map((tab, index) => (
            <Tab 
              key={index} 
              label={tab.label} 
              icon={tab.icon} 
              iconPosition="start"
              sx={{ 
                minHeight: 64,
                textTransform: 'none',
                fontSize: '1rem',
                fontWeight: activeTab === index ? 'bold' : 'normal'
              }}
            />
          ))}
        </Tabs>

        <Grid container spacing={3}>
          {/* Table of Contents */}
          <Grid item xs={12} md={3}>
            <Card sx={{ height: '100%' }}>
              <CardContent sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Table of Contents
                </Typography>
                <Box sx={{ position: 'relative', mb: 2 }}>
                  <TextField
                    fullWidth
                    placeholder="Search documentation..."
                    value={searchTerm}
                    onChange={handleSearch}
                    size="small"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon fontSize="small" />
                        </InputAdornment>
                      )
                    }}
                  />
                </Box>
                <Divider sx={{ mb: 2 }} />
                {loading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
                    <div className="loading-spinner"></div>
                  </Box>
                ) : filteredToc.length > 0 ? (
                  <List dense sx={{ maxHeight: 'calc(100vh - 300px)', overflow: 'auto' }}>
                    {filteredToc.map((item, index) => (
                      <ListItem 
                        key={index} 
                        disablePadding
                        sx={{ pl: (item.level - 1) * 2 }}
                      >
                        <ListItemButton 
                          component="a" 
                          href={`#${item.anchor}`}
                          dense
                          sx={{ borderRadius: 1 }}
                        >
                          <ListItemIcon sx={{ minWidth: 30 }}>
                            {item.level === 1 ? (
                              <MenuBookIcon fontSize="small" color="primary" />
                            ) : (
                              <ArrowRightIcon fontSize="small" color="action" />
                            )}
                          </ListItemIcon>
                          <ListItemText 
                            primary={item.text} 
                            primaryTypographyProps={{ 
                              variant: item.level === 1 ? 'subtitle1' : 'body2',
                              fontWeight: item.level === 1 ? 'medium' : 'normal'
                            }}
                          />
                        </ListItemButton>
                      </ListItem>
                    ))}
                  </List>
                ) : (
                  <Box sx={{ textAlign: 'center', py: 3 }}>
                    <HelpIcon color="action" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="body2" color="text.secondary">
                      No matching topics found
                    </Typography>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Documentation Content */}
          <Grid item xs={12} md={9}>
            <Paper sx={{ p: 3, minHeight: 'calc(100vh - 250px)' }}>
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 5 }}>
                  <div className="loading-spinner"></div>
                </Box>
              ) : (
                <Box className="markdown-content" sx={{ 
                  '& h1': { 
                    fontSize: '2rem', 
                    fontWeight: 'bold',
                    mb: 3,
                    pb: 1,
                    borderBottom: '1px solid',
                    borderColor: 'divider'
                  },
                  '& h2': { 
                    fontSize: '1.5rem', 
                    fontWeight: 'bold',
                    mt: 4,
                    mb: 2,
                    pb: 1,
                    borderBottom: '1px solid',
                    borderColor: 'divider'
                  },
                  '& h3': { 
                    fontSize: '1.25rem', 
                    fontWeight: 'medium',
                    mt: 3,
                    mb: 2
                  },
                  '& p': { mb: 2 },
                  '& ul, & ol': { mb: 2, pl: 3 },
                  '& li': { mb: 1 },
                  '& code': { 
                    bgcolor: 'grey.100', 
                    p: 0.5, 
                    borderRadius: 1,
                    fontFamily: 'monospace'
                  },
                  '& pre': { 
                    bgcolor: 'grey.900', 
                    color: 'common.white',
                    p: 2, 
                    borderRadius: 1,
                    overflow: 'auto',
                    mb: 2
                  },
                  '& pre code': { 
                    bgcolor: 'transparent',
                    color: 'inherit',
                    p: 0
                  },
                  '& table': {
                    width: '100%',
                    borderCollapse: 'collapse',
                    mb: 2
                  },
                  '& th, & td': {
                    border: '1px solid',
                    borderColor: 'divider',
                    p: 1
                  },
                  '& th': {
                    bgcolor: 'grey.100',
                    fontWeight: 'bold'
                  },
                  '& blockquote': {
                    borderLeft: '4px solid',
                    borderColor: 'primary.main',
                    pl: 2,
                    py: 1,
                    bgcolor: 'grey.50',
                    m: 0,
                    mb: 2
                  },
                  '& a': {
                    color: 'primary.main',
                    textDecoration: 'none',
                    '&:hover': {
                      textDecoration: 'underline'
                    }
                  },
                  '& hr': {
                    border: 'none',
                    height: '1px',
                    bgcolor: 'divider',
                    my: 3
                  }
                }}>
                  <ReactMarkdown>
                    {markdownContent}
                  </ReactMarkdown>
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </EnhancedAdminLayout>
  );
}
