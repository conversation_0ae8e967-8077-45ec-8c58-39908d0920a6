/* 
 * Mobile Responsive Styles for Admin Panel
 * This file contains responsive styles for the admin panel
 */

/* Base mobile styles (up to 767px) */
@media (max-width: 767px) {
  /* Layout adjustments */
  .sidebar {
    width: 0;
    overflow: hidden;
    padding: 0;
  }
  
  .sidebar.mobile-open {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1100;
  }
  
  .main-content {
    margin-left: 0 !important;
    width: 100% !important;
  }
  
  .topbar {
    padding: 0 10px;
  }
  
  .page-content {
    padding: 15px;
  }
  
  /* Mobile menu toggle */
  .mobile-menu-toggle {
    display: block !important;
  }
  
  /* Tables */
  .table-container {
    padding: 10px;
    overflow-x: auto;
  }
  
  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .table-actions {
    width: 100%;
    justify-content: space-between;
    flex-wrap: wrap;
  }
  
  .data-table th,
  .data-table td {
    padding: 8px;
    font-size: 0.9rem;
  }
  
  /* Cards */
  .card-header,
  .card-body,
  .card-footer {
    padding: 15px;
  }
  
  /* Forms */
  .form-row {
    flex-direction: column;
  }
  
  .form-group {
    width: 100% !important;
    margin-right: 0 !important;
  }
  
  /* Modals */
  .modal-content {
    width: 95%;
    max-height: 80vh;
  }
  
  /* Dashboard stats */
  .stats-grid {
    grid-template-columns: 1fr !important;
  }
  
  .stats-card {
    margin-bottom: 15px;
  }
  
  /* User profile */
  .profile-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .profile-avatar {
    margin-bottom: 15px;
    margin-right: 0 !important;
  }
  
  .profile-details .detail-grid {
    grid-template-columns: 1fr !important;
  }
  
  /* Activity feed */
  .activity-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .activity-filters {
    width: 100%;
    flex-wrap: wrap;
  }
  
  /* Tabs */
  .tab-nav {
    overflow-x: auto;
    white-space: nowrap;
    padding-bottom: 5px;
  }
  
  .tab-nav-item {
    padding: 8px 12px;
  }
}

/* Small tablets (768px to 991px) */
@media (min-width: 768px) and (max-width: 991px) {
  .sidebar {
    width: var(--sidebar-collapsed-width);
  }
  
  .sidebar .logo-text,
  .sidebar .user-info,
  .sidebar .nav-category,
  .sidebar .nav-text {
    display: none;
  }
  
  .main-content {
    margin-left: var(--sidebar-collapsed-width);
    width: calc(100% - var(--sidebar-collapsed-width));
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
  
  .profile-details .detail-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
  
  .table-container {
    overflow-x: auto;
  }
}

/* Tablets and small desktops (992px to 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
  
  .table-container {
    overflow-x: auto;
  }
}

/* Mobile menu toggle button */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--text-dark);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  margin-right: 10px;
}

/* Mobile sidebar overlay */
.sidebar-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1050;
}

.sidebar-overlay.active {
  display: block;
}

/* Mobile sidebar close button */
.mobile-sidebar-close {
  display: none;
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
}

@media (max-width: 767px) {
  .mobile-sidebar-close {
    display: block;
  }
}

/* Responsive utilities */
@media (max-width: 767px) {
  .d-mobile-none {
    display: none !important;
  }
  
  .d-mobile-block {
    display: block !important;
  }
  
  .d-mobile-flex {
    display: flex !important;
  }
  
  .text-mobile-center {
    text-align: center !important;
  }
  
  .flex-mobile-column {
    flex-direction: column !important;
  }
  
  .w-mobile-100 {
    width: 100% !important;
  }
  
  .mb-mobile-3 {
    margin-bottom: 1rem !important;
  }
}

/* Responsive DataGrid */
@media (max-width: 767px) {
  .MuiDataGrid-root {
    font-size: 0.85rem;
  }
  
  .MuiDataGrid-columnHeader {
    padding: 0 8px !important;
  }
  
  .MuiDataGrid-cell {
    padding: 8px !important;
  }
  
  .MuiTablePagination-selectLabel,
  .MuiTablePagination-displayedRows {
    margin: 0 !important;
  }
  
  .MuiTablePagination-toolbar {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
}
