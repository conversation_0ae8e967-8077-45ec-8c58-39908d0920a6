/**
 * Background Services Starter
 * 
 * This script starts the background services for the matrimony matching system.
 */

const { PrismaClient } = require('@prisma/client');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Initialize Prisma client
const prisma = new PrismaClient();

// Load settings
const settingsPath = path.join(__dirname, '../../config/production_settings.json');
const settings = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));

// Configure logging
const logDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

const logStream = fs.createWriteStream(path.join(logDir, 'background_services.log'), { flags: 'a' });

/**
 * Log a message to the console and log file
 * 
 * @param {string} message - Message to log
 */
function log(message) {
  const timestamp = new Date().toISOString();
  const formattedMessage = `[${timestamp}] ${message}`;
  console.log(formattedMessage);
  logStream.write(formattedMessage + '\n');
}

/**
 * Start the embedding precomputation service
 */
async function startEmbeddingService() {
  log('Starting embedding precomputation service...');
  
  try {
    // Start the Python script
    const pythonScript = path.join(__dirname, 'embedding_service.py');
    
    // Check if the script exists
    if (!fs.existsSync(pythonScript)) {
      log(`Error: Python script not found: ${pythonScript}`);
      return;
    }
    
    // Start the process
    const process = spawn('python', [pythonScript]);
    
    // Handle process events
    process.stdout.on('data', (data) => {
      log(`Embedding service: ${data}`);
    });
    
    process.stderr.on('data', (data) => {
      log(`Embedding service error: ${data}`);
    });
    
    process.on('close', (code) => {
      log(`Embedding service exited with code ${code}`);
      
      // Restart the service if it exits
      setTimeout(() => {
        log('Restarting embedding service...');
        startEmbeddingService();
      }, 5000);
    });
    
    log('Embedding precomputation service started');
  } catch (error) {
    log(`Error starting embedding service: ${error.message}`);
  }
}

/**
 * Start the batch processing service
 */
async function startBatchProcessingService() {
  log('Starting batch processing service...');
  
  try {
    // Start the Python script
    const pythonScript = path.join(__dirname, 'batch_processor.py');
    
    // Check if the script exists
    if (!fs.existsSync(pythonScript)) {
      log(`Error: Python script not found: ${pythonScript}`);
      return;
    }
    
    // Start the process
    const process = spawn('python', [pythonScript]);
    
    // Handle process events
    process.stdout.on('data', (data) => {
      log(`Batch processing service: ${data}`);
    });
    
    process.stderr.on('data', (data) => {
      log(`Batch processing service error: ${data}`);
    });
    
    process.on('close', (code) => {
      log(`Batch processing service exited with code ${code}`);
      
      // Restart the service if it exits
      setTimeout(() => {
        log('Restarting batch processing service...');
        startBatchProcessingService();
      }, 5000);
    });
    
    log('Batch processing service started');
  } catch (error) {
    log(`Error starting batch processing service: ${error.message}`);
  }
}

/**
 * Start the performance monitoring service
 */
async function startMonitoringService() {
  log('Starting performance monitoring service...');
  
  try {
    // Start the Python script
    const pythonScript = path.join(__dirname, 'performance_monitor.py');
    
    // Check if the script exists
    if (!fs.existsSync(pythonScript)) {
      log(`Error: Python script not found: ${pythonScript}`);
      return;
    }
    
    // Start the process
    const process = spawn('python', [pythonScript]);
    
    // Handle process events
    process.stdout.on('data', (data) => {
      log(`Monitoring service: ${data}`);
    });
    
    process.stderr.on('data', (data) => {
      log(`Monitoring service error: ${data}`);
    });
    
    process.on('close', (code) => {
      log(`Monitoring service exited with code ${code}`);
      
      // Restart the service if it exits
      setTimeout(() => {
        log('Restarting monitoring service...');
        startMonitoringService();
      }, 5000);
    });
    
    log('Performance monitoring service started');
  } catch (error) {
    log(`Error starting monitoring service: ${error.message}`);
  }
}

/**
 * Schedule active users for batch processing
 */
async function scheduleActiveUsers() {
  log('Scheduling active users for batch processing...');
  
  try {
    // Get active users
    const activeUsers = await prisma.user.findMany({
      where: {
        profileStatus: 'ACTIVE',
        lastActive: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Active in the last 7 days
        }
      },
      select: {
        id: true
      },
      take: settings.batch?.max_queue_size || 1000
    });
    
    log(`Found ${activeUsers.length} active users`);
    
    // Write user IDs to a file for the batch processor to read
    const userIdsFile = path.join(__dirname, '../../data/active_users.json');
    fs.writeFileSync(userIdsFile, JSON.stringify(activeUsers.map(user => user.id)));
    
    log(`Active users scheduled for batch processing: ${activeUsers.length}`);
  } catch (error) {
    log(`Error scheduling active users: ${error.message}`);
  }
}

/**
 * Main function
 */
async function main() {
  log('Starting background services...');
  
  // Schedule active users
  await scheduleActiveUsers();
  
  // Start services
  await startEmbeddingService();
  await startBatchProcessingService();
  await startMonitoringService();
  
  // Schedule active users periodically
  setInterval(scheduleActiveUsers, 24 * 60 * 60 * 1000); // Once a day
  
  log('All background services started');
}

// Start the main function
main().catch(error => {
  log(`Error in main function: ${error.message}`);
  process.exit(1);
});
