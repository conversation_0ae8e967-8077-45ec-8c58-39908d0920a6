/**
 * Axios Configuration
 *
 * This file configures axios with interceptors for:
 * - Adding authentication tokens to requests
 * - Handling token refresh on 401 errors
 * - Consistent error handling
 */

import axios from 'axios';
import { getAuthToken, getAdminToken, refreshAuthToken, clearAuthData } from '@/services/authService';

// Create axios instance with default config
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || '',
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
  },
});

// Create admin axios instance
const adminApi = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || '',
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token for regular users
api.interceptors.request.use(
  (config) => {
    // Add auth token to request if available
    const token = getAuthToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Request interceptor to add auth token for admin users
adminApi.interceptors.request.use(
  (config) => {
    // Add admin auth token to request if available
    const token = getAdminToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Track if we're currently refreshing the token
let isRefreshing = false;
// Store pending requests that should be retried after token refresh
let pendingRequests = [];

// Response interceptor to handle token refresh for regular users
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // If error is 401 Unauthorized and we haven't tried to refresh the token yet
    if (error.response?.status === 401 && !originalRequest._retry) {
      // Mark this request as retried
      originalRequest._retry = true;

      // If we're already refreshing, queue this request
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          pendingRequests.push({
            resolve,
            reject,
            config: originalRequest,
          });
        });
      }

      // Start refreshing
      isRefreshing = true;

      try {
        // Attempt to refresh the token
        const refreshResult = await refreshAuthToken();

        // If successful, update the token in the original request
        if (refreshResult.success) {
          originalRequest.headers.Authorization = `Bearer ${refreshResult.token}`;

          // Retry all pending requests with the new token
          pendingRequests.forEach((request) => {
            request.config.headers.Authorization = `Bearer ${refreshResult.token}`;
            request.resolve(api(request.config));
          });

          // Clear pending requests
          pendingRequests = [];

          // Retry the original request
          return api(originalRequest);
        } else {
          // If refresh failed, reject all pending requests
          pendingRequests.forEach((request) => {
            request.reject(error);
          });

          // Clear pending requests
          pendingRequests = [];

          // Clear auth data
          clearAuthData();

          // Redirect to login page if in browser
          if (typeof window !== 'undefined') {
            window.location.href = '/login?session=expired';
          }

          return Promise.reject(error);
        }
      } catch (refreshError) {
        // If refresh failed, reject all pending requests
        pendingRequests.forEach((request) => {
          request.reject(error);
        });

        // Clear pending requests
        pendingRequests = [];

        // Clear auth data
        clearAuthData();

        // Redirect to login page if in browser
        if (typeof window !== 'undefined') {
          window.location.href = '/login?session=expired';
        }

        return Promise.reject(refreshError);
      } finally {
        // Reset refreshing flag
        isRefreshing = false;
      }
    }

    // For other errors, just reject the promise
    return Promise.reject(error);
  }
);

// Response interceptor for admin API
adminApi.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // If we get a 401 Unauthorized error, redirect to admin login
    if (error.response && error.response.status === 401 && typeof window !== 'undefined') {
      // Only redirect if we're not already on the login page
      if (!window.location.pathname.includes('/admin/login')) {
        console.log('Unauthorized admin access, redirecting to login page');
        window.location.href = '/admin/login';
      }
    }

    return Promise.reject(error);
  }
);

export { api, adminApi };
export default api;
