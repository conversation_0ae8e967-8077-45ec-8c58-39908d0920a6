/**
 * Admin Dashboard Controller
 *
 * Handles requests for the admin dashboard data, recent activity, and recent users.
 */
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { getOrSetCache, invalidateDashboardStatsCache, CACHE_PREFIXES } = require('../../../redis/cacheService');

/**
 * Get dashboard statistics and data
 *
 * @route GET /api/admin/dashboard
 * @access Admin
 */
exports.getDashboardData = async (req, res) => {
  try {
    // Check if refresh parameter is provided
    const { refresh } = req.query;
    const shouldRefresh = refresh === 'true';

    // If refresh is not requested, try to get from cache
    if (!shouldRefresh) {
      const cacheKey = `${CACHE_PREFIXES.DASHBOARD_STATS}main`;

      return await getOrSetCache(
        cacheKey,
        async () => {
          // If not in cache or refresh requested, fetch fresh data
          const dashboardData = await fetchDashboardData();
          return res.status(200).json(dashboardData);
        },
        1800 // Cache for 30 minutes
      );
    }

    // If refresh is requested, invalidate cache and fetch fresh data
    if (shouldRefresh) {
      await invalidateDashboardStatsCache();
    }

    // Fetch fresh dashboard data
    const dashboardData = await fetchDashboardData();
    return res.status(200).json(dashboardData);
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    res.status(500).json({
      message: 'Failed to fetch dashboard data',
      error: error.message
    });
  }
};

/**
 * Fetch dashboard data from the database
 * @returns {Object} Dashboard data
 */
async function fetchDashboardData() {
    // Get counts for various entities
    const [
      totalUsers,
      premiumUsers,
      verifiedUsers,
      totalMatches,
      acceptedMatches,
      totalReports,
      pendingReports,
      totalSubscriptions,
      activeSubscriptions
    ] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({ where: { isPremium: true } }),
      prisma.user.count({ where: { isVerified: true } }),
      prisma.match.count(),
      prisma.match.count({ where: { status: 'ACCEPTED' } }),
      prisma.report.count(),
      prisma.report.count({ where: { status: 'PENDING' } }),
      prisma.subscription.count(),
      prisma.subscription.count({ where: { isActive: true } })
    ]);

    // Calculate percentages
    const premiumPercentage = totalUsers > 0 ? (premiumUsers / totalUsers) * 100 : 0;
    const verifiedPercentage = totalUsers > 0 ? (verifiedUsers / totalUsers) * 100 : 0;
    const matchSuccessRate = totalMatches > 0 ? (acceptedMatches / totalMatches) * 100 : 0;
    const activeSubscriptionRate = totalSubscriptions > 0 ? (activeSubscriptions / totalSubscriptions) * 100 : 0;

    // Get new users in the last 7 days
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const newUsers = await prisma.user.count({
      where: {
        createdAt: {
          gte: sevenDaysAgo
        }
      }
    });

    // Get revenue data
    const revenueResult = await prisma.subscription.aggregate({
      _sum: {
        amount: true
      }
    });

    const totalRevenue = revenueResult._sum.amount || 0;

    // Get gender distribution
    const maleUsers = await prisma.profile.count({
      where: {
        gender: 'MALE'
      }
    });

    const femaleUsers = await prisma.profile.count({
      where: {
        gender: 'FEMALE'
      }
    });

    // Get data from previous period for growth calculations
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const sixtyDaysAgo = new Date();
    sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

    // Get user counts from previous period
    const [
      previousPeriodUsers,
      previousPeriodPremiumUsers,
      previousPeriodVerifiedUsers,
      previousPeriodMatches,
      previousPeriodAcceptedMatches,
      previousPeriodReports,
      previousPeriodPendingReports,
      previousPeriodSubscriptions,
      previousPeriodRevenue
    ] = await Promise.all([
      prisma.user.count({
        where: {
          createdAt: {
            gte: sixtyDaysAgo,
            lt: thirtyDaysAgo
          }
        }
      }),
      prisma.user.count({
        where: {
          isPremium: true,
          createdAt: {
            gte: sixtyDaysAgo,
            lt: thirtyDaysAgo
          }
        }
      }),
      prisma.user.count({
        where: {
          isVerified: true,
          createdAt: {
            gte: sixtyDaysAgo,
            lt: thirtyDaysAgo
          }
        }
      }),
      prisma.match.count({
        where: {
          createdAt: {
            gte: sixtyDaysAgo,
            lt: thirtyDaysAgo
          }
        }
      }),
      prisma.match.count({
        where: {
          status: 'ACCEPTED',
          createdAt: {
            gte: sixtyDaysAgo,
            lt: thirtyDaysAgo
          }
        }
      }),
      prisma.report.count({
        where: {
          createdAt: {
            gte: sixtyDaysAgo,
            lt: thirtyDaysAgo
          }
        }
      }),
      prisma.report.count({
        where: {
          status: 'PENDING',
          createdAt: {
            gte: sixtyDaysAgo,
            lt: thirtyDaysAgo
          }
        }
      }),
      prisma.subscription.count({
        where: {
          createdAt: {
            gte: sixtyDaysAgo,
            lt: thirtyDaysAgo
          }
        }
      }),
      prisma.subscription.aggregate({
        _sum: {
          amount: true
        },
        where: {
          createdAt: {
            gte: sixtyDaysAgo,
            lt: thirtyDaysAgo
          }
        }
      }).then(result => result._sum.amount || 0)
    ]);

    // Calculate growth percentages
    const calculateGrowth = (current, previous) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous * 100).toFixed(1);
    };

    const totalUsersGrowth = calculateGrowth(totalUsers, previousPeriodUsers);
    const premiumUsersGrowth = calculateGrowth(premiumUsers, previousPeriodPremiumUsers);
    const verifiedUsersGrowth = calculateGrowth(verifiedUsers, previousPeriodVerifiedUsers);
    const matchesGrowth = calculateGrowth(totalMatches, previousPeriodMatches);
    const acceptedMatchesGrowth = calculateGrowth(acceptedMatches, previousPeriodAcceptedMatches);
    const reportsGrowth = calculateGrowth(totalReports, previousPeriodReports);
    const pendingReportsGrowth = calculateGrowth(pendingReports, previousPeriodPendingReports);
    const subscriptionsGrowth = calculateGrowth(totalSubscriptions, previousPeriodSubscriptions);
    const revenueGrowth = calculateGrowth(totalRevenue, previousPeriodRevenue);

    // Prepare response data with both detailed stats and frontend-friendly format
    const dashboardData = {
      // Detailed stats (original structure)
      userStats: {
        total: totalUsers,
        premium: premiumUsers,
        premiumPercentage: premiumPercentage.toFixed(1),
        verified: verifiedUsers,
        verifiedPercentage: verifiedPercentage.toFixed(1),
        newLast7Days: newUsers,
        genderDistribution: {
          male: maleUsers,
          female: femaleUsers,
          other: totalUsers - maleUsers - femaleUsers
        }
      },
      matchStats: {
        total: totalMatches,
        accepted: acceptedMatches,
        successRate: matchSuccessRate.toFixed(1)
      },
      reportStats: {
        total: totalReports,
        pending: pendingReports
      },
      subscriptionStats: {
        total: totalSubscriptions,
        active: activeSubscriptions,
        activeRate: activeSubscriptionRate.toFixed(1),
        totalRevenue: totalRevenue
      },

      // Frontend-friendly format (matches the frontend structure)
      stats: {
        totalUsers: totalUsers,
        totalUsersGrowth: parseFloat(totalUsersGrowth),
        newRegistrations: newUsers,
        newRegistrationsGrowth: parseFloat(calculateGrowth(newUsers, previousPeriodUsers)),
        successfulMatches: acceptedMatches,
        successfulMatchesGrowth: parseFloat(acceptedMatchesGrowth),
        pendingVerifications: await prisma.verification.count({ where: { status: 'PENDING' } }),
        pendingVerificationsGrowth: parseFloat(pendingReportsGrowth), // Using reports growth as proxy
        reportedProfiles: totalReports,
        premiumUsers: premiumUsers,
        premiumUsersGrowth: parseFloat(premiumUsersGrowth),
        revenue: totalRevenue,
        revenueGrowth: parseFloat(revenueGrowth)
      }
    };

    return dashboardData;
  }
};

/**
 * Get recent activity for the admin dashboard
 *
 * @route GET /api/admin/dashboard/recent-activity
 * @access Admin
 */
exports.getRecentActivity = async (req, res) => {
  try {
    const { limit = 10, types = '', refresh = 'false' } = req.query;
    const limitNum = parseInt(limit, 10);
    const shouldRefresh = refresh === 'true';

    // Create a cache key based on the query parameters
    const cacheKey = `${CACHE_PREFIXES.DASHBOARD_STATS}activity:limit:${limitNum}:types:${types}`;

    // If refresh is requested, invalidate the cache
    if (shouldRefresh) {
      await invalidateDashboardStatsCache();
    }

    // If not refreshing, try to get from cache
    if (!shouldRefresh) {
      const cachedData = await getOrSetCache(
        cacheKey,
        async () => {
          return await fetchRecentActivity(limitNum, types);
        },
        300 // Cache for 5 minutes
      );

      return res.status(200).json(cachedData);
    }

    // If refresh is requested, fetch fresh data
    const activityData = await fetchRecentActivity(limitNum, types);
    return res.status(200).json(activityData);
  } catch (error) {
    console.error('Error fetching recent activity:', error);
    res.status(500).json({
      message: 'Failed to fetch recent activity',
      error: error.message
    });
  }
};

/**
 * Fetch recent activity data from the database
 * @param {number} limitNum - Number of activities to fetch
 * @param {string} types - Comma-separated list of activity types
 * @returns {Object} Activity data
 */
async function fetchRecentActivity(limitNum, types) {
  // Parse activity types if provided
  const activityTypes = types ? types.split(',') : [];

  // Build where clause for activity types
  const whereClause = activityTypes.length > 0
    ? { type: { in: activityTypes } }
    : {};

  // Get recent activities
  const activities = await prisma.userInteraction.findMany({
    where: whereClause,
    orderBy: {
      createdAt: 'desc'
    },
    take: limitNum,
    include: {
      user: {
        select: {
          id: true,
          profile: {
            select: {
              firstName: true,
              lastName: true,
              photos: {
                where: { isMain: true },
                select: { url: true },
                take: 1
              }
            }
          }
        }
      },
      targetUser: {
        select: {
          id: true,
          profile: {
            select: {
              firstName: true,
              lastName: true,
              photos: {
                where: { isMain: true },
                select: { url: true },
                take: 1
              }
            }
          }
        }
      }
    }
  });

  // Format activities for response
  const formattedActivities = activities.map(activity => ({
    id: activity.id,
    type: activity.type,
    createdAt: activity.createdAt,
    user: {
      id: activity.user.id,
      name: `${activity.user.profile?.firstName || ''} ${activity.user.profile?.lastName || ''}`.trim(),
      photoUrl: activity.user.profile?.photos[0]?.url || null
    },
    targetUser: activity.targetUser ? {
      id: activity.targetUser.id,
      name: `${activity.targetUser.profile?.firstName || ''} ${activity.targetUser.profile?.lastName || ''}`.trim(),
      photoUrl: activity.targetUser.profile?.photos[0]?.url || null
    } : null,
    data: activity.data
  }));

  return {
    activities: formattedActivities,
    total: formattedActivities.length
  };
}

/**
 * Get recent users for the admin dashboard
 *
 * @route GET /api/admin/dashboard/recent-users
 * @access Admin
 */
exports.getRecentUsers = async (req, res) => {
  try {
    const { page = 1, limit = 10, filter = '', refresh = 'false' } = req.query;
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const shouldRefresh = refresh === 'true';

    // Create a cache key based on the query parameters
    const cacheKey = `${CACHE_PREFIXES.DASHBOARD_STATS}users:page:${pageNum}:limit:${limitNum}:filter:${filter}`;

    // If refresh is requested, invalidate the cache
    if (shouldRefresh) {
      await invalidateDashboardStatsCache();
    }

    // If not refreshing, try to get from cache
    if (!shouldRefresh) {
      const cachedData = await getOrSetCache(
        cacheKey,
        async () => {
          return await fetchRecentUsers(pageNum, limitNum, filter);
        },
        300 // Cache for 5 minutes
      );

      return res.status(200).json(cachedData);
    }

    // If refresh is requested, fetch fresh data
    const userData = await fetchRecentUsers(pageNum, limitNum, filter);
    return res.status(200).json(userData);
  } catch (error) {
    console.error('Error fetching recent users:', error);
    res.status(500).json({
      message: 'Failed to fetch recent users',
      error: error.message
    });
  }
};

/**
 * Fetch recent users data from the database
 * @param {number} pageNum - Page number
 * @param {number} limitNum - Number of users per page
 * @param {string} filter - Filter string
 * @returns {Object} User data
 */
async function fetchRecentUsers(pageNum, limitNum, filter) {
  const skip = (pageNum - 1) * limitNum;

  // Build where clause for filtering
  const whereClause = {};

  if (filter) {
    whereClause.OR = [
      { profile: { firstName: { contains: filter, mode: 'insensitive' } } },
      { profile: { lastName: { contains: filter, mode: 'insensitive' } } },
      { phone: { contains: filter } },
      { email: { contains: filter } }
    ];
  }

  // Get recent users
  const users = await prisma.user.findMany({
    where: whereClause,
    orderBy: {
      createdAt: 'desc'
    },
    skip,
    take: limitNum,
    include: {
      profile: {
        select: {
          firstName: true,
          lastName: true,
          gender: true,
          dateOfBirth: true,
          city: true,
          state: true,
          photos: {
            where: { isMain: true },
            select: { url: true },
            take: 1
          }
        }
      }
    }
  });

  // Get total count for pagination
  const totalUsers = await prisma.user.count({
    where: whereClause
  });

  // Format users for response
  const formattedUsers = users.map(user => ({
    id: user.id,
    name: `${user.profile?.firstName || ''} ${user.profile?.lastName || ''}`.trim(),
    photoUrl: user.profile?.photos[0]?.url || null,
    gender: user.profile?.gender || 'UNKNOWN',
    age: user.profile?.dateOfBirth ? calculateAge(user.profile.dateOfBirth) : null,
    location: user.profile?.city && user.profile?.state
      ? `${user.profile.city}, ${user.profile.state}`
      : user.profile?.city || user.profile?.state || 'Unknown',
    phone: user.phone,
    email: user.email,
    isPremium: user.isPremium,
    isVerified: user.isVerified,
    status: user.profileStatus,
    createdAt: user.createdAt
  }));

  return {
    users: formattedUsers,
    total: totalUsers,
    totalPages: Math.ceil(totalUsers / limitNum),
    currentPage: pageNum
  };
}

/**
 * Calculate age from date of birth
 *
 * @param {Date} dateOfBirth - Date of birth
 * @returns {number} Age in years
 */
function calculateAge(dateOfBirth) {
  const today = new Date();
  const birthDate = new Date(dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }

  return age;
}
