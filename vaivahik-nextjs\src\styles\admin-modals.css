/* Ad<PERSON> Modals Global Styles */

/* Force body to show modal */
body.modal-open {
  overflow: hidden;
}

/* Force modal to display */
.modal-overlay {
  display: flex !important;
}

/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex !important;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

/* Modal Container */
.modal {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  display: flex !important;
  flex-direction: column;
}

/* Modal Header */
.modal-header {
  display: flex !important;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 10;
}

.modal-title {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.modal-close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
}

.modal-close-button:hover {
  color: #333;
}

/* Modal Body */
.modal-body {
  padding: 24px;
  overflow-y: auto;
  max-height: calc(90vh - 130px);
  display: block !important; /* Force display */
}

/* Modal Footer */
.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #eee;
  display: flex !important;
  justify-content: flex-end;
  gap: 12px;
  position: sticky;
  bottom: 0;
  background-color: white;
  z-index: 10;
}

/* Button Styles */
.btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn-primary {
  background-color: #7c4dff;
  color: white;
}

.btn-primary:hover {
  background-color: #6a3de8;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #333;
}

.btn-secondary:hover {
  background-color: #e0e0e0;
}

.btn-success {
  background-color: #52c41a;
  color: white;
}

.btn-success:hover {
  background-color: #49ad18;
}

.btn-danger {
  background-color: #ff4d4f;
  color: white;
}

.btn-danger:hover {
  background-color: #e64548;
}

.btn-lg {
  padding: 10px 20px;
  font-size: 16px;
}

/* Action Buttons */
.action-buttons-container {
  margin-top: 20px;
  display: block !important;
}

.action-description {
  margin-bottom: 16px;
}

.action-buttons-row {
  display: flex !important;
  gap: 16px;
  justify-content: center;
}

.action-icon {
  margin-right: 8px;
}

/* Document/Evidence Viewer */
.document-modal {
  max-width: 90vw;
  max-height: 90vh;
}

.document-modal-body {
  padding: 0;
  display: flex !important;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0;
}

.document-full-image {
  max-width: 100%;
  max-height: 70vh;
}

/* User Profile Section */
.user-profile-section {
  margin-bottom: 20px;
  display: block !important;
}

.user-profile-header {
  display: flex !important;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 20px;
}

.user-avatar.large {
  width: 80px;
  height: 80px;
  font-size: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary);
  color: white;
  border-radius: 50%;
  overflow: hidden;
}

.user-avatar.large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-profile-info {
  flex: 1;
}

.user-profile-info h3 {
  margin: 0 0 5px 0;
  font-size: 1.5rem;
}

.user-meta {
  display: flex;
  gap: 10px;
  color: #666;
  margin-bottom: 5px;
}

/* Tab Sections */
.tab-section {
  margin-bottom: 25px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: block !important;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 15px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
  color: var(--primary);
}

/* Detail Grids */
.user-details-grid,
.report-info-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.detail-item {
  margin-bottom: 10px;
  display: block !important;
}

.detail-label {
  font-weight: 500;
  color: #666;
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.detail-value {
  font-size: 1rem;
  color: var(--text-dark);
}

/* Document/Evidence Grid */
.documents-grid,
.evidence-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.document-card,
.evidence-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  display: block !important;
}

.document-card:hover,
.evidence-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.document-preview,
.evidence-preview {
  height: 150px;
  overflow: hidden;
  background-color: #f5f5f5;
  display: flex !important;
  align-items: center;
  justify-content: center;
}

.document-preview img,
.evidence-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.document-info,
.evidence-info {
  padding: 10px;
  display: block !important;
}

.document-type,
.evidence-type {
  font-weight: 600;
  margin-bottom: 5px;
}

.document-name,
.evidence-date {
  font-size: 0.9rem;
  color: #666;
}

.document-action {
  font-size: 0.8rem;
  color: var(--primary);
  margin-top: 5px;
}

/* Status Badges */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-badge.pending {
  background-color: #faad14;
  color: white;
}

.status-badge.resolved {
  background-color: #52c41a;
  color: white;
}

.status-badge.dismissed {
  background-color: #999;
  color: white;
}

.status-badge.approved {
  background-color: #52c41a;
  color: white;
}

.status-badge.rejected {
  background-color: #ff4d4f;
  color: white;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .modal {
    max-width: 95vw;
    max-height: 95vh;
  }

  .modal-body {
    padding: 16px;
    max-height: calc(95vh - 120px);
  }

  .action-buttons-row {
    flex-direction: column;
    width: 100%;
  }

  .btn-lg {
    width: 100%;
  }

  .user-details-grid,
  .report-info-grid {
    grid-template-columns: 1fr;
  }

  .documents-grid,
  .evidence-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}
