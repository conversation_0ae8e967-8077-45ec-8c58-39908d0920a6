/**
 * Privacy Policy Page
 * Comprehensive privacy policy for the matrimony platform
 */

import { useState, useEffect } from 'react';
import Head from 'next/head';
import axios from 'axios';
import {
  Box,
  Container,
  Typography,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  List,
  ListItem,
  ListItemText,
  Alert,
  Button,
  Chip
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Security as SecurityIcon,
  Shield as ShieldIcon,
  Gavel as LegalIcon
} from '@mui/icons-material';

export default function PrivacyPolicy() {
  const [expandedSection, setExpandedSection] = useState('overview');
  const [dynamicPolicy, setDynamicPolicy] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDynamicPolicy();
  }, []);

  const fetchDynamicPolicy = async () => {
    try {
      const response = await axios.get('/api/policies/privacy-policy');
      if (response.data.success) {
        setDynamicPolicy(response.data.policy);
      }
    } catch (error) {
      console.error('Error fetching dynamic policy:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAccordionChange = (panel) => (event, isExpanded) => {
    setExpandedSection(isExpanded ? panel : false);
  };

  const lastUpdated = "December 28, 2024";

  return (
    <>
      <Head>
        <title>Privacy Policy - Vaivahik Matrimony</title>
        <meta name="description" content="Privacy Policy for Vaivahik Matrimony Platform - How we collect, use, and protect your personal information" />
        <meta name="robots" content="index, follow" />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header */}
        <Paper elevation={2} sx={{ p: 4, mb: 4, background: 'linear-gradient(135deg, #D9534F 0%, #4A00E0 100%)', color: 'white' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <ShieldIcon sx={{ fontSize: 40, mr: 2 }} />
            <Typography variant="h3" component="h1">
              Privacy Policy
            </Typography>
          </Box>
          <Typography variant="h6" sx={{ opacity: 0.9 }}>
            Your privacy is our priority. Learn how we protect your personal information.
          </Typography>
          <Chip 
            label={`Last Updated: ${lastUpdated}`} 
            sx={{ mt: 2, bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
          />
        </Paper>

        {/* Quick Summary */}
        <Alert severity="info" sx={{ mb: 4 }}>
          <Typography variant="subtitle1" gutterBottom>
            <strong>Quick Summary:</strong>
          </Typography>
          <Typography variant="body2">
            We collect only necessary information to provide matrimony services, never sell your data to third parties, 
            use industry-standard security measures, and give you full control over your privacy settings.
          </Typography>
        </Alert>

        {/* Privacy Policy Sections */}
        <Box>
          {/* Information We Collect */}
          <Accordion 
            expanded={expandedSection === 'collection'} 
            onChange={handleAccordionChange('collection')}
            sx={{ mb: 2 }}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">1. Information We Collect</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="subtitle1" gutterBottom>
                <strong>Personal Information:</strong>
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText primary="Basic Details: Name, age, gender, contact information" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Profile Information: Education, occupation, family details, preferences" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Birth Details: Date, time, and place of birth (for horoscope matching)" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Photos: Profile pictures and photo gallery" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Communication: Messages, interests sent/received" />
                </ListItem>
              </List>

              <Typography variant="subtitle1" gutterBottom sx={{ mt: 3 }}>
                <strong>Technical Information:</strong>
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText primary="Device Information: IP address, browser type, device type" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Usage Data: Pages visited, features used, time spent" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Location Data: City/state for matching purposes (with consent)" />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>

          {/* How We Use Information */}
          <Accordion 
            expanded={expandedSection === 'usage'} 
            onChange={handleAccordionChange('usage')}
            sx={{ mb: 2 }}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">2. How We Use Your Information</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                <ListItem>
                  <ListItemText 
                    primary="Matchmaking Services" 
                    secondary="To find and suggest compatible matches based on your preferences"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Profile Display" 
                    secondary="To show your profile to potential matches (with privacy controls)"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Communication" 
                    secondary="To enable messaging and interaction between members"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Service Improvement" 
                    secondary="To enhance our matching algorithms and user experience"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Safety & Security" 
                    secondary="To verify profiles, prevent fraud, and ensure platform safety"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Legal Compliance" 
                    secondary="To comply with applicable laws and regulations"
                  />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>

          {/* Information Sharing */}
          <Accordion 
            expanded={expandedSection === 'sharing'} 
            onChange={handleAccordionChange('sharing')}
            sx={{ mb: 2 }}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">3. Information Sharing</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Alert severity="success" sx={{ mb: 2 }}>
                <strong>We never sell your personal information to third parties.</strong>
              </Alert>
              
              <Typography variant="subtitle1" gutterBottom>
                <strong>We may share information only in these limited circumstances:</strong>
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText 
                    primary="With Other Members" 
                    secondary="Profile information visible to potential matches (controlled by your privacy settings)"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Service Providers" 
                    secondary="Trusted partners who help us operate the platform (under strict confidentiality)"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Legal Requirements" 
                    secondary="When required by law, court order, or to protect rights and safety"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Business Transfers" 
                    secondary="In case of merger, acquisition, or sale (with continued privacy protection)"
                  />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>

          {/* Privacy Controls */}
          <Accordion 
            expanded={expandedSection === 'controls'} 
            onChange={handleAccordionChange('controls')}
            sx={{ mb: 2 }}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">4. Your Privacy Controls</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="subtitle1" gutterBottom>
                <strong>You have full control over your privacy:</strong>
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText 
                    primary="Profile Visibility" 
                    secondary="Control who can see your profile and photos"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Contact Information" 
                    secondary="Decide when and with whom to share contact details"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Communication Preferences" 
                    secondary="Choose who can message you and how you receive notifications"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Data Access & Deletion" 
                    secondary="Request a copy of your data or delete your account anytime"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Marketing Communications" 
                    secondary="Opt-out of promotional emails and notifications"
                  />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>

          {/* Data Security */}
          <Accordion 
            expanded={expandedSection === 'security'} 
            onChange={handleAccordionChange('security')}
            sx={{ mb: 2 }}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">5. Data Security</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="subtitle1" gutterBottom>
                <strong>We protect your information using:</strong>
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText 
                    primary="Encryption" 
                    secondary="SSL/TLS encryption for data transmission and storage"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Access Controls" 
                    secondary="Strict employee access controls and regular security training"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Regular Audits" 
                    secondary="Periodic security assessments and vulnerability testing"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Secure Infrastructure" 
                    secondary="Industry-standard hosting and database security measures"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Incident Response" 
                    secondary="Rapid response procedures for any security incidents"
                  />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>

          {/* Data Retention */}
          <Accordion 
            expanded={expandedSection === 'retention'} 
            onChange={handleAccordionChange('retention')}
            sx={{ mb: 2 }}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">6. Data Retention</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                <ListItem>
                  <ListItemText 
                    primary="Active Accounts" 
                    secondary="We retain your information while your account is active"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Inactive Accounts" 
                    secondary="Profiles inactive for 2+ years may be archived or deleted"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Deleted Accounts" 
                    secondary="Most data deleted within 30 days; some records kept for legal compliance"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Communication Records" 
                    secondary="Messages and interactions retained for safety and dispute resolution"
                  />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>

          {/* Your Rights */}
          <Accordion 
            expanded={expandedSection === 'rights'} 
            onChange={handleAccordionChange('rights')}
            sx={{ mb: 2 }}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">7. Your Rights</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="subtitle1" gutterBottom>
                <strong>You have the right to:</strong>
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText primary="Access your personal data and download a copy" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Correct or update inaccurate information" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Delete your account and personal data" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Restrict or object to certain data processing" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Data portability (transfer to another service)" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Withdraw consent for optional data processing" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="File a complaint with data protection authorities" />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>

          {/* Contact Information */}
          <Accordion 
            expanded={expandedSection === 'contact'} 
            onChange={handleAccordionChange('contact')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">8. Contact Us</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body1" gutterBottom>
                For privacy-related questions or to exercise your rights, contact us:
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2"><strong>Email:</strong> <EMAIL></Typography>
                <Typography variant="body2"><strong>Phone:</strong> +91-XXXX-XXXXXX</Typography>
                <Typography variant="body2"><strong>Address:</strong> [Your Company Address]</Typography>
                <Typography variant="body2"><strong>Data Protection Officer:</strong> <EMAIL></Typography>
              </Box>
            </AccordionDetails>
          </Accordion>
        </Box>

        {/* Footer */}
        <Paper elevation={1} sx={{ p: 3, mt: 4, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            This Privacy Policy is effective as of {lastUpdated}. We may update this policy from time to time. 
            We will notify you of any material changes via email or platform notification.
          </Typography>
          <Button variant="outlined" sx={{ mt: 2 }} href="/legal/terms-of-service">
            View Terms of Service
          </Button>
        </Paper>
      </Container>
    </>
  );
}
