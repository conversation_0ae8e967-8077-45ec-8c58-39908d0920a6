const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function seedAlgorithmSettings() {
  console.log('Seeding algorithm settings...');

  // Default algorithm settings
  const defaultSettings = [
    // General settings
    {
      category: 'GENERAL',
      key: 'matchingAlgorithmVersion',
      value: 'v1.0',
      dataType: 'STRING',
      description: 'Current version of the matching algorithm',
      isActive: true
    },
    {
      category: 'GENERAL',
      key: 'enableAIMatching',
      value: 'true',
      dataType: 'BOOLEAN',
      description: 'Enable AI-powered matching',
      isActive: true
    },
    {
      category: 'GENERAL',
      key: 'matchingModel',
      value: 'TWO_TOWER',
      dataType: 'STRING',
      description: 'Type of matching model to use (TWO_TOWER, CONTENT_BASED, HYBRID)',
      isActive: true
    },
    
    // Weights
    {
      category: 'WEIGHTS',
      key: 'ageWeight',
      value: '8',
      dataType: 'NUMBER',
      description: 'Weight for age compatibility in matching algorithm',
      isActive: true
    },
    {
      category: 'WEIGHTS',
      key: 'heightWeight',
      value: '6',
      dataType: 'NUMBER',
      description: 'Weight for height compatibility in matching algorithm',
      isActive: true
    },
    {
      category: 'WEIGHTS',
      key: 'educationWeight',
      value: '7',
      dataType: 'NUMBER',
      description: 'Weight for education compatibility in matching algorithm',
      isActive: true
    },
    {
      category: 'WEIGHTS',
      key: 'occupationWeight',
      value: '7',
      dataType: 'NUMBER',
      description: 'Weight for occupation compatibility in matching algorithm',
      isActive: true
    },
    {
      category: 'WEIGHTS',
      key: 'locationWeight',
      value: '8',
      dataType: 'NUMBER',
      description: 'Weight for location compatibility in matching algorithm',
      isActive: true
    },
    {
      category: 'WEIGHTS',
      key: 'casteWeight',
      value: '9',
      dataType: 'NUMBER',
      description: 'Weight for caste compatibility in matching algorithm',
      isActive: true
    },
    {
      category: 'WEIGHTS',
      key: 'subCasteWeight',
      value: '5',
      dataType: 'NUMBER',
      description: 'Weight for sub-caste compatibility in matching algorithm',
      isActive: true
    },
    {
      category: 'WEIGHTS',
      key: 'gotraWeight',
      value: '6',
      dataType: 'NUMBER',
      description: 'Weight for gotra compatibility in matching algorithm',
      isActive: true
    },
    {
      category: 'WEIGHTS',
      key: 'incomeWeight',
      value: '5',
      dataType: 'NUMBER',
      description: 'Weight for income compatibility in matching algorithm',
      isActive: true
    },
    {
      category: 'WEIGHTS',
      key: 'lifestyleWeight',
      value: '4',
      dataType: 'NUMBER',
      description: 'Weight for lifestyle compatibility in matching algorithm',
      isActive: true
    },
    
    // Thresholds
    {
      category: 'THRESHOLDS',
      key: 'minimumMatchScore',
      value: '65',
      dataType: 'NUMBER',
      description: 'Minimum score required to consider a match',
      isActive: true
    },
    {
      category: 'THRESHOLDS',
      key: 'highQualityMatchThreshold',
      value: '80',
      dataType: 'NUMBER',
      description: 'Threshold for high-quality matches',
      isActive: true
    },
    
    // A/B Testing
    {
      category: 'AB_TESTING',
      key: 'abTestingEnabled',
      value: 'false',
      dataType: 'BOOLEAN',
      description: 'Enable A/B testing for matching algorithm',
      isActive: true
    },
    {
      category: 'AB_TESTING',
      key: 'abTestingVariant',
      value: 'A',
      dataType: 'STRING',
      description: 'Default variant for A/B testing',
      isActive: true
    },
    {
      category: 'AB_TESTING',
      key: 'abTestingDistribution',
      value: '50',
      dataType: 'NUMBER',
      description: 'Percentage of users to receive variant B',
      isActive: true
    },
    
    // Advanced settings
    {
      category: 'ADVANCED',
      key: 'maxDistanceKm',
      value: '100',
      dataType: 'NUMBER',
      description: 'Maximum distance in kilometers for location-based matching',
      isActive: true
    },
    {
      category: 'ADVANCED',
      key: 'maxAgeDifference',
      value: '10',
      dataType: 'NUMBER',
      description: 'Maximum age difference in years for matching',
      isActive: true
    },
    {
      category: 'ADVANCED',
      key: 'considerUserActivity',
      value: 'true',
      dataType: 'BOOLEAN',
      description: 'Consider user activity in matching algorithm',
      isActive: true
    },
    {
      category: 'ADVANCED',
      key: 'boostNewProfiles',
      value: 'true',
      dataType: 'BOOLEAN',
      description: 'Boost new profiles in matching algorithm',
      isActive: true
    },
    {
      category: 'ADVANCED',
      key: 'boostNewProfilesDays',
      value: '7',
      dataType: 'NUMBER',
      description: 'Number of days to boost new profiles',
      isActive: true
    },
    {
      category: 'ADVANCED',
      key: 'boostVerifiedProfiles',
      value: 'true',
      dataType: 'BOOLEAN',
      description: 'Boost verified profiles in matching algorithm',
      isActive: true
    },
    {
      category: 'ADVANCED',
      key: 'boostVerifiedProfilesAmount',
      value: '10',
      dataType: 'NUMBER',
      description: 'Percentage boost for verified profiles',
      isActive: true
    },
    {
      category: 'ADVANCED',
      key: 'boostPremiumProfiles',
      value: 'true',
      dataType: 'BOOLEAN',
      description: 'Boost premium profiles in matching algorithm',
      isActive: true
    },
    {
      category: 'ADVANCED',
      key: 'boostPremiumProfilesAmount',
      value: '15',
      dataType: 'NUMBER',
      description: 'Percentage boost for premium profiles',
      isActive: true
    },
    
    // Two-Tower Model settings
    {
      category: 'TWO_TOWER',
      key: 'embeddingSize',
      value: '128',
      dataType: 'NUMBER',
      description: 'Size of the embedding vectors in the two-tower model',
      isActive: true
    },
    {
      category: 'TWO_TOWER',
      key: 'learningRate',
      value: '0.001',
      dataType: 'NUMBER',
      description: 'Learning rate for training the two-tower model',
      isActive: true
    },
    {
      category: 'TWO_TOWER',
      key: 'batchSize',
      value: '64',
      dataType: 'NUMBER',
      description: 'Batch size for training the two-tower model',
      isActive: true
    },
    {
      category: 'TWO_TOWER',
      key: 'epochs',
      value: '10',
      dataType: 'NUMBER',
      description: 'Number of epochs for training the two-tower model',
      isActive: true
    },
    {
      category: 'TWO_TOWER',
      key: 'userTowerLayers',
      value: '[128, 64]',
      dataType: 'JSON',
      description: 'Hidden layer sizes for the user tower',
      isActive: true
    },
    {
      category: 'TWO_TOWER',
      key: 'matchTowerLayers',
      value: '[128, 64]',
      dataType: 'JSON',
      description: 'Hidden layer sizes for the match tower',
      isActive: true
    },
    {
      category: 'TWO_TOWER',
      key: 'dropoutRate',
      value: '0.2',
      dataType: 'NUMBER',
      description: 'Dropout rate for regularization in the two-tower model',
      isActive: true
    },
    {
      category: 'TWO_TOWER',
      key: 'similarityMetric',
      value: 'cosine',
      dataType: 'STRING',
      description: 'Similarity metric to use (cosine, dot, euclidean)',
      isActive: true
    }
  ];

  // Upsert algorithm settings
  for (const setting of defaultSettings) {
    await prisma.algorithmSetting.upsert({
      where: {
        category_key: {
          category: setting.category,
          key: setting.key
        }
      },
      update: setting,
      create: setting
    });
  }

  console.log('Algorithm settings seeded successfully!');
}

async function seedAlgorithmModels() {
  console.log('Seeding algorithm models...');

  // Default algorithm models
  const defaultModels = [
    {
      name: 'TwoTowerModel',
      version: '1.0.0',
      type: 'TWO_TOWER',
      description: 'Two-tower neural network model for matrimony matching',
      modelPath: '/models/two_tower_v1.0.0',
      config: JSON.stringify({
        userTowerLayers: [128, 64],
        matchTowerLayers: [128, 64],
        embeddingSize: 128,
        dropoutRate: 0.2,
        similarityMetric: 'cosine'
      }),
      metrics: JSON.stringify({
        accuracy: 0.85,
        precision: 0.82,
        recall: 0.79,
        f1Score: 0.80
      }),
      isActive: true,
      isDefault: true
    },
    {
      name: 'ContentBasedModel',
      version: '1.0.0',
      type: 'CONTENT_BASED',
      description: 'Content-based filtering model for matrimony matching',
      modelPath: '/models/content_based_v1.0.0',
      config: JSON.stringify({
        featureWeights: {
          age: 0.8,
          height: 0.6,
          education: 0.7,
          occupation: 0.7,
          location: 0.8,
          caste: 0.9,
          subCaste: 0.5,
          gotra: 0.6,
          income: 0.5,
          lifestyle: 0.4
        }
      }),
      metrics: JSON.stringify({
        accuracy: 0.82,
        precision: 0.79,
        recall: 0.76,
        f1Score: 0.77
      }),
      isActive: true,
      isDefault: false
    }
  ];

  // Upsert algorithm models
  for (const model of defaultModels) {
    await prisma.algorithmModel.upsert({
      where: {
        name_version: {
          name: model.name,
          version: model.version
        }
      },
      update: model,
      create: model
    });
  }

  console.log('Algorithm models seeded successfully!');
}

async function main() {
  try {
    await seedAlgorithmSettings();
    await seedAlgorithmModels();
  } catch (error) {
    console.error('Error seeding algorithm data:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
