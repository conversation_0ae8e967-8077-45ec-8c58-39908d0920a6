/**
 * Chat Support Bot Component
 * AI-powered FAQ handling and customer support assistance
 */

import { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  IconButton,
  List,
  ListItem,
  ListItemText,
  Avatar,
  Chip,
  Button,
  Divider,
  Fab,
  Collapse,
  CircularProgress
} from '@mui/material';
import {
  Send as SendIcon,
  Support as SupportIcon,
  Close as CloseIcon,
  SmartToy as BotIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  WhatsApp as WhatsAppIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import axios from 'axios';

export default function ChatSupportBot() {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showQuickActions, setShowQuickActions] = useState(true);
  const messagesEndRef = useRef(null);

  // FAQ categories and responses
  const faqCategories = {
    registration: {
      title: 'Registration & Profile',
      questions: [
        'How do I create a profile?',
        'What documents are required?',
        'How to verify my profile?',
        'Can I edit my profile later?'
      ]
    },
    premium: {
      title: 'Premium Features',
      questions: [
        'What are premium features?',
        'How much does premium cost?',
        'How to upgrade to premium?',
        'Can I cancel my subscription?'
      ]
    },
    matching: {
      title: 'Matching & Search',
      questions: [
        'How does matching work?',
        'Why am I not getting matches?',
        'How to search for profiles?',
        'What is Kundli matching?'
      ]
    },
    communication: {
      title: 'Communication',
      questions: [
        'How to send interest?',
        'How to chat with matches?',
        'How to get contact details?',
        'Is my information safe?'
      ]
    }
  };

  // Bot responses database
  const botResponses = {
    greeting: "Hello! I'm your Vaivahik support assistant. How can I help you today?",
    
    // Registration responses
    'how do i create a profile': "To create a profile: 1) Click 'Register' 2) Fill basic details 3) Upload photos 4) Complete profile information 5) Verify your mobile number. Need help with any specific step?",
    
    'what documents are required': "For verification you need: 1) Government ID (Aadhaar/PAN/Passport) 2) Recent photo 3) Educational certificates (optional) 4) Income proof (for premium features). All documents are securely stored.",
    
    'how to verify my profile': "Profile verification: 1) Upload clear government ID 2) Take a selfie for photo verification 3) Verify mobile number with OTP 4) Our team reviews within 24-48 hours. Verified profiles get 3x more responses!",
    
    // Premium responses
    'what are premium features': "Premium features include: ✅ Unlimited profile views ✅ Advanced search filters ✅ Contact details access ✅ Priority customer support ✅ Kundli matching ✅ Profile boost ✅ Chat with anyone",
    
    'how much does premium cost': "Premium plans: 📅 1 Month: ₹999 📅 3 Months: ₹2499 (Save 17%) 📅 6 Months: ₹3999 (Save 33%) 📅 12 Months: ₹5999 (Save 50%). All plans include full premium features!",
    
    // Matching responses
    'how does matching work': "Our AI matching considers: 🎯 Age, education, location 🎯 Religious & cultural preferences 🎯 Family background 🎯 Lifestyle choices 🎯 Kundli compatibility (premium). The more complete your profile, the better matches you get!",
    
    'why am i not getting matches': "To get more matches: ✅ Complete your profile 100% ✅ Add recent, clear photos ✅ Verify your profile ✅ Be active on the platform ✅ Consider premium for advanced search. Need help optimizing your profile?",
    
    // Communication responses
    'how to send interest': "To send interest: 1) Visit a profile you like 2) Click 'Send Interest' 3) Add a personal message (optional) 4) Wait for their response. Premium users can send unlimited interests!",
    
    'is my information safe': "Your privacy is our priority: 🔒 Bank-level encryption 🔒 Secure data storage 🔒 No data selling 🔒 Privacy controls 🔒 Contact reveal only with permission. You control who sees what!",
    
    // Default responses
    default: "I understand you need help with that. Let me connect you with our human support team for personalized assistance.",
    
    // Contact options
    contact: "You can reach our support team: 📞 Call: +91-XXXX-XXXXXX 📧 Email: <EMAIL> 💬 WhatsApp: +91-XXXX-XXXXXX ⏰ Hours: Mon-Sat, 9 AM - 6 PM IST"
  };

  useEffect(() => {
    if (isOpen && messages.length === 0) {
      // Initial greeting
      setMessages([{
        id: 1,
        text: botResponses.greeting,
        sender: 'bot',
        timestamp: new Date()
      }]);
    }
  }, [isOpen]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage = {
      id: Date.now(),
      text: inputMessage,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);
    setShowQuickActions(false);

    // Simulate bot thinking time
    setTimeout(() => {
      const botResponse = generateBotResponse(inputMessage.toLowerCase());
      const botMessage = {
        id: Date.now() + 1,
        text: botResponse,
        sender: 'bot',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botMessage]);
      setIsTyping(false);
    }, 1000 + Math.random() * 1000);
  };

  const generateBotResponse = (userInput) => {
    // Check for exact matches first
    for (const [key, response] of Object.entries(botResponses)) {
      if (userInput.includes(key)) {
        return response;
      }
    }

    // Check for keywords
    if (userInput.includes('premium') || userInput.includes('subscription') || userInput.includes('upgrade')) {
      return botResponses['what are premium features'];
    }
    
    if (userInput.includes('match') || userInput.includes('search')) {
      return botResponses['how does matching work'];
    }
    
    if (userInput.includes('contact') || userInput.includes('phone') || userInput.includes('support')) {
      return botResponses.contact;
    }
    
    if (userInput.includes('safe') || userInput.includes('privacy') || userInput.includes('secure')) {
      return botResponses['is my information safe'];
    }

    return botResponses.default;
  };

  const handleQuickAction = (question) => {
    setInputMessage(question);
    handleSendMessage();
  };

  const handleContactSupport = (method) => {
    switch (method) {
      case 'phone':
        window.open('tel:+91XXXXXXXXXX');
        break;
      case 'email':
        window.open('mailto:<EMAIL>');
        break;
      case 'whatsapp':
        window.open('https://wa.me/91XXXXXXXXXX');
        break;
    }
  };

  return (
    <>
      {/* Support Bot Fab */}
      <Fab
        color="primary"
        sx={{
          position: 'fixed',
          bottom: 20,
          right: 20,
          zIndex: 1000,
          background: 'linear-gradient(135deg, #D9534F 0%, #4A00E0 100%)'
        }}
        onClick={() => setIsOpen(!isOpen)}
      >
        {isOpen ? <CloseIcon /> : <SupportIcon />}
      </Fab>

      {/* Chat Window */}
      <Collapse in={isOpen}>
        <Paper
          elevation={8}
          sx={{
            position: 'fixed',
            bottom: 90,
            right: 20,
            width: 350,
            height: 500,
            zIndex: 1000,
            display: 'flex',
            flexDirection: 'column',
            borderRadius: 2,
            overflow: 'hidden'
          }}
        >
          {/* Header */}
          <Box
            sx={{
              background: 'linear-gradient(135deg, #D9534F 0%, #4A00E0 100%)',
              color: 'white',
              p: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', mr: 1 }}>
                <BotIcon />
              </Avatar>
              <Box>
                <Typography variant="subtitle1" fontWeight="bold">
                  Vaivahik Support
                </Typography>
                <Typography variant="caption" sx={{ opacity: 0.9 }}>
                  AI Assistant • Online
                </Typography>
              </Box>
            </Box>
            <IconButton
              size="small"
              sx={{ color: 'white' }}
              onClick={() => setIsOpen(false)}
            >
              <CloseIcon />
            </IconButton>
          </Box>

          {/* Messages */}
          <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
            <List dense>
              {messages.map((message) => (
                <ListItem
                  key={message.id}
                  sx={{
                    flexDirection: 'column',
                    alignItems: message.sender === 'user' ? 'flex-end' : 'flex-start',
                    px: 1,
                    py: 0.5
                  }}
                >
                  <Paper
                    elevation={1}
                    sx={{
                      p: 1.5,
                      maxWidth: '80%',
                      bgcolor: message.sender === 'user' ? 'primary.main' : 'grey.100',
                      color: message.sender === 'user' ? 'white' : 'text.primary',
                      borderRadius: 2,
                      borderTopRightRadius: message.sender === 'user' ? 0 : 2,
                      borderTopLeftRadius: message.sender === 'bot' ? 0 : 2
                    }}
                  >
                    <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                      {message.text}
                    </Typography>
                  </Paper>
                  <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </Typography>
                </ListItem>
              ))}
              
              {isTyping && (
                <ListItem sx={{ alignItems: 'flex-start', px: 1 }}>
                  <Paper elevation={1} sx={{ p: 1.5, bgcolor: 'grey.100', borderRadius: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <CircularProgress size={16} sx={{ mr: 1 }} />
                      <Typography variant="body2" color="text.secondary">
                        Support is typing...
                      </Typography>
                    </Box>
                  </Paper>
                </ListItem>
              )}
            </List>
            <div ref={messagesEndRef} />
          </Box>

          {/* Quick Actions */}
          {showQuickActions && (
            <Box sx={{ p: 1, borderTop: 1, borderColor: 'divider' }}>
              <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                Quick Help:
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                {Object.values(faqCategories).slice(0, 2).map((category) => (
                  category.questions.slice(0, 2).map((question, index) => (
                    <Chip
                      key={`${category.title}-${index}`}
                      label={question}
                      size="small"
                      variant="outlined"
                      onClick={() => handleQuickAction(question)}
                      sx={{ fontSize: '0.7rem' }}
                    />
                  ))
                ))}
              </Box>
            </Box>
          )}

          {/* Contact Options */}
          <Box sx={{ p: 1, borderTop: 1, borderColor: 'divider', bgcolor: 'grey.50' }}>
            <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
              Need human support?
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'space-around' }}>
              <IconButton size="small" onClick={() => handleContactSupport('phone')}>
                <PhoneIcon fontSize="small" />
              </IconButton>
              <IconButton size="small" onClick={() => handleContactSupport('email')}>
                <EmailIcon fontSize="small" />
              </IconButton>
              <IconButton size="small" onClick={() => handleContactSupport('whatsapp')}>
                <WhatsAppIcon fontSize="small" />
              </IconButton>
            </Box>
          </Box>

          {/* Input */}
          <Box sx={{ p: 1, borderTop: 1, borderColor: 'divider' }}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <TextField
                fullWidth
                size="small"
                placeholder="Type your question..."
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                disabled={isTyping}
              />
              <IconButton
                color="primary"
                onClick={handleSendMessage}
                disabled={!inputMessage.trim() || isTyping}
              >
                <SendIcon />
              </IconButton>
            </Box>
          </Box>
        </Paper>
      </Collapse>
    </>
  );
}
