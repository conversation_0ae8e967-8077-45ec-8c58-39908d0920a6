"""
Evaluation Script for Enhanced Two-Tower Model

This script evaluates the enhanced two-tower model on a test dataset
and generates performance metrics and visualizations.
"""

import os
import sys
import json
import argparse
import logging
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import roc_curve, precision_recall_curve, auc, confusion_matrix
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.enhanced_tower_model_pytorch import EnhancedMatrimonyMatchingModel
from services.model_trainer import ModelTrainer
from services.feature_processor import FeatureProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_test_data(data_path):
    """
    Load test data from JSON file
    
    Args:
        data_path (str): Path to the test data file
        
    Returns:
        tuple: user_profiles, match_profiles, test_pairs, labels
    """
    try:
        with open(data_path, 'r') as f:
            data = json.load(f)
        
        user_profiles = data.get('user_profiles', [])
        match_profiles = data.get('match_profiles', [])
        test_pairs = data.get('test_pairs', [])
        labels = data.get('labels', [])
        
        logger.info(f"Loaded {len(user_profiles)} user profiles, "
                   f"{len(match_profiles)} match profiles, "
                   f"{len(test_pairs)} test pairs")
        
        return user_profiles, match_profiles, test_pairs, labels
    except Exception as e:
        logger.error(f"Error loading test data: {str(e)}")
        return [], [], [], []

def prepare_test_data(user_profiles, match_profiles, test_pairs, labels):
    """
    Prepare test data for evaluation
    
    Args:
        user_profiles (list): List of user profile dictionaries
        match_profiles (list): List of match profile dictionaries
        test_pairs (list): List of (user_id, match_id) tuples for test examples
        labels (list): List of ground truth labels for test pairs
        
    Returns:
        tuple: user_features, match_features, labels
    """
    # Create dictionaries for quick lookup
    user_dict = {user['id']: user for user in user_profiles}
    match_dict = {match['id']: match for match in match_profiles}
    
    # Create feature processor
    feature_processor = FeatureProcessor()
    
    # Compute feature statistics
    feature_processor.compute_feature_stats(user_profiles + match_profiles)
    
    # Process test pairs
    user_features = []
    match_features = []
    processed_labels = []
    
    for (user_id, match_id), label in zip(test_pairs, labels):
        if user_id in user_dict and match_id in match_dict:
            user = user_dict[user_id]
            match = match_dict[match_id]
            
            # Get user preferences (simplified)
            user_preferences = {
                'minAge': user.get('age', 25) - 5 if user.get('age') else None,
                'maxAge': user.get('age', 25) + 5 if user.get('age') else None,
                'religion': user.get('religion'),
                'caste': user.get('caste')
            }
            
            # Process features
            user_feat, match_feat = feature_processor.process_match_pair(
                user, user_preferences, match
            )
            
            user_features.append(user_feat)
            match_features.append(match_feat)
            processed_labels.append(float(label))
    
    return user_features, match_features, processed_labels

def evaluate_model(model_path, user_features, match_features, labels):
    """
    Evaluate the model on test data
    
    Args:
        model_path (str): Path to the trained model
        user_features (list): List of user feature dictionaries
        match_features (list): List of match feature dictionaries
        labels (list): List of ground truth labels
        
    Returns:
        tuple: predictions, metrics
    """
    # Load model
    model = EnhancedMatrimonyMatchingModel()
    model.build_model()
    
    trainer = ModelTrainer(model)
    trainer.load_model(model_path)
    
    # Make predictions
    predictions = trainer.predict(user_features, match_features)
    
    # Calculate metrics
    metrics = calculate_metrics(labels, predictions)
    
    return predictions, metrics

def calculate_metrics(labels, predictions):
    """
    Calculate evaluation metrics
    
    Args:
        labels (list): Ground truth labels
        predictions (list): Predicted scores
        
    Returns:
        dict: Evaluation metrics
    """
    # Convert to numpy arrays
    labels = np.array(labels)
    predictions = np.array(predictions)
    
    # ROC curve and AUC
    fpr, tpr, roc_thresholds = roc_curve(labels, predictions)
    roc_auc = auc(fpr, tpr)
    
    # Precision-Recall curve and AUC
    precision, recall, pr_thresholds = precision_recall_curve(labels, predictions)
    pr_auc = auc(recall, precision)
    
    # Find optimal threshold using Youden's J statistic (TPR - FPR)
    optimal_idx = np.argmax(tpr - fpr)
    optimal_threshold = roc_thresholds[optimal_idx]
    
    # Binary predictions using optimal threshold
    binary_predictions = (predictions >= optimal_threshold).astype(int)
    
    # Confusion matrix
    tn, fp, fn, tp = confusion_matrix(labels, binary_predictions).ravel()
    
    # Calculate metrics
    accuracy = (tp + tn) / (tp + tn + fp + fn)
    precision_at_threshold = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall_at_threshold = tp / (tp + fn) if (tp + fn) > 0 else 0
    f1_score = 2 * precision_at_threshold * recall_at_threshold / (precision_at_threshold + recall_at_threshold) if (precision_at_threshold + recall_at_threshold) > 0 else 0
    
    # Return metrics
    metrics = {
        'roc_auc': roc_auc,
        'pr_auc': pr_auc,
        'optimal_threshold': optimal_threshold,
        'accuracy': accuracy,
        'precision': precision_at_threshold,
        'recall': recall_at_threshold,
        'f1_score': f1_score,
        'true_positives': int(tp),
        'false_positives': int(fp),
        'true_negatives': int(tn),
        'false_negatives': int(fn),
        'roc_curve': {
            'fpr': fpr.tolist(),
            'tpr': tpr.tolist(),
            'thresholds': roc_thresholds.tolist()
        },
        'pr_curve': {
            'precision': precision.tolist(),
            'recall': recall.tolist(),
            'thresholds': pr_thresholds.tolist() if len(pr_thresholds) > 0 else [0]
        }
    }
    
    return metrics

def visualize_evaluation(predictions, labels, metrics, output_dir):
    """
    Visualize evaluation results
    
    Args:
        predictions (list): Predicted scores
        labels (list): Ground truth labels
        metrics (dict): Evaluation metrics
        output_dir (str): Directory to save visualizations
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Plot ROC curve
    plt.figure(figsize=(10, 8))
    plt.plot(metrics['roc_curve']['fpr'], metrics['roc_curve']['tpr'], 
             label=f'ROC curve (AUC = {metrics["roc_auc"]:.3f})')
    plt.plot([0, 1], [0, 1], 'k--')
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('Receiver Operating Characteristic (ROC) Curve')
    plt.legend(loc="lower right")
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, 'roc_curve.png'))
    
    # Plot Precision-Recall curve
    plt.figure(figsize=(10, 8))
    plt.plot(metrics['pr_curve']['recall'], metrics['pr_curve']['precision'],
             label=f'PR curve (AUC = {metrics["pr_auc"]:.3f})')
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('Recall')
    plt.ylabel('Precision')
    plt.title('Precision-Recall Curve')
    plt.legend(loc="lower left")
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, 'pr_curve.png'))
    
    # Plot score distribution
    plt.figure(figsize=(10, 8))
    plt.hist([p for p, l in zip(predictions, labels) if l == 1], bins=20, alpha=0.5, label='Positive')
    plt.hist([p for p, l in zip(predictions, labels) if l == 0], bins=20, alpha=0.5, label='Negative')
    plt.axvline(x=metrics['optimal_threshold'], color='r', linestyle='--', 
                label=f'Optimal threshold: {metrics["optimal_threshold"]:.3f}')
    plt.xlabel('Predicted Score')
    plt.ylabel('Count')
    plt.title('Score Distribution')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, 'score_distribution.png'))
    
    # Create confusion matrix visualization
    plt.figure(figsize=(8, 6))
    cm = np.array([
        [metrics['true_negatives'], metrics['false_positives']],
        [metrics['false_negatives'], metrics['true_positives']]
    ])
    plt.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)
    plt.title('Confusion Matrix')
    plt.colorbar()
    tick_marks = np.arange(2)
    plt.xticks(tick_marks, ['Negative', 'Positive'])
    plt.yticks(tick_marks, ['Negative', 'Positive'])
    
    # Add text annotations
    thresh = cm.max() / 2.
    for i in range(2):
        for j in range(2):
            plt.text(j, i, format(cm[i, j], 'd'),
                     ha="center", va="center",
                     color="white" if cm[i, j] > thresh else "black")
    
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'confusion_matrix.png'))
    
    logger.info(f"Evaluation visualizations saved to {output_dir}")

def main():
    """Main function to evaluate the model"""
    parser = argparse.ArgumentParser(description='Evaluate the enhanced two-tower model')
    parser.add_argument('--model', type=str, required=True, help='Path to the trained model')
    parser.add_argument('--data', type=str, required=True, help='Path to test data JSON file')
    parser.add_argument('--output', type=str, default='evaluation', help='Output directory for evaluation results')
    args = parser.parse_args()
    
    # Create output directory
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    output_dir = os.path.join(args.output, f"evaluation_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    
    # Load test data
    user_profiles, match_profiles, test_pairs, labels = load_test_data(args.data)
    
    if not user_profiles or not match_profiles or not test_pairs:
        logger.error("No test data loaded, exiting")
        return
    
    # Prepare test data
    user_features, match_features, processed_labels = prepare_test_data(
        user_profiles, match_profiles, test_pairs, labels
    )
    
    # Evaluate model
    logger.info(f"Evaluating model from {args.model}...")
    predictions, metrics = evaluate_model(
        args.model, user_features, match_features, processed_labels
    )
    
    # Visualize evaluation results
    visualize_evaluation(predictions, processed_labels, metrics, output_dir)
    
    # Save metrics
    metrics_path = os.path.join(output_dir, "metrics.json")
    with open(metrics_path, 'w') as f:
        # Convert numpy values to Python types for JSON serialization
        serializable_metrics = {}
        for key, value in metrics.items():
            if isinstance(value, dict):
                serializable_metrics[key] = {}
                for k, v in value.items():
                    if isinstance(v, np.ndarray):
                        serializable_metrics[key][k] = v.tolist()
                    else:
                        serializable_metrics[key][k] = v
            elif isinstance(value, np.ndarray):
                serializable_metrics[key] = value.tolist()
            elif isinstance(value, (np.float32, np.float64)):
                serializable_metrics[key] = float(value)
            elif isinstance(value, (np.int32, np.int64)):
                serializable_metrics[key] = int(value)
            else:
                serializable_metrics[key] = value
        
        json.dump(serializable_metrics, f, indent=2)
    
    # Print summary metrics
    logger.info("Evaluation Results:")
    logger.info(f"ROC AUC: {metrics['roc_auc']:.4f}")
    logger.info(f"PR AUC: {metrics['pr_auc']:.4f}")
    logger.info(f"Accuracy: {metrics['accuracy']:.4f}")
    logger.info(f"Precision: {metrics['precision']:.4f}")
    logger.info(f"Recall: {metrics['recall']:.4f}")
    logger.info(f"F1 Score: {metrics['f1_score']:.4f}")
    logger.info(f"Optimal Threshold: {metrics['optimal_threshold']:.4f}")
    logger.info(f"Detailed metrics saved to {metrics_path}")

if __name__ == "__main__":
    main()
