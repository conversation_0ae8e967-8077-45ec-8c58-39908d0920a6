import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  Alert,
  CircularProgress,
  Switch,
  FormControlLabel,
  Tooltip,
  Avatar,
  Stack,
  Divider,
  Paper,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Badge,
  Tabs,
  Tab
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Search as SearchIcon,
  Campaign as CampaignIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
  FilterList as FilterListIcon,
  Schedule as ScheduleIcon,
  Assessment as AssessmentIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  Visibility as VisibilityIcon,
  Star as StarIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { toast } from 'react-toastify';
import { adminGet, adminPost, adminPut, adminDelete } from '@/services/adminApiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';

// Dynamic import for EnhancedAdminLayout to avoid SSR issues
const EnhancedAdminLayout = dynamic(() => import('@/components/admin/EnhancedAdminLayout'), {
  ssr: false
});

export default function PromotionsManagement() {
  const [promotions, setPromotions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [currentPromotion, setCurrentPromotion] = useState(null);
  const [promotionToDelete, setPromotionToDelete] = useState(null);
  const [stats, setStats] = useState({});
  const [filters, setFilters] = useState({
    status: '',
    search: ''
  });
  const [formData, setFormData] = useState({
    name: '',
    durationDays: 7,
    featureOverrides: {
      BASIC: {
        dailyMessageLimit: 20,
        canStartNewConversations: true,
        canSendImages: false,
        messageRetentionDays: 30
      },
      VERIFIED: {
        dailyMessageLimit: 50,
        canStartNewConversations: true,
        canSendImages: true,
        messageRetentionDays: 60
      }
    },
    display: {
      title: '',
      description: '',
      bannerColor: '#FF5722',
      bannerTextColor: '#FFFFFF',
      showCountdown: true,
      showOnHomepage: true,
      showInApp: true
    }
  });

  // Predefined promotion templates
  const promotionTemplates = [
    {
      name: 'freeChat',
      title: 'Free Chat Week',
      description: 'Enjoy unlimited messaging for a limited time!',
      durationDays: 7,
      color: '#FF5722'
    },
    {
      name: 'newUserBoost',
      title: 'Welcome Bonus',
      description: 'As a new user, enjoy enhanced messaging features!',
      durationDays: 14,
      color: '#4CAF50'
    },
    {
      name: 'festivalPromotion',
      title: 'Festival Special',
      description: 'Celebrate with special features!',
      durationDays: 3,
      color: '#9C27B0'
    }
  ];

  useEffect(() => {
    fetchPromotions();
    fetchStats();
  }, [filters]);

  const fetchPromotions = async () => {
    try {
      setLoading(true);
      const response = await adminGet(ADMIN_ENDPOINTS.PROMOTIONS);
      
      if (response.success) {
        setPromotions(response.promotions || []);
      } else {
        toast.error('Failed to fetch promotions');
      }
    } catch (error) {
      console.error('Error fetching promotions:', error);
      toast.error('Error fetching promotions');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Mock stats for now - can be implemented in backend later
      setStats({
        totalPromotions: promotions.length,
        activePromotions: promotions.filter(p => p.isCurrentlyActive).length,
        totalUsers: 1250,
        engagementRate: 78.5
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const handleCreatePromotion = () => {
    setCurrentPromotion(null);
    setFormData({
      name: '',
      durationDays: 7,
      featureOverrides: {
        BASIC: {
          dailyMessageLimit: 20,
          canStartNewConversations: true,
          canSendImages: false,
          messageRetentionDays: 30
        },
        VERIFIED: {
          dailyMessageLimit: 50,
          canStartNewConversations: true,
          canSendImages: true,
          messageRetentionDays: 60
        }
      },
      display: {
        title: '',
        description: '',
        bannerColor: '#FF5722',
        bannerTextColor: '#FFFFFF',
        showCountdown: true,
        showOnHomepage: true,
        showInApp: true
      }
    });
    setDialogOpen(true);
  };

  const handleEditPromotion = (promotion) => {
    setCurrentPromotion(promotion);
    setFormData({
      name: promotion.name || '',
      durationDays: promotion.durationDays || 7,
      featureOverrides: promotion.featureOverrides || {},
      display: promotion.display || {}
    });
    setDialogOpen(true);
  };

  const handleActivatePromotion = async (promotionName, durationDays = null) => {
    try {
      const response = await adminPost(`${ADMIN_ENDPOINTS.PROMOTIONS}/${promotionName}/activate`, {
        durationDays
      });

      if (response.success) {
        toast.success(`Promotion "${promotionName}" activated successfully`);
        fetchPromotions();
        fetchStats();
      } else {
        toast.error('Failed to activate promotion');
      }
    } catch (error) {
      console.error('Error activating promotion:', error);
      toast.error('Error activating promotion');
    }
  };

  const handleDeactivatePromotion = async (promotionName) => {
    try {
      const response = await adminPost(`${ADMIN_ENDPOINTS.PROMOTIONS}/${promotionName}/deactivate`);

      if (response.success) {
        toast.success(`Promotion "${promotionName}" deactivated successfully`);
        fetchPromotions();
        fetchStats();
      } else {
        toast.error('Failed to deactivate promotion');
      }
    } catch (error) {
      console.error('Error deactivating promotion:', error);
      toast.error('Error deactivating promotion');
    }
  };

  const handleSavePromotion = async () => {
    try {
      // Validate required fields
      if (!formData.name || !formData.display.title) {
        toast.error('Name and title are required');
        return;
      }

      let response;
      if (currentPromotion) {
        response = await adminPut(`${ADMIN_ENDPOINTS.PROMOTIONS}/${currentPromotion.name}`, formData);
      } else {
        response = await adminPost(ADMIN_ENDPOINTS.PROMOTIONS, formData);
      }

      if (response.success) {
        toast.success(`Promotion ${currentPromotion ? 'updated' : 'created'} successfully`);
        setDialogOpen(false);
        fetchPromotions();
        fetchStats();
      } else {
        toast.error(response.message || 'Failed to save promotion');
      }
    } catch (error) {
      console.error('Error saving promotion:', error);
      toast.error('Error saving promotion');
    }
  };

  const getStatusChip = (promotion) => {
    if (promotion.isCurrentlyActive) {
      return <Chip label="Active" color="success" size="small" />;
    } else if (promotion.isActive) {
      return <Chip label="Scheduled" color="warning" size="small" />;
    } else {
      return <Chip label="Inactive" color="default" size="small" />;
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const useTemplate = (template) => {
    setFormData({
      ...formData,
      name: template.name,
      durationDays: template.durationDays,
      display: {
        ...formData.display,
        title: template.title,
        description: template.description,
        bannerColor: template.color
      }
    });
  };

  return (
    <EnhancedAdminLayout title="Promotions Management">
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Promotions Management
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={() => {
                fetchPromotions();
                fetchStats();
              }}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleCreatePromotion}
            >
              Create Promotion
            </Button>
          </Box>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Total Promotions
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.totalPromotions || 0}
                    </Typography>
                  </Box>
                  <CampaignIcon color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Active Promotions
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.activePromotions || 0}
                    </Typography>
                  </Box>
                  <StarIcon color="success" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Total Users
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.totalUsers || 0}
                    </Typography>
                  </Box>
                  <PeopleIcon color="info" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Engagement Rate
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.engagementRate?.toFixed(1) || 0}%
                    </Typography>
                  </Box>
                  <TrendingUpIcon color="warning" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Filters */}
        <Paper sx={{ mb: 3, p: 2 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                label="Search Promotions"
                variant="outlined"
                size="small"
                fullWidth
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  label="Status"
                  onChange={(e) => setFilters({ ...filters, status: e.target.value })}
                >
                  <MenuItem value="">All Status</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="scheduled">Scheduled</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                variant="outlined"
                fullWidth
                onClick={() => setFilters({ status: '', search: '' })}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Promotions Table */}
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Promotion</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Duration</TableCell>
                <TableCell>Start Date</TableCell>
                <TableCell>End Date</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : promotions.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Typography variant="body2" color="text.secondary">
                      No promotions found
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                promotions.map((promotion) => (
                  <TableRow key={promotion.name}>
                    <TableCell>
                      <Box>
                        <Typography variant="body1" fontWeight="600">
                          {promotion.display?.title || promotion.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {promotion.display?.description || 'No description'}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      {getStatusChip(promotion)}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {promotion.durationDays} days
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatDate(promotion.startDate)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatDate(promotion.endDate)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        {promotion.isCurrentlyActive ? (
                          <Tooltip title="Deactivate">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleDeactivatePromotion(promotion.name)}
                            >
                              <StopIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        ) : (
                          <Tooltip title="Activate">
                            <IconButton
                              size="small"
                              color="success"
                              onClick={() => handleActivatePromotion(promotion.name)}
                            >
                              <PlayIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                        <Tooltip title="Edit">
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => handleEditPromotion(promotion)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="View Details">
                          <IconButton size="small" color="info">
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Create/Edit Promotion Dialog */}
        <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>
            {currentPromotion ? 'Edit Promotion' : 'Create New Promotion'}
          </DialogTitle>
          <DialogContent>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Quick Templates
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, mb: 3, flexWrap: 'wrap' }}>
                    {promotionTemplates.map((template) => (
                      <Button
                        key={template.name}
                        variant="outlined"
                        size="small"
                        onClick={() => useTemplate(template)}
                        sx={{
                          borderColor: template.color,
                          color: template.color,
                          '&:hover': {
                            backgroundColor: template.color + '20',
                            borderColor: template.color
                          }
                        }}
                      >
                        {template.title}
                      </Button>
                    ))}
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Promotion Name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    required
                    disabled={!!currentPromotion}
                    helperText={currentPromotion ? "Name cannot be changed" : "Unique identifier for the promotion"}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Duration (Days)"
                    type="number"
                    value={formData.durationDays}
                    onChange={(e) => setFormData({ ...formData, durationDays: parseInt(e.target.value) })}
                    required
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Display Settings
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Title"
                    value={formData.display.title}
                    onChange={(e) => setFormData({
                      ...formData,
                      display: { ...formData.display, title: e.target.value }
                    })}
                    required
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Banner Color"
                    type="color"
                    value={formData.display.bannerColor}
                    onChange={(e) => setFormData({
                      ...formData,
                      display: { ...formData.display, bannerColor: e.target.value }
                    })}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    multiline
                    rows={3}
                    label="Description"
                    value={formData.display.description}
                    onChange={(e) => setFormData({
                      ...formData,
                      display: { ...formData.display, description: e.target.value }
                    })}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Display Options
                  </Typography>
                  <Stack direction="row" spacing={2}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={formData.display.showCountdown}
                          onChange={(e) => setFormData({
                            ...formData,
                            display: { ...formData.display, showCountdown: e.target.checked }
                          })}
                        />
                      }
                      label="Show Countdown"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={formData.display.showOnHomepage}
                          onChange={(e) => setFormData({
                            ...formData,
                            display: { ...formData.display, showOnHomepage: e.target.checked }
                          })}
                        />
                      }
                      label="Show on Homepage"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={formData.display.showInApp}
                          onChange={(e) => setFormData({
                            ...formData,
                            display: { ...formData.display, showInApp: e.target.checked }
                          })}
                        />
                      }
                      label="Show in App"
                    />
                  </Stack>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Feature Overrides
                  </Typography>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Configure enhanced features for different user tiers during this promotion
                  </Alert>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        BASIC Tier Overrides
                      </Typography>
                      <TextField
                        fullWidth
                        label="Daily Message Limit"
                        type="number"
                        value={formData.featureOverrides.BASIC?.dailyMessageLimit || 20}
                        onChange={(e) => setFormData({
                          ...formData,
                          featureOverrides: {
                            ...formData.featureOverrides,
                            BASIC: {
                              ...formData.featureOverrides.BASIC,
                              dailyMessageLimit: parseInt(e.target.value)
                            }
                          }
                        })}
                        sx={{ mb: 2 }}
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.featureOverrides.BASIC?.canStartNewConversations || false}
                            onChange={(e) => setFormData({
                              ...formData,
                              featureOverrides: {
                                ...formData.featureOverrides,
                                BASIC: {
                                  ...formData.featureOverrides.BASIC,
                                  canStartNewConversations: e.target.checked
                                }
                              }
                            })}
                          />
                        }
                        label="Can Start New Conversations"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        VERIFIED Tier Overrides
                      </Typography>
                      <TextField
                        fullWidth
                        label="Daily Message Limit"
                        type="number"
                        value={formData.featureOverrides.VERIFIED?.dailyMessageLimit || 50}
                        onChange={(e) => setFormData({
                          ...formData,
                          featureOverrides: {
                            ...formData.featureOverrides,
                            VERIFIED: {
                              ...formData.featureOverrides.VERIFIED,
                              dailyMessageLimit: parseInt(e.target.value)
                            }
                          }
                        })}
                        sx={{ mb: 2 }}
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.featureOverrides.VERIFIED?.canSendImages || false}
                            onChange={(e) => setFormData({
                              ...formData,
                              featureOverrides: {
                                ...formData.featureOverrides,
                                VERIFIED: {
                                  ...formData.featureOverrides.VERIFIED,
                                  canSendImages: e.target.checked
                                }
                              }
                            })}
                          />
                        }
                        label="Can Send Images"
                      />
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </LocalizationProvider>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSavePromotion} variant="contained">
              {currentPromotion ? 'Update' : 'Create'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </EnhancedAdminLayout>
  );
}
