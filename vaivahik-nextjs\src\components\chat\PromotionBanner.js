import { useState, useEffect } from 'react';
import styles from '@/styles/PromotionBanner.module.css';

const PromotionBanner = ({ promotion }) => {
  const [timeLeft, setTimeLeft] = useState('');
  const [isVisible, setIsVisible] = useState(true);
  
  // Calculate time left for the promotion
  useEffect(() => {
    if (!promotion || !promotion.endDate) return;
    
    const calculateTimeLeft = () => {
      const now = new Date();
      const endDate = new Date(promotion.endDate);
      const difference = endDate - now;
      
      if (difference <= 0) {
        setTimeLeft('Expired');
        return;
      }
      
      // Calculate days, hours, minutes
      const days = Math.floor(difference / (1000 * 60 * 60 * 24));
      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      
      if (days > 0) {
        setTimeLeft(`${days} day${days !== 1 ? 's' : ''} left`);
      } else if (hours > 0) {
        setTimeLeft(`${hours} hour${hours !== 1 ? 's' : ''} left`);
      } else {
        setTimeLeft(`${minutes} minute${minutes !== 1 ? 's' : ''} left`);
      }
    };
    
    // Calculate initially
    calculateTimeLeft();
    
    // Update every minute
    const timer = setInterval(calculateTimeLeft, 60000);
    
    return () => clearInterval(timer);
  }, [promotion]);
  
  if (!promotion || !isVisible) return null;
  
  return (
    <div 
      className={styles.promotionBanner}
      style={{ 
        backgroundColor: promotion.bannerColor || '#FF5722',
        color: promotion.bannerTextColor || '#FFFFFF'
      }}
    >
      <div className={styles.promotionContent}>
        <h3 className={styles.promotionTitle}>{promotion.title}</h3>
        <p className={styles.promotionDescription}>{promotion.description}</p>
        {promotion.showCountdown && timeLeft && (
          <div className={styles.promotionCountdown}>{timeLeft}</div>
        )}
      </div>
      <button 
        className={styles.closeButton}
        onClick={() => setIsVisible(false)}
        aria-label="Close promotion banner"
      >
        ×
      </button>
    </div>
  );
};

export default PromotionBanner;
