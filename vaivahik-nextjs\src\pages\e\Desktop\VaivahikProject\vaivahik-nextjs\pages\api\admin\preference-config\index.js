// API endpoint for preference configuration
import { PrismaClient } from '@prisma/client';
import { withAuth } from '@/utils/authHandler';
import { handleApiError, handleDatabaseError } from '@/utils/errorHandler';

// Initialize Prisma client
const prisma = new PrismaClient();

// Fallback mock data for preference categories (used if database operations fail)
const mockCategories = [
  {
    id: 'cat1',
    name: 'physical_attributes',
    displayName: 'Physical Attributes',
    description: 'Physical characteristics preferences',
    displayOrder: 1,
    icon: 'person',
    isActive: true,
    isRequired: true
  },
  {
    id: 'cat2',
    name: 'education_career',
    displayName: 'Education & Career',
    description: 'Education and career preferences',
    displayOrder: 2,
    icon: 'school',
    isActive: true,
    isRequired: false
  },
  {
    id: 'cat3',
    name: 'lifestyle',
    displayName: 'Lifestyle',
    description: 'Lifestyle and habits preferences',
    displayOrder: 3,
    icon: 'restaurant',
    isActive: true,
    isRequired: false
  },
  {
    id: 'cat4',
    name: 'community',
    displayName: 'Community',
    description: 'Community and cultural preferences',
    displayOrder: 4,
    icon: 'groups',
    isActive: true,
    isRequired: true
  }
];

// Mock data for preference fields
const mockFields = [
  {
    id: 'field1',
    name: 'age_range',
    displayName: 'Age Range',
    description: 'Preferred age range of partner',
    fieldType: 'RANGE',
    displayOrder: 1,
    isActive: true,
    isRequired: true,
    isSearchable: true,
    isMatchCriteria: true,
    defaultValue: '{"min": 21, "max": 35}',
    validationRules: '{"minValue": 18, "maxValue": 70}',
    minValue: 18,
    maxValue: 70,
    stepValue: 1,
    categoryId: 'cat1'
  },
  {
    id: 'field2',
    name: 'height_range',
    displayName: 'Height Range',
    description: 'Preferred height range of partner',
    fieldType: 'RANGE',
    displayOrder: 2,
    isActive: true,
    isRequired: true,
    isSearchable: true,
    isMatchCriteria: true,
    defaultValue: '{"min": "5\\\'0\\\"", "max": "6\\\'0\\\""}',
    validationRules: '{"minValue": "4\\\'5\\\"", "maxValue": "6\\\'6\\\""}',
    minValue: 4.5,
    maxValue: 6.5,
    stepValue: 0.1,
    categoryId: 'cat1'
  },
  {
    id: 'field3',
    name: 'education_level',
    displayName: 'Education Level',
    description: 'Preferred education level of partner',
    fieldType: 'MULTI_SELECT',
    displayOrder: 1,
    isActive: true,
    isRequired: false,
    isSearchable: true,
    isMatchCriteria: true,
    defaultValue: '[]',
    categoryId: 'cat2'
  },
  {
    id: 'field4',
    name: 'diet_preference',
    displayName: 'Diet Preference',
    description: 'Preferred diet of partner',
    fieldType: 'SELECT',
    displayOrder: 1,
    isActive: true,
    isRequired: false,
    isSearchable: true,
    isMatchCriteria: true,
    defaultValue: '"DOESNT_MATTER"',
    categoryId: 'cat3'
  },
  {
    id: 'field5',
    name: 'accept_sub_castes',
    displayName: 'Accept Sub-castes',
    description: 'Sub-castes willing to accept',
    fieldType: 'MULTI_SELECT',
    displayOrder: 1,
    isActive: true,
    isRequired: true,
    isSearchable: true,
    isMatchCriteria: true,
    defaultValue: '[]',
    categoryId: 'cat4'
  },
  {
    id: 'field6',
    name: 'gotra_preference',
    displayName: 'Gotra Preference',
    description: 'Preferred gotra of partner',
    fieldType: 'SELECT',
    displayOrder: 2,
    isActive: true,
    isRequired: false,
    isSearchable: true,
    isMatchCriteria: true,
    defaultValue: '"DOESNT_MATTER"',
    categoryId: 'cat4'
  }
];

// Mock data for field options
const mockOptions = [
  {
    id: 'opt1',
    value: 'GRADUATE',
    displayText: 'Graduate',
    description: 'Bachelor\'s degree',
    displayOrder: 1,
    isActive: true,
    fieldId: 'field3'
  },
  {
    id: 'opt2',
    value: 'POST_GRADUATE',
    displayText: 'Post Graduate',
    description: 'Master\'s degree',
    displayOrder: 2,
    isActive: true,
    fieldId: 'field3'
  },
  {
    id: 'opt3',
    value: 'DOCTORATE',
    displayText: 'Doctorate',
    description: 'PhD or equivalent',
    displayOrder: 3,
    isActive: true,
    fieldId: 'field3'
  },
  {
    id: 'opt4',
    value: 'VEG',
    displayText: 'Vegetarian',
    description: 'Vegetarian diet',
    displayOrder: 1,
    isActive: true,
    fieldId: 'field4'
  },
  {
    id: 'opt5',
    value: 'NON_VEG',
    displayText: 'Non-Vegetarian',
    description: 'Non-vegetarian diet',
    displayOrder: 2,
    isActive: true,
    fieldId: 'field4'
  },
  {
    id: 'opt6',
    value: 'DOESNT_MATTER',
    displayText: 'Doesn\'t Matter',
    description: 'No preference',
    displayOrder: 3,
    isActive: true,
    fieldId: 'field4'
  },
  {
    id: 'opt7',
    value: 'KUNBI',
    displayText: 'Kunbi',
    description: 'Kunbi sub-caste',
    displayOrder: 1,
    isActive: true,
    fieldId: 'field5'
  },
  {
    id: 'opt8',
    value: '96_KULI_MARATHA',
    displayText: '96 Kuli Maratha',
    description: '96 Kuli Maratha sub-caste',
    displayOrder: 2,
    isActive: true,
    fieldId: 'field5'
  }
];

// Mock data for importance settings
const mockImportanceSettings = [
  {
    id: 'imp1',
    importanceLevel: 8.0,
    description: 'Age is highly important for males',
    isActive: true,
    fieldId: 'field1',
    gender: 'MALE'
  },
  {
    id: 'imp2',
    importanceLevel: 7.0,
    description: 'Age is important for females',
    isActive: true,
    fieldId: 'field1',
    gender: 'FEMALE'
  },
  {
    id: 'imp3',
    importanceLevel: 6.0,
    description: 'Height is moderately important for males',
    isActive: true,
    fieldId: 'field2',
    gender: 'MALE'
  },
  {
    id: 'imp4',
    importanceLevel: 8.0,
    description: 'Height is highly important for females',
    isActive: true,
    fieldId: 'field2',
    gender: 'FEMALE'
  },
  {
    id: 'imp5',
    importanceLevel: 9.0,
    description: 'Caste is very important for all',
    isActive: true,
    fieldId: 'field5',
    gender: 'ALL'
  }
];

// Mock data for default preferences
const mockDefaultPreferences = {
  ageMin: 21,
  ageMax: 35,
  heightMin: "5'0\"",
  heightMax: "6'0\"",
  educationLevel: ["GRADUATE", "POST_GRADUATE"],
  dietPreference: "DOESNT_MATTER",
  acceptSubCastes: ["KUNBI", "96_KULI_MARATHA"],
  gotraPreference: "DOESNT_MATTER"
};

// Main handler function
async function handler(req, res) {
  // Handle different HTTP methods
  try {
    switch (req.method) {
      case 'GET':
        return await getPreferenceConfig(req, res);
      case 'PUT':
        return await updatePreferenceConfig(req, res);
      case 'DELETE':
        return await deletePreferenceItem(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    return handleApiError(error, res, 'Preference configuration API');
  }
}

// Export the handler with authentication middleware
export default withAuth(handler, 'ADMIN');

// DELETE /api/admin/preference-config
async function deletePreferenceItem(req, res) {
  try {
    const { type, id } = req.query;

    // Validate the request
    if (!type || !id) {
      return res.status(400).json({ success: false, message: 'Type and ID are required' });
    }

    // Delete item from database based on the type
    if (type === 'category') {
      try {
        // Delete the category and all its related fields (cascade delete will handle this)
        await prisma.preferenceCategory.delete({
          where: { id }
        });

        return res.status(200).json({
          success: true,
          message: 'Category deleted successfully'
        });
      } catch (dbError) {
        return handleDatabaseError(dbError, res);
      }
    } else if (type === 'field') {
      try {
        // Delete the field and all its related options and importance settings (cascade delete will handle this)
        await prisma.preferenceField.delete({
          where: { id }
        });

        return res.status(200).json({
          success: true,
          message: 'Field deleted successfully'
        });
      } catch (dbError) {
        return handleDatabaseError(dbError, res);
      }
    } else if (type === 'option') {
      try {
        // Delete the option
        await prisma.preferenceOption.delete({
          where: { id }
        });

        return res.status(200).json({
          success: true,
          message: 'Option deleted successfully'
        });
      } catch (dbError) {
        return handleDatabaseError(dbError, res);
      }
    } else if (type === 'importance') {
      try {
        // Delete the importance setting
        await prisma.preferenceImportance.delete({
          where: { id }
        });

        return res.status(200).json({
          success: true,
          message: 'Importance setting deleted successfully'
        });
      } catch (dbError) {
        return handleDatabaseError(dbError, res);
      }
    } else {
      return res.status(400).json({
        success: false,
        message: `Invalid type: ${type}`
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Delete preference item');
  }
}

// GET /api/admin/preference-config
async function getPreferenceConfig(req, res) {
  try {
    // Check what data is requested
    const { type } = req.query;

    let responseData = {
      success: true
    };

    // Fetch data from database based on requested type
    if (!type || type === 'all') {
      // Fetch all data
      try {
        // Get categories
        const categories = await prisma.preferenceCategory.findMany({
          orderBy: { displayOrder: 'asc' }
        });
        responseData.categories = categories;

        // Get fields
        const fields = await prisma.preferenceField.findMany({
          orderBy: [
            { categoryId: 'asc' },
            { displayOrder: 'asc' }
          ]
        });
        responseData.fields = fields;

        // Get options
        const options = await prisma.preferenceOption.findMany({
          orderBy: [
            { fieldId: 'asc' },
            { displayOrder: 'asc' }
          ]
        });
        responseData.options = options;

        // Get importance settings
        const importanceSettings = await prisma.preferenceImportance.findMany();
        responseData.importanceSettings = importanceSettings;

        // Get default preferences from SystemConfig
        const defaultPrefsConfig = await prisma.systemConfig.findFirst({
          where: {
            configKey: 'default_preferences',
            isActive: true
          }
        });

        if (defaultPrefsConfig) {
          responseData.defaultPreferences = JSON.parse(defaultPrefsConfig.configValue);
        } else {
          // If no default preferences are found in the database, use mock data
          responseData.defaultPreferences = mockDefaultPreferences;
        }
      } catch (dbError) {
        // Log the error with detailed information
        handleDatabaseError(dbError, res);

        // Fallback to mock data if database operations fail
        responseData.categories = mockCategories;
        responseData.fields = mockFields;
        responseData.options = mockOptions;
        responseData.importanceSettings = mockImportanceSettings;
        responseData.defaultPreferences = mockDefaultPreferences;
      }
    } else if (type === 'categories') {
      // Get only categories
      try {
        const categories = await prisma.preferenceCategory.findMany({
          orderBy: { displayOrder: 'asc' }
        });
        responseData.categories = categories;
      } catch (dbError) {
        handleDatabaseError(dbError, res);
        responseData.categories = mockCategories;
      }
    } else if (type === 'fields') {
      // Get only fields
      try {
        const fields = await prisma.preferenceField.findMany({
          orderBy: [
            { categoryId: 'asc' },
            { displayOrder: 'asc' }
          ]
        });
        responseData.fields = fields;
      } catch (dbError) {
        handleDatabaseError(dbError, res);
        responseData.fields = mockFields;
      }
    } else if (type === 'options') {
      // Get only options
      try {
        const options = await prisma.preferenceOption.findMany({
          orderBy: [
            { fieldId: 'asc' },
            { displayOrder: 'asc' }
          ]
        });
        responseData.options = options;
      } catch (dbError) {
        handleDatabaseError(dbError, res);
        responseData.options = mockOptions;
      }
    } else if (type === 'importance') {
      // Get only importance settings
      try {
        const importanceSettings = await prisma.preferenceImportance.findMany();
        responseData.importanceSettings = importanceSettings;
      } catch (dbError) {
        handleDatabaseError(dbError, res);
        responseData.importanceSettings = mockImportanceSettings;
      }
    } else if (type === 'defaults') {
      // Get only default preferences
      try {
        const defaultPrefsConfig = await prisma.systemConfig.findFirst({
          where: {
            configKey: 'default_preferences',
            isActive: true
          }
        });

        if (defaultPrefsConfig) {
          responseData.defaultPreferences = JSON.parse(defaultPrefsConfig.configValue);
        } else {
          responseData.defaultPreferences = mockDefaultPreferences;
        }
      } catch (dbError) {
        handleDatabaseError(dbError, res);
        responseData.defaultPreferences = mockDefaultPreferences;
      }
    }

    return res.status(200).json(responseData);
  } catch (error) {
    return handleApiError(error, res, 'Get preference configuration');
  }
}

// PUT /api/admin/preference-config
async function updatePreferenceConfig(req, res) {
  try {
    const { type, data } = req.body;

    // Validate the request
    if (!type || !data) {
      return res.status(400).json({ success: false, message: 'Type and data are required' });
    }

    // Update database based on the type
    if (type === 'categories') {
      // Update categories
      try {
        // Process each category
        for (const category of data) {
          if (category.id && category.id.startsWith('cat')) {
            // This is a new category with a temporary ID, create it
            const { id, ...categoryData } = category;
            await prisma.preferenceCategory.create({
              data: categoryData
            });
          } else if (category.id) {
            // This is an existing category, update it
            const { id, ...categoryData } = category;
            await prisma.preferenceCategory.update({
              where: { id },
              data: categoryData
            });
          }
        }

        // Get the updated list of categories from the database
        const updatedCategories = await prisma.preferenceCategory.findMany({
          orderBy: { displayOrder: 'asc' }
        });

        return res.status(200).json({
          success: true,
          message: 'Categories updated successfully',
          data: updatedCategories
        });
      } catch (dbError) {
        return handleDatabaseError(dbError, res);
      }
    } else if (type === 'fields') {
      // Update fields
      try {
        // Process each field
        for (const field of data) {
          if (field.id && field.id.startsWith('field')) {
            // This is a new field with a temporary ID, create it
            const { id, ...fieldData } = field;
            await prisma.preferenceField.create({
              data: fieldData
            });
          } else if (field.id) {
            // This is an existing field, update it
            const { id, ...fieldData } = field;
            await prisma.preferenceField.update({
              where: { id },
              data: fieldData
            });
          }
        }

        // Get the updated list of fields from the database
        const updatedFields = await prisma.preferenceField.findMany({
          orderBy: [
            { categoryId: 'asc' },
            { displayOrder: 'asc' }
          ]
        });

        return res.status(200).json({
          success: true,
          message: 'Fields updated successfully',
          data: updatedFields
        });
      } catch (dbError) {
        return handleDatabaseError(dbError, res);
      }
    } else if (type === 'options') {
      // Update options
      try {
        // Process each option
        for (const option of data) {
          if (option.id && option.id.startsWith('opt')) {
            // This is a new option with a temporary ID, create it
            const { id, ...optionData } = option;
            await prisma.preferenceOption.create({
              data: optionData
            });
          } else if (option.id) {
            // This is an existing option, update it
            const { id, ...optionData } = option;
            await prisma.preferenceOption.update({
              where: { id },
              data: optionData
            });
          }
        }

        // Get the updated list of options from the database
        const updatedOptions = await prisma.preferenceOption.findMany({
          orderBy: [
            { fieldId: 'asc' },
            { displayOrder: 'asc' }
          ]
        });

        return res.status(200).json({
          success: true,
          message: 'Options updated successfully',
          data: updatedOptions
        });
      } catch (dbError) {
        return handleDatabaseError(dbError, res);
      }
    } else if (type === 'importance') {
      // Update importance settings
      try {
        // Process each importance setting
        for (const importance of data) {
          if (importance.id && importance.id.startsWith('imp')) {
            // This is a new importance setting with a temporary ID, create it
            const { id, ...importanceData } = importance;
            await prisma.preferenceImportance.create({
              data: importanceData
            });
          } else if (importance.id) {
            // This is an existing importance setting, update it
            const { id, ...importanceData } = importance;
            await prisma.preferenceImportance.update({
              where: { id },
              data: importanceData
            });
          }
        }

        // Get the updated list of importance settings from the database
        const updatedImportanceSettings = await prisma.preferenceImportance.findMany();

        return res.status(200).json({
          success: true,
          message: 'Importance settings updated successfully',
          data: updatedImportanceSettings
        });
      } catch (dbError) {
        return handleDatabaseError(dbError, res);
      }
    } else if (type === 'defaults') {
      // Update default preferences
      try {
        // Check if default preferences already exist
        const existingDefaults = await prisma.systemConfig.findFirst({
          where: { configKey: 'default_preferences' }
        });

        if (existingDefaults) {
          // Update existing default preferences
          await prisma.systemConfig.update({
            where: { id: existingDefaults.id },
            data: {
              configValue: JSON.stringify(data),
              updatedAt: new Date()
            }
          });
        } else {
          // Create new default preferences
          await prisma.systemConfig.create({
            data: {
              configKey: 'default_preferences',
              configValue: JSON.stringify(data),
              description: 'Default preference values for new users',
              isActive: true
            }
          });
        }

        return res.status(200).json({
          success: true,
          message: 'Default preferences updated successfully',
          data: data
        });
      } catch (dbError) {
        return handleDatabaseError(dbError, res);
      }
    } else {
      return res.status(400).json({
        success: false,
        message: `Invalid type: ${type}`
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Update preference configuration');
  }
}
