/**
 * User Controller
 * 
 * This controller handles all user-related operations.
 * It follows a consistent pattern for error handling and response formatting.
 */

const bcrypt = require('bcryptjs');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const apiResponse = require('../utils/apiResponse');
const logger = require('../utils/logger');
const { uploadToStorage, deleteFromStorage } = require('../utils/storage');

/**
 * Get all users with pagination and filtering
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 */
const getAllUsers = async (req, res, next) => {
  try {
    // Extract query parameters with defaults
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const sortBy = req.query.sortBy || 'createdAt';
    const sortOrder = req.query.sortOrder || 'desc';
    
    // Calculate skip value for pagination
    const skip = (page - 1) * limit;
    
    // Build filter conditions
    const where = search
      ? {
          OR: [
            { email: { contains: search, mode: 'insensitive' } },
            { fullName: { contains: search, mode: 'insensitive' } },
            { phone: { contains: search } }
          ]
        }
      : {};
    
    // Build sort options
    const orderBy = { [sortBy]: sortOrder };
    
    // Query database for users with pagination
    const [users, totalUsers] = await Promise.all([
      prisma.user.findMany({
        where,
        orderBy,
        skip,
        take: limit,
        select: {
          id: true,
          email: true,
          fullName: true,
          phone: true,
          gender: true,
          dateOfBirth: true,
          role: true,
          isVerified: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          // Exclude sensitive fields like password
          // Include related data as needed
          profile: {
            select: {
              occupation: true,
              education: true,
              bio: true,
              profilePhotoUrl: true
            }
          }
        }
      }),
      prisma.user.count({ where })
    ]);
    
    // Calculate pagination metadata
    const totalPages = Math.ceil(totalUsers / limit);
    
    // Return paginated response
    return apiResponse.paginated(
      res,
      'Users retrieved successfully',
      users,
      {
        page,
        limit,
        totalItems: totalUsers,
        totalPages
      }
    );
  } catch (error) {
    logger.error('Error in getAllUsers', { error: error.message });
    return next(error);
  }
};

/**
 * Get user by ID
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 */
const getUserById = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // Check if the user has permission to access this user
    // Regular users can only access their own data
    if (req.user.role !== 'ADMIN' && req.user.id !== id) {
      return apiResponse.forbidden(res, 'You do not have permission to access this user');
    }
    
    // Query database for user
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        fullName: true,
        phone: true,
        gender: true,
        dateOfBirth: true,
        role: true,
        isVerified: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        // Include related data
        profile: true,
        address: true
      }
    });
    
    // Check if user exists
    if (!user) {
      return apiResponse.notFound(res, 'User not found');
    }
    
    // Return success response
    return apiResponse.success(res, 'User retrieved successfully', user);
  } catch (error) {
    logger.error('Error in getUserById', { error: error.message, userId: req.params.id });
    return next(error);
  }
};

/**
 * Create a new user (admin only)
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 */
const createUser = async (req, res, next) => {
  try {
    // Check if user has admin role
    if (req.user.role !== 'ADMIN') {
      return apiResponse.forbidden(res, 'Only administrators can create users');
    }
    
    const { email, password, fullName, phone, role = 'USER' } = req.body;
    
    // Check if email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });
    
    if (existingUser) {
      return apiResponse.conflict(res, 'Email already in use');
    }
    
    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);
    
    // Create user in database
    const newUser = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        fullName,
        phone,
        role,
        profile: {
          create: {} // Create empty profile
        }
      },
      select: {
        id: true,
        email: true,
        fullName: true,
        phone: true,
        role: true,
        createdAt: true
      }
    });
    
    // Return created response
    return apiResponse.created(res, 'User created successfully', newUser);
  } catch (error) {
    logger.error('Error in createUser', { error: error.message });
    return next(error);
  }
};

/**
 * Update user by ID
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 */
const updateUser = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { email, fullName, phone, gender, dateOfBirth, address } = req.body;
    
    // Check if the user has permission to update this user
    // Regular users can only update their own data
    if (req.user.role !== 'ADMIN' && req.user.id !== id) {
      return apiResponse.forbidden(res, 'You do not have permission to update this user');
    }
    
    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id }
    });
    
    if (!existingUser) {
      return apiResponse.notFound(res, 'User not found');
    }
    
    // Check if email is being changed and already exists
    if (email && email !== existingUser.email) {
      const emailExists = await prisma.user.findUnique({
        where: { email }
      });
      
      if (emailExists) {
        return apiResponse.conflict(res, 'Email already in use');
      }
    }
    
    // Prepare update data
    const updateData = {};
    
    // Only include fields that are provided
    if (email) updateData.email = email;
    if (fullName) updateData.fullName = fullName;
    if (phone) updateData.phone = phone;
    if (gender) updateData.gender = gender;
    if (dateOfBirth) updateData.dateOfBirth = new Date(dateOfBirth);
    
    // Update user in database
    const updatedUser = await prisma.user.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        email: true,
        fullName: true,
        phone: true,
        gender: true,
        dateOfBirth: true,
        role: true,
        isVerified: true,
        isActive: true,
        updatedAt: true
      }
    });
    
    // Update address if provided
    if (address) {
      // Check if address exists
      const existingAddress = await prisma.address.findFirst({
        where: { userId: id }
      });
      
      if (existingAddress) {
        // Update existing address
        await prisma.address.update({
          where: { id: existingAddress.id },
          data: address
        });
      } else {
        // Create new address
        await prisma.address.create({
          data: {
            ...address,
            user: { connect: { id } }
          }
        });
      }
    }
    
    // Return success response
    return apiResponse.success(res, 'User updated successfully', updatedUser);
  } catch (error) {
    logger.error('Error in updateUser', { error: error.message, userId: req.params.id });
    return next(error);
  }
};

/**
 * Delete user by ID
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 */
const deleteUser = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // Only admins can delete users
    if (req.user.role !== 'ADMIN') {
      return apiResponse.forbidden(res, 'Only administrators can delete users');
    }
    
    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id }
    });
    
    if (!existingUser) {
      return apiResponse.notFound(res, 'User not found');
    }
    
    // Delete user from database
    await prisma.user.delete({
      where: { id }
    });
    
    // Return success response
    return apiResponse.success(res, 'User deleted successfully');
  } catch (error) {
    logger.error('Error in deleteUser', { error: error.message, userId: req.params.id });
    return next(error);
  }
};

/**
 * Upload profile photo
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 */
const uploadProfilePhoto = async (req, res, next) => {
  try {
    // Check if file was uploaded
    if (!req.file) {
      return apiResponse.badRequest(res, 'No file uploaded');
    }
    
    const userId = req.user.id;
    const file = req.file;
    
    // Upload file to storage
    const uploadResult = await uploadToStorage(file, `profile-photos/${userId}`);
    
    // Get current profile photo URL
    const currentUser = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        profile: {
          select: {
            profilePhotoUrl: true
          }
        }
      }
    });
    
    // Delete old profile photo if exists
    if (currentUser?.profile?.profilePhotoUrl) {
      await deleteFromStorage(currentUser.profile.profilePhotoUrl);
    }
    
    // Update profile photo URL in database
    await prisma.profile.upsert({
      where: { userId },
      update: {
        profilePhotoUrl: uploadResult.url
      },
      create: {
        userId,
        profilePhotoUrl: uploadResult.url
      }
    });
    
    // Return success response
    return apiResponse.success(res, 'Profile photo uploaded successfully', {
      url: uploadResult.url
    });
  } catch (error) {
    logger.error('Error in uploadProfilePhoto', { error: error.message, userId: req.user.id });
    return next(error);
  }
};

/**
 * Get current user profile
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 */
const getCurrentUserProfile = async (req, res, next) => {
  try {
    const userId = req.user.id;
    
    // Query database for user with related data
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        profile: true,
        address: true,
        preferences: true,
        familyDetails: true
      }
    });
    
    // Check if user exists
    if (!user) {
      return apiResponse.notFound(res, 'User not found');
    }
    
    // Remove sensitive data
    delete user.password;
    
    // Return success response
    return apiResponse.success(res, 'User profile retrieved successfully', user);
  } catch (error) {
    logger.error('Error in getCurrentUserProfile', { error: error.message, userId: req.user.id });
    return next(error);
  }
};

/**
 * Update current user profile
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 */
const updateCurrentUserProfile = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const profileData = req.body;
    
    // Separate user data and profile data
    const userData = {};
    const profileFields = {};
    
    // User fields
    if (profileData.fullName) userData.fullName = profileData.fullName;
    if (profileData.phone) userData.phone = profileData.phone;
    if (profileData.gender) userData.gender = profileData.gender;
    if (profileData.dateOfBirth) userData.dateOfBirth = new Date(profileData.dateOfBirth);
    
    // Profile fields
    if (profileData.bio) profileFields.bio = profileData.bio;
    if (profileData.occupation) profileFields.occupation = profileData.occupation;
    if (profileData.education) profileFields.education = profileData.education;
    if (profileData.height) profileFields.height = parseFloat(profileData.height);
    if (profileData.religion) profileFields.religion = profileData.religion;
    if (profileData.motherTongue) profileFields.motherTongue = profileData.motherTongue;
    if (profileData.maritalStatus) profileFields.maritalStatus = profileData.maritalStatus;
    
    // Update user data if needed
    if (Object.keys(userData).length > 0) {
      await prisma.user.update({
        where: { id: userId },
        data: userData
      });
    }
    
    // Update profile data if needed
    if (Object.keys(profileFields).length > 0) {
      await prisma.profile.upsert({
        where: { userId },
        update: profileFields,
        create: {
          ...profileFields,
          userId
        }
      });
    }
    
    // Get updated user data
    const updatedUser = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        profile: true
      }
    });
    
    // Remove sensitive data
    delete updatedUser.password;
    
    // Return success response
    return apiResponse.success(res, 'Profile updated successfully', updatedUser);
  } catch (error) {
    logger.error('Error in updateCurrentUserProfile', { error: error.message, userId: req.user.id });
    return next(error);
  }
};

/**
 * Change user password
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 */
const changePassword = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { currentPassword, newPassword } = req.body;
    
    // Get user with password
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        password: true
      }
    });
    
    // Check if user exists
    if (!user) {
      return apiResponse.notFound(res, 'User not found');
    }
    
    // Verify current password
    const isPasswordValid = await bcrypt.compare(currentPassword, user.password);
    
    if (!isPasswordValid) {
      return apiResponse.badRequest(res, 'Current password is incorrect');
    }
    
    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);
    
    // Update password in database
    await prisma.user.update({
      where: { id: userId },
      data: {
        password: hashedPassword
      }
    });
    
    // Return success response
    return apiResponse.success(res, 'Password changed successfully');
  } catch (error) {
    logger.error('Error in changePassword', { error: error.message, userId: req.user.id });
    return next(error);
  }
};

module.exports = {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  uploadProfilePhoto,
  getCurrentUserProfile,
  updateCurrentUserProfile,
  changePassword
};
