/* Admin Panel Layout Fixes */

/* Ensure the admin container is properly displayed */
.admin-container {
  display: flex !important;
  min-height: 100vh !important;
  width: 100% !important;
}

/* Ensure the sidebar is properly displayed */
.sidebar {
  display: block !important;
  position: fixed !important;
  height: 100vh !important;
  z-index: 1000 !important;
}

/* Ensure the main content is properly displayed */
.main-content {
  display: block !important;
  margin-left: 260px !important;
  width: calc(100% - 260px) !important;
  min-height: 100vh !important;
}

.main-content.collapsed {
  margin-left: 70px !important;
  width: calc(100% - 70px) !important;
}

/* Ensure the topbar is properly displayed */
.topbar {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

/* Ensure the content header is properly displayed */
.content-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
}

/* Ensure the card is properly displayed */
.card {
  display: block !important;
  width: 100% !important;
}

/* Ensure the card header is properly displayed */
.card-header {
  display: block !important;
}

/* Ensure the card body is properly displayed */
.card-body {
  display: block !important;
}

/* Ensure the table is properly displayed */
.table-container {
  display: block !important;
  width: 100% !important;
  overflow-x: auto !important;
}

.data-table {
  display: table !important;
  width: 100% !important;
}

/* Ensure the modal is properly displayed */
.modal.show {
  display: flex !important;
  z-index: 2000 !important;
}

/* Fix for tab navigation */
.tab-container {
  display: block !important;
  width: 100% !important;
}

.tab-nav {
  display: flex !important;
}

.tab-nav-item {
  display: block !important;
}

/* Fix for action buttons */
.action-buttons {
  display: flex !important;
  gap: 10px !important;
}

.action-btn {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Fix for form elements */
.form-group {
  display: block !important;
  margin-bottom: 20px !important;
}

.form-group label {
  display: block !important;
  margin-bottom: 8px !important;
}

/* Fix for buttons */
.btn {
  display: inline-flex !important;
  align-items: center !important;
}

/* Fix for loading spinner */
.loading-container {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
}

.loading-spinner {
  display: block !important;
}

/* Fix for responsive layout */
@media (max-width: 768px) {
  .main-content {
    margin-left: 70px !important;
    width: calc(100% - 70px) !important;
  }
  
  .content-header {
    flex-direction: column !important;
    align-items: flex-start !important;
  }
}
