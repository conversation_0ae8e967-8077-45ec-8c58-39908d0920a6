/**
 * Similarity Search Utility
 * 
 * This utility provides functions for finding similar profiles based on various criteria.
 * It calculates similarity scores between profiles and returns the most similar profiles.
 */

const logger = require('./logger');

/**
 * Calculate similarity score between two profiles
 * @param {Object} sourceProfile - Source profile
 * @param {Object} targetProfile - Target profile to compare
 * @param {Object} weights - Weights for different attributes
 * @returns {number} Similarity score (0-100)
 */
const calculateSimilarityScore = (sourceProfile, targetProfile, weights = {}) => {
    try {
        // Default weights if not provided
        const defaultWeights = {
            age: 15,
            education: 20,
            occupation: 15,
            religion: 10,
            caste: 10,
            location: 10,
            height: 5,
            maritalStatus: 5,
            diet: 5,
            interests: 5
        };
        
        // Merge provided weights with defaults
        const finalWeights = { ...defaultWeights, ...weights };
        
        // Initialize score
        let totalScore = 0;
        let totalWeight = 0;
        
        // Age similarity (within 5 years = 100%, decreases by 20% for each additional 5 years)
        if (sourceProfile.age && targetProfile.age) {
            const ageDiff = Math.abs(sourceProfile.age - targetProfile.age);
            const ageScore = Math.max(0, 100 - (Math.floor(ageDiff / 5) * 20));
            totalScore += ageScore * finalWeights.age;
            totalWeight += finalWeights.age;
        }
        
        // Education similarity (exact match = 100%, similar level = 50%, different = 0%)
        if (sourceProfile.education && targetProfile.education) {
            let educationScore = 0;
            if (sourceProfile.education === targetProfile.education) {
                educationScore = 100;
            } else {
                // Group education levels
                const educationGroups = {
                    basic: ['HIGH_SCHOOL', 'DIPLOMA'],
                    undergraduate: ['BACHELORS'],
                    graduate: ['MASTERS', 'DOCTORATE', 'PROFESSIONAL_DEGREE']
                };
                
                // Find groups for both profiles
                let sourceGroup = null;
                let targetGroup = null;
                
                for (const [group, levels] of Object.entries(educationGroups)) {
                    if (levels.includes(sourceProfile.education)) sourceGroup = group;
                    if (levels.includes(targetProfile.education)) targetGroup = group;
                }
                
                // If in same group, 50% similarity
                if (sourceGroup && targetGroup && sourceGroup === targetGroup) {
                    educationScore = 50;
                }
            }
            
            totalScore += educationScore * finalWeights.education;
            totalWeight += finalWeights.education;
        }
        
        // Occupation similarity (exact match = 100%, otherwise 0%)
        if (sourceProfile.occupation && targetProfile.occupation) {
            const occupationScore = sourceProfile.occupation === targetProfile.occupation ? 100 : 0;
            totalScore += occupationScore * finalWeights.occupation;
            totalWeight += finalWeights.occupation;
        }
        
        // Religion similarity (exact match = 100%, otherwise 0%)
        if (sourceProfile.religion && targetProfile.religion) {
            const religionScore = sourceProfile.religion === targetProfile.religion ? 100 : 0;
            totalScore += religionScore * finalWeights.religion;
            totalWeight += finalWeights.religion;
        }
        
        // Caste similarity (exact match = 100%, otherwise 0%)
        if (sourceProfile.caste && targetProfile.caste) {
            const casteScore = sourceProfile.caste === targetProfile.caste ? 100 : 0;
            totalScore += casteScore * finalWeights.caste;
            totalWeight += finalWeights.caste;
        }
        
        // Location similarity (exact match = 100%, same state = 50%, otherwise 0%)
        if (sourceProfile.city && targetProfile.city) {
            let locationScore = 0;
            if (sourceProfile.city === targetProfile.city) {
                locationScore = 100;
            } else if (sourceProfile.state && targetProfile.state && 
                       sourceProfile.state === targetProfile.state) {
                locationScore = 50;
            }
            
            totalScore += locationScore * finalWeights.location;
            totalWeight += finalWeights.location;
        }
        
        // Height similarity (within 2 inches = 100%, decreases by 25% for each additional 2 inches)
        if (sourceProfile.height && targetProfile.height) {
            const heightDiff = Math.abs(sourceProfile.height - targetProfile.height);
            const heightScore = Math.max(0, 100 - (Math.floor(heightDiff / 0.17) * 25)); // 0.17 feet ≈ 2 inches
            totalScore += heightScore * finalWeights.height;
            totalWeight += finalWeights.height;
        }
        
        // Marital status similarity (exact match = 100%, otherwise 0%)
        if (sourceProfile.maritalStatus && targetProfile.maritalStatus) {
            const maritalScore = sourceProfile.maritalStatus === targetProfile.maritalStatus ? 100 : 0;
            totalScore += maritalScore * finalWeights.maritalStatus;
            totalWeight += finalWeights.maritalStatus;
        }
        
        // Diet similarity (exact match = 100%, otherwise 0%)
        if (sourceProfile.diet && targetProfile.diet) {
            const dietScore = sourceProfile.diet === targetProfile.diet ? 100 : 0;
            totalScore += dietScore * finalWeights.diet;
            totalWeight += finalWeights.diet;
        }
        
        // Calculate final weighted score
        return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
    } catch (error) {
        logger.error(`Error calculating similarity score: ${error}`);
        return 0;
    }
};

/**
 * Find similar profiles based on a source profile
 * @param {Object} sourceProfile - Source profile
 * @param {Array} candidateProfiles - Array of candidate profiles
 * @param {Object} options - Options for similarity search
 * @returns {Array} Array of profiles with similarity scores
 */
const findSimilarProfiles = (sourceProfile, candidateProfiles, options = {}) => {
    try {
        const {
            minScore = 0,
            maxResults = 20,
            weights = {}
        } = options;
        
        // Calculate similarity scores for all candidate profiles
        const scoredProfiles = candidateProfiles.map(profile => {
            const similarityScore = calculateSimilarityScore(sourceProfile, profile, weights);
            return {
                ...profile,
                similarityScore
            };
        });
        
        // Filter by minimum score
        const filteredProfiles = scoredProfiles.filter(profile => 
            profile.similarityScore >= minScore
        );
        
        // Sort by similarity score (descending)
        const sortedProfiles = filteredProfiles.sort((a, b) => 
            b.similarityScore - a.similarityScore
        );
        
        // Limit results
        return sortedProfiles.slice(0, maxResults);
    } catch (error) {
        logger.error(`Error finding similar profiles: ${error}`);
        return [];
    }
};

module.exports = {
    calculateSimilarityScore,
    findSimilarProfiles
};
