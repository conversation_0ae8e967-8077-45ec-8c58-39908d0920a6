{"success": true, "activities": [{"id": 1, "type": "registration", "user": "<PERSON><PERSON>", "userId": 1, "details": "Registered as a new user", "time": "2 hours ago", "icon": "👤", "actionable": true, "actionText": "View Profile"}, {"id": 2, "type": "subscription", "user": "<PERSON><PERSON>", "userId": 2, "details": "Purchased Gold plan for 3 months", "time": "3 hours ago", "icon": "💎", "actionable": true, "actionText": "View Subscription"}, {"id": 3, "type": "verification", "user": "<PERSON><PERSON>", "userId": 3, "details": "Submitted verification documents", "time": "5 hours ago", "icon": "📄", "actionable": true, "actionText": "Review Documents"}, {"id": 4, "type": "report", "user": "Anonymous", "userId": null, "reportId": 123, "details": "Reported a profile for inappropriate content", "time": "8 hours ago", "icon": "🚫", "actionable": true, "actionText": "Review Report"}, {"id": 5, "type": "match", "user": "<PERSON><PERSON>", "userId": 5, "matchId": 456, "details": "Matched with <PERSON><PERSON><PERSON> with 92% compatibility", "time": "1 day ago", "icon": "💑", "actionable": true, "actionText": "View Match"}], "message": "Recent activity retrieved successfully"}