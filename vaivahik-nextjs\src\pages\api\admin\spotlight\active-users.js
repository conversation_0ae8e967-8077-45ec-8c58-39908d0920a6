// src/pages/api/admin/spotlight/active-users.js
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';

export default async function handler(req, res) {
  // Check if user is authenticated and is an admin
  const session = await getServerSession(req, res, authOptions);
  
  if (!session || !session.user || !session.user.isAdmin) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  // Handle GET request - fetch active spotlight users
  if (req.method === 'GET') {
    try {
      // Extract query parameters
      const { 
        page = 1, 
        limit = 10,
        featureId = ''
      } = req.query;
      
      // Build query parameters
      const queryParams = new URLSearchParams({
        page,
        limit,
        ...(featureId && { featureId })
      }).toString();

      // Fetch active users from backend API
      const response = await fetch(`${process.env.BACKEND_API_URL}/api/admin/spotlight/active-users?${queryParams}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.accessToken}`
        }
      }).catch(() => null);
      
      if (response && response.ok) {
        const data = await response.json();
        return res.status(200).json({
          success: true,
          message: data.message,
          activeUsers: data.activeUsers,
          pagination: data.pagination
        });
      } else {
        // Fallback to mock data if backend API fails
        const mockUsers = getMockActiveUsers(featureId);
        
        return res.status(200).json({
          success: true,
          message: 'Active spotlight users fetched successfully',
          activeUsers: mockUsers.slice((page - 1) * limit, page * limit),
          pagination: {
            currentPage: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(mockUsers.length / limit),
            totalActiveUsers: mockUsers.length
          }
        });
      }
    } catch (error) {
      console.error('Error fetching active spotlight users:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch active spotlight users',
        error: error.message
      });
    }
  }
  
  // Handle unsupported methods
  return res.status(405).json({ success: false, message: 'Method not allowed' });
}

// Helper function to get mock active users
function getMockActiveUsers(featureId = '') {
  const now = new Date();
  
  const features = [
    {
      id: 'feature-1',
      name: 'Premium Spotlight',
      durationHours: 24
    },
    {
      id: 'feature-2',
      name: 'Super Spotlight',
      durationHours: 48
    },
    {
      id: 'feature-3',
      name: 'Weekend Spotlight',
      durationHours: 72
    },
    {
      id: 'feature-5',
      name: 'Spotlight Pack',
      durationHours: 24
    },
    {
      id: 'feature-6',
      name: 'Festival Special',
      durationHours: 96
    }
  ];
  
  const users = [
    {
      id: 'user-1',
      name: 'Rahul Sharma',
      email: '<EMAIL>',
      phone: '+919876543210',
      gender: 'Male',
      age: 28,
      profilePic: 'https://example.com/profiles/rahul.jpg'
    },
    {
      id: 'user-2',
      name: 'Priya Patel',
      email: '<EMAIL>',
      phone: '+919876543211',
      gender: 'Female',
      age: 26,
      profilePic: 'https://example.com/profiles/priya.jpg'
    },
    {
      id: 'user-3',
      name: 'Amit Kumar',
      email: '<EMAIL>',
      phone: '+919876543212',
      gender: 'Male',
      age: 30,
      profilePic: 'https://example.com/profiles/amit.jpg'
    },
    {
      id: 'user-4',
      name: 'Neha Gupta',
      email: '<EMAIL>',
      phone: '+919876543213',
      gender: 'Female',
      age: 25,
      profilePic: 'https://example.com/profiles/neha.jpg'
    },
    {
      id: 'user-5',
      name: 'Vikram Singh',
      email: '<EMAIL>',
      phone: '+919876543214',
      gender: 'Male',
      age: 32,
      profilePic: 'https://example.com/profiles/vikram.jpg'
    },
    {
      id: 'user-6',
      name: 'Anjali Desai',
      email: '<EMAIL>',
      phone: '+919876543215',
      gender: 'Female',
      age: 27,
      profilePic: 'https://example.com/profiles/anjali.jpg'
    },
    {
      id: 'user-7',
      name: 'Rajesh Verma',
      email: '<EMAIL>',
      phone: '+919876543216',
      gender: 'Male',
      age: 29,
      profilePic: 'https://example.com/profiles/rajesh.jpg'
    },
    {
      id: 'user-8',
      name: 'Meera Joshi',
      email: '<EMAIL>',
      phone: '+919876543217',
      gender: 'Female',
      age: 24,
      profilePic: 'https://example.com/profiles/meera.jpg'
    },
    {
      id: 'user-9',
      name: 'Suresh Rao',
      email: '<EMAIL>',
      phone: '+919876543218',
      gender: 'Male',
      age: 31,
      profilePic: 'https://example.com/profiles/suresh.jpg'
    },
    {
      id: 'user-10',
      name: 'Pooja Mehta',
      email: '<EMAIL>',
      phone: '+919876543219',
      gender: 'Female',
      age: 28,
      profilePic: 'https://example.com/profiles/pooja.jpg'
    }
  ];
  
  // Generate random active spotlights
  const activeSpotlights = [];
  
  // For each feature, assign some random users
  features.forEach(feature => {
    // Skip if filtering by feature ID and this is not the requested feature
    if (featureId && feature.id !== featureId) {
      return;
    }
    
    // Get random number of users for this feature (1-4)
    const numUsers = Math.floor(Math.random() * 4) + 1;
    
    // Assign random users to this feature
    for (let i = 0; i < numUsers; i++) {
      const randomUserIndex = Math.floor(Math.random() * users.length);
      const user = users[randomUserIndex];
      
      // Generate random start time within the last 24 hours
      const startTime = new Date(now);
      startTime.setHours(startTime.getHours() - Math.floor(Math.random() * 24));
      
      // Calculate end time based on feature duration
      const endTime = new Date(startTime);
      endTime.setHours(endTime.getHours() + feature.durationHours);
      
      // Only include if the spotlight is still active
      if (endTime > now) {
        // Calculate remaining time in hours
        const remainingMs = endTime.getTime() - now.getTime();
        const remainingHours = Math.ceil(remainingMs / (1000 * 60 * 60));
        
        activeSpotlights.push({
          spotlightId: `spotlight-${feature.id}-${user.id}-${Date.now()}`,
          userId: user.id,
          name: user.name,
          email: user.email,
          phone: user.phone,
          gender: user.gender,
          age: user.age,
          profilePic: user.profilePic,
          featureName: feature.name,
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString(),
          timeRemaining: `${remainingHours} hours`
        });
      }
    }
  });
  
  return activeSpotlights;
}
