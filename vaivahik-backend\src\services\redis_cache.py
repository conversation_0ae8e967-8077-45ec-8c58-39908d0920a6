"""
Redis Cache Service for Matrimony Matching

This module provides Redis-based caching for the matrimony matching system
to reduce database load and improve performance.
"""

import json
import logging
import pickle
import hashlib
import time
import sys
import os
from datetime import datetime, timedelta
import numpy as np
import torch

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add parent directory to path to import redisClient
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'src'))
try:
    from config.redisClient import redisClient
except ImportError:
    logger.error("Could not import redisClient. Make sure the path is correct.")
    redisClient = None

class RedisCache:
    """Redis cache service for matrimony matching"""

    def __init__(self, config=None):
        """
        Initialize the Redis cache service

        Args:
            config (dict): Redis configuration
        """
        # Default configuration
        self.default_config = {
            'default_ttl': 86400,  # 24 hours in seconds
            'embedding_ttl': 604800,  # 7 days in seconds
            'match_ttl': 3600,  # 1 hour in seconds
            'enabled': True
        }

        # Use provided config or default
        self.config = config if config else self.default_config

        # Use the existing Redis client
        self.client = redisClient
        self.connected = self.client is not None and self.client.isReady

        if self.connected:
            logger.info("Using existing Redis client for matching service cache")
        else:
            logger.warning("Redis client not available or not ready")

    def is_connected(self):
        """Check if connected to Redis"""
        if not self.config['enabled'] or not self.connected:
            return False

        try:
            # Check if client is ready
            return self.client is not None and self.client.isReady
        except:
            self.connected = False
            return False

    def _generate_key(self, prefix, identifier):
        """
        Generate a Redis key

        Args:
            prefix (str): Key prefix
            identifier: Identifier (string, dict, etc.)

        Returns:
            str: Redis key
        """
        if isinstance(identifier, dict):
            # Sort dictionary by keys for consistent hashing
            identifier_str = json.dumps(identifier, sort_keys=True)
        else:
            identifier_str = str(identifier)

        # Generate hash
        hash_obj = hashlib.md5(identifier_str.encode())
        hash_str = hash_obj.hexdigest()

        return f"{prefix}:{hash_str}"

    async def set(self, key, value, ttl=None):
        """
        Set a value in the cache

        Args:
            key (str): Cache key
            value: Value to cache
            ttl (int): Time to live in seconds

        Returns:
            bool: Success status
        """
        if not self.is_connected():
            return False

        try:
            # Use default TTL if not specified
            if ttl is None:
                ttl = self.config['default_ttl']

            # Serialize value
            if isinstance(value, (dict, list, tuple)):
                serialized = json.dumps(value)
            elif isinstance(value, (np.ndarray, torch.Tensor)):
                serialized = pickle.dumps(value)
            else:
                serialized = pickle.dumps(value)

            # Set in Redis
            await self.client.set(key, serialized)

            # Set expiration
            if ttl > 0:
                await self.client.expire(key, ttl)

            return True
        except Exception as e:
            logger.error(f"Error setting cache key {key}: {str(e)}")
            return False

    async def get(self, key, default=None):
        """
        Get a value from the cache

        Args:
            key (str): Cache key
            default: Default value if key not found

        Returns:
            Value from cache or default
        """
        if not self.is_connected():
            return default

        try:
            # Get from Redis
            value = await self.client.get(key)

            if value is None:
                return default

            # Try to deserialize as JSON first
            try:
                if isinstance(value, bytes):
                    return json.loads(value.decode())
                else:
                    return json.loads(value)
            except:
                # If not JSON, try pickle
                try:
                    return pickle.loads(value)
                except:
                    # Return raw value if deserialization fails
                    return value
        except Exception as e:
            logger.error(f"Error getting cache key {key}: {str(e)}")
            return default

    async def delete(self, key):
        """
        Delete a key from the cache

        Args:
            key (str): Cache key

        Returns:
            bool: Success status
        """
        if not self.is_connected():
            return False

        try:
            await self.client.del_(key)
            return True
        except Exception as e:
            logger.error(f"Error deleting cache key {key}: {str(e)}")
            return False

    async def cache_user_embedding(self, user_id, embedding):
        """
        Cache a user embedding

        Args:
            user_id (str): User ID
            embedding: User embedding tensor or array

        Returns:
            bool: Success status
        """
        key = self._generate_key('user_embedding', user_id)
        return await self.set(key, embedding, ttl=self.config['embedding_ttl'])

    async def get_user_embedding(self, user_id):
        """
        Get a cached user embedding

        Args:
            user_id (str): User ID

        Returns:
            User embedding or None
        """
        key = self._generate_key('user_embedding', user_id)
        return await self.get(key)

    async def cache_match_results(self, user_id, match_results, params=None):
        """
        Cache match results for a user

        Args:
            user_id (str): User ID
            match_results (list): Match results
            params (dict): Query parameters

        Returns:
            bool: Success status
        """
        # Include query parameters in the key if provided
        if params:
            key = self._generate_key(f'matches:{user_id}', params)
        else:
            key = self._generate_key('matches', user_id)

        return await self.set(key, match_results, ttl=self.config['match_ttl'])

    async def get_match_results(self, user_id, params=None):
        """
        Get cached match results for a user

        Args:
            user_id (str): User ID
            params (dict): Query parameters

        Returns:
            list: Match results or None
        """
        # Include query parameters in the key if provided
        if params:
            key = self._generate_key(f'matches:{user_id}', params)
        else:
            key = self._generate_key('matches', user_id)

        return await self.get(key)

    async def cache_user_profile(self, user_id, profile):
        """
        Cache a user profile

        Args:
            user_id (str): User ID
            profile (dict): User profile

        Returns:
            bool: Success status
        """
        key = self._generate_key('user_profile', user_id)
        return await self.set(key, profile)

    async def get_user_profile(self, user_id):
        """
        Get a cached user profile

        Args:
            user_id (str): User ID

        Returns:
            dict: User profile or None
        """
        key = self._generate_key('user_profile', user_id)
        return await self.get(key)

    async def invalidate_user_cache(self, user_id):
        """
        Invalidate all cached data for a user

        Args:
            user_id (str): User ID

        Returns:
            bool: Success status
        """
        if not self.is_connected():
            return False

        try:
            # Delete user embedding
            embedding_key = self._generate_key('user_embedding', user_id)
            await self.client.del_(embedding_key)

            # Delete user profile
            profile_key = self._generate_key('user_profile', user_id)
            await self.client.del_(profile_key)

            # Delete match results
            # Note: This is a pattern match, so it will delete all keys that match
            match_pattern = f'matches:{user_id}:*'
            match_keys = await self.client.keys(match_pattern)
            if match_keys:
                await self.client.del_(*match_keys)

            return True
        except Exception as e:
            logger.error(f"Error invalidating cache for user {user_id}: {str(e)}")
            return False

    async def cache_ab_test_assignment(self, user_id, variant):
        """
        Cache A/B test variant assignment for a user

        Args:
            user_id (str): User ID
            variant (str): A/B test variant

        Returns:
            bool: Success status
        """
        key = self._generate_key('ab_test', user_id)
        # Cache for a longer period (30 days)
        return await self.set(key, variant, ttl=30 * 86400)

    async def get_ab_test_assignment(self, user_id):
        """
        Get cached A/B test variant assignment for a user

        Args:
            user_id (str): User ID

        Returns:
            str: A/B test variant or None
        """
        key = self._generate_key('ab_test', user_id)
        return await self.get(key)

    async def cache_model_metrics(self, model_id, metrics):
        """
        Cache model performance metrics

        Args:
            model_id (str): Model ID
            metrics (dict): Performance metrics

        Returns:
            bool: Success status
        """
        key = self._generate_key('model_metrics', model_id)
        return await self.set(key, metrics)

    async def get_model_metrics(self, model_id):
        """
        Get cached model performance metrics

        Args:
            model_id (str): Model ID

        Returns:
            dict: Performance metrics or None
        """
        key = self._generate_key('model_metrics', model_id)
        return await self.get(key)
