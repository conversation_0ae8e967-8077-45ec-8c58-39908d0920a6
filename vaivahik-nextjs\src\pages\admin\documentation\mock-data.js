import { useState } from 'react';
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);
import { mockDataUtils } from '@/config/apiConfig';
import {
  Box,
  Typography,
  Paper,
  Button,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Switch,
  FormControlLabel,
  Alert,
  AlertTitle,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Chip
} from '@mui/material';
import {
  Code as CodeIcon,
  Storage as StorageIcon,
  Api as ApiIcon,
  ToggleOn as ToggleOnIcon,
  Info as InfoIcon,
  Check as CheckIcon,
  Warning as WarningIcon
} from '@mui/icons-material';

export default function MockDataDocumentation() {
  const [useMockData, setUseMockData] = useState(mockDataUtils.isMockDataEnabled());
  
  const handleToggle = () => {
    mockDataUtils.toggleMockData();
    // The page will reload, so no need to update state
  };
  
  return (
    <EnhancedAdminLayout title="Mock Data Documentation">
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Mock Data Documentation
        </Typography>
        
        <Alert severity="info" sx={{ mb: 3 }}>
          <AlertTitle>Development Mode</AlertTitle>
          This documentation is for developers working on the Vaivahik admin panel. It explains how to use mock data during development.
        </Alert>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h5" gutterBottom>
                Overview
              </Typography>
              <Typography paragraph>
                The Vaivahik admin panel is designed to work with both mock data and a real backend API. During development, you can use mock data to test the UI and functionality without needing a working backend.
              </Typography>
              <Typography paragraph>
                Mock data is stored in JSON files in the <code>/public/mock-data/</code> directory. The API service automatically uses these files when mock data is enabled.
              </Typography>
              
              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={useMockData}
                      onChange={handleToggle}
                      color="primary"
                    />
                  }
                  label={useMockData ? "Currently using mock data" : "Currently using real API"}
                />
                <Button 
                  variant="outlined" 
                  color="primary" 
                  onClick={handleToggle}
                  startIcon={<ToggleOnIcon />}
                  sx={{ ml: 2 }}
                >
                  Toggle Mock Data
                </Button>
              </Box>
              
              <Divider sx={{ my: 3 }} />
              
              <Typography variant="h5" gutterBottom>
                How It Works
              </Typography>
              <Typography paragraph>
                When mock data is enabled, API requests are intercepted and redirected to the mock data files instead of being sent to the backend. This allows you to develop and test the UI without needing a working backend.
              </Typography>
              
              <List>
                <ListItem>
                  <ListItemIcon>
                    <StorageIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Mock Data Storage" 
                    secondary="Mock data is stored in JSON files in the /public/mock-data/ directory. The directory structure mirrors the API endpoints." 
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <ApiIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="API Service" 
                    secondary="The API service automatically uses mock data when enabled. It simulates API calls and returns the mock data from the JSON files." 
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <ToggleOnIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Toggle Switch" 
                    secondary="You can toggle between mock data and real API using the switch in the bottom right corner of the admin panel." 
                  />
                </ListItem>
              </List>
              
              <Divider sx={{ my: 3 }} />
              
              <Typography variant="h5" gutterBottom>
                Available Mock Data
              </Typography>
              <Typography paragraph>
                The following mock data files are available:
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={4}>
                  <Card variant="outlined">
                    <CardHeader title="Dashboard" />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary">
                        Dashboard statistics and charts
                      </Typography>
                      <Chip 
                        label="Available" 
                        color="success" 
                        size="small" 
                        icon={<CheckIcon />} 
                        sx={{ mt: 1 }} 
                      />
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <Card variant="outlined">
                    <CardHeader title="Users" />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary">
                        User profiles and management
                      </Typography>
                      <Chip 
                        label="Available" 
                        color="success" 
                        size="small" 
                        icon={<CheckIcon />} 
                        sx={{ mt: 1 }} 
                      />
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <Card variant="outlined">
                    <CardHeader title="Verification Queue" />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary">
                        User verification requests
                      </Typography>
                      <Chip 
                        label="Available" 
                        color="success" 
                        size="small" 
                        icon={<CheckIcon />} 
                        sx={{ mt: 1 }} 
                      />
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <Card variant="outlined">
                    <CardHeader title="Blog Posts" />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary">
                        Blog post management
                      </Typography>
                      <Chip 
                        label="Available" 
                        color="success" 
                        size="small" 
                        icon={<CheckIcon />} 
                        sx={{ mt: 1 }} 
                      />
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <Card variant="outlined">
                    <CardHeader title="Admin Users" />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary">
                        Admin user management
                      </Typography>
                      <Chip 
                        label="Available" 
                        color="success" 
                        size="small" 
                        icon={<CheckIcon />} 
                        sx={{ mt: 1 }} 
                      />
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <Card variant="outlined">
                    <CardHeader title="Email Templates" />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary">
                        Email template management
                      </Typography>
                      <Chip 
                        label="Available" 
                        color="success" 
                        size="small" 
                        icon={<CheckIcon />} 
                        sx={{ mt: 1 }} 
                      />
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Paper>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h5" gutterBottom>
                Adding New Mock Data
              </Typography>
              <Typography paragraph>
                To add new mock data, create a JSON file in the <code>/public/mock-data/</code> directory. The file name should match the API endpoint.
              </Typography>
              <Typography variant="h6" gutterBottom>
                Example:
              </Typography>
              <Box sx={{ bgcolor: 'grey.100', p: 2, borderRadius: 1, mb: 2 }}>
                <Typography variant="body2" component="pre" sx={{ fontFamily: 'monospace' }}>
                  {`// For API endpoint: /admin/settings
// Create file: /public/mock-data/admin/settings.json

{
  "success": true,
  "settings": [
    {
      "id": 1,
      "key": "site_name",
      "value": "Vaivahik",
      "type": "string"
    },
    {
      "id": 2,
      "key": "maintenance_mode",
      "value": "false",
      "type": "boolean"
    }
  ]
}`}
                </Typography>
              </Box>
              
              <Alert severity="warning" sx={{ mt: 3 }}>
                <AlertTitle>Important</AlertTitle>
                When creating mock data, make sure to follow the same structure as the real API responses. This ensures that the UI works correctly when switching to the real API.
              </Alert>
            </Paper>
            
            <Paper sx={{ p: 3 }}>
              <Typography variant="h5" gutterBottom>
                Connecting to Real Backend
              </Typography>
              <Typography paragraph>
                When the real backend is ready, you can switch to it by toggling off mock data. The API service will automatically send requests to the real backend.
              </Typography>
              <Typography paragraph>
                To configure the real backend URL, update the <code>API_BASE_URL</code> in <code>src/config/apiConfig.js</code>.
              </Typography>
              
              <Box sx={{ bgcolor: 'grey.100', p: 2, borderRadius: 1, mb: 2 }}>
                <Typography variant="body2" component="pre" sx={{ fontFamily: 'monospace' }}>
                  {`// src/config/apiConfig.js
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5000/api';`}
                </Typography>
              </Box>
              
              <Typography paragraph>
                You can also set the <code>NEXT_PUBLIC_API_BASE_URL</code> environment variable to override the default URL.
              </Typography>
              
              <Button 
                variant="contained" 
                color="primary" 
                startIcon={<ApiIcon />}
                fullWidth
                sx={{ mt: 2 }}
                href="/admin/documentation/backend-connection"
              >
                Backend Connection Guide
              </Button>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </EnhancedAdminLayout>
  );
}
