import axios from 'axios';
import { API_BASE_URL } from '@/config';

const API_URL = `${API_BASE_URL}/notifications`;

/**
 * Get user notifications
 * @param {Object} options - Query options
 * @returns {Promise} Promise with notifications data
 */
export const getUserNotifications = async (options = {}) => {
  try {
    const token = localStorage.getItem('token');
    
    const queryParams = new URLSearchParams();
    
    if (options.type) {
      queryParams.append('type', options.type);
    }
    
    if (options.page) {
      queryParams.append('page', options.page);
    }
    
    if (options.limit) {
      queryParams.append('limit', options.limit);
    }
    
    if (options.isRead !== undefined) {
      queryParams.append('isRead', options.isRead);
    }
    
    if (options.startDate) {
      queryParams.append('startDate', options.startDate);
    }
    
    if (options.endDate) {
      queryParams.append('endDate', options.endDate);
    }
    
    const response = await axios.get(`${API_URL}?${queryParams.toString()}`, {
      headers: {
        'x-auth-token': token
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error fetching notifications:', error);
    throw error;
  }
};

/**
 * Get unread notification count
 * @returns {Promise} Promise with unread count
 */
export const getUnreadCount = async () => {
  try {
    const token = localStorage.getItem('token');
    
    const response = await axios.get(`${API_URL}/unread-count`, {
      headers: {
        'x-auth-token': token
      }
    });
    
    return response.data.count;
  } catch (error) {
    console.error('Error fetching unread notification count:', error);
    throw error;
  }
};

/**
 * Mark notifications as read
 * @param {Array} notificationIds - Notification IDs to mark as read (optional)
 * @returns {Promise} Promise with result
 */
export const markNotificationsAsRead = async (notificationIds = null) => {
  try {
    const token = localStorage.getItem('token');
    
    const response = await axios.put(
      `${API_URL}/read`,
      { notificationIds },
      {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      }
    );
    
    return response.data;
  } catch (error) {
    console.error('Error marking notifications as read:', error);
    throw error;
  }
};

/**
 * Delete notifications
 * @param {Array} notificationIds - Notification IDs to delete
 * @returns {Promise} Promise with result
 */
export const deleteNotifications = async (notificationIds) => {
  try {
    const token = localStorage.getItem('token');
    
    const response = await axios.delete(
      API_URL,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        data: { notificationIds }
      }
    );
    
    return response.data;
  } catch (error) {
    console.error('Error deleting notifications:', error);
    throw error;
  }
};

/**
 * Get notification settings
 * @returns {Promise} Promise with notification settings
 */
export const getNotificationSettings = async () => {
  try {
    const token = localStorage.getItem('token');
    
    const response = await axios.get(`${API_URL}/settings`, {
      headers: {
        'x-auth-token': token
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error fetching notification settings:', error);
    throw error;
  }
};

/**
 * Update notification settings
 * @param {Object} settings - Updated notification settings
 * @returns {Promise} Promise with updated settings
 */
export const updateNotificationSettings = async (settings) => {
  try {
    const token = localStorage.getItem('token');
    
    const response = await axios.put(
      `${API_URL}/settings`,
      settings,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      }
    );
    
    return response.data;
  } catch (error) {
    console.error('Error updating notification settings:', error);
    throw error;
  }
};

/**
 * Format notification timestamp
 * @param {Date} timestamp - Notification timestamp
 * @returns {String} Formatted timestamp
 */
export const formatNotificationTime = (timestamp) => {
  const now = new Date();
  const notificationTime = new Date(timestamp);
  const diffMs = now - notificationTime;
  
  // Convert to seconds
  const diffSec = Math.floor(diffMs / 1000);
  
  if (diffSec < 60) {
    return 'Just now';
  }
  
  // Convert to minutes
  const diffMin = Math.floor(diffSec / 60);
  
  if (diffMin < 60) {
    return `${diffMin} minute${diffMin !== 1 ? 's' : ''} ago`;
  }
  
  // Convert to hours
  const diffHour = Math.floor(diffMin / 60);
  
  if (diffHour < 24) {
    return `${diffHour} hour${diffHour !== 1 ? 's' : ''} ago`;
  }
  
  // Convert to days
  const diffDay = Math.floor(diffHour / 24);
  
  if (diffDay < 7) {
    return `${diffDay} day${diffDay !== 1 ? 's' : ''} ago`;
  }
  
  // Format as date for older notifications
  return notificationTime.toLocaleDateString();
};
