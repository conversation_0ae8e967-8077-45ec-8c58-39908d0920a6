// Authentication Flow - OTP Based

class AuthManager {
    constructor() {
        this.currentStep = 1;
        this.phoneNumber = '';
        this.timerInterval = null;
        this.isExistingUser = false;
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Phone form submission
        document.getElementById('phoneForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handlePhoneSubmit();
        });

        // OTP form submission
        document.getElementById('otpForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleOTPVerification();
        });

        // Profile form submission (for new users)
        document.getElementById('profileForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleProfileUpdate();
        });

        // Resend OTP button
        document.getElementById('resendOtp').addEventListener('click', () => {
            this.handlePhoneSubmit(true);
        });
    }

    showStep(step) {
        document.querySelectorAll('.auth-step').forEach(el => el.style.display = 'none');
        document.querySelector(`.auth-step-${step}`).style.display = 'block';
        this.currentStep = step;
        this.updateProgress();
    }

    updateProgress() {
        const progress = ((this.currentStep - 1) / 2) * 100;
        document.querySelector('.progress-indicator').style.width = `${progress}%`;
    }

    showToast(message, type = 'error') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    async handlePhoneSubmit(isResend = false) {
        try {
            const phoneInput = document.getElementById('phone');
            const phone = phoneInput.value.trim();

            if (!/^[0-9]{10}$/.test(phone)) {
                this.showToast('Please enter a valid 10-digit phone number');
                return;
            }

            this.phoneNumber = phone;

            const response = await fetch('/api/users/check-phone', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ phone })
            });

            const data = await response.json();
            this.isExistingUser = data.exists;

            // Now request OTP
            const otpResponse = await fetch('/api/users/request-otp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ phone })
            });

            const otpData = await otpResponse.json();

            if (otpResponse.ok) {
                if (!isResend) {
                    this.showStep(2);
                    // Show appropriate message based on user status
                    const userStatus = document.getElementById('userStatus');
                    if (this.isExistingUser) {
                        userStatus.style.display = 'block';
                    } else {
                        userStatus.style.display = 'none';
                    }
                }
                // Start/restart OTP timer
                if (this.timerInterval) {
                    clearInterval(this.timerInterval);
                }
                this.timerInterval = startTimer(120); // 2 minutes
                this.showToast('OTP sent successfully', 'success');
            } else {
                this.showToast(otpData.message || 'Failed to send OTP');
            }
        } catch (error) {
            console.error('Error:', error);
            this.showToast('An error occurred while sending OTP');
        }
    }

    async handleOTPVerification() {
        try {
            const otpInputs = document.querySelectorAll('.otp-input');
            const otp = Array.from(otpInputs).map(input => input.value).join('');

            if (!/^[0-9]{6}$/.test(otp)) {
                this.showToast('Please enter a valid 6-digit OTP');
                return;
            }

            const response = await fetch('/api/users/verify-otp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    phone: this.phoneNumber,
                    otp
                })
            });

            const data = await response.json();

            if (response.ok) {
                if (!this.isExistingUser) {
                    // New user - show profile completion form
                    this.showStep(3);
                    this.showToast('Please complete your profile', 'success');
                } else {
                    // Existing user - redirect to dashboard
                    this.showToast('Sign in successful!', 'success');
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 1000);
                }
            } else {
                this.showToast(data.message || 'Invalid OTP');
            }
        } catch (error) {
            console.error('Error:', error);
            this.showToast('An error occurred while verifying OTP');
        }
    }

    async handleProfileUpdate() {
        try {
            // Clear previous errors
            clearErrors();

            // Get all form values
            const formData = {
                fullName: document.getElementById('fullName').value.trim(),
                profileFor: document.getElementById('profileFor').value,
                gender: document.getElementById('gender').value,
                maritalStatus: document.getElementById('maritalStatus').value,
                dateOfBirth: document.getElementById('dateOfBirth').value,
                birthTime: document.getElementById('birthTime').value,
                birthPlace: document.getElementById('birthPlace').value.trim(),
                height: parseInt(document.getElementById('height').value),
                religion: document.getElementById('religion').value,
                caste: document.getElementById('caste').value.trim(),
                city: document.getElementById('city').value.trim(),
                state: document.getElementById('state').value,
                education: document.getElementById('education').value.trim(),
                occupation: document.getElementById('occupation').value.trim(),
                incomeRange: document.getElementById('incomeRange').value,
                employmentType: document.getElementById('employmentType').value,
                fatherName: document.getElementById('fatherName').value.trim(),
                motherName: document.getElementById('motherName').value.trim(),
                totalSiblings: parseInt(document.getElementById('totalSiblings').value),
                marriedSiblings: parseInt(document.getElementById('marriedSiblings').value),
                familyContact: document.getElementById('familyContact').value.trim()
            };

            // Validation
            const errors = [];

            // Required field validation
            const requiredFields = {
                fullName: 'Full Name',
                profileFor: 'Profile For',
                gender: 'Gender',
                maritalStatus: 'Marital Status',
                dateOfBirth: 'Date of Birth',
                birthTime: 'Birth Time',
                birthPlace: 'Birth Place',
                height: 'Height',
                religion: 'Religion',
                city: 'City',
                state: 'State',
                education: 'Education',
                occupation: 'Occupation',
                incomeRange: 'Income Range',
                employmentType: 'Employment Type',
                fatherName: "Father's Name",
                motherName: "Mother's Name",
                totalSiblings: 'Total Siblings',
                marriedSiblings: 'Married Siblings',
                familyContact: 'Family Contact'
            };

            for (const [field, label] of Object.entries(requiredFields)) {
                if (!formData[field] && formData[field] !== 0) {
                    errors.push(`${label} is required`);
                }
            }

            // Name validation
            if (formData.fullName.length < 3) {
                errors.push('Full name must be at least 3 characters long');
            }

            // Height validation
            if (formData.height < 100 || formData.height > 250) {
                errors.push('Height must be between 100cm and 250cm');
            }

            // Date of birth validation
            const birthDate = new Date(formData.dateOfBirth);
            const today = new Date();
            const age = today.getFullYear() - birthDate.getFullYear();
            if (age < 18) {
                errors.push('You must be at least 18 years old to register');
            }

            // Siblings validation
            if (formData.marriedSiblings > formData.totalSiblings) {
                errors.push('Number of married siblings cannot exceed total siblings');
            }

            // Phone number validation
            const phoneRegex = /^[0-9]{10}$/;
            if (!phoneRegex.test(formData.familyContact)) {
                errors.push('Please enter a valid 10-digit family contact number');
            }

            // If there are validation errors, display them and return
            if (errors.length > 0) {
                displayErrors(errors);
                return;
            }

            const response = await fetch('/api/users/profile', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${getToken()}`
                },
                body: JSON.stringify(formData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Failed to update profile');
            }

            // Show success message
            showMessage('Profile updated successfully!', 'success');

            // Redirect to dashboard after a short delay
            setTimeout(() => {
                window.location.href = '/dashboard';
            }, 1500);

        } catch (error) {
            showMessage(error.message, 'error');
        }
    }
}

function clearErrors() {
    const errorContainer = document.querySelector('.error-container');
    if (errorContainer) {
        errorContainer.remove();
    }
}

function displayErrors(errors) {
    clearErrors();

    const errorContainer = document.createElement('div');
    errorContainer.className = 'error-container';
    errorContainer.style.cssText = `
        background-color: #fff3f3;
        border: 1px solid #ff5f6d;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        color: #ff5f6d;
    `;

    const errorList = document.createElement('ul');
    errorList.style.margin = '0';
    errorList.style.paddingLeft = '20px';

    errors.forEach(error => {
        const li = document.createElement('li');
        li.textContent = error;
        errorList.appendChild(li);
    });

    errorContainer.appendChild(errorList);
    document.getElementById('profileForm').insertBefore(errorContainer, document.querySelector('button[type="submit"]').parentNode);
}

function showMessage(message, type = 'info') {
    const messageContainer = document.createElement('div');
    messageContainer.className = `message ${type}`;
    messageContainer.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 25px;
        border-radius: 8px;
        background-color: ${type === 'success' ? '#e3fcef' : '#fff3f3'};
        border: 1px solid ${type === 'success' ? '#34d399' : '#ff5f6d'};
        color: ${type === 'success' ? '#047857' : '#dc2626'};
        z-index: 1000;
        animation: slideIn 0.3s ease-out;
    `;

    messageContainer.textContent = message;
    document.body.appendChild(messageContainer);

    setTimeout(() => {
        messageContainer.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => messageContainer.remove(), 300);
    }, 3000);
}

// Initialize the auth manager when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.authManager = new AuthManager();
});