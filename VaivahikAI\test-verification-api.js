// test-verification-api.js
const axios = require('axios');
require('dotenv').config();

// Configuration
const API_URL = 'http://localhost:8000/api';
const ADMIN_EMAIL = '<EMAIL>'; // Replace with your admin email
const ADMIN_PASSWORD = 'admin123'; // Replace with your admin password

// Test the verification queue API
async function testVerificationQueueAPI() {
    try {
        console.log('=== VERIFICATION QUEUE API TEST ===');

        // Step 1: Admin login
        console.log('\n1. Admin login...');
        const loginResponse = await axios.post(`${API_URL}/admin/login`, {
            email: ADMIN_EMAIL,
            password: ADMIN_PASSWORD
        });

        const adminToken = loginResponse.data.accessToken;
        console.log(`Admin login successful. Token: ${adminToken.substring(0, 20)}...`);

        // Step 2: Get verification queue
        console.log('\n2. Getting verification queue...');
        const queueResponse = await axios.get(`${API_URL}/admin/users/verification-queue`, {
            headers: {
                'Authorization': `Bearer ${adminToken}`
            }
        });

        console.log('Verification queue response:');
        console.log(JSON.stringify(queueResponse.data, null, 2));

        // Step 3: Get a user ID from the queue for testing
        if (queueResponse.data.users && queueResponse.data.users.length > 0) {
            const testUserId = queueResponse.data.users[0].id;
            console.log(`\nSelected test user ID: ${testUserId}`);

            // Step 4: Test approve verification
            console.log('\n3. Testing approve verification...');
            const approveResponse = await axios.put(`${API_URL}/admin/users/${testUserId}/verify`, {}, {
                headers: {
                    'Authorization': `Bearer ${adminToken}`
                }
            });

            console.log('Approve verification response:');
            console.log(JSON.stringify(approveResponse.data, null, 2));

            // Step 5: Test reject verification (create another user for this)
            console.log('\n4. Getting verification queue again for reject test...');
            const queueResponse2 = await axios.get(`${API_URL}/admin/users/verification-queue`, {
                headers: {
                    'Authorization': `Bearer ${adminToken}`
                }
            });

            if (queueResponse2.data.users && queueResponse2.data.users.length > 0) {
                const testUserId2 = queueResponse2.data.users[0].id;
                console.log(`Selected test user ID for reject: ${testUserId2}`);

                console.log('\n5. Testing reject verification...');
                const rejectResponse = await axios.put(`${API_URL}/admin/users/${testUserId2}/reject-verification`, {
                    reason: 'Test rejection'
                }, {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`
                    }
                });

                console.log('Reject verification response:');
                console.log(JSON.stringify(rejectResponse.data, null, 2));
            } else {
                console.log('No users in verification queue for reject test.');
            }
        } else {
            console.log('No users in verification queue for testing.');
        }

        console.log('\n=== TEST COMPLETED SUCCESSFULLY ===');
    } catch (error) {
        console.error('\n=== TEST FAILED ===');
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        } else {
            console.error(error.message);
        }
    }
}

// Run the test
testVerificationQueueAPI();
