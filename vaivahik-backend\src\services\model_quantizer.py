"""
Model Quantizer for Matrimony Matching

This module provides model quantization functionality to optimize the
matrimony matching model for production deployment.
"""

import os
import json
import logging
import torch
import torch.nn as nn
import torch.quantization as quantization
from datetime import datetime

from .enhanced_tower_model_pytorch import EnhancedMatrimonyMatchingModel, EnhancedTwoTowerModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelQuantizer:
    """Model quantizer for matrimony matching model"""
    
    def __init__(self, model_path=None, config=None):
        """
        Initialize the model quantizer
        
        Args:
            model_path (str): Path to the model to quantize
            config (dict): Configuration parameters
        """
        # Default configuration
        self.default_config = {
            'quantization_method': 'dynamic',  # 'dynamic' or 'static'
            'quantization_dtype': 'qint8',     # 'qint8' or 'float16'
            'model_dir': os.path.join(os.path.dirname(__file__), '../../models'),
            'calibration_samples': 100,        # Number of samples for static quantization
            'optimize_for_mobile': False,      # Whether to optimize for mobile deployment
            'enable_fusion': True              # Whether to enable operator fusion
        }
        
        # Use provided config or default
        self.config = config if config else self.default_config
        
        # Set device
        self.device = torch.device('cpu')  # Quantization requires CPU
        logger.info(f"Using device: {self.device} for quantization")
        
        # Load model if path provided
        self.model = None
        if model_path:
            self.load_model(model_path)
    
    def load_model(self, model_path):
        """
        Load a model from disk
        
        Args:
            model_path (str): Path to the model
            
        Returns:
            bool: Success status
        """
        try:
            # Load checkpoint
            checkpoint = torch.load(model_path, map_location=self.device)
            
            # Create model
            if 'config' in checkpoint:
                config = checkpoint['config']
                self.model = EnhancedMatrimonyMatchingModel(config)
                self.model.build_model()
                
                # Load state dict
                self.model.model.load_state_dict(checkpoint['model_state_dict'])
                self.model.model.to(self.device)
                self.model.model.eval()  # Set to evaluation mode
                
                logger.info(f"Model loaded from {model_path}")
                return True
            else:
                logger.error(f"Invalid model checkpoint: {model_path}")
                return False
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            return False
    
    def prepare_model_for_quantization(self):
        """
        Prepare the model for quantization by adding quantization stubs
        
        Returns:
            torch.nn.Module: Prepared model
        """
        if not self.model:
            logger.error("No model loaded")
            return None
        
        # Create a copy of the model for quantization
        model_to_quantize = EnhancedTwoTowerModel(
            self.model.model.user_tower.input_bn.num_features,
            self.model.model.match_tower.input_bn.num_features,
            self.model.config
        )
        model_to_quantize.load_state_dict(self.model.model.state_dict())
        model_to_quantize.eval()
        
        # Prepare for dynamic quantization
        if self.config['quantization_method'] == 'dynamic':
            # Specify which layers to quantize
            quantized_layers = {
                nn.Linear,
                nn.ReLU
            }
            
            # Prepare model for dynamic quantization
            model_prepared = quantization.quantize_dynamic(
                model_to_quantize,
                quantized_layers,
                dtype=torch.qint8 if self.config['quantization_dtype'] == 'qint8' else torch.float16
            )
            
            logger.info("Model prepared for dynamic quantization")
            return model_prepared
        
        # Prepare for static quantization
        elif self.config['quantization_method'] == 'static':
            # This requires calibration data and more complex setup
            # For simplicity, we'll use dynamic quantization by default
            logger.warning("Static quantization not fully implemented, falling back to dynamic")
            return self.prepare_model_for_dynamic_quantization(model_to_quantize)
        
        else:
            logger.error(f"Unknown quantization method: {self.config['quantization_method']}")
            return None
    
    def prepare_model_for_dynamic_quantization(self, model):
        """
        Prepare model for dynamic quantization
        
        Args:
            model (torch.nn.Module): Model to quantize
            
        Returns:
            torch.nn.Module: Prepared model
        """
        # Specify which layers to quantize
        quantized_layers = {
            nn.Linear,
            nn.ReLU
        }
        
        # Prepare model for dynamic quantization
        model_prepared = quantization.quantize_dynamic(
            model,
            quantized_layers,
            dtype=torch.qint8 if self.config['quantization_dtype'] == 'qint8' else torch.float16
        )
        
        return model_prepared
    
    def quantize_model(self):
        """
        Quantize the model
        
        Returns:
            torch.nn.Module: Quantized model
        """
        if not self.model:
            logger.error("No model loaded")
            return None
        
        try:
            # Prepare model for quantization
            model_prepared = self.prepare_model_for_quantization()
            
            if not model_prepared:
                return None
            
            # For dynamic quantization, the model is already quantized
            quantized_model = model_prepared
            
            # Log quantization results
            original_size = self._get_model_size(self.model.model)
            quantized_size = self._get_model_size(quantized_model)
            size_reduction = (1 - quantized_size / original_size) * 100
            
            logger.info(f"Original model size: {original_size:.2f} MB")
            logger.info(f"Quantized model size: {quantized_size:.2f} MB")
            logger.info(f"Size reduction: {size_reduction:.2f}%")
            
            return quantized_model
        except Exception as e:
            logger.error(f"Error quantizing model: {str(e)}")
            return None
    
    def _get_model_size(self, model):
        """
        Get the size of a model in MB
        
        Args:
            model (torch.nn.Module): Model to measure
            
        Returns:
            float: Model size in MB
        """
        torch.save(model.state_dict(), "temp_model.pt")
        size_bytes = os.path.getsize("temp_model.pt")
        os.remove("temp_model.pt")
        return size_bytes / (1024 * 1024)  # Convert to MB
    
    def save_quantized_model(self, model, output_path=None):
        """
        Save a quantized model
        
        Args:
            model (torch.nn.Module): Quantized model to save
            output_path (str): Path to save the model
            
        Returns:
            str: Path where the model was saved
        """
        if not output_path:
            # Generate output path
            timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
            output_path = os.path.join(
                self.config['model_dir'],
                f"quantized_model_{timestamp}.pt"
            )
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        try:
            # Save the quantized model
            torch.jit.save(torch.jit.script(model), output_path)
            logger.info(f"Quantized model saved to {output_path}")
            return output_path
        except Exception as e:
            logger.error(f"Error saving quantized model: {str(e)}")
            return None
    
    def optimize_for_inference(self, model):
        """
        Optimize a model for inference
        
        Args:
            model (torch.nn.Module): Model to optimize
            
        Returns:
            torch.nn.Module: Optimized model
        """
        try:
            # Script the model
            scripted_model = torch.jit.script(model)
            
            # Optimize for inference
            optimized_model = torch.jit.optimize_for_inference(scripted_model)
            
            logger.info("Model optimized for inference")
            return optimized_model
        except Exception as e:
            logger.error(f"Error optimizing model for inference: {str(e)}")
            return model  # Return original model on error
    
    def optimize_for_mobile(self, model):
        """
        Optimize a model for mobile deployment
        
        Args:
            model (torch.nn.Module): Model to optimize
            
        Returns:
            torch.nn.Module: Optimized model
        """
        if not self.config['optimize_for_mobile']:
            return model
        
        try:
            # Script the model
            scripted_model = torch.jit.script(model)
            
            # Optimize for mobile
            optimized_model = torch._C._jit_pass_optimize_for_mobile(scripted_model)
            
            logger.info("Model optimized for mobile")
            return optimized_model
        except Exception as e:
            logger.error(f"Error optimizing model for mobile: {str(e)}")
            return model  # Return original model on error
    
    def quantize_and_optimize(self, output_path=None):
        """
        Quantize and optimize the model
        
        Args:
            output_path (str): Path to save the optimized model
            
        Returns:
            str: Path where the model was saved
        """
        # Quantize the model
        quantized_model = self.quantize_model()
        if not quantized_model:
            return None
        
        # Optimize for inference
        optimized_model = self.optimize_for_inference(quantized_model)
        
        # Optimize for mobile if requested
        if self.config['optimize_for_mobile']:
            optimized_model = self.optimize_for_mobile(optimized_model)
        
        # Save the optimized model
        return self.save_quantized_model(optimized_model, output_path)
