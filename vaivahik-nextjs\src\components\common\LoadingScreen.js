/**
 * Loading Screen Component
 * 
 * This component displays a loading spinner with an optional message.
 */

import React from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';

/**
 * Loading Screen Component
 * @param {Object} props - Component props
 * @param {string} [props.message='Loading...'] - Message to display
 * @param {string} [props.size='medium'] - Size of the loading spinner
 * @returns {React.ReactNode} - Loading screen component
 */
const LoadingScreen = ({ 
  message = 'Loading...', 
  size = 'medium' 
}) => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        minHeight: '200px',
        width: '100%',
        p: 3
      }}
    >
      <CircularProgress size={size === 'small' ? 24 : size === 'large' ? 60 : 40} />
      
      {message && (
        <Typography
          variant="body1"
          sx={{
            mt: 2,
            textAlign: 'center',
            color: 'text.secondary'
          }}
        >
          {message}
        </Typography>
      )}
    </Box>
  );
};

export default LoadingScreen;
