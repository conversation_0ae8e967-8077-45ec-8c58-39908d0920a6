/**
 * Validation Middleware
 *
 * This middleware provides validation for request data based on the validation rules.
 * It ensures that the data sent to the server is valid before processing it.
 */

const { validateField } = require('../utils/validationUtils');
const { VALIDATION } = require('../utils/validationConstants');
const { ValidationError } = require('../utils/errors');
const { isLockedField, getLockedFieldChanges, generateLockedFieldErrors } = require('../utils/fieldLocking');

/**
 * Creates validation middleware for a specific form type
 * @param {string} formType - The type of form to validate (e.g., 'basicDetails', 'familyDetails')
 * @returns {Function} - Express middleware function
 */
const validateForm = (formType) => {
  return (req, res, next) => {
    const formData = req.body;
    const errors = {};

    // Define required fields for each form type
    const requiredFields = getRequiredFields(formType);

    // Check required fields
    requiredFields.forEach(field => {
      if (!formData[field]) {
        errors[field] = `${formatFieldName(field)} is required`;
      }
    });

    // Validate all fields
    Object.keys(formData).forEach(field => {
      if (!errors[field]) { // Skip if already has error from required check
        const error = validateField(field, formData[field], formData);
        if (error) {
          errors[field] = error;
        }
      }
    });

    // Add form-specific validations
    const formSpecificErrors = validateFormSpecific(formType, formData);
    Object.assign(errors, formSpecificErrors);

    // If there are errors, throw a ValidationError
    if (Object.keys(errors).length > 0) {
      throw new ValidationError('Validation failed', errors);
    }

    // No errors, proceed to the next middleware
    next();
  };
};

/**
 * Gets required fields for a form type
 * @param {string} formType - The type of form
 * @returns {Array} - Array of required field names
 */
const getRequiredFields = (formType) => {
  switch (formType) {
    case 'basicDetails':
      return ['fullName', 'gender', 'dateOfBirth', 'height'];
    case 'familyDetails':
      return ['familyType', 'fatherName', 'motherName'];
    case 'educationCareer':
      return ['highestEducation', 'occupation'];
    case 'locationDetails':
      return ['city', 'state', 'country'];
    case 'lifestyleHabits':
      return ['diet'];
    case 'aboutMe':
      return ['aboutMe'];
    default:
      return [];
  }
};

/**
 * Formats a field name for error messages
 * @param {string} field - The field name in camelCase
 * @returns {string} - Formatted field name
 */
const formatFieldName = (field) => {
  return field
    // Insert a space before all uppercase letters
    .replace(/([A-Z])/g, ' $1')
    // Replace the first character with its uppercase version
    .replace(/^./, str => str.toUpperCase());
};

/**
 * Performs form-specific validations
 * @param {string} formType - The type of form
 * @param {Object} formData - The form data
 * @returns {Object} - Validation errors
 */
const validateFormSpecific = (formType, formData) => {
  const errors = {};

  switch (formType) {
    case 'familyDetails':
      // Validate married siblings count
      if (formData.brothers && formData.marriedBrothers) {
        if (parseInt(formData.marriedBrothers) > parseInt(formData.brothers)) {
          errors.marriedBrothers = 'Married brothers cannot exceed total brothers';
        }
      }

      if (formData.sisters && formData.marriedSisters) {
        if (parseInt(formData.marriedSisters) > parseInt(formData.sisters)) {
          errors.marriedSisters = 'Married sisters cannot exceed total sisters';
        }
      }
      break;

    case 'partnerPreferences':
      // Validate age range
      if (formData.ageRange && Array.isArray(formData.ageRange) && formData.ageRange.length === 2) {
        if (formData.ageRange[0] >= formData.ageRange[1]) {
          errors.ageRange = 'Minimum age must be less than maximum age';
        }
      }

      // Validate height range
      if (formData.heightRange && Array.isArray(formData.heightRange) && formData.heightRange.length === 2) {
        if (formData.heightRange[0] >= formData.heightRange[1]) {
          errors.heightRange = 'Minimum height must be less than maximum height';
        }
      }
      break;
  }

  return errors;
};

/**
 * Middleware to prevent changes to locked fields
 * @returns {Function} - Express middleware function
 */
const preventLockedFieldChanges = () => {
  return async (req, res, next) => {
    try {
      // Check if this is an admin override
      if (req.adminOverride) {
        console.log('[ADMIN OVERRIDE] Bypassing locked field validation');
        return next();
      }

      const prisma = req.prisma;
      const userId = req.user?.userId;

      if (!userId) {
        return next();
      }

      // Get the user's existing profile
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: { profile: true }
      });

      if (!user || !user.profile) {
        return next();
      }

      // Check if any locked fields are being changed
      const lockedChanges = getLockedFieldChanges(user.profile, req.body);

      // If there are locked field changes, throw a validation error
      if (Object.keys(lockedChanges).length > 0) {
        const errors = generateLockedFieldErrors(lockedChanges);
        throw new ValidationError('Cannot modify locked fields', errors);
      }

      // No locked field changes, proceed to the next middleware
      next();
    } catch (error) {
      next(error);
    }
  };
};

module.exports = {
  validateForm,
  preventLockedFieldChanges
};
