// API endpoint for managing transactions
import axios from 'axios';
import { handleApiError } from '@/utils/errorHandler';
import { withAuth } from '@/utils/authHandler';

// Backend API URL - adjust this to your actual backend URL
const BACKEND_API_URL = 'http://localhost:3001/api';

async function handler(req, res) {
  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getTransactions(req, res);
      case 'PUT':
        return await updateTransaction(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    return handleApiError(error, res, 'Transactions API');
  }
}

// GET /api/admin/transactions
async function getTransactions(req, res) {
  try {
    // Get the auth token from the request cookies or headers
    const token = req.cookies?.adminToken || req.headers.authorization?.split(' ')[1];

    if (!token) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    // Get query parameters for pagination and filtering
    const { 
      page = 1, 
      limit = 10, 
      status, 
      paymentMethod,
      startDate,
      endDate,
      userId,
      search 
    } = req.query;

    try {
      // Fetch transactions from the backend API
      const response = await axios({
        method: 'GET',
        url: `${BACKEND_API_URL}/admin/transactions`,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        params: {
          page,
          limit,
          status,
          paymentMethod,
          startDate,
          endDate,
          userId,
          search
        },
        timeout: 10000 // 10 second timeout
      });

      // Return the response from the backend
      return res.status(200).json(response.data);
    } catch (apiError) {
      // Log the error
      console.error('Error fetching transactions from backend API:', apiError.message);
      
      // Return a meaningful error message
      return res.status(503).json({
        success: false,
        message: 'Failed to fetch transactions from backend API.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available'
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Get transactions');
  }
}

// PUT /api/admin/transactions
async function updateTransaction(req, res) {
  try {
    const token = req.cookies?.adminToken || req.headers.authorization?.split(' ')[1];

    if (!token) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    const { id, ...transactionData } = req.body;

    if (!id) {
      return res.status(400).json({ success: false, message: 'Transaction ID is required' });
    }

    try {
      const response = await axios({
        method: 'PUT',
        url: `${BACKEND_API_URL}/admin/transactions/${id}`,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        data: transactionData,
        timeout: 10000
      });

      return res.status(200).json(response.data);
    } catch (apiError) {
      console.error('Error updating transaction:', apiError.message);
      
      return res.status(503).json({
        success: false,
        message: 'Failed to update transaction.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available'
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Update transaction');
  }
}

// Export the handler with authentication middleware
export default withAuth(handler, 'ADMIN');
