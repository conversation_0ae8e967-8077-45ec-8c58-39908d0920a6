/**
 * API Service
 *
 * This service provides methods for making authenticated API requests to the backend.
 * It handles token management, request/response interceptors, error handling, and caching.
 * It also supports using mock data for development.
 */

import axios from 'axios';
import { logout, adminLogout, getToken, getAdminToken } from './authService';
import { API_BASE_URL, API_TIMEOUT, mockDataUtils } from '@/config/apiConfig';
import { fetchMockData, createMockItem, updateMockItem, deleteMockItem } from './mockDataService';
import { formatError, logError, isNetworkError } from '@/utils/errorHandling';
import { withRetry } from '@/utils/retryLogic';

// Simple in-memory cache
const cache = {
  data: new Map(),

  // Set cache item with optional expiration time (in milliseconds)
  set(key, value, ttl = 60000) { // Default TTL: 1 minute
    const expiresAt = Date.now() + ttl;
    this.data.set(key, { value, expiresAt });

    // Set up automatic cleanup after TTL
    setTimeout(() => {
      this.delete(key);
    }, ttl);
  },

  // Get cache item if it exists and is not expired
  get(key) {
    const item = this.data.get(key);
    if (!item) return null;

    // Check if item has expired
    if (Date.now() > item.expiresAt) {
      this.delete(key);
      return null;
    }

    return item.value;
  },

  // Delete cache item
  delete(key) {
    this.data.delete(key);
  },

  // Clear all cache
  clear() {
    this.data.clear();
  },

  // Clear cache items by prefix
  clearByPrefix(prefix) {
    for (const key of this.data.keys()) {
      if (key.startsWith(prefix)) {
        this.delete(key);
      }
    }
  }
};

// Create API clients
// We'll create mock API clients that use the mock data service
const createMockApi = () => {
  return {
    get: async (url, config = {}) => {
      const data = await fetchMockData(url, config.params);
      return { data };
    },
    post: async (url, data) => {
      const result = await createMockItem(url, data);
      return { data: result };
    },
    put: async (url, data) => {
      // Extract ID from URL if it's in the format /path/to/resource/:id
      const id = url.split('/').pop();
      const result = await updateMockItem(url, id, data);
      return { data: result };
    },
    delete: async (url) => {
      // Extract ID from URL if it's in the format /path/to/resource/:id
      const id = url.split('/').pop();
      const result = await deleteMockItem(url, id);
      return { data: result };
    },
    interceptors: {
      request: { use: () => {} },
      response: { use: () => {} }
    }
  };
};

// Create real API clients
const createRealApi = (baseUrl) => {
  const api = axios.create({
    baseURL: baseUrl,
    timeout: API_TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  return api;
};

// Use mock or real API clients based on configuration
const userApi = mockDataUtils.isMockDataEnabled() ? createMockApi() : createRealApi(API_BASE_URL);
const adminApi = mockDataUtils.isMockDataEnabled() ? createMockApi() : createRealApi(API_BASE_URL);

// Request interceptor for user API - adds auth token to requests
userApi.interceptors.request.use(
  (config) => {
    const token = getToken();
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Request interceptor for admin API - adds admin auth token to requests
adminApi.interceptors.request.use(
  (config) => {
    const token = getAdminToken();
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for user API - handles token expiration
userApi.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // If the error is 401 (Unauthorized) and we haven't already tried to refresh the token
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      // For now, just logout the user
      // In a real implementation, you would try to refresh the token first
      await logout();

      // Redirect to login page
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }

      return Promise.reject(error);
    }

    return Promise.reject(error);
  }
);

// Response interceptor for admin API - handles token expiration
adminApi.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // If the error is 401 (Unauthorized) and we haven't already tried to refresh the token
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      // For now, just logout the admin
      // In a real implementation, you would try to refresh the token first
      await adminLogout();

      // Redirect to admin login page
      if (typeof window !== 'undefined') {
        window.location.href = '/admin/login';
      }

      return Promise.reject(error);
    }

    return Promise.reject(error);
  }
);

/**
 * Make a GET request to the user API
 * @param {string} url - The URL to request
 * @param {Object} params - Query parameters
 * @param {Object} options - Additional options
 * @param {boolean} options.useCache - Whether to use cache (default: true)
 * @param {number} options.cacheTTL - Cache TTL in milliseconds (default: 60000 - 1 minute)
 * @param {boolean} options.useRetry - Whether to use retry logic (default: true)
 * @param {number} options.maxRetries - Maximum number of retries (default: 3)
 * @returns {Promise} - Promise with the response
 */
export const get = async (url, params = {}, options = {}) => {
  const {
    useCache = true,
    cacheTTL = 60000,
    useRetry = true,
    maxRetries = 3
  } = options;

  // Generate cache key from URL and params
  const cacheKey = `user:${url}:${JSON.stringify(params)}`;

  // Check cache if enabled
  if (useCache) {
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Cache hit for ${url}`);
      return cachedData;
    }
  }

  // Define the actual API call function
  const makeRequest = async () => {
    try {
      const response = await userApi.get(url, { params });
      const data = response.data;

      // Cache the response if enabled
      if (useCache && data) {
        cache.set(cacheKey, data, cacheTTL);
      }

      return data;
    } catch (error) {
      // Format and log the error
      const formattedError = formatError(error);
      logError(formattedError, `GET ${url}`);

      // Rethrow the error to be handled by the retry logic or caller
      throw error;
    }
  };

  // Use retry logic if enabled, otherwise make a single request
  if (useRetry) {
    return withRetry(makeRequest, {
      maxRetries,
      retryCondition: isNetworkError
    });
  } else {
    return makeRequest();
  }
};

/**
 * Make a POST request to the user API
 * @param {string} url - The URL to request
 * @param {Object} data - The data to send
 * @param {Object} options - Additional options
 * @param {string} options.invalidatePrefix - Cache prefix to invalidate after successful request
 * @param {boolean} options.useRetry - Whether to use retry logic (default: true)
 * @param {number} options.maxRetries - Maximum number of retries (default: 3)
 * @returns {Promise} - Promise with the response
 */
export const post = async (url, data = {}, options = {}) => {
  const {
    invalidatePrefix = null,
    useRetry = true,
    maxRetries = 3
  } = options;

  // Define the actual API call function
  const makeRequest = async () => {
    try {
      const response = await userApi.post(url, data);
      const responseData = response.data;

      // Invalidate cache if prefix is provided
      if (invalidatePrefix) {
        cache.clearByPrefix(`user:${invalidatePrefix}`);
      }

      return responseData;
    } catch (error) {
      // Format and log the error
      const formattedError = formatError(error);
      logError(formattedError, `POST ${url}`);

      // Rethrow the error to be handled by the retry logic or caller
      throw error;
    }
  };

  // Use retry logic if enabled, otherwise make a single request
  if (useRetry) {
    return withRetry(makeRequest, {
      maxRetries,
      retryCondition: isNetworkError
    });
  } else {
    return makeRequest();
  }
};

/**
 * Make a PUT request to the user API
 * @param {string} url - The URL to request
 * @param {Object} data - The data to send
 * @param {Object} options - Additional options
 * @param {string} options.invalidatePrefix - Cache prefix to invalidate after successful request
 * @param {boolean} options.useRetry - Whether to use retry logic (default: true)
 * @param {number} options.maxRetries - Maximum number of retries (default: 3)
 * @returns {Promise} - Promise with the response
 */
export const put = async (url, data = {}, options = {}) => {
  const {
    invalidatePrefix = null,
    useRetry = true,
    maxRetries = 3
  } = options;

  // Define the actual API call function
  const makeRequest = async () => {
    try {
      const response = await userApi.put(url, data);
      const responseData = response.data;

      // Invalidate cache if prefix is provided
      if (invalidatePrefix) {
        cache.clearByPrefix(`user:${invalidatePrefix}`);
      }

      return responseData;
    } catch (error) {
      // Format and log the error
      const formattedError = formatError(error);
      logError(formattedError, `PUT ${url}`);

      // Rethrow the error to be handled by the retry logic or caller
      throw error;
    }
  };

  // Use retry logic if enabled, otherwise make a single request
  if (useRetry) {
    return withRetry(makeRequest, {
      maxRetries,
      retryCondition: isNetworkError
    });
  } else {
    return makeRequest();
  }
};

/**
 * Make a DELETE request to the user API
 * @param {string} url - The URL to request
 * @param {Object} options - Additional options
 * @param {string} options.invalidatePrefix - Cache prefix to invalidate after successful request
 * @param {boolean} options.useRetry - Whether to use retry logic (default: true)
 * @param {number} options.maxRetries - Maximum number of retries (default: 3)
 * @returns {Promise} - Promise with the response
 */
export const del = async (url, options = {}) => {
  const {
    invalidatePrefix = null,
    useRetry = true,
    maxRetries = 3
  } = options;

  // Define the actual API call function
  const makeRequest = async () => {
    try {
      const response = await userApi.delete(url);
      const responseData = response.data;

      // Invalidate cache if prefix is provided
      if (invalidatePrefix) {
        cache.clearByPrefix(`user:${invalidatePrefix}`);
      }

      return responseData;
    } catch (error) {
      // Format and log the error
      const formattedError = formatError(error);
      logError(formattedError, `DELETE ${url}`);

      // Rethrow the error to be handled by the retry logic or caller
      throw error;
    }
  };

  // Use retry logic if enabled, otherwise make a single request
  if (useRetry) {
    return withRetry(makeRequest, {
      maxRetries,
      retryCondition: isNetworkError
    });
  } else {
    return makeRequest();
  }
};

/**
 * Make a GET request to the admin API
 * @param {string} url - The URL to request
 * @param {Object} params - Query parameters
 * @param {Object} options - Additional options
 * @param {boolean} options.useCache - Whether to use cache (default: true)
 * @param {number} options.cacheTTL - Cache TTL in milliseconds (default: 60000 - 1 minute)
 * @param {boolean} options.useMockFallback - Whether to use mock data as fallback (default: true)
 * @returns {Promise} - Promise with the response
 */
export const adminGet = async (url, params = {}, options = {}) => {
  const { useCache = true, cacheTTL = 60000, useMockFallback = true } = options;

  // Generate cache key from URL and params
  const cacheKey = `admin:${url}:${JSON.stringify(params)}`;

  // Check cache if enabled
  if (useCache) {
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Cache hit for admin ${url}`);
      return cachedData;
    }
  }

  try {
    const response = await adminApi.get(url, { params });
    const data = response.data;

    // Cache the response if enabled
    if (useCache && data) {
      cache.set(cacheKey, data, cacheTTL);
    }

    return data;
  } catch (error) {
    console.error(`Admin GET request error for ${url}:`, error.response?.data || error.message);

    // If mock fallback is enabled and we're not already using mock data, try to fetch mock data
    if (useMockFallback && !mockDataUtils.isMockDataEnabled()) {
      console.log(`Falling back to mock data for ${url}`);
      try {
        const mockData = await fetchMockData(url, params);
        return mockData;
      } catch (mockError) {
        console.error(`Mock data fallback error for ${url}:`, mockError);
        throw error; // Throw the original error if mock fallback fails
      }
    }

    throw error;
  }
};

/**
 * Make a POST request to the admin API
 * @param {string} url - The URL to request
 * @param {Object} data - The data to send
 * @param {Object} options - Additional options
 * @param {string} options.invalidatePrefix - Cache prefix to invalidate after successful request
 * @returns {Promise} - Promise with the response
 */
export const adminPost = async (url, data = {}, options = {}) => {
  const { invalidatePrefix = null } = options;

  try {
    const response = await adminApi.post(url, data);
    const responseData = response.data;

    // Invalidate cache if prefix is provided
    if (invalidatePrefix) {
      cache.clearByPrefix(`admin:${invalidatePrefix}`);
    }

    return responseData;
  } catch (error) {
    console.error(`Admin POST request error for ${url}:`, error.response?.data || error.message);
    throw error;
  }
};

/**
 * Make a PUT request to the admin API
 * @param {string} url - The URL to request
 * @param {Object} data - The data to send
 * @param {Object} options - Additional options
 * @param {string} options.invalidatePrefix - Cache prefix to invalidate after successful request
 * @returns {Promise} - Promise with the response
 */
export const adminPut = async (url, data = {}, options = {}) => {
  const { invalidatePrefix = null, useMockFallback = false } = options;

  try {
    const response = await adminApi.put(url, data);
    const responseData = response.data;

    // Invalidate cache if prefix is provided
    if (invalidatePrefix) {
      cache.clearByPrefix(`admin:${invalidatePrefix}`);
    }

    return responseData;
  } catch (error) {
    console.error(`Admin PUT request error for ${url}:`, error.response?.data || error.message);

    // If mock fallback is enabled and we're not already using mock data, try to return success response
    if (useMockFallback && !mockDataUtils.isMockDataEnabled()) {
      console.log(`Falling back to mock success response for PUT ${url}`);
      return {
        success: true,
        message: 'Settings updated successfully (mock mode)',
        data: data
      };
    }

    throw error;
  }
};

/**
 * Make a DELETE request to the admin API
 * @param {string} url - The URL to request
 * @param {Object} options - Additional options
 * @param {string} options.invalidatePrefix - Cache prefix to invalidate after successful request
 * @returns {Promise} - Promise with the response
 */
export const adminDel = async (url, options = {}) => {
  const { invalidatePrefix = null } = options;

  try {
    const response = await adminApi.delete(url);
    const responseData = response.data;

    // Invalidate cache if prefix is provided
    if (invalidatePrefix) {
      cache.clearByPrefix(`admin:${invalidatePrefix}`);
    }

    return responseData;
  } catch (error) {
    console.error(`Admin DELETE request error for ${url}:`, error.response?.data || error.message);
    throw error;
  }
};

// Export the axios instances for direct use if needed
export { userApi, adminApi };
