import Head from 'next/head';
import Link from 'next/link';

export default function ApiToolsReadme() {
  return (
    <div className="container">
      <Head>
        <title>API Tools Documentation - Vaivahik Admin</title>
        <meta name="description" content="Documentation for API tools" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="main">
        <h1 className="title">API Tools Documentation</h1>

        <div className="content">
          <section className="section">
            <h2>Overview</h2>
            <p>
              The Vaivahik Admin Panel includes two powerful tools to help you work with the API:
            </p>
            <ul>
              <li><strong>API Viewer</strong> - For viewing responses from specific API endpoints</li>
              <li><strong>API Discovery</strong> - For discovering and testing available API endpoints</li>
            </ul>
            <p>
              These tools are designed to help developers and administrators understand the API structure,
              test endpoints, and debug issues.
            </p>
          </section>

          <section className="section">
            <h2>API Viewer</h2>
            <p>
              The API Viewer allows you to fetch and view data from any API endpoint in a formatted JSON view.
            </p>

            <h3>How to Use:</h3>
            <ol>
              <li>Enter the API endpoint URL in the input field (e.g., <code>/api/admin/premium-plans</code>)</li>
              <li>Click "Fetch Data" to retrieve the response</li>
              <li>The response will be displayed in a formatted JSON view</li>
            </ol>

            <h3>Features:</h3>
            <ul>
              <li>Formatted JSON display for easy reading</li>
              <li>Error handling for failed requests</li>
              <li>URL parameter support for bookmarking specific endpoints</li>
            </ul>

            <div className="button-container">
              <Link href="/api-viewer" className="button">
                Go to API Viewer
              </Link>
            </div>
          </section>

          <section className="section">
            <h2>API Discovery Tool</h2>
            <p>
              The API Discovery Tool helps you identify working API endpoints and test different HTTP methods.
            </p>

            <h3>How to Use:</h3>
            <ol>
              <li>Enter the API base URL (default is the current host)</li>
              <li>Click "Check Server Status" to verify the server is running</li>
              <li>Click "Discover GET Endpoints" to test common GET endpoints</li>
              <li>Click "Test PUT/POST Endpoints" to test update operations</li>
              <li>View the results in the results panel</li>
            </ol>

            <h3>Features:</h3>
            <ul>
              <li>Server status checking</li>
              <li>Automatic testing of common API endpoints</li>
              <li>Support for testing GET, POST, and PUT methods</li>
              <li>Detailed results with status codes and response snippets</li>
              <li>Error handling and suggestions for troubleshooting</li>
            </ul>

            <div className="button-container">
              <Link href="/api-discovery" className="button">
                Go to API Discovery Tool
              </Link>
            </div>
          </section>

          <section className="section">
            <h2>Common API Endpoints</h2>
            <p>
              Here are some common API endpoints available in the Vaivahik system:
            </p>

            <h3>Admin Endpoints:</h3>
            <ul>
              <li><code>/api/admin/users</code> - Get all users</li>
              <li><code>/api/admin/dashboard</code> - Get dashboard data</li>

              <li><strong>Verification Queue:</strong></li>
              <ul>
                <li><code>/api/admin/verification-queue</code> - Get verification queue</li>
                <li><code>/api/admin/verification-queue/[id]</code> - Get specific verification request</li>
                <li><code>/api/admin/verification-queue/approve</code> - Approve verification (POST)</li>
                <li><code>/api/admin/verification-queue/reject</code> - Reject verification (POST)</li>
              </ul>

              <li><strong>Reported Profiles:</strong></li>
              <ul>
                <li><code>/api/admin/reported-profiles</code> - Get reported profiles</li>
                <li><code>/api/admin/reported-profiles/[id]</code> - Get specific report</li>
                <li><code>/api/admin/reported-profiles/resolve</code> - Resolve report (POST)</li>
                <li><code>/api/admin/reported-profiles/dismiss</code> - Dismiss report (POST)</li>
              </ul>

              <li><strong>Premium Plans:</strong></li>
              <ul>
                <li><code>/api/admin/premium-plans</code> - Get all premium plans</li>
                <li><code>/api/admin/premium-plans/[id]</code> - Get, update or delete specific plan</li>
              </ul>

              <li><strong>Features:</strong></li>
              <ul>
                <li><code>/api/admin/features</code> - Get all features</li>
                <li><code>/api/admin/features/[id]</code> - Get, update or delete specific feature</li>
                <li><code>/api/admin/features/[id]/activate</code> - Activate feature</li>
                <li><code>/api/admin/features/[id]/deactivate</code> - Deactivate feature</li>
                <li><code>/api/admin/feature-management</code> - Get feature management settings</li>
              </ul>

              <li><strong>Other Admin Endpoints:</strong></li>
              <ul>
                <li><code>/api/admin/algorithm-settings</code> - Get/update algorithm settings</li>
                <li><code>/api/admin/preference-config</code> - Get/update preference configurations</li>
                <li><code>/api/admin/success-analytics</code> - Get success analytics</li>
                <li><code>/api/admin/subscriptions</code> - Get subscription data</li>
                <li><code>/api/admin/transactions</code> - Get transaction data</li>
                <li><code>/api/admin/revenue-reports</code> - Get revenue reports</li>
                <li><code>/api/admin/settings</code> - Get/update system settings</li>
                <li><code>/api/admin/admin-users</code> - Manage admin users</li>
              </ul>
            </ul>

            <h3>User Endpoints:</h3>
            <ul>
              <li><strong>Authentication:</strong></li>
              <ul>
                <li><code>/api/auth/login</code> - User login (POST)</li>
                <li><code>/api/auth/register</code> - User registration (POST)</li>
                <li><code>/api/auth/send-otp</code> - Send OTP for verification (POST)</li>
                <li><code>/api/auth/verify-otp</code> - Verify OTP (POST)</li>
              </ul>

              <li><strong>User Profile:</strong></li>
              <ul>
                <li><code>/api/users</code> - Get user data</li>
                <li><code>/api/users/profile</code> - Get/update user profile</li>
                <li><code>/api/users/photos</code> - Upload profile photos</li>
              </ul>

              <li><strong>Matrimony Features:</strong></li>
              <ul>
                <li><code>/api/profiles</code> - Get profiles</li>
                <li><code>/api/matches</code> - Get matches</li>
                <li><code>/api/preferences</code> - Get/update preferences</li>
                <li><code>/api/documents</code> - Upload verification documents</li>
                <li><code>/api/subscriptions</code> - Manage subscriptions</li>
                <li><code>/api/payments</code> - Payment history and processing</li>
                <li><code>/api/notifications</code> - User notifications</li>
                <li><code>/api/messages</code> - User messages</li>
              </ul>
            </ul>

            <h3>System Endpoints:</h3>
            <ul>
              <li><code>/api/health</code> - System health check</li>
              <li><code>/api/status</code> - System status</li>
              <li><code>/api/version</code> - API version information</li>
              <li><code>/api/hello</code> - Basic test endpoint</li>
            </ul>
          </section>

          <section className="section">
            <h2>Troubleshooting</h2>
            <p>
              If you encounter issues with the API tools, here are some common solutions:
            </p>

            <h3>Common Issues:</h3>
            <ul>
              <li>
                <strong>404 Not Found</strong> - The endpoint doesn't exist or isn't properly configured.
                Check the URL and make sure the API route is defined.
              </li>
              <li>
                <strong>401 Unauthorized</strong> - Authentication is required. Make sure you're logged in
                as an admin user.
              </li>
              <li>
                <strong>500 Internal Server Error</strong> - There's an error on the server side.
                Check the server logs for more details.
              </li>
              <li>
                <strong>CORS Error</strong> - If testing from a different domain, make sure CORS is properly
                configured on the server.
              </li>
            </ul>
          </section>

          <div className="button-container center">
            <Link href="/admin/dashboard" className="button secondary">
              Back to Dashboard
            </Link>
          </div>
        </div>
      </main>

      <style jsx>{`
        .container {
          min-height: 100vh;
          padding: 0 2rem;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: center;
          background-color: #f5f5f5;
        }

        .main {
          padding: 4rem 0;
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: center;
          width: 100%;
          max-width: 900px;
        }

        .title {
          margin: 0 0 2rem;
          line-height: 1.15;
          font-size: 2.5rem;
          text-align: center;
          color: #333;
        }

        .content {
          width: 100%;
          background-color: white;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          padding: 2rem;
        }

        .section {
          margin-bottom: 2.5rem;
          border-bottom: 1px solid #f0f0f0;
          padding-bottom: 1.5rem;
        }

        .section:last-child {
          border-bottom: none;
          margin-bottom: 1rem;
        }

        h2 {
          color: #5e35b1;
          margin-top: 0;
          margin-bottom: 1rem;
          font-size: 1.75rem;
        }

        h3 {
          color: #333;
          margin-top: 1.5rem;
          margin-bottom: 0.75rem;
          font-size: 1.25rem;
        }

        p {
          margin: 0.75rem 0;
          line-height: 1.6;
          color: #444;
        }

        ul, ol {
          margin: 1rem 0;
          padding-left: 1.5rem;
        }

        li {
          margin-bottom: 0.5rem;
          line-height: 1.6;
          color: #444;
        }

        code {
          background-color: #f5f5f5;
          padding: 0.2rem 0.4rem;
          border-radius: 3px;
          font-family: 'Consolas', 'Monaco', monospace;
          font-size: 0.9em;
          color: #e91e63;
        }

        .button-container {
          margin-top: 1.5rem;
        }

        .button-container.center {
          display: flex;
          justify-content: center;
        }

        .button {
          display: inline-block;
          background-color: #5e35b1;
          color: white;
          padding: 0.75rem 1.5rem;
          border-radius: 4px;
          text-decoration: none;
          font-weight: 500;
          transition: all 0.2s ease;
        }

        .button:hover {
          background-color: #4527a0;
          transform: translateY(-2px);
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        .button.secondary {
          background-color: #757575;
        }

        .button.secondary:hover {
          background-color: #616161;
        }

        @media (max-width: 768px) {
          .main {
            padding: 2rem 0;
          }

          .content {
            padding: 1.5rem;
          }

          .title {
            font-size: 2rem;
          }

          h2 {
            font-size: 1.5rem;
          }

          h3 {
            font-size: 1.2rem;
          }
        }
      `}</style>
    </div>
  );
}
