/**
 * Error Handler Middleware
 *
 * This middleware provides centralized error handling for the application.
 * It formats errors in a consistent way and sends appropriate responses.
 */

const { ValidationError, NotFoundError, UnauthorizedError, ForbiddenError } = require('../utils/errors');
const { trackError } = require('../services/errorAnalytics.service');

/**
 * Global error handler middleware
 * @param {Error} err - The error object
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 */
const errorHandler = (err, req, res, next) => {
  // Default status code and message
  let statusCode = err.status || 500;
  let message = err.message || 'Internal Server Error';
  let errors = err.errors || null;

  // Log the error
  console.error(`[ERROR] ${statusCode} - ${message}`, err);

  // Track the error for analytics
  trackError(err, { req });

  // Handle specific error types
  if (err instanceof ValidationError) {
    statusCode = err.status;
    message = err.message;
    errors = err.errors;
  } else if (err instanceof NotFoundError) {
    statusCode = err.status;
    message = err.message;
  } else if (err instanceof UnauthorizedError || err.name === 'TokenExpiredError') {
    statusCode = 401;
    message = err.message || 'Unauthorized: Invalid or expired token';
  } else if (err instanceof ForbiddenError) {
    statusCode = err.status;
    message = err.message;
  } else if (err.code === 'P2002') {
    // Prisma unique constraint violation
    statusCode = 409;
    message = 'Conflict: Resource already exists';
    const field = err.meta?.target?.join(', ');
    errors = { [field]: `The ${field} is already in use` };
  } else if (err.code === 'P2025') {
    // Prisma record not found
    statusCode = 404;
    message = 'Resource not found';
  }

  // Send response
  res.status(statusCode).json({
    success: false,
    message,
    errors,
    // Include stack trace in development environment
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
};

/**
 * Create a validation error
 * @param {string} message - Error message
 * @param {object} errors - Validation errors
 * @returns {Error} - Validation error
 */
const createValidationError = (message, errors) => {
  const error = new Error(message || 'Validation failed');
  error.name = 'ValidationError';
  error.status = 422;
  error.errors = errors;
  return error;
};

/**
 * Create a not found error
 * @param {string} message - Error message
 * @returns {Error} - Not found error
 */
const createNotFoundError = (message) => {
  const error = new Error(message || 'Resource not found');
  error.name = 'NotFoundError';
  error.status = 404;
  return error;
};

/**
 * Create an unauthorized error
 * @param {string} message - Error message
 * @returns {Error} - Unauthorized error
 */
const createUnauthorizedError = (message) => {
  const error = new Error(message || 'Unauthorized');
  error.name = 'UnauthorizedError';
  error.status = 401;
  return error;
};

/**
 * Create a forbidden error
 * @param {string} message - Error message
 * @returns {Error} - Forbidden error
 */
const createForbiddenError = (message) => {
  const error = new Error(message || 'Forbidden');
  error.name = 'ForbiddenError';
  error.status = 403;
  return error;
};

module.exports = {
  errorHandler,
  createValidationError,
  createNotFoundError,
  createUnauthorizedError,
  createForbiddenError
};
