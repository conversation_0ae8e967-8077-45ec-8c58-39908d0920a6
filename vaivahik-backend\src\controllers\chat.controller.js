// src/controllers/chat.controller.js

const ChatSettings = require('../models/ChatSettings');
const PromotionSettings = require('../models/PromotionSettings');
const textModerationService = require('../services/textModeration.service');
const { invalidateConversationCache } = require('../../redis/cacheService');

/**
 * @description Get all conversations for a user
 * @route GET /api/users/conversations
 */
exports.getConversations = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;
    const { page = 1, limit = 10 } = req.query;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const take = parseInt(limit);

        // Get conversations where the user is either user1 or user2
        const conversations = await prisma.conversation.findMany({
            where: {
                OR: [
                    { user1Id: userId },
                    { user2Id: userId }
                ],
                isActive: true
            },
            include: {
                user1: {
                    select: {
                        id: true,
                        profile: {
                            select: {
                                fullName: true
                            }
                        },
                        photos: {
                            where: {
                                isProfilePic: true,
                                status: 'APPROVED'
                            },
                            select: {
                                url: true
                            },
                            take: 1
                        }
                    }
                },
                user2: {
                    select: {
                        id: true,
                        profile: {
                            select: {
                                fullName: true
                            }
                        },
                        photos: {
                            where: {
                                isProfilePic: true,
                                status: 'APPROVED'
                            },
                            select: {
                                url: true
                            },
                            take: 1
                        }
                    }
                },
                messages: {
                    orderBy: {
                        sentAt: 'desc'
                    },
                    take: 1
                }
            },
            orderBy: {
                lastMessageAt: 'desc'
            },
            skip,
            take
        });

        // Count total conversations
        const totalConversations = await prisma.conversation.count({
            where: {
                OR: [
                    { user1Id: userId },
                    { user2Id: userId }
                ],
                isActive: true
            }
        });

        // Format conversations for response
        const formattedConversations = conversations.map(conversation => {
            // Determine the other user in the conversation
            const otherUser = conversation.user1Id === userId ? conversation.user2 : conversation.user1;

            return {
                id: conversation.id,
                otherUser: {
                    id: otherUser.id,
                    name: otherUser.profile?.fullName || 'User',
                    profilePicture: otherUser.photos[0]?.url || null
                },
                lastMessage: conversation.messages[0] || null,
                lastMessageAt: conversation.lastMessageAt,
                unreadCount: 0 // Will be calculated in a separate query
            };
        });

        // Get unread message counts for each conversation
        for (const conversation of formattedConversations) {
            const unreadCount = await prisma.message.count({
                where: {
                    conversationId: conversation.id,
                    receiverId: userId,
                    isRead: false
                }
            });
            conversation.unreadCount = unreadCount;
        }

        res.status(200).json({
            success: true,
            conversations: formattedConversations,
            pagination: {
                currentPage: parseInt(page),
                totalPages: Math.ceil(totalConversations / take),
                totalItems: totalConversations,
                itemsPerPage: take
            }
        });
    } catch (error) {
        console.error('Error fetching conversations:', error);
        next(error);
    }
};

/**
 * @description Get messages for a specific conversation
 * @route GET /api/users/conversations/:conversationId/messages
 */
exports.getMessages = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;
    const { conversationId } = req.params;
    const { page = 1, limit = 20 } = req.query;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Verify the user is part of this conversation
        const conversation = await prisma.conversation.findFirst({
            where: {
                id: conversationId,
                OR: [
                    { user1Id: userId },
                    { user2Id: userId }
                ]
            }
        });

        if (!conversation) {
            return res.status(403).json({
                success: false,
                message: "You don't have access to this conversation."
            });
        }

        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const take = parseInt(limit);

        // Get messages for this conversation
        const messages = await prisma.message.findMany({
            where: {
                conversationId
            },
            orderBy: {
                sentAt: 'desc'
            },
            skip,
            take
        });

        // Count total messages
        const totalMessages = await prisma.message.count({
            where: {
                conversationId
            }
        });

        // Mark messages as read if the user is the receiver
        await prisma.message.updateMany({
            where: {
                conversationId,
                receiverId: userId,
                isRead: false
            },
            data: {
                isRead: true,
                readAt: new Date()
            }
        });

        res.status(200).json({
            success: true,
            messages: messages.reverse(), // Return in chronological order
            pagination: {
                currentPage: parseInt(page),
                totalPages: Math.ceil(totalMessages / take),
                totalItems: totalMessages,
                itemsPerPage: take
            }
        });
    } catch (error) {
        console.error('Error fetching messages:', error);
        next(error);
    }
};

/**
 * @description Send a message in a conversation
 * @route POST /api/users/conversations/:conversationId/messages
 */
exports.sendMessage = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;
    const { conversationId } = req.params;
    const { content, messageType = 'TEXT', metadata = null } = req.body;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    if (!content || content.trim() === '') {
        return res.status(400).json({
            success: false,
            message: 'Message content cannot be empty.'
        });
    }

    try {
        // Verify the user is part of this conversation
        const conversation = await prisma.conversation.findFirst({
            where: {
                id: conversationId,
                OR: [
                    { user1Id: userId },
                    { user2Id: userId }
                ]
            }
        });

        if (!conversation) {
            return res.status(403).json({
                success: false,
                message: "You don't have access to this conversation."
            });
        }

        // Determine the receiver
        const receiverId = conversation.user1Id === userId ? conversation.user2Id : conversation.user1Id;

        // Check message length limit
        if (content.length > ChatSettings.maxMessageLength) {
            return res.status(400).json({
                success: false,
                message: `Message exceeds maximum length of ${ChatSettings.maxMessageLength} characters.`
            });
        }

        // Get user info for moderation settings
        const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { isVerified: true, isPremium: true, createdAt: true }
        });

        // Get user's tier for moderation settings
        let userTier = 'BASIC';
        if (user.isVerified) userTier = 'VERIFIED';
        if (user.isPremium) userTier = 'PREMIUM';

        // Get tier-specific moderation settings with any active promotion overrides
        const moderationSettings = ChatSettings.getModerationSettingsForTier(userTier);

        // Moderate the message content
        const moderationResult = await textModerationService.moderateText(content, {
            userId,
            strictness: moderationSettings.strictness,
            autoReject: moderationSettings.autoReject,
            maskProfanity: moderationSettings.maskProfanity,
            allowContactInfo: moderationSettings.allowContactInfo,
            allowedContactTypes: moderationSettings.allowedContactTypes
        });

        // If message is rejected by moderation
        if (!moderationResult.isApproved) {
            return res.status(400).json({
                success: false,
                message: 'Your message contains inappropriate content and cannot be sent.',
                moderationFlags: moderationResult.flags
            });
        }

        // Use moderated text if available
        const finalContent = moderationResult.moderatedText || content;

        // Create the message with moderation info
        const message = await prisma.message.create({
            data: {
                senderId: userId,
                receiverId,
                conversationId,
                content: finalContent,
                messageType,
                metadata: metadata ? JSON.stringify(metadata) : null,
                sentAt: new Date(),
                // Add moderation fields
                isModerated: moderationResult.flags.length > 0,
                moderationStatus: moderationResult.isApproved ? 'APPROVED' : 'REJECTED',
                moderatedContent: moderationResult.moderatedText,
                moderationFlags: moderationResult.flags.join(',')
            }
        });

        // Update the conversation's lastMessageAt
        await prisma.conversation.update({
            where: { id: conversationId },
            data: {
                lastMessageAt: new Date(),
                isActive: true
            }
        });

        // Update user's messagesSent count
        await prisma.user.update({
            where: { id: userId },
            data: {
                messagesSent: {
                    increment: 1
                }
            }
        });

        // Create a notification for the receiver
        await prisma.notification.create({
            data: {
                userId: receiverId,
                title: 'New Message',
                message: 'You have received a new message',
                type: 'MESSAGE_RECEIVED',
                actionUrl: `/messages/${conversationId}`
            }
        });

        // Invalidate conversation cache
        await invalidateConversationCache(conversationId);

        res.status(201).json({
            success: true,
            message
        });
    } catch (error) {
        console.error('Error sending message:', error);
        next(error);
    }
};

/**
 * @description Start a new conversation with another user
 * @route POST /api/users/conversations
 */
exports.startConversation = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;
    const { receiverId, initialMessage } = req.body;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    if (!receiverId) {
        return res.status(400).json({
            success: false,
            message: 'Receiver ID is required.'
        });
    }

    if (userId === receiverId) {
        return res.status(400).json({
            success: false,
            message: 'You cannot start a conversation with yourself.'
        });
    }

    try {
        // Check if users are matched or connected
        const match = await prisma.match.findFirst({
            where: {
                OR: [
                    { user1Id: userId, user2Id: receiverId },
                    { user1Id: receiverId, user2Id: userId }
                ],
                status: 'ACCEPTED'
            }
        });

        if (!match) {
            return res.status(403).json({
                success: false,
                message: "You can only start conversations with users you've matched with."
            });
        }

        // Check if a conversation already exists
        let conversation = await prisma.conversation.findFirst({
            where: {
                OR: [
                    { user1Id: userId, user2Id: receiverId },
                    { user1Id: receiverId, user2Id: userId }
                ]
            }
        });

        // If no conversation exists, create one
        if (!conversation) {
            conversation = await prisma.conversation.create({
                data: {
                    user1Id: userId,
                    user2Id: receiverId,
                    lastMessageAt: initialMessage ? new Date() : null
                }
            });
        } else {
            // If conversation exists but is inactive, reactivate it
            if (!conversation.isActive) {
                conversation = await prisma.conversation.update({
                    where: { id: conversation.id },
                    data: { isActive: true }
                });
            }
        }

        // If an initial message was provided, send it
        if (initialMessage && initialMessage.trim() !== '') {
            await prisma.message.create({
                data: {
                    senderId: userId,
                    receiverId,
                    conversationId: conversation.id,
                    content: initialMessage,
                    messageType: 'TEXT',
                    sentAt: new Date()
                }
            });

            // Update the conversation's lastMessageAt
            await prisma.conversation.update({
                where: { id: conversation.id },
                data: { lastMessageAt: new Date() }
            });

            // Update user's messagesSent count
            await prisma.user.update({
                where: { id: userId },
                data: {
                    messagesSent: {
                        increment: 1
                    }
                }
            });

            // Create a notification for the receiver
            await prisma.notification.create({
                data: {
                    userId: receiverId,
                    title: 'New Conversation',
                    message: 'Someone has started a conversation with you',
                    type: 'CONVERSATION_STARTED',
                    actionUrl: `/messages/${conversation.id}`
                }
            });

            // Invalidate conversation cache
            await invalidateConversationCache(conversation.id);
        }

        res.status(201).json({
            success: true,
            conversation
        });
    } catch (error) {
        console.error('Error starting conversation:', error);
        next(error);
    }
};

/**
 * @description Get chat settings
 * @route GET /api/users/chat-settings
 */
exports.getChatSettings = async (req, res, next) => {
    const userId = req.user?.userId;
    const prisma = req.prisma;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Get user's verification and premium status
        const user = await prisma.user.findUnique({
            where: { id: userId },
            select: {
                isVerified: true,
                isPremium: true,
                subscriptions: {
                    where: {
                        isActive: true,
                        endDate: { gt: new Date() }
                    },
                    select: {
                        planType: true
                    },
                    take: 1
                }
            }
        });

        // Determine user tier
        let userTier = 'BASIC';
        if (user.isVerified) userTier = 'VERIFIED';
        if (user.isPremium || (user.subscriptions && user.subscriptions.length > 0)) userTier = 'PREMIUM';

        // Get tier-specific settings with promotions applied
        const tierSettings = ChatSettings.getTierSettings(userTier, user.createdAt);

        // Get active promotions
        const activePromotions = ChatSettings.getActivePromotions();

        // Prepare client-side settings
        const clientSettings = {
            enableReadReceipts: ChatSettings.enableReadReceipts,
            enableTypingIndicators: ChatSettings.enableTypingIndicators,
            enableMessageReactions: ChatSettings.enableMessageReactions,
            enableFileSharing: userTier === 'PREMIUM' && ChatSettings.enableFileSharing,
            maxMessageLength: ChatSettings.maxMessageLength,
            dailyMessageLimit: tierSettings.dailyMessageLimit,
            canStartNewConversations: tierSettings.canStartNewConversations,
            canSendImages: tierSettings.canSendImages,
            messageRetentionDays: tierSettings.messageRetentionDays,
            userTier,
            isPremium: userTier === 'PREMIUM',
            allowedFileTypes: ChatSettings.allowedFileTypes,
            maxFileSize: ChatSettings.maxFileSize,
            aiFeatures: {
                smartReplies: userTier === 'PREMIUM' && ChatSettings.aiFeatures.smartReplies.enabled,
                contentModeration: ChatSettings.aiFeatures.contentModeration.enabled,
                translationSupport: userTier === 'PREMIUM' && ChatSettings.aiFeatures.translationSupport.enabled
            },
            // Add promotion information
            promotions: {
                hasActivePromotions: Object.keys(activePromotions).length > 0,
                activePromotions: Object.entries(activePromotions).map(([name, promo]) => ({
                    name,
                    title: promo.display.title,
                    description: promo.display.description,
                    endDate: promo.endDate,
                    bannerColor: promo.display.bannerColor,
                    bannerTextColor: promo.display.bannerTextColor,
                    showCountdown: promo.display.showCountdown
                }))
            }
        };

        res.status(200).json({
            success: true,
            settings: clientSettings
        });
    } catch (error) {
        console.error('Error fetching chat settings:', error);
        next(error);
    }
};

/**
 * @description Mark messages as read
 * @route PUT /api/users/conversations/:conversationId/read
 */
exports.markMessagesAsRead = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;
    const { conversationId } = req.params;
    const { messageIds } = req.body;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Verify the user is part of this conversation
        const conversation = await prisma.conversation.findFirst({
            where: {
                id: conversationId,
                OR: [
                    { user1Id: userId },
                    { user2Id: userId }
                ]
            }
        });

        if (!conversation) {
            return res.status(403).json({
                success: false,
                message: "You don't have access to this conversation."
            });
        }

        // Build the query to mark messages as read
        const whereClause = {
            conversationId,
            receiverId: userId,
            isRead: false
        };

        // If specific message IDs were provided, add them to the query
        if (messageIds && messageIds.length > 0) {
            whereClause.id = { in: messageIds };
        }

        // Mark messages as read
        const result = await prisma.message.updateMany({
            where: whereClause,
            data: {
                isRead: true,
                readAt: new Date()
            }
        });

        // Invalidate conversation cache
        await invalidateConversationCache(conversationId);

        res.status(200).json({
            success: true,
            messagesMarkedAsRead: result.count
        });
    } catch (error) {
        console.error('Error marking messages as read:', error);
        next(error);
    }
};

/**
 * @description Delete a conversation (soft delete)
 * @route DELETE /api/users/conversations/:conversationId
 */
exports.deleteConversation = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;
    const { conversationId } = req.params;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Verify the user is part of this conversation
        const conversation = await prisma.conversation.findFirst({
            where: {
                id: conversationId,
                OR: [
                    { user1Id: userId },
                    { user2Id: userId }
                ]
            }
        });

        if (!conversation) {
            return res.status(403).json({
                success: false,
                message: "You don't have access to this conversation."
            });
        }

        // Soft delete the conversation by marking it as inactive
        await prisma.conversation.update({
            where: { id: conversationId },
            data: { isActive: false }
        });

        // Invalidate conversation cache
        await invalidateConversationCache(conversationId);

        res.status(200).json({
            success: true,
            message: 'Conversation deleted successfully.'
        });
    } catch (error) {
        console.error('Error deleting conversation:', error);
        next(error);
    }
};

/**
 * @description Get unread message count
 * @route GET /api/users/unread-messages
 */
exports.getUnreadMessageCount = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Count unread messages
        const unreadCount = await prisma.message.count({
            where: {
                receiverId: userId,
                isRead: false
            }
        });

        // Count conversations with unread messages
        const conversationsWithUnread = await prisma.conversation.count({
            where: {
                OR: [
                    { user1Id: userId },
                    { user2Id: userId }
                ],
                isActive: true,
                messages: {
                    some: {
                        receiverId: userId,
                        isRead: false
                    }
                }
            }
        });

        res.status(200).json({
            success: true,
            unreadCount,
            conversationsWithUnread
        });
    } catch (error) {
        console.error('Error getting unread message count:', error);
        next(error);
    }
};
