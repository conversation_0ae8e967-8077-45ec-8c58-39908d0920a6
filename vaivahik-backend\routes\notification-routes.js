/**
 * Notification Routes
 *
 * This file contains all routes related to notifications.
 */
const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const notificationService = require('../services/notification/notification-service');
const notificationScheduler = require('../services/notification/notification-scheduler');
const { authenticateUser, authenticateAdmin } = require('../middleware/auth');

/**
 * @route GET /api/notifications
 * @desc Get all notifications for the authenticated user
 * @access Private
 */
router.get('/', authenticateUser, async (req, res) => {
  try {
    const notifications = await prisma.notification.findMany({
      where: {
        userId: req.user.id
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.json(notifications);
  } catch (error) {
    console.error('Error fetching notifications:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route PUT /api/notifications/:id/read
 * @desc Mark a notification as read
 * @access Private
 */
router.put('/:id/read', authenticateUser, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if notification exists and belongs to user
    const notification = await prisma.notification.findFirst({
      where: {
        id,
        userId: req.user.id
      }
    });

    if (!notification) {
      return res.status(404).json({ message: 'Notification not found' });
    }

    // Update notification
    const updatedNotification = await prisma.notification.update({
      where: { id },
      data: {
        isRead: true,
        readAt: new Date()
      }
    });

    res.json(updatedNotification);
  } catch (error) {
    console.error('Error marking notification as read:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route PUT /api/notifications/read-all
 * @desc Mark all notifications as read for the authenticated user
 * @access Private
 */
router.put('/read-all', authenticateUser, async (req, res) => {
  try {
    // Update all unread notifications
    await prisma.notification.updateMany({
      where: {
        userId: req.user.id,
        isRead: false
      },
      data: {
        isRead: true,
        readAt: new Date()
      }
    });

    res.json({ message: 'All notifications marked as read' });
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route DELETE /api/notifications/:id
 * @desc Delete a notification
 * @access Private
 */
router.delete('/:id', authenticateUser, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if notification exists and belongs to user
    const notification = await prisma.notification.findFirst({
      where: {
        id,
        userId: req.user.id
      }
    });

    if (!notification) {
      return res.status(404).json({ message: 'Notification not found' });
    }

    // Delete notification
    await prisma.notification.delete({
      where: { id }
    });

    res.json({ message: 'Notification deleted' });
  } catch (error) {
    console.error('Error deleting notification:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/notifications/fcm-token
 * @desc Register FCM token for the authenticated user
 * @access Private
 */
router.post('/fcm-token', authenticateUser, async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({ message: 'Token is required' });
    }

    // Get user's current tokens
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: { fcmTokens: true }
    });

    // Add token if it doesn't exist
    if (!user.fcmTokens.includes(token)) {
      await prisma.user.update({
        where: { id: req.user.id },
        data: {
          fcmTokens: {
            push: token
          }
        }
      });
    }

    res.json({ message: 'FCM token registered successfully' });
  } catch (error) {
    console.error('Error registering FCM token:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route DELETE /api/notifications/fcm-token
 * @desc Remove FCM token for the authenticated user
 * @access Private
 */
router.delete('/fcm-token', authenticateUser, async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({ message: 'Token is required' });
    }

    // Get user's current tokens
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: { fcmTokens: true }
    });

    // Remove token if it exists
    if (user.fcmTokens.includes(token)) {
      const updatedTokens = user.fcmTokens.filter(t => t !== token);

      await prisma.user.update({
        where: { id: req.user.id },
        data: {
          fcmTokens: updatedTokens
        }
      });
    }

    res.json({ message: 'FCM token removed successfully' });
  } catch (error) {
    console.error('Error removing FCM token:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Admin routes for managing notifications

/**
 * @route POST /api/notifications/admin/send
 * @desc Send a notification to a user or topic
 * @access Admin
 */
router.post('/admin/send', authenticateAdmin, async (req, res) => {
  try {
    const { targetType, targetId, notificationType, data } = req.body;

    if (!targetType || !notificationType || !data) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    // Get the notification template
    const template = notificationService.templates[`${notificationType}Template`];

    if (!template) {
      return res.status(400).json({ message: `Template not found for notification type: ${notificationType}` });
    }

    // Create the notification content
    const notificationContent = template(data);

    // Send the notification based on target type
    let result;
    if (targetType === 'USER') {
      if (!targetId) {
        return res.status(400).json({ message: 'Target ID is required for USER target type' });
      }

      result = await notificationService.sendToUser(targetId, notificationContent);
    } else if (targetType === 'TOPIC') {
      if (!targetId) {
        return res.status(400).json({ message: 'Target ID is required for TOPIC target type' });
      }

      result = await notificationService.sendToTopic(targetId, notificationContent);
    } else if (['ALL_USERS', 'PREMIUM_USERS', 'FREE_USERS', 'VERIFIED_USERS'].includes(targetType)) {
      // Build the where clause based on target type
      const whereClause = {
        fcmTokens: {
          isEmpty: false
        }
      };

      // Add filters based on target type
      if (targetType === 'PREMIUM_USERS') {
        whereClause.isPremium = true;
      } else if (targetType === 'FREE_USERS') {
        whereClause.isPremium = false;
      } else if (targetType === 'VERIFIED_USERS') {
        whereClause.isVerified = true;
      }

      // Get filtered users
      const users = await prisma.user.findMany({
        where: whereClause,
        select: { id: true }
      });

      const userIds = users.map(user => user.id);
      result = await notificationService.sendToUsers(userIds, notificationContent);
    } else {
      return res.status(400).json({ message: `Invalid target type: ${targetType}` });
    }

    res.json(result);
  } catch (error) {
    console.error('Error sending notification:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/notifications/admin/schedule
 * @desc Schedule a notification to be sent at a specific time
 * @access Admin
 */
router.post('/admin/schedule', authenticateAdmin, async (req, res) => {
  try {
    const { targetType, targetId, notificationType, data, scheduledFor } = req.body;

    if (!targetType || !notificationType || !data || !scheduledFor) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    // Create the scheduled notification
    const scheduledNotification = await notificationScheduler.createScheduledNotification({
      notificationType,
      targetType,
      targetId,
      scheduledFor: new Date(scheduledFor),
      data,
      createdBy: req.admin.id
    });

    res.json(scheduledNotification);
  } catch (error) {
    console.error('Error scheduling notification:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route GET /api/notifications/admin/scheduled
 * @desc Get all scheduled notifications
 * @access Admin
 */
router.get('/admin/scheduled', authenticateAdmin, async (req, res) => {
  try {
    const scheduledNotifications = await prisma.scheduledNotification.findMany({
      orderBy: {
        scheduledFor: 'asc'
      }
    });

    res.json(scheduledNotifications);
  } catch (error) {
    console.error('Error fetching scheduled notifications:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route DELETE /api/notifications/admin/scheduled/:id
 * @desc Cancel a scheduled notification
 * @access Admin
 */
router.delete('/admin/scheduled/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Cancel the scheduled notification
    const cancelledNotification = await notificationScheduler.cancelScheduledNotification(id);

    res.json(cancelledNotification);
  } catch (error) {
    console.error('Error cancelling scheduled notification:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
