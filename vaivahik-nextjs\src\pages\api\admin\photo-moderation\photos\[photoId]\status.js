// src/pages/api/admin/photo-moderation/photos/[photoId]/status.js
import axios from 'axios';

export default async function handler(req, res) {
  const { photoId } = req.query;

  // Only allow PUT requests
  if (req.method !== 'PUT') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  // Get the auth token from the request cookies or headers
  const token = req.cookies?.adminToken || req.headers.authorization?.split(' ')[1];

  if (!token) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  try {
    // Forward the request to the backend API
    const response = await axios({
      method: 'PUT',
      url: `${process.env.BACKEND_API_URL}/admin/photo-moderation/photos/${photoId}/status`,
      data: req.body,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000 // 10 second timeout
    });

    // Return the response from the backend
    return res.status(response.status).json(response.data);
  } catch (error) {
    console.error('Error proxying request to backend:', error);

    // Return the error response from the backend if available
    if (error.response) {
      return res.status(error.response.status).json(error.response.data);
    }

    // Handle timeout errors
    if (error.code === 'ECONNABORTED') {
      return res.status(504).json({
        success: false,
        message: 'Request to backend timed out. Please check if the backend server is running.'
      });
    }

    // Handle connection errors
    if (error.code === 'ECONNREFUSED') {
      return res.status(503).json({
        success: false,
        message: 'Cannot connect to backend server. Please check if the backend server is running.'
      });
    }

    // Otherwise return a generic error
    return res.status(500).json({
      success: false,
      message: 'Error connecting to backend service: ' + (error.message || 'Unknown error')
    });
  }
}
