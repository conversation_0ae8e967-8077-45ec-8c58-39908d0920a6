// API endpoint to run project structure analysis
import { exec } from 'child_process';
import path from 'path';
import fs from 'fs';

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Get the project root directory
    const projectRoot = process.cwd();
    
    // Path to the analysis script
    const scriptPath = path.join(projectRoot, '..', 'check-project-structure.js');
    
    // Check if the script exists
    if (!fs.existsSync(scriptPath)) {
      return res.status(404).json({
        success: false,
        message: 'Analysis script not found. Please make sure check-project-structure.js exists in the project root.'
      });
    }
    
    // Run the analysis script
    exec(`node ${scriptPath}`, { cwd: path.join(projectRoot, '..') }, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error running project structure analysis: ${error.message}`);
        return res.status(500).json({
          success: false,
          message: 'Error running project structure analysis',
          error: error.message,
          stdout,
          stderr
        });
      }
      
      if (stderr) {
        console.error(`Analysis script stderr: ${stderr}`);
      }
      
      // Path to the analysis results file
      const resultsFilePath = path.join(projectRoot, '..', 'project-structure-analysis.json');
      
      // Check if the results file exists
      if (!fs.existsSync(resultsFilePath)) {
        return res.status(404).json({
          success: false,
          message: 'Analysis results file not found. The analysis may have failed to complete.'
        });
      }
      
      // Read the analysis results
      try {
        const analysisResults = JSON.parse(fs.readFileSync(resultsFilePath, 'utf8'));
        
        // Add the stdout to the results
        analysisResults.output = stdout;
        
        return res.status(200).json(analysisResults);
      } catch (readError) {
        console.error(`Error reading analysis results: ${readError.message}`);
        return res.status(500).json({
          success: false,
          message: 'Error reading analysis results',
          error: readError.message
        });
      }
    });
  } catch (error) {
    console.error('Error in project structure analysis handler:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
}
