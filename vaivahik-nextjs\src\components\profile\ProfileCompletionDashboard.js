import { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Button,
  Grid,
  Divider,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Tooltip,
  useTheme
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  ArrowForward as ArrowForwardIcon,
  EmojiEvents as EmojiEventsIcon,
  Visibility as VisibilityIcon,
  Person as PersonIcon,
  People as PeopleIcon,
  FamilyRestroom as FamilyIcon,
  School as SchoolIcon,
  LocationOn as LocationIcon,
  Favorite as FavoriteIcon,
  LocalDining as DiningIcon,
  Interests as InterestsIcon,
  PhotoCamera as PhotoIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Lock as LockIcon,
  LockOpen as LockOpenIcon
} from '@mui/icons-material';
import { useRouter } from 'next/router';
import ContactedUsersList from '@/components/contact/ContactedUsersList';

// Mock data for profile completion - in real app, this would come from API
const calculateCategoryCompletion = (userData, category) => {
  // This would be replaced with actual logic based on your data model
  switch (category) {
    case 'basic':
      return userData.fullName && userData.gender && userData.dateOfBirth ? 100 :
             userData.fullName && (userData.gender || userData.dateOfBirth) ? 66 :
             userData.fullName ? 33 : 0;
    case 'photos':
      return userData.profilePhoto ? 100 : 0;
    case 'family':
      return userData.familyDetails ?
             userData.familyDetails.isComplete ? 100 : 50 : 0;
    case 'education':
      return userData.education ? 100 : 0;
    case 'location':
      return userData.city && userData.state ? 100 :
             userData.city || userData.state ? 50 : 0;
    case 'preferences':
      return userData.preferences ?
             userData.preferences.isComplete ? 100 : 50 : 0;
    case 'lifestyle':
      return userData.lifestyle ?
             userData.lifestyle.isComplete ? 100 : 50 : 0;
    case 'about':
      return userData.aboutMe ? 100 : 0;
    default:
      return 0;
  }
};

const ProfileCompletionDashboard = ({ userData, onUpdateSection }) => {
  const theme = useTheme();
  const router = useRouter();
  const [overallCompletion, setOverallCompletion] = useState(0);
  const [earnedBadges, setEarnedBadges] = useState([]);
  const [lockedFeatures, setLockedFeatures] = useState([]);

  // Categories for profile completion
  const categories = [
    {
      id: 'basic',
      name: 'Basic Details',
      icon: <PersonIcon />,
      route: '/profile/edit/basic',
      completionPercentage: calculateCategoryCompletion(userData, 'basic'),
      requiredForLevel: 1,
      benefitText: 'Required to appear in search results',
      timeToComplete: '2 min'
    },
    {
      id: 'photos',
      name: 'Profile Photos',
      icon: <PhotoIcon />,
      route: '/profile/edit/photos',
      completionPercentage: calculateCategoryCompletion(userData, 'photos'),
      requiredForLevel: 1,
      benefitText: 'Profiles with photos get 10x more responses',
      timeToComplete: '1 min'
    },
    {
      id: 'family',
      name: 'Family Details',
      icon: <FamilyIcon />,
      route: '/profile/edit/family',
      completionPercentage: calculateCategoryCompletion(userData, 'family'),
      requiredForLevel: 2,
      benefitText: 'Improves match quality by 40%',
      timeToComplete: '3 min'
    },
    {
      id: 'education',
      name: 'Education & Career',
      icon: <SchoolIcon />,
      route: '/profile/edit/education',
      completionPercentage: calculateCategoryCompletion(userData, 'education'),
      requiredForLevel: 1,
      benefitText: 'Helps match with compatible professionals',
      timeToComplete: '2 min'
    },
    {
      id: 'location',
      name: 'Location Details',
      icon: <LocationIcon />,
      route: '/profile/edit/location',
      completionPercentage: calculateCategoryCompletion(userData, 'location'),
      requiredForLevel: 1,
      benefitText: 'Find matches in your preferred locations',
      timeToComplete: '1 min'
    },
    {
      id: 'preferences',
      name: 'Partner Preferences',
      icon: <FavoriteIcon />,
      route: '/website/pages/profile/edit/preferences',
      completionPercentage: calculateCategoryCompletion(userData, 'preferences'),
      requiredForLevel: 2,
      benefitText: 'Receive more relevant match suggestions',
      timeToComplete: '4 min'
    },
    {
      id: 'lifestyle',
      name: 'Lifestyle & Habits',
      icon: <DiningIcon />,
      route: '/website/pages/profile/edit/lifestyle',
      completionPercentage: calculateCategoryCompletion(userData, 'lifestyle'),
      requiredForLevel: 3,
      benefitText: 'Find partners with compatible lifestyles',
      timeToComplete: '2 min'
    },
    {
      id: 'about',
      name: 'About Me',
      icon: <InterestsIcon />,
      route: '/profile/edit/about',
      completionPercentage: calculateCategoryCompletion(userData, 'about'),
      requiredForLevel: 1,
      benefitText: 'Express yourself to attract better matches',
      timeToComplete: '3 min'
    }
  ];

  // Calculate overall completion percentage
  useEffect(() => {
    const totalCategories = categories.length;
    const completedPercentage = categories.reduce((sum, category) =>
      sum + category.completionPercentage, 0) / totalCategories;

    setOverallCompletion(Math.round(completedPercentage));

    // Determine earned badges based on completion
    const badges = [];
    if (completedPercentage >= 25) badges.push('bronze');
    if (completedPercentage >= 50) badges.push('silver');
    if (completedPercentage >= 75) badges.push('gold');
    if (completedPercentage === 100) badges.push('platinum');
    setEarnedBadges(badges);

    // Determine locked features
    const locked = [];
    if (completedPercentage < 40) locked.push('advanced_search');
    if (completedPercentage < 60) locked.push('contact_info');
    if (completedPercentage < 80) locked.push('spotlight');
    setLockedFeatures(locked);
  }, [categories]);

  // Navigate to edit section
  const handleEditSection = (route) => {
    router.push(route);
  };

  // Get profile level based on completion
  const getProfileLevel = () => {
    if (overallCompletion >= 80) return 'Premium';
    if (overallCompletion >= 60) return 'Advanced';
    if (overallCompletion >= 40) return 'Standard';
    return 'Basic';
  };

  // Get visibility percentage based on completion
  const getVisibilityPercentage = () => {
    return Math.min(100, overallCompletion + 20);
  };

  return (
    <Box sx={{ mb: 4 }}>
      {/* Main Completion Card */}
      <Card elevation={3} sx={{ mb: 3, borderRadius: 2, overflow: 'hidden' }}>
        <Box
          sx={{
            p: 3,
            background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.primary.light} 90%)`,
            color: 'white'
          }}
        >
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={8}>
              <Typography variant="h5" fontWeight="bold" gutterBottom>
                Complete Your Profile
              </Typography>
              <Typography variant="body1" sx={{ mb: 2 }}>
                Your profile is {overallCompletion}% complete. Profiles with higher completion rates receive up to 10x more interest!
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{ flexGrow: 1, mr: 2 }}>
                  <LinearProgress
                    variant="determinate"
                    value={overallCompletion}
                    sx={{
                      height: 10,
                      borderRadius: 5,
                      backgroundColor: 'rgba(255,255,255,0.3)',
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: 'white'
                      }
                    }}
                  />
                </Box>
                <Typography variant="h6" fontWeight="bold">
                  {overallCompletion}%
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4} sx={{ textAlign: 'center' }}>
              <Paper
                elevation={4}
                sx={{
                  p: 2,
                  borderRadius: 2,
                  display: 'inline-block',
                  background: 'rgba(255,255,255,0.9)',
                  color: theme.palette.text.primary
                }}
              >
                <EmojiEventsIcon sx={{ fontSize: 40, color: theme.palette.warning.main }} />
                <Typography variant="h6" fontWeight="bold">
                  {getProfileLevel()} Profile
                </Typography>
                <Typography variant="body2">
                  Profile Visibility: {getVisibilityPercentage()}%
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </Box>

        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ mt: 1 }}>
            Complete these sections to improve your profile
          </Typography>

          <List>
            {categories
              .filter(category => category.completionPercentage < 100)
              .sort((a, b) => a.requiredForLevel - b.requiredForLevel)
              .map((category) => (
                <ListItem
                  key={category.id}
                  sx={{
                    mb: 1,
                    p: 2,
                    borderRadius: 2,
                    border: '1px solid',
                    borderColor: 'divider',
                    bgcolor: category.completionPercentage > 0 ? 'rgba(0, 150, 136, 0.05)' : 'transparent'
                  }}
                >
                  <ListItemIcon>
                    {category.icon}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {category.name}
                        {category.requiredForLevel === 1 && (
                          <Chip
                            size="small"
                            label="Required"
                            color="error"
                            variant="outlined"
                            sx={{ ml: 1 }}
                          />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box sx={{ mt: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <LinearProgress
                            variant="determinate"
                            value={category.completionPercentage}
                            sx={{ flexGrow: 1, height: 6, borderRadius: 3 }}
                          />
                          <Typography variant="body2" sx={{ ml: 1, minWidth: '40px' }}>
                            {category.completionPercentage}%
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="caption" color="text.secondary">
                            {category.benefitText}
                          </Typography>
                          <Chip
                            size="small"
                            icon={<AccessTimeIcon fontSize="small" />}
                            label={category.timeToComplete}
                            variant="outlined"
                          />
                        </Box>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Button
                      variant="contained"
                      color="primary"
                      endIcon={<ArrowForwardIcon />}
                      onClick={() => handleEditSection(category.route)}
                      size="small"
                    >
                      {category.completionPercentage > 0 ? 'Continue' : 'Start'}
                    </Button>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
          </List>

          {categories.filter(category => category.completionPercentage < 100).length === 0 && (
            <Box sx={{ textAlign: 'center', py: 3 }}>
              <CheckCircleIcon sx={{ fontSize: 60, color: 'success.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Congratulations! Your profile is 100% complete.
              </Typography>
              <Typography variant="body1" color="text.secondary">
                You've maximized your chances of finding the perfect match.
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Badges and Rewards */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card elevation={2} sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <EmojiEventsIcon sx={{ mr: 1 }} /> Your Profile Badges
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Grid container spacing={2} justifyContent="center">
                <Grid item xs={6} sm={3}>
                  <Tooltip title={earnedBadges.includes('bronze') ? 'Earned: 25% Complete' : 'Complete 25% of your profile'}>
                    <Paper
                      elevation={earnedBadges.includes('bronze') ? 3 : 0}
                      sx={{
                        p: 2,
                        textAlign: 'center',
                        bgcolor: earnedBadges.includes('bronze') ? 'rgba(176, 141, 87, 0.1)' : 'action.disabledBackground',
                        border: '1px solid',
                        borderColor: earnedBadges.includes('bronze') ? 'warning.light' : 'divider',
                        borderRadius: 2,
                        opacity: earnedBadges.includes('bronze') ? 1 : 0.6
                      }}
                    >
                      <StarIcon sx={{ fontSize: 40, color: earnedBadges.includes('bronze') ? '#CD7F32' : 'text.disabled' }} />
                      <Typography variant="subtitle2">Bronze</Typography>
                    </Paper>
                  </Tooltip>
                </Grid>

                <Grid item xs={6} sm={3}>
                  <Tooltip title={earnedBadges.includes('silver') ? 'Earned: 50% Complete' : 'Complete 50% of your profile'}>
                    <Paper
                      elevation={earnedBadges.includes('silver') ? 3 : 0}
                      sx={{
                        p: 2,
                        textAlign: 'center',
                        bgcolor: earnedBadges.includes('silver') ? 'rgba(192, 192, 192, 0.1)' : 'action.disabledBackground',
                        border: '1px solid',
                        borderColor: earnedBadges.includes('silver') ? 'grey.400' : 'divider',
                        borderRadius: 2,
                        opacity: earnedBadges.includes('silver') ? 1 : 0.6
                      }}
                    >
                      <StarIcon sx={{ fontSize: 40, color: earnedBadges.includes('silver') ? '#C0C0C0' : 'text.disabled' }} />
                      <Typography variant="subtitle2">Silver</Typography>
                    </Paper>
                  </Tooltip>
                </Grid>

                <Grid item xs={6} sm={3}>
                  <Tooltip title={earnedBadges.includes('gold') ? 'Earned: 75% Complete' : 'Complete 75% of your profile'}>
                    <Paper
                      elevation={earnedBadges.includes('gold') ? 3 : 0}
                      sx={{
                        p: 2,
                        textAlign: 'center',
                        bgcolor: earnedBadges.includes('gold') ? 'rgba(255, 215, 0, 0.1)' : 'action.disabledBackground',
                        border: '1px solid',
                        borderColor: earnedBadges.includes('gold') ? 'warning.main' : 'divider',
                        borderRadius: 2,
                        opacity: earnedBadges.includes('gold') ? 1 : 0.6
                      }}
                    >
                      <StarIcon sx={{ fontSize: 40, color: earnedBadges.includes('gold') ? '#FFD700' : 'text.disabled' }} />
                      <Typography variant="subtitle2">Gold</Typography>
                    </Paper>
                  </Tooltip>
                </Grid>

                <Grid item xs={6} sm={3}>
                  <Tooltip title={earnedBadges.includes('platinum') ? 'Earned: 100% Complete' : 'Complete 100% of your profile'}>
                    <Paper
                      elevation={earnedBadges.includes('platinum') ? 3 : 0}
                      sx={{
                        p: 2,
                        textAlign: 'center',
                        bgcolor: earnedBadges.includes('platinum') ? 'rgba(229, 228, 226, 0.2)' : 'action.disabledBackground',
                        border: '1px solid',
                        borderColor: earnedBadges.includes('platinum') ? 'primary.light' : 'divider',
                        borderRadius: 2,
                        opacity: earnedBadges.includes('platinum') ? 1 : 0.6
                      }}
                    >
                      <StarIcon sx={{ fontSize: 40, color: earnedBadges.includes('platinum') ? '#E5E4E2' : 'text.disabled' }} />
                      <Typography variant="subtitle2">Platinum</Typography>
                    </Paper>
                  </Tooltip>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card elevation={2} sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <VisibilityIcon sx={{ mr: 1 }} /> Profile Visibility Benefits
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <List dense>
                <ListItem>
                  <ListItemIcon>
                    {overallCompletion >= 40 ? <LockOpenIcon color="success" /> : <LockIcon color="action" />}
                  </ListItemIcon>
                  <ListItemText
                    primary="Advanced Search Visibility"
                    secondary="Your profile appears in advanced search results"
                  />
                  <Chip
                    size="small"
                    label={overallCompletion >= 40 ? "Unlocked" : "40% Required"}
                    color={overallCompletion >= 40 ? "success" : "default"}
                    variant={overallCompletion >= 40 ? "filled" : "outlined"}
                  />
                </ListItem>

                <ListItem>
                  <ListItemIcon>
                    {overallCompletion >= 60 ? <LockOpenIcon color="success" /> : <LockIcon color="action" />}
                  </ListItemIcon>
                  <ListItemText
                    primary="Contact Information Sharing"
                    secondary="Share contact details with interested matches"
                  />
                  <Chip
                    size="small"
                    label={overallCompletion >= 60 ? "Unlocked" : "60% Required"}
                    color={overallCompletion >= 60 ? "success" : "default"}
                    variant={overallCompletion >= 60 ? "filled" : "outlined"}
                  />
                </ListItem>

                <ListItem>
                  <ListItemIcon>
                    {overallCompletion >= 80 ? <LockOpenIcon color="success" /> : <LockIcon color="action" />}
                  </ListItemIcon>
                  <ListItemText
                    primary="Spotlight Feature Eligibility"
                    secondary="Highlight your profile to get more attention"
                  />
                  <Chip
                    size="small"
                    label={overallCompletion >= 80 ? "Unlocked" : "80% Required"}
                    color={overallCompletion >= 80 ? "success" : "default"}
                    variant={overallCompletion >= 80 ? "filled" : "outlined"}
                  />
                </ListItem>

                <ListItem>
                  <ListItemIcon>
                    {overallCompletion >= 100 ? <LockOpenIcon color="success" /> : <LockIcon color="action" />}
                  </ListItemIcon>
                  <ListItemText
                    primary="Premium Match Algorithm"
                    secondary="Get highest quality matches based on compatibility"
                  />
                  <Chip
                    size="small"
                    label={overallCompletion >= 100 ? "Unlocked" : "100% Required"}
                    color={overallCompletion >= 100 ? "success" : "default"}
                    variant={overallCompletion >= 100 ? "filled" : "outlined"}
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Contact History Section */}
      <Box sx={{ mt: 4 }}>
        <ContactedUsersList
          userId={userData?.id}
          maxItems={10}
          showTitle={true}
        />
      </Box>
    </Box>
  );
};

export default ProfileCompletionDashboard;
