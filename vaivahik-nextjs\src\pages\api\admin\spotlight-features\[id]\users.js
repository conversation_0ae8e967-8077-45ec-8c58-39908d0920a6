// Import necessary modules
import { withAuth } from '@/utils/authHandler';
import { handleApiError } from '@/utils/errorHandler';

// Mock data for active users
const mockActiveUsers = [
  {
    spotlightId: 1,
    userId: 101,
    name: "<PERSON><PERSON>",
    gender: "Male",
    age: 28,
    email: "<EMAIL>",
    phone: "+91 98765 43210",
    profilePic: null,
    startTime: new Date(Date.now() - 12 * 60 * 60 * 1000),
    endTime: new Date(Date.now() + 12 * 60 * 60 * 1000),
    timeRemaining: "12 hours remaining",
    pricePaid: 449.1
  },
  {
    spotlightId: 1,
    userId: 102,
    name: "<PERSON><PERSON>",
    gender: "Female",
    age: 26,
    email: "<EMAIL>",
    phone: "+91 87654 32109",
    profilePic: null,
    startTime: new Date(Date.now() - 6 * 60 * 60 * 1000),
    endTime: new Date(Date.now() + 18 * 60 * 60 * 1000),
    timeRemaining: "18 hours remaining",
    pricePaid: 449.1
  },
  {
    spotlightId: 2,
    userId: 103,
    name: "<PERSON><PERSON>",
    gender: "Male",
    age: 30,
    email: "<EMAIL>",
    phone: "+91 76543 21098",
    profilePic: null,
    startTime: new Date(Date.now() - 24 * 60 * 60 * 1000),
    endTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
    timeRemaining: "24 hours remaining",
    pricePaid: 1104.15
  }
];

// In production, we would import Prisma
let prisma;
if (process.env.NODE_ENV === 'production') {
  try {
    const { PrismaClient } = require('@prisma/client');
    prisma = new PrismaClient();
  } catch (error) {
    console.error('Failed to initialize Prisma client:', error);
  }
}

async function handler(req, res) {
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ success: false, message: 'Feature ID is required' });
  }

  // Only allow GET method
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // In development mode, return mock data
    if (process.env.NODE_ENV === 'development') {
      // Find users with this spotlight feature ID
      const featureId = parseInt(id);
      const users = mockActiveUsers.filter(user => user.spotlightId === featureId);

      // Mock feature data
      const mockFeature = {
        id: featureId,
        name: featureId === 1 ? "24-Hour Spotlight" :
              featureId === 2 ? "Premium Spotlight Pack" :
              featureId === 3 ? "Weekend Spotlight" :
              "Spotlight Feature",
        durationHours: featureId === 3 ? 48 : 24
      };

      return res.status(200).json({
        success: true,
        feature: mockFeature,
        users,
        totalCount: users.length
      });
    }

    // In production, use Prisma
    // Check if feature exists
    const feature = await prisma.spotlightFeature.findUnique({
      where: { id: parseInt(id) }
    });

    if (!feature) {
      return res.status(404).json({ success: false, message: 'Spotlight feature not found' });
    }

    // Get users with this spotlight feature
    const now = new Date();
    const spotlightUsers = await prisma.userSpotlight.findMany({
      where: {
        spotlightId: parseInt(id),
        isActive: true,
        endTime: {
          gt: now
        }
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            profile: {
              select: {
                fullName: true,
                gender: true,
                dateOfBirth: true,
                city: true,
                state: true
              }
            },
            photos: {
              where: {
                isProfilePic: true
              },
              select: {
                url: true
              },
              take: 1
            }
          }
        }
      },
      orderBy: {
        endTime: 'asc'
      }
    });

    // Format the response
    const formattedUsers = spotlightUsers.map(spotlight => {
      const user = spotlight.user;
      const profilePic = user.photos.length > 0 ? user.photos[0].url : null;

      return {
        spotlightId: spotlight.id,
        userId: user.id,
        name: user.profile?.fullName || user.name,
        email: user.email,
        phone: user.phone,
        gender: user.profile?.gender,
        age: user.profile?.dateOfBirth ? calculateAge(user.profile.dateOfBirth) : null,
        location: user.profile?.city ? `${user.profile.city}, ${user.profile.state || ''}` : null,
        profilePic,
        startTime: spotlight.startTime,
        endTime: spotlight.endTime,
        timeRemaining: calculateTimeRemaining(spotlight.endTime),
        pricePaid: spotlight.pricePaid
      };
    });

    return res.status(200).json({
      success: true,
      feature: {
        id: feature.id,
        name: feature.name,
        durationHours: feature.durationHours
      },
      users: formattedUsers,
      totalCount: formattedUsers.length
    });
  } catch (error) {
    console.error('Error fetching spotlight users:', error);

    // If there's an error in production, return mock data in development mode
    if (process.env.NODE_ENV === 'development') {
      const featureId = parseInt(id);
      const users = mockActiveUsers.filter(user => user.spotlightId === featureId);

      const mockFeature = {
        id: featureId,
        name: featureId === 1 ? "24-Hour Spotlight" :
              featureId === 2 ? "Premium Spotlight Pack" :
              featureId === 3 ? "Weekend Spotlight" :
              "Spotlight Feature",
        durationHours: featureId === 3 ? 48 : 24
      };

      return res.status(200).json({
        success: true,
        feature: mockFeature,
        users,
        totalCount: users.length
      });
    }

    return res.status(500).json({ success: false, message: 'Failed to fetch spotlight users' });
  }
}

// Export the handler with authentication middleware
export default withAuth(handler, 'ADMIN');

// Helper function to calculate age from date of birth
function calculateAge(dateOfBirth) {
  const today = new Date();
  const birthDate = new Date(dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }

  return age;
}

// Helper function to calculate time remaining
function calculateTimeRemaining(endTime) {
  const now = new Date();
  const end = new Date(endTime);
  const diffMs = end - now;

  if (diffMs <= 0) {
    return 'Expired';
  }

  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

  if (diffHours > 24) {
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} remaining`;
  }

  return `${diffHours} hour${diffHours !== 1 ? 's' : ''}, ${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} remaining`;
}
