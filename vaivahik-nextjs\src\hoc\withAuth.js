/**
 * Authentication Higher-Order Component
 * 
 * This HOC provides authentication protection for pages.
 * It redirects to the login page if the user is not authenticated.
 */

import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '@/contexts/AuthContext';
import LoadingScreen from '@/components/common/LoadingScreen';

/**
 * withAuth HOC
 * @param {React.ComponentType} Component - Component to wrap
 * @param {Object} options - Options for the HOC
 * @param {string} [options.redirectTo='/login'] - Path to redirect to if not authenticated
 * @param {string[]} [options.roles] - Roles allowed to access this page
 * @returns {React.ComponentType} - Protected component
 */
const withAuth = (Component, options = {}) => {
  const {
    redirectTo = '/login',
    roles = []
  } = options;

  const WithAuthComponent = (props) => {
    const { user, loading, isAuthenticated } = useAuth();
    const router = useRouter();
    const [isChecking, setIsChecking] = useState(true);

    useEffect(() => {
      // Skip check if auth context is still loading
      if (loading) return;

      const checkAuth = async () => {
        // Check if user is authenticated
        const authenticated = isAuthenticated();

        if (!authenticated) {
          // Redirect to login page with return URL
          router.push({
            pathname: redirectTo,
            query: { returnUrl: router.asPath }
          });
          return;
        }

        // If roles are specified, check if user has required role
        if (roles.length > 0 && user) {
          const hasRole = roles.includes(user.role);
          if (!hasRole) {
            // Redirect to unauthorized page
            router.push('/unauthorized');
            return;
          }
        }

        // User is authenticated and has required role
        setIsChecking(false);
      };

      checkAuth();
    }, [user, loading, isAuthenticated, router]);

    // Show loading screen while checking authentication
    if (loading || isChecking) {
      return <LoadingScreen message="Checking authentication..." />;
    }

    // Render component if authenticated
    return <Component {...props} />;
  };

  // Copy getInitialProps from the wrapped component
  if (Component.getInitialProps) {
    WithAuthComponent.getInitialProps = async (ctx) => {
      const componentProps = await Component.getInitialProps(ctx);
      return { ...componentProps };
    };
  }

  return WithAuthComponent;
};

/**
 * withAdminAuth HOC - Specifically for admin pages
 * @param {React.ComponentType} Component - Component to wrap
 * @returns {React.ComponentType} - Protected admin component
 */
export const withAdminAuth = (Component) => {
  return withAuth(Component, {
    redirectTo: '/admin/login',
    roles: ['ADMIN', 'MODERATOR']
  });
};

export default withAuth;
