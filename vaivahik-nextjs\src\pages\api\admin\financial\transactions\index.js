// src/pages/api/admin/financial/transactions/index.js
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../auth/[...nextauth]';

export default async function handler(req, res) {
  // Check if user is authenticated and is an admin
  const session = await getServerSession(req, res, authOptions);
  
  if (!session || !session.user || !session.user.isAdmin) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  // Handle GET request - fetch transactions with pagination and filtering
  if (req.method === 'GET') {
    try {
      // Extract query parameters
      const { 
        page = 1, 
        limit = 10, 
        status = '', 
        paymentMethod = '',
        search = '',
        sortBy = 'createdAt', 
        order = 'desc',
        startDate = '',
        endDate = '',
        minAmount = '',
        maxAmount = '',
        productType = ''
      } = req.query;
      
      // Build query parameters
      const queryParams = new URLSearchParams({
        page,
        limit,
        ...(status && { status }),
        ...(paymentMethod && { paymentMethod }),
        ...(search && { search }),
        ...(sortBy && { sortBy }),
        ...(order && { order }),
        ...(startDate && { startDate }),
        ...(endDate && { endDate }),
        ...(minAmount && { minAmount }),
        ...(maxAmount && { maxAmount }),
        ...(productType && { productType })
      }).toString();

      // Fetch transactions from backend API
      const response = await fetch(`${process.env.BACKEND_API_URL}/api/admin/financial/transactions?${queryParams}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.accessToken}`
        }
      }).catch(() => null);
      
      if (response && response.ok) {
        const data = await response.json();
        return res.status(200).json({
          success: true,
          transactions: data.transactions,
          pagination: data.pagination
        });
      } else {
        // Fallback to mock data if backend API fails
        const mockData = getMockTransactions(page, limit, status, paymentMethod, search, sortBy, order, startDate, endDate, minAmount, maxAmount, productType);
        
        return res.status(200).json({
          success: true,
          transactions: mockData.transactions,
          pagination: mockData.pagination
        });
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch transactions',
        error: error.message
      });
    }
  }
  
  // Handle unsupported methods
  return res.status(405).json({ success: false, message: 'Method not allowed' });
}

// Helper function to get mock transactions
function getMockTransactions(page, limit, status, paymentMethod, search, sortBy, order, startDate, endDate, minAmount, maxAmount, productType) {
  // Generate mock transactions
  const allTransactions = [];
  
  for (let i = 1; i <= 100; i++) {
    const createdAt = new Date();
    createdAt.setDate(createdAt.getDate() - Math.floor(Math.random() * 90)); // Random date within last 90 days
    
    const statuses = ['COMPLETED', 'PENDING', 'FAILED', 'REFUNDED'];
    const paymentMethods = ['Credit Card', 'UPI', 'Net Banking', 'Wallet', 'Other'];
    const productTypes = ['Premium Plan', 'Standard Plan', 'Basic Plan', 'Spotlight Feature', 'Biodata PDF'];
    
    const transaction = {
      id: `txn-${i.toString().padStart(5, '0')}`,
      orderId: `order-${i.toString().padStart(5, '0')}`,
      paymentId: `pay-${Math.random().toString(36).substring(2, 10)}`,
      userId: `user-${Math.floor(Math.random() * 1000)}`,
      amount: Math.floor(Math.random() * 10000) + 500, // Random amount between 500 and 10500
      currency: 'INR',
      status: statuses[Math.floor(Math.random() * statuses.length)],
      paymentMethod: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
      productType: productTypes[Math.floor(Math.random() * productTypes.length)],
      createdAt: createdAt.toISOString(),
      updatedAt: createdAt.toISOString(),
      user: {
        id: `user-${Math.floor(Math.random() * 1000)}`,
        name: `User ${i}`,
        email: `user${i}@example.com`,
        phone: `+91${Math.floor(Math.random() * 10000000000).toString().padStart(10, '0')}`
      }
    };
    
    allTransactions.push(transaction);
  }
  
  // Apply filters
  let filteredTransactions = [...allTransactions];
  
  if (status) {
    filteredTransactions = filteredTransactions.filter(t => t.status === status);
  }
  
  if (paymentMethod) {
    filteredTransactions = filteredTransactions.filter(t => t.paymentMethod === paymentMethod);
  }
  
  if (productType) {
    filteredTransactions = filteredTransactions.filter(t => t.productType === productType);
  }
  
  if (search) {
    const searchLower = search.toLowerCase();
    filteredTransactions = filteredTransactions.filter(t => 
      t.orderId.toLowerCase().includes(searchLower) ||
      t.paymentId.toLowerCase().includes(searchLower) ||
      t.user.name.toLowerCase().includes(searchLower) ||
      t.user.email.toLowerCase().includes(searchLower)
    );
  }
  
  if (startDate) {
    const startDateObj = new Date(startDate);
    filteredTransactions = filteredTransactions.filter(t => new Date(t.createdAt) >= startDateObj);
  }
  
  if (endDate) {
    const endDateObj = new Date(endDate);
    filteredTransactions = filteredTransactions.filter(t => new Date(t.createdAt) <= endDateObj);
  }
  
  if (minAmount) {
    const minAmountValue = parseFloat(minAmount);
    filteredTransactions = filteredTransactions.filter(t => t.amount >= minAmountValue);
  }
  
  if (maxAmount) {
    const maxAmountValue = parseFloat(maxAmount);
    filteredTransactions = filteredTransactions.filter(t => t.amount <= maxAmountValue);
  }
  
  // Apply sorting
  filteredTransactions.sort((a, b) => {
    const aValue = a[sortBy];
    const bValue = b[sortBy];
    
    if (typeof aValue === 'string') {
      return order === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    } else {
      return order === 'asc' ? aValue - bValue : bValue - aValue;
    }
  });
  
  // Apply pagination
  const pageNum = parseInt(page, 10);
  const limitNum = parseInt(limit, 10);
  const startIndex = (pageNum - 1) * limitNum;
  const endIndex = startIndex + limitNum;
  const paginatedTransactions = filteredTransactions.slice(startIndex, endIndex);
  
  return {
    transactions: paginatedTransactions,
    pagination: {
      currentPage: pageNum,
      limit: limitNum,
      totalPages: Math.ceil(filteredTransactions.length / limitNum),
      totalTransactions: filteredTransactions.length
    }
  };
}
