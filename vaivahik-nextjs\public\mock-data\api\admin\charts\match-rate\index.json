{"success": true, "chartData": {"title": "Match Success Rate", "subtitle": "Percentage of successful matches over time", "type": "line", "timeRange": "last_30_days", "data": {"labels": ["Jan 1", "Jan 2", "Jan 3", "Jan 4", "Jan 5", "Jan 6", "Jan 7", "Jan 8", "Jan 9", "Jan 10", "Jan 11", "Jan 12", "Jan 13", "Jan 14", "Jan 15", "Jan 16", "Jan 17", "Jan 18", "Jan 19", "Jan 20", "Jan 21", "Jan 22", "Jan 23", "Jan 24", "Jan 25", "Jan 26", "Jan 27", "Jan 28", "Jan 29", "Jan 30"], "datasets": [{"label": "Match Success Rate (%)", "data": [12.5, 15.2, 18.7, 16.3, 19.8, 22.1, 25.4, 23.7, 26.9, 24.5, 28.3, 31.2, 29.8, 33.5, 35.7, 32.4, 36.8, 39.2, 37.6, 41.3, 43.8, 40.9, 44.7, 47.2, 45.6, 48.9, 51.3, 49.7, 52.8, 55.2], "borderColor": "#FF69B4", "backgroundColor": "rgba(255, 105, 180, 0.1)", "borderWidth": 3, "fill": true, "tension": 0.4}, {"label": "Industry Average (%)", "data": [20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20], "borderColor": "#FFA500", "backgroundColor": "rgba(255, 165, 0, 0.1)", "borderWidth": 2, "borderDash": [5, 5], "fill": false}]}, "options": {"responsive": true, "maintainAspectRatio": false, "plugins": {"legend": {"position": "top"}, "tooltip": {"mode": "index", "intersect": false}}, "scales": {"x": {"display": true, "title": {"display": true, "text": "Date"}}, "y": {"display": true, "title": {"display": true, "text": "Success Rate (%)"}, "min": 0, "max": 100}}}}, "statistics": {"currentRate": 55.2, "previousRate": 48.9, "change": 6.3, "changePercentage": 12.9, "trend": "increasing", "averageRate": 35.7, "bestDay": {"date": "Jan 30", "rate": 55.2}, "worstDay": {"date": "Jan 1", "rate": 12.5}}, "insights": [{"type": "positive", "message": "Match success rate has improved by 12.9% compared to last month"}, {"type": "info", "message": "Current rate (55.2%) is significantly above industry average (20%)"}, {"type": "recommendation", "message": "Consider analyzing factors contributing to recent success rate improvements"}], "filters": {"timeRanges": ["last_7_days", "last_30_days", "last_90_days", "last_year"], "userTypes": ["all", "premium", "free", "verified"], "ageGroups": ["18-25", "26-30", "31-35", "36-40", "40+"], "locations": ["Mumbai", "Pune", "<PERSON><PERSON>", "Nagpur", "Other"]}, "metadata": {"lastUpdated": "2024-01-15T12:00:00Z", "dataSource": "matching_algorithm", "totalMatches": 1247, "successfulMatches": 688, "pendingMatches": 234, "rejectedMatches": 325}}