"""
Model Interpreter for Matrimony Matching

This module provides interpretability features for the matrimony matching model,
explaining why matches are recommended.
"""

import os
import json
import logging
import numpy as np
import torch
import matplotlib.pyplot as plt
from collections import defaultdict

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelInterpreter:
    """Interpreter for matrimony matching model"""
    
    def __init__(self, model, feature_processor=None):
        """
        Initialize the model interpreter
        
        Args:
            model: An instance of EnhancedMatrimonyMatchingModel
            feature_processor: Feature processor for preprocessing
        """
        self.model = model
        self.feature_processor = feature_processor
        
        # Set device
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Feature importance cache
        self.feature_importance_cache = {}
        
        # Feature groups for interpretation
        self.feature_groups = {
            'demographics': ['age', 'height'],
            'background': ['religion', 'caste', 'subCaste', 'gotra'],
            'education': ['education'],
            'career': ['occupation', 'income'],
            'location': ['city', 'state']
        }
        
        # Compatibility factors
        self.compatibility_factors = [
            'age_in_range', 'height_in_range', 'income_meets_min',
            'religion_match', 'caste_match', 'education_match',
            'occupation_match', 'city_match', 'state_match'
        ]
    
    def explain_match(self, user_profile, user_preferences, match_profile, score=None):
        """
        Generate an explanation for a match
        
        Args:
            user_profile (dict): User profile data
            user_preferences (dict): User preferences
            match_profile (dict): Match profile data
            score (float): Match score (if None, will be calculated)
            
        Returns:
            dict: Match explanation
        """
        # Calculate score if not provided
        if score is None:
            score = self.calculate_match_score(user_profile, user_preferences, match_profile)
        
        # Calculate feature contributions
        contributions = self.calculate_feature_contributions(
            user_profile, user_preferences, match_profile
        )
        
        # Calculate compatibility factors
        compatibility = self.calculate_compatibility(
            user_profile, user_preferences, match_profile
        )
        
        # Generate explanation text
        explanation_text = self.generate_explanation_text(
            user_profile, match_profile, contributions, compatibility, score
        )
        
        # Generate suggestions
        suggestions = self.generate_suggestions(
            user_profile, user_preferences, match_profile, compatibility
        )
        
        # Create explanation object
        explanation = {
            'score': int(round(score * 100)),
            'contributions': contributions,
            'compatibility': compatibility,
            'explanation': explanation_text,
            'suggestions': suggestions
        }
        
        return explanation
    
    def calculate_match_score(self, user_profile, user_preferences, match_profile):
        """
        Calculate match score
        
        Args:
            user_profile (dict): User profile data
            user_preferences (dict): User preferences
            match_profile (dict): Match profile data
            
        Returns:
            float: Match score (0-1)
        """
        # Process features
        if self.feature_processor:
            user_features, match_features = self.feature_processor.process_match_pair(
                user_profile, user_preferences, match_profile
            )
        else:
            # Simple feature extraction if no processor
            user_features = {k: v for k, v in user_profile.items() if isinstance(v, (int, float, str))}
            match_features = {k: v for k, v in match_profile.items() if isinstance(v, (int, float, str))}
        
        # Convert to tensors
        user_tensor = self._features_to_tensor([user_features])
        match_tensor = self._features_to_tensor([match_features])
        
        # Move to device
        user_tensor = user_tensor.to(self.device)
        match_tensor = match_tensor.to(self.device)
        
        # Get prediction
        self.model.model.eval()
        with torch.no_grad():
            score = self.model.model(user_tensor, match_tensor)
        
        return score.item()
    
    def _features_to_tensor(self, features_list):
        """
        Convert feature dictionaries to tensor
        
        Args:
            features_list (list): List of feature dictionaries
            
        Returns:
            torch.Tensor: Feature tensor
        """
        # Get all feature keys
        all_keys = set()
        for features in features_list:
            all_keys.update(features.keys())
        
        # Sort keys for consistent ordering
        sorted_keys = sorted(all_keys)
        
        # Create tensor
        tensor_data = []
        for features in features_list:
            feature_vector = [features.get(key, 0.0) for key in sorted_keys]
            tensor_data.append(feature_vector)
        
        return torch.tensor(tensor_data, dtype=torch.float32)
    
    def calculate_feature_contributions(self, user_profile, user_preferences, match_profile):
        """
        Calculate feature contributions to the match score
        
        Args:
            user_profile (dict): User profile data
            user_preferences (dict): User preferences
            match_profile (dict): Match profile data
            
        Returns:
            dict: Feature contributions by group
        """
        # Process features
        if self.feature_processor:
            user_features, match_features = self.feature_processor.process_match_pair(
                user_profile, user_preferences, match_profile
            )
        else:
            # Simple feature extraction if no processor
            user_features = {k: v for k, v in user_profile.items() if isinstance(v, (int, float, str))}
            match_features = {k: v for k, v in match_profile.items() if isinstance(v, (int, float, str))}
        
        # Calculate baseline score
        baseline_score = self.calculate_match_score(
            user_profile, user_preferences, match_profile
        )
        
        # Calculate contribution for each feature group
        contributions = {}
        
        for group_name, features in self.feature_groups.items():
            # Create a copy of features with this group zeroed out
            user_features_zeroed = user_features.copy()
            match_features_zeroed = match_features.copy()
            
            # Zero out features in this group
            for feature in features:
                for key in list(user_features_zeroed.keys()):
                    if key.startswith(feature):
                        user_features_zeroed[key] = 0.0
                
                for key in list(match_features_zeroed.keys()):
                    if key.startswith(feature):
                        match_features_zeroed[key] = 0.0
            
            # Calculate score with this group zeroed out
            zeroed_user_profile = {**user_profile, **user_features_zeroed}
            zeroed_match_profile = {**match_profile, **match_features_zeroed}
            
            zeroed_score = self.calculate_match_score(
                zeroed_user_profile, user_preferences, zeroed_match_profile
            )
            
            # Contribution is the difference in scores
            contribution = baseline_score - zeroed_score
            
            # Normalize to percentage
            contribution_percent = max(0, min(100, contribution * 100))
            
            contributions[group_name] = contribution_percent
        
        # Normalize contributions to sum to 100%
        total_contribution = sum(contributions.values())
        if total_contribution > 0:
            for group in contributions:
                contributions[group] = (contributions[group] / total_contribution) * 100
        
        return contributions
    
    def calculate_compatibility(self, user_profile, user_preferences, match_profile):
        """
        Calculate compatibility factors
        
        Args:
            user_profile (dict): User profile data
            user_preferences (dict): User preferences
            match_profile (dict): Match profile data
            
        Returns:
            dict: Compatibility factors
        """
        compatibility = {}
        
        # Age compatibility
        if 'age' in match_profile and 'minAge' in user_preferences and 'maxAge' in user_preferences:
            min_age = user_preferences['minAge']
            max_age = user_preferences['maxAge']
            match_age = match_profile['age']
            
            age_in_range = min_age <= match_age <= max_age
            age_distance = 0
            
            if max_age > min_age:
                ideal_age = (min_age + max_age) / 2
                age_distance = abs(match_age - ideal_age) / ((max_age - min_age) / 2)
                age_distance = min(1.0, age_distance)
            
            compatibility['age'] = {
                'in_range': age_in_range,
                'distance': age_distance,
                'score': 100 if age_in_range else int((1 - age_distance) * 70),
                'details': f"{match_age} years"
            }
        
        # Height compatibility
        if 'height' in match_profile and 'minHeight' in user_preferences and 'maxHeight' in user_preferences:
            min_height = user_preferences['minHeight']
            max_height = user_preferences['maxHeight']
            match_height = match_profile['height']
            
            height_in_range = min_height <= match_height <= max_height
            
            compatibility['height'] = {
                'in_range': height_in_range,
                'score': 100 if height_in_range else 70,
                'details': f"{match_height} cm"
            }
        
        # Religion compatibility
        if 'religion' in match_profile and 'religion' in user_preferences:
            religion_match = match_profile['religion'] == user_preferences['religion']
            
            compatibility['religion'] = {
                'match': religion_match,
                'score': 100 if religion_match else 50,
                'details': match_profile['religion']
            }
        
        # Caste compatibility
        if 'caste' in match_profile and 'caste' in user_preferences:
            caste_match = match_profile['caste'] == user_preferences['caste']
            
            compatibility['caste'] = {
                'match': caste_match,
                'score': 100 if caste_match else 60,
                'details': match_profile['caste']
            }
        
        # Education compatibility
        if 'education' in match_profile and 'education' in user_preferences:
            education_match = match_profile['education'] == user_preferences['education']
            
            compatibility['education'] = {
                'match': education_match,
                'score': 100 if education_match else 80,
                'details': match_profile['education']
            }
        
        # Occupation compatibility
        if 'occupation' in match_profile:
            occupation_details = match_profile['occupation']
            
            compatibility['occupation'] = {
                'score': 90,  # Default score
                'details': occupation_details
            }
        
        # Income compatibility
        if 'income' in match_profile and 'minIncome' in user_preferences:
            min_income = user_preferences['minIncome']
            match_income = match_profile['income']
            
            income_meets_min = match_income >= min_income
            income_ratio = match_income / min_income if min_income > 0 else 1.0
            
            compatibility['income'] = {
                'meets_min': income_meets_min,
                'ratio': min(5.0, income_ratio),
                'score': 100 if income_meets_min else 60,
                'details': f"{match_income:,}"
            }
        
        # Location compatibility
        if 'city' in match_profile and 'city' in user_profile:
            city_match = match_profile['city'] == user_profile['city']
            
            compatibility['location'] = {
                'city_match': city_match,
                'score': 100 if city_match else 70,
                'details': match_profile['city']
            }
        
        return compatibility
    
    def generate_explanation_text(self, user_profile, match_profile, contributions, compatibility, score):
        """
        Generate explanation text
        
        Args:
            user_profile (dict): User profile data
            match_profile (dict): Match profile data
            contributions (dict): Feature contributions
            compatibility (dict): Compatibility factors
            score (float): Match score
            
        Returns:
            str: Explanation text
        """
        # Sort contributions by value (descending)
        sorted_contributions = sorted(
            contributions.items(), key=lambda x: x[1], reverse=True
        )
        
        # Get top 3 contributing factors
        top_factors = sorted_contributions[:3]
        
        # Generate explanation
        explanation = f"You and {match_profile.get('name', 'this person')} have a {int(round(score * 100))}% match. "
        
        # Add top factors
        explanation += "The top factors in this match are: "
        
        factor_texts = []
        for factor, value in top_factors:
            if factor == 'demographics':
                if 'age' in compatibility:
                    factor_texts.append(f"age ({compatibility['age']['details']})")
            elif factor == 'background':
                if 'religion' in compatibility:
                    factor_texts.append(f"religious background ({compatibility['religion']['details']})")
                if 'caste' in compatibility:
                    factor_texts.append(f"caste ({compatibility['caste']['details']})")
            elif factor == 'education':
                if 'education' in compatibility:
                    factor_texts.append(f"education ({compatibility['education']['details']})")
            elif factor == 'career':
                if 'occupation' in compatibility:
                    factor_texts.append(f"career ({compatibility['occupation']['details']})")
                if 'income' in compatibility:
                    factor_texts.append(f"income ({compatibility['income']['details']})")
            elif factor == 'location':
                if 'location' in compatibility:
                    factor_texts.append(f"location ({compatibility['location']['details']})")
        
        explanation += ", ".join(factor_texts) + "."
        
        return explanation
    
    def generate_suggestions(self, user_profile, user_preferences, match_profile, compatibility):
        """
        Generate suggestions based on compatibility
        
        Args:
            user_profile (dict): User profile data
            user_preferences (dict): User preferences
            match_profile (dict): Match profile data
            compatibility (dict): Compatibility factors
            
        Returns:
            list: Suggestions
        """
        suggestions = []
        
        # Add specific suggestions based on compatibility
        if 'occupation' in compatibility:
            occupation = match_profile.get('occupation', '')
            if occupation:
                suggestions.append(f"The match's career as a {occupation} could complement your profession.")
        
        if 'location' in compatibility and not compatibility['location'].get('city_match', True):
            user_city = user_profile.get('city', '')
            match_city = match_profile.get('city', '')
            if user_city and match_city:
                suggestions.append(f"Consider discussing future living arrangements as you currently live in different cities ({user_city} and {match_city}).")
        
        if 'education' in compatibility and compatibility['education'].get('match', False):
            education = match_profile.get('education', '')
            if education:
                suggestions.append(f"You both have similar educational backgrounds ({education}) which can lead to shared values and interests.")
        
        if 'religion' in compatibility and compatibility['religion'].get('match', False):
            religion = match_profile.get('religion', '')
            if religion:
                suggestions.append(f"Your shared {religion} background may provide a strong foundation for your relationship.")
        
        # Add general suggestions if we don't have enough specific ones
        if len(suggestions) < 2:
            suggestions.append("Consider discussing your long-term goals and values to ensure compatibility.")
        
        if len(suggestions) < 3:
            suggestions.append("Take time to get to know each other's families, as family relationships are important in a marriage.")
        
        return suggestions
    
    def visualize_match(self, user_profile, user_preferences, match_profile, output_path=None):
        """
        Visualize match compatibility
        
        Args:
            user_profile (dict): User profile data
            user_preferences (dict): User preferences
            match_profile (dict): Match profile data
            output_path (str): Path to save visualization
            
        Returns:
            str: Path to saved visualization or None
        """
        # Calculate score and explanation
        score = self.calculate_match_score(user_profile, user_preferences, match_profile)
        explanation = self.explain_match(user_profile, user_preferences, match_profile, score)
        
        # Create figure
        plt.figure(figsize=(12, 8))
        
        # Plot compatibility factors
        compatibility = explanation['compatibility']
        factor_names = []
        factor_scores = []
        
        for factor, details in compatibility.items():
            factor_names.append(factor.capitalize())
            factor_scores.append(details['score'])
        
        # Sort by score (descending)
        sorted_indices = np.argsort(factor_scores)[::-1]
        factor_names = [factor_names[i] for i in sorted_indices]
        factor_scores = [factor_scores[i] for i in sorted_indices]
        
        # Plot horizontal bar chart
        plt.barh(factor_names, factor_scores, color='skyblue')
        plt.xlabel('Compatibility Score')
        plt.title(f'Match Compatibility: {int(round(score * 100))}%')
        plt.xlim(0, 100)
        plt.grid(axis='x', linestyle='--', alpha=0.7)
        
        # Add score labels
        for i, score in enumerate(factor_scores):
            plt.text(score + 2, i, f"{score:.0f}", va='center')
        
        # Add explanation text
        plt.figtext(0.5, 0.02, explanation['explanation'], wrap=True, 
                   horizontalalignment='center', fontsize=10)
        
        # Save or show
        if output_path:
            plt.tight_layout(rect=[0, 0.05, 1, 0.95])
            plt.savefig(output_path)
            plt.close()
            return output_path
        else:
            plt.tight_layout(rect=[0, 0.05, 1, 0.95])
            plt.show()
            return None
