import { useState, useEffect, useRef } from 'react';
import EnhancedAdminLayout from '@/components/admin/EnhancedAdminLayout';
import { adminGet } from '@/services/apiService';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
  Alert,
  CircularProgress,
  Switch,
  FormControlLabel,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Badge
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Clear as ClearIcon,
  FilterList as FilterIcon,
  SmartToy as AIIcon,
  Person as UserIcon,
  Schedule as TimeIcon,
  Error as ErrorIcon,
  CheckCircle as SuccessIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon
} from '@mui/icons-material';
import { format } from 'date-fns';

export default function MCPLogsPage() {
  const [loading, setLoading] = useState(true);
  const [logs, setLogs] = useState([]);
  const [filteredLogs, setFilteredLogs] = useState([]);
  const [mcpStatus, setMcpStatus] = useState({ isRunning: false, connectedClients: 0 });
  const [realTimeEnabled, setRealTimeEnabled] = useState(false);
  const [filters, setFilters] = useState({
    algorithm: 'all',
    userId: '',
    dateFrom: '',
    dateTo: '',
    success: 'all'
  });
  const [stats, setStats] = useState({
    totalDecisions: 0,
    successRate: 0,
    avgExecutionTime: 0,
    topAlgorithms: []
  });
  
  const wsRef = useRef(null);
  const logsEndRef = useRef(null);

  useEffect(() => {
    fetchInitialData();
    
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  useEffect(() => {
    applyFilters();
  }, [logs, filters]);

  useEffect(() => {
    if (realTimeEnabled) {
      connectWebSocket();
    } else {
      disconnectWebSocket();
    }
  }, [realTimeEnabled]);

  const fetchInitialData = async () => {
    setLoading(true);
    try {
      // Fetch MCP server status
      const statusResponse = await adminGet('/mcp/status');
      if (statusResponse.success) {
        setMcpStatus(statusResponse.data);
      }

      // Fetch AI decision logs
      const logsResponse = await adminGet('/mcp/logs', {
        limit: 100,
        includeStats: true
      });
      
      if (logsResponse.success) {
        setLogs(logsResponse.data.logs || []);
        setStats(logsResponse.data.stats || {});
      }
    } catch (error) {
      console.error('Error fetching MCP data:', error);
    } finally {
      setLoading(false);
    }
  };

  const connectWebSocket = () => {
    if (wsRef.current) return;

    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/api/admin/mcp/logs/stream`;
    
    wsRef.current = new WebSocket(wsUrl);
    
    wsRef.current.onopen = () => {
      console.log('Connected to MCP logs stream');
    };
    
    wsRef.current.onmessage = (event) => {
      const newLog = JSON.parse(event.data);
      setLogs(prev => [newLog, ...prev].slice(0, 1000)); // Keep last 1000 logs
      scrollToBottom();
    };
    
    wsRef.current.onclose = () => {
      console.log('Disconnected from MCP logs stream');
      wsRef.current = null;
    };
    
    wsRef.current.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  };

  const disconnectWebSocket = () => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
  };

  const scrollToBottom = () => {
    logsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const applyFilters = () => {
    let filtered = [...logs];

    if (filters.algorithm !== 'all') {
      filtered = filtered.filter(log => log.algorithm === filters.algorithm);
    }

    if (filters.userId) {
      filtered = filtered.filter(log => 
        log.userId?.toLowerCase().includes(filters.userId.toLowerCase())
      );
    }

    if (filters.success !== 'all') {
      const isSuccess = filters.success === 'true';
      filtered = filtered.filter(log => log.success === isSuccess);
    }

    if (filters.dateFrom) {
      const fromDate = new Date(filters.dateFrom);
      filtered = filtered.filter(log => new Date(log.timestamp) >= fromDate);
    }

    if (filters.dateTo) {
      const toDate = new Date(filters.dateTo);
      filtered = filtered.filter(log => new Date(log.timestamp) <= toDate);
    }

    setFilteredLogs(filtered);
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({ ...prev, [field]: value }));
  };

  const clearFilters = () => {
    setFilters({
      algorithm: 'all',
      userId: '',
      dateFrom: '',
      dateTo: '',
      success: 'all'
    });
  };

  const exportLogs = () => {
    const csvContent = [
      ['Timestamp', 'Algorithm', 'User ID', 'Success', 'Execution Time', 'Input', 'Result'].join(','),
      ...filteredLogs.map(log => [
        log.timestamp,
        log.algorithm,
        log.userId,
        log.success,
        log.executionTimeMs || 'N/A',
        `"${JSON.stringify(log.inputData).replace(/"/g, '""')}"`,
        `"${JSON.stringify(log.result).replace(/"/g, '""')}"`
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `mcp-logs-${format(new Date(), 'yyyy-MM-dd-HH-mm')}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const getAlgorithmColor = (algorithm) => {
    const colors = {
      user_matching: 'primary',
      profile_analysis: 'secondary',
      compatibility_score: 'success',
      smart_recommendations: 'info',
      fraud_detection: 'warning',
      success_prediction: 'error'
    };
    return colors[algorithm] || 'default';
  };

  if (loading) {
    return (
      <EnhancedAdminLayout>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <CircularProgress />
        </Box>
      </EnhancedAdminLayout>
    );
  }

  return (
    <EnhancedAdminLayout>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" fontWeight="600">
            MCP Server Logs & AI Usage
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <FormControlLabel
              control={
                <Switch
                  checked={realTimeEnabled}
                  onChange={(e) => setRealTimeEnabled(e.target.checked)}
                />
              }
              label="Real-time"
            />
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchInitialData}
            >
              Refresh
            </Button>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={exportLogs}
              disabled={filteredLogs.length === 0}
            >
              Export
            </Button>
          </Box>
        </Box>

        {/* Status Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Badge
                    color={mcpStatus.isRunning ? 'success' : 'error'}
                    variant="dot"
                  >
                    <AIIcon fontSize="large" />
                  </Badge>
                  <Box>
                    <Typography variant="h6">
                      {mcpStatus.isRunning ? 'Running' : 'Stopped'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      MCP Server Status
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <UserIcon fontSize="large" color="primary" />
                  <Box>
                    <Typography variant="h6">
                      {mcpStatus.connectedClients || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Connected Clients
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <SuccessIcon fontSize="large" color="success" />
                  <Box>
                    <Typography variant="h6">
                      {stats.successRate?.toFixed(1) || 0}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Success Rate
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <TimeIcon fontSize="large" color="info" />
                  <Box>
                    <Typography variant="h6">
                      {stats.avgExecutionTime?.toFixed(0) || 0}ms
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Avg Execution Time
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Filters */}
        <Paper sx={{ p: 3, mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <FilterIcon />
            <Typography variant="h6">Filters</Typography>
            <Button
              size="small"
              startIcon={<ClearIcon />}
              onClick={clearFilters}
            >
              Clear All
            </Button>
          </Box>
          
          <Grid container spacing={2}>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Algorithm</InputLabel>
                <Select
                  value={filters.algorithm}
                  onChange={(e) => handleFilterChange('algorithm', e.target.value)}
                  label="Algorithm"
                >
                  <MenuItem value="all">All Algorithms</MenuItem>
                  <MenuItem value="user_matching">User Matching</MenuItem>
                  <MenuItem value="profile_analysis">Profile Analysis</MenuItem>
                  <MenuItem value="compatibility_score">Compatibility Score</MenuItem>
                  <MenuItem value="smart_recommendations">Smart Recommendations</MenuItem>
                  <MenuItem value="fraud_detection">Fraud Detection</MenuItem>
                  <MenuItem value="success_prediction">Success Prediction</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                size="small"
                label="User ID"
                value={filters.userId}
                onChange={(e) => handleFilterChange('userId', e.target.value)}
              />
            </Grid>

            <Grid item xs={12} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.success}
                  onChange={(e) => handleFilterChange('success', e.target.value)}
                  label="Status"
                >
                  <MenuItem value="all">All</MenuItem>
                  <MenuItem value="true">Success</MenuItem>
                  <MenuItem value="false">Error</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                size="small"
                type="datetime-local"
                label="From Date"
                value={filters.dateFrom}
                onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                size="small"
                type="datetime-local"
                label="To Date"
                value={filters.dateTo}
                onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>
        </Paper>

        {/* Logs Table */}
        <Paper>
          <CardHeader 
            title={`AI Decision Logs (${filteredLogs.length})`}
            action={
              realTimeEnabled && (
                <Chip 
                  icon={<PlayIcon />} 
                  label="Live" 
                  color="success" 
                  size="small" 
                />
              )
            }
          />
          <TableContainer sx={{ maxHeight: 600 }}>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell>Timestamp</TableCell>
                  <TableCell>Algorithm</TableCell>
                  <TableCell>User ID</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Execution Time</TableCell>
                  <TableCell>Input</TableCell>
                  <TableCell>Result</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredLogs.map((log, index) => (
                  <TableRow key={log.id || index}>
                    <TableCell>
                      <Typography variant="body2">
                        {format(new Date(log.timestamp), 'MMM dd, HH:mm:ss')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={log.algorithm}
                        color={getAlgorithmColor(log.algorithm)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontFamily="monospace">
                        {log.userId || 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        icon={log.success ? <SuccessIcon /> : <ErrorIcon />}
                        label={log.success ? 'Success' : 'Error'}
                        color={log.success ? 'success' : 'error'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {log.executionTimeMs ? `${log.executionTimeMs}ms` : 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Tooltip title={JSON.stringify(log.inputData, null, 2)}>
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            maxWidth: 200, 
                            overflow: 'hidden', 
                            textOverflow: 'ellipsis',
                            cursor: 'pointer'
                          }}
                        >
                          {JSON.stringify(log.inputData)}
                        </Typography>
                      </Tooltip>
                    </TableCell>
                    <TableCell>
                      <Tooltip title={JSON.stringify(log.result, null, 2)}>
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            maxWidth: 200, 
                            overflow: 'hidden', 
                            textOverflow: 'ellipsis',
                            cursor: 'pointer'
                          }}
                        >
                          {JSON.stringify(log.result)}
                        </Typography>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          <div ref={logsEndRef} />
        </Paper>

        {filteredLogs.length === 0 && (
          <Alert severity="info" sx={{ mt: 2 }}>
            No AI decision logs found. Try adjusting your filters or check if the MCP server is running.
          </Alert>
        )}
      </Box>
    </EnhancedAdminLayout>
  );
}
