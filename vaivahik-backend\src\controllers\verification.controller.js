// src/controllers/verification.controller.js

const fs = require('fs/promises');
const path = require('path');
const sharp = require('sharp');

// Constants
const DOCUMENT_UPLOAD_DIR = path.join(__dirname, '../../uploads/verification_documents');

/**
 * @description Upload a verification document
 * @route POST /api/users/verification/documents
 */
exports.uploadVerificationDocument = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;
    const { documentType } = req.body;
    
    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }
    
    if (!req.file) {
        const error = new Error('No document file was uploaded.');
        error.status = 400;
        return next(error);
    }
    
    if (!documentType || !['AADHAR_CARD', 'PAN_CARD', 'VOTER_ID', 'PASSPORT', 'DRIVING_LICENSE', 'OTHER'].includes(documentType)) {
        const error = new Error('Invalid or missing document type.');
        error.status = 400;
        return next(error);
    }
    
    try {
        // Ensure directory exists
        await fs.mkdir(DOCUMENT_UPLOAD_DIR, { recursive: true });
        
        const file = req.file;
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const filename = `doc-${userId}-${uniqueSuffix}${path.extname(file.originalname)}`;
        const outputPath = path.join(DOCUMENT_UPLOAD_DIR, filename);
        const publicUrlPath = `/uploads/verification_documents/${filename}`;
        
        // Process the file based on its type
        if (file.mimetype.startsWith('image/')) {
            // For images, optimize them
            await sharp(file.buffer)
                .resize({ width: 1200, height: 1200, fit: 'inside', withoutEnlargement: true })
                .jpeg({ quality: 85 })
                .toFile(outputPath);
        } else if (file.mimetype === 'application/pdf') {
            // For PDFs, just save the file
            await fs.writeFile(outputPath, file.buffer);
        }
        
        // Save document information to database
        const document = await prisma.verificationDocument.create({
            data: {
                userId: userId,
                type: documentType,
                url: publicUrlPath,
                filename: filename,
                filesize: file.size,
                mimeType: file.mimetype,
                status: 'PENDING_REVIEW'
            }
        });
        
        // Update user profile status if needed
        const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { profileStatus: true }
        });
        
        if (user && user.profileStatus === 'INCOMPLETE') {
            await prisma.user.update({
                where: { id: userId },
                data: { profileStatus: 'PENDING_APPROVAL' }
            });
        }
        
        console.log(`Verification document uploaded for user ${userId}: ${document.id}`);
        
        res.status(201).json({
            message: 'Document uploaded successfully and pending review.',
            document: {
                id: document.id,
                type: document.type,
                url: document.url,
                status: document.status,
                uploadedAt: document.uploadedAt
            }
        });
    } catch (error) {
        console.error('Error uploading verification document:', error);
        next(error);
    }
};

/**
 * @description Get all verification documents for the current user
 * @route GET /api/users/verification/documents
 */
exports.getUserVerificationDocuments = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;
    
    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }
    
    try {
        const documents = await prisma.verificationDocument.findMany({
            where: { userId: userId },
            orderBy: { uploadedAt: 'desc' }
        });
        
        res.status(200).json({
            message: 'Verification documents retrieved successfully.',
            documents: documents
        });
    } catch (error) {
        console.error('Error fetching verification documents:', error);
        next(error);
    }
};

/**
 * @description Delete a verification document
 * @route DELETE /api/users/verification/documents/:documentId
 */
exports.deleteVerificationDocument = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;
    const { documentId } = req.params;
    
    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }
    
    if (!documentId) {
        const error = new Error('Document ID parameter is required.');
        error.status = 400;
        return next(error);
    }
    
    try {
        // Find the document first to get the file path
        const document = await prisma.verificationDocument.findUnique({
            where: { id: documentId, userId: userId },
            select: { url: true }
        });
        
        if (!document) {
            const error = new Error('Document not found or you do not own this document.');
            error.status = 404;
            return next(error);
        }
        
        // Delete from database
        await prisma.verificationDocument.delete({
            where: { id: documentId }
        });
        
        // Delete the file
        if (document.url) {
            const filePath = path.join(DOCUMENT_UPLOAD_DIR, path.basename(document.url));
            try {
                await fs.unlink(filePath);
                console.log(`Deleted document file: ${filePath}`);
            } catch (fileError) {
                console.error(`Error deleting document file ${filePath}: ${fileError.message}`);
            }
        }
        
        console.log(`Deleted verification document ${documentId} for user ${userId}`);
        
        res.status(204).send();
    } catch (error) {
        console.error('Error deleting verification document:', error);
        next(error);
    }
};
