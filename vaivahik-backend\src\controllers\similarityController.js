// src/controllers/similarityController.js
const redisClient = require('../utils/redisClient');
const logger = require('../utils/logger');
const similaritySearch = require('../utils/similaritySearch');
const compatibilityScore = require('../utils/compatibilityScore');

/**
 * @description Find similar profiles to a specific user (premium feature)
 * @route GET /api/users/similar-profiles/:userId
 */
exports.findSimilarProfiles = async (req, res, next) => {
    const prisma = req.prisma;
    const currentUserId = req.user?.userId;
    const { userId } = req.params;
    const { 
        minScore = 0, 
        maxResults = 20,
        skipCache = false
    } = req.query;

    if (!currentUserId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Check if user is premium
        const user = await prisma.user.findUnique({
            where: { id: currentUserId },
            select: { isPremium: true }
        });
        
        // If not premium, return upgrade message
        if (!user?.isPremium) {
            return res.status(403).json({
                success: false,
                message: 'Similar profiles search is a premium feature',
                upgradeRequired: true,
                featureName: 'similar-profiles'
            });
        }
        
        // Create cache key
        const cacheKey = redisClient.generateSearchCacheKey({
            type: 'similar-profiles',
            userId,
            currentUserId,
            minScore,
            maxResults
        });
        
        // Try to get results from cache first (unless skipCache is true)
        if (!skipCache) {
            const cachedResults = await redisClient.getCache(cacheKey);
            if (cachedResults) {
                logger.info(`Similar profiles found in cache for key: ${cacheKey}`);
                
                // Add cache indicator to response
                return res.status(200).json({
                    success: true,
                    profiles: cachedResults.profiles,
                    sourceProfile: cachedResults.sourceProfile,
                    fromCache: true
                });
            }
        }
        
        // Get source profile (the profile to find similar matches for)
        const sourceProfile = await prisma.user.findUnique({
            where: { id: userId },
            select: {
                id: true,
                profile: {
                    select: {
                        fullName: true,
                        gender: true,
                        dateOfBirth: true,
                        city: true,
                        state: true,
                        occupation: true,
                        education: true,
                        religion: true,
                        caste: true,
                        height: true,
                        maritalStatus: true,
                        diet: true
                    }
                }
            }
        });
        
        if (!sourceProfile) {
            return res.status(404).json({
                success: false,
                message: 'Source profile not found'
            });
        }
        
        // Format source profile for similarity calculation
        const formattedSourceProfile = {
            id: sourceProfile.id,
            fullName: sourceProfile.profile?.fullName,
            age: sourceProfile.profile?.dateOfBirth ? calculateAge(sourceProfile.profile.dateOfBirth) : null,
            city: sourceProfile.profile?.city,
            state: sourceProfile.profile?.state,
            occupation: sourceProfile.profile?.occupation,
            education: sourceProfile.profile?.education,
            religion: sourceProfile.profile?.religion,
            caste: sourceProfile.profile?.caste,
            height: sourceProfile.profile?.height,
            maritalStatus: sourceProfile.profile?.maritalStatus,
            diet: sourceProfile.profile?.diet
        };
        
        // Get candidate profiles (opposite gender, active profiles)
        const candidateProfiles = await prisma.user.findMany({
            where: {
                id: { not: userId }, // Exclude source profile
                profileStatus: 'ACTIVE',
                profile: {
                    gender: sourceProfile.profile?.gender === 'MALE' ? 'FEMALE' : 'MALE' // Opposite gender
                }
            },
            select: {
                id: true,
                isVerified: true,
                isPremium: true,
                createdAt: true,
                profile: {
                    select: {
                        fullName: true,
                        gender: true,
                        dateOfBirth: true,
                        city: true,
                        state: true,
                        occupation: true,
                        education: true,
                        religion: true,
                        caste: true,
                        height: true,
                        maritalStatus: true,
                        diet: true
                    }
                },
                photos: {
                    where: {
                        isProfilePic: true,
                        visibility: 'PUBLIC'
                    },
                    select: { url: true, id: true },
                    take: 1
                },
                spotlightFeatures: {
                    where: {
                        endTime: {
                            gt: new Date()
                        }
                    },
                    select: {
                        id: true,
                        endTime: true,
                        feature: {
                            select: {
                                name: true,
                                displayName: true
                            }
                        }
                    },
                    take: 1
                }
            },
            take: 100 // Limit to 100 candidates for performance
        });
        
        // Format candidate profiles for similarity calculation
        const formattedCandidates = candidateProfiles.map(profile => ({
            id: profile.id,
            isVerified: profile.isVerified,
            isPremium: profile.isPremium,
            fullName: profile.profile?.fullName,
            age: profile.profile?.dateOfBirth ? calculateAge(profile.profile.dateOfBirth) : null,
            city: profile.profile?.city,
            state: profile.profile?.state,
            occupation: profile.profile?.occupation,
            education: profile.profile?.education,
            religion: profile.profile?.religion,
            caste: profile.profile?.caste,
            height: profile.profile?.height,
            maritalStatus: profile.profile?.maritalStatus,
            diet: profile.profile?.diet,
            profilePicUrl: profile.photos?.[0]?.url || null,
            photoId: profile.photos?.[0]?.id || null,
            isSpotlighted: profile.spotlightFeatures?.length > 0,
            spotlightEndTime: profile.spotlightFeatures?.[0]?.endTime || null,
            spotlightFeature: profile.spotlightFeatures?.[0]?.feature?.displayName || null,
            createdAt: profile.createdAt
        }));
        
        // Find similar profiles
        const similarProfiles = similaritySearch.findSimilarProfiles(
            formattedSourceProfile,
            formattedCandidates,
            {
                minScore: parseInt(minScore, 10),
                maxResults: parseInt(maxResults, 10),
                weights: {
                    age: 15,
                    education: 20,
                    occupation: 15,
                    religion: 10,
                    caste: 10,
                    location: 10,
                    height: 5,
                    maritalStatus: 5,
                    diet: 5
                }
            }
        );
        
        // Prepare response
        const response = {
            success: true,
            profiles: similarProfiles,
            sourceProfile: formattedSourceProfile,
            fromCache: false
        };
        
        // Cache the results for 15 minutes (900 seconds)
        await redisClient.setCache(cacheKey, response, 900);
        logger.info(`Cached similar profiles for key: ${cacheKey}`);
        
        res.status(200).json(response);
    } catch (error) {
        logger.error('Error finding similar profiles:', error);
        next(error);
    }
};

/**
 * @description Get compatibility score with a specific user (premium feature)
 * @route GET /api/users/compatibility/:userId
 */
exports.getCompatibilityScore = async (req, res, next) => {
    const prisma = req.prisma;
    const currentUserId = req.user?.userId;
    const { userId } = req.params;
    const { skipCache = false } = req.query;

    if (!currentUserId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Check if user is premium
        const user = await prisma.user.findUnique({
            where: { id: currentUserId },
            select: { isPremium: true }
        });
        
        // If not premium, return upgrade message
        if (!user?.isPremium) {
            return res.status(403).json({
                success: false,
                message: 'Compatibility score is a premium feature',
                upgradeRequired: true,
                featureName: 'compatibility-score'
            });
        }
        
        // Create cache key
        const cacheKey = redisClient.generateSearchCacheKey({
            type: 'compatibility-score',
            userId,
            currentUserId
        });
        
        // Try to get results from cache first (unless skipCache is true)
        if (!skipCache) {
            const cachedResults = await redisClient.getCache(cacheKey);
            if (cachedResults) {
                logger.info(`Compatibility score found in cache for key: ${cacheKey}`);
                
                // Add cache indicator to response
                return res.status(200).json({
                    success: true,
                    compatibility: cachedResults.compatibility,
                    userProfile: cachedResults.userProfile,
                    matchProfile: cachedResults.matchProfile,
                    fromCache: true
                });
            }
        }
        
        // Get current user profile
        const userProfile = await prisma.user.findUnique({
            where: { id: currentUserId },
            select: {
                id: true,
                profile: {
                    select: {
                        fullName: true,
                        gender: true,
                        dateOfBirth: true,
                        city: true,
                        state: true,
                        occupation: true,
                        education: true,
                        religion: true,
                        caste: true,
                        height: true,
                        maritalStatus: true,
                        diet: true,
                        smoking: true,
                        drinking: true
                    }
                },
                preferences: {
                    select: {
                        ageFrom: true,
                        ageTo: true,
                        heightFrom: true,
                        heightTo: true,
                        education: true,
                        occupation: true,
                        location: true,
                        religion: true,
                        caste: true,
                        incomeRange: true
                    }
                }
            }
        });
        
        // Get match profile
        const matchProfile = await prisma.user.findUnique({
            where: { id: userId },
            select: {
                id: true,
                profile: {
                    select: {
                        fullName: true,
                        gender: true,
                        dateOfBirth: true,
                        city: true,
                        state: true,
                        occupation: true,
                        education: true,
                        religion: true,
                        caste: true,
                        height: true,
                        maritalStatus: true,
                        diet: true,
                        smoking: true,
                        drinking: true,
                        incomeRange: true
                    }
                }
            }
        });
        
        if (!userProfile || !matchProfile) {
            return res.status(404).json({
                success: false,
                message: 'One or both profiles not found'
            });
        }
        
        // Format profiles for compatibility calculation
        const formattedUserProfile = {
            id: userProfile.id,
            fullName: userProfile.profile?.fullName,
            age: userProfile.profile?.dateOfBirth ? calculateAge(userProfile.profile.dateOfBirth) : null,
            city: userProfile.profile?.city,
            state: userProfile.profile?.state,
            occupation: userProfile.profile?.occupation,
            education: userProfile.profile?.education,
            religion: userProfile.profile?.religion,
            caste: userProfile.profile?.caste,
            height: userProfile.profile?.height,
            maritalStatus: userProfile.profile?.maritalStatus,
            diet: userProfile.profile?.diet,
            smoking: userProfile.profile?.smoking,
            drinking: userProfile.profile?.drinking
        };
        
        const formattedMatchProfile = {
            id: matchProfile.id,
            fullName: matchProfile.profile?.fullName,
            age: matchProfile.profile?.dateOfBirth ? calculateAge(matchProfile.profile.dateOfBirth) : null,
            city: matchProfile.profile?.city,
            state: matchProfile.profile?.state,
            occupation: matchProfile.profile?.occupation,
            education: matchProfile.profile?.education,
            religion: matchProfile.profile?.religion,
            caste: matchProfile.profile?.caste,
            height: matchProfile.profile?.height,
            maritalStatus: matchProfile.profile?.maritalStatus,
            diet: matchProfile.profile?.diet,
            smoking: matchProfile.profile?.smoking,
            drinking: matchProfile.profile?.drinking,
            incomeRange: matchProfile.profile?.incomeRange
        };
        
        // Calculate compatibility score
        const compatibility = compatibilityScore.calculateCompatibilityScore(
            formattedUserProfile,
            formattedMatchProfile,
            userProfile.preferences || {}
        );
        
        // Prepare response
        const response = {
            success: true,
            compatibility,
            userProfile: formattedUserProfile,
            matchProfile: formattedMatchProfile,
            fromCache: false
        };
        
        // Cache the results for 1 day (86400 seconds)
        await redisClient.setCache(cacheKey, response, 86400);
        logger.info(`Cached compatibility score for key: ${cacheKey}`);
        
        res.status(200).json(response);
    } catch (error) {
        logger.error('Error calculating compatibility score:', error);
        next(error);
    }
};

// Helper function to calculate age from date of birth
function calculateAge(dateOfBirth) {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }

    return age;
}
