/**
 * Admin Authentication Utility
 * Handles admin authentication and authorization
 */

import jwt from 'jsonwebtoken';
import { getSession } from 'next-auth/react';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Mock admin users (in production, this would be from database)
const adminUsers = [
  {
    id: 'admin_1',
    email: '<EMAIL>',
    name: 'Super Admin',
    role: 'super_admin',
    permissions: ['all']
  },
  {
    id: 'admin_2',
    email: '<EMAIL>',
    name: 'Support Admin',
    role: 'support_admin',
    permissions: ['users', 'support', 'analytics']
  }
];

/**
 * Authenticate admin user from request
 * @param {Object} req - Request object
 * @returns {Object} Authentication result
 */
export async function authenticateAdmin(req) {
  try {
    // Check for session-based authentication (NextAuth)
    const session = await getSession({ req });
    
    if (session?.user?.role === 'admin') {
      return {
        success: true,
        admin: {
          id: session.user.id,
          email: session.user.email,
          name: session.user.name,
          role: session.user.role,
          permissions: session.user.permissions || ['all']
        }
      };
    }

    // Check for JWT token in headers
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      try {
        const decoded = jwt.verify(token, JWT_SECRET);
        const admin = adminUsers.find(user => user.id === decoded.userId);
        
        if (admin) {
          return {
            success: true,
            admin
          };
        }
      } catch (jwtError) {
        console.error('JWT verification failed:', jwtError);
      }
    }

    // Check for API key authentication
    const apiKey = req.headers['x-api-key'];
    if (apiKey === process.env.ADMIN_API_KEY) {
      return {
        success: true,
        admin: {
          id: 'api_admin',
          email: '<EMAIL>',
          name: 'API Admin',
          role: 'api_admin',
          permissions: ['all']
        }
      };
    }

    return {
      success: false,
      message: 'No valid authentication found'
    };

  } catch (error) {
    console.error('Admin authentication error:', error);
    return {
      success: false,
      message: 'Authentication failed'
    };
  }
}

/**
 * Check if admin has specific permission
 * @param {Object} admin - Admin user object
 * @param {string} permission - Permission to check
 * @returns {boolean} Has permission
 */
export function hasPermission(admin, permission) {
  if (!admin || !admin.permissions) {
    return false;
  }

  // Super admin has all permissions
  if (admin.permissions.includes('all')) {
    return true;
  }

  return admin.permissions.includes(permission);
}

/**
 * Generate JWT token for admin
 * @param {Object} admin - Admin user object
 * @returns {string} JWT token
 */
export function generateAdminToken(admin) {
  return jwt.sign(
    {
      userId: admin.id,
      email: admin.email,
      role: admin.role
    },
    JWT_SECRET,
    { expiresIn: '24h' }
  );
}

/**
 * Middleware to protect admin routes
 * @param {Function} handler - Route handler
 * @returns {Function} Protected handler
 */
export function withAdminAuth(handler) {
  return async (req, res) => {
    const authResult = await authenticateAdmin(req);
    
    if (!authResult.success) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized access'
      });
    }

    // Add admin data to request
    req.admin = authResult.admin;
    
    return handler(req, res);
  };
}

/**
 * Middleware to check specific permission
 * @param {string} permission - Required permission
 * @returns {Function} Permission middleware
 */
export function requirePermission(permission) {
  return (handler) => {
    return withAdminAuth(async (req, res) => {
      if (!hasPermission(req.admin, permission)) {
        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
      }

      return handler(req, res);
    });
  };
}

/**
 * Get admin user by email
 * @param {string} email - Admin email
 * @returns {Object|null} Admin user or null
 */
export function getAdminByEmail(email) {
  return adminUsers.find(admin => admin.email === email) || null;
}

/**
 * Validate admin credentials
 * @param {string} email - Admin email
 * @param {string} password - Admin password
 * @returns {Object} Validation result
 */
export async function validateAdminCredentials(email, password) {
  try {
    const admin = getAdminByEmail(email);
    
    if (!admin) {
      return {
        success: false,
        message: 'Admin not found'
      };
    }

    // In production, you would hash and compare passwords
    // For now, using a simple check
    const validPassword = password === 'admin123'; // This should be hashed in production
    
    if (!validPassword) {
      return {
        success: false,
        message: 'Invalid credentials'
      };
    }

    return {
      success: true,
      admin,
      token: generateAdminToken(admin)
    };

  } catch (error) {
    console.error('Admin credential validation error:', error);
    return {
      success: false,
      message: 'Validation failed'
    };
  }
}
