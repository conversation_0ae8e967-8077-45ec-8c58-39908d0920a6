/**
 * Redis Client Utility
 *
 * This utility provides a Redis client for caching search results and other data.
 * It uses the redis library for Redis operations.
 */

const redis = require('redis');

// Simple logger to replace the missing logger module
const logger = {
  info: (message) => console.log(`[INFO] ${message}`),
  error: (message) => console.error(`[ERROR] ${message}`),
  warn: (message) => console.warn(`[WARN] ${message}`)
};

// Create Redis client
let redisClient = null;

/**
 * Initialize Redis client
 */
const initRedisClient = async () => {
  try {
    if (redisClient && redisClient.isReady) return redisClient;

    // Create Redis client
    redisClient = redis.createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379'
    });

    // Set up event handlers
    redisClient.on('error', (err) => {
      logger.error(`Redis client error: ${err}`);
    });

    redisClient.on('connect', () => {
      logger.info('Redis client connecting...');
    });

    redisClient.on('ready', () => {
      logger.info('Redis client connected and ready!');
    });

    // Connect to Redis
    await redisClient.connect();

    return redisClient;
  } catch (error) {
    logger.error(`Error initializing Redis client: ${error}`);
    return null;
  }
};

/**
 * Get Redis client
 * @returns {Object} Redis client
 */
const getRedisClient = async () => {
  if (!redisClient || !redisClient.isReady) {
    return await initRedisClient();
  }
  return redisClient;
};

/**
 * Set cache with expiration
 * @param {string} key - Cache key
 * @param {any} value - Value to cache (will be JSON stringified)
 * @param {number} expirySeconds - Expiration time in seconds
 * @returns {Promise<boolean>} Success status
 */
const setCache = async (key, value, expirySeconds = 3600) => {
  try {
    const client = await getRedisClient();
    if (!client) return false;

    const serializedValue = JSON.stringify(value);
    await client.set(key, serializedValue, { EX: expirySeconds });
    return true;
  } catch (error) {
    logger.error(`Error setting cache for key ${key}: ${error}`);
    return false;
  }
};

/**
 * Get cache value
 * @param {string} key - Cache key
 * @returns {Promise<any>} Cached value or null
 */
const getCache = async (key) => {
  try {
    const client = await getRedisClient();
    if (!client) return null;

    const cachedValue = await client.get(key);
    if (!cachedValue) return null;

    return JSON.parse(cachedValue);
  } catch (error) {
    logger.error(`Error getting cache for key ${key}: ${error}`);
    return null;
  }
};

/**
 * Delete cache
 * @param {string} key - Cache key
 * @returns {Promise<boolean>} Success status
 */
const deleteCache = async (key) => {
  try {
    const client = await getRedisClient();
    if (!client) return false;

    await client.del(key);
    return true;
  } catch (error) {
    logger.error(`Error deleting cache for key ${key}: ${error}`);
    return false;
  }
};

/**
 * Clear cache by pattern
 * @param {string} pattern - Key pattern to clear (e.g., "search:*")
 * @returns {Promise<boolean>} Success status
 */
const clearCacheByPattern = async (pattern) => {
  try {
    const client = await getRedisClient();
    if (!client) return false;

    // Get all keys matching the pattern
    const keys = await client.keys(pattern);
    if (keys.length === 0) return true;

    // Delete all matching keys
    if (keys.length > 0) {
      await client.del(keys);
    }
    return true;
  } catch (error) {
    logger.error(`Error clearing cache by pattern ${pattern}: ${error}`);
    return false;
  }
};

/**
 * Generate search cache key
 * @param {Object} searchParams - Search parameters
 * @returns {string} Cache key
 */
const generateSearchCacheKey = (searchParams) => {
  try {
    // Sort keys to ensure consistent cache keys
    const sortedParams = Object.keys(searchParams)
      .sort()
      .reduce((acc, key) => {
        acc[key] = searchParams[key];
        return acc;
      }, {});

    return `search:${JSON.stringify(sortedParams)}`;
  } catch (error) {
    logger.error(`Error generating search cache key: ${error}`);
    return `search:${Date.now()}`;
  }
};

module.exports = {
  getRedisClient,
  setCache,
  getCache,
  deleteCache,
  clearCacheByPattern,
  generateSearchCacheKey
};
