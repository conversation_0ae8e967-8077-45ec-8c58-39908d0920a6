import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Chip,
  Grid,
  styled
} from '@mui/material';
import {
  Close as CloseIcon,
  Download as DownloadIcon,
  ShoppingCart as PurchaseIcon,
  Visibility as PreviewIcon,
  Star as StarIcon
} from '@mui/icons-material';

const PreviewCard = styled(Card)(({ theme }) => ({
  borderRadius: 20,
  overflow: 'hidden',
  background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
  border: '1px solid rgba(102, 126, 234, 0.1)',
  position: 'relative',
  height: '100%',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: '0 20px 40px rgba(102, 126, 234, 0.2)'
  }
}));

const TemplateFrame = styled('iframe')({
  width: '100%',
  height: '600px',
  border: 'none',
  borderRadius: '15px',
  boxShadow: '0 10px 30px rgba(0,0,0,0.1)',
  transform: 'scale(0.8)',
  transformOrigin: 'top left',
  overflow: 'hidden'
});

const BiodataTemplatePreview = ({ template, open, onClose, onPurchase }) => {
  const [loading, setLoading] = useState(false);

  const handlePurchase = async () => {
    setLoading(true);
    try {
      await onPurchase(template);
    } finally {
      setLoading(false);
    }
  };

  const sampleData = {
    name: template?.targetGender === 'female' ? 'Priya Sharma' : 'Arjun Patil',
    tagline: template?.targetGender === 'female' ? 'Seeking a life partner with traditional values' : 'Looking for a caring and understanding life partner',
    age: template?.targetGender === 'female' ? '24' : '28',
    height: template?.targetGender === 'female' ? '5\'4"' : '5\'10"',
    education: template?.targetGender === 'female' ? 'MBA Finance' : 'B.Tech Computer Science',
    occupation: template?.targetGender === 'female' ? 'Financial Analyst' : 'Software Engineer',
    dateOfBirth: template?.targetGender === 'female' ? '15/03/1999' : '22/07/1995',
    birthPlace: 'Pune, Maharashtra',
    religion: 'Hindu',
    caste: 'Maratha',
    subCaste: '96 Kuli Maratha',
    gotra: 'Kashyap',
    fatherName: template?.targetGender === 'female' ? 'Mr. Rajesh Sharma' : 'Mr. Suresh Patil',
    fatherOccupation: 'Business Owner',
    motherName: template?.targetGender === 'female' ? 'Mrs. Sunita Sharma' : 'Mrs. Kavita Patil',
    motherOccupation: 'Homemaker',
    familyType: 'Nuclear Family',
    siblings: '1 Elder Brother (Married)',
    annualIncome: template?.targetGender === 'female' ? '₹8-10 Lakhs' : '₹12-15 Lakhs',
    company: template?.targetGender === 'female' ? 'HDFC Bank' : 'TCS Limited',
    partnerExpectations: 'Looking for a well-educated, family-oriented partner from a good Maratha family with similar values and traditions.',
    profilePicture: template?.targetGender === 'female' 
      ? 'https://images.unsplash.com/photo-*************-2616c6d4e6e8?w=200&h=250&fit=crop&crop=face'
      : 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=200&h=250&fit=crop&crop=face',
    additionalPhotos: template?.targetGender === 'female' 
      ? [
          'https://images.unsplash.com/photo-*************-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
          'https://images.unsplash.com/photo-*************-a5d8b219a5bb?w=150&h=150&fit=crop&crop=face',
          'https://images.unsplash.com/photo-*************-b1c1722653e1?w=150&h=150&fit=crop&crop=face'
        ]
      : [
          'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
          'https://images.unsplash.com/photo-*************-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',
          'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face'
        ]
  };

  const generatePreviewHTML = () => {
    if (!template) return '';
    
    // This would normally fetch the actual template HTML and replace placeholders
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${template.name} Preview</title>
        <style>
          body { 
            font-family: 'Inter', sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
          }
          .preview-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
          }
          .invocation {
            font-size: 24px;
            color: #667eea;
            font-weight: 600;
            margin-bottom: 20px;
          }
          .profile-section {
            display: flex;
            align-items: center;
            gap: 30px;
            margin-bottom: 30px;
          }
          .profile-photo {
            width: 150px;
            height: 200px;
            border-radius: 15px;
            object-fit: cover;
            border: 3px solid #667eea;
          }
          .profile-info h1 {
            font-size: 32px;
            color: #667eea;
            margin-bottom: 10px;
          }
          .profile-info p {
            font-size: 16px;
            color: #666;
            margin-bottom: 15px;
          }
          .details-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 30px;
          }
          .detail-item {
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            border-left: 4px solid #667eea;
          }
          .detail-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            margin-bottom: 5px;
          }
          .detail-value {
            font-size: 16px;
            font-weight: 600;
            color: #333;
          }
          .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #667eea;
            font-weight: 600;
          }
        </style>
      </head>
      <body>
        <div class="preview-container">
          <div class="header">
            <div class="invocation">॥ श्री गणेशाय नमः ॥</div>
          </div>
          
          <div class="profile-section">
            <img src="${sampleData.profilePicture}" alt="Profile Photo" class="profile-photo">
            <div class="profile-info">
              <h1>${sampleData.name}</h1>
              <p>${sampleData.tagline}</p>
              <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                <span style="background: #667eea; color: white; padding: 5px 15px; border-radius: 20px; font-size: 14px;">Age: ${sampleData.age}</span>
                <span style="background: #667eea; color: white; padding: 5px 15px; border-radius: 20px; font-size: 14px;">Height: ${sampleData.height}</span>
              </div>
            </div>
          </div>
          
          <div class="details-grid">
            <div class="detail-item">
              <div class="detail-label">Education</div>
              <div class="detail-value">${sampleData.education}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Occupation</div>
              <div class="detail-value">${sampleData.occupation}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Religion</div>
              <div class="detail-value">${sampleData.religion}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Caste</div>
              <div class="detail-value">${sampleData.caste}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Father's Name</div>
              <div class="detail-value">${sampleData.fatherName}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Mother's Name</div>
              <div class="detail-value">${sampleData.motherName}</div>
            </div>
          </div>
          
          <div class="footer">
            Powered by Vaivahik - The Premier Maratha Matrimony Platform
          </div>
        </div>
      </body>
      </html>
    `;
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 4,
          maxHeight: '90vh'
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        py: 3
      }}>
        <Box>
          <Typography variant="h5" fontWeight="700">
            {template?.name} - Live Preview
          </Typography>
          <Typography variant="body2" sx={{ opacity: 0.9, mt: 0.5 }}>
            See how your biodata will look with this premium template
          </Typography>
        </Box>
        <IconButton onClick={onClose} sx={{ color: 'white' }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      
      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ p: 3 }}>
          {/* Template Info */}
          <Box sx={{ mb: 3, p: 3, background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)', borderRadius: 3 }}>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={8}>
                <Typography variant="h6" fontWeight="600" gutterBottom>
                  {template?.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {template?.description}
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {template?.features?.slice(0, 3).map((feature, index) => (
                    <Chip
                      key={index}
                      label={feature}
                      size="small"
                      sx={{
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        color: '#667eea',
                        fontSize: '0.75rem'
                      }}
                    />
                  ))}
                </Box>
              </Grid>
              <Grid item xs={12} md={4} sx={{ textAlign: 'center' }}>
                <Typography variant="h4" fontWeight="800" sx={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>
                  ₹{template?.price}
                </Typography>
                {template?.discount && (
                  <Typography variant="body2" sx={{ textDecoration: 'line-through', color: 'text.secondary' }}>
                    ₹{template?.originalPrice}
                  </Typography>
                )}
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1, mt: 1 }}>
                  <StarIcon sx={{ color: '#FFD700', fontSize: 16 }} />
                  <Typography variant="body2" fontWeight="600">
                    {template?.rating} ({(template?.downloads / 1000).toFixed(1)}k downloads)
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Box>

          {/* Live Preview */}
          <Box sx={{ 
            border: '2px solid rgba(102, 126, 234, 0.1)', 
            borderRadius: 3, 
            overflow: 'hidden',
            background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
            p: 2
          }}>
            <Typography variant="h6" fontWeight="600" sx={{ mb: 2, color: '#667eea' }}>
              📄 Live Template Preview
            </Typography>
            <TemplateFrame
              srcDoc={generatePreviewHTML()}
              title={`${template?.name} Preview`}
            />
          </Box>
        </Box>
      </DialogContent>
      
      <DialogActions sx={{ p: 3, background: '#f8f9fa' }}>
        <Button
          variant="outlined"
          onClick={onClose}
          sx={{ 
            borderColor: '#667eea', 
            color: '#667eea',
            borderRadius: 3,
            px: 3
          }}
        >
          Close Preview
        </Button>
        <Button
          variant="contained"
          startIcon={<PurchaseIcon />}
          onClick={handlePurchase}
          disabled={loading}
          sx={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: 3,
            px: 4,
            py: 1.5,
            fontWeight: 700
          }}
        >
          {loading ? 'Processing...' : `Purchase for ₹${template?.price}`}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BiodataTemplatePreview;
