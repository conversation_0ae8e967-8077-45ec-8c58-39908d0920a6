// API endpoint for revenue reports
import axios from 'axios';
import { handleApiError } from '@/utils/errorHandler';
import { withAuth } from '@/utils/authHandler';

// Backend API URL - adjust this to your actual backend URL
const BACKEND_API_URL = 'http://localhost:3001/api';

async function handler(req, res) {
  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getRevenueReports(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    return handleApiError(error, res, 'Revenue Reports API');
  }
}

// GET /api/admin/revenue-reports
async function getRevenueReports(req, res) {
  try {
    // Get the auth token from the request cookies or headers
    const token = req.cookies?.adminToken || req.headers.authorization?.split(' ')[1];

    if (!token) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    // Get query parameters
    const { 
      period = 'month', 
      startDate,
      endDate,
      groupBy = 'day'
    } = req.query;

    try {
      // Fetch revenue reports from the backend API
      const response = await axios({
        method: 'GET',
        url: `${BACKEND_API_URL}/admin/revenue-reports`,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        params: {
          period,
          startDate,
          endDate,
          groupBy
        },
        timeout: 10000 // 10 second timeout
      });

      // Return the response from the backend
      return res.status(200).json(response.data);
    } catch (apiError) {
      // Log the error
      console.error('Error fetching revenue reports from backend API:', apiError.message);
      
      // Return a meaningful error message
      return res.status(503).json({
        success: false,
        message: 'Failed to fetch revenue reports from backend API.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available'
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Get revenue reports');
  }
}

// Export the handler with authentication middleware
export default withAuth(handler, 'ADMIN');
