// src/tasks/scheduledTasks.js

const cron = require('node-cron');
const notificationService = require('../services/notification.service');

/**
 * Initialize scheduled tasks
 */
function initScheduledTasks() {
    console.log('Initializing scheduled tasks...');
    
    // Send verification reminders daily at 10:00 AM
    cron.schedule('0 10 * * *', async () => {
        console.log('Running scheduled task: Send verification reminders');
        try {
            // Send reminders to users who registered more than 3 days ago
            const remindersSent = await notificationService.sendVerificationReminders(3);
            console.log(`Verification reminder task completed: ${remindersSent} reminders sent`);
        } catch (error) {
            console.error('Error in verification reminder task:', error);
        }
    });
    
    // Send follow-up reminders to users who still haven't verified after 7 days
    cron.schedule('0 11 * * *', async () => {
        console.log('Running scheduled task: Send follow-up verification reminders');
        try {
            // Send reminders to users who registered more than 7 days ago
            const remindersSent = await notificationService.sendVerificationReminders(7);
            console.log(`Follow-up verification reminder task completed: ${remindersSent} reminders sent`);
        } catch (error) {
            console.error('Error in follow-up verification reminder task:', error);
        }
    });
    
    console.log('Scheduled tasks initialized');
}

module.exports = {
    initScheduledTasks
};
