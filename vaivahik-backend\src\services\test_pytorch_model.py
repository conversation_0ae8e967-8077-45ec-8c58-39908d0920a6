"""
Test script for the PyTorch implementation of the two-tower model
"""

import os
import json
import numpy as np
from two_tower_model_pytorch import MatrimonyMatchingModel

def test_model_creation():
    """Test creating the model"""
    print("Testing model creation...")
    model = MatrimonyMatchingModel()
    model.build_model()
    print("Model created successfully!")
    return model

def test_prediction():
    """Test model prediction"""
    print("\nTesting model prediction...")
    model = MatrimonyMatchingModel()
    model.build_model()
    
    # Create dummy data
    num_samples = 5
    users = [{"id": f"user{i}"} for i in range(num_samples)]
    preferences = [{"ageMin": 25, "ageMax": 35} for _ in range(num_samples)]
    matches = [{"id": f"match{i}"} for i in range(num_samples)]
    
    # Get predictions
    scores = model.predict(users, preferences, matches)
    
    print(f"Generated {len(scores)} predictions:")
    for i, score in enumerate(scores):
        print(f"  User {users[i]['id']} + Match {matches[i]['id']} = {score:.4f}")
    
    return scores

def test_training():
    """Test model training"""
    print("\nTesting model training...")
    model = MatrimonyMatchingModel()
    model.build_model()
    
    # Create dummy data
    num_samples = 100
    users = [{"id": f"user{i}"} for i in range(num_samples)]
    preferences = [{"ageMin": 25, "ageMax": 35} for _ in range(num_samples)]
    matches = [{"id": f"match{i}"} for i in range(num_samples)]
    
    # Create dummy labels (1 for match, 0 for non-match)
    labels = np.random.randint(0, 2, num_samples).astype(float)
    
    # Train model
    history = model.train(users, preferences, matches, labels, validation_split=0.2)
    
    print("Training completed!")
    print(f"Final training loss: {history['train_loss'][-1]:.4f}")
    print(f"Final validation loss: {history['val_loss'][-1]:.4f}")
    print(f"Final training accuracy: {history['train_acc'][-1]:.4f}")
    print(f"Final validation accuracy: {history['val_acc'][-1]:.4f}")
    
    return history

def test_save_load():
    """Test saving and loading the model"""
    print("\nTesting model save/load...")
    model = MatrimonyMatchingModel()
    model.build_model()
    
    # Create directory if it doesn't exist
    os.makedirs("models", exist_ok=True)
    
    # Save model
    model.save("models/test_model")
    print("Model saved successfully!")
    
    # Load model
    new_model = MatrimonyMatchingModel()
    new_model.load("models/test_model")
    print("Model loaded successfully!")
    
    return new_model

def test_embeddings():
    """Test getting embeddings"""
    print("\nTesting embeddings...")
    model = MatrimonyMatchingModel()
    model.build_model()
    
    # Create dummy data
    user = {"id": "user1", "age": 30}
    preferences = {"ageMin": 25, "ageMax": 35}
    match = {"id": "match1", "age": 28}
    
    # Get embeddings
    user_embedding = model.get_user_embedding(user, preferences)
    match_embedding = model.get_match_embedding(match)
    
    print(f"User embedding shape: {user_embedding.shape}")
    print(f"Match embedding shape: {match_embedding.shape}")
    
    return user_embedding, match_embedding

def main():
    """Main function"""
    print("=== Testing PyTorch Two-Tower Model ===\n")
    
    try:
        # Test model creation
        model = test_model_creation()
        
        # Test prediction
        scores = test_prediction()
        
        # Test embeddings
        user_embedding, match_embedding = test_embeddings()
        
        # Test save/load
        new_model = test_save_load()
        
        # Test training (this will take longer)
        history = test_training()
        
        print("\n=== All tests passed! ===")
        
    except Exception as e:
        print(f"\nError during testing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
