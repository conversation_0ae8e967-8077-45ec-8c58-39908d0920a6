"""
Enhanced Two-Tower Model for Matrimony Matching (PyTorch Implementation)

This module implements an improved two-tower neural network model for matrimony matching using PyTorch.
Enhancements include:
1. Batch normalization for better training stability
2. Residual connections to prevent vanishing gradients
3. Improved similarity calculation with multiple metrics
4. Enhanced feature preprocessing
"""

import os
import json
import random
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedTowerNetwork(nn.Module):
    """Enhanced tower network with batch normalization and residual connections"""

    def __init__(self, input_dim, hidden_layers, embedding_size, dropout_rate=0.2):
        """
        Initialize the enhanced tower network

        Args:
            input_dim (int): Input dimension
            hidden_layers (list): List of hidden layer sizes
            embedding_size (int): Size of the embedding vector
            dropout_rate (float): Dropout rate
        """
        super(EnhancedTowerNetwork, self).__init__()

        # Batch normalization for input
        self.input_bn = nn.BatchNorm1d(input_dim)

        # Create layers with residual connections
        self.layers = nn.ModuleList()
        self.skip_connections = nn.ModuleList()

        prev_size = input_dim
        for i, size in enumerate(hidden_layers):
            # Main layer block
            layer_block = nn.Sequential(
                nn.Linear(prev_size, size),
                nn.BatchNorm1d(size),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            )
            self.layers.append(layer_block)

            # Skip connection if dimensions match, otherwise create projection
            if prev_size == size:
                self.skip_connections.append(nn.Identity())
            else:
                self.skip_connections.append(nn.Linear(prev_size, size))

            prev_size = size

        # Final embedding layer
        self.embedding_layer = nn.Linear(prev_size, embedding_size)
        self.embedding_bn = nn.BatchNorm1d(embedding_size)

    def forward(self, x):
        """Forward pass through the network"""
        # Apply input batch normalization
        x = self.input_bn(x)

        # Apply layers with residual connections
        for layer, skip in zip(self.layers, self.skip_connections):
            residual = skip(x)
            x = layer(x)
            x = x + residual  # Add residual connection

        # Final embedding
        x = self.embedding_layer(x)
        x = self.embedding_bn(x)
        return torch.tanh(x)  # Normalize embeddings to unit hypersphere

class EnhancedTwoTowerModel(nn.Module):
    """Enhanced Two-Tower Model for matrimony matching"""

    def __init__(self, user_input_dim, match_input_dim, config=None):
        """
        Initialize the Enhanced Two-Tower Model

        Args:
            user_input_dim (int): Dimension of user input
            match_input_dim (int): Dimension of match input
            config (dict): Configuration parameters for the model
        """
        super(EnhancedTwoTowerModel, self).__init__()

        # Default configuration
        self.default_config = {
            'user_tower_layers': [256, 128],
            'match_tower_layers': [256, 128],
            'embedding_size': 128,
            'dropout_rate': 0.2,
            'learning_rate': 0.001,
            'similarity_metrics': ['cosine', 'euclidean', 'dot'],
            'similarity_weights': [0.6, 0.2, 0.2],
            'batch_size': 64,
            'epochs': 10
        }

        # Use provided config or default
        self.config = config if config else self.default_config

        # Create user tower
        self.user_tower = EnhancedTowerNetwork(
            user_input_dim,
            self.config['user_tower_layers'],
            self.config['embedding_size'],
            self.config['dropout_rate']
        )

        # Create match tower
        self.match_tower = EnhancedTowerNetwork(
            match_input_dim,
            self.config['match_tower_layers'],
            self.config['embedding_size'],
            self.config['dropout_rate']
        )

        # Convert similarity weights to tensor
        self.register_buffer(
            'similarity_weights',
            torch.tensor(self.config['similarity_weights'], dtype=torch.float32)
        )

    def forward(self, user_input, match_input):
        """
        Forward pass

        Args:
            user_input (torch.Tensor): User profile and preference data
            match_input (torch.Tensor): Match profile data

        Returns:
            torch.Tensor: Match score
        """
        # Get embeddings
        user_embedding = self.user_tower(user_input)
        match_embedding = self.match_tower(match_input)

        # Calculate similarity using multiple metrics
        similarity = self.calculate_similarity(user_embedding, match_embedding)

        return similarity.view(-1, 1)

    def calculate_similarity(self, user_embedding, match_embedding):
        """
        Calculate similarity between user and match embeddings using multiple metrics

        Args:
            user_embedding (torch.Tensor): User embedding
            match_embedding (torch.Tensor): Match embedding

        Returns:
            torch.Tensor: Combined similarity score
        """
        similarities = []

        # Calculate cosine similarity
        if 'cosine' in self.config['similarity_metrics']:
            cosine_sim = F.cosine_similarity(user_embedding, match_embedding, dim=1)
            cosine_sim = (cosine_sim + 1) / 2  # Scale from [-1,1] to [0,1]
            similarities.append(cosine_sim)

        # Calculate Euclidean distance-based similarity
        if 'euclidean' in self.config['similarity_metrics']:
            euclidean_dist = torch.sqrt(torch.sum((user_embedding - match_embedding) ** 2, dim=1))
            euclidean_sim = torch.exp(-euclidean_dist)  # Convert distance to similarity
            similarities.append(euclidean_sim)

        # Calculate dot product similarity
        if 'dot' in self.config['similarity_metrics']:
            dot_sim = torch.sum(user_embedding * match_embedding, dim=1)
            dot_sim = torch.sigmoid(dot_sim)  # Scale to [0,1]
            similarities.append(dot_sim)

        # Combine similarities using weights
        if len(similarities) == 1:
            return similarities[0]
        else:
            # Use only the weights for the metrics we calculated
            weights = self.similarity_weights[:len(similarities)]
            weights = weights / weights.sum()  # Normalize weights to sum to 1

            # Weighted sum of similarities
            combined_sim = torch.zeros_like(similarities[0])
            for i, sim in enumerate(similarities):
                combined_sim += weights[i] * sim

            return combined_sim

class EnhancedMatrimonyMatchingModel:
    """Wrapper class for the enhanced two-tower model"""

    def __init__(self, config=None):
        """
        Initialize the model

        Args:
            config (dict): Configuration parameters for the model
        """
        # Default configuration
        self.default_config = {
            'user_tower_layers': [256, 128],
            'match_tower_layers': [256, 128],
            'embedding_size': 128,
            'dropout_rate': 0.2,
            'learning_rate': 0.001,
            'similarity_metrics': ['cosine', 'euclidean', 'dot'],
            'similarity_weights': [0.6, 0.2, 0.2],
            'batch_size': 64,
            'epochs': 10,
            'feature_stats': {}  # Will store feature statistics for normalization
        }

        # Use provided config or default
        self.config = config if config else self.default_config

        # Set device
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {self.device}")

        # Initialize model
        self.model = None

        # Feature definitions
        self.user_features = [
            'age', 'gender', 'height', 'religion', 'caste', 'subCaste', 'gotra',
            'education', 'occupation', 'income', 'city', 'state', 'maritalStatus'
        ]

        self.preference_features = [
            'minAge', 'maxAge', 'minHeight', 'maxHeight', 'religion', 'caste',
            'subCaste', 'education', 'occupation', 'minIncome', 'maxIncome',
            'city', 'state', 'maritalStatus'
        ]

        self.match_features = [
            'age', 'gender', 'height', 'religion', 'caste', 'subCaste', 'gotra',
            'education', 'occupation', 'income', 'city', 'state', 'maritalStatus'
        ]

        # Feature statistics for normalization
        self.feature_stats = self.config.get('feature_stats', {})

    def preprocess_features(self, user_data, preference_data, match_data):
        """
        Enhanced feature preprocessing

        Args:
            user_data (dict): User profile data
            preference_data (dict): User preference data
            match_data (dict): Match profile data

        Returns:
            tuple: Processed user features and match features
        """
        # Create copies to avoid modifying originals
        user_features = user_data.copy()
        match_features = match_data.copy()

        # Normalize numerical features
        numerical_features = ['age', 'height', 'income']

        for feature in numerical_features:
            # If we have statistics for this feature
            if feature in self.feature_stats:
                mean = self.feature_stats[feature]['mean']
                std = self.feature_stats[feature]['std']

                # Normalize user feature
                if feature in user_features:
                    user_features[feature] = (user_features[feature] - mean) / std

                # Normalize match feature
                if feature in match_features:
                    match_features[feature] = (match_features[feature] - mean) / std

        # Create compatibility features
        if preference_data:
            # Age compatibility (flexible)
            if 'minAge' in preference_data and 'maxAge' in preference_data and 'age' in match_features:
                min_age = preference_data['minAge']
                max_age = preference_data['maxAge']
                match_age = match_data['age']

                # Exact range match
                if min_age <= match_age <= max_age:
                    match_features['age_compatibility'] = 1.0
                else:
                    # Flexible age scoring
                    age_diff = min(abs(match_age - min_age), abs(match_age - max_age))
                    if age_diff <= 2:
                        match_features['age_compatibility'] = 0.9
                    elif age_diff <= 5:
                        match_features['age_compatibility'] = 0.7
                    elif age_diff <= 10:
                        match_features['age_compatibility'] = 0.4
                    else:
                        match_features['age_compatibility'] = 0.1

            # Height compatibility
            if 'minHeight' in preference_data and 'maxHeight' in preference_data and 'height' in match_features:
                min_height = preference_data['minHeight']
                max_height = preference_data['maxHeight']
                match_height = match_data['height']
                match_features['height_compatibility'] = 1.0 if min_height <= match_height <= max_height else 0.0

            # Income compatibility
            if 'minIncome' in preference_data and 'income' in match_features:
                min_income = preference_data['minIncome']
                match_income = match_data['income']
                match_features['income_compatibility'] = 1.0 if match_income >= min_income else 0.0

        # Create categorical match features (with flexibility)
        categorical_features = ['religion', 'caste', 'subCaste', 'education', 'occupation', 'city', 'state', 'maritalStatus']

        for feature in categorical_features:
            if feature in user_features and feature in match_features:
                # Exact match
                match_features[f'{feature}_exact_match'] = 1.0 if user_features[feature] == match_features[feature] else 0.0

                # Flexible compatibility
                if feature == 'religion':
                    match_features[f'{feature}_compatibility'] = self._get_religion_compatibility(
                        user_features[feature], match_features[feature]
                    )
                elif feature == 'caste':
                    match_features[f'{feature}_compatibility'] = self._get_caste_compatibility(
                        user_features[feature], match_features[feature]
                    )
                elif feature == 'education':
                    match_features[f'{feature}_compatibility'] = self._get_education_compatibility(
                        user_features[feature], match_features[feature]
                    )
                else:
                    # For other features, use exact match as compatibility
                    match_features[f'{feature}_compatibility'] = match_features[f'{feature}_exact_match']

        return user_features, match_features

    def _get_religion_compatibility(self, user_religion, match_religion):
        """Get religion compatibility score"""
        if user_religion == match_religion:
            return 1.0

        religion_groups = {
            'Hindu': ['Hindu', 'Jain', 'Buddhist', 'Sikh'],
            'Muslim': ['Muslim', 'Sufi'],
            'Christian': ['Christian', 'Catholic', 'Protestant']
        }

        for group in religion_groups.values():
            if user_religion in group and match_religion in group:
                return 0.8
        return 0.3

    def _get_caste_compatibility(self, user_caste, match_caste):
        """Get caste compatibility score"""
        if user_caste == match_caste:
            return 1.0

        caste_groups = {
            'Maratha': ['Maratha', 'Kunbi', 'Mali', 'Dhangar'],
            'Brahmin': ['Brahmin', 'Deshastha', 'Chitpavan', 'Karhade']
        }

        for group in caste_groups.values():
            if user_caste in group and match_caste in group:
                return 0.7
        return 0.2

    def _get_education_compatibility(self, user_education, match_education):
        """Get education compatibility score"""
        if user_education == match_education:
            return 1.0

        education_levels = {
            'PhD': ['PhD', 'Masters', 'Post Graduate'],
            'Masters': ['Masters', 'PhD', 'Post Graduate', 'Graduate'],
            'Post Graduate': ['Post Graduate', 'Masters', 'PhD', 'Graduate'],
            'Graduate': ['Graduate', 'Post Graduate', 'Masters', 'Diploma'],
            'Diploma': ['Diploma', 'Graduate', 'Certificate']
        }

        for level, compatible in education_levels.items():
            if user_education == level and match_education in compatible:
                return 0.8
        return 0.4

    def build_model(self):
        """Build the enhanced two-tower model architecture"""
        # Calculate input dimensions
        user_input_dim = len(self.user_features) + len(self.preference_features)
        match_input_dim = len(self.match_features)

        # Create model
        self.model = EnhancedTwoTowerModel(
            user_input_dim,
            match_input_dim,
            self.config
        ).to(self.device)

        # Create optimizer
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=self.config['learning_rate']
        )

        # Create loss function
        self.criterion = nn.BCELoss()

        return self.model
