"""
Hyperparameter Optimizer for Matrimony Matching Model

This module provides hyperparameter optimization for the matrimony matching model
using Bayesian optimization.
"""

import os
import json
import logging
import numpy as np
import torch
from torch.utils.data import DataLoader, TensorDataset
import optuna
from optuna.samplers import TPESampler
from datetime import datetime

from .enhanced_tower_model_pytorch import EnhancedMatrimonyMatchingModel
from .model_trainer import ModelTrainer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HyperparameterOptimizer:
    """Hyperparameter optimizer for matrimony matching model"""
    
    def __init__(self, config=None):
        """
        Initialize the hyperparameter optimizer
        
        Args:
            config (dict): Configuration parameters
        """
        # Default configuration
        self.default_config = {
            'n_trials': 50,
            'timeout': 3600 * 8,  # 8 hours
            'study_name': 'matrimony_model_optimization',
            'storage': None,  # Use in-memory storage
            'direction': 'minimize',
            'metric': 'val_loss',
            'n_jobs': 1,
            'model_dir': os.path.join(os.path.dirname(__file__), '../../models/hpo')
        }
        
        # Use provided config or default
        self.config = config if config else self.default_config
        
        # Create model directory if it doesn't exist
        os.makedirs(self.config['model_dir'], exist_ok=True)
        
        # Set device
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {self.device}")
    
    def optimize(self, user_features, match_features, labels, validation_split=0.2):
        """
        Optimize hyperparameters
        
        Args:
            user_features (list): List of user feature dictionaries
            match_features (list): List of match feature dictionaries
            labels (list): List of match labels (1 for match, 0 for non-match)
            validation_split (float): Fraction of data to use for validation
            
        Returns:
            dict: Best hyperparameters
        """
        # Create study
        study = optuna.create_study(
            study_name=self.config['study_name'],
            storage=self.config['storage'],
            direction=self.config['direction'],
            sampler=TPESampler(seed=42),
            load_if_exists=True
        )
        
        # Define objective function
        def objective(trial):
            # Sample hyperparameters
            params = self._sample_hyperparameters(trial)
            
            # Create model with these hyperparameters
            model = EnhancedMatrimonyMatchingModel(params)
            model.build_model()
            
            # Create trainer
            trainer = ModelTrainer(model, params)
            
            # Prepare data
            train_loader, val_loader = trainer.prepare_data(
                user_features, match_features, labels
            )
            
            # Train model
            history = trainer.train(train_loader, val_loader)
            
            # Get metric value
            if self.config['metric'] == 'val_loss':
                metric_value = min(history['val_loss']) if history['val_loss'] else float('inf')
            else:
                # For other metrics, you can add them here
                metric_value = float('inf')
            
            # Save trial results
            self._save_trial_results(trial.number, params, metric_value, history)
            
            return metric_value
        
        # Run optimization
        logger.info(f"Starting hyperparameter optimization with {self.config['n_trials']} trials")
        study.optimize(
            objective,
            n_trials=self.config['n_trials'],
            timeout=self.config['timeout'],
            n_jobs=self.config['n_jobs']
        )
        
        # Get best parameters
        best_params = study.best_params
        best_value = study.best_value
        
        logger.info(f"Best {self.config['metric']}: {best_value}")
        logger.info(f"Best parameters: {best_params}")
        
        # Save best parameters
        self._save_best_parameters(best_params, best_value)
        
        return best_params
    
    def _sample_hyperparameters(self, trial):
        """
        Sample hyperparameters for a trial
        
        Args:
            trial: Optuna trial
            
        Returns:
            dict: Hyperparameters
        """
        params = {
            # Model architecture
            'user_tower_layers': [
                trial.suggest_int('user_layer1', 64, 512),
                trial.suggest_int('user_layer2', 32, 256)
            ],
            'match_tower_layers': [
                trial.suggest_int('match_layer1', 64, 512),
                trial.suggest_int('match_layer2', 32, 256)
            ],
            'embedding_size': trial.suggest_int('embedding_size', 32, 256),
            'dropout_rate': trial.suggest_float('dropout_rate', 0.1, 0.5),
            
            # Similarity metrics
            'similarity_metrics': ['cosine', 'euclidean', 'dot'],
            'similarity_weights': [
                trial.suggest_float('cosine_weight', 0.1, 0.8),
                trial.suggest_float('euclidean_weight', 0.1, 0.8),
                trial.suggest_float('dot_weight', 0.1, 0.8)
            ],
            
            # Training parameters
            'learning_rate': trial.suggest_float('learning_rate', 1e-4, 1e-2, log=True),
            'weight_decay': trial.suggest_float('weight_decay', 1e-5, 1e-2, log=True),
            'batch_size': trial.suggest_categorical('batch_size', [32, 64, 128, 256]),
            'epochs': 20,  # Fixed for optimization
            
            # Early stopping
            'early_stopping_patience': trial.suggest_int('early_stopping_patience', 3, 10),
            'reduce_lr_patience': trial.suggest_int('reduce_lr_patience', 2, 5),
            'reduce_lr_factor': trial.suggest_float('reduce_lr_factor', 0.1, 0.5),
            'gradient_clip_value': trial.suggest_float('gradient_clip_value', 0.5, 5.0),
            
            # Other
            'validation_split': 0.2,
            'model_dir': self.config['model_dir']
        }
        
        # Normalize similarity weights
        total_weight = sum(params['similarity_weights'])
        params['similarity_weights'] = [w / total_weight for w in params['similarity_weights']]
        
        return params
    
    def _save_trial_results(self, trial_number, params, metric_value, history):
        """
        Save trial results
        
        Args:
            trial_number (int): Trial number
            params (dict): Hyperparameters
            metric_value (float): Metric value
            history (dict): Training history
        """
        # Create trial directory
        trial_dir = os.path.join(self.config['model_dir'], f"trial_{trial_number}")
        os.makedirs(trial_dir, exist_ok=True)
        
        # Save parameters
        params_path = os.path.join(trial_dir, "params.json")
        with open(params_path, 'w') as f:
            json.dump(params, f, indent=2)
        
        # Save metric value
        metric_path = os.path.join(trial_dir, "metric.json")
        with open(metric_path, 'w') as f:
            json.dump({self.config['metric']: float(metric_value)}, f, indent=2)
        
        # Save history
        history_path = os.path.join(trial_dir, "history.json")
        with open(history_path, 'w') as f:
            # Convert numpy values to Python types
            serializable_history = {}
            for key, values in history.items():
                serializable_history[key] = [float(v) for v in values]
            
            json.dump(serializable_history, f, indent=2)
    
    def _save_best_parameters(self, best_params, best_value):
        """
        Save best parameters
        
        Args:
            best_params (dict): Best hyperparameters
            best_value (float): Best metric value
        """
        # Create results directory
        timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
        results_dir = os.path.join(self.config['model_dir'], f"best_{timestamp}")
        os.makedirs(results_dir, exist_ok=True)
        
        # Save best parameters
        params_path = os.path.join(results_dir, "best_params.json")
        with open(params_path, 'w') as f:
            json.dump(best_params, f, indent=2)
        
        # Save best metric value
        metric_path = os.path.join(results_dir, "best_metric.json")
        with open(metric_path, 'w') as f:
            json.dump({self.config['metric']: float(best_value)}, f, indent=2)
        
        logger.info(f"Best parameters saved to {params_path}")
    
    def train_with_best_params(self, user_features, match_features, labels, best_params=None):
        """
        Train a model with the best hyperparameters
        
        Args:
            user_features (list): List of user feature dictionaries
            match_features (list): List of match feature dictionaries
            labels (list): List of match labels (1 for match, 0 for non-match)
            best_params (dict): Best hyperparameters (if None, load from file)
            
        Returns:
            tuple: Trained model and trainer
        """
        # Load best parameters if not provided
        if best_params is None:
            # Find the most recent best parameters file
            best_dirs = [d for d in os.listdir(self.config['model_dir']) if d.startswith('best_')]
            if not best_dirs:
                logger.error("No best parameters found")
                return None, None
            
            # Sort by timestamp (newest first)
            best_dirs.sort(reverse=True)
            best_dir = os.path.join(self.config['model_dir'], best_dirs[0])
            
            # Load parameters
            params_path = os.path.join(best_dir, "best_params.json")
            with open(params_path, 'r') as f:
                best_params = json.load(f)
        
        # Create model with best parameters
        model = EnhancedMatrimonyMatchingModel(best_params)
        model.build_model()
        
        # Create trainer
        trainer = ModelTrainer(model, best_params)
        
        # Prepare data
        train_loader, val_loader = trainer.prepare_data(
            user_features, match_features, labels
        )
        
        # Train model
        logger.info("Training model with best hyperparameters")
        trainer.train(train_loader, val_loader)
        
        # Save model
        timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
        model_path = os.path.join(self.config['model_dir'], f"best_model_{timestamp}.pt")
        trainer.save_model(model_path)
        
        logger.info(f"Model trained with best hyperparameters saved to {model_path}")
        
        return model, trainer
