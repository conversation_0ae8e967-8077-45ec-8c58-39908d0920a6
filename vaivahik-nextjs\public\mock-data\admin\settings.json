{"success": true, "settings": [{"id": 1, "category": "general", "key": "site_name", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string", "description": "The name of the website", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 2, "category": "general", "key": "site_description", "value": "The premier matrimony platform for the Maratha community", "type": "string", "description": "A short description of the website", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 3, "category": "general", "key": "contact_email", "value": "<EMAIL>", "type": "string", "description": "The primary contact email address", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 4, "category": "general", "key": "support_phone", "value": "+91 9876543210", "type": "string", "description": "The customer support phone number", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 5, "category": "general", "key": "office_address", "value": "123 Main Street, Mumbai, Maharashtra, India", "type": "text", "description": "The physical office address", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 6, "category": "system", "key": "maintenance_mode", "value": "false", "type": "boolean", "description": "Whether the site is in maintenance mode", "isPublic": false, "updatedAt": "2023-07-15T10:30:00Z"}, {"id": 7, "category": "system", "key": "maintenance_message", "value": "We're currently performing scheduled maintenance. Please check back soon!", "type": "text", "description": "Message to display during maintenance", "isPublic": false, "updatedAt": "2023-07-15T10:30:00Z"}, {"id": 8, "category": "system", "key": "debug_mode", "value": "false", "type": "boolean", "description": "Whether debug mode is enabled", "isPublic": false, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 9, "category": "system", "key": "api_rate_limit", "value": "100", "type": "number", "description": "API rate limit per minute", "isPublic": false, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 10, "category": "system", "key": "cache_ttl", "value": "3600", "type": "number", "description": "Cache time-to-live in seconds", "isPublic": false, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 11, "category": "registration", "key": "allow_registrations", "value": "true", "type": "boolean", "description": "Whether new user registrations are allowed", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 12, "category": "registration", "key": "require_email_verification", "value": "true", "type": "boolean", "description": "Whether email verification is required for new accounts", "isPublic": false, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 13, "category": "registration", "key": "minimum_age_male", "value": "21", "type": "number", "description": "Minimum age for male users", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 14, "category": "registration", "key": "minimum_age_female", "value": "18", "type": "number", "description": "Minimum age for female users", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 15, "category": "registration", "key": "required_fields", "value": "[\"name\",\"email\",\"phone\",\"gender\",\"dob\",\"marital_status\",\"religion\",\"caste\"]", "type": "json", "description": "Required fields for registration", "isPublic": false, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 16, "category": "security", "key": "password_min_length", "value": "8", "type": "number", "description": "Minimum password length", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 17, "category": "security", "key": "password_requires_special_char", "value": "true", "type": "boolean", "description": "Whether passwords require special characters", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 18, "category": "security", "key": "login_attempts_before_lockout", "value": "5", "type": "number", "description": "Number of failed login attempts before account lockout", "isPublic": false, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 19, "category": "security", "key": "account_lockout_duration", "value": "30", "type": "number", "description": "Account lockout duration in minutes", "isPublic": false, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 20, "category": "security", "key": "jwt_expiry", "value": "86400", "type": "number", "description": "JWT token expiry in seconds", "isPublic": false, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 21, "category": "content", "key": "max_profile_photos", "value": "6", "type": "number", "description": "Maximum number of profile photos allowed", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 22, "category": "content", "key": "max_photo_size_kb", "value": "5120", "type": "number", "description": "Maximum photo size in KB", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 23, "category": "content", "key": "allowed_photo_types", "value": "[\"image/jpeg\",\"image/png\",\"image/webp\"]", "type": "json", "description": "Allowed photo file types", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 24, "category": "content", "key": "auto_photo_moderation", "value": "true", "type": "boolean", "description": "Whether automatic photo moderation is enabled", "isPublic": false, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 25, "category": "content", "key": "profanity_filter", "value": "true", "type": "boolean", "description": "Whether profanity filtering is enabled", "isPublic": false, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 26, "category": "payment", "key": "currency", "value": "INR", "type": "string", "description": "Default currency", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 27, "category": "payment", "key": "tax_rate", "value": "18", "type": "number", "description": "Tax rate percentage", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 28, "category": "payment", "key": "payment_gateways", "value": "[\"razorpay\",\"paytm\",\"upi\"]", "type": "json", "description": "Enabled payment gateways", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 29, "category": "payment", "key": "razorpay_key_id", "value": "rzp_test_1234567890", "type": "string", "description": "Razorpay Key ID", "isPublic": false, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 30, "category": "payment", "key": "razorpay_key_secret", "value": "REDACTED", "type": "string", "description": "Razorpay Key Secret", "isPublic": false, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 31, "category": "social", "key": "facebook_url", "value": "https://facebook.com/vaivahik", "type": "string", "description": "Facebook page URL", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 32, "category": "social", "key": "instagram_url", "value": "https://instagram.com/vaivahik", "type": "string", "description": "Instagram profile URL", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 33, "category": "social", "key": "twitter_url", "value": "https://twitter.com/vaivahik", "type": "string", "description": "Twitter profile URL", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 34, "category": "social", "key": "youtube_url", "value": "https://youtube.com/vaivahik", "type": "string", "description": "YouTube channel URL", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}, {"id": 35, "category": "social", "key": "whatsapp_number", "value": "+919876543210", "type": "string", "description": "WhatsApp contact number", "isPublic": true, "updatedAt": "2023-01-01T00:00:00Z"}], "categories": [{"id": "general", "name": "General Settings", "description": "Basic website settings", "icon": "settings"}, {"id": "system", "name": "System Settings", "description": "Technical system configuration", "icon": "computer"}, {"id": "registration", "name": "Registration Settings", "description": "User registration configuration", "icon": "person_add"}, {"id": "security", "name": "Security Settings", "description": "Security and authentication settings", "icon": "security"}, {"id": "content", "name": "Content Settings", "description": "Content management settings", "icon": "article"}, {"id": "payment", "name": "Payment Settings", "description": "Payment gateway configuration", "icon": "payment"}, {"id": "social", "name": "Social Media Settings", "description": "Social media links and integration", "icon": "share"}]}