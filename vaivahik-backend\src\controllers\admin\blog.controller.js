// src/controllers/admin/blog.controller.js

const logger = require('../../utils/logger');

// Mock data for blog posts
const mockBlogPosts = [
    {
        id: 1,
        title: "Finding Your Perfect Match: Tips for Creating an Attractive Profile",
        slug: "finding-perfect-match-attractive-profile",
        excerpt: "Learn how to create a profile that stands out and attracts the right matches.",
        content: "Creating an attractive profile is the first step towards finding your perfect match. Here are some essential tips...",
        featuredImage: "https://placehold.co/600x400/2196f3/ffffff?text=Profile+Tips",
        category: "PROFILE_TIPS",
        tags: ["profile", "tips", "matching"],
        status: "PUBLISHED",
        publishedAt: "2023-05-15T00:00:00Z",
        createdAt: "2023-05-15T00:00:00Z",
        updatedAt: "2023-05-15T00:00:00Z",
        author: {
            id: "admin1",
            name: "<PERSON><PERSON> <PERSON>"
        },
        views: 250,
        comments: 18
    },
    {
        id: 2,
        title: "The Importance of Family Values in Maratha Marriages",
        slug: "importance-family-values-maratha-marriages",
        excerpt: "Discover why family values play a crucial role in successful Maratha marriages.",
        content: "Family values have always been the cornerstone of Maratha culture. In this article, we explore...",
        featuredImage: "https://placehold.co/600x400/4caf50/ffffff?text=Family+Values",
        category: "CULTURE",
        tags: ["family", "values", "culture"],
        status: "PUBLISHED",
        publishedAt: "2023-06-20T00:00:00Z",
        createdAt: "2023-06-20T00:00:00Z",
        updatedAt: "2023-06-20T00:00:00Z",
        author: {
            id: "admin2",
            name: "Rajesh Patil"
        },
        views: 380,
        comments: 12
    },
    {
        id: 3,
        title: "Modern Dating vs Traditional Matchmaking: Finding the Balance",
        slug: "modern-dating-vs-traditional-matchmaking",
        excerpt: "Exploring the balance between modern dating approaches and traditional matchmaking.",
        content: "In today's digital age, the landscape of finding a life partner has evolved significantly...",
        featuredImage: "https://placehold.co/600x400/ff9800/ffffff?text=Dating+Balance",
        category: "RELATIONSHIPS",
        tags: ["dating", "traditional", "modern"],
        status: "DRAFT",
        publishedAt: null,
        createdAt: "2023-07-10T00:00:00Z",
        updatedAt: "2023-07-10T00:00:00Z",
        author: {
            id: "admin1",
            name: "Priya Sharma"
        },
        views: 0,
        comments: 0
    }
];

// Check if mock data should be used
const shouldUseMockData = () => {
    return process.env.NODE_ENV === 'development' || process.env.USE_MOCK_DATA === 'true';
};

/**
 * @description Get all blog posts with pagination
 * @route GET /api/admin/blog-posts
 */
exports.getBlogPosts = async (req, res, next) => {
    try {
        const { page = 1, limit = 10, search = '', category = '', status = '' } = req.query;

        if (shouldUseMockData()) {
            // Return mock data
            let filteredPosts = [...mockBlogPosts];

            // Apply search filter
            if (search) {
                filteredPosts = filteredPosts.filter(post =>
                    post.title.toLowerCase().includes(search.toLowerCase()) ||
                    post.content.toLowerCase().includes(search.toLowerCase()) ||
                    post.excerpt.toLowerCase().includes(search.toLowerCase())
                );
            }

            // Apply category filter
            if (category) {
                filteredPosts = filteredPosts.filter(post => post.category === category);
            }

            // Apply status filter
            if (status) {
                filteredPosts = filteredPosts.filter(post => post.status === status);
            }

            // Get unique categories
            const categories = [...new Set(mockBlogPosts.map(post => post.category))];

            // Apply pagination
            const pageNum = parseInt(page);
            const limitNum = parseInt(limit);
            const startIndex = (pageNum - 1) * limitNum;
            const endIndex = startIndex + limitNum;
            const paginatedPosts = filteredPosts.slice(startIndex, endIndex);

            return res.status(200).json({
                success: true,
                message: "Blog posts fetched successfully (Mock Data)",
                posts: paginatedPosts,
                pagination: {
                    total: filteredPosts.length,
                    page: pageNum,
                    limit: limitNum,
                    totalPages: Math.ceil(filteredPosts.length / limitNum)
                },
                categories,
                useMockData: true
            });
        }

        // Real database implementation
        const prisma = req.prisma;

        // Build where clause based on filters
        let whereClause = {};

        if (search) {
            whereClause.OR = [
                { title: { contains: search, mode: 'insensitive' } },
                { content: { contains: search, mode: 'insensitive' } },
                { excerpt: { contains: search, mode: 'insensitive' } }
            ];
        }

        if (category) {
            whereClause.category = category;
        }

        if (status) {
            whereClause.status = status;
        }

        // Get total count for pagination
        const totalPosts = await prisma.blogPost.count({
            where: whereClause
        });

        // Get paginated posts
        const posts = await prisma.blogPost.findMany({
            where: whereClause,
            orderBy: {
                createdAt: 'desc'
            },
            skip: (parseInt(page) - 1) * parseInt(limit),
            take: parseInt(limit),
            include: {
                author: {
                    select: {
                        id: true,
                        name: true
                    }
                }
            }
        });

        // Get unique categories for filtering
        const categories = await prisma.blogPost.findMany({
            select: {
                category: true
            },
            distinct: ['category']
        });

        res.status(200).json({
            success: true,
            message: "Blog posts fetched successfully",
            posts,
            pagination: {
                total: totalPosts,
                page: parseInt(page),
                limit: parseInt(limit),
                totalPages: Math.ceil(totalPosts / parseInt(limit))
            },
            categories: categories.map(c => c.category),
            useMockData: false
        });
    } catch (error) {
        logger.error("Error fetching blog posts:", error);
        next(error);
    }
};

/**
 * @description Get a single blog post by ID
 * @route GET /api/admin/content/blog/:id
 */
exports.getBlogPost = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;

    try {
        const post = await prisma.blogPost.findUnique({
            where: { id },
            include: {
                author: {
                    select: {
                        id: true,
                        name: true
                    }
                }
            }
        });

        if (!post) {
            return res.status(404).json({
                success: false,
                message: "Blog post not found"
            });
        }

        res.status(200).json({
            success: true,
            post
        });
    } catch (error) {
        console.error("Error fetching blog post:", error);
        next(error);
    }
};

/**
 * @description Create a new blog post
 * @route POST /api/admin/content/blog
 */
exports.createBlogPost = async (req, res, next) => {
    const prisma = req.prisma;
    const { title, slug, excerpt, content, featuredImage, category, tags, status } = req.body;
    const adminId = req.admin.id;

    try {
        // Validate required fields
        if (!title || !slug) {
            return res.status(400).json({
                success: false,
                message: "Title and slug are required"
            });
        }

        // Check if slug is unique
        const existingPost = await prisma.blogPost.findUnique({
            where: { slug }
        });

        if (existingPost) {
            return res.status(400).json({
                success: false,
                message: "A post with this slug already exists"
            });
        }

        // Create the blog post
        const post = await prisma.blogPost.create({
            data: {
                title,
                slug,
                excerpt: excerpt || title,
                content: content || "",
                featuredImage: featuredImage || "",
                category: category || "UNCATEGORIZED",
                tags: tags || [],
                status: status || "DRAFT",
                publishedAt: status === "PUBLISHED" ? new Date() : null,
                author: {
                    connect: { id: adminId }
                }
            }
        });

        res.status(201).json({
            success: true,
            message: "Blog post created successfully",
            post
        });
    } catch (error) {
        console.error("Error creating blog post:", error);
        next(error);
    }
};

/**
 * @description Update a blog post
 * @route PUT /api/admin/content/blog/:id
 */
exports.updateBlogPost = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;
    const { title, slug, excerpt, content, featuredImage, category, tags, status } = req.body;

    try {
        // Check if post exists
        const existingPost = await prisma.blogPost.findUnique({
            where: { id }
        });

        if (!existingPost) {
            return res.status(404).json({
                success: false,
                message: "Blog post not found"
            });
        }

        // Check if slug is unique (if changed)
        if (slug !== existingPost.slug) {
            const slugExists = await prisma.blogPost.findFirst({
                where: {
                    slug,
                    id: { not: id }
                }
            });

            if (slugExists) {
                return res.status(400).json({
                    success: false,
                    message: "A post with this slug already exists"
                });
            }
        }

        // Update the blog post
        const updatedPost = await prisma.blogPost.update({
            where: { id },
            data: {
                title: title || existingPost.title,
                slug: slug || existingPost.slug,
                excerpt: excerpt || existingPost.excerpt,
                content: content !== undefined ? content : existingPost.content,
                featuredImage: featuredImage !== undefined ? featuredImage : existingPost.featuredImage,
                category: category || existingPost.category,
                tags: tags || existingPost.tags,
                status: status || existingPost.status,
                publishedAt: status === "PUBLISHED" && !existingPost.publishedAt ? new Date() : existingPost.publishedAt
            }
        });

        res.status(200).json({
            success: true,
            message: "Blog post updated successfully",
            post: updatedPost
        });
    } catch (error) {
        console.error("Error updating blog post:", error);
        next(error);
    }
};

/**
 * @description Delete a blog post
 * @route DELETE /api/admin/content/blog/:id
 */
exports.deleteBlogPost = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;

    try {
        // Check if post exists
        const existingPost = await prisma.blogPost.findUnique({
            where: { id }
        });

        if (!existingPost) {
            return res.status(404).json({
                success: false,
                message: "Blog post not found"
            });
        }

        // Delete the blog post
        await prisma.blogPost.delete({
            where: { id }
        });

        res.status(200).json({
            success: true,
            message: "Blog post deleted successfully"
        });
    } catch (error) {
        console.error("Error deleting blog post:", error);
        next(error);
    }
};

/**
 * @description Get blog statistics
 * @route GET /api/admin/blog-posts/stats/overview
 */
exports.getBlogStats = async (req, res, next) => {
    try {
        if (shouldUseMockData()) {
            const stats = {
                totalPosts: mockBlogPosts.length,
                publishedPosts: mockBlogPosts.filter(p => p.status === 'PUBLISHED').length,
                draftPosts: mockBlogPosts.filter(p => p.status === 'DRAFT').length,
                totalViews: mockBlogPosts.reduce((sum, p) => sum + p.views, 0),
                totalComments: mockBlogPosts.reduce((sum, p) => sum + p.comments, 0),
                averageViews: Math.round(mockBlogPosts.reduce((sum, p) => sum + p.views, 0) / mockBlogPosts.length),
                categories: [...new Set(mockBlogPosts.map(p => p.category))].length,
                recentPosts: mockBlogPosts.slice(0, 5)
            };

            return res.status(200).json({
                success: true,
                message: "Blog statistics fetched successfully (Mock Data)",
                stats,
                useMockData: true
            });
        }

        // Real database implementation would go here
        return res.status(200).json({
            success: true,
            message: "Blog statistics fetched successfully (Real Data - Not Implemented Yet)",
            stats: {
                totalPosts: 0,
                publishedPosts: 0,
                draftPosts: 0,
                totalViews: 0,
                totalComments: 0,
                averageViews: 0,
                categories: 0,
                recentPosts: []
            },
            useMockData: false
        });

    } catch (error) {
        logger.error('Error fetching blog statistics:', error);
        next(error);
    }
};
