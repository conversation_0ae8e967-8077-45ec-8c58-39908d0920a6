// routes/similarity.routes.js

const express = require('express');
const router = express.Router();

// Import the controller functions
const similarityController = require('../controllers/similarityController.js');

// Import the authentication middleware
const { authenticateToken } = require('../middleware/auth.middleware.js');
// Import the feature access middleware
const { featureAccessMiddleware, trackFeatureUsage } = require('../middleware/featureAccess.middleware.js');

// --- Similarity and Compatibility Routes (Premium Features) ---
router.get('/similar-profiles/:userId', authenticateToken, featureAccessMiddleware, trackFeatureUsage, similarityController.findSimilarProfiles);
router.get('/compatibility/:userId', authenticateToken, featureAccessMiddleware, trackFeatureUsage, similarityController.getCompatibilityScore);

module.exports = router;
