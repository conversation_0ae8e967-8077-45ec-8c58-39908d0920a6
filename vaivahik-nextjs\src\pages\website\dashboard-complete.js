/**
 * Complete Website Dashboard - Full Screen Layout with Sidebar
 * Matches landing page design with comprehensive matrimony features
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  Button,
  Avatar,
  Chip,
  IconButton,
  Badge,
  Drawer,
  AppBar,
  Toolbar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
  useMediaQuery,
  CardMedia,
  CardActions,
  Rating,
  Tooltip,
  Divider,
  Paper,
  Container
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Dashboard as DashboardIcon,
  Search as SearchIcon,
  Person as PersonIcon,
  Message as MessageIcon,
  Favorite as FavoriteIcon,
  Star as StarIcon,
  Verified as VerifiedIcon,
  Premium as PremiumIcon,
  Menu as MenuIcon,
  Phone as PhoneIcon,
  Chat as ChatIcon,
  Psychology as KundliIcon,
  LocationOn as LocationIcon,
  Work as WorkIcon,
  School as EducationIcon,
  Height as HeightIcon,
  Cake as AgeIcon,
  FiberManualRecord as OnlineIcon,
  Logout as LogoutIcon,
  Home as HomeIcon,
  TrendingUp as TrendingUpIcon,
  Security as SecurityIcon,
  Description as BiodataIcon,
  Spotlight as SpotlightIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  Bookmark as BookmarkIcon,
  ContactPhone as ContactPhoneIcon,
  History as HistoryIcon
} from '@mui/icons-material';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';
import ProfileCompletionDashboard from '@/website/components/profile/ProfileCompletionDashboard';
import EnhancedMatchDashboard from '../../components/enhanced/EnhancedMatchDashboard';

// Styled Components matching landing page design
const DashboardContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  minHeight: '100vh',
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      radial-gradient(circle at 25% 25%, rgba(255, 105, 180, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(255, 182, 193, 0.1) 0%, transparent 50%)
    `,
    zIndex: 0
  }
}));

const Sidebar = styled(Drawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    width: 280,
    background: 'linear-gradient(180deg, #1a1a2e 0%, #16213e 100%)',
    color: 'white',
    border: 'none',
    boxShadow: '4px 0 20px rgba(0,0,0,0.1)',
    zIndex: theme.zIndex.drawer + 1
  }
}));

const TopBar = styled(AppBar)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  color: '#333',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
  borderBottom: '1px solid rgba(255, 255, 255, 0.2)',
  zIndex: theme.zIndex.drawer + 2
}));

const MainContent = styled(Box)(({ theme }) => ({
  flexGrow: 1,
  marginLeft: 0,
  transition: theme.transitions.create(['margin'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  [theme.breakpoints.up('md')]: {
    marginLeft: 280,
  },
}));

const ProfileCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  borderRadius: 20,
  border: '1px solid rgba(255, 255, 255, 0.2)',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  overflow: 'hidden',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 16px 48px rgba(0, 0, 0, 0.2)',
  }
}));

const StatsCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  borderRadius: 20,
  border: '1px solid rgba(255, 255, 255, 0.2)',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s ease',
  textAlign: 'center',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
  }
}));

const ActionButton = styled(Button)(({ theme, variant }) => ({
  borderRadius: 25,
  padding: '8px 20px',
  fontWeight: 600,
  textTransform: 'none',
  boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s ease',
  ...(variant === 'call' && {
    background: 'linear-gradient(135deg, #4CAF50, #8BC34A)',
    color: 'white',
    '&:hover': {
      background: 'linear-gradient(135deg, #45a049, #7cb342)',
      transform: 'translateY(-2px)',
      boxShadow: '0 6px 20px rgba(76, 175, 80, 0.4)'
    }
  }),
  ...(variant === 'chat' && {
    background: 'linear-gradient(135deg, #2196F3, #64B5F6)',
    color: 'white',
    '&:hover': {
      background: 'linear-gradient(135deg, #1976D2, #42A5F5)',
      transform: 'translateY(-2px)',
      boxShadow: '0 6px 20px rgba(33, 150, 243, 0.4)'
    }
  }),
  ...(variant === 'kundli' && {
    background: 'linear-gradient(135deg, #FF9800, #FFB74D)',
    color: 'white',
    '&:hover': {
      background: 'linear-gradient(135deg, #F57C00, #FF9800)',
      transform: 'translateY(-2px)',
      boxShadow: '0 6px 20px rgba(255, 152, 0, 0.4)'
    }
  })
}));

export default function UserDashboard() {
  const { user, logout } = useAuth();
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // State management
  const [activeTab, setActiveTab] = useState(0);
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    profileViews: 127,
    interests: 23,
    messages: 8,
    matches: 45
  });
  const [aiMatches, setAiMatches] = useState([]);
  const [notifications, setNotifications] = useState(3);

  // Navigation items
  const navigationItems = [
    { label: 'Dashboard', icon: DashboardIcon, value: 0 },
    { label: 'AI Matches', icon: StarIcon, value: 1 },
    { label: 'Search', icon: SearchIcon, value: 2, route: '/search' },
    { label: 'My Profile', icon: PersonIcon, value: 3 },
    { label: 'Messages', icon: MessageIcon, value: 4, premium: true },
    { label: 'Interests', icon: FavoriteIcon, value: 5, route: '/interests' },
    { label: 'Shortlist', icon: BookmarkIcon, value: 6, route: '/shortlist' },
    { label: 'Contacts', icon: ContactPhoneIcon, value: 7, route: '/contacts' },
    { label: 'Interactions', icon: HistoryIcon, value: 8, route: '/interactions' },
    { label: 'Verification', icon: SecurityIcon, value: 9 },
    { label: 'Biodata', icon: BiodataIcon, value: 10 },
    { label: 'Spotlight', icon: SpotlightIcon, value: 11, premium: true },
    { label: 'Settings', icon: SettingsIcon, value: 12 },
  ];

  // Mock AI-powered matches data
  const mockAiMatches = [
    {
      id: 1,
      name: 'Priya Sharma',
      age: 26,
      height: '5\'4"',
      location: 'Mumbai, Maharashtra',
      education: 'MBA Finance',
      occupation: 'Financial Analyst',
      photo: '/api/placeholder/300/400',
      compatibility: 94,
      isOnline: true,
      verified: true,
      premium: false,
      interests: ['Reading', 'Traveling', 'Cooking'],
      about: 'Looking for a life partner who shares similar values and dreams.',
      aiInsight: 'Perfect match based on career ambitions and family values'
    },
    {
      id: 2,
      name: 'Anita Patil',
      age: 24,
      height: '5\'2"',
      location: 'Pune, Maharashtra',
      education: 'B.Tech Computer Science',
      occupation: 'Software Engineer',
      photo: '/api/placeholder/300/400',
      compatibility: 89,
      isOnline: false,
      verified: true,
      premium: true,
      interests: ['Technology', 'Music', 'Fitness'],
      about: 'Passionate about technology and looking for someone who understands my ambitions.',
      aiInsight: 'Excellent compatibility in professional goals and lifestyle'
    },
    {
      id: 3,
      name: 'Kavya Desai',
      age: 27,
      height: '5\'5"',
      location: 'Nashik, Maharashtra',
      education: 'M.Sc Biotechnology',
      occupation: 'Research Scientist',
      photo: '/api/placeholder/300/400',
      compatibility: 87,
      isOnline: true,
      verified: false,
      premium: false,
      interests: ['Science', 'Nature', 'Photography'],
      about: 'Love exploring nature and conducting research. Seeking a understanding partner.',
      aiInsight: 'Strong match in intellectual interests and life philosophy'
    }
  ];

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Set AI matches
      setAiMatches(mockAiMatches);
      
      // Simulate loading
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (newValue) => {
    // Check if this navigation item has a route
    const navItem = navigationItems.find(item => item.value === newValue);
    if (navItem && navItem.route) {
      // Navigate to external route
      router.push(navItem.route);
    } else {
      // Handle internal tab navigation
      setActiveTab(newValue);
    }
  };

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/login');
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Failed to logout');
    }
  };

  const handleProfileAction = (action, profileId) => {
    switch (action) {
      case 'call':
        toast.info(`Initiating call to profile ${profileId}`);
        break;
      case 'chat':
        router.push(`/chat/${profileId}`);
        break;
      case 'kundli':
        router.push(`/kundli/${profileId}`);
        break;
      case 'interest':
        toast.success(`Interest sent to profile ${profileId}`);
        break;
      default:
        break;
    }
  };

  const renderDashboardOverview = () => (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Welcome Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" fontWeight="700" sx={{ color: 'white', mb: 2 }}>
          Welcome back, {user?.name || 'Beautiful Soul'}! ✨
        </Typography>
        <Typography variant="h6" sx={{ color: 'rgba(255,255,255,0.8)', mb: 4 }}>
          Your perfect match is waiting to meet you. Let's make magic happen! 💕
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {[
          { icon: TrendingUpIcon, label: 'Profile Views', value: stats.profileViews, color: '#2196F3' },
          { icon: FavoriteIcon, label: 'Interests', value: stats.interests, color: '#E91E63' },
          { icon: MessageIcon, label: 'Messages', value: stats.messages, color: '#4CAF50' },
          { icon: StarIcon, label: 'AI Matches', value: stats.matches, color: '#FF9800' }
        ].map((stat, index) => (
          <Grid item xs={6} md={3} key={index}>
            <StatsCard>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{
                  width: 64,
                  height: 64,
                  borderRadius: '50%',
                  background: `${stat.color}20`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto 16px'
                }}>
                  <stat.icon sx={{ fontSize: 32, color: stat.color }} />
                </Box>
                <Typography variant="h4" fontWeight="700" color={stat.color} gutterBottom>
                  {stat.value}
                </Typography>
                <Typography variant="body2" color="text.secondary" fontWeight="500">
                  {stat.label}
                </Typography>
              </CardContent>
            </StatsCard>
          </Grid>
        ))}
      </Grid>

      {/* AI-Powered Matches Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" fontWeight="700" sx={{ color: 'white', mb: 3 }}>
          🤖 AI-Powered Perfect Matches
        </Typography>
        <Grid container spacing={3}>
          {aiMatches.map((match) => (
            <Grid item xs={12} sm={6} md={4} key={match.id}>
              <ProfileCard>
                <CardMedia
                  component="img"
                  height="300"
                  image={match.photo}
                  alt={match.name}
                  sx={{ objectFit: 'cover' }}
                />
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6" fontWeight="600" sx={{ flexGrow: 1 }}>
                      {match.name}, {match.age}
                    </Typography>
                    {match.isOnline && (
                      <OnlineIcon sx={{ color: '#4CAF50', fontSize: 12, mr: 1 }} />
                    )}
                    {match.verified && (
                      <VerifiedIcon sx={{ color: '#4CAF50', fontSize: 20 }} />
                    )}
                    {match.premium && (
                      <PremiumIcon sx={{ color: '#FFD700', fontSize: 20, ml: 0.5 }} />
                    )}
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <LocationIcon sx={{ fontSize: 16, color: '#666', mr: 1 }} />
                    <Typography variant="body2" color="text.secondary">
                      {match.location}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <WorkIcon sx={{ fontSize: 16, color: '#666', mr: 1 }} />
                    <Typography variant="body2" color="text.secondary">
                      {match.occupation}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <EducationIcon sx={{ fontSize: 16, color: '#666', mr: 1 }} />
                    <Typography variant="body2" color="text.secondary">
                      {match.education}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <HeightIcon sx={{ fontSize: 16, color: '#666', mr: 1 }} />
                    <Typography variant="body2" color="text.secondary">
                      {match.height}
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Chip
                      label={`${match.compatibility}% Match`}
                      color="primary"
                      size="small"
                      sx={{
                        background: 'linear-gradient(135deg, #FF69B4, #FF1493)',
                        color: 'white',
                        fontWeight: 600
                      }}
                    />
                  </Box>

                  <Typography variant="body2" color="primary" sx={{ 
                    fontStyle: 'italic', 
                    mb: 2,
                    background: 'linear-gradient(135deg, #667eea, #764ba2)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    fontWeight: 600
                  }}>
                    🤖 AI Insight: {match.aiInsight}
                  </Typography>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {match.about}
                  </Typography>
                </CardContent>

                <CardActions sx={{ p: 3, pt: 0, gap: 1 }}>
                  <ActionButton
                    variant="call"
                    size="small"
                    startIcon={<PhoneIcon />}
                    onClick={() => handleProfileAction('call', match.id)}
                  >
                    Call
                  </ActionButton>
                  <ActionButton
                    variant="chat"
                    size="small"
                    startIcon={<ChatIcon />}
                    onClick={() => handleProfileAction('chat', match.id)}
                  >
                    Chat
                  </ActionButton>
                  <ActionButton
                    variant="kundli"
                    size="small"
                    startIcon={<KundliIcon />}
                    onClick={() => handleProfileAction('kundli', match.id)}
                  >
                    Kundli
                  </ActionButton>
                  <IconButton
                    color="primary"
                    onClick={() => handleProfileAction('interest', match.id)}
                    sx={{
                      background: 'linear-gradient(135deg, #FF69B4, #FF1493)',
                      color: 'white',
                      '&:hover': {
                        background: 'linear-gradient(135deg, #FF1493, #DC143C)',
                      }
                    }}
                  >
                    <FavoriteIcon />
                  </IconButton>
                </CardActions>
              </ProfileCard>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Container>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 0:
        return renderDashboardOverview();
      case 1:
        return <EnhancedMatchDashboard user={user} />;
      case 3: // My Profile - Profile Completion
        return (
          <Container maxWidth="xl" sx={{ py: 4 }}>
            <Box sx={{
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(20px)',
              borderRadius: 4,
              p: 4,
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
            }}>
              <Typography variant="h4" gutterBottom sx={{ mb: 3, fontWeight: 700 }}>
                Complete Your Profile
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
                Complete your profile to get better matches and increase your visibility
              </Typography>
              <ProfileCompletionDashboard
                userData={user}
                onUpdateSection={(section) => {
                  toast.info(`Redirecting to ${section} section...`);
                }}
              />
            </Box>
          </Container>
        );
      default:
        return (
          <Container maxWidth="xl" sx={{ py: 4 }}>
            <Typography variant="h4" sx={{ color: 'white', textAlign: 'center', py: 8 }}>
              {navigationItems.find(item => item.value === activeTab)?.label} - Coming Soon
            </Typography>
          </Container>
        );
    }
  };

  return (
    <>
      <Head>
        <title>Dashboard - Find Your Perfect Match | Vaivahik</title>
        <meta name="description" content="Your personalized matrimony dashboard with AI-powered matches and comprehensive features" />
      </Head>

      <DashboardContainer>
        {/* Top Navigation Bar */}
        <TopBar position="fixed">
          <Toolbar>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              onClick={handleSidebarToggle}
              edge="start"
              sx={{ mr: 2, display: { md: 'none' } }}
            >
              <MenuIcon />
            </IconButton>
            
            <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1, fontWeight: 700 }}>
              Vaivahik - AI Matrimony Platform
            </Typography>

            <IconButton color="inherit" sx={{ mr: 1 }}>
              <Badge badgeContent={notifications} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>

            <IconButton color="inherit" onClick={handleLogout}>
              <LogoutIcon />
            </IconButton>
          </Toolbar>
        </TopBar>

        {/* Sidebar Navigation */}
        <Sidebar
          variant={isMobile ? "temporary" : "persistent"}
          open={sidebarOpen}
          onClose={handleSidebarToggle}
          ModalProps={{ keepMounted: true }}
        >
          <Toolbar />
          <Box sx={{ p: 3, textAlign: 'center', borderBottom: '1px solid rgba(255,255,255,0.1)' }}>
            <Avatar
              src={user?.profilePhoto || '/api/placeholder/80/80'}
              sx={{ width: 80, height: 80, margin: '0 auto 16px', border: '3px solid #4CAF50' }}
            />
            <Typography variant="h6" fontWeight="600" gutterBottom>
              {user?.name || 'Beautiful Soul'}
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.7 }}>
              {user?.location || 'Mumbai, India'}
            </Typography>
            <Chip
              label={user?.isPremium ? 'Premium Member' : 'Free Member'}
              size="small"
              sx={{
                mt: 1,
                background: user?.isPremium ? '#FFD700' : '#666',
                color: user?.isPremium ? '#000' : '#fff'
              }}
            />
          </Box>

          <List sx={{ px: 2, py: 2 }}>
            {navigationItems.map((item) => (
              <ListItem
                button
                key={item.value}
                onClick={() => handleTabChange(item.value)}
                sx={{
                  borderRadius: 2,
                  mb: 1,
                  backgroundColor: activeTab === item.value ? 'rgba(255,255,255,0.1)' : 'transparent',
                  '&:hover': {
                    backgroundColor: 'rgba(255,255,255,0.05)'
                  }
                }}
              >
                <ListItemIcon sx={{ color: 'white', minWidth: 40 }}>
                  <item.icon />
                </ListItemIcon>
                <ListItemText 
                  primary={item.label}
                  sx={{ 
                    '& .MuiListItemText-primary': { 
                      fontWeight: activeTab === item.value ? 600 : 400 
                    }
                  }}
                />
                {item.premium && (
                  <Chip
                    label="Premium"
                    size="small"
                    sx={{
                      background: '#FFD700',
                      color: '#000',
                      fontSize: '0.7rem',
                      height: 20
                    }}
                  />
                )}
              </ListItem>
            ))}
          </List>

          <Box sx={{ mt: 'auto', p: 2 }}>
            <Button
              fullWidth
              variant="contained"
              startIcon={<HomeIcon />}
              onClick={() => router.push('/')}
              sx={{
                background: 'linear-gradient(135deg, #667eea, #764ba2)',
                borderRadius: 2,
                py: 1.5
              }}
            >
              Back to Home
            </Button>
          </Box>
        </Sidebar>

        {/* Main Content Area */}
        <MainContent>
          <Toolbar />
          <Box sx={{ position: 'relative', zIndex: 1 }}>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
                <Typography variant="h6" sx={{ color: 'white' }}>Loading your perfect matches...</Typography>
              </Box>
            ) : (
              renderTabContent()
            )}
          </Box>
        </MainContent>
      </DashboardContainer>
    </>
  );
}
