// src/routes/admin/financial.routes.js
const express = require('express');
const router = express.Router();
const financialController = require('../../controllers/admin/financial.controller');
const authenticateAdmin = require('../../middleware/adminAuth.middleware.js');

// Subscription Routes
router.get('/subscriptions', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        financialController.getSubscriptions(req, res, next);
    });
});

router.get('/subscriptions/:id', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        financialController.getSubscriptionById(req, res, next);
    });
});

router.put('/subscriptions/:id', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        financialController.updateSubscription(req, res, next);
    });
});

// Transaction Routes
router.get('/transactions', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        financialController.getTransactions(req, res, next);
    });
});

router.get('/transactions/:id', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        financialController.getTransactionById(req, res, next);
    });
});

router.put('/transactions/:id', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        financialController.updateTransaction(req, res, next);
    });
});

router.post('/transactions/:id/refund', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        financialController.processRefund(req, res, next);
    });
});

router.get('/transactions/export/csv', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        financialController.exportTransactionsCSV(req, res, next);
    });
});

// Revenue Report Routes
router.get('/reports/revenue', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        financialController.getRevenueReports(req, res, next);
    });
});

router.get('/reports/revenue/export/csv', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        financialController.exportRevenueReportCSV(req, res, next);
    });
});

module.exports = router;
