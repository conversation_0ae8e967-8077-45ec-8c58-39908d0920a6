/**
 * Rate Limiting Middleware
 * 
 * This middleware implements rate limiting for API endpoints to prevent abuse.
 * It uses a simple in-memory store for development and Redis for production.
 */

const logger = require('../utils/logger');

// In-memory store for rate limiting (for development)
const inMemoryStore = new Map();

// Rate limit options
const defaultOptions = {
  windowMs: process.env.RATE_LIMIT_WINDOW_MS || 15 * 60 * 1000, // 15 minutes by default
  max: process.env.RATE_LIMIT_MAX_REQUESTS || 100, // 100 requests per windowMs by default
  message: 'Too many requests, please try again later.',
  statusCode: 429,
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
};

/**
 * Get client IP address
 * @param {Object} req - Express request object
 * @returns {string} - Client IP address
 */
const getClientIp = (req) => {
  // Get IP from various headers or connection
  return req.headers['x-forwarded-for']?.split(',')[0].trim() ||
    req.headers['x-real-ip'] ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    req.connection.socket?.remoteAddress ||
    'unknown';
};

/**
 * Create a rate limiter middleware
 * @param {Object} options - Rate limiter options
 * @returns {Function} - Express middleware
 */
const createRateLimiter = (options = {}) => {
  // Merge default options with provided options
  const opts = { ...defaultOptions, ...options };
  
  // Return middleware function
  return (req, res, next) => {
    // Skip rate limiting if disabled
    if (process.env.ENABLE_RATE_LIMIT !== 'true' && process.env.NODE_ENV !== 'production') {
      return next();
    }
    
    // Get client identifier (IP address by default)
    const clientId = getClientIp(req);
    
    // Get current timestamp
    const now = Date.now();
    
    // Get client data from store
    let clientData = inMemoryStore.get(clientId) || { count: 0, resetTime: now + opts.windowMs };
    
    // Reset count if window has passed
    if (now > clientData.resetTime) {
      clientData = { count: 0, resetTime: now + opts.windowMs };
    }
    
    // Increment request count
    clientData.count += 1;
    
    // Update store
    inMemoryStore.set(clientId, clientData);
    
    // Set rate limit headers
    const remaining = Math.max(0, opts.max - clientData.count);
    res.setHeader('RateLimit-Limit', opts.max);
    res.setHeader('RateLimit-Remaining', remaining);
    res.setHeader('RateLimit-Reset', Math.ceil(clientData.resetTime / 1000));
    
    // Check if rate limit exceeded
    if (clientData.count > opts.max) {
      logger.warn(`Rate limit exceeded for ${clientId}`, {
        ip: clientId,
        path: req.path,
        method: req.method,
        count: clientData.count,
        limit: opts.max,
      });
      
      return res.status(opts.statusCode).json({
        success: false,
        message: opts.message,
        retryAfter: Math.ceil((clientData.resetTime - now) / 1000),
      });
    }
    
    // Continue to next middleware
    next();
  };
};

// Create default rate limiter
const rateLimiter = createRateLimiter();

// Create API rate limiter with stricter limits
const apiRateLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per 15 minutes
  message: 'Too many API requests, please try again later.',
});

// Create auth rate limiter with stricter limits
const authRateLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // 10 requests per 15 minutes
  message: 'Too many authentication attempts, please try again later.',
});

module.exports = {
  rateLimiter,
  apiRateLimiter,
  authRateLimiter,
  createRateLimiter,
};
