import { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Divider,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Switch,
  TextField,
  Typography,
  Button,
  Chip,
  CircularProgress,
  Alert,
  IconButton
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import { toast } from 'react-toastify';

export default function ModerationSettings() {
  const [settings, setSettings] = useState({
    enabled: true,
    strictness: 'medium',
    autoReject: true,
    maskProfanity: true,
    maskCharacter: '*',
    allowedDomains: [],
    maxRepeatedCharacters: 5,
    maxConsecutiveCapitals: 10,
    spamThreshold: 0.7
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  // For allowed domains input
  const [newDomain, setNewDomain] = useState('');

  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

  // Fetch settings on component mount
  useEffect(() => {
    fetchSettings();
  }, []);

  // Fetch settings from API
  const fetchSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('adminToken');

      const response = await fetch(`${apiUrl}/api/admin/text-moderation/settings`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch moderation settings');
      }

      const data = await response.json();

      if (data.success) {
        setSettings(data.settings);
      } else {
        throw new Error(data.message || 'Failed to fetch moderation settings');
      }
    } catch (error) {
      console.error('Error fetching moderation settings:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Save settings to API
  const saveSettings = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(false);

      const token = localStorage.getItem('adminToken');

      const response = await fetch(`${apiUrl}/api/admin/text-moderation/settings`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ settings })
      });

      if (!response.ok) {
        throw new Error('Failed to update moderation settings');
      }

      const data = await response.json();

      if (data.success) {
        setSuccess(true);
        toast.success('Moderation settings updated successfully');
      } else {
        throw new Error(data.message || 'Failed to update moderation settings');
      }
    } catch (error) {
      console.error('Error updating moderation settings:', error);
      setError(error.message);
      toast.error(error.message);
    } finally {
      setSaving(false);
    }
  };

  // Handle input change
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle number input change
  const handleNumberChange = (e) => {
    const { name, value } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: parseFloat(value)
    }));
  };

  // Add domain to allowed domains
  const handleAddDomain = () => {
    if (!newDomain || !newDomain.includes('.')) {
      toast.error('Please enter a valid domain');
      return;
    }

    if (settings.allowedDomains.includes(newDomain)) {
      toast.error('Domain already exists in the list');
      return;
    }

    setSettings(prev => ({
      ...prev,
      allowedDomains: [...prev.allowedDomains, newDomain]
    }));

    setNewDomain('');
  };

  // Remove domain from allowed domains
  const handleRemoveDomain = (domain) => {
    setSettings(prev => ({
      ...prev,
      allowedDomains: prev.allowedDomains.filter(d => d !== domain)
    }));
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          Settings updated successfully
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="General Settings" />
            <Divider />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.enabled}
                        onChange={handleChange}
                        name="enabled"
                        color="primary"
                      />
                    }
                    label="Enable Text Moderation"
                  />
                  <FormHelperText>
                    When disabled, no moderation will be applied to messages
                  </FormHelperText>
                </Grid>

                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Strictness Level</InputLabel>
                    <Select
                      value={settings.strictness}
                      onChange={handleChange}
                      name="strictness"
                      label="Strictness Level"
                    >
                      <MenuItem value="low">Low</MenuItem>
                      <MenuItem value="medium">Medium</MenuItem>
                      <MenuItem value="high">High</MenuItem>
                    </Select>
                    <FormHelperText>
                      Low: Only reject severe violations<br />
                      Medium: Balance between user experience and safety<br />
                      High: Strict moderation, reject most violations
                    </FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.autoReject}
                        onChange={handleChange}
                        name="autoReject"
                        color="primary"
                      />
                    }
                    label="Auto-Reject Inappropriate Messages"
                  />
                  <FormHelperText>
                    When enabled, messages that violate policies will be automatically rejected
                  </FormHelperText>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Profanity Settings" />
            <Divider />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.maskProfanity}
                        onChange={handleChange}
                        name="maskProfanity"
                        color="primary"
                      />
                    }
                    label="Mask Profanity"
                  />
                  <FormHelperText>
                    When enabled, profanity will be masked with the character below
                  </FormHelperText>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    label="Mask Character"
                    value={settings.maskCharacter}
                    onChange={handleChange}
                    name="maskCharacter"
                    inputProps={{ maxLength: 1 }}
                    disabled={!settings.maskProfanity}
                    fullWidth
                    helperText="Character used to mask profanity (e.g., * or #)"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Spam Detection Settings" />
            <Divider />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    label="Max Repeated Characters"
                    type="number"
                    value={settings.maxRepeatedCharacters}
                    onChange={handleNumberChange}
                    name="maxRepeatedCharacters"
                    fullWidth
                    InputProps={{ inputProps: { min: 1 } }}
                    helperText="Maximum number of repeated characters allowed (e.g., 'hellooooo')"
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    label="Max Consecutive Capitals"
                    type="number"
                    value={settings.maxConsecutiveCapitals}
                    onChange={handleNumberChange}
                    name="maxConsecutiveCapitals"
                    fullWidth
                    InputProps={{ inputProps: { min: 1 } }}
                    helperText="Maximum number of consecutive capital letters allowed"
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    label="Spam Threshold"
                    type="number"
                    value={settings.spamThreshold}
                    onChange={handleNumberChange}
                    name="spamThreshold"
                    fullWidth
                    InputProps={{ inputProps: { min: 0, max: 1, step: 0.1 } }}
                    helperText="Threshold for spam detection (0.0 to 1.0)"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Contact Information Settings" />
            <Divider />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom>
                    Allowed Email Domains
                  </Typography>
                  <Typography variant="body2" color="textSecondary" paragraph>
                    Emails with these domains will not be flagged as contact information
                  </Typography>

                  <Box sx={{ display: 'flex', mb: 2 }}>
                    <TextField
                      label="Add Domain"
                      value={newDomain}
                      onChange={(e) => setNewDomain(e.target.value)}
                      placeholder="example.com"
                      fullWidth
                    />
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={handleAddDomain}
                      sx={{ ml: 1 }}
                    >
                      <AddIcon />
                    </Button>
                  </Box>

                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {settings.allowedDomains.map((domain) => (
                      <Chip
                        key={domain}
                        label={domain}
                        onDelete={() => handleRemoveDomain(domain)}
                        color="primary"
                        variant="outlined"
                      />
                    ))}
                    {settings.allowedDomains.length === 0 && (
                      <Typography variant="body2" color="textSecondary">
                        No domains added yet
                      </Typography>
                    )}
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<SaveIcon />}
          onClick={saveSettings}
          disabled={saving}
        >
          {saving ? 'Saving...' : 'Save Settings'}
        </Button>
      </Box>
    </Box>
  );
}
