# Vaivahik Project Testing Tools

This document provides instructions for using the testing tools created for the Vaivahik matrimony application. These tools will help you verify that all components of your application are working correctly.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Admin Functionality Testing](#admin-functionality-testing)
3. [API Endpoint Testing](#api-endpoint-testing)
4. [Project Structure Analysis](#project-structure-analysis)
5. [Manual Testing Checklist](#manual-testing-checklist)
6. [Interpreting Test Results](#interpreting-test-results)
7. [Troubleshooting](#troubleshooting)

## Prerequisites

Before running the tests, make sure you have the following:

- Node.js installed (version 14 or higher)
- Both frontend and backend servers running
- Required npm packages installed:
  - puppeteer (for admin functionality testing)
  - axios (for API endpoint testing)

You can install the required packages with:

```bash
npm install puppeteer axios
```

## Admin Functionality Testing

The admin functionality test uses Puppeteer to automate browser interactions and verify that admin pages load correctly.

### Running the Test

```bash
# Navigate to the project root directory
cd vaivahik-project

# Run the test
node vaivahik-nextjs/test-admin-functionality.js
```

### What It Tests

- Loads each admin page
- Verifies that the page title is correct
- Checks if the main content is present
- Takes screenshots of each page for visual inspection
- Generates a summary of passed and failed tests

### Test Results

The test will create a `test-screenshots` directory with screenshots of each admin page. It will also print a summary of the test results to the console.

## API Endpoint Testing

The API endpoint test makes HTTP requests to each endpoint and verifies the responses.

### Running the Test

```bash
# Navigate to the project root directory
cd vaivahik-project

# Run the test
node vaivahik-backend/test-api-endpoints.js
```

### What It Tests

- Makes requests to each API endpoint
- Verifies that the responses have the expected structure
- Checks HTTP status codes
- Saves response data for inspection
- Generates a summary of passed and failed tests

### Test Results

The test will create a `test-results` directory with JSON files containing the response data from each endpoint. It will also print a summary of the test results to the console and save a `summary.json` file with the overall results.

## Project Structure Analysis

The project structure analysis script scans your project directories to identify potential issues.

### Running the Analysis

```bash
# Navigate to the project root directory
cd vaivahik-project

# Run the analysis
node check-project-structure.js
```

### What It Analyzes

- Identifies duplicate files (based on content hash)
- Detects inconsistent naming patterns
- Finds potentially unused files (based on last modified date)
- Provides statistics about file types in the project

### Analysis Results

The analysis will create a `project-structure-analysis.json` file with detailed results. It will also print a summary of the findings to the console.

## Manual Testing Checklist

A comprehensive checklist for manual testing is provided in the `vaivahik-project-test-checklist.md` file. This checklist covers:

- Admin panel functionality
- Website functionality
- API endpoints
- Performance
- Security
- Mobile responsiveness
- Browser compatibility
- Feature flag testing

Use this checklist to systematically test all aspects of the application.

## Interpreting Test Results

### Admin Functionality Test

- **Success**: The page loaded correctly, has the expected title, and contains the main content.
- **Failure**: The page failed to load, has an incorrect title, or is missing the main content.

### API Endpoint Test

- **Success**: The endpoint returned a 2xx status code and the response has the expected structure.
- **Failure**: The endpoint returned an error status code or the response does not have the expected structure.

### Project Structure Analysis

- **Duplicates**: Files with identical content that may be redundant.
- **Naming Issues**: Files or directories that don't follow consistent naming conventions.
- **Potentially Unused**: Files that haven't been modified in a long time and might be unused.

## Troubleshooting

### Admin Functionality Test Issues

- **Error: Browser failed to launch**: Make sure you have Chrome installed or specify a different browser in the test script.
- **Error: Navigation timeout**: Increase the timeout value in the test script or check if the server is running correctly.
- **Error: Element not found**: The page structure may have changed; update the selectors in the test script.

### API Endpoint Test Issues

- **Error: Connection refused**: Make sure the backend server is running and the base URL is correct.
- **Error: Authentication failed**: Update the test credentials or implement a test-specific authentication mechanism.
- **Error: Unexpected response structure**: The API response format may have changed; update the expected structure in the test script.

### Project Structure Analysis Issues

- **Error: ENOENT: no such file or directory**: Make sure the project directories specified in the configuration exist.
- **High memory usage**: For very large projects, you may need to adjust the script to process files in batches.

## Extending the Tests

### Adding New Admin Pages

To add new admin pages to the test, edit the `config.adminPages` array in `test-admin-functionality.js`:

```javascript
adminPages: [
  // Add your new page here
  { path: '/admin/new-page', name: 'New Page' }
]
```

### Adding New API Endpoints

To add new API endpoints to the test, edit the `config.endpoints` array in `test-api-endpoints.js`:

```javascript
endpoints: [
  // Add your new endpoint here
  { method: 'GET', path: '/new-endpoint', name: 'New Endpoint' }
]
```

### Customizing the Project Structure Analysis

To customize the project structure analysis, edit the configuration options in `check-project-structure.js`:

```javascript
const config = {
  // Add or modify configuration options here
  projectDirs: ['new-directory'],
  excludeDirs: ['additional-exclude-dir']
}
```

## Conclusion

These testing tools provide a comprehensive way to verify the functionality of your Vaivahik matrimony application. By regularly running these tests, you can catch issues early and ensure that your application is working correctly.
