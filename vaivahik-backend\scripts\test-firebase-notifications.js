#!/usr/bin/env node

/**
 * Test Firebase Notifications Configuration and Functionality
 */

// Load environment variables
require('dotenv').config();

async function testFirebaseNotifications() {
    console.log('🔔 Testing Firebase Notifications...\n');
    
    // Check environment variables
    const requiredEnvVars = [
        'FIREBASE_PROJECT_ID',
        'FIREBASE_PRIVATE_KEY',
        'FIREBASE_CLIENT_EMAIL'
    ];
    
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
        console.log('❌ Missing Firebase environment variables:');
        missingVars.forEach(varName => {
            console.log(`   - ${varName}`);
        });
        console.log('\n💡 Required Firebase variables:');
        console.log('   - FIREBASE_PROJECT_ID');
        console.log('   - FIREBASE_PRIVATE_KEY');
        console.log('   - FIREBASE_CLIENT_EMAIL');
        return false;
    }
    
    console.log('✅ Firebase environment variables found');
    
    try {
        // Test Firebase Admin SDK initialization
        console.log('\n1️⃣ Testing Firebase Admin SDK initialization...');
        
        const admin = require('firebase-admin');
        
        // Check if already initialized
        if (admin.apps.length === 0) {
            const serviceAccount = {
                type: 'service_account',
                project_id: process.env.FIREBASE_PROJECT_ID,
                private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
                private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
                client_email: process.env.FIREBASE_CLIENT_EMAIL,
                client_id: process.env.FIREBASE_CLIENT_ID,
                auth_uri: 'https://accounts.google.com/o/oauth2/auth',
                token_uri: 'https://oauth2.googleapis.com/token',
                auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
                client_x509_cert_url: process.env.FIREBASE_CLIENT_CERT_URL
            };
            
            admin.initializeApp({
                credential: admin.credential.cert(serviceAccount),
                projectId: process.env.FIREBASE_PROJECT_ID
            });
        }
        
        console.log('✅ Firebase Admin SDK initialized successfully');
        
        // Test 2: Get Firebase Messaging instance
        console.log('\n2️⃣ Testing Firebase Messaging instance...');
        const messaging = admin.messaging();
        console.log('✅ Firebase Messaging instance created');
        
        // Test 3: Test notification structure
        console.log('\n3️⃣ Testing notification message structure...');
        
        const testNotification = {
            notification: {
                title: 'Test Notification',
                body: 'This is a test notification from Vaivahik'
            },
            data: {
                type: 'test',
                timestamp: new Date().toISOString()
            }
        };
        
        // Validate notification structure
        if (testNotification.notification && testNotification.notification.title && testNotification.notification.body) {
            console.log('✅ Notification structure is valid');
        } else {
            console.log('❌ Invalid notification structure');
            return false;
        }
        
        // Test 4: Test topic subscription (dry run)
        console.log('\n4️⃣ Testing topic operations...');
        
        const testTopic = 'test-topic';
        const dummyToken = 'dummy-fcm-token-for-testing';
        
        try {
            // This will fail with invalid token, but we can check if the method exists
            await messaging.subscribeToTopic([dummyToken], testTopic);
        } catch (error) {
            if (error.code === 'messaging/invalid-registration-token') {
                console.log('✅ Topic subscription method working (expected token error)');
            } else {
                console.log(`⚠️  Topic subscription error: ${error.code}`);
            }
        }
        
        // Test 5: Test sending to topic (dry run)
        console.log('\n5️⃣ Testing send to topic...');
        
        try {
            const topicMessage = {
                ...testNotification,
                topic: 'non-existent-topic-for-testing'
            };
            
            // This should fail gracefully
            await messaging.send(topicMessage, true); // dry run
            console.log('✅ Send to topic method working');
        } catch (error) {
            if (error.code === 'messaging/invalid-argument' || error.code === 'messaging/registration-token-not-registered') {
                console.log('✅ Send to topic method working (expected validation error)');
            } else {
                console.log(`⚠️  Send to topic error: ${error.code}`);
            }
        }
        
        console.log('\n📊 Firebase Configuration Summary:');
        console.log(`Project ID: ${process.env.FIREBASE_PROJECT_ID}`);
        console.log(`Client Email: ${process.env.FIREBASE_CLIENT_EMAIL}`);
        console.log(`Apps initialized: ${admin.apps.length}`);
        
        return true;
        
    } catch (error) {
        console.log('\n❌ Firebase Notifications test failed:');
        console.log(`Error: ${error.message}`);
        
        if (error.code) {
            console.log(`Error Code: ${error.code}`);
        }
        
        if (error.message.includes('private_key')) {
            console.log('\n💡 Private Key Issues:');
            console.log('1. Ensure FIREBASE_PRIVATE_KEY is properly formatted');
            console.log('2. Check for escaped newlines (\\n)');
            console.log('3. Verify the key is from the correct service account');
        }
        
        if (error.message.includes('project_id')) {
            console.log('\n💡 Project ID Issues:');
            console.log('1. Verify FIREBASE_PROJECT_ID matches your Firebase project');
            console.log('2. Check project exists and is active');
        }
        
        return false;
    }
}

// Run the test
testFirebaseNotifications().then(success => {
    if (success) {
        console.log('\n🎉 Firebase Notifications are working correctly!');
        console.log('\n📝 Next steps:');
        console.log('1. Test with real FCM tokens from your app');
        console.log('2. Set up notification topics for user segments');
        console.log('3. Implement notification templates');
        console.log('4. Monitor notification delivery in Firebase Console');
    } else {
        console.log('\n❌ Firebase Notifications need configuration.');
        console.log('\n🔧 Setup steps:');
        console.log('1. Go to Firebase Console');
        console.log('2. Create/select your project');
        console.log('3. Go to Project Settings > Service Accounts');
        console.log('4. Generate new private key');
        console.log('5. Add credentials to .env file');
    }
    
    process.exit(success ? 0 : 1);
});
