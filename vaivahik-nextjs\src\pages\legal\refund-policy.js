/**
 * Refund Policy Page
 * Dynamically loads and displays the current refund policy
 */

import { useState, useEffect } from 'react';
import Head from 'next/head';
import {
  Container,
  Typography,
  Paper,
  Box,
  Skeleton,
  Alert,
  Chip,
  Grid,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Button
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
  Schedule as TimeIcon,
  ContactSupport as SupportIcon
} from '@mui/icons-material';
import axios from 'axios';

export default function RefundPolicy() {
  const [policy, setPolicy] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchPolicy();
  }, []);

  const fetchPolicy = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/policies/refund-policy');
      
      if (response.data.success) {
        setPolicy(response.data.policy);
      } else {
        throw new Error(response.data.message || 'Failed to load refund policy');
      }
    } catch (error) {
      console.error('Error fetching refund policy:', error);
      setError('Failed to load refund policy. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const renderMarkdown = (content) => {
    // Simple markdown-to-HTML conversion
    return content
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^\*\*(.*)\*\*$/gim, '<strong>$1</strong>')
      .replace(/^\* (.*$)/gim, '<li>$1</li>')
      .replace(/^- (.*$)/gim, '<li>$1</li>')
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>');
  };

  // Refund eligibility criteria
  const eligibleCriteria = [
    'Refund requested within 7 days of purchase',
    'Account not used extensively',
    'No successful matches or meaningful interactions',
    'Technical issues not resolved within reasonable time'
  ];

  const nonEligibleCriteria = [
    'Subscriptions used for more than 7 days',
    'Accounts with successful matches or extensive usage',
    'Violation of terms of service',
    'Change of mind after extensive platform use'
  ];

  const refundProcess = [
    { step: 1, title: 'Contact Support', description: 'Email <NAME_EMAIL> with your request' },
    { step: 2, title: 'Provide Details', description: 'Share your account details and reason for refund' },
    { step: 3, title: 'Review Process', description: 'Allow 5-7 business days for our team to review' },
    { step: 4, title: 'Refund Processing', description: 'Approved refunds processed to original payment method' }
  ];

  if (loading) {
    return (
      <>
        <Head>
          <title>Refund Policy - Vaivahik Matrimony</title>
          <meta name="description" content="Refund Policy for Vaivahik Matrimony Platform" />
        </Head>
        
        <Container maxWidth="md" sx={{ py: 4 }}>
          <Skeleton variant="text" height={60} sx={{ mb: 2 }} />
          <Skeleton variant="text" height={30} sx={{ mb: 4 }} />
          <Paper elevation={2} sx={{ p: 4 }}>
            {[...Array(10)].map((_, index) => (
              <Skeleton key={index} variant="text" height={20} sx={{ mb: 1 }} />
            ))}
          </Paper>
        </Container>
      </>
    );
  }

  if (error) {
    return (
      <>
        <Head>
          <title>Refund Policy - Vaivahik Matrimony</title>
        </Head>
        
        <Container maxWidth="md" sx={{ py: 4 }}>
          <Alert severity="error" sx={{ mb: 4 }}>
            {error}
          </Alert>
        </Container>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Refund Policy - Vaivahik Matrimony</title>
        <meta name="description" content="Refund Policy for Vaivahik Matrimony Platform" />
        <meta name="robots" content="index, follow" />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4, textAlign: 'center' }}>
          <Typography variant="h3" component="h1" gutterBottom>
            {policy?.title || 'Refund Policy'}
          </Typography>
          
          {policy && (
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, flexWrap: 'wrap' }}>
              <Chip
                label={`Version ${policy.version}`}
                variant="outlined"
                color="primary"
              />
              <Chip
                label={`Last Updated: ${new Date(policy.lastUpdated).toLocaleDateString()}`}
                variant="outlined"
                color="info"
              />
            </Box>
          )}
        </Box>

        {/* Quick Overview */}
        <Paper elevation={1} sx={{ p: 3, mb: 4, bgcolor: 'success.light', color: 'success.contrastText' }}>
          <Typography variant="h6" gutterBottom>
            💰 Quick Overview
          </Typography>
          <Typography variant="body2">
            We offer refunds within 7 days of purchase for eligible accounts. Our refund process is transparent and customer-friendly.
          </Typography>
        </Paper>

        <Grid container spacing={4}>
          {/* Eligible Criteria */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom color="success.main">
                  <CheckIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Eligible for Refund
                </Typography>
                <List dense>
                  {eligibleCriteria.map((criteria, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <CheckIcon color="success" />
                      </ListItemIcon>
                      <ListItemText primary={criteria} />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Non-Eligible Criteria */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom color="error.main">
                  <CancelIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Not Eligible for Refund
                </Typography>
                <List dense>
                  {nonEligibleCriteria.map((criteria, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <CancelIcon color="error" />
                      </ListItemIcon>
                      <ListItemText primary={criteria} />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Refund Process */}
        <Box sx={{ mt: 4, mb: 4 }}>
          <Typography variant="h5" gutterBottom color="primary">
            Refund Process
          </Typography>
          <Grid container spacing={3}>
            {refundProcess.map((process) => (
              <Grid item xs={12} sm={6} md={3} key={process.step}>
                <Card sx={{ textAlign: 'center', height: '100%' }}>
                  <CardContent>
                    <Box
                      sx={{
                        width: 50,
                        height: 50,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        color: 'white',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mx: 'auto',
                        mb: 2,
                        fontSize: '1.5rem',
                        fontWeight: 'bold'
                      }}
                    >
                      {process.step}
                    </Box>
                    <Typography variant="h6" gutterBottom>
                      {process.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {process.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Processing Times */}
        <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            <TimeIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Processing Times
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={4}>
              <Typography variant="subtitle2" color="primary">Review Time</Typography>
              <Typography variant="body2">5-7 business days</Typography>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography variant="subtitle2" color="primary">Processing Time</Typography>
              <Typography variant="body2">7-14 business days</Typography>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography variant="subtitle2" color="primary">Bank Processing</Typography>
              <Typography variant="body2">Additional 3-5 days</Typography>
            </Grid>
          </Grid>
        </Paper>

        {/* Full Policy Content */}
        <Paper elevation={2} sx={{ p: 4, mb: 4 }}>
          <Typography variant="h5" gutterBottom color="primary">
            Complete Refund Policy
          </Typography>
          
          {policy && (
            <Box
              sx={{
                '& h1': {
                  fontSize: '2rem',
                  fontWeight: 600,
                  mb: 3,
                  color: 'primary.main'
                },
                '& h2': {
                  fontSize: '1.5rem',
                  fontWeight: 600,
                  mb: 2,
                  mt: 4,
                  color: 'text.primary'
                },
                '& h3': {
                  fontSize: '1.25rem',
                  fontWeight: 600,
                  mb: 2,
                  mt: 3,
                  color: 'text.primary'
                },
                '& p': {
                  mb: 2,
                  lineHeight: 1.7,
                  color: 'text.secondary'
                },
                '& li': {
                  mb: 1,
                  ml: 2,
                  color: 'text.secondary'
                },
                '& strong': {
                  fontWeight: 600,
                  color: 'text.primary'
                }
              }}
              dangerouslySetInnerHTML={{
                __html: renderMarkdown(policy.content)
              }}
            />
          )}
        </Paper>

        {/* Contact Support */}
        <Paper elevation={1} sx={{ p: 3, textAlign: 'center', bgcolor: 'primary.light' }}>
          <Typography variant="h6" gutterBottom>
            <SupportIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Need Help with Refunds?
          </Typography>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Our support team is here to help you with any refund-related questions.
          </Typography>
          <Button
            variant="contained"
            color="primary"
            href="mailto:<EMAIL>"
            sx={{ mr: 2 }}
          >
            Email Support
          </Button>
          <Button
            variant="outlined"
            color="primary"
            href="tel:+91XXXXXXXXXX"
          >
            Call Support
          </Button>
        </Paper>
      </Container>
    </>
  );
}
