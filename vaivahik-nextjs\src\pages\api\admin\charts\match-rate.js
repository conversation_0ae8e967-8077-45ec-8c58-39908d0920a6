import axios from 'axios';
import { handleApiError } from '@/utils/errorHandler';
import { withAuth } from '@/utils/authHandler';
import { getCache, setCache, CACHE_TTL } from '@/utils/redisClient';

// Backend API URL - adjust this to your actual backend URL
const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3001/api';

// Cache keys
const CACHE_KEYS = {
  MATCH_RATE_DATA: (period) => `dashboard:charts:match-rate:${period}`
};

async function handler(req, res) {
  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getMatchRateData(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    return handleApiError(error, res, 'Match Rate Chart API');
  }
}

// GET /api/admin/charts/match-rate
async function getMatchRateData(req, res) {
  try {
    const { period = 'month', refresh = 'false' } = req.query;
    const forceRefresh = refresh === 'true';

    // Get the auth token from the request cookies or headers
    const token = req.cookies?.adminToken || req.headers.authorization?.split(' ')[1];

    // Generate cache key for this specific period
    const cacheKey = CACHE_KEYS.MATCH_RATE_DATA(period);

    // If not forcing refresh, try to get data from cache
    if (!forceRefresh) {
      const cachedData = await getCache(cacheKey);

      if (cachedData) {
        console.log(`Returning match rate data for period ${period} from Redis cache`);

        // Add cache metadata to response
        return res.status(200).json({
          ...cachedData,
          _cache: {
            hit: true,
            period
          }
        });
      }
    } else {
      console.log(`Force refreshing match rate data for period ${period}`);
    }

    // In production, try to fetch from the backend API
    if (process.env.NODE_ENV === 'production') {
      try {
        // Fetch match rate data from the backend API
        const response = await axios({
          method: 'GET',
          url: `${BACKEND_API_URL}/admin/charts/match-rate`,
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          params: { period },
          timeout: 10000 // 10 second timeout
        });

        // Cache the response data
        const responseData = response.data;
        if (responseData.success) {
          await setCache(
            cacheKey,
            responseData,
            CACHE_TTL.DASHBOARD_CHARTS
          );

          // Add cache metadata to response
          responseData._cache = {
            hit: false,
            period
          };
        }

        // Return the response from the backend
        return res.status(200).json(responseData);
      } catch (apiError) {
        console.error('Error fetching match rate data from backend API:', apiError.message);

        // Try to get stale data from cache if available
        const cachedData = await getCache(cacheKey);
        if (cachedData) {
          console.log(`Returning stale match rate data for period ${period} from cache due to API error`);

          return res.status(200).json({
            ...cachedData,
            _cache: {
              hit: true,
              stale: true,
              period
            }
          });
        }

        // In production, return the error if no cache is available
        return res.status(500).json({
          success: false,
          message: 'Failed to fetch match rate data from backend API.',
          error: apiError.message
        });
      }
    }

    // Generate mock data based on the period
    let data = {};

    // Add slight variations to make the data more realistic
    const getVariation = (base, range = 0.05) => {
      return +(base * (1 + (Math.random() * 2 - 1) * range)).toFixed(1);
    };

    switch (period) {
      case 'week':
        data = {
          total: 120,
          accepted: 99,
          successRate: getVariation(82.5)
        };
        break;
      case 'month':
        data = {
          total: 850,
          accepted: 665,
          successRate: getVariation(78.3)
        };
        break;
      case 'year':
        data = {
          total: 9500,
          accepted: 7200,
          successRate: getVariation(75.8)
        };
        break;
      default:
        data = {
          total: 850,
          accepted: 665,
          successRate: getVariation(78.3)
        };
    }

    // Create response data
    const mockData = {
      success: true,
      data,
      period
    };

    // Cache the mock data
    await setCache(
      cacheKey,
      mockData,
      CACHE_TTL.DASHBOARD_CHARTS
    );

    // Add cache metadata to response
    mockData._cache = {
      hit: false,
      environment: 'development',
      period
    };

    // In development or if backend call fails in production, return mock data
    return res.status(200).json(mockData);
  } catch (error) {
    return handleApiError(error, res, 'Get match rate data');
  }
}

// Export the handler with authentication middleware
export default withAuth(handler, 'ADMIN');
