// Spotlight Feature Model
model SpotlightFeature {
  id                String    @id @default(cuid())
  name              String    // Feature name (e.g., "24-Hour Spotlight")
  description       String    // Feature description
  price             Float     // Regular price
  discountedPrice   Float?    // Discounted price (if applicable)
  discountPercent   Int?      // Discount percentage
  isActive          Boolean   @default(true)
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @default(now()) @updatedAt @map("updated_at")
  
  // Relationships
  purchases         UserSpotlight[]
  
  @@map("spotlight_features")
}

// User Spotlight Purchase Model
model UserSpotlight {
  id                String    @id @default(cuid())
  userId            String    @map("user_id")
  user              User      @relation(fields: [userId], references: [id])
  spotlightId       String    @map("spotlight_id")
  spotlight         SpotlightFeature @relation(fields: [spotlightId], references: [id])
  purchaseDate      DateTime  @default(now()) @map("purchase_date")
  pricePaid         Float     @map("price_paid")
  transactionId     String?   @map("transaction_id")
  availableCount    Int       @default(1) @map("available_count")
  usedCount         Int       @default(0) @map("used_count")
  createdAt         DateTime  @default(now()) @map("created_at")
  
  // Relationships
  activations       SpotlightActivation[]
  
  @@index([userId])
  @@index([spotlightId])
  @@map("user_spotlights")
}

// Spotlight Activation Model
model SpotlightActivation {
  id                String    @id @default(cuid())
  userSpotlightId   String    @map("user_spotlight_id")
  userSpotlight     UserSpotlight @relation(fields: [userSpotlightId], references: [id])
  userId            String    @map("user_id")
  startTime         DateTime  @map("start_time")
  endTime           DateTime  @map("end_time")
  isActive          Boolean   @default(true)
  createdAt         DateTime  @default(now()) @map("created_at")
  
  @@index([userId])
  @@index([userSpotlightId])
  @@index([isActive])
  @@index([endTime])
  @@map("spotlight_activations")
}
