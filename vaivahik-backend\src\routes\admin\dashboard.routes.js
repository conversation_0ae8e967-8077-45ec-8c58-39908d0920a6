/**
 * Admin Dashboard Routes
 *
 * Routes for the admin dashboard data, recent activity, and recent users.
 */
const express = require('express');
const router = express.Router();
const dashboardController = require('../../controllers/admin/dashboard.controller');
const authenticateAdmin = require('../../middleware/adminAuth.middleware');
const { cacheDashboardStats } = require('../../middleware/cache.middleware');

// Get dashboard data
router.get('/', authenticateAdmin, cacheDashboardStats(1800), dashboardController.getDashboardData);

// Get recent activity
router.get('/recent-activity', authenticateAdmin, cacheDashboardStats(300), dashboardController.getRecentActivity);

// Get recent users
router.get('/recent-users', authenticateAdmin, cacheDashboardStats(300), dashboardController.getRecentUsers);

module.exports = router;
