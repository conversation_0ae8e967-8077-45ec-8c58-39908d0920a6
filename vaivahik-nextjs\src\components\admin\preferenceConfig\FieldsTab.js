import { useState } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Switch,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControlLabel,
  Tooltip,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import InfoIcon from '@mui/icons-material/Info';
import ListIcon from '@mui/icons-material/List';

export default function FieldsTab({ fields, categories, options, onUpdate, onUpdateOptions, onDeleteField, onDeleteOption }) {
  const [editingField, setEditingField] = useState(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [fieldToDelete, setFieldToDelete] = useState(null);
  const [optionsDialogOpen, setOptionsDialogOpen] = useState(false);
  const [currentFieldOptions, setCurrentFieldOptions] = useState([]);
  const [currentFieldId, setCurrentFieldId] = useState(null);
  const [editingOption, setEditingOption] = useState(null);
  const [optionDialogOpen, setOptionDialogOpen] = useState(false);
  const [deleteOptionConfirmOpen, setDeleteOptionConfirmOpen] = useState(false);
  const [optionToDelete, setOptionToDelete] = useState(null);
  const [expandedCategory, setExpandedCategory] = useState(null);

  const fieldTypes = [
    { value: 'TEXT', label: 'Text' },
    { value: 'NUMBER', label: 'Number' },
    { value: 'RANGE', label: 'Range' },
    { value: 'SELECT', label: 'Select (Single)' },
    { value: 'MULTI_SELECT', label: 'Select (Multiple)' },
    { value: 'BOOLEAN', label: 'Boolean (Yes/No)' }
  ];

  const handleAddField = (categoryId) => {
    setEditingField({
      id: '',
      name: '',
      displayName: '',
      description: '',
      fieldType: 'TEXT',
      displayOrder: fields.filter(f => f.categoryId === categoryId).length + 1,
      isActive: true,
      isRequired: false,
      isSearchable: true,
      isMatchCriteria: true,
      defaultValue: '',
      validationRules: '',
      minValue: null,
      maxValue: null,
      stepValue: null,
      categoryId: categoryId
    });
    setIsDialogOpen(true);
  };

  const handleEditField = (field) => {
    setEditingField({ ...field });
    setIsDialogOpen(true);
  };

  const handleDeleteField = (field) => {
    setFieldToDelete(field);
    setDeleteConfirmOpen(true);
  };

  const confirmDeleteField = () => {
    if (onDeleteField) {
      // Use the API delete function
      onDeleteField(fieldToDelete.id);
    } else {
      // Fallback to client-side filtering
      // Filter out the field to delete
      const updatedFields = fields.filter(f => f.id !== fieldToDelete.id);

      // Also remove any options associated with this field
      const updatedOptions = options.filter(o => o.fieldId !== fieldToDelete.id);

      onUpdate(updatedFields);
      onUpdateOptions(updatedOptions);
    }

    setDeleteConfirmOpen(false);
    setFieldToDelete(null);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditingField(null);
  };

  const handleSaveField = () => {
    if (!editingField.name || !editingField.displayName || !editingField.fieldType) {
      // Show validation error
      return;
    }

    let updatedFields;

    if (editingField.id) {
      // Update existing field
      updatedFields = fields.map(field =>
        field.id === editingField.id ? editingField : field
      );
    } else {
      // Add new field with a generated ID
      const newField = {
        ...editingField,
        id: `field${Date.now()}` // Simple ID generation
      };
      updatedFields = [...fields, newField];
    }

    onUpdate(updatedFields);
    setIsDialogOpen(false);
    setEditingField(null);
  };

  const handleInputChange = (e) => {
    const { name, value, checked, type } = e.target;

    setEditingField(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleCategoryChange = (e) => {
    const categoryId = e.target.value;
    const categoryFields = fields.filter(f => f.categoryId === categoryId);

    setEditingField(prev => ({
      ...prev,
      categoryId,
      displayOrder: categoryFields.length + 1
    }));
  };

  const handleManageOptions = (fieldId) => {
    const fieldOptions = options.filter(o => o.fieldId === fieldId);
    setCurrentFieldOptions(fieldOptions);
    setCurrentFieldId(fieldId);
    setOptionsDialogOpen(true);
  };

  const handleAddOption = () => {
    setEditingOption({
      id: '',
      value: '',
      displayText: '',
      description: '',
      displayOrder: currentFieldOptions.length + 1,
      isActive: true,
      fieldId: currentFieldId
    });
    setOptionDialogOpen(true);
  };

  const handleEditOption = (option) => {
    setEditingOption({ ...option });
    setOptionDialogOpen(true);
  };

  const handleDeleteOption = (option) => {
    setOptionToDelete(option);
    setDeleteOptionConfirmOpen(true);
  };

  const confirmDeleteOption = () => {
    if (onDeleteOption) {
      // Use the API delete function
      onDeleteOption(optionToDelete.id);

      // Update the current field options
      const updatedFieldOptions = currentFieldOptions.filter(o => o.id !== optionToDelete.id);
      setCurrentFieldOptions(updatedFieldOptions);
    } else {
      // Fallback to client-side filtering
      // Filter out the option to delete
      const updatedOptions = options.filter(o => o.id !== optionToDelete.id);

      // Update the display order of remaining options for this field
      const fieldOptions = updatedOptions.filter(o => o.fieldId === currentFieldId);
      const reorderedOptions = fieldOptions.map((o, index) => ({
        ...o,
        displayOrder: index + 1
      }));

      // Combine with options from other fields
      const otherOptions = updatedOptions.filter(o => o.fieldId !== currentFieldId);
      const finalOptions = [...otherOptions, ...reorderedOptions];

      onUpdateOptions(finalOptions);
      setCurrentFieldOptions(reorderedOptions);
    }

    setDeleteOptionConfirmOpen(false);
    setOptionToDelete(null);
  };

  const handleCloseOptionDialog = () => {
    setOptionDialogOpen(false);
    setEditingOption(null);
  };

  const handleSaveOption = () => {
    if (!editingOption.value || !editingOption.displayText) {
      // Show validation error
      return;
    }

    let updatedOptions;

    if (editingOption.id) {
      // Update existing option
      updatedOptions = options.map(opt =>
        opt.id === editingOption.id ? editingOption : opt
      );
    } else {
      // Add new option with a generated ID
      const newOption = {
        ...editingOption,
        id: `opt${Date.now()}` // Simple ID generation
      };
      updatedOptions = [...options, newOption];
    }

    // Update the current field options
    const fieldOptions = updatedOptions.filter(o => o.fieldId === currentFieldId);
    setCurrentFieldOptions(fieldOptions);

    onUpdateOptions(updatedOptions);
    setOptionDialogOpen(false);
    setEditingOption(null);
  };

  const handleOptionInputChange = (e) => {
    const { name, value, checked, type } = e.target;

    setEditingOption(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleAccordionChange = (categoryId) => (event, isExpanded) => {
    setExpandedCategory(isExpanded ? categoryId : null);
  };

  return (
    <Box>
      {categories.map((category) => (
        <Accordion
          key={category.id}
          expanded={expandedCategory === category.id}
          onChange={handleAccordionChange(category.id)}
          sx={{ mb: 2 }}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">
              {category.displayName}
              {!category.isActive && (
                <Chip
                  label="Inactive"
                  size="small"
                  color="default"
                  sx={{ ml: 1 }}
                />
              )}
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={() => handleAddField(category.id)}
              >
                Add Field
              </Button>
            </Box>

            <TableContainer component={Paper}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Display Name</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Required</TableCell>
                    <TableCell>Searchable</TableCell>
                    <TableCell>Match Criteria</TableCell>
                    <TableCell>Active</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {fields
                    .filter(field => field.categoryId === category.id)
                    .sort((a, b) => a.displayOrder - b.displayOrder)
                    .map((field) => (
                      <TableRow key={field.id}>
                        <TableCell>{field.name}</TableCell>
                        <TableCell>{field.displayName}</TableCell>
                        <TableCell>
                          {fieldTypes.find(t => t.value === field.fieldType)?.label || field.fieldType}
                          {(field.fieldType === 'SELECT' || field.fieldType === 'MULTI_SELECT') && (
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => handleManageOptions(field.id)}
                              title="Manage Options"
                            >
                              <ListIcon fontSize="small" />
                            </IconButton>
                          )}
                        </TableCell>
                        <TableCell>
                          <Switch
                            checked={field.isRequired}
                            disabled
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Switch
                            checked={field.isSearchable}
                            disabled
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Switch
                            checked={field.isMatchCriteria}
                            disabled
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Switch
                            checked={field.isActive}
                            disabled
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <IconButton
                            color="primary"
                            size="small"
                            onClick={() => handleEditField(field)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                          <IconButton
                            color="error"
                            size="small"
                            onClick={() => handleDeleteField(field)}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
          </AccordionDetails>
        </Accordion>
      ))}

      {/* Edit/Add Field Dialog */}
      <Dialog open={isDialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingField?.id ? 'Edit Field' : 'Add Field'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                name="name"
                label="Internal Name"
                value={editingField?.name || ''}
                onChange={handleInputChange}
                fullWidth
                required
                helperText="Used in code (e.g., age_range)"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="displayName"
                label="Display Name"
                value={editingField?.displayName || ''}
                onChange={handleInputChange}
                fullWidth
                required
                helperText="Shown to users (e.g., Age Range)"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="description"
                label="Description"
                value={editingField?.description || ''}
                onChange={handleInputChange}
                fullWidth
                multiline
                rows={2}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Category</InputLabel>
                <Select
                  value={editingField?.categoryId || ''}
                  onChange={handleCategoryChange}
                  label="Category"
                >
                  {categories.map((category) => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.displayName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Field Type</InputLabel>
                <Select
                  name="fieldType"
                  value={editingField?.fieldType || ''}
                  onChange={handleInputChange}
                  label="Field Type"
                >
                  {fieldTypes.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="displayOrder"
                label="Display Order"
                type="number"
                value={editingField?.displayOrder || 1}
                onChange={handleInputChange}
                fullWidth
                InputProps={{ inputProps: { min: 1 } }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="defaultValue"
                label="Default Value"
                value={editingField?.defaultValue || ''}
                onChange={handleInputChange}
                fullWidth
                helperText="JSON format (e.g., {'min': 21, 'max': 35} for range)"
              />
            </Grid>

            {/* Range-specific fields */}
            {editingField?.fieldType === 'RANGE' && (
              <>
                <Grid item xs={12} sm={4}>
                  <TextField
                    name="minValue"
                    label="Minimum Value"
                    type="number"
                    value={editingField?.minValue || ''}
                    onChange={handleInputChange}
                    fullWidth
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    name="maxValue"
                    label="Maximum Value"
                    type="number"
                    value={editingField?.maxValue || ''}
                    onChange={handleInputChange}
                    fullWidth
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    name="stepValue"
                    label="Step Value"
                    type="number"
                    value={editingField?.stepValue || ''}
                    onChange={handleInputChange}
                    fullWidth
                  />
                </Grid>
              </>
            )}

            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    name="isRequired"
                    checked={editingField?.isRequired || false}
                    onChange={handleInputChange}
                  />
                }
                label="Required"
              />
              <Tooltip title="If required, users must fill in this preference">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    name="isActive"
                    checked={editingField?.isActive || false}
                    onChange={handleInputChange}
                  />
                }
                label="Active"
              />
              <Tooltip title="If inactive, this field will not be shown to users">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    name="isSearchable"
                    checked={editingField?.isSearchable || false}
                    onChange={handleInputChange}
                  />
                }
                label="Searchable"
              />
              <Tooltip title="If searchable, users can search by this preference">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    name="isMatchCriteria"
                    checked={editingField?.isMatchCriteria || false}
                    onChange={handleInputChange}
                  />
                }
                label="Match Criteria"
              />
              <Tooltip title="If match criteria, this preference is used in the matching algorithm">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSaveField} variant="contained" color="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Field Confirmation Dialog */}
      <Dialog open={deleteConfirmOpen} onClose={() => setDeleteConfirmOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the field "{fieldToDelete?.displayName}"?
            This will also delete all options associated with this field.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button onClick={confirmDeleteField} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Manage Options Dialog */}
      <Dialog open={optionsDialogOpen} onClose={() => setOptionsDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Manage Options for {fields.find(f => f.id === currentFieldId)?.displayName}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddOption}
            >
              Add Option
            </Button>
          </Box>

          <TableContainer component={Paper}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Order</TableCell>
                  <TableCell>Value</TableCell>
                  <TableCell>Display Text</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>Active</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {currentFieldOptions
                  .sort((a, b) => a.displayOrder - b.displayOrder)
                  .map((option) => (
                    <TableRow key={option.id}>
                      <TableCell>{option.displayOrder}</TableCell>
                      <TableCell>{option.value}</TableCell>
                      <TableCell>{option.displayText}</TableCell>
                      <TableCell>{option.description}</TableCell>
                      <TableCell>
                        <Switch
                          checked={option.isActive}
                          disabled
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <IconButton
                          color="primary"
                          size="small"
                          onClick={() => handleEditOption(option)}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                        <IconButton
                          color="error"
                          size="small"
                          onClick={() => handleDeleteOption(option)}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOptionsDialogOpen(false)} variant="contained">
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit/Add Option Dialog */}
      <Dialog open={optionDialogOpen} onClose={handleCloseOptionDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingOption?.id ? 'Edit Option' : 'Add Option'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                name="value"
                label="Internal Value"
                value={editingOption?.value || ''}
                onChange={handleOptionInputChange}
                fullWidth
                required
                helperText="Used in code (e.g., VEG)"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="displayText"
                label="Display Text"
                value={editingOption?.displayText || ''}
                onChange={handleOptionInputChange}
                fullWidth
                required
                helperText="Shown to users (e.g., Vegetarian)"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="description"
                label="Description"
                value={editingOption?.description || ''}
                onChange={handleOptionInputChange}
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="displayOrder"
                label="Display Order"
                type="number"
                value={editingOption?.displayOrder || 1}
                onChange={handleOptionInputChange}
                fullWidth
                InputProps={{ inputProps: { min: 1 } }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    name="isActive"
                    checked={editingOption?.isActive || false}
                    onChange={handleOptionInputChange}
                  />
                }
                label="Active"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseOptionDialog}>Cancel</Button>
          <Button onClick={handleSaveOption} variant="contained" color="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Option Confirmation Dialog */}
      <Dialog open={deleteOptionConfirmOpen} onClose={() => setDeleteOptionConfirmOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the option "{optionToDelete?.displayText}"?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteOptionConfirmOpen(false)}>Cancel</Button>
          <Button onClick={confirmDeleteOption} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
