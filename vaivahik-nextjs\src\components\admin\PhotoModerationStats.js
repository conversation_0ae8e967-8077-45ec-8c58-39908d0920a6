import { useState, useEffect } from 'react';
import { adminGet } from '@/services/apiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';

export default function PhotoModerationStats() {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    setLoading(true);
    try {
      const data = await adminGet(`${ADMIN_ENDPOINTS.PHOTO_MODERATION}/stats`);

      if (data.success) {
        setStats(data.stats);
        setError(null);
      } else {
        console.error('API returned error:', data);
        throw new Error(data.message || 'Failed to load statistics');
      }
    } catch (err) {
      console.error('Error fetching moderation stats:', err);
      setError('Failed to load moderation statistics. Please check if the backend server is running.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="stats-loading">
        <div className="loading-spinner"></div>
        <p>Loading statistics...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="stats-error">
        <p>{error}</p>
        <button className="btn btn-outline-primary" onClick={fetchStats}>
          Try Again
        </button>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="stats-empty">
        <p>No statistics available</p>
        <button className="btn btn-outline-primary" onClick={fetchStats}>
          Refresh
        </button>
      </div>
    );
  }

  return (
    <div className="stats-container">
      <div className="stats-header">
        <h3>Photo Moderation Statistics</h3>
        <button
          className="btn btn-sm btn-outline-primary refresh-btn"
          onClick={fetchStats}
        >
          Refresh
        </button>
      </div>

      <div className="stats-summary">
        <div className="stat-card total">
          <div className="stat-value">{stats.total}</div>
          <div className="stat-label">Total Photos</div>
        </div>

        <div className="stat-card pending">
          <div className="stat-value">{stats.pending.count}</div>
          <div className="stat-label">Pending</div>
          <div className="stat-percentage">{Math.round(stats.pending.percentage)}%</div>
        </div>

        <div className="stat-card approved">
          <div className="stat-value">{stats.approved.count}</div>
          <div className="stat-label">Approved</div>
          <div className="stat-percentage">{Math.round(stats.approved.percentage)}%</div>
        </div>

        <div className="stat-card rejected">
          <div className="stat-value">{stats.rejected.count}</div>
          <div className="stat-label">Rejected</div>
          <div className="stat-percentage">{Math.round(stats.rejected.percentage)}%</div>
        </div>
      </div>

      <div className="ai-stats">
        <h4>AI Processing</h4>
        <div className="ai-stats-content">
          <div className="ai-stat">
            <div className="ai-stat-label">AI Processed Photos</div>
            <div className="ai-stat-value">{stats.aiModerated.count}</div>
            <div className="ai-stat-percentage">{Math.round(stats.aiModerated.percentage)}% of total</div>
          </div>

          {stats.aiDecisions && (
            <>
              <div className="ai-accuracy">
                <div className="ai-stat-label">AI Accuracy</div>
                <div className="ai-stat-value">{Math.round(stats.aiDecisions.accuracy || 0)}%</div>
                <div className="ai-stat-detail">
                  Based on admin agreement with AI decisions
                </div>
              </div>

              <div className="ai-performance">
                <div className="performance-item">
                  <div className="performance-label">Auto-Approved</div>
                  <div className="performance-value">{stats.aiDecisions.autoApproved || 0}</div>
                </div>
                <div className="performance-item">
                  <div className="performance-label">Auto-Rejected</div>
                  <div className="performance-value">{stats.aiDecisions.autoRejected || 0}</div>
                </div>
                <div className="performance-item">
                  <div className="performance-label">Manual Review</div>
                  <div className="performance-value">{stats.aiDecisions.manualReview || 0}</div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {stats.recentActivity && stats.recentActivity.length > 0 && (
        <div className="recent-activity">
          <h4>Recent Activity</h4>
          <div className="activity-list">
            {stats.recentActivity.slice(0, 5).map((activity, index) => (
              <div key={index} className="activity-item">
                <div className="activity-photo">
                  <img src={activity.photoUrl} alt="Photo" />
                </div>
                <div className="activity-details">
                  <div className="activity-user">{activity.userName}</div>
                  <div className="activity-decision">
                    <span className={`decision-badge ${activity.decision.toLowerCase()}`}>
                      {activity.decision}
                    </span>
                    {activity.confidence && (
                      <span className="activity-confidence">
                        {Math.round(activity.confidence)}% confidence
                      </span>
                    )}
                  </div>
                  {activity.flags && activity.flags.length > 0 && (
                    <div className="activity-flags">
                      {activity.flags.map((flag, i) => (
                        <span key={i} className="activity-flag">
                          {flag.replace(/_/g, ' ')}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
                <div className="activity-time">
                  {new Date(activity.timestamp).toLocaleString()}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <style jsx>{`
        .stats-container {
          background-color: white;
          border-radius: 12px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          padding: 20px;
          margin-bottom: 30px;
        }

        .stats-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
        }

        .stats-header h3 {
          margin: 0;
          font-size: 1.3rem;
          color: var(--text-dark);
        }

        .refresh-btn {
          padding: 5px 10px;
          font-size: 0.85rem;
        }

        .stats-summary {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 15px;
          margin-bottom: 30px;
        }

        .stat-card {
          padding: 15px;
          border-radius: 8px;
          text-align: center;
          color: white;
          position: relative;
        }

        .stat-card.total {
          background-color: #5e35b1;
        }

        .stat-card.pending {
          background-color: #fb8c00;
        }

        .stat-card.approved {
          background-color: #43a047;
        }

        .stat-card.rejected {
          background-color: #e53935;
        }

        .stat-value {
          font-size: 2rem;
          font-weight: 700;
          margin-bottom: 5px;
        }

        .stat-label {
          font-size: 0.9rem;
          opacity: 0.9;
        }

        .stat-percentage {
          position: absolute;
          top: 10px;
          right: 10px;
          font-size: 0.8rem;
          opacity: 0.8;
        }

        .ai-stats {
          background-color: #f5f5f5;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 30px;
        }

        .ai-stats h4 {
          margin-top: 0;
          margin-bottom: 15px;
          font-size: 1.1rem;
          color: var(--text-dark);
        }

        .ai-stats-content {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
        }

        .ai-stat {
          display: flex;
          flex-direction: column;
        }

        .ai-stat-label {
          font-size: 0.9rem;
          color: #666;
          margin-bottom: 5px;
        }

        .ai-stat-value {
          font-size: 1.5rem;
          font-weight: 600;
          color: var(--text-dark);
        }

        .ai-stat-percentage, .ai-stat-detail {
          font-size: 0.8rem;
          color: #666;
          margin-top: 3px;
        }

        .ai-performance {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
        }

        .performance-item {
          flex: 1;
          min-width: 100px;
        }

        .performance-label {
          font-size: 0.8rem;
          color: #666;
          margin-bottom: 3px;
        }

        .performance-value {
          font-size: 1.2rem;
          font-weight: 600;
          color: var(--text-dark);
        }

        .recent-activity h4 {
          margin-top: 0;
          margin-bottom: 15px;
          font-size: 1.1rem;
          color: var(--text-dark);
        }

        .activity-list {
          display: flex;
          flex-direction: column;
          gap: 10px;
        }

        .activity-item {
          display: flex;
          align-items: center;
          padding: 10px;
          border-radius: 8px;
          background-color: #f9f9f9;
          gap: 15px;
        }

        .activity-photo {
          width: 50px;
          height: 50px;
          border-radius: 4px;
          overflow: hidden;
          flex-shrink: 0;
        }

        .activity-photo img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .activity-details {
          flex: 1;
        }

        .activity-user {
          font-weight: 500;
          margin-bottom: 3px;
        }

        .activity-decision {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 3px;
        }

        .decision-badge {
          display: inline-block;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 0.75rem;
          font-weight: 500;
        }

        .decision-badge.approved {
          background-color: #e8f5e9;
          color: #2e7d32;
        }

        .decision-badge.rejected {
          background-color: #ffebee;
          color: #c62828;
        }

        .decision-badge.pending {
          background-color: #fff8e1;
          color: #ff8f00;
        }

        .activity-confidence {
          font-size: 0.75rem;
          color: #666;
        }

        .activity-flags {
          display: flex;
          flex-wrap: wrap;
          gap: 5px;
        }

        .activity-flag {
          display: inline-block;
          padding: 2px 6px;
          background-color: #f0f0f0;
          border-radius: 10px;
          font-size: 0.7rem;
          color: #555;
        }

        .activity-time {
          font-size: 0.75rem;
          color: #999;
          white-space: nowrap;
        }

        .stats-loading, .stats-error, .stats-empty {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 40px 20px;
          background-color: white;
          border-radius: 12px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          text-align: center;
        }

        .stats-error {
          color: #e53935;
        }

        .stats-error button, .stats-empty button {
          margin-top: 15px;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
          .stats-summary {
            grid-template-columns: repeat(2, 1fr);
          }

          .ai-stats-content {
            grid-template-columns: 1fr;
          }

          .activity-item {
            flex-direction: column;
            align-items: flex-start;
          }

          .activity-photo {
            width: 100%;
            height: 150px;
          }

          .activity-time {
            align-self: flex-end;
          }
        }
      `}</style>
    </div>
  );
}
