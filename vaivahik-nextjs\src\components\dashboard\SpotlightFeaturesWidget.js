import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  LinearProgress,
  styled,
  CircularProgress
} from '@mui/material';
import {
  FlashOn as SpotlightIcon,
  TrendingUp as BoostIcon,
  Visibility as ViewIcon,
  Star as StarIcon,
  CheckCircle as CheckIcon,
  Schedule as ScheduleIcon,
  LocalFireDepartment as FireIcon,
  EmojiEvents as TrophyIcon
} from '@mui/icons-material';

// Import API services
import { adminGet } from '@/services/apiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';
import { mockDataUtils } from '@/config/apiConfig';

const SpotlightCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: 24,
  boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
    background: 'linear-gradient(90deg, #FF5F6D, #FFC371)',
    borderRadius: '24px 24px 0 0'
  }
}));

const FeatureCard = styled(Card)(({ theme, isActive }) => ({
  border: isActive ? '3px solid #FFD700' : '2px solid rgba(255, 95, 109, 0.2)',
  borderRadius: 16,
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  position: 'relative',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 12px 24px rgba(255, 95, 109, 0.2)',
    borderColor: '#FF5F6D'
  },
  ...(isActive && {
    '&::before': {
      content: '"ACTIVE"',
      position: 'absolute',
      top: -1,
      right: -1,
      background: 'linear-gradient(135deg, #FFD700, #FFA000)',
      color: '#000',
      padding: '4px 12px',
      borderRadius: '0 16px 0 16px',
      fontSize: '0.75rem',
      fontWeight: 700,
      zIndex: 1
    }
  })
}));

const SpotlightFeaturesWidget = ({ userId, currentSpotlight = null }) => {
  const [spotlightFeatures, setSpotlightFeatures] = useState([]);
  const [activeSpotlight, setActiveSpotlight] = useState(currentSpotlight);
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSpotlightFeatures();
    fetchSpotlightStats();
  }, [userId]);

  const fetchSpotlightFeatures = async () => {
    try {
      setLoading(true);

      if (mockDataUtils.isMockDataEnabled()) {
        // Use mock data
        const mockFeatures = [
          {
            id: 1,
            name: 'Profile Boost',
            description: 'Increase your profile visibility by 500% for 24 hours',
            price: 199,
            originalPrice: 299,
            discount: 33,
            duration: '24 hours',
            benefits: [
              '500% more profile views',
              'Top position in search results',
              'Featured in recommendations',
              'Priority in match suggestions'
            ],
            icon: <BoostIcon />,
            color: '#FF5F6D',
            popular: true
          },
          {
            id: 2,
            name: 'Super Spotlight',
            description: 'Premium spotlight with maximum visibility for 7 days',
            price: 999,
            originalPrice: 1499,
            discount: 33,
            duration: '7 days',
            benefits: [
              '1000% more profile views',
              'Featured on homepage',
              'Premium badge display',
              'Priority customer support',
              'Advanced analytics'
            ],
            icon: <StarIcon />,
            color: '#FFD700',
            popular: false
          },
          {
            id: 3,
            name: 'Weekend Special',
            description: 'Special weekend boost when activity is highest',
            price: 399,
            originalPrice: 599,
            discount: 33,
            duration: 'Weekend (48 hours)',
            benefits: [
              '700% more profile views',
              'Weekend featured section',
              'Peak time visibility',
              'Enhanced match rate'
            ],
            icon: <FireIcon />,
            color: '#FF9800',
            popular: false
          }
        ];

        setSpotlightFeatures(mockFeatures);
      } else {
        // Use real API
        const response = await adminGet(ADMIN_ENDPOINTS.SPOTLIGHT_FEATURES);

        if (response.success && response.features) {
          // Transform API data to match component expectations
          const transformedFeatures = response.features.map(feature => ({
            ...feature,
            originalPrice: feature.price + (feature.price * (feature.discountPercent || 0) / 100),
            discount: feature.discountPercent || 0,
            duration: `${feature.durationHours} hours`,
            benefits: [
              `${Math.floor(feature.durationHours * 20)}% more profile views`,
              'Top position in search results',
              'Featured in recommendations',
              'Priority in match suggestions'
            ],
            icon: feature.id === 1 ? <BoostIcon /> : feature.id === 2 ? <StarIcon /> : <FireIcon />,
            color: feature.id === 1 ? '#FF5F6D' : feature.id === 2 ? '#FFD700' : '#FF9800',
            popular: feature.id === 1
          }));

          setSpotlightFeatures(transformedFeatures);
        } else {
          // Fallback to mock data if API fails
          console.warn('Failed to fetch spotlight features, using mock data');
          fetchSpotlightFeatures(); // Recursive call with mock data
        }
      }
    } catch (error) {
      console.error('Error fetching spotlight features:', error);
      // Fallback to mock data on error
      if (!mockDataUtils.isMockDataEnabled()) {
        console.warn('API error, falling back to mock data');
        // Set mock data flag temporarily and retry
        localStorage.setItem('useMockData', 'true');
        fetchSpotlightFeatures();
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchSpotlightStats = async () => {
    try {
      // Mock spotlight stats
      const mockStats = {
        totalViews: 2847,
        viewsIncrease: 450,
        matches: 23,
        interests: 67,
        timeRemaining: '14 hours 32 minutes',
        isActive: !!currentSpotlight
      };
      
      setStats(mockStats);
    } catch (error) {
      console.error('Error fetching spotlight stats:', error);
    }
  };

  const handlePurchaseSpotlight = (feature) => {
    console.log('Purchasing spotlight:', feature);
    // Implement purchase logic with payment gateway
    setActiveSpotlight(feature);
  };

  const formatTimeRemaining = (timeString) => {
    // Parse and format time remaining
    return timeString || 'Not active';
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress size={60} sx={{ color: '#FF5F6D' }} />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        mb: 3,
        p: 3,
        background: 'linear-gradient(135deg, rgba(255, 95, 109, 0.1) 0%, rgba(255, 195, 113, 0.1) 100%)',
        borderRadius: 3
      }}>
        <SpotlightIcon sx={{ fontSize: 32, color: '#FF5F6D', mr: 2 }} />
        <Box>
          <Typography variant="h5" fontWeight="700" color="#FF5F6D">
            ⚡ Spotlight Features
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Boost your profile visibility and get more matches instantly
          </Typography>
        </Box>
      </Box>

      {/* Current Spotlight Status */}
      {stats.isActive && (
        <SpotlightCard sx={{ mb: 4 }}>
          <CardContent sx={{ p: 4 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <TrophyIcon sx={{ fontSize: 32, color: '#FFD700', mr: 2 }} />
              <Box>
                <Typography variant="h6" fontWeight="700" color="#FFD700">
                  🔥 Your Spotlight is Active!
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Time remaining: {formatTimeRemaining(stats.timeRemaining)}
                </Typography>
              </Box>
            </Box>

            <LinearProgress
              variant="determinate"
              value={65}
              sx={{
                height: 12,
                borderRadius: 6,
                backgroundColor: 'rgba(255, 215, 0, 0.1)',
                '& .MuiLinearProgress-bar': {
                  background: 'linear-gradient(90deg, #FFD700, #FFA000)',
                  borderRadius: 6
                }
              }}
            />

            <Grid container spacing={3} sx={{ mt: 2 }}>
              <Grid item xs={6} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" fontWeight="700" color="#FF5F6D">
                    {stats.totalViews.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Views
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" fontWeight="700" color="#4CAF50">
                    +{stats.viewsIncrease}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Views Increase
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" fontWeight="700" color="#FF9800">
                    {stats.matches}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    New Matches
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" fontWeight="700" color="#9C27B0">
                    {stats.interests}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Interests Received
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </SpotlightCard>
      )}

      {/* Spotlight Features */}
      <Grid container spacing={3}>
        {spotlightFeatures.map((feature) => (
          <Grid item xs={12} md={4} key={feature.id}>
            <FeatureCard isActive={activeSpotlight?.id === feature.id}>
              <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
                <Box sx={{ textAlign: 'center', mb: 3 }}>
                  <Box sx={{
                    width: 64,
                    height: 64,
                    borderRadius: '50%',
                    background: `linear-gradient(135deg, ${feature.color}, ${feature.color}80)`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto 16px',
                    boxShadow: `0 8px 24px ${feature.color}40`
                  }}>
                    {React.cloneElement(feature.icon, {
                      sx: { fontSize: 32, color: 'white' }
                    })}
                  </Box>
                  
                  <Typography variant="h6" fontWeight="700" gutterBottom>
                    {feature.name}
                  </Typography>
                  {feature.popular && (
                    <Chip
                      label="Most Popular"
                      size="small"
                      sx={{
                        backgroundColor: '#FF5F6D',
                        color: 'white',
                        fontWeight: 600,
                        mb: 1
                      }}
                    />
                  )}
                  <Typography variant="body2" color="text.secondary">
                    {feature.description}
                  </Typography>
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
                    <Typography variant="h4" fontWeight="700" color={feature.color}>
                      ₹{feature.price}
                    </Typography>
                    {feature.discount && (
                      <Box sx={{ ml: 2 }}>
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            textDecoration: 'line-through',
                            color: 'text.secondary'
                          }}
                        >
                          ₹{feature.originalPrice}
                        </Typography>
                        <Chip
                          label={`${feature.discount}% OFF`}
                          size="small"
                          color="success"
                        />
                      </Box>
                    )}
                  </Box>
                  <Typography variant="body2" color="text.secondary" align="center">
                    Duration: {feature.duration}
                  </Typography>
                </Box>

                <List dense sx={{ flex: 1 }}>
                  {feature.benefits.map((benefit, index) => (
                    <ListItem key={index} sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <CheckIcon sx={{ color: feature.color, fontSize: 20 }} />
                      </ListItemIcon>
                      <ListItemText 
                        primary={benefit}
                        primaryTypographyProps={{ fontSize: '0.875rem' }}
                      />
                    </ListItem>
                  ))}
                </List>

                <Button
                  variant="contained"
                  fullWidth
                  size="large"
                  onClick={() => handlePurchaseSpotlight(feature)}
                  disabled={activeSpotlight?.id === feature.id}
                  sx={{
                    mt: 2,
                    background: activeSpotlight?.id === feature.id 
                      ? 'linear-gradient(135deg, #4CAF50, #8BC34A)'
                      : `linear-gradient(135deg, ${feature.color}, ${feature.color}CC)`,
                    borderRadius: 3,
                    py: 1.5,
                    fontSize: '1rem',
                    fontWeight: 600,
                    '&:hover': {
                      background: activeSpotlight?.id === feature.id 
                        ? 'linear-gradient(135deg, #4CAF50, #8BC34A)'
                        : `linear-gradient(135deg, ${feature.color}DD, ${feature.color}AA)`
                    }
                  }}
                >
                  {activeSpotlight?.id === feature.id ? 'Active' : 'Activate Now'}
                </Button>
              </CardContent>
            </FeatureCard>
          </Grid>
        ))}
      </Grid>

      {/* Success Stories */}
      <Box sx={{ 
        mt: 4, 
        p: 3, 
        background: 'rgba(76, 175, 80, 0.1)', 
        borderRadius: 2,
        border: '1px solid rgba(76, 175, 80, 0.2)'
      }}>
        <Typography variant="h6" fontWeight="600" color="#4CAF50" gutterBottom>
          💫 Success Stories
        </Typography>
        <Typography variant="body2" color="text.secondary">
          "I got 10x more profile views and found my perfect match within 3 days of using Spotlight!" - Priya S.
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          "The Super Spotlight feature helped me connect with verified profiles instantly." - Rahul M.
        </Typography>
      </Box>
    </Box>
  );
};

export default SpotlightFeaturesWidget;
