// Admin Authentication JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    checkAuth();
    
    // Set up logout button
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', logout);
    }
    
    // Set up refresh button
    const refreshBtn = document.getElementById('refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshPage);
    }
});

// Check if user is authenticated
function checkAuth() {
    const token = localStorage.getItem('adminToken');
    
    if (!token) {
        // Redirect to login page if not authenticated
        window.location.href = '/admin/login.html';
    }
}

// Logout function
function logout() {
    // Clear token from local storage
    localStorage.removeItem('adminToken');
    
    // Redirect to login page
    window.location.href = '/admin/login.html';
}

// Refresh page function
function refreshPage() {
    window.location.reload();
}

// Helper function to get the auth token
function getAuthToken() {
    return localStorage.getItem('adminToken') || '';
}
