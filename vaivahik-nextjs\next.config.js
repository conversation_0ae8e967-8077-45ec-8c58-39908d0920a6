/** @type {import('next').NextConfig} */
const path = require('path');

const nextConfig = {
  reactStrictMode: true,
  transpilePackages: ['@mui/x-data-grid'],

  // Enhanced webpack configuration
  webpack: (config, { isServer }) => {
    // This is needed to make @mui/x-data-grid work with Next.js

    // Add support for both page directories
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.resolve(__dirname, 'src'),
      '@website': path.resolve(__dirname, 'src/website'),
      '@admin': path.resolve(__dirname, 'src/pages/admin'),
      '@components': path.resolve(__dirname, 'src/components'),
      '@utils': path.resolve(__dirname, 'src/utils'),
      '@styles': path.resolve(__dirname, 'src/styles'),
      '@services': path.resolve(__dirname, 'src/services'),
      '@contexts': path.resolve(__dirname, 'src/contexts'),
      '@hooks': path.resolve(__dirname, 'src/hooks'),
    };

    return config;
  },

  // Configure page extensions
  pageExtensions: ['js', 'jsx', 'ts', 'tsx'],

  // Redirects for website folder structure
  async redirects() {
    return [
      // Redirect old profile paths to new website folder structure
      {
        source: '/profile',
        destination: '/website/profile',
        permanent: false,
      },
      {
        source: '/profile/edit/lifestyle',
        destination: '/website/profile/edit/lifestyle',
        permanent: false,
      },
      {
        source: '/profile/edit/preferences',
        destination: '/website/profile/edit/preferences',
        permanent: false,
      },
      {
        source: '/examples/interests-display',
        destination: '/website/examples/interests-display',
        permanent: false,
      },
    ]
  },

  // Ensure we're using the src/pages directory
  distDir: '.next',
  poweredByHeader: false,

  // Ensure we're properly handling the admin routes
  trailingSlash: false,

  // Server configuration
  serverExternalPackages: [],

  // Experimental features
  experimental: {
    // Empty experimental section - keeping for future options
  },
}

module.exports = nextConfig
