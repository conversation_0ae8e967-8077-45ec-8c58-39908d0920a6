#!/usr/bin/env node

/**
 * Test Brevo Email Service
 */

require('dotenv').config();

async function testBrevoEmail() {
    console.log('📧 Testing Brevo Email Service...\n');
    
    // Check environment variables
    const requiredVars = ['SMTP_HOST', 'SMTP_PORT', 'SMTP_USER', 'SMTP_PASS', 'EMAIL_FROM'];
    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
        console.log('❌ Missing email environment variables:');
        missingVars.forEach(varName => {
            console.log(`   - ${varName}`);
        });
        return false;
    }
    
    console.log('✅ Email environment variables found');
    console.log(`SMTP Host: ${process.env.SMTP_HOST}`);
    console.log(`SMTP Port: ${process.env.SMTP_PORT}`);
    console.log(`SMTP User: ${process.env.SMTP_USER}`);
    console.log(`Email From: ${process.env.EMAIL_FROM}`);
    
    try {
        // Import email service
        const { sendEmail } = require('../src/services/email.service.js');
        
        console.log('\n1️⃣ Testing Welcome Email...');
        
        // Test 1: Welcome Email
        const welcomeResult = await sendEmail({
            to: process.env.SMTP_USER, // Send to yourself for testing
            subject: '🎉 Welcome to Vaivahik - Test Email',
            template: 'welcome',
            data: {
                name: 'Test User',
                message: 'This is a test email from your Vaivahik matrimony app using Brevo!'
            }
        });
        
        console.log('✅ Welcome email sent successfully');
        console.log(`Message ID: ${welcomeResult.messageId}`);
        
        console.log('\n2️⃣ Testing Match Notification Email...');
        
        // Test 2: Match Notification Email (simulate)
        const matchResult = await sendEmail({
            to: process.env.SMTP_USER,
            subject: '💕 New Match Found - Vaivahik',
            template: 'default',
            data: {
                name: 'Test User',
                message: `
                    <h3>🎉 Great News! We found a potential match for you!</h3>
                    <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px;">
                        <h4>Match Details:</h4>
                        <p><strong>Name:</strong> Sample Match</p>
                        <p><strong>Age:</strong> 28 years</p>
                        <p><strong>Location:</strong> Mumbai, Maharashtra</p>
                        <p><strong>Education:</strong> Software Engineer</p>
                        <p><strong>Interests:</strong> Reading, Travel, Music</p>
                    </div>
                    <p>Login to your Vaivahik account to view the complete profile and connect!</p>
                    <a href="http://localhost:3000/dashboard" style="background: #7e57c2; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Profile</a>
                `
            }
        });
        
        console.log('✅ Match notification email sent successfully');
        console.log(`Message ID: ${matchResult.messageId}`);
        
        console.log('\n3️⃣ Testing Promotional Email...');
        
        // Test 3: Promotional Email
        const promoResult = await sendEmail({
            to: process.env.SMTP_USER,
            subject: '🎁 Special Offer - Premium Membership - Vaivahik',
            template: 'default',
            data: {
                name: 'Test User',
                message: `
                    <h3>🎁 Limited Time Offer - 50% Off Premium!</h3>
                    <p>Upgrade to Premium and unlock exclusive features:</p>
                    <ul style="text-align: left;">
                        <li>✅ Unlimited profile views</li>
                        <li>✅ Priority matching algorithm</li>
                        <li>✅ Direct contact details access</li>
                        <li>✅ Advanced search filters</li>
                        <li>✅ Profile boost feature</li>
                    </ul>
                    <div style="background: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 8px;">
                        <h4 style="color: #7e57c2;">Special Price: ₹999 ₹499/month</h4>
                        <p>Offer valid till: ${new Date(Date.now() + 7*24*60*60*1000).toLocaleDateString()}</p>
                    </div>
                    <a href="http://localhost:3000/upgrade" style="background: #28a745; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold;">Upgrade Now</a>
                `
            }
        });
        
        console.log('✅ Promotional email sent successfully');
        console.log(`Message ID: ${promoResult.messageId}`);
        
        return true;
        
    } catch (error) {
        console.log('\n❌ Email test failed:');
        console.log(`Error: ${error.message}`);
        
        if (error.message.includes('authentication')) {
            console.log('\n💡 Authentication Issues:');
            console.log('1. Check SMTP_USER (should be your Brevo login email)');
            console.log('2. Check SMTP_PASS (should be your Brevo SMTP key, not password)');
            console.log('3. Verify credentials in Brevo dashboard');
        }
        
        if (error.message.includes('connection')) {
            console.log('\n💡 Connection Issues:');
            console.log('1. Check internet connection');
            console.log('2. Verify SMTP_HOST: smtp-relay.brevo.com');
            console.log('3. Verify SMTP_PORT: 587');
        }
        
        return false;
    }
}

// Run the test
testBrevoEmail().then(success => {
    if (success) {
        console.log('\n🎉 Brevo Email Service is working perfectly!');
        console.log('\n📧 Check your email inbox for 3 test emails:');
        console.log('1. Welcome email');
        console.log('2. Match notification email');
        console.log('3. Promotional email');
        console.log('\n📊 Brevo Usage:');
        console.log('- Daily limit: 300 emails');
        console.log('- Monthly limit: 9,000 emails');
        console.log('- Perfect for your matrimony app!');
    } else {
        console.log('\n❌ Brevo Email Service needs configuration.');
        console.log('\n🔧 Next steps:');
        console.log('1. Verify Brevo SMTP credentials in .env file');
        console.log('2. Check Brevo account is active');
        console.log('3. Ensure sender email is verified in Brevo');
    }
    
    process.exit(success ? 0 : 1);
});
