/**
 * MSG91 OTP Test Script
 * 
 * This script tests the MSG91 OTP functionality by sending an OTP to a phone number.
 * It can be used to verify that the MSG91 integration is working correctly.
 * 
 * Usage:
 * node scripts/test-msg91-otp.js <phone_number>
 * 
 * Example:
 * node scripts/test-msg91-otp.js 9123456789
 */

// Load environment variables
require('dotenv').config();

// Import the MSG91 service
const msg91Service = require('../src/services/sms/msg91.service');

// Import the Redis OTP service
const otpService = require('../redis/otpService');

// Check if phone number is provided
const phoneNumber = process.argv[2];
if (!phoneNumber) {
  console.error('Please provide a phone number as an argument.');
  console.error('Usage: node scripts/test-msg91-otp.js <phone_number>');
  process.exit(1);
}

// Initialize the MSG91 service
msg91Service.initialize({
  apiKey: process.env.MSG91_API_KEY,
  senderId: process.env.MSG91_SENDER_ID,
  dltTemplateId: process.env.MSG91_DLT_TEMPLATE_ID,
  dltPeId: process.env.MSG91_DLT_PE_ID,
  otpTemplate: process.env.MSG91_OTP_TEMPLATE
});

// Format the phone number
const formattedPhone = msg91Service.formatPhoneNumber(phoneNumber);

// Test functions
async function testSendOTP() {
  console.log(`\n=== Testing Send OTP to ${formattedPhone} ===`);
  
  try {
    // Generate OTP
    const otp = msg91Service.generateOtp();
    console.log(`Generated OTP: ${otp}`);
    
    // Store OTP in Redis
    await otpService.setOTP(formattedPhone, otp, 300);
    console.log('OTP stored in Redis');
    
    // Send OTP via MSG91
    console.log('Sending OTP via MSG91...');
    const result = await msg91Service.sendOtp(formattedPhone, otp);
    
    if (result.success) {
      console.log('✅ OTP sent successfully!');
      console.log('Response:', JSON.stringify(result.data, null, 2));
    } else {
      console.error('❌ Failed to send OTP:', result.message);
      if (result.error) {
        console.error('Error details:', result.error);
      }
    }
    
    return { otp, success: result.success };
  } catch (error) {
    console.error('❌ Error in testSendOTP:', error);
    return { success: false };
  }
}

async function testVerifyOTP(otp) {
  console.log(`\n=== Testing Verify OTP for ${formattedPhone} ===`);
  
  try {
    // Verify OTP in Redis
    console.log('Verifying OTP in Redis...');
    const redisResult = await otpService.verifyOTP(formattedPhone, otp);
    
    if (redisResult) {
      console.log('✅ OTP verified successfully in Redis!');
    } else {
      console.error('❌ Failed to verify OTP in Redis');
    }
    
    // Verify OTP via MSG91
    console.log('Verifying OTP via MSG91...');
    const msg91Result = await msg91Service.verifyOtp(formattedPhone, otp);
    
    if (msg91Result.success) {
      console.log('✅ OTP verified successfully via MSG91!');
      console.log('Response:', JSON.stringify(msg91Result.data, null, 2));
    } else {
      console.error('❌ Failed to verify OTP via MSG91:', msg91Result.message);
      if (msg91Result.error) {
        console.error('Error details:', msg91Result.error);
      }
    }
    
    return { redisSuccess: redisResult, msg91Success: msg91Result.success };
  } catch (error) {
    console.error('❌ Error in testVerifyOTP:', error);
    return { redisSuccess: false, msg91Success: false };
  }
}

async function testResendOTP() {
  console.log(`\n=== Testing Resend OTP for ${formattedPhone} ===`);
  
  try {
    // Check if OTP exists in Redis
    const existingOtp = await otpService.getOTP(formattedPhone);
    
    if (existingOtp) {
      console.log(`Existing OTP found in Redis: ${existingOtp}`);
      
      // Get TTL for the OTP
      const ttl = await otpService.getOTPTTL(`otp:${formattedPhone}`);
      console.log(`TTL for existing OTP: ${ttl} seconds`);
      
      // Resend OTP via MSG91
      console.log('Resending OTP via MSG91...');
      const result = await msg91Service.resendOtp(formattedPhone);
      
      if (result.success) {
        console.log('✅ OTP resent successfully!');
        console.log('Response:', JSON.stringify(result.data, null, 2));
      } else {
        console.error('❌ Failed to resend OTP:', result.message);
        if (result.error) {
          console.error('Error details:', result.error);
        }
      }
      
      return { success: result.success };
    } else {
      console.log('No existing OTP found in Redis. Sending new OTP...');
      return await testSendOTP();
    }
  } catch (error) {
    console.error('❌ Error in testResendOTP:', error);
    return { success: false };
  }
}

// Run the tests
async function runTests() {
  try {
    console.log('\n=== MSG91 OTP Test Script ===');
    console.log('Testing with phone number:', formattedPhone);
    
    // Test environment variables
    console.log('\n=== Checking Environment Variables ===');
    const requiredVars = [
      'MSG91_API_KEY',
      'MSG91_SENDER_ID',
      'MSG91_DLT_TEMPLATE_ID'
    ];
    
    let missingVars = false;
    for (const varName of requiredVars) {
      if (!process.env[varName]) {
        console.error(`❌ Missing environment variable: ${varName}`);
        missingVars = true;
      } else {
        console.log(`✅ ${varName} is set`);
      }
    }
    
    if (missingVars) {
      console.error('\n❌ Please set all required environment variables in .env file');
      process.exit(1);
    }
    
    // Ask user which test to run
    const readline = require('readline').createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    console.log('\nSelect a test to run:');
    console.log('1. Send OTP');
    console.log('2. Verify OTP');
    console.log('3. Resend OTP');
    console.log('4. Run all tests');
    
    const answer = await new Promise(resolve => {
      readline.question('Enter your choice (1-4): ', resolve);
    });
    
    let sendResult, verifyResult, resendResult;
    
    switch (answer) {
      case '1':
        sendResult = await testSendOTP();
        break;
      case '2':
        const otpToVerify = await new Promise(resolve => {
          readline.question('Enter the OTP to verify: ', resolve);
        });
        verifyResult = await testVerifyOTP(otpToVerify);
        break;
      case '3':
        resendResult = await testResendOTP();
        break;
      case '4':
        sendResult = await testSendOTP();
        
        if (sendResult.success) {
          // Wait for user to confirm OTP received
          await new Promise(resolve => {
            readline.question('\nPress Enter when you receive the OTP...', resolve);
          });
          
          verifyResult = await testVerifyOTP(sendResult.otp);
          
          // Wait a bit before testing resend
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          resendResult = await testResendOTP();
        }
        break;
      default:
        console.error('Invalid choice');
        process.exit(1);
    }
    
    readline.close();
    
    console.log('\n=== Test Summary ===');
    if (sendResult) {
      console.log(`Send OTP: ${sendResult.success ? '✅ Success' : '❌ Failed'}`);
    }
    if (verifyResult) {
      console.log(`Verify OTP in Redis: ${verifyResult.redisSuccess ? '✅ Success' : '❌ Failed'}`);
      console.log(`Verify OTP via MSG91: ${verifyResult.msg91Success ? '✅ Success' : '❌ Failed'}`);
    }
    if (resendResult) {
      console.log(`Resend OTP: ${resendResult.success ? '✅ Success' : '❌ Failed'}`);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error running tests:', error);
    process.exit(1);
  }
}

// Run the tests
runTests();
