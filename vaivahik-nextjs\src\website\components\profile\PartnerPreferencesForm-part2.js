  return (
    <Box sx={{ mb: 4 }}>
      <Paper elevation={0} sx={{ p: 2, mb: 3, bgcolor: 'primary.light', color: 'primary.contrastText', borderRadius: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <FavoriteIcon sx={{ mr: 1 }} />
          <Typography variant="h5" component="h1">
            Partner Preferences
          </Typography>
        </Box>
        <Typography variant="body2" sx={{ mt: 1 }}>
          Define your preferences to receive more relevant match suggestions.
        </Typography>
        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
          <Box sx={{ flexGrow: 1, mr: 2 }}>
            <LinearProgress 
              variant="determinate" 
              value={completionPercentage} 
              sx={{ 
                height: 8, 
                borderRadius: 4,
                backgroundColor: 'rgba(255,255,255,0.3)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: 'white'
                }
              }} 
            />
          </Box>
          <Typography variant="body2" fontWeight="bold">
            {completionPercentage}% Complete
          </Typography>
        </Box>
      </Paper>
      
      <form onSubmit={handleSubmit}>
        <Card elevation={3} sx={{ mb: 3, borderRadius: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              Age & Height Preferences
              <Tooltip title="Specify the age and height range you're looking for">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth error={!!errors.ageRange}>
                  <FormLabel id="age-range-label">
                    Age Range: {formData.ageMin} - {formData.ageMax} years
                  </FormLabel>
                  <Box sx={{ px: 2, py: 1 }}>
                    <Slider
                      value={[formData.ageMin, formData.ageMax]}
                      onChange={handleAgeRangeChange}
                      valueLabelDisplay="auto"
                      min={18}
                      max={70}
                      marks={[
                        { value: 18, label: '18' },
                        { value: 30, label: '30' },
                        { value: 50, label: '50' },
                        { value: 70, label: '70' }
                      ]}
                    />
                  </Box>
                  {errors.ageRange && <FormHelperText error>{errors.ageRange}</FormHelperText>}
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.heightMin}>
                  <FormLabel htmlFor="heightMin">Minimum Height</FormLabel>
                  <Select
                    id="heightMin"
                    name="heightMin"
                    value={formData.heightMin}
                    onChange={handleInputChange}
                    size="small"
                  >
                    <MenuItem value="4.0">4'0" (122 cm)</MenuItem>
                    <MenuItem value="4.5">4'6" (137 cm)</MenuItem>
                    <MenuItem value="5.0">5'0" (152 cm)</MenuItem>
                    <MenuItem value="5.5">5'6" (168 cm)</MenuItem>
                    <MenuItem value="6.0">6'0" (183 cm)</MenuItem>
                    <MenuItem value="6.5">6'6" (198 cm)</MenuItem>
                  </Select>
                  {errors.heightMin && <FormHelperText error>{errors.heightMin}</FormHelperText>}
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.heightMax}>
                  <FormLabel htmlFor="heightMax">Maximum Height</FormLabel>
                  <Select
                    id="heightMax"
                    name="heightMax"
                    value={formData.heightMax}
                    onChange={handleInputChange}
                    size="small"
                  >
                    <MenuItem value="5.0">5'0" (152 cm)</MenuItem>
                    <MenuItem value="5.5">5'6" (168 cm)</MenuItem>
                    <MenuItem value="6.0">6'0" (183 cm)</MenuItem>
                    <MenuItem value="6.5">6'6" (198 cm)</MenuItem>
                    <MenuItem value="7.0">7'0" (213 cm)</MenuItem>
                  </Select>
                  {errors.heightMax && <FormHelperText error>{errors.heightMax}</FormHelperText>}
                </FormControl>
              </Grid>
              {errors.heightRange && (
                <Grid item xs={12}>
                  <Alert severity="error">{errors.heightRange}</Alert>
                </Grid>
              )}
            </Grid>
          </CardContent>
        </Card>
        
        <Card elevation={3} sx={{ mb: 3, borderRadius: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              Education & Career
              <Tooltip title="Specify education and career preferences">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <SchoolIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" fontWeight="medium">
                    Education Level
                  </Typography>
                </Box>
                <FilterChips
                  label="Preferred Education"
                  helperText="Select the education levels you prefer in a partner"
                  options={EDUCATION_OPTIONS}
                  selectedOptions={formData.educationLevel}
                  onChange={(newValue) => handleArrayChange('educationLevel', newValue)}
                  allowCustom={false}
                  maxSelections={5}
                  categoryColors={EDUCATION_CATEGORIES}
                  showSelectedCount={true}
                />
              </Grid>
              
              <Grid item xs={12} sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <WorkIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" fontWeight="medium">
                    Occupation
                  </Typography>
                </Box>
                <FilterChips
                  label="Preferred Occupations"
                  helperText="Select the occupations you prefer in a partner"
                  options={OCCUPATION_OPTIONS}
                  selectedOptions={formData.occupations}
                  onChange={(newValue) => handleArrayChange('occupations', newValue)}
                  allowCustom={true}
                  customPlaceholder="Add another occupation..."
                  maxSelections={8}
                  categoryColors={OCCUPATION_CATEGORIES}
                  showSelectedCount={true}
                />
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth>
                  <FormLabel id="incomeMin-label">Minimum Annual Income</FormLabel>
                  <Select
                    labelId="incomeMin-label"
                    id="incomeMin"
                    name="incomeMin"
                    value={formData.incomeMin}
                    onChange={handleInputChange}
                    size="small"
                  >
                    <MenuItem value="">No Preference</MenuItem>
                    <MenuItem value="ANY">Any Income</MenuItem>
                    <MenuItem value="UPTO_3L">Upto 3 Lakhs</MenuItem>
                    <MenuItem value="3L_5L">3 - 5 Lakhs</MenuItem>
                    <MenuItem value="5L_10L">5 - 10 Lakhs</MenuItem>
                    <MenuItem value="10L_20L">10 - 20 Lakhs</MenuItem>
                    <MenuItem value="ABOVE_20L">Above 20 Lakhs</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
