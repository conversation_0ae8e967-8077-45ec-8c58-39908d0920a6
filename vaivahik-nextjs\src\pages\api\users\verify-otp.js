import { BACKEND_API_URL } from '@/config';

// Server-side function to check if we should use real backend
const isUsingRealBackend = (req) => {
  // In production, always use real backend
  if (process.env.NODE_ENV === 'production') {
    return true;
  }

  // Check for header sent from client-side
  const useMockData = req.headers['x-use-mock-data'] === 'true';
  return !useMockData;
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { phone, otp } = req.body;

    // Validate required fields
    if (!phone || !otp) {
      return res.status(400).json({
        success: false,
        message: 'Phone number and OTP are required'
      });
    }

    // Validate phone number
    if (!/^[0-9]{10}$/.test(phone)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid phone number format'
      });
    }

    // Validate OTP format
    if (!/^[0-9]{4,6}$/.test(otp)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid OTP format'
      });
    }

    // Check if we should use real backend or mock data
    if (isUsingRealBackend(req)) {
      try {
        // Call the real backend API
        const apiUrl = `${BACKEND_API_URL}/users/verify-otp`;
        console.log(`[API] Verifying OTP with real backend: ${apiUrl}`);
        
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ phone, otp })
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `Backend API error: ${response.status}`);
        }
        
        const data = await response.json();
        return res.status(200).json({
          success: true,
          message: data.message || 'OTP verified successfully',
          isNewUser: data.isNewUser,
          userId: data.userId,
          accessToken: data.accessToken,
          profileStatus: data.profileStatus,
          source: 'real'
        });
      } catch (error) {
        console.error(`[API] Real backend error, falling back to mock data:`, error);
        // Fall back to mock implementation if real backend fails
        return res.status(200).json({
          success: true,
          message: 'OTP verified successfully (mock)',
          isNewUser: true, // Assume new user for mock
          source: 'mock',
          fallbackReason: error.message
        });
      }
    } else {
      // Use mock implementation
      console.log(`[API] Using mock implementation (feature flag set to mock)`);
      
      // In a real application, you would:
      // 1. Retrieve the stored OTP for the phone number
      // 2. Check if the OTP is valid and not expired
      // 3. Mark the phone number as verified
      
      // For development/testing, we'll just check against a fixed OTP
      const isValid = otp === '1234'; // Only for development/testing
      
      if (!isValid) {
        return res.status(400).json({
          success: false,
          message: 'Invalid OTP',
          source: 'mock'
        });
      }
      
      return res.status(200).json({
        success: true,
        message: 'OTP verified successfully (mock)',
        isNewUser: true, // Assume new user for mock
        source: 'mock'
      });
    }
  } catch (error) {
    console.error('Error verifying OTP:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while verifying OTP',
      error: error.message || 'Unknown error'
    });
  }
}
