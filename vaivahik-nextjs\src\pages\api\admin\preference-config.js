/**
 * Admin Preference Configuration API
 * Handles CRUD operations for preference categories, fields, importance settings, and defaults
 */

import { isUsingRealBackend, getApiBaseUrl } from '@/utils/featureFlags';

// Mock data for development/testing
const mockCategories = [
  {
    id: 'cat1',
    name: 'physical_attributes',
    displayName: 'Physical Attributes',
    description: 'Physical characteristics preferences',
    displayOrder: 1,
    icon: 'person',
    isActive: true,
    isRequired: true
  },
  {
    id: 'cat2',
    name: 'education_career',
    displayName: 'Education & Career',
    description: 'Education and career preferences',
    displayOrder: 2,
    icon: 'school',
    isActive: true,
    isRequired: false
  },
  {
    id: 'cat3',
    name: 'lifestyle',
    displayName: 'Lifestyle',
    description: 'Lifestyle and habits preferences',
    displayOrder: 3,
    icon: 'restaurant',
    isActive: true,
    isRequired: false
  },
  {
    id: 'cat4',
    name: 'community',
    displayName: 'Community',
    description: 'Community and cultural preferences',
    displayOrder: 4,
    icon: 'groups',
    isActive: true,
    isRequired: true
  }
];

const mockFields = [
  {
    id: 'field1',
    name: 'age_range',
    displayName: 'Age Range',
    description: 'Preferred age range of partner',
    fieldType: 'RANGE',
    displayOrder: 1,
    isActive: true,
    isRequired: true,
    isSearchable: true,
    isMatchCriteria: true,
    defaultValue: '{"min": 21, "max": 35}',
    validationRules: '{"minValue": 18, "maxValue": 70}',
    minValue: 18,
    maxValue: 70,
    stepValue: 1,
    categoryId: 'cat1'
  },
  {
    id: 'field2',
    name: 'height_range',
    displayName: 'Height Range',
    description: 'Preferred height range of partner',
    fieldType: 'RANGE',
    displayOrder: 2,
    isActive: true,
    isRequired: true,
    isSearchable: true,
    isMatchCriteria: true,
    defaultValue: '{"min": "5\'0\\"", "max": "6\'0\\""}',
    validationRules: '{"minValue": "4\'5\\"", "maxValue": "6\'6\\""}',
    minValue: 4.5,
    maxValue: 6.5,
    stepValue: 0.1,
    categoryId: 'cat1'
  },
  {
    id: 'field3',
    name: 'education_level',
    displayName: 'Education Level',
    description: 'Preferred education level of partner',
    fieldType: 'MULTI_SELECT',
    displayOrder: 1,
    isActive: true,
    isRequired: false,
    isSearchable: true,
    isMatchCriteria: true,
    defaultValue: '[]',
    categoryId: 'cat2'
  }
];

const mockOptions = [
  {
    id: 'opt1',
    value: 'GRADUATE',
    displayText: 'Graduate',
    description: 'Bachelor\'s degree',
    displayOrder: 1,
    isActive: true,
    fieldId: 'field3'
  },
  {
    id: 'opt2',
    value: 'POST_GRADUATE',
    displayText: 'Post Graduate', 
    description: 'Master\'s degree',
    displayOrder: 2,
    isActive: true,
    fieldId: 'field3'
  }
];

const mockImportanceSettings = [
  {
    id: 'imp1',
    importanceLevel: 8.0,
    description: 'Age is highly important for males',
    isActive: true,
    fieldId: 'field1',
    gender: 'MALE'
  },
  {
    id: 'imp2',
    importanceLevel: 7.0,
    description: 'Age is important for females',
    isActive: true,
    fieldId: 'field1',
    gender: 'FEMALE'
  }
];

const mockDefaultPreferences = {
  ageMin: 21,
  ageMax: 35,
  heightMin: "5'0\"",
  heightMax: "6'0\"",
  educationLevel: ["GRADUATE", "POST_GRADUATE"],
  dietPreference: "DOESNT_MATTER"
};

export default async function handler(req, res) {
  const { method, query } = req;

  try {
    if (isUsingRealBackend()) {
      // Forward to real backend API
      const apiUrl = `${getApiBaseUrl()}/api/admin/preference-config`;
      const response = await fetch(apiUrl, {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...req.headers
        },
        body: method !== 'GET' ? JSON.stringify(req.body) : undefined
      });

      const data = await response.json();
      return res.status(response.status).json(data);
    }

    // Mock implementation for development
    switch (method) {
      case 'GET':
        return handleGet(req, res);
      case 'POST':
        return handlePost(req, res);
      case 'PUT':
        return handlePut(req, res);
      case 'DELETE':
        return handleDelete(req, res);
      default:
        return res.status(405).json({ 
          success: false, 
          message: `Method ${method} not allowed` 
        });
    }
  } catch (error) {
    console.error('Preference Config API Error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
}

async function handleGet(req, res) {
  // Return all preference configuration data
  return res.status(200).json({
    success: true,
    data: {
      categories: mockCategories,
      fields: mockFields,
      options: mockOptions,
      importanceSettings: mockImportanceSettings,
      defaultPreferences: mockDefaultPreferences
    },
    message: 'Preference configuration retrieved successfully'
  });
}

async function handlePost(req, res) {
  const { type, data } = req.body;

  // Simulate creating new preference configuration item
  const newId = `new_${Date.now()}`;
  const newItem = { ...data, id: newId, createdAt: new Date().toISOString() };

  return res.status(201).json({
    success: true,
    data: newItem,
    message: `${type} created successfully`
  });
}

async function handlePut(req, res) {
  const { type, data } = req.body;

  // Simulate updating preference configuration
  let updatedData;
  
  switch (type) {
    case 'categories':
      updatedData = data;
      break;
    case 'fields':
      updatedData = data;
      break;
    case 'options':
      updatedData = data;
      break;
    case 'importance':
      updatedData = data;
      break;
    case 'defaults':
      updatedData = data;
      break;
    default:
      return res.status(400).json({
        success: false,
        message: 'Invalid update type'
      });
  }

  return res.status(200).json({
    success: true,
    data: updatedData,
    message: `${type} updated successfully`
  });
}

async function handleDelete(req, res) {
  const { type, id } = req.query;

  if (!type || !id) {
    return res.status(400).json({
      success: false,
      message: 'Type and ID are required for deletion'
    });
  }

  // Simulate deletion
  return res.status(200).json({
    success: true,
    message: `${type} with ID ${id} deleted successfully`
  });
}
