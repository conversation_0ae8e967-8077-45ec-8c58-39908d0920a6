import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useFCM } from './FCMProvider';
import useNotificationSocket from '@/hooks/useNotificationSocket';
import axios from 'axios';
import {
  Badge,
  IconButton,
  Menu,
  MenuItem,
  Typography,
  Box,
  Divider,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  CircularProgress,
  Tooltip,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  NotificationsActive as NotificationsActiveIcon,
  NotificationsOff as NotificationsOffIcon,
  CheckCircle as CheckCircleIcon,
  Delete as DeleteIcon,
  MarkChatRead as MarkChatReadIcon
} from '@mui/icons-material';
import { formatDistanceToNow } from 'date-fns';

/**
 * Notification Center Component
 * Displays a notification bell icon with a dropdown menu of notifications
 */
export default function NotificationCenter() {
  const { data: session } = useSession();
  const { notificationPermission, requestPermission } = useFCM();
  const [loading, setLoading] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Use WebSocket for real-time notifications
  const {
    notifications,
    unreadCount,
    markAsRead: markAsReadWs,
    markAllAsRead: markAllAsReadWs,
    fetchNotifications: fetchNotificationsWs,
    isConnected
  } = useNotificationSocket();

  // Fetch notifications when session changes or WebSocket connects
  useEffect(() => {
    if (session?.user && isConnected) {
      fetchNotificationsWs({ limit: 20, offset: 0 });
    } else if (session?.user && !isConnected) {
      // Fallback to REST API if WebSocket is not connected
      fetchNotificationsRest();
    }
  }, [session, isConnected]);

  // Fetch notifications from the REST API (fallback)
  const fetchNotificationsRest = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/notifications');

      // Only update if WebSocket is not connected
      if (!isConnected) {
        // The hook will handle notifications and unreadCount state
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  // Mark a notification as read
  const markAsRead = async (id) => {
    try {
      if (isConnected) {
        // Use WebSocket if connected
        markAsReadWs(id);
      } else {
        // Fallback to REST API
        await axios.put(`/api/notifications/${id}/read`);
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      if (isConnected) {
        // Use WebSocket if connected
        markAllAsReadWs();
      } else {
        // Fallback to REST API
        await axios.put('/api/notifications/read-all');
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  // Delete a notification
  const deleteNotification = async (id) => {
    try {
      await axios.delete(`/api/notifications/${id}`);

      // Update local state
      const updatedNotifications = notifications.filter(notification => notification.id !== id);
      setNotifications(updatedNotifications);

      // Update unread count
      const unread = updatedNotifications.filter(notification => !notification.isRead).length;
      setUnreadCount(unread);
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  // Handle click on notification bell
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  // Handle close of notification menu
  const handleClose = () => {
    setAnchorEl(null);
  };

  // Handle click on a notification
  const handleNotificationClick = (notification) => {
    // Mark as read
    if (!notification.isRead) {
      markAsRead(notification.id);
    }

    // Handle navigation based on notification type
    const data = notification.data;
    if (data) {
      // Navigate based on notification type
      switch (data.type) {
        case 'NEW_MATCH':
          window.location.href = `/profile/${data.matchId}`;
          break;
        case 'PROFILE_VIEW':
          window.location.href = `/profile/${data.viewerId}`;
          break;
        case 'INTEREST_RECEIVED':
          window.location.href = '/interests/received';
          break;
        case 'INTEREST_ACCEPTED':
          window.location.href = `/profile/${data.acceptorId}`;
          break;
        case 'NEW_MESSAGE':
          window.location.href = `/messages/${data.conversationId}`;
          break;
        case 'PROMOTION':
          window.location.href = data.webLink || '/promotions';
          break;
        case 'VERIFICATION_STATUS':
          window.location.href = data.status === 'APPROVED' ? '/profile' : '/profile/verification';
          break;
        default:
          // Use webLink if provided, otherwise do nothing
          if (data.webLink) {
            window.location.href = data.webLink;
          }
      }
    }

    handleClose();
  };

  // Get notification icon based on permission status
  const getNotificationIcon = () => {
    if (loading) {
      return <CircularProgress size={24} color="inherit" />;
    }

    if (unreadCount > 0) {
      return <NotificationsActiveIcon />;
    }

    if (notificationPermission === 'granted') {
      return <NotificationsIcon />;
    }

    return <NotificationsOffIcon />;
  };

  // Format notification time
  const formatNotificationTime = (date) => {
    return formatDistanceToNow(new Date(date), { addSuffix: true });
  };

  return (
    <>
      <Tooltip title="Notifications">
        <IconButton
          onClick={handleClick}
          size="large"
          color="inherit"
          aria-label="notifications"
        >
          <Badge badgeContent={unreadCount} color="error">
            {getNotificationIcon()}
          </Badge>
        </IconButton>
      </Tooltip>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: isMobile ? '100%' : 350,
            maxWidth: '100%',
            maxHeight: '80vh',
            mt: 1.5,
            '& .MuiList-root': {
              py: 0,
            },
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">Notifications</Typography>
          {unreadCount > 0 && (
            <Tooltip title="Mark all as read">
              <IconButton size="small" onClick={markAllAsRead}>
                <MarkChatReadIcon />
              </IconButton>
            </Tooltip>
          )}
        </Box>

        <Divider />

        {notificationPermission !== 'granted' && (
          <Box sx={{ p: 2, bgcolor: 'action.hover' }}>
            <Typography variant="body2" gutterBottom>
              Enable push notifications to stay updated
            </Typography>
            <Button
              variant="outlined"
              size="small"
              onClick={requestPermission}
              startIcon={<NotificationsIcon />}
            >
              Enable Notifications
            </Button>
          </Box>
        )}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress size={30} />
          </Box>
        ) : notifications.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography color="text.secondary">No notifications yet</Typography>
          </Box>
        ) : (
          <List sx={{ p: 0, maxHeight: 400, overflow: 'auto' }}>
            {notifications.map((notification) => (
              <ListItem
                key={notification.id}
                alignItems="flex-start"
                sx={{
                  bgcolor: notification.isRead ? 'inherit' : 'action.hover',
                  '&:hover': {
                    bgcolor: 'action.selected',
                    cursor: 'pointer',
                  },
                }}
                secondaryAction={
                  <Tooltip title="Delete">
                    <IconButton
                      edge="end"
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteNotification(notification.id);
                      }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                }
                onClick={() => handleNotificationClick(notification)}
              >
                <ListItemAvatar>
                  <Avatar
                    src={notification.imageUrl}
                    alt=""
                    sx={{
                      bgcolor: notification.isRead ? 'grey.300' : 'primary.main',
                    }}
                  >
                    {notification.isRead ? <CheckCircleIcon /> : <NotificationsIcon />}
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={notification.title}
                  secondary={
                    <>
                      <Typography
                        component="span"
                        variant="body2"
                        color="text.primary"
                        sx={{ display: 'block' }}
                      >
                        {notification.body}
                      </Typography>
                      <Typography
                        component="span"
                        variant="caption"
                        color="text.secondary"
                      >
                        {formatNotificationTime(notification.createdAt)}
                      </Typography>
                    </>
                  }
                />
              </ListItem>
            ))}
          </List>
        )}
      </Menu>
    </>
  );
}
