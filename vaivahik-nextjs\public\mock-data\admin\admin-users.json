{"success": true, "adminUsers": [{"id": 1, "name": "Super Admin", "email": "<EMAIL>", "role": "SUPER_ADMIN", "permissions": ["all"], "lastLogin": "2023-08-01T10:15:00Z", "createdAt": "2023-01-01T00:00:00Z", "status": "active", "avatar": null}, {"id": 2, "name": "Content Manager", "email": "<EMAIL>", "role": "CONTENT_MANAGER", "permissions": ["blog_posts", "success_stories", "biodata_templates"], "lastLogin": "2023-07-28T14:30:00Z", "createdAt": "2023-02-15T00:00:00Z", "status": "active", "avatar": null}, {"id": 3, "name": "User Manager", "email": "<EMAIL>", "role": "USER_MANAGER", "permissions": ["users", "verification", "reports"], "lastLogin": "2023-07-30T09:45:00Z", "createdAt": "2023-03-10T00:00:00Z", "status": "active", "avatar": null}, {"id": 4, "name": "Finance Admin", "email": "<EMAIL>", "role": "FINANCE_ADMIN", "permissions": ["subscriptions", "transactions", "revenue_reports"], "lastLogin": "2023-07-25T16:20:00Z", "createdAt": "2023-04-05T00:00:00Z", "status": "active", "avatar": null}, {"id": 5, "name": "Support Staff", "email": "<EMAIL>", "role": "SUPPORT_STAFF", "permissions": ["users", "verification", "reports", "notifications"], "lastLogin": "2023-07-31T11:10:00Z", "createdAt": "2023-05-20T00:00:00Z", "status": "active", "avatar": null}, {"id": 6, "name": "Test Admin", "email": "<EMAIL>", "role": "CONTENT_MANAGER", "permissions": ["blog_posts", "success_stories"], "lastLogin": null, "createdAt": "2023-06-15T00:00:00Z", "status": "inactive", "avatar": null}], "pagination": {"total": 6, "page": 1, "limit": 10, "totalPages": 1}, "roles": ["SUPER_ADMIN", "CONTENT_MANAGER", "USER_MANAGER", "FINANCE_ADMIN", "SUPPORT_STAFF"]}