import { differenceInYears, isValid, parseISO } from 'date-fns';

/**
 * Validates date of birth based on gender
 * @param {Date|string} dateOfBirth - The date of birth to validate
 * @param {string} gender - The gender of the user (MALE or FEMALE)
 * @returns {Object} - { isValid: boolean, message: string }
 */
export const validateDateOfBirth = (dateOfBirth, gender) => {
  // Handle empty values
  if (!dateOfBirth) {
    return { isValid: false, message: 'Date of birth is required' };
  }

  // Parse the date if it's a string
  const dob = typeof dateOfBirth === 'string' ? parseISO(dateOfBirth) : dateOfBirth;

  // Check if the date is valid
  if (!isValid(dob)) {
    return { isValid: false, message: 'Invalid date format' };
  }

  // Check if the date is in the future
  if (dob > new Date()) {
    return { isValid: false, message: 'Date of birth cannot be in the future' };
  }

  // Calculate age
  const age = differenceInYears(new Date(), dob);
  
  // Validate based on gender
  const minAge = gender === 'FEMALE' ? 18 : 21;
  const maxAge = 80; // Reasonable upper limit
  
  if (age < minAge) {
    return { 
      isValid: false, 
      message: `Minimum age for ${gender === 'FEMALE' ? 'females' : 'males'} is ${minAge} years` 
    };
  }
  
  if (age > maxAge) {
    return { 
      isValid: false, 
      message: `Maximum age allowed is ${maxAge} years` 
    };
  }
  
  return { isValid: true, message: '' };
};

/**
 * Validates birth time
 * @param {Date|string} birthTime - The birth time to validate
 * @returns {Object} - { isValid: boolean, message: string }
 */
export const validateBirthTime = (birthTime) => {
  // Birth time is optional but if provided, must be valid
  if (!birthTime) {
    return { isValid: true, message: '' };
  }
  
  // Parse the time if it's a string
  const time = typeof birthTime === 'string' ? parseISO(`2000-01-01T${birthTime}`) : birthTime;
  
  // Check if the time is valid
  if (!isValid(time)) {
    return { isValid: false, message: 'Invalid time format' };
  }
  
  return { isValid: true, message: '' };
};

/**
 * Validates birth place
 * @param {string} birthPlace - The birth place to validate
 * @returns {Object} - { isValid: boolean, message: string }
 */
export const validateBirthPlace = (birthPlace) => {
  if (!birthPlace || birthPlace.trim() === '') {
    return { isValid: false, message: 'Birth place is required' };
  }
  
  if (birthPlace.length < 3) {
    return { isValid: false, message: 'Birth place must be at least 3 characters' };
  }
  
  return { isValid: true, message: '' };
};

/**
 * Validates all birth details
 * @param {Object} birthDetails - The birth details to validate
 * @param {Date|string} birthDetails.dateOfBirth - Date of birth
 * @param {Date|string} birthDetails.birthTime - Birth time
 * @param {string} birthDetails.birthPlace - Birth place
 * @param {string} birthDetails.gender - Gender (MALE or FEMALE)
 * @returns {Object} - { isValid: boolean, errors: Object }
 */
export const validateBirthDetails = (birthDetails) => {
  const { dateOfBirth, birthTime, birthPlace, gender } = birthDetails;
  
  const dobValidation = validateDateOfBirth(dateOfBirth, gender);
  const timeValidation = validateBirthTime(birthTime);
  const placeValidation = validateBirthPlace(birthPlace);
  
  const errors = {};
  
  if (!dobValidation.isValid) {
    errors.dateOfBirth = dobValidation.message;
  }
  
  if (!timeValidation.isValid) {
    errors.birthTime = timeValidation.message;
  }
  
  if (!placeValidation.isValid) {
    errors.birthPlace = placeValidation.message;
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Formats birth details for display or API submission
 * @param {Object} birthDetails - The birth details to format
 * @returns {Object} - Formatted birth details
 */
export const formatBirthDetails = (birthDetails) => {
  const { dateOfBirth, birthTime, birthPlace, birthPlaceCoordinates } = birthDetails;
  
  // Format date as ISO string (YYYY-MM-DD)
  const formattedDate = dateOfBirth instanceof Date 
    ? dateOfBirth.toISOString().split('T')[0] 
    : dateOfBirth;
  
  // Format time as 24-hour string (HH:MM)
  let formattedTime = '';
  if (birthTime) {
    if (birthTime instanceof Date) {
      formattedTime = birthTime.toTimeString().slice(0, 5);
    } else {
      formattedTime = birthTime;
    }
  }
  
  return {
    dateOfBirth: formattedDate,
    birthTime: formattedTime,
    birthPlace,
    birthPlaceCoordinates
  };
};
