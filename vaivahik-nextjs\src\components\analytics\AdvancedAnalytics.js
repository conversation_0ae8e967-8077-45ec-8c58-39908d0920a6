/**
 * Advanced Analytics System
 * Features: Real-time metrics, user behavior tracking, conversion funnels, predictive analytics
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  People as PeopleIcon,
  Favorite as FavoriteIcon,
  AttachMoney as MoneyIcon,
  Speed as SpeedIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { motion } from 'framer-motion';

const AdvancedAnalytics = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [realTimeData, setRealTimeData] = useState({});
  const [userBehaviorData, setUserBehaviorData] = useState([]);
  const [conversionFunnel, setConversionFunnel] = useState([]);

  // Fetch analytics data
  const fetchAnalyticsData = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/advanced-analytics?timeRange=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setAnalyticsData(data);
        setUserBehaviorData(data.userBehavior || []);
        setConversionFunnel(data.conversionFunnel || []);
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
    } finally {
      setLoading(false);
    }
  }, [timeRange]);

  // Real-time data updates
  useEffect(() => {
    const interval = setInterval(async () => {
      try {
        const response = await fetch('/api/admin/real-time-metrics', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          setRealTimeData(data);
        }
      } catch (error) {
        console.error('Error fetching real-time data:', error);
      }
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    fetchAnalyticsData();
  }, [fetchAnalyticsData]);

  // Chart colors
  const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00'];

  // Metric cards data
  const metricCards = [
    {
      title: 'Active Users',
      value: realTimeData.activeUsers || 0,
      change: '+12.5%',
      trend: 'up',
      icon: PeopleIcon,
      color: '#1976d2'
    },
    {
      title: 'Matches Made',
      value: realTimeData.matchesToday || 0,
      change: '+8.3%',
      trend: 'up',
      icon: FavoriteIcon,
      color: '#d32f2f'
    },
    {
      title: 'Revenue Today',
      value: `₹${realTimeData.revenueToday || 0}`,
      change: '+15.2%',
      trend: 'up',
      icon: MoneyIcon,
      color: '#388e3c'
    },
    {
      title: 'Avg Response Time',
      value: `${realTimeData.avgResponseTime || 0}ms`,
      change: '-5.1%',
      trend: 'down',
      icon: SpeedIcon,
      color: '#f57c00'
    }
  ];

  // User behavior heatmap data
  const heatmapData = [
    { hour: '00', Monday: 20, Tuesday: 25, Wednesday: 30, Thursday: 35, Friday: 40, Saturday: 45, Sunday: 50 },
    { hour: '06', Monday: 60, Tuesday: 65, Wednesday: 70, Thursday: 75, Friday: 80, Saturday: 85, Sunday: 90 },
    { hour: '12', Monday: 100, Tuesday: 95, Wednesday: 90, Thursday: 85, Friday: 80, Saturday: 75, Sunday: 70 },
    { hour: '18', Monday: 120, Tuesday: 115, Wednesday: 110, Thursday: 105, Friday: 100, Saturday: 95, Sunday: 90 },
    { hour: '24', Monday: 40, Tuesday: 35, Wednesday: 30, Thursday: 25, Friday: 20, Saturday: 15, Sunday: 10 }
  ];

  // Conversion funnel data
  const funnelData = [
    { name: 'Visitors', value: 10000, fill: '#8884d8' },
    { name: 'Registrations', value: 3000, fill: '#82ca9d' },
    { name: 'Profile Complete', value: 2400, fill: '#ffc658' },
    { name: 'First Match', value: 1800, fill: '#ff7300' },
    { name: 'Premium Upgrade', value: 600, fill: '#00ff00' },
    { name: 'Success Story', value: 150, fill: '#ff0000' }
  ];

  // Geographic data
  const geographicData = [
    { state: 'Maharashtra', users: 4500, matches: 1200 },
    { state: 'Karnataka', users: 2800, matches: 750 },
    { state: 'Gujarat', users: 2200, matches: 580 },
    { state: 'Rajasthan', users: 1800, matches: 420 },
    { state: 'Madhya Pradesh', users: 1500, matches: 350 }
  ];

  // Age demographics
  const ageData = [
    { age: '18-25', male: 1200, female: 1800 },
    { age: '26-30', male: 2400, female: 2200 },
    { age: '31-35', male: 1800, female: 1200 },
    { age: '36-40', male: 800, female: 600 },
    { age: '40+', male: 400, female: 200 }
  ];

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <LinearProgress />
        <Typography sx={{ mt: 2 }}>Loading advanced analytics...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          Advanced Analytics
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={timeRange}
              label="Time Range"
              onChange={(e) => setTimeRange(e.target.value)}
            >
              <MenuItem value="1d">Last 24 Hours</MenuItem>
              <MenuItem value="7d">Last 7 Days</MenuItem>
              <MenuItem value="30d">Last 30 Days</MenuItem>
              <MenuItem value="90d">Last 3 Months</MenuItem>
              <MenuItem value="1y">Last Year</MenuItem>
            </Select>
          </FormControl>
          
          <Tooltip title="Refresh Data">
            <IconButton onClick={fetchAnalyticsData}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Export Report">
            <IconButton>
              <DownloadIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Real-time Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {metricCards.map((metric, index) => (
          <Grid item xs={12} sm={6} md={3} key={metric.title}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        {metric.title}
                      </Typography>
                      <Typography variant="h4" component="div">
                        {metric.value}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                        {metric.trend === 'up' ? (
                          <TrendingUpIcon sx={{ color: 'success.main', mr: 0.5 }} />
                        ) : (
                          <TrendingDownIcon sx={{ color: 'error.main', mr: 0.5 }} />
                        )}
                        <Typography
                          variant="body2"
                          color={metric.trend === 'up' ? 'success.main' : 'error.main'}
                        >
                          {metric.change}
                        </Typography>
                      </Box>
                    </Box>
                    <metric.icon sx={{ fontSize: 40, color: metric.color, opacity: 0.7 }} />
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Charts Grid */}
      <Grid container spacing={3}>
        {/* User Growth Chart */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                User Growth Trend
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={userBehaviorData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <RechartsTooltip />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="newUsers"
                    stackId="1"
                    stroke="#8884d8"
                    fill="#8884d8"
                    name="New Users"
                  />
                  <Area
                    type="monotone"
                    dataKey="activeUsers"
                    stackId="1"
                    stroke="#82ca9d"
                    fill="#82ca9d"
                    name="Active Users"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Conversion Funnel */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Conversion Funnel
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={funnelData} layout="horizontal">
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="name" type="category" width={100} />
                  <RechartsTooltip />
                  <Bar dataKey="value" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Age Demographics */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Age Demographics
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={ageData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="age" />
                  <YAxis />
                  <RechartsTooltip />
                  <Legend />
                  <Bar dataKey="male" fill="#8884d8" name="Male" />
                  <Bar dataKey="female" fill="#82ca9d" name="Female" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Geographic Distribution */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Geographic Distribution
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>State</TableCell>
                      <TableCell align="right">Users</TableCell>
                      <TableCell align="right">Matches</TableCell>
                      <TableCell align="right">Success Rate</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {geographicData.map((row) => (
                      <TableRow key={row.state}>
                        <TableCell component="th" scope="row">
                          {row.state}
                        </TableCell>
                        <TableCell align="right">{row.users}</TableCell>
                        <TableCell align="right">{row.matches}</TableCell>
                        <TableCell align="right">
                          <Chip
                            label={`${((row.matches / row.users) * 100).toFixed(1)}%`}
                            color={row.matches / row.users > 0.25 ? 'success' : 'warning'}
                            size="small"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* User Behavior Heatmap */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                User Activity Heatmap (by Hour and Day)
              </Typography>
              <ResponsiveContainer width="100%" height={200}>
                <LineChart data={heatmapData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="hour" />
                  <YAxis />
                  <RechartsTooltip />
                  <Legend />
                  {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day, index) => (
                    <Line
                      key={day}
                      type="monotone"
                      dataKey={day}
                      stroke={colors[index % colors.length]}
                      strokeWidth={2}
                    />
                  ))}
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AdvancedAnalytics;
