import { useState, useEffect } from 'react';
import EnhancedAdminLayout from '@/components/admin/EnhancedAdminLayout';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Divider,
  FormControlLabel,
  Grid,
  Paper,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Alert,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Storage as StorageIcon,
  Code as CodeIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import {
  getAllFeatureFlags,
  setFeatureFlag,
  resetFeatureFlags,
  isUsingRealBackend
} from '@/utils/featureFlags';
import axiosInstance from '@/utils/axiosConfig';

export default function FeatureFlags() {
  const [flags, setFlags] = useState({});
  const [needsReload, setNeedsReload] = useState(false);
  const [loading, setLoading] = useState(true);

  // Load feature flags on mount
  useEffect(() => {
    loadFeatureFlags();
  }, []);

  const loadFeatureFlags = async () => {
    try {
      setLoading(true);

      // Try to fetch from API first
      const response = await axiosInstance.get('/api/admin/feature-flags');

      if (response.data && response.data.success) {
        setFlags(response.data.flags);
      } else {
        // Fallback to local storage
        setFlags(getAllFeatureFlags());
      }
    } catch (error) {
      console.error('Error loading feature flags:', error);
      // Fallback to local storage
      setFlags(getAllFeatureFlags());
    } finally {
      setLoading(false);
    }
  };

  // Handle toggle for feature flags
  const handleToggleFlag = (flagName) => {
    const currentValue = flags[flagName];
    const newValue = !currentValue;
    
    // Update the flag
    setFeatureFlag(flagName, newValue);
    
    // Update local state
    setFlags(prev => ({ ...prev, [flagName]: newValue }));
    
    // Check if reload is needed
    const reloadFlags = ['useRealBackend', 'enableDarkMode', 'enableAnimations'];
    if (reloadFlags.includes(flagName)) {
      setNeedsReload(true);
      toast.info(`Changed ${flagName} to ${newValue}. Reload required for changes to take effect.`);
    } else {
      toast.success(`Changed ${flagName} to ${newValue}`);
    }
  };

  // Handle reset all flags
  const handleResetFlags = () => {
    resetFeatureFlags();
    setFlags(getAllFeatureFlags());
    setNeedsReload(true);
    toast.info('All feature flags reset to defaults. Reload required for changes to take effect.');
  };

  // Handle page reload
  const handleReload = () => {
    window.location.reload();
  };

  return (
    <EnhancedAdminLayout title="Feature Flags">
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4">Feature Flags</Typography>
          <Box>
            {needsReload && (
              <Button 
                variant="contained" 
                color="warning" 
                startIcon={<RefreshIcon />} 
                onClick={handleReload}
                sx={{ mr: 2 }}
              >
                Reload Page
              </Button>
            )}
            <Button 
              variant="outlined" 
              startIcon={<RefreshIcon />} 
              onClick={handleResetFlags}
            >
              Reset All Flags
            </Button>
          </Box>
        </Box>

        <Alert severity="info" sx={{ mb: 3 }}>
          Feature flags allow you to toggle features on and off without changing code. 
          Some flags require a page reload to take effect.
        </Alert>

        {/* Data Source Section */}
        <Card sx={{ mb: 3 }}>
          <CardHeader 
            title={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <StorageIcon sx={{ mr: 1 }} />
                <Typography variant="h6">Data Source</Typography>
              </Box>
            }
            action={
              <Chip 
                label={isUsingRealBackend() ? 'Real Backend' : 'Mock Data'} 
                color={isUsingRealBackend() ? 'success' : 'warning'} 
                variant="outlined"
              />
            }
          />
          <Divider />
          <CardContent>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="subtitle1">Backend Data Source</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Toggle between real backend API and mock data
                    </Typography>
                  </Box>
                  <Switch
                    checked={flags.useRealBackend || false}
                    onChange={() => handleToggleFlag('useRealBackend')}
                    color="primary"
                  />
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Feature Flags Table */}
        <Card>
          <CardHeader 
            title={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <SettingsIcon sx={{ mr: 1 }} />
                <Typography variant="h6">Feature Flags</Typography>
              </Box>
            }
          />
          <Divider />
          <CardContent>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Flag Name</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Toggle</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {Object.entries(flags)
                    .filter(([key]) => key !== 'useRealBackend') // Already handled above
                    .map(([key, value]) => (
                      typeof value === 'boolean' && (
                        <TableRow key={key}>
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              {key}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {getFlagDescription(key)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              icon={value ? <CheckCircleIcon /> : <CancelIcon />}
                              label={value ? 'Enabled' : 'Disabled'}
                              color={value ? 'success' : 'default'}
                              variant="outlined"
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Switch
                              checked={value}
                              onChange={() => handleToggleFlag(key)}
                              color="primary"
                            />
                          </TableCell>
                        </TableRow>
                      )
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        {/* Environment Information */}
        <Card sx={{ mt: 3 }}>
          <CardHeader 
            title={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CodeIcon sx={{ mr: 1 }} />
                <Typography variant="h6">Environment Information</Typography>
              </Box>
            }
          />
          <Divider />
          <CardContent>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <Paper variant="outlined" sx={{ p: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>Node Environment</Typography>
                  <Chip 
                    label={process.env.NODE_ENV || 'development'} 
                    color={process.env.NODE_ENV === 'production' ? 'success' : 'info'} 
                  />
                </Paper>
              </Grid>
              <Grid item xs={12} md={4}>
                <Paper variant="outlined" sx={{ p: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>API URL</Typography>
                  <Typography variant="body2" noWrap>
                    {process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api'}
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} md={4}>
                <Paper variant="outlined" sx={{ p: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>Backend URL</Typography>
                  <Typography variant="body2" noWrap>
                    {process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:5000/api'}
                  </Typography>
                </Paper>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Box>
    </EnhancedAdminLayout>
  );
}

// Helper function to get flag descriptions
const getFlagDescription = (flagName) => {
  const descriptions = {
    enableBiodataTemplates: 'Enable biodata template features',
    enableSpotlightFeatures: 'Enable spotlight features',
    enablePaymentGateway: 'Enable payment gateway integration',
    enableNotifications: 'Enable notification system',
    enableMatchingAlgorithm: 'Enable AI matching algorithm',
    enableDarkMode: 'Enable dark mode support',
    enableAnimations: 'Enable UI animations',
    showMockDataIndicator: 'Show indicator when using mock data',
    enableDebugLogging: 'Enable debug logging'
  };
  
  return descriptions[flagName] || 'No description available';
};
