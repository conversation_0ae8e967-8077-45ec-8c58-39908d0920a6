// src/models/ChatSettings.js

/**
 * ChatSettings model for storing chat configuration parameters
 * This model will be used to configure various aspects of the chat system
 */

const ChatSettings = {
  // Chat feature flags
  enableReadReceipts: true,
  enableTypingIndicators: true,
  enableMessageReactions: true,
  enableFileSharing: false,
  enableVoiceMessages: false,
  enableVideoChat: false,

  // Chat limits
  maxMessageLength: 1000,
  maxFileSize: 5 * 1024 * 1024, // 5MB
  allowedFileTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],

  // Chat behavior
  messageExpiryDays: 180, // How long to keep messages before archiving
  offlineMessageNotification: true,

  // Premium features
  premiumFeatures: [
    'unlimitedMessages',
    'fileSharing',
    'messageRetention',
    'prioritySupport',
    'readReceipts',
    'typingIndicators'
  ],

  // Feature limits by user tier
  tierLimits: {
    BASIC: {
      dailyMessageLimit: 10,
      canStartNewConversations: false,
      canSendImages: false,
      messageRetentionDays: 30
    },
    VERIFIED: {
      dailyMessageLimit: 30,
      canStartNewConversations: true,
      canSendImages: true,
      messageRetentionDays: 60
    },
    PREMIUM: {
      dailyMessageLimit: null, // unlimited
      canStartNewConversations: true,
      canSendImages: true,
      messageRetentionDays: 365
    }
  },

  // AI-powered features
  aiFeatures: {
    smartReplies: {
      enabled: true,
      suggestionCount: 3
    },
    contentModeration: {
      enabled: true,
      strictness: 'medium', // 'low', 'medium', 'high'
      // Tier-specific moderation settings
      tierSettings: {
        BASIC: {
          strictness: 'high',
          autoReject: true,
          maskProfanity: true,
          allowContactInfo: false,
          allowedContactTypes: []
        },
        VERIFIED: {
          strictness: 'medium',
          autoReject: true,
          maskProfanity: true,
          allowContactInfo: false,
          allowedContactTypes: []
        },
        PREMIUM: {
          strictness: 'low',
          autoReject: false,
          maskProfanity: false,
          allowContactInfo: true,
          allowedContactTypes: ['email', 'phone']
        }
      }
    },
    translationSupport: {
      enabled: false,
      languages: ['en', 'hi', 'mr'] // English, Hindi, Marathi
    }
  },

  // Get tier settings with promotions applied
  getTierSettings: function(userTier, userCreatedAt) {
    // If no promotions module is available, return base settings
    try {
      const PromotionSettings = require('./PromotionSettings');
      return PromotionSettings.getTierSettingsWithPromotions(userTier, userCreatedAt);
    } catch (error) {
      console.warn('PromotionSettings not available, using base settings');
      return this.tierLimits[userTier] || this.tierLimits.BASIC;
    }
  },

  // Get active promotions
  getActivePromotions: function() {
    try {
      const PromotionSettings = require('./PromotionSettings');
      return PromotionSettings.getActivePromotions();
    } catch (error) {
      console.warn('PromotionSettings not available');
      return {};
    }
  },

  // Check if a specific promotion is active
  isPromotionActive: function(promotionName) {
    try {
      const PromotionSettings = require('./PromotionSettings');
      return PromotionSettings.isPromotionActive(promotionName);
    } catch (error) {
      console.warn('PromotionSettings not available');
      return false;
    }
  },

  // Get moderation settings for a specific tier
  getModerationSettingsForTier: function(userTier) {
    // Default to BASIC tier if not specified
    const tier = userTier || 'BASIC';

    // Get base settings
    const baseSettings = this.aiFeatures.contentModeration;

    // Get tier-specific settings
    const tierSettings = baseSettings.tierSettings[tier] || baseSettings.tierSettings.BASIC;

    // Check if there are any active promotions that affect moderation
    let promotionOverrides = {};
    try {
      const PromotionSettings = require('./PromotionSettings');
      const activePromotions = PromotionSettings.getActivePromotions();

      // Look for moderation overrides in active promotions
      Object.values(activePromotions).forEach(promotion => {
        if (promotion.moderationOverrides && promotion.moderationOverrides[tier]) {
          // Merge promotion overrides with existing settings
          promotionOverrides = {
            ...promotionOverrides,
            ...promotion.moderationOverrides[tier]
          };
        }
      });
    } catch (error) {
      console.warn('PromotionSettings not available for moderation settings');
    }

    // Combine base settings, tier settings, and promotion overrides
    return {
      enabled: baseSettings.enabled,
      strictness: tierSettings.strictness,
      autoReject: tierSettings.autoReject,
      maskProfanity: tierSettings.maskProfanity,
      allowContactInfo: tierSettings.allowContactInfo,
      allowedContactTypes: tierSettings.allowedContactTypes,
      // Apply any promotion overrides
      ...promotionOverrides
    };
  }
};

module.exports = ChatSettings;
