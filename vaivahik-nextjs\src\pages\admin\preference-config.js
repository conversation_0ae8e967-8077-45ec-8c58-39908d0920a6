import { useState, useEffect } from 'react';
import EnhancedAdminLayout from '@/components/admin/EnhancedAdminLayout';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  Snackbar
} from '@mui/material';
import CategoryIcon from '@mui/icons-material/Category';
import ListAltIcon from '@mui/icons-material/ListAlt';
import TuneIcon from '@mui/icons-material/Tune';
import SettingsIcon from '@mui/icons-material/Settings';

// Import tab components
import CategoriesTab from '@/components/admin/preferenceConfig/CategoriesTab';
import FieldsTab from '@/components/admin/preferenceConfig/FieldsTab';
import ImportanceTab from '@/components/admin/preferenceConfig/ImportanceTab';
import DefaultsTab from '@/components/admin/preferenceConfig/DefaultsTab';

export default function PreferenceConfig() {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // State for preference data
  const [categories, setCategories] = useState([]);
  const [fields, setFields] = useState([]);
  const [options, setOptions] = useState([]);
  const [importanceSettings, setImportanceSettings] = useState([]);
  const [defaultPreferences, setDefaultPreferences] = useState({});

  useEffect(() => {
    // Fetch preference configuration data
    const fetchPreferenceData = async () => {
      setLoading(true);
      try {
        // Fetch data from API
        const response = await fetch('/api/admin/preference-config');

        if (!response.ok) {
          throw new Error(`Failed to fetch preference configuration: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
          // Set data from API
          if (data.categories) setCategories(data.categories);
          if (data.fields) setFields(data.fields);
          if (data.options) setOptions(data.options);
          if (data.importanceSettings) setImportanceSettings(data.importanceSettings);
          if (data.defaultPreferences) setDefaultPreferences(data.defaultPreferences);
        } else {
          // If API returns error, use mock data
          console.error('API returned error:', data.message);
          setMockData();
        }

        setLoading(false);
      } catch (err) {
        console.error('Error fetching preference configuration:', err);
        setError('Failed to load preference configuration. Please try again.');
        // Fallback to mock data
        setMockData();
        setLoading(false);
      }
    };

    fetchPreferenceData();
  }, []);

  const setMockData = () => {
    // Mock categories
    setCategories([
      {
        id: 'cat1',
        name: 'physical_attributes',
        displayName: 'Physical Attributes',
        description: 'Physical characteristics preferences',
        displayOrder: 1,
        icon: 'person',
        isActive: true,
        isRequired: true
      },
      {
        id: 'cat2',
        name: 'education_career',
        displayName: 'Education & Career',
        description: 'Education and career preferences',
        displayOrder: 2,
        icon: 'school',
        isActive: true,
        isRequired: false
      },
      {
        id: 'cat3',
        name: 'lifestyle',
        displayName: 'Lifestyle',
        description: 'Lifestyle and habits preferences',
        displayOrder: 3,
        icon: 'restaurant',
        isActive: true,
        isRequired: false
      },
      {
        id: 'cat4',
        name: 'community',
        displayName: 'Community',
        description: 'Community and cultural preferences',
        displayOrder: 4,
        icon: 'groups',
        isActive: true,
        isRequired: true
      }
    ]);

    // Mock fields
    setFields([
      {
        id: 'field1',
        name: 'age_range',
        displayName: 'Age Range',
        description: 'Preferred age range of partner',
        fieldType: 'RANGE',
        displayOrder: 1,
        isActive: true,
        isRequired: true,
        isSearchable: true,
        isMatchCriteria: true,
        defaultValue: '{"min": 21, "max": 35}',
        validationRules: '{"minValue": 18, "maxValue": 70}',
        minValue: 18,
        maxValue: 70,
        stepValue: 1,
        categoryId: 'cat1'
      },
      {
        id: 'field2',
        name: 'height_range',
        displayName: 'Height Range',
        description: 'Preferred height range of partner',
        fieldType: 'RANGE',
        displayOrder: 2,
        isActive: true,
        isRequired: true,
        isSearchable: true,
        isMatchCriteria: true,
        defaultValue: '{"min": "5\'0\\"", "max": "6\'0\\""}',
        validationRules: '{"minValue": "4\'5\\"", "maxValue": "6\'6\\""}',
        minValue: 4.5,
        maxValue: 6.5,
        stepValue: 0.1,
        categoryId: 'cat1'
      },
      {
        id: 'field3',
        name: 'education_level',
        displayName: 'Education Level',
        description: 'Preferred education level of partner',
        fieldType: 'MULTI_SELECT',
        displayOrder: 1,
        isActive: true,
        isRequired: false,
        isSearchable: true,
        isMatchCriteria: true,
        defaultValue: '[]',
        categoryId: 'cat2'
      },
      {
        id: 'field4',
        name: 'diet_preference',
        displayName: 'Diet Preference',
        description: 'Preferred diet of partner',
        fieldType: 'SELECT',
        displayOrder: 1,
        isActive: true,
        isRequired: false,
        isSearchable: true,
        isMatchCriteria: true,
        defaultValue: '"DOESNT_MATTER"',
        categoryId: 'cat3'
      },
      {
        id: 'field5',
        name: 'accept_sub_castes',
        displayName: 'Accept Sub-castes',
        description: 'Sub-castes willing to accept',
        fieldType: 'MULTI_SELECT',
        displayOrder: 1,
        isActive: true,
        isRequired: true,
        isSearchable: true,
        isMatchCriteria: true,
        defaultValue: '[]',
        categoryId: 'cat4'
      },
      {
        id: 'field6',
        name: 'gotra_preference',
        displayName: 'Gotra Preference',
        description: 'Preferred gotra of partner',
        fieldType: 'SELECT',
        displayOrder: 2,
        isActive: true,
        isRequired: false,
        isSearchable: true,
        isMatchCriteria: true,
        defaultValue: '"DOESNT_MATTER"',
        categoryId: 'cat4'
      }
    ]);

    // Mock options for select fields
    setOptions([
      {
        id: 'opt1',
        value: 'GRADUATE',
        displayText: 'Graduate',
        description: 'Bachelor\'s degree',
        displayOrder: 1,
        isActive: true,
        fieldId: 'field3'
      },
      {
        id: 'opt2',
        value: 'POST_GRADUATE',
        displayText: 'Post Graduate',
        description: 'Master\'s degree',
        displayOrder: 2,
        isActive: true,
        fieldId: 'field3'
      },
      {
        id: 'opt3',
        value: 'DOCTORATE',
        displayText: 'Doctorate',
        description: 'PhD or equivalent',
        displayOrder: 3,
        isActive: true,
        fieldId: 'field3'
      },
      {
        id: 'opt4',
        value: 'VEG',
        displayText: 'Vegetarian',
        description: 'Vegetarian diet',
        displayOrder: 1,
        isActive: true,
        fieldId: 'field4'
      },
      {
        id: 'opt5',
        value: 'NON_VEG',
        displayText: 'Non-Vegetarian',
        description: 'Non-vegetarian diet',
        displayOrder: 2,
        isActive: true,
        fieldId: 'field4'
      },
      {
        id: 'opt6',
        value: 'DOESNT_MATTER',
        displayText: 'Doesn\'t Matter',
        description: 'No preference',
        displayOrder: 3,
        isActive: true,
        fieldId: 'field4'
      }
    ]);

    // Mock importance settings
    setImportanceSettings([
      {
        id: 'imp1',
        importanceLevel: 8.0,
        description: 'Age is highly important for males',
        isActive: true,
        fieldId: 'field1',
        gender: 'MALE'
      },
      {
        id: 'imp2',
        importanceLevel: 7.0,
        description: 'Age is important for females',
        isActive: true,
        fieldId: 'field1',
        gender: 'FEMALE'
      },
      {
        id: 'imp3',
        importanceLevel: 6.0,
        description: 'Height is moderately important for males',
        isActive: true,
        fieldId: 'field2',
        gender: 'MALE'
      },
      {
        id: 'imp4',
        importanceLevel: 8.0,
        description: 'Height is highly important for females',
        isActive: true,
        fieldId: 'field2',
        gender: 'FEMALE'
      },
      {
        id: 'imp5',
        importanceLevel: 9.0,
        description: 'Caste is very important for all',
        isActive: true,
        fieldId: 'field5',
        gender: 'ALL'
      }
    ]);

    // Mock default preferences
    setDefaultPreferences({
      ageMin: 21,
      ageMax: 35,
      heightMin: "5'0\"",
      heightMax: "6'0\"",
      educationLevel: ["GRADUATE", "POST_GRADUATE"],
      dietPreference: "DOESNT_MATTER",
      acceptSubCastes: ["KUNBI", "96_KULI_MARATHA"],
      gotraPreference: "DOESNT_MATTER"
    });
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleCloseSnackbar = () => {
    setSuccess(false);
    setError(null);
  };

  const showSuccessMessage = (message) => {
    setSuccessMessage(message);
    setSuccess(true);
  };

  // Functions to update data via API
  const updateCategories = async (updatedCategories) => {
    try {
      const response = await fetch('/api/admin/preference-config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'categories',
          data: updatedCategories
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update categories: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setCategories(data.data || updatedCategories);
        showSuccessMessage('Categories updated successfully');
      } else {
        setError(data.message || 'Failed to update categories');
      }
    } catch (err) {
      console.error('Error updating categories:', err);
      setError('Failed to update categories. Please try again.');
    }
  };

  // Function to delete a category
  const deleteCategory = async (categoryId) => {
    try {
      const response = await fetch(`/api/admin/preference-config?type=category&id=${categoryId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete category: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Remove the category from the state
        setCategories(categories.filter(cat => cat.id !== categoryId));
        showSuccessMessage('Category deleted successfully');
      } else {
        setError(data.message || 'Failed to delete category');
      }
    } catch (err) {
      console.error('Error deleting category:', err);
      setError('Failed to delete category. Please try again.');
    }
  };

  const updateFields = async (updatedFields) => {
    try {
      const response = await fetch('/api/admin/preference-config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'fields',
          data: updatedFields
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update fields: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setFields(data.data || updatedFields);
        showSuccessMessage('Fields updated successfully');
      } else {
        setError(data.message || 'Failed to update fields');
      }
    } catch (err) {
      console.error('Error updating fields:', err);
      setError('Failed to update fields. Please try again.');
    }
  };

  // Function to delete a field
  const deleteField = async (fieldId) => {
    try {
      const response = await fetch(`/api/admin/preference-config?type=field&id=${fieldId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete field: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Remove the field from the state
        setFields(fields.filter(field => field.id !== fieldId));
        // Also remove any options associated with this field
        setOptions(options.filter(option => option.fieldId !== fieldId));
        // And remove any importance settings associated with this field
        setImportanceSettings(importanceSettings.filter(imp => imp.fieldId !== fieldId));
        showSuccessMessage('Field deleted successfully');
      } else {
        setError(data.message || 'Failed to delete field');
      }
    } catch (err) {
      console.error('Error deleting field:', err);
      setError('Failed to delete field. Please try again.');
    }
  };

  const updateOptions = async (updatedOptions) => {
    try {
      const response = await fetch('/api/admin/preference-config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'options',
          data: updatedOptions
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update options: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setOptions(data.data || updatedOptions);
        showSuccessMessage('Options updated successfully');
      } else {
        setError(data.message || 'Failed to update options');
      }
    } catch (err) {
      console.error('Error updating options:', err);
      setError('Failed to update options. Please try again.');
    }
  };

  // Function to delete an option
  const deleteOption = async (optionId) => {
    try {
      const response = await fetch(`/api/admin/preference-config?type=option&id=${optionId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete option: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Remove the option from the state
        setOptions(options.filter(option => option.id !== optionId));
        showSuccessMessage('Option deleted successfully');
      } else {
        setError(data.message || 'Failed to delete option');
      }
    } catch (err) {
      console.error('Error deleting option:', err);
      setError('Failed to delete option. Please try again.');
    }
  };

  const updateImportanceSettings = async (updatedImportanceSettings) => {
    try {
      const response = await fetch('/api/admin/preference-config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'importance',
          data: updatedImportanceSettings
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update importance settings: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setImportanceSettings(data.data || updatedImportanceSettings);
        showSuccessMessage('Importance settings updated successfully');
      } else {
        setError(data.message || 'Failed to update importance settings');
      }
    } catch (err) {
      console.error('Error updating importance settings:', err);
      setError('Failed to update importance settings. Please try again.');
    }
  };

  // Function to delete an importance setting
  const deleteImportanceSetting = async (importanceId) => {
    try {
      const response = await fetch(`/api/admin/preference-config?type=importance&id=${importanceId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete importance setting: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Remove the importance setting from the state
        setImportanceSettings(importanceSettings.filter(imp => imp.id !== importanceId));
        showSuccessMessage('Importance setting deleted successfully');
      } else {
        setError(data.message || 'Failed to delete importance setting');
      }
    } catch (err) {
      console.error('Error deleting importance setting:', err);
      setError('Failed to delete importance setting. Please try again.');
    }
  };

  const updateDefaultPreferences = async (updatedDefaultPreferences) => {
    try {
      const response = await fetch('/api/admin/preference-config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'defaults',
          data: updatedDefaultPreferences
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update default preferences: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setDefaultPreferences(updatedDefaultPreferences);
        showSuccessMessage('Default preferences updated successfully');
      } else {
        setError(data.message || 'Failed to update default preferences');
      }
    } catch (err) {
      console.error('Error updating default preferences:', err);
      setError('Failed to update default preferences. Please try again.');
    }
  };

  return (
    <EnhancedAdminLayout title="Preference Configuration">
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Preference Configuration
        </Typography>

        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab icon={<CategoryIcon />} label="Categories" />
            <Tab icon={<ListAltIcon />} label="Fields" />
            <Tab icon={<TuneIcon />} label="Importance Settings" />
            <Tab icon={<SettingsIcon />} label="Default Values" />
          </Tabs>
        </Paper>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            {/* Categories Tab */}
            {activeTab === 0 && (
              <CategoriesTab
                categories={categories}
                onUpdate={updateCategories}
                onDelete={deleteCategory}
              />
            )}

            {/* Fields Tab */}
            {activeTab === 1 && (
              <FieldsTab
                fields={fields}
                categories={categories}
                options={options}
                onUpdate={updateFields}
                onUpdateOptions={updateOptions}
                onDeleteField={deleteField}
                onDeleteOption={deleteOption}
              />
            )}

            {/* Importance Settings Tab */}
            {activeTab === 2 && (
              <ImportanceTab
                importanceSettings={importanceSettings}
                fields={fields}
                categories={categories}
                onUpdate={updateImportanceSettings}
                onDelete={deleteImportanceSetting}
              />
            )}

            {/* Default Values Tab */}
            {activeTab === 3 && (
              <DefaultsTab
                defaultPreferences={defaultPreferences}
                fields={fields}
                categories={categories}
                options={options}
                onUpdate={updateDefaultPreferences}
              />
            )}
          </>
        )}

        <Snackbar
          open={success}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          message={successMessage}
        />

        <Snackbar
          open={!!error}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
        >
          <Alert onClose={handleCloseSnackbar} severity="error">
            {error}
          </Alert>
        </Snackbar>
      </Box>
    </EnhancedAdminLayout>
  );
}

