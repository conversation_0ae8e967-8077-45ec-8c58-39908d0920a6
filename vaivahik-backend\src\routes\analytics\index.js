const express = require('express');
const router = express.Router();

// Import analytics routes
const trackInteractionRouter = require('./track-interaction');
const userFeedbackRouter = require('./user-feedback');
const successStoriesRouter = require('./success-stories');
const preferenceHistoryRouter = require('./preference-history');
const matchScoresRouter = require('./match-scores');

// Register routes
router.use('/track-interaction', trackInteractionRouter);
router.use('/feedback', userFeedbackRouter);
router.use('/success-stories', successStoriesRouter);
router.use('/preference-history', preferenceHistoryRouter);
router.use('/match-scores', matchScoresRouter);

module.exports = router;
