import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  CircularProgress
} from '@mui/material';
import { ToastContainer } from 'react-toastify';
import ModerationSettings from '@/components/admin/textModeration/ModerationSettingsFixed';
import BannedWordsList from '@/components/admin/textModeration/BannedWordsListFixed';
import FlaggedMessages from '@/components/admin/textModeration/FlaggedMessages';
import ModerationStats from '@/components/admin/textModeration/ModerationStats';
import ModerationTester from '@/components/admin/textModeration/ModerationTester';
import axiosInstance from '@/utils/axiosConfig';

export default function TextModerationPage() {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [moderationData, setModerationData] = useState(null);

  useEffect(() => {
    fetchModerationData();
  }, []);

  const fetchModerationData = async () => {
    try {
      setLoading(true);

      // Fetch moderation statistics and settings
      const response = await axiosInstance.get('/api/admin/text-moderation/stats');

      if (response.data && response.data.success) {
        setModerationData(response.data.data);
      } else {
        // Fallback to mock data if API fails
        setModerationData({
          totalMessages: 15420,
          flaggedMessages: 234,
          autoModerated: 156,
          manualReviews: 78,
          bannedWords: 45,
          moderationRate: 1.5
        });
      }
    } catch (error) {
      console.error('Error fetching moderation data:', error);
      // Set mock data on error
      setModerationData({
        totalMessages: 15420,
        flaggedMessages: 234,
        autoModerated: 156,
        manualReviews: 78,
        bannedWords: 45,
        moderationRate: 1.5
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  if (loading) {
    return (
      <EnhancedAdminLayout title="Text Moderation">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
          <CircularProgress />
        </Box>
      </EnhancedAdminLayout>
    );
  }

  if (error) {
    return (
      <EnhancedAdminLayout title="Text Moderation">
        <Box sx={{ p: 3 }}>
          <Typography variant="h5" color="error" gutterBottom>
            Error Loading Text Moderation
          </Typography>
          <Typography>{error}</Typography>
        </Box>
      </EnhancedAdminLayout>
    );
  }

  return (
    <EnhancedAdminLayout title="Text Moderation">
      <ToastContainer position="top-right" autoClose={5000} />
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Text Moderation
        </Typography>

        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab label="Settings" />
            <Tab label="Banned Words" />
            <Tab label="Flagged Messages" />
            <Tab label="Statistics" />
            <Tab label="Test Moderation" />
          </Tabs>
        </Paper>

        {activeTab === 0 && <ModerationSettings />}
        {activeTab === 1 && <BannedWordsList />}
        {activeTab === 2 && <FlaggedMessages />}
        {activeTab === 3 && <ModerationStats />}
        {activeTab === 4 && <ModerationTester />}
      </Box>
    </EnhancedAdminLayout>
  );
}

