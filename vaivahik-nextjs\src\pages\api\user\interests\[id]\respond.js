import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Verify JWT token
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ success: false, message: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const userId = decoded.userId;

    const { id } = req.query;
    const { action, message } = req.body;

    if (!action || !['accept', 'reject'].includes(action)) {
      return res.status(400).json({
        success: false,
        message: 'Valid action (accept/reject) is required'
      });
    }

    // Find the interest
    const interest = await prisma.interest.findUnique({
      where: { id: id },
      include: {
        user: {
          include: {
            profile: true
          }
        }
      }
    });

    if (!interest) {
      return res.status(404).json({
        success: false,
        message: 'Interest not found'
      });
    }

    // Verify that the current user is the target of this interest
    if (interest.targetUserId !== userId) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to respond to this interest'
      });
    }

    // Check if already responded
    if (interest.status !== 'PENDING') {
      return res.status(400).json({
        success: false,
        message: 'Interest has already been responded to'
      });
    }

    // Update the interest status
    const updatedInterest = await prisma.interest.update({
      where: { id: id },
      data: {
        status: action === 'accept' ? 'ACCEPTED' : 'REJECTED',
        responseMessage: message,
        respondedAt: new Date()
      }
    });

    // Create notification for the sender
    const notificationTitle = action === 'accept' 
      ? 'Interest Accepted!' 
      : 'Interest Response';
    
    const notificationMessage = action === 'accept'
      ? 'Your interest has been accepted. You can now contact each other!'
      : 'Your interest has been declined.';

    await prisma.notification.create({
      data: {
        userId: interest.userId,
        type: action === 'accept' ? 'INTEREST_ACCEPTED' : 'INTEREST_REJECTED',
        title: notificationTitle,
        message: notificationMessage,
        data: JSON.stringify({
          interestId: interest.id,
          fromUserId: userId,
          responseMessage: message
        })
      }
    });

    // If accepted, create mutual contact access
    if (action === 'accept') {
      // Create contact access for both users
      await prisma.contactAccess.createMany({
        data: [
          {
            userId: interest.userId,
            targetUserId: userId,
            status: 'GRANTED',
            grantedAt: new Date(),
            reason: 'INTEREST_ACCEPTED'
          },
          {
            userId: userId,
            targetUserId: interest.userId,
            status: 'GRANTED',
            grantedAt: new Date(),
            reason: 'INTEREST_ACCEPTED'
          }
        ],
        skipDuplicates: true
      });

      // Track successful match
      await prisma.userInteraction.create({
        data: {
          userId: userId,
          targetUserId: interest.userId,
          interactionType: 'INTEREST_ACCEPTED',
          metadata: JSON.stringify({
            interestId: interest.id,
            responseMessage: message
          })
        }
      });
    } else {
      // Track rejection
      await prisma.userInteraction.create({
        data: {
          userId: userId,
          targetUserId: interest.userId,
          interactionType: 'INTEREST_REJECTED',
          metadata: JSON.stringify({
            interestId: interest.id,
            responseMessage: message
          })
        }
      });
    }

    return res.status(200).json({
      success: true,
      message: `Interest ${action}ed successfully`,
      data: {
        interestId: updatedInterest.id,
        status: updatedInterest.status,
        responseMessage: updatedInterest.responseMessage,
        respondedAt: updatedInterest.respondedAt
      }
    });

  } catch (error) {
    console.error('Error responding to interest:', error);
    return res.status(500).json({
      success: false,
      message: 'Error responding to interest',
      error: error.message
    });
  }
}
