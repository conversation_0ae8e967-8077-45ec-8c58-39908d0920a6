/**
 * Validation Middleware
 * 
 * This middleware provides centralized validation for request data.
 * It uses express-validator to validate and sanitize input data.
 */

const { validationResult } = require('express-validator');
const apiResponse = require('../utils/apiResponse');

/**
 * Process validation results and return errors if any
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 * @returns {object|void} - Express response or void
 */
const validate = (req, res, next) => {
  const errors = validationResult(req);
  
  if (errors.isEmpty()) {
    return next();
  }
  
  // Format errors for consistent response
  const formattedErrors = {};
  
  errors.array().forEach(err => {
    const field = err.path || err.param;
    
    // Store only the first error for each field
    if (!formattedErrors[field]) {
      formattedErrors[field] = err.msg;
    }
  });
  
  // Return validation error response
  return apiResponse.validationError(res, 'Validation failed', formattedErrors);
};

module.exports = {
  validate
};
