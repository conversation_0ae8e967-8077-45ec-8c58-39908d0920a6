<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Executive Premium Biodata</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&family=Cormorant:wght@400;500;600;700&display=swap');
        
        :root {
            --primary-color: #111;
            --secondary-color: #333;
            --accent-color: #d4af37; /* Gold */
            --text-color: #333;
            --light-text: #777;
            --border-color: #d4af37;
            --light-bg: #f9f9f9;
            --header-font: 'Cinzel', serif;
            --body-font: 'Cormorant', serif;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--body-font);
            color: var(--text-color);
            line-height: 1.6;
            background-color: white;
            padding: 0;
            margin: 0;
            font-size: 16px;
        }
        
        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 30px;
            background-color: black;
            color: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.3);
            position: relative;
            background-image: url('data:image/svg+xml;utf8,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect width="20" height="20" fill="none" stroke="%23d4af37" stroke-width="0.5" opacity="0.1"/></svg>');
            background-size: 20px 20px;
        }
        
        /* Gold Border */
        .gold-border {
            position: absolute;
            top: 15px;
            left: 15px;
            right: 15px;
            bottom: 15px;
            border: 1px solid var(--accent-color);
            pointer-events: none;
            z-index: 1;
        }
        
        .content-wrapper {
            position: relative;
            z-index: 2;
            padding: 20px;
        }
        
        /* Invocation */
        .invocation {
            text-align: center;
            font-family: var(--header-font);
            color: var(--accent-color);
            padding: 15px 0;
            font-weight: 600;
            font-size: 18px;
            margin-bottom: 30px;
            letter-spacing: 2px;
            border-bottom: 1px solid rgba(212, 175, 55, 0.3);
        }
        
        /* Header Section */
        .header {
            display: flex;
            align-items: center;
            margin-bottom: 40px;
            position: relative;
        }
        
        .profile-photo {
            width: 180px;
            height: 240px;
            object-fit: cover;
            border: 2px solid var(--accent-color);
            padding: 5px;
            background-color: black;
            margin-right: 40px;
        }
        
        .header-content {
            flex: 1;
        }
        
        .name {
            font-family: var(--header-font);
            font-size: 36px;
            color: var(--accent-color);
            margin-bottom: 10px;
            font-weight: 700;
            letter-spacing: 1px;
        }
        
        .tagline {
            font-size: 18px;
            color: white;
            margin-bottom: 20px;
            font-style: italic;
            letter-spacing: 0.5px;
        }
        
        .quick-info {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 20px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            font-size: 16px;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 8px 15px;
            border: 1px solid rgba(212, 175, 55, 0.3);
        }
        
        .info-label {
            font-weight: 600;
            margin-right: 8px;
            color: var(--accent-color);
        }
        
        /* Section Styling */
        .section {
            margin-bottom: 35px;
            position: relative;
        }
        
        .section-title {
            font-family: var(--header-font);
            color: var(--accent-color);
            font-size: 24px;
            padding-bottom: 10px;
            margin-bottom: 20px;
            border-bottom: 1px solid rgba(212, 175, 55, 0.3);
            position: relative;
            letter-spacing: 1px;
        }
        
        .section-title:after {
            content: '';
            position: absolute;
            left: 0;
            bottom: -1px;
            width: 60px;
            height: 2px;
            background-color: var(--accent-color);
        }
        
        .section-content {
            padding: 0 10px;
        }
        
        /* Two Column Layout */
        .two-column {
            display: flex;
            gap: 40px;
            margin-bottom: 35px;
        }
        
        .column {
            flex: 1;
        }
        
        /* Details Table */
        .details-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .details-table tr {
            border-bottom: 1px solid rgba(212, 175, 55, 0.2);
        }
        
        .details-table tr:last-child {
            border-bottom: none;
        }
        
        .details-table td {
            padding: 12px 5px;
            vertical-align: top;
            font-size: 17px;
        }
        
        .details-table td:first-child {
            width: 40%;
            font-weight: 600;
            color: var(--accent-color);
        }
        
        /* Premium Card */
        .premium-card {
            background-color: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(212, 175, 55, 0.3);
            padding: 25px;
            margin-bottom: 30px;
            position: relative;
        }
        
        .premium-card:before {
            content: '';
            position: absolute;
            top: 5px;
            left: 5px;
            right: 5px;
            bottom: 5px;
            border: 1px solid rgba(212, 175, 55, 0.2);
            pointer-events: none;
        }
        
        .card-title {
            font-family: var(--header-font);
            color: var(--accent-color);
            font-size: 20px;
            margin-bottom: 15px;
            font-weight: 600;
            letter-spacing: 1px;
        }
        
        /* Photo Gallery */
        .photo-gallery {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        
        .gallery-photo {
            width: calc(33.33% - 10px);
            height: 150px;
            object-fit: cover;
            border: 1px solid var(--accent-color);
        }
        
        /* Career Timeline */
        .timeline-item {
            position: relative;
            padding-left: 30px;
            margin-bottom: 25px;
            border-left: 1px solid rgba(212, 175, 55, 0.5);
        }
        
        .timeline-item:before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            width: 10px;
            height: 10px;
            background-color: var(--accent-color);
            border-radius: 50%;
        }
        
        .timeline-title {
            font-weight: 600;
            color: var(--accent-color);
            margin-bottom: 5px;
            font-size: 18px;
        }
        
        .timeline-subtitle {
            font-size: 16px;
            color: white;
            margin-bottom: 5px;
        }
        
        .timeline-content {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        /* Expectations */
        .expectations {
            background-color: rgba(0, 0, 0, 0.5);
            padding: 25px;
            border: 1px solid rgba(212, 175, 55, 0.3);
            position: relative;
        }
        
        .expectations:before {
            content: '';
            position: absolute;
            top: 5px;
            left: 5px;
            right: 5px;
            bottom: 5px;
            border: 1px solid rgba(212, 175, 55, 0.2);
            pointer-events: none;
        }
        
        /* Footer */
        .footer {
            margin-top: 40px;
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid rgba(212, 175, 55, 0.3);
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .branding {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 15px;
        }
        
        .brand-logo {
            height: 30px;
            margin-right: 10px;
        }
        
        .brand-name {
            font-weight: 600;
            color: var(--accent-color);
            letter-spacing: 1px;
        }
        
        /* Print Styles */
        @media print {
            body {
                background-color: white;
            }
            
            .container {
                box-shadow: none;
                padding: 0;
                max-width: 100%;
            }
            
            @page {
                size: A4;
                margin: 1cm;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="gold-border"></div>
        <div class="content-wrapper">
            <!-- Invocation -->
            <div class="invocation">
                ॥ श्री गणेशाय नमः ॥
            </div>
            
            <!-- Header Section -->
            <div class="header">
                <img src="{{profilePicture}}" alt="Profile Photo" class="profile-photo">
                <div class="header-content">
                    <h1 class="name">{{name}}</h1>
                    <p class="tagline">{{tagline}}</p>
                    <div class="quick-info">
                        <div class="info-item">
                            <span class="info-label">Age:</span> {{age}} years
                        </div>
                        <div class="info-item">
                            <span class="info-label">Height:</span> {{height}}
                        </div>
                        <div class="info-item">
                            <span class="info-label">Education:</span> {{education}}
                        </div>
                        <div class="info-item">
                            <span class="info-label">Profession:</span> {{occupation}}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Photo Gallery -->
            <div class="section">
                <div class="photo-gallery">
                    <img src="{{additionalPhotos.0}}" alt="Additional Photo" class="gallery-photo">
                    <img src="{{additionalPhotos.1}}" alt="Additional Photo" class="gallery-photo">
                    <img src="{{additionalPhotos.2}}" alt="Additional Photo" class="gallery-photo">
                </div>
            </div>
            
            <!-- Two Column Layout for Personal Details and Family Background -->
            <div class="two-column">
                <!-- Personal Details -->
                <div class="column">
                    <div class="section">
                        <h2 class="section-title">Personal Details</h2>
                        <div class="section-content">
                            <div class="premium-card">
                                <table class="details-table">
                                    <tr>
                                        <td>Date of Birth</td>
                                        <td>{{dateOfBirth}}</td>
                                    </tr>
                                    <tr>
                                        <td>Birth Place</td>
                                        <td>{{birthPlace}}</td>
                                    </tr>
                                    <tr>
                                        <td>Religion</td>
                                        <td>{{religion}}</td>
                                    </tr>
                                    <tr>
                                        <td>Caste</td>
                                        <td>{{caste}}</td>
                                    </tr>
                                    <tr>
                                        <td>Sub-caste</td>
                                        <td>{{subCaste}}</td>
                                    </tr>
                                    <tr>
                                        <td>Gotra</td>
                                        <td>{{gotra}}</td>
                                    </tr>
                                    <tr>
                                        <td>Marital Status</td>
                                        <td>{{maritalStatus}}</td>
                                    </tr>
                                    <tr>
                                        <td>Diet</td>
                                        <td>{{diet}}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Family Background -->
                <div class="column">
                    <div class="section">
                        <h2 class="section-title">Family Background</h2>
                        <div class="section-content">
                            <div class="premium-card">
                                <table class="details-table">
                                    <tr>
                                        <td>Father's Name</td>
                                        <td>{{fatherName}}</td>
                                    </tr>
                                    <tr>
                                        <td>Father's Occupation</td>
                                        <td>{{fatherOccupation}}</td>
                                    </tr>
                                    <tr>
                                        <td>Mother's Name</td>
                                        <td>{{motherName}}</td>
                                    </tr>
                                    <tr>
                                        <td>Mother's Occupation</td>
                                        <td>{{motherOccupation}}</td>
                                    </tr>
                                    <tr>
                                        <td>Family Type</td>
                                        <td>{{familyType}}</td>
                                    </tr>
                                    <tr>
                                        <td>Family Status</td>
                                        <td>{{familyStatus}}</td>
                                    </tr>
                                    <tr>
                                        <td>Siblings</td>
                                        <td>{{siblings}}</td>
                                    </tr>
                                    <tr>
                                        <td>Native Place</td>
                                        <td>{{nativePlace}}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Education & Career -->
            <div class="section">
                <h2 class="section-title">Education & Career</h2>
                <div class="section-content">
                    <div class="premium-card">
                        <h3 class="card-title">Professional Journey</h3>
                        <div class="timeline-item">
                            <div class="timeline-title">{{education}}</div>
                            <div class="timeline-subtitle">{{educationDetails}}</div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-title">{{occupation}}</div>
                            <div class="timeline-subtitle">{{company}}</div>
                            <div class="timeline-content">{{occupationDetails}}</div>
                        </div>
                        
                        <table class="details-table" style="margin-top: 20px;">
                            <tr>
                                <td>Annual Income</td>
                                <td>{{annualIncome}}</td>
                            </tr>
                            <tr>
                                <td>Assets</td>
                                <td>{{assets}}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Two Column Layout for About Me and Expectations -->
            <div class="two-column">
                <!-- About Me -->
                <div class="column">
                    <div class="section">
                        <h2 class="section-title">About Me</h2>
                        <div class="section-content">
                            <div class="premium-card">
                                <p style="font-size: 17px;">{{aboutMe}}</p>
                                
                                <div style="margin-top: 20px;">
                                    <div style="font-weight: 600; color: var(--accent-color); margin-bottom: 10px; font-size: 18px;">Lifestyle & Interests</div>
                                    <p style="font-size: 17px;">{{hobbies}}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Partner Expectations -->
                <div class="column">
                    <div class="section">
                        <h2 class="section-title">Partner Expectations</h2>
                        <div class="section-content">
                            <div class="expectations">
                                <p style="font-size: 17px;">{{partnerPreferences}}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="section">
                <h2 class="section-title">Contact Information</h2>
                <div class="section-content">
                    <div class="premium-card">
                        <table class="details-table">
                            <tr>
                                <td>Current Location</td>
                                <td>{{city}}, {{state}}, {{country}}</td>
                            </tr>
                            <tr>
                                <td>Email</td>
                                <td>{{email}}</td>
                            </tr>
                            <tr>
                                <td>Phone</td>
                                <td>{{phone}}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Footer with Branding -->
            <div class="footer">
                <div class="branding">
                    <img src="{{brandLogo}}" alt="Brand Logo" class="brand-logo">
                    <span class="brand-name">{{brandName}}</span>
                </div>
                <p>{{brandTagline}}</p>
                <p>Created on {{createdAt}}</p>
            </div>
        </div>
    </div>
</body>
</html>
