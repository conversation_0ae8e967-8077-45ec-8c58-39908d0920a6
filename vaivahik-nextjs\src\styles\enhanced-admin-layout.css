/* Enhanced Admin Layout Styles */

/* Base Styles */
:root {
  --primary: #6a1b9a;
  --primary-light: #9c4dcc;
  --primary-dark: #38006b;
  --secondary: #ff5722;
  --secondary-light: #ff8a50;
  --secondary-dark: #c41c00;
  --success: #4caf50;
  --danger: #f44336;
  --warning: #ff9800;
  --info: #2196f3;
  --text-dark: #333;
  --text-light: #f5f5f5;
  --bg-light: #f9f9f9;
  --bg-dark: #121212;
  --border-color: #e0e0e0;
  --sidebar-width: 260px;
  --sidebar-collapsed-width: 70px;
  --topbar-height: 60px;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
  --transition-speed: 0.3s;
}

/* Admin Container */
.admin-container {
  display: flex;
  min-height: 100vh;
  background-color: var(--bg-light);
}

/* Sidebar */
.sidebar {
  width: var(--sidebar-width);
  height: 100vh;
  background-color: var(--primary);
  color: var(--text-light);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  transition: width var(--transition-speed) ease;
  overflow-y: auto;
  box-shadow: var(--shadow-md);
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border-bottom: 1px solid var(--primary-dark);
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--text-light);
  text-decoration: none;
  font-weight: 600;
  font-size: 1.2rem;
}

.sidebar-logo div {
  width: 40px;
  height: 40px;
  background-color: var(--text-light);
  color: var(--primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.5rem;
}

.sidebar.collapsed .sidebar-logo span {
  display: none;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: var(--text-light);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color var(--transition-speed) ease;
}

.sidebar-toggle:hover {
  background-color: var(--primary-dark);
}

.sidebar-user {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  border-bottom: 1px solid var(--primary-dark);
}

.sidebar.collapsed .sidebar-user .user-info {
  display: none;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background-color: var(--primary-light);
  color: var(--text-light);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
}

.user-role {
  font-size: 0.8rem;
  opacity: 0.8;
}

.sidebar-nav {
  padding: 15px 0;
}

.nav-category {
  padding: 10px 15px;
  font-size: 0.8rem;
  text-transform: uppercase;
  color: var(--text-light);
  opacity: 0.7;
  margin-top: 10px;
}

.sidebar.collapsed .nav-category {
  text-align: center;
  font-size: 0.6rem;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 15px;
  color: var(--text-light);
  text-decoration: none;
  transition: background-color var(--transition-speed) ease;
  border-radius: 4px;
  margin: 2px 10px;
  cursor: pointer;
  border: none;
  background: none;
  width: calc(100% - 20px);
  text-align: left;
}

.nav-item:hover {
  background-color: var(--primary-light);
}

.nav-item.active {
  background-color: var(--primary-light);
  font-weight: 600;
}

.nav-icon {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
}

.sidebar.collapsed .nav-text {
  display: none;
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  transition: margin-left var(--transition-speed) ease, width var(--transition-speed) ease;
  width: calc(100% - var(--sidebar-width));
  min-height: 100vh;
  background-color: var(--bg-light);
}

.main-content.collapsed {
  margin-left: var(--sidebar-collapsed-width);
  width: calc(100% - var(--sidebar-collapsed-width));
}

/* Topbar */
.topbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: var(--topbar-height);
  background-color: white;
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 900;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--text-dark);
  font-size: 0.9rem;
}

.breadcrumb a {
  color: var(--primary);
  text-decoration: none;
}

.topbar-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.dark-mode-toggle {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 5px;
  border-radius: 4px;
  transition: background-color var(--transition-speed) ease;
}

.dark-mode-toggle:hover {
  background-color: var(--bg-light);
}

.notifications {
  position: relative;
}

.notification-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 5px;
  border-radius: 4px;
  transition: background-color var(--transition-speed) ease;
}

.notification-btn:hover {
  background-color: var(--bg-light);
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--danger);
  color: white;
  font-size: 0.7rem;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Dark Mode */
body.dark-mode {
  background-color: var(--bg-dark);
  color: var(--text-light);
}

body.dark-mode .main-content {
  background-color: var(--bg-dark);
}

body.dark-mode .topbar {
  background-color: #1e1e1e;
  color: var(--text-light);
}

body.dark-mode .breadcrumb {
  color: var(--text-light);
}

body.dark-mode .dark-mode-toggle:hover,
body.dark-mode .notification-btn:hover {
  background-color: #2a2a2a;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    width: var(--sidebar-collapsed-width);
  }
  
  .sidebar .sidebar-logo span,
  .sidebar .user-info,
  .sidebar .nav-text {
    display: none;
  }
  
  .main-content {
    margin-left: var(--sidebar-collapsed-width);
    width: calc(100% - var(--sidebar-collapsed-width));
  }
}
