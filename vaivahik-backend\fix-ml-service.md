# ML Service Startup Error Fix

## Problem Analysis

The ML service startup error occurs because:

1. **Python Dependencies Missing**: Flask and other required packages not installed
2. **Python Environment Issues**: Python path and module imports
3. **Port Conflicts**: ML service trying to use port 5000 which might be occupied
4. **Model Initialization**: PyTorch model taking time to initialize
5. **Debug Mode Issues**: Flask debug mode causing restart loops

## Root Causes

### 1. Missing Python Dependencies
Your `requirements.txt` only has PyTorch dependencies but missing Flask:
```
torch>=1.7.0
torchvision>=0.8.1
Pillow>=8.0.0
numpy>=1.19.0
facenet-pytorch>=2.5.0
```

**Missing**: Flask, Flask-CORS, pandas, scikit-learn, etc.

### 2. Debug Mode Restart Loop
The Flask app is running in debug mode which causes automatic restarts:
```python
debug_mode = os.getenv('NODE_ENV', 'development') != 'production'
app.run(host='0.0.0.0', port=5000, debug=debug_mode)
```

### 3. Model Initialization Timeout
The ML service takes time to build the PyTorch model, causing timeout.

## Solutions

### Solution 1: Update Python Dependencies

Create a complete `requirements.txt`:

```txt
# Core ML Dependencies
torch>=1.7.0
torchvision>=0.8.1
numpy>=1.19.0
pandas>=1.3.0
scikit-learn>=1.0.0

# Web Framework
Flask>=2.0.0
Flask-CORS>=3.0.0
flask-restful>=0.3.9

# Image Processing
Pillow>=8.0.0
facenet-pytorch>=2.5.0

# Data Processing
scipy>=1.7.0
matplotlib>=3.3.0

# Utilities
python-dotenv>=0.19.0
requests>=2.25.0
```

### Solution 2: Fix Flask App Configuration

Update `src/api/matching_api.py`:

```python
if __name__ == '__main__':
    # Initialize model
    initialize_model()
    
    # Run the app with proper configuration
    import os
    
    # Disable debug mode to prevent restart loops
    debug_mode = False  # Always False for production stability
    
    # Use different port if 5000 is occupied
    port = int(os.getenv('ML_SERVICE_PORT', 5000))
    
    print(f"Starting ML Service on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug_mode, threaded=True)
```

### Solution 3: Improve Server Startup Logic

Update `server.js` ML service startup:

```javascript
const startMLService = async () => {
    try {
        logger.info('🚀 Starting Python ML Service...');

        // Check if Python is available
        try {
            const pythonCheck = spawn('python', ['--version'], { stdio: 'pipe' });
            await new Promise((resolve, reject) => {
                pythonCheck.on('close', (code) => {
                    if (code !== 0) reject(new Error('Python not found'));
                    else resolve();
                });
                pythonCheck.on('error', reject);
            });
        } catch (error) {
            logger.warn('Python not found, ML service will be unavailable');
            return false;
        }

        // Start ML service with better error handling
        mlServiceProcess = spawn('python', ['src/api/matching_api.py'], {
            cwd: __dirname,
            stdio: ['pipe', 'pipe', 'pipe'],
            env: { ...process.env, ML_SERVICE_PORT: '5000' }
        });

        let mlServiceReady = false;
        let startupTimeout;

        // Set startup timeout
        startupTimeout = setTimeout(() => {
            logger.warn('⚠️ ML Service startup timeout - continuing anyway');
            mlServiceReady = true;
        }, 45000); // Increased to 45 seconds

        mlServiceProcess.stdout.on('data', (data) => {
            const output = data.toString().trim();
            if (output && !output.includes('Restarting') && !output.includes('Debugger')) {
                logger.info(`ML Service: ${output}`);

                // Check if service is ready
                if (output.includes('Running on') || output.includes('Built new model')) {
                    mlServiceReady = true;
                    clearTimeout(startupTimeout);
                }
            }
        });

        mlServiceProcess.stderr.on('data', (data) => {
            const error = data.toString().trim();
            // Filter out Flask debug messages
            if (error && 
                !error.includes('WARNING') && 
                !error.includes('Tip:') &&
                !error.includes('Restarting') &&
                !error.includes('Debugger') &&
                !error.includes('CTRL+C')) {
                logger.error(`ML Service Error: ${error}`);
            }
        });

        mlServiceProcess.on('close', (code) => {
            clearTimeout(startupTimeout);
            if (code !== 0 && code !== null) {
                logger.error(`ML Service exited with code ${code}`);
            } else {
                logger.info('ML Service stopped gracefully');
            }
            mlServiceProcess = null;
        });

        mlServiceProcess.on('error', (error) => {
            clearTimeout(startupTimeout);
            logger.error(`Failed to start ML Service: ${error.message}`);
            mlServiceProcess = null;
        });

        // Wait for ML service to be ready
        logger.info('⏳ Waiting for ML Service to be ready...');
        for (let i = 0; i < 60; i++) { // Increased to 60 seconds
            if (mlServiceReady) {
                logger.info('✅ ML Service is ready!');
                return true;
            }

            // Try to ping the health endpoint
            try {
                const response = await axios.get('http://localhost:5000/health', { 
                    timeout: 2000,
                    validateStatus: () => true 
                });
                if (response.status === 200) {
                    logger.info('✅ ML Service health check passed!');
                    clearTimeout(startupTimeout);
                    return true;
                }
            } catch (e) {
                // Service not ready yet, continue waiting
            }

            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        logger.warn('⚠️ ML Service startup timeout - continuing anyway');
        return false;

    } catch (error) {
        logger.error(`Error starting ML Service: ${error.message}`);
        return false;
    }
};
```

### Solution 4: Alternative Lightweight ML Service

Create a simplified ML service that starts faster:

```python
# src/api/matching_api_simple.py
"""
Simplified Flask API for matrimony matching service
"""

import os
import json
from flask import Flask, request, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

# Simple in-memory model flag
model_ready = False

@app.route('/health', methods=['GET'])
def health():
    """Health check endpoint"""
    return jsonify({
        'success': True,
        'message': 'ML Matching Service is running',
        'model_initialized': model_ready
    })

@app.route('/match', methods=['POST'])
def match():
    """Simple matching endpoint with fallback logic"""
    try:
        data = request.json
        
        # Simple compatibility calculation
        user = data.get('user', {})
        preferences = data.get('preferences', {})
        potential_matches = data.get('potential_matches', [])
        
        matches = []
        for match in potential_matches:
            # Simple scoring based on basic criteria
            score = calculate_simple_score(user, match, preferences)
            matches.append({
                'userId': match.get('id'),
                'score': int(score * 100)
            })
        
        # Sort by score
        matches.sort(key=lambda x: x['score'], reverse=True)
        
        return jsonify({
            'success': True,
            'matches': matches
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error: {str(e)}'
        }), 500

def calculate_simple_score(user, match, preferences):
    """Simple scoring algorithm"""
    score = 0.5  # Base score
    
    # Age compatibility
    user_age = user.get('age', 25)
    match_age = match.get('age', 25)
    age_diff = abs(user_age - match_age)
    if age_diff <= 3:
        score += 0.2
    elif age_diff <= 5:
        score += 0.1
    
    # Education compatibility
    if user.get('education') == match.get('education'):
        score += 0.15
    
    # Location compatibility
    if user.get('city') == match.get('city'):
        score += 0.15
    
    return min(score, 1.0)

if __name__ == '__main__':
    global model_ready
    model_ready = True
    print("Simple ML Service initialized")
    
    port = int(os.getenv('ML_SERVICE_PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=False, threaded=True)
```

## Implementation Steps

### Step 1: Install Python Dependencies
```bash
cd vaivahik-backend
pip install -r requirements.txt
```

### Step 2: Update Requirements File
Replace the current `requirements.txt` with the complete version above.

### Step 3: Choose ML Service Version
- For **production**: Use the simplified version for faster startup
- For **development**: Fix the full PyTorch version

### Step 4: Update Environment Variables
Add to your `.env` file:
```env
ML_SERVICE_PORT=5000
ML_SERVICE_TIMEOUT=60
PYTHON_PATH=python
```

### Step 5: Test ML Service Independently
```bash
cd vaivahik-backend
python src/api/matching_api.py
```

## Quick Fix (Immediate Solution)

1. **Disable ML Service Temporarily**:
```javascript
// In server.js, comment out ML service startup
// await startMLService();
logger.info('ML Service disabled for now');
```

2. **Use Fallback Matching**:
Your `mlMatchingService.js` already has fallback logic when ML service is unavailable.

## Long-term Solution

1. **Containerize ML Service**: Use Docker for consistent Python environment
2. **Separate ML Service**: Run ML service as independent microservice
3. **Model Caching**: Pre-build and cache models for faster startup
4. **Health Monitoring**: Add proper health checks and auto-restart

## Testing

After implementing fixes:

1. **Test ML Service Health**:
```bash
curl http://localhost:5000/health
```

2. **Test Backend Startup**:
```bash
npm run dev
```

3. **Check Logs**: Verify no more ML service errors in logs

This comprehensive fix should resolve your ML service startup issues and provide a stable foundation for your matrimony matching system.
