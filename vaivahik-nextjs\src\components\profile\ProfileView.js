import React, { useContext } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Avatar,
  Chip,
  Divider,
  Button,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Tooltip
} from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import CakeIcon from '@mui/icons-material/Cake';
import HeightIcon from '@mui/icons-material/Height';
import SchoolIcon from '@mui/icons-material/School';
import WorkIcon from '@mui/icons-material/Work';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import PhoneIcon from '@mui/icons-material/Phone';
import EmailIcon from '@mui/icons-material/Email';
import LockIcon from '@mui/icons-material/Lock';
import VerifiedIcon from '@mui/icons-material/Verified';
import { usePrivacy } from '@/contexts/PrivacyContext';
import { AuthContext } from '@/contexts/AuthContext';
import UserOnlineStatus from '@/components/common/UserOnlineStatus';

/**
 * Profile View Component
 *
 * Displays a user profile with respect to privacy settings
 */
const ProfileView = ({ profile }) => {
  const { user } = useContext(AuthContext);
  const { canViewContent } = usePrivacy();

  // Check if the current user can view specific content
  const canView = (contentType) => {
    return canViewContent(contentType, profile);
  };

  // Render contact information based on privacy settings
  const renderContactInfo = () => {
    const canViewPhone = canView('phone');
    const canViewEmail = canView('email');

    if (!canViewPhone && !canViewEmail) {
      return (
        <Card variant="outlined" sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <LockIcon color="primary" sx={{ mr: 1 }} />
              <Typography variant="h6">Contact Information</Typography>
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
              <LockIcon fontSize="small" sx={{ mr: 1 }} />
              Contact details are visible only to premium members
            </Typography>
            <Button
              variant="contained"
              color="primary"
              sx={{ mt: 2 }}
              href="/subscription"
            >
              Upgrade to Premium
            </Button>
          </CardContent>
        </Card>
      );
    }

    return (
      <Card variant="outlined" sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Contact Information
          </Typography>
          <List dense>
            {canViewPhone ? (
              <ListItem>
                <ListItemIcon>
                  <PhoneIcon />
                </ListItemIcon>
                <ListItemText
                  primary="Phone"
                  secondary={profile.phone}
                />
              </ListItem>
            ) : (
              <ListItem>
                <ListItemIcon>
                  <LockIcon />
                </ListItemIcon>
                <ListItemText
                  primary="Phone"
                  secondary="Upgrade to premium to view"
                />
              </ListItem>
            )}

            {canViewEmail ? (
              <ListItem>
                <ListItemIcon>
                  <EmailIcon />
                </ListItemIcon>
                <ListItemText
                  primary="Email"
                  secondary={profile.email}
                />
              </ListItem>
            ) : (
              <ListItem>
                <ListItemIcon>
                  <LockIcon />
                </ListItemIcon>
                <ListItemText
                  primary="Email"
                  secondary="Upgrade to premium to view"
                />
              </ListItem>
            )}
          </List>
        </CardContent>
      </Card>
    );
  };

  // Render photos based on privacy settings
  const renderPhotos = () => {
    const canViewPhotos = canView('photo');

    if (!canViewPhotos) {
      return (
        <Box
          sx={{
            height: 300,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            bgcolor: 'grey.100',
            borderRadius: 2,
            mb: 3
          }}
        >
          <LockIcon sx={{ fontSize: 48, color: 'grey.500', mb: 2 }} />
          <Typography variant="body1" color="text.secondary" gutterBottom>
            Photos are private
          </Typography>
          <Typography variant="body2" color="text.secondary" align="center" sx={{ px: 2 }}>
            This user has chosen to share photos only with premium members
          </Typography>
          <Button
            variant="contained"
            color="primary"
            sx={{ mt: 2 }}
            href="/subscription"
          >
            Upgrade to Premium
          </Button>
        </Box>
      );
    }

    return (
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Photos
        </Typography>
        <Grid container spacing={2}>
          {profile.photos?.map((photo, index) => (
            <Grid item xs={6} sm={4} key={index}>
              <Box
                component="img"
                src={photo}
                alt={`${profile.name} - Photo ${index + 1}`}
                sx={{
                  width: '100%',
                  height: 200,
                  objectFit: 'cover',
                  borderRadius: 2
                }}
              />
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  };

  return (
    <Paper elevation={0} sx={{ p: 3, borderRadius: 2 }}>
      {/* Profile Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Avatar
          src={profile.profilePhoto}
          alt={profile.name}
          sx={{ width: 100, height: 100, mr: 3 }}
        />
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Typography variant="h5" component="h1">
              {profile.name}
            </Typography>
            {profile.isVerified && (
              <Tooltip title="Verified Profile">
                <VerifiedIcon color="primary" sx={{ ml: 1 }} />
              </Tooltip>
            )}
          </Box>
          <Typography variant="body1" color="text.secondary" gutterBottom>
            {profile.occupation} | {profile.city}, {profile.state}
          </Typography>

          {/* Online Status */}
          <Box sx={{ my: 1 }}>
            <UserOnlineStatus userId={profile.id} showLastActive={true} />
          </Box>

          <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
            <Chip
              label={`ID: ${profile.id}`}
              size="small"
              variant="outlined"
            />
            {profile.isPremium && (
              <Chip
                label="Premium"
                size="small"
                color="primary"
              />
            )}
          </Box>
        </Box>
      </Box>

      <Divider sx={{ mb: 3 }} />

      {/* Basic Information */}
      <Card variant="outlined" sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Basic Information
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <PersonIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Gender"
                    secondary={profile.gender === 'MALE' ? 'Male' : 'Female'}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CakeIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Age"
                    secondary={`${profile.age} years`}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <HeightIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Height"
                    secondary={profile.height}
                  />
                </ListItem>
              </List>
            </Grid>
            <Grid item xs={12} sm={6}>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <PersonIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Marital Status"
                    secondary={profile.maritalStatus.replace('_', ' ')}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <PersonIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Religion"
                    secondary={profile.religion}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <PersonIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Caste"
                    secondary={profile.caste}
                  />
                </ListItem>
              </List>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Contact Information */}
      {renderContactInfo()}

      {/* Education & Career */}
      {canView('career') ? (
        <Card variant="outlined" sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Education & Career
            </Typography>
            <List dense>
              <ListItem>
                <ListItemIcon>
                  <SchoolIcon />
                </ListItemIcon>
                <ListItemText
                  primary="Education"
                  secondary={`${profile.education}${profile.educationField ? ` in ${profile.educationField}` : ''}`}
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <WorkIcon />
                </ListItemIcon>
                <ListItemText
                  primary="Occupation"
                  secondary={profile.occupation}
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <WorkIcon />
                </ListItemIcon>
                <ListItemText
                  primary="Working With"
                  secondary={profile.workingWith || 'Not specified'}
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <WorkIcon />
                </ListItemIcon>
                <ListItemText
                  primary="Annual Income"
                  secondary={profile.incomeRange.replace('_', ' - ')}
                />
              </ListItem>
            </List>
          </CardContent>
        </Card>
      ) : (
        <Card variant="outlined" sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <LockIcon color="primary" sx={{ mr: 1 }} />
              <Typography variant="h6">Education & Career</Typography>
            </Box>
            <Typography variant="body2" color="text.secondary">
              Career details are visible only to premium members
            </Typography>
            <Button
              variant="contained"
              color="primary"
              sx={{ mt: 2 }}
              href="/subscription"
            >
              Upgrade to Premium
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Photos */}
      {renderPhotos()}

      {/* Action Buttons */}
      <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mt: 4 }}>
        <Button variant="contained" color="primary">
          Send Interest
        </Button>
        <Button variant="outlined">
          Shortlist
        </Button>
      </Box>
    </Paper>
  );
};

export default ProfileView;
