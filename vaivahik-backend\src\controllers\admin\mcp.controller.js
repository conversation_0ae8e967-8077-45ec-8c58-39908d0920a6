/**
 * MCP Admin Controller
 * Manages MCP server operations from admin panel
 */

const mcpServerManager = require('../../services/mcp/mcpServerManager');
const logger = require('../../utils/logger');

/**
 * Get MCP server status
 */
exports.getStatus = async (req, res, next) => {
  try {
    const status = mcpServerManager.getStatus();
    const healthCheck = await mcpServerManager.healthCheck();
    
    res.status(200).json({
      success: true,
      data: {
        ...status,
        health: healthCheck
      }
    });
  } catch (error) {
    logger.error('Error getting MCP status:', error);
    next(error);
  }
};

/**
 * Start MCP server
 */
exports.startServer = async (req, res, next) => {
  try {
    await mcpServerManager.start();
    
    res.status(200).json({
      success: true,
      message: 'MCP server started successfully'
    });
  } catch (error) {
    logger.error('Error starting MCP server:', error);
    next(error);
  }
};

/**
 * Stop MCP server
 */
exports.stopServer = async (req, res, next) => {
  try {
    await mcpServerManager.stop();
    
    res.status(200).json({
      success: true,
      message: 'MCP server stopped successfully'
    });
  } catch (error) {
    logger.error('Error stopping MCP server:', error);
    next(error);
  }
};

/**
 * Restart MCP server
 */
exports.restartServer = async (req, res, next) => {
  try {
    await mcpServerManager.restart();
    
    res.status(200).json({
      success: true,
      message: 'MCP server restarted successfully'
    });
  } catch (error) {
    logger.error('Error restarting MCP server:', error);
    next(error);
  }
};

/**
 * Get connected clients
 */
exports.getClients = async (req, res, next) => {
  try {
    const clients = mcpServerManager.getConnectedClients();
    
    res.status(200).json({
      success: true,
      data: clients
    });
  } catch (error) {
    logger.error('Error getting MCP clients:', error);
    next(error);
  }
};

/**
 * Get registered tools
 */
exports.getTools = async (req, res, next) => {
  try {
    const tools = mcpServerManager.getRegisteredTools();
    
    res.status(200).json({
      success: true,
      data: tools
    });
  } catch (error) {
    logger.error('Error getting MCP tools:', error);
    next(error);
  }
};

/**
 * Get registered resources
 */
exports.getResources = async (req, res, next) => {
  try {
    const resources = mcpServerManager.getRegisteredResources();
    
    res.status(200).json({
      success: true,
      data: resources
    });
  } catch (error) {
    logger.error('Error getting MCP resources:', error);
    next(error);
  }
};

/**
 * Get registered prompts
 */
exports.getPrompts = async (req, res, next) => {
  try {
    const prompts = mcpServerManager.getRegisteredPrompts();
    
    res.status(200).json({
      success: true,
      data: prompts
    });
  } catch (error) {
    logger.error('Error getting MCP prompts:', error);
    next(error);
  }
};

/**
 * Update MCP server configuration
 */
exports.updateConfig = async (req, res, next) => {
  try {
    const { port, autoRestart, maxRestartAttempts, restartDelay } = req.body;
    
    const newConfig = {};
    if (port !== undefined) newConfig.port = parseInt(port);
    if (autoRestart !== undefined) newConfig.autoRestart = Boolean(autoRestart);
    if (maxRestartAttempts !== undefined) newConfig.maxRestartAttempts = parseInt(maxRestartAttempts);
    if (restartDelay !== undefined) newConfig.restartDelay = parseInt(restartDelay);
    
    mcpServerManager.updateConfig(newConfig);
    
    res.status(200).json({
      success: true,
      message: 'MCP server configuration updated successfully',
      data: newConfig
    });
  } catch (error) {
    logger.error('Error updating MCP config:', error);
    next(error);
  }
};

/**
 * Test AI tool
 */
exports.testTool = async (req, res, next) => {
  try {
    const { toolName, arguments: args } = req.body;
    
    if (!toolName) {
      return res.status(400).json({
        success: false,
        message: 'Tool name is required'
      });
    }

    // Get the server instance and call the tool directly
    const server = mcpServerManager.server;
    if (!server) {
      return res.status(503).json({
        success: false,
        message: 'MCP server is not running'
      });
    }

    const result = await server.handleToolCall({ name: toolName, arguments: args || {} });
    
    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Error testing MCP tool:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

/**
 * Get AI analytics
 */
exports.getAnalytics = async (req, res, next) => {
  try {
    const { timeRange = '24h' } = req.query;
    
    // Mock analytics data - in production, this would come from a database
    const analytics = {
      totalRequests: Math.floor(Math.random() * 1000) + 500,
      successfulRequests: Math.floor(Math.random() * 900) + 450,
      failedRequests: Math.floor(Math.random() * 50) + 10,
      averageResponseTime: Math.floor(Math.random() * 200) + 100,
      toolUsage: {
        user_matching: Math.floor(Math.random() * 200) + 100,
        profile_analysis: Math.floor(Math.random() * 150) + 75,
        compatibility_score: Math.floor(Math.random() * 300) + 150,
        smart_recommendations: Math.floor(Math.random() * 100) + 50,
        fraud_detection: Math.floor(Math.random() * 50) + 25,
        success_prediction: Math.floor(Math.random() * 80) + 40
      },
      performanceMetrics: {
        cpuUsage: Math.random() * 30 + 20,
        memoryUsage: Math.random() * 40 + 30,
        activeConnections: Math.floor(Math.random() * 10) + 5
      },
      timeRange: timeRange,
      lastUpdated: new Date().toISOString()
    };
    
    res.status(200).json({
      success: true,
      data: analytics
    });
  } catch (error) {
    logger.error('Error getting MCP analytics:', error);
    next(error);
  }
};

/**
 * Register custom tool
 */
exports.registerTool = async (req, res, next) => {
  try {
    const { name, description, inputSchema } = req.body;
    
    if (!name || !description || !inputSchema) {
      return res.status(400).json({
        success: false,
        message: 'Name, description, and inputSchema are required'
      });
    }

    const tool = {
      name,
      description,
      inputSchema
    };

    mcpServerManager.registerTool(name, tool);
    
    res.status(201).json({
      success: true,
      message: 'Tool registered successfully',
      data: tool
    });
  } catch (error) {
    logger.error('Error registering MCP tool:', error);
    next(error);
  }
};

/**
 * Register custom resource
 */
exports.registerResource = async (req, res, next) => {
  try {
    const { name, uri, description, mimeType } = req.body;
    
    if (!name || !uri || !description) {
      return res.status(400).json({
        success: false,
        message: 'Name, URI, and description are required'
      });
    }

    const resource = {
      name,
      uri,
      description,
      mimeType: mimeType || 'application/json'
    };

    mcpServerManager.registerResource(name, resource);
    
    res.status(201).json({
      success: true,
      message: 'Resource registered successfully',
      data: resource
    });
  } catch (error) {
    logger.error('Error registering MCP resource:', error);
    next(error);
  }
};

/**
 * Register custom prompt
 */
exports.registerPrompt = async (req, res, next) => {
  try {
    const { name, description, arguments: promptArgs } = req.body;
    
    if (!name || !description) {
      return res.status(400).json({
        success: false,
        message: 'Name and description are required'
      });
    }

    const prompt = {
      name,
      description,
      arguments: promptArgs || []
    };

    mcpServerManager.registerPrompt(name, prompt);
    
    res.status(201).json({
      success: true,
      message: 'Prompt registered successfully',
      data: prompt
    });
  } catch (error) {
    logger.error('Error registering MCP prompt:', error);
    next(error);
  }
};

/**
 * Health check endpoint
 */
exports.healthCheck = async (req, res, next) => {
  try {
    const health = await mcpServerManager.healthCheck();
    
    const statusCode = health.healthy ? 200 : 503;
    
    res.status(statusCode).json({
      success: health.healthy,
      data: health
    });
  } catch (error) {
    logger.error('Error in MCP health check:', error);
    res.status(503).json({
      success: false,
      message: 'Health check failed',
      error: error.message
    });
  }
};
