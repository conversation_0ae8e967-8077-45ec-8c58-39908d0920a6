import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';

// Configure NextAuth
export const authOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        try {
          // In a real implementation, you would fetch the user from your database
          // and verify the password using bcrypt or another secure method

          // For now, we'll use a simple check for admin credentials
          // Replace this with actual database authentication in production
          if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {
            return {
              id: '1',
              name: 'Admin User',
              email: '<EMAIL>',
              role: 'ADMIN'
            };
          }

          // In a production implementation, you would fetch the user from your backend API:
          // const response = await fetch(`${process.env.BACKEND_API_URL}/api/auth/login`, {
          //   method: 'POST',
          //   headers: { 'Content-Type': 'application/json' },
          //   body: JSON.stringify({ email: credentials.email, password: credentials.password })
          // });
          //
          // const data = await response.json();
          // if (data.success) {
          //   return data.user;
          // }

          return null;
        } catch (error) {
          console.error('Auth error:', error);
          return null;
        }
      }
    })
  ],
  callbacks: {
    async jwt({ token, user }) {
      // Add role to JWT token if user is present
      if (user) {
        token.role = user.role;
      }
      return token;
    },
    async session({ session, token }) {
      // Add role to session
      if (token) {
        session.user.role = token.role;
      }
      return session;
    }
  },
  pages: {
    signIn: '/admin/login',
    error: '/admin/login',
  },
  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24 hours
  },
  secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-change-in-production',
};

export default NextAuth(authOptions);
