/**
 * Modern Lifestyle & Habits Form
 *
 * A modern UI form for collecting lifestyle and habits details as part of the profile completion process.
 * Uses the shared styled components for consistent UI across the application.
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  MenuItem,
  FormHelperText,
  CircularProgress,
  Alert,
  InputAdornment,
  Typography,
  Chip,
  Autocomplete
} from '@mui/material';
import {
  Restaurant as DietIcon,
  SmokingRooms as SmokingIcon,
  LocalBar as DrinkingIcon,
  Favorite as HobbiesIcon,
  Language as LanguageIcon,
  Pets as PetsIcon
} from '@mui/icons-material';
import { validateField, VALIDATION_RULES } from '@/utils/validationUtils';
import { formatError, getUserFriendlyMessage, isValidationError } from '@/utils/errorHandling';
import {
  StyledPaper,
  StyledTextField,
  StyledSelect,
  StyledButton,
  StyledFormLabel,
  FloatingElement,
  FormSection,
  FormRow,
  StyledSectionTitle
} from '@/components/ui/ModernFormComponents';

// Constants for form options
const DIET_OPTIONS = [
  { value: 'Vegetarian', label: 'Vegetarian' },
  { value: 'Non-Vegetarian', label: 'Non-Vegetarian' },
  { value: 'Eggetarian', label: 'Eggetarian' },
  { value: 'Vegan', label: 'Vegan' },
  { value: 'Jain', label: 'Jain' }
];

const SMOKING_OPTIONS = [
  { value: 'No', label: 'No' },
  { value: 'Occasionally', label: 'Occasionally' },
  { value: 'Yes', label: 'Yes' }
];

const DRINKING_OPTIONS = [
  { value: 'No', label: 'No' },
  { value: 'Occasionally', label: 'Occasionally' },
  { value: 'Yes', label: 'Yes' }
];

const PETS_OPTIONS = [
  { value: 'No', label: 'No' },
  { value: 'Yes', label: 'Yes' },
  { value: 'Love pets but don\'t have any', label: 'Love pets but don\'t have any' }
];

const LANGUAGE_OPTIONS = [
  'English',
  'Hindi',
  'Marathi',
  'Gujarati',
  'Tamil',
  'Telugu',
  'Kannada',
  'Malayalam',
  'Bengali',
  'Punjabi'
];

const HOBBY_OPTIONS = [
  'Reading',
  'Traveling',
  'Cooking',
  'Photography',
  'Music',
  'Dancing',
  'Painting',
  'Sports',
  'Gardening',
  'Yoga',
  'Meditation',
  'Movies',
  'Writing',
  'Hiking',
  'Swimming'
];

const ModernLifestyleHabitsForm = ({ userData, onSave, isLoading = false }) => {
  const [formData, setFormData] = useState({
    diet: '',
    smoking: 'No',
    drinking: 'No',
    hobbies: [],
    interests: [],
    languages: [],
    pets: 'No',
    lifestyle: ''
  });

  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  // Initialize form with user data if available
  useEffect(() => {
    if (userData?.lifestyleHabits) {
      const lifestyleHabits = userData.lifestyleHabits;

      // Parse string arrays if needed
      const hobbies = typeof lifestyleHabits.hobbies === 'string'
        ? lifestyleHabits.hobbies.split(',').map(h => h.trim())
        : lifestyleHabits.hobbies || [];

      const interests = typeof lifestyleHabits.interests === 'string'
        ? lifestyleHabits.interests.split(',').map(i => i.trim())
        : lifestyleHabits.interests || [];

      const languages = typeof lifestyleHabits.languages === 'string'
        ? lifestyleHabits.languages.split(',').map(l => l.trim())
        : lifestyleHabits.languages || [];

      setFormData({
        ...formData,
        ...lifestyleHabits,
        hobbies,
        interests,
        languages
      });
    }
  }, [userData]);

  // Validate a single field
  const validateSingleField = (name, value) => {
    let rule;

    switch (name) {
      case 'diet':
        rule = VALIDATION_RULES.DIET;
        break;
      case 'smoking':
        rule = VALIDATION_RULES.SMOKING;
        break;
      case 'drinking':
        rule = VALIDATION_RULES.DRINKING;
        break;
      default:
        return null;
    }

    return validateField(name, value, rule, formData);
  };

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    const newFormData = {
      ...formData,
      [name]: value
    };

    setFormData(newFormData);

    // Mark field as touched
    setTouched({
      ...touched,
      [name]: true
    });

    // Validate field on change if it's been touched
    if (touched[name]) {
      const fieldError = validateSingleField(name, value);
      setErrors(prevErrors => {
        if (fieldError) {
          return { ...prevErrors, [name]: fieldError };
        } else {
          // Remove error if it exists
          const { [name]: _, ...restErrors } = prevErrors;
          return restErrors;
        }
      });
    }
  };

  // Handle autocomplete change
  const handleAutocompleteChange = (name, value) => {
    const newFormData = {
      ...formData,
      [name]: value
    };

    setFormData(newFormData);

    // Mark field as touched
    setTouched({
      ...touched,
      [name]: true
    });

    // Validate field on change if it's been touched
    if (touched[name]) {
      const fieldError = validateSingleField(name, value);
      setErrors(prevErrors => {
        if (fieldError) {
          return { ...prevErrors, [name]: fieldError };
        } else {
          // Remove error if it exists
          const { [name]: _, ...restErrors } = prevErrors;
          return restErrors;
        }
      });
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    // Validate all fields
    Object.keys(formData).forEach(fieldName => {
      const error = validateSingleField(fieldName, formData[fieldName]);
      if (error) {
        newErrors[fieldName] = error;
      }
    });

    // Required fields
    if (!formData.diet) {
      newErrors.diet = 'Diet preference is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Call the onSave function with the form data
    onSave(formData);
  };

  return (
    <StyledPaper>
      {/* Decorative elements */}
      <FloatingElement position="top-right" />
      <FloatingElement position="bottom-left" />

      <Box sx={{ position: 'relative', zIndex: 1 }}>
        <StyledSectionTitle>Lifestyle & Habits</StyledSectionTitle>

        {/* Form content */}
        <form onSubmit={handleSubmit}>
          <FormSection title="Diet & Habits">
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <DietIcon fontSize="small" sx={{ mr: 1 }} />
                    Diet Preference*
                  </Box>
                </StyledFormLabel>
                <StyledSelect
                  name="diet"
                  value={formData.diet}
                  onChange={handleChange}
                  fullWidth
                  error={!!errors.diet}
                >
                  <MenuItem value="">Select Diet Preference</MenuItem>
                  {DIET_OPTIONS.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </StyledSelect>
                {errors.diet && <FormHelperText error>{errors.diet}</FormHelperText>}
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <SmokingIcon fontSize="small" sx={{ mr: 1 }} />
                    Smoking
                  </Box>
                </StyledFormLabel>
                <StyledSelect
                  name="smoking"
                  value={formData.smoking}
                  onChange={handleChange}
                  fullWidth
                >
                  {SMOKING_OPTIONS.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </StyledSelect>
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <DrinkingIcon fontSize="small" sx={{ mr: 1 }} />
                    Drinking
                  </Box>
                </StyledFormLabel>
                <StyledSelect
                  name="drinking"
                  value={formData.drinking}
                  onChange={handleChange}
                  fullWidth
                >
                  {DRINKING_OPTIONS.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </StyledSelect>
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <PetsIcon fontSize="small" sx={{ mr: 1 }} />
                    Pets
                  </Box>
                </StyledFormLabel>
                <StyledSelect
                  name="pets"
                  value={formData.pets}
                  onChange={handleChange}
                  fullWidth
                >
                  {PETS_OPTIONS.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </StyledSelect>
              </Grid>
            </Grid>
          </FormSection>

          <FormSection title="Hobbies & Interests">
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <HobbiesIcon fontSize="small" sx={{ mr: 1 }} />
                    Hobbies
                  </Box>
                </StyledFormLabel>
                <Autocomplete
                  multiple
                  options={HOBBY_OPTIONS}
                  value={formData.hobbies}
                  onChange={(e, newValue) => handleAutocompleteChange('hobbies', newValue)}
                  freeSolo
                  renderInput={(params) => (
                    <StyledTextField
                      {...params}
                      placeholder="Add your hobbies"
                    />
                  )}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip
                        label={option}
                        {...getTagProps({ index })}
                        sx={{
                          background: 'var(--primary-gradient)',
                          color: 'white',
                        }}
                      />
                    ))
                  }
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <HobbiesIcon fontSize="small" sx={{ mr: 1 }} />
                    Interests
                  </Box>
                </StyledFormLabel>
                <Autocomplete
                  multiple
                  options={[]}
                  value={formData.interests}
                  onChange={(e, newValue) => handleAutocompleteChange('interests', newValue)}
                  freeSolo
                  renderInput={(params) => (
                    <StyledTextField
                      {...params}
                      placeholder="Add your interests"
                    />
                  )}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip
                        label={option}
                        {...getTagProps({ index })}
                        sx={{
                          background: 'var(--primary-gradient)',
                          color: 'white',
                        }}
                      />
                    ))
                  }
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <LanguageIcon fontSize="small" sx={{ mr: 1 }} />
                    Languages
                  </Box>
                </StyledFormLabel>
                <Autocomplete
                  multiple
                  options={LANGUAGE_OPTIONS}
                  value={formData.languages}
                  onChange={(e, newValue) => handleAutocompleteChange('languages', newValue)}
                  freeSolo
                  renderInput={(params) => (
                    <StyledTextField
                      {...params}
                      placeholder="Add languages you speak"
                    />
                  )}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip
                        label={option}
                        {...getTagProps({ index })}
                        sx={{
                          background: 'var(--primary-gradient)',
                          color: 'white',
                        }}
                      />
                    ))
                  }
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>Lifestyle</StyledFormLabel>
                <StyledTextField
                  name="lifestyle"
                  value={formData.lifestyle}
                  onChange={handleChange}
                  fullWidth
                  placeholder="Describe your lifestyle (e.g., Active, Homely, etc.)"
                />
              </Grid>
            </Grid>
          </FormSection>

          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
            <StyledButton
              type="submit"
              variant="contained"
              disabled={isLoading}
              startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : null}
            >
              {isLoading ? 'Saving...' : 'Save Lifestyle & Habits'}
            </StyledButton>
          </Box>
        </form>
      </Box>
    </StyledPaper>
  );
};

export default ModernLifestyleHabitsForm;
