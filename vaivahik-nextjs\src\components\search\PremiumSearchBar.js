import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Tabs,
  Tab,
  TextField,
  Button,
  Grid,
  FormControl,
  FormLabel,
  Divider,
  Chip,
  Select,
  MenuItem,
  InputAdornment,
  IconButton,
  Collapse,
  Tooltip,
  Card,
  CardContent,
  Badge,
  Autocomplete,
  Slider,
  Stack,
  useMediaQuery,
  useTheme,
  Fade,
  Zoom,
  Popper,
  ClickAwayListener,
  Grow
} from '@mui/material';
import {
  Search as SearchIcon,
  Person as PersonIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon
} from '@mui/icons-material';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import TuneIcon from '@mui/icons-material/Tune';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import WorkIcon from '@mui/icons-material/Work';
import SchoolIcon from '@mui/icons-material/School';
import CakeIcon from '@mui/icons-material/Cake';
import HeightIcon from '@mui/icons-material/Height';
import PaidIcon from '@mui/icons-material/Paid';
import CloseIcon from '@mui/icons-material/Close';
import HistoryIcon from '@mui/icons-material/History';
import FavoriteIcon from '@mui/icons-material/Favorite';
import HeightRangeSelector from './HeightRangeSelector';
import { formatHeight } from '@/utils/heightUtils';

/**
 * Premium Search Bar Component
 *
 * A world-class, advanced search interface for matrimony applications
 * with elegant animations, intuitive controls, and comprehensive filtering
 */
const PremiumSearchBar = ({
  onSearch,
  savedSearches = [],
  onSaveSearch,
  userGender = 'MALE', // The current user's gender
  recentSearches = []
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));

  // Search mode tabs
  const [searchMode, setSearchMode] = useState(0); // 0: Regular Search, 1: ID Search

  // Active filter section
  const [activeFilter, setActiveFilter] = useState(null);

  // Search parameters
  const [searchParams, setSearchParams] = useState({
    // Basic search params (always visible)
    ageFrom: userGender === 'MALE' ? 18 : 21,
    ageTo: userGender === 'MALE' ? 35 : 40,
    heightFrom: 53, // 4'5"
    heightTo: 77, // 6'5"
    location: '',
    occupation: [],
    incomeRange: '',

    // Religious & Community
    religion: 'HINDU',
    caste: 'MARATHA',
    subCaste: '',
    gotra: '',
    manglik: 'DOESNT_MATTER',

    // Education & Career
    education: [],

    // Lifestyle
    diet: '',
    smoking: 'DOESNT_MATTER',
    drinking: 'DOESNT_MATTER',

    // Appearance & Status
    maritalStatus: [],
    withPhoto: true,
    profileCreatedWithin: '',
    profileType: [], // VERIFIED, PREMIUM, etc.

    // ID search params
    userId: ''
  });

  // Count of active filters
  const [activeFilterCount, setActiveFilterCount] = useState(0);

  // Calculate active filter count on parameter changes
  useEffect(() => {
    let count = 0;

    if (searchParams.location) count++;
    if (searchParams.occupation.length > 0) count++;
    if (searchParams.incomeRange) count++;
    if (searchParams.religion !== 'HINDU') count++;
    if (searchParams.caste !== 'MARATHA') count++;
    if (searchParams.subCaste) count++;
    if (searchParams.gotra) count++;
    if (searchParams.manglik !== 'DOESNT_MATTER') count++;
    if (searchParams.education.length > 0) count++;
    if (searchParams.diet) count++;
    if (searchParams.smoking !== 'DOESNT_MATTER') count++;
    if (searchParams.drinking !== 'DOESNT_MATTER') count++;
    if (searchParams.maritalStatus.length > 0) count++;
    if (searchParams.profileCreatedWithin) count++;
    if (searchParams.profileType.length > 0) count++;

    setActiveFilterCount(count);
  }, [searchParams]);

  // Handle search mode change
  const handleSearchModeChange = (_, newValue) => {
    setSearchMode(newValue);
    setActiveFilter(null);
  };

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams({ ...searchParams, [name]: value });
  };

  // Handle multi-select change
  const handleMultiSelectChange = (name, value) => {
    setSearchParams({ ...searchParams, [name]: value });
  };

  // Handle height range change
  const handleHeightChange = (min, max) => {
    setSearchParams({
      ...searchParams,
      heightFrom: min,
      heightTo: max
    });
  };

  // Toggle filter section
  const handleFilterClick = (filter) => {
    setActiveFilter(activeFilter === filter ? null : filter);
  };

  // Handle search submission
  const handleSearch = () => {
    // Prepare search data based on active tab
    let searchData = {};

    if (searchMode === 0) { // Regular Search
      searchData = {
        searchType: 'REGULAR',
        targetGender: userGender === 'MALE' ? 'FEMALE' : 'MALE',
        ...searchParams
      };
    } else { // ID Search
      searchData = {
        searchType: 'ID',
        userId: searchParams.userId
      };
    }

    // Close any open filter
    setActiveFilter(null);

    // Call the search handler
    onSearch(searchData);
  };

  // Save current search
  const handleSaveSearch = () => {
    if (onSaveSearch) {
      onSaveSearch({
        ...searchParams,
        searchMode
      });
    }
  };

  // Clear all filters
  const handleClearFilters = () => {
    setSearchParams({
      ...searchParams,
      location: '',
      occupation: [],
      incomeRange: '',
      religion: 'HINDU',
      caste: 'MARATHA',
      subCaste: '',
      gotra: '',
      manglik: 'DOESNT_MATTER',
      education: [],
      diet: '',
      smoking: 'DOESNT_MATTER',
      drinking: 'DOESNT_MATTER',
      maritalStatus: [],
      profileCreatedWithin: '',
      profileType: []
    });
  };

  // Generate age options
  const generateAgeOptions = (min, max) => {
    const options = [];
    for (let i = min; i <= max; i++) {
      options.push(i);
    }
    return options;
  };

  // Age options based on target gender (opposite of user)
  const targetGender = userGender === 'MALE' ? 'FEMALE' : 'MALE';
  const minAge = targetGender === 'FEMALE' ? 18 : 21;
  const ageFromOptions = generateAgeOptions(minAge, 60);
  const ageToOptions = generateAgeOptions(
    Math.max(minAge, searchParams.ageFrom),
    70
  );

  // Occupation options
  const occupationOptions = [
    { value: 'IT_SOFTWARE', label: 'IT/Software' },
    { value: 'ENGINEERING', label: 'Engineering' },
    { value: 'MEDICAL', label: 'Medical' },
    { value: 'FINANCE', label: 'Finance' },
    { value: 'EDUCATION', label: 'Education' },
    { value: 'BUSINESS', label: 'Business' },
    { value: 'GOVERNMENT', label: 'Government' },
    { value: 'OTHER', label: 'Other' }
  ];

  // Income range options
  const incomeOptions = [
    { value: '', label: 'Any Income' },
    { value: 'UPTO_3L', label: 'Upto 3 Lakhs' },
    { value: '3L_5L', label: '3 - 5 Lakhs' },
    { value: '5L_10L', label: '5 - 10 Lakhs' },
    { value: '10L_20L', label: '10 - 20 Lakhs' },
    { value: 'ABOVE_20L', label: 'Above 20 Lakhs' }
  ];

  // Religion options
  const religionOptions = [
    { value: 'HINDU', label: 'Hindu' },
    { value: 'MUSLIM', label: 'Muslim' },
    { value: 'CHRISTIAN', label: 'Christian' },
    { value: 'SIKH', label: 'Sikh' },
    { value: 'JAIN', label: 'Jain' },
    { value: 'BUDDHIST', label: 'Buddhist' },
    { value: 'OTHER', label: 'Other' }
  ];

  // State for advanced filters
  const [advancedFilterTab, setAdvancedFilterTab] = useState(0);

  // Handle advanced filter tab change
  const handleAdvancedFilterTabChange = (_, newValue) => {
    setAdvancedFilterTab(newValue);
  };

  // Render filter popper content
  const renderFilterContent = () => {
    switch (activeFilter) {
      case 'age':
        return (
          <Box sx={{ p: 2, width: 300 }}>
            <Typography variant="subtitle1" gutterBottom>
              Age Range
            </Typography>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={5}>
                <FormControl fullWidth size="small">
                  <Select
                    name="ageFrom"
                    value={searchParams.ageFrom}
                    onChange={handleInputChange}
                  >
                    {ageFromOptions.map(age => (
                      <MenuItem key={`from-${age}`} value={age}>{age}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={2} sx={{ textAlign: 'center' }}>
                <Typography>to</Typography>
              </Grid>
              <Grid item xs={5}>
                <FormControl fullWidth size="small">
                  <Select
                    name="ageTo"
                    value={searchParams.ageTo}
                    onChange={handleInputChange}
                  >
                    {ageToOptions.map(age => (
                      <MenuItem key={`to-${age}`} value={age}>{age}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
              {targetGender === 'FEMALE' ? 'Minimum age: 18 years' : 'Minimum age: 21 years'}
            </Typography>
          </Box>
        );

      case 'height':
        return (
          <Box sx={{ p: 2, width: 350 }}>
            <Typography variant="subtitle1" gutterBottom>
              Height Range
            </Typography>
            <HeightRangeSelector
              minHeight={searchParams.heightFrom}
              maxHeight={searchParams.heightTo}
              onChange={handleHeightChange}
              gender={targetGender}
            />
          </Box>
        );

      case 'occupation':
        return (
          <Box sx={{ p: 2, width: 300 }}>
            <Typography variant="subtitle1" gutterBottom>
              Occupation
            </Typography>
            <FormControl fullWidth size="small">
              <Select
                name="occupation"
                value={searchParams.occupation}
                onChange={(e) => handleMultiSelectChange('occupation', e.target.value)}
                multiple
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Chip
                        key={value}
                        label={occupationOptions.find(opt => opt.value === value)?.label}
                        size="small"
                      />
                    ))}
                  </Box>
                )}
              >
                {occupationOptions.map(option => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        );

      case 'income':
        return (
          <Box sx={{ p: 2, width: 300 }}>
            <Typography variant="subtitle1" gutterBottom>
              Annual Income
            </Typography>
            <FormControl fullWidth size="small">
              <Select
                name="incomeRange"
                value={searchParams.incomeRange}
                onChange={handleInputChange}
              >
                {incomeOptions.map(option => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        );

      case 'location':
        return (
          <Box sx={{ p: 2, width: 300 }}>
            <Typography variant="subtitle1" gutterBottom>
              Location
            </Typography>
            <TextField
              name="location"
              value={searchParams.location}
              onChange={handleInputChange}
              placeholder="City, State or Country"
              size="small"
              fullWidth
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LocationOnIcon fontSize="small" />
                  </InputAdornment>
                ),
              }}
            />
            {recentSearches.length > 0 && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Recent locations:
                </Typography>
                <Stack direction="row" spacing={1} sx={{ mt: 0.5, flexWrap: 'wrap', gap: 0.5 }}>
                  {recentSearches.map((location, index) => (
                    <Chip
                      key={index}
                      label={location}
                      size="small"
                      onClick={() => setSearchParams({...searchParams, location})}
                    />
                  ))}
                </Stack>
              </Box>
            )}
          </Box>
        );

      case 'more':
        return (
          <Box sx={{ width: isMobile ? '100%' : 500, maxWidth: '100%' }}>
            {/* Advanced Filter Tabs */}
            <Tabs
              value={advancedFilterTab}
              onChange={handleAdvancedFilterTabChange}
              variant="fullWidth"
              sx={{ borderBottom: 1, borderColor: 'divider' }}
            >
              <Tab label="Religious" />
              <Tab label="Education" />
              <Tab label="Lifestyle" />
              <Tab label="Profile" />
            </Tabs>

            {/* Religious & Community Filters */}
            {advancedFilterTab === 0 && (
              <Box sx={{ p: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Religious & Community
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                      <FormLabel>Religion</FormLabel>
                      <Select
                        name="religion"
                        value={searchParams.religion}
                        onChange={handleInputChange}
                      >
                        {religionOptions.map(option => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                      <FormLabel>Caste</FormLabel>
                      <TextField
                        name="caste"
                        value={searchParams.caste}
                        onChange={handleInputChange}
                        placeholder="e.g., Maratha"
                        size="small"
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                      <FormLabel>Sub-caste</FormLabel>
                      <TextField
                        name="subCaste"
                        value={searchParams.subCaste}
                        onChange={handleInputChange}
                        placeholder="Enter sub-caste"
                        size="small"
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                      <FormLabel>Gotra</FormLabel>
                      <TextField
                        name="gotra"
                        value={searchParams.gotra}
                        onChange={handleInputChange}
                        placeholder="Enter gotra"
                        size="small"
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xs={12}>
                    <FormControl fullWidth size="small">
                      <FormLabel>Manglik</FormLabel>
                      <Select
                        name="manglik"
                        value={searchParams.manglik}
                        onChange={handleInputChange}
                      >
                        <MenuItem value="DOESNT_MATTER">Doesn't Matter</MenuItem>
                        <MenuItem value="YES">Yes</MenuItem>
                        <MenuItem value="NO">No</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* Education & Career Filters */}
            {advancedFilterTab === 1 && (
              <Box sx={{ p: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Education & Career
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                      <FormLabel>Education</FormLabel>
                      <Select
                        name="education"
                        value={searchParams.education}
                        onChange={(e) => handleMultiSelectChange('education', e.target.value)}
                        multiple
                        renderValue={(selected) => (
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {selected.map((value) => (
                              <Chip key={value} label={value.replace('_', ' ')} size="small" />
                            ))}
                          </Box>
                        )}
                      >
                        <MenuItem value="HIGH_SCHOOL">High School</MenuItem>
                        <MenuItem value="BACHELORS">Bachelor's Degree</MenuItem>
                        <MenuItem value="MASTERS">Master's Degree</MenuItem>
                        <MenuItem value="DOCTORATE">Doctorate</MenuItem>
                        <MenuItem value="DIPLOMA">Diploma</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  {/* Occupation is already in the main filters */}
                  {/* Income Range is already in the main filters */}
                </Grid>
              </Box>
            )}

            {/* Lifestyle Filters */}
            {advancedFilterTab === 2 && (
              <Box sx={{ p: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Lifestyle
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                      <FormLabel>Diet</FormLabel>
                      <Select
                        name="diet"
                        value={searchParams.diet}
                        onChange={handleInputChange}
                      >
                        <MenuItem value="">Any</MenuItem>
                        <MenuItem value="VEG">Vegetarian</MenuItem>
                        <MenuItem value="NON_VEG">Non-Vegetarian</MenuItem>
                        <MenuItem value="JAIN">Jain</MenuItem>
                        <MenuItem value="VEGAN">Vegan</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                      <FormLabel>Smoking</FormLabel>
                      <Select
                        name="smoking"
                        value={searchParams.smoking}
                        onChange={handleInputChange}
                      >
                        <MenuItem value="DOESNT_MATTER">Doesn't Matter</MenuItem>
                        <MenuItem value="NO">No</MenuItem>
                        <MenuItem value="YES">Yes</MenuItem>
                        <MenuItem value="OCCASIONALLY">Occasionally</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth size="small">
                      <FormLabel>Drinking</FormLabel>
                      <Select
                        name="drinking"
                        value={searchParams.drinking}
                        onChange={handleInputChange}
                      >
                        <MenuItem value="DOESNT_MATTER">Doesn't Matter</MenuItem>
                        <MenuItem value="NO">No</MenuItem>
                        <MenuItem value="YES">Yes</MenuItem>
                        <MenuItem value="OCCASIONALLY">Occasionally</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* Profile Status Filters */}
            {advancedFilterTab === 3 && (
              <Box sx={{ p: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Profile Status
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                      <FormLabel>Marital Status</FormLabel>
                      <Select
                        name="maritalStatus"
                        value={searchParams.maritalStatus}
                        onChange={(e) => handleMultiSelectChange('maritalStatus', e.target.value)}
                        multiple
                        renderValue={(selected) => (
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {selected.map((value) => (
                              <Chip key={value} label={value.replace('_', ' ')} size="small" />
                            ))}
                          </Box>
                        )}
                      >
                        <MenuItem value="NEVER_MARRIED">Never Married</MenuItem>
                        <MenuItem value="DIVORCED">Divorced</MenuItem>
                        <MenuItem value="WIDOWED">Widowed</MenuItem>
                        <MenuItem value="AWAITING_DIVORCE">Awaiting Divorce</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                      <FormLabel>Profile Created</FormLabel>
                      <Select
                        name="profileCreatedWithin"
                        value={searchParams.profileCreatedWithin}
                        onChange={handleInputChange}
                      >
                        <MenuItem value="">Any Time</MenuItem>
                        <MenuItem value="1_WEEK">Last Week</MenuItem>
                        <MenuItem value="1_MONTH">Last Month</MenuItem>
                        <MenuItem value="3_MONTHS">Last 3 Months</MenuItem>
                        <MenuItem value="6_MONTHS">Last 6 Months</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                      <FormLabel>Profile Type</FormLabel>
                      <Select
                        name="profileType"
                        value={searchParams.profileType}
                        onChange={(e) => handleMultiSelectChange('profileType', e.target.value)}
                        multiple
                        renderValue={(selected) => (
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {selected.map((value) => (
                              <Chip key={value} label={value} size="small" />
                            ))}
                          </Box>
                        )}
                      >
                        <MenuItem value="VERIFIED">Verified</MenuItem>
                        <MenuItem value="PREMIUM">Premium</MenuItem>
                        <MenuItem value="FEATURED">Featured</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth sx={{ mt: 1 }}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={searchParams.withPhoto}
                            onChange={handleSwitchChange}
                            name="withPhoto"
                          />
                        }
                        label="Profiles with Photo"
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              </Box>
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Paper
      elevation={3}
      sx={{
        borderRadius: 2,
        overflow: 'hidden',
        transition: 'all 0.3s ease',
        position: 'relative'
      }}
    >
      {/* Search Tabs */}
      <Tabs
        value={searchMode}
        onChange={handleSearchModeChange}
        variant="fullWidth"
        sx={{
          bgcolor: theme.palette.primary.main,
          color: 'white',
          '& .MuiTab-root': { color: 'rgba(255,255,255,0.7)' },
          '& .Mui-selected': { color: 'white' },
          '& .MuiTabs-indicator': { bgcolor: 'white' }
        }}
      >
        <Tab
          label="Find Matches"
          icon={<SearchIcon />}
          iconPosition="start"
        />
        <Tab
          label="Search by ID"
          icon={<PersonIcon />}
          iconPosition="start"
        />
      </Tabs>

      {/* Regular Search Form */}
      {searchMode === 0 && (
        <Box sx={{ p: { xs: 2, md: 3 } }}>
          <Typography variant="h6" gutterBottom>
            Find Your Perfect {targetGender === 'FEMALE' ? 'Bride' : 'Groom'}
          </Typography>

          {/* Main Search Filters */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            {/* Age Filter */}
            <Grid item xs={6} sm={4} md={2}>
              <Card
                variant="outlined"
                sx={{
                  cursor: 'pointer',
                  borderColor: activeFilter === 'age' ? theme.palette.primary.main : 'divider',
                  bgcolor: activeFilter === 'age' ? 'rgba(0, 0, 0, 0.04)' : 'transparent',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    borderColor: theme.palette.primary.main,
                    bgcolor: 'rgba(0, 0, 0, 0.02)'
                  }
                }}
                onClick={() => handleFilterClick('age')}
              >
                <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                    <CakeIcon fontSize="small" color="primary" sx={{ mr: 1 }} />
                    <Typography variant="body2" fontWeight="medium">Age</Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    {searchParams.ageFrom} - {searchParams.ageTo} yrs
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Height Filter */}
            <Grid item xs={6} sm={4} md={2}>
              <Card
                variant="outlined"
                sx={{
                  cursor: 'pointer',
                  borderColor: activeFilter === 'height' ? theme.palette.primary.main : 'divider',
                  bgcolor: activeFilter === 'height' ? 'rgba(0, 0, 0, 0.04)' : 'transparent',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    borderColor: theme.palette.primary.main,
                    bgcolor: 'rgba(0, 0, 0, 0.02)'
                  }
                }}
                onClick={() => handleFilterClick('height')}
              >
                <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                    <HeightIcon fontSize="small" color="primary" sx={{ mr: 1 }} />
                    <Typography variant="body2" fontWeight="medium">Height</Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    {formatHeight(Math.floor(searchParams.heightFrom / 12), searchParams.heightFrom % 12)} - {formatHeight(Math.floor(searchParams.heightTo / 12), searchParams.heightTo % 12)}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Occupation Filter */}
            <Grid item xs={6} sm={4} md={2}>
              <Card
                variant="outlined"
                sx={{
                  cursor: 'pointer',
                  borderColor: activeFilter === 'occupation' ? theme.palette.primary.main : 'divider',
                  bgcolor: activeFilter === 'occupation' ? 'rgba(0, 0, 0, 0.04)' : 'transparent',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    borderColor: theme.palette.primary.main,
                    bgcolor: 'rgba(0, 0, 0, 0.02)'
                  }
                }}
                onClick={() => handleFilterClick('occupation')}
              >
                <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                    <WorkIcon fontSize="small" color="primary" sx={{ mr: 1 }} />
                    <Typography variant="body2" fontWeight="medium">Occupation</Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    {searchParams.occupation.length > 0
                      ? `${searchParams.occupation.length} selected`
                      : 'Any occupation'}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Income Filter */}
            <Grid item xs={6} sm={4} md={2}>
              <Card
                variant="outlined"
                sx={{
                  cursor: 'pointer',
                  borderColor: activeFilter === 'income' ? theme.palette.primary.main : 'divider',
                  bgcolor: activeFilter === 'income' ? 'rgba(0, 0, 0, 0.04)' : 'transparent',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    borderColor: theme.palette.primary.main,
                    bgcolor: 'rgba(0, 0, 0, 0.02)'
                  }
                }}
                onClick={() => handleFilterClick('income')}
              >
                <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                    <PaidIcon fontSize="small" color="primary" sx={{ mr: 1 }} />
                    <Typography variant="body2" fontWeight="medium">Income</Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    {searchParams.incomeRange
                      ? incomeOptions.find(opt => opt.value === searchParams.incomeRange)?.label
                      : 'Any income'}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Location Filter */}
            <Grid item xs={6} sm={4} md={2}>
              <Card
                variant="outlined"
                sx={{
                  cursor: 'pointer',
                  borderColor: activeFilter === 'location' ? theme.palette.primary.main : 'divider',
                  bgcolor: activeFilter === 'location' ? 'rgba(0, 0, 0, 0.04)' : 'transparent',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    borderColor: theme.palette.primary.main,
                    bgcolor: 'rgba(0, 0, 0, 0.02)'
                  }
                }}
                onClick={() => handleFilterClick('location')}
              >
                <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                    <LocationOnIcon fontSize="small" color="primary" sx={{ mr: 1 }} />
                    <Typography variant="body2" fontWeight="medium">Location</Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary" noWrap>
                    {searchParams.location || 'Any location'}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* More Filters Button */}
            <Grid item xs={6} sm={4} md={2}>
              <Card
                variant="outlined"
                sx={{
                  cursor: 'pointer',
                  borderColor: activeFilter === 'more' ? theme.palette.primary.main : 'divider',
                  bgcolor: activeFilter === 'more' ? 'rgba(0, 0, 0, 0.04)' : 'transparent',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    borderColor: theme.palette.primary.main,
                    bgcolor: 'rgba(0, 0, 0, 0.02)'
                  },
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
                onClick={() => handleFilterClick('more')}
              >
                <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 }, textAlign: 'center' }}>
                  <Badge badgeContent={activeFilterCount} color="primary" sx={{ mr: 1 }}>
                    <FilterAltIcon color="primary" />
                  </Badge>
                  <Typography variant="body2" fontWeight="medium">
                    More Filters
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Filter Popper */}
          <Popper
            open={!!activeFilter}
            anchorEl={document.getElementById('search-container')}
            placement="bottom-start"
            transition
            sx={{
              zIndex: 1200,
              width: isMobile ? '100%' : 'auto',
              maxWidth: '100%'
            }}
          >
            {({ TransitionProps }) => (
              <Grow {...TransitionProps} timeout={350}>
                <Paper
                  elevation={6}
                  sx={{
                    mt: 1,
                    overflow: 'hidden',
                    border: `1px solid ${theme.palette.divider}`
                  }}
                >
                  <ClickAwayListener onClickAway={() => setActiveFilter(null)}>
                    <Box>
                      {renderFilterContent()}
                    </Box>
                  </ClickAwayListener>
                </Paper>
              </Grow>
            )}
          </Popper>

          {/* Action Buttons */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
            <Button
              variant="outlined"
              color="primary"
              size="medium"
              onClick={handleClearFilters}
              disabled={activeFilterCount === 0}
              startIcon={<CloseIcon />}
            >
              Clear Filters
            </Button>

            <Box>
              <Button
                variant="outlined"
                color="primary"
                size="medium"
                onClick={handleSaveSearch}
                sx={{ mr: 1 }}
                startIcon={<BookmarkIcon />}
              >
                Save
              </Button>

              <Button
                variant="contained"
                color="primary"
                size="medium"
                onClick={handleSearch}
                startIcon={<SearchIcon />}
              >
                Search
              </Button>
            </Box>
          </Box>
        </Box>
      )}

      {/* ID Search Form */}
      {searchMode === 1 && (
        <Box sx={{ p: { xs: 2, md: 3 } }}>
          <Typography variant="h6" gutterBottom>
            Find Profile by ID
          </Typography>

          <Typography variant="body2" color="text.secondary" paragraph>
            Enter the User ID to find a specific profile. User IDs are typically displayed on profiles as "VAI12345".
          </Typography>

          <TextField
            fullWidth
            label="User ID"
            name="userId"
            value={searchParams.userId}
            onChange={handleInputChange}
            placeholder="e.g., VAI12345"
            sx={{ mb: 3 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <PersonIcon />
                </InputAdornment>
              ),
            }}
          />

          <Button
            variant="contained"
            color="primary"
            size="large"
            fullWidth
            onClick={handleSearch}
            startIcon={<SearchIcon />}
            disabled={!searchParams.userId}
          >
            Find Profile
          </Button>
        </Box>
      )}
    </Paper>
  );
};

export default PremiumSearchBar;
