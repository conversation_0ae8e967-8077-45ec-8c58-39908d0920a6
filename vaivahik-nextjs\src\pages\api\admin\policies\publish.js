/**
 * Policy Publishing API
 * Handles publishing of policies to make them live on the website
 */

import { authenticateAdmin } from '@/utils/adminAuth';

// Mock policy storage (same as in index.js - in production, this would be shared database)
let policies = {};

export default async function handler(req, res) {
  try {
    // Authenticate admin
    const adminData = await authenticateAdmin(req);
    if (!adminData.success) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized access'
      });
    }

    if (req.method !== 'POST') {
      return res.status(405).json({
        success: false,
        message: 'Method not allowed'
      });
    }

    const { policyId } = req.body;

    if (!policyId) {
      return res.status(400).json({
        success: false,
        message: 'Policy ID is required'
      });
    }

    // Check if policy exists
    if (!policies[policyId]) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found'
      });
    }

    // Increment version and publish
    const currentVersion = policies[policyId].version || '1.0';
    const versionParts = currentVersion.split('.');
    const newMinorVersion = parseInt(versionParts[1]) + 1;
    const newVersion = `${versionParts[0]}.${newMinorVersion}`;

    policies[policyId] = {
      ...policies[policyId],
      version: newVersion,
      status: 'published',
      isPublished: true,
      publishedAt: new Date().toISOString(),
      publishedBy: adminData.admin.name
    };

    return res.status(200).json({
      success: true,
      message: 'Policy published successfully',
      policy: policies[policyId]
    });

  } catch (error) {
    console.error('Policy publishing error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to publish policy'
    });
  }
}
