import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Box,
  Card,
  CardContent,
  Typography,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  Slider,
  Switch,
  Button,
  CircularProgress,
  Divider,
  Grid,
  Paper,
  Alert,
  Snackbar
} from '@mui/material';
import { styled } from '@mui/material/styles';

// Styled components
const StyledCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
}));

const ModeCard = styled(Paper)(({ theme, selected }) => ({
  padding: theme.spacing(2),
  cursor: 'pointer',
  border: selected ? `2px solid ${theme.palette.primary.main}` : '2px solid transparent',
  backgroundColor: selected ? 'rgba(94, 53, 177, 0.05)' : theme.palette.background.paper,
  transition: 'all 0.2s ease',
  '&:hover': {
    borderColor: theme.palette.primary.light
  }
}));

const SliderContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2, 0)
}));

const PhotoModerationSettings = () => {
  // State for settings
  const [settings, setSettings] = useState({
    operationMode: 0,
    automationPercentage: 0,
    autoApprovalConfidence: 95,
    autoRejectionConfidence: 98,
    requireFaceDetection: true,
    allowMultipleFaces: false,
    minFaceSize: 15,
    rejectExplicitContent: true,
    rejectViolentContent: true,
    flagSuggestiveContent: true,
    minResolution: 400
  });
  
  // UI state
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [stats, setStats] = useState(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  
  // Operation mode descriptions
  const operationModes = [
    { 
      value: 0, 
      label: 'Manual Only', 
      description: 'All photos require manual review by admins. AI is disabled.' 
    },
    { 
      value: 1, 
      label: 'Shadow Mode', 
      description: 'AI analyzes photos but all decisions are still manual. Use this to test AI accuracy.' 
    },
    { 
      value: 2, 
      label: 'Limited Automation', 
      description: 'AI handles clear cases automatically. Control the percentage below.' 
    },
    { 
      value: 3, 
      label: 'Full Automation', 
      description: 'AI handles most cases automatically with customizable thresholds.' 
    }
  ];
  
  // Fetch current settings
  const fetchSettings = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/admin/photo-moderation/settings');
      if (response.data.success && response.data.settings) {
        setSettings(response.data.settings);
      }
    } catch (error) {
      console.error('Error fetching moderation settings:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load moderation settings',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch moderation stats
  const fetchStats = async () => {
    try {
      const response = await axios.get('/api/admin/photo-moderation/stats');
      if (response.data.success) {
        setStats(response.data.stats);
      }
    } catch (error) {
      console.error('Error fetching moderation stats:', error);
    }
  };
  
  // Save settings
  const saveSettings = async () => {
    setSaving(true);
    try {
      const response = await axios.put('/api/admin/photo-moderation/settings', {
        settings
      });
      
      if (response.data.success) {
        setSnackbar({
          open: true,
          message: 'Moderation settings saved successfully',
          severity: 'success'
        });
      }
    } catch (error) {
      console.error('Error saving moderation settings:', error);
      setSnackbar({
        open: true,
        message: 'Failed to save moderation settings',
        severity: 'error'
      });
    } finally {
      setSaving(false);
    }
  };
  
  // Handle input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : 
              type === 'number' ? parseFloat(value) :
              parseInt(value)
    }));
  };
  
  // Handle slider changes
  const handleSliderChange = (name) => (e, value) => {
    setSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Handle operation mode change
  const handleModeChange = (mode) => {
    setSettings(prev => ({
      ...prev,
      operationMode: mode
    }));
  };
  
  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar(prev => ({
      ...prev,
      open: false
    }));
  };
  
  // Load data on component mount
  useEffect(() => {
    fetchSettings();
    fetchStats();
  }, []);
  
  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="400px">
        <CircularProgress />
      </Box>
    );
  }
  
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Photo Moderation Settings
      </Typography>
      
      {stats && (
        <StyledCard>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Moderation Statistics
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={6} sm={3}>
                <Typography variant="body2" color="textSecondary">
                  Total Photos
                </Typography>
                <Typography variant="h6">
                  {stats.total}
                </Typography>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Typography variant="body2" color="textSecondary">
                  Pending Review
                </Typography>
                <Typography variant="h6">
                  {stats.pending.count} ({Math.round(stats.pending.percentage)}%)
                </Typography>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Typography variant="body2" color="textSecondary">
                  Approved
                </Typography>
                <Typography variant="h6">
                  {stats.approved.count} ({Math.round(stats.approved.percentage)}%)
                </Typography>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Typography variant="body2" color="textSecondary">
                  Rejected
                </Typography>
                <Typography variant="h6">
                  {stats.rejected.count} ({Math.round(stats.rejected.percentage)}%)
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </StyledCard>
      )}
      
      <StyledCard>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Operation Mode
          </Typography>
          <Typography variant="body2" color="textSecondary" paragraph>
            Control how the AI moderation system operates. Start with Manual or Shadow mode and gradually move to automation as you build confidence in the system.
          </Typography>
          
          <Grid container spacing={2}>
            {operationModes.map(mode => (
              <Grid item xs={12} sm={6} md={3} key={mode.value}>
                <ModeCard 
                  selected={settings.operationMode === mode.value}
                  onClick={() => handleModeChange(mode.value)}
                >
                  <Box display="flex" alignItems="center" mb={1}>
                    <Radio
                      checked={settings.operationMode === mode.value}
                      onChange={() => handleModeChange(mode.value)}
                      value={mode.value}
                      name="operation-mode-radio"
                      size="small"
                    />
                    <Typography variant="subtitle1">
                      {mode.label}
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="textSecondary">
                    {mode.description}
                  </Typography>
                </ModeCard>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </StyledCard>
      
      {settings.operationMode === 2 && (
        <StyledCard>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Automation Percentage
            </Typography>
            <Typography variant="body2" color="textSecondary" paragraph>
              In Limited Automation mode, control what percentage of uploads are processed by AI.
            </Typography>
            
            <SliderContainer>
              <Typography gutterBottom>
                Percentage of photos to process with AI: {settings.automationPercentage}%
              </Typography>
              <Slider
                value={settings.automationPercentage}
                onChange={handleSliderChange('automationPercentage')}
                aria-labelledby="automation-percentage-slider"
                valueLabelDisplay="auto"
                step={10}
                marks
                min={0}
                max={100}
              />
            </SliderContainer>
          </CardContent>
        </StyledCard>
      )}
      
      {(settings.operationMode === 2 || settings.operationMode === 3) && (
        <>
          <StyledCard>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Confidence Thresholds
              </Typography>
              <Typography variant="body2" color="textSecondary" paragraph>
                Set how confident the AI must be to automatically approve or reject photos.
              </Typography>
              
              <SliderContainer>
                <Typography gutterBottom>
                  Auto-Approval Threshold: {settings.autoApprovalConfidence}%
                </Typography>
                <Slider
                  value={settings.autoApprovalConfidence}
                  onChange={handleSliderChange('autoApprovalConfidence')}
                  aria-labelledby="auto-approval-slider"
                  valueLabelDisplay="auto"
                  step={1}
                  min={50}
                  max={99}
                />
              </SliderContainer>
              
              <SliderContainer>
                <Typography gutterBottom>
                  Auto-Rejection Threshold: {settings.autoRejectionConfidence}%
                </Typography>
                <Slider
                  value={settings.autoRejectionConfidence}
                  onChange={handleSliderChange('autoRejectionConfidence')}
                  aria-labelledby="auto-rejection-slider"
                  valueLabelDisplay="auto"
                  step={1}
                  min={50}
                  max={99}
                />
              </SliderContainer>
            </CardContent>
          </StyledCard>
          
          <StyledCard>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Content Detection Settings
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.requireFaceDetection}
                        onChange={handleChange}
                        name="requireFaceDetection"
                        color="primary"
                      />
                    }
                    label="Require face detection"
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.allowMultipleFaces}
                        onChange={handleChange}
                        name="allowMultipleFaces"
                        color="primary"
                        disabled={!settings.requireFaceDetection}
                      />
                    }
                    label="Allow multiple faces in photos"
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.rejectExplicitContent}
                        onChange={handleChange}
                        name="rejectExplicitContent"
                        color="primary"
                      />
                    }
                    label="Reject explicit content"
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.rejectViolentContent}
                        onChange={handleChange}
                        name="rejectViolentContent"
                        color="primary"
                      />
                    }
                    label="Reject violent content"
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.flagSuggestiveContent}
                        onChange={handleChange}
                        name="flagSuggestiveContent"
                        color="primary"
                      />
                    }
                    label="Flag suggestive content for review"
                  />
                </Grid>
              </Grid>
              
              {settings.requireFaceDetection && (
                <SliderContainer>
                  <Typography gutterBottom>
                    Minimum face size: {settings.minFaceSize}% of image
                  </Typography>
                  <Slider
                    value={settings.minFaceSize}
                    onChange={handleSliderChange('minFaceSize')}
                    aria-labelledby="min-face-size-slider"
                    valueLabelDisplay="auto"
                    step={1}
                    min={5}
                    max={30}
                  />
                </SliderContainer>
              )}
              
              <Box mt={2}>
                <Typography gutterBottom>
                  Minimum resolution (pixels):
                </Typography>
                <FormControl variant="outlined" size="small">
                  <input
                    type="number"
                    name="minResolution"
                    value={settings.minResolution}
                    onChange={handleChange}
                    min={200}
                    max={1000}
                    step={50}
                    style={{ width: '100px', padding: '8px' }}
                  />
                </FormControl>
              </Box>
            </CardContent>
          </StyledCard>
        </>
      )}
      
      <Box display="flex" justifyContent="flex-end" mt={3}>
        <Button
          variant="outlined"
          color="secondary"
          onClick={fetchSettings}
          disabled={loading || saving}
          sx={{ mr: 2 }}
        >
          Reset
        </Button>
        
        <Button
          variant="contained"
          color="primary"
          onClick={saveSettings}
          disabled={saving}
          startIcon={saving && <CircularProgress size={20} color="inherit" />}
        >
          {saving ? 'Saving...' : 'Save Settings'}
        </Button>
      </Box>
      
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default PhotoModerationSettings;
