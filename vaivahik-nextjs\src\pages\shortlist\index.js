import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  CardMedia,
  Avatar,
  Button,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  Paper,
  Tooltip,
  Menu,
  MenuItem
} from '@mui/material';
import {
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  Visibility as ViewIcon,
  Message as MessageIcon,
  Person as PersonIcon,
  Favorite as FavoriteIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Share as ShareIcon,
  Note as NoteIcon,
  School as SchoolIcon,
  Work as WorkIcon
} from '@mui/icons-material';
import { format } from 'date-fns';

export default function ShortlistPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [shortlist, setShortlist] = useState([]);
  const [selectedProfile, setSelectedProfile] = useState(null);
  const [noteDialog, setNoteDialog] = useState(false);
  const [note, setNote] = useState('');
  const [actionLoading, setActionLoading] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedProfileId, setSelectedProfileId] = useState(null);

  useEffect(() => {
    fetchShortlist();
  }, []);

  const fetchShortlist = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/user/shortlist', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setShortlist(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching shortlist:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveFromShortlist = async (profileId) => {
    setActionLoading(true);
    try {
      const response = await fetch(`/api/user/shortlist/${profileId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        setShortlist(shortlist.filter(item => item.profile.id !== profileId));
      }
    } catch (error) {
      console.error('Error removing from shortlist:', error);
    } finally {
      setActionLoading(false);
      setAnchorEl(null);
    }
  };

  const handleAddNote = async (profileId, noteText) => {
    setActionLoading(true);
    try {
      const response = await fetch(`/api/user/shortlist/${profileId}/note`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ note: noteText })
      });

      if (response.ok) {
        await fetchShortlist(); // Refresh the list
        setNoteDialog(false);
        setNote('');
        setSelectedProfile(null);
      }
    } catch (error) {
      console.error('Error adding note:', error);
    } finally {
      setActionLoading(false);
    }
  };

  const handleSendInterest = async (profileId) => {
    setActionLoading(true);
    try {
      const response = await fetch(`/api/user/interests/send`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          targetUserId: profileId,
          message: 'I found your profile interesting and would like to connect.'
        })
      });

      if (response.ok) {
        // Update the shortlist item to reflect interest sent
        setShortlist(shortlist.map(item => 
          item.profile.id === profileId 
            ? { ...item, interestSent: true }
            : item
        ));
      }
    } catch (error) {
      console.error('Error sending interest:', error);
    } finally {
      setActionLoading(false);
      setAnchorEl(null);
    }
  };

  const handleViewProfile = (userId) => {
    router.push(`/profile/${userId}`);
  };

  const handleMenuOpen = (event, profileId) => {
    setAnchorEl(event.currentTarget);
    setSelectedProfileId(profileId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedProfileId(null);
  };

  const ShortlistCard = ({ item }) => (
    <Card sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      borderRadius: 3,
      overflow: 'hidden',
      transition: 'all 0.3s ease',
      '&:hover': {
        transform: 'translateY(-4px)',
        boxShadow: '0 12px 24px rgba(0,0,0,0.15)'
      }
    }}>
      {/* Profile Image Header */}
      <Box sx={{ position: 'relative', height: 200 }}>
        <CardMedia
          component="img"
          height="200"
          image={item.profile?.profilePicture || '/default-avatar.png'}
          alt={item.profile?.firstName}
          sx={{ objectFit: 'cover' }}
        />

        {/* Compatibility Badge */}
        {item.profile?.compatibility && (
          <Box sx={{
            position: 'absolute',
            top: 12,
            left: 12,
            background: `linear-gradient(135deg,
              ${item.profile.compatibility >= 90 ? '#4CAF50' :
                item.profile.compatibility >= 80 ? '#FF9800' : '#FF5722'},
              rgba(255,255,255,0.2))`,
            color: 'white',
            padding: '6px 12px',
            borderRadius: 2,
            fontWeight: 600,
            fontSize: '0.8rem'
          }}>
            {item.profile.compatibility}% Match
          </Box>
        )}

        {/* Menu Button */}
        <IconButton
          onClick={(e) => handleMenuOpen(e, item.profile?.id)}
          sx={{
            position: 'absolute',
            top: 12,
            right: 12,
            background: 'rgba(255,255,255,0.9)',
            '&:hover': { background: 'white' }
          }}
        >
          <MoreVertIcon />
        </IconButton>

        {/* Interest Status */}
        {item.interestSent && (
          <Box sx={{
            position: 'absolute',
            bottom: 12,
            right: 12,
            background: '#4CAF50',
            color: 'white',
            padding: '4px 8px',
            borderRadius: 1,
            fontSize: '0.7rem',
            fontWeight: 600
          }}>
            Interest Sent ✓
          </Box>
        )}
      </Box>

      <CardContent sx={{ flexGrow: 1, p: 3 }}>
        {/* Basic Info */}
        <Typography variant="h6" fontWeight="600" gutterBottom>
          {item.profile?.firstName} {item.profile?.lastName}
        </Typography>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          {item.profile?.age} years • {item.profile?.location}
        </Typography>

        {/* Details */}
        <Box sx={{ mt: 2, mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            <SchoolIcon sx={{ fontSize: 16, color: '#666' }} />
            <Typography variant="body2" color="text.secondary">
              {item.profile?.education}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <WorkIcon sx={{ fontSize: 16, color: '#666' }} />
            <Typography variant="body2" color="text.secondary">
              {item.profile?.occupation}
            </Typography>
          </Box>
        </Box>

        {/* Personal Note */}
        {item.note && (
          <Paper sx={{
            mt: 2,
            p: 2,
            background: 'linear-gradient(135deg, #e3f2fd, #f3e5f5)',
            border: '1px solid #e1bee7',
            borderRadius: 2
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <NoteIcon sx={{ fontSize: 16, color: '#9c27b0' }} />
              <Typography variant="caption" fontWeight="600" color="primary">
                Your Note:
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ fontStyle: 'italic', color: '#333' }}>
              {item.note}
            </Typography>
          </Paper>
        )}

        {/* Added Date */}
        <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block' }}>
          <ScheduleIcon sx={{ fontSize: 12, mr: 0.5, verticalAlign: 'middle' }} />
          Added on {format(new Date(item.createdAt), 'MMM dd, yyyy')}
        </Typography>
      </CardContent>

      <CardActions sx={{ p: 3, pt: 0, gap: 1 }}>
        <Button
          size="small"
          variant="outlined"
          startIcon={<ViewIcon />}
          onClick={() => handleViewProfile(item.profile?.id)}
          fullWidth
          sx={{ borderRadius: 2 }}
        >
          View Profile
        </Button>

        {!item.interestSent ? (
          <Button
            size="small"
            variant="contained"
            startIcon={<FavoriteIcon />}
            onClick={() => handleSendInterest(item.profile?.id)}
            disabled={actionLoading}
            fullWidth
            sx={{
              borderRadius: 2,
              background: 'linear-gradient(135deg, #e91e63, #c2185b)',
              '&:hover': {
                background: 'linear-gradient(135deg, #c2185b, #ad1457)'
              }
            }}
          >
            Send Interest
          </Button>
        ) : (
          <Button
            size="small"
            variant="contained"
            startIcon={<MessageIcon />}
            onClick={() => router.push(`/messages?user=${item.profile?.id}`)}
            fullWidth
            sx={{
              borderRadius: 2,
              background: 'linear-gradient(135deg, #2196F3, #1976D2)',
              '&:hover': {
                background: 'linear-gradient(135deg, #1976D2, #1565C0)'
              }
            }}
          >
            Message
          </Button>
        )}
      </CardActions>
    </Card>
  );

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
        <CircularProgress />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading your shortlist...
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
        <BookmarkIcon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
        <Typography variant="h4">
          My Shortlist ({shortlist.length})
        </Typography>
      </Box>

      {shortlist.length > 0 ? (
        <Grid container spacing={3}>
          {shortlist.map((item) => (
            <Grid item xs={12} sm={6} md={4} key={item.id}>
              <ShortlistCard item={item} />
            </Grid>
          ))}
        </Grid>
      ) : (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <BookmarkBorderIcon sx={{ fontSize: 64, color: 'grey.400', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            Your shortlist is empty
          </Typography>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Start browsing profiles and add interesting ones to your shortlist for easy access later.
          </Typography>
          <Button
            variant="contained"
            sx={{ mt: 2 }}
            onClick={() => router.push('/search')}
          >
            Browse Profiles
          </Button>
        </Paper>
      )}

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem
          onClick={() => {
            setSelectedProfile(shortlist.find(item => item.profile?.id === selectedProfileId));
            setNote(shortlist.find(item => item.profile?.id === selectedProfileId)?.note || '');
            setNoteDialog(true);
            handleMenuClose();
          }}
        >
          <NoteIcon sx={{ mr: 1 }} />
          {shortlist.find(item => item.profile?.id === selectedProfileId)?.note ? 'Edit Note' : 'Add Note'}
        </MenuItem>
        <MenuItem
          onClick={() => {
            handleRemoveFromShortlist(selectedProfileId);
          }}
          sx={{ color: 'error.main' }}
        >
          <DeleteIcon sx={{ mr: 1 }} />
          Remove from Shortlist
        </MenuItem>
      </Menu>

      {/* Note Dialog */}
      <Dialog
        open={noteDialog}
        onClose={() => setNoteDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Add Note for {selectedProfile?.profile?.firstName}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Add a personal note to remember why you shortlisted this profile:
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={3}
            placeholder="e.g., Great personality, similar interests, family background matches..."
            value={note}
            onChange={(e) => setNote(e.target.value)}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNoteDialog(false)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={() => handleAddNote(selectedProfile?.profile?.id, note)}
            disabled={actionLoading}
          >
            {actionLoading ? <CircularProgress size={20} /> : 'Save Note'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
