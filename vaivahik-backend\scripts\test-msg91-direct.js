/**
 * Direct MSG91 API Test
 * 
 * This script tests MSG91 API directly with different authentication methods
 * to identify the correct format.
 */

const axios = require('axios');
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });

const phoneNumber = process.argv[2] || '9123456789';

console.log('=== MSG91 Direct API Test ===');
console.log(`Testing with phone number: ${phoneNumber}`);
console.log(`API Key: ${process.env.MSG91_API_KEY}`);
console.log(`Sender ID: ${process.env.MSG91_SENDER_ID}`);
console.log(`Template ID: ${process.env.MSG91_DLT_TEMPLATE_ID}`);

async function testMethod1() {
  console.log('\n=== Method 1: sendotp.php with authkey in body ===');
  
  const payload = {
    mobile: phoneNumber,
    authkey: process.env.MSG91_API_KEY,
    otp: '1234',
    otp_expiry: 15,
    otp_length: '4',
    template_id: process.env.MSG91_DLT_TEMPLATE_ID,
    sender: process.env.MSG91_SENDER_ID,
    var1: '1234',
    pe_id: process.env.MSG91_DLT_PE_ID
  };

  try {
    const response = await axios.post('https://control.msg91.com/api/sendotp.php', payload, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('Error:', error.response?.status, error.response?.data || error.message);
  }
}

async function testMethod2() {
  console.log('\n=== Method 2: v5/otp/send with authkey in headers ===');
  
  const payload = {
    mobile: phoneNumber,
    otp: '1234',
    otp_expiry: 15,
    otp_length: '4',
    template_id: process.env.MSG91_DLT_TEMPLATE_ID,
    sender: process.env.MSG91_SENDER_ID,
    var1: '1234',
    pe_id: process.env.MSG91_DLT_PE_ID
  };

  try {
    const response = await axios.post('https://control.msg91.com/api/v5/otp/send', payload, {
      headers: {
        'Content-Type': 'application/json',
        'authkey': process.env.MSG91_API_KEY
      }
    });
    
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('Error:', error.response?.status, error.response?.data || error.message);
  }
}

async function testMethod3() {
  console.log('\n=== Method 3: v5/otp/send with authkey in body ===');
  
  const payload = {
    mobile: phoneNumber,
    authkey: process.env.MSG91_API_KEY,
    otp: '1234',
    otp_expiry: 15,
    otp_length: '4',
    template_id: process.env.MSG91_DLT_TEMPLATE_ID,
    sender: process.env.MSG91_SENDER_ID,
    var1: '1234',
    pe_id: process.env.MSG91_DLT_PE_ID
  };

  try {
    const response = await axios.post('https://control.msg91.com/api/v5/otp/send', payload, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('Error:', error.response?.status, error.response?.data || error.message);
  }
}

async function testMethod4() {
  console.log('\n=== Method 4: GET request format ===');
  
  const params = new URLSearchParams({
    mobile: phoneNumber,
    authkey: process.env.MSG91_API_KEY,
    otp: '1234',
    otp_expiry: '15',
    otp_length: '4',
    template_id: process.env.MSG91_DLT_TEMPLATE_ID,
    sender: process.env.MSG91_SENDER_ID,
    var1: '1234',
    pe_id: process.env.MSG91_DLT_PE_ID
  });

  try {
    const response = await axios.get(`https://control.msg91.com/api/sendotp.php?${params.toString()}`);
    
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('Error:', error.response?.status, error.response?.data || error.message);
  }
}

async function runAllTests() {
  await testMethod1();
  await testMethod2();
  await testMethod3();
  await testMethod4();
}

runAllTests().catch(console.error);
