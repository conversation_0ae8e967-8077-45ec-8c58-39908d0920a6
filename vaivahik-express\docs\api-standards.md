# Vaivahik API Standards

This document outlines the standards and conventions for the Vaivahik API. Following these guidelines ensures consistency, maintainability, and a better developer experience.

## Table of Contents

1. [API Structure](#api-structure)
2. [Endpoint Naming](#endpoint-naming)
3. [Request Methods](#request-methods)
4. [Response Format](#response-format)
5. [<PERSON><PERSON><PERSON>ling](#error-handling)
6. [Validation](#validation)
7. [Authentication](#authentication)
8. [Versioning](#versioning)
9. [Pagination](#pagination)
10. [Filtering and Sorting](#filtering-and-sorting)
11. [Rate Limiting](#rate-limiting)
12. [Documentation](#documentation)

## API Structure

The API follows a RESTful architecture with a clear separation of concerns:

```
/api/v1/resource-name
```

- All API endpoints are prefixed with `/api`
- Version is included in the URL path (`/v1`)
- Resource names are plural and kebab-case (e.g., `user-profiles`)

## Endpoint Naming

Endpoints should follow these conventions:

- **Collection endpoints**: `/api/v1/users`
- **Specific resource**: `/api/v1/users/:id`
- **Sub-resources**: `/api/v1/users/:id/photos`
- **Actions**: `/api/v1/users/:id/verify`

## Request Methods

Use appropriate HTTP methods for different operations:

- **GET**: Retrieve resources
- **POST**: Create new resources
- **PUT**: Update a resource completely
- **PATCH**: Update a resource partially
- **DELETE**: Remove a resource

## Response Format

All API responses follow a consistent format:

### Success Response

```json
{
  "success": true,
  "message": "Operation successful",
  "data": { ... },
  "timestamp": "2023-07-25T12:34:56.789Z",
  "meta": { ... }  // Optional metadata (pagination, etc.)
}
```

### Error Response

```json
{
  "success": false,
  "message": "Error message",
  "errors": { ... },  // Optional detailed errors
  "errorCode": "ERROR_CODE",  // Optional error code
  "timestamp": "2023-07-25T12:34:56.789Z"
}
```

## Error Handling

The API uses standard HTTP status codes:

- **2xx**: Success
  - 200: OK
  - 201: Created
  - 204: No Content
- **4xx**: Client errors
  - 400: Bad Request
  - 401: Unauthorized
  - 403: Forbidden
  - 404: Not Found
  - 409: Conflict
  - 422: Unprocessable Entity
  - 429: Too Many Requests
- **5xx**: Server errors
  - 500: Internal Server Error
  - 503: Service Unavailable

## Validation

Input validation is performed using express-validator:

- All user inputs must be validated
- Validation rules are defined in route files
- Validation errors return 422 Unprocessable Entity status
- Validation errors include field-specific error messages

Example validation:

```javascript
[
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email')
    .normalizeEmail(),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
]
```

## Authentication

Authentication is handled using JWT (JSON Web Tokens):

- Access tokens are short-lived (1 hour)
- Refresh tokens are long-lived (7 days)
- Tokens are stored in HTTP-only cookies for web clients
- Mobile clients can use Authorization header with Bearer token

Protected routes use the `authenticateToken` middleware:

```javascript
router.get('/profile', authenticateToken, userController.getProfile);
```

## Versioning

API versioning is included in the URL path:

```
/api/v1/users
```

When making breaking changes, increment the version number:

```
/api/v2/users
```

## Pagination

Collection endpoints support pagination with consistent parameters:

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)

Response includes pagination metadata:

```json
{
  "success": true,
  "data": [...],
  "meta": {
    "pagination": {
      "page": 2,
      "limit": 10,
      "totalItems": 57,
      "totalPages": 6,
      "hasNextPage": true,
      "hasPrevPage": true
    }
  }
}
```

## Filtering and Sorting

Collection endpoints support filtering and sorting:

- **Filtering**: Use query parameters with field names
  - `/api/v1/users?gender=MALE&ageMin=25&ageMax=35`

- **Sorting**: Use `sortBy` and `sortOrder` parameters
  - `/api/v1/users?sortBy=createdAt&sortOrder=desc`

## Rate Limiting

API endpoints are protected by rate limiting:

- Default limit: 100 requests per 15 minutes per IP
- Authentication endpoints: 10 requests per minute per IP
- Rate limit headers are included in responses

## Documentation

API documentation is available at `/api/docs` and includes:

- Endpoint descriptions
- Request parameters
- Request body schemas
- Response schemas
- Authentication requirements
- Example requests and responses

## File Structure

The API follows a structured organization:

```
src/
├── controllers/       # Business logic
├── middleware/        # Custom middleware
├── models/            # Data models
├── routes/            # Route definitions
├── services/          # Business services
├── utils/             # Utility functions
└── validators/        # Input validation
```

## Controller Pattern

Controllers follow a consistent pattern:

```javascript
/**
 * Get all users with pagination
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 */
const getAllUsers = async (req, res, next) => {
  try {
    // Business logic
    
    // Return success response
    return apiResponse.success(res, 'Users retrieved successfully', data);
  } catch (error) {
    // Pass error to global error handler
    return next(error);
  }
};
```

## Middleware Pattern

Middleware follows a consistent pattern:

```javascript
/**
 * Authentication middleware
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 */
const authenticateToken = (req, res, next) => {
  try {
    // Middleware logic
    
    // Call next middleware
    next();
  } catch (error) {
    // Handle error
    return apiResponse.unauthorized(res, 'Invalid token');
  }
};
```

## Route Pattern

Routes follow a consistent pattern:

```javascript
/**
 * @route   GET /api/v1/users
 * @desc    Get all users
 * @access  Private/Admin
 */
router.get(
  '/',
  authenticateToken,
  [
    // Validation rules
  ],
  validate,
  userController.getAllUsers
);
```
