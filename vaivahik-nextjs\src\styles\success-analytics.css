/* Success Analytics Page Styles */

.success-analytics-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  font-size: 1.8rem;
  margin-bottom: 5px;
  color: var(--text-dark);
}

.page-header p {
  color: var(--text-muted);
}

.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 200px;
}

.filter-group label {
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--text-dark);
}

.filter-group select {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--bg-white);
  color: var(--text-dark);
}

.refresh-btn {
  padding: 8px 16px;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  align-self: flex-end;
  margin-left: auto;
}

.refresh-btn:hover {
  background-color: var(--primary-dark);
}

.refresh-btn:disabled {
  background-color: var(--text-muted);
  cursor: not-allowed;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.summary-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.metric-card {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 15px;
  box-shadow: var(--shadow-sm);
  text-align: center;
}

.metric-card h3 {
  font-size: 0.9rem;
  color: var(--text-muted);
  margin-bottom: 10px;
}

.metric-value {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-dark);
}

.metric-value.positive {
  color: var(--success);
}

.metric-value.negative {
  color: var(--danger);
}

.charts-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.chart-card {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 20px;
  box-shadow: var(--shadow-sm);
}

.chart-card h3 {
  font-size: 1.1rem;
  margin-bottom: 15px;
  color: var(--text-dark);
}

.chart-container {
  height: 300px;
  position: relative;
}

.insights-section {
  margin-top: 30px;
}

.insights-section h2 {
  font-size: 1.4rem;
  margin-bottom: 15px;
  color: var(--text-dark);
}

.insights-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.insight-card {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 20px;
  box-shadow: var(--shadow-sm);
  display: flex;
  gap: 15px;
}

.insight-icon {
  font-size: 2rem;
  color: var(--primary);
}

.insight-content h3 {
  font-size: 1.1rem;
  margin-bottom: 10px;
  color: var(--text-dark);
}

.insight-content p {
  color: var(--text-muted);
  line-height: 1.5;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  text-align: center;
}

.empty-state p {
  margin-bottom: 15px;
  color: var(--text-muted);
}

.btn-primary {
  padding: 8px 16px;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

/* Dark mode styles */
body.dark-mode .metric-card,
body.dark-mode .chart-card,
body.dark-mode .insight-card,
body.dark-mode .filters-container {
  background-color: var(--bg-dark-light);
}

body.dark-mode .filter-group select {
  background-color: var(--bg-dark);
  border-color: var(--border-color);
  color: var(--text-dark);
}

body.dark-mode .loading-spinner {
  border-color: rgba(255, 255, 255, 0.1);
  border-top-color: var(--primary);
}

/* Responsive styles */
@media (max-width: 768px) {
  .charts-container {
    grid-template-columns: 1fr;
  }
  
  .summary-metrics {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
  
  .insights-container {
    grid-template-columns: 1fr;
  }
  
  .filter-group {
    min-width: 100%;
  }
  
  .filters-container {
    flex-direction: column;
  }
  
  .refresh-btn {
    width: 100%;
    margin-top: 10px;
  }
}
