// API endpoint for recent activity on the admin dashboard
import { handleApiError } from '@/utils/errorHandler';
import { withAuth } from '@/utils/authHandler';

async function handler(req, res) {
  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getRecentActivity(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    return handleApiError(error, res, 'Recent activity API');
  }
}

// GET /api/admin/dashboard/recent-activity
async function getRecentActivity(req, res) {
  try {
    // Get query parameters
    const { limit = 10, types = '' } = req.query;

    // Mock data for activities
    const allActivities = [
      { id: 1, type: 'registration', user: '<PERSON><PERSON>', userId: 1, details: 'Registered as a new user', time: '2 hours ago', icon: '👤', actionable: true, actionText: 'View Profile' },
      { id: 2, type: 'subscription', user: '<PERSON><PERSON><PERSON>', userId: 4, details: 'Upgraded to Premium Plan', time: '5 hours ago', icon: '💎', actionable: true, actionText: 'View Subscription' },
      { id: 3, type: 'verification', user: 'Amit Kumar', userId: 3, details: 'Submitted verification documents', time: '1 day ago', icon: '📄', actionable: true, actionText: 'Review Documents' },
      { id: 4, type: 'report', user: 'Priya Patel', userId: 2, reportId: 1, details: 'Reported a profile for inappropriate content', time: '2 days ago', icon: '🚩', actionable: true, actionText: 'Review Report' },
      { id: 5, type: 'match', user: 'Vikram Singh', userId: 5, matchId: 1, details: 'Found a match with Neha Gupta', time: '3 days ago', icon: '💑', actionable: true, actionText: 'View Match' },
      { id: 6, type: 'registration', user: 'Ananya Desai', userId: 6, details: 'Registered as a new user', time: '3 days ago', icon: '👤', actionable: true, actionText: 'View Profile' },
      { id: 7, type: 'subscription', user: 'Rajesh Khanna', userId: 7, details: 'Upgraded to Premium Plan', time: '4 days ago', icon: '💎', actionable: true, actionText: 'View Subscription' },
      { id: 8, type: 'verification', user: 'Meera Joshi', userId: 8, details: 'Submitted verification documents', time: '5 days ago', icon: '📄', actionable: true, actionText: 'Review Documents' },
      { id: 9, type: 'report', user: 'Suresh Patel', userId: 9, reportId: 2, details: 'Reported a profile for fake information', time: '6 days ago', icon: '🚩', actionable: true, actionText: 'Review Report' },
      { id: 10, type: 'match', user: 'Kavita Sharma', userId: 10, matchId: 2, details: 'Found a match with Amit Kumar', time: '1 week ago', icon: '💑', actionable: true, actionText: 'View Match' }
    ];

    // Filter activities based on the types parameter
    let filteredActivities = [...allActivities];
    if (types) {
      const typesList = types.split(',');
      filteredActivities = allActivities.filter(activity => typesList.includes(activity.type));
    }

    // Limit the number of activities
    const limitedActivities = filteredActivities.slice(0, parseInt(limit));

    // Return the activities
    return res.status(200).json({
      success: true,
      activities: limitedActivities
    });
  } catch (error) {
    return handleApiError(error, res, 'Get recent activity');
  }
}

// Export the handler with authentication middleware
export default withAuth(handler, 'ADMIN');
