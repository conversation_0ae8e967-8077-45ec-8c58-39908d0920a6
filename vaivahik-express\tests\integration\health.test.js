/**
 * Health Endpoint Tests
 * 
 * This file contains tests for the health endpoint.
 */

const request = require('supertest');
const express = require('express');
const router = require('../../src/routes/index');

// Create Express app for testing
const app = express();
app.use('/api', router);

describe('Health Endpoint', () => {
  it('should return 200 OK with status information', async () => {
    const response = await request(app).get('/api/health');
    
    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty('status', 'ok');
    expect(response.body).toHaveProperty('timestamp');
    expect(response.body).toHaveProperty('version');
    expect(response.body).toHaveProperty('environment');
  });
});
