# Script to kill all Node.js processes and restart the server
# This is useful when you have a port conflict

Write-Host "Stopping all Node.js processes..."
Get-Process | Where-Object { $_.ProcessName -eq "node" -or $_.ProcessName -eq "nodemon" } | ForEach-Object {
    Write-Host "Killing process $($_.Id) ($($_.ProcessName))"
    Stop-Process -Id $_.Id -Force
}

Write-Host "All Node.js processes have been stopped."
Write-Host "Waiting 2 seconds before starting the server..."
Start-Sleep -Seconds 2

Write-Host "Starting the server..."
npm run dev
