// API endpoint for rejecting verification requests
import { generateMockVerifications } from '@/utils/mockData';

export default function handler(req, res) {
  // Set proper content type header
  res.setHeader('Content-Type', 'application/json');

  try {
    // Only allow POST method
    if (req.method !== 'POST') {
      return res.status(405).json({
        success: false,
        message: 'Method not allowed'
      });
    }

    // Get data from request body
    const { userId, reason } = req.body;

    // Validate required fields
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }

    if (!reason) {
      return res.status(400).json({
        success: false,
        message: 'Rejection reason is required'
      });
    }

    // In a real implementation, this would update the database
    // For now, we'll just return a success response
    return res.status(200).json({
      success: true,
      message: 'Verification rejected successfully',
      data: {
        userId,
        reason,
        status: 'REJECTED',
        rejectedAt: new Date().toISOString(),
        rejectedBy: 'Admin User'
      }
    });
  } catch (error) {
    console.error('Error rejecting verification:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to reject verification'
    });
  }
}
