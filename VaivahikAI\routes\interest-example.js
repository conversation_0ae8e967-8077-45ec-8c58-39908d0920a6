/**
 * Example of integrating event notifications with interests
 * This is a sample implementation to demonstrate how to integrate notifications
 */
const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { authenticateUser } = require('../middleware/auth');
const eventNotifications = require('../services/notification/event-notifications');
const autoTriggers = require('../services/notification/auto-notification-triggers');

/**
 * @route POST /api/interests/send
 * @desc Send an interest to another user
 * @access Private
 */
router.post('/send', authenticateUser, async (req, res) => {
  try {
    const { recipientId } = req.body;
    const senderId = req.user.id;

    // Don't allow sending interest to self
    if (recipientId === senderId) {
      return res.status(400).json({ message: 'Cannot send interest to yourself' });
    }

    // Check if recipient exists
    const recipient = await prisma.user.findUnique({
      where: { id: recipientId },
      select: { id: true }
    });

    if (!recipient) {
      return res.status(404).json({ message: 'Recipient not found' });
    }

    // Check if interest already exists
    const existingInterest = await prisma.interest.findFirst({
      where: {
        senderId,
        recipientId,
        status: { in: ['PENDING', 'ACCEPTED'] }
      }
    });

    if (existingInterest) {
      return res.status(400).json({
        message: `Interest already ${existingInterest.status === 'PENDING' ? 'sent' : 'accepted'}`
      });
    }

    // Create the interest
    const interest = await prisma.interest.create({
      data: {
        senderId,
        recipientId,
        status: 'PENDING',
        sentAt: new Date()
      }
    });

    // Get sender's information for the notification
    const sender = await prisma.user.findUnique({
      where: { id: senderId },
      select: {
        profile: {
          select: {
            firstName: true,
            lastName: true,
            photos: {
              where: { isMain: true },
              select: { url: true }
            }
          }
        }
      }
    });

    if (sender && sender.profile) {
      // Send notification to recipient
      const senderData = {
        senderId,
        senderName: `${sender.profile.firstName} ${sender.profile.lastName || ''}`.trim(),
        senderPhotoUrl: sender.profile.photos[0]?.url || null,
        interestId: interest.id
      };

      // Use auto-trigger for comprehensive notifications (push + email)
      await autoTriggers.onInterestReceived(recipientId, senderData);
    }

    res.json({ success: true, interest });
  } catch (error) {
    console.error('Error sending interest:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route PUT /api/interests/:id/accept
 * @desc Accept an interest
 * @access Private
 */
router.put('/:id/accept', authenticateUser, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Find the interest
    const interest = await prisma.interest.findUnique({
      where: { id },
      select: { id: true, senderId: true, recipientId: true, status: true }
    });

    // Check if interest exists and belongs to user
    if (!interest) {
      return res.status(404).json({ message: 'Interest not found' });
    }

    if (interest.recipientId !== userId) {
      return res.status(403).json({ message: 'Not authorized to accept this interest' });
    }

    if (interest.status === 'ACCEPTED') {
      return res.status(400).json({ message: 'Interest already accepted' });
    }

    // Update the interest
    const updatedInterest = await prisma.interest.update({
      where: { id },
      data: {
        status: 'ACCEPTED',
        respondedAt: new Date()
      }
    });

    // Get acceptor's information for the notification
    const acceptor = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        profile: {
          select: {
            firstName: true,
            lastName: true,
            photos: {
              where: { isMain: true },
              select: { url: true }
            }
          }
        }
      }
    });

    if (acceptor && acceptor.profile) {
      // Send notification to sender
      const acceptorData = {
        acceptorId: userId,
        acceptorName: `${acceptor.profile.firstName} ${acceptor.profile.lastName || ''}`.trim(),
        acceptorPhotoUrl: acceptor.profile.photos[0]?.url || null
      };

      // Use auto-trigger for comprehensive notifications (push + email)
      await autoTriggers.onInterestAccepted(interest.senderId, acceptorData);
    }

    res.json({ success: true, interest: updatedInterest });
  } catch (error) {
    console.error('Error accepting interest:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
