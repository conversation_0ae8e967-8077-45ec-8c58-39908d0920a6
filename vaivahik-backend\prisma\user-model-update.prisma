model User {
  id            String    @id @default(cuid())
  email         String?   @unique
  phone         String?   @unique
  password      String?
  name          String?
  role          String    @default("USER")
  isActive      Boolean   @default(true) @map("is_active")
  isVerified    Boolean   @default(false) @map("is_verified")
  verifiedAt    DateTime? @map("verified_at")
  lastLogin     DateTime? @map("last_login")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  // Profile relationship
  profile       Profile?

  // Authentication
  emailVerified DateTime? @map("email_verified")
  phoneVerified DateTime? @map("phone_verified")
  image         String?
  accounts      Account[]
  sessions      Session[]

  // Verification documents
  verificationDocuments VerificationDocument[]

  // Subscription and payments
  subscriptions  Subscription[]

  // Photos
  photos        Photo[]

  // Preferences
  preference    Preference?

  // Matches
  matches1      Match[]     @relation("UserMatches1")
  matches2      Match[]     @relation("UserMatches2")

  // Reports
  reports       Report[]    @relation("UserReports")
  reportedBy    Report[]    @relation("UserReported")

  // Notifications
  notifications Notification[]

  // Usage tracking
  profileViews  ProfileView[] @relation("ViewerRelation")
  viewedBy      ProfileView[] @relation("ViewedRelation")
  matchViews    MatchView[]

  // Messages
  sentMessages     Message[] @relation("SentMessages")
  receivedMessages Message[] @relation("ReceivedMessages")

  // Conversation relationships
  conversationsAsUser1 Conversation[] @relation("User1Conversations")
  conversationsAsUser2 Conversation[] @relation("User2Conversations")

  // Behavior tracking relationships
  interactions        UserInteraction[] @relation("UserInteractions")
  targetInteractions  UserInteraction[] @relation("TargetUserInteractions")
  feedback            UserFeedback[]    @relation("UserFeedback")
  targetFeedback      UserFeedback[]    @relation("TargetUserFeedback")
  successStories1     SuccessStory[]    @relation("UserSuccessStories1")
  successStories2     SuccessStory[]    @relation("UserSuccessStories2")
  preferenceHistory   UserPreferenceHistory[]
  matchScores         MatchScore[]      @relation("UserMatchScores")
  targetMatchScores   MatchScore[]      @relation("TargetUserMatchScores")
  
  // Premium features relationships
  biodatas           UserBiodata[]
  spotlights         UserSpotlight[]

  @@map("users")
}
