// src/pages/api/admin/text-moderation/flagged-messages.js
import axios from 'axios';

export default async function handler(req, res) {
  // Get the auth token from the request cookies or headers
  const token = req.cookies?.adminAccessToken || req.headers.authorization?.split(' ')[1];

  if (!token) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  try {
    // Forward the request to the backend API
    const backendUrl = process.env.BACKEND_API_URL || 'http://localhost:8000';
    const endpoint = `${backendUrl}/api/admin/text-moderation/flagged-messages`;
    
    let response;
    
    if (req.method === 'GET') {
      // Add query parameters if they exist
      const queryParams = new URLSearchParams();
      if (req.query.status) queryParams.append('status', req.query.status);
      if (req.query.page) queryParams.append('page', req.query.page);
      if (req.query.limit) queryParams.append('limit', req.query.limit);
      
      const url = `${endpoint}${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
      
      response = await axios({
        method: 'GET',
        url,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000 // 10 second timeout
      });
    } else if (req.method === 'PUT') {
      // For updating message status (approve/reject)
      const messageId = req.query.id;
      if (!messageId) {
        return res.status(400).json({ success: false, message: 'Message ID is required' });
      }
      
      response = await axios({
        method: 'PUT',
        url: `${endpoint}/${messageId}`,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        data: req.body,
        timeout: 10000 // 10 second timeout
      });
    } else {
      return res.status(405).json({ success: false, message: 'Method not allowed' });
    }

    // Return the response from the backend
    return res.status(response.status).json(response.data);
  } catch (error) {
    console.error('Error handling flagged messages request:', error);
    
    // Return the error response from the backend if available
    if (error.response) {
      return res.status(error.response.status).json(error.response.data);
    }
    
    // Handle timeout errors
    if (error.code === 'ECONNABORTED') {
      return res.status(504).json({
        success: false,
        message: 'Request to backend timed out. Please check if the backend server is running.'
      });
    }
    
    // Handle connection errors
    if (error.code === 'ECONNREFUSED') {
      // Fallback to mock data if backend is not available
      if (req.method === 'GET') {
        console.log('Backend not available, returning mock data for flagged messages');
        
        // Generate some mock flagged messages
        const mockMessages = [
          {
            id: '1',
            content: 'This message contains inappropriate content that was flagged by the system.',
            sender: {
              id: 'user1',
              name: 'John Doe',
              profilePic: 'https://via.placeholder.com/50'
            },
            receiver: {
              id: 'user2',
              name: 'Jane Smith',
              profilePic: 'https://via.placeholder.com/50'
            },
            timestamp: new Date(Date.now() - 3600000).toISOString(),
            status: 'PENDING',
            flags: ['PROFANITY', 'CONTACT_INFO'],
            confidence: 0.85
          },
          {
            id: '2',
            content: 'Another message with potentially inappropriate content for review.',
            sender: {
              id: 'user3',
              name: 'Alice Johnson',
              profilePic: 'https://via.placeholder.com/50'
            },
            receiver: {
              id: 'user4',
              name: 'Bob Williams',
              profilePic: 'https://via.placeholder.com/50'
            },
            timestamp: new Date(Date.now() - 7200000).toISOString(),
            status: 'PENDING',
            flags: ['SPAM', 'SUSPICIOUS_LINK'],
            confidence: 0.72
          }
        ];
        
        return res.status(200).json({
          success: true,
          messages: mockMessages,
          pagination: {
            total: 2,
            page: parseInt(req.query.page) || 1,
            limit: parseInt(req.query.limit) || 10,
            pages: 1
          }
        });
      } else if (req.method === 'PUT') {
        console.log('Backend not available, simulating successful update for flagged message');
        return res.status(200).json({
          success: true,
          message: 'Message status updated successfully (mock)'
        });
      }
    }
    
    // Otherwise return a generic error
    return res.status(500).json({
      success: false,
      message: 'Error connecting to backend service: ' + (error.message || 'Unknown error')
    });
  }
}
