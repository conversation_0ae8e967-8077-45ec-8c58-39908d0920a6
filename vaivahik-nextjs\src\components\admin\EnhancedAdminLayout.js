import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import FeatureFlagToggle from './FeatureFlagToggle';
import MockAuthManager from './MockAuthManager';
import { isUsingRealBackend } from '@/utils/featureFlags';

// Collapsible Category Component
const CollapsibleCategory = ({ name, icon, isExpanded, toggleExpand, children, isActive }) => {
  return (
    <div className={`nav-category-container ${isActive ? 'has-active' : ''}`}>
      <div
        className={`nav-category-header ${isExpanded ? 'expanded' : ''}`}
        onClick={toggleExpand}
      >
        <div className="category-icon">{icon}</div>
        <div className="nav-category">{name}</div>
        <div className="expand-icon">{isExpanded ? '▼' : '▶'}</div>
      </div>
      <div className={`nav-category-items ${isExpanded ? 'expanded' : ''}`}>
        {children}
      </div>
    </div>
  );
};

const EnhancedAdminLayout = ({ children, title = 'Admin Dashboard' }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const [adminName, setAdminName] = useState('Admin User');
  const [adminRole, setAdminRole] = useState('Super Admin');
  const router = useRouter();

  // Define categories and their default expanded state
  const categories = [
    { id: 'main', name: 'Main', icon: '🏠' },
    { id: 'user', name: 'User Management', icon: '👥' },
    { id: 'premium', name: 'Premium Features', icon: '💎' },
    { id: 'ai', name: 'AI & Matching', icon: '🧠' },
    { id: 'content', name: 'Content', icon: '📝' },
    { id: 'financial', name: 'Financial', icon: '💰' },
    { id: 'communication', name: 'Communication', icon: '✉️' },
    { id: 'system', name: 'System', icon: '⚙️' },
    { id: 'developer', name: 'Developer Tools', icon: '🛠️' }
  ];

  // State to track expanded categories
  const [expandedCategories, setExpandedCategories] = useState({});

  useEffect(() => {
    // Only run in browser environment
    if (typeof window !== 'undefined') {
      // Check if sidebar should be collapsed on load
      const savedSidebarState = localStorage.getItem('sidebarCollapsed');
      if (savedSidebarState === 'true') {
        setSidebarCollapsed(true);
      }

      // Check if dark mode should be enabled on load
      const savedDarkMode = localStorage.getItem('darkMode');
      if (savedDarkMode === 'true') {
        setDarkMode(true);
        document.body.classList.add('dark-mode');
      }

      // Get admin info from localStorage
      const storedAdminName = localStorage.getItem('adminName');
      const storedAdminRole = localStorage.getItem('adminRole');

      if (storedAdminName) setAdminName(storedAdminName);
      if (storedAdminRole) setAdminRole(storedAdminRole);

      // Load expanded categories from localStorage
      try {
        const savedExpandedCategories = localStorage.getItem('expandedCategories');
        if (savedExpandedCategories) {
          setExpandedCategories(JSON.parse(savedExpandedCategories));
        } else {
          // Default to having main category expanded
          setExpandedCategories({ main: true });
          localStorage.setItem('expandedCategories', JSON.stringify({ main: true }));
        }
      } catch (error) {
        console.error('Error loading expanded categories:', error);
        // Default to having main category expanded
        setExpandedCategories({ main: true });
      }

      // Check authentication
      const token = localStorage.getItem('adminAccessToken');
      if (!token) {
        // Only redirect if we're not already on the login page
        if (!router.pathname.includes('/admin/login')) {
          console.log('No authentication token found, redirecting to login page');
          router.push('/admin/login');
        }
      }
    }
  }, [router]);

  const toggleSidebar = () => {
    const newState = !sidebarCollapsed;
    setSidebarCollapsed(newState);
    if (typeof window !== 'undefined') {
      localStorage.setItem('sidebarCollapsed', newState.toString());
    }
  };

  const toggleDarkMode = () => {
    const newState = !darkMode;
    setDarkMode(newState);
    if (typeof window !== 'undefined') {
      document.body.classList.toggle('dark-mode');
      localStorage.setItem('darkMode', newState.toString());
    }
  };

  const handleLogout = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('adminAccessToken');
      localStorage.removeItem('adminRole');
      localStorage.removeItem('adminName');
      router.push('/admin/login');
    }
  };

  // Function to toggle category expansion
  const toggleCategoryExpansion = (categoryId) => {
    const newExpandedState = {
      ...expandedCategories,
      [categoryId]: !expandedCategories[categoryId]
    };

    setExpandedCategories(newExpandedState);

    // Save to localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('expandedCategories', JSON.stringify(newExpandedState));
    }
  };

  // Function to check if a category contains the active page
  const isCategoryActive = (categoryId) => {
    const path = router.pathname;

    switch (categoryId) {
      case 'main':
        return path === '/admin/dashboard';
      case 'user':
        return path.includes('/admin/users') || path.includes('/admin/verification-queue') || path.includes('/admin/reported-profiles');
      case 'premium':
        return path.includes('/admin/premium-plans') || path.includes('/admin/feature-management') || path.includes('/admin/promotions');
      case 'ai':
        return path.includes('/admin/algorithm-settings') || path.includes('/admin/preference-config') ||
               path.includes('/admin/success-analytics') || path.includes('/admin/ai-phase-management') ||
               path.includes('/admin/mcp-server') || path.includes('/admin/mcp-logs');
      case 'content':
        return path.includes('/admin/photo-moderation') || path.includes('/admin/text-moderation') ||
               path.includes('/admin/success-stories') || path.includes('/admin/blog-posts') ||
               path.includes('/admin/biodata-templates') || path.includes('/admin/spotlight-features');
      case 'financial':
        return path.includes('/admin/subscriptions') || path.includes('/admin/transactions') || path.includes('/admin/revenue-reports');
      case 'communication':
        return path.includes('/admin/notifications') || path.includes('/admin/email-templates') ||
               path.includes('/admin/chat-management') || path.includes('/admin/contact-security') ||
               path.includes('/admin/contact-configuration');
      case 'system':
        return path.includes('/admin/settings') || path.includes('/admin/feature-flags') ||
               path.includes('/admin/admin-users') || path.includes('/admin/security-settings') ||
               path.includes('/admin/privacy-controls') || path.includes('/admin/advanced-analytics') ||
               path.includes('/admin/system-monitoring') || path.includes('/admin/logs-monitoring') ||
               path.includes('/admin/backup-recovery') || path.includes('/admin/sms-configuration') ||
               path.includes('/admin/api-management') || path.includes('/admin/policy-management');
      case 'developer':
        return path.includes('/admin/testing-tools') || path.includes('/api-viewer-enhanced') || path.includes('/api-discovery-enhanced');
      default:
        return false;
    }
  };

  // Auto-expand category if it contains the active page
  useEffect(() => {
    const path = router.pathname;

    // Check which category contains the active page
    let activeCategory = null;
    if (path === '/admin/dashboard') {
      activeCategory = 'main';
    } else if (path.includes('/admin/users') || path.includes('/admin/verification-queue') || path.includes('/admin/reported-profiles')) {
      activeCategory = 'user';
    } else if (path.includes('/admin/premium-plans') || path.includes('/admin/feature-management') || path.includes('/admin/promotions')) {
      activeCategory = 'premium';
    } else if (path.includes('/admin/algorithm-settings') || path.includes('/admin/preference-config') || path.includes('/admin/success-analytics')) {
      activeCategory = 'ai';
    } else if (path.includes('/admin/photo-moderation') || path.includes('/admin/text-moderation') ||
               path.includes('/admin/success-stories') || path.includes('/admin/blog-posts') ||
               path.includes('/admin/biodata-templates') || path.includes('/admin/spotlight-features')) {
      activeCategory = 'content';
    } else if (path.includes('/admin/subscriptions') || path.includes('/admin/transactions') || path.includes('/admin/revenue-reports')) {
      activeCategory = 'financial';
    } else if (path.includes('/admin/notifications') || path.includes('/admin/email-templates')) {
      activeCategory = 'communication';
    } else if (path.includes('/admin/settings') || path.includes('/admin/feature-flags') || path.includes('/admin/admin-users')) {
      activeCategory = 'system';
    } else if (path.includes('/admin/testing-tools') || path.includes('/api-viewer-enhanced') || path.includes('/api-discovery-enhanced')) {
      activeCategory = 'developer';
    }

    // Auto-expand the category if it's not already expanded
    if (activeCategory && !expandedCategories[activeCategory]) {
      const newExpandedState = {
        ...expandedCategories,
        [activeCategory]: true
      };
      setExpandedCategories(newExpandedState);

      // Save to localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('expandedCategories', JSON.stringify(newExpandedState));
      }
    }
  }, [router.pathname, expandedCategories]);

  return (
    <>
      <Head>
        <title>{typeof title === 'string' ? title : 'Admin'} - Vaivahik Admin</title>
        <meta name="description" content="Vaivahik Admin Panel" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <style jsx global>{`
        /* Collapsible Category Styles */
        .nav-category-container {
          margin-bottom: 5px;
        }

        .nav-category-header {
          display: flex;
          align-items: center;
          padding: 10px 15px;
          cursor: pointer;
          border-radius: 4px;
          transition: background-color 0.2s ease;
        }

        .nav-category-header:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }

        .nav-category-header.expanded {
          background-color: rgba(255, 255, 255, 0.05);
        }

        .category-icon {
          margin-right: 10px;
          font-size: 16px;
        }

        .expand-icon {
          margin-left: auto;
          font-size: 10px;
          transition: transform 0.2s ease;
        }

        .nav-category-items {
          max-height: 0;
          overflow: hidden;
          transition: max-height 0.3s ease;
        }

        .nav-category-items.expanded {
          max-height: 500px; /* Adjust based on your needs */
        }

        .sidebar.collapsed .nav-category-header {
          justify-content: center;
          padding: 10px 5px;
        }

        .sidebar.collapsed .nav-category {
          display: none;
        }

        .sidebar.collapsed .expand-icon {
          display: none;
        }

        .sidebar.collapsed .nav-category-items.expanded {
          position: absolute;
          left: 70px;
          top: 0;
          width: 200px;
          background: linear-gradient(to bottom, var(--primary), var(--primary-dark));
          border-radius: 0 4px 4px 0;
          box-shadow: 5px 0 10px rgba(0, 0, 0, 0.1);
          padding: 10px;
          z-index: 1000;
        }

        .has-active .nav-category-header {
          border-left: 3px solid #fff;
          background-color: rgba(255, 255, 255, 0.1);
        }

        .logout-button {
          margin-top: 20px;
          border-top: 1px solid rgba(255, 255, 255, 0.1);
          padding-top: 15px;
        }
      `}</style>

      <div className="admin-container" style={{ display: 'flex', minHeight: '100vh' }}>
        {/* Sidebar */}
        <aside className={`sidebar ${sidebarCollapsed ? 'collapsed' : ''}`} style={{ display: 'block', position: 'fixed', height: '100vh', zIndex: 1000 }}>
          <div className="sidebar-header">
            <Link href="/admin/dashboard" className="sidebar-logo">
              <div>V</div>
              <span>Vaivahik Admin</span>
            </Link>
            <button className="sidebar-toggle" onClick={toggleSidebar} aria-label="Toggle Sidebar">≡</button>
          </div>
          <div className="sidebar-user">
            <div className="user-avatar">{adminName.charAt(0).toUpperCase()}</div>
            <div className="user-info">
              <div className="user-name">{adminName}</div>
              <div className="user-role">{adminRole}</div>
            </div>
          </div>
          <nav className="sidebar-nav">
            {/* Main Category */}
            <CollapsibleCategory
              name="Main"
              icon="🏠"
              isExpanded={expandedCategories['main']}
              toggleExpand={() => toggleCategoryExpansion('main')}
              isActive={isCategoryActive('main')}
            >
              <Link href="/admin/dashboard" className={`nav-item ${router.pathname === '/admin/dashboard' ? 'active' : ''}`}>
                <div className="nav-icon">📊</div>
                <span className="nav-text">Dashboard</span>
              </Link>
            </CollapsibleCategory>

            {/* User Management Category */}
            <CollapsibleCategory
              name="User Management"
              icon="👥"
              isExpanded={expandedCategories['user']}
              toggleExpand={() => toggleCategoryExpansion('user')}
              isActive={isCategoryActive('user')}
            >
              <Link href="/admin/users" className={`nav-item ${router.pathname === '/admin/users' ? 'active' : ''}`}>
                <div className="nav-icon">👥</div>
                <span className="nav-text">All Users</span>
              </Link>
              <Link href="/admin/verification-queue" className={`nav-item ${router.pathname === '/admin/verification-queue' ? 'active' : ''}`}>
                <div className="nav-icon">✓</div>
                <span className="nav-text">Verification Queue</span>
              </Link>
              <Link href="/admin/reported-profiles" className={`nav-item ${router.pathname === '/admin/reported-profiles' ? 'active' : ''}`}>
                <div className="nav-icon">🚫</div>
                <span className="nav-text">Reported Profiles</span>
              </Link>
            </CollapsibleCategory>

            {/* Premium Features Category */}
            <CollapsibleCategory
              name="Premium Features"
              icon="💎"
              isExpanded={expandedCategories['premium']}
              toggleExpand={() => toggleCategoryExpansion('premium')}
              isActive={isCategoryActive('premium')}
            >
              <Link href="/admin/premium-plans" className={`nav-item ${router.pathname === '/admin/premium-plans' ? 'active' : ''}`}>
                <div className="nav-icon">💎</div>
                <span className="nav-text">Premium Plans</span>
              </Link>
              <Link href="/admin/feature-management" className={`nav-item ${router.pathname === '/admin/feature-management' ? 'active' : ''}`}>
                <div className="nav-icon">🔑</div>
                <span className="nav-text">Feature Management</span>
              </Link>
              <Link href="/admin/promotions" className={`nav-item ${router.pathname === '/admin/promotions' ? 'active' : ''}`}>
                <div className="nav-icon">🎁</div>
                <span className="nav-text">Promotions</span>
              </Link>
            </CollapsibleCategory>

            {/* AI & Matching Category */}
            <CollapsibleCategory
              name="AI & Matching"
              icon="🧠"
              isExpanded={expandedCategories['ai']}
              toggleExpand={() => toggleCategoryExpansion('ai')}
              isActive={isCategoryActive('ai')}
            >
              <Link href="/admin/algorithm-settings" className={`nav-item ${router.pathname === '/admin/algorithm-settings' ? 'active' : ''}`}>
                <div className="nav-icon">🧠</div>
                <span className="nav-text">Algorithm Settings</span>
              </Link>
              <Link href="/admin/preference-config" className={`nav-item ${router.pathname === '/admin/preference-config' ? 'active' : ''}`}>
                <div className="nav-icon">⚙️</div>
                <span className="nav-text">Preference Config</span>
              </Link>
              <Link href="/admin/success-analytics" className={`nav-item ${router.pathname === '/admin/success-analytics' ? 'active' : ''}`}>
                <div className="nav-icon">📈</div>
                <span className="nav-text">Success Analytics</span>
              </Link>
              <Link href="/admin/ai-phase-management" className={`nav-item ${router.pathname === '/admin/ai-phase-management' ? 'active' : ''}`}>
                <div className="nav-icon">🤖</div>
                <span className="nav-text">AI Phase Management</span>
              </Link>
              <Link href="/admin/mcp-server" className={`nav-item ${router.pathname === '/admin/mcp-server' ? 'active' : ''}`}>
                <div className="nav-icon">🔗</div>
                <span className="nav-text">MCP Server</span>
              </Link>
              <Link href="/admin/mcp-logs" className={`nav-item ${router.pathname === '/admin/mcp-logs' ? 'active' : ''}`}>
                <div className="nav-icon">📋</div>
                <span className="nav-text">MCP Logs</span>
              </Link>
            </CollapsibleCategory>

            {/* Content Category */}
            <CollapsibleCategory
              name="Content"
              icon="📝"
              isExpanded={expandedCategories['content']}
              toggleExpand={() => toggleCategoryExpansion('content')}
              isActive={isCategoryActive('content')}
            >
              <Link href="/admin/photo-moderation" className={`nav-item ${router.pathname === '/admin/photo-moderation' ? 'active' : ''}`}>
                <div className="nav-icon">🖼️</div>
                <span className="nav-text">Photo Moderation</span>
              </Link>
              <Link href="/admin/text-moderation" className={`nav-item ${router.pathname === '/admin/text-moderation' ? 'active' : ''}`}>
                <div className="nav-icon">💬</div>
                <span className="nav-text">Text Moderation</span>
              </Link>
              <Link href="/admin/success-stories" className={`nav-item ${router.pathname === '/admin/success-stories' ? 'active' : ''}`}>
                <div className="nav-icon">💑</div>
                <span className="nav-text">Success Stories</span>
              </Link>
              <Link href="/admin/blog-posts" className={`nav-item ${router.pathname === '/admin/blog-posts' ? 'active' : ''}`}>
                <div className="nav-icon">📝</div>
                <span className="nav-text">Blog Posts</span>
              </Link>
              <Link href="/admin/biodata-templates" className={`nav-item ${router.pathname === '/admin/biodata-templates' ? 'active' : ''}`}>
                <div className="nav-icon">📄</div>
                <span className="nav-text">Biodata Templates</span>
              </Link>
              <Link href="/admin/spotlight-features" className={`nav-item ${router.pathname === '/admin/spotlight-features' ? 'active' : ''}`}>
                <div className="nav-icon">✨</div>
                <span className="nav-text">Spotlight Features</span>
              </Link>
            </CollapsibleCategory>

            {/* Financial Category */}
            <CollapsibleCategory
              name="Financial"
              icon="💰"
              isExpanded={expandedCategories['financial']}
              toggleExpand={() => toggleCategoryExpansion('financial')}
              isActive={isCategoryActive('financial')}
            >
              <Link href="/admin/subscriptions" className={`nav-item ${router.pathname === '/admin/subscriptions' ? 'active' : ''}`}>
                <div className="nav-icon">💰</div>
                <span className="nav-text">Subscriptions</span>
              </Link>
              <Link href="/admin/transactions" className={`nav-item ${router.pathname === '/admin/transactions' ? 'active' : ''}`}>
                <div className="nav-icon">💳</div>
                <span className="nav-text">Transactions</span>
              </Link>
              <Link href="/admin/revenue-reports" className={`nav-item ${router.pathname === '/admin/revenue-reports' ? 'active' : ''}`}>
                <div className="nav-icon">📊</div>
                <span className="nav-text">Revenue Reports</span>
              </Link>
            </CollapsibleCategory>

            {/* Communication Category */}
            <CollapsibleCategory
              name="Communication"
              icon="✉️"
              isExpanded={expandedCategories['communication']}
              toggleExpand={() => toggleCategoryExpansion('communication')}
              isActive={isCategoryActive('communication')}
            >
              <Link href="/admin/notifications" className={`nav-item ${router.pathname === '/admin/notifications' ? 'active' : ''}`}>
                <div className="nav-icon">🔔</div>
                <span className="nav-text">Notifications</span>
              </Link>
              <Link href="/admin/email-templates" className={`nav-item ${router.pathname === '/admin/email-templates' ? 'active' : ''}`}>
                <div className="nav-icon">✉️</div>
                <span className="nav-text">Email Templates</span>
              </Link>
              <Link href="/admin/chat-management" className={`nav-item ${router.pathname === '/admin/chat-management' ? 'active' : ''}`}>
                <div className="nav-icon">💬</div>
                <span className="nav-text">Chat Management</span>
              </Link>
              <Link href="/admin/contact-security" className={`nav-item ${router.pathname === '/admin/contact-security' ? 'active' : ''}`}>
                <div className="nav-icon">🔒</div>
                <span className="nav-text">Contact Security</span>
              </Link>
              <Link href="/admin/contact-configuration" className={`nav-item ${router.pathname === '/admin/contact-configuration' ? 'active' : ''}`}>
                <div className="nav-icon">📞</div>
                <span className="nav-text">Contact Configuration</span>
              </Link>
            </CollapsibleCategory>

            {/* System Category */}
            <CollapsibleCategory
              name="System"
              icon="⚙️"
              isExpanded={expandedCategories['system']}
              toggleExpand={() => toggleCategoryExpansion('system')}
              isActive={isCategoryActive('system')}
            >
              <Link href="/admin/settings" className={`nav-item ${router.pathname === '/admin/settings' ? 'active' : ''}`}>
                <div className="nav-icon">⚙️</div>
                <span className="nav-text">Settings</span>
              </Link>
              <Link href="/admin/feature-flags" className={`nav-item ${router.pathname === '/admin/feature-flags' ? 'active' : ''}`}>
                <div className="nav-icon">🚩</div>
                <span className="nav-text">Feature Flags</span>
              </Link>
              <Link href="/admin/admin-users" className={`nav-item ${router.pathname === '/admin/admin-users' ? 'active' : ''}`}>
                <div className="nav-icon">👤</div>
                <span className="nav-text">Admin Users</span>
              </Link>
              <Link href="/admin/security-settings" className={`nav-item ${router.pathname === '/admin/security-settings' ? 'active' : ''}`}>
                <div className="nav-icon">🔐</div>
                <span className="nav-text">Security Settings</span>
              </Link>
              <Link href="/admin/privacy-controls" className={`nav-item ${router.pathname === '/admin/privacy-controls' ? 'active' : ''}`}>
                <div className="nav-icon">🛡️</div>
                <span className="nav-text">Privacy Controls</span>
              </Link>
              <Link href="/admin/advanced-analytics" className={`nav-item ${router.pathname === '/admin/advanced-analytics' ? 'active' : ''}`}>
                <div className="nav-icon">📊</div>
                <span className="nav-text">Advanced Analytics</span>
              </Link>
              <Link href="/admin/system-monitoring" className={`nav-item ${router.pathname === '/admin/system-monitoring' ? 'active' : ''}`}>
                <div className="nav-icon">🖥️</div>
                <span className="nav-text">System Monitoring</span>
              </Link>
              <Link href="/admin/logs-monitoring" className={`nav-item ${router.pathname === '/admin/logs-monitoring' ? 'active' : ''}`}>
                <div className="nav-icon">📝</div>
                <span className="nav-text">Logs Monitoring</span>
              </Link>
              <Link href="/admin/backup-recovery" className={`nav-item ${router.pathname === '/admin/backup-recovery' ? 'active' : ''}`}>
                <div className="nav-icon">💾</div>
                <span className="nav-text">Backup & Recovery</span>
              </Link>
              <Link href="/admin/sms-configuration" className={`nav-item ${router.pathname === '/admin/sms-configuration' ? 'active' : ''}`}>
                <div className="nav-icon">📱</div>
                <span className="nav-text">SMS Configuration</span>
              </Link>
              <Link href="/admin/api-management" className={`nav-item ${router.pathname === '/admin/api-management' ? 'active' : ''}`}>
                <div className="nav-icon">🔌</div>
                <span className="nav-text">API Management</span>
              </Link>
              <Link href="/admin/policy-management" className={`nav-item ${router.pathname === '/admin/policy-management' ? 'active' : ''}`}>
                <div className="nav-icon">📋</div>
                <span className="nav-text">Policy Management</span>
              </Link>
            </CollapsibleCategory>

            {/* Developer Tools Category */}
            <CollapsibleCategory
              name="Developer Tools"
              icon="🛠️"
              isExpanded={expandedCategories['developer']}
              toggleExpand={() => toggleCategoryExpansion('developer')}
              isActive={isCategoryActive('developer')}
            >
              <Link href="/admin/testing-tools" className={`nav-item ${router.pathname === '/admin/testing-tools' ? 'active' : ''}`}>
                <div className="nav-icon">🧪</div>
                <span className="nav-text">Testing Tools</span>
              </Link>
              <Link href="/api-viewer-enhanced" className={`nav-item ${router.pathname === '/api-viewer-enhanced' ? 'active' : ''}`}>
                <div className="nav-icon">📚</div>
                <span className="nav-text">API Viewer</span>
              </Link>
              <Link href="/api-discovery-enhanced" className={`nav-item ${router.pathname === '/api-discovery-enhanced' ? 'active' : ''}`}>
                <div className="nav-icon">🔍</div>
                <span className="nav-text">API Discovery</span>
              </Link>
              <Link href="/admin/error-monitoring" className={`nav-item ${router.pathname === '/admin/error-monitoring' ? 'active' : ''}`}>
                <div className="nav-icon">🐞</div>
                <span className="nav-text">Error Monitoring</span>
              </Link>
              <Link href="/admin/toast-demo" className={`nav-item ${router.pathname === '/admin/toast-demo' ? 'active' : ''}`}>
                <div className="nav-icon">🔔</div>
                <span className="nav-text">Toast Notifications</span>
              </Link>
            </CollapsibleCategory>

            {/* Logout Button */}
            <button className="nav-item logout-button" onClick={handleLogout}>
              <div className="nav-icon">🚪</div>
              <span className="nav-text">Logout</span>
            </button>
          </nav>
        </aside>

        {/* Main Content */}
        <div
          className={`main-content ${sidebarCollapsed ? 'collapsed' : ''}`}
          style={{
            display: 'block',
            marginLeft: sidebarCollapsed ? '70px' : '260px',
            width: sidebarCollapsed ? 'calc(100% - 70px)' : 'calc(100% - 260px)',
            minHeight: '100vh'
          }}
        >
          {/* Top Bar */}
          <div className="topbar" style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div className="breadcrumb">
              <Link href="/admin/dashboard">Dashboard</Link> / <span>{title}</span>
            </div>
            <div className="topbar-actions">
              {/* Mock Auth Manager - only in development mode */}
              {process.env.NODE_ENV === 'development' && <MockAuthManager />}

              {/* Feature Flag Toggle */}
              <FeatureFlagToggle />

              {/* Data Source Indicator */}
              {!isUsingRealBackend() && (
                <div className="mock-data-indicator" style={{
                  backgroundColor: '#FFF3CD',
                  color: '#856404',
                  padding: '2px 8px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  marginRight: '10px',
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <span style={{ marginRight: '4px' }}>🔄</span>
                  <span>Using Mock Data</span>
                </div>
              )}

              <button className="dark-mode-toggle" onClick={toggleDarkMode} aria-label="Toggle Dark Mode">
                <span className="light-icon">☀️</span>
                <span className="dark-icon">🌙</span>
              </button>
              <div className="notifications">
                <button className="notification-btn" aria-label="Notifications">
                  🔔
                  <span className="notification-badge">3</span>
                </button>
              </div>
            </div>
          </div>

          {/* Page Content */}
          <div style={{ display: 'block', width: '100%' }}>
            {children}
          </div>
        </div>
      </div>
    </>
  );
};

export default EnhancedAdminLayout;
