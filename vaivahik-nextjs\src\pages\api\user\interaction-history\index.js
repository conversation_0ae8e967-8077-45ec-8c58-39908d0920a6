import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Verify JWT token
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ success: false, message: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const userId = decoded.userId;

    // Get all user interactions
    const allInteractions = await prisma.userInteraction.findMany({
      where: {
        userId: userId
      },
      include: {
        targetUser: {
          include: {
            profile: true
          }
        }
      },
      orderBy: {
        timestamp: 'desc'
      },
      take: 1000 // Limit to last 1000 interactions
    });

    // Get profile likes given by user
    const profileLikes = await prisma.profileLike.findMany({
      where: {
        userId: userId
      },
      include: {
        targetUser: {
          include: {
            profile: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Get detailed profile views
    const profileViews = await prisma.profileViewDetailed.findMany({
      where: {
        viewerId: userId
      },
      include: {
        viewedUser: {
          include: {
            profile: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 500 // Limit to last 500 views
    });

    // Format interactions
    const formatUser = (user) => ({
      id: user.id,
      firstName: user.profile?.firstName || 'Unknown',
      lastName: user.profile?.lastName || '',
      age: user.profile?.age,
      location: `${user.profile?.city || ''}, ${user.profile?.state || ''}`.trim().replace(/^,|,$/, ''),
      profilePicture: user.profile?.profilePicUrl
    });

    // Format all interactions
    const formattedInteractions = allInteractions.map(interaction => ({
      id: interaction.id,
      interactionType: interaction.interactionType,
      targetUserId: interaction.targetUserId,
      timestamp: interaction.timestamp,
      duration: interaction.duration,
      metadata: interaction.metadata,
      targetUser: formatUser(interaction.targetUser)
    }));

    // Format profile likes as interactions
    const formattedLikes = profileLikes.map(like => ({
      id: `like_${like.id}`,
      interactionType: `PROFILE_${like.likeType}`,
      targetUserId: like.targetUserId,
      timestamp: like.createdAt,
      duration: null,
      metadata: JSON.stringify({ likeType: like.likeType }),
      targetUser: formatUser(like.targetUser)
    }));

    // Format profile views as interactions
    const formattedViews = profileViews.map(view => ({
      id: `view_${view.id}`,
      interactionType: 'PROFILE_VIEW_DETAILED',
      targetUserId: view.viewedUserId,
      timestamp: view.createdAt,
      duration: view.viewDuration ? view.viewDuration * 1000 : null, // Convert to milliseconds
      metadata: JSON.stringify({
        deviceType: view.deviceType,
        sectionsViewed: view.sectionsViewed,
        referrer: view.referrer
      }),
      targetUser: formatUser(view.viewedUser)
    }));

    // Combine all interactions
    const allCombined = [
      ...formattedInteractions,
      ...formattedLikes,
      ...formattedViews
    ].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Categorize interactions
    const categorized = {
      all: allCombined,
      views: allCombined.filter(i => 
        i.interactionType.includes('VIEW') || i.interactionType === 'PROFILE_VIEW'
      ),
      likes: allCombined.filter(i => 
        i.interactionType.includes('LIKE') || i.interactionType.includes('DISLIKE')
      ),
      interests: allCombined.filter(i => 
        i.interactionType.includes('INTEREST')
      ),
      contacts: allCombined.filter(i => 
        i.interactionType.includes('CONTACT') || i.interactionType.includes('SHORTLIST')
      )
    };

    // Calculate stats
    const stats = {
      totalViews: categorized.views.length,
      totalLikes: categorized.likes.filter(i => i.interactionType.includes('LIKE') && !i.interactionType.includes('DISLIKE')).length,
      totalDislikes: categorized.likes.filter(i => i.interactionType.includes('DISLIKE')).length,
      totalInterests: categorized.interests.length,
      totalContacts: categorized.contacts.length,
      totalInteractions: allCombined.length
    };

    // Get recent activity summary (last 7 days)
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const recentActivity = allCombined.filter(i => new Date(i.timestamp) >= sevenDaysAgo);
    
    const recentStats = {
      recentViews: recentActivity.filter(i => i.interactionType.includes('VIEW')).length,
      recentLikes: recentActivity.filter(i => i.interactionType.includes('LIKE') && !i.interactionType.includes('DISLIKE')).length,
      recentInterests: recentActivity.filter(i => i.interactionType.includes('INTEREST')).length,
      recentContacts: recentActivity.filter(i => i.interactionType.includes('CONTACT')).length
    };

    return res.status(200).json({
      success: true,
      data: categorized,
      stats: {
        ...stats,
        recent: recentStats
      }
    });

  } catch (error) {
    console.error('Error fetching interaction history:', error);
    return res.status(500).json({
      success: false,
      message: 'Error fetching interaction history',
      error: error.message
    });
  }
}
