import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { PrismaClient } from '@prisma/client';
import crypto from 'crypto';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  // Check if user is authenticated
  const session = await getServerSession(req, res, authOptions);
  
  if (!session || !session.user) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }
  
  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }
  
  try {
    const { orderId, paymentId, signature } = req.body;
    const userId = session.user.id;
    
    if (!orderId || !paymentId || !signature) {
      return res.status(400).json({ success: false, message: 'Order ID, payment ID, and signature are required' });
    }
    
    // Verify signature
    const isValidSignature = verifyPaymentSignature(orderId, paymentId, signature);
    
    if (!isValidSignature) {
      return res.status(400).json({ success: false, message: 'Invalid payment signature' });
    }
    
    // Get payment from database (if Payment model exists)
    let payment;
    try {
      payment = await prisma.payment.findFirst({
        where: { orderId }
      });
      
      // Verify that the payment belongs to the user
      if (payment && payment.userId !== userId) {
        return res.status(403).json({ success: false, message: 'Unauthorized access to payment' });
      }
    } catch (error) {
      console.error('Warning: Could not fetch payment from database:', error);
      // Continue anyway since we can process based on the order ID
    }
    
    // Process the payment based on product type
    let result;
    
    if (payment) {
      // If we have the payment record, use it
      if (payment.productType === 'BIODATA') {
        result = await processBiodataPurchase(payment, paymentId);
      } else if (payment.productType === 'SPOTLIGHT') {
        result = await processSpotlightPurchase(payment, paymentId);
      } else {
        return res.status(400).json({ success: false, message: 'Invalid product type' });
      }
      
      // Update payment status
      try {
        await prisma.payment.update({
          where: { id: payment.id },
          data: {
            status: 'COMPLETED',
            paymentId,
            completedAt: new Date()
          }
        });
      } catch (error) {
        console.error('Warning: Could not update payment status:', error);
        // Continue anyway since the purchase was processed
      }
    } else {
      // If we don't have the payment record, try to extract info from the order ID
      const orderIdParts = orderId.split('_');
      if (orderIdParts.length >= 3) {
        const productType = orderIdParts[0].toUpperCase();
        const productId = orderIdParts[2];
        
        if (productType === 'BIODATA') {
          result = await processBiodataWithoutPayment(userId, productId, paymentId);
        } else if (productType === 'SPOTLIGHT') {
          result = await processSpotlightWithoutPayment(userId, productId, paymentId);
        } else {
          return res.status(400).json({ success: false, message: 'Invalid product type in order ID' });
        }
      } else {
        return res.status(400).json({ success: false, message: 'Invalid order ID format' });
      }
    }
    
    return res.status(200).json({
      success: true,
      message: 'Payment verified and processed successfully',
      result
    });
  } catch (error) {
    console.error('Error verifying payment:', error);
    return res.status(500).json({ success: false, message: error.message || 'Failed to verify payment' });
  }
}

/**
 * Verify payment signature
 * @param {string} orderId - The order ID
 * @param {string} paymentId - The payment ID
 * @param {string} signature - The signature from Razorpay
 * @returns {boolean} - Whether the signature is valid
 */
function verifyPaymentSignature(orderId, paymentId, signature) {
  try {
    const generatedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
      .update(`${orderId}|${paymentId}`)
      .digest('hex');
    
    return generatedSignature === signature;
  } catch (error) {
    console.error('Error verifying payment signature:', error);
    return false;
  }
}

/**
 * Process biodata purchase with payment record
 * @param {Object} payment - The payment object
 * @param {string} paymentId - The payment ID
 * @returns {Promise<Object>} - The processed payment details
 */
async function processBiodataPurchase(payment, paymentId) {
  try {
    const metadata = JSON.parse(payment.metadata || '{}');
    const { templateId } = metadata;
    
    if (!templateId) {
      throw new Error('Template ID not found in payment metadata');
    }
    
    // Check if user has already purchased this template
    const existingPurchase = await prisma.userBiodata.findFirst({
      where: {
        userId: payment.userId,
        templateId
      }
    });
    
    if (existingPurchase) {
      return { alreadyPurchased: true, purchase: existingPurchase };
    }
    
    // Create user biodata purchase
    const purchase = await prisma.userBiodata.create({
      data: {
        userId: payment.userId,
        templateId,
        purchaseDate: new Date(),
        pricePaid: payment.amount,
        downloadCount: 0,
        transactionId: paymentId
      }
    });
    
    return { purchase };
  } catch (error) {
    console.error('Error processing biodata purchase:', error);
    throw error;
  }
}

/**
 * Process spotlight purchase with payment record
 * @param {Object} payment - The payment object
 * @param {string} paymentId - The payment ID
 * @returns {Promise<Object>} - The processed payment details
 */
async function processSpotlightPurchase(payment, paymentId) {
  try {
    const metadata = JSON.parse(payment.metadata || '{}');
    const { spotlightId, quantity = 1, defaultCount = 1 } = metadata;
    const totalCount = quantity * defaultCount;
    
    if (!spotlightId) {
      throw new Error('Spotlight ID not found in payment metadata');
    }
    
    // Check if user already has this spotlight
    const existingSpotlight = await prisma.userSpotlight.findFirst({
      where: {
        userId: payment.userId,
        spotlightId
      }
    });
    
    let spotlight;
    
    if (existingSpotlight) {
      // Update existing spotlight
      spotlight = await prisma.userSpotlight.update({
        where: { id: existingSpotlight.id },
        data: {
          availableCount: existingSpotlight.availableCount + totalCount,
          pricePaid: existingSpotlight.pricePaid + payment.amount
        }
      });
    } else {
      // Create new spotlight purchase
      spotlight = await prisma.userSpotlight.create({
        data: {
          userId: payment.userId,
          spotlightId,
          purchaseDate: new Date(),
          pricePaid: payment.amount,
          availableCount: totalCount,
          usedCount: 0,
          transactionId: paymentId
        }
      });
    }
    
    return { spotlight, totalCount };
  } catch (error) {
    console.error('Error processing spotlight purchase:', error);
    throw error;
  }
}

/**
 * Process biodata purchase without payment record
 * @param {string} userId - The user ID
 * @param {string} templateId - The biodata template ID
 * @param {string} paymentId - The payment ID
 * @returns {Promise<Object>} - The processed payment details
 */
async function processBiodataWithoutPayment(userId, templateId, paymentId) {
  try {
    // Get template
    const template = await prisma.biodataTemplate.findUnique({
      where: { id: templateId }
    });
    
    if (!template) {
      throw new Error('Template not found');
    }
    
    // Check if user has already purchased this template
    const existingPurchase = await prisma.userBiodata.findFirst({
      where: {
        userId,
        templateId
      }
    });
    
    if (existingPurchase) {
      return { alreadyPurchased: true, purchase: existingPurchase };
    }
    
    // Create user biodata purchase
    const purchase = await prisma.userBiodata.create({
      data: {
        userId,
        templateId,
        purchaseDate: new Date(),
        pricePaid: template.discountedPrice || template.price,
        downloadCount: 0,
        transactionId: paymentId
      }
    });
    
    return { purchase };
  } catch (error) {
    console.error('Error processing biodata purchase without payment record:', error);
    throw error;
  }
}

/**
 * Process spotlight purchase without payment record
 * @param {string} userId - The user ID
 * @param {string} spotlightId - The spotlight feature ID
 * @param {string} paymentId - The payment ID
 * @returns {Promise<Object>} - The processed payment details
 */
async function processSpotlightWithoutPayment(userId, spotlightId, paymentId) {
  try {
    // Get spotlight feature
    const spotlight = await prisma.spotlightFeature.findUnique({
      where: { id: spotlightId }
    });
    
    if (!spotlight) {
      throw new Error('Spotlight feature not found');
    }
    
    const defaultCount = spotlight.defaultCount || 1;
    const price = spotlight.discountedPrice || spotlight.price;
    
    // Check if user already has this spotlight
    const existingSpotlight = await prisma.userSpotlight.findFirst({
      where: {
        userId,
        spotlightId
      }
    });
    
    let userSpotlight;
    
    if (existingSpotlight) {
      // Update existing spotlight
      userSpotlight = await prisma.userSpotlight.update({
        where: { id: existingSpotlight.id },
        data: {
          availableCount: existingSpotlight.availableCount + defaultCount,
          pricePaid: existingSpotlight.pricePaid + price
        }
      });
    } else {
      // Create new spotlight purchase
      userSpotlight = await prisma.userSpotlight.create({
        data: {
          userId,
          spotlightId,
          purchaseDate: new Date(),
          pricePaid: price,
          availableCount: defaultCount,
          usedCount: 0,
          transactionId: paymentId
        }
      });
    }
    
    return { spotlight: userSpotlight, totalCount: defaultCount };
  } catch (error) {
    console.error('Error processing spotlight purchase without payment record:', error);
    throw error;
  }
}
