/**
 * Payment Routes for Razorpay Integration
 */

const express = require('express');
const crypto = require('crypto');
const router = express.Router();
const razorpayService = require('../services/payment/razorpay-service');
const { authenticateToken } = require('../src/middleware/auth.middleware');

/**
 * @route POST /api/payments/create-subscription-order
 * @desc Create a payment order for subscription
 * @access Private
 */
router.post('/create-subscription-order', authenticateToken, async (req, res) => {
  try {
    const { planType, planDuration } = req.body;
    const userId = req.user.id;

    if (!planType || !planDuration) {
      return res.status(400).json({
        success: false,
        message: 'Plan type and duration are required'
      });
    }

    const result = await razorpayService.createSubscriptionOrder(userId, planType, planDuration);

    if (result.success) {
      res.json({
        success: true,
        order: result.order,
        paymentOrderId: result.paymentOrderId,
        key: process.env.RAZORPAY_KEY_ID
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Error creating subscription order:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

/**
 * @route POST /api/payments/create-feature-order
 * @desc Create a payment order for one-time features
 * @access Private
 */
router.post('/create-feature-order', authenticateToken, async (req, res) => {
  try {
    const { featureType, quantity = 1 } = req.body;
    const userId = req.user.id;

    if (!featureType) {
      return res.status(400).json({
        success: false,
        message: 'Feature type is required'
      });
    }

    const result = await razorpayService.createFeatureOrder(userId, featureType, quantity);

    if (result.success) {
      res.json({
        success: true,
        order: result.order,
        paymentOrderId: result.paymentOrderId,
        key: process.env.RAZORPAY_KEY_ID
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Error creating feature order:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

/**
 * @route POST /api/payments/verify-payment
 * @desc Verify and process successful payment
 * @access Private
 */
router.post('/verify-payment', authenticateToken, async (req, res) => {
  try {
    const { orderId, paymentId, signature } = req.body;
    const userId = req.user.id;

    if (!orderId || !paymentId || !signature) {
      return res.status(400).json({
        success: false,
        message: 'Order ID, payment ID, and signature are required'
      });
    }

    const result = await razorpayService.processSuccessfulPayment({
      orderId,
      paymentId,
      signature,
      userId
    });

    if (result.success) {
      res.json({
        success: true,
        message: result.message,
        subscription: result.subscription
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Error verifying payment:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

/**
 * @route POST /api/payments/payment-failed
 * @desc Handle failed payment
 * @access Private
 */
router.post('/payment-failed', authenticateToken, async (req, res) => {
  try {
    const { orderId, reason } = req.body;

    if (!orderId) {
      return res.status(400).json({
        success: false,
        message: 'Order ID is required'
      });
    }

    const result = await razorpayService.processFailedPayment(orderId, reason);

    if (result.success) {
      res.json({
        success: true,
        message: result.message
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Error processing failed payment:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

/**
 * @route GET /api/payments/history
 * @desc Get user's payment history
 * @access Private
 */
router.get('/history', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { limit = 10, offset = 0 } = req.query;

    const result = await razorpayService.getPaymentHistory(
      userId,
      parseInt(limit),
      parseInt(offset)
    );

    if (result.success) {
      res.json({
        success: true,
        payments: result.payments
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Error getting payment history:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

/**
 * @route GET /api/payments/plans
 * @desc Get available subscription plans
 * @access Private
 */
router.get('/plans', authenticateToken, async (req, res) => {
  try {
    const plans = {
      PREMIUM: {
        monthly: {
          amount: 999,
          duration: 'monthly',
          name: 'Premium Monthly',
          features: [
            'Unlimited profile views',
            'Priority matching',
            'Direct contact details',
            'Advanced search filters',
            'Profile boost'
          ]
        },
        quarterly: {
          amount: 2499,
          duration: 'quarterly',
          name: 'Premium Quarterly',
          savings: '17% off',
          features: [
            'All monthly features',
            '3 months validity',
            'Save ₹500',
            'Priority customer support'
          ]
        },
        annual: {
          amount: 7999,
          duration: 'annual',
          name: 'Premium Annual',
          savings: '33% off',
          features: [
            'All monthly features',
            '12 months validity',
            'Save ₹4,000',
            'Dedicated relationship manager',
            'Free horoscope matching'
          ]
        }
      }
    };

    const features = {
      PROFILE_BOOST: {
        amount: 199,
        name: 'Profile Boost',
        description: 'Boost your profile visibility for 30 days'
      },
      SUPER_LIKES: {
        amount: 99,
        name: 'Super Likes (10 pack)',
        description: 'Send super likes to show special interest'
      },
      CONTACT_REVEAL: {
        amount: 49,
        name: 'Contact Details Reveal',
        description: 'Reveal contact details of one profile'
      },
      HOROSCOPE_MATCH: {
        amount: 299,
        name: 'Horoscope Matching',
        description: 'Get detailed horoscope compatibility report'
      },
      BACKGROUND_VERIFY: {
        amount: 999,
        name: 'Background Verification',
        description: 'Professional background verification service'
      }
    };

    res.json({
      success: true,
      plans,
      features
    });
  } catch (error) {
    console.error('Error getting plans:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

/**
 * @route POST /api/payments/webhook
 * @desc Razorpay webhook for payment events (OPTIONAL)
 * @access Public (but verified)
 * @note You can skip this if you don't want to use webhooks
 */
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  try {
    // Skip webhook verification if no webhook secret is provided
    if (!process.env.RAZORPAY_WEBHOOK_SECRET) {
      console.log('Webhook received but no secret configured - skipping verification');
      return res.json({ status: 'ok', message: 'Webhook not configured' });
    }

    const signature = req.headers['x-razorpay-signature'];
    const body = req.body;

    // Verify webhook signature
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_WEBHOOK_SECRET)
      .update(body)
      .digest('hex');

    if (signature !== expectedSignature) {
      return res.status(400).json({ message: 'Invalid signature' });
    }

    const event = JSON.parse(body);

    // Handle different webhook events
    switch (event.event) {
      case 'payment.captured':
        console.log('Payment captured:', event.payload.payment.entity);
        break;

      case 'payment.failed':
        console.log('Payment failed:', event.payload.payment.entity);
        break;

      case 'order.paid':
        console.log('Order paid:', event.payload.order.entity);
        break;

      default:
        console.log('Unhandled webhook event:', event.event);
    }

    res.json({ status: 'ok' });
  } catch (error) {
    console.error('Webhook error:', error);
    res.status(500).json({ message: 'Webhook error' });
  }
});

module.exports = router;
