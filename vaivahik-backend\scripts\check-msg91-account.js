/**
 * MSG91 Account Status Checker
 * 
 * This script checks your MSG91 account status and configuration
 */

const axios = require('axios');
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });

console.log('=== MSG91 Account Status Check ===');
console.log(`API Key: ${process.env.MSG91_API_KEY}`);
console.log(`Sender ID: ${process.env.MSG91_SENDER_ID}`);
console.log(`Template ID: ${process.env.MSG91_DLT_TEMPLATE_ID}`);
console.log(`PE ID: ${process.env.MSG91_DLT_PE_ID}`);

async function checkAccountBalance() {
  console.log('\n=== Checking Account Balance ===');
  
  try {
    const response = await axios.get(`https://control.msg91.com/api/balance.php?authkey=${process.env.MSG91_API_KEY}`);
    console.log('Balance Response:', response.data);
  } catch (error) {
    console.log('Balance Check Error:', error.response?.data || error.message);
  }
}

async function checkSenderID() {
  console.log('\n=== Checking Sender ID Status ===');
  
  try {
    const response = await axios.get(`https://control.msg91.com/api/getSenderid.php?authkey=${process.env.MSG91_API_KEY}`);
    console.log('Sender ID Response:', response.data);
  } catch (error) {
    console.log('Sender ID Check Error:', error.response?.data || error.message);
  }
}

async function testSimpleOTP() {
  console.log('\n=== Testing Simple OTP (without DLT) ===');
  
  const params = new URLSearchParams({
    mobile: '************',
    authkey: process.env.MSG91_API_KEY,
    otp: '9999',
    sender: process.env.MSG91_SENDER_ID,
    message: 'Your OTP is 9999 for Maratha Wedding verification.'
  });

  try {
    const response = await axios.get(`https://control.msg91.com/api/sendotp.php?${params.toString()}`);
    console.log('Simple OTP Response:', response.data);
  } catch (error) {
    console.log('Simple OTP Error:', error.response?.data || error.message);
  }
}

async function testWithoutTemplate() {
  console.log('\n=== Testing OTP without DLT Template ===');
  
  const params = new URLSearchParams({
    mobile: '************',
    authkey: process.env.MSG91_API_KEY,
    otp: '8888',
    sender: process.env.MSG91_SENDER_ID
  });

  try {
    const response = await axios.get(`https://control.msg91.com/api/sendotp.php?${params.toString()}`);
    console.log('No Template OTP Response:', response.data);
  } catch (error) {
    console.log('No Template OTP Error:', error.response?.data || error.message);
  }
}

async function runAllChecks() {
  await checkAccountBalance();
  await checkSenderID();
  await testSimpleOTP();
  await testWithoutTemplate();
  
  console.log('\n=== Recommendations ===');
  console.log('1. Check if your MSG91 account has sufficient balance');
  console.log('2. Verify that your Sender ID is approved');
  console.log('3. Ensure your DLT template is approved and active');
  console.log('4. Check if your account is in test mode or production mode');
  console.log('5. Verify that your phone number is not in DND (Do Not Disturb) list');
}

runAllChecks().catch(console.error);
