// DOM Elements
const searchInput = document.getElementById('searchInput');
const statusFilter = document.getElementById('statusFilter');
const refreshBtn = document.getElementById('refreshBtn');
const entriesSelect = document.getElementById('entriesSelect');
const verificationTableBody = document.getElementById('verificationTableBody');
const paginationControls = document.getElementById('paginationControls');
const tableInfo = document.getElementById('tableInfo');
const verificationModalOverlay = document.getElementById('verificationModalOverlay');
let modalCloseButton, approveBtn, rejectBtn;

// Safely get modal elements
try {
    if (verificationModalOverlay) {
        modalCloseButton = verificationModalOverlay.querySelector('.modal-close-button');
        approveBtn = document.getElementById('approveBtn');
        rejectBtn = document.getElementById('rejectBtn');
    }
} catch (e) {
    console.error('Error getting modal elements:', e);
}

// State variables
let currentPage = 1;
let totalPages = 1;
let currentVerificationId = null;
let searchQuery = '';
let selectedStatus = 'all';
let itemsPerPage = 10;

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    setupEventListeners();
    fetchVerificationQueue();
});

// Event Listeners
function setupEventListeners() {
    // Search input with debounce
    if (searchInput) {
        searchInput.addEventListener('input', debounce(() => {
            searchQuery = searchInput.value;
            currentPage = 1;
            fetchVerificationQueue();
        }, 500));
    }

    // Status filter
    if (statusFilter) {
        statusFilter.addEventListener('change', () => {
            selectedStatus = statusFilter.value;
            currentPage = 1;
            fetchVerificationQueue();
        });
    }

    // Refresh button
    if (refreshBtn) {
        refreshBtn.addEventListener('click', () => {
            fetchVerificationQueue();
        });
    }

    // Entries per page
    if (entriesSelect) {
        entriesSelect.addEventListener('change', () => {
            itemsPerPage = parseInt(entriesSelect.value);
            currentPage = 1;
            fetchVerificationQueue();
        });
    }

    // Modal close button
    if (modalCloseButton) {
        modalCloseButton.addEventListener('click', () => {
            if (verificationModalOverlay) {
                verificationModalOverlay.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
    }

    // Close modals when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target.classList.contains('modal-overlay')) {
            e.target.classList.remove('active');
            document.body.style.overflow = '';
        }
    });

    // Approve and Reject buttons
    if (approveBtn) {
        approveBtn.addEventListener('click', () => handleVerificationAction('approve'));
    }

    if (rejectBtn) {
        rejectBtn.addEventListener('click', () => handleVerificationAction('reject'));
    }
}

// Fetch verification queue data
async function fetchVerificationQueue() {
    try {
        showLoading();

        try {
            const response = await fetch(`/api/admin/verification-queue?page=${currentPage}&limit=${itemsPerPage}&status=${selectedStatus}&search=${searchQuery}`);

            if (!response.ok) {
                throw new Error(`Failed to fetch verification queue: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            renderVerificationQueue(data.verifications || []);
            updatePagination(data.total || 0);
            updateTableInfo(data.total || 0, data.pages || 1);
        } catch (error) {
            console.error('Error fetching verification queue:', error);

            // Generate mock data for testing
            const mockVerifications = generateMockVerifications();
            renderVerificationQueue(mockVerifications);
            updatePagination(mockVerifications.length);
            updateTableInfo(mockVerifications.length, 1);

            // Show a non-intrusive warning
            console.warn('Using mock data for verification queue. API endpoint may not be implemented yet.');
        }
    } catch (error) {
        console.error('Error in fetchVerificationQueue function:', error);
        showError('Failed to load verification queue. Please try again.');
    } finally {
        hideLoading();
    }
}

// Generate mock data for testing
function generateMockVerifications() {
    const mockStatuses = ['PENDING', 'APPROVED', 'REJECTED'];
    const mockLocations = ['Mumbai', 'Delhi', 'Bangalore', 'Chennai', 'Kolkata'];

    return Array.from({ length: 5 }, (_, i) => ({
        id: `mock-verification-${i + 1}`,
        user: {
            name: `User ${i + 1}`,
            email: `user${i + 1}@example.com`,
            profilePicture: null,
            location: mockLocations[Math.floor(Math.random() * mockLocations.length)]
        },
        status: mockStatuses[Math.floor(Math.random() * mockStatuses.length)],
        submissionDate: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString()
    }));
}

// Update table information
function updateTableInfo(total, pages) {
    if (!tableInfo) return;

    const start = total === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1;
    const end = Math.min(start + itemsPerPage - 1, total);
    tableInfo.textContent = `Showing ${start} to ${end} of ${total} entries`;
}

// Render verification queue
function renderVerificationQueue(verifications) {
    if (!verificationTableBody) {
        console.error('Table body element not found');
        return;
    }

    if (!verifications || !verifications.length) {
        verificationTableBody.innerHTML = `
            <tr>
                <td colspan="6" style="text-align: center;">No verifications found</td>
            </tr>
        `;
        return;
    }

    verificationTableBody.innerHTML = verifications.map(verification => {
        // Ensure user object exists
        const user = verification.user || { name: 'Unknown User', email: 'No email' };

        // Default profile picture
        const defaultAvatar = 'img/default-avatar.png';

        // Format the status for display
        const status = verification.status ? verification.status.toLowerCase() : 'pending';

        // Format the dates
        let registrationDate, submissionDate;
        try {
            registrationDate = new Date(user.createdAt || verification.submissionDate).toLocaleDateString();
            submissionDate = new Date(verification.submissionDate).toLocaleDateString();
        } catch (e) {
            registrationDate = 'Unknown date';
            submissionDate = 'Unknown date';
        }

        return `
        <tr>
            <td>
                <div class="user-cell">
                    <img src="${user.profilePicture || defaultAvatar}"
                         alt="${user.name}"
                         class="table-avatar"
                         onerror="this.src='${defaultAvatar}'">
                    <div class="user-info">
                        <h4>${user.name}</h4>
                        <p>${user.email}</p>
                    </div>
                </div>
            </td>
            <td>${registrationDate}</td>
            <td>${user.location || 'Unknown location'}</td>
            <td>
                <span class="status-badge ${status}">
                    ${status.toUpperCase()}
                </span>
            </td>
            <td>${submissionDate}</td>
            <td>
                <div class="action-cell">
                    <button class="table-action" onclick="viewVerificationDetails('${verification.id}')" title="View Details">
                        👁️
                    </button>
                    ${status === 'pending' ? `
                        <button class="table-action" onclick="handleVerificationAction('approve', '${verification.id}')" title="Approve">
                            ✓
                        </button>
                        <button class="table-action delete" onclick="handleVerificationAction('reject', '${verification.id}')" title="Reject">
                            ✕
                        </button>
                    ` : ''}
                </div>
            </td>
        </tr>
        `;
    }).join('');
}

// View verification details
async function viewVerificationDetails(id) {
    try {
        currentVerificationId = id;
        if (verificationModalOverlay) {
            verificationModalOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        } else {
            console.error('Verification modal not found');
            return;
        }

        const modalContent = document.getElementById('verificationModalContent');
        if (!modalContent) {
            console.error('Verification modal content not found');
            return;
        }

        modalContent.innerHTML = '<div class="loading-spinner"></div>';

        try {
            const response = await fetch(`/api/admin/verification-queue/${id}`);

            if (!response.ok) {
                throw new Error(`Failed to fetch verification details: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();

            // Create a placeholder for missing data
            const user = data.user || {
                name: 'Unknown User',
                email: 'No email provided',
                phone: 'No phone provided',
                location: 'Unknown location'
            };

            const documents = data.documents || [];

            modalContent.innerHTML = `
                <div class="verification-details">
                    <h4>User Information</h4>
                    <p><strong>Name:</strong> ${user.name}</p>
                    <p><strong>Email:</strong> ${user.email}</p>
                    <p><strong>Phone:</strong> ${user.phone}</p>
                    <p><strong>Location:</strong> ${user.location}</p>

                    <h4>Verification Documents</h4>
                    <div class="document-grid">
                        ${documents.length > 0 ? documents.map(doc => `
                            <div class="document-item">
                                <img src="${doc.url}" alt="${doc.type}" onclick="viewFullImage('${doc.url}')">
                                <p>${doc.type}</p>
                            </div>
                        `).join('') : '<p>No documents provided</p>'}
                    </div>

                    <h4>Additional Notes</h4>
                    <p>${data.notes || 'No additional notes'}</p>
                </div>
            `;
        } catch (error) {
            console.error('Error fetching verification details:', error);

            // Display a more user-friendly error message with mock data for testing
            modalContent.innerHTML = `
                <div class="verification-details">
                    <div class="error-message">
                        <p>Failed to load verification details from the server. This could be due to:</p>
                        <ul>
                            <li>The API endpoint is not implemented yet</li>
                            <li>The server is not running</li>
                            <li>Network connectivity issues</li>
                        </ul>
                        <p>Error details: ${error.message}</p>
                    </div>

                    <h4>Sample Verification Data (For Testing)</h4>
                    <p><strong>Verification ID:</strong> ${id}</p>

                    <h4>User Information</h4>
                    <p><strong>Name:</strong> Sample User</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Phone:</strong> +91 98765 43210</p>
                    <p><strong>Location:</strong> Mumbai, Maharashtra</p>

                    <h4>Verification Documents</h4>
                    <div class="document-grid">
                        <p>Sample documents would be displayed here</p>
                    </div>

                    <h4>Additional Notes</h4>
                    <p>User has submitted all required documents for verification.</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error in viewVerificationDetails function:', error);
        showError('Failed to load verification details. Please try again.');
    }
}

// Handle verification action (approve/reject)
async function handleVerificationAction(action, id = currentVerificationId) {
    if (!id) {
        showError('No verification selected');
        return;
    }

    try {
        // For testing purposes, simulate a successful API call
        // In production, uncomment the fetch call below
        /*
        const response = await fetch(`/api/admin/verification-queue/${id}/${action}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.message || `Failed to ${action} verification`);
        }
        */

        // Simulate successful response for testing
        console.log(`API call would be: PUT /api/admin/verification-queue/${id}/${action}`);

        // Show success message
        showSuccess(`Successfully ${action}ed verification`);

        // Close the verification details modal
        if (verificationModalOverlay) {
            verificationModalOverlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        // Refresh the table
        fetchVerificationQueue();
    } catch (error) {
        console.error(`Error ${action}ing verification:`, error);
        showError(`Failed to ${action} verification. Please try again.`);
    }
}

// Update pagination
function updatePagination(total) {
    totalPages = Math.ceil(total / itemsPerPage);

    let paginationHTML = '';

    // Previous button
    paginationHTML += `
        <button class="page-item ${currentPage === 1 ? 'disabled' : ''}"
                onclick="changePage(${currentPage - 1})"
                ${currentPage === 1 ? 'disabled' : ''}>
            ←
        </button>
    `;

    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            paginationHTML += `
                <button class="page-item ${i === currentPage ? 'active' : ''}"
                        onclick="changePage(${i})">
                    ${i}
                </button>
            `;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            paginationHTML += '<button class="page-item disabled">...</button>';
        }
    }

    // Next button
    paginationHTML += `
        <button class="page-item ${currentPage === totalPages ? 'disabled' : ''}"
                onclick="changePage(${currentPage + 1})"
                ${currentPage === totalPages ? 'disabled' : ''}>
            →
        </button>
    `;

    paginationControls.innerHTML = paginationHTML;
}

// Change page
function changePage(page) {
    if (page < 1 || page > totalPages || page === currentPage) return;
    currentPage = page;
    fetchVerificationQueue();
}

// Utility functions
function showLoading() {
    verificationTableBody.innerHTML = `
        <tr>
            <td colspan="5" class="loading-cell">
                <div class="loading-spinner"></div>
                <p>Loading verification queue...</p>
            </td>
        </tr>
    `;
}

function hideLoading() {
    // Loading state is replaced by the actual content
}

function showError(message) {
    // Implement your error message display logic
    alert(message);
}

function showSuccess(message) {
    // Implement your success message display logic
    alert(message);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// View full image in a modal
function viewFullImage(url) {
    // Create modal element
    const modal = document.createElement('div');
    modal.className = 'modal-overlay active';

    // Set modal content
    modal.innerHTML = `
        <div class="modal">
            <div class="modal-header">
                <h2 class="modal-title">Document Preview</h2>
                <button class="modal-close-button" onclick="this.closest('.modal-overlay').remove()">&times;</button>
            </div>
            <div class="modal-body">
                <img src="${url}" alt="Document" style="max-width: 100%; height: auto;">
            </div>
        </div>
    `;

    // Add modal to document
    document.body.appendChild(modal);
}