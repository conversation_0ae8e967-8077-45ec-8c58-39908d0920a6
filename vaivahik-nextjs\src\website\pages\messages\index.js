import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import ConversationList from '@/components/chat/ConversationList';
import PromotionBanner from '@/components/chat/PromotionBanner';
import MainLayout from '@/components/layouts/MainLayout';
import styles from '@/styles/MessagesPage.module.css';

export default function MessagesPage() {
  const [userId, setUserId] = useState(null);
  const [token, setToken] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [chatSettings, setChatSettings] = useState(null);
  const [activePromotion, setActivePromotion] = useState(null);

  const router = useRouter();
  const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

  // Check authentication
  useEffect(() => {
    const storedToken = localStorage.getItem('token');
    const storedUserId = localStorage.getItem('userId');

    if (!storedToken || !storedUserId) {
      router.push('/login?redirect=/messages');
      return;
    }

    setToken(storedToken);
    setUserId(storedUserId);
    setLoading(false);

    // Fetch chat settings including promotions
    fetchChatSettings(storedToken);
  }, [router]);

  // Fetch chat settings
  const fetchChatSettings = async (token) => {
    try {
      const response = await fetch(`${apiBaseUrl}/api/users/chat-settings`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch chat settings');
      }

      const data = await response.json();

      if (data.success) {
        setChatSettings(data.settings);

        // Set active promotion if available
        if (data.settings.promotions &&
            data.settings.promotions.hasActivePromotions &&
            data.settings.promotions.activePromotions.length > 0) {
          setActivePromotion(data.settings.promotions.activePromotions[0]);
        }
      }
    } catch (error) {
      console.error('Error fetching chat settings:', error);
    }
  };

  // Handle conversation selection
  const handleSelectConversation = (conversationId) => {
    router.push(`/messages/${conversationId}`);
  };

  if (loading) {
    return (
      <MainLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading messages...</p>
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <div className={styles.errorContainer}>
          <h2>Error</h2>
          <p>{error}</p>
          <button onClick={() => router.reload()}>Try Again</button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <Head>
        <title>Messages | Vaivahik</title>
        <meta name="description" content="Your conversations on Vaivahik" />
      </Head>

      <div className={styles.messagesPage}>
        {activePromotion && (
          <div className={styles.promotionContainer}>
            <PromotionBanner promotion={activePromotion} />
          </div>
        )}

        <div className={styles.conversationListContainer}>
          <ConversationList
            userId={userId}
            token={token}
            apiBaseUrl={apiBaseUrl}
            onSelectConversation={handleSelectConversation}
            chatSettings={chatSettings}
          />
        </div>

        <div className={styles.emptyStateContainer}>
          <div className={styles.emptyState}>
            <div className={styles.emptyStateIcon}>💬</div>
            <h2>Select a Conversation</h2>
            <p>Choose a conversation from the list or start a new one.</p>
            {activePromotion && (
              <div className={styles.promotionHighlight}>
                <p><strong>{activePromotion.title}</strong> - {activePromotion.description}</p>
              </div>
            )}
            <button
              className={styles.newChatButton}
              onClick={() => router.push('/matches')}
            >
              Find Matches to Chat With
            </button>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
