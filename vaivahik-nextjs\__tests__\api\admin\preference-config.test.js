/**
 * Unit tests for the preference configuration API
 */
import { createMocks } from 'node-mocks-http';
import handler from '@/pages/api/admin/preference-config';
import { withAuth } from '@/utils/authHandler';
import { PrismaClient } from '@prisma/client';

// Mock next-auth
jest.mock('@/utils/authHandler', () => ({
  withAuth: jest.fn((handler) => handler),
}));

// Mock Prisma client
jest.mock('@prisma/client', () => {
  const mockPrismaClient = {
    preferenceCategory: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    preferenceField: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    preferenceOption: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    preferenceImportance: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    systemConfig: {
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    $connect: jest.fn(),
    $disconnect: jest.fn(),
  };

  return {
    PrismaClient: jest.fn(() => mockPrismaClient),
  };
});

// Get the mocked Prisma client
const prisma = new PrismaClient();

describe('Preference Configuration API', () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/admin/preference-config', () => {
    test('should return all preference configuration data', async () => {
      // Mock data
      const mockCategories = [{ id: 'cat1', name: 'test_category' }];
      const mockFields = [{ id: 'field1', name: 'test_field', categoryId: 'cat1' }];
      const mockOptions = [{ id: 'opt1', value: 'test_option', fieldId: 'field1' }];
      const mockImportanceSettings = [{ id: 'imp1', fieldId: 'field1', importanceLevel: 5 }];
      const mockDefaultPreferences = { test: 'value' };

      // Setup Prisma mock responses
      prisma.preferenceCategory.findMany.mockResolvedValue(mockCategories);
      prisma.preferenceField.findMany.mockResolvedValue(mockFields);
      prisma.preferenceOption.findMany.mockResolvedValue(mockOptions);
      prisma.preferenceImportance.findMany.mockResolvedValue(mockImportanceSettings);
      prisma.systemConfig.findFirst.mockResolvedValue({
        configValue: JSON.stringify(mockDefaultPreferences),
      });

      // Create mock request and response
      const { req, res } = createMocks({
        method: 'GET',
        query: {},
      });

      // Call the handler
      await handler(req, res);

      // Assertions
      expect(res.statusCode).toBe(200);
      expect(JSON.parse(res._getData())).toEqual({
        success: true,
        categories: mockCategories,
        fields: mockFields,
        options: mockOptions,
        importanceSettings: mockImportanceSettings,
        defaultPreferences: mockDefaultPreferences,
      });
    });

    test('should return only categories when type=categories', async () => {
      // Mock data
      const mockCategories = [{ id: 'cat1', name: 'test_category' }];

      // Setup Prisma mock responses
      prisma.preferenceCategory.findMany.mockResolvedValue(mockCategories);

      // Create mock request and response
      const { req, res } = createMocks({
        method: 'GET',
        query: { type: 'categories' },
      });

      // Call the handler
      await handler(req, res);

      // Assertions
      expect(res.statusCode).toBe(200);
      expect(JSON.parse(res._getData())).toEqual({
        success: true,
        categories: mockCategories,
      });
    });

    test('should handle database errors gracefully', async () => {
      // Setup Prisma mock to throw an error
      prisma.preferenceCategory.findMany.mockRejectedValue(new Error('Database error'));

      // Create mock request and response
      const { req, res } = createMocks({
        method: 'GET',
        query: { type: 'categories' },
      });

      // Call the handler
      await handler(req, res);

      // Assertions
      expect(res.statusCode).toBe(500);
      expect(JSON.parse(res._getData())).toHaveProperty('success', false);
    });
  });

  describe('PUT /api/admin/preference-config', () => {
    test('should update categories successfully', async () => {
      // Mock data
      const mockCategories = [
        { id: 'cat1', name: 'updated_category', displayName: 'Updated Category' },
      ];
      const mockUpdatedCategories = [
        { id: 'cat1', name: 'updated_category', displayName: 'Updated Category' },
      ];

      // Setup Prisma mock responses
      prisma.preferenceCategory.update.mockResolvedValue(mockCategories[0]);
      prisma.preferenceCategory.findMany.mockResolvedValue(mockUpdatedCategories);

      // Create mock request and response
      const { req, res } = createMocks({
        method: 'PUT',
        body: {
          type: 'categories',
          data: mockCategories,
        },
      });

      // Call the handler
      await handler(req, res);

      // Assertions
      expect(res.statusCode).toBe(200);
      expect(JSON.parse(res._getData())).toEqual({
        success: true,
        message: 'Categories updated successfully',
        data: mockUpdatedCategories,
      });
      expect(prisma.preferenceCategory.update).toHaveBeenCalledWith({
        where: { id: 'cat1' },
        data: expect.objectContaining({
          name: 'updated_category',
          displayName: 'Updated Category',
        }),
      });
    });

    test('should handle validation errors', async () => {
      // Create mock request and response with missing data
      const { req, res } = createMocks({
        method: 'PUT',
        body: {
          type: 'categories',
          // Missing data
        },
      });

      // Call the handler
      await handler(req, res);

      // Assertions
      expect(res.statusCode).toBe(400);
      expect(JSON.parse(res._getData())).toEqual({
        success: false,
        message: 'Type and data are required',
      });
    });
  });

  describe('DELETE /api/admin/preference-config', () => {
    test('should delete a category successfully', async () => {
      // Setup Prisma mock responses
      prisma.preferenceCategory.delete.mockResolvedValue({ id: 'cat1' });

      // Create mock request and response
      const { req, res } = createMocks({
        method: 'DELETE',
        query: {
          type: 'category',
          id: 'cat1',
        },
      });

      // Call the handler
      await handler(req, res);

      // Assertions
      expect(res.statusCode).toBe(200);
      expect(JSON.parse(res._getData())).toEqual({
        success: true,
        message: 'Category deleted successfully',
      });
      expect(prisma.preferenceCategory.delete).toHaveBeenCalledWith({
        where: { id: 'cat1' },
      });
    });

    test('should handle validation errors', async () => {
      // Create mock request and response with missing id
      const { req, res } = createMocks({
        method: 'DELETE',
        query: {
          type: 'category',
          // Missing id
        },
      });

      // Call the handler
      await handler(req, res);

      // Assertions
      expect(res.statusCode).toBe(400);
      expect(JSON.parse(res._getData())).toEqual({
        success: false,
        message: 'Type and ID are required',
      });
    });
  });
});
