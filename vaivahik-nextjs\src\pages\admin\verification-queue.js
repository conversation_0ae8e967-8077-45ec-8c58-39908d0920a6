import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import styles from '@/styles/verification-queue.module.css';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);

export default function VerificationQueue() {
  const [verifications, setVerifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentVerification, setCurrentVerification] = useState(null);
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmAction, setConfirmAction] = useState('');

  useEffect(() => {
    fetchVerificationQueue();
  }, [currentPage, searchQuery, selectedStatus, itemsPerPage]);

  const fetchVerificationQueue = async () => {
    // Only run in browser environment
    if (typeof window === 'undefined') {
      return;
    }

    setLoading(true);
    try {
      // Construct the API URL with query parameters
      let apiUrl = `/api/admin/verification-queue?page=${currentPage}&limit=${itemsPerPage}`;

      // Add optional query parameters if they exist
      if (selectedStatus !== 'all') apiUrl += `&status=${encodeURIComponent(selectedStatus)}`;
      if (searchQuery) apiUrl += `&search=${encodeURIComponent(searchQuery)}`;

      try {
        // Call the API endpoint
        const response = await fetch(apiUrl);

        if (!response.ok) {
          console.warn(`API returned status: ${response.status}. Using mock data instead.`);
          throw new Error(`Failed to fetch verification queue: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
          // Check if the data has the expected structure
          if (data.verifications) {
            setVerifications(data.verifications);
            setTotalPages(data.pagination?.totalPages || 1);
          } else if (data.users) {
            // Handle alternative API response structure
            setVerifications(data.users);
            setTotalPages(data.pagination?.totalPages || 1);
          } else {
            console.warn('Unexpected API response structure:', data);
            // Fallback to mock data
            throw new Error('Unexpected API response structure');
          }
        } else {
          console.warn('API returned error:', data.message);
          throw new Error(data.message || 'API returned unsuccessful response');
        }
      } catch (error) {
        console.warn('Using mock data as fallback due to API error:', error.message);
        // Generate mock data
        const mockData = generateMockVerifications();
        setVerifications(mockData);
        setTotalPages(1);
      }

      setLoading(false);
    } catch (error) {
      console.error('Error in fetchVerificationQueue:', error);
      // Fallback to mock data if API fails
      console.log('Using mock data as fallback');
      const mockData = generateMockVerifications();
      setVerifications(mockData);
      setTotalPages(1);
      setLoading(false);
    }
  };

  // View verification details
  const handleViewVerification = (verification) => {
    setCurrentVerification(verification);
    setShowVerificationModal(true);
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      document.body.classList.add('modal-open');
    }
  };

  // View document in full size
  const viewDocument = (doc) => {
    // Only run in browser environment
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return;
    }

    // Create a modal for document viewing
    const modal = document.createElement('div');
    modal.className = 'modal-overlay document-viewer-modal';
    modal.style.zIndex = '3000';

    // Use a fallback image if the URL is missing or invalid
    const documentUrl = doc.url || '/img/document-placeholder.svg';

    // Determine if the document is an image or PDF
    const isPdf = doc.name && doc.name.toLowerCase().endsWith('.pdf');

    // Create modal content
    if (isPdf) {
      modal.innerHTML = `
        <div class="modal document-modal">
          <div class="modal-header">
            <h2 class="modal-title">${doc.type || 'Document'} Preview</h2>
            <button class="modal-close-button" onclick="this.closest('.modal-overlay').remove(); document.body.classList.remove('modal-open');">&times;</button>
          </div>
          <div class="modal-body document-modal-body">
            <iframe src="${documentUrl}" width="100%" height="500px" style="border: none;"></iframe>
          </div>
          <div class="modal-footer">
            <button class="btn btn-primary" onclick="window.open('${documentUrl}', '_blank')">Open in New Tab</button>
            <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove(); document.body.classList.remove('modal-open');">Close</button>
          </div>
        </div>
      `;
    } else {
      modal.innerHTML = `
        <div class="modal document-modal">
          <div class="modal-header">
            <h2 class="modal-title">${doc.type || 'Document'} Preview</h2>
            <button class="modal-close-button" onclick="this.closest('.modal-overlay').remove(); document.body.classList.remove('modal-open');">&times;</button>
          </div>
          <div class="modal-body document-modal-body">
            <img
              src="${documentUrl}"
              alt="${doc.type || 'Document'}"
              class="document-full-image"
              style="max-width: 100%; height: auto; display: block; margin: 0 auto;"
              onerror="this.onerror=null; this.src='/img/document-placeholder.svg';"
            >
          </div>
          <div class="modal-footer">
            <button class="btn btn-primary" onclick="window.open('${documentUrl}', '_blank')">Open in New Tab</button>
            <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove(); document.body.classList.remove('modal-open');">Close</button>
          </div>
        </div>
      `;
    }

    // Add modal to document
    document.body.appendChild(modal);
    document.body.classList.add('modal-open');
  };

  // Handle approve verification
  const handleApproveVerification = (verification) => {
    setCurrentVerification(verification);
    setConfirmAction('approve');
    setShowConfirmModal(true);
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      document.body.classList.add('modal-open');
    }
  };

  // Handle reject verification
  const handleRejectVerification = (verification) => {
    setCurrentVerification(verification);
    setConfirmAction('reject');
    setShowConfirmModal(true);
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      document.body.classList.add('modal-open');
    }
  };

  // Confirm verification action
  const confirmVerificationAction = async () => {
    // Only run in browser environment
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return;
    }

    try {
      // Call the API endpoint
      const response = await fetch(`/api/admin/verification-queue/${currentVerification.id}?action=${confirmAction}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to ${confirmAction} verification: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Update local state
        const updatedVerifications = verifications.map(v => {
          if (v.id === currentVerification.id) {
            return {
              ...v,
              status: confirmAction === 'approve' ? 'approved' : 'rejected',
              actionDate: new Date().toISOString().split('T')[0],
              ...(confirmAction === 'approve' ? { approvedBy: 'Admin User' } : { rejectedBy: 'Admin User' }),
              ...(confirmAction === 'reject' ? { rejectionReason: 'Documents not valid' } : {})
            };
          }
          return v;
        });

        setVerifications(updatedVerifications);
        setShowConfirmModal(false);
        document.body.classList.remove('modal-open');

        // Show success message
        alert(data.message || `Verification ${confirmAction === 'approve' ? 'approved' : 'rejected'} successfully`);
      } else {
        console.error('API returned error:', data.message);
        alert(data.message || `Failed to ${confirmAction} verification. Please try again.`);
      }
    } catch (error) {
      console.error(`Error ${confirmAction}ing verification:`, error);
      alert(`Failed to ${confirmAction} verification. Please try again.`);
    }
  };

  // Close verification modal
  const closeVerificationModal = () => {
    setShowVerificationModal(false);
    setCurrentVerification(null);
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      document.body.classList.remove('modal-open');
    }
  };

  // Close confirmation modal
  const closeConfirmModal = () => {
    setShowConfirmModal(false);
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      document.body.classList.remove('modal-open');
    }
  };



  const generateMockVerifications = () => {
    return [
      {
        id: 1,
        user: {
          id: 101,
          name: 'Rahul Sharma',
          age: 28,
          location: 'Mumbai',
          registrationDate: '2023-01-15',
          photo: '/img/user-placeholder.svg',
          email: '<EMAIL>',
          phone: '+91 9876543210',
          occupation: 'Software Engineer',
          education: 'B.Tech in Computer Science',
          maritalStatus: 'Never Married',
          // Additional details
          height: '5\'10"',
          weight: '75 kg',
          complexion: 'Fair',
          religion: 'Hindu',
          caste: 'Maratha',
          subcaste: 'Deshmukh',
          gotra: 'Kashyap',
          diet: 'Vegetarian',
          smoking: 'No',
          drinking: 'Occasionally',
          income: '12-15 LPA',
          family: {
            father: 'Rajesh Sharma',
            fatherOccupation: 'Retired Government Officer',
            mother: 'Sunita Sharma',
            motherOccupation: 'Homemaker',
            brothers: 1,
            sisters: 1,
            familyType: 'Joint Family',
            familyValues: 'Traditional',
            familyStatus: 'Middle Class',
            familyLocation: 'Mumbai'
          },
          horoscope: {
            manglik: 'No',
            nakshatra: 'Rohini',
            rashi: 'Taurus'
          },
          preferences: {
            ageRange: '25-30',
            heightRange: '5\'2" - 5\'8"',
            education: 'Graduate',
            occupation: 'Any',
            location: 'Mumbai, Pune',
            caste: 'No Preference',
            income: 'Any'
          }
        },
        documents: [
          { id: 1, type: 'ID Proof', url: '/img/document-placeholder.svg', name: 'Aadhar Card' },
          { id: 2, type: 'Address Proof', url: '/img/document-placeholder.svg', name: 'Passport' }
        ],
        status: 'pending',
        submittedOn: '2023-07-10'
      },
      {
        id: 2,
        user: {
          id: 102,
          name: 'Priya Patel',
          age: 26,
          location: 'Pune',
          registrationDate: '2023-02-20',
          photo: '/img/user-placeholder.svg',
          email: '<EMAIL>',
          phone: '+91 9876543211',
          occupation: 'Doctor',
          education: 'MBBS',
          maritalStatus: 'Never Married'
        },
        documents: [
          { id: 3, type: 'ID Proof', url: '/img/document-placeholder.svg', name: 'Aadhar Card' },
          { id: 4, type: 'Address Proof', url: '/img/document-placeholder.svg', name: 'Voter ID' }
        ],
        status: 'pending',
        submittedOn: '2023-07-12'
      },
      {
        id: 3,
        user: {
          id: 103,
          name: 'Amit Desai',
          age: 30,
          location: 'Bangalore',
          registrationDate: '2023-03-05',
          photo: '/img/user-placeholder.svg',
          email: '<EMAIL>',
          phone: '+91 **********',
          occupation: 'Business Analyst',
          education: 'MBA in Finance',
          maritalStatus: 'Never Married'
        },
        documents: [
          { id: 5, type: 'ID Proof', url: '/img/document-placeholder.svg', name: 'PAN Card' },
          { id: 6, type: 'Address Proof', url: '/img/document-placeholder.svg', name: 'Driving License' }
        ],
        status: 'approved',
        submittedOn: '2023-07-08',
        actionDate: '2023-07-09',
        approvedBy: 'Admin User'
      },
      {
        id: 4,
        user: {
          id: 104,
          name: 'Neha Singh',
          age: 27,
          location: 'Delhi',
          registrationDate: '2023-04-10',
          photo: '/img/user-placeholder.svg',
          email: '<EMAIL>',
          phone: '+91 **********',
          occupation: 'Marketing Manager',
          education: 'MBA in Marketing',
          maritalStatus: 'Never Married'
        },
        documents: [
          { id: 7, type: 'ID Proof', url: '/img/document-placeholder.svg', name: 'Aadhar Card' },
          { id: 8, type: 'Address Proof', url: '/img/document-placeholder.svg', name: 'Passport' }
        ],
        status: 'rejected',
        submittedOn: '2023-07-05',
        actionDate: '2023-07-06',
        rejectionReason: 'Documents unclear',
        rejectedBy: 'Admin User'
      },
      {
        id: 5,
        user: {
          id: 105,
          name: 'Vikram Malhotra',
          age: 32,
          location: 'Chennai',
          registrationDate: '2023-05-15',
          photo: '/img/user-placeholder.svg',
          email: '<EMAIL>',
          phone: '+91 9876543214',
          occupation: 'Architect',
          education: 'B.Arch',
          maritalStatus: 'Never Married'
        },
        documents: [
          { id: 9, type: 'ID Proof', url: '/img/document-placeholder.svg', name: 'Aadhar Card' },
          { id: 10, type: 'Address Proof', url: '/img/document-placeholder.svg', name: 'Electricity Bill' }
        ],
        status: 'pending',
        submittedOn: '2023-07-14'
      }
    ];
  };

  return (
    <EnhancedAdminLayout title="Verification Queue">
      <div className="content-header">
        <h2 className="page-title">Verification Queue</h2>
        <div className="header-actions">
          <div className="search-container">
            <input
              type="text"
              className="search-input"
              placeholder="Search users..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <button className="search-button" title="Search">
              🔍
            </button>
          </div>
          <select
            className="status-filter"
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
          </select>
          <button
            className="refresh-button"
            onClick={fetchVerificationQueue}
          >
            ↻ Refresh
          </button>
        </div>
      </div>

      {loading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading verification queue...</p>
        </div>
      ) : (
        <div className="table-container">
          <table className="data-table">
            <thead>
              <tr>
                <th>User</th>
                <th>Registration Date</th>
                <th>Location</th>
                <th>Verification Status</th>
                <th>Submitted On</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {verifications.map(verification => (
                <tr key={verification.id}>
                  <td>
                    <div className="user-cell">
                      <div className="user-avatar">
                        {verification.user.photo ? (
                          <img
                            src={verification.user.photo}
                            alt={verification.user.name}
                            onError={(e) => {
                              e.target.onerror = null;
                              e.target.src = '/img/user-placeholder.svg';
                            }}
                          />
                        ) : (
                          <img
                            src="/img/user-placeholder.svg"
                            alt={verification.user.name}
                          />
                        )}
                      </div>
                      <div className="user-info">
                        <div className="user-name">{verification.user.name}</div>
                        <div className="user-age">{verification.user.age} years</div>
                      </div>
                    </div>
                  </td>
                  <td>{new Date(verification.user.registrationDate).toLocaleDateString()}</td>
                  <td>{verification.user.location}</td>
                  <td>
                    <span className={`${styles['status-badge']} ${styles[`status-badge-${verification.status}`]}`}>
                      {verification.status.charAt(0).toUpperCase() + verification.status.slice(1)}
                    </span>
                  </td>
                  <td>{new Date(verification.submittedOn).toLocaleDateString()}</td>
                  <td>
                    <div className="action-buttons">
                      <button
                        className="action-btn view-btn"
                        title="View Details"
                        onClick={() => handleViewVerification(verification)}
                      >
                        👁️
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          <div className="table-footer">
            <div className="entries-info">
              Showing {verifications.length > 0 ? 1 : 0} to {verifications.length} of {verifications.length} entries
            </div>
            <div className="pagination">
              <button
                className="pagination-btn"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              >
                Previous
              </button>
              <span className="pagination-info">Page {currentPage} of {totalPages}</span>
              <button
                className="pagination-btn"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Verification Details Modal */}
      {showVerificationModal && currentVerification && (
        <div className="modal-overlay" style={{ zIndex: 2000, position: 'fixed', left: 0, top: 0, width: '100%', height: '100%' }}>
          <div className="modal" style={{ margin: '0 auto', maxWidth: '800px', maxHeight: '90vh', overflowY: 'auto', position: 'relative', width: '100%' }}>
            <div className="modal-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <h2 className="modal-title">Verification Details</h2>
              <button
                className="modal-close-button"
                onClick={closeVerificationModal}
              >
                &times;
              </button>
            </div>
            <div className="modal-body" style={{ maxHeight: 'calc(90vh - 130px)', overflowY: 'auto', padding: '20px' }}>
              <div className="verification-tabs" style={{ display: 'block' }}>
                <div className="tab-content" style={{ display: 'block' }}>
                  {/* User Profile Header */}
                  <div className="user-profile-section" style={{ marginBottom: '20px' }}>
                    <div className="user-profile-header" style={{ display: 'flex', alignItems: 'center', gap: '15px', paddingBottom: '15px', borderBottom: '1px solid #eee' }}>
                      <div className="user-avatar large" style={{ width: '80px', height: '80px', borderRadius: '50%', overflow: 'hidden', display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#5e35b1', color: 'white', fontSize: '2rem' }}>
                        {currentVerification.user.photo ? (
                          <img
                            src={currentVerification.user.photo}
                            alt={currentVerification.user.name}
                            style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                            onError={(e) => {
                              e.target.onerror = null;
                              e.target.src = '/img/user-placeholder.svg';
                            }}
                          />
                        ) : (
                          <img
                            src="/img/user-placeholder.svg"
                            alt={currentVerification.user.name}
                            style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                          />
                        )}
                      </div>
                      <div className="user-profile-info" style={{ flex: 1 }}>
                        <h3 className="user-name" style={{ margin: '0 0 5px 0', fontSize: '1.5rem' }}>{currentVerification.user.name}</h3>
                        <div className="user-meta" style={{ display: 'flex', gap: '8px', color: '#666', marginBottom: '8px' }}>
                          <span>{currentVerification.user.age} years</span>
                          <span>•</span>
                          <span>{currentVerification.user.location}</span>
                        </div>
                        <div className="verification-status" style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                          <span className={`status-badge ${currentVerification.status}`} style={{ padding: '4px 10px', borderRadius: '4px', fontSize: '0.85rem', fontWeight: '500' }}>
                            {currentVerification.status.charAt(0).toUpperCase() + currentVerification.status.slice(1)}
                          </span>
                          {currentVerification.status !== 'pending' && (
                            <span className="verification-date" style={{ fontSize: '0.85rem', color: '#666' }}>
                              on {new Date(currentVerification.actionDate).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Personal Information */}
                  <div className="tab-section">
                    <h4 className="section-title">Personal Information</h4>
                    <div className="user-details-grid">
                      {/* Essential fields with fallbacks */}
                      <div className="detail-item">
                        <div className="detail-label">Full Name</div>
                        <div className="detail-value">
                          {currentVerification?.user?.name || 'Not available'}
                        </div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Age</div>
                        <div className="detail-value">
                          {currentVerification?.user?.age ? `${currentVerification.user.age} years` : 'Not available'}
                        </div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Location</div>
                        <div className="detail-value">
                          {currentVerification?.user?.location || 'Not available'}
                        </div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Email</div>
                        <div className="detail-value">
                          {currentVerification?.user?.email || 'Not available'}
                        </div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Phone</div>
                        <div className="detail-value">
                          {currentVerification?.user?.phone || 'Not available'}
                        </div>
                      </div>

                      {/* Optional physical attributes */}
                      {(currentVerification?.user?.height ||
                        currentVerification?.user?.weight ||
                        currentVerification?.user?.complexion) && (
                        <>
                          {currentVerification?.user?.height && (
                            <div className="detail-item">
                              <div className="detail-label">Height</div>
                              <div className="detail-value">{currentVerification.user.height}</div>
                            </div>
                          )}
                          {currentVerification?.user?.weight && (
                            <div className="detail-item">
                              <div className="detail-label">Weight</div>
                              <div className="detail-value">{currentVerification.user.weight}</div>
                            </div>
                          )}
                          {currentVerification?.user?.complexion && (
                            <div className="detail-item">
                              <div className="detail-label">Complexion</div>
                              <div className="detail-value">{currentVerification.user.complexion}</div>
                            </div>
                          )}
                        </>
                      )}

                      {/* Optional lifestyle attributes */}
                      {(currentVerification?.user?.diet ||
                        currentVerification?.user?.smoking ||
                        currentVerification?.user?.drinking) && (
                        <>
                          {currentVerification?.user?.diet && (
                            <div className="detail-item">
                              <div className="detail-label">Diet</div>
                              <div className="detail-value">{currentVerification.user.diet}</div>
                            </div>
                          )}
                          {currentVerification?.user?.smoking && (
                            <div className="detail-item">
                              <div className="detail-label">Smoking</div>
                              <div className="detail-value">{currentVerification.user.smoking}</div>
                            </div>
                          )}
                          {currentVerification?.user?.drinking && (
                            <div className="detail-item">
                              <div className="detail-label">Drinking</div>
                              <div className="detail-value">{currentVerification.user.drinking}</div>
                            </div>
                          )}
                        </>
                      )}

                      <div className="detail-item">
                        <div className="detail-label">Marital Status</div>
                        <div className="detail-value">
                          {currentVerification?.user?.maritalStatus || 'Not available'}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Professional Information - Only show if at least one field exists */}
                  {(currentVerification?.user?.education ||
                    currentVerification?.user?.occupation ||
                    currentVerification?.user?.income) && (
                    <div className="tab-section">
                      <h4 className="section-title">Professional Information</h4>
                      <div className="user-details-grid">
                        {currentVerification?.user?.education && (
                          <div className="detail-item">
                            <div className="detail-label">Education</div>
                            <div className="detail-value">{currentVerification.user.education}</div>
                          </div>
                        )}
                        {currentVerification?.user?.occupation && (
                          <div className="detail-item">
                            <div className="detail-label">Occupation</div>
                            <div className="detail-value">{currentVerification.user.occupation}</div>
                          </div>
                        )}
                        {currentVerification?.user?.income && (
                          <div className="detail-item">
                            <div className="detail-label">Income</div>
                            <div className="detail-value">{currentVerification.user.income}</div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Religious Information - Only show if at least one field exists */}
                  {(currentVerification?.user?.religion ||
                    currentVerification?.user?.caste ||
                    currentVerification?.user?.subcaste ||
                    currentVerification?.user?.gotra) && (
                    <div className="tab-section">
                      <h4 className="section-title">Religious Information</h4>
                      <div className="user-details-grid">
                        {currentVerification?.user?.religion && (
                          <div className="detail-item">
                            <div className="detail-label">Religion</div>
                            <div className="detail-value">{currentVerification.user.religion}</div>
                          </div>
                        )}
                        {currentVerification?.user?.caste && (
                          <div className="detail-item">
                            <div className="detail-label">Caste</div>
                            <div className="detail-value">{currentVerification.user.caste}</div>
                          </div>
                        )}
                        {currentVerification?.user?.subcaste && (
                          <div className="detail-item">
                            <div className="detail-label">Subcaste</div>
                            <div className="detail-value">{currentVerification.user.subcaste}</div>
                          </div>
                        )}
                        {currentVerification?.user?.gotra && (
                          <div className="detail-item">
                            <div className="detail-label">Gotra</div>
                            <div className="detail-value">{currentVerification.user.gotra}</div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Family Information - Only show if family object exists */}
                  {currentVerification?.user?.family && (
                    <div className="tab-section">
                      <h4 className="section-title">Family Information</h4>
                      <div className="user-details-grid">
                        {currentVerification.user.family.father && (
                          <div className="detail-item">
                            <div className="detail-label">Father's Name</div>
                            <div className="detail-value">{currentVerification.user.family.father}</div>
                          </div>
                        )}
                        {currentVerification.user.family.fatherOccupation && (
                          <div className="detail-item">
                            <div className="detail-label">Father's Occupation</div>
                            <div className="detail-value">{currentVerification.user.family.fatherOccupation}</div>
                          </div>
                        )}
                        {currentVerification.user.family.mother && (
                          <div className="detail-item">
                            <div className="detail-label">Mother's Name</div>
                            <div className="detail-value">{currentVerification.user.family.mother}</div>
                          </div>
                        )}
                        {currentVerification.user.family.motherOccupation && (
                          <div className="detail-item">
                            <div className="detail-label">Mother's Occupation</div>
                            <div className="detail-value">{currentVerification.user.family.motherOccupation}</div>
                          </div>
                        )}

                        {/* Siblings information */}
                        {currentVerification.user.family.brothers !== undefined && (
                          <div className="detail-item">
                            <div className="detail-label">Brothers</div>
                            <div className="detail-value">{currentVerification.user.family.brothers}</div>
                          </div>
                        )}
                        {currentVerification.user.family.sisters !== undefined && (
                          <div className="detail-item">
                            <div className="detail-label">Sisters</div>
                            <div className="detail-value">{currentVerification.user.family.sisters}</div>
                          </div>
                        )}

                        {/* Family details */}
                        {currentVerification.user.family.familyType && (
                          <div className="detail-item">
                            <div className="detail-label">Family Type</div>
                            <div className="detail-value">{currentVerification.user.family.familyType}</div>
                          </div>
                        )}
                        {currentVerification.user.family.familyValues && (
                          <div className="detail-item">
                            <div className="detail-label">Family Values</div>
                            <div className="detail-value">{currentVerification.user.family.familyValues}</div>
                          </div>
                        )}
                        {currentVerification.user.family.familyStatus && (
                          <div className="detail-item">
                            <div className="detail-label">Family Status</div>
                            <div className="detail-value">{currentVerification.user.family.familyStatus}</div>
                          </div>
                        )}
                        {currentVerification.user.family.familyLocation && (
                          <div className="detail-item">
                            <div className="detail-label">Family Location</div>
                            <div className="detail-value">{currentVerification.user.family.familyLocation}</div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Horoscope Information - Only show if horoscope object exists */}
                  {currentVerification?.user?.horoscope && (
                    <div className="tab-section">
                      <h4 className="section-title">Horoscope Information</h4>
                      <div className="user-details-grid">
                        {currentVerification.user.horoscope.manglik && (
                          <div className="detail-item">
                            <div className="detail-label">Manglik</div>
                            <div className="detail-value">{currentVerification.user.horoscope.manglik}</div>
                          </div>
                        )}
                        {currentVerification.user.horoscope.nakshatra && (
                          <div className="detail-item">
                            <div className="detail-label">Nakshatra</div>
                            <div className="detail-value">{currentVerification.user.horoscope.nakshatra}</div>
                          </div>
                        )}
                        {currentVerification.user.horoscope.rashi && (
                          <div className="detail-item">
                            <div className="detail-label">Rashi</div>
                            <div className="detail-value">{currentVerification.user.horoscope.rashi}</div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Account Information */}
                  <div className="tab-section">
                    <h4 className="section-title">Account Information</h4>
                    <div className="user-details-grid">
                      {currentVerification?.user?.registrationDate ? (
                        <div className="detail-item">
                          <div className="detail-label">Registration Date</div>
                          <div className="detail-value">
                            {new Date(currentVerification.user.registrationDate).toLocaleDateString()}
                          </div>
                        </div>
                      ) : (
                        <div className="detail-item">
                          <div className="detail-label">Registration Date</div>
                          <div className="detail-value">Not available</div>
                        </div>
                      )}
                      {currentVerification?.submittedOn ? (
                        <div className="detail-item">
                          <div className="detail-label">Verification Submitted</div>
                          <div className="detail-value">
                            {new Date(currentVerification.submittedOn).toLocaleDateString()}
                          </div>
                        </div>
                      ) : (
                        <div className="detail-item">
                          <div className="detail-label">Verification Submitted</div>
                          <div className="detail-value">Not available</div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Document Viewer Section */}
                  <div className="tab-section document-section" style={{ marginTop: '30px' }}>
                    <h4 className="section-title" style={{ marginBottom: '15px', fontSize: '1.2rem', fontWeight: '600' }}>Verification Documents</h4>
                    <div className="documents-grid" style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))', gap: '20px' }}>
                      {currentVerification.documents && currentVerification.documents.length > 0 ? (
                        currentVerification.documents.map((document, index) => (
                          <div key={index} className="document-card" onClick={() => viewDocument(document)} style={{ cursor: 'pointer', border: '1px solid #eee', borderRadius: '8px', overflow: 'hidden', boxShadow: '0 2px 4px rgba(0,0,0,0.05)', transition: 'transform 0.2s, box-shadow 0.2s' }}>
                            <div className="document-preview" style={{ height: '150px', overflow: 'hidden', backgroundColor: '#f9f9f9', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                              <img
                                src={document.url || '/img/document-placeholder.svg'}
                                alt={document.type || 'Document'}
                                style={{ maxWidth: '100%', maxHeight: '100%', objectFit: 'contain' }}
                                onError={(e) => {
                                  e.target.onerror = null;
                                  e.target.src = '/img/document-placeholder.svg';
                                }}
                              />
                            </div>
                            <div className="document-info" style={{ padding: '12px' }}>
                              <div className="document-type" style={{ fontWeight: '600', marginBottom: '4px' }}>{document.type || 'Document'}</div>
                              <div className="document-name" style={{ fontSize: '0.9rem', color: '#666', marginBottom: '8px' }}>{document.name || 'View Document'}</div>
                              <div className="document-action" style={{ fontSize: '0.8rem', color: '#5e35b1', fontWeight: '500' }}>Click to enlarge</div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="no-documents" style={{ gridColumn: '1 / -1', padding: '20px', textAlign: 'center', backgroundColor: '#f9f9f9', borderRadius: '8px', color: '#666' }}>No verification documents uploaded</div>
                      )}
                    </div>
                  </div>

                  {/* Verification Actions */}
                  {currentVerification.status === 'pending' && (
                    <div className="tab-section action-section" style={{ marginTop: '30px', backgroundColor: '#f9f9f9', borderRadius: '8px', padding: '20px' }}>
                      <h4 className="section-title" style={{ marginBottom: '15px', fontSize: '1.2rem', fontWeight: '600' }}>Verification Actions</h4>
                      <div className="action-buttons-container" style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
                        <div className="action-description" style={{ marginBottom: '10px' }}>
                          <p style={{ margin: '0', color: '#555' }}>Please review the user's information and documents carefully before taking action.</p>
                        </div>
                        <div className="action-buttons-row" style={{ display: 'flex', gap: '15px', flexWrap: 'wrap' }}>
                          <button
                            className="btn btn-success btn-lg"
                            onClick={() => handleApproveVerification(currentVerification)}
                            style={{ padding: '12px 24px', backgroundColor: '#4caf50', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer', fontWeight: '500', display: 'flex', alignItems: 'center', gap: '8px' }}
                          >
                            <span className="action-icon">✓</span> Approve Verification
                          </button>
                          <button
                            className="btn btn-danger btn-lg"
                            onClick={() => handleRejectVerification(currentVerification)}
                            style={{ padding: '12px 24px', backgroundColor: '#f44336', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer', fontWeight: '500', display: 'flex', alignItems: 'center', gap: '8px' }}
                          >
                            <span className="action-icon">✗</span> Reject Verification
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="modal-footer" style={{ display: 'flex', justifyContent: 'flex-end', gap: '10px', padding: '15px 20px', borderTop: '1px solid #eee' }}>
              {currentVerification.status === 'pending' && (
                <>
                  <button
                    className="btn btn-success"
                    onClick={() => handleApproveVerification(currentVerification)}
                    style={{ padding: '8px 16px', backgroundColor: '#4caf50', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer', fontWeight: '500', display: 'flex', alignItems: 'center', gap: '5px' }}
                  >
                    <span className="action-icon">✓</span> Approve
                  </button>
                  <button
                    className="btn btn-danger"
                    onClick={() => handleRejectVerification(currentVerification)}
                    style={{ padding: '8px 16px', backgroundColor: '#f44336', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer', fontWeight: '500', display: 'flex', alignItems: 'center', gap: '5px' }}
                  >
                    <span className="action-icon">✗</span> Reject
                  </button>
                </>
              )}
              <button
                className="btn btn-secondary"
                onClick={closeVerificationModal}
                style={{ padding: '8px 16px', backgroundColor: '#f0f0f0', color: '#333', border: 'none', borderRadius: '4px', cursor: 'pointer', fontWeight: '500' }}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmModal && currentVerification && (
        <div className="modal-overlay" style={{ zIndex: 2100 }}>
          <div className="modal confirmation-modal" style={{ margin: '0 auto', maxWidth: '500px' }}>
            <div className="modal-header">
              <h2 className="modal-title">
                {confirmAction === 'approve' ? 'Approve Verification' : 'Reject Verification'}
              </h2>
              <button
                className="modal-close-button"
                onClick={closeConfirmModal}
              >
                &times;
              </button>
            </div>
            <div className="modal-body">
              <div className="confirmation-message">
                <div className="confirmation-icon">
                  {confirmAction === 'approve' ? '✓' : '✗'}
                </div>
                <p>
                  Are you sure you want to <strong>{confirmAction}</strong> the verification for <strong>{currentVerification.user.name}</strong>?
                </p>
                {confirmAction === 'reject' && (
                  <div className="rejection-reason">
                    <label htmlFor="rejectionReason">Rejection Reason:</label>
                    <textarea
                      id="rejectionReason"
                      placeholder="Enter reason for rejection"
                      rows="3"
                    ></textarea>
                  </div>
                )}
              </div>
            </div>
            <div className="modal-footer">
              <button
                className="btn btn-secondary"
                onClick={closeConfirmModal}
              >
                Cancel
              </button>
              <button
                className={`btn ${confirmAction === 'approve' ? 'btn-success' : 'btn-danger'}`}
                onClick={confirmVerificationAction}
              >
                {confirmAction === 'approve' ? 'Approve' : 'Reject'}
              </button>
            </div>
          </div>
        </div>
      )}

      <style jsx global>{`
        /* Force body to show modal */
        body.modal-open {
          overflow: hidden;
        }

        /* Force modal to display */
        .modal-overlay {
          display: flex !important;
        }
        /* Table Styles */
        .table-container {
          background-color: white;
          border-radius: 12px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          overflow: hidden;
          margin-bottom: 30px;
        }

        .data-table {
          width: 100%;
          border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
          padding: 15px;
          text-align: left;
          border-bottom: 1px solid #eee;
        }

        .data-table th {
          background-color: #f9f9f9;
          font-weight: 600;
          color: #555;
        }

        .data-table tr:last-child td {
          border-bottom: none;
        }

        .data-table tr:hover {
          background-color: #f5f5f5;
        }

        /* User Cell */
        .user-cell {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .user-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background-color: var(--primary-light);
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 1.1rem;
          overflow: hidden;
        }

        .user-avatar img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .user-info {
          display: flex;
          flex-direction: column;
        }

        .user-name {
          font-weight: 600;
          color: var(--text-dark);
        }

        .user-age {
          font-size: 0.8rem;
          color: #666;
        }

        /* Status Badge */
        .status-badge {
          display: inline-block;
          padding: 5px 10px;
          border-radius: 12px;
          font-size: 0.75rem;
          font-weight: 500;
        }

        .status-badge.pending {
          background-color: #fff8e1;
          color: #ff8f00;
        }

        .status-badge.approved {
          background-color: #e8f5e9;
          color: #2e7d32;
        }

        .status-badge.rejected {
          background-color: #ffebee;
          color: #c62828;
        }

        /* Action Buttons */
        .action-buttons {
          display: flex;
          gap: 8px;
        }

        .action-btn {
          background: none;
          border: none;
          cursor: pointer;
          font-size: 1rem;
          padding: 4px;
          border-radius: 4px;
          transition: background-color 0.2s ease;
        }

        .action-btn:hover {
          background-color: #f0f0f0;
        }

        .view-btn {
          color: var(--primary);
        }

        .approve-btn {
          color: var(--success);
        }

        .reject-btn {
          color: var(--danger);
        }

        /* Table Footer */
        .table-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 15px;
          border-top: 1px solid #eee;
        }

        .entries-info {
          color: #666;
          font-size: 0.9rem;
        }

        .pagination {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .pagination-btn {
          background-color: #f5f5f5;
          border: 1px solid #ddd;
          border-radius: 4px;
          padding: 5px 10px;
          cursor: pointer;
          font-size: 0.9rem;
          transition: all 0.2s ease;
        }

        .pagination-btn:hover:not(:disabled) {
          background-color: var(--primary-light);
          color: white;
          border-color: var(--primary-light);
        }

        .pagination-btn:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .pagination-info {
          color: #666;
          font-size: 0.9rem;
        }

        /* Header Actions */
        .header-actions {
          display: flex;
          gap: 15px;
          align-items: center;
        }

        .search-container {
          position: relative;
        }

        .search-input {
          padding: 10px 15px;
          padding-right: 40px;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 0.9rem;
          width: 250px;
          transition: all 0.3s ease;
        }

        .search-input:focus {
          border-color: var(--secondary);
          box-shadow: 0 0 0 2px rgba(255, 87, 34, 0.2);
          outline: none;
        }

        .search-button {
          position: absolute;
          right: 0;
          top: 0;
          height: 100%;
          width: 40px;
          background: none;
          border: none;
          cursor: pointer;
          color: #666;
          font-size: 1.1rem;
        }

        .search-button:hover {
          color: var(--secondary);
        }

        .status-filter {
          padding: 10px 15px;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 0.9rem;
          background-color: white;
          min-width: 140px;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .status-filter:focus {
          border-color: var(--secondary);
          box-shadow: 0 0 0 2px rgba(255, 87, 34, 0.2);
          outline: none;
        }

        .refresh-button {
          padding: 10px 15px;
          background-color: var(--secondary);
          color: white;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-size: 0.9rem;
          display: flex;
          align-items: center;
          gap: 5px;
          transition: background-color 0.3s ease;
        }

        .refresh-button:hover {
          background-color: #e64a19;
        }

        /* Loading */
        .loading-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 50px;
          background-color: white;
          border-radius: 12px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid var(--primary);
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 15px;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 992px) {
          .header-actions {
            flex-wrap: wrap;
          }

          .search-input {
            width: 100%;
          }
        }

        @media (max-width: 768px) {
          .data-table th:nth-child(2),
          .data-table td:nth-child(2),
          .data-table th:nth-child(5),
          .data-table td:nth-child(5) {
            display: none;
          }

          .table-footer {
            flex-direction: column;
            gap: 10px;
          }
        }

        /* Modal Styles */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex !important;
          justify-content: center;
          align-items: center;
          z-index: 2000; /* Higher than sidebar z-index (1000) */
          overflow-y: auto;
          padding: 20px;
        }

        .modal {
          background-color: white;
          border-radius: 12px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
          width: 100%;
          max-width: 800px;
          max-height: 90vh;
          overflow-y: auto;
          display: flex !important;
          flex-direction: column;
          margin-left: auto; /* Center modal in viewport, not just main content */
          margin-right: auto;
          position: relative; /* Ensure proper stacking context */
        }

        /* Verification Details Styles */
        .verification-details {
          display: flex !important;
          flex-direction: column;
          gap: 20px;
        }

        .user-profile-section {
          margin-bottom: 20px;
        }

        .user-profile-header {
          display: flex;
          align-items: center;
          gap: 15px;
          padding-bottom: 15px;
          border-bottom: 1px solid #eee;
        }

        .user-avatar.large {
          width: 80px;
          height: 80px;
          font-size: 2rem;
        }

        .user-profile-info {
          display: flex;
          flex-direction: column;
          gap: 5px;
        }

        .user-meta {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #666;
          font-size: 0.9rem;
        }

        .verification-status {
          margin-top: 5px;
        }

        .verification-date {
          font-size: 0.8rem;
          color: #666;
          margin-left: 8px;
        }

        .tab-section {
          background-color: #f9f9f9;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 20px;
        }

        .section-title {
          font-size: 1.1rem;
          font-weight: 600;
          color: var(--text-dark);
          margin-top: 0;
          margin-bottom: 15px;
          padding-bottom: 8px;
          border-bottom: 1px solid #eee;
        }

        .user-details-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 15px;
        }

        .detail-item {
          display: flex;
          flex-direction: column;
          gap: 5px;
        }

        .detail-item.full-width {
          grid-column: 1 / -1;
        }

        .detail-label {
          font-size: 0.8rem;
          color: #666;
          font-weight: 500;
        }

        .detail-value {
          font-size: 0.95rem;
          color: var(--text-dark);
        }

        /* Document Section Styles */
        .document-section {
          margin-top: 20px;
        }

        .documents-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
          gap: 15px;
        }

        .document-card {
          border: 1px solid #eee;
          border-radius: 8px;
          overflow: hidden;
          cursor: pointer;
          transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .document-card:hover {
          transform: translateY(-3px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .document-preview {
          height: 120px;
          background-color: #f5f5f5;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
        }

        .document-preview img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .document-info {
          padding: 10px;
        }

        .document-type {
          font-size: 0.8rem;
          color: #666;
          margin-bottom: 3px;
        }

        .document-name {
          font-size: 0.9rem;
          font-weight: 500;
          color: var(--text-dark);
          margin-bottom: 3px;
        }

        .document-action {
          font-size: 0.75rem;
          color: var(--primary);
        }

        .no-documents {
          grid-column: 1 / -1;
          padding: 20px;
          text-align: center;
          color: #666;
          background-color: #f5f5f5;
          border-radius: 8px;
        }

        .confirmation-modal {
          max-width: 500px;
        }

        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 15px 20px;
          border-bottom: 1px solid #eee;
        }

        .modal-title {
          font-size: 1.2rem;
          font-weight: 600;
          color: var(--text-dark);
          margin: 0;
        }

        .modal-close-button {
          background: none;
          border: none;
          font-size: 1.5rem;
          cursor: pointer;
          color: #999;
          transition: color 0.2s ease;
        }

        .modal-close-button:hover {
          color: var(--text-dark);
        }

        .modal-body {
          padding: 20px;
          overflow-y: auto;
          flex: 1;
        }

        .modal-footer {
          display: flex;
          justify-content: flex-end;
          gap: 10px;
          padding: 15px 20px;
          border-top: 1px solid #eee;
        }

        /* Verification Details Styles */
        .verification-details {
          display: flex;
          flex-direction: column;
          gap: 20px;
        }

        .user-profile-section {
          margin-bottom: 20px;
        }

        .user-profile-header {
          display: flex;
          align-items: center;
          gap: 20px;
          padding-bottom: 20px;
          border-bottom: 1px solid #eee;
        }

        .user-avatar.large {
          width: 80px;
          height: 80px;
          font-size: 2rem;
        }

        .user-profile-info {
          flex: 1;
        }

        .user-profile-info .user-name {
          font-size: 1.5rem;
          margin-bottom: 5px;
        }

        .user-meta {
          display: flex;
          gap: 8px;
          color: #666;
          margin-bottom: 10px;
        }

        .verification-status {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .verification-date {
          font-size: 0.85rem;
          color: #666;
        }

        .verification-tabs {
          display: flex;
          flex-direction: column;
        }

        .tab-section {
          margin-bottom: 30px;
        }

        .section-title {
          font-size: 1.1rem;
          font-weight: 600;
          margin-bottom: 15px;
          padding-bottom: 8px;
          border-bottom: 1px solid #eee;
          color: var(--text-dark);
        }

        .user-details-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 15px;
        }

        .detail-item.full-width {
          grid-column: 1 / -1;
        }

        .detail-item {
          display: flex;
          flex-direction: column;
          gap: 5px;
        }

        .detail-label {
          font-size: 0.85rem;
          color: #666;
        }

        .detail-value {
          font-weight: 500;
          color: var(--text-dark);
        }

        .documents-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 15px;
        }

        .document-card {
          border: 1px solid #eee;
          border-radius: 8px;
          overflow: hidden;
          cursor: pointer;
          transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .document-card:hover {
          transform: translateY(-3px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .document-preview {
          height: 150px;
          overflow: hidden;
          background-color: #f5f5f5;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .document-preview img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .document-info {
          padding: 10px;
        }

        .document-type {
          font-weight: 600;
          color: var(--text-dark);
          margin-bottom: 3px;
        }

        .document-name {
          font-size: 0.85rem;
          color: #666;
        }

        .document-action {
          font-size: 0.75rem;
          color: var(--primary);
          margin-top: 3px;
          font-style: italic;
        }

        .no-documents {
          grid-column: 1 / -1;
          padding: 15px;
          text-align: center;
          color: #666;
          background-color: #f9f9f9;
          border-radius: 8px;
        }

        /* Action Section Styles */
        .action-section {
          background-color: #f9f9f9;
          border-radius: 8px;
          padding: 20px;
          margin-top: 20px;
        }

        .action-buttons-container {
          display: flex;
          flex-direction: column;
          gap: 15px;
        }

        .action-description {
          margin-bottom: 10px;
        }

        .action-description p {
          color: #555;
          font-size: 0.95rem;
          margin: 0;
        }

        .action-buttons-row {
          display: flex;
          gap: 15px;
          justify-content: center;
        }

        .btn-lg {
          padding: 12px 24px;
          font-size: 1rem;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          min-width: 200px;
        }

        .action-icon {
          font-size: 1.2rem;
          font-weight: bold;
        }

        /* Confirmation Modal Styles */
        .confirmation-message {
          text-align: center;
          padding: 20px 0;
        }

        .confirmation-icon {
          font-size: 3rem;
          margin-bottom: 20px;
          display: inline-block;
          width: 80px;
          height: 80px;
          line-height: 80px;
          border-radius: 50%;
          background-color: #f5f5f5;
          color: var(--primary);
        }

        .rejection-reason {
          margin-top: 20px;
          text-align: left;
        }

        .rejection-reason label {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
        }

        .rejection-reason textarea {
          width: 100%;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          resize: vertical;
        }

        /* Button Styles */
        .btn {
          padding: 8px 16px;
          border-radius: 4px;
          font-weight: 500;
          cursor: pointer;
          border: none;
          transition: background-color 0.2s ease;
        }

        .btn-primary {
          background-color: var(--primary);
          color: white;
        }

        .btn-primary:hover {
          background-color: var(--primary-dark);
        }

        .btn-secondary {
          background-color: #f5f5f5;
          color: var(--text-dark);
        }

        .btn-secondary:hover {
          background-color: #e0e0e0;
        }

        .btn-success {
          background-color: var(--success);
          color: white;
        }

        .btn-success:hover {
          background-color: #388e3c;
        }

        .btn-danger {
          background-color: var(--danger);
          color: white;
        }

        .btn-danger:hover {
          background-color: #d32f2f;
        }

        /* Responsive Modal */
        @media (max-width: 768px) {
          .modal {
            width: 95%;
            max-height: 80vh;
          }

          .user-profile-header {
            flex-direction: column;
            text-align: center;
          }

          .user-details-grid,
          .documents-grid {
            grid-template-columns: 1fr;
          }
        }

        /* Document Modal Styles */
        .document-modal-body {
          padding: 20px;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #f5f5f5;
          min-height: 300px;
        }

        .document-full-image {
          max-width: 100%;
          max-height: 70vh;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .document-viewer-modal {
          z-index: 3000;
        }

        /* Additional modal positioning is handled in globals.css */
      `}</style>
    </EnhancedAdminLayout>
  );
}

