/**
 * User Analytics Widget - Shows profile performance and insights
 * Leverages admin analytics functionality for user dashboard
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  LinearProgress,
  Chip,
  Button,
  IconButton,
  Tooltip,
  Divider,
  styled,
  CircularProgress,
  Alert
} from '@mui/material';

// Icons
import {
  Analytics as AnalyticsIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Visibility as ViewIcon,
  Favorite as HeartIcon,
  Message as MessageIcon,
  Star as StarIcon,
  Person as PersonIcon,
  PhotoCamera as PhotoIcon,
  Verified as VerifiedIcon,
  Schedule as TimeIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

// Chart components (you can use recharts or similar)
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as ChartTooltip,
  Responsive<PERSON><PERSON>r,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>
} from 'recharts';

// Styled components
const AnalyticsCard = styled(Card)(({ theme }) => ({
  borderRadius: 16,
  boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
  border: '1px solid rgba(255,255,255,0.1)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 16px 48px rgba(0,0,0,0.12)'
  }
}));

const MetricCard = styled(Card)(({ theme }) => ({
  borderRadius: 12,
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white',
  textAlign: 'center'
}));

const TrendChip = styled(Chip)(({ trend }) => ({
  fontWeight: 600,
  background: trend === 'up' ? 'linear-gradient(135deg, #4CAF50, #8BC34A)' :
             trend === 'down' ? 'linear-gradient(135deg, #F44336, #FF5722)' :
             'linear-gradient(135deg, #9E9E9E, #BDBDBD)',
  color: 'white'
}));

// Mock data for charts
const profileViewsData = [
  { name: 'Mon', views: 12 },
  { name: 'Tue', views: 19 },
  { name: 'Wed', views: 15 },
  { name: 'Thu', views: 25 },
  { name: 'Fri', views: 22 },
  { name: 'Sat', views: 30 },
  { name: 'Sun', views: 28 }
];

const interestData = [
  { name: 'Sent', value: 15, color: '#667eea' },
  { name: 'Received', value: 23, color: '#764ba2' },
  { name: 'Accepted', value: 8, color: '#4CAF50' }
];

const demographicData = [
  { age: '21-25', count: 8 },
  { age: '26-30', count: 15 },
  { age: '31-35', count: 12 },
  { age: '36-40', count: 5 }
];

export default function UserAnalyticsWidget({ userId }) {
  const [analytics, setAnalytics] = useState({});
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  const fetchAnalytics = async () => {
    setLoading(true);
    try {
      // Simulate API call to fetch user analytics
      // This would call your existing analytics endpoints
      setTimeout(() => {
        setAnalytics({
          profileViews: {
            total: 127,
            trend: 'up',
            change: 15.2,
            data: profileViewsData
          },
          interests: {
            sent: 15,
            received: 23,
            accepted: 8,
            trend: 'up',
            change: 8.5
          },
          messages: {
            total: 12,
            unread: 3,
            trend: 'up',
            change: 25.0
          },
          profileScore: {
            overall: 85,
            completeness: 92,
            photoQuality: 78,
            responseRate: 88
          },
          demographics: {
            topAgeGroup: '26-30',
            topLocation: 'Mumbai',
            topEducation: 'Engineering'
          },
          recommendations: [
            'Add more photos to increase profile views by 40%',
            'Complete your family details section',
            'Update your preferences for better matches',
            'Verify your profile to gain more trust'
          ]
        });
        setLoading(false);
      }, 1500);
    } catch (error) {
      console.error('Error fetching analytics:', error);
      setLoading(false);
    }
  };

  const renderMetricCard = (title, value, trend, change, icon, color) => (
    <MetricCard>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
          {icon}
        </Box>
        <Typography variant="h4" fontWeight="700" gutterBottom>
          {value}
        </Typography>
        <Typography variant="body2" sx={{ opacity: 0.9, mb: 2 }}>
          {title}
        </Typography>
        <TrendChip
          icon={trend === 'up' ? <TrendingUpIcon /> : <TrendingDownIcon />}
          label={`${change > 0 ? '+' : ''}${change}%`}
          size="small"
          trend={trend}
        />
      </CardContent>
    </MetricCard>
  );

  const renderProfileScore = () => (
    <AnalyticsCard>
      <CardContent sx={{ p: 4 }}>
        <Typography variant="h6" fontWeight="600" gutterBottom>
          Profile Performance Score
        </Typography>
        
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2">Overall Score</Typography>
            <Typography variant="body2" fontWeight="600">
              {analytics.profileScore?.overall}/100
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={analytics.profileScore?.overall || 0}
            sx={{
              height: 8,
              borderRadius: 4,
              backgroundColor: 'rgba(102, 126, 234, 0.1)',
              '& .MuiLinearProgress-bar': {
                background: 'linear-gradient(90deg, #667eea, #764ba2)',
                borderRadius: 4
              }
            }}
          />
        </Box>

        <Grid container spacing={2}>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Profile Completeness
            </Typography>
            <Typography variant="h6" fontWeight="600" color="#4CAF50">
              {analytics.profileScore?.completeness}%
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Photo Quality
            </Typography>
            <Typography variant="h6" fontWeight="600" color="#FF9800">
              {analytics.profileScore?.photoQuality}%
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Response Rate
            </Typography>
            <Typography variant="h6" fontWeight="600" color="#2196F3">
              {analytics.profileScore?.responseRate}%
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Verification Status
            </Typography>
            <Chip
              icon={<VerifiedIcon />}
              label="Verified"
              size="small"
              sx={{ backgroundColor: '#4CAF50', color: 'white' }}
            />
          </Grid>
        </Grid>
      </CardContent>
    </AnalyticsCard>
  );

  const renderRecommendations = () => (
    <AnalyticsCard>
      <CardContent sx={{ p: 4 }}>
        <Typography variant="h6" fontWeight="600" gutterBottom>
          AI Recommendations
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Personalized suggestions to improve your profile performance
        </Typography>
        
        {analytics.recommendations?.map((recommendation, index) => (
          <Alert
            key={index}
            severity="info"
            sx={{ mb: 2 }}
            action={
              <Button color="inherit" size="small">
                Apply
              </Button>
            }
          >
            {recommendation}
          </Alert>
        ))}
      </CardContent>
    </AnalyticsCard>
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <AnalyticsIcon sx={{ fontSize: 32, color: '#667eea', mr: 2 }} />
          <Typography variant="h5" fontWeight="700">
            Profile Analytics
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          {['7d', '30d', '90d'].map((range) => (
            <Button
              key={range}
              size="small"
              variant={timeRange === range ? 'contained' : 'outlined'}
              onClick={() => setTimeRange(range)}
              sx={{
                ...(timeRange === range && {
                  background: 'linear-gradient(135deg, #667eea, #764ba2)'
                })
              }}
            >
              {range}
            </Button>
          ))}
          <IconButton onClick={fetchAnalytics}>
            <RefreshIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Metric Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={6} md={3}>
          {renderMetricCard(
            'Profile Views',
            analytics.profileViews?.total || 0,
            analytics.profileViews?.trend || 'up',
            analytics.profileViews?.change || 0,
            <ViewIcon sx={{ fontSize: 32 }} />,
            '#2196F3'
          )}
        </Grid>
        <Grid item xs={6} md={3}>
          {renderMetricCard(
            'Interests Received',
            analytics.interests?.received || 0,
            analytics.interests?.trend || 'up',
            analytics.interests?.change || 0,
            <HeartIcon sx={{ fontSize: 32 }} />,
            '#E91E63'
          )}
        </Grid>
        <Grid item xs={6} md={3}>
          {renderMetricCard(
            'Messages',
            analytics.messages?.total || 0,
            analytics.messages?.trend || 'up',
            analytics.messages?.change || 0,
            <MessageIcon sx={{ fontSize: 32 }} />,
            '#4CAF50'
          )}
        </Grid>
        <Grid item xs={6} md={3}>
          {renderMetricCard(
            'Profile Score',
            `${analytics.profileScore?.overall || 0}/100`,
            'up',
            5.2,
            <StarIcon sx={{ fontSize: 32 }} />,
            '#FF9800'
          )}
        </Grid>
      </Grid>

      {/* Charts and Detailed Analytics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Profile Views Chart */}
        <Grid item xs={12} md={8}>
          <AnalyticsCard>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h6" fontWeight="600" gutterBottom>
                Profile Views Trend
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={profileViewsData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <ChartTooltip />
                    <Line
                      type="monotone"
                      dataKey="views"
                      stroke="#667eea"
                      strokeWidth={3}
                      dot={{ fill: '#667eea', strokeWidth: 2, r: 6 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </AnalyticsCard>
        </Grid>

        {/* Interest Distribution */}
        <Grid item xs={12} md={4}>
          <AnalyticsCard>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h6" fontWeight="600" gutterBottom>
                Interest Distribution
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={interestData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {interestData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <ChartTooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
              <Box sx={{ mt: 2 }}>
                {interestData.map((item, index) => (
                  <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: item.color,
                        mr: 1
                      }}
                    />
                    <Typography variant="body2">
                      {item.name}: {item.value}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </AnalyticsCard>
        </Grid>
      </Grid>

      {/* Profile Score and Recommendations */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          {renderProfileScore()}
        </Grid>
        <Grid item xs={12} md={6}>
          {renderRecommendations()}
        </Grid>
      </Grid>
    </Box>
  );
}
