import { Box, Tooltip, keyframes } from '@mui/material';
import { FlashOn as FlashOnIcon } from '@mui/icons-material';

// Define keyframes for the glow animation
const glowAnimation = keyframes`
  0% {
    box-shadow: 0 0 5px #e91e63, 0 0 10px #e91e63;
  }
  50% {
    box-shadow: 0 0 10px #e91e63, 0 0 20px #e91e63;
  }
  100% {
    box-shadow: 0 0 5px #e91e63, 0 0 10px #e91e63;
  }
`;

// Define keyframes for the pulse animation
const pulseAnimation = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
`;

/**
 * SpotlightIndicator component
 * Displays a visual indicator for profiles that have the spotlight feature active
 *
 * @param {Object} props - Component props
 * @param {string} props.position - Position of the indicator ('top-right', 'top-left', 'bottom-right', 'bottom-left')
 * @param {string} props.size - Size of the indicator ('small', 'medium', 'large')
 * @param {string} props.tooltipPlacement - Placement of the tooltip
 * @param {boolean} props.showTooltip - Whether to show the tooltip
 * @param {string} props.tooltipText - Text to display in the tooltip
 * @returns {JSX.Element} - The spotlight indicator component
 */
export default function SpotlightIndicator({
  position = 'top-right',
  size = 'medium',
  tooltipPlacement = 'top',
  showTooltip = true,
  tooltipText = 'Spotlight Profile'
}) {
  // Determine position styles
  const positionStyles = {
    'top-right': { top: 10, right: 10 },
    'top-left': { top: 10, left: 10 },
    'bottom-right': { bottom: 10, right: 10 },
    'bottom-left': { bottom: 10, left: 10 }
  };

  // Determine size styles
  const sizeMap = {
    small: {
      width: { xs: 20, sm: 24 },
      height: { xs: 20, sm: 24 },
      iconSize: { xs: 14, sm: 16 }
    },
    medium: {
      width: { xs: 28, sm: 32 },
      height: { xs: 28, sm: 32 },
      iconSize: { xs: 18, sm: 20 }
    },
    large: {
      width: { xs: 36, sm: 40 },
      height: { xs: 36, sm: 40 },
      iconSize: { xs: 22, sm: 24 }
    }
  };

  const { width, height, iconSize } = sizeMap[size] || sizeMap.medium;

  // The indicator component
  const indicator = (
    <Box
      sx={{
        position: 'absolute',
        ...positionStyles[position],
        width: width,
        height: height,
        borderRadius: '50%',
        backgroundColor: 'rgba(233, 30, 99, 0.9)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10,
        animation: `${glowAnimation} 2s infinite ease-in-out`,
        '&:hover': {
          backgroundColor: 'rgba(233, 30, 99, 1)',
        }
      }}
    >
      <FlashOnIcon
        sx={{
          color: 'white',
          fontSize: iconSize,
          animation: `${pulseAnimation} 2s infinite ease-in-out`
        }}
      />
    </Box>
  );

  // Wrap in tooltip if showTooltip is true
  return showTooltip ? (
    <Tooltip title={tooltipText} placement={tooltipPlacement}>
      {indicator}
    </Tooltip>
  ) : indicator;
}
