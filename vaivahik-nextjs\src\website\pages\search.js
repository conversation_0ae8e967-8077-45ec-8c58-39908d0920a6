import { useState, useEffect } from 'react';
import Head from 'next/head';
import {
  Container,
  Grid,
  Typo<PERSON>,
  Box,
  Card,
  CardContent,
  Divider,
  Chip,
  <PERSON>ton,
  Alert,
  Snackbar,
  useTheme,
  keyframes,
  Pagination,
  CircularProgress,
  IconButton,
  Paper,
  Stack,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  FlashOn as FlashOnIcon,
  History as HistoryIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  FilterList as FilterListIcon,
  Clear as ClearIcon,
  Sort as SortIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';
import PremiumSearchBar from '@/components/search/PremiumSearchBar';
import SpotlightIndicator from '@/components/profile/SpotlightIndicator';
import FilterChips from '@/components/search/FilterChips';
import { formatHeight } from '@/utils/heightUtils';
import { isUsingRealBackend } from '@/utils/featureFlags';

export default function SearchPage() {
  const theme = useTheme();

  // Current user's gender (would come from auth context in a real app)
  const userGender = 'MALE';

  // Search results state
  const [searchResults, setSearchResults] = useState([]);
  const [allResults, setAllResults] = useState([]); // All results before pagination
  const [isSearching, setIsSearching] = useState(false);
  const [searchPerformed, setSearchPerformed] = useState(false);
  const [savedSearches, setSavedSearches] = useState([]);
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });

  // Pagination state
  const [page, setPage] = useState(1);
  const [resultsPerPage, setResultsPerPage] = useState(9);
  const [totalPages, setTotalPages] = useState(0);

  // Search history state
  const [searchHistory, setSearchHistory] = useState([]);
  const [historyAnchorEl, setHistoryAnchorEl] = useState(null);
  const historyMenuOpen = Boolean(historyAnchorEl);

  // Current search parameters
  const [currentSearchParams, setCurrentSearchParams] = useState(null);

  // Sort options
  const [sortOption, setSortOption] = useState('relevance');
  const [sortAnchorEl, setSortAnchorEl] = useState(null);
  const sortMenuOpen = Boolean(sortAnchorEl);

  // Recent locations (would be fetched from user history in a real app)
  const recentLocations = ['Mumbai', 'Pune', 'Nagpur', 'Delhi', 'Bangalore'];

  // Load search history from localStorage on mount
  useEffect(() => {
    try {
      const storedHistory = localStorage.getItem('searchHistory');
      if (storedHistory) {
        setSearchHistory(JSON.parse(storedHistory));
      }
    } catch (error) {
      console.error('Error loading search history:', error);
    }
  }, []);

  // Update pagination when results or page changes
  useEffect(() => {
    if (allResults.length > 0) {
      const startIndex = (page - 1) * resultsPerPage;
      const endIndex = startIndex + resultsPerPage;
      setSearchResults(allResults.slice(startIndex, endIndex));
      setTotalPages(Math.ceil(allResults.length / resultsPerPage));
    }
  }, [allResults, page, resultsPerPage]);

  // Handle search submission
  const handleSearch = async (searchData) => {
    setIsSearching(true);
    setPage(1); // Reset to first page on new search
    setCurrentSearchParams(searchData); // Store current search parameters

    try {
      // In a real app, this would be an API call
      console.log('Search data:', searchData);

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock search results
      const mockResults = generateMockResults(searchData);
      setAllResults(mockResults);
      setSearchPerformed(true);

      // Add to search history
      addToSearchHistory(searchData);

    } catch (error) {
      console.error('Search error:', error);
      setNotification({
        open: true,
        message: 'An error occurred while searching. Please try again.',
        severity: 'error'
      });
    } finally {
      setIsSearching(false);
    }
  };

  // Add search to history
  const addToSearchHistory = (searchData) => {
    // Create a history entry with timestamp
    const historyEntry = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      searchData,
      resultCount: allResults.length
    };

    // Add to history (limit to 10 entries)
    const updatedHistory = [historyEntry, ...searchHistory].slice(0, 10);
    setSearchHistory(updatedHistory);

    // Save to localStorage
    try {
      localStorage.setItem('searchHistory', JSON.stringify(updatedHistory));
    } catch (error) {
      console.error('Error saving search history:', error);
    }
  };

  // Handle page change
  const handlePageChange = (event, newPage) => {
    setPage(newPage);
    // Scroll to top of results
    document.getElementById('search-results-top')?.scrollIntoView({ behavior: 'smooth' });
  };

  // Handle removing a filter
  const handleRemoveFilter = (filterId) => {
    if (!currentSearchParams) return;

    const updatedParams = { ...currentSearchParams };

    // Handle different filter types
    switch (filterId) {
      case 'age':
        delete updatedParams.ageFrom;
        delete updatedParams.ageTo;
        break;
      case 'height':
        delete updatedParams.heightFrom;
        delete updatedParams.heightTo;
        break;
      case 'location':
        delete updatedParams.location;
        break;
      case 'religion':
        updatedParams.religion = 'ANY';
        break;
      case 'caste':
        updatedParams.caste = 'ANY';
        break;
      case 'education':
        updatedParams.education = [];
        break;
      case 'occupation':
        updatedParams.occupation = [];
        break;
      case 'incomeRange':
        updatedParams.incomeRange = 'ANY';
        break;
      case 'maritalStatus':
        updatedParams.maritalStatus = [];
        break;
      case 'diet':
        updatedParams.diet = 'ANY';
        break;
      case 'withPhoto':
        updatedParams.withPhoto = false;
        break;
      case 'profileCreatedWithin':
        delete updatedParams.profileCreatedWithin;
        break;
      default:
        break;
    }

    // Perform search with updated parameters
    handleSearch(updatedParams);
  };

  // Handle clearing all filters
  const handleClearAllFilters = () => {
    // Reset to basic search with minimal parameters
    const basicParams = {
      searchType: 'REGULAR',
      targetGender: userGender === 'MALE' ? 'FEMALE' : 'MALE',
      ageFrom: userGender === 'MALE' ? 18 : 21,
      ageTo: userGender === 'MALE' ? 35 : 40,
      heightFrom: 53, // 4'5"
      heightTo: 77, // 6'5"
    };

    handleSearch(basicParams);
  };

  // Show search history menu
  const handleHistoryClick = (event) => {
    setHistoryAnchorEl(event.currentTarget);
  };

  // Close search history menu
  const handleHistoryClose = () => {
    setHistoryAnchorEl(null);
  };

  // Load a search from history
  const handleLoadFromHistory = (historyEntry) => {
    handleSearch(historyEntry.searchData);
    handleHistoryClose();
  };

  // Clear search history
  const handleClearHistory = () => {
    setSearchHistory([]);
    localStorage.removeItem('searchHistory');
    handleHistoryClose();

    setNotification({
      open: true,
      message: 'Search history cleared',
      severity: 'success'
    });
  };

  // Handle saving a search
  const handleSaveSearch = (searchData) => {
    const newSavedSearch = {
      id: Date.now(),
      name: `Search on ${new Date().toLocaleDateString()}`,
      data: searchData
    };

    setSavedSearches([newSavedSearch, ...savedSearches]);
    setNotification({
      open: true,
      message: 'Search saved successfully!',
      severity: 'success'
    });
  };

  // Generate mock search results based on search criteria
  const generateMockResults = (searchData) => {
    const targetGender = userGender === 'MALE' ? 'FEMALE' : 'MALE';
    const count = Math.floor(Math.random() * 10) + 5; // 5-15 results
    const results = [];

    for (let i = 0; i < count; i++) {
      const age = Math.floor(Math.random() *
        (searchData.ageTo - searchData.ageFrom + 1)) + searchData.ageFrom;

      const heightInches = Math.floor(Math.random() *
        (searchData.heightTo - searchData.heightFrom + 1)) + searchData.heightFrom;

      const feet = Math.floor(heightInches / 12);
      const inches = heightInches % 12;

      // Add spotlight to some profiles (every 5th profile)
      const isSpotlighted = i % 5 === 0;

      // Calculate spotlight end time (24 hours from now) for spotlighted profiles
      const spotlightEndTime = isSpotlighted ?
        new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() : null;

      results.push({
        id: `VAI${10000 + i}`,
        name: targetGender === 'FEMALE' ?
          ['Priya', 'Anjali', 'Neha', 'Pooja', 'Sneha'][i % 5] :
          ['Rahul', 'Amit', 'Vikram', 'Raj', 'Sanjay'][i % 5],
        age,
        height: `${feet}'${inches}"`,
        heightInInches: heightInches,
        location: ['Mumbai', 'Pune', 'Nagpur', 'Nashik', 'Aurangabad'][i % 5],
        education: ['B.Tech', 'M.B.A.', 'M.S.', 'B.Com', 'M.C.A.'][i % 5],
        occupation: ['Software Engineer', 'Doctor', 'Business', 'Teacher', 'Banker'][i % 5],
        religion: searchData.religion || 'HINDU',
        caste: searchData.caste || 'MARATHA',
        maritalStatus: ['NEVER_MARRIED', 'DIVORCED', 'WIDOWED'][i % 3],
        isVerified: i % 3 === 0,
        isPremium: i % 4 === 0,
        isSpotlighted,
        spotlightEndTime,
        photoUrl: `/mock-profiles/${targetGender.toLowerCase()}${(i % 5) + 1}.jpg`
      });
    }

    return results;
  };

  // Close notification
  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  return (
    <>
      <Head>
        <title>Search Profiles | Vaivahik</title>
        <meta name="description" content="Find your perfect match on Vaivahik" />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Search Bar */}
        <Box
          id="search-container"
          sx={{
            mb: 4,
            position: 'relative',
            zIndex: 10
          }}
        >
          <PremiumSearchBar
            onSearch={handleSearch}
            savedSearches={savedSearches}
            onSaveSearch={handleSaveSearch}
            userGender={userGender}
            recentSearches={recentLocations}
          />
        </Box>

        {/* Search Results Section */}
        <Box id="search-results-top">
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h5" component="h1">
              {searchPerformed ? (
                `${allResults.length} Matches Found`
              ) : (
                'Search Results'
              )}
            </Typography>

            {searchPerformed && (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {/* Search History Button */}
                <Tooltip title="Search History">
                  <IconButton
                    size="small"
                    onClick={handleHistoryClick}
                    sx={{ mr: 1 }}
                  >
                    <HistoryIcon />
                  </IconButton>
                </Tooltip>

                {/* Sort Button */}
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<SortIcon />}
                  onClick={(e) => setSortAnchorEl(e.currentTarget)}
                  sx={{ mr: 1 }}
                >
                  Sort
                </Button>

                {/* Filter Button */}
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<FilterListIcon />}
                >
                  Filter
                </Button>
              </Box>
            )}
          </Box>

          {/* Search History Menu */}
          <Menu
            anchorEl={historyAnchorEl}
            open={historyMenuOpen}
            onClose={handleHistoryClose}
            PaperProps={{
              sx: { width: 320, maxHeight: 400 }
            }}
          >
            <Box sx={{ px: 2, py: 1, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="subtitle1">Recent Searches</Typography>
              <Button
                size="small"
                startIcon={<DeleteIcon />}
                onClick={handleClearHistory}
              >
                Clear
              </Button>
            </Box>
            <Divider />

            {searchHistory.length === 0 ? (
              <Box sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  No recent searches
                </Typography>
              </Box>
            ) : (
              searchHistory.map((entry) => (
                <MenuItem
                  key={entry.id}
                  onClick={() => handleLoadFromHistory(entry)}
                  sx={{ py: 1.5 }}
                >
                  <Box sx={{ width: '100%' }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                      <Typography variant="body2" fontWeight="medium">
                        {entry.searchData.searchType === 'ID'
                          ? `ID Search: ${entry.searchData.userId}`
                          : `${entry.searchData.targetGender} Profiles`}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(entry.timestamp).toLocaleDateString()}
                      </Typography>
                    </Box>
                    <Typography variant="caption" color="text.secondary">
                      {entry.searchData.searchType === 'ID'
                        ? 'Search by user ID'
                        : `Age: ${entry.searchData.ageFrom}-${entry.searchData.ageTo}, ${entry.searchData.location || 'Any location'}`}
                    </Typography>
                    <Typography variant="caption" color="primary" sx={{ display: 'block' }}>
                      {entry.resultCount} results
                    </Typography>
                  </Box>
                </MenuItem>
              ))
            )}
          </Menu>

          {/* Sort Menu */}
          <Menu
            anchorEl={sortAnchorEl}
            open={sortMenuOpen}
            onClose={() => setSortAnchorEl(null)}
          >
            <MenuItem onClick={() => { setSortOption('relevance'); setSortAnchorEl(null); }}>
              <ListItemText primary="Relevance" />
              {sortOption === 'relevance' && <ListItemIcon sx={{ ml: 1 }}>✓</ListItemIcon>}
            </MenuItem>
            <MenuItem onClick={() => { setSortOption('newest'); setSortAnchorEl(null); }}>
              <ListItemText primary="Newest First" />
              {sortOption === 'newest' && <ListItemIcon sx={{ ml: 1 }}>✓</ListItemIcon>}
            </MenuItem>
            <MenuItem onClick={() => { setSortOption('age_asc'); setSortAnchorEl(null); }}>
              <ListItemText primary="Age: Low to High" />
              {sortOption === 'age_asc' && <ListItemIcon sx={{ ml: 1 }}>✓</ListItemIcon>}
            </MenuItem>
            <MenuItem onClick={() => { setSortOption('age_desc'); setSortAnchorEl(null); }}>
              <ListItemText primary="Age: High to Low" />
              {sortOption === 'age_desc' && <ListItemIcon sx={{ ml: 1 }}>✓</ListItemIcon>}
            </MenuItem>
          </Menu>

          <Divider sx={{ mb: 2 }} />

          {/* Active Filters */}
          {searchPerformed && currentSearchParams && (
            <FilterChips
              searchParams={currentSearchParams}
              onRemoveFilter={handleRemoveFilter}
              onClearAllFilters={handleClearAllFilters}
            />
          )}

          {!searchPerformed && (
            <Box
              sx={{
                textAlign: 'center',
                py: 8,
                bgcolor: 'rgba(0, 0, 0, 0.02)',
                borderRadius: 2
              }}
            >
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Find Your Perfect Match
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Use the search options above to discover compatible profiles
              </Typography>
            </Box>
          )}

          {searchPerformed && searchResults.length === 0 && (
            <Box
              sx={{
                textAlign: 'center',
                py: 8,
                bgcolor: 'rgba(0, 0, 0, 0.02)',
                borderRadius: 2
              }}
            >
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No Matches Found
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Try adjusting your search criteria to find more matches
              </Typography>
            </Box>
          )}

          {/* Loading indicator */}
          {isSearching && (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
              <CircularProgress />
            </Box>
          )}

          {/* Search results grid */}
          {!isSearching && (
            <Grid container spacing={3}>
              {searchResults.map(profile => (
                <Grid item xs={12} sm={6} md={4} key={profile.id}>
                  <Card
                    variant="outlined"
                    sx={{
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        boxShadow: theme.shadows[4],
                        transform: 'translateY(-4px)'
                      },
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      position: 'relative',
                      ...(profile.isSpotlighted && {
                        boxShadow: `0 0 15px ${theme.palette.secondary.main}`,
                        border: `1px solid ${theme.palette.secondary.main}`,
                        transform: 'scale(1.02)',
                        zIndex: 1
                      })
                    }}
                  >
                    <Box
                      sx={{
                        position: 'relative',
                        pt: '75%', // 4:3 Aspect ratio
                        backgroundImage: `url(${profile.photoUrl})`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center'
                      }}
                    >
                      {/* Spotlight indicator */}
                      {profile.isSpotlighted && (
                        <SpotlightIndicator
                          position="top-right"
                          size="medium"
                          tooltipText="Spotlight Profile - Featured for 24 hours"
                        />
                      )}

                      <Box
                        sx={{
                          position: 'absolute',
                          top: 8,
                          right: profile.isSpotlighted ? 48 : 8, // Move to the left if there's a spotlight indicator
                          bgcolor: 'rgba(255,255,255,0.9)',
                          borderRadius: 1,
                          px: 1,
                          py: 0.5
                        }}
                      >
                        <Typography variant="caption" fontWeight="medium">
                          {profile.id}
                        </Typography>
                      </Box>

                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          right: 0,
                          bgcolor: 'rgba(0,0,0,0.6)',
                          color: 'white',
                          p: 1.5
                        }}
                      >
                        <Typography variant="subtitle1" component="h2" fontWeight="medium">
                          {profile.name}
                        </Typography>
                        <Typography variant="body2">
                          {profile.age} yrs, {profile.height} • {profile.location}
                        </Typography>
                      </Box>
                    </Box>

                    <CardContent sx={{ flexGrow: 1 }}>
                      <Typography variant="body2" gutterBottom>
                        {profile.education}, {profile.occupation}
                      </Typography>

                      <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {profile.isVerified && (
                          <Chip
                            label="Verified"
                            size="small"
                            color="success"
                            variant="outlined"
                          />
                        )}
                        {profile.isPremium && (
                          <Chip
                            label="Premium"
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        )}
                        {profile.isSpotlighted && (
                          <Chip
                            size="small"
                            label="Spotlight"
                            color="secondary"
                            icon={<FlashOnIcon style={{ fontSize: 16 }} />}
                            sx={{
                              animation: `${keyframes`
                                0% { box-shadow: 0 0 0 0 rgba(233, 30, 99, 0.4); }
                                70% { box-shadow: 0 0 0 6px rgba(233, 30, 99, 0); }
                                100% { box-shadow: 0 0 0 0 rgba(233, 30, 99, 0); }
                              `} 2s infinite`
                            }}
                          />
                        )}
                        <Chip
                          label={profile.religion}
                          size="small"
                          variant="outlined"
                        />
                      </Box>
                    </CardContent>

                    <Box sx={{ display: 'flex', p: 1.5, pt: 0 }}>
                      <Button
                        size="small"
                        variant="outlined"
                        sx={{ flexGrow: 1, mr: 1 }}
                      >
                        View Profile
                      </Button>
                      <Button
                        size="small"
                        variant="contained"
                        color="primary"
                        sx={{ flexGrow: 1 }}
                      >
                        Connect
                      </Button>
                    </Box>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}

          {/* Pagination */}
          {searchPerformed && allResults.length > 0 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={handlePageChange}
                color="primary"
                size="large"
                showFirstButton
                showLastButton
              />
            </Box>
          )}

          {/* Mock data indicator */}
          {searchPerformed && !isUsingRealBackend() && (
            <Paper
              variant="outlined"
              sx={{
                mt: 4,
                p: 2,
                bgcolor: 'rgba(255, 244, 229, 0.5)',
                border: '1px dashed #FF9800'
              }}
            >
              <Typography variant="body2" color="warning.dark" align="center">
                <b>Development Mode:</b> Using mock data. Toggle to real data in the admin panel.
              </Typography>
            </Paper>
          )}
        </Box>
      </Container>

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </>
  );
}
