/**
 * Contact API Service
 * Handles contact reveal and calling functionality with enterprise security
 * Cross-platform support for Web, Android, iOS
 * Advanced fraud prevention and user protection
 */

import { API_BASE_URL } from '@/config';

// Security and analytics tracking
const trackSecurityEvent = (eventType, data) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventType, {
      event_category: 'Security',
      event_label: data.reason || 'unknown',
      custom_parameters: {
        risk_score: data.riskScore,
        user_tier: data.userTier,
        platform: 'WEB'
      }
    });
  }

  // Also log to console for debugging
  console.log(`Security Event: ${eventType}`, data);
};

// Get auth token from localStorage or cookies
const getAuthToken = () => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('authToken') ||
           document.cookie.split('; ').find(row => row.startsWith('authToken='))?.split('=')[1];
  }
  return null;
};

// Get platform information
const getPlatform = () => {
  if (typeof window === 'undefined') return 'WEB';

  const userAgent = window.navigator.userAgent;
  if (/Android/i.test(userAgent)) return 'ANDROID';
  if (/iPhone|iPad|iPod/i.test(userAgent)) return 'IOS';
  return 'WEB';
};

// Base API call function
const apiCall = async (endpoint, options = {}) => {
  const token = getAuthToken();
  const platform = getPlatform();

  const config = {
    headers: {
      'Content-Type': 'application/json',
      'X-Platform': platform,
      ...(token && { 'Authorization': `Bearer ${token}` }),
      ...options.headers
    },
    ...options
  };

  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || `HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error(`API call failed for ${endpoint}:`, error);
    throw error;
  }
};

export const contactApi = {
  /**
   * Check if user can access contact details without revealing them
   * @param {string} userId - Target user ID
   * @returns {Promise<Object>} Access check result
   */
  canAccessContact: async (userId) => {
    return await apiCall(`/contact/can-access/${userId}`);
  },

  /**
   * Reveal contact details of a user with security checks
   * @param {string} userId - Target user ID
   * @param {string} platform - Platform (WEB, ANDROID, IOS)
   * @returns {Promise<Object>} Contact details with dialer URL
   */
  revealContact: async (userId, platform = null) => {
    const requestPlatform = platform || getPlatform();

    try {
      const result = await apiCall(`/contact/reveal/${userId}`, {
        method: 'POST',
        body: JSON.stringify({ platform: requestPlatform })
      });

      // Track successful contact reveal
      trackSecurityEvent('contact_reveal_success', {
        targetUserId: userId,
        platform: requestPlatform,
        accessReason: result.accessReason
      });

      return result;
    } catch (error) {
      // Track security blocks and errors
      if (error.message.includes('security') || error.message.includes('risk')) {
        trackSecurityEvent('contact_reveal_blocked', {
          targetUserId: userId,
          platform: requestPlatform,
          reason: error.message,
          riskScore: error.riskScore
        });
      } else if (error.message.includes('premium') || error.message.includes('subscription')) {
        trackSecurityEvent('contact_reveal_premium_required', {
          targetUserId: userId,
          platform: requestPlatform,
          reason: error.message
        });
      }

      throw error;
    }
  },

  /**
   * Get contact access history
   * @param {number} limit - Number of records to fetch
   * @returns {Promise<Object>} Access history
   */
  getAccessHistory: async (limit = 50) => {
    return await apiCall(`/contact/access-history?limit=${limit}`);
  },

  /**
   * Update contact privacy settings
   * @param {Object} settings - Privacy settings
   * @returns {Promise<Object>} Updated settings
   */
  updatePrivacySettings: async (settings) => {
    return await apiCall('/contact/privacy-settings', {
      method: 'PUT',
      body: JSON.stringify(settings)
    });
  },

  /**
   * Get current contact privacy settings
   * @returns {Promise<Object>} Current settings
   */
  getPrivacySettings: async () => {
    return await apiCall('/contact/privacy-settings');
  },

  /**
   * Get available contact options and preferences
   * @returns {Promise<Object>} Available options
   */
  getContactOptions: async () => {
    return await apiCall('/contact/options');
  }
};

/**
 * Contact Utilities
 */
export const contactUtils = {
  /**
   * Open native dialer with phone number
   * @param {string} phoneNumber - Phone number to call
   * @param {string} platform - Platform (WEB, ANDROID, IOS)
   */
  openDialer: (phoneNumber, platform = null) => {
    const currentPlatform = platform || getPlatform();
    const cleanNumber = phoneNumber.replace(/[^\d+]/g, '');
    const dialerUrl = `tel:${cleanNumber}`;

    try {
      if (typeof window !== 'undefined') {
        // For web browsers
        window.location.href = dialerUrl;

        // For React Native WebView
        if (window.ReactNativeWebView) {
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'OPEN_DIALER',
            url: dialerUrl,
            platform: currentPlatform
          }));
        }
      }
    } catch (error) {
      console.error('Error opening dialer:', error);
    }
  },

  /**
   * Format phone number for display
   * @param {string} phoneNumber - Raw phone number
   * @returns {string} Formatted phone number
   */
  formatPhoneNumber: (phoneNumber) => {
    if (!phoneNumber) return '';

    // Remove all non-digit characters except +
    const cleaned = phoneNumber.replace(/[^\d+]/g, '');

    // Format Indian numbers
    if (cleaned.startsWith('+91') && cleaned.length === 13) {
      return `+91 ${cleaned.slice(3, 8)} ${cleaned.slice(8)}`;
    }

    // Format other international numbers
    if (cleaned.startsWith('+')) {
      return cleaned;
    }

    // Format 10-digit Indian numbers
    if (cleaned.length === 10) {
      return `${cleaned.slice(0, 5)} ${cleaned.slice(5)}`;
    }

    return cleaned;
  },

  /**
   * Validate phone number
   * @param {string} phoneNumber - Phone number to validate
   * @returns {boolean} Is valid phone number
   */
  isValidPhoneNumber: (phoneNumber) => {
    if (!phoneNumber) return false;

    const cleaned = phoneNumber.replace(/[^\d+]/g, '');

    // Indian numbers: +91XXXXXXXXXX or XXXXXXXXXX
    const indianPattern = /^(\+91)?[6-9]\d{9}$/;

    return indianPattern.test(cleaned);
  },

  /**
   * Get call availability text
   * @param {string} availability - Availability code
   * @returns {string} Human readable availability
   */
  getAvailabilityText: (availability) => {
    switch (availability) {
      case 'BUSINESS_HOURS': return '9 AM - 6 PM';
      case 'EVENING_ONLY': return '6 PM - 10 PM';
      case 'WEEKEND_ONLY': return 'Weekends Only';
      case 'ANYTIME':
      default: return 'Anytime';
    }
  },

  /**
   * Get contact reveal preference text
   * @param {string} preference - Preference code
   * @returns {string} Human readable preference
   */
  getRevealPreferenceText: (preference) => {
    switch (preference) {
      case 'PREMIUM_ONLY': return 'Premium Users Only';
      case 'MUTUAL_INTEREST': return 'Mutual Interest Required';
      case 'ACCEPTED_INTEREST': return 'Accepted Interest Required';
      case 'NEVER': return 'Never Share Contact';
      default: return 'Premium Users Only';
    }
  },

  /**
   * Check if current time is within call availability
   * @param {string} availability - Availability setting
   * @returns {boolean} Is currently available for calls
   */
  isCurrentlyAvailable: (availability) => {
    if (!availability || availability === 'ANYTIME') return true;

    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay(); // 0 = Sunday, 6 = Saturday

    switch (availability) {
      case 'BUSINESS_HOURS':
        return hour >= 9 && hour < 18;
      case 'EVENING_ONLY':
        return hour >= 18 && hour < 22;
      case 'WEEKEND_ONLY':
        return day === 0 || day === 6; // Sunday or Saturday
      default:
        return true;
    }
  }
};

/**
 * React Native Integration Helpers
 */
export const reactNativeHelpers = {
  /**
   * Send message to React Native app
   * @param {Object} message - Message to send
   */
  sendToReactNative: (message) => {
    if (typeof window !== 'undefined' && window.ReactNativeWebView) {
      window.ReactNativeWebView.postMessage(JSON.stringify(message));
    }
  },

  /**
   * Request permission for phone calls (Android)
   */
  requestCallPermission: () => {
    reactNativeHelpers.sendToReactNative({
      type: 'REQUEST_CALL_PERMISSION'
    });
  },

  /**
   * Check if call permission is granted (Android)
   */
  checkCallPermission: () => {
    reactNativeHelpers.sendToReactNative({
      type: 'CHECK_CALL_PERMISSION'
    });
  }
};

export default contactApi;
