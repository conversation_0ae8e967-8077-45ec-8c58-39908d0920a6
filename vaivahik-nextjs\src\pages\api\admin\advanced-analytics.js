/**
 * Advanced Analytics API Endpoint
 * Provides comprehensive analytics data for admin dashboard
 */

// Simple rate limiting and error handling
const requestCounts = new Map();

const simpleRateLimit = (ip, limit = 50, windowMs = 15 * 60 * 1000) => {
  const now = Date.now();
  const windowStart = now - windowMs;

  if (!requestCounts.has(ip)) {
    requestCounts.set(ip, []);
  }

  const requests = requestCounts.get(ip);
  const validRequests = requests.filter(time => time > windowStart);

  if (validRequests.length >= limit) {
    return { allowed: false, resetTime: Math.min(...validRequests) + windowMs };
  }

  validRequests.push(now);
  requestCounts.set(ip, validRequests);
  return { allowed: true };
};

export default async function handler(req, res) {
  // Apply rate limiting
  const rateLimitResult = simpleRateLimit(req.ip || req.connection.remoteAddress);

  if (!rateLimitResult.allowed) {
    return res.status(429).json({
      success: false,
      message: 'Too many requests',
      retryAfter: Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000)
    });
  }

  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    const { timeRange = '7d' } = req.query;

    // Generate mock analytics data based on time range
    const analyticsData = generateAnalyticsData(timeRange);

    res.status(200).json({
      success: true,
      data: analyticsData,
      timeRange,
      generatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Analytics API Error:', error);

    res.status(500).json({
      success: false,
      message: 'Failed to fetch analytics data',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
}

function generateAnalyticsData(timeRange) {
  const now = new Date();
  const days = timeRange === '1d' ? 1 : timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
  
  // Generate user behavior data
  const userBehavior = [];
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
    userBehavior.push({
      date: date.toISOString().split('T')[0],
      newUsers: Math.floor(Math.random() * 100) + 50,
      activeUsers: Math.floor(Math.random() * 500) + 200,
      sessions: Math.floor(Math.random() * 800) + 400,
      pageViews: Math.floor(Math.random() * 2000) + 1000,
      bounceRate: (Math.random() * 0.3 + 0.2).toFixed(2),
      avgSessionDuration: Math.floor(Math.random() * 300) + 180
    });
  }

  // Generate conversion funnel data
  const conversionFunnel = [
    { name: 'Visitors', value: 10000, fill: '#8884d8' },
    { name: 'Registrations', value: 3000, fill: '#82ca9d' },
    { name: 'Profile Complete', value: 2400, fill: '#ffc658' },
    { name: 'First Match', value: 1800, fill: '#ff7300' },
    { name: 'Premium Upgrade', value: 600, fill: '#00ff00' },
    { name: 'Success Story', value: 150, fill: '#ff0000' }
  ];

  // Generate real-time metrics
  const realTimeMetrics = {
    activeUsers: Math.floor(Math.random() * 200) + 100,
    onlineUsers: Math.floor(Math.random() * 50) + 25,
    currentSessions: Math.floor(Math.random() * 150) + 75,
    pageViewsPerMinute: Math.floor(Math.random() * 20) + 10,
    newRegistrationsToday: Math.floor(Math.random() * 50) + 20,
    matchesMadeToday: Math.floor(Math.random() * 100) + 50,
    messagesExchanged: Math.floor(Math.random() * 500) + 200,
    premiumUpgradesToday: Math.floor(Math.random() * 10) + 2
  };

  // Generate geographic data
  const geographicData = [
    { state: 'Maharashtra', users: 4500, matches: 1200, revenue: 125000 },
    { state: 'Karnataka', users: 2800, matches: 750, revenue: 85000 },
    { state: 'Gujarat', users: 2200, matches: 580, revenue: 65000 },
    { state: 'Rajasthan', users: 1800, matches: 420, revenue: 45000 },
    { state: 'Madhya Pradesh', users: 1500, matches: 350, revenue: 38000 },
    { state: 'Delhi', users: 1200, matches: 280, revenue: 32000 },
    { state: 'Uttar Pradesh', users: 1000, matches: 220, revenue: 25000 }
  ];

  // Generate device and browser analytics
  const deviceAnalytics = {
    devices: [
      { name: 'Mobile', value: 65, users: 6500 },
      { name: 'Desktop', value: 30, users: 3000 },
      { name: 'Tablet', value: 5, users: 500 }
    ],
    browsers: [
      { name: 'Chrome', value: 70, users: 7000 },
      { name: 'Safari', value: 15, users: 1500 },
      { name: 'Firefox', value: 10, users: 1000 },
      { name: 'Edge', value: 5, users: 500 }
    ],
    operatingSystems: [
      { name: 'Android', value: 45, users: 4500 },
      { name: 'iOS', value: 25, users: 2500 },
      { name: 'Windows', value: 20, users: 2000 },
      { name: 'macOS', value: 8, users: 800 },
      { name: 'Linux', value: 2, users: 200 }
    ]
  };

  // Generate user engagement metrics
  const engagementMetrics = {
    averageSessionDuration: '8m 32s',
    pagesPerSession: 4.2,
    returnVisitorRate: 68.5,
    newVisitorRate: 31.5,
    profileCompletionRate: 78.3,
    messageResponseRate: 45.2,
    matchAcceptanceRate: 23.7,
    premiumConversionRate: 12.8
  };

  // Generate revenue analytics
  const revenueAnalytics = {
    totalRevenue: 450000,
    monthlyRecurringRevenue: 125000,
    averageRevenuePerUser: 850,
    customerLifetimeValue: 2400,
    churnRate: 5.2,
    revenueGrowthRate: 15.8,
    revenueByPlan: [
      { plan: 'Basic', revenue: 180000, users: 3600 },
      { plan: 'Premium', revenue: 200000, users: 1600 },
      { plan: 'Elite', revenue: 70000, users: 400 }
    ]
  };

  // Generate matching algorithm performance
  const algorithmPerformance = {
    totalMatches: 15420,
    successfulMatches: 2156,
    averageMatchScore: 72.4,
    matchAccuracyRate: 87.3,
    userSatisfactionScore: 4.2,
    algorithmVersion: 'v1.0',
    lastTrainingDate: '2024-06-10',
    modelPerformance: {
      precision: 84.6,
      recall: 89.1,
      f1Score: 86.8,
      accuracy: 87.3
    }
  };

  return {
    userBehavior,
    conversionFunnel,
    realTimeMetrics,
    geographicData,
    deviceAnalytics,
    engagementMetrics,
    revenueAnalytics,
    algorithmPerformance,
    summary: {
      totalUsers: 10000,
      activeUsers: 7500,
      premiumUsers: 2400,
      successStories: 156,
      totalMatches: 15420,
      totalRevenue: 450000,
      growthRate: 15.8,
      satisfactionScore: 4.2
    }
  };
}
