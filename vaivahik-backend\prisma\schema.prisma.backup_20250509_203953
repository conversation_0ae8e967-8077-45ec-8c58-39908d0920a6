// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  // Note: For efficient geospatial queries later, consider enabling the PostGIS extension
  // extensions = [postgis] // Requires enabling PostGIS on your PostgreSQL server
}

// Enum for photo visibility
enum PhotoVisibility {
  PUBLIC
  PAID
  CONNECTIONS_ONLY
}

// Enum for Photo Moderation Status
enum PhotoStatus {
  PENDING   // Newly uploaded, awaiting review
  APPROVED  // Visible according to visibility rules
  REJECTED  // Not visible, potentially deleted later
}

// Enum for Message Types
enum MessageType {
  TEXT
  IMAGE
  FILE
  AUDIO
  VIDEO
  SYSTEM
}

// ------------------- User Model (Core Details) -------------------
model User {
  id            String    @id @default(cuid())
  phone         String    @unique
  email         String?   @unique
  password      String?
  isVerified    <PERSON><PERSON><PERSON>   @default(false)
  profileStatus String    @default("INCOMPLETE") // INCOMPLETE, PENDING_APPROVAL, ACTIVE, SUSPENDED, INACTIVE
  isPremium     Boolean   @default(false)

  // Premium and feature usage fields
  isIncognito   Boolean   @default(false) @map("is_incognito")
  boostedUntil  DateTime? @map("boosted_until")
  messagesSent  Int       @default(0) @map("messages_sent")

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  // --- Relationships ---
  profile       Profile?    // One-to-one with Profile
  photos        Photo[]     // One-to-many with Photo
  preference    Preference? // One-to-one with Preference
  verificationDocuments VerificationDocument[] // One-to-many with VerificationDocument
  notifications Notification[] // One-to-many with Notification

  // Match relationships
  matchesAsUser1 Match[] @relation("UserMatches1")
  matchesAsUser2 Match[] @relation("UserMatches2")

  // Report relationships
  reportsSubmitted Report[] @relation("UserReports")
  reportsReceived  Report[] @relation("UserReported")

  // Subscription relationship
  subscriptions    Subscription[]

  // Usage tracking relationships
  profilesViewed   ProfileView[] @relation("ViewerRelation")
  profileViewers   ProfileView[] @relation("ViewedRelation")
  matchViews       MatchView[]

  // Message relationships
  sentMessages     Message[]     @relation("SentMessages")
  receivedMessages Message[]     @relation("ReceivedMessages")

  // Conversation relationships
  conversationsAsUser1 Conversation[] @relation("User1Conversations")
  conversationsAsUser2 Conversation[] @relation("User2Conversations")

  @@map("users")
}

// ------------------- Profile Model (Detailed Info - UPDATED) -------------------
model Profile {
  id            String    @id @default(cuid())
  userId        String    @unique @map("user_id") // Foreign key referencing User
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade) // Define relation

  // --- Basic Profile Details ---
  fullName        String?
  gender          String?   // Consider Enum: enum Gender { MALE FEMALE OTHER }
  profileFor      String?   @map("profile_for") // Self, Son, Daughter, Brother, Sister, etc.
  dateOfBirth     DateTime? @map("birth_date")
  birthTime       String?   @map("birth_time")
  birthPlace      String?   @map("birth_place")

  // --- Community / Background ---  (ADDED/UPDATED Fields)
  religion        String?                 // Added
  caste           String?                 // Added
  subCaste        String? @map("sub_caste") // Specific Maratha sub-caste
  gotra           String?                 // Ancestral lineage
  kul             String?                 // Family/clan name
  motherTongue    String? @map("mother_tongue") // Added + mapped
  marathiProficiency String? @map("marathi_proficiency") // Fluency in Marathi language

  // --- Lifestyle & Physical --- (ADDED/UPDATED Fields)
  height          String?
  diet            String?                 // Added
  maritalStatus   String? @map("marital_status") // Added + mapped // Consider Enum

  // --- Location --- (ADDED Fields)
  city            String?
  state           String?                 // Added
  country         String?                 // Added
  nativePlace     String?   @map("native_place")
  nativeDistrict  String?   @map("native_district") // District in Maharashtra
  maharashtrianOrigin Boolean? @map("maharashtrian_origin") // Is family originally from Maharashtra
  latitude        Float?    // Store latitude
  longitude       Float?    // Store longitude

  // --- Education & Profession --- (UPDATED Fields)
  highestEducation String? @map("highest_education") // Renamed from 'education'
  occupation      String?

  // --- Financial --- (UPDATED Fields)
  annualIncome    String? @map("annual_income") // Renamed from 'incomeRange'

  // --- Family Details --- (ADDED/UPDATED Fields)
  familyType        String? @map("family_type") // Added
  familyStatus      String? @map("family_status") // Added
  fatherName        String? @map("father_name") // Kept original name, but added occupation below
  fatherOccupation  String? @map("father_occupation") // Added
  motherName        String? @map("mother_name") // Kept original name, but added occupation below
  motherOccupation  String? @map("mother_occupation") // Added
  uncleName         String? @map("uncle_name")
  siblings          String? // Changed from total/married Int? to String? for flexibility
  // totalSiblings     Int?    @map("total_siblings") // Removed/Replaced
  // marriedSiblings   Int?    @map("married_siblings") // Removed/Replaced
  familyContact     String? @map("family_contact")

  // --- About --- (ADDED Fields)
  aboutMe           String? @map("about_me") @db.Text // Added, use Text for longer content
  partnerPreferences String? @map("partner_preferences") @db.Text // Added

  // --- Hobbies & Interests --- (ADDED Fields)
  hobbies           String? @db.Text // Comma-separated list or JSON string of hobbies
  interests         String? @db.Text // Comma-separated list or JSON string of interests

  // --- Timestamps ---
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("profiles")
}

// ------------------- Photo Model -------------------
model Photo {
  id            String          @id @default(cuid())
  userId        String          @map("user_id")
  user          User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  url           String          // URL path to the stored photo
  visibility    PhotoVisibility @default(PUBLIC) // Visibility setting for this photo
  status        PhotoStatus     @default(PENDING) // Moderation Status
  isProfilePic  Boolean         @default(false) @map("is_profile_pic")
  uploadedAt    DateTime        @default(now()) @map("uploaded_at")

  // AI Moderation fields
  aiFlags       String?         @map("ai_flags") // Comma-separated flags from AI analysis
  aiConfidence  Float?          @map("ai_confidence") // Confidence score from AI (0-100)

  // Relationships
  moderationLogs ModerationLog[]

  @@map("photos")
}


// ------------------- Admin Model -------------------
model Admin {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  role      String   @default("ADMIN") // e.g., ADMIN, SUPER_ADMIN
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@map("admins")
}

// ------------------- Preference Model -------------------
model Preference {
  id               String    @id @default(cuid())
  userId           String    @unique @map("user_id")
  user             User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Age range
  ageMin           Int?      @map("age_min")
  ageMax           Int?      @map("age_max")

  // Height range
  heightMin        String?   @map("height_min")
  heightMax        String?   @map("height_max")

  // Education & Career
  educationLevel   String[]  @map("education_level")  // Array (multiple degrees allowed)
  occupations      String[]                           // Array (multiple jobs allowed)
  incomeMin        String?   @map("income_min")        // Minimum expected income

  // Location preferences
  preferredCities  String[]  @map("preferred_cities")
  preferredStates  String[]  @map("preferred_states")

  // Maratha specific
  acceptSubCastes  String[]  @map("accept_sub_castes") // Accepted sub-castes
  gotraPreference  String?   @map("gotra_preference")  // Specific Gotra if needed

  // Lifestyle
  dietPreference   String?   @map("diet_preference")   // Veg, Non-Veg, Eggetarian etc.

  // Hobbies & Interests preferences
  hobbiesPreference String?  @map("hobbies_preference") @db.Text // Preferred hobbies in partner
  interestsPreference String? @map("interests_preference") @db.Text // Preferred interests in partner

  // Other preferences
  otherPreferences String?   @map("other_preferences") @db.Text

  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("preferences")
}

// ------------------- Match Model -------------------
model Match {
  id                 String    @id @default(cuid())

  // The two users in this match
  user1Id            String    @map("user1_id")
  user1              User      @relation("UserMatches1", fields: [user1Id], references: [id], onDelete: Cascade)
  user2Id            String    @map("user2_id")
  user2              User      @relation("UserMatches2", fields: [user2Id], references: [id], onDelete: Cascade)

  // Match status
  status             String    @default("PENDING") // PENDING, ACCEPTED, REJECTED, CANCELLED
  initiatedBy        String    @map("initiated_by") // user1_id or user2_id

  // AI matching score
  compatibilityScore Float?    @map("compatibility_score")
  matchReason        String?   @map("match_reason") @db.Text // Why AI suggested this match

  // Communication
  lastMessageAt      DateTime? @map("last_message_at")

  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @default(now()) @updatedAt @map("updated_at")

  @@unique([user1Id, user2Id])
  @@map("matches")
}

// ------------------- Report Model -------------------
model Report {
  id            String    @id @default(cuid())
  reporterId    String    @map("reporter_id")
  reporter      User      @relation("UserReports", fields: [reporterId], references: [id], onDelete: Cascade)
  reportedId    String    @map("reported_id")
  reported      User      @relation("UserReported", fields: [reportedId], references: [id], onDelete: Cascade)

  reason        String    // Predefined reason code
  details       String?   @db.Text // Additional details provided by reporter
  status        String    @default("PENDING") // PENDING, REVIEWED, DISMISSED, ACTIONED
  adminNotes    String?   @map("admin_notes") @db.Text // Notes from admin review

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("reports")
}

// ------------------- Subscription Model -------------------
model Subscription {
  id            String    @id @default(cuid())
  userId        String    @map("user_id")
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  planType      String    @map("plan_type") // BASIC, PREMIUM, VIP
  amount        Float
  currency      String    @default("INR")
  startDate     DateTime  @map("start_date")
  endDate       DateTime  @map("end_date")
  isActive      Boolean   @default(true) @map("is_active")
  autoRenew     Boolean   @default(false) @map("auto_renew")

  // Payment details
  paymentMethod String?   @map("payment_method")
  transactionId String?   @map("transaction_id")

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("subscriptions")
}

// ------------------- VerificationDocument Model -------------------
enum DocumentType {
  AADHAR_CARD
  PAN_CARD
  VOTER_ID
  PASSPORT
  DRIVING_LICENSE
  OTHER
}

enum DocumentStatus {
  PENDING_REVIEW
  APPROVED
  REJECTED
}

model VerificationDocument {
  id            String        @id @default(cuid())
  userId        String        @map("user_id")
  user          User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  type          DocumentType
  url           String        // URL path to the stored document
  filename      String        // Original filename
  filesize      Int           // Size in bytes
  mimeType      String        @map("mime_type")

  status        DocumentStatus @default(PENDING_REVIEW)
  adminNotes    String?       @map("admin_notes") @db.Text

  uploadedAt    DateTime      @default(now()) @map("uploaded_at")
  reviewedAt    DateTime?     @map("reviewed_at")
  reviewedBy    String?       @map("reviewed_by") // Admin ID who reviewed this document

  @@map("verification_documents")
}

// ------------------- Notification Model -------------------
model Notification {
  id            String    @id @default(cuid())
  userId        String    @map("user_id")
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  title         String
  message       String
  type          String    // e.g., VERIFICATION_REMINDER, MATCH_FOUND, MESSAGE_RECEIVED
  actionUrl     String?   @map("action_url")

  isRead        Boolean   @default(false) @map("is_read")
  readAt        DateTime? @map("read_at")

  createdAt     DateTime  @default(now()) @map("created_at")

  @@map("notifications")
}

// ------------------- Usage Tracking Models -------------------

// Track profile views
model ProfileView {
  id            String    @id @default(cuid())
  viewerId      String    @map("viewer_id")
  viewer        User      @relation("ViewerRelation", fields: [viewerId], references: [id], onDelete: Cascade)
  viewedId      String    @map("viewed_id")
  viewed        User      @relation("ViewedRelation", fields: [viewedId], references: [id], onDelete: Cascade)
  viewedAt      DateTime  @default(now()) @map("viewed_at")

  @@map("profile_views")
}

// Track match views
model MatchView {
  id            String    @id @default(cuid())
  userId        String    @map("user_id")
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  viewedAt      DateTime  @default(now()) @map("viewed_at")

  @@map("match_views")
}

// ------------------- Conversation Model -------------------
model Conversation {
  id            String    @id @default(cuid())

  // The two users in this conversation
  user1Id       String    @map("user1_id")
  user1         User      @relation("User1Conversations", fields: [user1Id], references: [id], onDelete: Cascade)
  user2Id       String    @map("user2_id")
  user2         User      @relation("User2Conversations", fields: [user2Id], references: [id], onDelete: Cascade)

  // Conversation status
  lastMessageAt DateTime? @map("last_message_at")
  isActive      Boolean   @default(true) @map("is_active")

  // Timestamps
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  // Relationships
  messages      Message[]

  @@unique([user1Id, user2Id])
  @@index([user1Id])
  @@index([user2Id])
  @@map("conversations")
}

// ------------------- Message Model -------------------
model Message {
  id            String      @id @default(cuid())

  // Sender and receiver
  senderId      String      @map("sender_id")
  sender        User        @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)
  receiverId    String      @map("receiver_id")
  receiver      User        @relation("ReceivedMessages", fields: [receiverId], references: [id], onDelete: Cascade)

  // Conversation relationship
  conversationId String?    @map("conversation_id")
  conversation   Conversation? @relation(fields: [conversationId], references: [id], onDelete: SetNull)

  // Message content
  content       String      @db.Text
  messageType   MessageType @default(TEXT) @map("message_type")
  metadata      String?     @db.Text // JSON string with additional data based on message type

  // Message status
  isRead        Boolean     @default(false) @map("is_read")
  readAt        DateTime?   @map("read_at")
  sentAt        DateTime    @default(now()) @map("sent_at")

  // Moderation fields
  isModerated   Boolean     @default(false) @map("is_moderated")
  moderationStatus String?  @map("moderation_status") // APPROVED, REJECTED, PENDING
  moderatedContent String?  @db.Text @map("moderated_content") // Content after moderation (e.g., with profanity masked)
  moderationFlags String?   @map("moderation_flags") // Comma-separated list of flags (profanity, contact_info, spam, etc.)

  @@index([conversationId])
  @@map("messages")
}

// ------------------- Subscription Plan Model -------------------
model SubscriptionPlan {
  id            String    @id @default(cuid())
  name          String
  planType      String    @map("plan_type") // BASIC, PREMIUM, VIP
  price         Float
  currency      String    @default("INR")
  duration      Int       // Duration in days
  description   String?
  features      String    @db.Text // JSON array of features
  isActive      Boolean   @default(true) @map("is_active")

  // Relationships
  featureAccess FeatureAccess[]

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("subscription_plans")
}

// ------------------- Feature Configuration Models -------------------

// Features that can be configured
model Feature {
  id            String    @id @default(cuid())
  name          String    @unique
  displayName   String    @map("display_name")
  description   String?
  category      String    // BASIC, COMMUNICATION, MATCHING, PREMIUM
  isActive      Boolean   @default(true) @map("is_active")

  // Relationships
  accessRules   FeatureAccess[]

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("features")
}

// Access rules for features based on user tier
model FeatureAccess {
  id                String    @id @default(cuid())
  featureId         String    @map("feature_id")
  feature           Feature   @relation(fields: [featureId], references: [id], onDelete: Cascade)

  // User tier this rule applies to
  userTier          String    @map("user_tier") // BASIC, VERIFIED, PREMIUM

  // Subscription plan this rule applies to (optional, for premium tier)
  subscriptionPlanId String?  @map("subscription_plan_id")
  subscriptionPlan   SubscriptionPlan? @relation(fields: [subscriptionPlanId], references: [id], onDelete: SetNull)

  // Access configuration
  isEnabled         Boolean   @default(false) @map("is_enabled")
  dailyLimit        Int?      @map("daily_limit")
  totalLimit        Int?      @map("total_limit")
  limitPeriod       String?   @map("limit_period") // DAILY, WEEKLY, MONTHLY, TOTAL

  // For search features
  allowedFilters    String?   @map("allowed_filters") @db.Text // JSON array of allowed filters

  // Upgrade messaging
  upgradeMessage    String?   @map("upgrade_message")

  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @default(now()) @updatedAt @map("updated_at")

  @@unique([featureId, userTier, subscriptionPlanId])
  @@map("feature_access")
}

// System configuration for the application
model SystemConfig {
  id            String    @id @default(cuid())
  configKey     String    @unique @map("config_key")
  configValue   String    @map("config_value") @db.Text
  description   String?
  isActive      Boolean   @default(true) @map("is_active")

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("system_config")
}

// ------------------- Moderation Log Model -------------------
model ModerationLog {
  id            String    @id @default(cuid())

  // Content type and ID
  contentType   String    @map("content_type") // PHOTO, MESSAGE, PROFILE, etc.
  contentId     String?   @map("content_id") // ID of the moderated content

  // Photo relationship (for backward compatibility)
  photoId       String?   @map("photo_id")
  photo         Photo?    @relation(fields: [photoId], references: [id], onDelete: SetNull)

  // User who created the content
  userId        String?   @map("user_id")

  // Moderation details
  decision      String    @map("decision") // APPROVED, REJECTED, PENDING
  flags         String?   @map("flags") // Comma-separated flags from analysis
  confidence    Float?    @map("confidence") // Confidence score (0-100)
  details       String?   @db.Text // JSON string with detailed analysis

  // AI-specific fields (for photo moderation)
  aiDecision    String?   @map("ai_decision") // APPROVED, REJECTED, PENDING
  aiFlags       String?   @map("ai_flags") // Comma-separated flags from AI analysis
  aiConfidence  Float?    @map("ai_confidence") // Confidence score from AI (0-100)

  // Admin action
  reviewedBy    String?   @map("reviewed_by") // Admin ID who reviewed this
  reviewedAt    DateTime? @map("reviewed_at")
  adminNotes    String?   @map("admin_notes")

  createdAt     DateTime  @default(now()) @map("created_at")

  @@index([photoId])
  @@index([contentType, contentId])
  @@index([userId])
  @@map("moderation_logs")
}

// --- Add other models later as needed ---
// model Transaction { ... }
// model BlogPost { ... }
// model SuccessStory { ... }
// model Setting { ... }
