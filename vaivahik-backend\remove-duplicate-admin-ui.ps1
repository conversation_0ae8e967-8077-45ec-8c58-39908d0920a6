# Script to remove duplicate admin UI files
# This script removes the admin UI files that are no longer needed

# Remove admin-ui directory
if (Test-Path -Path "admin-ui") {
    Write-Host "Removing admin-ui directory..."
    Remove-Item -Path "admin-ui" -Recurse -Force
    Write-Host "admin-ui directory removed successfully."
}

# Remove public/admin directory
if (Test-Path -Path "public/admin") {
    Write-Host "Removing public/admin directory..."
    Remove-Item -Path "public/admin" -Recurse -Force
    Write-Host "public/admin directory removed successfully."
}

Write-Host "Cleanup completed. Duplicate admin UI files have been removed."
Write-Host "Note: The admin API routes and controllers have been preserved as they are likely used by your Next.js frontend."
