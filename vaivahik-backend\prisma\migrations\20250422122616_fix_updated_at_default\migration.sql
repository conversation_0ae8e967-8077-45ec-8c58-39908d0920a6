-- <PERSON>reate<PERSON><PERSON>
CREATE TYPE "PhotoVisibility" AS ENUM ('PUBLIC', 'PAID', 'CONNECTIONS_ONLY');

-- CreateTable
CREATE TABLE "users" (
    "id" TEXT NOT NULL,
    "phone" TEXT NOT NULL,
    "email" TEXT,
    "password" TEXT,
    "isVerified" BOOLEAN NOT NULL DEFAULT false,
    "profileStatus" TEXT NOT NULL DEFAULT 'INCOMPLETE',
    "fullName" TEXT,
    "gender" TEXT,
    "birth_date" TIMESTAMP(3),
    "birth_time" TEXT,
    "birth_place" TEXT,
    "height" TEXT,
    "city" TEXT,
    "education" TEXT,
    "occupation" TEXT,
    "income_range" TEXT,
    "profile_picture_url" TEXT,
    "profile_picture_visibility" "PhotoVisibility" NOT NULL DEFAULT 'PUBLIC',
    "father_name" TEXT,
    "mother_name" TEXT,
    "uncle_name" TEXT,
    "native_place" TEXT,
    "total_siblings" INTEGER,
    "married_siblings" INTEGER,
    "family_contact" TEXT,
    "isPremium" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "admins" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "role" TEXT NOT NULL DEFAULT 'ADMIN',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "admins_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_phone_key" ON "users"("phone");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "admins_email_key" ON "admins"("email");
