import { useState } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Switch,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControlLabel,
  Tooltip,
  Paper
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import InfoIcon from '@mui/icons-material/Info';

export default function CategoriesTab({ categories, onUpdate, onDelete }) {
  const [editingCategory, setEditingCategory] = useState(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState(null);

  const handleAddCategory = () => {
    setEditingCategory({
      id: '',
      name: '',
      displayName: '',
      description: '',
      displayOrder: categories.length + 1,
      icon: '',
      isActive: true,
      isRequired: false
    });
    setIsDialogOpen(true);
  };

  const handleEditCategory = (category) => {
    setEditingCategory({ ...category });
    setIsDialogOpen(true);
  };

  const handleDeleteCategory = (category) => {
    setCategoryToDelete(category);
    setDeleteConfirmOpen(true);
  };

  const confirmDeleteCategory = () => {
    if (onDelete) {
      // Use the API delete function
      onDelete(categoryToDelete.id);
    } else {
      // Fallback to client-side filtering
      // Filter out the category to delete
      const updatedCategories = categories.filter(cat => cat.id !== categoryToDelete.id);

      // Update the display order of remaining categories
      const reorderedCategories = updatedCategories.map((cat, index) => ({
        ...cat,
        displayOrder: index + 1
      }));

      onUpdate(reorderedCategories);
    }

    setDeleteConfirmOpen(false);
    setCategoryToDelete(null);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditingCategory(null);
  };

  const handleSaveCategory = () => {
    if (!editingCategory.name || !editingCategory.displayName) {
      // Show validation error
      return;
    }

    let updatedCategories;

    if (editingCategory.id) {
      // Update existing category
      updatedCategories = categories.map(cat =>
        cat.id === editingCategory.id ? editingCategory : cat
      );
    } else {
      // Add new category with a generated ID
      const newCategory = {
        ...editingCategory,
        id: `cat${Date.now()}` // Simple ID generation
      };
      updatedCategories = [...categories, newCategory];
    }

    // Sort by display order
    updatedCategories.sort((a, b) => a.displayOrder - b.displayOrder);

    onUpdate(updatedCategories);
    setIsDialogOpen(false);
    setEditingCategory(null);
  };

  const handleMoveCategory = (categoryId, direction) => {
    const categoryIndex = categories.findIndex(cat => cat.id === categoryId);
    if (
      (direction === 'up' && categoryIndex === 0) ||
      (direction === 'down' && categoryIndex === categories.length - 1)
    ) {
      return; // Can't move further
    }

    const updatedCategories = [...categories];
    const targetIndex = direction === 'up' ? categoryIndex - 1 : categoryIndex + 1;

    // Swap display orders
    const tempOrder = updatedCategories[categoryIndex].displayOrder;
    updatedCategories[categoryIndex].displayOrder = updatedCategories[targetIndex].displayOrder;
    updatedCategories[targetIndex].displayOrder = tempOrder;

    // Sort by display order
    updatedCategories.sort((a, b) => a.displayOrder - b.displayOrder);

    onUpdate(updatedCategories);
  };

  const handleInputChange = (e) => {
    const { name, value, checked } = e.target;

    setEditingCategory(prev => ({
      ...prev,
      [name]: name === 'isActive' || name === 'isRequired' ? checked : value
    }));
  };

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleAddCategory}
        >
          Add Category
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Order</TableCell>
              <TableCell>Name</TableCell>
              <TableCell>Display Name</TableCell>
              <TableCell>Description</TableCell>
              <TableCell>Icon</TableCell>
              <TableCell>Required</TableCell>
              <TableCell>Active</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {categories.map((category) => (
              <TableRow key={category.id}>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {category.displayOrder}
                    <Box>
                      <IconButton
                        size="small"
                        onClick={() => handleMoveCategory(category.id, 'up')}
                        disabled={category.displayOrder === 1}
                      >
                        <ArrowUpwardIcon fontSize="small" />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleMoveCategory(category.id, 'down')}
                        disabled={category.displayOrder === categories.length}
                      >
                        <ArrowDownwardIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>{category.name}</TableCell>
                <TableCell>{category.displayName}</TableCell>
                <TableCell>{category.description}</TableCell>
                <TableCell>{category.icon}</TableCell>
                <TableCell>
                  <Switch
                    checked={category.isRequired}
                    disabled
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Switch
                    checked={category.isActive}
                    disabled
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <IconButton
                    color="primary"
                    size="small"
                    onClick={() => handleEditCategory(category)}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                  <IconButton
                    color="error"
                    size="small"
                    onClick={() => handleDeleteCategory(category)}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Edit/Add Category Dialog */}
      <Dialog open={isDialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingCategory?.id ? 'Edit Category' : 'Add Category'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                name="name"
                label="Internal Name"
                value={editingCategory?.name || ''}
                onChange={handleInputChange}
                fullWidth
                required
                helperText="Used in code (e.g., physical_attributes)"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="displayName"
                label="Display Name"
                value={editingCategory?.displayName || ''}
                onChange={handleInputChange}
                fullWidth
                required
                helperText="Shown to users (e.g., Physical Attributes)"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="description"
                label="Description"
                value={editingCategory?.description || ''}
                onChange={handleInputChange}
                fullWidth
                multiline
                rows={2}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="icon"
                label="Icon"
                value={editingCategory?.icon || ''}
                onChange={handleInputChange}
                fullWidth
                helperText="Icon name (e.g., person, school)"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="displayOrder"
                label="Display Order"
                type="number"
                value={editingCategory?.displayOrder || 1}
                onChange={handleInputChange}
                fullWidth
                InputProps={{ inputProps: { min: 1 } }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    name="isRequired"
                    checked={editingCategory?.isRequired || false}
                    onChange={handleInputChange}
                  />
                }
                label="Required"
              />
              <Tooltip title="If required, users must fill in at least one preference in this category">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    name="isActive"
                    checked={editingCategory?.isActive || false}
                    onChange={handleInputChange}
                  />
                }
                label="Active"
              />
              <Tooltip title="If inactive, this category will not be shown to users">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSaveCategory} variant="contained" color="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteConfirmOpen} onClose={() => setDeleteConfirmOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the category "{categoryToDelete?.displayName}"?
            This will also delete all fields associated with this category.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button onClick={confirmDeleteCategory} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
