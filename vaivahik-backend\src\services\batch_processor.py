"""
Batch Processor for Matrimony Matching

This module provides batch processing functionality for the matrimony matching system
to improve efficiency and reduce resource usage.
"""

import os
import json
import logging
import asyncio
import torch
import numpy as np
from datetime import datetime, timedelta
from prisma.client import PrismaClient
from collections import defaultdict

from .embedding_service import EmbeddingService
from .redis_cache import RedisCache

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BatchProcessor:
    """Batch processor for matrimony matching"""
    
    def __init__(self, embedding_service=None, redis_cache=None, config=None):
        """
        Initialize the batch processor
        
        Args:
            embedding_service: Embedding service instance
            redis_cache: Redis cache instance
            config (dict): Configuration parameters
        """
        # Default configuration
        self.default_config = {
            'batch_size': 64,
            'processing_interval': 300,  # 5 minutes in seconds
            'max_queue_size': 1000,
            'similarity_threshold': 0.5,
            'max_matches_per_user': 100
        }
        
        # Use provided config or default
        self.config = config if config else self.default_config
        
        # Initialize Prisma client
        self.prisma = PrismaClient()
        
        # Initialize Redis cache
        self.redis_cache = redis_cache
        
        # Initialize embedding service
        self.embedding_service = embedding_service
        
        # Initialize processing queue
        self.processing_queue = []
        
        # Set device
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {self.device}")
    
    async def add_to_queue(self, user_id):
        """
        Add a user to the processing queue
        
        Args:
            user_id (str): User ID
            
        Returns:
            bool: Success status
        """
        # Check if user is already in queue
        if user_id in self.processing_queue:
            return True
        
        # Check if queue is full
        if len(self.processing_queue) >= self.config['max_queue_size']:
            logger.warning(f"Processing queue is full, cannot add user {user_id}")
            return False
        
        # Add to queue
        self.processing_queue.append(user_id)
        logger.info(f"Added user {user_id} to processing queue")
        return True
    
    async def process_queue(self):
        """
        Process the queue
        
        Returns:
            int: Number of users processed
        """
        if not self.processing_queue:
            return 0
        
        # Get batch from queue
        batch_size = min(self.config['batch_size'], len(self.processing_queue))
        batch = self.processing_queue[:batch_size]
        self.processing_queue = self.processing_queue[batch_size:]
        
        logger.info(f"Processing batch of {len(batch)} users")
        
        # Process each user in the batch
        processed_count = 0
        for user_id in batch:
            try:
                # Process user
                success = await self.process_user(user_id)
                if success:
                    processed_count += 1
            except Exception as e:
                logger.error(f"Error processing user {user_id}: {str(e)}")
        
        logger.info(f"Processed {processed_count} users")
        return processed_count
    
    async def process_user(self, user_id):
        """
        Process a user
        
        Args:
            user_id (str): User ID
            
        Returns:
            bool: Success status
        """
        try:
            # Get user embedding
            user_embedding = await self.embedding_service.get_user_embedding(user_id)
            if user_embedding is None:
                logger.error(f"Could not get embedding for user {user_id}")
                return False
            
            # Get user
            user = await self.prisma.user.find_unique(
                where={'id': user_id},
                include={
                    'profile': True,
                    'preference': True
                }
            )
            
            if not user or not user.profile:
                logger.error(f"User {user_id} not found or has no profile")
                return False
            
            # Get potential matches
            potential_matches = await self.prisma.user.find_many(
                where={
                    'id': {'not': user_id},
                    'profileStatus': 'ACTIVE',
                    'gender': user.preference.gender if user.preference else ('FEMALE' if user.gender == 'MALE' else 'MALE')
                },
                include={
                    'profile': True
                },
                take=500  # Get more than needed for scoring
            )
            
            if not potential_matches:
                logger.info(f"No potential matches found for user {user_id}")
                return True
            
            # Process matches in batches
            batch_size = self.config['batch_size']
            all_matches = []
            
            for i in range(0, len(potential_matches), batch_size):
                batch = potential_matches[i:i+batch_size]
                
                # Get match embeddings
                match_embeddings = []
                match_ids = []
                
                for match in batch:
                    if not match.profile:
                        continue
                    
                    # Try to get from cache first
                    match_embedding = None
                    if self.redis_cache and self.redis_cache.is_connected():
                        match_embedding = await self.redis_cache.get_user_embedding(match.id)
                    
                    # Compute if not in cache
                    if match_embedding is None:
                        match_profile = {
                            'id': match.id,
                            'name': match.name,
                            'age': match.profile.age,
                            'gender': match.profile.gender,
                            'height': match.profile.height,
                            'religion': match.profile.religion,
                            'caste': match.profile.caste,
                            'subCaste': match.profile.subCaste,
                            'gotra': match.profile.gotra,
                            'education': match.profile.education,
                            'occupation': match.profile.occupation,
                            'income': match.profile.income,
                            'city': match.profile.city,
                            'state': match.profile.state,
                            'maritalStatus': match.profile.maritalStatus
                        }
                        
                        match_embedding = await self.embedding_service.compute_match_embedding(match_profile)
                        
                        # Cache embedding
                        if match_embedding is not None and self.redis_cache and self.redis_cache.is_connected():
                            await self.redis_cache.cache_user_embedding(match.id, match_embedding)
                    
                    if match_embedding is not None:
                        match_embeddings.append(match_embedding)
                        match_ids.append(match.id)
                
                # Calculate similarities
                if match_embeddings:
                    # Stack embeddings
                    match_embeddings_tensor = torch.stack(match_embeddings)
                    
                    # Calculate cosine similarity
                    user_embedding_normalized = user_embedding / user_embedding.norm(dim=1, keepdim=True)
                    match_embeddings_normalized = match_embeddings_tensor / match_embeddings_tensor.norm(dim=1, keepdim=True)
                    similarities = torch.mm(user_embedding_normalized, match_embeddings_normalized.t()).squeeze()
                    
                    # Convert to list
                    similarities = similarities.tolist()
                    if not isinstance(similarities, list):
                        similarities = [similarities]
                    
                    # Create match results
                    for match_id, similarity in zip(match_ids, similarities):
                        # Convert similarity to percentage score
                        score = int(round((similarity + 1) / 2 * 100))
                        
                        # Add to matches if above threshold
                        if score >= self.config['similarity_threshold'] * 100:
                            all_matches.append({
                                'userId': match_id,
                                'score': score
                            })
            
            # Sort matches by score
            all_matches.sort(key=lambda x: x['score'], reverse=True)
            
            # Limit to max matches
            all_matches = all_matches[:self.config['max_matches_per_user']]
            
            # Cache matches
            if self.redis_cache and self.redis_cache.is_connected():
                await self.redis_cache.cache_match_results(user_id, all_matches)
            
            logger.info(f"Processed {len(all_matches)} matches for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error processing user {user_id}: {str(e)}")
            return False
    
    async def start_processing_scheduler(self):
        """Start the batch processing scheduler"""
        while True:
            try:
                # Process queue
                await self.process_queue()
                
                # Sleep until next processing
                await asyncio.sleep(self.config['processing_interval'])
            except Exception as e:
                logger.error(f"Error in processing scheduler: {str(e)}")
                await asyncio.sleep(60)  # Sleep for a minute before retrying
    
    async def schedule_active_users(self):
        """Schedule active users for processing"""
        try:
            # Get active users
            active_users = await self.prisma.user.find_many(
                where={
                    'profileStatus': 'ACTIVE',
                    'lastActive': {
                        'gte': datetime.now() - timedelta(days=7)  # Active in the last 7 days
                    }
                },
                select={
                    'id': True
                },
                take=self.config['max_queue_size']
            )
            
            # Add to queue
            for user in active_users:
                await self.add_to_queue(user.id)
            
            logger.info(f"Scheduled {len(active_users)} active users for processing")
            return len(active_users)
        except Exception as e:
            logger.error(f"Error scheduling active users: {str(e)}")
            return 0
