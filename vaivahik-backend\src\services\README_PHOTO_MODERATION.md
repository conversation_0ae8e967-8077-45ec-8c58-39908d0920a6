# Photo Moderation Service

This service provides photo moderation functionality for the Vaivahik matrimony app using PyTorch.

## Overview

The photo moderation service:
- Detects NSFW content in images
- Performs face detection (counts faces)
- Assesses image quality
- Supports different operation modes (manual, shadow, limited auto, full auto)

## Implementation

The service uses PyTorch for both NSFW detection and face detection, replacing the previous TensorFlow implementation. This provides several advantages:
- Single ML framework for both photo moderation and the 2-tower matching model
- Reduced memory footprint
- Simplified dependency management
- Better performance on the same hardware

## Setup Instructions

### 1. Install Python Dependencies

```bash
cd vaivahik-backend
pip install -r requirements.txt
```

### 2. Prepare Models

The service will automatically download pre-trained models if they don't exist, but for better performance, you can download them in advance:

```bash
# Create models directory
mkdir -p models

# Download NSFW detection model (if you have a pre-trained one)
# cp /path/to/nsfw_model.pt models/

# Face detection models will be downloaded automatically by facenet-pytorch
```

### 3. Configuration

The service reads configuration from the database (`systemConfig` table with `configKey: 'photoModeration'`). You can configure:

- `operationMode`:
  - 0: Manual (all photos require manual review)
  - 1: Shadow (AI analyzes but all photos still require manual review)
  - 2: Limited Auto (AI auto-approves/rejects some photos based on `automationPercentage`)
  - 3: Full Auto (AI auto-approves/rejects all photos)
- `automationPercentage`: Percentage of photos to auto-process in Limited Auto mode
- `rejectExplicitContent`: Whether to auto-reject explicit content
- `rejectNoFace`: Whether to auto-reject photos with no faces
- `rejectMultipleFaces`: Whether to auto-reject photos with multiple faces
- `rejectPoorQuality`: Whether to auto-reject poor quality photos
- `nsfw_threshold`: Threshold for NSFW content detection (0.0-1.0)

## How It Works

1. When a user uploads a photo, it's saved to disk
2. The photo moderation service is called with the path to the photo
3. The service spawns a Python process that uses PyTorch to analyze the photo
4. The Python process returns a JSON result with the moderation decision
5. The service processes the result according to the configured operation mode
6. The photo is either approved, rejected, or queued for manual review

## Fallback Mechanism

If Python or PyTorch is not available, the service will fall back to a mock implementation that approves all photos. This ensures the app continues to function even if the ML components are not properly set up.

## Files

- `photoModeration.service.js`: Main JavaScript service that interfaces with the rest of the app
- `photoModeration_pytorch.py`: PyTorch implementation of the photo moderation
- `run_pytorch_moderation.py`: Bridge script between JavaScript and PyTorch

## Troubleshooting

### Python Not Found

If you see "Python not found, photo moderation will run in mock mode" in the logs:
1. Ensure Python is installed and in your PATH
2. Try running `python --version` from the command line

### Model Loading Errors

If you see errors related to loading models:
1. Check that you have internet access (for downloading models)
2. Ensure you have sufficient disk space
3. Try manually downloading the models

### Performance Issues

If photo moderation is slow:
1. Consider upgrading to a GPU-enabled server
2. Optimize the image size before processing
3. Implement a queue system for batch processing
