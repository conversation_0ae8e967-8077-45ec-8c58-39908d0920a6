// API endpoint for subscription plans
import { getMockPlans } from '../mockData';

export default function handler(req, res) {
  // Set proper content type header
  res.setHeader('Content-Type', 'application/json');

  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return getPlans(req, res);
      case 'POST':
        return createPlan(req, res);
      case 'PUT':
        return updatePlan(req, res);
      default:
        return res.status(405).json({
          success: false,
          message: 'Method not allowed'
        });
    }
  } catch (error) {
    console.error('Error in subscription plans API:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}

// GET /api/admin/subscription-plans
function getPlans(req, res) {
  try {
    // In a real implementation, this would fetch data from a database
    // For now, we'll use mock data
    const mockPlans = getMockPlans();

    // Return the plans
    return res.status(200).json({
      success: true,
      plans: mockPlans
    });
  } catch (error) {
    console.error('Error fetching subscription plans:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch subscription plans'
    });
  }
}

// POST /api/admin/subscription-plans
function createPlan(req, res) {
  try {
    // Get plan data from request body
    const planData = req.body;

    // Validate required fields
    if (!planData.name || !planData.price || !planData.duration) {
      return res.status(400).json({
        success: false,
        message: 'Name, price, and duration are required'
      });
    }

    // In a real implementation, this would create a new plan in the database
    // For now, we'll just return a success response with a mock ID

    const newPlan = {
      ...planData,
      id: `plan-${Date.now()}` // Generate a mock ID
    };

    return res.status(201).json({
      success: true,
      message: 'Plan created successfully',
      plan: newPlan
    });
  } catch (error) {
    console.error('Error creating subscription plan:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create subscription plan'
    });
  }
}

// PUT /api/admin/subscription-plans
function updatePlan(req, res) {
  try {
    // Get plan data from request body
    const planData = req.body;

    // Validate required fields
    if (!planData.id) {
      return res.status(400).json({
        success: false,
        message: 'Plan ID is required for update'
      });
    }

    if (!planData.name || !planData.price || !planData.duration) {
      return res.status(400).json({
        success: false,
        message: 'Name, price, and duration are required'
      });
    }

    // In a real implementation, this would update the plan in the database
    // For now, we'll just return a success response

    return res.status(200).json({
      success: true,
      message: 'Plan updated successfully',
      plan: planData
    });
  } catch (error) {
    console.error('Error updating subscription plan:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update subscription plan'
    });
  }
}


