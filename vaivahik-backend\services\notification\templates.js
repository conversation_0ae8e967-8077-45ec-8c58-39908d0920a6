/**
 * Notification Templates
 * 
 * This file contains templates for different types of notifications.
 * Each template is a function that returns an object with the notification content.
 */

/**
 * Template for new match notifications
 * 
 * @param {Object} data - Data for the notification
 * @param {string} data.recipientName - Name of the recipient
 * @param {string} data.matchName - Name of the match
 * @param {string} data.matchId - ID of the match
 * @param {string} data.matchPhotoUrl - URL of the match's profile photo
 * @returns {Object} Notification content
 */
const newMatchTemplate = (data) => {
  const { recipientName, matchName, matchId, matchPhotoUrl } = data;
  
  return {
    title: 'New Match Found!',
    body: `${recipientName}, we found a new match for you: ${matchName}`,
    imageUrl: matchPhotoUrl,
    data: {
      type: 'NEW_MATCH',
      matchId,
      click_action: `FLUTTER_NOTIFICATION_CLICK`,
      webLink: `/profile/${matchId}`
    }
  };
};

/**
 * Template for profile view notifications
 * 
 * @param {Object} data - Data for the notification
 * @param {string} data.viewerName - Name of the person who viewed the profile
 * @param {string} data.viewerId - ID of the person who viewed the profile
 * @param {string} data.viewerPhotoUrl - URL of the viewer's profile photo
 * @returns {Object} Notification content
 */
const profileViewTemplate = (data) => {
  const { viewerName, viewerId, viewerPhotoUrl } = data;
  
  return {
    title: 'Someone Viewed Your Profile',
    body: `${viewerName} just viewed your profile`,
    imageUrl: viewerPhotoUrl,
    data: {
      type: 'PROFILE_VIEW',
      viewerId,
      click_action: `FLUTTER_NOTIFICATION_CLICK`,
      webLink: `/profile/${viewerId}`
    }
  };
};

/**
 * Template for interest received notifications
 * 
 * @param {Object} data - Data for the notification
 * @param {string} data.senderName - Name of the person who sent the interest
 * @param {string} data.senderId - ID of the person who sent the interest
 * @param {string} data.senderPhotoUrl - URL of the sender's profile photo
 * @param {string} data.interestId - ID of the interest
 * @returns {Object} Notification content
 */
const interestReceivedTemplate = (data) => {
  const { senderName, senderId, senderPhotoUrl, interestId } = data;
  
  return {
    title: 'New Interest Received',
    body: `${senderName} has shown interest in your profile`,
    imageUrl: senderPhotoUrl,
    data: {
      type: 'INTEREST_RECEIVED',
      senderId,
      interestId,
      click_action: `FLUTTER_NOTIFICATION_CLICK`,
      webLink: `/interests/received`
    }
  };
};

/**
 * Template for interest accepted notifications
 * 
 * @param {Object} data - Data for the notification
 * @param {string} data.recipientName - Name of the recipient
 * @param {string} data.acceptorName - Name of the person who accepted the interest
 * @param {string} data.acceptorId - ID of the person who accepted the interest
 * @param {string} data.acceptorPhotoUrl - URL of the acceptor's profile photo
 * @returns {Object} Notification content
 */
const interestAcceptedTemplate = (data) => {
  const { recipientName, acceptorName, acceptorId, acceptorPhotoUrl } = data;
  
  return {
    title: 'Interest Accepted',
    body: `${acceptorName} has accepted your interest request`,
    imageUrl: acceptorPhotoUrl,
    data: {
      type: 'INTEREST_ACCEPTED',
      acceptorId,
      click_action: `FLUTTER_NOTIFICATION_CLICK`,
      webLink: `/profile/${acceptorId}`
    }
  };
};

/**
 * Template for new message notifications
 * 
 * @param {Object} data - Data for the notification
 * @param {string} data.senderName - Name of the sender
 * @param {string} data.senderId - ID of the sender
 * @param {string} data.senderPhotoUrl - URL of the sender's profile photo
 * @param {string} data.messagePreview - Preview of the message
 * @param {string} data.conversationId - ID of the conversation
 * @returns {Object} Notification content
 */
const newMessageTemplate = (data) => {
  const { senderName, senderId, senderPhotoUrl, messagePreview, conversationId } = data;
  
  return {
    title: `New Message from ${senderName}`,
    body: messagePreview,
    imageUrl: senderPhotoUrl,
    data: {
      type: 'NEW_MESSAGE',
      senderId,
      conversationId,
      click_action: `FLUTTER_NOTIFICATION_CLICK`,
      webLink: `/messages/${conversationId}`
    }
  };
};

/**
 * Template for promotional notifications
 * 
 * @param {Object} data - Data for the notification
 * @param {string} data.title - Title of the promotion
 * @param {string} data.body - Body of the promotion
 * @param {string} data.imageUrl - URL of the promotion image
 * @param {string} data.promotionId - ID of the promotion
 * @param {string} data.targetUrl - Target URL for the promotion
 * @returns {Object} Notification content
 */
const promotionalTemplate = (data) => {
  const { title, body, imageUrl, promotionId, targetUrl } = data;
  
  return {
    title,
    body,
    imageUrl,
    data: {
      type: 'PROMOTION',
      promotionId,
      click_action: `FLUTTER_NOTIFICATION_CLICK`,
      webLink: targetUrl || '/promotions'
    }
  };
};

/**
 * Template for verification status notifications
 * 
 * @param {Object} data - Data for the notification
 * @param {string} data.status - Status of the verification (APPROVED, REJECTED)
 * @param {string} data.reason - Reason for rejection (if applicable)
 * @returns {Object} Notification content
 */
const verificationStatusTemplate = (data) => {
  const { status, reason } = data;
  
  if (status === 'APPROVED') {
    return {
      title: 'Profile Verification Approved',
      body: 'Congratulations! Your profile has been verified.',
      data: {
        type: 'VERIFICATION_STATUS',
        status,
        click_action: `FLUTTER_NOTIFICATION_CLICK`,
        webLink: '/profile'
      }
    };
  } else {
    return {
      title: 'Profile Verification Rejected',
      body: reason || 'Your profile verification was rejected. Please check the details.',
      data: {
        type: 'VERIFICATION_STATUS',
        status,
        reason,
        click_action: `FLUTTER_NOTIFICATION_CLICK`,
        webLink: '/profile/verification'
      }
    };
  }
};

module.exports = {
  newMatchTemplate,
  profileViewTemplate,
  interestReceivedTemplate,
  interestAcceptedTemplate,
  newMessageTemplate,
  promotionalTemplate,
  verificationStatusTemplate
};
