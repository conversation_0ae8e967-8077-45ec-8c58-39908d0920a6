// src/pages/api/admin/text-moderation/banned-words.js
import axios from 'axios';

export default async function handler(req, res) {
  // Get the auth token from the request cookies or headers
  const token = req.cookies?.adminAccessToken || req.headers.authorization?.split(' ')[1];

  if (!token) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  try {
    // Forward the request to the backend API
    const backendUrl = process.env.BACKEND_API_URL || 'http://localhost:8000';
    const endpoint = `${backendUrl}/api/admin/text-moderation/banned-words`;

    let response;

    if (req.method === 'GET') {
      response = await axios({
        method: 'GET',
        url: endpoint,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000 // 10 second timeout
      });
    } else if (req.method === 'PUT') {
      response = await axios({
        method: 'PUT',
        url: endpoint,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        data: req.body,
        timeout: 10000 // 10 second timeout
      });
    } else {
      return res.status(405).json({ success: false, message: 'Method not allowed' });
    }

    // Return the response from the backend
    return res.status(response.status).json(response.data);
  } catch (error) {
    console.error('Error handling banned words request:', error);

    // Return the error response from the backend if available
    if (error.response) {
      return res.status(error.response.status).json(error.response.data);
    }

    // Handle timeout errors
    if (error.code === 'ECONNABORTED') {
      return res.status(504).json({
        success: false,
        message: 'Request to backend timed out. Please check if the backend server is running.'
      });
    }

    // Handle connection errors
    if (error.code === 'ECONNREFUSED') {
      // Fallback to mock data if backend is not available
      if (req.method === 'GET') {
        console.log('Backend not available, returning mock data for banned words');
        return res.status(200).json({
          success: true,
          bannedWords: [
            'badword1',
            'badword2',
            'badword3',
            'offensive1',
            'offensive2',
            'inappropriate1',
            'inappropriate2'
          ]
        });
      } else if (req.method === 'PUT') {
        console.log('Backend not available, simulating successful update for banned words');
        return res.status(200).json({
          success: true,
          message: 'Banned words updated successfully (mock)'
        });
      }
    }

    // Otherwise return a generic error
    return res.status(500).json({
      success: false,
      message: 'Error connecting to backend service: ' + (error.message || 'Unknown error')
    });
  }
}
