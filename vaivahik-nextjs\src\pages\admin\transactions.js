import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);
import { toast } from 'react-toastify';
import { adminGet, adminPut } from '@/services/apiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';

export default function Transactions() {
  const [loading, setLoading] = useState(true);
  const [transactions, setTransactions] = useState([]);
  const [totalTransactions, setTotalTransactions] = useState(0);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [statusFilter, setStatusFilter] = useState('');
  const [paymentMethodFilter, setPaymentMethodFilter] = useState('');
  const [dateRange, setDateRange] = useState({ startDate: '', endDate: '' });
  const [searchQuery, setSearchQuery] = useState('');
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [currentTransaction, setCurrentTransaction] = useState(null);
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [updateFormData, setUpdateFormData] = useState({
    status: '',
    notes: ''
  });

  useEffect(() => {
    fetchTransactions();
  }, [page, limit, statusFilter, paymentMethodFilter, dateRange, searchQuery]);

  const fetchTransactions = async () => {
    setLoading(true);
    try {
      const params = {
        page,
        limit,
        ...(statusFilter && { status: statusFilter }),
        ...(paymentMethodFilter && { paymentMethod: paymentMethodFilter }),
        ...(dateRange.startDate && { startDate: dateRange.startDate }),
        ...(dateRange.endDate && { endDate: dateRange.endDate }),
        ...(searchQuery && { search: searchQuery })
      };

      const response = await adminGet(ADMIN_ENDPOINTS.TRANSACTIONS, params);

      if (response.success) {
        setTransactions(response.transactions || []);
        setTotalTransactions(response.pagination?.totalTransactions || 0);
      } else {
        toast.error(response.message || 'Failed to fetch transactions');
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
      toast.error('Error fetching transactions: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (newPage) => {
    setPage(newPage);
  };

  const handleLimitChange = (e) => {
    setLimit(parseInt(e.target.value));
    setPage(1);
  };

  const handleStatusFilterChange = (e) => {
    setStatusFilter(e.target.value);
    setPage(1);
  };

  const handlePaymentMethodFilterChange = (e) => {
    setPaymentMethodFilter(e.target.value);
    setPage(1);
  };

  const handleDateRangeChange = (e) => {
    const { name, value } = e.target;
    setDateRange({
      ...dateRange,
      [name]: value
    });
  };

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setPage(1);
    fetchTransactions();
  };

  const clearFilters = () => {
    setStatusFilter('');
    setPaymentMethodFilter('');
    setDateRange({ startDate: '', endDate: '' });
    setSearchQuery('');
    setPage(1);
  };

  const openDetailsModal = (transaction) => {
    setCurrentTransaction(transaction);
    setShowDetailsModal(true);
  };

  const openUpdateModal = (transaction) => {
    setCurrentTransaction(transaction);
    setUpdateFormData({
      status: transaction.status,
      notes: transaction.notes || ''
    });
    setShowUpdateModal(true);
  };

  const closeModal = () => {
    setShowDetailsModal(false);
    setShowUpdateModal(false);
    setCurrentTransaction(null);
  };

  const handleUpdateInputChange = (e) => {
    const { name, value } = e.target;
    setUpdateFormData({
      ...updateFormData,
      [name]: value
    });
  };

  const handleUpdateTransaction = async (e) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem('adminAccessToken');
      
      if (!token) {
        toast.error('Authentication token not found');
        return;
      }
      
      const updateData = {
        id: currentTransaction.id,
        ...updateFormData
      };
      
      const response = await fetch('/api/admin/transactions', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });
      
      const data = await response.json();
      
      if (data.success) {
        toast.success('Transaction updated successfully');
        closeModal();
        fetchTransactions();
      } else {
        toast.error(data.message || 'Failed to update transaction');
      }
    } catch (error) {
      console.error('Error updating transaction:', error);
      toast.error('Error updating transaction: ' + error.message);
    }
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);
  };

  // Calculate pagination
  const totalPages = Math.ceil(totalTransactions / limit);
  const paginationItems = [];
  for (let i = 1; i <= totalPages; i++) {
    paginationItems.push(i);
  }

  return (
    <EnhancedAdminLayout title="Transactions">
      <div className="transactions-page">
        <div className="page-header">
          <h1>Transactions</h1>
          <div className="header-actions">
            <button className="btn-secondary" onClick={clearFilters}>
              <i className="fas fa-filter"></i> Clear Filters
            </button>
            <button className="btn-primary" onClick={fetchTransactions}>
              <i className="fas fa-sync"></i> Refresh
            </button>
          </div>
        </div>

        <div className="filters-container">
          <div className="search-box">
            <form onSubmit={handleSearch}>
              <input
                type="text"
                placeholder="Search by ID, user, or reference..."
                value={searchQuery}
                onChange={handleSearchChange}
              />
              <button type="submit">
                <i className="fas fa-search"></i>
              </button>
            </form>
          </div>
          
          <div className="filter-group">
            <label htmlFor="statusFilter">Status:</label>
            <select
              id="statusFilter"
              value={statusFilter}
              onChange={handleStatusFilterChange}
            >
              <option value="">All</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
              <option value="failed">Failed</option>
              <option value="refunded">Refunded</option>
            </select>
          </div>
          
          <div className="filter-group">
            <label htmlFor="paymentMethodFilter">Payment Method:</label>
            <select
              id="paymentMethodFilter"
              value={paymentMethodFilter}
              onChange={handlePaymentMethodFilterChange}
            >
              <option value="">All</option>
              <option value="credit_card">Credit Card</option>
              <option value="debit_card">Debit Card</option>
              <option value="upi">UPI</option>
              <option value="net_banking">Net Banking</option>
              <option value="wallet">Wallet</option>
            </select>
          </div>
          
          <div className="filter-group">
            <label htmlFor="startDate">From:</label>
            <input
              type="date"
              id="startDate"
              name="startDate"
              value={dateRange.startDate}
              onChange={handleDateRangeChange}
            />
          </div>
          
          <div className="filter-group">
            <label htmlFor="endDate">To:</label>
            <input
              type="date"
              id="endDate"
              name="endDate"
              value={dateRange.endDate}
              onChange={handleDateRangeChange}
            />
          </div>
          
          <div className="filter-group">
            <label htmlFor="limitFilter">Show:</label>
            <select
              id="limitFilter"
              value={limit}
              onChange={handleLimitChange}
            >
              <option value="10">10</option>
              <option value="25">25</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
          </div>
        </div>

        {loading ? (
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading transactions...</p>
          </div>
        ) : transactions.length > 0 ? (
          <>
            <div className="table-container">
              <table className="data-table">
                <thead>
                  <tr>
                    <th>Transaction ID</th>
                    <th>User</th>
                    <th>Amount</th>
                    <th>Date</th>
                    <th>Payment Method</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {transactions.map((transaction) => (
                    <tr key={transaction.id}>
                      <td>{transaction.transactionId}</td>
                      <td>{transaction.user?.name || 'Unknown'}</td>
                      <td>{formatCurrency(transaction.amount)}</td>
                      <td>{formatDate(transaction.createdAt)}</td>
                      <td>{transaction.paymentMethod}</td>
                      <td>
                        <span className={`status-badge ${transaction.status}`}>
                          {transaction.status}
                        </span>
                      </td>
                      <td className="action-cell">
                        <button
                          className="btn-icon"
                          onClick={() => openDetailsModal(transaction)}
                          title="View Details"
                        >
                          <i className="fas fa-eye"></i>
                        </button>
                        <button
                          className="btn-icon"
                          onClick={() => openUpdateModal(transaction)}
                          title="Update Status"
                        >
                          <i className="fas fa-edit"></i>
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="pagination-container">
              <div className="pagination-info">
                Showing {(page - 1) * limit + 1} to {Math.min(page * limit, totalTransactions)} of {totalTransactions} transactions
              </div>
              <div className="pagination">
                <button
                  className="pagination-btn"
                  disabled={page === 1}
                  onClick={() => handlePageChange(page - 1)}
                >
                  <i className="fas fa-chevron-left"></i>
                </button>
                {paginationItems.map((pageNum) => (
                  <button
                    key={pageNum}
                    className={`pagination-btn ${pageNum === page ? 'active' : ''}`}
                    onClick={() => handlePageChange(pageNum)}
                  >
                    {pageNum}
                  </button>
                ))}
                <button
                  className="pagination-btn"
                  disabled={page === totalPages}
                  onClick={() => handlePageChange(page + 1)}
                >
                  <i className="fas fa-chevron-right"></i>
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="empty-state">
            <div className="empty-icon">💳</div>
            <h3>No Transactions Found</h3>
            <p>There are no transactions matching your filters.</p>
            <button className="btn-primary" onClick={clearFilters}>
              Clear Filters
            </button>
          </div>
        )}

        {/* Transaction Details Modal */}
        {showDetailsModal && currentTransaction && (
          <div className="modal-overlay">
            <div className="modal-content">
              <div className="modal-header">
                <h2>Transaction Details</h2>
                <button className="close-modal" onClick={closeModal}>×</button>
              </div>
              <div className="modal-body">
                <div className="transaction-details">
                  <div className="detail-row">
                    <div className="detail-label">Transaction ID:</div>
                    <div className="detail-value">{currentTransaction.transactionId}</div>
                  </div>
                  <div className="detail-row">
                    <div className="detail-label">User:</div>
                    <div className="detail-value">{currentTransaction.user?.name || 'Unknown'}</div>
                  </div>
                  <div className="detail-row">
                    <div className="detail-label">User ID:</div>
                    <div className="detail-value">{currentTransaction.userId}</div>
                  </div>
                  <div className="detail-row">
                    <div className="detail-label">Amount:</div>
                    <div className="detail-value">{formatCurrency(currentTransaction.amount)}</div>
                  </div>
                  <div className="detail-row">
                    <div className="detail-label">Date:</div>
                    <div className="detail-value">{formatDate(currentTransaction.createdAt)}</div>
                  </div>
                  <div className="detail-row">
                    <div className="detail-label">Payment Method:</div>
                    <div className="detail-value">{currentTransaction.paymentMethod}</div>
                  </div>
                  <div className="detail-row">
                    <div className="detail-label">Status:</div>
                    <div className="detail-value">
                      <span className={`status-badge ${currentTransaction.status}`}>
                        {currentTransaction.status}
                      </span>
                    </div>
                  </div>
                  <div className="detail-row">
                    <div className="detail-label">Subscription Plan:</div>
                    <div className="detail-value">{currentTransaction.subscriptionPlan?.name || 'N/A'}</div>
                  </div>
                  <div className="detail-row">
                    <div className="detail-label">Payment Gateway:</div>
                    <div className="detail-value">{currentTransaction.paymentGateway || 'N/A'}</div>
                  </div>
                  <div className="detail-row">
                    <div className="detail-label">Gateway Reference:</div>
                    <div className="detail-value">{currentTransaction.gatewayReference || 'N/A'}</div>
                  </div>
                  <div className="detail-row">
                    <div className="detail-label">Notes:</div>
                    <div className="detail-value">{currentTransaction.notes || 'No notes'}</div>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button className="btn-secondary" onClick={closeModal}>
                  Close
                </button>
                <button className="btn-primary" onClick={() => {
                  closeModal();
                  openUpdateModal(currentTransaction);
                }}>
                  Update Status
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Update Transaction Modal */}
        {showUpdateModal && currentTransaction && (
          <div className="modal-overlay">
            <div className="modal-content">
              <div className="modal-header">
                <h2>Update Transaction Status</h2>
                <button className="close-modal" onClick={closeModal}>×</button>
              </div>
              <form onSubmit={handleUpdateTransaction}>
                <div className="modal-body">
                  <div className="form-group">
                    <label htmlFor="transaction-id">Transaction ID:</label>
                    <input
                      type="text"
                      id="transaction-id"
                      value={currentTransaction.transactionId}
                      readOnly
                    />
                  </div>
                  <div className="form-group">
                    <label htmlFor="status">Status:</label>
                    <select
                      id="status"
                      name="status"
                      value={updateFormData.status}
                      onChange={handleUpdateInputChange}
                      required
                    >
                      <option value="completed">Completed</option>
                      <option value="pending">Pending</option>
                      <option value="failed">Failed</option>
                      <option value="refunded">Refunded</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <label htmlFor="notes">Notes:</label>
                    <textarea
                      id="notes"
                      name="notes"
                      value={updateFormData.notes}
                      onChange={handleUpdateInputChange}
                      rows="3"
                      placeholder="Add notes about this transaction"
                    ></textarea>
                  </div>
                </div>
                <div className="modal-footer">
                  <button type="button" className="btn-secondary" onClick={closeModal}>
                    Cancel
                  </button>
                  <button type="submit" className="btn-primary">
                    Update Transaction
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </EnhancedAdminLayout>
  );
}
