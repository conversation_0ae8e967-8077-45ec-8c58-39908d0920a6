/**
 * API Documentation Routes
 * 
 * This file contains routes for API documentation.
 */

const express = require('express');
const router = express.Router();
const path = require('path');
const { generateOpenApiSpec, saveOpenApiSpec } = require('../utils/openApiGenerator');
const apiResponse = require('../utils/apiResponse');
const logger = require('../utils/logger');

/**
 * @route   GET /api/v1/docs/openapi.json
 * @desc    Get OpenAPI specification
 * @access  Public
 */
router.get('/openapi.json', (req, res, next) => {
  try {
    // Generate OpenAPI specification
    const spec = generateOpenApiSpec();
    
    // Return specification
    return res.status(200).json(spec);
  } catch (error) {
    logger.error('Error generating OpenAPI specification', { error: error.message });
    return next(error);
  }
});

/**
 * @route   GET /api/v1/docs/endpoints
 * @desc    Get API endpoints
 * @access  Public
 */
router.get('/endpoints', (req, res, next) => {
  try {
    // Generate OpenAPI specification
    const spec = generateOpenApiSpec();
    
    // Extract endpoints from specification
    const endpoints = [];
    
    for (const [path, methods] of Object.entries(spec.paths)) {
      for (const [method, details] of Object.entries(methods)) {
        if (method !== 'parameters') { // Skip common parameters
          endpoints.push({
            path,
            method: method.toUpperCase(),
            summary: details.summary || '',
            description: details.description || '',
            tags: details.tags || [],
            parameters: details.parameters || [],
            requestBody: details.requestBody || null,
            responses: details.responses || {},
            security: details.security || []
          });
        }
      }
    }
    
    // Return endpoints
    return apiResponse.success(res, 'API endpoints retrieved successfully', endpoints);
  } catch (error) {
    logger.error('Error retrieving API endpoints', { error: error.message });
    return next(error);
  }
});

/**
 * @route   GET /api/v1/docs/generate
 * @desc    Generate and save OpenAPI specification
 * @access  Private/Admin
 */
router.get('/generate', (req, res, next) => {
  try {
    // Generate OpenAPI specification
    const spec = generateOpenApiSpec();
    
    // Save specification to file
    const outputPath = path.join(__dirname, '../../public/api-docs/openapi.json');
    saveOpenApiSpec(spec, outputPath);
    
    // Return success response
    return apiResponse.success(res, 'OpenAPI specification generated and saved successfully', {
      path: outputPath
    });
  } catch (error) {
    logger.error('Error generating and saving OpenAPI specification', { error: error.message });
    return next(error);
  }
});

module.exports = router;
