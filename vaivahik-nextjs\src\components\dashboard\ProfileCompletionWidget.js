import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  LinearProgress,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Avatar,
  styled
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  RadioButtonUnchecked as UncheckedIcon,
  Edit as EditIcon,
  CameraAlt as PhotoIcon,
  Work as WorkIcon,
  Home as HomeIcon,
  Favorite as PreferenceIcon,
  Psychology as PersonalityIcon,
  Star as StarIcon
} from '@mui/icons-material';

const CompletionCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: 24,
  boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
    background: 'linear-gradient(90deg, #FF5F6D, #FFC371)',
    borderRadius: '24px 24px 0 0'
  }
}));

const SectionCard = styled(Card)(({ theme, completed }) => ({
  border: completed ? '2px solid #4CAF50' : '2px solid rgba(255, 95, 109, 0.2)',
  borderRadius: 16,
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 12px 24px rgba(0, 0, 0, 0.1)'
  }
}));

const ProfileCompletionWidget = ({ userId, userData = {} }) => {
  const [profileData, setProfileData] = useState({});
  const [completionPercentage, setCompletionPercentage] = useState(0);

  useEffect(() => {
    calculateCompletion();
  }, [userData]);

  const calculateCompletion = () => {
    // Define profile sections and their completion status
    const sections = {
      basic: {
        name: 'Basic Information',
        icon: <EditIcon />,
        fields: ['fullName', 'gender', 'dateOfBirth', 'height', 'maritalStatus'],
        completed: 0,
        total: 5
      },
      photos: {
        name: 'Profile Photos',
        icon: <PhotoIcon />,
        fields: ['profilePhoto'],
        completed: 0,
        total: 1
      },
      education: {
        name: 'Education & Career',
        icon: <WorkIcon />,
        fields: ['education', 'occupation', 'income', 'workingWith'],
        completed: 0,
        total: 4
      },
      location: {
        name: 'Location & Family',
        icon: <HomeIcon />,
        fields: ['city', 'state', 'familyType', 'familyValues'],
        completed: 0,
        total: 4
      },
      preferences: {
        name: 'Partner Preferences',
        icon: <PreferenceIcon />,
        fields: ['agePreference', 'heightPreference', 'educationPreference'],
        completed: 0,
        total: 3
      },
      personality: {
        name: 'Personality & Lifestyle',
        icon: <PersonalityIcon />,
        fields: ['diet', 'smoking', 'drinking', 'hobbies'],
        completed: 0,
        total: 4
      }
    };

    // Calculate completion for each section
    Object.keys(sections).forEach(sectionKey => {
      const section = sections[sectionKey];
      section.completed = section.fields.filter(field => userData[field]).length;
      section.percentage = Math.round((section.completed / section.total) * 100);
    });

    // Calculate overall completion
    const totalFields = Object.values(sections).reduce((sum, section) => sum + section.total, 0);
    const completedFields = Object.values(sections).reduce((sum, section) => sum + section.completed, 0);
    const overallPercentage = Math.round((completedFields / totalFields) * 100);

    setProfileData(sections);
    setCompletionPercentage(overallPercentage);
  };

  const handleSectionClick = (sectionKey) => {
    // Navigate to specific section for completion
    console.log('Edit section:', sectionKey);
    // You can implement navigation to specific forms here
  };

  const getCompletionColor = (percentage) => {
    if (percentage >= 80) return '#4CAF50';
    if (percentage >= 50) return '#FF9800';
    return '#FF5F6D';
  };

  const getCompletionMessage = (percentage) => {
    if (percentage >= 90) return '🎉 Excellent! Your profile is almost complete';
    if (percentage >= 70) return '👍 Great progress! A few more details to go';
    if (percentage >= 50) return '📈 Good start! Complete more sections to get better matches';
    return '🚀 Let\'s complete your profile to find perfect matches';
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        mb: 3,
        p: 3,
        background: 'linear-gradient(135deg, rgba(255, 95, 109, 0.1) 0%, rgba(255, 195, 113, 0.1) 100%)',
        borderRadius: 3
      }}>
        <StarIcon sx={{ fontSize: 32, color: '#FF5F6D', mr: 2 }} />
        <Box>
          <Typography variant="h5" fontWeight="700" color="#FF5F6D">
            Complete Your Profile
          </Typography>
          <Typography variant="body1" color="text.secondary">
            A complete profile gets 10x more matches and better recommendations
          </Typography>
        </Box>
      </Box>

      <CompletionCard>
        <CardContent sx={{ p: 4 }}>
          {/* Overall Progress */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" fontWeight="600">
                Profile Completion
              </Typography>
              <Chip
                label={`${completionPercentage}% Complete`}
                sx={{
                  backgroundColor: getCompletionColor(completionPercentage),
                  color: 'white',
                  fontWeight: 600
                }}
              />
            </Box>
            
            <LinearProgress
              variant="determinate"
              value={completionPercentage}
              sx={{
                height: 12,
                borderRadius: 6,
                backgroundColor: 'rgba(255, 95, 109, 0.1)',
                '& .MuiLinearProgress-bar': {
                  background: `linear-gradient(90deg, ${getCompletionColor(completionPercentage)}, #FFC371)`,
                  borderRadius: 6
                }
              }}
            />
            
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              {getCompletionMessage(completionPercentage)}
            </Typography>
          </Box>

          {/* Section Progress */}
          <Grid container spacing={3}>
            {Object.entries(profileData).map(([key, section]) => (
              <Grid item xs={12} md={6} key={key}>
                <SectionCard 
                  completed={section.percentage === 100}
                  onClick={() => handleSectionClick(key)}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Box sx={{
                        width: 40,
                        height: 40,
                        borderRadius: '50%',
                        background: section.percentage === 100 ? '#4CAF50' : 'rgba(255, 95, 109, 0.1)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mr: 2
                      }}>
                        {React.cloneElement(section.icon, {
                          sx: { 
                            color: section.percentage === 100 ? 'white' : '#FF5F6D',
                            fontSize: 20
                          }
                        })}
                      </Box>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="subtitle1" fontWeight="600">
                          {section.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {section.completed}/{section.total} completed
                        </Typography>
                      </Box>
                      <Chip
                        label={`${section.percentage}%`}
                        size="small"
                        sx={{
                          backgroundColor: section.percentage === 100 ? '#4CAF50' : 'rgba(255, 95, 109, 0.1)',
                          color: section.percentage === 100 ? 'white' : '#FF5F6D',
                          fontWeight: 600
                        }}
                      />
                    </Box>
                    
                    <LinearProgress
                      variant="determinate"
                      value={section.percentage}
                      sx={{
                        height: 6,
                        borderRadius: 3,
                        backgroundColor: 'rgba(255, 95, 109, 0.1)',
                        '& .MuiLinearProgress-bar': {
                          background: section.percentage === 100 ? '#4CAF50' : 'linear-gradient(90deg, #FF5F6D, #FFC371)',
                          borderRadius: 3
                        }
                      }}
                    />
                  </CardContent>
                </SectionCard>
              </Grid>
            ))}
          </Grid>

          {/* Quick Actions */}
          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <Typography variant="h6" fontWeight="600" gutterBottom>
              Quick Actions
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                startIcon={<PhotoIcon />}
                onClick={() => handleSectionClick('photos')}
                sx={{
                  background: 'linear-gradient(135deg, #FF5F6D, #FFC371)',
                  borderRadius: 3,
                  px: 3
                }}
              >
                Add Photos
              </Button>
              <Button
                variant="outlined"
                startIcon={<WorkIcon />}
                onClick={() => handleSectionClick('education')}
                sx={{
                  borderColor: '#FF5F6D',
                  color: '#FF5F6D',
                  borderRadius: 3,
                  px: 3
                }}
              >
                Update Career
              </Button>
              <Button
                variant="outlined"
                startIcon={<PreferenceIcon />}
                onClick={() => handleSectionClick('preferences')}
                sx={{
                  borderColor: '#FF5F6D',
                  color: '#FF5F6D',
                  borderRadius: 3,
                  px: 3
                }}
              >
                Set Preferences
              </Button>
            </Box>
          </Box>
        </CardContent>
      </CompletionCard>
    </Box>
  );
};

export default ProfileCompletionWidget;
