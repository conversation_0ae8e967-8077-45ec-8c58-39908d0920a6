"""
Two-Tower Model for Matrimony Matching (PyTorch Implementation)

This module implements a two-tower neural network model for matrimony matching using PyTorch.
The model consists of two separate neural networks (towers):
1. User Tower: Processes user profile and preference data
2. Match Tower: Processes potential match profile data

The outputs of both towers are compared to calculate a match score.
"""

import os
import json
import random
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader

class TwoTowerDataset(Dataset):
    """Dataset for the two-tower model"""

    def __init__(self, user_data, match_data, labels=None):
        """
        Initialize the dataset

        Args:
            user_data (np.ndarray): User profile and preference data
            match_data (np.ndarray): Match profile data
            labels (np.ndarray): Match labels (1 for match, 0 for non-match)
        """
        self.user_data = torch.FloatTensor(user_data)
        self.match_data = torch.FloatTensor(match_data)
        self.labels = torch.FloatTensor(labels) if labels is not None else None

    def __len__(self):
        return len(self.user_data)

    def __getitem__(self, idx):
        if self.labels is not None:
            return self.user_data[idx], self.match_data[idx], self.labels[idx]
        else:
            return self.user_data[idx], self.match_data[idx]

class TowerNetwork(nn.Module):
    """Neural network for a single tower"""

    def __init__(self, input_dim, hidden_layers, embedding_size, dropout_rate):
        """
        Initialize the tower network

        Args:
            input_dim (int): Input dimension
            hidden_layers (list): List of hidden layer sizes
            embedding_size (int): Size of the embedding vector
            dropout_rate (float): Dropout rate
        """
        super(TowerNetwork, self).__init__()

        # Create layers
        layers = []
        prev_size = input_dim

        # Add hidden layers
        for size in hidden_layers:
            layers.append(nn.Linear(prev_size, size))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout_rate))
            prev_size = size

        # Add embedding layer
        layers.append(nn.Linear(prev_size, embedding_size))
        layers.append(nn.Tanh())

        # Create sequential model
        self.model = nn.Sequential(*layers)

    def forward(self, x):
        """Forward pass"""
        return self.model(x)

class TwoTowerModel(nn.Module):
    """Two-Tower Model for matrimony matching"""

    def __init__(self, user_input_dim, match_input_dim, config=None):
        """
        Initialize the Two-Tower Model

        Args:
            user_input_dim (int): Dimension of user input
            match_input_dim (int): Dimension of match input
            config (dict): Configuration parameters for the model
        """
        super(TwoTowerModel, self).__init__()

        # Default configuration
        self.default_config = {
            'user_tower_layers': [128, 64],
            'match_tower_layers': [128, 64],
            'embedding_size': 128,
            'dropout_rate': 0.2,
            'learning_rate': 0.001,
            'similarity_metric': 'cosine',
            'batch_size': 64,
            'epochs': 10
        }

        # Use provided config or default
        self.config = config if config else self.default_config

        # Create user tower
        self.user_tower = TowerNetwork(
            user_input_dim,
            self.config['user_tower_layers'],
            self.config['embedding_size'],
            self.config['dropout_rate']
        )

        # Create match tower
        self.match_tower = TowerNetwork(
            match_input_dim,
            self.config['match_tower_layers'],
            self.config['embedding_size'],
            self.config['dropout_rate']
        )

    def forward(self, user_input, match_input):
        """
        Forward pass

        Args:
            user_input (torch.Tensor): User profile and preference data
            match_input (torch.Tensor): Match profile data

        Returns:
            torch.Tensor: Match score
        """
        # Get embeddings
        user_embedding = self.user_tower(user_input)
        match_embedding = self.match_tower(match_input)

        # Calculate similarity
        if self.config['similarity_metric'] == 'cosine':
            # Cosine similarity
            similarity = F.cosine_similarity(user_embedding, match_embedding, dim=1)
            # Rescale from [-1, 1] to [0, 1]
            similarity = (similarity + 1) / 2
        elif self.config['similarity_metric'] == 'dot':
            # Dot product
            similarity = torch.sum(user_embedding * match_embedding, dim=1)
            # Apply sigmoid to get [0, 1]
            similarity = torch.sigmoid(similarity)
        else:
            # Euclidean distance (converted to similarity)
            distance = torch.sqrt(torch.sum((user_embedding - match_embedding) ** 2, dim=1))
            # Convert distance to similarity (1 when distance is 0, 0 when distance is large)
            similarity = torch.exp(-distance)

        # Reshape to match expected output shape
        return similarity.view(-1, 1)

class MatrimonyMatchingModel:
    """Wrapper class for the two-tower model"""

    def __init__(self, config=None):
        """
        Initialize the model

        Args:
            config (dict): Configuration parameters for the model
        """
        # Default configuration
        self.default_config = {
            'user_tower_layers': [128, 64],
            'match_tower_layers': [128, 64],
            'embedding_size': 128,
            'dropout_rate': 0.2,
            'learning_rate': 0.001,
            'similarity_metric': 'cosine',
            'batch_size': 64,
            'epochs': 10
        }

        # Use provided config or default
        self.config = config if config else self.default_config

        # Initialize model
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # Feature columns
        self.user_features = [
            'age', 'gender', 'height', 'religion', 'caste', 'subCaste', 'gotra',
            'education', 'occupation', 'income', 'city', 'state', 'maritalStatus',
            # Lifestyle features
            'diet', 'smoking', 'drinking',
            # Hobbies features
            'hobbies',
            # Family background features
            'familyType', 'familyValues', 'familyStatus', 'familyIncome'
        ]

        self.preference_features = [
            'ageMin', 'ageMax', 'heightMin', 'heightMax', 'religion', 'caste',
            'subCaste', 'gotra', 'education', 'occupation', 'incomeMin',
            'location', 'maritalStatus',
            # Lifestyle preferences
            'dietPreference', 'smokingPreference', 'drinkingPreference',
            # Family background preferences
            'familyTypePreference', 'familyValuesPreference'
        ]

        self.match_features = [
            'age', 'gender', 'height', 'religion', 'caste', 'subCaste', 'gotra',
            'education', 'occupation', 'income', 'city', 'state', 'maritalStatus',
            # Lifestyle features
            'diet', 'smoking', 'drinking',
            # Hobbies features
            'hobbies',
            # Family background features
            'familyType', 'familyValues', 'familyStatus', 'familyIncome'
        ]

    def build_model(self):
        """Build the two-tower model architecture"""
        # Calculate input dimensions
        user_input_dim = len(self.user_features) + len(self.preference_features)
        match_input_dim = len(self.match_features)

        # Create model
        self.model = TwoTowerModel(
            user_input_dim,
            match_input_dim,
            self.config
        ).to(self.device)

        # Create optimizer
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=self.config['learning_rate']
        )

        # Create loss function
        self.criterion = nn.BCELoss()

        return self.model

    def preprocess_data(self, users, preferences, matches, labels=None):
        """
        Preprocess data for training or inference

        Args:
            users (list): List of user profiles
            preferences (list): List of user preferences
            matches (list): List of potential matches
            labels (list): Match labels (1 for match, 0 for non-match)

        Returns:
            tuple: Processed inputs and outputs for the model
        """
        # Initialize feature arrays
        user_data = np.zeros((len(users), len(self.user_features) + len(self.preference_features)))
        match_data = np.zeros((len(matches), len(self.match_features)))

        # Process each user profile and preferences
        for i, (user, preference) in enumerate(zip(users, preferences)):
            # Extract user features
            user_features = self._extract_user_features(user)

            # Extract preference features
            preference_features = self._extract_preference_features(preference)

            # Combine user and preference features
            combined_features = np.concatenate([user_features, preference_features])

            # Store in user_data array
            user_data[i] = combined_features

        # Process each potential match
        for i, match in enumerate(matches):
            # Extract match features
            match_features = self._extract_match_features(match)

            # Store in match_data array
            match_data[i] = match_features

        if labels is not None:
            return user_data, match_data, np.array(labels)
        else:
            return user_data, match_data

    def _extract_user_features(self, user):
        """
        Extract features from a user profile

        Args:
            user (dict): User profile

        Returns:
            np.array: Extracted features
        """
        # Initialize feature array
        features = np.zeros(len(self.user_features))

        # Process age (normalize to [0, 1] range assuming age between 18-80)
        if 'age' in user:
            age = user.get('age', 30)  # Default to 30 if missing
            features[self.user_features.index('age')] = self._normalize_age(age)

        # Process gender (one-hot encoding: 0 for MALE, 1 for FEMALE)
        if 'gender' in user:
            gender = user.get('gender', 'MALE')  # Default to MALE if missing
            features[self.user_features.index('gender')] = 1.0 if gender == 'FEMALE' else 0.0

        # Process height (normalize to [0, 1] range assuming height between 140-200 cm)
        if 'height' in user:
            height = user.get('height', 170)  # Default to 170 if missing
            features[self.user_features.index('height')] = self._normalize_height(height)

        # Process religion (one-hot encoding based on predefined categories)
        if 'religion' in user:
            religion = user.get('religion', 'Hindu')  # Default to Hindu if missing
            religion_index = self._get_religion_index(religion)
            features[self.user_features.index('religion')] = religion_index

        # Process caste (one-hot encoding based on predefined categories)
        if 'caste' in user:
            caste = user.get('caste', 'Maratha')  # Default to Maratha if missing
            caste_index = self._get_caste_index(caste)
            features[self.user_features.index('caste')] = caste_index

        # Process subCaste (one-hot encoding based on predefined categories)
        if 'subCaste' in user:
            sub_caste = user.get('subCaste', '')  # Default to empty if missing
            sub_caste_index = self._get_sub_caste_index(sub_caste)
            features[self.user_features.index('subCaste')] = sub_caste_index

        # Process gotra (one-hot encoding based on predefined categories)
        if 'gotra' in user:
            gotra = user.get('gotra', '')  # Default to empty if missing
            gotra_index = self._get_gotra_index(gotra)
            features[self.user_features.index('gotra')] = gotra_index

        # Process education (one-hot encoding based on predefined categories)
        if 'education' in user:
            education = user.get('education', 'Bachelors')  # Default to Bachelors if missing
            education_index = self._get_education_index(education)
            features[self.user_features.index('education')] = education_index

        # Process occupation (one-hot encoding based on predefined categories)
        if 'occupation' in user:
            occupation = user.get('occupation', '')  # Default to empty if missing
            occupation_index = self._get_occupation_index(occupation)
            features[self.user_features.index('occupation')] = occupation_index

        # Process income (normalize to [0, 1] range assuming income between 0-5000000)
        if 'income' in user:
            income = user.get('income', 500000)  # Default to 500000 if missing
            features[self.user_features.index('income')] = self._normalize_income(income)

        # Process city (one-hot encoding based on predefined categories)
        if 'city' in user:
            city = user.get('city', '')  # Default to empty if missing
            city_index = self._get_city_index(city)
            features[self.user_features.index('city')] = city_index

        # Process state (one-hot encoding based on predefined categories)
        if 'state' in user:
            state = user.get('state', 'Maharashtra')  # Default to Maharashtra if missing
            state_index = self._get_state_index(state)
            features[self.user_features.index('state')] = state_index

        # Process maritalStatus (one-hot encoding based on predefined categories)
        if 'maritalStatus' in user:
            marital_status = user.get('maritalStatus', 'NEVER_MARRIED')  # Default to NEVER_MARRIED if missing
            marital_status_index = self._get_marital_status_index(marital_status)
            features[self.user_features.index('maritalStatus')] = marital_status_index

        # Process lifestyle features
        # Diet preference
        if 'lifestyle' in user and 'diet' in user['lifestyle']:
            diet = user['lifestyle'].get('diet', 'Vegetarian')  # Default to Vegetarian if missing
            diet_index = self._get_diet_index(diet)
            features[self.user_features.index('diet')] = diet_index
        elif 'diet' in user:
            diet = user.get('diet', 'Vegetarian')  # Default to Vegetarian if missing
            diet_index = self._get_diet_index(diet)
            features[self.user_features.index('diet')] = diet_index

        # Smoking preference
        if 'lifestyle' in user and 'smoking' in user['lifestyle']:
            smoking = user['lifestyle'].get('smoking', 'Never')  # Default to Never if missing
            smoking_index = self._get_smoking_index(smoking)
            features[self.user_features.index('smoking')] = smoking_index
        elif 'smoking' in user:
            smoking = user.get('smoking', 'Never')  # Default to Never if missing
            smoking_index = self._get_smoking_index(smoking)
            features[self.user_features.index('smoking')] = smoking_index

        # Drinking preference
        if 'lifestyle' in user and 'drinking' in user['lifestyle']:
            drinking = user['lifestyle'].get('drinking', 'Never')  # Default to Never if missing
            drinking_index = self._get_drinking_index(drinking)
            features[self.user_features.index('drinking')] = drinking_index
        elif 'drinking' in user:
            drinking = user.get('drinking', 'Never')  # Default to Never if missing
            drinking_index = self._get_drinking_index(drinking)
            features[self.user_features.index('drinking')] = drinking_index

        # Process hobbies
        if 'hobbies' in user:
            hobbies = user.get('hobbies', [])  # Default to empty list if missing
            hobbies_index = self._get_hobbies_index(hobbies)
            features[self.user_features.index('hobbies')] = hobbies_index

        # Process family background features
        # Family type
        if 'family' in user and 'type' in user['family']:
            family_type = user['family'].get('type', 'Nuclear')  # Default to Nuclear if missing
            family_type_index = self._get_family_type_index(family_type)
            features[self.user_features.index('familyType')] = family_type_index
        elif 'familyType' in user:
            family_type = user.get('familyType', 'Nuclear')  # Default to Nuclear if missing
            family_type_index = self._get_family_type_index(family_type)
            features[self.user_features.index('familyType')] = family_type_index

        # Family values
        if 'family' in user and 'values' in user['family']:
            family_values = user['family'].get('values', 'Traditional')  # Default to Traditional if missing
            family_values_index = self._get_family_values_index(family_values)
            features[self.user_features.index('familyValues')] = family_values_index
        elif 'familyValues' in user:
            family_values = user.get('familyValues', 'Traditional')  # Default to Traditional if missing
            family_values_index = self._get_family_values_index(family_values)
            features[self.user_features.index('familyValues')] = family_values_index

        # Family status
        if 'family' in user and 'status' in user['family']:
            family_status = user['family'].get('status', 'Middle Class')  # Default to Middle Class if missing
            family_status_index = self._get_family_status_index(family_status)
            features[self.user_features.index('familyStatus')] = family_status_index
        elif 'familyStatus' in user:
            family_status = user.get('familyStatus', 'Middle Class')  # Default to Middle Class if missing
            family_status_index = self._get_family_status_index(family_status)
            features[self.user_features.index('familyStatus')] = family_status_index

        # Family income
        if 'family' in user and 'income' in user['family']:
            family_income = user['family'].get('income', 1000000)  # Default to 1000000 if missing
            features[self.user_features.index('familyIncome')] = self._normalize_income(family_income)
        elif 'familyIncome' in user:
            family_income = user.get('familyIncome', 1000000)  # Default to 1000000 if missing
            features[self.user_features.index('familyIncome')] = self._normalize_income(family_income)

        return features

    def _extract_preference_features(self, preference):
        """
        Extract features from user preferences

        Args:
            preference (dict): User preferences

        Returns:
            np.array: Extracted features
        """
        # Initialize feature array
        features = np.zeros(len(self.preference_features))

        # Process ageMin (normalize to [0, 1] range assuming age between 18-80)
        if 'ageMin' in preference:
            age_min = preference.get('ageMin', 25)  # Default to 25 if missing
            features[self.preference_features.index('ageMin')] = self._normalize_age(age_min)

        # Process ageMax (normalize to [0, 1] range assuming age between 18-80)
        if 'ageMax' in preference:
            age_max = preference.get('ageMax', 35)  # Default to 35 if missing
            features[self.preference_features.index('ageMax')] = self._normalize_age(age_max)

        # Process heightMin (normalize to [0, 1] range assuming height between 140-200 cm)
        if 'heightMin' in preference:
            height_min = preference.get('heightMin', 150)  # Default to 150 if missing
            features[self.preference_features.index('heightMin')] = self._normalize_height(height_min)

        # Process heightMax (normalize to [0, 1] range assuming height between 140-200 cm)
        if 'heightMax' in preference:
            height_max = preference.get('heightMax', 180)  # Default to 180 if missing
            features[self.preference_features.index('heightMax')] = self._normalize_height(height_max)

        # Process religion (one-hot encoding based on predefined categories)
        if 'religion' in preference:
            religion = preference.get('religion', 'Hindu')  # Default to Hindu if missing
            religion_index = self._get_religion_index(religion)
            features[self.preference_features.index('religion')] = religion_index

        # Process caste (one-hot encoding based on predefined categories)
        if 'caste' in preference:
            caste = preference.get('caste', 'Maratha')  # Default to Maratha if missing
            caste_index = self._get_caste_index(caste)
            features[self.preference_features.index('caste')] = caste_index

        # Process subCaste (one-hot encoding based on predefined categories)
        if 'subCaste' in preference:
            sub_caste = preference.get('subCaste', '')  # Default to empty if missing
            sub_caste_index = self._get_sub_caste_index(sub_caste)
            features[self.preference_features.index('subCaste')] = sub_caste_index

        # Process gotra (one-hot encoding based on predefined categories)
        if 'gotra' in preference:
            gotra = preference.get('gotra', '')  # Default to empty if missing
            gotra_index = self._get_gotra_index(gotra)
            features[self.preference_features.index('gotra')] = gotra_index

        # Process education (one-hot encoding based on predefined categories)
        if 'education' in preference:
            education = preference.get('education', 'Bachelors')  # Default to Bachelors if missing
            education_index = self._get_education_index(education)
            features[self.preference_features.index('education')] = education_index

        # Process occupation (one-hot encoding based on predefined categories)
        if 'occupation' in preference:
            occupation = preference.get('occupation', '')  # Default to empty if missing
            occupation_index = self._get_occupation_index(occupation)
            features[self.preference_features.index('occupation')] = occupation_index

        # Process incomeMin (normalize to [0, 1] range assuming income between 0-5000000)
        if 'incomeMin' in preference:
            income_min = preference.get('incomeMin', 300000)  # Default to 300000 if missing
            features[self.preference_features.index('incomeMin')] = self._normalize_income(income_min)

        # Process location (one-hot encoding based on predefined categories)
        if 'location' in preference:
            location = preference.get('location', 'Maharashtra')  # Default to Maharashtra if missing
            location_index = self._get_state_index(location)
            features[self.preference_features.index('location')] = location_index

        # Process maritalStatus (one-hot encoding based on predefined categories)
        if 'maritalStatus' in preference:
            marital_status = preference.get('maritalStatus', 'NEVER_MARRIED')  # Default to NEVER_MARRIED if missing
            marital_status_index = self._get_marital_status_index(marital_status)
            features[self.preference_features.index('maritalStatus')] = marital_status_index

        return features

    def _extract_match_features(self, match):
        """
        Extract features from a potential match profile

        Args:
            match (dict): Potential match profile

        Returns:
            np.array: Extracted features
        """
        # This is essentially the same as _extract_user_features
        return self._extract_user_features(match)

    def train(self, users, preferences, matches, labels, validation_split=0.2):
        """
        Train the model

        Args:
            users (list): List of user profiles
            preferences (list): List of user preferences
            matches (list): List of potential matches
            labels (list): Match labels (1 for match, 0 for non-match)
            validation_split (float): Fraction of data to use for validation

        Returns:
            dict: Training history
        """
        # Build model if not already built
        if self.model is None:
            self.build_model()

        # Preprocess data
        user_data, match_data, label_data = self.preprocess_data(users, preferences, matches, labels)

        # Split data into train and validation sets
        n_samples = len(user_data)
        n_val = int(n_samples * validation_split)
        n_train = n_samples - n_val

        indices = np.random.permutation(n_samples)
        train_idx, val_idx = indices[:n_train], indices[n_train:]

        train_user_data = user_data[train_idx]
        train_match_data = match_data[train_idx]
        train_labels = label_data[train_idx]

        val_user_data = user_data[val_idx]
        val_match_data = match_data[val_idx]
        val_labels = label_data[val_idx]

        # Create datasets and dataloaders
        train_dataset = TwoTowerDataset(train_user_data, train_match_data, train_labels)
        val_dataset = TwoTowerDataset(val_user_data, val_match_data, val_labels)

        train_loader = DataLoader(
            train_dataset,
            batch_size=self.config['batch_size'],
            shuffle=True
        )

        val_loader = DataLoader(
            val_dataset,
            batch_size=self.config['batch_size'],
            shuffle=False
        )

        # Training history
        history = {
            'train_loss': [],
            'val_loss': [],
            'train_acc': [],
            'val_acc': []
        }

        # Train model
        best_val_loss = float('inf')
        for epoch in range(self.config['epochs']):
            # Training
            self.model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0

            for user_batch, match_batch, label_batch in train_loader:
                # Move data to device
                user_batch = user_batch.to(self.device)
                match_batch = match_batch.to(self.device)
                label_batch = label_batch.to(self.device)

                # Zero gradients
                self.optimizer.zero_grad()

                # Forward pass
                outputs = self.model(user_batch, match_batch)

                # Calculate loss
                loss = self.criterion(outputs, label_batch.view(-1, 1))

                # Backward pass and optimize
                loss.backward()
                self.optimizer.step()

                # Update statistics
                train_loss += loss.item() * user_batch.size(0)
                train_preds = (outputs > 0.5).float()
                train_correct += (train_preds == label_batch.view(-1, 1)).sum().item()
                train_total += user_batch.size(0)

            # Validation
            self.model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0

            with torch.no_grad():
                for user_batch, match_batch, label_batch in val_loader:
                    # Move data to device
                    user_batch = user_batch.to(self.device)
                    match_batch = match_batch.to(self.device)
                    label_batch = label_batch.to(self.device)

                    # Forward pass
                    outputs = self.model(user_batch, match_batch)

                    # Calculate loss
                    loss = self.criterion(outputs, label_batch.view(-1, 1))

                    # Update statistics
                    val_loss += loss.item() * user_batch.size(0)
                    val_preds = (outputs > 0.5).float()
                    val_correct += (val_preds == label_batch.view(-1, 1)).sum().item()
                    val_total += user_batch.size(0)

            # Calculate epoch statistics
            train_loss = train_loss / train_total
            train_acc = train_correct / train_total
            val_loss = val_loss / val_total
            val_acc = val_correct / val_total

            # Update history
            history['train_loss'].append(train_loss)
            history['train_acc'].append(train_acc)
            history['val_loss'].append(val_loss)
            history['val_acc'].append(val_acc)

            # Save best model
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                torch.save(self.model.state_dict(), 'models/best_model.pt')

            print(f'Epoch {epoch+1}/{self.config["epochs"]} - '
                  f'train_loss: {train_loss:.4f} - train_acc: {train_acc:.4f} - '
                  f'val_loss: {val_loss:.4f} - val_acc: {val_acc:.4f}')

        # Load best model
        self.model.load_state_dict(torch.load('models/best_model.pt'))

        return history

    # Helper methods for feature normalization and encoding

    def _normalize_age(self, age):
        """Normalize age to [0, 1] range"""
        min_age = 18
        max_age = 80
        return max(0, min(1, (age - min_age) / (max_age - min_age)))

    def _normalize_height(self, height):
        """Normalize height to [0, 1] range"""
        min_height = 140
        max_height = 200
        return max(0, min(1, (height - min_height) / (max_height - min_height)))

    def _normalize_income(self, income):
        """Normalize income to [0, 1] range"""
        min_income = 0
        max_income = 5000000
        return max(0, min(1, (income - min_income) / (max_income - min_income)))

    def _get_religion_index(self, religion):
        """Get index for religion"""
        religions = ['Hindu', 'Muslim', 'Christian', 'Sikh', 'Buddhist', 'Jain', 'Other']
        try:
            return religions.index(religion) / len(religions)
        except ValueError:
            return 0.0  # Default to first religion

    def _get_caste_index(self, caste):
        """Get index for caste"""
        castes = ['Maratha', 'Brahmin', 'Kshatriya', 'Vaishya', 'Shudra', 'Other']
        try:
            return castes.index(caste) / len(castes)
        except ValueError:
            return 0.0  # Default to first caste

    def _get_sub_caste_index(self, sub_caste):
        """Get index for sub-caste"""
        sub_castes = ['Deshmukh', 'Jadhav', 'Patil', 'Pawar', 'Shinde', 'Other', '']
        try:
            return sub_castes.index(sub_caste) / len(sub_castes)
        except ValueError:
            return 6.0 / len(sub_castes)  # Default to empty

    def _get_gotra_index(self, gotra):
        """Get index for gotra"""
        gotras = ['Kashyap', 'Bharadwaj', 'Gautam', 'Vasishtha', 'Jamadagni', 'Other', '']
        try:
            return gotras.index(gotra) / len(gotras)
        except ValueError:
            return 6.0 / len(gotras)  # Default to empty

    def _get_education_index(self, education):
        """Get index for education"""
        educations = ['High School', 'Diploma', 'Bachelors', 'Masters', 'PhD', 'Other']
        try:
            return educations.index(education) / len(educations)
        except ValueError:
            return 2.0 / len(educations)  # Default to Bachelors

    def _get_occupation_index(self, occupation):
        """Get index for occupation"""
        occupations = ['Software Engineer', 'Doctor', 'Engineer', 'Teacher', 'Business', 'Government', 'Other', '']
        try:
            return occupations.index(occupation) / len(occupations)
        except ValueError:
            return 7.0 / len(occupations)  # Default to empty

    def _get_city_index(self, city):
        """Get index for city"""
        cities = ['Mumbai', 'Pune', 'Nagpur', 'Nashik', 'Aurangabad', 'Other', '']
        try:
            return cities.index(city) / len(cities)
        except ValueError:
            return 6.0 / len(cities)  # Default to empty

    def _get_state_index(self, state):
        """Get index for state"""
        states = ['Maharashtra', 'Gujarat', 'Karnataka', 'Delhi', 'Other', '']
        try:
            return states.index(state) / len(states)
        except ValueError:
            return 5.0 / len(states)  # Default to empty

    def _get_marital_status_index(self, marital_status):
        """Get index for marital status"""
        statuses = ['NEVER_MARRIED', 'DIVORCED', 'WIDOWED', 'SEPARATED', 'OTHER']
        try:
            return statuses.index(marital_status) / len(statuses)
        except ValueError:
            return 0.0  # Default to NEVER_MARRIED

    def _get_diet_index(self, diet):
        """Get index for diet preference"""
        diets = ['Vegetarian', 'Non-Vegetarian', 'Eggetarian', 'Vegan', 'Jain', 'Other']
        try:
            return diets.index(diet) / len(diets)
        except ValueError:
            return 0.0  # Default to Vegetarian

    def _get_smoking_index(self, smoking):
        """Get index for smoking preference"""
        smoking_prefs = ['Never', 'Occasionally', 'Regularly', 'Other']
        try:
            return smoking_prefs.index(smoking) / len(smoking_prefs)
        except ValueError:
            return 0.0  # Default to Never

    def _get_drinking_index(self, drinking):
        """Get index for drinking preference"""
        drinking_prefs = ['Never', 'Occasionally', 'Regularly', 'Other']
        try:
            return drinking_prefs.index(drinking) / len(drinking_prefs)
        except ValueError:
            return 0.0  # Default to Never

    def _get_hobbies_index(self, hobbies):
        """Get index for hobbies"""
        # For simplicity, we'll just count the number of hobbies
        # In a real implementation, we would use a more sophisticated approach
        if isinstance(hobbies, list):
            return min(1.0, len(hobbies) / 10.0)  # Normalize to [0, 1]
        return 0.0  # Default to no hobbies

    def _get_family_type_index(self, family_type):
        """Get index for family type"""
        family_types = ['Nuclear', 'Joint', 'Extended', 'Other']
        try:
            return family_types.index(family_type) / len(family_types)
        except ValueError:
            return 0.0  # Default to Nuclear

    def _get_family_values_index(self, family_values):
        """Get index for family values"""
        values = ['Traditional', 'Moderate', 'Liberal', 'Other']
        try:
            return values.index(family_values) / len(values)
        except ValueError:
            return 0.0  # Default to Traditional

    def _get_family_status_index(self, family_status):
        """Get index for family status"""
        statuses = ['Upper Class', 'Upper Middle Class', 'Middle Class', 'Lower Middle Class', 'Other']
        try:
            return statuses.index(family_status) / len(statuses)
        except ValueError:
            return 2.0 / len(statuses)  # Default to Middle Class

    def predict(self, users, preferences, matches):
        """
        Predict match scores

        Args:
            users (list): List of user profiles
            preferences (list): List of user preferences
            matches (list): List of potential matches

        Returns:
            list: Match scores
        """
        # Build model if not already built
        if self.model is None:
            self.build_model()

        print(f"predict() - Number of users: {len(users)}")
        print(f"predict() - Number of preferences: {len(preferences)}")
        print(f"predict() - Number of matches: {len(matches)}")

        # Extract features from user profiles and preferences
        user_features = []
        for i, (user, preference) in enumerate(zip(users, preferences)):
            # Extract user features
            user_feat = self._extract_user_features(user)

            # Extract preference features
            pref_feat = self._extract_preference_features(preference)

            # Combine user and preference features
            combined_features = np.concatenate([user_feat, pref_feat])
            user_features.append(combined_features)

        # Extract features from potential matches
        match_features = []
        for match in matches:
            match_feat = self._extract_match_features(match)
            match_features.append(match_feat)

        # Calculate scores based on feature similarity
        scores = []
        for match_feat in match_features:
            # For now, use a simple scoring function based on feature similarity
            # In a real implementation, this would use the trained model
            score = self._calculate_similarity(user_features[0], match_feat)
            scores.append(score)

        print(f"predict() - Generated scores: {scores}")

        return scores

    def _get_preference_adjustments(self):
        """
        Get preference adjustments based on user feedback

        Returns:
            dict: Preference adjustments for each feature
        """
        # In a real implementation, this would load from a database
        # For now, return default values
        return {
            'age': 1.0,
            'religion': 1.0,
            'caste': 1.0,
            'education': 1.0,
            'marital': 1.0,
            'location': 1.0,
            'gotra': 1.0,
            'occupation': 1.0,
            'income': 1.0,
            'lifestyle': 1.0,
            'family': 1.0,
            'hobbies': 1.0
        }

    def _calculate_similarity(self, user_features, match_features):
        """
        Calculate similarity between user and match features

        Args:
            user_features (np.array): User features
            match_features (np.array): Match features

        Returns:
            float: Similarity score
        """
        # For now, use a simple scoring function based on feature similarity
        # This is a temporary solution until the full model is implemented

        # Calculate similarity based on key matching criteria
        user_age = user_features[self.user_features.index('age')]
        match_age = match_features[self.match_features.index('age')]
        age_diff = abs(user_age - match_age)
        age_score = max(0, 1 - age_diff)

        # Religion compatibility (flexible matching)
        try:
            user_religion = user_features[self.user_features.index('religion')]
            match_religion = match_features[self.match_features.index('religion')]

            # Define compatible religion groups
            religion_groups = {
                'Hindu': ['Hindu', 'Jain', 'Buddhist', 'Sikh'],
                'Muslim': ['Muslim', 'Sufi'],
                'Christian': ['Christian', 'Catholic', 'Protestant']
            }

            if user_religion == match_religion:
                religion_score = 1.0  # Exact match
            else:
                # Check if religions are compatible
                compatible = False
                for group in religion_groups.values():
                    if user_religion in group and match_religion in group:
                        compatible = True
                        break
                religion_score = 0.8 if compatible else 0.3
        except (ValueError, IndexError):
            religion_score = 0.5  # Default if religion information is missing

        # Caste compatibility (flexible matching)
        try:
            user_caste = user_features[self.user_features.index('caste')]
            match_caste = match_features[self.match_features.index('caste')]

            # Define compatible caste groups
            caste_groups = {
                'Maratha': ['Maratha', 'Kunbi', 'Mali', 'Dhangar'],
                'Brahmin': ['Brahmin', 'Deshastha', 'Chitpavan', 'Karhade']
            }

            if user_caste == match_caste:
                caste_score = 1.0  # Exact match
            else:
                # Check if castes are compatible
                compatible = False
                for group in caste_groups.values():
                    if user_caste in group and match_caste in group:
                        compatible = True
                        break
                caste_score = 0.7 if compatible else 0.2
        except (ValueError, IndexError):
            caste_score = 0.5  # Default if caste information is missing

        # SubCaste match - This is important within the Maratha community
        try:
            user_subcaste = user_features[self.user_features.index('subCaste')]
            match_subcaste = match_features[self.match_features.index('subCaste')]

            # Check if user has preference for same subcaste
            # This would typically come from user preferences
            same_subcaste_preference = True

            if same_subcaste_preference:
                # User prefers same subcaste
                subcaste_score = 1.0 if user_subcaste == match_subcaste else 0.7
            else:
                # User is open to different subcastes
                subcaste_score = 0.9  # Still slightly prefer same subcaste
        except (ValueError, IndexError):
            subcaste_score = 0.8  # Default if subcaste information is missing

        # Education match
        user_education = user_features[self.user_features.index('education')]
        match_education = match_features[self.match_features.index('education')]
        education_score = 1.0 if user_education == match_education else 0.5

        # Marital status match
        user_marital = user_features[self.user_features.index('maritalStatus')]
        match_marital = match_features[self.match_features.index('maritalStatus')]
        marital_score = 1.0 if user_marital == match_marital else 0.0

        # Location match
        user_state = user_features[self.user_features.index('state')]
        match_state = match_features[self.match_features.index('state')]
        location_score = 1.0 if user_state == match_state else 0.5

        # Height match
        user_height = user_features[self.user_features.index('height')]
        match_height = match_features[self.match_features.index('height')]

        # For male users, prefer matches 4-6 inches shorter than them
        # For female users, prefer matches 4-6 inches taller than them
        # 1 inch = 2.54 cm, so 4-6 inches = 10.16-15.24 cm
        if user_features[self.user_features.index('gender')] < 0.5:  # Male user
            height_diff = user_height - match_height
            # Ideal: male 4-6 inches (10.16-15.24 cm) taller than female
            if 10 <= height_diff <= 15:  # Ideal range (in cm)
                height_score = 1.0
            elif 5 <= height_diff < 10:  # Close to ideal
                height_score = 0.9
            elif 15 < height_diff <= 20:  # Slightly more than ideal
                height_score = 0.8
            elif height_diff < 5:  # Male not much taller
                height_score = 0.6
            else:  # Too much height difference
                height_score = 0.5
        else:  # Female user
            height_diff = match_height - user_height
            # Ideal: male 4-6 inches (10.16-15.24 cm) taller than female
            if 10 <= height_diff <= 15:  # Ideal range (in cm)
                height_score = 1.0
            elif 5 <= height_diff < 10:  # Close to ideal
                height_score = 0.9
            elif 15 < height_diff <= 20:  # Slightly more than ideal
                height_score = 0.8
            elif height_diff < 5:  # Male not much taller
                height_score = 0.6
            else:  # Too much height difference
                height_score = 0.5

        # Occupation match
        user_occupation = user_features[self.user_features.index('occupation')]
        match_occupation = match_features[self.match_features.index('occupation')]
        occupation_score = 1.0 if user_occupation == match_occupation else 0.7

        # Income match
        user_income = user_features[self.user_features.index('income')]
        match_income = match_features[self.match_features.index('income')]

        # For male users, income should be slightly more than female candidates
        # For female users, prefer matches with slightly higher income
        if user_features[self.user_features.index('gender')] < 0.5:  # Male user
            # Calculate income ratio (male to female)
            if match_income > 0:  # Avoid division by zero
                income_ratio = user_income / match_income
                # Ideal: male income 1.1-1.5 times female income
                if 1.1 <= income_ratio <= 1.5:
                    income_score = 1.0  # Ideal range
                elif 1.0 <= income_ratio < 1.1:
                    income_score = 0.9  # Almost ideal
                elif 1.5 < income_ratio <= 2.0:
                    income_score = 0.8  # Slightly more than ideal
                elif income_ratio < 1.0:
                    income_score = 0.6  # Male earning less than female
                else:
                    income_score = 0.7  # Too much income difference
            else:
                income_score = 0.7  # Default if match has no income
        else:  # Female user
            # Calculate income ratio (male to female)
            if user_income > 0:  # Avoid division by zero
                income_ratio = match_income / user_income
                # Ideal: male income 1.1-1.5 times female income
                if 1.1 <= income_ratio <= 1.5:
                    income_score = 1.0  # Ideal range
                elif 1.0 <= income_ratio < 1.1:
                    income_score = 0.9  # Almost ideal
                elif 1.5 < income_ratio <= 2.0:
                    income_score = 0.8  # Slightly more than ideal
                elif income_ratio < 1.0:
                    income_score = 0.6  # Male earning less than female
                else:
                    income_score = 0.7  # Too much income difference
            else:
                income_score = 0.6  # Default if match has no income

        # Diet preference match
        try:
            user_diet = user_features[self.user_features.index('diet')]
            match_diet = match_features[self.match_features.index('diet')]
            diet_score = 1.0 if user_diet == match_diet else 0.5
        except (ValueError, IndexError):
            diet_score = 0.5  # Default score if feature is missing

        # Smoking preference match
        try:
            user_smoking = user_features[self.user_features.index('smoking')]
            match_smoking = match_features[self.match_features.index('smoking')]
            smoking_score = 1.0 if user_smoking == match_smoking else 0.5
        except (ValueError, IndexError):
            smoking_score = 0.5  # Default score if feature is missing

        # Drinking preference match
        try:
            user_drinking = user_features[self.user_features.index('drinking')]
            match_drinking = match_features[self.match_features.index('drinking')]
            drinking_score = 1.0 if user_drinking == match_drinking else 0.5
        except (ValueError, IndexError):
            drinking_score = 0.5  # Default score if feature is missing

        # Family type match
        try:
            user_family_type = user_features[self.user_features.index('familyType')]
            match_family_type = match_features[self.match_features.index('familyType')]
            family_type_score = 1.0 if user_family_type == match_family_type else 0.5
        except (ValueError, IndexError):
            family_type_score = 0.5  # Default score if feature is missing

        # Family values match
        try:
            user_family_values = user_features[self.user_features.index('familyValues')]
            match_family_values = match_features[self.match_features.index('familyValues')]
            family_values_score = 1.0 if user_family_values == match_family_values else 0.5
        except (ValueError, IndexError):
            family_values_score = 0.5  # Default score if feature is missing

        # Calculate weighted score
        weights = {
            'age': 0.10,          # Age is important
            'religion': 0.05,     # All users are Hindu, so less weight
            'caste': 0.05,        # All users are Maratha, so less weight
            'subcaste': 0.15,     # SubCaste is very important within Maratha community
            'education': 0.10,    # Education is important
            'marital': 0.08,      # Marital status is important
            'location': 0.05,     # Location is moderately important
            'height': 0.12,       # Height difference is very important
            'occupation': 0.08,   # Occupation is important
            'income': 0.12,       # Income ratio is very important
            'diet': 0.03,         # Diet is less important
            'smoking': 0.02,      # Smoking is less important
            'drinking': 0.02,     # Drinking is less important
            'familyType': 0.03,   # Family type is less important
            'familyValues': 0.05  # Family values are moderately important
        }

        # Apply user preference adjustments
        # This allows the system to learn from user feedback over time
        preference_adjustments = self._get_preference_adjustments()
        for key in weights:
            if key in preference_adjustments:
                weights[key] *= preference_adjustments[key]

        # Normalize weights to ensure they sum to 1
        total_weight = sum(weights.values())
        if total_weight != 0:
            for key in weights:
                weights[key] /= total_weight

        total_score = (
            weights['age'] * age_score +
            weights['religion'] * religion_score +
            weights['caste'] * caste_score +
            weights['subcaste'] * subcaste_score +
            weights['education'] * education_score +
            weights['marital'] * marital_score +
            weights['location'] * location_score +
            weights['height'] * height_score +
            weights['occupation'] * occupation_score +
            weights['income'] * income_score +
            weights['diet'] * diet_score +
            weights['smoking'] * smoking_score +
            weights['drinking'] * drinking_score +
            weights['familyType'] * family_type_score +
            weights['familyValues'] * family_values_score
        )

        return total_score

    def get_user_embedding(self, user, preferences):
        """
        Get embedding for a user

        Args:
            user (dict): User profile
            preferences (dict): User preferences

        Returns:
            np.array: User embedding
        """
        # Preprocess data
        user_data, _ = self.preprocess_data([user], [preferences], [{}])

        # Convert to tensor
        user_tensor = torch.FloatTensor(user_data).to(self.device)

        # Get embedding
        self.model.eval()
        with torch.no_grad():
            embedding = self.model.user_tower(user_tensor)

        return embedding.cpu().numpy()[0]

    def get_match_embedding(self, match):
        """
        Get embedding for a potential match

        Args:
            match (dict): Potential match profile

        Returns:
            np.array: Match embedding
        """
        # Preprocess data
        _, match_data = self.preprocess_data([{}], [{}], [match])

        # Convert to tensor
        match_tensor = torch.FloatTensor(match_data).to(self.device)

        # Get embedding
        self.model.eval()
        with torch.no_grad():
            embedding = self.model.match_tower(match_tensor)

        return embedding.cpu().numpy()[0]

    def get_match_analysis(self, user, preferences, match):
        """
        Get detailed analysis of a match score

        Args:
            user (dict): User profile
            preferences (dict): User preferences
            match (dict): Potential match profile

        Returns:
            dict: Detailed analysis of the match score
        """
        # Extract features
        user_features = self._extract_user_features(user)
        preference_features = self._extract_preference_features(preferences)
        match_features = self._extract_match_features(match)

        # Calculate individual factor scores
        factors = []

        # Age factor
        user_age = user.get('age', 30)
        match_age = match.get('age', 30)
        age_diff = abs(user_age - match_age)
        age_score = max(0, 1 - age_diff / 10)  # Normalize to [0, 1]

        age_description = ""
        if age_diff == 0:
            age_description = f"You are both the same age ({user_age} years)."
        elif user.get('gender', 'MALE') == 'MALE' and user_age > match_age:
            age_description = f"You are {age_diff} years older, which is traditional in Maratha marriages."
        elif user.get('gender', 'MALE') == 'FEMALE' and match_age > user_age:
            age_description = f"The match is {age_diff} years older, which is traditional in Maratha marriages."
        else:
            age_description = f"There is an age difference of {age_diff} years."

        factors.append({
            'name': 'Age',
            'score': age_score,
            'description': age_description,
            'weight': 0.10
        })

        # Religion factor - All users are Hindu
        factors.append({
            'name': 'Religion',
            'score': 1.0,
            'description': "You both belong to the Hindu religion.",
            'weight': 0.05
        })

        # Caste factor - All users are Maratha
        factors.append({
            'name': 'Caste',
            'score': 1.0,
            'description': "You both belong to the Maratha caste.",
            'weight': 0.05
        })

        # SubCaste factor
        user_subcaste = user.get('subCaste', '')
        match_subcaste = match.get('subCaste', '')

        if user_subcaste == match_subcaste and user_subcaste:
            subcaste_score = 1.0
            subcaste_description = f"You both belong to the same Maratha sub-caste ({user_subcaste})."
        elif user_subcaste and match_subcaste:
            subcaste_score = 0.7
            subcaste_description = f"You belong to different Maratha sub-castes (yours: {user_subcaste}, match: {match_subcaste})."
        else:
            subcaste_score = 0.8
            subcaste_description = "Sub-caste information is incomplete."

        factors.append({
            'name': 'SubCaste',
            'score': subcaste_score,
            'description': subcaste_description,
            'weight': 0.15
        })

        # Height factor
        user_height = user.get('height', 170)
        match_height = match.get('height', 165)
        height_diff = user_height - match_height

        if user.get('gender', 'MALE') == 'MALE':
            # Male user, female match
            if 10 <= height_diff <= 15:
                height_score = 1.0
                height_description = f"You are {height_diff}cm taller, which is the ideal height difference in Maratha marriages."
            elif 5 <= height_diff < 10:
                height_score = 0.9
                height_description = f"You are {height_diff}cm taller, which is close to the ideal height difference."
            elif 15 < height_diff <= 20:
                height_score = 0.8
                height_description = f"You are {height_diff}cm taller, which is slightly more than the ideal height difference."
            elif height_diff < 5:
                height_score = 0.6
                height_description = f"You are only {height_diff}cm taller, which is less than the traditional height difference."
            else:
                height_score = 0.5
                height_description = f"You are {height_diff}cm taller, which is more than the traditional height difference."
        else:
            # Female user, male match
            height_diff = match_height - user_height
            if 10 <= height_diff <= 15:
                height_score = 1.0
                height_description = f"The match is {height_diff}cm taller, which is the ideal height difference in Maratha marriages."
            elif 5 <= height_diff < 10:
                height_score = 0.9
                height_description = f"The match is {height_diff}cm taller, which is close to the ideal height difference."
            elif 15 < height_diff <= 20:
                height_score = 0.8
                height_description = f"The match is {height_diff}cm taller, which is slightly more than the ideal height difference."
            elif height_diff < 5:
                height_score = 0.6
                height_description = f"The match is only {height_diff}cm taller, which is less than the traditional height difference."
            else:
                height_score = 0.5
                height_description = f"The match is {height_diff}cm taller, which is more than the traditional height difference."

        factors.append({
            'name': 'Height',
            'score': height_score,
            'description': height_description,
            'weight': 0.12
        })

        # Education factor
        user_education = user.get('education', 'Bachelors')
        match_education = match.get('education', 'Bachelors')

        education_levels = ['High School', 'Diploma', 'Bachelors', 'Masters', 'PhD']
        try:
            user_edu_level = education_levels.index(user_education)
            match_edu_level = education_levels.index(match_education)

            if user_edu_level == match_edu_level:
                education_score = 1.0
                education_description = f"You both have the same level of education ({user_education})."
            elif abs(user_edu_level - match_edu_level) == 1:
                education_score = 0.8
                education_description = f"Your education levels are similar (yours: {user_education}, match: {match_education})."
            else:
                education_score = 0.6
                education_description = f"There is a significant difference in education levels (yours: {user_education}, match: {match_education})."
        except ValueError:
            education_score = 0.7
            education_description = "Education information is incomplete or non-standard."

        factors.append({
            'name': 'Education',
            'score': education_score,
            'description': education_description,
            'weight': 0.10
        })

        # Occupation factor
        user_occupation = user.get('occupation', '')
        match_occupation = match.get('occupation', '')

        if user_occupation == match_occupation and user_occupation:
            occupation_score = 1.0
            occupation_description = f"You both have the same occupation ({user_occupation})."
        elif user_occupation and match_occupation:
            occupation_score = 0.8
            occupation_description = f"You have different occupations (yours: {user_occupation}, match: {match_occupation})."
        else:
            occupation_score = 0.7
            occupation_description = "Occupation information is incomplete."

        factors.append({
            'name': 'Occupation',
            'score': occupation_score,
            'description': occupation_description,
            'weight': 0.08
        })

        # Income factor
        user_income = user.get('income', 500000)
        match_income = match.get('income', 500000)

        if user.get('gender', 'MALE') == 'MALE':
            # Male user, female match
            if match_income > 0:
                income_ratio = user_income / match_income
                if 1.1 <= income_ratio <= 1.5:
                    income_score = 1.0
                    income_description = "Your income is slightly higher than the match, which is traditional in Maratha marriages."
                elif 1.0 <= income_ratio < 1.1:
                    income_score = 0.9
                    income_description = "Your income is similar to the match."
                elif 1.5 < income_ratio <= 2.0:
                    income_score = 0.8
                    income_description = "Your income is moderately higher than the match."
                elif income_ratio < 1.0:
                    income_score = 0.6
                    income_description = "Your income is lower than the match, which is less traditional in Maratha marriages."
                else:
                    income_score = 0.7
                    income_description = "Your income is significantly higher than the match."
            else:
                income_score = 0.7
                income_description = "Income information for the match is not available."
        else:
            # Female user, male match
            if user_income > 0:
                income_ratio = match_income / user_income
                if 1.1 <= income_ratio <= 1.5:
                    income_score = 1.0
                    income_description = "The match's income is slightly higher than yours, which is traditional in Maratha marriages."
                elif 1.0 <= income_ratio < 1.1:
                    income_score = 0.9
                    income_description = "Your incomes are similar."
                elif 1.5 < income_ratio <= 2.0:
                    income_score = 0.8
                    income_description = "The match's income is moderately higher than yours."
                elif income_ratio < 1.0:
                    income_score = 0.6
                    income_description = "The match's income is lower than yours, which is less traditional in Maratha marriages."
                else:
                    income_score = 0.7
                    income_description = "The match's income is significantly higher than yours."
            else:
                income_score = 0.6
                income_description = "Your income information is not available."

        factors.append({
            'name': 'Income',
            'score': income_score,
            'description': income_description,
            'weight': 0.12
        })

        # Location factor
        user_state = user.get('state', '')
        match_state = match.get('state', '')
        user_city = user.get('city', '')
        match_city = match.get('city', '')

        if user_city == match_city and user_city:
            location_score = 1.0
            location_description = f"You both live in the same city ({user_city})."
        elif user_state == match_state and user_state:
            location_score = 0.8
            location_description = f"You both live in the same state ({user_state}), but different cities (yours: {user_city}, match: {match_city})."
        else:
            location_score = 0.6
            location_description = "You live in different locations."

        factors.append({
            'name': 'Location',
            'score': location_score,
            'description': location_description,
            'weight': 0.05
        })

        # Marital Status factor
        user_marital = user.get('maritalStatus', 'NEVER_MARRIED')
        match_marital = match.get('maritalStatus', 'NEVER_MARRIED')

        if user_marital == match_marital:
            marital_score = 1.0
            marital_description = f"You both have the same marital status ({user_marital.replace('_', ' ').title()})."
        else:
            marital_score = 0.5
            marital_description = f"You have different marital statuses (yours: {user_marital.replace('_', ' ').title()}, match: {match_marital.replace('_', ' ').title()})."

        factors.append({
            'name': 'MaritalStatus',
            'score': marital_score,
            'description': marital_description,
            'weight': 0.08
        })

        # Gotra factor
        user_gotra = user.get('gotra', '')
        match_gotra = match.get('gotra', '')

        if user_gotra != match_gotra and user_gotra and match_gotra:
            gotra_score = 1.0
            gotra_description = f"You have different gotras (yours: {user_gotra}, match: {match_gotra}), which is preferred in Maratha marriages."
        elif user_gotra == match_gotra and user_gotra:
            gotra_score = 0.5
            gotra_description = f"You both have the same gotra ({user_gotra}), which is traditionally avoided in Maratha marriages."
        else:
            gotra_score = 0.7
            gotra_description = "Gotra information is incomplete."

        factors.append({
            'name': 'Gotra',
            'score': gotra_score,
            'description': gotra_description,
            'weight': 0.05
        })

        # Calculate overall score
        overall_score = 0.0
        total_weight = 0.0

        for factor in factors:
            overall_score += factor['score'] * factor['weight']
            total_weight += factor['weight']

        if total_weight > 0:
            overall_score /= total_weight

        # Generate suggestions
        suggestions = []

        # Add suggestions based on compatibility
        excellent_factors = [f for f in factors if f['score'] >= 0.9]
        poor_factors = [f for f in factors if f['score'] < 0.6]

        if len(excellent_factors) >= 3:
            excellent_names = [f['name'] for f in excellent_factors[:3]]
            suggestions.append(f"You have excellent compatibility in {', '.join(excellent_names)}, which forms a strong foundation for a relationship.")

        if len(poor_factors) > 0:
            poor_names = [f['name'] for f in poor_factors]
            suggestions.append(f"You may want to consider the differences in {', '.join(poor_names)} before proceeding.")

        # Add specific suggestions
        if user.get('gender', 'MALE') == 'MALE' and match.get('occupation', ''):
            suggestions.append(f"The match's career as a {match.get('occupation')} could complement your profession as a {user.get('occupation', 'professional')}.")

        if user_city != match_city and user_city and match_city:
            suggestions.append(f"Consider discussing future living arrangements as you currently live in different cities.")

        # Return analysis
        return {
            'overallScore': overall_score,
            'factors': factors,
            'suggestions': suggestions
        }

    def save(self, path):
        """
        Save the model

        Args:
            path (str): Path to save the model
        """
        # Create directory if it doesn't exist
        os.makedirs(path, exist_ok=True)

        # Save model
        torch.save(self.model.state_dict(), f"{path}/model.pt")

        # Save config
        with open(f"{path}/config.json", 'w') as f:
            json.dump(self.config, f)

    def load(self, path):
        """
        Load the model

        Args:
            path (str): Path to load the model from
        """
        # Load config
        with open(f"{path}/config.json", 'r') as f:
            self.config = json.load(f)

        # Build model
        self.build_model()

        # Load model weights
        self.model.load_state_dict(torch.load(f"{path}/model.pt", map_location=self.device))
