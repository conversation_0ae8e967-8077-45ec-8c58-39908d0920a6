/**
 * Test script for 4-digit OTP implementation
 * 
 * This script tests the 4-digit OTP implementation by:
 * 1. Generating a 4-digit OTP
 * 2. Sending it to a phone number
 * 3. Verifying that the OTP is 4 digits
 * 
 * Usage:
 * node scripts/test-4digit-otp.js <phone_number>
 * 
 * Example:
 * node scripts/test-4digit-otp.js 919527159115
 */

// Load environment variables
require('dotenv').config();

// Import required modules
const msg91Service = require('../src/services/sms/msg91.service');
const redisOtpService = require('../redis/otpService');

// Check if phone number is provided
const phoneNumber = process.argv[2];
if (!phoneNumber) {
  console.error('Please provide a phone number as an argument.');
  console.error('Usage: node scripts/test-4digit-otp.js <phone_number>');
  process.exit(1);
}

// Initialize the MSG91 service
msg91Service.initialize({
  apiKey: process.env.MSG91_API_KEY,
  senderId: process.env.MSG91_SENDER_ID,
  dltTemplateId: process.env.MSG91_DLT_TEMPLATE_ID,
  dltPeId: process.env.MSG91_DLT_PE_ID,
  otpLength: 4,
  otpExpiry: 15,
  otpTemplate: process.env.MSG91_OTP_TEMPLATE
});

// Format the phone number
const formattedPhone = msg91Service.formatPhoneNumber(phoneNumber);

// Test the OTP generation
async function testOtpGeneration() {
  console.log('\n=== Testing OTP Generation ===');
  
  // Generate OTP using MSG91 service
  const msg91Otp = msg91Service.generateOtp();
  console.log(`MSG91 OTP: ${msg91Otp} (Length: ${msg91Otp.length})`);
  
  // Generate OTP using Redis OTP service
  const redisOtp = redisOtpService.generateOTP();
  console.log(`Redis OTP: ${redisOtp} (Length: ${redisOtp.length})`);
  
  // Verify that both OTPs are 4 digits
  if (msg91Otp.length !== 4) {
    console.error('❌ MSG91 OTP is not 4 digits!');
  } else {
    console.log('✅ MSG91 OTP is 4 digits');
  }
  
  if (redisOtp.length !== 4) {
    console.error('❌ Redis OTP is not 4 digits!');
  } else {
    console.log('✅ Redis OTP is 4 digits');
  }
  
  return msg91Otp;
}

// Test sending OTP
async function testSendOtp(otp) {
  console.log('\n=== Testing OTP Sending ===');
  console.log(`Sending OTP ${otp} to ${formattedPhone}...`);
  
  try {
    const result = await msg91Service.sendOtp(formattedPhone, otp);
    
    if (result.success) {
      console.log('✅ OTP sent successfully!');
      console.log('Response:', JSON.stringify(result.data, null, 2));
      
      // Store OTP in Redis
      await redisOtpService.setOTP(formattedPhone, otp, 900); // 15 minutes
      console.log('✅ OTP stored in Redis');
      
      return true;
    } else {
      console.error('❌ Failed to send OTP:', result.message);
      console.error('Response data:', JSON.stringify(result.data, null, 2));
      return false;
    }
  } catch (error) {
    console.error('❌ Error sending OTP:', error);
    return false;
  }
}

// Run the tests
async function runTests() {
  try {
    console.log('=== 4-Digit OTP Test ===');
    console.log(`Testing with phone number: ${formattedPhone}`);
    
    // Test OTP generation
    const otp = await testOtpGeneration();
    
    // Test sending OTP
    const sendResult = await testSendOtp(otp);
    
    if (sendResult) {
      console.log('\n✅ OTP test completed successfully!');
      console.log(`The 4-digit OTP ${otp} has been sent to ${formattedPhone}`);
      console.log('Please check your phone to verify that you received a 4-digit OTP.');
    } else {
      console.error('\n❌ OTP test failed!');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error running tests:', error);
    process.exit(1);
  }
}

// Run the tests
runTests();
