/**
 * API Endpoints Test Script
 *
 * This script tests the functionality of API endpoints in the Vaivahik backend.
 * It makes HTTP requests to each endpoint and verifies the responses.
 *
 * To run this test:
 * 1. Make sure the backend server is running
 * 2. Run: node test-api-endpoints.js
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config(); // Load environment variables

// Configuration
const config = {
  baseUrl: 'http://localhost:8000/api',
  // Admin credentials for authentication
  auth: {
    email: '<EMAIL>',
    password: 'admin123'
  },
  endpoints: [
    // Admin endpoints
    { method: 'GET', path: '/admin/users', name: 'Get All Users', requiresAuth: true, authType: 'admin' },
    { method: 'GET', path: '/admin/verification-queue', name: 'Get Verification Queue', requiresAuth: true, authType: 'admin' },
    { method: 'GET', path: '/admin/reported-profiles', name: 'Get Reported Profiles', requiresAuth: true, authType: 'admin' },
    { method: 'GET', path: '/admin/premium-plans', name: 'Get Premium Plans', requiresAuth: true, authType: 'admin' },
    { method: 'GET', path: '/admin/success-stories', name: 'Get Success Stories', requiresAuth: true, authType: 'admin' },

    // User endpoints
    { method: 'GET', path: '/users/profiles', name: 'Get User Profiles', requiresAuth: true, authType: 'user' },
    { method: 'GET', path: '/users/matches', name: 'Get User Matches', requiresAuth: true, authType: 'user' },

    // Auth endpoints
    { method: 'POST', path: '/auth/login', name: 'User Login', data: { email: '<EMAIL>', password: 'password123' } },
    { method: 'POST', path: '/auth/register', name: 'User Registration', data: { email: '<EMAIL>', password: 'password123', name: 'Test User' } },

    // Feature flag endpoint
    { method: 'GET', path: '/feature-flags', name: 'Get Feature Flags' }
  ],
  resultsDir: './test-results'
};

// Store auth tokens
let adminAuthToken = null;
let userAuthToken = null;

/**
 * Get authentication token
 * @param {string} type - 'admin' or 'user'
 * @returns {Promise<string>} - Auth token
 */
async function getAuthToken(type) {
  if (type === 'admin' && adminAuthToken) {
    return adminAuthToken;
  }

  if (type === 'user' && userAuthToken) {
    return userAuthToken;
  }

  try {
    // Login endpoint
    const loginUrl = `${config.baseUrl}/admin/login`;

    // Credentials based on type
    const credentials = type === 'admin'
      ? { email: config.auth.email, password: config.auth.password }
      : { email: '<EMAIL>', password: 'password123' };

    // Make login request
    const response = await axios.post(loginUrl, credentials);

    // Extract token from response
    const token = response.data.token || response.data.accessToken;

    if (!token) {
      console.warn(`No token found in ${type} login response`);
      return null;
    }

    // Store token
    if (type === 'admin') {
      adminAuthToken = token;
    } else {
      userAuthToken = token;
    }

    return token;
  } catch (error) {
    console.error(`Error getting ${type} auth token:`, error.message);
    return null;
  }
}

/**
 * Test a single API endpoint
 * @param {Object} endpoint - Endpoint configuration
 * @returns {Object} Test result
 */
async function testEndpoint(endpoint) {
  console.log(`Testing ${endpoint.name} (${endpoint.method} ${endpoint.path})...`);

  try {
    const url = `${config.baseUrl}${endpoint.path}`;
    let response;

    // Setup headers
    const headers = {};

    // Add auth token if required
    if (endpoint.requiresAuth) {
      const token = await getAuthToken(endpoint.authType);
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      } else {
        console.warn(`No auth token available for ${endpoint.name}, proceeding without authentication`);
      }
    }

    // Make the request based on the HTTP method
    switch (endpoint.method.toUpperCase()) {
      case 'GET':
        response = await axios.get(url, { headers });
        break;
      case 'POST':
        response = await axios.post(url, endpoint.data || {}, { headers });
        break;
      case 'PUT':
        response = await axios.put(url, endpoint.data || {}, { headers });
        break;
      case 'DELETE':
        response = await axios.delete(url, { headers });
        break;
      default:
        throw new Error(`Unsupported HTTP method: ${endpoint.method}`);
    }

    // Check if the response is successful
    const isSuccess = response.status >= 200 && response.status < 300;

    // Check if the response has the expected structure
    const hasExpectedStructure = response.data &&
      (response.data.success !== undefined ||
       Array.isArray(response.data) ||
       Object.keys(response.data).length > 0);

    // Save the response to a file
    const filename = `${endpoint.method.toLowerCase()}-${endpoint.path.replace(/\//g, '-')}.json`;
    fs.writeFileSync(
      path.join(config.resultsDir, filename),
      JSON.stringify(response.data, null, 2)
    );

    return {
      endpoint,
      success: isSuccess && hasExpectedStructure,
      status: response.status,
      hasExpectedStructure,
      error: null,
      response: response.data
    };
  } catch (error) {
    console.error(`Error testing ${endpoint.name}:`, error.message);

    // Save the error to a file
    const filename = `${endpoint.method.toLowerCase()}-${endpoint.path.replace(/\//g, '-')}-error.json`;
    fs.writeFileSync(
      path.join(config.resultsDir, filename),
      JSON.stringify({
        error: error.message,
        response: error.response ? error.response.data : null
      }, null, 2)
    );

    return {
      endpoint,
      success: false,
      status: error.response ? error.response.status : null,
      hasExpectedStructure: false,
      error: error.message,
      response: error.response ? error.response.data : null
    };
  }
}

/**
 * Test all API endpoints
 */
async function testAllEndpoints() {
  console.log('Starting API endpoint tests...');

  // Create results directory if it doesn't exist
  if (!fs.existsSync(config.resultsDir)) {
    fs.mkdirSync(config.resultsDir, { recursive: true });
  }

  // Results array
  const results = [];

  // Test each endpoint
  for (const endpoint of config.endpoints) {
    const result = await testEndpoint(endpoint);
    results.push(result);
  }

  // Print summary
  console.log('\n=== Test Results Summary ===');

  let passCount = 0;
  let failCount = 0;

  for (const result of results) {
    if (result.success) {
      console.log(`✅ ${result.endpoint.name}: Passed (${result.status})`);
      passCount++;
    } else {
      console.log(`❌ ${result.endpoint.name}: Failed`);
      if (result.status) console.log(`   - Status: ${result.status}`);
      if (!result.hasExpectedStructure) console.log(`   - Unexpected response structure`);
      if (result.error) console.log(`   - Error: ${result.error}`);
      failCount++;
    }
  }

  console.log(`\nTotal: ${results.length} tests, ${passCount} passed, ${failCount} failed`);

  // Save overall results
  fs.writeFileSync(
    path.join(config.resultsDir, 'summary.json'),
    JSON.stringify({
      total: results.length,
      passed: passCount,
      failed: failCount,
      results: results.map(r => ({
        name: r.endpoint.name,
        method: r.endpoint.method,
        path: r.endpoint.path,
        success: r.success,
        status: r.status,
        error: r.error
      }))
    }, null, 2)
  );

  return {
    total: results.length,
    passed: passCount,
    failed: failCount,
    results
  };
}

// Run the tests if this file is executed directly
if (require.main === module) {
  testAllEndpoints()
    .then(summary => {
      console.log('\nTest execution completed.');
      process.exit(summary.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = {
  testEndpoint,
  testAllEndpoints
};
