# Vaivahik Admin Panel Developer Guide

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Project Structure](#project-structure)
3. [Component Library](#component-library)
4. [State Management](#state-management)
5. [API Integration](#api-integration)
6. [Authentication](#authentication)
7. [Routing](#routing)
8. [Styling](#styling)
9. [Performance Optimization](#performance-optimization)
10. [Testing](#testing)
11. [Deployment](#deployment)
12. [Contributing Guidelines](#contributing-guidelines)

## Architecture Overview

The Vaivahik Admin Panel is built using Next.js, a React framework that enables server-side rendering and static site generation. The architecture follows a modular approach with reusable components and services.

### Key Technologies

- **Next.js**: Framework for server-rendered React applications
- **React**: UI library for building component-based interfaces
- **Material UI**: Component library for consistent design
- **Prisma**: ORM for database interactions
- **Redis**: For caching and real-time features
- **Chart.js**: For data visualization
- **React Query**: For server state management

### System Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Next.js Admin  │────▶│  Express API    │────▶│  Database       │
│  Frontend       │     │  Backend        │     │  (PostgreSQL)   │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        │                       │                        │
        │                       │                        │
        ▼                       ▼                        ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Redis Cache    │     │  File Storage   │     │  External APIs  │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

## Project Structure

The admin panel follows a structured organization:

```
vaivahik-nextjs/
├── public/
│   ├── images/
│   ├── styles/
│   └── docs/
├── src/
│   ├── components/
│   │   ├── admin/
│   │   ├── common/
│   │   └── ui/
│   ├── config/
│   ├── hooks/
│   ├── pages/
│   │   ├── admin/
│   │   └── api/
│   ├── services/
│   ├── styles/
│   └── utils/
├── prisma/
└── package.json
```

### Key Directories

- **components/admin**: Admin-specific components
- **components/common**: Shared components
- **components/ui**: UI elements and design system
- **config**: Configuration files
- **hooks**: Custom React hooks
- **pages/admin**: Admin panel pages
- **pages/api**: API routes
- **services**: API service layer
- **utils**: Utility functions

## Component Library

The admin panel uses a combination of Material UI components and custom components.

### Core Components

- **AdminLayout**: Main layout wrapper for all admin pages
- **DataTable**: Reusable table component with sorting, filtering, and pagination
- **FormBuilder**: Dynamic form generation component
- **Charts**: Visualization components
- **Dialogs**: Modal dialog components
- **Cards**: Information and action cards

### Component Guidelines

1. **Modularity**: Components should be self-contained and reusable
2. **Props**: Use prop types or TypeScript for type checking
3. **State**: Minimize component state, prefer props
4. **Styling**: Use Material UI's styling system or styled-components
5. **Accessibility**: Ensure all components are accessible

## State Management

The admin panel uses a combination of local state, context API, and React Query for state management.

### Local State

- Component-specific state using `useState`
- Form state using form libraries

### Context API

- Authentication state
- Theme and preferences
- Global notifications

### React Query

- Server state management
- Data fetching and caching
- Optimistic updates

## API Integration

The admin panel communicates with the backend API through a service layer.

### API Service Structure

```javascript
// Example API service
import { apiClient } from '@/utils/apiClient';

export const userService = {
  getUsers: async (params) => {
    return apiClient.get('/admin/users', { params });
  },
  
  getUserById: async (id) => {
    return apiClient.get(`/admin/users/${id}`);
  },
  
  updateUser: async (id, data) => {
    return apiClient.put(`/admin/users/${id}`, data);
  },
  
  deleteUser: async (id) => {
    return apiClient.delete(`/admin/users/${id}`);
  }
};
```

### Error Handling

All API calls should include proper error handling:

```javascript
try {
  const response = await userService.getUsers(params);
  // Handle success
} catch (error) {
  // Handle error based on type
  if (error.response) {
    // Server responded with an error status
    handleApiError(error.response.data);
  } else if (error.request) {
    // Request was made but no response received
    handleNetworkError();
  } else {
    // Something else went wrong
    handleUnexpectedError(error);
  }
}
```

## Authentication

The admin panel uses JWT-based authentication.

### Authentication Flow

1. Admin logs in with credentials
2. Server validates credentials and returns JWT token
3. Token is stored in localStorage or secure cookie
4. Token is included in all API requests
5. Server validates token for protected routes

### Auth Service

```javascript
export const authService = {
  login: async (credentials) => {
    const response = await apiClient.post('/admin/login', credentials);
    if (response.data.token) {
      localStorage.setItem('adminAccessToken', response.data.token);
      localStorage.setItem('adminRole', response.data.role);
      localStorage.setItem('adminName', response.data.name);
    }
    return response.data;
  },
  
  logout: () => {
    localStorage.removeItem('adminAccessToken');
    localStorage.removeItem('adminRole');
    localStorage.removeItem('adminName');
  },
  
  getCurrentAdmin: () => {
    const token = localStorage.getItem('adminAccessToken');
    if (token) {
      // Decode token to get admin info
      return decodeToken(token);
    }
    return null;
  },
  
  isAuthenticated: () => {
    const token = localStorage.getItem('adminAccessToken');
    return !!token && !isTokenExpired(token);
  }
};
```

## Routing

The admin panel uses Next.js routing with dynamic routes.

### Route Structure

- `/admin/login`: Admin login page
- `/admin/dashboard`: Main dashboard
- `/admin/users`: User management
- `/admin/users/[id]`: User details
- `/admin/verification-queue`: Verification management
- `/admin/blog-posts`: Blog management
- `/admin/blog-posts/[id]`: Blog post details

### Route Protection

Protected routes check for authentication:

```javascript
// Example route protection in _app.js or middleware
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { authService } from '@/services/authService';

function MyApp({ Component, pageProps }) {
  const router = useRouter();
  
  useEffect(() => {
    // Check if route requires authentication
    if (router.pathname.startsWith('/admin') && 
        router.pathname !== '/admin/login') {
      if (!authService.isAuthenticated()) {
        router.push('/admin/login');
      }
    }
  }, [router.pathname]);
  
  return <Component {...pageProps} />;
}
```

## Styling

The admin panel uses a combination of Material UI styling and custom CSS.

### Theme Configuration

```javascript
// Example theme configuration
import { createTheme } from '@mui/material/styles';

export const theme = createTheme({
  palette: {
    primary: {
      main: '#5e35b1', // Deep Purple
      light: '#7e57c2',
      dark: '#4527a0',
    },
    secondary: {
      main: '#ff5722', // Deep Orange
      light: '#ff8a50',
      dark: '#c41c00',
    },
    // Other color definitions
  },
  typography: {
    fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
    // Typography settings
  },
  components: {
    // Component customizations
  },
});
```

### CSS Variables

Global CSS variables are defined in `admin-ui-components.css`:

```css
:root {
  /* Primary Colors */
  --primary: #5e35b1;
  --primary-light: #7e57c2;
  --primary-dark: #4527a0;
  /* Other variables */
}
```

### Responsive Design

All components should be responsive using Material UI's responsive utilities or media queries.

## Performance Optimization

### Code Splitting

Use dynamic imports for code splitting:

```javascript
import dynamic from 'next/dynamic';

const DynamicChart = dynamic(() => import('@/components/admin/Chart'), {
  loading: () => <p>Loading chart...</p>,
  ssr: false
});
```

### Image Optimization

Use Next.js Image component for optimized images:

```javascript
import Image from 'next/image';

<Image 
  src="/images/profile.jpg"
  alt="Profile"
  width={100}
  height={100}
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,..."
/>
```

### Memoization

Use React.memo and useMemo for expensive computations:

```javascript
const MemoizedComponent = React.memo(function MyComponent(props) {
  // Component logic
});

// In functional components
const memoizedValue = useMemo(() => computeExpensiveValue(a, b), [a, b]);
```

## Testing

The admin panel uses Jest and React Testing Library for testing.

### Unit Testing

```javascript
// Example component test
import { render, screen } from '@testing-library/react';
import UserCard from '@/components/admin/UserCard';

describe('UserCard', () => {
  it('renders user information correctly', () => {
    const user = {
      name: 'John Doe',
      email: '<EMAIL>',
      role: 'admin'
    };
    
    render(<UserCard user={user} />);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('admin')).toBeInTheDocument();
  });
});
```

### Integration Testing

```javascript
// Example integration test
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import UserList from '@/components/admin/UserList';
import { userService } from '@/services/userService';

// Mock the service
jest.mock('@/services/userService');

describe('UserList', () => {
  it('loads and displays users', async () => {
    // Setup mock
    userService.getUsers.mockResolvedValue({
      data: {
        users: [
          { id: 1, name: 'John Doe', email: '<EMAIL>' },
          { id: 2, name: 'Jane Smith', email: '<EMAIL>' }
        ],
        total: 2
      }
    });
    
    render(<UserList />);
    
    // Check loading state
    expect(screen.getByText('Loading...')).toBeInTheDocument();
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    });
  });
});
```

## Deployment

The admin panel is deployed alongside the main application.

### Build Process

```bash
# Install dependencies
npm install

# Build for production
npm run build

# Start production server
npm start
```

### Environment Variables

Required environment variables:

```
# API Configuration
NEXT_PUBLIC_API_URL=https://api.example.com

# Authentication
NEXTAUTH_URL=https://example.com
NEXTAUTH_SECRET=your-secret-key

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/mydb

# Redis
REDIS_URL=redis://localhost:6379
```

## Contributing Guidelines

### Code Style

- Follow ESLint and Prettier configurations
- Use meaningful variable and function names
- Write comments for complex logic
- Follow component naming conventions

### Pull Request Process

1. Create a feature branch from `develop`
2. Make your changes
3. Write or update tests
4. Ensure all tests pass
5. Submit a pull request to `develop`
6. Request code review

### Commit Message Format

Follow conventional commits:

```
feat: add user verification feature
fix: resolve issue with pagination
docs: update API documentation
style: format code according to style guide
refactor: simplify authentication logic
test: add tests for user service
chore: update dependencies
```

---

This guide is regularly updated to reflect the latest development practices and standards for the Vaivahik Admin Panel.

Last Updated: [Current Date]
