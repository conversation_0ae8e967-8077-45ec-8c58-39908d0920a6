/**
 * This script validates that the directory structure follows the project's policy.
 * It checks that new pages are added to the /pages directory rather than /src/pages.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Get the root directory of the project
const rootDir = path.resolve(__dirname, '..');

// Get the list of modified files
function getModifiedFiles() {
  try {
    // Get the list of staged files
    const stagedFiles = execSync('git diff --cached --name-only', { encoding: 'utf8' })
      .split('\n')
      .filter(Boolean);

    // Get the list of unstaged files
    const unstagedFiles = execSync('git diff --name-only', { encoding: 'utf8' })
      .split('\n')
      .filter(Boolean);

    // Combine the lists
    return [...new Set([...stagedFiles, ...unstagedFiles])];
  } catch (error) {
    console.error('Error getting modified files:', error.message);
    return [];
  }
}

// Check if a file is a new page in the src/pages directory
function isNewPageInSrcPages(file) {
  return file.startsWith('src/pages/') && file.endsWith('.js') && !fs.existsSync(path.join(rootDir, file));
}

// Main validation function
function validateDirectoryStructure() {
  const modifiedFiles = getModifiedFiles();
  const newPagesInSrcPages = modifiedFiles.filter(isNewPageInSrcPages);

  if (newPagesInSrcPages.length > 0) {
    console.error('\n❌ Directory Structure Validation Failed');
    console.error('The following new pages were added to src/pages instead of pages:');
    newPagesInSrcPages.forEach(file => {
      console.error(`  - ${file}`);
    });
    console.error('\nPlease move these files to the pages directory instead.');
    console.error('See DIRECTORY_POLICY.md for more information.\n');
    process.exit(1);
  }

  console.log('✅ Directory structure validation passed');
}

// Run the validation
validateDirectoryStructure();
