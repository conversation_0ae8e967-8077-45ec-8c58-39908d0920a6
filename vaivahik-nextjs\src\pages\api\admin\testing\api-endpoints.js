// API endpoint to run API endpoint tests
import { exec } from 'child_process';
import path from 'path';
import fs from 'fs';

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Get the project root directory
    const projectRoot = process.cwd();
    
    // Path to the test script
    const testScriptPath = path.join(projectRoot, '..', 'vaivahik-backend', 'test-api-endpoints.js');
    
    // Check if the test script exists
    if (!fs.existsSync(testScriptPath)) {
      return res.status(404).json({
        success: false,
        message: 'Test script not found. Please make sure test-api-endpoints.js exists in the vaivahik-backend directory.'
      });
    }
    
    // Create results directory if it doesn't exist
    const resultsDir = path.join(projectRoot, '..', 'vaivahik-backend', 'test-results');
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    // Run the test script
    exec(`node ${testScriptPath}`, { cwd: path.join(projectRoot, '..', 'vaivahik-backend') }, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error running API endpoint test: ${error.message}`);
        return res.status(500).json({
          success: false,
          message: 'Error running API endpoint test',
          error: error.message,
          stdout,
          stderr
        });
      }
      
      if (stderr) {
        console.error(`Test script stderr: ${stderr}`);
      }
      
      // Parse the test results from stdout
      let results = {
        success: true,
        total: 0,
        passed: 0,
        failed: 0,
        results: [],
        output: stdout
      };
      
      try {
        // Try to extract test results from the output
        const summaryMatch = stdout.match(/Total: (\d+) tests, (\d+) passed, (\d+) failed/);
        if (summaryMatch) {
          results.total = parseInt(summaryMatch[1]);
          results.passed = parseInt(summaryMatch[2]);
          results.failed = parseInt(summaryMatch[3]);
        }
        
        // Extract individual test results
        const testResults = [];
        const testResultRegex = /(✅|❌) ([^:]+): (Passed|Failed)(?: \((\d+)\))?/g;
        let match;
        while ((match = testResultRegex.exec(stdout)) !== null) {
          testResults.push({
            status: match[1] === '✅' ? 'passed' : 'failed',
            name: match[2].trim(),
            message: match[3],
            statusCode: match[4] ? parseInt(match[4]) : null
          });
        }
        
        if (testResults.length > 0) {
          results.results = testResults;
        }
        
        // Try to read the summary.json file if it exists
        const summaryFilePath = path.join(resultsDir, 'summary.json');
        if (fs.existsSync(summaryFilePath)) {
          const summaryData = JSON.parse(fs.readFileSync(summaryFilePath, 'utf8'));
          results = { ...results, ...summaryData };
        }
      } catch (parseError) {
        console.error('Error parsing test results:', parseError);
      }
      
      return res.status(200).json(results);
    });
  } catch (error) {
    console.error('Error in API endpoint test handler:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
}
