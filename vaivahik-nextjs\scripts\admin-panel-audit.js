#!/usr/bin/env node

/**
 * Comprehensive Admin Panel Audit Script
 * Verifies all 35 admin functions and their connections
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

// Admin functions to audit
const adminFunctions = [
  // IMPLEMENTED FUNCTIONS (should be ✅)
  { id: 1, name: 'User Management', frontend: 'src/pages/admin/users/index.js', backend: 'src/controllers/admin/user.controller.js', priority: 'HIGH', implemented: true },
  { id: 2, name: 'Profile Management', frontend: 'src/pages/admin/profiles/index.js', backend: 'src/controllers/admin/profile.controller.js', priority: 'HIGH', implemented: true },
  { id: 3, name: 'Verification Queue', frontend: 'src/pages/admin/verification/index.js', backend: 'src/controllers/admin/verification.controller.js', priority: 'HIGH', implemented: true },
  { id: 4, name: 'Document Verification', frontend: 'src/pages/admin/documents/index.js', backend: 'src/controllers/admin/document.controller.js', priority: 'HIGH', implemented: true },
  { id: 5, name: 'Subscription Management', frontend: 'src/pages/admin/subscriptions/index.js', backend: 'src/controllers/admin/subscription.controller.js', priority: 'HIGH', implemented: true },
  { id: 6, name: 'Payment Management', frontend: 'src/pages/admin/payments/index.js', backend: 'src/controllers/admin/payment.controller.js', priority: 'HIGH', implemented: true },
  { id: 7, name: 'Analytics Dashboard', frontend: 'src/pages/admin/analytics/index.js', backend: 'src/controllers/admin/analytics.controller.js', priority: 'HIGH', implemented: true },
  { id: 8, name: 'Reports Generation', frontend: 'src/pages/admin/reports/index.js', backend: 'src/controllers/admin/reports.controller.js', priority: 'MEDIUM', implemented: true },
  { id: 9, name: 'Matching Algorithm', frontend: 'src/pages/admin/matching/index.js', backend: 'src/controllers/admin/matching.controller.js', priority: 'HIGH', implemented: true },
  { id: 10, name: 'Content Moderation', frontend: 'src/pages/admin/moderation/index.js', backend: 'src/controllers/admin/moderation.controller.js', priority: 'HIGH', implemented: true },
  { id: 11, name: 'Notification Management', frontend: 'src/pages/admin/notifications/index.js', backend: 'src/controllers/admin/notification.controller.js', priority: 'MEDIUM', implemented: true },
  { id: 12, name: 'Support Tickets', frontend: 'src/pages/admin/support/index.js', backend: 'src/controllers/admin/support.controller.js', priority: 'MEDIUM', implemented: true },
  { id: 13, name: 'System Settings', frontend: 'src/pages/admin/settings/index.js', backend: 'src/controllers/admin/settings.controller.js', priority: 'MEDIUM', implemented: true },
  { id: 14, name: 'Bulk Operations', frontend: 'src/pages/admin/bulk-operations/index.js', backend: 'src/controllers/admin/bulk.controller.js', priority: 'LOW', implemented: true },
  { id: 15, name: 'Data Export/Import', frontend: 'src/pages/admin/data-management/index.js', backend: 'src/controllers/admin/data.controller.js', priority: 'LOW', implemented: true },
  { id: 16, name: 'Feedback Management', frontend: 'src/pages/admin/feedback/index.js', backend: 'src/controllers/admin/feedback.controller.js', priority: 'MEDIUM', implemented: true },
  { id: 17, name: 'Marketing Campaigns', frontend: 'src/pages/admin/marketing/index.js', backend: 'src/controllers/admin/marketing.controller.js', priority: 'MEDIUM', implemented: true },
  { id: 18, name: 'Referral Management', frontend: 'src/pages/admin/referrals/index.js', backend: 'src/controllers/admin/referral.controller.js', priority: 'LOW', implemented: true },
  { id: 19, name: 'Fraud Detection', frontend: 'src/pages/admin/fraud-detection/index.js', backend: 'src/controllers/admin/fraud.controller.js', priority: 'HIGH', implemented: true },
  { id: 20, name: 'Performance Monitoring', frontend: 'src/pages/admin/performance/index.js', backend: 'src/controllers/admin/performance.controller.js', priority: 'MEDIUM', implemented: true },

  // NEWLY IMPLEMENTED FUNCTIONS (should be ✅)
  { id: 21, name: 'Success Stories', frontend: 'src/pages/admin/success-stories/index.js', backend: 'src/controllers/admin/successStory.controller.js', priority: 'HIGH', implemented: true },
  { id: 22, name: 'Blog Posts', frontend: 'src/pages/admin/blog-posts/index.js', backend: 'src/controllers/admin/blog.controller.js', priority: 'HIGH', implemented: true },
  { id: 23, name: 'Biodata Templates', frontend: 'src/pages/admin/biodata-templates/index.js', backend: 'src/controllers/admin/biodataTemplate.controller.js', priority: 'MEDIUM', implemented: true },
  { id: 24, name: 'Spotlight Features', frontend: 'src/pages/admin/spotlight/index.js', backend: 'src/controllers/admin/spotlight.controller.js', priority: 'MEDIUM', implemented: true },
  { id: 25, name: 'Email Templates', frontend: 'src/pages/admin/email-templates/index.js', backend: 'src/controllers/admin/emailTemplate.controller.js', priority: 'MEDIUM', implemented: true },
  { id: 26, name: 'Chat Management', frontend: 'src/pages/admin/chat-management/index.js', backend: 'src/controllers/admin/chat.controller.js', priority: 'HIGH', implemented: true },
  { id: 27, name: 'Contact Reveal Security', frontend: 'src/pages/admin/contact-security/index.js', backend: 'src/controllers/admin/contactSecurity.controller.js', priority: 'HIGH', implemented: true },
  { id: 28, name: 'Privacy Controls', frontend: 'src/pages/admin/privacy-controls/index.js', backend: 'src/controllers/admin/privacy.controller.js', priority: 'HIGH', implemented: true },
  { id: 29, name: 'Security Settings', frontend: 'src/pages/admin/security-settings/index.js', backend: 'src/controllers/admin/security.controller.js', priority: 'HIGH', implemented: true },
  { id: 30, name: 'Advanced Analytics', frontend: 'src/pages/admin/advanced-analytics/index.js', backend: 'src/controllers/admin/advancedAnalytics.controller.js', priority: 'HIGH', implemented: true },
  { id: 31, name: 'Promotions Management', frontend: 'src/pages/admin/promotions/index.js', backend: 'src/controllers/admin/promotions.controller.js', priority: 'MEDIUM', implemented: true },

  // REMAINING FUNCTIONS (need implementation)
  { id: 32, name: 'API Management', frontend: 'src/pages/admin/api-management/index.js', backend: 'src/controllers/admin/api.controller.js', priority: 'MEDIUM', implemented: false },
  { id: 33, name: 'SMS Configuration', frontend: 'src/pages/admin/sms-config/index.js', backend: 'src/controllers/admin/sms.controller.js', priority: 'MEDIUM', implemented: false },
  { id: 34, name: 'Backup & Recovery', frontend: 'src/pages/admin/backup/index.js', backend: 'src/controllers/admin/backup.controller.js', priority: 'MEDIUM', implemented: false },
  { id: 35, name: 'Admin Users', frontend: 'src/pages/admin/admin-users/index.js', backend: 'src/controllers/admin/adminUser.controller.js', priority: 'LOW', implemented: false }
];

/**
 * Check if a file exists
 */
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

/**
 * Check if a file contains specific content
 */
function fileContains(filePath, searchString) {
  try {
    if (!fileExists(filePath)) return false;
    const content = fs.readFileSync(filePath, 'utf8');
    return content.includes(searchString);
  } catch (error) {
    return false;
  }
}

/**
 * Audit a single admin function
 */
function auditFunction(func) {
  const frontendPath = path.join(__dirname, '..', func.frontend);
  const backendPath = path.join(__dirname, '../../vaivahik-backend', func.backend);
  
  const results = {
    id: func.id,
    name: func.name,
    priority: func.priority,
    implemented: func.implemented,
    frontend: {
      exists: fileExists(frontendPath),
      hasEnhancedLayout: fileContains(frontendPath, 'EnhancedAdminLayout'),
      hasApiCalls: fileContains(frontendPath, 'adminGet') || fileContains(frontendPath, 'adminPost'),
      hasErrorHandling: fileContains(frontendPath, 'try') && fileContains(frontendPath, 'catch')
    },
    backend: {
      exists: fileExists(backendPath),
      hasRoutes: fileContains(backendPath, 'router') || fileContains(backendPath, 'app.'),
      hasValidation: fileContains(backendPath, 'validate') || fileContains(backendPath, 'joi'),
      hasErrorHandling: fileContains(backendPath, 'try') && fileContains(backendPath, 'catch')
    }
  };
  
  // Calculate overall status
  if (func.implemented) {
    results.status = results.frontend.exists && results.backend.exists ? 'COMPLETE' : 'PARTIAL';
  } else {
    results.status = 'NOT_IMPLEMENTED';
  }
  
  return results;
}

/**
 * Generate audit report
 */
function generateReport(auditResults) {
  log('\n🔍 COMPREHENSIVE ADMIN PANEL AUDIT REPORT', 'bright');
  log('=' * 60, 'blue');
  
  const stats = {
    total: auditResults.length,
    complete: 0,
    partial: 0,
    notImplemented: 0,
    highPriority: 0,
    mediumPriority: 0,
    lowPriority: 0
  };
  
  // Calculate statistics
  auditResults.forEach(result => {
    switch (result.status) {
      case 'COMPLETE': stats.complete++; break;
      case 'PARTIAL': stats.partial++; break;
      case 'NOT_IMPLEMENTED': stats.notImplemented++; break;
    }
    
    switch (result.priority) {
      case 'HIGH': stats.highPriority++; break;
      case 'MEDIUM': stats.mediumPriority++; break;
      case 'LOW': stats.lowPriority++; break;
    }
  });
  
  // Display summary
  log('\n📊 SUMMARY STATISTICS', 'cyan');
  log(`Total Functions: ${stats.total}`, 'blue');
  log(`✅ Complete: ${stats.complete} (${((stats.complete/stats.total)*100).toFixed(1)}%)`, 'green');
  log(`⚠️  Partial: ${stats.partial} (${((stats.partial/stats.total)*100).toFixed(1)}%)`, 'yellow');
  log(`❌ Not Implemented: ${stats.notImplemented} (${((stats.notImplemented/stats.total)*100).toFixed(1)}%)`, 'red');
  
  log('\n🎯 PRIORITY BREAKDOWN', 'cyan');
  log(`🔴 High Priority: ${stats.highPriority}`, 'red');
  log(`🟡 Medium Priority: ${stats.mediumPriority}`, 'yellow');
  log(`🟢 Low Priority: ${stats.lowPriority}`, 'green');
  
  // Display detailed results
  log('\n📋 DETAILED AUDIT RESULTS', 'cyan');
  log('-' * 80, 'blue');
  
  auditResults.forEach(result => {
    const statusIcon = result.status === 'COMPLETE' ? '✅' : result.status === 'PARTIAL' ? '⚠️' : '❌';
    const priorityColor = result.priority === 'HIGH' ? 'red' : result.priority === 'MEDIUM' ? 'yellow' : 'green';
    
    log(`\n${statusIcon} ${result.id}. ${result.name}`, 'bright');
    log(`   Priority: ${result.priority}`, priorityColor);
    log(`   Status: ${result.status}`, result.status === 'COMPLETE' ? 'green' : result.status === 'PARTIAL' ? 'yellow' : 'red');
    
    // Frontend details
    const frontendStatus = result.frontend.exists ? '✅' : '❌';
    log(`   Frontend: ${frontendStatus} ${result.frontend.exists ? 'EXISTS' : 'MISSING'}`, result.frontend.exists ? 'green' : 'red');
    
    if (result.frontend.exists) {
      log(`     - Enhanced Layout: ${result.frontend.hasEnhancedLayout ? '✅' : '❌'}`, result.frontend.hasEnhancedLayout ? 'green' : 'red');
      log(`     - API Integration: ${result.frontend.hasApiCalls ? '✅' : '❌'}`, result.frontend.hasApiCalls ? 'green' : 'red');
      log(`     - Error Handling: ${result.frontend.hasErrorHandling ? '✅' : '❌'}`, result.frontend.hasErrorHandling ? 'green' : 'red');
    }
    
    // Backend details
    const backendStatus = result.backend.exists ? '✅' : '❌';
    log(`   Backend: ${backendStatus} ${result.backend.exists ? 'EXISTS' : 'MISSING'}`, result.backend.exists ? 'green' : 'red');
    
    if (result.backend.exists) {
      log(`     - Routes: ${result.backend.hasRoutes ? '✅' : '❌'}`, result.backend.hasRoutes ? 'green' : 'red');
      log(`     - Validation: ${result.backend.hasValidation ? '✅' : '❌'}`, result.backend.hasValidation ? 'green' : 'red');
      log(`     - Error Handling: ${result.backend.hasErrorHandling ? '✅' : '❌'}`, result.backend.hasErrorHandling ? 'green' : 'red');
    }
  });
  
  return stats;
}

/**
 * Generate recommendations
 */
function generateRecommendations(auditResults, stats) {
  log('\n💡 RECOMMENDATIONS', 'cyan');
  log('-' * 40, 'blue');
  
  const incomplete = auditResults.filter(r => r.status !== 'COMPLETE');
  const highPriorityIncomplete = incomplete.filter(r => r.priority === 'HIGH');
  
  if (highPriorityIncomplete.length > 0) {
    log('\n🔴 HIGH PRIORITY ACTIONS NEEDED:', 'red');
    highPriorityIncomplete.forEach(result => {
      log(`   • Complete ${result.name}`, 'red');
      if (!result.frontend.exists) log(`     - Create frontend page: ${result.frontend}`, 'yellow');
      if (!result.backend.exists) log(`     - Create backend controller: ${result.backend}`, 'yellow');
    });
  }
  
  const partialImplementations = auditResults.filter(r => r.status === 'PARTIAL');
  if (partialImplementations.length > 0) {
    log('\n⚠️  PARTIAL IMPLEMENTATIONS TO FIX:', 'yellow');
    partialImplementations.forEach(result => {
      log(`   • Fix ${result.name}`, 'yellow');
      if (!result.frontend.exists) log(`     - Missing frontend component`, 'red');
      if (!result.backend.exists) log(`     - Missing backend controller`, 'red');
    });
  }
  
  log('\n🎯 NEXT STEPS:', 'green');
  log('   1. Complete remaining 4 admin functions', 'blue');
  log('   2. Test all frontend-backend connections', 'blue');
  log('   3. Verify user-facing integration', 'blue');
  log('   4. Implement MCP server infrastructure', 'blue');
  log('   5. Set up AI algorithm phase management', 'blue');
}

/**
 * Main audit function
 */
async function runAudit() {
  log('🚀 Starting Comprehensive Admin Panel Audit...', 'bright');
  
  const auditResults = [];
  
  log('\n🔍 Auditing admin functions...', 'cyan');
  
  for (const func of adminFunctions) {
    const result = auditFunction(func);
    auditResults.push(result);
    
    const statusIcon = result.status === 'COMPLETE' ? '✅' : result.status === 'PARTIAL' ? '⚠️' : '❌';
    log(`${statusIcon} ${func.id}. ${func.name}`, result.status === 'COMPLETE' ? 'green' : result.status === 'PARTIAL' ? 'yellow' : 'red');
  }
  
  const stats = generateReport(auditResults);
  generateRecommendations(auditResults, stats);
  
  log('\n🏁 Audit Complete!', 'bright');
  
  return { auditResults, stats };
}

// Run the audit
if (require.main === module) {
  runAudit().catch(error => {
    log('❌ Audit failed:', 'red');
    log(error.message, 'red');
    process.exit(1);
  });
}

module.exports = { runAudit, adminFunctions };
