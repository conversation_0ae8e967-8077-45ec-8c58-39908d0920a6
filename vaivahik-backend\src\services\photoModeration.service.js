// src/services/photoModeration.service.js

const fs = require('fs').promises;
const path = require('path');
const { PrismaClient } = require('@prisma/client');
const { spawn } = require('child_process');
const util = require('util');
const exec = util.promisify(require('child_process').exec);

// Initialize Prisma client
const prisma = new PrismaClient();

// Path to Python script
const PYTHON_SCRIPT = path.join(__dirname, 'run_pytorch_moderation.py');

// Service state
let isInitializing = false;
let initializationError = null;

/**
 * Photo Moderation Service
 * This service provides functions to moderate photo content in the application
 * It uses a PyTorch-based implementation for NSFW detection and face detection
 */
class PhotoModerationService {
  constructor() {
    this.initialize();
    console.log('Photo moderation service initialized with PyTorch backend');
  }

  /**
   * Initialize the service
   */
  async initialize() {
    if (isInitializing) {
      return;
    }

    isInitializing = true;
    try {
      // Check if Python is available
      try {
        await exec('python --version');
        console.log('Python is available');
      } catch (error) {
        console.warn('Python not found, photo moderation will run in mock mode');
      }

      isInitializing = false;
      console.log('Photo moderation service initialized successfully');
    } catch (error) {
      console.error('Error initializing photo moderation service:', error);
      initializationError = error;
      isInitializing = false;
    }
  }

  /**
   * Get moderation settings from database
   * @returns {Promise<Object>} Moderation settings
   */
  async getModerationSettings() {
    try {
      const settingsRecord = await prisma.systemConfig.findUnique({
        where: { configKey: 'photoModeration' }
      });

      if (settingsRecord) {
        return JSON.parse(settingsRecord.configValue);
      }

      // Default settings
      return {
        operationMode: 2, // 0: Manual, 1: Shadow, 2: Limited Auto, 3: Full Auto
        automationPercentage: 70,
        rejectExplicitContent: true,
        rejectNoFace: true,
        rejectMultipleFaces: true,
        rejectPoorQuality: true,
        nsfw_threshold: 0.7
      };
    } catch (error) {
      console.error('Error getting moderation settings:', error);
      // Return default settings on error
      return {
        operationMode: 0, // Manual mode on error
        automationPercentage: 0,
        rejectExplicitContent: true,
        rejectNoFace: true,
        rejectMultipleFaces: true,
        rejectPoorQuality: true,
        nsfw_threshold: 0.7
      };
    }
  }

  /**
   * Run PyTorch moderation on an image
   * @param {string} imagePath - Path to the image file
   * @param {string} photoId - Optional photo ID
   * @returns {Promise<Object>} Moderation result
   */
  async runPyTorchModeration(imagePath, photoId = null) {
    try {
      return new Promise((resolve, reject) => {
        const args = [PYTHON_SCRIPT, imagePath];
        if (photoId) {
          args.push(photoId);
        }

        const process = spawn('python', args);
        let output = '';
        let errorOutput = '';

        process.stdout.on('data', (data) => {
          output += data.toString();
        });

        process.stderr.on('data', (data) => {
          errorOutput += data.toString();
        });

        process.on('close', (code) => {
          if (code !== 0) {
            console.error(`Python process exited with code ${code}`);
            console.error(`Error output: ${errorOutput}`);

            // Return mock result on error
            return resolve(this.getMockModerationResult());
          }

          try {
            const result = JSON.parse(output);
            resolve(result);
          } catch (error) {
            console.error('Error parsing Python output:', error);
            console.error('Output:', output);

            // Return mock result on error
            resolve(this.getMockModerationResult());
          }
        });
      });
    } catch (error) {
      console.error('Error running PyTorch moderation:', error);
      return this.getMockModerationResult();
    }
  }

  /**
   * Get a mock moderation result (used when Python/PyTorch is not available)
   * @returns {Object} Mock moderation result
   */
  getMockModerationResult() {
    return {
      decision: 'APPROVED',
      flags: [],
      confidence: 95,
      details: {
        nsfw: {
          safe: true,
          nsfwProbability: 0.01,
          classifications: [
            { className: 'Drawing', probability: 0.01 },
            { className: 'Hentai', probability: 0.01 },
            { className: 'Neutral', probability: 0.95 },
            { className: 'Porn', probability: 0.01 },
            { className: 'Sexy', probability: 0.02 }
          ]
        },
        face: {
          faceCount: 1,
          multipleFaces: false,
          faceDetails: [{
            box: [100, 100, 200, 200],
            score: 0.99
          }]
        },
        quality: {
          quality: 'good',
          width: 1200,
          height: 800,
          resolution: 960000,
          brightness: 150,
          contrast: 50,
          issues: {
            lowResolution: false,
            tooDark: false,
            tooBright: false,
            lowContrast: false
          }
        }
      }
    };
  }

  /**
   * Moderate a photo using AI
   * @param {string} imagePath - Path to the image file
   * @param {string} photoId - Optional photo ID if already in database
   * @returns {Promise<Object>} Moderation result
   */
  async moderatePhoto(imagePath, photoId = null) {
    try {
      // Run PyTorch moderation
      return await this.runPyTorchModeration(imagePath, photoId);
    } catch (error) {
      console.error('Error in photo moderation:', error);
      return {
        decision: 'PENDING', // Default to manual review on error
        flags: ['moderation_error'],
        confidence: 0,
        details: { error: error.message }
      };
    }
  }
  /**
   * Process a photo through the moderation system
   * This is the main entry point for the moderation service
   * @param {string} imagePath - Path to the image file
   * @param {string} photoId - Optional photo ID if already in database
   * @returns {Promise<Object>} Processing result with decision
   */
  async processPhoto(imagePath, photoId = null) {
    try {
      // Get current moderation settings
      const settings = await this.getModerationSettings();

      // If in manual mode (0), skip AI processing
      if (settings.operationMode === 0) {
        return {
          decision: 'PENDING', // Always manual review in manual mode
          flags: ['manual_mode'],
          confidence: 0,
          details: { mode: 'Manual' }
        };
      }

      // In shadow mode (1), always return PENDING but still do analysis
      if (settings.operationMode === 1) {
        const aiResult = await this.moderatePhoto(imagePath, photoId);
        return {
          decision: 'PENDING', // Always manual review in shadow mode
          flags: aiResult.flags,
          confidence: aiResult.confidence,
          details: {
            ...aiResult.details,
            aiDecision: aiResult.decision,
            mode: 'Shadow'
          }
        };
      }

      // For limited auto mode (2) and full auto mode (3)
      const aiResult = await this.moderatePhoto(imagePath, photoId);

      // In limited auto mode, apply automation percentage
      if (settings.operationMode === 2) {
        // Randomly determine if this photo should be auto-processed
        const useAutomation = Math.random() * 100 < settings.automationPercentage;

        if (!useAutomation) {
          return {
            decision: 'PENDING',
            flags: aiResult.flags,
            confidence: aiResult.confidence,
            details: {
              ...aiResult.details,
              aiDecision: aiResult.decision,
              mode: 'Limited Auto (Manual Selected)'
            }
          };
        }
      }

      // For full auto mode (3) or limited auto with automation selected
      return aiResult;
    } catch (error) {
      console.error('Error processing photo:', error);
      return {
        decision: 'PENDING', // Default to manual review on error
        flags: ['processing_error'],
        confidence: 0,
        details: { error: error.message }
      };
    }
  }

  /**
   * Log moderation result to database
   * @param {string} photoId - Photo ID
   * @param {Object} result - Moderation result
   * @returns {Promise<Object>} Created log entry
   */
  async logModerationResult(photoId, result) {
    try {
      // Create a moderation log entry
      return await prisma.moderationLog.create({
        data: {
          contentType: 'PHOTO',
          contentId: photoId,
          photoId: photoId,
          decision: result.decision,
          flags: result.flags.join(','),
          confidence: result.confidence,
          details: JSON.stringify(result.details),
          aiDecision: result.decision,
          aiFlags: result.flags.join(','),
          aiConfidence: result.confidence,
          createdAt: new Date()
        }
      });
    } catch (error) {
      console.error('Error logging moderation result:', error);
      return null;
    }
  }
}

module.exports = new PhotoModerationService();
