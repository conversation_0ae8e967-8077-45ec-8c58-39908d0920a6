<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>API Discovery Tool</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      text-align: center;
      color: #333;
    }
    p {
      color: #666;
      text-align: center;
    }
    input[type="text"] {
      width: 100%;
      padding: 10px;
      margin: 10px 0;
      box-sizing: border-box;
    }
    .button-group {
      display: flex;
      gap: 10px;
      margin: 20px 0;
    }
    button {
      flex: 1;
      padding: 10px;
      cursor: pointer;
      border: none;
      border-radius: 4px;
      font-weight: bold;
    }
    .check-button {
      background-color: #2196F3;
      color: white;
    }
    .discover-button {
      background-color: #673AB7;
      color: white;
    }
    .test-button {
      background-color: #4CAF50;
      color: white;
    }
    .clear-button {
      background-color: #F44336;
      color: white;
    }
    .nav-links {
      display: flex;
      justify-content: space-between;
      margin: 20px 0;
    }
    .nav-links a {
      text-decoration: none;
      color: #2196F3;
    }
    .results {
      margin-top: 20px;
      border-top: 1px solid #ddd;
      padding-top: 20px;
    }
    .result-item {
      padding: 10px;
      margin: 5px 0;
      border-radius: 4px;
    }
    .info {
      background-color: #f0f0f0;
    }
    .success {
      background-color: #E8F5E9;
      color: #2E7D32;
    }
    .warning {
      background-color: #FFF8E1;
      color: #F57F17;
    }
    .error {
      background-color: #FFEBEE;
      color: #C62828;
    }
    .server-status {
      text-align: right;
      margin-bottom: 10px;
    }
    .status-badge {
      display: inline-block;
      padding: 5px 10px;
      border-radius: 4px;
      font-weight: bold;
    }
    .status-online {
      background-color: #4CAF50;
      color: white;
    }
    .status-offline {
      background-color: #F44336;
      color: white;
    }
    .status-error {
      background-color: #FF9800;
      color: white;
    }
    .timestamp {
      font-size: 0.8em;
      color: #999;
      margin-right: 10px;
    }
  </style>
</head>
<body>
  <h1>API Discovery Tool</h1>
  <p>This tool helps you identify working API endpoints for your backend.</p>
  <p>If you're seeing 404 errors, it means the endpoints don't exist or aren't accessible.</p>

  <input type="text" id="apiBaseUrl" placeholder="http://localhost:3000" value="http://localhost:3000">
  <p><small>Note: If you're opening this file directly (file://), you need to enter the full URL where your API is running.</small></p>

  <div class="button-group">
    <button class="check-button" id="checkServerBtn">Check Server Status</button>
    <button class="discover-button" id="discoverEndpointsBtn">Discover GET Endpoints</button>
    <button class="test-button" id="testEndpointsBtn">Test PUT/POST Endpoints</button>
    <button class="clear-button" id="clearResultsBtn">Clear Results</button>
  </div>

  <div class="nav-links">
    <a href="#" id="viewerLink">Go to API Viewer</a>
    <a href="#" id="dashboardLink">Back to Dashboard</a>
  </div>

  <div class="server-status" id="serverStatusContainer" style="display: none;">
    Server: <span class="status-badge" id="serverStatus"></span>
  </div>

  <div class="results" id="resultsContainer">
    <h2>Results</h2>
    <div id="results"></div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const apiBaseUrlInput = document.getElementById('apiBaseUrl');
      const checkServerBtn = document.getElementById('checkServerBtn');
      const discoverEndpointsBtn = document.getElementById('discoverEndpointsBtn');
      const testEndpointsBtn = document.getElementById('testEndpointsBtn');
      const clearResultsBtn = document.getElementById('clearResultsBtn');
      const viewerLink = document.getElementById('viewerLink');
      const dashboardLink = document.getElementById('dashboardLink');
      const serverStatusContainer = document.getElementById('serverStatusContainer');
      const serverStatus = document.getElementById('serverStatus');
      const resultsContainer = document.getElementById('resultsContainer');
      const results = document.getElementById('results');

      let isLoading = false;

      // Initialize with the current host
      if (window.location.origin && window.location.origin !== 'file://') {
        apiBaseUrlInput.value = window.location.origin;
      }

      function addResult(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const resultItem = document.createElement('div');
        resultItem.className = `result-item ${type}`;

        const timestampSpan = document.createElement('span');
        timestampSpan.className = 'timestamp';
        timestampSpan.textContent = timestamp;

        const messageSpan = document.createElement('span');
        messageSpan.innerHTML = message;

        resultItem.appendChild(timestampSpan);
        resultItem.appendChild(messageSpan);

        results.appendChild(resultItem);
        resultItem.scrollIntoView({ behavior: 'smooth' });
      }

      function clearResults() {
        results.innerHTML = '';
      }

      function setServerStatusUI(status) {
        serverStatusContainer.style.display = 'block';
        serverStatus.textContent = status.charAt(0).toUpperCase() + status.slice(1);
        serverStatus.className = `status-badge status-${status}`;
      }

      function setLoading(loading) {
        isLoading = loading;
        checkServerBtn.disabled = loading;
        discoverEndpointsBtn.disabled = loading;
        testEndpointsBtn.disabled = loading;
        apiBaseUrlInput.disabled = loading;
      }

      async function checkServerStatus() {
        setLoading(true);
        clearResults();
        const apiBaseUrl = apiBaseUrlInput.value;
        addResult(`Checking server status at ${apiBaseUrl}...`);

        try {
          // Try a simple fetch to see if the server responds
          const response = await fetch(`${apiBaseUrl}/api/hello`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            },
            mode: 'cors'
          });

          if (response.ok) {
            setServerStatusUI('online');
            addResult(`✅ Backend server is running and API is accessible!`, 'success');
            addResult(`Found working endpoint: ${apiBaseUrl}/api/hello`, 'success');

            // Try a more complex endpoint
            try {
              const adminResponse = await fetch(`${apiBaseUrl}/api/admin/premium-plans`, {
                method: 'GET',
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json'
                },
                mode: 'cors'
              });

              if (adminResponse.ok) {
                addResult(`✅ Admin API is also accessible!`, 'success');
              } else {
                addResult(`⚠️ Admin API returned status ${adminResponse.status}`, 'warning');
              }
            } catch (adminError) {
              addResult(`⚠️ Could not access Admin API: ${adminError.message}`, 'warning');
            }
          } else if (response.status === 401 || response.status === 403) {
            setServerStatusUI('error');
            addResult(`⚠️ Backend server is running but authentication required (${response.status})`, 'warning');
            addResult(`Try logging in to access protected endpoints.`, 'info');
          } else {
            setServerStatusUI('error');
            addResult(`⚠️ Backend server responded with status ${response.status}`, 'error');
            addResult(`The server is running but there might be issues with the API.`, 'info');
          }
        } catch (error) {
          setServerStatusUI('offline');
          addResult(`❌ Could not connect to backend server: ${error.message}`, 'error');
          addResult(`Make sure your backend server is running at ${apiBaseUrl}`, 'info');
          addResult(`Common issues:
1. Backend server is not started
2. Backend is running on a different port
3. CORS is not configured correctly
4. Network connectivity issues`, 'info');
        } finally {
          setLoading(false);
        }
      }

      async function discoverEndpoints() {
        setLoading(true);
        clearResults();
        const apiBaseUrl = apiBaseUrlInput.value;
        addResult(`Discovering GET endpoints at ${apiBaseUrl}...`);

        const commonEndpoints = [
          '/api/hello',
          '/api/admin/users',
          '/api/admin/premium-plans',
          '/api/admin/verification-queue',
          '/api/admin/reported-profiles',
          '/api/admin/features',
          '/api/admin/success-stories',
          '/api/admin/photo-moderation',
          '/api/admin/dashboard',
          '/api/admin/settings',
          '/api/users/profile',
          '/api/users/matches',
          '/api/users/preferences',
          '/api/users/documents',
          '/api/users/subscription'
        ];

        let successCount = 0;

        for (const endpoint of commonEndpoints) {
          try {
            const url = `${apiBaseUrl}${endpoint}`;
            addResult(`Testing: ${url}`, 'info');

            const response = await fetch(url, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
              },
              mode: 'cors'
            });

            if (response.ok) {
              successCount++;
              addResult(`✅ ${url} - Status ${response.status}`, 'success');

              try {
                const data = await response.json();
                const dataPreview = JSON.stringify(data).substring(0, 100) + (JSON.stringify(data).length > 100 ? '...' : '');
                addResult(`Response: ${dataPreview}`, 'info');
              } catch (parseError) {
                addResult(`Could not parse response as JSON`, 'warning');
              }
            } else {
              addResult(`❌ ${url} - Status ${response.status}`, 'error');
            }
          } catch (error) {
            addResult(`❌ Error testing ${apiBaseUrl}${endpoint}: ${error.message}`, 'error');
          }
        }

        addResult(`Discovery complete. Found ${successCount} working endpoints out of ${commonEndpoints.length}.`, successCount > 0 ? 'success' : 'warning');
        setLoading(false);
      }

      async function testUpdateEndpoints() {
        setLoading(true);
        clearResults();
        const apiBaseUrl = apiBaseUrlInput.value;
        addResult(`Testing PUT/POST endpoints at ${apiBaseUrl}...`);

        const testEndpoints = [
          {
            method: 'POST',
            url: '/api/admin/users',
            payload: { name: 'Test User', email: '<EMAIL>' }
          },
          {
            method: 'PUT',
            url: '/api/admin/users/1',
            payload: { name: 'Updated User', email: '<EMAIL>' }
          },
          {
            method: 'POST',
            url: '/api/admin/verification-queue/1/approve',
            payload: { reason: 'Documents verified' }
          },
          {
            method: 'POST',
            url: '/api/admin/verification-queue/1/reject',
            payload: { reason: 'Invalid documents' }
          },
          {
            method: 'POST',
            url: '/api/admin/reported-profiles/1/resolve',
            payload: { action: 'warning', message: 'Please follow community guidelines' }
          }
        ];

        let successCount = 0;

        for (const endpoint of testEndpoints) {
          try {
            const url = `${apiBaseUrl}${endpoint.url}`;
            addResult(`Testing ${endpoint.method}: ${url}`, 'info');
            addResult(`Payload: ${JSON.stringify(endpoint.payload)}`, 'info');

            const response = await fetch(url, {
              method: endpoint.method,
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
              },
              body: JSON.stringify(endpoint.payload),
              mode: 'cors'
            });

            if (response.ok) {
              successCount++;
              addResult(`✅ ${endpoint.method} ${url} - Status ${response.status}`, 'success');

              try {
                const data = await response.json();
                const dataPreview = JSON.stringify(data).substring(0, 100) + (JSON.stringify(data).length > 100 ? '...' : '');
                addResult(`Response: ${dataPreview}`, 'info');
              } catch (parseError) {
                addResult(`Could not parse response as JSON`, 'warning');
              }
            } else {
              addResult(`❌ ${endpoint.method} ${url} - Status ${response.status}`, 'error');
            }
          } catch (error) {
            addResult(`❌ Error testing ${endpoint.method} ${apiBaseUrl}${endpoint.url}: ${error.message}`, 'error');
          }
        }

        addResult(`Testing complete. ${successCount} out of ${testEndpoints.length} endpoints worked.`, successCount > 0 ? 'success' : 'warning');
        setLoading(false);
      }

      checkServerBtn.addEventListener('click', checkServerStatus);
      discoverEndpointsBtn.addEventListener('click', discoverEndpoints);
      testEndpointsBtn.addEventListener('click', testUpdateEndpoints);
      clearResultsBtn.addEventListener('click', clearResults);

      viewerLink.addEventListener('click', function(e) {
        e.preventDefault();
        // Check if we're running from a file:// URL
        if (window.location.protocol === 'file:') {
          alert('API Viewer is not available when running from a local file. Please run this from a web server.');
        } else {
          window.location.href = '/api-viewer';
        }
      });

      dashboardLink.addEventListener('click', function(e) {
        e.preventDefault();
        // Check if we're running from a file:// URL
        if (window.location.protocol === 'file:') {
          alert('Dashboard is not available when running from a local file. Please run this from a web server.');
        } else {
          window.location.href = '/dashboard';
        }
      });

      // Auto-check server status on load
      setTimeout(checkServerStatus, 500);
    });
  </script>
</body>
</html>
