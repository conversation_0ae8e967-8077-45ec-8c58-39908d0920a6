/* Reported Profiles Page Specific Styles */

/* Report Details Modal */
.report-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.user-profile-section {
  margin-bottom: 20px;
}

.user-profile-header {
  display: flex;
  align-items: center;
  gap: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.user-avatar.large {
  width: 80px;
  height: 80px;
  font-size: 2rem;
  background-color: #7c4dff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.user-avatar.large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.user-profile-info {
  flex: 1;
}

.user-profile-info h3 {
  margin: 0 0 5px 0;
  font-size: 1.3rem;
  color: #333;
}

.user-meta {
  display: flex;
  gap: 8px;
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 8px;
}

.report-status {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 5px;
}

.status-badge {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-badge.pending {
  background-color: #fff3e0;
  color: #e65100;
  border-left: 3px solid #ff9800;
}

.status-badge.resolved {
  background-color: #e8f5e9;
  color: #2e7d32;
  border-left: 3px solid #4caf50;
}

.status-badge.dismissed {
  background-color: #f5f5f5;
  color: #616161;
  border-left: 3px solid #9e9e9e;
}

.report-date {
  font-size: 0.8rem;
  color: #666;
}

/* Tab Content Styles */
.report-tabs {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tab-content {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.tab-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.report-info-grid,
.user-details-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.detail-item {
  margin-bottom: 10px;
}

.detail-label {
  font-weight: 500;
  color: #666;
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.detail-value {
  color: #333;
}

/* Evidence Grid */
.evidence-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 15px;
}

.evidence-item {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.evidence-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.evidence-preview {
  height: 140px;
  overflow: hidden;
}

.evidence-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.evidence-info {
  padding: 10px;
  background-color: #f9f9f9;
}

.evidence-type {
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.evidence-date {
  font-size: 0.8rem;
  color: #666;
}

.no-evidence {
  grid-column: 1 / -1;
  padding: 15px;
  text-align: center;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 8px;
}

/* Action Section */
.action-section {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.action-buttons-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.action-description {
  margin-bottom: 15px;
}

.action-options {
  display: flex;
  gap: 20px;
}

.action-option {
  flex: 1;
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.action-option h5 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #333;
}

.action-inputs {
  margin-top: 15px;
}

.form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  margin-bottom: 10px;
}

.feature-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 10px;
  margin: 10px 0;
}

.feature-checkbox {
  display: flex;
  align-items: center;
  gap: 5px;
}

.mt-2 {
  margin-top: 10px;
}

.mt-3 {
  margin-top: 15px;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .report-info-grid,
  .user-details-grid {
    grid-template-columns: 1fr;
  }
  
  .action-options {
    flex-direction: column;
  }
  
  .user-profile-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .evidence-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  }
}
