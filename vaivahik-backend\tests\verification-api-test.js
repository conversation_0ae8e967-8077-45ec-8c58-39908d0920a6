// tests/verification-api-test.js

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// Configuration
const API_BASE_URL = 'http://localhost:8080'; // Updated to match the server port
let authToken = null;
let userId = null;
let documentId = null;

// Test user credentials
const testUser = {
  phone: '+919876543210',
  email: '<EMAIL>',
  password: 'TestPassword123'
};

// Helper function to log test results
function logTest(testName, success, details = null) {
  console.log(`\n${success ? '✅' : '❌'} ${testName}`);
  if (details) {
    console.log(details);
  }
}

// Test functions
async function registerTestUser() {
  try {
    const response = await axios.post(`${API_BASE_URL}/api/auth/register`, testUser);
    userId = response.data.user.id;
    logTest('Register test user', true, { userId });
    return true;
  } catch (error) {
    logTest('Register test user', false, error.response?.data || error.message);
    return false;
  }
}

async function loginTestUser() {
  try {
    const response = await axios.post(`${API_BASE_URL}/api/auth/login`, {
      phone: testUser.phone,
      password: testUser.password
    });
    authToken = response.data.token;
    logTest('Login test user', true, { token: authToken.substring(0, 20) + '...' });
    return true;
  } catch (error) {
    logTest('Login test user', false, error.response?.data || error.message);
    return false;
  }
}

async function uploadVerificationDocument() {
  try {
    // Create a test document file
    const testFilePath = path.join(__dirname, 'test-document.jpg');
    fs.writeFileSync(testFilePath, 'Test document content');

    const formData = new FormData();
    formData.append('document', fs.createReadStream(testFilePath));
    formData.append('documentType', 'AADHAR_CARD');

    const response = await axios.post(
      `${API_BASE_URL}/api/users/verification/documents`,
      formData,
      {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${authToken}`
        }
      }
    );

    documentId = response.data.document.id;
    logTest('Upload verification document', true, response.data);

    // Clean up test file
    fs.unlinkSync(testFilePath);
    return true;
  } catch (error) {
    logTest('Upload verification document', false, error.response?.data || error.message);
    return false;
  }
}

async function getUserVerificationDocuments() {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/api/users/verification/documents`,
      {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      }
    );
    logTest('Get user verification documents', true, response.data);
    return true;
  } catch (error) {
    logTest('Get user verification documents', false, error.response?.data || error.message);
    return false;
  }
}

async function approveVerificationDocument() {
  try {
    // First, we need to login as admin
    // This is a simplified example - in a real test, you'd use actual admin credentials
    const adminLoginResponse = await axios.post(`${API_BASE_URL}/api/admin/login`, {
      email: '<EMAIL>',
      password: 'AdminPassword123'
    });
    const adminToken = adminLoginResponse.data.token;

    const response = await axios.put(
      `${API_BASE_URL}/api/admin/verification/documents/${documentId}/review`,
      {
        status: 'APPROVED'
      },
      {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      }
    );
    logTest('Approve verification document', true, response.data);
    return true;
  } catch (error) {
    logTest('Approve verification document', false, error.response?.data || error.message);
    return false;
  }
}

async function deleteVerificationDocument() {
  try {
    const response = await axios.delete(
      `${API_BASE_URL}/api/users/verification/documents/${documentId}`,
      {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      }
    );
    logTest('Delete verification document', true, { status: response.status });
    return true;
  } catch (error) {
    logTest('Delete verification document', false, error.response?.data || error.message);
    return false;
  }
}

async function cleanupTestUser() {
  try {
    // This would require admin access to delete a user
    // In a real test environment, you'd have a way to clean up test data
    console.log('\n🧹 Note: In a real test environment, you would clean up the test user here.');
    return true;
  } catch (error) {
    console.error('Error cleaning up test user:', error);
    return false;
  }
}

// Run all tests
async function runTests() {
  console.log('🧪 Starting Verification API Tests...');

  try {
    // Register and login
    await registerTestUser();
    await loginTestUser();

    // Test document operations
    await uploadVerificationDocument();
    await getUserVerificationDocuments();
    await approveVerificationDocument();
    await deleteVerificationDocument();

    // Cleanup
    await cleanupTestUser();

    console.log('\n🏁 Verification API Tests completed!');
  } catch (error) {
    console.error('\n❌ Test suite error:', error);
  }
}

// Run the tests if this file is executed directly
if (require.main === module) {
  runTests();
}
