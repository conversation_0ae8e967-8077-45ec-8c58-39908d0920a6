/**
 * MSG91 Configuration Test Script
 *
 * This script tests your MSG91 configuration by sending a test OTP.
 * It helps verify that your API key, sender ID, template ID, and PE ID are correctly configured.
 *
 * Usage:
 * node scripts/test-msg91-config.js <phone_number>
 *
 * Example:
 * node scripts/test-msg91-config.js 9123456789
 */

// Load environment variables
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });

// Log environment variables for debugging
console.log('Environment variables loaded:');
console.log(`MSG91_API_KEY: ${process.env.MSG91_API_KEY ? '✅ Set' : '❌ Missing'}`);
console.log(`MSG91_SENDER_ID: ${process.env.MSG91_SENDER_ID ? '✅ Set' : '❌ Missing'}`);
console.log(`MSG91_DLT_TEMPLATE_ID: ${process.env.MSG91_DLT_TEMPLATE_ID ? '✅ Set' : '❌ Missing'}`);
console.log(`MSG91_DLT_PE_ID: ${process.env.MSG91_DLT_PE_ID ? '✅ Set' : '❌ Missing'}`);
console.log(`MSG91_OTP_TEMPLATE: ${process.env.MSG91_OTP_TEMPLATE ? '✅ Set' : '❌ Missing'}`);

// Import the MSG91 service
const msg91Service = require('../src/services/sms/msg91.service');

// Check if phone number is provided
const phoneNumber = process.argv[2];
if (!phoneNumber) {
  console.error('Please provide a phone number as an argument.');
  console.error('Usage: node scripts/test-msg91-config.js <phone_number>');
  process.exit(1);
}

// Display configuration
console.log('\n=== MSG91 Configuration ===');
console.log(`API Key: ${process.env.MSG91_API_KEY ? '✅ Set' : '❌ Missing'}`);
console.log(`Sender ID: ${process.env.MSG91_SENDER_ID || '❌ Missing'}`);
console.log(`DLT Template ID: ${process.env.MSG91_DLT_TEMPLATE_ID || '❌ Missing'}`);
console.log(`DLT PE ID: ${process.env.MSG91_DLT_PE_ID || '❌ Missing'}`);
console.log(`OTP Template: ${process.env.MSG91_OTP_TEMPLATE || '❌ Missing'}`);
console.log('===========================\n');

// Initialize the MSG91 service with your configuration
msg91Service.initialize({
  apiKey: process.env.MSG91_API_KEY,
  senderId: process.env.MSG91_SENDER_ID,
  dltTemplateId: process.env.MSG91_DLT_TEMPLATE_ID,
  dltPeId: process.env.MSG91_DLT_PE_ID,
  otpTemplate: process.env.MSG91_OTP_TEMPLATE
});

// Format the phone number
const formattedPhone = msg91Service.formatPhoneNumber(phoneNumber);
console.log(`Formatted phone number: ${formattedPhone}`);

// Generate a test OTP
const testOtp = msg91Service.generateOtp();
console.log(`Generated OTP: ${testOtp}`);

// Send the test OTP
async function sendTestOtp() {
  console.log(`\nSending test OTP to ${formattedPhone}...`);

  try {
    const result = await msg91Service.sendOtp(formattedPhone, testOtp);

    if (result.success) {
      console.log('\n✅ OTP sent successfully!');
      console.log('Response:', JSON.stringify(result.data, null, 2));

      // Display the expected SMS content
      console.log('\nExpected SMS content:');
      console.log(`${process.env.MSG91_OTP_TEMPLATE.replace('##OTP##', testOtp)}`);

      // Prompt for verification
      console.log('\nPlease check your phone for the OTP message.');
      console.log('Did you receive the OTP with the correct format? (Y/n)');

      // Set up readline interface for user input
      const readline = require('readline').createInterface({
        input: process.stdin,
        output: process.stdout
      });

      readline.question('', (answer) => {
        if (answer.toLowerCase() === 'n') {
          console.log('\n❌ There might be an issue with your template configuration.');
          console.log('Please check the following:');
          console.log('1. Verify that your DLT template ID is correct');
          console.log('2. Make sure your template is approved and active');
          console.log('3. Confirm that the template format matches what you expect');
          console.log('4. Check if the var1 parameter is correctly mapping to ##OTP##');
        } else {
          console.log('\n✅ Great! Your MSG91 configuration is working correctly.');
        }

        readline.close();
        process.exit(0);
      });
    } else {
      console.log('\n❌ Failed to send OTP:');
      console.log('Error:', result.message);
      console.log('Response data:', JSON.stringify(result.data, null, 2));

      console.log('\nPossible issues:');
      console.log('1. Invalid API key');
      console.log('2. Invalid sender ID');
      console.log('3. Invalid or unapproved DLT template ID');
      console.log('4. Invalid PE ID');
      console.log('5. Template variables not correctly mapped');

      process.exit(1);
    }
  } catch (error) {
    console.error('\n❌ Error sending OTP:', error);
    process.exit(1);
  }
}

// Run the test
sendTestOtp();
