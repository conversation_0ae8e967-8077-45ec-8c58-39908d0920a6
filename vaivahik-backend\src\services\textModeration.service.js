// src/services/textModeration.service.js

/**
 * Text Moderation Service
 * This service provides functions to moderate text content in the application
 * It includes profanity filtering, sensitive content detection, and spam detection
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Default banned words list (will be extended from database)
let bannedWords = [
    // Profanity
    'fuck', 'shit', 'asshole', 'bitch', 'bastard', 'cunt', 'dick', 'pussy', 'cock', 'whore',
    // Sexual content
    'sex', 'porn', 'nude', 'naked', 'boobs', 'penis', 'vagina', 'masturbate',
    // Violence
    'kill', 'murder', 'suicide', 'rape', 'terrorist',
    // Discrimination
    'nigger', 'faggot', 'retard', 'spastic', 'chink', 'paki',
    // Hindi/Marathi profanity
    'chutiya', 'behenchod', 'madarchod', 'bhosadike', 'lund', 'randi', 'gandu'
];

// Moderation settings
let moderationSettings = {
    enabled: true,
    strictness: 'medium', // 'low', 'medium', 'high'
    autoReject: true,
    maskProfanity: true,
    maskCharacter: '*',
    allowedDomains: ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'],
    maxRepeatedCharacters: 5,
    maxConsecutiveCapitals: 10,
    spamThreshold: 0.7,
    phoneNumberRegex: /(\+?\d{1,3}[- ]?)?\(?\d{3}\)?[- ]?\d{3}[- ]?\d{4}/g,
    emailRegex: /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g,
    urlRegex: /(https?:\/\/[^\s]+)/g
};

// Initialize the service
async function initialize() {
    try {
        // Load banned words from database
        const bannedWordsConfig = await prisma.systemConfig.findUnique({
            where: { configKey: 'BANNED_WORDS' }
        });
        
        if (bannedWordsConfig) {
            try {
                const customBannedWords = JSON.parse(bannedWordsConfig.configValue);
                if (Array.isArray(customBannedWords) && customBannedWords.length > 0) {
                    bannedWords = [...new Set([...bannedWords, ...customBannedWords])];
                }
            } catch (error) {
                console.error('Error parsing banned words from database:', error);
            }
        }
        
        // Load moderation settings from database
        const moderationConfig = await prisma.systemConfig.findUnique({
            where: { configKey: 'TEXT_MODERATION_SETTINGS' }
        });
        
        if (moderationConfig) {
            try {
                const customSettings = JSON.parse(moderationConfig.configValue);
                moderationSettings = { ...moderationSettings, ...customSettings };
            } catch (error) {
                console.error('Error parsing moderation settings from database:', error);
            }
        }
        
        return true;
    } catch (error) {
        console.error('Error initializing text moderation service:', error);
        return false;
    }
}

/**
 * Check if text contains banned words
 * @param {string} text - Text to check
 * @returns {Object} Result with banned words found
 */
function checkProfanity(text) {
    if (!text) return { hasProfanity: false, words: [] };
    
    // Convert to lowercase for case-insensitive matching
    const lowerText = text.toLowerCase();
    
    // Find all banned words in the text
    const foundWords = bannedWords.filter(word => {
        // Create a regex that matches the word as a whole word
        const regex = new RegExp(`\\b${word}\\b`, 'i');
        return regex.test(lowerText);
    });
    
    return {
        hasProfanity: foundWords.length > 0,
        words: foundWords
    };
}

/**
 * Check if text contains contact information (phone, email, etc.)
 * @param {string} text - Text to check
 * @returns {Object} Result with contact info found
 */
function checkContactInfo(text) {
    if (!text) return { hasContactInfo: false, items: [] };
    
    const items = [];
    
    // Check for phone numbers
    const phoneMatches = text.match(moderationSettings.phoneNumberRegex);
    if (phoneMatches) {
        items.push(...phoneMatches.map(match => ({ type: 'phone', value: match })));
    }
    
    // Check for email addresses
    const emailMatches = text.match(moderationSettings.emailRegex);
    if (emailMatches) {
        // Filter out allowed domains
        const filteredEmails = emailMatches.filter(email => {
            // If no allowed domains are specified, all emails are considered contact info
            if (!moderationSettings.allowedDomains || moderationSettings.allowedDomains.length === 0) {
                return true;
            }
            
            // Check if the email domain is in the allowed list
            const domain = email.split('@')[1]?.toLowerCase();
            return domain && !moderationSettings.allowedDomains.includes(domain);
        });
        
        if (filteredEmails.length > 0) {
            items.push(...filteredEmails.map(match => ({ type: 'email', value: match })));
        }
    }
    
    // Check for URLs
    const urlMatches = text.match(moderationSettings.urlRegex);
    if (urlMatches) {
        items.push(...urlMatches.map(match => ({ type: 'url', value: match })));
    }
    
    return {
        hasContactInfo: items.length > 0,
        items
    };
}

/**
 * Check if text looks like spam
 * @param {string} text - Text to check
 * @returns {Object} Result with spam score
 */
function checkSpam(text) {
    if (!text) return { isSpam: false, score: 0, reasons: [] };
    
    let spamScore = 0;
    const reasons = [];
    
    // Check for ALL CAPS
    const capsPercentage = (text.match(/[A-Z]/g) || []).length / text.length;
    if (capsPercentage > 0.7 && text.length > 5) {
        spamScore += 0.3;
        reasons.push('excessive_capitals');
    }
    
    // Check for repeated characters (e.g., "hellooooooo")
    const repeatedCharsRegex = new RegExp(`(.)\\1{${moderationSettings.maxRepeatedCharacters},}`, 'g');
    if (repeatedCharsRegex.test(text)) {
        spamScore += 0.2;
        reasons.push('repeated_characters');
    }
    
    // Check for repeated punctuation (e.g., "hello!!!!!!!")
    if (/(\!|\?|\.){3,}/.test(text)) {
        spamScore += 0.1;
        reasons.push('repeated_punctuation');
    }
    
    // Check for excessive emojis
    const emojiRegex = /[\u{1F300}-\u{1F6FF}\u{1F900}-\u{1F9FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu;
    const emojiCount = (text.match(emojiRegex) || []).length;
    if (emojiCount > 5 || (emojiCount > 0 && emojiCount / text.length > 0.2)) {
        spamScore += 0.2;
        reasons.push('excessive_emojis');
    }
    
    // Check for message length (very short messages are often spam)
    if (text.length < 3) {
        spamScore += 0.1;
        reasons.push('very_short_message');
    }
    
    // Check for very long messages (often copy-pasted spam)
    if (text.length > 500) {
        spamScore += 0.1;
        reasons.push('very_long_message');
    }
    
    return {
        isSpam: spamScore >= moderationSettings.spamThreshold,
        score: spamScore,
        reasons
    };
}

/**
 * Mask profanity in text
 * @param {string} text - Text to mask
 * @param {Array} words - Words to mask
 * @returns {string} Masked text
 */
function maskProfanity(text, words) {
    if (!text || !words || words.length === 0) return text;
    
    let maskedText = text;
    
    words.forEach(word => {
        const regex = new RegExp(`\\b${word}\\b`, 'gi');
        maskedText = maskedText.replace(regex, match => 
            moderationSettings.maskCharacter.repeat(match.length)
        );
    });
    
    return maskedText;
}

/**
 * Moderate text content
 * @param {string} text - Text to moderate
 * @param {Object} options - Moderation options
 * @returns {Object} Moderation result
 */
async function moderateText(text, options = {}) {
    // Merge default settings with options
    const settings = {
        ...moderationSettings,
        ...options
    };
    
    // Skip moderation if disabled
    if (!settings.enabled) {
        return {
            isApproved: true,
            moderatedText: text,
            flags: [],
            details: { skipped: true }
        };
    }
    
    // Initialize result
    const result = {
        isApproved: true,
        moderatedText: text,
        flags: [],
        details: {}
    };
    
    // Check for profanity
    const profanityCheck = checkProfanity(text);
    if (profanityCheck.hasProfanity) {
        result.flags.push('profanity');
        result.details.profanity = profanityCheck;
        
        // Apply strictness rules
        if (settings.strictness === 'high' || 
            (settings.strictness === 'medium' && profanityCheck.words.length > 1)) {
            result.isApproved = false;
        }
        
        // Mask profanity if enabled
        if (settings.maskProfanity) {
            result.moderatedText = maskProfanity(text, profanityCheck.words);
        }
    }
    
    // Check for contact information
    const contactCheck = checkContactInfo(text);
    if (contactCheck.hasContactInfo) {
        result.flags.push('contact_info');
        result.details.contactInfo = contactCheck;
        
        // Apply strictness rules
        if (settings.strictness === 'high' || settings.strictness === 'medium') {
            result.isApproved = false;
        }
    }
    
    // Check for spam
    const spamCheck = checkSpam(text);
    if (spamCheck.isSpam) {
        result.flags.push('spam');
        result.details.spam = spamCheck;
        
        // Apply strictness rules
        if (settings.strictness === 'high' || 
            (settings.strictness === 'medium' && spamCheck.score > 0.8)) {
            result.isApproved = false;
        }
    }
    
    // Log moderation result if not approved
    if (!result.isApproved) {
        try {
            await prisma.moderationLog.create({
                data: {
                    contentType: 'MESSAGE',
                    contentId: options.messageId || null,
                    userId: options.userId || null,
                    decision: 'REJECTED',
                    flags: result.flags.join(','),
                    details: JSON.stringify(result.details)
                }
            });
        } catch (error) {
            console.error('Error logging moderation result:', error);
        }
    }
    
    return result;
}

/**
 * Get moderation settings
 * @returns {Object} Current moderation settings
 */
function getModerationSettings() {
    return { ...moderationSettings };
}

/**
 * Update moderation settings
 * @param {Object} newSettings - New settings to apply
 * @returns {Object} Updated settings
 */
async function updateModerationSettings(newSettings) {
    try {
        // Merge new settings with existing ones
        moderationSettings = {
            ...moderationSettings,
            ...newSettings
        };
        
        // Save to database
        await prisma.systemConfig.upsert({
            where: { configKey: 'TEXT_MODERATION_SETTINGS' },
            update: {
                configValue: JSON.stringify(moderationSettings)
            },
            create: {
                configKey: 'TEXT_MODERATION_SETTINGS',
                configValue: JSON.stringify(moderationSettings),
                description: 'Text moderation settings'
            }
        });
        
        return { ...moderationSettings };
    } catch (error) {
        console.error('Error updating moderation settings:', error);
        throw error;
    }
}

/**
 * Update banned words list
 * @param {Array} words - New banned words list
 * @returns {Array} Updated banned words list
 */
async function updateBannedWords(words) {
    try {
        if (!Array.isArray(words)) {
            throw new Error('Banned words must be an array');
        }
        
        // Update the list
        bannedWords = [...new Set(words)];
        
        // Save to database
        await prisma.systemConfig.upsert({
            where: { configKey: 'BANNED_WORDS' },
            update: {
                configValue: JSON.stringify(bannedWords)
            },
            create: {
                configKey: 'BANNED_WORDS',
                configValue: JSON.stringify(bannedWords),
                description: 'List of banned words for text moderation'
            }
        });
        
        return [...bannedWords];
    } catch (error) {
        console.error('Error updating banned words:', error);
        throw error;
    }
}

/**
 * Get banned words list
 * @returns {Array} Current banned words list
 */
function getBannedWords() {
    return [...bannedWords];
}

// Initialize the service when the module is loaded
initialize().then(success => {
    if (success) {
        console.log('Text moderation service initialized successfully');
    } else {
        console.error('Failed to initialize text moderation service');
    }
});

module.exports = {
    moderateText,
    getModerationSettings,
    updateModerationSettings,
    getBannedWords,
    updateBannedWords,
    initialize
};
