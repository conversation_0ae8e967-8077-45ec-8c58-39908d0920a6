import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  CircularProgress,
  Grid,
  Paper,
  Chip,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Pagination,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Tooltip,
  Alert,
  Snackbar
} from '@mui/material';
import { styled } from '@mui/material/styles';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import RefreshIcon from '@mui/icons-material/Refresh';
import AutorenewIcon from '@mui/icons-material/Autorenew';
import ZoomInIcon from '@mui/icons-material/ZoomIn';

// Styled components
const StyledCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
}));

const PhotoCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  position: 'relative',
  height: '100%',
  display: 'flex',
  flexDirection: 'column'
}));

const PhotoImage = styled('img')(({ theme }) => ({
  width: '100%',
  height: '200px',
  objectFit: 'cover',
  borderRadius: theme.shape.borderRadius,
  marginBottom: theme.spacing(1)
}));

const FlagChip = styled(Chip)(({ theme }) => ({
  margin: theme.spacing(0.5),
  textTransform: 'capitalize'
}));

const PhotoModerationQueue = () => {
  // State
  const [photos, setPhotos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [currentTab, setCurrentTab] = useState('PENDING');
  const [selectedPhoto, setSelectedPhoto] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [adminNotes, setAdminNotes] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  
  // Fetch photos by status
  const fetchPhotos = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/admin/photo-moderation/photos', {
        params: {
          status: currentTab,
          page,
          limit: pageSize
        }
      });
      
      if (response.data.success) {
        setPhotos(response.data.photos);
        setTotalPages(response.data.pagination.pages);
      }
    } catch (error) {
      console.error('Error fetching photos:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load photos',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Process batch of photos with AI
  const processBatch = async () => {
    setProcessing(true);
    try {
      const response = await axios.post('/api/admin/photo-moderation/batch-process', {
        limit: 20
      });
      
      if (response.data.success) {
        setSnackbar({
          open: true,
          message: response.data.message,
          severity: 'success'
        });
        
        // Refresh the list
        fetchPhotos();
      }
    } catch (error) {
      console.error('Error processing batch:', error);
      setSnackbar({
        open: true,
        message: 'Failed to process photos',
        severity: 'error'
      });
    } finally {
      setProcessing(false);
    }
  };
  
  // Update photo status
  const updatePhotoStatus = async (photoId, status) => {
    try {
      const response = await axios.put(`/api/admin/photo-moderation/photos/${photoId}/status`, {
        status,
        adminNotes: adminNotes
      });
      
      if (response.data.success) {
        // Remove the photo from the list if we're viewing pending
        if (currentTab === 'PENDING') {
          setPhotos(photos.filter(photo => photo.id !== photoId));
        } else {
          // Refresh the list for other tabs
          fetchPhotos();
        }
        
        setSnackbar({
          open: true,
          message: `Photo ${status.toLowerCase()} successfully`,
          severity: 'success'
        });
      }
    } catch (error) {
      console.error('Error updating photo status:', error);
      setSnackbar({
        open: true,
        message: 'Failed to update photo status',
        severity: 'error'
      });
    } finally {
      setDialogOpen(false);
      setAdminNotes('');
    }
  };
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
    setPage(1);
  };
  
  // Handle page change
  const handlePageChange = (event, value) => {
    setPage(value);
  };
  
  // Handle page size change
  const handlePageSizeChange = (event) => {
    setPageSize(event.target.value);
    setPage(1);
  };
  
  // Open photo detail dialog
  const openPhotoDialog = (photo) => {
    setSelectedPhoto(photo);
    setDialogOpen(true);
  };
  
  // Close photo detail dialog
  const closePhotoDialog = () => {
    setDialogOpen(false);
    setSelectedPhoto(null);
    setAdminNotes('');
  };
  
  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar(prev => ({
      ...prev,
      open: false
    }));
  };
  
  // Format flag for display
  const formatFlag = (flag) => {
    return flag.replace(/_/g, ' ');
  };
  
  // Load photos when tab, page, or pageSize changes
  useEffect(() => {
    fetchPhotos();
  }, [currentTab, page, pageSize]);
  
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Photo Moderation Queue
      </Typography>
      
      <StyledCard>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Tabs
              value={currentTab}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
            >
              <Tab label="Pending Review" value="PENDING" />
              <Tab label="Approved" value="APPROVED" />
              <Tab label="Rejected" value="REJECTED" />
              <Tab label="All Photos" value="ALL" />
            </Tabs>
            
            <Box>
              {currentTab === 'PENDING' && (
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={processing ? <CircularProgress size={20} color="inherit" /> : <AutorenewIcon />}
                  onClick={processBatch}
                  disabled={processing || loading}
                  sx={{ mr: 1 }}
                >
                  {processing ? 'Processing...' : 'Process Batch with AI'}
                </Button>
              )}
              
              <Tooltip title="Refresh">
                <IconButton onClick={fetchPhotos} disabled={loading}>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          
          {loading ? (
            <Box display="flex" justifyContent="center" alignItems="center" height="400px">
              <CircularProgress />
            </Box>
          ) : photos.length === 0 ? (
            <Box textAlign="center" py={5}>
              <Typography variant="h6" color="textSecondary">
                No photos found
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {currentTab === 'PENDING' 
                  ? 'There are no photos pending review.' 
                  : `There are no ${currentTab.toLowerCase()} photos.`}
              </Typography>
            </Box>
          ) : (
            <>
              <Grid container spacing={3}>
                {photos.map(photo => (
                  <Grid item xs={12} sm={6} md={4} lg={3} key={photo.id}>
                    <PhotoCard>
                      <PhotoImage src={photo.url} alt="User upload" />
                      
                      <Typography variant="subtitle1" noWrap>
                        {photo.user.name}
                      </Typography>
                      
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        {photo.user.gender}, {photo.user.age || 'Unknown'} • {photo.user.location}
                      </Typography>
                      
                      {photo.aiFlags && photo.aiFlags.length > 0 && (
                        <Box my={1}>
                          {photo.aiFlags.map(flag => (
                            <FlagChip
                              key={flag}
                              label={formatFlag(flag)}
                              size="small"
                              color={flag.includes('explicit') || flag.includes('violent') ? 'error' : 'default'}
                            />
                          ))}
                        </Box>
                      )}
                      
                      {photo.aiConfidence && (
                        <Typography variant="body2" color="textSecondary">
                          AI Confidence: {Math.round(photo.aiConfidence)}%
                        </Typography>
                      )}
                      
                      <Box mt="auto" pt={2} display="flex" justifyContent="space-between">
                        <Tooltip title="View Details">
                          <IconButton onClick={() => openPhotoDialog(photo)} size="small">
                            <ZoomInIcon />
                          </IconButton>
                        </Tooltip>
                        
                        {currentTab === 'PENDING' && (
                          <Box>
                            <Tooltip title="Approve">
                              <IconButton 
                                onClick={() => updatePhotoStatus(photo.id, 'APPROVED')}
                                color="success"
                                size="small"
                              >
                                <CheckCircleIcon />
                              </IconButton>
                            </Tooltip>
                            
                            <Tooltip title="Reject">
                              <IconButton 
                                onClick={() => updatePhotoStatus(photo.id, 'REJECTED')}
                                color="error"
                                size="small"
                              >
                                <CancelIcon />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        )}
                      </Box>
                    </PhotoCard>
                  </Grid>
                ))}
              </Grid>
              
              <Box display="flex" justifyContent="space-between" alignItems="center" mt={3}>
                <FormControl variant="outlined" size="small">
                  <InputLabel id="page-size-label">Per Page</InputLabel>
                  <Select
                    labelId="page-size-label"
                    value={pageSize}
                    onChange={handlePageSizeChange}
                    label="Per Page"
                  >
                    <MenuItem value={12}>12</MenuItem>
                    <MenuItem value={20}>20</MenuItem>
                    <MenuItem value={40}>40</MenuItem>
                    <MenuItem value={60}>60</MenuItem>
                  </Select>
                </FormControl>
                
                <Pagination
                  count={totalPages}
                  page={page}
                  onChange={handlePageChange}
                  color="primary"
                />
              </Box>
            </>
          )}
        </CardContent>
      </StyledCard>
      
      {/* Photo Detail Dialog */}
      {selectedPhoto && (
        <Dialog
          open={dialogOpen}
          onClose={closePhotoDialog}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            Photo Details
          </DialogTitle>
          
          <DialogContent dividers>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <img 
                  src={selectedPhoto.url} 
                  alt="User upload" 
                  style={{ width: '100%', borderRadius: '4px' }}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Typography variant="h6">
                  User Information
                </Typography>
                
                <Box mb={2}>
                  <Typography variant="body1">
                    <strong>Name:</strong> {selectedPhoto.user.name}
                  </Typography>
                  
                  <Typography variant="body1">
                    <strong>Gender:</strong> {selectedPhoto.user.gender}
                  </Typography>
                  
                  <Typography variant="body1">
                    <strong>Age:</strong> {selectedPhoto.user.age || 'Unknown'}
                  </Typography>
                  
                  <Typography variant="body1">
                    <strong>Location:</strong> {selectedPhoto.user.location}
                  </Typography>
                  
                  <Typography variant="body1">
                    <strong>Phone:</strong> {selectedPhoto.user.phone}
                  </Typography>
                </Box>
                
                <Typography variant="h6">
                  Photo Information
                </Typography>
                
                <Box mb={2}>
                  <Typography variant="body1">
                    <strong>Upload Date:</strong> {new Date(selectedPhoto.uploadedAt).toLocaleString()}
                  </Typography>
                  
                  <Typography variant="body1">
                    <strong>Status:</strong> {selectedPhoto.status}
                  </Typography>
                  
                  <Typography variant="body1">
                    <strong>Profile Picture:</strong> {selectedPhoto.isProfilePic ? 'Yes' : 'No'}
                  </Typography>
                </Box>
                
                {selectedPhoto.aiFlags && selectedPhoto.aiFlags.length > 0 && (
                  <Box mb={2}>
                    <Typography variant="h6">
                      AI Analysis
                    </Typography>
                    
                    <Typography variant="body1">
                      <strong>Confidence:</strong> {Math.round(selectedPhoto.aiConfidence)}%
                    </Typography>
                    
                    <Typography variant="body1">
                      <strong>Flags:</strong>
                    </Typography>
                    
                    <Box mt={1}>
                      {selectedPhoto.aiFlags.map(flag => (
                        <FlagChip
                          key={flag}
                          label={formatFlag(flag)}
                          color={flag.includes('explicit') || flag.includes('violent') ? 'error' : 'default'}
                        />
                      ))}
                    </Box>
                  </Box>
                )}
                
                {currentTab === 'PENDING' && (
                  <Box mt={3}>
                    <Typography variant="h6">
                      Moderation Action
                    </Typography>
                    
                    <TextField
                      label="Admin Notes"
                      multiline
                      rows={3}
                      value={adminNotes}
                      onChange={(e) => setAdminNotes(e.target.value)}
                      fullWidth
                      margin="normal"
                      variant="outlined"
                      placeholder="Optional notes about this moderation decision"
                    />
                  </Box>
                )}
              </Grid>
            </Grid>
          </DialogContent>
          
          <DialogActions>
            <Button onClick={closePhotoDialog} color="inherit">
              Close
            </Button>
            
            {currentTab === 'PENDING' && (
              <>
                <Button 
                  onClick={() => updatePhotoStatus(selectedPhoto.id, 'REJECTED')}
                  color="error"
                  variant="contained"
                  startIcon={<CancelIcon />}
                >
                  Reject
                </Button>
                
                <Button 
                  onClick={() => updatePhotoStatus(selectedPhoto.id, 'APPROVED')}
                  color="success"
                  variant="contained"
                  startIcon={<CheckCircleIcon />}
                >
                  Approve
                </Button>
              </>
            )}
          </DialogActions>
        </Dialog>
      )}
      
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default PhotoModerationQueue;
