import React, { useEffect } from 'react';
import { ThemeProvider as Mu<PERSON><PERSON>hem<PERSON><PERSON>rovider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import theme from '../styles/theme';

// Note: theme.css is imported globally in _app.js

const ThemeProvider = ({ children }) => {
  // Add animate.css for animations
  useEffect(() => {
    // Add animate.css to the document head
    const animateCssLink = document.createElement('link');
    animateCssLink.rel = 'stylesheet';
    animateCssLink.href = 'https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css';
    document.head.appendChild(animateCssLink);

    // Add Font Awesome for icons
    const fontAwesomeLink = document.createElement('link');
    fontAwesomeLink.rel = 'stylesheet';
    fontAwesomeLink.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
    document.head.appendChild(fontAwesomeLink);

    // Add Google Fonts
    const googleFontsLink = document.createElement('link');
    googleFontsLink.rel = 'stylesheet';
    googleFontsLink.href = 'https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap';
    document.head.appendChild(googleFontsLink);

    // Cleanup function
    return () => {
      document.head.removeChild(animateCssLink);
      document.head.removeChild(fontAwesomeLink);
      document.head.removeChild(googleFontsLink);
    };
  }, []);

  return (
    <MuiThemeProvider theme={theme}>
      <CssBaseline />
      {children}
    </MuiThemeProvider>
  );
};

export default ThemeProvider;
