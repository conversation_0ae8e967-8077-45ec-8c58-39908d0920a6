// src/routes/matches.routes.js

const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth.middleware');

// Mock matches data
const mockMatches = [
  {
    id: 1,
    name: '<PERSON><PERSON>',
    age: 26,
    location: 'Mumbai, Maharashtra',
    education: 'MBA Finance',
    occupation: 'Financial Analyst',
    height: '5\'4"',
    religion: 'Hindu',
    caste: '<PERSON><PERSON>',
    subcaste: '<PERSON><PERSON><PERSON>',
    photo: '/api/placeholder/300/400',
    photos: [
      '/api/placeholder/300/400',
      '/api/placeholder/300/400',
      '/api/placeholder/300/400'
    ],
    compatibility: 94,
    isOnline: true,
    lastSeen: new Date(),
    verified: true,
    premium: false,
    interests: ['Reading', 'Traveling', 'Cooking'],
    about: 'Looking for a life partner who shares similar values and dreams.',
    familyDetails: {
      fatherOccupation: 'Business',
      motherOccupation: 'Homemaker',
      siblings: '1 Sister (Married)'
    }
  },
  {
    id: 2,
    name: '<PERSON>',
    age: 24,
    location: 'Pune, Maharashtra',
    education: 'B.Tech Computer Science',
    occupation: 'Software Engineer',
    height: '5\'2"',
    religion: 'Hindu',
    caste: '<PERSON><PERSON>',
    subcaste: 'Patil',
    photo: '/api/placeholder/300/400',
    photos: [
      '/api/placeholder/300/400',
      '/api/placeholder/300/400',
      '/api/placeholder/300/400'
    ],
    compatibility: 89,
    isOnline: false,
    lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    verified: true,
    premium: true,
    interests: ['Technology', 'Music', 'Fitness'],
    about: 'Passionate about technology and looking for someone who understands my ambitions.',
    familyDetails: {
      fatherOccupation: 'Engineer',
      motherOccupation: 'Teacher',
      siblings: '1 Brother (Unmarried)'
    }
  },
  {
    id: 3,
    name: 'Kavya Desai',
    age: 27,
    location: 'Nashik, Maharashtra',
    education: 'M.Sc Biotechnology',
    occupation: 'Research Scientist',
    height: '5\'5"',
    religion: 'Hindu',
    caste: 'Maratha',
    subcaste: 'Desai',
    photo: '/api/placeholder/300/400',
    photos: [
      '/api/placeholder/300/400',
      '/api/placeholder/300/400',
      '/api/placeholder/300/400'
    ],
    compatibility: 87,
    isOnline: true,
    lastSeen: new Date(),
    verified: false,
    premium: false,
    interests: ['Science', 'Nature', 'Photography'],
    about: 'Love exploring nature and conducting research. Seeking a understanding partner.',
    familyDetails: {
      fatherOccupation: 'Doctor',
      motherOccupation: 'Nurse',
      siblings: 'No Siblings'
    }
  },
  {
    id: 4,
    name: 'Sneha Jadhav',
    age: 25,
    location: 'Kolhapur, Maharashtra',
    education: 'B.Com',
    occupation: 'Accountant',
    height: '5\'3"',
    religion: 'Hindu',
    caste: 'Maratha',
    subcaste: 'Jadhav',
    photo: '/api/placeholder/300/400',
    photos: [
      '/api/placeholder/300/400',
      '/api/placeholder/300/400',
      '/api/placeholder/300/400'
    ],
    compatibility: 92,
    isOnline: false,
    lastSeen: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    verified: true,
    premium: true,
    interests: ['Dancing', 'Cooking', 'Family Time'],
    about: 'Family-oriented person who believes in traditional values with modern thinking.',
    familyDetails: {
      fatherOccupation: 'Farmer',
      motherOccupation: 'Homemaker',
      siblings: '2 Brothers (Both Married)'
    }
  },
  {
    id: 5,
    name: 'Pooja Bhosale',
    age: 23,
    location: 'Aurangabad, Maharashtra',
    education: 'B.Ed',
    occupation: 'Teacher',
    height: '5\'1"',
    religion: 'Hindu',
    caste: 'Maratha',
    subcaste: 'Bhosale',
    photo: '/api/placeholder/300/400',
    photos: [
      '/api/placeholder/300/400',
      '/api/placeholder/300/400',
      '/api/placeholder/300/400'
    ],
    compatibility: 85,
    isOnline: true,
    lastSeen: new Date(),
    verified: true,
    premium: false,
    interests: ['Teaching', 'Art', 'Social Work'],
    about: 'Dedicated teacher who loves working with children and contributing to society.',
    familyDetails: {
      fatherOccupation: 'Government Employee',
      motherOccupation: 'Homemaker',
      siblings: '1 Sister (Unmarried)'
    }
  }
];

// Check if mock data should be used
const shouldUseMockData = () => {
  return process.env.NODE_ENV === 'development' || process.env.USE_MOCK_DATA === 'true';
};

/**
 * @description Get matches for the authenticated user
 * @route GET /api/matches
 */
router.get('/', authenticateToken, async (req, res, next) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      ageMin = 18,
      ageMax = 35,
      location = '',
      education = '',
      occupation = '',
      caste = '',
      subcaste = '',
      sortBy = 'compatibility', 
      order = 'desc'
    } = req.query;

    if (shouldUseMockData()) {
      // Return mock data
      let filteredMatches = [...mockMatches];

      // Apply filters
      if (ageMin) {
        filteredMatches = filteredMatches.filter(match => match.age >= parseInt(ageMin));
      }
      if (ageMax) {
        filteredMatches = filteredMatches.filter(match => match.age <= parseInt(ageMax));
      }
      if (location) {
        filteredMatches = filteredMatches.filter(match => 
          match.location.toLowerCase().includes(location.toLowerCase())
        );
      }
      if (education) {
        filteredMatches = filteredMatches.filter(match => 
          match.education.toLowerCase().includes(education.toLowerCase())
        );
      }
      if (occupation) {
        filteredMatches = filteredMatches.filter(match => 
          match.occupation.toLowerCase().includes(occupation.toLowerCase())
        );
      }
      if (caste) {
        filteredMatches = filteredMatches.filter(match => 
          match.caste.toLowerCase().includes(caste.toLowerCase())
        );
      }
      if (subcaste) {
        filteredMatches = filteredMatches.filter(match => 
          match.subcaste.toLowerCase().includes(subcaste.toLowerCase())
        );
      }

      // Apply sorting
      const sortOrder = order.toLowerCase() === 'asc' ? 1 : -1;
      filteredMatches.sort((a, b) => {
        const aValue = a[sortBy];
        const bValue = b[sortBy];
        if (aValue < bValue) return -1 * sortOrder;
        if (aValue > bValue) return 1 * sortOrder;
        return 0;
      });

      // Apply pagination
      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const startIndex = (pageNum - 1) * limitNum;
      const endIndex = startIndex + limitNum;
      const paginatedMatches = filteredMatches.slice(startIndex, endIndex);

      return res.status(200).json({
        success: true,
        message: "Matches fetched successfully",
        matches: paginatedMatches,
        pagination: {
          currentPage: pageNum,
          limit: limitNum,
          totalPages: Math.ceil(filteredMatches.length / limitNum),
          totalMatches: filteredMatches.length
        },
        useMockData: true
      });
    }

    // Real database implementation would go here
    return res.status(200).json({
      success: true,
      message: "Matches fetched successfully (Real Data - Not Implemented Yet)",
      matches: [],
      pagination: {
        currentPage: 1,
        limit: parseInt(limit),
        totalPages: 0,
        totalMatches: 0
      },
      useMockData: false
    });

  } catch (error) {
    console.error('Error fetching matches:', error);
    next(error);
  }
});

/**
 * @description Get match details by ID
 * @route GET /api/matches/:id
 */
router.get('/:id', authenticateToken, async (req, res, next) => {
  try {
    const { id } = req.params;

    if (shouldUseMockData()) {
      const match = mockMatches.find(m => m.id === parseInt(id));
      if (!match) {
        return res.status(404).json({
          success: false,
          message: "Match not found"
        });
      }

      return res.status(200).json({
        success: true,
        message: "Match details fetched successfully",
        match,
        useMockData: true
      });
    }

    // Real database implementation would go here
    return res.status(404).json({
      success: false,
      message: "Match not found (Real Data - Not Implemented Yet)"
    });

  } catch (error) {
    console.error('Error fetching match details:', error);
    next(error);
  }
});

/**
 * @description Express interest in a match
 * @route POST /api/matches/:id/interest
 */
router.post('/:id/interest', authenticateToken, async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.userId;

    if (shouldUseMockData()) {
      const match = mockMatches.find(m => m.id === parseInt(id));
      if (!match) {
        return res.status(404).json({
          success: false,
          message: "Match not found"
        });
      }

      return res.status(200).json({
        success: true,
        message: "Interest expressed successfully",
        data: {
          matchId: parseInt(id),
          userId: userId,
          status: 'SENT',
          timestamp: new Date()
        },
        useMockData: true
      });
    }

    // Real database implementation would go here
    return res.status(501).json({
      success: false,
      message: "Express interest not implemented for real data yet"
    });

  } catch (error) {
    console.error('Error expressing interest:', error);
    next(error);
  }
});

module.exports = router;
