// ------------------- Notification Models -------------------
model Notification {
  id            String    @id @default(cuid())
  userId        String    @map("user_id")
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  title         String
  body          String    // Changed from 'message' to match FCM terminology
  imageUrl      String?   @map("image_url")
  data          Json?     // Additional data for the notification (type, actionUrl, etc.)

  is<PERSON><PERSON>an   @default(false) @map("is_read")
  readAt        DateTime? @map("read_at")
  sentViaFCM    Boolean   @default(false) @map("sent_via_fcm")

  createdAt     DateTime  @default(now()) @map("created_at")

  @@map("notifications")
}

// Topic-based notifications
model TopicNotification {
  id            String    @id @default(cuid())
  topic         String    // The topic this notification was sent to
  
  title         String
  body          String
  imageUrl      String?   @map("image_url")
  data          Json?     // Additional data for the notification
  
  sentAt        DateTime  @default(now()) @map("sent_at")
  
  @@map("topic_notifications")
}

// User topic subscriptions
model TopicSubscription {
  id            String    @id @default(cuid())
  userId        String    @map("user_id")
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  topic         String    // The topic the user is subscribed to
  subscribedAt  DateTime  @default(now()) @map("subscribed_at")
  
  @@unique([userId, topic])
  @@map("topic_subscriptions")
}

// Scheduled notifications
model ScheduledNotification {
  id              String    @id @default(cuid())
  notificationType String    @map("notification_type") // e.g., 'promotional', 'newMatch'
  
  targetType      String    @map("target_type") // USER, TOPIC, ALL_USERS
  targetId        String?   @map("target_id") // User ID or topic name (null for ALL_USERS)
  
  data            Json      // Data for the notification template
  scheduledFor    DateTime  @map("scheduled_for")
  sentAt          DateTime? @map("sent_at")
  
  status          String    @default("SCHEDULED") // SCHEDULED, SENT, FAILED, CANCELLED
  statusDetails   String?   @map("status_details") @db.Text
  
  createdAt       DateTime  @default(now()) @map("created_at")
  createdBy       String?   @map("created_by") // Admin ID who created this scheduled notification
  
  @@map("scheduled_notifications")
}
