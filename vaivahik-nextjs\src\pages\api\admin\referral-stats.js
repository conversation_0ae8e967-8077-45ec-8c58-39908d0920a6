import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  // Check if user is authenticated and is an admin
  const session = await getServerSession(req, res, authOptions);
  
  if (!session || !session.user || session.user.role !== 'ADMIN') {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  // Only allow GET method
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Get total referrals
    const totalReferrals = await prisma.referral.count();
    
    // Get active referrals (pending or completed but not rewarded)
    const activeReferrals = await prisma.referral.count({
      where: {
        status: {
          in: ['pending', 'completed']
        }
      }
    });
    
    // Get completed referrals for conversion rate
    const completedReferrals = await prisma.referral.count({
      where: {
        status: {
          in: ['completed', 'rewarded']
        }
      }
    });
    
    // Calculate conversion rate
    const conversionRate = totalReferrals > 0 
      ? Math.round((completedReferrals / totalReferrals) * 100) 
      : 0;
    
    // Get total rewards given (cash only)
    const totalRewards = await prisma.referralReward.aggregate({
      where: {
        rewardType: 'cash'
      },
      _sum: {
        rewardAmount: true
      }
    });
    
    // Get monthly referral trends (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    
    const monthlyTrends = await prisma.$queryRaw`
      SELECT 
        DATE_TRUNC('month', "created_at") as month,
        COUNT(*) as count
      FROM 
        referrals
      WHERE 
        "created_at" >= ${sixMonthsAgo}
      GROUP BY 
        DATE_TRUNC('month', "created_at")
      ORDER BY 
        month ASC
    `;
    
    // Get top referrers
    const topReferrers = await prisma.referral.groupBy({
      by: ['referrerId'],
      _count: {
        referralCode: true
      },
      orderBy: {
        _count: {
          referralCode: 'desc'
        }
      },
      take: 5
    });
    
    // Enhance top referrers with user info
    const enhancedTopReferrers = await Promise.all(
      topReferrers.map(async (referrer) => {
        const user = await prisma.user.findUnique({
          where: { id: referrer.referrerId },
          select: {
            id: true,
            name: true,
            email: true,
            profilePicture: true
          }
        });
        
        return {
          ...user,
          referralCount: referrer._count.referralCode
        };
      })
    );
    
    // Return all stats
    return res.status(200).json({
      success: true,
      stats: {
        totalReferrals,
        activeReferrals,
        conversionRate,
        totalRewards: totalRewards._sum?.rewardAmount || 0,
        monthlyTrends,
        topReferrers: enhancedTopReferrers
      }
    });
  } catch (error) {
    console.error('Error fetching referral stats:', error);
    return res.status(500).json({ success: false, message: 'Failed to fetch referral statistics' });
  }
}
