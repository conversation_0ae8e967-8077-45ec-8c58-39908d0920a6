import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { adminGet, adminPost, adminPut, adminDel } from '@/services/apiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);

export default function PremiumPlans() {
  const [plans, setPlans] = useState([]);
  const [features, setFeatures] = useState([]);
  const [loading, setLoading] = useState(true);
  const [featuresLoading, setFeaturesLoading] = useState(true);
  const [currentPlan, setCurrentPlan] = useState(null);
  const [showPlanModal, setShowPlanModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmAction, setConfirmAction] = useState('');

  useEffect(() => {
    fetchPremiumPlans();
    fetchFeatures();
  }, []);

  const fetchPremiumPlans = async () => {
    setLoading(true);
    try {
      // Use the admin API service which handles mock/real data toggle
      const data = await adminGet(ADMIN_ENDPOINTS.PREMIUM_PLANS);

      if (data.success) {
        setPlans(data.plans);
      } else {
        console.error('API returned error:', data.message);
        // Fallback to mock data if API fails
        const mockData = getMockPlans();
        setPlans(mockData);
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching premium plans:', error);
      // Fallback to mock data if API fails
      console.log('Using mock data as fallback');
      const mockData = getMockPlans();
      setPlans(mockData);
      setLoading(false);
    }
  };

  const fetchFeatures = async () => {
    setFeaturesLoading(true);
    try {
      // Use the admin API service which handles mock/real data toggle
      const data = await adminGet(ADMIN_ENDPOINTS.FEATURES);

      if (data.success) {
        setFeatures(data.features);
      } else {
        console.error('API returned error:', data.message);
        // Fallback to mock data if API fails
        const mockData = getMockFeatures();
        setFeatures(mockData);
      }

      setFeaturesLoading(false);
    } catch (error) {
      console.error('Error fetching features:', error);
      // Fallback to mock data if API fails
      console.log('Using mock data as fallback');
      const mockData = getMockFeatures();
      setFeatures(mockData);
      setFeaturesLoading(false);
    }
  };

  // Function to handle adding a new plan
  const handleAddPlan = () => {
    setCurrentPlan(null);
    setShowPlanModal(true);
    document.body.classList.add('modal-open');
  };

  // Function to handle editing a plan
  const handleEditPlan = (plan) => {
    setCurrentPlan(plan);
    setShowPlanModal(true);
    document.body.classList.add('modal-open');
  };

  // Function to handle deleting a plan
  const handleDeletePlan = (plan) => {
    setCurrentPlan(plan);
    setConfirmAction('delete');
    setShowConfirmModal(true);
    document.body.classList.add('modal-open');
  };

  // Function to close the plan modal
  const closePlanModal = () => {
    setShowPlanModal(false);
    setCurrentPlan(null);
    document.body.classList.remove('modal-open');
  };

  // Function to close the confirmation modal
  const closeConfirmModal = () => {
    setShowConfirmModal(false);
    document.body.classList.remove('modal-open');
  };

  // Function to save a plan
  const handleSavePlan = async () => {
    // Get form data
    const form = document.getElementById('planForm');
    if (!form) return;

    const formData = new FormData(form);
    const hasDiscount = formData.get('hasDiscount') === 'on';

    const planData = {
      name: formData.get('name'),
      planType: formData.get('planType'),
      amount: parseFloat(formData.get('amount')),
      currency: formData.get('currency'),
      durationDays: parseInt(formData.get('durationDays')),
      description: formData.get('description'),
      isActive: formData.get('isActive') === 'on',
      isPopular: formData.get('isPopular') === 'on',
      features: Array.from(formData.getAll('features')),
      // Offer system fields
      hasDiscount: hasDiscount
    };

    // Add discount fields if discount is enabled
    if (hasDiscount) {
      planData.discountType = formData.get('discountType');
      planData.discountValue = parseFloat(formData.get('discountValue') || '0');
      planData.offerStartDate = formData.get('offerStartDate') || null;
      planData.offerEndDate = formData.get('offerEndDate') || null;
      planData.offerMessage = formData.get('offerMessage') || '';

      // Calculate discounted price
      if (planData.discountType === 'percentage' && planData.discountValue) {
        planData.discountedAmount = planData.amount - (planData.amount * (planData.discountValue / 100));
      } else if (planData.discountType === 'flat' && planData.discountValue) {
        planData.discountedAmount = Math.max(0, planData.amount - planData.discountValue);
      }
    }

    try {
      // Prepare the plan data with feature details
      const preparedPlanData = {
        ...planData,
        features: features
          .filter(f => planData.features.includes(f.id))
          .map(f => ({ id: f.id, name: f.displayName }))
      };

      // Use the admin API service which handles mock/real data toggle
      const data = currentPlan
        ? await adminPut(`${ADMIN_ENDPOINTS.PREMIUM_PLANS}/${currentPlan.id}`, preparedPlanData)
        : await adminPost(ADMIN_ENDPOINTS.PREMIUM_PLANS, preparedPlanData);

      if (data.success) {
        if (currentPlan) {
          // Update existing plan
          setPlans(plans.map(p => p.id === currentPlan.id ? data.plan || preparedPlanData : p));
        } else {
          // Add new plan
          setPlans([...plans, data.plan]);
        }

        // Close modal
        closePlanModal();

        // Show success message
        alert(data.message || `Plan ${currentPlan ? 'updated' : 'created'} successfully!`);
      } else {
        console.error('API returned error:', data.message);
        alert(data.message || `Failed to ${currentPlan ? 'update' : 'create'} plan. Please try again.`);
      }
    } catch (error) {
      console.error(`Error ${currentPlan ? 'updating' : 'creating'} plan:`, error);
      alert(`Error ${currentPlan ? 'updating' : 'creating'} plan. Please try again.`);
    }
  };

  // Function to confirm an action (like deleting a plan)
  const handleConfirmAction = async () => {
    if (confirmAction === 'delete' && currentPlan) {
      try {
        // Use the admin API service which handles mock/real data toggle
        const data = await adminDel(`${ADMIN_ENDPOINTS.PREMIUM_PLANS}/${currentPlan.id}`);

        if (data.success) {
          // Update local state
          setPlans(plans.filter(p => p.id !== currentPlan.id));
          setShowConfirmModal(false);
          document.body.classList.remove('modal-open');

          // Show success message
          alert(data.message || 'Plan deleted successfully');
        } else {
          console.error('API returned error:', data.message);
          alert(data.message || 'Failed to delete plan. Please try again.');
        }
      } catch (error) {
        console.error('Error deleting plan:', error);
        alert('Failed to delete plan. Please try again.');
      }
    }
  };

  // Function to get duration text
  const getDurationText = (days) => {
    if (!days) return 'Unknown duration';

    if (days === 30) return '1 month';
    if (days === 90) return '3 months';
    if (days === 180) return '6 months';
    if (days === 365) return '1 year';

    return `${days} days`;
  };

  // Function to check if an offer is valid (within date range)
  const isOfferValid = (plan) => {
    if (!plan.hasDiscount) return false;

    const now = new Date();
    let isValid = true;

    // Check start date if it exists
    if (plan.offerStartDate) {
      const startDate = new Date(plan.offerStartDate);
      if (now < startDate) isValid = false;
    }

    // Check end date if it exists
    if (plan.offerEndDate) {
      const endDate = new Date(plan.offerEndDate);
      if (now > endDate) isValid = false;
    }

    return isValid;
  };

  // Function to get access icon for feature comparison
  const getAccessIcon = (accessRules, tier) => {
    if (!accessRules) return <span className="feature-unavailable">✕</span>;

    // Handle both array format and object format for accessRules
    let rule;
    if (Array.isArray(accessRules)) {
      rule = accessRules.find(r => r.userTier === tier);
    } else {
      // Handle object format where keys are tier names (lowercase)
      const tierKey = tier.toLowerCase();
      rule = accessRules[tierKey];
    }

    if (!rule || !rule.isEnabled) {
      return <span className="feature-unavailable">✕</span>;
    }

    if (rule.dailyLimit || (rule.limitType === 'daily' && rule.limitValue > 0)) {
      const limit = rule.dailyLimit || rule.limitValue;
      return <span className="feature-limited">{limit}/day</span>;
    }

    if (rule.totalLimit || (rule.limitType === 'total' && rule.limitValue > 0)) {
      const limit = rule.totalLimit || rule.limitValue;
      return <span className="feature-limited">{limit} total</span>;
    }

    return <span className="feature-available">✓</span>;
  };

  // Mock data functions
  const getMockPlans = () => {
    return [
      {
        id: 'plan-1',
        name: 'Premium Monthly',
        planType: 'MONTHLY',
        amount: 499,
        currency: 'INR',
        durationDays: 30,
        description: 'Access all premium features for one month.',
        isActive: true,
        isPopular: true,
        features: [
          { id: 'feature-1', name: 'View contact details' },
          { id: 'feature-2', name: 'Advanced search filters' },
          { id: 'feature-3', name: 'Priority in search results' },
          { id: 'feature-4', name: 'Unlimited messages' }
        ]
      },
      {
        id: 'plan-2',
        name: 'Premium Quarterly',
        planType: 'QUARTERLY',
        amount: 1299,
        currency: 'INR',
        durationDays: 90,
        description: 'Access all premium features for three months at a discounted rate.',
        isActive: true,
        isPopular: false,
        features: [
          { id: 'feature-1', name: 'View contact details' },
          { id: 'feature-2', name: 'Advanced search filters' },
          { id: 'feature-3', name: 'Priority in search results' },
          { id: 'feature-4', name: 'Unlimited messages' },
          { id: 'feature-5', name: 'Horoscope matching' }
        ]
      },
      {
        id: 'plan-3',
        name: 'Premium Annual',
        planType: 'ANNUAL',
        amount: 4999,
        currency: 'INR',
        durationDays: 365,
        description: 'Access all premium features for one year at our best value rate.',
        isActive: true,
        isPopular: false,
        features: [
          { id: 'feature-1', name: 'View contact details' },
          { id: 'feature-2', name: 'Advanced search filters' },
          { id: 'feature-3', name: 'Priority in search results' },
          { id: 'feature-4', name: 'Unlimited messages' },
          { id: 'feature-5', name: 'Horoscope matching' },
          { id: 'feature-6', name: 'Profile highlighting' }
        ]
      },
      {
        id: 'plan-4',
        name: 'Basic Monthly',
        planType: 'MONTHLY',
        amount: 299,
        currency: 'INR',
        durationDays: 30,
        description: 'Access essential features for one month.',
        isActive: false,
        isPopular: false,
        features: [
          { id: 'feature-1', name: 'View contact details' },
          { id: 'feature-2', name: 'Advanced search filters' }
        ]
      }
    ];
  };

  const getMockFeatures = () => {
    return [
      {
        id: 'feature-1',
        name: 'view-contacts',
        displayName: 'View Contact Details',
        description: 'View phone numbers and email addresses of other users',
        category: 'COMMUNICATION',
        isActive: true,
        accessRules: [
          { userTier: 'BASIC', isEnabled: false },
          { userTier: 'VERIFIED', isEnabled: true, dailyLimit: 5 },
          { userTier: 'PREMIUM', isEnabled: true }
        ]
      },
      {
        id: 'feature-2',
        name: 'advanced-search',
        displayName: 'Advanced Search Filters',
        description: 'Use additional filters like education, profession, and income',
        category: 'SEARCH',
        isActive: true,
        accessRules: [
          { userTier: 'BASIC', isEnabled: false },
          { userTier: 'VERIFIED', isEnabled: true },
          { userTier: 'PREMIUM', isEnabled: true }
        ]
      },
      {
        id: 'feature-3',
        name: 'priority-search',
        displayName: 'Priority in Search Results',
        description: 'Appear higher in search results of other users',
        category: 'VISIBILITY',
        isActive: true,
        accessRules: [
          { userTier: 'BASIC', isEnabled: false },
          { userTier: 'VERIFIED', isEnabled: false },
          { userTier: 'PREMIUM', isEnabled: true }
        ]
      },
      {
        id: 'feature-4',
        name: 'unlimited-messages',
        displayName: 'Unlimited Messages',
        description: 'Send unlimited messages to other users',
        category: 'COMMUNICATION',
        isActive: true,
        accessRules: [
          { userTier: 'BASIC', isEnabled: true, dailyLimit: 10 },
          { userTier: 'VERIFIED', isEnabled: true, dailyLimit: 30 },
          { userTier: 'PREMIUM', isEnabled: true }
        ]
      },
      {
        id: 'feature-5',
        name: 'horoscope-matching',
        displayName: 'Horoscope Matching',
        description: 'View detailed horoscope compatibility analysis',
        category: 'MATCHING',
        isActive: true,
        accessRules: [
          { userTier: 'BASIC', isEnabled: false },
          { userTier: 'VERIFIED', isEnabled: false },
          { userTier: 'PREMIUM', isEnabled: true }
        ]
      },
      {
        id: 'feature-6',
        name: 'profile-highlight',
        displayName: 'Profile Highlighting',
        description: 'Make your profile stand out with special highlighting',
        category: 'VISIBILITY',
        isActive: true,
        accessRules: [
          { userTier: 'BASIC', isEnabled: false },
          { userTier: 'VERIFIED', isEnabled: false },
          { userTier: 'PREMIUM', isEnabled: true }
        ]
      }
    ];
  };
  return (
    <EnhancedAdminLayout title="Premium Plans">
      <div className="content-header">
        <h2 className="page-title">Premium Plans Management</h2>
        <button className="btn btn-primary" onClick={() => handleAddPlan()}>
          <span className="btn-icon">+</span> Add New Plan
        </button>
      </div>

      {/* Premium Plans Cards */}
      <div className="card-grid">
        <div className="plans-container">
          {loading ? (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>Loading premium plans...</p>
            </div>
          ) : plans.length === 0 ? (
            <div className="text-center p-4">
              <p>No premium plans found. Create your first plan to get started.</p>
            </div>
          ) : (
            plans.map(plan => (
              <div className="plan-card" key={plan.id} data-plan-id={plan.id}>
                {plan.isPopular && <span className="plan-badge popular">Popular</span>}
                {plan.hasDiscount && isOfferValid(plan) && (
                  <span className="plan-badge offer">{plan.offerMessage || 'Special Offer'}</span>
                )}
                <div className={`plan-status ${!plan.isActive ? 'inactive' : ''}`} title={plan.isActive ? 'Active' : 'Inactive'}></div>
                <div className="plan-header">
                  <h3 className="plan-title">{plan.name}</h3>
                  <div className="plan-type">{plan.planType}</div>

                  {plan.hasDiscount && isOfferValid(plan) ? (
                    <div className="plan-price-container">
                      <div className="plan-original-price">{plan.currency === 'INR' ? '₹' : '$'}{plan.amount}</div>
                      <div className="plan-price">{plan.currency === 'INR' ? '₹' : '$'}{plan.discountedAmount.toFixed(2)}</div>
                      <div className="plan-savings">
                        {plan.discountType === 'percentage'
                          ? `Save ${plan.discountValue}%`
                          : `Save ${plan.currency === 'INR' ? '₹' : '$'}${plan.discountValue}`}
                      </div>
                    </div>
                  ) : (
                    <div className="plan-price">{plan.currency === 'INR' ? '₹' : '$'}{plan.amount}</div>
                  )}

                  <div className="plan-duration">{getDurationText(plan.durationDays)}</div>
                </div>
                <div className="plan-body">
                  <div className="plan-description">{plan.description || 'No description available.'}</div>
                  <ul className="plan-features">
                    {plan.features && plan.features.length > 0 ? (
                      plan.features.map(feature => (
                        <li key={feature.id}><span className="feature-icon">✓</span> {feature.name}</li>
                      ))
                    ) : (
                      <li>No features specified</li>
                    )}
                  </ul>
                  <div className="plan-actions">
                    <button
                      className="btn btn-sm btn-outline-primary"
                      onClick={() => handleEditPlan(plan)}
                    >
                      Edit
                    </button>
                    <button
                      className="btn btn-sm btn-outline-danger"
                      onClick={() => handleDeletePlan(plan)}
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Feature Comparison Table */}
      <div className="card">
        <div className="card-header">
          <h3 className="table-title">Feature Comparison</h3>
        </div>
        <div className="table-container">
          <table id="featureComparisonTable">
            <thead>
              <tr>
                <th>Feature</th>
                <th>Basic (Free)</th>
                <th>Verified</th>
                <th>Premium</th>
              </tr>
            </thead>
            <tbody>
              {featuresLoading ? (
                <tr>
                  <td colSpan="4" style={{ textAlign: 'center' }}>
                    <div className="loading-spinner"></div> Loading features...
                  </td>
                </tr>
              ) : features.length === 0 ? (
                <tr>
                  <td colSpan="4" style={{ textAlign: 'center' }}>
                    No features found. Add features to display comparison.
                  </td>
                </tr>
              ) : (
                features.map(feature => (
                  <tr key={feature.id}>
                    <td>{feature.displayName}</td>
                    <td>{getAccessIcon(feature.accessRules, 'BASIC')}</td>
                    <td>{getAccessIcon(feature.accessRules, 'VERIFIED')}</td>
                    <td>{getAccessIcon(feature.accessRules, 'PREMIUM')}</td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add/Edit Plan Modal */}
      {showPlanModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3 className="modal-title">{currentPlan ? 'Edit Premium Plan' : 'Add New Premium Plan'}</h3>
              <button className="modal-close-button" onClick={closePlanModal}>&times;</button>
            </div>
            <div className="modal-body">
              <form id="planForm">
                <div className="form-group">
                  <label htmlFor="planName">Plan Name</label>
                  <input
                    type="text"
                    id="planName"
                    name="name"
                    required
                    placeholder="e.g., Monthly Premium"
                    defaultValue={currentPlan?.name || ''}
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="planType">Plan Type</label>
                  <select
                    id="planType"
                    name="planType"
                    required
                    defaultValue={currentPlan?.planType || 'MONTHLY'}
                  >
                    <option value="MONTHLY">Monthly</option>
                    <option value="QUARTERLY">Quarterly</option>
                    <option value="ANNUAL">Annual</option>
                    <option value="LIFETIME">Lifetime</option>
                  </select>
                </div>
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="planAmount">Regular Price</label>
                    <input
                      type="number"
                      id="planAmount"
                      name="amount"
                      required
                      placeholder="e.g., 999"
                      defaultValue={currentPlan?.amount || ''}
                    />
                  </div>
                  <div className="form-group">
                    <label htmlFor="planCurrency">Currency</label>
                    <select
                      id="planCurrency"
                      name="currency"
                      required
                      defaultValue={currentPlan?.currency || 'INR'}
                    >
                      <option value="INR">INR (₹)</option>
                      <option value="USD">USD ($)</option>
                    </select>
                  </div>
                </div>

                {/* Offer System */}
                <div className="form-group">
                  <div className="form-check">
                    <input
                      type="checkbox"
                      id="hasDiscount"
                      name="hasDiscount"
                      defaultChecked={currentPlan?.hasDiscount || false}
                      onChange={(e) => {
                        const discountFields = document.getElementById('discountFields');
                        if (discountFields) {
                          discountFields.style.display = e.target.checked ? 'block' : 'none';
                        }
                      }}
                    />
                    <label htmlFor="hasDiscount">Apply Discount/Offer</label>
                  </div>
                </div>

                <div id="discountFields" style={{ display: currentPlan?.hasDiscount ? 'block' : 'none' }}>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="discountType">Discount Type</label>
                      <select
                        id="discountType"
                        name="discountType"
                        defaultValue={currentPlan?.discountType || 'percentage'}
                      >
                        <option value="percentage">Percentage (%)</option>
                        <option value="flat">Flat Amount</option>
                      </select>
                    </div>
                    <div className="form-group">
                      <label htmlFor="discountValue">Discount Value</label>
                      <input
                        type="number"
                        id="discountValue"
                        name="discountValue"
                        placeholder="e.g., 10 for 10% or 100 for ₹100 off"
                        defaultValue={currentPlan?.discountValue || ''}
                      />
                    </div>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="offerStartDate">Offer Start Date</label>
                      <input
                        type="date"
                        id="offerStartDate"
                        name="offerStartDate"
                        defaultValue={currentPlan?.offerStartDate || ''}
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="offerEndDate">Offer End Date</label>
                      <input
                        type="date"
                        id="offerEndDate"
                        name="offerEndDate"
                        defaultValue={currentPlan?.offerEndDate || ''}
                      />
                    </div>
                  </div>

                  <div className="form-group">
                    <label htmlFor="offerMessage">Offer Message</label>
                    <input
                      type="text"
                      id="offerMessage"
                      name="offerMessage"
                      placeholder="e.g., Limited Time Offer! or Diwali Special"
                      defaultValue={currentPlan?.offerMessage || ''}
                    />
                    <div className="form-hint">This message will be displayed with the discount</div>
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="planDuration">Duration (Days)</label>
                  <input
                    type="number"
                    id="planDuration"
                    name="durationDays"
                    required
                    placeholder="e.g., 30"
                    defaultValue={currentPlan?.durationDays || ''}
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="planDescription">Description</label>
                  <textarea
                    id="planDescription"
                    name="description"
                    rows="3"
                    placeholder="Describe the plan benefits"
                    defaultValue={currentPlan?.description || ''}
                  ></textarea>
                </div>
                <div className="form-group">
                  <label>Features</label>
                  <div className="feature-checkboxes">
                    {features.length === 0 ? (
                      <p>No features available. Please add features first.</p>
                    ) : (
                      features.map(feature => {
                        const isChecked = currentPlan?.features?.some(f => f.id === feature.id) || false;
                        return (
                          <div className="feature-checkbox" key={feature.id}>
                            <input
                              type="checkbox"
                              id={`feature-${feature.id}`}
                              name="features"
                              value={feature.id}
                              defaultChecked={isChecked}
                            />
                            <label htmlFor={`feature-${feature.id}`}>
                              {feature.displayName}
                              <span className="feature-description">{feature.description || 'No description'}</span>
                            </label>
                          </div>
                        );
                      })
                    )}
                  </div>
                </div>
                <div className="form-group">
                  <div className="form-check">
                    <input
                      type="checkbox"
                      id="planActive"
                      name="isActive"
                      defaultChecked={currentPlan?.isActive !== false}
                    />
                    <label htmlFor="planActive">Active</label>
                  </div>
                </div>
                <div className="form-group">
                  <div className="form-check">
                    <input
                      type="checkbox"
                      id="planPopular"
                      name="isPopular"
                      defaultChecked={currentPlan?.isPopular || false}
                    />
                    <label htmlFor="planPopular">Mark as Popular</label>
                  </div>
                </div>
              </form>
            </div>
            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={closePlanModal}>Cancel</button>
              <button className="btn btn-primary" onClick={handleSavePlan}>Save Plan</button>
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmModal && (
        <div className="modal-overlay">
          <div className="modal confirmation-modal">
            <div className="modal-header">
              <h3 className="modal-title">
                {confirmAction === 'delete' ? 'Delete Premium Plan' : 'Confirm Action'}
              </h3>
              <button className="modal-close-button" onClick={closeConfirmModal}>&times;</button>
            </div>
            <div className="modal-body">
              <div className="confirmation-message">
                <div className="confirmation-icon">
                  {confirmAction === 'delete' ? '✗' : '?'}
                </div>
                <p>
                  {confirmAction === 'delete' && currentPlan ? (
                    <>Are you sure you want to delete the <strong>"{currentPlan.name}"</strong> plan? This action cannot be undone.</>
                  ) : (
                    'Are you sure you want to perform this action?'
                  )}
                </p>
              </div>
            </div>
            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={closeConfirmModal}>Cancel</button>
              <button
                className={`btn ${confirmAction === 'delete' ? 'btn-danger' : 'btn-primary'}`}
                onClick={handleConfirmAction}
              >
                {confirmAction === 'delete' ? 'Delete' : 'Confirm'}
              </button>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        /* Premium Plans Cards */
        .plans-container {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 20px;
          margin-bottom: 30px;
        }

        .plan-card {
          background-color: white;
          border-radius: 10px;
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
          overflow: hidden;
          transition: transform 0.3s ease, box-shadow 0.3s ease;
          position: relative;
        }

        .plan-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .plan-header {
          padding: 20px;
          background-color: var(--primary);
          color: white;
          position: relative;
        }

        .plan-header h3 {
          margin: 0;
          font-size: 1.4rem;
        }

        .plan-type {
          font-size: 0.9rem;
          opacity: 0.8;
          margin-top: 5px;
        }

        .plan-price {
          font-size: 2rem;
          font-weight: bold;
          margin: 15px 0 5px;
        }

        .plan-duration {
          font-size: 0.9rem;
          opacity: 0.8;
        }

        .plan-body {
          padding: 20px;
        }

        .plan-description {
          margin-bottom: 20px;
          color: #555;
          font-size: 0.95rem;
          line-height: 1.5;
        }

        .plan-features {
          list-style: none;
          padding: 0;
          margin: 0 0 20px 0;
        }

        .plan-features li {
          padding: 8px 0;
          border-bottom: 1px solid #eee;
          display: flex;
          align-items: center;
        }

        .plan-features li:last-child {
          border-bottom: none;
        }

        .feature-icon {
          margin-right: 10px;
          color: var(--success);
        }

        .plan-actions {
          display: flex;
          justify-content: space-between;
          margin-top: 20px;
        }

        .plan-badge {
          position: absolute;
          top: 10px;
          right: 10px;
          background-color: var(--warning);
          color: white;
          padding: 5px 10px;
          border-radius: 20px;
          font-size: 0.8rem;
          font-weight: bold;
          z-index: 1;
        }

        .plan-status {
          position: absolute;
          top: 10px;
          left: 10px;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background-color: var(--success);
        }

        .plan-status.inactive {
          background-color: var(--danger);
        }

        /* Feature comparison table styles */
        #featureComparisonTable {
          width: 100%;
          border-collapse: collapse;
        }

        #featureComparisonTable th {
          background-color: var(--primary);
          color: white;
          text-align: center;
          padding: 12px;
        }

        #featureComparisonTable th:first-child {
          text-align: left;
          width: 30%;
        }

        #featureComparisonTable td {
          padding: 12px;
          text-align: center;
          border-bottom: 1px solid #eee;
        }

        #featureComparisonTable td:first-child {
          text-align: left;
          font-weight: 500;
        }

        .feature-available {
          color: var(--success);
          font-size: 1.2rem;
        }

        .feature-unavailable {
          color: var(--danger);
          font-size: 1.2rem;
        }

        .feature-limited {
          color: var(--warning);
          font-size: 0.9rem;
        }

        /* Form styles */
        .form-row {
          display: flex;
          gap: 15px;
        }

        .form-row .form-group {
          flex: 1;
        }

        .feature-checkboxes {
          max-height: 200px;
          overflow-y: auto;
          border: 1px solid #ddd;
          border-radius: 4px;
          padding: 10px;
        }

        .feature-checkbox {
          margin-bottom: 8px;
          display: flex;
          align-items: center;
        }

        .feature-checkbox input {
          margin-right: 8px;
        }

        .feature-checkbox label {
          display: flex;
          flex-direction: column;
        }

        .feature-checkbox .feature-description {
          font-size: 0.8rem;
          color: #666;
          margin-top: 2px;
        }

        /* Modal Styles */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 2000; /* Higher than sidebar z-index (1000) */
          overflow-y: auto;
          padding: 20px;
        }

        .modal {
          background-color: white;
          border-radius: 12px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
          width: 100%;
          max-width: 800px;
          max-height: 90vh;
          overflow-y: auto;
          display: flex;
          flex-direction: column;
          margin-left: auto;
          margin-right: auto;
          position: relative;
        }

        .confirmation-modal {
          max-width: 500px;
        }

        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 15px 20px;
          border-bottom: 1px solid #eee;
        }

        .modal-title {
          font-size: 1.2rem;
          font-weight: 600;
          color: var(--text-dark);
          margin: 0;
        }

        .modal-close-button {
          background: none;
          border: none;
          font-size: 1.5rem;
          cursor: pointer;
          color: #999;
          transition: color 0.2s ease;
        }

        .modal-close-button:hover {
          color: var(--text-dark);
        }

        .modal-body {
          padding: 20px;
          overflow-y: auto;
          flex: 1;
        }

        .modal-footer {
          display: flex;
          justify-content: flex-end;
          gap: 10px;
          padding: 15px 20px;
          border-top: 1px solid #eee;
        }

        /* Form Styles */
        .form-group {
          margin-bottom: 15px;
        }

        .form-group label {
          display: block;
          margin-bottom: 5px;
          font-weight: 500;
          color: var(--text-dark);
        }

        .form-group input[type="text"],
        .form-group input[type="number"],
        .form-group select,
        .form-group textarea {
          width: 100%;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 1rem;
        }

        .form-check {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
        }

        .form-check input[type="checkbox"] {
          margin-right: 10px;
        }

        /* Confirmation Modal Styles */
        .confirmation-message {
          text-align: center;
          padding: 20px 0;
        }

        .confirmation-icon {
          font-size: 3rem;
          margin-bottom: 20px;
          display: inline-block;
          width: 80px;
          height: 80px;
          line-height: 80px;
          border-radius: 50%;
          background-color: #f5f5f5;
          color: var(--primary);
        }

        /* Button Styles */
        .btn {
          padding: 8px 16px;
          border-radius: 4px;
          font-weight: 500;
          cursor: pointer;
          border: none;
          transition: background-color 0.2s ease;
        }

        .btn-primary {
          background-color: var(--primary);
          color: white;
        }

        .btn-primary:hover {
          background-color: var(--primary-dark);
        }

        .btn-secondary {
          background-color: #f5f5f5;
          color: var(--text-dark);
        }

        .btn-secondary:hover {
          background-color: #e0e0e0;
        }

        .btn-danger {
          background-color: var(--danger);
          color: white;
        }

        .btn-danger:hover {
          background-color: #d32f2f;
        }

        .btn-sm {
          padding: 5px 10px;
          font-size: 0.85rem;
        }

        .btn-outline-primary {
          background-color: transparent;
          border: 1px solid var(--primary);
          color: var(--primary);
        }

        .btn-outline-primary:hover {
          background-color: var(--primary);
          color: white;
        }

        .btn-outline-danger {
          background-color: transparent;
          border: 1px solid var(--danger);
          color: var(--danger);
        }

        .btn-outline-danger:hover {
          background-color: var(--danger);
          color: white;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
          .plans-container {
            grid-template-columns: 1fr;
          }

          .form-row {
            flex-direction: column;
            gap: 0;
          }

          .modal {
            width: 95%;
            max-height: 80vh;
          }
        }
      `}</style>
    </EnhancedAdminLayout>
  );
}

