/**
 * Basic SMS Test via MSG91
 * 
 * This script tests basic SMS sending (not OTP) to check if SMS delivery works
 */

const axios = require('axios');
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });

const phoneNumber = process.argv[2] || '9527159115';

console.log('=== Basic SMS Test via MSG91 ===');
console.log(`Testing with phone number: ${phoneNumber}`);

async function testBasicSMS() {
  console.log('\n=== Testing Basic SMS Sending ===');
  
  const params = new URLSearchParams({
    authkey: process.env.MSG91_API_KEY,
    mobiles: `91${phoneNumber}`,
    message: 'Test message from Maratha Wedding app. If you receive this, SMS is working!',
    sender: process.env.MSG91_SENDER_ID,
    route: '4', // Transactional route
    country: '91'
  });

  try {
    const response = await axios.post('https://control.msg91.com/api/sendhttp.php', params.toString(), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    
    console.log('SMS Response Status:', response.status);
    console.log('SMS Response Data:', response.data);
    
    if (response.data && response.data.includes('success')) {
      console.log('✅ SMS API call successful - Check your phone!');
    } else {
      console.log('❌ SMS API call failed or returned error');
    }
  } catch (error) {
    console.log('SMS Error:', error.response?.data || error.message);
  }
}

async function testWithDifferentRoute() {
  console.log('\n=== Testing with Promotional Route ===');
  
  const params = new URLSearchParams({
    authkey: process.env.MSG91_API_KEY,
    mobiles: `91${phoneNumber}`,
    message: 'Promotional test from Maratha Wedding. Route 1 test.',
    sender: process.env.MSG91_SENDER_ID,
    route: '1', // Promotional route
    country: '91'
  });

  try {
    const response = await axios.post('https://control.msg91.com/api/sendhttp.php', params.toString(), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    
    console.log('Promotional SMS Response:', response.data);
  } catch (error) {
    console.log('Promotional SMS Error:', error.response?.data || error.message);
  }
}

async function runTests() {
  await testBasicSMS();
  await testWithDifferentRoute();
  
  console.log('\n=== Next Steps ===');
  console.log('1. Check your phone for SMS messages');
  console.log('2. If no SMS received, contact MSG91 support');
  console.log('3. Verify account configuration in MSG91 dashboard');
  console.log('4. Check if phone number is in DND list');
}

runTests().catch(console.error);
