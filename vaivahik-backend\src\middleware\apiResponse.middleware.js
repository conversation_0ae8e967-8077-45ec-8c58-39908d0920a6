/**
 * API Response Middleware
 * 
 * This middleware standardizes API responses across the application.
 * It provides consistent response formats for success and error cases.
 */

/**
 * Standardize success responses
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const standardizeResponse = (req, res, next) => {
  // Store the original res.json method
  const originalJson = res.json;

  // Override res.json method
  res.json = function(data) {
    // If the response is already standardized, just pass it through
    if (data && (data.success !== undefined || data.error !== undefined)) {
      return originalJson.call(this, data);
    }

    // Standardize the response
    const standardizedResponse = {
      success: true,
      data: data,
      timestamp: new Date().toISOString()
    };

    // Call the original json method with the standardized response
    return originalJson.call(this, standardizedResponse);
  };

  // Add success response helpers
  res.success = function(data, message = 'Success', statusCode = 200) {
    const response = {
      success: true,
      message: message,
      data: data,
      timestamp: new Date().toISOString()
    };
    return this.status(statusCode).json(response);
  };

  res.created = function(data, message = 'Resource created successfully') {
    return this.success(data, message, 201);
  };

  res.updated = function(data, message = 'Resource updated successfully') {
    return this.success(data, message, 200);
  };

  res.deleted = function(message = 'Resource deleted successfully') {
    return this.success(null, message, 200);
  };

  res.noContent = function() {
    return this.status(204).end();
  };

  // Add error response helpers
  res.error = function(message, statusCode = 500, errors = null) {
    const response = {
      success: false,
      message: message,
      timestamp: new Date().toISOString()
    };

    if (errors) {
      response.errors = errors;
    }

    return this.status(statusCode).json(response);
  };

  res.badRequest = function(message = 'Bad request', errors = null) {
    return this.error(message, 400, errors);
  };

  res.unauthorized = function(message = 'Unauthorized') {
    return this.error(message, 401);
  };

  res.forbidden = function(message = 'Forbidden') {
    return this.error(message, 403);
  };

  res.notFound = function(message = 'Resource not found') {
    return this.error(message, 404);
  };

  res.validationError = function(errors, message = 'Validation failed') {
    return this.error(message, 422, errors);
  };

  res.conflict = function(message = 'Conflict', errors = null) {
    return this.error(message, 409, errors);
  };

  res.serverError = function(message = 'Internal server error') {
    return this.error(message, 500);
  };

  // Add pagination helper
  res.paginated = function(data, pagination, message = 'Success') {
    const response = {
      success: true,
      message: message,
      data: data,
      pagination: pagination,
      timestamp: new Date().toISOString()
    };
    return this.status(200).json(response);
  };

  next();
};

module.exports = standardizeResponse;
