"""
Model Trainer for Enhanced Two-Tower Model

This module provides advanced training functionality for the enhanced two-tower model,
including learning rate scheduling, early stopping, and gradient clipping.
"""

import os
import json
import time
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, TensorDataset
from .enhanced_tower_model_pytorch import EnhancedMatrimonyMatchingModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelTrainer:
    """Advanced trainer for the enhanced two-tower model"""
    
    def __init__(self, model=None, config=None):
        """
        Initialize the model trainer
        
        Args:
            model: An instance of EnhancedMatrimonyMatchingModel or None to create a new one
            config (dict): Configuration parameters for training
        """
        # Default configuration
        self.default_config = {
            'learning_rate': 0.001,
            'weight_decay': 0.01,
            'batch_size': 64,
            'epochs': 50,
            'early_stopping_patience': 5,
            'reduce_lr_patience': 3,
            'reduce_lr_factor': 0.5,
            'gradient_clip_value': 1.0,
            'validation_split': 0.2,
            'model_dir': os.path.join(os.path.dirname(__file__), '../../models')
        }
        
        # Use provided config or default
        self.config = config if config else self.default_config
        
        # Set device
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {self.device}")
        
        # Initialize or use provided model
        if model is None:
            self.model = EnhancedMatrimonyMatchingModel(self.config)
            self.model.build_model()
        else:
            self.model = model
        
        # Ensure model is on the correct device
        if hasattr(self.model, 'model'):
            self.model.model = self.model.model.to(self.device)
        
        # Create model directory if it doesn't exist
        os.makedirs(self.config['model_dir'], exist_ok=True)
    
    def prepare_data(self, user_features, match_features, labels):
        """
        Prepare data for training
        
        Args:
            user_features (list): List of user feature dictionaries
            match_features (list): List of match feature dictionaries
            labels (list): List of match labels (1 for match, 0 for non-match)
            
        Returns:
            tuple: Training and validation data loaders
        """
        # Convert features to tensors
        user_tensor = self._features_to_tensor(user_features)
        match_tensor = self._features_to_tensor(match_features)
        labels_tensor = torch.tensor(labels, dtype=torch.float32).view(-1, 1)
        
        # Create dataset
        dataset = TensorDataset(user_tensor, match_tensor, labels_tensor)
        
        # Split into training and validation sets
        val_size = int(len(dataset) * self.config['validation_split'])
        train_size = len(dataset) - val_size
        
        train_dataset, val_dataset = torch.utils.data.random_split(
            dataset, [train_size, val_size]
        )
        
        # Create data loaders
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.config['batch_size'],
            shuffle=True
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.config['batch_size'],
            shuffle=False
        )
        
        return train_loader, val_loader
    
    def _features_to_tensor(self, features_list):
        """
        Convert feature dictionaries to tensor
        
        Args:
            features_list (list): List of feature dictionaries
            
        Returns:
            torch.Tensor: Feature tensor
        """
        # Get all feature keys
        all_keys = set()
        for features in features_list:
            all_keys.update(features.keys())
        
        # Sort keys for consistent ordering
        sorted_keys = sorted(all_keys)
        
        # Create tensor
        tensor_data = []
        for features in features_list:
            feature_vector = [features.get(key, 0.0) for key in sorted_keys]
            tensor_data.append(feature_vector)
        
        return torch.tensor(tensor_data, dtype=torch.float32)
    
    def train(self, train_loader, val_loader=None):
        """
        Train the model with advanced techniques
        
        Args:
            train_loader: DataLoader for training data
            val_loader: DataLoader for validation data
            
        Returns:
            dict: Training history
        """
        # Set up optimizer with weight decay (L2 regularization)
        optimizer = optim.AdamW(
            self.model.model.parameters(),
            lr=self.config['learning_rate'],
            weight_decay=self.config['weight_decay']
        )
        
        # Set up learning rate scheduler
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=self.config['reduce_lr_factor'],
            patience=self.config['reduce_lr_patience'],
            verbose=True
        )
        
        # Set up loss function
        criterion = nn.BCELoss()
        
        # Training history
        history = {
            'train_loss': [],
            'val_loss': [],
            'learning_rates': []
        }
        
        # Early stopping variables
        best_val_loss = float('inf')
        best_model_path = os.path.join(self.config['model_dir'], 'best_model.pt')
        patience_counter = 0
        
        # Training loop
        for epoch in range(self.config['epochs']):
            # Training phase
            self.model.model.train()
            train_loss = 0.0
            
            for batch_idx, (user_data, match_data, labels) in enumerate(train_loader):
                # Move data to device
                user_data = user_data.to(self.device)
                match_data = match_data.to(self.device)
                labels = labels.to(self.device)
                
                # Zero gradients
                optimizer.zero_grad()
                
                # Forward pass
                outputs = self.model.model(user_data, match_data)
                
                # Calculate loss
                loss = criterion(outputs, labels)
                
                # Backward pass
                loss.backward()
                
                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(
                    self.model.model.parameters(),
                    max_norm=self.config['gradient_clip_value']
                )
                
                # Update weights
                optimizer.step()
                
                # Accumulate loss
                train_loss += loss.item() * user_data.size(0)
            
            # Calculate average training loss
            avg_train_loss = train_loss / len(train_loader.dataset)
            history['train_loss'].append(avg_train_loss)
            
            # Get current learning rate
            current_lr = optimizer.param_groups[0]['lr']
            history['learning_rates'].append(current_lr)
            
            # Validation phase
            if val_loader:
                val_loss = self.evaluate(val_loader, criterion)
                history['val_loss'].append(val_loss)
                
                # Update learning rate scheduler
                scheduler.step(val_loss)
                
                # Early stopping
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    
                    # Save best model
                    self.save_model(best_model_path)
                    logger.info(f"Saved best model with validation loss: {val_loss:.4f}")
                else:
                    patience_counter += 1
                    if patience_counter >= self.config['early_stopping_patience']:
                        logger.info(f"Early stopping at epoch {epoch+1}")
                        break
                
                logger.info(f"Epoch {epoch+1}/{self.config['epochs']}, "
                           f"Train Loss: {avg_train_loss:.4f}, "
                           f"Val Loss: {val_loss:.4f}, "
                           f"LR: {current_lr:.6f}")
            else:
                logger.info(f"Epoch {epoch+1}/{self.config['epochs']}, "
                           f"Train Loss: {avg_train_loss:.4f}, "
                           f"LR: {current_lr:.6f}")
        
        # Load best model if validation was used
        if val_loader and os.path.exists(best_model_path):
            self.load_model(best_model_path)
            logger.info("Loaded best model from checkpoint")
        
        return history
    
    def evaluate(self, data_loader, criterion=None):
        """
        Evaluate the model on a dataset
        
        Args:
            data_loader: DataLoader for evaluation data
            criterion: Loss function (if None, uses BCELoss)
            
        Returns:
            float: Average loss
        """
        # Set model to evaluation mode
        self.model.model.eval()
        
        # Use BCELoss if no criterion is provided
        if criterion is None:
            criterion = nn.BCELoss()
        
        # Evaluation loop
        total_loss = 0.0
        
        with torch.no_grad():
            for user_data, match_data, labels in data_loader:
                # Move data to device
                user_data = user_data.to(self.device)
                match_data = match_data.to(self.device)
                labels = labels.to(self.device)
                
                # Forward pass
                outputs = self.model.model(user_data, match_data)
                
                # Calculate loss
                loss = criterion(outputs, labels)
                
                # Accumulate loss
                total_loss += loss.item() * user_data.size(0)
        
        # Calculate average loss
        avg_loss = total_loss / len(data_loader.dataset)
        
        return avg_loss
    
    def save_model(self, path=None):
        """
        Save the model to disk
        
        Args:
            path (str): Path to save the model (if None, uses default)
            
        Returns:
            str: Path where the model was saved
        """
        if path is None:
            timestamp = time.strftime("%Y%m%d-%H%M%S")
            path = os.path.join(self.config['model_dir'], f"model_{timestamp}.pt")
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # Save model state
        torch.save({
            'model_state_dict': self.model.model.state_dict(),
            'config': self.model.config,
            'feature_stats': self.model.feature_stats if hasattr(self.model, 'feature_stats') else {}
        }, path)
        
        logger.info(f"Model saved to {path}")
        
        return path
    
    def load_model(self, path):
        """
        Load a model from disk
        
        Args:
            path (str): Path to the saved model
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Load checkpoint
            checkpoint = torch.load(path, map_location=self.device)
            
            # Update model configuration
            if 'config' in checkpoint:
                self.model.config.update(checkpoint['config'])
            
            # Update feature statistics
            if 'feature_stats' in checkpoint and hasattr(self.model, 'feature_stats'):
                self.model.feature_stats = checkpoint['feature_stats']
            
            # Load model state
            self.model.model.load_state_dict(checkpoint['model_state_dict'])
            
            logger.info(f"Model loaded from {path}")
            return True
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            return False
    
    def predict(self, user_features, match_features):
        """
        Make predictions with the model
        
        Args:
            user_features (list): List of user feature dictionaries
            match_features (list): List of match feature dictionaries
            
        Returns:
            numpy.ndarray: Predicted match scores
        """
        # Convert features to tensors
        user_tensor = self._features_to_tensor(user_features)
        match_tensor = self._features_to_tensor(match_features)
        
        # Move to device
        user_tensor = user_tensor.to(self.device)
        match_tensor = match_tensor.to(self.device)
        
        # Set model to evaluation mode
        self.model.model.eval()
        
        # Make predictions
        with torch.no_grad():
            predictions = self.model.model(user_tensor, match_tensor)
        
        # Convert to numpy array
        predictions = predictions.cpu().numpy().flatten()
        
        return predictions
