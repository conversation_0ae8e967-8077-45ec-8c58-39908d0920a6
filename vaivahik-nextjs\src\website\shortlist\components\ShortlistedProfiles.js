import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Button,
  Chip,
  IconButton,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  Skeleton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Tooltip,
  useTheme,
  alpha
} from '@mui/material';
import useUndoableActions from '@/hooks/useUndoableActions';
import { useToast } from '@/contexts/ToastContext';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';
import DeleteIcon from '@mui/icons-material/Delete';
import VisibilityIcon from '@mui/icons-material/Visibility';
import FavoriteIcon from '@mui/icons-material/Favorite';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import PersonIcon from '@mui/icons-material/Person';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import WorkIcon from '@mui/icons-material/Work';
import SchoolIcon from '@mui/icons-material/School';
import VerifiedIcon from '@mui/icons-material/Verified';
import StarIcon from '@mui/icons-material/Star';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import EditIcon from '@mui/icons-material/Edit';
import SortIcon from '@mui/icons-material/Sort';
import FilterListIcon from '@mui/icons-material/FilterList';
import SearchIcon from '@mui/icons-material/Search';
import UserOnlineStatus from '@/components/common/UserOnlineStatus';
import SmartCallButton from '@/components/contact/SmartCallButton';
import { useRouter } from 'next/router';

// Mock API functions - replace with actual API calls
const getShortlistedProfiles = async () => {
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Mock data
  return Array(12).fill(0).map((_, index) => ({
    _id: `user${index + 1}`,
    name: `User ${index + 1}`,
    age: 25 + (index % 10),
    height: `5'${6 + (index % 6)}"`,
    occupation: ['Software Engineer', 'Doctor', 'Teacher', 'Business Analyst', 'Lawyer'][index % 5],
    education: ['Bachelor\'s Degree', 'Master\'s Degree', 'Ph.D.', 'MBA'][index % 4],
    city: ['Mumbai', 'Delhi', 'Bangalore', 'Pune', 'Chennai'][index % 5],
    state: ['Maharashtra', 'Delhi', 'Karnataka', 'Maharashtra', 'Tamil Nadu'][index % 5],
    profilePhoto: `/mock-profiles/${index % 2 === 0 ? 'male' : 'female'}${1 + (index % 5)}.jpg`,
    isPremium: index % 3 === 0,
    isVerified: index % 4 === 0,
    shortlistedOn: new Date(Date.now() - (index * 24 * 60 * 60 * 1000)),
    notes: index % 3 === 0 ? 'Good match, similar interests and values.' : ''
  }));
};

const removeFromShortlist = async (userId) => {
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 1000));
  return { success: true };
};

const updateShortlistNotes = async (userId, notes) => {
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 1000));
  return { success: true };
};

const sendInterest = async (userId) => {
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 1000));
  return { success: true };
};

/**
 * Shortlisted Profiles Component
 *
 * Advanced UI for displaying and managing shortlisted profiles
 */
const ShortlistedProfiles = () => {
  const theme = useTheme();
  const router = useRouter();

  const [profiles, setProfiles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortOption, setSortOption] = useState('recent');
  const [sortMenuAnchor, setSortMenuAnchor] = useState(null);
  const [notesDialog, setNotesDialog] = useState({
    open: false,
    userId: null,
    notes: ''
  });

  const { showToast } = useToast();
  const { removeWithUndo, actionLoading } = useUndoableActions();

  // Load shortlisted profiles on component mount
  useEffect(() => {
    loadShortlistedProfiles();
  }, []);

  // Load shortlisted profiles
  const loadShortlistedProfiles = async () => {
    try {
      setLoading(true);
      const data = await getShortlistedProfiles();
      setProfiles(data);
    } catch (error) {
      console.error('Error loading shortlisted profiles:', error);
      showToast('Failed to load shortlisted profiles');
    } finally {
      setLoading(false);
    }
  };

  // Handle remove from shortlist
  const handleRemoveFromShortlist = async (profile) => {
    removeWithUndo({
      itemId: profile._id,
      itemName: profile.name,
      itemType: 'profile',
      removeFunction: removeFromShortlist,
      addFunction: (id, data) => addToShortlist(id, data.notes || ''),
      onRemoveFromState: (id) => {
        setProfiles(prev => prev.filter(p => p._id !== id));
      },
      onAddToState: (profile) => {
        setProfiles(prev => [...prev, profile]);
      },
      itemData: profile,
      additionalData: { notes: profile.notes || '' }
    });
  };

  // Handle edit notes
  const handleEditNotes = (userId, notes) => {
    setNotesDialog({
      open: true,
      userId,
      notes: notes || ''
    });
  };

  // Save notes
  const saveNotes = async () => {
    const { userId, notes } = notesDialog;

    try {
      // Show loading state
      const loadingId = `notes-${userId}`;

      await updateShortlistNotes(userId, notes);

      // Update local state
      setProfiles(prev =>
        prev.map(profile =>
          profile._id === userId
            ? { ...profile, notes }
            : profile
        )
      );

      // Show success message
      showToast('Notes updated successfully');

      // Close dialog
      setNotesDialog({ open: false, userId: null, notes: '' });
    } catch (error) {
      console.error('Error updating notes:', error);
      showToast('Failed to update notes');
    }
  };

  // Handle send interest
  const handleSendInterest = async (userId) => {
    try {
      // Use actionLoading state from useUndoableActions
      await sendInterest(userId);

      // Show success message
      showToast('Interest sent successfully');
    } catch (error) {
      console.error('Error sending interest:', error);
      showToast('Failed to send interest');
    }
  };

  // Handle view profile
  const handleViewProfile = (userId) => {
    router.push(`/website/pages/profile/${userId}`);
  };

  // Handle contact reveal success
  const handleCallInitiated = (data) => {
    console.log('Contact revealed:', data);

    // Track in analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'contact_reveal_success', {
        event_category: 'Shortlist_Engagement',
        event_label: data.targetUserId,
        custom_parameters: {
          source: 'shortlisted_profiles',
          access_reason: data.accessReason,
          platform: data.platform
        }
      });
    }

    showToast(`Contact revealed for ${data.targetUserName}! Opening dialer...`);
  };

  // Handle security blocks
  const handleSecurityBlock = (data) => {
    console.log('Security block:', data);

    // Track security events
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'security_block', {
        event_category: 'Security',
        event_label: data.reason,
        custom_parameters: {
          source: 'shortlisted_profiles',
          risk_score: data.riskScore,
          target_user_id: data.targetUserId
        }
      });
    }

    showToast(`Access blocked: ${data.reason}`, 'warning');
  };

  // Handle sort menu open
  const handleSortMenuOpen = (event) => {
    setSortMenuAnchor(event.currentTarget);
  };

  // Handle sort menu close
  const handleSortMenuClose = () => {
    setSortMenuAnchor(null);
  };

  // Handle sort option change
  const handleSortOptionChange = (option) => {
    setSortOption(option);
    setSortMenuAnchor(null);

    // Sort profiles
    const sortedProfiles = [...profiles];

    switch (option) {
      case 'recent':
        sortedProfiles.sort((a, b) => new Date(b.shortlistedOn) - new Date(a.shortlistedOn));
        break;
      case 'name':
        sortedProfiles.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'age':
        sortedProfiles.sort((a, b) => a.age - b.age);
        break;
    }

    setProfiles(sortedProfiles);
  };

  // Filter profiles based on search query
  const filteredProfiles = profiles.filter(profile => {
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase();
    return (
      profile.name.toLowerCase().includes(query) ||
      profile.occupation.toLowerCase().includes(query) ||
      profile.city.toLowerCase().includes(query) ||
      profile.state.toLowerCase().includes(query)
    );
  });

  // Format shortlisted date
  const formatShortlistedDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Render profile card
  const renderProfileCard = (profile) => {
    return (
      <Card
        elevation={3}
        sx={{
          height: '100%',
          borderRadius: 2,
          transition: 'transform 0.3s ease, box-shadow 0.3s ease',
          '&:hover': {
            transform: 'translateY(-5px)',
            boxShadow: 6
          },
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <Box sx={{ position: 'relative' }}>
          <CardMedia
            component="img"
            height={200}
            image={profile.profilePhoto || '/images/default-profile.jpg'}
            alt={profile.name}
            sx={{ objectFit: 'cover' }}
          />

          {/* Premium badge */}
          {profile.isPremium && (
            <Chip
              icon={<StarIcon fontSize="small" />}
              label="Premium"
              color="primary"
              size="small"
              sx={{
                position: 'absolute',
                top: 10,
                right: 10,
                backgroundColor: 'rgba(103, 58, 183, 0.85)',
                backdropFilter: 'blur(4px)'
              }}
            />
          )}

          {/* Verified badge */}
          {profile.isVerified && (
            <Tooltip title="Verified Profile">
              <Chip
                icon={<VerifiedIcon fontSize="small" />}
                label="Verified"
                color="success"
                size="small"
                sx={{
                  position: 'absolute',
                  top: profile.isPremium ? 50 : 10,
                  right: 10,
                  backgroundColor: 'rgba(46, 125, 50, 0.85)',
                  backdropFilter: 'blur(4px)'
                }}
              />
            </Tooltip>
          )}

          {/* Shortlisted badge */}
          <Chip
            icon={<BookmarkIcon fontSize="small" />}
            label={`Shortlisted on ${formatShortlistedDate(profile.shortlistedOn)}`}
            size="small"
            sx={{
              position: 'absolute',
              bottom: 10,
              left: 10,
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
              color: 'white',
              backdropFilter: 'blur(4px)'
            }}
          />
        </Box>

        <CardContent sx={{ flexGrow: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
            <Typography variant="h6" component="div" noWrap>
              {profile.name}
            </Typography>
            <Chip
              label={`ID: ${profile._id.substring(0, 6)}`}
              size="small"
              variant="outlined"
            />
          </Box>

          <Box sx={{ mb: 1 }}>
            <UserOnlineStatus userId={profile._id} size="small" />
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
            <PersonIcon fontSize="small" color="action" sx={{ mr: 1 }} />
            <Typography variant="body2" color="text.secondary">
              {profile.age} yrs, {profile.height}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
            <WorkIcon fontSize="small" color="action" sx={{ mr: 1 }} />
            <Typography variant="body2" color="text.secondary" noWrap>
              {profile.occupation}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
            <LocationOnIcon fontSize="small" color="action" sx={{ mr: 1 }} />
            <Typography variant="body2" color="text.secondary" noWrap>
              {profile.city}{profile.state ? `, ${profile.state}` : ''}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SchoolIcon fontSize="small" color="action" sx={{ mr: 1 }} />
            <Typography variant="body2" color="text.secondary" noWrap>
              {profile.education}
            </Typography>
          </Box>

          {profile.notes && (
            <Box sx={{ mt: 2, p: 1, bgcolor: alpha(theme.palette.primary.main, 0.05), borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary">
                <strong>Notes:</strong> {profile.notes}
              </Typography>
            </Box>
          )}
        </CardContent>

        <Box sx={{ mt: 'auto' }}>
          <Divider />

          <CardActions sx={{ p: 2, flexDirection: 'column', gap: 1 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
              <Button
                variant="outlined"
                startIcon={<VisibilityIcon />}
                size="small"
                onClick={() => handleViewProfile(profile._id)}
              >
                View Profile
              </Button>

              <Box>
                <Tooltip title="Edit Notes">
                  <IconButton
                    color="primary"
                    size="small"
                    onClick={() => handleEditNotes(profile._id, profile.notes)}
                    sx={{ mr: 1 }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Tooltip>

                <Tooltip title="Send Interest">
                  <IconButton
                    color="secondary"
                    size="small"
                    onClick={() => handleSendInterest(profile._id)}
                    disabled={actionLoading === profile._id}
                    sx={{ mr: 1 }}
                  >
                    {actionLoading === profile._id ?
                      <CircularProgress size={20} /> :
                      <FavoriteBorderIcon fontSize="small" />
                    }
                  </IconButton>
                </Tooltip>

                <Tooltip title="Remove from Shortlist">
                  <IconButton
                    color="error"
                    size="small"
                    onClick={() => handleRemoveFromShortlist(profile)}
                    disabled={actionLoading === profile._id}
                  >
                    {actionLoading === profile._id ?
                      <CircularProgress size={20} /> :
                      <DeleteIcon fontSize="small" />
                    }
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>

            {/* Smart Call Button */}
            <Box sx={{ width: '100%' }}>
              <SmartCallButton
                targetUserId={profile._id}
                targetUserName={profile.name}
                onCallInitiated={handleCallInitiated}
                onSecurityBlock={handleSecurityBlock}
                variant="outlined"
                size="small"
                fullWidth
              />
            </Box>
          </CardActions>
        </Box>
      </Card>
    );
  };

  // Render loading skeleton
  const renderSkeleton = () => {
    return Array(6).fill(0).map((_, index) => (
      <Grid item xs={12} sm={6} md={4} key={index}>
        <Card sx={{ height: '100%', borderRadius: 2 }}>
          <Skeleton variant="rectangular" height={200} />
          <CardContent>
            <Skeleton variant="text" width="70%" height={32} />
            <Skeleton variant="text" width="40%" height={24} />
            <Skeleton variant="text" width="90%" height={24} />
            <Skeleton variant="text" width="60%" height={24} />
            <Skeleton variant="text" width="80%" height={24} />
          </CardContent>
          <CardActions sx={{ p: 2 }}>
            <Skeleton variant="rectangular" width={120} height={36} />
            <Box sx={{ flexGrow: 1 }} />
            <Skeleton variant="circular" width={32} height={32} sx={{ ml: 1 }} />
            <Skeleton variant="circular" width={32} height={32} sx={{ ml: 1 }} />
            <Skeleton variant="circular" width={32} height={32} sx={{ ml: 1 }} />
          </CardActions>
        </Card>
      </Grid>
    ));
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          Shortlisted Profiles
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Manage profiles you've saved for later review
        </Typography>
      </Box>

      {/* Search and Sort Controls */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 3,
        flexDirection: { xs: 'column', sm: 'row' },
        gap: 2
      }}>
        <Box sx={{ position: 'relative', width: { xs: '100%', sm: '50%' } }}>
          <TextField
            fullWidth
            placeholder="Search by name, occupation, location..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <SearchIcon color="action" sx={{ mr: 1 }} />
              ),
              endAdornment: searchQuery && (
                <IconButton
                  size="small"
                  onClick={() => setSearchQuery('')}
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              )
            }}
            variant="outlined"
            size="small"
          />
        </Box>

        <Box>
          <Button
            variant="outlined"
            startIcon={<SortIcon />}
            onClick={handleSortMenuOpen}
            size="small"
          >
            Sort: {sortOption === 'recent' ? 'Most Recent' : sortOption === 'name' ? 'Name' : 'Age'}
          </Button>

          <Menu
            anchorEl={sortMenuAnchor}
            open={Boolean(sortMenuAnchor)}
            onClose={handleSortMenuClose}
          >
            <MenuItem
              selected={sortOption === 'recent'}
              onClick={() => handleSortOptionChange('recent')}
            >
              Most Recent
            </MenuItem>
            <MenuItem
              selected={sortOption === 'name'}
              onClick={() => handleSortOptionChange('name')}
            >
              Name
            </MenuItem>
            <MenuItem
              selected={sortOption === 'age'}
              onClick={() => handleSortOptionChange('age')}
            >
              Age
            </MenuItem>
          </Menu>
        </Box>
      </Box>

      {/* Results Count */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2" color="text.secondary">
          {loading ? 'Loading profiles...' : `Showing ${filteredProfiles.length} of ${profiles.length} shortlisted profiles`}
        </Typography>
      </Box>

      {/* Profile Grid */}
      <Grid container spacing={3}>
        {loading ? (
          renderSkeleton()
        ) : filteredProfiles.length > 0 ? (
          filteredProfiles.map(profile => (
            <Grid item xs={12} sm={6} md={4} key={profile._id}>
              {renderProfileCard(profile)}
            </Grid>
          ))
        ) : (
          <Grid item xs={12}>
            <Paper sx={{ p: 3, textAlign: 'center', borderRadius: 2 }}>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No profiles found
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {searchQuery ? 'Try a different search term' : 'You haven\'t shortlisted any profiles yet'}
              </Typography>
              {searchQuery && (
                <Button
                  variant="outlined"
                  onClick={() => setSearchQuery('')}
                  sx={{ mt: 2 }}
                >
                  Clear Search
                </Button>
              )}
            </Paper>
          </Grid>
        )}
      </Grid>

      {/* Notes Dialog */}
      <Dialog
        open={notesDialog.open}
        onClose={() => setNotesDialog({ ...notesDialog, open: false })}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Add Notes</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Notes about this profile"
            fullWidth
            multiline
            rows={4}
            value={notesDialog.notes}
            onChange={(e) => setNotesDialog({ ...notesDialog, notes: e.target.value })}
            placeholder="Add your personal notes about this profile..."
            variant="outlined"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNotesDialog({ ...notesDialog, open: false })}>
            Cancel
          </Button>
          <Button onClick={saveNotes} variant="contained" color="primary">
            Save Notes
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ShortlistedProfiles;