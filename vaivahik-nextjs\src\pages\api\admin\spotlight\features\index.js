// src/pages/api/admin/spotlight/features/index.js
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../auth/[...nextauth]';

export default async function handler(req, res) {
  // Check if user is authenticated and is an admin
  const session = await getServerSession(req, res, authOptions);
  
  if (!session || !session.user || !session.user.isAdmin) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  // Handle GET request - fetch spotlight features
  if (req.method === 'GET') {
    try {
      // Extract query parameters
      const { 
        page = 1, 
        limit = 10, 
        search = '',
        sortBy = 'createdAt', 
        order = 'desc',
        isActive = ''
      } = req.query;
      
      // Build query parameters
      const queryParams = new URLSearchParams({
        page,
        limit,
        ...(search && { search }),
        ...(sortBy && { sortBy }),
        ...(order && { order }),
        ...(isActive !== '' && { isActive })
      }).toString();

      // Fetch features from backend API
      const response = await fetch(`${process.env.BACKEND_API_URL}/api/admin/spotlight/features?${queryParams}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.accessToken}`
        }
      }).catch(() => null);
      
      if (response && response.ok) {
        const data = await response.json();
        return res.status(200).json({
          success: true,
          message: data.message,
          features: data.features,
          pagination: data.pagination
        });
      } else {
        // Fallback to mock data if backend API fails
        return res.status(200).json({
          success: true,
          message: 'Spotlight features fetched successfully',
          features: getMockFeatures(page, limit, search, sortBy, order, isActive),
          pagination: {
            currentPage: parseInt(page),
            limit: parseInt(limit),
            totalPages: 1,
            totalFeatures: 6
          }
        });
      }
    } catch (error) {
      console.error('Error fetching spotlight features:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch spotlight features',
        error: error.message
      });
    }
  }
  
  // Handle POST request - create a new spotlight feature
  if (req.method === 'POST') {
    try {
      const featureData = req.body;
      
      // Validate required fields
      if (!featureData.name || !featureData.description || !featureData.price) {
        return res.status(400).json({
          success: false,
          message: 'Name, description, and price are required'
        });
      }
      
      // Create feature in backend API
      const response = await fetch(`${process.env.BACKEND_API_URL}/api/admin/spotlight/features`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.accessToken}`
        },
        body: JSON.stringify(featureData)
      }).catch(() => null);
      
      if (response && response.ok) {
        const data = await response.json();
        return res.status(201).json({
          success: true,
          message: 'Spotlight feature created successfully',
          feature: data.feature
        });
      } else {
        // Fallback if backend API fails
        return res.status(201).json({
          success: true,
          message: 'Spotlight feature created successfully',
          feature: {
            id: `feature-${Date.now()}`,
            ...featureData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            purchaseCount: 0,
            activeCount: 0,
            revenue: 0
          }
        });
      }
    } catch (error) {
      console.error('Error creating spotlight feature:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to create spotlight feature',
        error: error.message
      });
    }
  }
  
  // Handle unsupported methods
  return res.status(405).json({ success: false, message: 'Method not allowed' });
}

// Helper function to get mock features
function getMockFeatures(page, limit, search, sortBy, order, isActive) {
  const features = [
    {
      id: 'feature-1',
      name: 'Premium Spotlight',
      description: 'Get your profile featured at the top of search results for 24 hours',
      price: 499,
      discountPercent: 10,
      discountedPrice: 449.1,
      durationHours: 24,
      defaultCount: 1,
      isActive: true,
      purchaseCount: 150,
      activeCount: 12,
      revenue: 67365,
      createdAt: '2023-01-15T10:30:00Z',
      updatedAt: '2023-02-20T14:15:00Z'
    },
    {
      id: 'feature-2',
      name: 'Super Spotlight',
      description: 'Get your profile featured in the spotlight section for 48 hours',
      price: 799,
      discountPercent: null,
      discountedPrice: null,
      durationHours: 48,
      defaultCount: 1,
      isActive: true,
      purchaseCount: 85,
      activeCount: 8,
      revenue: 67915,
      createdAt: '2023-02-10T09:45:00Z',
      updatedAt: '2023-02-10T09:45:00Z'
    },
    {
      id: 'feature-3',
      name: 'Weekend Spotlight',
      description: 'Get your profile featured throughout the weekend (Friday to Sunday)',
      price: 999,
      discountPercent: 15,
      discountedPrice: 849.15,
      durationHours: 72,
      defaultCount: 1,
      isActive: true,
      purchaseCount: 65,
      activeCount: 5,
      revenue: 55194.75,
      createdAt: '2023-03-05T11:20:00Z',
      updatedAt: '2023-04-10T16:30:00Z'
    },
    {
      id: 'feature-4',
      name: 'Quick Boost',
      description: 'A short 12-hour boost to get more visibility',
      price: 299,
      discountPercent: null,
      discountedPrice: null,
      durationHours: 12,
      defaultCount: 1,
      isActive: false,
      purchaseCount: 40,
      activeCount: 0,
      revenue: 11960,
      createdAt: '2023-04-20T13:10:00Z',
      updatedAt: '2023-05-15T10:05:00Z'
    },
    {
      id: 'feature-5',
      name: 'Spotlight Pack',
      description: 'Get 3 spotlight features to use whenever you want',
      price: 1199,
      discountPercent: 20,
      discountedPrice: 959.2,
      durationHours: 24,
      defaultCount: 3,
      isActive: true,
      purchaseCount: 55,
      activeCount: 10,
      revenue: 52756,
      createdAt: '2023-05-25T15:40:00Z',
      updatedAt: '2023-05-25T15:40:00Z'
    },
    {
      id: 'feature-6',
      name: 'Festival Special',
      description: 'Special spotlight during festival season with extended visibility',
      price: 1499,
      discountPercent: 25,
      discountedPrice: 1124.25,
      durationHours: 96,
      defaultCount: 1,
      isActive: true,
      purchaseCount: 30,
      activeCount: 6,
      revenue: 33727.5,
      createdAt: '2023-06-10T09:15:00Z',
      updatedAt: '2023-07-05T11:30:00Z'
    }
  ];
  
  // Apply filters
  let filteredFeatures = [...features];
  
  if (search) {
    const searchLower = search.toLowerCase();
    filteredFeatures = filteredFeatures.filter(f => 
      f.name.toLowerCase().includes(searchLower) ||
      f.description.toLowerCase().includes(searchLower)
    );
  }
  
  if (isActive !== '') {
    const isActiveValue = isActive === 'true';
    filteredFeatures = filteredFeatures.filter(f => f.isActive === isActiveValue);
  }
  
  // Apply sorting
  filteredFeatures.sort((a, b) => {
    const aValue = a[sortBy];
    const bValue = b[sortBy];
    
    if (typeof aValue === 'string') {
      return order === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    } else {
      return order === 'asc' ? aValue - bValue : bValue - aValue;
    }
  });
  
  // Apply pagination
  const pageNum = parseInt(page, 10);
  const limitNum = parseInt(limit, 10);
  const startIndex = (pageNum - 1) * limitNum;
  const endIndex = startIndex + limitNum;
  
  return filteredFeatures.slice(startIndex, endIndex);
}
