"""
Simplified Flask API for matrimony matching service
Fast startup alternative to the full PyTorch implementation
"""

import os
import json
import logging
from flask import Flask, request, jsonify
from flask_cors import CORS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Simple in-memory model flag
model_ready = False

@app.route('/health', methods=['GET'])
def health():
    """Health check endpoint"""
    return jsonify({
        'success': True,
        'message': 'ML Matching Service is running',
        'model_initialized': model_ready,
        'version': 'simple-v1.0'
    })

@app.route('/match', methods=['POST'])
def match():
    """Simple matching endpoint with fallback logic"""
    try:
        data = request.json
        logger.info(f"Received matching request for user: {data.get('userId', 'unknown')}")
        
        # Extract data
        user = data.get('user', {})
        preferences = data.get('preferences', {})
        potential_matches = data.get('potential_matches', [])
        algorithm_version = data.get('algorithm_version', 'v1.0')
        
        matches = []
        for match in potential_matches:
            # Calculate compatibility score
            score = calculate_compatibility_score(user, match, preferences, algorithm_version)
            matches.append({
                'userId': match.get('id'),
                'score': int(score * 100),
                'reasons': get_match_reasons(user, match, preferences)
            })
        
        # Sort by score (highest first)
        matches.sort(key=lambda x: x['score'], reverse=True)
        
        # Limit results
        max_results = data.get('max_results', 50)
        matches = matches[:max_results]
        
        logger.info(f"Generated {len(matches)} matches for user")
        
        return jsonify({
            'success': True,
            'matches': matches,
            'algorithm_version': algorithm_version,
            'total_processed': len(potential_matches)
        })
        
    except Exception as e:
        logger.error(f"Error in matching: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error: {str(e)}'
        }), 500

def calculate_compatibility_score(user, match, preferences, algorithm_version):
    """Calculate compatibility score based on algorithm version"""
    
    if algorithm_version == 'v1.0':
        return calculate_rule_based_score(user, match, preferences)
    elif algorithm_version == 'v1.5':
        return calculate_flexible_score(user, match, preferences)
    elif algorithm_version == 'v2.0':
        return calculate_personalized_score(user, match, preferences)
    elif algorithm_version == 'v2.5':
        return calculate_intelligent_score(user, match, preferences)
    elif algorithm_version == 'v3.0':
        return calculate_advanced_score(user, match, preferences)
    else:
        return calculate_rule_based_score(user, match, preferences)

def calculate_rule_based_score(user, match, preferences):
    """Rule-based matching (v1.0)"""
    score = 0.0
    
    # Age compatibility (25% weight)
    user_age = user.get('age', 25)
    match_age = match.get('age', 25)
    age_diff = abs(user_age - match_age)
    
    if age_diff <= 2:
        score += 0.25
    elif age_diff <= 5:
        score += 0.15
    elif age_diff <= 8:
        score += 0.05
    
    # Education compatibility (20% weight)
    user_education = user.get('education', '').lower()
    match_education = match.get('education', '').lower()
    
    if user_education == match_education:
        score += 0.20
    elif 'engineering' in user_education and 'engineering' in match_education:
        score += 0.15
    elif any(level in user_education and level in match_education for level in ['graduate', 'master', 'phd']):
        score += 0.10
    
    # Location compatibility (15% weight)
    user_city = user.get('city', '').lower()
    match_city = match.get('city', '').lower()
    user_state = user.get('state', '').lower()
    match_state = match.get('state', '').lower()
    
    if user_city == match_city:
        score += 0.15
    elif user_state == match_state:
        score += 0.10
    
    # Occupation compatibility (15% weight)
    user_occupation = user.get('occupation', '').lower()
    match_occupation = match.get('occupation', '').lower()
    
    if user_occupation == match_occupation:
        score += 0.15
    elif any(field in user_occupation and field in match_occupation for field in ['software', 'engineer', 'doctor', 'teacher']):
        score += 0.10
    
    # Maratha subcaste compatibility (25% weight)
    user_subcaste = user.get('subcaste', '').lower()
    match_subcaste = match.get('subcaste', '').lower()
    
    if user_subcaste == match_subcaste:
        score += 0.25
    elif 'maratha' in user_subcaste and 'maratha' in match_subcaste:
        score += 0.15
    
    return min(score, 1.0)

def calculate_flexible_score(user, match, preferences):
    """Flexible matching with preferences (v1.5)"""
    base_score = calculate_rule_based_score(user, match, preferences)
    
    # Apply preference adjustments
    preference_bonus = 0.0
    
    # Height preferences
    user_height = user.get('height', 5.5)
    match_height = match.get('height', 5.5)
    height_pref = preferences.get('height_range', [4.5, 6.5])
    
    if height_pref[0] <= match_height <= height_pref[1]:
        preference_bonus += 0.1
    
    # Income preferences
    match_income = match.get('income', 0)
    income_pref = preferences.get('income_range', [0, 10000000])
    
    if income_pref[0] <= match_income <= income_pref[1]:
        preference_bonus += 0.1
    
    return min(base_score + preference_bonus, 1.0)

def calculate_personalized_score(user, match, preferences):
    """Personalized matching with learning (v2.0)"""
    flexible_score = calculate_flexible_score(user, match, preferences)
    
    # Add personalization factors
    personalization_bonus = 0.0
    
    # Profile completeness bonus
    user_completeness = calculate_profile_completeness(user)
    match_completeness = calculate_profile_completeness(match)
    
    if user_completeness > 0.8 and match_completeness > 0.8:
        personalization_bonus += 0.05
    
    # Activity level bonus
    if match.get('last_active_days', 30) <= 7:
        personalization_bonus += 0.05
    
    return min(flexible_score + personalization_bonus, 1.0)

def calculate_intelligent_score(user, match, preferences):
    """Intelligent matching with advanced insights (v2.5)"""
    personalized_score = calculate_personalized_score(user, match, preferences)
    
    # Add intelligent factors
    intelligence_bonus = 0.0
    
    # Family background compatibility
    user_family_type = user.get('family_type', '').lower()
    match_family_type = match.get('family_type', '').lower()
    
    if user_family_type == match_family_type:
        intelligence_bonus += 0.05
    
    # Lifestyle compatibility
    user_lifestyle = user.get('lifestyle', '').lower()
    match_lifestyle = match.get('lifestyle', '').lower()
    
    if user_lifestyle == match_lifestyle:
        intelligence_bonus += 0.05
    
    return min(personalized_score + intelligence_bonus, 1.0)

def calculate_advanced_score(user, match, preferences):
    """Advanced AI matching with deep learning simulation (v3.0)"""
    intelligent_score = calculate_intelligent_score(user, match, preferences)
    
    # Simulate advanced AI factors
    ai_bonus = 0.0
    
    # Communication style compatibility
    user_communication = user.get('communication_style', 'moderate')
    match_communication = match.get('communication_style', 'moderate')
    
    if user_communication == match_communication:
        ai_bonus += 0.03
    
    # Interest compatibility
    user_interests = set(user.get('interests', []))
    match_interests = set(match.get('interests', []))
    
    if user_interests and match_interests:
        common_interests = len(user_interests.intersection(match_interests))
        total_interests = len(user_interests.union(match_interests))
        if total_interests > 0:
            ai_bonus += (common_interests / total_interests) * 0.07
    
    return min(intelligent_score + ai_bonus, 1.0)

def calculate_profile_completeness(profile):
    """Calculate profile completeness percentage"""
    required_fields = ['age', 'education', 'occupation', 'city', 'state']
    optional_fields = ['height', 'income', 'family_type', 'lifestyle', 'interests']
    
    completed_required = sum(1 for field in required_fields if profile.get(field))
    completed_optional = sum(1 for field in optional_fields if profile.get(field))
    
    required_score = completed_required / len(required_fields)
    optional_score = completed_optional / len(optional_fields)
    
    return (required_score * 0.7) + (optional_score * 0.3)

def get_match_reasons(user, match, preferences):
    """Get reasons for the match"""
    reasons = []
    
    # Age compatibility
    age_diff = abs(user.get('age', 25) - match.get('age', 25))
    if age_diff <= 3:
        reasons.append("Similar age group")
    
    # Education
    if user.get('education', '').lower() == match.get('education', '').lower():
        reasons.append("Same educational background")
    
    # Location
    if user.get('city', '').lower() == match.get('city', '').lower():
        reasons.append("Same city")
    elif user.get('state', '').lower() == match.get('state', '').lower():
        reasons.append("Same state")
    
    # Subcaste
    if user.get('subcaste', '').lower() == match.get('subcaste', '').lower():
        reasons.append("Same Maratha subcaste")
    
    # Professional compatibility
    user_occ = user.get('occupation', '').lower()
    match_occ = match.get('occupation', '').lower()
    if any(field in user_occ and field in match_occ for field in ['software', 'engineer', 'doctor']):
        reasons.append("Professional compatibility")
    
    return reasons[:3]  # Return top 3 reasons

@app.route('/algorithms', methods=['GET'])
def get_algorithms():
    """Get available algorithm versions"""
    return jsonify({
        'success': True,
        'algorithms': [
            {'version': 'v1.0', 'name': 'Rule-Based Matching', 'description': 'Traditional compatibility matching'},
            {'version': 'v1.5', 'name': 'Flexible Matching', 'description': 'Rule-based with user preferences'},
            {'version': 'v2.0', 'name': 'Personalized Matching', 'description': 'Learning user preferences'},
            {'version': 'v2.5', 'name': 'Intelligent Matching', 'description': 'Advanced compatibility insights'},
            {'version': 'v3.0', 'name': 'Advanced AI Matching', 'description': 'Deep learning simulation'}
        ]
    })

if __name__ == '__main__':
    global model_ready
    model_ready = True
    logger.info("Simple ML Matching Service initialized successfully")
    logger.info("All algorithm versions (v1.0 to v3.0) are available")
    
    port = int(os.getenv('ML_SERVICE_PORT', 5000))
    logger.info(f"Starting ML Service on port {port}")
    
    # Run with production settings
    app.run(host='0.0.0.0', port=port, debug=False, threaded=True)
