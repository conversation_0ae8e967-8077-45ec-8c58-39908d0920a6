#!/usr/bin/env node

/**
 * Test Brevo Email using API (Alternative to SMTP)
 */

require('dotenv').config();
const axios = require('axios');

async function testBrevoAPI() {
    console.log('📧 Testing Brevo Email API...\n');
    
    const apiKey = process.env.BREVO_API_KEY;
    const fromEmail = process.env.SMTP_USER || '<EMAIL>';
    
    if (!apiKey) {
        console.log('❌ BREVO_API_KEY not found in .env file');
        return false;
    }
    
    console.log('✅ Brevo API Key found');
    console.log(`From Email: ${fromEmail}`);
    
    try {
        // Test 1: Send Welcome Email via Brevo API
        console.log('\n1️⃣ Testing Welcome Email via Brevo API...');
        
        const emailData = {
            sender: {
                name: "Vaivahik",
                email: fromEmail
            },
            to: [
                {
                    email: fromEmail,
                    name: "Test User"
                }
            ],
            subject: "🎉 Welcome to Vaivahik - API Test",
            htmlContent: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #7e57c2; text-align: center;">Welcome to Vaivahik! 💕</h2>
                    <p>Hello Test User,</p>
                    <p>This is a test email sent via <strong>Brevo API</strong> from your Vaivahik matrimony app!</p>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
                        <h3>✅ Email Service Status:</h3>
                        <ul>
                            <li>Brevo API: Working perfectly!</li>
                            <li>Daily Limit: 300 emails</li>
                            <li>Perfect for matrimony app</li>
                        </ul>
                    </div>
                    <p>Best regards,<br>The Vaivahik Team</p>
                </div>
            `
        };
        
        const response = await axios.post(
            'https://api.brevo.com/v3/smtp/email',
            emailData,
            {
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'api-key': apiKey
                }
            }
        );
        
        console.log('✅ Welcome email sent successfully via API');
        console.log(`Message ID: ${response.data.messageId}`);
        
        // Test 2: Send Match Notification
        console.log('\n2️⃣ Testing Match Notification Email...');
        
        const matchEmailData = {
            sender: {
                name: "Vaivahik",
                email: fromEmail
            },
            to: [
                {
                    email: fromEmail,
                    name: "Test User"
                }
            ],
            subject: "💕 New Match Found - Vaivahik",
            htmlContent: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #7e57c2; text-align: center;">New Match Found! 🎉</h2>
                    <p>Hello Test User,</p>
                    <p>Great news! We found a potential match for you:</p>
                    
                    <div style="border: 2px solid #7e57c2; padding: 20px; margin: 20px 0; border-radius: 10px; background: #f8f9fa;">
                        <h3 style="color: #7e57c2; margin-top: 0;">Match Profile</h3>
                        <p><strong>Name:</strong> Sample Match</p>
                        <p><strong>Age:</strong> 28 years</p>
                        <p><strong>Location:</strong> Mumbai, Maharashtra</p>
                        <p><strong>Education:</strong> Software Engineer</p>
                        <p><strong>Interests:</strong> Reading, Travel, Music</p>
                        <p><strong>Family:</strong> Joint family, 2 siblings</p>
                    </div>
                    
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="http://localhost:3000/dashboard" 
                           style="background: #7e57c2; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; display: inline-block;">
                           View Complete Profile
                        </a>
                    </div>
                    
                    <p>Login to your Vaivahik account to connect and start your journey!</p>
                    <p>Best regards,<br>The Vaivahik Team</p>
                </div>
            `
        };
        
        const matchResponse = await axios.post(
            'https://api.brevo.com/v3/smtp/email',
            matchEmailData,
            {
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'api-key': apiKey
                }
            }
        );
        
        console.log('✅ Match notification sent successfully');
        console.log(`Message ID: ${matchResponse.data.messageId}`);
        
        return true;
        
    } catch (error) {
        console.log('\n❌ Brevo API test failed:');
        
        if (error.response) {
            console.log(`Status: ${error.response.status}`);
            console.log(`Error: ${JSON.stringify(error.response.data, null, 2)}`);
            
            if (error.response.status === 401) {
                console.log('\n💡 API Key Issues:');
                console.log('1. Check if BREVO_API_KEY is correct');
                console.log('2. Verify API key is active in Brevo dashboard');
                console.log('3. Ensure API key has email sending permissions');
            }
            
            if (error.response.status === 400) {
                console.log('\n💡 Email Format Issues:');
                console.log('1. Check sender email is verified in Brevo');
                console.log('2. Verify email addresses are valid');
            }
        } else {
            console.log(`Error: ${error.message}`);
        }
        
        return false;
    }
}

// Run the test
testBrevoAPI().then(success => {
    if (success) {
        console.log('\n🎉 Brevo API Email Service is working perfectly!');
        console.log('\n📧 Check your email inbox for test emails');
        console.log('\n💡 Recommendation: Use Brevo API instead of SMTP');
        console.log('   - More reliable than SMTP');
        console.log('   - Better error handling');
        console.log('   - Advanced features available');
        console.log('\n📊 Your email service is ready for production!');
    } else {
        console.log('\n❌ Brevo API needs configuration.');
        console.log('\n🔧 Check:');
        console.log('1. Brevo API key in .env file');
        console.log('2. Sender email verified in Brevo');
        console.log('3. Brevo account is active');
    }
    
    process.exit(success ? 0 : 1);
});
