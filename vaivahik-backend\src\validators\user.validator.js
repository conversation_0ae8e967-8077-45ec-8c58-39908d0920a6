// src/validators/user.validator.js

const { body, validationResult } = require('express-validator');
// Optional: Import constants from utils if using strict validation
// const { incomeOptions } = require('../../utils/project.js');

// Define allowed enum values if strict validation is desired
// const photoVisibilityEnum = ['PUBLIC', 'PAID', 'CONNECTIONS_ONLY']; // Defined in Prisma schema
// const genderEnum = ['Male', 'Female', 'Other', 'PreferNotToSay']; // Example
// const maritalStatusEnum = ['Never Married', 'Divorced', 'Widowed', 'Awaiting Divorce', 'Annulled']; // Example

// Validation rules for the updateUserProfile endpoint
const updateUserProfileRules = [
  // Email: Optional, but if provided, must be a valid email format.
  body('email')
    .optional() // Use standard optional for strings
    .trim()
    .isEmail()
    .withMessage('Invalid email format provided.') // Updated message
    .normalizeEmail(),

  // fullName: Optional, string, trimmed, non-empty, length check.
  body('fullName')
    .optional()
    .trim()
    .notEmpty()
    .withMessage('Full name cannot be empty.')
    .isLength({ min: 2, max: 100 })
    .withMessage('Full name must be between 2 and 100 characters.'),

  // gender: Optional, string, trimmed, non-empty.
  body('gender')
    .optional()
    .trim()
    .notEmpty()
    .withMessage('Gender cannot be empty.'),
    // .isIn(genderEnum) // Example: Uncomment and define genderEnum above for strict check
    // .withMessage(`Gender must be one of: ${genderEnum.join(', ')}`),

  // dateOfBirth: Optional, must be a valid date. checkFalsy allows empty string/null.
  body('dateOfBirth')
    .optional({ checkFalsy: true })
    .isISO8601({ strict: true, strictSeparator: true }) // Enforce YYYY-MM-DD format
    .withMessage('Date of birth must be a valid date in YYYY-MM-DD format.')
    .toDate(),

  // birthTime: Optional, string. Add specific format validation if needed.
  body('birthTime')
    .optional()
    .trim()
    // Example: Add regex for HH:MM AM/PM format if required
    // .matches(/^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/i)
    // .withMessage('Birth time must be in HH:MM AM/PM format.')
    .isLength({ max: 10 })
    .withMessage('Birth time is too long.'),

  // birthPlace: Optional, string, length check.
  body('birthPlace')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Birth place cannot exceed 100 characters.'),

  // height: Optional, string, length check.
  body('height')
    .optional()
    .trim()
    .isLength({ max: 20 })
    .withMessage('Height cannot exceed 20 characters.'),

  // city: Optional, string, length check.
  body('city')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('City cannot exceed 100 characters.'),

  // nativePlace: Optional, string, length check.
  body('nativePlace')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Native place cannot exceed 100 characters.'),

  // education: Optional, string, length check.
  body('education')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Education cannot exceed 100 characters.'),

  // occupation: Optional, string, length check.
  body('occupation')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Occupation cannot exceed 100 characters.'),

  // incomeRange: Optional, string, length check.
  body('incomeRange')
    .optional()
    .trim()
    // .isIn(incomeOptions) // Example: Uncomment and import incomeOptions for strict check
    // .withMessage(`Income range must be one of the predefined options.`)
    .isLength({ max: 50 })
    .withMessage('Income range cannot exceed 50 characters.'),

  // fatherName: Optional, string, length check.
  body('fatherName')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Father name cannot exceed 100 characters.'),

  // motherName: Optional, string, length check.
  body('motherName')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Mother name cannot exceed 100 characters.'),

  // uncleName: Optional, string, length check.
  body('uncleName')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Uncle name cannot exceed 100 characters.'),

  // totalSiblings: Optional, must be an integer >= 0. checkFalsy allows empty string/null/0.
  body('totalSiblings')
    .optional({ checkFalsy: true })
    .isInt({ min: 0 })
    .withMessage('Total siblings must be a non-negative whole number.')
    .toInt(),

  // marriedSiblings: Optional, must be an integer >= 0. checkFalsy allows empty string/null/0.
  body('marriedSiblings')
    .optional({ checkFalsy: true })
    .isInt({ min: 0 })
    .withMessage('Married siblings must be a non-negative whole number.')
    .toInt(),

  // familyContact: Optional, string, length check.
  body('familyContact')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Family contact cannot exceed 50 characters.'),

  // latitude: Optional, must be a float between -90 and 90. checkFalsy allows empty string/null/0.
  body('latitude')
    .optional({ checkFalsy: true })
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be a valid number between -90 and 90.')
    .toFloat(),

  // longitude: Optional, must be a float between -180 and 180. checkFalsy allows empty string/null/0.
  body('longitude')
    .optional({ checkFalsy: true })
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be a valid number between -180 and 180.')
    .toFloat(),
];

// Middleware to handle validation results
const validate = (req, res, next) => {
  const errors = validationResult(req);
  if (errors.isEmpty()) {
    return next(); // No errors, proceed to the controller
  }

  // Extract error messages into a more structured format if desired
  const extractedErrors = {};
  errors.array().forEach(err => {
      // Use err.path (or err.param in older versions) as the key
      const field = err.path || err.param;
      if (!extractedErrors[field]) {
          extractedErrors[field] = err.msg; // Store only the first error per field
      }
  });

  // Send 422 Unprocessable Entity response with validation errors
  return res.status(422).json({
    message: "Input validation failed.",
    errors: extractedErrors, // Send structured errors
  });
};

module.exports = {
  updateUserProfileRules,
  validate, // Export the validation handling middleware
};
