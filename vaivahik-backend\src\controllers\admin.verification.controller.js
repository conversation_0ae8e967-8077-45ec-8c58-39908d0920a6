// src/controllers/admin.verification.controller.js

/**
 * @description Review a verification document (approve or reject)
 * @route PUT /api/admin/verification/documents/:documentId/review
 */
exports.reviewVerificationDocument = async (req, res, next) => {
    const prisma = req.prisma;
    const adminId = req.admin?.adminId;
    const { documentId } = req.params;
    const { status, adminNotes } = req.body;
    
    if (!adminId) {
        const error = new Error('Authentication error: Admin ID missing.');
        error.status = 401;
        return next(error);
    }
    
    if (!documentId) {
        const error = new Error('Document ID parameter is required.');
        error.status = 400;
        return next(error);
    }
    
    if (!status || !['APPROVED', 'REJECTED'].includes(status)) {
        const error = new Error('Invalid or missing status. Must be APPROVED or REJECTED.');
        error.status = 400;
        return next(error);
    }
    
    try {
        // Update the document status
        const updatedDocument = await prisma.verificationDocument.update({
            where: { id: documentId },
            data: {
                status: status,
                adminNotes: adminNotes || null,
                reviewedAt: new Date(),
                reviewedBy: adminId
            },
            include: {
                user: {
                    select: {
                        id: true,
                        profileStatus: true,
                        isVerified: true,
                        verificationDocuments: {
                            select: {
                                id: true,
                                status: true
                            }
                        }
                    }
                }
            }
        });
        
        // Check if all documents are approved and update user verification status if needed
        if (status === 'APPROVED') {
            const allDocuments = updatedDocument.user.verificationDocuments;
            const allApproved = allDocuments.every(doc => doc.status === 'APPROVED');
            
            if (allApproved && !updatedDocument.user.isVerified) {
                await prisma.user.update({
                    where: { id: updatedDocument.user.id },
                    data: {
                        isVerified: true,
                        profileStatus: 'ACTIVE'
                    }
                });
                
                console.log(`User ${updatedDocument.user.id} automatically verified after all documents approved.`);
            }
        }
        
        res.status(200).json({
            message: `Document ${status === 'APPROVED' ? 'approved' : 'rejected'} successfully.`,
            document: {
                id: updatedDocument.id,
                status: updatedDocument.status,
                reviewedAt: updatedDocument.reviewedAt
            }
        });
    } catch (error) {
        console.error('Error reviewing verification document:', error);
        next(error);
    }
};

/**
 * @description Approve a user's verification request
 * @route PUT /api/admin/users/:userId/verify
 */
exports.verifyUser = async (req, res, next) => {
    const prisma = req.prisma;
    const { userId } = req.params;
    
    if (!userId) {
        const error = new Error('User ID parameter is required.');
        error.status = 400;
        return next(error);
    }
    
    try {
        const userToVerify = await prisma.user.findUnique({
            where: { id: userId },
            select: {
                profileStatus: true,
                verificationDocuments: {
                    select: {
                        id: true,
                        status: true
                    }
                }
            }
        });
        
        if (!userToVerify) {
            const error = new Error('User not found.');
            error.status = 404;
            return next(error);
        }
        
        if (!['INCOMPLETE', 'PENDING_APPROVAL'].includes(userToVerify.profileStatus)) {
            const error = new Error(`User status is already '${userToVerify.profileStatus}'. No action needed or cannot verify from this state.`);
            error.status = 409;
            return next(error);
        }
        
        // Update all pending documents to approved
        if (userToVerify.verificationDocuments && userToVerify.verificationDocuments.length > 0) {
            await prisma.verificationDocument.updateMany({
                where: {
                    userId: userId,
                    status: 'PENDING_REVIEW'
                },
                data: {
                    status: 'APPROVED',
                    reviewedAt: new Date()
                }
            });
        }
        
        // Update user status
        const updatedUser = await prisma.user.update({
            where: { id: userId },
            data: {
                profileStatus: 'ACTIVE',
                isVerified: true
            },
            select: {
                id: true,
                profileStatus: true,
                isVerified: true,
                profile: {
                    select: {
                        fullName: true
                    }
                }
            }
        });
        
        res.status(200).json({
            message: `User ${updatedUser.profile?.fullName || userId} has been verified successfully.`,
            user: updatedUser
        });
    } catch (error) {
        console.error('Error verifying user:', error);
        next(error);
    }
};

/**
 * @description Reject a user's verification request
 * @route PUT /api/admin/users/:userId/reject-verification
 */
exports.rejectVerification = async (req, res, next) => {
    const prisma = req.prisma;
    const { userId } = req.params;
    const { reason } = req.body;
    
    if (!userId) {
        const error = new Error('User ID parameter is required.');
        error.status = 400;
        return next(error);
    }
    
    try {
        const userToReject = await prisma.user.findUnique({
            where: { id: userId },
            select: {
                profileStatus: true,
                verificationDocuments: {
                    select: {
                        id: true,
                        status: true
                    }
                }
            }
        });
        
        if (!userToReject) {
            const error = new Error('User not found.');
            error.status = 404;
            return next(error);
        }
        
        if (userToReject.profileStatus !== 'PENDING_APPROVAL') {
            const error = new Error(`User status is '${userToReject.profileStatus}'. Cannot reject verification from this state.`);
            error.status = 409;
            return next(error);
        }
        
        // Update all pending documents to rejected
        if (userToReject.verificationDocuments && userToReject.verificationDocuments.length > 0) {
            await prisma.verificationDocument.updateMany({
                where: {
                    userId: userId,
                    status: 'PENDING_REVIEW'
                },
                data: {
                    status: 'REJECTED',
                    adminNotes: reason || 'Verification rejected by admin',
                    reviewedAt: new Date()
                }
            });
        }
        
        // Update user status back to INCOMPLETE
        const updatedUser = await prisma.user.update({
            where: { id: userId },
            data: {
                profileStatus: 'INCOMPLETE',
                isVerified: false
            },
            select: {
                id: true,
                profileStatus: true,
                isVerified: true,
                profile: {
                    select: {
                        fullName: true
                    }
                }
            }
        });
        
        res.status(200).json({
            message: `Verification for user ${updatedUser.profile?.fullName || userId} has been rejected.`,
            user: updatedUser
        });
    } catch (error) {
        console.error('Error rejecting verification:', error);
        next(error);
    }
};
