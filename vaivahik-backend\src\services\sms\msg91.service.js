/**
 * MSG91 SMS Service
 *
 * This service handles sending SMS messages via MSG91, specifically for OTP verification.
 * It uses the approved DLT template for sending OTP messages.
 *
 * Documentation: https://docs.msg91.com/reference/send-otp-1
 */

const axios = require('axios');
const logger = require('../../utils/logger');

// Configuration
let config = {
  apiKey: process.env.MSG91_API_KEY,
  senderId: process.env.MSG91_SENDER_ID,
  dltTemplateId: process.env.MSG91_DLT_TEMPLATE_ID,
  dltPeId: process.env.MSG91_DLT_PE_ID, // Principal Entity ID
  otpLength: 6, // Changed to 6 digits for better security
  otpExpiry: 15, // minutes (MSG91 minimum requirement)
  baseUrl: 'https://control.msg91.com/api',
  useTemplate: true, // Whether to use DLT template or not
  otpTemplate: '##OTP## is the OTP to verify your mobile number -Maratha Wedding'
};

/**
 * Initialize the MSG91 service with configuration
 * @param {Object} options - Configuration options
 */
const initialize = (options = {}) => {
  config = { ...config, ...options };

  // Validate required configuration
  if (!config.apiKey) {
    console.error('MSG91 API key is required');
  }

  if (!config.senderId) {
    console.error('MSG91 Sender ID is required');
  }

  if (!config.dltTemplateId) {
    console.error('MSG91 DLT Template ID is required');
  }

  console.log('MSG91 service initialized');
};

/**
 * Send OTP via MSG91 (Enhanced with robust error handling and fallback)
 * @param {string} phone - Phone number to send OTP to (with country code)
 * @param {string} otp - OTP to send (optional - MSG91 will generate if not provided)
 * @returns {Promise<Object>} Response from MSG91
 */
const sendOtp = async (phone, otp = null) => {
  try {
    // Validate phone number
    if (!phone) {
      throw new Error('Phone number is required');
    }

    // Validate configuration
    if (!config.apiKey || !config.dltTemplateId) {
      throw new Error('MSG91 configuration is incomplete. Check API key and template ID.');
    }

    // Format phone number (ensure it has country code)
    const formattedPhone = formatPhoneNumber(phone);

    // Enhanced request options with better error handling
    const requestOptions = {
      method: 'POST',
      url: 'https://control.msg91.com/api/v5/otp',
      params: {
        otp_expiry: config.otpExpiry.toString(),
        template_id: config.dltTemplateId,
        mobile: formattedPhone,
        authkey: config.apiKey,
        realTimeResponse: '1',
        otp_length: config.otpLength.toString()
      },
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: 30000, // 30 second timeout
      data: {}
    };

    // Add custom OTP if provided
    if (otp) {
      requestOptions.params.otp = otp;
    }

    logger.info(`Sending OTP to ${formattedPhone} via MSG91 v5 API`);

    // Send OTP using the MSG91 v5 API with retry logic
    let response;
    let lastError;

    for (let attempt = 1; attempt <= 3; attempt++) {
      try {
        response = await axios.request(requestOptions);
        break; // Success, exit retry loop
      } catch (error) {
        lastError = error;
        logger.warn(`MSG91 API attempt ${attempt} failed:`, error.message);

        if (attempt < 3) {
          // Wait before retry (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, attempt * 1000));
        }
      }
    }

    if (!response) {
      throw lastError || new Error('Failed to get response from MSG91 after 3 attempts');
    }

    // Enhanced response validation
    const isSuccess = response.data && (
      response.data.type === 'success' ||
      response.status === 200 ||
      (response.data.message && response.data.message.toLowerCase().includes('success'))
    );

    if (isSuccess) {
      logger.info(`OTP sent successfully to ${formattedPhone}`);
      return {
        success: true,
        message: 'OTP sent successfully',
        data: response.data,
        provider: 'MSG91',
        timestamp: new Date().toISOString()
      };
    } else {
      logger.error(`Failed to send OTP to ${formattedPhone}: ${JSON.stringify(response.data)}`);
      return {
        success: false,
        message: response.data?.message || 'Failed to send OTP',
        data: response.data,
        provider: 'MSG91'
      };
    }
  } catch (error) {
    logger.error('Error sending OTP via MSG91:', error);

    // Enhanced error categorization
    let errorCategory = 'UNKNOWN';
    let shouldRetry = false;

    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      errorCategory = 'NETWORK';
      shouldRetry = true;
    } else if (error.response?.status === 401 || error.response?.status === 403) {
      errorCategory = 'AUTHENTICATION';
    } else if (error.response?.status >= 500) {
      errorCategory = 'SERVER_ERROR';
      shouldRetry = true;
    } else if (error.code === 'ECONNABORTED') {
      errorCategory = 'TIMEOUT';
      shouldRetry = true;
    }

    return {
      success: false,
      message: error.message || 'Failed to send OTP',
      error: error,
      errorCategory,
      shouldRetry,
      provider: 'MSG91',
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * Verify OTP via MSG91 (Enhanced with robust error handling)
 * @param {string} phone - Phone number to verify OTP for (with country code)
 * @param {string} otp - OTP to verify
 * @returns {Promise<Object>} Response from MSG91
 */
const verifyOtp = async (phone, otp) => {
  try {
    // Validate inputs
    if (!phone || !otp) {
      throw new Error('Phone number and OTP are required');
    }

    // Validate configuration
    if (!config.apiKey) {
      throw new Error('MSG91 API key is not configured');
    }

    // Format phone number
    const formattedPhone = formatPhoneNumber(phone);

    // Enhanced request options
    const requestOptions = {
      method: 'GET',
      url: 'https://control.msg91.com/api/v5/otp/verify',
      params: {
        otp: otp.toString().trim(),
        mobile: formattedPhone
      },
      headers: {
        authkey: config.apiKey,
        'Accept': 'application/json'
      },
      timeout: 30000 // 30 second timeout
    };

    logger.info(`Verifying OTP for ${formattedPhone} via MSG91 v5 API`);

    // Verify OTP using the MSG91 v5 API with retry logic
    let response;
    let lastError;

    for (let attempt = 1; attempt <= 2; attempt++) {
      try {
        response = await axios.request(requestOptions);
        break; // Success, exit retry loop
      } catch (error) {
        lastError = error;
        logger.warn(`MSG91 verify attempt ${attempt} failed:`, error.message);

        if (attempt < 2) {
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }

    if (!response) {
      throw lastError || new Error('Failed to get response from MSG91 verification service');
    }

    // Enhanced verification response validation
    const isVerified = response.data && (
      response.data.type === 'success' ||
      response.status === 200 ||
      (response.data.message && response.data.message.toLowerCase().includes('success')) ||
      (response.data.message && response.data.message.toLowerCase().includes('verified'))
    );

    if (isVerified) {
      logger.info(`OTP verified successfully for ${formattedPhone}`);
      return {
        success: true,
        message: 'OTP verified successfully',
        data: response.data,
        provider: 'MSG91',
        timestamp: new Date().toISOString()
      };
    } else {
      logger.warn(`OTP verification failed for ${formattedPhone}: ${JSON.stringify(response.data)}`);
      return {
        success: false,
        message: response.data?.message || 'Invalid or expired OTP',
        data: response.data,
        provider: 'MSG91',
        errorType: 'VERIFICATION_FAILED'
      };
    }
  } catch (error) {
    logger.error('Error verifying OTP via MSG91:', error);

    // Enhanced error categorization for verification
    let errorCategory = 'UNKNOWN';
    let userMessage = 'Failed to verify OTP';

    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      errorCategory = 'NETWORK';
      userMessage = 'Network error. Please try again.';
    } else if (error.response?.status === 401 || error.response?.status === 403) {
      errorCategory = 'AUTHENTICATION';
      userMessage = 'Service authentication error. Please contact support.';
    } else if (error.response?.status >= 500) {
      errorCategory = 'SERVER_ERROR';
      userMessage = 'Service temporarily unavailable. Please try again.';
    } else if (error.code === 'ECONNABORTED') {
      errorCategory = 'TIMEOUT';
      userMessage = 'Request timeout. Please try again.';
    }

    return {
      success: false,
      message: userMessage,
      error: error,
      errorCategory,
      provider: 'MSG91',
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * Resend OTP via MSG91
 * @param {string} phone - Phone number to resend OTP to (with country code)
 * @param {string} retryType - Type of retry: 'text' or 'voice'
 * @returns {Promise<Object>} Response from MSG91
 */
const resendOtp = async (phone, retryType = 'text') => {
  try {
    // Validate phone number
    if (!phone) {
      throw new Error('Phone number is required');
    }

    // Format phone number
    const formattedPhone = formatPhoneNumber(phone);

    // Validate retry type
    if (retryType !== 'text' && retryType !== 'voice') {
      logger.warn(`Invalid retry type: ${retryType}. Using 'text' as default.`);
      retryType = 'text';
    }

    // Use the working MSG91 v5 resend API (from your snippets)
    const requestOptions = {
      method: 'GET',
      url: 'https://control.msg91.com/api/v5/otp/retry',
      params: {
        authkey: config.apiKey,
        retrytype: retryType,
        mobile: formattedPhone
      }
    };

    logger.info(`Resending OTP to ${formattedPhone} via MSG91 v5 API (${retryType} mode)`);

    // Resend OTP using the working MSG91 v5 API
    const response = await axios.request(requestOptions);

    // Check if the response indicates success (MSG91 v5 format)
    const isSuccess = response.data && response.data.type === 'success';

    if (isSuccess) {
      logger.info(`OTP resent successfully to ${formattedPhone} (${retryType} mode)`);
      return {
        success: true,
        message: `OTP resent successfully via ${retryType}`,
        data: response.data
      };
    } else {
      logger.error(`Failed to resend OTP to ${formattedPhone}: ${JSON.stringify(response.data)}`);
      return {
        success: false,
        message: response.data?.message || 'Failed to resend OTP',
        data: response.data
      };
    }
  } catch (error) {
    logger.error('Error resending OTP via MSG91:', error);
    return {
      success: false,
      message: error.message || 'Failed to resend OTP',
      error: error
    };
  }
};

/**
 * Format phone number to ensure it has country code
 * @param {string} phone - Phone number
 * @returns {string} Formatted phone number
 */
const formatPhoneNumber = (phone) => {
  // Remove any non-digit characters
  let cleaned = phone.replace(/\D/g, '');

  // If the number starts with a plus sign, remove it
  if (phone.startsWith('+')) {
    cleaned = phone.substring(1).replace(/\D/g, '');
  }

  // If the number doesn't start with country code (e.g., 91 for India)
  // add the default country code (91 for India)
  if (!cleaned.startsWith('91') && cleaned.length === 10) {
    cleaned = '91' + cleaned;
  }

  return cleaned;
};

/**
 * Generate a random OTP
 * @param {number} length - Length of the OTP (default: from config)
 * @returns {string} Generated OTP
 */
const generateOtp = (length = config.otpLength) => {
  // Ensure length is a valid number
  const otpLength = parseInt(length) || 6;

  // Generate a random OTP of the specified length
  let otp = '';
  for (let i = 0; i < otpLength; i++) {
    otp += Math.floor(Math.random() * 10);
  }

  return otp;
};

module.exports = {
  initialize,
  sendOtp,
  verifyOtp,
  resendOtp,
  generateOtp,
  formatPhoneNumber
};
