// API endpoint for API discovery
import { detectApiEndpoints, testEndpointMethod, categorizeEndpoints } from '@/utils/apiEndpointDetector';

export default async function handler(req, res) {
  // Set CORS headers to allow API testing from different origins
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return await discoverEndpoints(req, res);
    case 'POST':
      return await testEndpoint(req, res);
    default:
      return res.status(405).json({ 
        success: false,
        message: 'Method not allowed' 
      });
  }
}

/**
 * Discover API endpoints
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
async function discoverEndpoints(req, res) {
  try {
    // Get base URL from query parameter or use current host
    const baseUrl = req.query.baseUrl || (req.headers.host ? `http://${req.headers.host}` : 'http://localhost:3000');
    
    // Detect endpoints
    const endpoints = await detectApiEndpoints(baseUrl);
    
    // Categorize endpoints
    const categorizedEndpoints = categorizeEndpoints(endpoints);
    
    // Return the endpoints
    return res.status(200).json({
      success: true,
      baseUrl,
      endpoints,
      categorizedEndpoints,
      count: endpoints.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error discovering API endpoints:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to discover API endpoints',
      error: error.message
    });
  }
}

/**
 * Test a specific endpoint with a specific method
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
async function testEndpoint(req, res) {
  try {
    const { baseUrl, path, method, payload } = req.body;
    
    if (!baseUrl || !path || !method) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameters: baseUrl, path, and method are required'
      });
    }
    
    // Test the endpoint
    const result = await testEndpointMethod(baseUrl, path, method, payload);
    
    // Return the result
    return res.status(200).json({
      success: true,
      result,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error testing endpoint:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to test endpoint',
      error: error.message
    });
  }
}
