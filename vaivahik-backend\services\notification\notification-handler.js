/**
 * Notification Event Handler
 * 
 * This service provides a simple interface to trigger notifications from anywhere in the application.
 * It handles the complexity of notification delivery through multiple channels.
 */
const enhancedNotificationService = require('./enhanced-notification-service');
const eventNotifications = require('./event-notifications');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * Trigger a notification for a specific event
 * 
 * @param {string} eventType - Type of event (e.g., 'NEW_MATCH', 'PROFILE_VIEW')
 * @param {Object} eventData - Data related to the event
 * @returns {Promise<Object>} Result of the notification operation
 */
const triggerNotification = async (eventType, eventData) => {
  try {
    let result;
    
    switch (eventType) {
      case 'NEW_MATCH':
        result = await eventNotifications.notifyNewMatch(
          eventData.userId,
          {
            matchId: eventData.matchId,
            matchName: eventData.matchName,
            matchPhotoUrl: eventData.matchPhotoUrl,
            matchPercentage: eventData.matchPercentage
          }
        );
        break;
        
      case 'PROFILE_VIEW':
        result = await eventNotifications.notifyProfileView(
          eventData.profileOwnerId,
          {
            viewerId: eventData.viewerId,
            viewerName: eventData.viewerName,
            viewerPhotoUrl: eventData.viewerPhotoUrl
          }
        );
        break;
        
      case 'INTEREST_RECEIVED':
        result = await eventNotifications.notifyInterestReceived(
          eventData.recipientId,
          {
            senderId: eventData.senderId,
            senderName: eventData.senderName,
            senderPhotoUrl: eventData.senderPhotoUrl,
            interestId: eventData.interestId
          }
        );
        break;
        
      case 'INTEREST_ACCEPTED':
        result = await eventNotifications.notifyInterestAccepted(
          eventData.senderId,
          {
            acceptorId: eventData.acceptorId,
            acceptorName: eventData.acceptorName,
            acceptorPhotoUrl: eventData.acceptorPhotoUrl
          }
        );
        break;
        
      case 'NEW_MESSAGE':
        result = await eventNotifications.notifyNewMessage(
          eventData.recipientId,
          {
            senderId: eventData.senderId,
            senderName: eventData.senderName,
            senderPhotoUrl: eventData.senderPhotoUrl,
            messagePreview: eventData.messagePreview,
            conversationId: eventData.conversationId
          }
        );
        break;
        
      case 'VERIFICATION_STATUS':
        result = await eventNotifications.notifyVerificationStatus(
          eventData.userId,
          {
            status: eventData.status,
            reason: eventData.reason
          }
        );
        break;
        
      case 'CUSTOM':
        // For custom notifications that don't fit the predefined types
        result = await enhancedNotificationService.sendToUser(
          eventData.userId,
          {
            title: eventData.title,
            body: eventData.body,
            imageUrl: eventData.imageUrl,
            data: eventData.data || {}
          },
          eventData.options || {}
        );
        break;
        
      default:
        throw new Error(`Unknown event type: ${eventType}`);
    }
    
    return result;
  } catch (error) {
    console.error(`Error triggering notification for event ${eventType}:`, error);
    return { success: false, error: error.message };
  }
};

/**
 * Trigger a notification for multiple users
 * 
 * @param {string} eventType - Type of event
 * @param {Array<Object>} eventsData - Array of event data objects for each user
 * @returns {Promise<Object>} Result of the notification operations
 */
const triggerBulkNotifications = async (eventType, eventsData) => {
  try {
    const results = await Promise.all(
      eventsData.map(eventData => triggerNotification(eventType, eventData))
    );
    
    return {
      success: true,
      results
    };
  } catch (error) {
    console.error(`Error triggering bulk notifications for event ${eventType}:`, error);
    return { success: false, error: error.message };
  }
};

/**
 * Trigger a notification for a user segment
 * 
 * @param {string} segment - User segment ('PREMIUM_USERS', 'FREE_USERS', 'VERIFIED_USERS', 'ALL_USERS')
 * @param {Object} notificationData - Notification content
 * @param {Object} options - Options for the notification
 * @returns {Promise<Object>} Result of the notification operation
 */
const triggerSegmentNotification = async (segment, notificationData, options = {}) => {
  try {
    // Create notification content
    const notification = {
      title: notificationData.title,
      body: notificationData.body,
      imageUrl: notificationData.imageUrl,
      data: notificationData.data || {}
    };
    
    // Send to segment
    return await enhancedNotificationService.sendToUsers(
      [], // User IDs will be determined by the service based on segment
      notification,
      {
        ...options,
        segment
      }
    );
  } catch (error) {
    console.error(`Error triggering segment notification for ${segment}:`, error);
    return { success: false, error: error.message };
  }
};

/**
 * Schedule a notification for future delivery
 * 
 * @param {string} eventType - Type of event
 * @param {Object} eventData - Data related to the event
 * @param {Date} scheduledFor - When to deliver the notification
 * @returns {Promise<Object>} Result of the scheduling operation
 */
const scheduleNotification = async (eventType, eventData, scheduledFor) => {
  try {
    // Determine notification type and target based on event type
    let notificationType, targetType, targetId, data;
    
    switch (eventType) {
      case 'CUSTOM':
        notificationType = 'promotional'; // Default for custom notifications
        targetType = 'USER';
        targetId = eventData.userId;
        data = {
          title: eventData.title,
          body: eventData.body,
          imageUrl: eventData.imageUrl,
          targetUrl: eventData.targetUrl
        };
        break;
        
      case 'SEGMENT':
        notificationType = 'promotional';
        targetType = eventData.segment;
        targetId = null;
        data = {
          title: eventData.title,
          body: eventData.body,
          imageUrl: eventData.imageUrl,
          targetUrl: eventData.targetUrl
        };
        break;
        
      default:
        throw new Error(`Scheduling not supported for event type: ${eventType}`);
    }
    
    // Schedule the notification
    return await enhancedNotificationService.scheduleNotification(
      notificationType,
      targetType,
      targetId,
      scheduledFor,
      data
    );
  } catch (error) {
    console.error(`Error scheduling notification for event ${eventType}:`, error);
    return { success: false, error: error.message };
  }
};

module.exports = {
  triggerNotification,
  triggerBulkNotifications,
  triggerSegmentNotification,
  scheduleNotification
};
