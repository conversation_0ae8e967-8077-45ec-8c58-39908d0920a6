/**
 * Error Monitoring Controller
 * 
 * This controller provides endpoints for monitoring and analyzing errors in the application.
 * It allows administrators to view error trends, search for specific errors,
 * and get detailed information about errors.
 */

const { getErrorAnalytics, resetDailyStats } = require('../../services/errorAnalytics.service');
const { Sentry } = require('@sentry/node');

/**
 * Get error analytics overview
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.getErrorOverview = async (req, res, next) => {
  try {
    const { timeframe = 'day', limit = 10 } = req.query;
    
    // Get error analytics overview
    const overview = getErrorAnalytics({
      timeframe,
      type: 'overview',
      limit: parseInt(limit)
    });
    
    res.status(200).json({
      success: true,
      message: 'Error analytics overview retrieved successfully',
      data: overview
    });
  } catch (error) {
    console.error('Error getting error analytics overview:', error);
    next(error);
  }
};

/**
 * Get errors by type
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.getErrorsByType = async (req, res, next) => {
  try {
    const { timeframe = 'day', limit = 10 } = req.query;
    
    // Get errors by type
    const errorsByType = getErrorAnalytics({
      timeframe,
      type: 'byType',
      limit: parseInt(limit)
    });
    
    res.status(200).json({
      success: true,
      message: 'Errors by type retrieved successfully',
      data: errorsByType
    });
  } catch (error) {
    console.error('Error getting errors by type:', error);
    next(error);
  }
};

/**
 * Get errors by endpoint
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.getErrorsByEndpoint = async (req, res, next) => {
  try {
    const { timeframe = 'day', limit = 10 } = req.query;
    
    // Get errors by endpoint
    const errorsByEndpoint = getErrorAnalytics({
      timeframe,
      type: 'byEndpoint',
      limit: parseInt(limit)
    });
    
    res.status(200).json({
      success: true,
      message: 'Errors by endpoint retrieved successfully',
      data: errorsByEndpoint
    });
  } catch (error) {
    console.error('Error getting errors by endpoint:', error);
    next(error);
  }
};

/**
 * Get errors by user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.getErrorsByUser = async (req, res, next) => {
  try {
    const { timeframe = 'day', limit = 10 } = req.query;
    
    // Get errors by user
    const errorsByUser = getErrorAnalytics({
      timeframe,
      type: 'byUser',
      limit: parseInt(limit)
    });
    
    res.status(200).json({
      success: true,
      message: 'Errors by user retrieved successfully',
      data: errorsByUser
    });
  } catch (error) {
    console.error('Error getting errors by user:', error);
    next(error);
  }
};

/**
 * Get recent errors
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.getRecentErrors = async (req, res, next) => {
  try {
    const { limit = 20 } = req.query;
    
    // Get recent errors
    const recentErrors = getErrorAnalytics({
      type: 'recent',
      limit: parseInt(limit)
    });
    
    res.status(200).json({
      success: true,
      message: 'Recent errors retrieved successfully',
      data: recentErrors
    });
  } catch (error) {
    console.error('Error getting recent errors:', error);
    next(error);
  }
};

/**
 * Reset error statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.resetErrorStats = async (req, res, next) => {
  try {
    // Reset error statistics
    resetDailyStats();
    
    res.status(200).json({
      success: true,
      message: 'Error statistics reset successfully'
    });
  } catch (error) {
    console.error('Error resetting error statistics:', error);
    next(error);
  }
};

/**
 * Get Sentry issues (if Sentry is configured)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.getSentryIssues = async (req, res, next) => {
  try {
    // Check if Sentry is available
    if (!Sentry) {
      return res.status(400).json({
        success: false,
        message: 'Sentry is not configured'
      });
    }
    
    // For security reasons, we don't provide direct access to Sentry issues
    // Instead, we provide a link to the Sentry dashboard
    res.status(200).json({
      success: true,
      message: 'Sentry is configured',
      data: {
        message: 'Please visit the Sentry dashboard to view issues',
        dashboardUrl: process.env.SENTRY_DASHBOARD_URL || 'https://sentry.io/organizations/your-org/issues/'
      }
    });
  } catch (error) {
    console.error('Error getting Sentry issues:', error);
    next(error);
  }
};

module.exports = exports;
