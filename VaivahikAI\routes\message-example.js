/**
 * Example of integrating event notifications with messaging
 * This is a sample implementation to demonstrate how to integrate notifications
 */
const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { authenticateUser } = require('../middleware/auth');
const eventNotifications = require('../services/notification/event-notifications');

/**
 * @route POST /api/messages/send
 * @desc Send a message to another user
 * @access Private
 */
router.post('/send', authenticateUser, async (req, res) => {
  try {
    const { recipientId, content, conversationId } = req.body;
    const senderId = req.user.id;
    
    // Don't allow sending message to self
    if (recipientId === senderId) {
      return res.status(400).json({ message: 'Cannot send message to yourself' });
    }
    
    // Check if recipient exists
    const recipient = await prisma.user.findUnique({
      where: { id: recipientId },
      select: { id: true }
    });
    
    if (!recipient) {
      return res.status(404).json({ message: 'Recipient not found' });
    }
    
    // Check if sender has premium status or has free messages left
    const sender = await prisma.user.findUnique({
      where: { id: senderId },
      select: { isPremium: true, messagesSent: true }
    });
    
    // If not premium and already used free messages, reject
    const FREE_MESSAGE_LIMIT = 10; // Example limit
    if (!sender.isPremium && sender.messagesSent >= FREE_MESSAGE_LIMIT) {
      return res.status(403).json({ 
        message: 'You have reached your free message limit. Upgrade to premium to send more messages.'
      });
    }
    
    // Get or create conversation
    let conversation;
    if (conversationId) {
      // Verify conversation exists and user is part of it
      conversation = await prisma.conversation.findFirst({
        where: {
          id: conversationId,
          OR: [
            { user1Id: senderId, user2Id: recipientId },
            { user1Id: recipientId, user2Id: senderId }
          ]
        }
      });
      
      if (!conversation) {
        return res.status(404).json({ message: 'Conversation not found' });
      }
    } else {
      // Check if conversation already exists
      conversation = await prisma.conversation.findFirst({
        where: {
          OR: [
            { user1Id: senderId, user2Id: recipientId },
            { user1Id: recipientId, user2Id: senderId }
          ]
        }
      });
      
      // Create new conversation if it doesn't exist
      if (!conversation) {
        conversation = await prisma.conversation.create({
          data: {
            user1Id: senderId,
            user2Id: recipientId,
            createdAt: new Date()
          }
        });
      }
    }
    
    // Create the message
    const message = await prisma.message.create({
      data: {
        conversationId: conversation.id,
        senderId,
        recipientId,
        content,
        sentAt: new Date()
      }
    });
    
    // Update conversation's last message
    await prisma.conversation.update({
      where: { id: conversation.id },
      data: {
        lastMessageAt: new Date(),
        lastMessagePreview: content.substring(0, 50) + (content.length > 50 ? '...' : '')
      }
    });
    
    // Increment message count for non-premium users
    if (!sender.isPremium) {
      await prisma.user.update({
        where: { id: senderId },
        data: {
          messagesSent: { increment: 1 }
        }
      });
    }
    
    // Get sender's information for the notification
    const senderProfile = await prisma.profile.findUnique({
      where: { userId: senderId },
      select: {
        firstName: true,
        lastName: true,
        photos: {
          where: { isMain: true },
          select: { url: true }
        }
      }
    });
    
    if (senderProfile) {
      // Create a preview of the message (truncate if too long)
      const messagePreview = content.length > 100 
        ? content.substring(0, 97) + '...' 
        : content;
      
      // Send notification to recipient
      const messageData = {
        senderId,
        senderName: `${senderProfile.firstName} ${senderProfile.lastName || ''}`.trim(),
        senderPhotoUrl: senderProfile.photos[0]?.url || null,
        messagePreview,
        conversationId: conversation.id
      };
      
      eventNotifications.notifyNewMessage(recipientId, messageData);
    }
    
    res.json({ success: true, message, conversation });
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
