/**
 * Modern Education & Career Form
 *
 * A modern UI form for collecting education and career details as part of the profile completion process.
 * Uses the shared styled components for consistent UI across the application.
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  MenuItem,
  FormHelperText,
  CircularProgress,
  Alert,
  InputAdornment,
  Typography,
  Autocomplete,
  Chip
} from '@mui/material';
import {
  School as EducationIcon,
  Work as WorkIcon,
  Business as CompanyIcon,
  AttachMoney as IncomeIcon,
  LocationOn as LocationIcon
} from '@mui/icons-material';
import { validateField, VALIDATION_RULES } from '@/utils/validationUtils';
import { formatError, getUserFriendlyMessage, isValidationError } from '@/utils/errorHandling';
import {
  StyledPaper,
  StyledTextField,
  StyledSelect,
  StyledButton,
  StyledFormLabel,
  FloatingElement,
  FormSection,
  FormRow,
  StyledSectionTitle
} from '@/components/ui/ModernFormComponents';

// Constants for form options
const EDUCATION_LEVELS = [
  { value: 'High School', label: 'High School' },
  { value: 'Diploma', label: 'Diploma' },
  { value: 'Bachelor\'s', label: 'Bachelor\'s' },
  { value: 'Master\'s', label: 'Master\'s' },
  { value: 'Doctorate', label: 'Doctorate' },
  { value: 'Professional Degree', label: 'Professional Degree' }
];

const EDUCATION_FIELDS = [
  { value: 'Computer Science', label: 'Computer Science' },
  { value: 'Engineering', label: 'Engineering' },
  { value: 'Medicine', label: 'Medicine' },
  { value: 'Business', label: 'Business' },
  { value: 'Arts', label: 'Arts' },
  { value: 'Science', label: 'Science' },
  { value: 'Law', label: 'Law' },
  { value: 'Commerce', label: 'Commerce' },
  { value: 'Other', label: 'Other' }
];

const OCCUPATION_OPTIONS = [
  { value: 'IT Professional', label: 'IT Professional' },
  { value: 'Engineer', label: 'Engineer' },
  { value: 'Doctor', label: 'Doctor' },
  { value: 'Teacher', label: 'Teacher' },
  { value: 'Business Owner', label: 'Business Owner' },
  { value: 'Government Employee', label: 'Government Employee' },
  { value: 'Lawyer', label: 'Lawyer' },
  { value: 'Accountant', label: 'Accountant' },
  { value: 'Other', label: 'Other' }
];

const INCOME_RANGES = [
  { value: 'Less than 3 LPA', label: 'Less than 3 LPA' },
  { value: '3-5 LPA', label: '3-5 LPA' },
  { value: '5-7 LPA', label: '5-7 LPA' },
  { value: '7-10 LPA', label: '7-10 LPA' },
  { value: '10-15 LPA', label: '10-15 LPA' },
  { value: '15-20 LPA', label: '15-20 LPA' },
  { value: '20-30 LPA', label: '20-30 LPA' },
  { value: 'Above 30 LPA', label: 'Above 30 LPA' }
];

const ModernEducationCareerForm = ({ userData, onSave, isLoading = false }) => {
  const [formData, setFormData] = useState({
    education: '',
    educationField: '',
    occupation: '',
    workingWith: '',
    incomeRange: '',
    workLocation: ''
  });

  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  // Initialize form with user data if available
  useEffect(() => {
    if (userData?.educationCareer) {
      setFormData({
        ...formData,
        ...userData.educationCareer
      });
    }
  }, [userData]);

  // Validate a single field
  const validateSingleField = (name, value) => {
    let rule;

    switch (name) {
      case 'education':
        rule = VALIDATION_RULES.EDUCATION;
        break;
      case 'educationField':
        rule = VALIDATION_RULES.EDUCATION_FIELD;
        break;
      case 'occupation':
        rule = VALIDATION_RULES.OCCUPATION;
        break;
      case 'workingWith':
        rule = VALIDATION_RULES.WORKING_WITH;
        break;
      case 'incomeRange':
        rule = VALIDATION_RULES.INCOME_RANGE;
        break;
      default:
        return null;
    }

    return validateField(name, value, rule, formData);
  };

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    const newFormData = {
      ...formData,
      [name]: value
    };

    setFormData(newFormData);

    // Mark field as touched
    setTouched({
      ...touched,
      [name]: true
    });

    // Validate field on change if it's been touched
    if (touched[name]) {
      const fieldError = validateSingleField(name, value);
      setErrors(prevErrors => {
        if (fieldError) {
          return { ...prevErrors, [name]: fieldError };
        } else {
          // Remove error if it exists
          const { [name]: _, ...restErrors } = prevErrors;
          return restErrors;
        }
      });
    }
  };

  // Handle select change
  const handleSelectChange = (name, value) => {
    const newFormData = {
      ...formData,
      [name]: value
    };

    setFormData(newFormData);

    // Mark field as touched
    setTouched({
      ...touched,
      [name]: true
    });

    // Validate field on change if it's been touched
    if (touched[name]) {
      const fieldError = validateSingleField(name, value);
      setErrors(prevErrors => {
        if (fieldError) {
          return { ...prevErrors, [name]: fieldError };
        } else {
          // Remove error if it exists
          const { [name]: _, ...restErrors } = prevErrors;
          return restErrors;
        }
      });
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    // Validate all fields
    Object.keys(formData).forEach(fieldName => {
      const error = validateSingleField(fieldName, formData[fieldName]);
      if (error) {
        newErrors[fieldName] = error;
      }
    });

    // Additional required fields check
    if (!formData.education) {
      newErrors.education = 'Education is required';
    }

    if (!formData.occupation) {
      newErrors.occupation = 'Occupation is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Call the onSave function with the form data
    onSave(formData);
  };

  return (
    <StyledPaper>
      {/* Decorative elements */}
      <FloatingElement position="top-right" />
      <FloatingElement position="bottom-left" />

      <Box sx={{ position: 'relative', zIndex: 1 }}>
        <StyledSectionTitle>Education & Career</StyledSectionTitle>

        {/* Form content */}
        <form onSubmit={handleSubmit}>
          <FormSection title="Education">
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <EducationIcon fontSize="small" sx={{ mr: 1 }} />
                    Highest Education*
                  </Box>
                </StyledFormLabel>
                <StyledSelect
                  name="education"
                  value={formData.education}
                  onChange={handleChange}
                  fullWidth
                  error={!!errors.education}
                >
                  <MenuItem value="">Select Education Level</MenuItem>
                  {EDUCATION_LEVELS.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </StyledSelect>
                {errors.education && <FormHelperText error>{errors.education}</FormHelperText>}
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>Education Field</StyledFormLabel>
                <StyledSelect
                  name="educationField"
                  value={formData.educationField}
                  onChange={handleChange}
                  fullWidth
                >
                  <MenuItem value="">Select Field of Study</MenuItem>
                  {EDUCATION_FIELDS.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </StyledSelect>
              </Grid>
            </Grid>
          </FormSection>

          <FormSection title="Career">
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <WorkIcon fontSize="small" sx={{ mr: 1 }} />
                    Occupation*
                  </Box>
                </StyledFormLabel>
                <StyledSelect
                  name="occupation"
                  value={formData.occupation}
                  onChange={handleChange}
                  fullWidth
                  error={!!errors.occupation}
                >
                  <MenuItem value="">Select Occupation</MenuItem>
                  {OCCUPATION_OPTIONS.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </StyledSelect>
                {errors.occupation && <FormHelperText error>{errors.occupation}</FormHelperText>}
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <CompanyIcon fontSize="small" sx={{ mr: 1 }} />
                    Working With
                  </Box>
                </StyledFormLabel>
                <StyledTextField
                  name="workingWith"
                  value={formData.workingWith}
                  onChange={handleChange}
                  fullWidth
                  placeholder="Company/Organization name"
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <IncomeIcon fontSize="small" sx={{ mr: 1 }} />
                    Annual Income
                  </Box>
                </StyledFormLabel>
                <StyledSelect
                  name="incomeRange"
                  value={formData.incomeRange}
                  onChange={handleChange}
                  fullWidth
                >
                  <MenuItem value="">Select Income Range</MenuItem>
                  {INCOME_RANGES.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </StyledSelect>
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <LocationIcon fontSize="small" sx={{ mr: 1 }} />
                    Work Location
                  </Box>
                </StyledFormLabel>
                <StyledTextField
                  name="workLocation"
                  value={formData.workLocation}
                  onChange={handleChange}
                  fullWidth
                  placeholder="City/Town where you work"
                />
              </Grid>
            </Grid>
          </FormSection>

          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
            <StyledButton
              type="submit"
              variant="contained"
              disabled={isLoading}
              startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : null}
            >
              {isLoading ? 'Saving...' : 'Save Education & Career'}
            </StyledButton>
          </Box>
        </form>
      </Box>
    </StyledPaper>
  );
};

export default ModernEducationCareerForm;
