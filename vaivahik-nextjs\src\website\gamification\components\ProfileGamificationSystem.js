import { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Button,
  Grid,
  Divider,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Tooltip,
  useTheme,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Badge,
  Avatar
} from '@mui/material';
import {
  EmojiEvents as EmojiEventsIcon,
  Visibility as VisibilityIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Lock as LockIcon,
  LockOpen as LockOpenIcon,
  Celebration as CelebrationIcon,
  CheckCircle as CheckCircleIcon,
  ArrowUpward as ArrowUpwardIcon,
  Notifications as NotificationsIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useRouter } from 'next/router';
import Confetti from 'react-confetti';

// Badge definitions
const BADGES = {
  PROFILE_BRONZE: {
    id: 'profile_bronze',
    name: 'Bronze Profile',
    description: 'Complete 25% of your profile',
    icon: <StarIcon sx={{ color: '#CD7F32' }} />,
    requirement: 'Complete at least 25% of your profile',
    requiredValue: 25,
    type: 'profile_completion'
  },
  PROFILE_SILVER: {
    id: 'profile_silver',
    name: 'Silver Profile',
    description: 'Complete 50% of your profile',
    icon: <StarIcon sx={{ color: '#C0C0C0' }} />,
    requirement: 'Complete at least 50% of your profile',
    requiredValue: 50,
    type: 'profile_completion'
  },
  PROFILE_GOLD: {
    id: 'profile_gold',
    name: 'Gold Profile',
    description: 'Complete 75% of your profile',
    icon: <StarIcon sx={{ color: '#FFD700' }} />,
    requirement: 'Complete at least 75% of your profile',
    requiredValue: 75,
    type: 'profile_completion'
  },
  PROFILE_PLATINUM: {
    id: 'profile_platinum',
    name: 'Platinum Profile',
    description: 'Complete 100% of your profile',
    icon: <StarIcon sx={{ color: '#E5E4E2' }} />,
    requirement: 'Complete 100% of your profile',
    requiredValue: 100,
    type: 'profile_completion'
  },
  PHOTO_UPLOAD: {
    id: 'photo_upload',
    name: 'Picture Perfect',
    description: 'Upload your profile photo',
    icon: <CheckCircleIcon sx={{ color: '#4CAF50' }} />,
    requirement: 'Upload at least one profile photo',
    requiredValue: 1,
    type: 'photo_upload'
  },
  PREFERENCES_SET: {
    id: 'preferences_set',
    name: 'Preference Pro',
    description: 'Set your partner preferences',
    icon: <CheckCircleIcon sx={{ color: '#2196F3' }} />,
    requirement: 'Complete your partner preferences',
    requiredValue: 50,
    type: 'preferences_completion'
  },
  FAMILY_DETAILS: {
    id: 'family_details',
    name: 'Family First',
    description: 'Add your family details',
    icon: <CheckCircleIcon sx={{ color: '#9C27B0' }} />,
    requirement: 'Complete your family details',
    requiredValue: 50,
    type: 'family_completion'
  }
};

// Feature unlock levels
const FEATURE_UNLOCKS = {
  ADVANCED_SEARCH: {
    id: 'advanced_search',
    name: 'Advanced Search Visibility',
    description: 'Your profile appears in advanced search results',
    requiredCompletion: 40,
    icon: <VisibilityIcon />
  },
  CONTACT_INFO: {
    id: 'contact_info',
    name: 'Contact Information Sharing',
    description: 'Share contact details with interested matches',
    requiredCompletion: 60,
    icon: <VisibilityIcon />
  },
  SPOTLIGHT: {
    id: 'spotlight',
    name: 'Spotlight Feature Eligibility',
    description: 'Highlight your profile to get more attention',
    requiredCompletion: 80,
    icon: <VisibilityIcon />
  },
  PREMIUM_MATCH: {
    id: 'premium_match',
    name: 'Premium Match Algorithm',
    description: 'Get highest quality matches based on compatibility',
    requiredCompletion: 100,
    icon: <VisibilityIcon />
  }
};

const ProfileGamificationSystem = ({
  userData,
  profileCompletion,
  photoCount,
  preferencesCompletion,
  familyCompletion
}) => {
  const theme = useTheme();
  const router = useRouter();
  const [earnedBadges, setEarnedBadges] = useState([]);
  const [unlockedFeatures, setUnlockedFeatures] = useState([]);
  const [showConfetti, setShowConfetti] = useState(false);
  const [newBadge, setNewBadge] = useState(null);
  const [showBadgeDialog, setShowBadgeDialog] = useState(false);

  // Calculate earned badges and unlocked features based on user data
  useEffect(() => {
    const badges = [];
    const features = [];

    // Check profile completion badges
    if (profileCompletion >= 25) badges.push(BADGES.PROFILE_BRONZE.id);
    if (profileCompletion >= 50) badges.push(BADGES.PROFILE_SILVER.id);
    if (profileCompletion >= 75) badges.push(BADGES.PROFILE_GOLD.id);
    if (profileCompletion === 100) badges.push(BADGES.PROFILE_PLATINUM.id);

    // Check photo upload badge
    if (photoCount > 0) badges.push(BADGES.PHOTO_UPLOAD.id);

    // Check preferences badge
    if (preferencesCompletion >= 50) badges.push(BADGES.PREFERENCES_SET.id);

    // Check family details badge
    if (familyCompletion >= 50) badges.push(BADGES.FAMILY_DETAILS.id);

    // Check for newly earned badges
    const previousBadges = earnedBadges;
    const newBadges = badges.filter(badge => !previousBadges.includes(badge));

    if (newBadges.length > 0) {
      // Show confetti and badge dialog for the first new badge
      setNewBadge(BADGES[Object.keys(BADGES).find(key => BADGES[key].id === newBadges[0])]);
      setShowBadgeDialog(true);
      setShowConfetti(true);

      // Hide confetti after 5 seconds
      setTimeout(() => {
        setShowConfetti(false);
      }, 5000);
    }

    setEarnedBadges(badges);

    // Check unlocked features
    if (profileCompletion >= 40) features.push(FEATURE_UNLOCKS.ADVANCED_SEARCH.id);
    if (profileCompletion >= 60) features.push(FEATURE_UNLOCKS.CONTACT_INFO.id);
    if (profileCompletion >= 80) features.push(FEATURE_UNLOCKS.SPOTLIGHT.id);
    if (profileCompletion === 100) features.push(FEATURE_UNLOCKS.PREMIUM_MATCH.id);

    setUnlockedFeatures(features);
  }, [profileCompletion, photoCount, preferencesCompletion, familyCompletion]);

  // Get profile level based on completion
  const getProfileLevel = () => {
    if (profileCompletion >= 80) return 'Premium';
    if (profileCompletion >= 60) return 'Advanced';
    if (profileCompletion >= 40) return 'Standard';
    return 'Basic';
  };

  // Get visibility percentage based on completion
  const getVisibilityPercentage = () => {
    return Math.min(100, profileCompletion + 20);
  };

  // Handle badge dialog close
  const handleCloseBadgeDialog = () => {
    setShowBadgeDialog(false);
  };

  // Render badge item
  const renderBadgeItem = (badge, earned) => {
    return (
      <Grid item xs={6} sm={3} key={badge.id}>
        <Tooltip title={earned ? `Earned: ${badge.description}` : badge.requirement}>
          <Paper
            elevation={earned ? 3 : 0}
            sx={{
              p: 2,
              textAlign: 'center',
              bgcolor: earned ? `rgba(${badge.id.includes('bronze') ? '176, 141, 87' : badge.id.includes('silver') ? '192, 192, 192' : badge.id.includes('gold') ? '255, 215, 0' : '229, 228, 226'}, 0.1)` : 'action.disabledBackground',
              border: '1px solid',
              borderColor: earned ? (badge.id.includes('bronze') ? 'warning.light' : badge.id.includes('silver') ? 'grey.400' : badge.id.includes('gold') ? 'warning.main' : 'primary.light') : 'divider',
              borderRadius: 2,
              opacity: earned ? 1 : 0.6
            }}
          >
            {badge.icon}
            <Typography variant="subtitle2" sx={{ mt: 1 }}>{badge.name}</Typography>
          </Paper>
        </Tooltip>
      </Grid>
    );
  };

  return (
    <>
      {showConfetti && <Confetti width={window.innerWidth} height={window.innerHeight} recycle={false} />}

      <Card elevation={3} sx={{ mb: 3, borderRadius: 2, overflow: 'hidden' }}>
        <Box
          sx={{
            p: 3,
            background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.primary.light} 90%)`,
            color: 'white'
          }}
        >
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={8}>
              <Typography variant="h5" fontWeight="bold" gutterBottom>
                Your Profile Status
              </Typography>
              <Typography variant="body1" sx={{ mb: 2 }}>
                Your profile is {profileCompletion}% complete. Profiles with higher completion rates receive up to 10x more interest!
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{ flexGrow: 1, mr: 2 }}>
                  <LinearProgress
                    variant="determinate"
                    value={profileCompletion}
                    sx={{
                      height: 10,
                      borderRadius: 5,
                      backgroundColor: 'rgba(255,255,255,0.3)',
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: 'white'
                      }
                    }}
                  />
                </Box>
                <Typography variant="h6" fontWeight="bold">
                  {profileCompletion}%
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4} sx={{ textAlign: 'center' }}>
              <Paper
                elevation={4}
                sx={{
                  p: 2,
                  borderRadius: 2,
                  display: 'inline-block',
                  background: 'rgba(255,255,255,0.9)',
                  color: theme.palette.text.primary
                }}
              >
                <EmojiEventsIcon sx={{ fontSize: 40, color: theme.palette.warning.main }} />
                <Typography variant="h6" fontWeight="bold">
                  {getProfileLevel()} Profile
                </Typography>
                <Typography variant="body2">
                  Profile Visibility: {getVisibilityPercentage()}%
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </Box>

        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ mt: 1, display: 'flex', alignItems: 'center' }}>
            <EmojiEventsIcon sx={{ mr: 1 }} /> Your Earned Badges
          </Typography>
          <Divider sx={{ mb: 2 }} />

          <Grid container spacing={2}>
            {Object.values(BADGES).map(badge =>
              renderBadgeItem(badge, earnedBadges.includes(badge.id))
            )}
          </Grid>

          <Typography variant="h6" gutterBottom sx={{ mt: 4, display: 'flex', alignItems: 'center' }}>
            <LockOpenIcon sx={{ mr: 1 }} /> Unlocked Features
          </Typography>
          <Divider sx={{ mb: 2 }} />

          <List>
            {Object.values(FEATURE_UNLOCKS).map(feature => (
              <ListItem
                key={feature.id}
                sx={{
                  mb: 1,
                  p: 2,
                  borderRadius: 2,
                  border: '1px solid',
                  borderColor: 'divider',
                  bgcolor: unlockedFeatures.includes(feature.id) ? 'rgba(0, 150, 136, 0.05)' : 'transparent'
                }}
              >
                <ListItemIcon>
                  {unlockedFeatures.includes(feature.id) ? <LockOpenIcon color="success" /> : <LockIcon color="action" />}
                </ListItemIcon>
                <ListItemText
                  primary={feature.name}
                  secondary={feature.description}
                />
                <Chip
                  size="small"
                  label={unlockedFeatures.includes(feature.id) ? "Unlocked" : `${feature.requiredCompletion}% Required`}
                  color={unlockedFeatures.includes(feature.id) ? "success" : "default"}
                  variant={unlockedFeatures.includes(feature.id) ? "filled" : "outlined"}
                />
              </ListItem>
            ))}
          </List>

          <Box sx={{ mt: 3, textAlign: 'center' }}>
            <Button
              variant="contained"
              color="primary"
              onClick={() => router.push('/website/pages/profile')}
              startIcon={<ArrowUpwardIcon />}
            >
              Improve Your Profile
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* New Badge Dialog */}
      <Dialog
        open={showBadgeDialog}
        onClose={handleCloseBadgeDialog}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle sx={{ textAlign: 'center', pb: 0 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6" sx={{ flexGrow: 1 }}>
              <CelebrationIcon sx={{ mr: 1, verticalAlign: 'middle', color: theme.palette.warning.main }} />
              New Badge Earned!
            </Typography>
            <IconButton onClick={handleCloseBadgeDialog} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ textAlign: 'center', pt: 3 }}>
          {newBadge && (
            <>
              <Box sx={{ mb: 2 }}>
                <Avatar
                  sx={{
                    width: 80,
                    height: 80,
                    margin: '0 auto',
                    bgcolor: theme.palette.primary.light
                  }}
                >
                  {newBadge.icon}
                </Avatar>
              </Box>
              <Typography variant="h5" gutterBottom>
                {newBadge.name}
              </Typography>
              <Typography variant="body1" color="text.secondary" paragraph>
                {newBadge.description}
              </Typography>
              <Typography variant="body2" sx={{ mt: 2 }}>
                Keep completing your profile to earn more badges and unlock premium features!
              </Typography>
            </>
          )}
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'center', pb: 3 }}>
          <Button
            variant="contained"
            onClick={handleCloseBadgeDialog}
            color="primary"
          >
            Continue
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ProfileGamificationSystem;