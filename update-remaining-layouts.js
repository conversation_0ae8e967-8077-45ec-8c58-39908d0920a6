// Node.js script to update remaining AdminLayout to EnhancedAdminLayout
const fs = require('fs');
const path = require('path');

// Path to admin pages directory
const adminPagesDir = path.join(__dirname, 'vaivahik-nextjs', 'src', 'pages', 'admin');

// List of files to update (from our previous scan)
const filesToUpdate = [
  'admin-users/index.js',
  'api-discovery.js',
  'api-viewer.js',
  'blog-posts/create.js',
  'blog-posts/index.js',
  'documentation/deployment.js',
  'documentation/mock-data.js',
  'documentation.js',
  'email-templates/index.js',
  'notifications.js',
  'premium-plans.js',
  'production-checklist.js',
  'promotions.js',
  'referral-programs.js',
  'reported-profiles.js',
  'settings.js',
  'subscriptions.js',
  'success-stories/index.js',
  'transactions.js'
];

// Counter for tracking changes
let updatedCount = 0;
let errorCount = 0;

// Function to update a file
function updateFile(relativePath) {
  const filePath = path.join(adminPagesDir, relativePath);
  
  try {
    // Read file content
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Replace import statement
    content = content.replace(
      /import AdminLayout from ['"]@\/components\/admin\/AdminLayout['"];/g,
      `import dynamic from 'next/dynamic';\n\n// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues\nconst EnhancedAdminLayout = dynamic(\n  () => import('@/components/admin/EnhancedAdminLayout'),\n  { ssr: false }\n);`
    );
    
    // Replace component usage
    content = content.replace(/<AdminLayout([^>]*)>/g, '<EnhancedAdminLayout$1>');
    content = content.replace(/<\/AdminLayout>/g, '</EnhancedAdminLayout>');
    
    // Write updated content back to file
    fs.writeFileSync(filePath, content, 'utf8');
    
    console.log(`✅ Updated: ${relativePath}`);
    updatedCount++;
  } catch (err) {
    console.error(`❌ Error updating ${relativePath}:`, err);
    errorCount++;
  }
}

// Process all files
console.log('Updating AdminLayout to EnhancedAdminLayout in remaining files...\n');

filesToUpdate.forEach(file => {
  updateFile(file);
});

// Print summary
console.log(`\n=== SUMMARY ===`);
console.log(`✅ Successfully updated: ${updatedCount} files`);
console.log(`❌ Errors: ${errorCount} files`);
console.log('\nDone!');
