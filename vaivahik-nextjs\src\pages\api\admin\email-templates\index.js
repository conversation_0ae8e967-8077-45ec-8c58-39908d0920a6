// API endpoint for email templates
import { handleApiError } from '@/utils/errorHandler';
import { withAuth } from '@/utils/authHandler';

async function handler(req, res) {
  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getEmailTemplates(req, res);
      case 'POST':
        return await createEmailTemplate(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    return handleApiError(error, res, 'Email Templates API');
  }
}

// GET /api/admin/email-templates
async function getEmailTemplates(req, res) {
  try {
    // Get query parameters
    const { page = 1, limit = 10, search = '', category = '' } = req.query;
    
    // Mock data for email templates
    const allTemplates = [
      {
        id: 1,
        name: 'Welcome Email',
        subject: 'Welcome to Vaivahik - Your Journey to Finding the Perfect Match Begins!',
        content: '<p>Dear {{name}},</p><p>Welcome to Vaivahik! We\'re thrilled to have you join our community dedicated to helping members of the Maratha community find their perfect life partner.</p><p>Here\'s what you can do now:</p><ul><li>Complete your profile</li><li>Upload photos</li><li>Set your preferences</li><li>Start exploring matches</li></ul><p>If you have any questions, our support team is here to help.</p><p>Best wishes,<br>The Vaivahik Team</p>',
        category: 'Onboarding',
        variables: ['name', 'email'],
        isActive: true,
        createdAt: '2023-01-15T10:30:00Z',
        updatedAt: '2023-05-20T14:45:00Z',
        lastSent: '2023-08-01T09:15:00Z',
        sendCount: 1250
      },
      {
        id: 2,
        name: 'Profile Verification Success',
        subject: 'Congratulations! Your Vaivahik Profile is Now Verified',
        content: '<p>Dear {{name}},</p><p>Great news! Your profile has been successfully verified. The verified badge will now appear on your profile, increasing your visibility and trustworthiness in the community.</p><p>Thank you for completing the verification process.</p><p>Best regards,<br>The Vaivahik Team</p>',
        category: 'Verification',
        variables: ['name', 'email', 'profile_link'],
        isActive: true,
        createdAt: '2023-02-10T11:20:00Z',
        updatedAt: '2023-06-15T16:30:00Z',
        lastSent: '2023-07-30T13:45:00Z',
        sendCount: 980
      },
      {
        id: 3,
        name: 'New Match Notification',
        subject: 'You Have a New Match on Vaivahik!',
        content: '<p>Dear {{name}},</p><p>Exciting news! We\'ve found a new match for you on Vaivahik.</p><p>{{match_name}}, aged {{match_age}} from {{match_location}}, matches your preferences with a compatibility score of {{compatibility_score}}%.</p><p>Click the button below to view their profile and take the next step in your journey.</p><p><a href="{{match_profile_link}}" style="background-color: #5e35b1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Match</a></p><p>Best wishes,<br>The Vaivahik Team</p>',
        category: 'Matching',
        variables: ['name', 'email', 'match_name', 'match_age', 'match_location', 'compatibility_score', 'match_profile_link'],
        isActive: true,
        createdAt: '2023-03-05T09:45:00Z',
        updatedAt: '2023-07-10T15:20:00Z',
        lastSent: '2023-08-02T10:30:00Z',
        sendCount: 3450
      },
      {
        id: 4,
        name: 'Premium Subscription Confirmation',
        subject: 'Your Vaivahik Premium Subscription is Active',
        content: '<p>Dear {{name}},</p><p>Thank you for subscribing to Vaivahik Premium! Your payment of ₹{{amount}} has been successfully processed.</p><p>Your premium subscription is now active and will expire on {{expiry_date}}.</p><p>You now have access to all premium features including:</p><ul><li>Unlimited messaging</li><li>Advanced search filters</li><li>Priority in search results</li><li>View contact details of matches</li><li>And much more!</li></ul><p>If you have any questions about your subscription, please contact our support team.</p><p>Best regards,<br>The Vaivahik Team</p>',
        category: 'Subscription',
        variables: ['name', 'email', 'amount', 'plan_name', 'expiry_date'],
        isActive: true,
        createdAt: '2023-04-20T14:15:00Z',
        updatedAt: '2023-06-25T11:30:00Z',
        lastSent: '2023-07-29T16:45:00Z',
        sendCount: 320
      },
      {
        id: 5,
        name: 'Interest Received',
        subject: 'Someone is Interested in Your Profile on Vaivahik',
        content: '<p>Dear {{name}},</p><p>Great news! {{sender_name}} has shown interest in your profile on Vaivahik.</p><p>You can view their profile and respond to their interest by clicking the button below.</p><p><a href="{{sender_profile_link}}" style="background-color: #5e35b1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Profile</a></p><p>Best wishes,<br>The Vaivahik Team</p>',
        category: 'Interaction',
        variables: ['name', 'email', 'sender_name', 'sender_profile_link'],
        isActive: true,
        createdAt: '2023-05-12T10:00:00Z',
        updatedAt: '2023-07-05T13:20:00Z',
        lastSent: '2023-08-01T11:30:00Z',
        sendCount: 2780
      },
      {
        id: 6,
        name: 'Password Reset',
        subject: 'Reset Your Vaivahik Password',
        content: '<p>Dear {{name}},</p><p>We received a request to reset your password for your Vaivahik account. If you didn\'t make this request, you can safely ignore this email.</p><p>To reset your password, click the button below. This link will expire in 24 hours.</p><p><a href="{{reset_link}}" style="background-color: #5e35b1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p><p>If the button doesn\'t work, you can copy and paste the following link into your browser:</p><p>{{reset_link}}</p><p>Best regards,<br>The Vaivahik Team</p>',
        category: 'Account',
        variables: ['name', 'email', 'reset_link'],
        isActive: true,
        createdAt: '2023-01-05T08:30:00Z',
        updatedAt: '2023-05-15T09:45:00Z',
        lastSent: '2023-07-31T14:20:00Z',
        sendCount: 156
      }
    ];
    
    // Filter templates based on search and category
    let filteredTemplates = [...allTemplates];
    
    if (search) {
      const searchLower = search.toLowerCase();
      filteredTemplates = filteredTemplates.filter(template => 
        template.name.toLowerCase().includes(searchLower) || 
        template.subject.toLowerCase().includes(searchLower) ||
        template.content.toLowerCase().includes(searchLower)
      );
    }
    
    if (category) {
      filteredTemplates = filteredTemplates.filter(template => template.category === category);
    }
    
    // Calculate pagination
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedTemplates = filteredTemplates.slice(startIndex, endIndex);
    
    // Get unique categories for filtering
    const categories = [...new Set(allTemplates.map(template => template.category))];
    
    return res.status(200).json({
      success: true,
      templates: paginatedTemplates,
      pagination: {
        total: filteredTemplates.length,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(filteredTemplates.length / parseInt(limit))
      },
      categories
    });
  } catch (error) {
    return handleApiError(error, res, 'Get email templates');
  }
}

// POST /api/admin/email-templates
async function createEmailTemplate(req, res) {
  try {
    const { name, subject, content, category, variables, isActive } = req.body;
    
    // Validate required fields
    if (!name || !subject || !content || !category) {
      return res.status(400).json({
        success: false,
        message: 'Name, subject, content, and category are required'
      });
    }
    
    // In a real implementation, this would create a new template in the database
    // For now, we'll just return a success response with mock data
    
    return res.status(201).json({
      success: true,
      message: 'Email template created successfully',
      template: {
        id: Math.floor(Math.random() * 1000) + 7,
        name,
        subject,
        content,
        category,
        variables: variables || [],
        isActive: isActive !== undefined ? isActive : true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastSent: null,
        sendCount: 0
      }
    });
  } catch (error) {
    return handleApiError(error, res, 'Create email template');
  }
}

export default handler;
