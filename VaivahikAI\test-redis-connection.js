/**
 * Test Redis Connection
 * 
 * This script tests the Redis connection using the standard redis package.
 */

// Load environment variables
require('dotenv').config();

// Import the redis client from config
const redisClient = require('./src/config/redisClient');

console.log('Testing Redis connection...');

// Test function
async function testRedisConnection() {
  try {
    // Check if Redis is connected
    if (!redisClient.isReady) {
      console.log('Redis client is not ready. Waiting for connection...');
      
      // Wait for the ready event
      await new Promise((resolve) => {
        redisClient.on('ready', () => {
          console.log('Redis client is now ready!');
          resolve();
        });
        
        // Set a timeout in case the connection never establishes
        setTimeout(() => {
          console.log('Timed out waiting for Redis connection');
          resolve();
        }, 5000);
      });
    }
    
    // Try to set a value
    if (redisClient.isReady) {
      console.log('Setting test value in Redis...');
      await redisClient.set('test-key', 'Hello from Vaivahik!');
      console.log('Successfully set test value in Redis');
      
      // Try to get the value
      const value = await redisClient.get('test-key');
      console.log('Retrieved value from Redis:', value);
      
      // Clean up
      await redisClient.del('test-key');
      console.log('Deleted test key from Redis');
      
      console.log('Redis connection test successful!');
    } else {
      console.error('Redis client is still not ready after waiting');
    }
  } catch (error) {
    console.error('Error testing Redis connection:', error);
  } finally {
    // Close the Redis connection
    if (redisClient.isReady) {
      await redisClient.quit();
      console.log('Redis connection closed');
    }
    
    process.exit(0);
  }
}

// Run the test
testRedisConnection();
