import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Verify JWT token
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ success: false, message: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const userId = decoded.userId;

    const { id: targetUserId } = req.query;
    const { action, data } = req.body;

    if (!targetUserId) {
      return res.status(400).json({
        success: false,
        message: 'Target user ID is required'
      });
    }

    if (!action || !['like', 'dislike', 'super_like', 'view'].includes(action)) {
      return res.status(400).json({
        success: false,
        message: 'Valid action is required (like, dislike, super_like, view)'
      });
    }

    // Check if target user exists
    const targetUser = await prisma.user.findUnique({
      where: { id: targetUserId },
      include: { profile: true }
    });

    if (!targetUser) {
      return res.status(404).json({
        success: false,
        message: 'Target user not found'
      });
    }

    let result = {};

    switch (action) {
      case 'like':
      case 'dislike':
      case 'super_like':
        result = await handleLikeDislike(userId, targetUserId, action);
        break;
      case 'view':
        result = await handleProfileView(userId, targetUserId, data);
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid action'
        });
    }

    return res.status(200).json({
      success: true,
      message: `Profile ${action} recorded successfully`,
      data: result
    });

  } catch (error) {
    console.error('Error handling profile action:', error);
    return res.status(500).json({
      success: false,
      message: 'Error handling profile action',
      error: error.message
    });
  }
}

async function handleLikeDislike(userId, targetUserId, action) {
  // Check if like/dislike already exists
  const existingLike = await prisma.profileLike.findFirst({
    where: {
      userId: userId,
      targetUserId: targetUserId
    }
  });

  const likeType = action.toUpperCase();

  if (existingLike) {
    // Update existing like/dislike
    const updatedLike = await prisma.profileLike.update({
      where: { id: existingLike.id },
      data: { likeType: likeType }
    });

    // Track interaction update
    await prisma.userInteraction.create({
      data: {
        userId: userId,
        targetUserId: targetUserId,
        interactionType: `PROFILE_${likeType}_UPDATED`,
        metadata: JSON.stringify({
          previousType: existingLike.likeType,
          newType: likeType
        })
      }
    });

    return { likeId: updatedLike.id, action: 'updated', type: likeType };
  } else {
    // Create new like/dislike
    const newLike = await prisma.profileLike.create({
      data: {
        userId: userId,
        targetUserId: targetUserId,
        likeType: likeType
      }
    });

    // Track interaction
    await prisma.userInteraction.create({
      data: {
        userId: userId,
        targetUserId: targetUserId,
        interactionType: `PROFILE_${likeType}`,
        metadata: JSON.stringify({
          likeType: likeType
        })
      }
    });

    // Check for mutual like (match)
    if (likeType === 'LIKE' || likeType === 'SUPER_LIKE') {
      const mutualLike = await prisma.profileLike.findFirst({
        where: {
          userId: targetUserId,
          targetUserId: userId,
          likeType: { in: ['LIKE', 'SUPER_LIKE'] }
        }
      });

      if (mutualLike) {
        // Create match notification
        await prisma.notification.create({
          data: {
            userId: targetUserId,
            type: 'MUTUAL_LIKE',
            title: 'It\'s a Match!',
            message: 'You and another user have liked each other!',
            data: JSON.stringify({
              matchedUserId: userId,
              likeType: likeType
            })
          }
        });

        await prisma.notification.create({
          data: {
            userId: userId,
            type: 'MUTUAL_LIKE',
            title: 'It\'s a Match!',
            message: 'You and another user have liked each other!',
            data: JSON.stringify({
              matchedUserId: targetUserId,
              likeType: likeType
            })
          }
        });

        return { 
          likeId: newLike.id, 
          action: 'created', 
          type: likeType, 
          isMatch: true,
          mutualLikeId: mutualLike.id
        };
      }
    }

    return { likeId: newLike.id, action: 'created', type: likeType, isMatch: false };
  }
}

async function handleProfileView(userId, targetUserId, viewData = {}) {
  // Create detailed profile view record
  const profileView = await prisma.profileViewDetailed.create({
    data: {
      viewerId: userId,
      viewedUserId: targetUserId,
      viewDuration: viewData.duration || null,
      deviceType: viewData.deviceType || null,
      ipAddress: viewData.ipAddress || null,
      userAgent: viewData.userAgent || null,
      referrer: viewData.referrer || null,
      sectionsViewed: viewData.sectionsViewed || null
    }
  });

  // Also create simple profile view for compatibility
  await prisma.profileView.create({
    data: {
      viewerId: userId,
      viewedId: targetUserId
    }
  });

  // Track interaction
  await prisma.userInteraction.create({
    data: {
      userId: userId,
      targetUserId: targetUserId,
      interactionType: 'PROFILE_VIEW',
      duration: viewData.duration || null,
      metadata: JSON.stringify({
        deviceType: viewData.deviceType,
        sectionsViewed: viewData.sectionsViewed
      })
    }
  });

  // Track behavior for ML
  if (global.userBehaviorTracker) {
    await global.userBehaviorTracker.trackBehavior(
      userId,
      'PROFILE_VIEW',
      targetUserId,
      {
        duration: viewData.duration,
        deviceType: viewData.deviceType,
        sectionsViewed: viewData.sectionsViewed
      }
    );
  }

  return { 
    viewId: profileView.id, 
    action: 'viewed',
    duration: viewData.duration 
  };
}
