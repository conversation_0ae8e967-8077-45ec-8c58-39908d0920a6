// src/pages/api/admin/promotions/[name]/index.js
import axios from 'axios';
import { handleApiError } from '@/utils/errorHandler';
import { withAuth } from '@/utils/authHandler';

// Backend API URL - adjust this to your actual backend URL
const BACKEND_API_URL = 'http://localhost:3001/api';

async function handler(req, res) {
  try {
    const { name } = req.query;

    // Get the auth token from the request cookies or headers
    const token = req.cookies?.adminToken || req.headers.authorization?.split(' ')[1];

    if (!token) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    try {
      // Forward the request to the backend API
      const response = await axios({
        method: req.method,
        url: `${BACKEND_API_URL}/admin/promotions/${name}`,
        data: req.body,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000 // 10 second timeout
      });

      // Return the response from the backend
      return res.status(response.status).json(response.data);
    } catch (apiError) {
      // Log the error
      console.error('Error proxying request to backend:', apiError.message);

      // Return the error response from the backend if available
      if (apiError.response) {
        return res.status(apiError.response.status).json(apiError.response.data);
      }

      // Handle timeout errors
      if (apiError.code === 'ECONNABORTED') {
        return res.status(504).json({
          success: false,
          message: 'Request to backend timed out. Please check if the backend server is running.'
        });
      }

      // Handle connection errors
      if (apiError.code === 'ECONNREFUSED') {
        return res.status(503).json({
          success: false,
          message: 'Cannot connect to backend server. Please check if the backend server is running.'
        });
      }

      // Otherwise return a generic error
      return res.status(500).json({
        success: false,
        message: 'Error connecting to backend service: ' + (apiError.message || 'Unknown error')
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Promotions API');
  }
}

// Export the handler with authentication middleware
export default withAuth(handler, 'ADMIN');
