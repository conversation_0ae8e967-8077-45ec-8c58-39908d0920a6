/**
 * Notification Service
 * 
 * This service handles sending notifications to users via Firebase Cloud Messaging (FCM).
 */
const { getMessaging } = require('./fcm-config');
const templates = require('./templates');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * Send a notification to a single user
 * 
 * @param {string} userId - ID of the user to send the notification to
 * @param {Object} notification - Notification content
 * @returns {Promise<Object>} Result of the send operation
 */
const sendToUser = async (userId, notification) => {
  try {
    // Get the user's FCM tokens
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { fcmTokens: true }
    });

    if (!user || !user.fcmTokens || user.fcmTokens.length === 0) {
      console.log(`No FCM tokens found for user ${userId}`);
      return { success: false, error: 'No FCM tokens found for user' };
    }

    // Send to all user devices
    const messaging = getMessaging();
    const message = {
      notification: {
        title: notification.title,
        body: notification.body,
      },
      data: notification.data,
      tokens: user.fcmTokens,
    };

    // Add image if provided
    if (notification.imageUrl) {
      message.notification.imageUrl = notification.imageUrl;
    }

    // Send the message
    const response = await messaging.sendMulticast(message);
    
    // Store notification in database
    await storeNotification(userId, notification);
    
    // Handle failed tokens
    if (response.failureCount > 0) {
      const failedTokens = [];
      response.responses.forEach((resp, idx) => {
        if (!resp.success) {
          failedTokens.push(user.fcmTokens[idx]);
        }
      });
      
      // Remove failed tokens
      if (failedTokens.length > 0) {
        await removeFailedTokens(userId, failedTokens);
      }
    }

    return {
      success: true,
      successCount: response.successCount,
      failureCount: response.failureCount
    };
  } catch (error) {
    console.error('Error sending notification to user:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Send a notification to multiple users
 * 
 * @param {string[]} userIds - IDs of users to send the notification to
 * @param {Object} notification - Notification content
 * @returns {Promise<Object>} Result of the send operation
 */
const sendToUsers = async (userIds, notification) => {
  try {
    const results = await Promise.all(
      userIds.map(userId => sendToUser(userId, notification))
    );
    
    return {
      success: true,
      results
    };
  } catch (error) {
    console.error('Error sending notification to multiple users:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Send a notification to a topic
 * 
 * @param {string} topic - Topic to send the notification to
 * @param {Object} notification - Notification content
 * @returns {Promise<Object>} Result of the send operation
 */
const sendToTopic = async (topic, notification) => {
  try {
    const messaging = getMessaging();
    const message = {
      notification: {
        title: notification.title,
        body: notification.body,
      },
      data: notification.data,
      topic,
    };

    // Add image if provided
    if (notification.imageUrl) {
      message.notification.imageUrl = notification.imageUrl;
    }

    // Send the message
    const response = await messaging.send(message);
    
    // Store notification for topic
    await storeTopicNotification(topic, notification);
    
    return {
      success: true,
      messageId: response
    };
  } catch (error) {
    console.error(`Error sending notification to topic ${topic}:`, error);
    return { success: false, error: error.message };
  }
};

/**
 * Store a notification in the database
 * 
 * @param {string} userId - ID of the user
 * @param {Object} notification - Notification content
 * @returns {Promise<Object>} Created notification
 */
const storeNotification = async (userId, notification) => {
  try {
    return await prisma.notification.create({
      data: {
        userId,
        title: notification.title,
        body: notification.body,
        imageUrl: notification.imageUrl,
        data: notification.data,
        isRead: false
      }
    });
  } catch (error) {
    console.error('Error storing notification:', error);
    // Don't throw, as this shouldn't block sending the notification
  }
};

/**
 * Store a topic notification in the database
 * 
 * @param {string} topic - Topic the notification was sent to
 * @param {Object} notification - Notification content
 * @returns {Promise<Object>} Created notification
 */
const storeTopicNotification = async (topic, notification) => {
  try {
    return await prisma.topicNotification.create({
      data: {
        topic,
        title: notification.title,
        body: notification.body,
        imageUrl: notification.imageUrl,
        data: notification.data
      }
    });
  } catch (error) {
    console.error('Error storing topic notification:', error);
    // Don't throw, as this shouldn't block sending the notification
  }
};

/**
 * Remove failed FCM tokens for a user
 * 
 * @param {string} userId - ID of the user
 * @param {string[]} failedTokens - Failed FCM tokens
 * @returns {Promise<void>}
 */
const removeFailedTokens = async (userId, failedTokens) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { fcmTokens: true }
    });

    if (!user) return;

    // Filter out failed tokens
    const validTokens = user.fcmTokens.filter(
      token => !failedTokens.includes(token)
    );

    // Update user with valid tokens
    await prisma.user.update({
      where: { id: userId },
      data: { fcmTokens: validTokens }
    });
  } catch (error) {
    console.error('Error removing failed tokens:', error);
  }
};

/**
 * Subscribe a user to a topic
 * 
 * @param {string} userId - ID of the user
 * @param {string} topic - Topic to subscribe to
 * @returns {Promise<Object>} Result of the operation
 */
const subscribeToTopic = async (userId, topic) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { fcmTokens: true }
    });

    if (!user || !user.fcmTokens || user.fcmTokens.length === 0) {
      return { success: false, error: 'No FCM tokens found for user' };
    }

    const messaging = getMessaging();
    const response = await messaging.subscribeToTopic(user.fcmTokens, topic);

    // Store subscription in database
    await prisma.topicSubscription.create({
      data: {
        userId,
        topic
      }
    });

    return {
      success: true,
      successCount: response.successCount,
      failureCount: response.failureCount
    };
  } catch (error) {
    console.error(`Error subscribing user ${userId} to topic ${topic}:`, error);
    return { success: false, error: error.message };
  }
};

/**
 * Unsubscribe a user from a topic
 * 
 * @param {string} userId - ID of the user
 * @param {string} topic - Topic to unsubscribe from
 * @returns {Promise<Object>} Result of the operation
 */
const unsubscribeFromTopic = async (userId, topic) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { fcmTokens: true }
    });

    if (!user || !user.fcmTokens || user.fcmTokens.length === 0) {
      return { success: false, error: 'No FCM tokens found for user' };
    }

    const messaging = getMessaging();
    const response = await messaging.unsubscribeFromTopic(user.fcmTokens, topic);

    // Remove subscription from database
    await prisma.topicSubscription.deleteMany({
      where: {
        userId,
        topic
      }
    });

    return {
      success: true,
      successCount: response.successCount,
      failureCount: response.failureCount
    };
  } catch (error) {
    console.error(`Error unsubscribing user ${userId} from topic ${topic}:`, error);
    return { success: false, error: error.message };
  }
};

module.exports = {
  sendToUser,
  sendToUsers,
  sendToTopic,
  subscribeToTopic,
  unsubscribeFromTopic,
  templates
};
