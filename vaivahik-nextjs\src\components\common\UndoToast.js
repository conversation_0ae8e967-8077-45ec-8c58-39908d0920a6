import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Button, useTheme } from '@mui/material';

/**
 * UndoToast Component
 * 
 * A reusable toast notification component with undo functionality.
 * Used for providing feedback after actions with the ability to undo.
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.open - Whether the toast is visible
 * @param {string} props.message - Message to display in the toast
 * @param {Function} props.onClose - Function to call when the toast is closed
 * @param {Function} props.onUndo - Function to call when the undo button is clicked
 * @param {number} props.duration - Duration in milliseconds to show the toast (default: 6000)
 * @param {Object} props.position - Position of the toast (default: bottom center)
 */
const UndoToast = ({
  open,
  message,
  onClose,
  onUndo,
  duration = 6000,
  position = { vertical: 'bottom', horizontal: 'center' }
}) => {
  const theme = useTheme();
  
  return (
    <Snackbar
      open={open}
      autoHideDuration={duration}
      onClose={onClose}
      message={message}
      action={
        onUndo ? (
          <Button 
            color="secondary" 
            size="small" 
            onClick={() => {
              onUndo();
              onClose();
            }}
          >
            UNDO
          </Button>
        ) : undefined
      }
      anchorOrigin={position}
      sx={{ 
        '& .MuiSnackbarContent-root': { 
          bgcolor: theme.palette.background.paper,
          color: theme.palette.text.primary,
          boxShadow: theme.shadows[3]
        }
      }}
    />
  );
};

export default UndoToast;
