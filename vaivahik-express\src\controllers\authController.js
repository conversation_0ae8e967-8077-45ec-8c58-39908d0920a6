/**
 * Authentication Controller
 * 
 * This controller handles user authentication operations.
 */

const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const apiResponse = require('../utils/apiResponse');
const logger = require('../utils/logger');

/**
 * User login
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 */
const login = async (req, res, next) => {
  try {
    const { email, password } = req.body;
    
    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email }
    });
    
    // Check if user exists
    if (!user) {
      return apiResponse.unauthorized(res, 'Invalid email or password');
    }
    
    // Check if user is active
    if (!user.isActive) {
      return apiResponse.forbidden(res, 'Your account has been deactivated');
    }
    
    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    
    if (!isPasswordValid) {
      return apiResponse.unauthorized(res, 'Invalid email or password');
    }
    
    // Generate tokens
    const accessToken = generateAccessToken(user.id);
    const refreshToken = generateRefreshToken(user.id);
    
    // Store refresh token in database
    await prisma.refreshToken.create({
      data: {
        token: refreshToken,
        userId: user.id,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
      }
    });
    
    // Remove sensitive data
    const userData = {
      id: user.id,
      email: user.email,
      fullName: user.fullName,
      role: user.role
    };
    
    // Return success response
    return apiResponse.success(res, 'Login successful', {
      user: userData,
      accessToken,
      refreshToken
    });
  } catch (error) {
    logger.error('Error in login', { error: error.message });
    return next(error);
  }
};

/**
 * User registration
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 */
const register = async (req, res, next) => {
  try {
    const { email, password, fullName, phone, gender, dateOfBirth } = req.body;
    
    // Check if email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });
    
    if (existingUser) {
      return apiResponse.conflict(res, 'Email already in use');
    }
    
    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);
    
    // Create user
    const newUser = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        fullName,
        phone,
        gender,
        dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
        role: 'USER',
        isActive: true,
        isVerified: false,
        profile: {
          create: {} // Create empty profile
        }
      }
    });
    
    // Generate tokens
    const accessToken = generateAccessToken(newUser.id);
    const refreshToken = generateRefreshToken(newUser.id);
    
    // Store refresh token in database
    await prisma.refreshToken.create({
      data: {
        token: refreshToken,
        userId: newUser.id,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
      }
    });
    
    // Remove sensitive data
    const userData = {
      id: newUser.id,
      email: newUser.email,
      fullName: newUser.fullName,
      role: newUser.role
    };
    
    // Return success response
    return apiResponse.created(res, 'User registered successfully', {
      user: userData,
      accessToken,
      refreshToken
    });
  } catch (error) {
    logger.error('Error in register', { error: error.message });
    return next(error);
  }
};

/**
 * Refresh access token
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 */
const refreshToken = async (req, res, next) => {
  try {
    const { refreshToken } = req.body;
    
    // Verify refresh token
    let decoded;
    try {
      decoded = jwt.verify(refreshToken, process.env.REFRESH_TOKEN_SECRET);
    } catch (error) {
      return apiResponse.unauthorized(res, 'Invalid refresh token');
    }
    
    // Check if refresh token exists in database
    const storedToken = await prisma.refreshToken.findFirst({
      where: {
        token: refreshToken,
        userId: decoded.userId,
        expiresAt: {
          gt: new Date()
        }
      }
    });
    
    if (!storedToken) {
      return apiResponse.unauthorized(res, 'Invalid refresh token');
    }
    
    // Generate new access token
    const accessToken = generateAccessToken(decoded.userId);
    
    // Return success response
    return apiResponse.success(res, 'Token refreshed successfully', {
      accessToken
    });
  } catch (error) {
    logger.error('Error in refreshToken', { error: error.message });
    return next(error);
  }
};

/**
 * User logout
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 */
const logout = async (req, res, next) => {
  try {
    // Get authorization header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return apiResponse.unauthorized(res, 'No token provided');
    }
    
    // Extract token
    const token = authHeader.split(' ')[1];
    
    // Verify token
    let decoded;
    try {
      decoded = jwt.verify(token, process.env.ACCESS_TOKEN_SECRET);
    } catch (error) {
      return apiResponse.unauthorized(res, 'Invalid token');
    }
    
    // Delete all refresh tokens for user
    await prisma.refreshToken.deleteMany({
      where: {
        userId: decoded.userId
      }
    });
    
    // Return success response
    return apiResponse.success(res, 'Logout successful');
  } catch (error) {
    logger.error('Error in logout', { error: error.message });
    return next(error);
  }
};

/**
 * Generate access token
 * @param {string} userId - User ID
 * @returns {string} Access token
 */
const generateAccessToken = (userId) => {
  return jwt.sign(
    { userId },
    process.env.ACCESS_TOKEN_SECRET,
    { expiresIn: '1h' }
  );
};

/**
 * Generate refresh token
 * @param {string} userId - User ID
 * @returns {string} Refresh token
 */
const generateRefreshToken = (userId) => {
  return jwt.sign(
    { userId },
    process.env.REFRESH_TOKEN_SECRET,
    { expiresIn: '7d' }
  );
};

module.exports = {
  login,
  register,
  refreshToken,
  logout
};
