// API endpoint for specific verification request
import { generateMockVerifications } from '@/utils/mockData';

export default function handler(req, res) {
  // Get the verification ID from the URL
  const { id } = req.query;
  
  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getVerificationById(req, res, id);
    case 'POST':
      return updateVerificationStatus(req, res, id);
    default:
      return res.status(405).json({ message: 'Method not allowed' });
  }
}

// GET /api/admin/verification-queue/[id]
function getVerificationById(req, res, id) {
  try {
    // In a real implementation, this would fetch data from a database
    // For now, we'll use mock data
    const mockVerifications = generateMockVerifications();
    
    // Find the verification by ID
    const verification = mockVerifications.find(v => v.id.toString() === id);
    
    // If verification not found, return 404
    if (!verification) {
      return res.status(404).json({ 
        success: false, 
        message: 'Verification not found' 
      });
    }
    
    // Return the verification
    return res.status(200).json({
      success: true,
      verification
    });
  } catch (error) {
    console.error('Error fetching verification:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch verification' 
    });
  }
}

// POST /api/admin/verification-queue/[id]
function updateVerificationStatus(req, res, id) {
  try {
    // Get the action from the URL (approve or reject)
    const { action } = req.query;
    
    // Validate the action
    if (!action || (action !== 'approve' && action !== 'reject')) {
      return res.status(400).json({ 
        success: false, 
        message: 'Invalid action. Must be "approve" or "reject"' 
      });
    }
    
    // In a real implementation, this would update the database
    // For now, we'll just return a success response
    
    return res.status(200).json({
      success: true,
      message: `Verification ${action === 'approve' ? 'approved' : 'rejected'} successfully`,
      verificationId: id,
      action
    });
  } catch (error) {
    console.error('Error updating verification status:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to update verification status' 
    });
  }
}
