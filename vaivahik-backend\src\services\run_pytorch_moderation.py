#!/usr/bin/env python
"""
Python bridge script to run PyTorch-based photo moderation

This script serves as a bridge between the Node.js application and the PyTorch
photo moderation implementation. It takes an image path as input, processes
the image using the PyTorch implementation, and returns the results as JSON.
"""

import sys
import json
import asyncio
from pathlib import Path

# Add the current directory to sys.path
sys.path.append(str(Path(__file__).parent))

# Import the PyTorch moderation service
from photoModeration_pytorch import PhotoModerationService

# Initialize the service
moderation_service = PhotoModerationService()

async def process_image(image_path, photo_id=None):
    """Process an image and return moderation results"""
    result = await moderation_service.process_photo(image_path, photo_id)
    return result

async def main():
    """Main function to handle command line arguments"""
    if len(sys.argv) < 2:
        print(json.dumps({
            "error": "No image path provided",
            "decision": "PENDING",
            "flags": ["error"],
            "confidence": 0
        }))
        return
    
    image_path = sys.argv[1]
    photo_id = sys.argv[2] if len(sys.argv) > 2 else None
    
    try:
        result = await process_image(image_path, photo_id)
        print(json.dumps(result))
    except Exception as e:
        print(json.dumps({
            "error": str(e),
            "decision": "PENDING",
            "flags": ["error"],
            "confidence": 0
        }))

if __name__ == "__main__":
    asyncio.run(main())
