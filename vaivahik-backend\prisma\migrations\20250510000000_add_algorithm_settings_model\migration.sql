-- CreateTable
CREATE TABLE "algorithm_settings" (
    "id" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "data_type" TEXT NOT NULL DEFAULT 'STRING',
    "description" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "algorithm_settings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "algorithm_models" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "version" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "description" TEXT,
    "model_path" TEXT,
    "config" TEXT,
    "metrics" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT false,
    "is_default" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "algorithm_models_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "algorithm_ab_tests" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "variant_a" TEXT NOT NULL,
    "variant_b" TEXT NOT NULL,
    "distribution" INTEGER NOT NULL DEFAULT 50,
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3),
    "metrics" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "algorithm_ab_tests_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "algorithm_settings_category_key_key" ON "algorithm_settings"("category", "key");

-- CreateIndex
CREATE UNIQUE INDEX "algorithm_models_name_version_key" ON "algorithm_models"("name", "version");

-- CreateIndex
CREATE UNIQUE INDEX "algorithm_ab_tests_name_key" ON "algorithm_ab_tests"("name");
