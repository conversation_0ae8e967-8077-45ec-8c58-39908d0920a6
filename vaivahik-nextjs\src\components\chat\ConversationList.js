import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import styles from '@/styles/ConversationList.module.css';
import PromotionBanner from './PromotionBanner';

const ConversationItem = ({ conversation, isActive, onClick }) => {
  const { id, otherUser, lastMessage, lastMessageAt, unreadCount } = conversation;

  // Format the last message time
  const formatTime = (timestamp) => {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const now = new Date();
    const diffDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      // Today - show time
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      // Yesterday
      return 'Yesterday';
    } else if (diffDays < 7) {
      // This week - show day name
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      // Older - show date
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  // Truncate message content
  const truncateMessage = (content, maxLength = 40) => {
    if (!content) return '';
    return content.length > maxLength
      ? content.substring(0, maxLength) + '...'
      : content;
  };

  return (
    <div
      className={`${styles.conversationItem} ${isActive ? styles.active : ''}`}
      onClick={() => onClick(id)}
    >
      <div className={styles.avatar}>
        {otherUser.profilePicture ? (
          <img src={otherUser.profilePicture} alt={otherUser.name} />
        ) : (
          otherUser.name.charAt(0)
        )}
      </div>
      <div className={styles.conversationDetails}>
        <div className={styles.conversationHeader}>
          <h3 className={styles.userName}>{otherUser.name}</h3>
          <span className={styles.timestamp}>{formatTime(lastMessageAt)}</span>
        </div>
        <div className={styles.conversationPreview}>
          <p className={styles.lastMessage}>
            {lastMessage ? truncateMessage(lastMessage.content) : 'No messages yet'}
          </p>
          {unreadCount > 0 && (
            <span className={styles.unreadBadge}>{unreadCount}</span>
          )}
        </div>
      </div>
    </div>
  );
};

const ConversationList = ({ userId, token, apiBaseUrl, onSelectConversation, chatSettings }) => {
  const [conversations, setConversations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeConversation, setActiveConversation] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [activePromotion, setActivePromotion] = useState(null);

  const router = useRouter();

  // Fetch conversations
  useEffect(() => {
    if (!token || !userId) return;

    // Set active promotion from chat settings
    if (chatSettings &&
        chatSettings.promotions &&
        chatSettings.promotions.hasActivePromotions &&
        chatSettings.promotions.activePromotions.length > 0) {
      setActivePromotion(chatSettings.promotions.activePromotions[0]);
    }

    const fetchConversations = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${apiBaseUrl}/api/users/conversations`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error('Failed to fetch conversations');
        }

        const data = await response.json();

        if (data.success) {
          setConversations(data.conversations);
        } else {
          throw new Error(data.message || 'Failed to fetch conversations');
        }
      } catch (error) {
        console.error('Error fetching conversations:', error);
        setError(error.message || 'Failed to fetch conversations');
      } finally {
        setLoading(false);
      }
    };

    fetchConversations();

    // Set up polling for new messages
    const intervalId = setInterval(fetchConversations, 30000); // Poll every 30 seconds

    return () => clearInterval(intervalId);
  }, [token, userId, apiBaseUrl]);

  // Handle conversation selection
  const handleSelectConversation = (conversationId) => {
    setActiveConversation(conversationId);
    if (onSelectConversation) {
      onSelectConversation(conversationId);
    } else {
      router.push(`/messages/${conversationId}`);
    }
  };

  // Filter conversations based on search query
  const filteredConversations = conversations.filter(conversation =>
    conversation.otherUser.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className={styles.conversationList}>
      <div className={styles.header}>
        <h2>Messages</h2>
        <button
          className={styles.newChatButton}
          onClick={() => router.push('/matches')}
        >
          New Chat
        </button>
      </div>

      {activePromotion && (
        <div className={styles.inlinePromotion}>
          <PromotionBanner promotion={activePromotion} />
        </div>
      )}

      <div className={styles.searchContainer}>
        <input
          type="text"
          placeholder="Search conversations..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className={styles.searchInput}
        />
      </div>

      <div className={styles.conversations}>
        {loading ? (
          <div className={styles.loadingContainer}>
            <div className={styles.loadingSpinner}></div>
            <p>Loading conversations...</p>
          </div>
        ) : error ? (
          <div className={styles.errorContainer}>
            <p>{error}</p>
            <button onClick={() => setError(null)}>Dismiss</button>
          </div>
        ) : filteredConversations.length === 0 ? (
          <div className={styles.emptyState}>
            {searchQuery ? (
              <p>No conversations match your search.</p>
            ) : (
              <>
                <p>No conversations yet.</p>
                <button
                  className={styles.startChatButton}
                  onClick={() => router.push('/matches')}
                >
                  Start a conversation
                </button>
              </>
            )}
          </div>
        ) : (
          filteredConversations.map(conversation => (
            <ConversationItem
              key={conversation.id}
              conversation={conversation}
              isActive={conversation.id === activeConversation}
              onClick={handleSelectConversation}
            />
          ))
        )}
      </div>
    </div>
  );
};

export default ConversationList;
