/**
 * API Response Utility
 * 
 * This utility provides standardized response formats for all API endpoints.
 * It ensures consistent response structure across the application.
 */

/**
 * Send a success response
 * @param {object} res - Express response object
 * @param {string} message - Success message
 * @param {object|array} data - Response data
 * @param {number} statusCode - HTTP status code (default: 200)
 * @param {object} meta - Additional metadata (pagination, etc.)
 * @returns {object} - Express response
 */
const success = (res, message = 'Success', data = null, statusCode = 200, meta = null) => {
  const response = {
    success: true,
    message,
    timestamp: new Date().toISOString()
  };

  // Add data if provided
  if (data !== null) {
    response.data = data;
  }

  // Add metadata if provided
  if (meta !== null) {
    response.meta = meta;
  }

  return res.status(statusCode).json(response);
};

/**
 * Send an error response
 * @param {object} res - Express response object
 * @param {string} message - Error message
 * @param {number} statusCode - HTTP status code (default: 400)
 * @param {object} errors - Validation errors or additional error details
 * @param {string} errorCode - Error code for client-side error handling
 * @returns {object} - Express response
 */
const error = (res, message = 'Error', statusCode = 400, errors = null, errorCode = null) => {
  const response = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };

  // Add errors if provided
  if (errors !== null) {
    response.errors = errors;
  }

  // Add error code if provided
  if (errorCode !== null) {
    response.errorCode = errorCode;
  }

  return res.status(statusCode).json(response);
};

/**
 * Send a created response (201)
 * @param {object} res - Express response object
 * @param {string} message - Success message
 * @param {object|array} data - Response data
 * @returns {object} - Express response
 */
const created = (res, message = 'Resource created successfully', data = null) => {
  return success(res, message, data, 201);
};

/**
 * Send a no content response (204)
 * @param {object} res - Express response object
 * @returns {object} - Express response
 */
const noContent = (res) => {
  return res.status(204).end();
};

/**
 * Send a bad request response (400)
 * @param {object} res - Express response object
 * @param {string} message - Error message
 * @param {object} errors - Validation errors
 * @returns {object} - Express response
 */
const badRequest = (res, message = 'Bad request', errors = null) => {
  return error(res, message, 400, errors, 'BAD_REQUEST');
};

/**
 * Send an unauthorized response (401)
 * @param {object} res - Express response object
 * @param {string} message - Error message
 * @returns {object} - Express response
 */
const unauthorized = (res, message = 'Unauthorized') => {
  return error(res, message, 401, null, 'UNAUTHORIZED');
};

/**
 * Send a forbidden response (403)
 * @param {object} res - Express response object
 * @param {string} message - Error message
 * @returns {object} - Express response
 */
const forbidden = (res, message = 'Forbidden') => {
  return error(res, message, 403, null, 'FORBIDDEN');
};

/**
 * Send a not found response (404)
 * @param {object} res - Express response object
 * @param {string} message - Error message
 * @returns {object} - Express response
 */
const notFound = (res, message = 'Resource not found') => {
  return error(res, message, 404, null, 'NOT_FOUND');
};

/**
 * Send a validation error response (422)
 * @param {object} res - Express response object
 * @param {string} message - Error message
 * @param {object} errors - Validation errors
 * @returns {object} - Express response
 */
const validationError = (res, message = 'Validation error', errors = null) => {
  return error(res, message, 422, errors, 'VALIDATION_ERROR');
};

/**
 * Send a conflict response (409)
 * @param {object} res - Express response object
 * @param {string} message - Error message
 * @param {object} errors - Additional error details
 * @returns {object} - Express response
 */
const conflict = (res, message = 'Conflict', errors = null) => {
  return error(res, message, 409, errors, 'CONFLICT');
};

/**
 * Send a server error response (500)
 * @param {object} res - Express response object
 * @param {string} message - Error message
 * @param {object} errors - Additional error details (only in development)
 * @returns {object} - Express response
 */
const serverError = (res, message = 'Internal server error', errors = null) => {
  // Only include detailed errors in development
  const errorDetails = process.env.NODE_ENV === 'development' ? errors : null;
  return error(res, message, 500, errorDetails, 'SERVER_ERROR');
};

/**
 * Send a service unavailable response (503)
 * @param {object} res - Express response object
 * @param {string} message - Error message
 * @returns {object} - Express response
 */
const serviceUnavailable = (res, message = 'Service unavailable') => {
  return error(res, message, 503, null, 'SERVICE_UNAVAILABLE');
};

/**
 * Send a too many requests response (429)
 * @param {object} res - Express response object
 * @param {string} message - Error message
 * @param {number} retryAfter - Seconds to wait before retrying
 * @returns {object} - Express response
 */
const tooManyRequests = (res, message = 'Too many requests', retryAfter = 60) => {
  res.setHeader('Retry-After', retryAfter);
  return error(res, message, 429, { retryAfter }, 'TOO_MANY_REQUESTS');
};

/**
 * Send a paginated response
 * @param {object} res - Express response object
 * @param {string} message - Success message
 * @param {object|array} data - Response data
 * @param {object} pagination - Pagination details
 * @param {number} statusCode - HTTP status code (default: 200)
 * @returns {object} - Express response
 */
const paginated = (res, message = 'Success', data = [], pagination = {}, statusCode = 200) => {
  const { page = 1, limit = 10, totalItems = 0, totalPages = 1 } = pagination;
  
  return success(res, message, data, statusCode, {
    pagination: {
      page: parseInt(page, 10),
      limit: parseInt(limit, 10),
      totalItems,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1
    }
  });
};

module.exports = {
  success,
  error,
  created,
  noContent,
  badRequest,
  unauthorized,
  forbidden,
  notFound,
  validationError,
  conflict,
  serverError,
  serviceUnavailable,
  tooManyRequests,
  paginated
};
