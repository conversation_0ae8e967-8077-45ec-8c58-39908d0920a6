// Import the auth utility
// Using relative path instead of alias to avoid import issues
import { verifyAdminToken } from '../../../../utils/auth';

export default async function handler(req, res) {
  console.log('API: Received request to /api/admin/chat-settings');
  console.log('Request method:', req.method);

  // Check if the request method is GET
  if (req.method !== 'GET') {
    console.log('Method not allowed:', req.method);
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Log auth header for debugging
    console.log('Auth header exists:', !!req.headers.authorization);

    // Verify admin token
    const adminData = verifyAdminToken(req);
    if (!adminData) {
      console.log('Unauthorized: Invalid or missing token');
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    console.log('Admin authenticated:', adminData.name);

    // Mock data for chat settings
    // In a real application, you would fetch this from your database
    const chatSettings = {
      success: true,
      settings: {
        aiFeatures: {
          contentModeration: {
            enabled: true,
            strictness: 'medium',
            tierSettings: {
              BASIC: {
                strictness: 'high',
                autoReject: true,
                maskProfanity: true,
                allowContactInfo: false,
                allowedContactTypes: []
              },
              VERIFIED: {
                strictness: 'medium',
                autoReject: true,
                maskProfanity: true,
                allowContactInfo: false,
                allowedContactTypes: []
              },
              PREMIUM: {
                strictness: 'low',
                autoReject: false,
                maskProfanity: false,
                allowContactInfo: true,
                allowedContactTypes: ['email', 'phone']
              }
            }
          }
        }
      }
    };

    return res.status(200).json(chatSettings);
  } catch (error) {
    console.error('Error in chat-settings API:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
}
