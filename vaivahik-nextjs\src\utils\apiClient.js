/**
 * API Client for communicating with the backend
 */
import { API_BASE_URL } from '@/config';

/**
 * Make a GET request to the API
 * @param {string} endpoint - API endpoint
 * @param {object} params - Query parameters
 * @returns {Promise<object>} - API response
 */
export const apiGet = async (endpoint, params = {}) => {
  try {
    // Build query string
    const queryString = Object.keys(params).length > 0
      ? `?${new URLSearchParams(params).toString()}`
      : '';
    
    // Make request
    const response = await fetch(`${API_BASE_URL}${endpoint}${queryString}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies for authentication
    });
    
    // Parse response
    const data = await response.json();
    
    // Check for error
    if (!response.ok) {
      throw new Error(data.message || 'API request failed');
    }
    
    return data;
  } catch (error) {
    console.error(`API GET error (${endpoint}):`, error);
    throw error;
  }
};

/**
 * Make a POST request to the API
 * @param {string} endpoint - API endpoint
 * @param {object} body - Request body
 * @returns {Promise<object>} - API response
 */
export const apiPost = async (endpoint, body = {}) => {
  try {
    // Make request
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies for authentication
      body: JSON.stringify(body),
    });
    
    // Parse response
    const data = await response.json();
    
    // Check for error
    if (!response.ok) {
      throw new Error(data.message || 'API request failed');
    }
    
    return data;
  } catch (error) {
    console.error(`API POST error (${endpoint}):`, error);
    throw error;
  }
};

/**
 * Make a PUT request to the API
 * @param {string} endpoint - API endpoint
 * @param {object} body - Request body
 * @returns {Promise<object>} - API response
 */
export const apiPut = async (endpoint, body = {}) => {
  try {
    // Make request
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies for authentication
      body: JSON.stringify(body),
    });
    
    // Parse response
    const data = await response.json();
    
    // Check for error
    if (!response.ok) {
      throw new Error(data.message || 'API request failed');
    }
    
    return data;
  } catch (error) {
    console.error(`API PUT error (${endpoint}):`, error);
    throw error;
  }
};

/**
 * Make a DELETE request to the API
 * @param {string} endpoint - API endpoint
 * @param {object} params - Query parameters
 * @returns {Promise<object>} - API response
 */
export const apiDelete = async (endpoint, params = {}) => {
  try {
    // Build query string
    const queryString = Object.keys(params).length > 0
      ? `?${new URLSearchParams(params).toString()}`
      : '';
    
    // Make request
    const response = await fetch(`${API_BASE_URL}${endpoint}${queryString}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies for authentication
    });
    
    // Parse response
    const data = await response.json();
    
    // Check for error
    if (!response.ok) {
      throw new Error(data.message || 'API request failed');
    }
    
    return data;
  } catch (error) {
    console.error(`API DELETE error (${endpoint}):`, error);
    throw error;
  }
};
