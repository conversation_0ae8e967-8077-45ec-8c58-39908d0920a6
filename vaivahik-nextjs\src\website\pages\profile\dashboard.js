/**
 * Profile Dashboard Page
 *
 * This page displays the profile completion dashboard with gamification elements.
 */

import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Paper,
  CircularProgress,
  Al<PERSON>,
  But<PERSON>
} from '@mui/material';
import ProfileCompletionDashboard from '@/website/components/profile/ProfileCompletionDashboard';
import { isUsingRealBackend } from '@/utils/featureFlags';
import { api } from '@/utils/axiosConfig';
import { useAuth } from '@/contexts/AuthContext';

// Mock user data for development
const MOCK_USER_DATA = {
  id: 'user-1',
  fullName: '<PERSON><PERSON>',
  gender: 'MALE',
  dateOfBirth: '1990-05-15',
  profileCompletionPercentage: 65,
  profilePhoto: '/images/profile-placeholder.jpg',
  education: 'Master\'s Degree',
  educationField: 'Computer Science',
  occupation: 'Software Engineer',
  city: 'Mumbai',
  state: 'Maharashtra',
  aboutMe: 'I am a software engineer who loves to travel and explore new places.',
  familyDetails: {
    familyType: 'NUCLEAR',
    familyStatus: 'MIDDLE_CLASS',
    father<PERSON>ame: '<PERSON>esh <PERSON>',
    fatherOccupation: 'Government Employee',
    motherName: 'Sunita <PERSON>',
    motherOccupation: 'Teacher',
    siblings: '1 younger sister',
    totalSiblings: '1',
    marriedSiblings: '0',
    motherTongue: 'MARATHI',
    marathiProficiency: 'NATIVE',
    kul: 'Sharma',
    maharashtrianOrigin: true,
    nativePlace: 'Pune',
    nativeDistrict: 'Pune'
  },
  preferences: {
    ageMin: 25,
    ageMax: 32,
    heightMin: '5.0',
    heightMax: '5.8',
    educationLevel: ['Bachelor\'s Degree', 'Master\'s Degree'],
    occupations: ['Software Engineer', 'Teacher', 'Doctor'],
    incomeMin: '5L_10L',
    preferredCities: ['Mumbai', 'Pune'],
    preferredStates: ['Maharashtra'],
    acceptSubCastes: ['96 Kuli Maratha', 'Deshmukh'],
    gotraPreference: '',
    dietPreference: 'VEGETARIAN'
  },
  lifestyle: {
    diet: 'VEGETARIAN',
    smoking: 'NO',
    drinking: 'OCCASIONALLY',
    hobbies: ['Reading', 'Traveling', 'Music', 'Photography', 'Cricket'],
    interests: 'I enjoy watching documentaries about history and science. I also like to explore new cuisines and restaurants.'
  }
};

export default function ProfileDashboardPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoading(true);

        if (isUsingRealBackend()) {
          // Call real API
          const response = await api.get('/api/users/profile');
          setUserData(response.data);
        } else {
          // Use mock data
          setTimeout(() => {
            setUserData(MOCK_USER_DATA);
            setLoading(false);
          }, 500); // Simulate API delay
        }
      } catch (err) {
        console.error('Error fetching user data:', err);
        setError('Failed to load user data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  // Handle section update
  const handleUpdateSection = (route) => {
    router.push(route);
  };

  return (
    <>
      <Head>
        <title>Profile Dashboard | Vaivahik</title>
        <meta name="description" content="Complete your profile on Vaivahik matrimony" />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2 }}>
          <Typography variant="h4" gutterBottom>
            Profile Dashboard
          </Typography>
          <Typography variant="body1" paragraph>
            Complete your profile to get better matches and unlock premium features. Our matching algorithm works best with complete profiles.
          </Typography>
        </Paper>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
            <CircularProgress />
          </Box>
        ) : (
          <ProfileCompletionDashboard
            userData={userData}
            onUpdateSection={handleUpdateSection}
          />
        )}
      </Container>
    </>
  );
}
