/**
 * Advanced Search Widget - Leverages existing admin search functionality
 * Provides comprehensive search with filters for premium users
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  TextField,
  Button,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Switch,
  FormControlLabel,
  Collapse,
  IconButton,
  Divider,
  styled,
  Alert,
  Autocomplete
} from '@mui/material';

// Icons
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Tune as AdvancedFilterIcon,
  ExpandMore as ExpandIcon,
  ExpandLess as CollapseIcon,
  Clear as ClearIcon,
  Save as SaveIcon,
  Star as StarIcon,
  WorkspacePremium as PremiumIcon
} from '@mui/icons-material';

// Styled components
const SearchCard = styled(Card)(({ theme }) => ({
  borderRadius: 16,
  boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
  border: '1px solid rgba(255,255,255,0.1)'
}));

const FilterChip = styled(Chip)(({ theme }) => ({
  margin: theme.spacing(0.5),
  '&.premium': {
    background: 'linear-gradient(135deg, #FFD700, #FFA000)',
    color: '#000',
    fontWeight: 600
  }
}));

const PremiumSection = styled(Box)(({ theme }) => ({
  background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 160, 0, 0.1))',
  border: '2px dashed #FFD700',
  borderRadius: 12,
  padding: theme.spacing(3),
  textAlign: 'center',
  position: 'relative'
}));

// Search filter options (based on your admin search functionality)
const ageRanges = [
  { label: '18-25', value: [18, 25] },
  { label: '26-30', value: [26, 30] },
  { label: '31-35', value: [31, 35] },
  { label: '36-40', value: [36, 40] },
  { label: '41+', value: [41, 60] }
];

const educationOptions = [
  'High School', 'Diploma', 'Bachelor\'s', 'Master\'s', 'PhD', 'Professional'
];

const occupationOptions = [
  'Software Engineer', 'Doctor', 'Teacher', 'Business', 'Government', 'Other'
];

const locationOptions = [
  'Mumbai', 'Pune', 'Nashik', 'Kolhapur', 'Aurangabad', 'Nagpur', 'Other'
];

const incomeRanges = [
  { label: 'Below 5 LPA', value: [0, 500000] },
  { label: '5-10 LPA', value: [500000, 1000000] },
  { label: '10-20 LPA', value: [1000000, 2000000] },
  { label: '20+ LPA', value: [2000000, 10000000] }
];

const marathaSubcastes = [
  'Maratha', '96 Kuli Maratha', 'Kunbi Maratha', 'Other Maratha'
];

export default function AdvancedSearchWidget({ isPremium, onPremiumFeatureClick }) {
  const [searchQuery, setSearchQuery] = useState('');
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [activeFilters, setActiveFilters] = useState({});
  const [savedSearches, setSavedSearches] = useState([]);
  
  // Basic filters
  const [ageRange, setAgeRange] = useState([21, 35]);
  const [selectedEducation, setSelectedEducation] = useState([]);
  const [selectedOccupation, setSelectedOccupation] = useState([]);
  const [selectedLocation, setSelectedLocation] = useState([]);
  
  // Premium filters
  const [incomeRange, setIncomeRange] = useState([500000, 2000000]);
  const [selectedSubcaste, setSelectedSubcaste] = useState([]);
  const [heightRange, setHeightRange] = useState([4.5, 6.5]);
  const [onlyVerified, setOnlyVerified] = useState(false);
  const [onlyWithPhotos, setOnlyWithPhotos] = useState(false);
  const [recentlyActive, setRecentlyActive] = useState(false);

  const handleSearch = () => {
    const filters = {
      query: searchQuery,
      ageRange,
      education: selectedEducation,
      occupation: selectedOccupation,
      location: selectedLocation,
      ...(isPremium && {
        incomeRange,
        subcaste: selectedSubcaste,
        heightRange,
        onlyVerified,
        onlyWithPhotos,
        recentlyActive
      })
    };

    // This would call your existing search API
    console.log('Searching with filters:', filters);
    setActiveFilters(filters);
  };

  const handleClearFilters = () => {
    setSearchQuery('');
    setAgeRange([21, 35]);
    setSelectedEducation([]);
    setSelectedOccupation([]);
    setSelectedLocation([]);
    setIncomeRange([500000, 2000000]);
    setSelectedSubcaste([]);
    setHeightRange([4.5, 6.5]);
    setOnlyVerified(false);
    setOnlyWithPhotos(false);
    setRecentlyActive(false);
    setActiveFilters({});
  };

  const handleSaveSearch = () => {
    if (!isPremium) {
      onPremiumFeatureClick('save-search');
      return;
    }
    
    const searchName = `Search ${savedSearches.length + 1}`;
    const newSearch = {
      id: Date.now(),
      name: searchName,
      filters: { ...activeFilters },
      createdAt: new Date()
    };
    
    setSavedSearches([...savedSearches, newSearch]);
  };

  const renderBasicFilters = () => (
    <Grid container spacing={3}>
      {/* Age Range */}
      <Grid item xs={12} md={6}>
        <Typography gutterBottom>Age Range: {ageRange[0]} - {ageRange[1]} years</Typography>
        <Slider
          value={ageRange}
          onChange={(e, newValue) => setAgeRange(newValue)}
          valueLabelDisplay="auto"
          min={18}
          max={60}
          sx={{ color: '#667eea' }}
        />
      </Grid>

      {/* Education */}
      <Grid item xs={12} md={6}>
        <Autocomplete
          multiple
          options={educationOptions}
          value={selectedEducation}
          onChange={(e, newValue) => setSelectedEducation(newValue)}
          renderInput={(params) => (
            <TextField {...params} label="Education" placeholder="Select education levels" />
          )}
          renderTags={(value, getTagProps) =>
            value.map((option, index) => (
              <Chip variant="outlined" label={option} {...getTagProps({ index })} />
            ))
          }
        />
      </Grid>

      {/* Occupation */}
      <Grid item xs={12} md={6}>
        <Autocomplete
          multiple
          options={occupationOptions}
          value={selectedOccupation}
          onChange={(e, newValue) => setSelectedOccupation(newValue)}
          renderInput={(params) => (
            <TextField {...params} label="Occupation" placeholder="Select occupations" />
          )}
          renderTags={(value, getTagProps) =>
            value.map((option, index) => (
              <Chip variant="outlined" label={option} {...getTagProps({ index })} />
            ))
          }
        />
      </Grid>

      {/* Location */}
      <Grid item xs={12} md={6}>
        <Autocomplete
          multiple
          options={locationOptions}
          value={selectedLocation}
          onChange={(e, newValue) => setSelectedLocation(newValue)}
          renderInput={(params) => (
            <TextField {...params} label="Location" placeholder="Select locations" />
          )}
          renderTags={(value, getTagProps) =>
            value.map((option, index) => (
              <Chip variant="outlined" label={option} {...getTagProps({ index })} />
            ))
          }
        />
      </Grid>
    </Grid>
  );

  const renderPremiumFilters = () => (
    <>
      {isPremium ? (
        <Grid container spacing={3}>
          {/* Income Range */}
          <Grid item xs={12} md={6}>
            <Typography gutterBottom>
              Income Range: ₹{(incomeRange[0] / 100000).toFixed(1)}L - ₹{(incomeRange[1] / 100000).toFixed(1)}L
            </Typography>
            <Slider
              value={incomeRange}
              onChange={(e, newValue) => setIncomeRange(newValue)}
              valueLabelDisplay="auto"
              min={0}
              max={5000000}
              step={100000}
              valueLabelFormat={(value) => `₹${(value / 100000).toFixed(1)}L`}
              sx={{ color: '#FFD700' }}
            />
          </Grid>

          {/* Height Range */}
          <Grid item xs={12} md={6}>
            <Typography gutterBottom>
              Height Range: {heightRange[0]}' - {heightRange[1]}'
            </Typography>
            <Slider
              value={heightRange}
              onChange={(e, newValue) => setHeightRange(newValue)}
              valueLabelDisplay="auto"
              min={4.0}
              max={7.0}
              step={0.1}
              sx={{ color: '#FFD700' }}
            />
          </Grid>

          {/* Maratha Subcaste */}
          <Grid item xs={12} md={6}>
            <Autocomplete
              multiple
              options={marathaSubcastes}
              value={selectedSubcaste}
              onChange={(e, newValue) => setSelectedSubcaste(newValue)}
              renderInput={(params) => (
                <TextField {...params} label="Maratha Subcaste" placeholder="Select subcaste" />
              )}
              renderTags={(value, getTagProps) =>
                value.map((option, index) => (
                  <FilterChip className="premium" label={option} {...getTagProps({ index })} />
                ))
              }
            />
          </Grid>

          {/* Premium Toggles */}
          <Grid item xs={12} md={6}>
            <Box>
              <FormControlLabel
                control={
                  <Switch
                    checked={onlyVerified}
                    onChange={(e) => setOnlyVerified(e.target.checked)}
                    sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#FFD700' } }}
                  />
                }
                label="Only Verified Profiles"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={onlyWithPhotos}
                    onChange={(e) => setOnlyWithPhotos(e.target.checked)}
                    sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#FFD700' } }}
                  />
                }
                label="Only With Photos"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={recentlyActive}
                    onChange={(e) => setRecentlyActive(e.target.checked)}
                    sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#FFD700' } }}
                  />
                }
                label="Recently Active"
              />
            </Box>
          </Grid>
        </Grid>
      ) : (
        <PremiumSection>
          <PremiumIcon sx={{ fontSize: 48, color: '#FFD700', mb: 2 }} />
          <Typography variant="h6" fontWeight="600" gutterBottom>
            Premium Search Filters
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Unlock advanced filters like income range, height preferences, subcaste options, and more!
          </Typography>
          <Button
            variant="contained"
            startIcon={<PremiumIcon />}
            onClick={() => onPremiumFeatureClick('advanced-search')}
            sx={{
              background: 'linear-gradient(135deg, #FFD700, #FFA000)',
              color: '#000',
              fontWeight: 600
            }}
          >
            Upgrade to Premium
          </Button>
        </PremiumSection>
      )}
    </>
  );

  return (
    <Box>
      {/* Main Search */}
      <SearchCard sx={{ mb: 3 }}>
        <CardContent sx={{ p: 4 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <SearchIcon sx={{ fontSize: 32, color: '#667eea', mr: 2 }} />
            <Typography variant="h5" fontWeight="700">
              Advanced Search
            </Typography>
          </Box>

          {/* Search Input */}
          <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
            <TextField
              fullWidth
              placeholder="Search by name, profession, location..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ color: 'text.secondary', mr: 1 }} />
              }}
            />
            <Button
              variant="contained"
              onClick={handleSearch}
              sx={{
                background: 'linear-gradient(135deg, #667eea, #764ba2)',
                minWidth: 120
              }}
            >
              Search
            </Button>
          </Box>

          {/* Filter Toggle */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Button
              startIcon={<FilterIcon />}
              endIcon={showAdvancedFilters ? <CollapseIcon /> : <ExpandIcon />}
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              variant="outlined"
            >
              Advanced Filters
            </Button>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                size="small"
                startIcon={<SaveIcon />}
                onClick={handleSaveSearch}
                disabled={Object.keys(activeFilters).length === 0}
              >
                {isPremium ? 'Save Search' : 'Save (Premium)'}
              </Button>
              <Button
                size="small"
                startIcon={<ClearIcon />}
                onClick={handleClearFilters}
                color="error"
              >
                Clear All
              </Button>
            </Box>
          </Box>

          {/* Advanced Filters */}
          <Collapse in={showAdvancedFilters}>
            <Box sx={{ pt: 3 }}>
              <Typography variant="h6" fontWeight="600" gutterBottom>
                Basic Filters
              </Typography>
              {renderBasicFilters()}
              
              <Divider sx={{ my: 4 }} />
              
              <Typography variant="h6" fontWeight="600" gutterBottom>
                Premium Filters
                {!isPremium && (
                  <Chip
                    label="Premium"
                    size="small"
                    sx={{
                      ml: 1,
                      background: 'linear-gradient(135deg, #FFD700, #FFA000)',
                      color: '#000'
                    }}
                  />
                )}
              </Typography>
              {renderPremiumFilters()}
            </Box>
          </Collapse>
        </CardContent>
      </SearchCard>

      {/* Active Filters */}
      {Object.keys(activeFilters).length > 0 && (
        <SearchCard sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="subtitle1" fontWeight="600" gutterBottom>
              Active Filters:
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {activeFilters.query && (
                <FilterChip label={`Query: ${activeFilters.query}`} onDelete={() => {}} />
              )}
              {activeFilters.ageRange && (
                <FilterChip
                  label={`Age: ${activeFilters.ageRange[0]}-${activeFilters.ageRange[1]}`}
                  onDelete={() => {}}
                />
              )}
              {activeFilters.education?.map((edu) => (
                <FilterChip key={edu} label={`Education: ${edu}`} onDelete={() => {}} />
              ))}
              {activeFilters.occupation?.map((occ) => (
                <FilterChip key={occ} label={`Occupation: ${occ}`} onDelete={() => {}} />
              ))}
              {activeFilters.location?.map((loc) => (
                <FilterChip key={loc} label={`Location: ${loc}`} onDelete={() => {}} />
              ))}
            </Box>
          </CardContent>
        </SearchCard>
      )}

      {/* Saved Searches */}
      {isPremium && savedSearches.length > 0 && (
        <SearchCard>
          <CardContent>
            <Typography variant="subtitle1" fontWeight="600" gutterBottom>
              Saved Searches:
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {savedSearches.map((search) => (
                <FilterChip
                  key={search.id}
                  icon={<StarIcon />}
                  label={search.name}
                  onClick={() => setActiveFilters(search.filters)}
                  className="premium"
                />
              ))}
            </Box>
          </CardContent>
        </SearchCard>
      )}
    </Box>
  );
}
