import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import dynamic from 'next/dynamic';

// Dynamic import for EnhancedAdminLayout to avoid SSR issues
const EnhancedAdminLayout = dynamic(() => import('@/components/admin/EnhancedAdminLayout'), {
  ssr: false
});

import {
  Box,
  Button,
  Card,
  CardContent,
  CardMedia,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControlLabel,
  Grid,
  IconButton,
  InputAdornment,
  Switch,
  TextField,
  Typography,
  Chip,
  Tooltip,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Alert,
  Avatar,
  Stack,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Badge
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  CloudUpload as CloudUploadIcon,
  AttachMoney as AttachMoneyIcon,
  <PERSON><PERSON>hart as BarChartIcon,
  Search as SearchIcon,
  Description as DescriptionIcon,
  TrendingUp as TrendingUpIcon,
  Download as DownloadIcon,
  People as PeopleIcon,
  FilterList as FilterListIcon,
  Preview as PreviewIcon,
  Male as MaleIcon,
  Female as FemaleIcon,
  Person as PersonIcon,
  Star as StarIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { adminGet, adminPost, adminPut, adminDelete } from '@/services/adminApiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';

export default function BiodataTemplatesManagement() {
  const router = useRouter();
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openModal, setOpenModal] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [currentTemplate, setCurrentTemplate] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    discountPercent: '',
    discountedPrice: '',
    isActive: true
  });
  const [previewImage, setPreviewImage] = useState(null);
  const [designFile, setDesignFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState('');
  const [designFileName, setDesignFileName] = useState('');
  const [templateToDelete, setTemplateToDelete] = useState(null);
  const [analyticsData, setAnalyticsData] = useState({
    totalTemplates: 0,
    totalPurchases: 0,
    totalDownloads: 0,
    totalRevenue: 0
  });
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
  const [templateToPreview, setTemplateToPreview] = useState(null);
  const [stats, setStats] = useState({});
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [filters, setFilters] = useState({
    category: '',
    targetGender: '',
    isActive: '',
    search: ''
  });

  // Category options
  const categoryOptions = [
    { value: 'Traditional', label: 'Traditional', icon: '🏛️' },
    { value: 'Modern', label: 'Modern', icon: '🎨' },
    { value: 'Premium', label: 'Premium', icon: '💎' },
    { value: 'Classic', label: 'Classic', icon: '📜' },
    { value: 'Elegant', label: 'Elegant', icon: '✨' },
    { value: 'Royal', label: 'Royal', icon: '👑' }
  ];

  // Target gender options
  const genderOptions = [
    { value: 'male', label: 'Male-Oriented', icon: <MaleIcon />, color: 'primary' },
    { value: 'female', label: 'Female-Oriented', icon: <FemaleIcon />, color: 'secondary' },
    { value: 'neutral', label: 'Gender-Neutral', icon: <PersonIcon />, color: 'default' }
  ];

  useEffect(() => {
    fetchTemplates();
    fetchStats();
  }, [page, rowsPerPage, filters]);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams();
      queryParams.append('page', page + 1);
      queryParams.append('limit', rowsPerPage);

      if (filters.search) queryParams.append('search', filters.search);
      if (filters.category) queryParams.append('category', filters.category);
      if (filters.targetGender) queryParams.append('targetGender', filters.targetGender);
      if (filters.isActive !== '') queryParams.append('isActive', filters.isActive);

      const response = await adminGet(`${ADMIN_ENDPOINTS.BIODATA_TEMPLATES}?${queryParams}`);

      if (response.success) {
        setTemplates(response.templates || []);
        setTotalCount(response.pagination?.totalTemplates || 0);
      } else {
        toast.error('Failed to fetch biodata templates');
      }
    } catch (error) {
      console.error('Error fetching biodata templates:', error);
      toast.error('Error fetching biodata templates');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await adminGet(`${ADMIN_ENDPOINTS.BIODATA_TEMPLATES}/stats/overview`);
      if (response.success) {
        setStats(response.stats || {});
        // Update analytics data for backward compatibility
        setAnalyticsData({
          totalTemplates: response.stats?.totalTemplates || 0,
          totalPurchases: response.stats?.totalPurchases || 0,
          totalDownloads: response.stats?.totalDownloads || 0,
          totalRevenue: response.stats?.totalRevenue || 0
        });
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const handleCreateTemplate = () => {
    setCurrentTemplate(null);
    setFormData({
      name: '',
      description: '',
      previewImage: '',
      designFile: '',
      price: 0,
      discountPercent: 0,
      category: 'Traditional',
      targetGender: 'male',
      isPremium: false,
      isActive: true
    });
    setDialogOpen(true);
  };

  const handleEditTemplate = (template) => {
    setCurrentTemplate(template);
    setFormData({
      name: template.name || '',
      description: template.description || '',
      previewImage: template.previewImage || '',
      designFile: template.designFile || '',
      price: template.price || 0,
      discountPercent: template.discountPercent || 0,
      category: template.category || 'Traditional',
      targetGender: template.targetGender || 'male',
      isPremium: template.isPremium || false,
      isActive: template.isActive !== undefined ? template.isActive : true
    });
    setDialogOpen(true);
  };

  const handleDeleteTemplate = (template) => {
    setTemplateToDelete(template);
    setDeleteDialogOpen(true);
  };

  const handlePreviewTemplate = (template) => {
    setTemplateToPreview(template);
    setPreviewDialogOpen(true);
  };

  const handleSaveTemplate = async () => {
    try {
      // Validate required fields
      if (!formData.name || !formData.description) {
        toast.error('Name and description are required');
        return;
      }

      const templateData = {
        ...formData,
        discountedPrice: formData.discountPercent > 0
          ? formData.price * (1 - formData.discountPercent / 100)
          : null
      };

      let response;
      if (currentTemplate) {
        response = await adminPut(`${ADMIN_ENDPOINTS.BIODATA_TEMPLATES}/${currentTemplate.id}`, templateData);
      } else {
        response = await adminPost(ADMIN_ENDPOINTS.BIODATA_TEMPLATES, templateData);
      }

      if (response.success) {
        toast.success(`Biodata template ${currentTemplate ? 'updated' : 'created'} successfully`);
        setDialogOpen(false);
        fetchTemplates();
        fetchStats();
      } else {
        toast.error(response.message || 'Failed to save biodata template');
      }
    } catch (error) {
      console.error('Error saving biodata template:', error);
      toast.error('Error saving biodata template');
    }
  };

  const confirmDelete = async () => {
    try {
      const response = await adminDelete(`${ADMIN_ENDPOINTS.BIODATA_TEMPLATES}/${templateToDelete.id}`);

      if (response.success) {
        toast.success('Biodata template deleted successfully');
        setDeleteDialogOpen(false);
        setTemplateToDelete(null);
        fetchTemplates();
        fetchStats();
      } else {
        toast.error('Failed to delete biodata template');
      }
    } catch (error) {
      console.error('Error deleting biodata template:', error);
      toast.error('Error deleting biodata template');
    }
  };

  const getGenderChip = (targetGender) => {
    const genderConfig = genderOptions.find(opt => opt.value === targetGender) || genderOptions[2];
    return (
      <Chip
        icon={genderConfig.icon}
        label={genderConfig.label}
        color={genderConfig.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const getCategoryChip = (category) => {
    const categoryConfig = categoryOptions.find(opt => opt.value === category) || categoryOptions[0];
    return (
      <Chip
        label={`${categoryConfig.icon} ${categoryConfig.label}`}
        size="small"
        variant="outlined"
        color="primary"
      />
    );
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleOpenModal = (template = null) => {
    if (template) {
      setCurrentTemplate(template);
      setFormData({
        name: template.name,
        description: template.description || '',
        price: template.price,
        discountPercent: template.discountPercent || '',
        discountedPrice: template.discountedPrice || '',
        isActive: template.isActive
      });
      setPreviewUrl(template.previewImage);
    } else {
      setCurrentTemplate(null);
      setFormData({
        name: '',
        description: '',
        price: '',
        discountPercent: '',
        discountedPrice: '',
        isActive: true
      });
      setPreviewUrl('');
      setDesignFileName('');
    }
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setPreviewImage(null);
    setDesignFile(null);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Calculate discounted price if price or discount percent changes
    if (name === 'price' || name === 'discountPercent') {
      if (formData.price && formData.discountPercent) {
        const price = parseFloat(name === 'price' ? value : formData.price);
        const discount = parseFloat(name === 'discountPercent' ? value : formData.discountPercent);
        if (!isNaN(price) && !isNaN(discount)) {
          const discountedPrice = price - (price * (discount / 100));
          setFormData(prev => ({ ...prev, discountedPrice: discountedPrice.toFixed(2) }));
        }
      }
    }
  };

  const handleCheckboxChange = (e) => {
    setFormData(prev => ({ ...prev, [e.target.name]: e.target.checked }));
  };

  const handleFileChange = (e) => {
    const { name, files } = e.target;
    if (files && files[0]) {
      if (name === 'previewImage') {
        setPreviewImage(files[0]);
        setPreviewUrl(URL.createObjectURL(files[0]));
      } else if (name === 'designFile') {
        setDesignFile(files[0]);
        setDesignFileName(files[0].name);
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      // Create FormData object for file upload
      const formDataObj = new FormData();
      formDataObj.append('name', formData.name);
      formDataObj.append('description', formData.description);
      formDataObj.append('price', formData.price);
      formDataObj.append('isActive', formData.isActive);

      if (formData.discountPercent) {
        formDataObj.append('discountPercent', formData.discountPercent);
      }

      if (previewImage) {
        formDataObj.append('previewImage', previewImage);
      }

      if (designFile) {
        formDataObj.append('designFile', designFile);
      }

      let response;

      if (currentTemplate) {
        // Update existing template
        response = await fetch(`/api/admin/biodata/templates/${currentTemplate.id}`, {
          method: 'PUT',
          body: formDataObj
        });
      } else {
        // Create new template
        response = await fetch('/api/admin/biodata/templates', {
          method: 'POST',
          body: formDataObj
        });
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.message) {
        toast.success(currentTemplate ? 'Template updated successfully' : 'Template created successfully');
        handleCloseModal();
        fetchTemplates();
      } else {
        throw new Error(data.message || 'Operation failed');
      }
    } catch (error) {
      console.error('Error saving template:', error);
      toast.error('Error: ' + error.message);
    }
  };

  const handleDeleteClick = (template) => {
    setTemplateToDelete(template);
    setOpenDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      const response = await fetch(`/api/admin/biodata/templates/${templateToDelete.id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.message) {
        toast.success('Template deleted successfully');
        setOpenDeleteModal(false);
        fetchTemplates();
      } else {
        throw new Error(data.message || 'Failed to delete template');
      }
    } catch (error) {
      console.error('Error deleting template:', error);
      toast.error('Error: ' + error.message);
    }
  };

  const handleToggleStatus = async (templateId, currentStatus) => {
    try {
      const formDataObj = new FormData();
      formDataObj.append('isActive', !currentStatus);

      const response = await fetch(`/api/admin/biodata/templates/${templateId}`, {
        method: 'PUT',
        body: formDataObj
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.message) {
        toast.success(`Template ${currentStatus ? 'deactivated' : 'activated'} successfully`);
        fetchTemplates();
      } else {
        throw new Error(data.message || 'Failed to update template status');
      }
    } catch (error) {
      console.error('Error toggling template status:', error);
      toast.error('Error: ' + error.message);
    }
  };



  return (
    <EnhancedAdminLayout title="Biodata Templates">
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Biodata Templates Management
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleCreateTemplate}
          >
            Create New Template
          </Button>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Total Templates
                    </Typography>
                    <Typography variant="h4" component="div">
                      {analyticsData.totalTemplates}
                    </Typography>
                  </Box>
                  <DescriptionIcon color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Total Purchases
                    </Typography>
                    <Typography variant="h4" component="div">
                      {analyticsData.totalPurchases}
                    </Typography>
                  </Box>
                  <PeopleIcon color="success" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Total Downloads
                    </Typography>
                    <Typography variant="h4" component="div">
                      {analyticsData.totalDownloads}
                    </Typography>
                  </Box>
                  <DownloadIcon color="info" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Total Revenue
                    </Typography>
                    <Typography variant="h4" component="div">
                      {formatCurrency(analyticsData.totalRevenue)}
                    </Typography>
                  </Box>
                  <AttachMoneyIcon color="warning" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Filters and Search */}
        <Paper sx={{ mb: 3, p: 2 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                label="Search Templates"
                variant="outlined"
                size="small"
                fullWidth
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Category</InputLabel>
                <Select
                  value={filters.category}
                  label="Category"
                  onChange={(e) => setFilters({ ...filters, category: e.target.value })}
                >
                  <MenuItem value="">All Categories</MenuItem>
                  {categoryOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.icon} {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Target Gender</InputLabel>
                <Select
                  value={filters.targetGender}
                  label="Target Gender"
                  onChange={(e) => setFilters({ ...filters, targetGender: e.target.value })}
                >
                  <MenuItem value="">All Genders</MenuItem>
                  {genderOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.icon} {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.isActive}
                  label="Status"
                  onChange={(e) => setFilters({ ...filters, isActive: e.target.value })}
                >
                  <MenuItem value="">All Status</MenuItem>
                  <MenuItem value="true">Active</MenuItem>
                  <MenuItem value="false">Inactive</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                variant="outlined"
                fullWidth
                onClick={() => setFilters({ category: '', targetGender: '', isActive: '', search: '' })}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Grid container spacing={3}>
            {templates.map(template => (
              <Grid item xs={12} sm={6} md={4} key={template.id}>
                <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <Box sx={{ position: 'relative' }}>
                    <CardMedia
                      component="img"
                      height="200"
                      image={template.previewImage || template.thumbnail || '/img/template-placeholder.jpg'}
                      alt={template.name}
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = '/img/template-placeholder.jpg';
                      }}
                      sx={{ objectFit: 'cover' }}
                    />
                    <Box sx={{
                      position: 'absolute',
                      top: 10,
                      right: 10,
                      bgcolor: template.isActive ? 'success.main' : 'error.main',
                      color: 'white',
                      borderRadius: 1,
                      px: 1,
                      py: 0.5
                    }}>
                      {template.isActive ? 'Active' : 'Inactive'}
                    </Box>
                  </Box>
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Typography gutterBottom variant="h5" component="h2">
                      {template.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {template.description || 'No description provided'}
                    </Typography>

                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      {template.isPremium ? (
                        <Typography variant="body1" fontWeight="bold">
                          ₹{template.price || 0}
                        </Typography>
                      ) : (
                        <Typography variant="body1" fontWeight="bold" color="success.main">
                          Free
                        </Typography>
                      )}
                      {template.discountedPrice && (
                        <>
                          <Typography variant="body2" color="text.secondary" sx={{ textDecoration: 'line-through', ml: 1, mr: 1 }}>
                            ₹{template.price}
                          </Typography>
                          <Typography variant="body1" color="primary" fontWeight="bold">
                            ₹{template.discountedPrice}
                          </Typography>
                          {template.discountPercent && (
                            <Chip
                              label={`-${template.discountPercent}%`}
                              size="small"
                              color="secondary"
                              sx={{ ml: 1 }}
                            />
                          )}
                        </>
                      )}
                    </Box>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                      <Box>
                        {template.purchaseCount !== undefined && (
                          <Tooltip title="Purchases">
                            <Chip
                              icon={<AttachMoneyIcon />}
                              label={template.purchaseCount || 0}
                              size="small"
                              sx={{ mr: 1 }}
                            />
                          </Tooltip>
                        )}
                        <Tooltip title="Downloads">
                          <Chip
                            icon={<CloudUploadIcon />}
                            label={template.downloadCount || 0}
                            size="small"
                          />
                        </Tooltip>
                      </Box>
                      {template.revenue !== undefined && (
                        <Typography variant="body2" color="text.secondary">
                          Revenue: ₹{template.revenue || 0}
                        </Typography>
                      )}
                    </Box>
                  </CardContent>
                  <Divider />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                    <Box>
                      <Tooltip title="Preview">
                        <IconButton onClick={() => handlePreviewTemplate(template)}>
                          <VisibilityIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit">
                        <IconButton onClick={() => handleOpenModal(template)}>
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete">
                        <IconButton onClick={() => handleDeleteClick(template)}>
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={template.isActive}
                          onChange={() => handleToggleStatus(template.id, template.isActive)}
                          color="primary"
                        />
                      }
                      label=""
                    />
                  </Box>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Box>

      {/* Add/Edit Template Modal */}
      <Dialog open={openModal} onClose={handleCloseModal} maxWidth="md" fullWidth>
        <DialogTitle>{currentTemplate ? 'Edit Template' : 'Add New Template'}</DialogTitle>
        <form onSubmit={handleSubmit}>
          <DialogContent>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="name"
                  label="Template Name"
                  value={formData.name}
                  onChange={handleChange}
                  fullWidth
                  required
                  margin="normal"
                />
                <TextField
                  name="description"
                  label="Description"
                  value={formData.description}
                  onChange={handleChange}
                  fullWidth
                  multiline
                  rows={3}
                  margin="normal"
                />
                <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                  <TextField
                    name="price"
                    label="Regular Price (₹)"
                    type="number"
                    value={formData.price}
                    onChange={handleChange}
                    fullWidth
                    required
                    margin="normal"
                    InputProps={{
                      startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                    }}
                  />
                  <TextField
                    name="discountPercent"
                    label="Discount (%)"
                    type="number"
                    value={formData.discountPercent}
                    onChange={handleChange}
                    fullWidth
                    margin="normal"
                    InputProps={{
                      endAdornment: <InputAdornment position="end">%</InputAdornment>,
                    }}
                  />
                </Box>
                {formData.discountedPrice && (
                  <TextField
                    name="discountedPrice"
                    label="Discounted Price (₹)"
                    value={formData.discountedPrice}
                    fullWidth
                    margin="normal"
                    InputProps={{
                      startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                      readOnly: true,
                    }}
                  />
                )}
                <FormControlLabel
                  control={
                    <Switch
                      name="isActive"
                      checked={formData.isActive}
                      onChange={handleCheckboxChange}
                      color="primary"
                    />
                  }
                  label="Active"
                  sx={{ mt: 2 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" gutterBottom>
                  Preview Image
                </Typography>
                <Box
                  sx={{
                    border: '1px dashed grey',
                    borderRadius: 1,
                    p: 1,
                    mb: 2,
                    height: 200,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                >
                  {previewUrl ? (
                    <img
                      src={previewUrl}
                      alt="Preview"
                      style={{ maxWidth: '100%', maxHeight: '100%', objectFit: 'contain' }}
                    />
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No preview image selected
                    </Typography>
                  )}
                </Box>
                <Button
                  variant="outlined"
                  component="label"
                  fullWidth
                  startIcon={<CloudUploadIcon />}
                >
                  Upload Preview Image
                  <input
                    type="file"
                    name="previewImage"
                    onChange={handleFileChange}
                    accept="image/*"
                    hidden
                  />
                </Button>

                <Typography variant="subtitle1" gutterBottom sx={{ mt: 3 }}>
                  Template Design File
                </Typography>
                <Box
                  sx={{
                    border: '1px dashed grey',
                    borderRadius: 1,
                    p: 2,
                    mb: 2,
                    minHeight: 60,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  {designFileName || (currentTemplate && currentTemplate.designFile) ? (
                    <Typography variant="body2">
                      {designFileName || currentTemplate.designFile.split('/').pop()}
                    </Typography>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No design file selected
                    </Typography>
                  )}
                </Box>
                <Button
                  variant="outlined"
                  component="label"
                  fullWidth
                  startIcon={<CloudUploadIcon />}
                >
                  Upload Design File
                  <input
                    type="file"
                    name="designFile"
                    onChange={handleFileChange}
                    accept=".html,.css,.zip"
                    hidden
                  />
                </Button>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSaveTemplate} variant="contained" color="primary">
              {currentTemplate ? 'Update' : 'Create'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      {/* Preview Dialog */}
      <Dialog open={previewDialogOpen} onClose={() => setPreviewDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Template Preview: {templateToPreview?.name}
        </DialogTitle>
        <DialogContent>
          {templateToPreview && (
            <Box sx={{ textAlign: 'center' }}>
              <img
                src={templateToPreview.previewImage || '/img/template-placeholder.jpg'}
                alt={templateToPreview.name}
                style={{ maxWidth: '100%', height: 'auto', borderRadius: '8px' }}
              />
              <Typography variant="body1" sx={{ mt: 2 }}>
                {templateToPreview.description}
              </Typography>
              <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center', gap: 2 }}>
                {getCategoryChip(templateToPreview.category)}
                {getGenderChip(templateToPreview.targetGender)}
                <Chip
                  label={templateToPreview.isActive ? 'Active' : 'Inactive'}
                  color={templateToPreview.isActive ? 'success' : 'default'}
                  size="small"
                />
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialogOpen(false)}>Close</Button>
          <Button onClick={() => handleEditTemplate(templateToPreview)} variant="contained">
            Edit Template
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the template "{templateToDelete?.name}"? This action cannot be undone.
            {templateToDelete?.purchaseCount > 0 && (
              <Typography color="error" sx={{ mt: 2 }}>
                Warning: This template has been purchased by {templateToDelete.purchaseCount} users.
                Deleting it will make it unavailable for them.
              </Typography>
            )}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </EnhancedAdminLayout>
  );
}
