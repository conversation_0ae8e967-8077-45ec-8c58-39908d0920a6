// src/controllers/admin.mock.controller.js

/**
 * This controller provides mock implementations for admin endpoints that
 * may not have a fully implemented database schema yet.
 * It returns realistic mock data that matches the expected API response format.
 */

// Mock data for verification queue
const mockVerificationUsers = [
    {
        id: "v1",
        email: "<EMAIL>",
        phone: "9876543210",
        profileStatus: "PENDING_APPROVAL",
        createdAt: "2023-06-15T10:30:00Z",
        updatedAt: "2023-06-15T14:45:00Z",
        verificationDocuments: [
            {
                id: "doc1",
                type: "Aadhar Card",
                url: "https://placehold.co/600x400/e91e63/ffffff?text=A<PERSON>har+Card",
                uploadedAt: "2023-06-15T11:30:00Z",
                status: "PENDING_REVIEW"
            },
            {
                id: "doc2",
                type: "PAN Card",
                url: "https://placehold.co/600x400/3f51b5/ffffff?text=PAN+Card",
                uploadedAt: "2023-06-15T11:35:00Z",
                status: "PENDING_REVIEW"
            }
        ],
        profile: {
            fullName: "<PERSON><PERSON>",
            gender: "Male",
            dateOfBirth: "1995-05-15",
            city: "Mumbai",
            state: "Maharashtra",
            caste: "Maratha",
            subCaste: "Kunbi",
            gotra: "Kashyap",
            religion: "Hindu"
        },
        photos: [
            {
                id: "photo1",
                url: "https://placehold.co/400x400/e91e63/ffffff?text=RP"
            }
        ]
    },
    {
        id: "v2",
        email: "<EMAIL>",
        phone: "9876543211",
        profileStatus: "PENDING_APPROVAL",
        createdAt: "2023-06-14T09:20:00Z",
        updatedAt: "2023-06-14T11:30:00Z",
        verificationDocuments: [
            {
                id: "doc3",
                type: "Aadhar Card",
                url: "https://placehold.co/600x400/009688/ffffff?text=Aadhar+Card",
                uploadedAt: "2023-06-14T10:15:00Z",
                status: "PENDING_REVIEW"
            },
            {
                id: "doc4",
                type: "Voter ID",
                url: "https://placehold.co/600x400/ff9800/ffffff?text=Voter+ID",
                uploadedAt: "2023-06-14T10:20:00Z",
                status: "PENDING_REVIEW"
            }
        ],
        profile: {
            fullName: "Priya Sharma",
            gender: "Female",
            dateOfBirth: "1997-08-22",
            city: "Pune",
            state: "Maharashtra",
            caste: "Maratha",
            subCaste: "Deshmukh",
            gotra: "Bharadwaj",
            religion: "Hindu"
        },
        photos: [
            {
                id: "photo2",
                url: "https://placehold.co/400x400/009688/ffffff?text=PS"
            }
        ]
    },
    {
        id: "v3",
        email: "<EMAIL>",
        phone: "9876543212",
        profileStatus: "PENDING_APPROVAL",
        createdAt: "2023-06-13T14:45:00Z",
        updatedAt: "2023-06-13T16:20:00Z",
        profile: {
            fullName: "Amit Desai",
            gender: "Male",
            dateOfBirth: "1993-03-10",
            city: "Nagpur",
            state: "Maharashtra",
            caste: "Maratha",
            subCaste: "Jadhav",
            gotra: "Vishwamitra",
            religion: "Hindu"
        },
        photos: [
            {
                id: "photo3",
                url: "https://placehold.co/400x400/4caf50/ffffff?text=AD"
            }
        ]
    }
];

// Mock data for reported profiles
const mockReportedProfiles = [
    {
        id: "r1",
        reportedUser: {
            id: "u1",
            name: "Fake User",
            email: "<EMAIL>",
            profilePicture: "https://placehold.co/400x400/f44336/ffffff?text=FU"
        },
        reportedBy: {
            id: "u2",
            name: "Genuine User",
            email: "<EMAIL>"
        },
        reason: "Fake Profile",
        status: "PENDING",
        reportCount: 3,
        reportDate: "2023-06-20T08:30:00Z",
        additionalNotes: "This profile is using fake photos and information."
    },
    {
        id: "r2",
        reportedUser: {
            id: "u3",
            name: "Inappropriate User",
            email: "<EMAIL>",
            profilePicture: "https://placehold.co/400x400/9c27b0/ffffff?text=IU"
        },
        reportedBy: {
            id: "u4",
            name: "Concerned User",
            email: "<EMAIL>"
        },
        reason: "Inappropriate Content",
        status: "PENDING",
        reportCount: 2,
        reportDate: "2023-06-19T14:15:00Z",
        additionalNotes: "This profile has inappropriate content in the bio."
    },
    {
        id: "r3",
        reportedUser: {
            id: "u5",
            name: "Harassing User",
            email: "<EMAIL>",
            profilePicture: "https://placehold.co/400x400/2196f3/ffffff?text=HU"
        },
        reportedBy: {
            id: "u6",
            name: "Victim User",
            email: "<EMAIL>"
        },
        reason: "Harassment",
        status: "PENDING",
        reportCount: 1,
        reportDate: "2023-06-18T11:45:00Z",
        additionalNotes: "This user is sending harassing messages."
    }
];

/**
 * @description Get users pending verification with pagination, sorting, and filtering
 * @route GET /api/admin/users/verification-queue
 */
exports.getVerificationQueue = async (req, res, next) => {
    try {
        console.log("Mock verification queue endpoint called");
        
        const { page = 1, limit = 10, search = '', sortBy = 'createdAt', order = 'desc' } = req.query;
        const pageNum = parseInt(page, 10);
        const limitNum = parseInt(limit, 10);
        const skip = (pageNum - 1) * limitNum;
        
        // Filter users based on search query
        let filteredUsers = [...mockVerificationUsers];
        if (search) {
            const searchLower = search.toLowerCase();
            filteredUsers = filteredUsers.filter(user => 
                user.email.toLowerCase().includes(searchLower) ||
                user.phone.includes(search) ||
                user.profile?.fullName.toLowerCase().includes(searchLower) ||
                user.profile?.city?.toLowerCase().includes(searchLower)
            );
        }
        
        // Sort users
        filteredUsers.sort((a, b) => {
            let valueA, valueB;
            
            if (sortBy === 'fullName') {
                valueA = a.profile?.fullName || '';
                valueB = b.profile?.fullName || '';
            } else if (sortBy === 'city') {
                valueA = a.profile?.city || '';
                valueB = b.profile?.city || '';
            } else {
                valueA = a[sortBy] || '';
                valueB = b[sortBy] || '';
            }
            
            if (order === 'asc') {
                return valueA.localeCompare(valueB);
            } else {
                return valueB.localeCompare(valueA);
            }
        });
        
        // Paginate users
        const paginatedUsers = filteredUsers.slice(skip, skip + limitNum);
        const totalUsers = filteredUsers.length;
        const totalPages = Math.ceil(totalUsers / limitNum);
        
        res.status(200).json({
            message: "Verification queue fetched successfully.",
            users: paginatedUsers,
            pagination: {
                currentPage: pageNum,
                limit: limitNum,
                totalPages: totalPages,
                totalPending: totalUsers
            }
        });
    } catch (error) {
        console.error("Error in mock verification queue:", error);
        next(error);
    }
};

/**
 * @description Get reported profiles with pagination and filters
 * @route GET /api/admin/users/reported
 */
exports.getReportedProfiles = async (req, res, next) => {
    try {
        console.log("Mock reported profiles endpoint called");
        
        const { page = 1, limit = 10, status, search } = req.query;
        const pageNum = parseInt(page, 10);
        const limitNum = parseInt(limit, 10);
        const skip = (pageNum - 1) * limitNum;
        
        // Filter reports based on status and search query
        let filteredReports = [...mockReportedProfiles];
        
        if (status) {
            filteredReports = filteredReports.filter(report => report.status === status);
        }
        
        if (search) {
            const searchLower = search.toLowerCase();
            filteredReports = filteredReports.filter(report => 
                report.reason.toLowerCase().includes(searchLower) ||
                report.reportedUser.name.toLowerCase().includes(searchLower) ||
                report.reportedBy.name.toLowerCase().includes(searchLower)
            );
        }
        
        // Paginate reports
        const paginatedReports = filteredReports.slice(skip, skip + limitNum);
        const totalReports = filteredReports.length;
        const totalPages = Math.ceil(totalReports / limitNum);
        
        res.status(200).json({
            reports: paginatedReports,
            total: totalReports,
            totalPages: totalPages,
            currentPage: pageNum
        });
    } catch (error) {
        console.error("Error in mock reported profiles:", error);
        next(error);
    }
};

/**
 * @description Get detailed information about a specific report
 * @route GET /api/admin/users/reported/:id
 */
exports.getReportDetails = async (req, res, next) => {
    try {
        console.log("Mock report details endpoint called");
        
        const { id } = req.params;
        const report = mockReportedProfiles.find(r => r.id === id);
        
        if (!report) {
            return res.status(404).json({ message: 'Report not found.' });
        }
        
        res.status(200).json(report);
    } catch (error) {
        console.error("Error in mock report details:", error);
        next(error);
    }
};

/**
 * @description Update report status
 * @route PUT /api/admin/users/reported/:id/status
 */
exports.updateReportStatus = async (req, res, next) => {
    try {
        console.log("Mock update report status endpoint called");
        
        const { id } = req.params;
        const { status } = req.body;
        
        const report = mockReportedProfiles.find(r => r.id === id);
        
        if (!report) {
            return res.status(404).json({ message: 'Report not found.' });
        }
        
        if (!['PENDING', 'REVIEWED', 'DISMISSED', 'ACTIONED'].includes(status)) {
            return res.status(400).json({ message: 'Invalid status. Must be one of: PENDING, REVIEWED, DISMISSED, ACTIONED' });
        }
        
        // Update the report status in the mock data
        report.status = status;
        
        res.status(200).json({
            message: 'Report status updated successfully.',
            report: {
                id: report.id,
                status: report.status
            }
        });
    } catch (error) {
        console.error("Error in mock update report status:", error);
        next(error);
    }
};

/**
 * @description Export reported profiles as CSV
 * @route GET /api/admin/users/reported/export/csv
 */
exports.exportReportedProfilesCsv = async (req, res, next) => {
    try {
        console.log("Mock export reported profiles as CSV endpoint called");
        
        const { status } = req.query;
        
        // Filter reports based on status
        let filteredReports = [...mockReportedProfiles];
        if (status) {
            filteredReports = filteredReports.filter(report => report.status === status);
        }
        
        // Create CSV header
        let csv = 'ID,Reported User,Reported User Email,Reported By,Reason,Status,Report Date\n';
        
        // Add report data to CSV
        filteredReports.forEach(report => {
            const reportedUserName = report.reportedUser?.name || 'Unknown';
            const reportedUserEmail = report.reportedUser?.email || 'No email';
            const reportedByName = report.reportedBy?.name || 'Unknown';
            const reason = report.reason || 'Not specified';
            const status = report.status || 'PENDING';
            const reportDate = new Date(report.reportDate).toLocaleDateString();
            
            // Escape CSV values
            const escapeCsv = (value) => {
                if (typeof value !== 'string') return value;
                if (value.includes(',') || value.includes('"') || value.includes('\n')) {
                    return `"${value.replace(/"/g, '""')}"`;
                }
                return value;
            };
            
            csv += `${report.id},${escapeCsv(reportedUserName)},${escapeCsv(reportedUserEmail)},${escapeCsv(reportedByName)},${escapeCsv(reason)},${status},${reportDate}\n`;
        });
        
        // Set headers for CSV download
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename=reported-profiles.csv');
        
        // Send CSV data
        res.send(csv);
    } catch (error) {
        console.error("Error in mock export reported profiles as CSV:", error);
        next(error);
    }
};

/**
 * @description Export reported profiles as Excel
 * @route GET /api/admin/users/reported/export/xlsx
 */
exports.exportReportedProfilesXlsx = async (req, res, next) => {
    // Redirect to CSV export since we don't have Excel generation
    res.redirect(`/api/admin/users/reported/export/csv${req.query.status ? '?status=' + req.query.status : ''}`);
};
