/**
 * API Enhancement Script
 * 
 * This script runs all the enhancement scripts to standardize the API.
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const config = {
  scriptsDir: __dirname,
  backupDir: path.join(__dirname, '../backups', `api-enhancement-${new Date().toISOString().replace(/:/g, '-')}`)
};

// Create backup directory
if (!fs.existsSync(config.backupDir)) {
  fs.mkdirSync(config.backupDir, { recursive: true });
}

/**
 * Run a script
 * @param {string} scriptName - Name of the script
 */
function runScript(scriptName) {
  console.log(`\n=== Running ${scriptName} ===\n`);
  
  try {
    execSync(`node ${path.join(config.scriptsDir, scriptName)}`, { stdio: 'inherit' });
    console.log(`\n=== ${scriptName} completed successfully ===\n`);
  } catch (error) {
    console.error(`\n=== Error running ${scriptName} ===\n`);
    console.error(error.message);
    process.exit(1);
  }
}

/**
 * Main function
 */
function main() {
  console.log('Starting API enhancement process...');
  
  // Update routes to follow new standards
  runScript('update-routes.js');
  
  // Optimize caching for frequently accessed data
  runScript('optimize-caching.js');
  
  // Set up performance monitoring
  runScript('setup-performance-monitoring.js');
  
  // Enhance API documentation
  runScript('enhance-documentation.js');
  
  // Generate tests for all endpoints
  runScript('generate-tests.js');
  
  // Generate OpenAPI documentation
  runScript('generate-docs.js');
  
  console.log('\nAPI enhancement process completed successfully!');
  console.log('\nNext steps:');
  console.log('1. Review the changes made to the codebase');
  console.log('2. Run the tests to ensure everything is working correctly');
  console.log('3. Start the server and test the API endpoints');
  console.log('4. Check the API documentation at /api-docs');
  console.log('5. Monitor API performance at /performance-dashboard');
}

// Run main function
main();
