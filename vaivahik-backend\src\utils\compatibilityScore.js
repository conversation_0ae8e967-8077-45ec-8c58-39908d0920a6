/**
 * Compatibility Score Utility
 * 
 * This utility calculates compatibility scores between two profiles based on
 * various factors including preferences, astrology, and lifestyle compatibility.
 */

const logger = require('./logger');

/**
 * Calculate preference match score
 * @param {Object} userProfile - User profile
 * @param {Object} candidateProfile - Candidate profile
 * @param {Object} userPreferences - User preferences
 * @returns {Object} Preference match scores
 */
const calculatePreferenceMatch = (userProfile, candidateProfile, userPreferences) => {
    try {
        const scores = {
            age: 0,
            height: 0,
            education: 0,
            occupation: 0,
            location: 0,
            religion: 0,
            caste: 0,
            income: 0,
            total: 0
        };
        
        // Age preference match
        if (userPreferences.ageFrom && userPreferences.ageTo && candidateProfile.age) {
            if (candidateProfile.age >= userPreferences.ageFrom && 
                candidateProfile.age <= userPreferences.ageTo) {
                scores.age = 100;
            } else {
                // Calculate how far outside the range
                const ageDiff = Math.min(
                    Math.abs(candidateProfile.age - userPreferences.ageFrom),
                    Math.abs(candidateProfile.age - userPreferences.ageTo)
                );
                scores.age = Math.max(0, 100 - (ageDiff * 20)); // Reduce by 20% per year outside range
            }
        }
        
        // Height preference match
        if (userPreferences.heightFrom && userPreferences.heightTo && candidateProfile.height) {
            if (candidateProfile.height >= userPreferences.heightFrom && 
                candidateProfile.height <= userPreferences.heightTo) {
                scores.height = 100;
            } else {
                // Calculate how far outside the range (in feet)
                const heightDiff = Math.min(
                    Math.abs(candidateProfile.height - userPreferences.heightFrom),
                    Math.abs(candidateProfile.height - userPreferences.heightTo)
                );
                scores.height = Math.max(0, 100 - (heightDiff * 100)); // Reduce by 10% per 0.1 feet
            }
        }
        
        // Education preference match
        if (userPreferences.education && userPreferences.education.length > 0 && candidateProfile.education) {
            if (userPreferences.education.includes(candidateProfile.education)) {
                scores.education = 100;
            } else {
                // Education level hierarchy
                const educationLevels = [
                    'HIGH_SCHOOL',
                    'DIPLOMA',
                    'BACHELORS',
                    'MASTERS',
                    'DOCTORATE',
                    'PROFESSIONAL_DEGREE'
                ];
                
                // Find minimum acceptable level
                const minAcceptableLevel = Math.min(
                    ...userPreferences.education.map(edu => educationLevels.indexOf(edu))
                );
                
                // Check if candidate has higher education than minimum
                const candidateLevel = educationLevels.indexOf(candidateProfile.education);
                if (candidateLevel >= minAcceptableLevel) {
                    scores.education = 75; // Higher education than minimum
                } else {
                    scores.education = 0; // Lower education than minimum
                }
            }
        }
        
        // Occupation preference match
        if (userPreferences.occupation && userPreferences.occupation.length > 0 && candidateProfile.occupation) {
            scores.occupation = userPreferences.occupation.includes(candidateProfile.occupation) ? 100 : 0;
        }
        
        // Location preference match
        if (userPreferences.location && candidateProfile.city) {
            if (candidateProfile.city.includes(userPreferences.location) || 
                userPreferences.location.includes(candidateProfile.city)) {
                scores.location = 100;
            } else if (candidateProfile.state && userPreferences.location.includes(candidateProfile.state)) {
                scores.location = 50; // Same state
            } else {
                scores.location = 0;
            }
        }
        
        // Religion preference match
        if (userPreferences.religion && candidateProfile.religion) {
            scores.religion = userPreferences.religion === 'ANY' || 
                              userPreferences.religion === candidateProfile.religion ? 100 : 0;
        }
        
        // Caste preference match
        if (userPreferences.caste && candidateProfile.caste) {
            scores.caste = userPreferences.caste === 'ANY' || 
                           userPreferences.caste === candidateProfile.caste ? 100 : 0;
        }
        
        // Income preference match
        if (userPreferences.incomeRange && candidateProfile.incomeRange) {
            // Income range hierarchy
            const incomeRanges = [
                'BELOW_5_LAKHS',
                '5_10_LAKHS',
                '10_15_LAKHS',
                '15_20_LAKHS',
                '20_30_LAKHS',
                'ABOVE_30_LAKHS'
            ];
            
            const preferredIndex = incomeRanges.indexOf(userPreferences.incomeRange);
            const candidateIndex = incomeRanges.indexOf(candidateProfile.incomeRange);
            
            if (userPreferences.incomeRange === 'ANY' || userPreferences.incomeRange === candidateProfile.incomeRange) {
                scores.income = 100;
            } else if (candidateIndex > preferredIndex) {
                scores.income = 100; // Higher income than preferred
            } else {
                // Lower income than preferred
                const diff = preferredIndex - candidateIndex;
                scores.income = Math.max(0, 100 - (diff * 25)); // Reduce by 25% per income level
            }
        }
        
        // Calculate total score (average of all scores)
        const validScores = Object.entries(scores)
            .filter(([key, value]) => key !== 'total' && value !== 0)
            .map(([key, value]) => value);
        
        scores.total = validScores.length > 0 
            ? Math.round(validScores.reduce((sum, score) => sum + score, 0) / validScores.length) 
            : 0;
        
        return scores;
    } catch (error) {
        logger.error(`Error calculating preference match: ${error}`);
        return { total: 0 };
    }
};

/**
 * Calculate lifestyle compatibility score
 * @param {Object} userProfile - User profile
 * @param {Object} candidateProfile - Candidate profile
 * @returns {Object} Lifestyle compatibility scores
 */
const calculateLifestyleCompatibility = (userProfile, candidateProfile) => {
    try {
        const scores = {
            diet: 0,
            smoking: 0,
            drinking: 0,
            total: 0
        };
        
        // Diet compatibility
        if (userProfile.diet && candidateProfile.diet) {
            if (userProfile.diet === candidateProfile.diet) {
                scores.diet = 100; // Exact match
            } else if (
                (userProfile.diet === 'VEGETARIAN' && candidateProfile.diet === 'VEGAN') ||
                (userProfile.diet === 'VEGAN' && candidateProfile.diet === 'VEGETARIAN')
            ) {
                scores.diet = 80; // Compatible vegetarian types
            } else if (
                (userProfile.diet === 'NON_VEGETARIAN' && 
                 (candidateProfile.diet === 'VEGETARIAN' || candidateProfile.diet === 'VEGAN'))
            ) {
                scores.diet = 40; // Non-veg with veg (potential conflict)
            } else {
                scores.diet = 60; // Other combinations
            }
        }
        
        // Smoking compatibility
        if (userProfile.smoking && candidateProfile.smoking) {
            if (userProfile.smoking === candidateProfile.smoking) {
                scores.smoking = 100; // Exact match
            } else if (
                (userProfile.smoking === 'NEVER' && candidateProfile.smoking === 'OCCASIONALLY') ||
                (userProfile.smoking === 'OCCASIONALLY' && candidateProfile.smoking === 'NEVER')
            ) {
                scores.smoking = 70; // Minor difference
            } else {
                scores.smoking = 30; // Major difference
            }
        }
        
        // Drinking compatibility
        if (userProfile.drinking && candidateProfile.drinking) {
            if (userProfile.drinking === candidateProfile.drinking) {
                scores.drinking = 100; // Exact match
            } else if (
                (userProfile.drinking === 'NEVER' && candidateProfile.drinking === 'OCCASIONALLY') ||
                (userProfile.drinking === 'OCCASIONALLY' && candidateProfile.drinking === 'NEVER')
            ) {
                scores.drinking = 70; // Minor difference
            } else {
                scores.drinking = 30; // Major difference
            }
        }
        
        // Calculate total score (average of all scores)
        const validScores = Object.entries(scores)
            .filter(([key, value]) => key !== 'total' && value !== 0)
            .map(([key, value]) => value);
        
        scores.total = validScores.length > 0 
            ? Math.round(validScores.reduce((sum, score) => sum + score, 0) / validScores.length) 
            : 0;
        
        return scores;
    } catch (error) {
        logger.error(`Error calculating lifestyle compatibility: ${error}`);
        return { total: 0 };
    }
};

/**
 * Calculate overall compatibility score
 * @param {Object} userProfile - User profile
 * @param {Object} candidateProfile - Candidate profile
 * @param {Object} userPreferences - User preferences
 * @returns {Object} Overall compatibility scores and details
 */
const calculateCompatibilityScore = (userProfile, candidateProfile, userPreferences) => {
    try {
        // Calculate individual compatibility scores
        const preferenceMatch = calculatePreferenceMatch(userProfile, candidateProfile, userPreferences);
        const lifestyleCompatibility = calculateLifestyleCompatibility(userProfile, candidateProfile);
        
        // Calculate overall score (weighted average)
        const weights = {
            preferenceMatch: 0.7,
            lifestyleCompatibility: 0.3
        };
        
        const overallScore = Math.round(
            (preferenceMatch.total * weights.preferenceMatch) +
            (lifestyleCompatibility.total * weights.lifestyleCompatibility)
        );
        
        return {
            overallScore,
            preferenceMatch,
            lifestyleCompatibility
        };
    } catch (error) {
        logger.error(`Error calculating compatibility score: ${error}`);
        return {
            overallScore: 0,
            preferenceMatch: { total: 0 },
            lifestyleCompatibility: { total: 0 }
        };
    }
};

module.exports = {
    calculatePreferenceMatch,
    calculateLifestyleCompatibility,
    calculateCompatibilityScore
};
