// src/controllers/admin/financial.controller.js

/**
 * @description Get all subscriptions with pagination, sorting, and filtering
 * @route GET /api/admin/financial/subscriptions
 */
exports.getSubscriptions = async (req, res, next) => {
    const prisma = req.prisma;
    const {
        page = 1,
        limit = 10,
        status = '',
        search = '',
        sortBy = 'createdAt',
        order = 'desc',
        planType = '',
        startDate = '',
        endDate = ''
    } = req.query;

    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    if (isNaN(pageNum) || pageNum < 1 || isNaN(limitNum) || limitNum < 1) {
        const error = new Error("Invalid page or limit parameter.");
        error.status = 400;
        return next(error);
    }

    const skip = (pageNum - 1) * limitNum;
    const take = limitNum;
    const sortOrder = order.toLowerCase() === 'asc' ? 'asc' : 'desc';

    try {
        // Check if Subscription model exists
        let subscriptionModelExists = true;
        try {
            await prisma.subscription.findFirst();
        } catch (e) {
            if (e.message.includes('does not exist in the current database')) {
                subscriptionModelExists = false;
            } else {
                throw e;
            }
        }

        if (!subscriptionModelExists) {
            return res.status(200).json({
                message: "Subscription data not yet available. Please run database migrations first.",
                subscriptions: [],
                pagination: { currentPage: 1, limit: take, totalPages: 0, totalSubscriptions: 0 }
            });
        }

        // Build where clause based on filters
        let whereClause = {};

        if (status) {
            whereClause.isActive = status.toLowerCase() === 'active';
        }

        if (planType) {
            whereClause.planType = planType;
        }

        if (search) {
            whereClause.OR = [
                {
                    user: {
                        name: {
                            contains: search,
                            mode: 'insensitive'
                        }
                    }
                },
                {
                    user: {
                        email: {
                            contains: search,
                            mode: 'insensitive'
                        }
                    }
                },
                {
                    user: {
                        phone: {
                            contains: search,
                            mode: 'insensitive'
                        }
                    }
                },
                {
                    transactionId: {
                        contains: search,
                        mode: 'insensitive'
                    }
                }
            ];
        }

        // Date range filter
        if (startDate && endDate) {
            whereClause.createdAt = {
                gte: new Date(startDate),
                lte: new Date(endDate)
            };
        } else if (startDate) {
            whereClause.createdAt = {
                gte: new Date(startDate)
            };
        } else if (endDate) {
            whereClause.createdAt = {
                lte: new Date(endDate)
            };
        }

        // Get total count for pagination
        const totalSubscriptions = await prisma.subscription.count({
            where: whereClause
        });

        // Get subscriptions with pagination, sorting, and filtering
        const subscriptions = await prisma.subscription.findMany({
            where: whereClause,
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        phone: true
                    }
                }
            },
            orderBy: {
                [sortBy]: sortOrder
            },
            skip,
            take
        });

        const totalPages = Math.ceil(totalSubscriptions / take);

        res.status(200).json({
            message: "Subscriptions fetched successfully.",
            subscriptions,
            pagination: {
                currentPage: pageNum,
                limit: take,
                totalPages,
                totalSubscriptions
            }
        });
    } catch (error) {
        console.error("Error fetching subscriptions:", error);
        next(error);
    }
};

/**
 * @description Get subscription details by ID
 * @route GET /api/admin/financial/subscriptions/:id
 */
exports.getSubscriptionById = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;

    try {
        const subscription = await prisma.subscription.findUnique({
            where: { id },
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        phone: true,
                        profileStatus: true
                    }
                }
            }
        });

        if (!subscription) {
            return res.status(404).json({
                message: "Subscription not found."
            });
        }

        res.status(200).json({
            message: "Subscription fetched successfully.",
            subscription
        });
    } catch (error) {
        console.error("Error fetching subscription:", error);
        next(error);
    }
};

/**
 * @description Update subscription details
 * @route PUT /api/admin/financial/subscriptions/:id
 */
exports.updateSubscription = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;
    const { isActive, endDate, autoRenew } = req.body;

    try {
        const subscription = await prisma.subscription.findUnique({
            where: { id }
        });

        if (!subscription) {
            return res.status(404).json({
                message: "Subscription not found."
            });
        }

        const updatedSubscription = await prisma.subscription.update({
            where: { id },
            data: {
                isActive: isActive !== undefined ? isActive : subscription.isActive,
                endDate: endDate ? new Date(endDate) : subscription.endDate,
                autoRenew: autoRenew !== undefined ? autoRenew : subscription.autoRenew,
                updatedAt: new Date()
            }
        });

        res.status(200).json({
            message: "Subscription updated successfully.",
            subscription: updatedSubscription
        });
    } catch (error) {
        console.error("Error updating subscription:", error);
        next(error);
    }
};

/**
 * @description Get all transactions with pagination, sorting, and filtering
 * @route GET /api/admin/financial/transactions
 */
exports.getTransactions = async (req, res, next) => {
    const prisma = req.prisma;
    const {
        page = 1,
        limit = 10,
        status = '',
        paymentMethod = '',
        search = '',
        sortBy = 'createdAt',
        order = 'desc',
        startDate = '',
        endDate = '',
        minAmount = '',
        maxAmount = '',
        productType = ''
    } = req.query;

    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    if (isNaN(pageNum) || pageNum < 1 || isNaN(limitNum) || limitNum < 1) {
        const error = new Error("Invalid page or limit parameter.");
        error.status = 400;
        return next(error);
    }

    const skip = (pageNum - 1) * limitNum;
    const take = limitNum;
    const sortOrder = order.toLowerCase() === 'asc' ? 'asc' : 'desc';

    try {
        // Check if Payment model exists
        let paymentModelExists = true;
        try {
            await prisma.payment.findFirst();
        } catch (e) {
            if (e.message.includes('does not exist in the current database')) {
                paymentModelExists = false;
            } else {
                throw e;
            }
        }

        if (!paymentModelExists) {
            return res.status(200).json({
                message: "Transaction data not yet available. Please run database migrations first.",
                transactions: [],
                pagination: { currentPage: 1, limit: take, totalPages: 0, totalTransactions: 0 }
            });
        }

        // Build where clause based on filters
        let whereClause = {};

        if (status) {
            whereClause.status = status;
        }

        if (paymentMethod) {
            whereClause.paymentMethod = paymentMethod;
        }

        if (productType) {
            whereClause.productType = productType;
        }

        if (search) {
            whereClause.OR = [
                {
                    orderId: {
                        contains: search,
                        mode: 'insensitive'
                    }
                },
                {
                    paymentId: {
                        contains: search,
                        mode: 'insensitive'
                    }
                },
                {
                    user: {
                        name: {
                            contains: search,
                            mode: 'insensitive'
                        }
                    }
                },
                {
                    user: {
                        email: {
                            contains: search,
                            mode: 'insensitive'
                        }
                    }
                }
            ];
        }

        // Date range filter
        if (startDate && endDate) {
            whereClause.createdAt = {
                gte: new Date(startDate),
                lte: new Date(endDate)
            };
        } else if (startDate) {
            whereClause.createdAt = {
                gte: new Date(startDate)
            };
        } else if (endDate) {
            whereClause.createdAt = {
                lte: new Date(endDate)
            };
        }

        // Amount range filter
        if (minAmount && maxAmount) {
            whereClause.amount = {
                gte: parseFloat(minAmount),
                lte: parseFloat(maxAmount)
            };
        } else if (minAmount) {
            whereClause.amount = {
                gte: parseFloat(minAmount)
            };
        } else if (maxAmount) {
            whereClause.amount = {
                lte: parseFloat(maxAmount)
            };
        }

        // Get total count for pagination
        const totalTransactions = await prisma.payment.count({
            where: whereClause
        });

        // Get transactions with pagination, sorting, and filtering
        const transactions = await prisma.payment.findMany({
            where: whereClause,
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        phone: true
                    }
                }
            },
            orderBy: {
                [sortBy]: sortOrder
            },
            skip,
            take
        });

        const totalPages = Math.ceil(totalTransactions / take);

        res.status(200).json({
            message: "Transactions fetched successfully.",
            transactions,
            pagination: {
                currentPage: pageNum,
                limit: take,
                totalPages,
                totalTransactions
            }
        });
    } catch (error) {
        console.error("Error fetching transactions:", error);
        next(error);
    }
};

/**
 * @description Get transaction details by ID
 * @route GET /api/admin/financial/transactions/:id
 */
exports.getTransactionById = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;

    try {
        const transaction = await prisma.payment.findUnique({
            where: { id },
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        phone: true,
                        profileStatus: true
                    }
                }
            }
        });

        if (!transaction) {
            return res.status(404).json({
                message: "Transaction not found."
            });
        }

        res.status(200).json({
            message: "Transaction fetched successfully.",
            transaction
        });
    } catch (error) {
        console.error("Error fetching transaction:", error);
        next(error);
    }
};

/**
 * @description Update transaction status
 * @route PUT /api/admin/financial/transactions/:id
 */
exports.updateTransaction = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;
    const { status, notes } = req.body;

    try {
        const transaction = await prisma.payment.findUnique({
            where: { id }
        });

        if (!transaction) {
            return res.status(404).json({
                message: "Transaction not found."
            });
        }

        const updatedTransaction = await prisma.payment.update({
            where: { id },
            data: {
                status: status || transaction.status,
                notes: notes || transaction.notes,
                updatedAt: new Date()
            }
        });

        res.status(200).json({
            message: "Transaction updated successfully.",
            transaction: updatedTransaction
        });
    } catch (error) {
        console.error("Error updating transaction:", error);
        next(error);
    }
};

/**
 * @description Process a refund for a transaction
 * @route POST /api/admin/financial/transactions/:id/refund
 */
exports.processRefund = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;
    const { amount, reason } = req.body;
    const adminId = req.admin.id;

    try {
        const transaction = await prisma.payment.findUnique({
            where: { id },
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true
                    }
                }
            }
        });

        if (!transaction) {
            return res.status(404).json({
                message: "Transaction not found."
            });
        }

        if (transaction.status !== 'COMPLETED' && transaction.status !== 'SUCCESS') {
            return res.status(400).json({
                message: "Only completed transactions can be refunded."
            });
        }

        // In a real implementation, you would call your payment gateway's API to process the refund
        // For now, we'll just create a refund record

        const refundAmount = amount || transaction.amount;

        // Create refund record
        const refund = await prisma.refund.create({
            data: {
                paymentId: transaction.id,
                userId: transaction.userId,
                adminId,
                amount: refundAmount,
                reason: reason || 'Admin initiated refund',
                status: 'COMPLETED',
                refundDate: new Date()
            }
        });

        // Update original transaction
        const updatedTransaction = await prisma.payment.update({
            where: { id },
            data: {
                status: 'REFUNDED',
                notes: `Refunded: ${reason || 'Admin initiated refund'}`,
                updatedAt: new Date()
            }
        });

        // In a real implementation, you would also send an email notification to the user

        res.status(200).json({
            message: "Refund processed successfully.",
            refund,
            transaction: updatedTransaction
        });
    } catch (error) {
        console.error("Error processing refund:", error);
        next(error);
    }
};

/**
 * @description Get revenue reports with various aggregations
 * @route GET /api/admin/financial/reports/revenue
 */
exports.getRevenueReports = async (req, res, next) => {
    const prisma = req.prisma;
    const {
        period = 'monthly',
        startDate = '',
        endDate = ''
    } = req.query;

    try {
        // Check if Payment model exists
        let paymentModelExists = true;
        try {
            await prisma.payment.findFirst();
        } catch (e) {
            if (e.message.includes('does not exist in the current database')) {
                paymentModelExists = false;
            } else {
                throw e;
            }
        }

        if (!paymentModelExists) {
            return res.status(200).json({
                message: "Revenue data not yet available. Please run database migrations first.",
                revenue: {
                    total: 0,
                    byPeriod: [],
                    byProductType: [],
                    byPaymentMethod: []
                }
            });
        }

        // Build date range filter
        let dateFilter = {};
        if (startDate && endDate) {
            dateFilter = {
                createdAt: {
                    gte: new Date(startDate),
                    lte: new Date(endDate)
                }
            };
        } else {
            // Default to last 12 months if no date range specified
            const endDate = new Date();
            const startDate = new Date();
            startDate.setMonth(startDate.getMonth() - 12);

            dateFilter = {
                createdAt: {
                    gte: startDate,
                    lte: endDate
                }
            };
        }

        // Only include successful payments
        const statusFilter = {
            status: {
                in: ['COMPLETED', 'SUCCESS']
            }
        };

        const whereClause = {
            ...dateFilter,
            ...statusFilter
        };

        // Get total revenue
        const totalRevenueResult = await prisma.payment.aggregate({
            where: whereClause,
            _sum: { amount: true },
            _count: { id: true }
        });

        const totalRevenue = totalRevenueResult._sum.amount || 0;
        const totalTransactions = totalRevenueResult._count.id || 0;

        // Get revenue by product type
        const revenueByProductType = await prisma.payment.groupBy({
            by: ['productType'],
            where: whereClause,
            _sum: { amount: true },
            _count: { id: true }
        });

        // Get revenue by payment method
        const revenueByPaymentMethod = await prisma.payment.groupBy({
            by: ['paymentMethod'],
            where: whereClause,
            _sum: { amount: true },
            _count: { id: true }
        });

        // Get all payments for period-based grouping
        const payments = await prisma.payment.findMany({
            where: whereClause,
            select: {
                id: true,
                amount: true,
                createdAt: true,
                productType: true,
                paymentMethod: true
            },
            orderBy: {
                createdAt: 'asc'
            }
        });

        // Group by period (daily, weekly, monthly, yearly)
        let revenueByPeriod = [];

        if (payments.length > 0) {
            const groupedByPeriod = {};

            payments.forEach(payment => {
                const date = new Date(payment.createdAt);
                let periodKey;

                switch (period) {
                    case 'daily':
                        periodKey = date.toISOString().split('T')[0]; // YYYY-MM-DD
                        break;
                    case 'weekly':
                        // Get the first day of the week (Sunday)
                        const firstDayOfWeek = new Date(date);
                        const dayOfWeek = date.getDay();
                        firstDayOfWeek.setDate(date.getDate() - dayOfWeek);
                        periodKey = firstDayOfWeek.toISOString().split('T')[0];
                        break;
                    case 'yearly':
                        periodKey = date.getFullYear().toString();
                        break;
                    case 'monthly':
                    default:
                        periodKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
                        break;
                }

                if (!groupedByPeriod[periodKey]) {
                    groupedByPeriod[periodKey] = {
                        period: periodKey,
                        revenue: 0,
                        count: 0
                    };
                }

                groupedByPeriod[periodKey].revenue += payment.amount;
                groupedByPeriod[periodKey].count += 1;
            });

            // Convert to array and sort
            revenueByPeriod = Object.values(groupedByPeriod).sort((a, b) => a.period.localeCompare(b.period));
        }

        // Calculate growth metrics
        let growthMetrics = {
            revenueGrowth: 0,
            transactionGrowth: 0
        };

        if (revenueByPeriod.length >= 2) {
            const currentPeriodRevenue = revenueByPeriod[revenueByPeriod.length - 1].revenue;
            const previousPeriodRevenue = revenueByPeriod[revenueByPeriod.length - 2].revenue;

            const currentPeriodCount = revenueByPeriod[revenueByPeriod.length - 1].count;
            const previousPeriodCount = revenueByPeriod[revenueByPeriod.length - 2].count;

            if (previousPeriodRevenue > 0) {
                growthMetrics.revenueGrowth = ((currentPeriodRevenue - previousPeriodRevenue) / previousPeriodRevenue) * 100;
            }

            if (previousPeriodCount > 0) {
                growthMetrics.transactionGrowth = ((currentPeriodCount - previousPeriodCount) / previousPeriodCount) * 100;
            }
        }

        res.status(200).json({
            message: "Revenue reports fetched successfully.",
            revenue: {
                total: totalRevenue,
                totalTransactions,
                averageTransaction: totalTransactions > 0 ? totalRevenue / totalTransactions : 0,
                byPeriod: revenueByPeriod,
                byProductType: revenueByProductType.map(item => ({
                    productType: item.productType || 'Unknown',
                    revenue: item._sum.amount || 0,
                    count: item._count.id || 0
                })),
                byPaymentMethod: revenueByPaymentMethod.map(item => ({
                    paymentMethod: item.paymentMethod || 'Unknown',
                    revenue: item._sum.amount || 0,
                    count: item._count.id || 0
                })),
                growthMetrics
            }
        });
    } catch (error) {
        console.error("Error fetching revenue reports:", error);
        next(error);
    }
};

/**
 * @description Export transactions as CSV
 * @route GET /api/admin/financial/transactions/export/csv
 */
exports.exportTransactionsCSV = async (req, res, next) => {
    const prisma = req.prisma;
    const {
        status = '',
        paymentMethod = '',
        startDate = '',
        endDate = '',
        productType = ''
    } = req.query;

    try {
        // Build where clause based on filters
        let whereClause = {};

        if (status) {
            whereClause.status = status;
        }

        if (paymentMethod) {
            whereClause.paymentMethod = paymentMethod;
        }

        if (productType) {
            whereClause.productType = productType;
        }

        // Date range filter
        if (startDate && endDate) {
            whereClause.createdAt = {
                gte: new Date(startDate),
                lte: new Date(endDate)
            };
        } else if (startDate) {
            whereClause.createdAt = {
                gte: new Date(startDate)
            };
        } else if (endDate) {
            whereClause.createdAt = {
                lte: new Date(endDate)
            };
        }

        // Get transactions
        const transactions = await prisma.payment.findMany({
            where: whereClause,
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        phone: true
                    }
                }
            },
            orderBy: {
                createdAt: 'desc'
            }
        });

        // Format data for CSV
        const csvData = transactions.map(transaction => ({
            'Transaction ID': transaction.orderId || transaction.id,
            'Payment ID': transaction.paymentId || '',
            'User': transaction.user?.name || 'Unknown',
            'Email': transaction.user?.email || '',
            'Phone': transaction.user?.phone || '',
            'Amount': transaction.amount,
            'Currency': transaction.currency || 'INR',
            'Status': transaction.status,
            'Payment Method': transaction.paymentMethod || 'Unknown',
            'Product Type': transaction.productType || 'Unknown',
            'Created At': transaction.createdAt.toISOString(),
            'Updated At': transaction.updatedAt.toISOString()
        }));

        // Convert to CSV
        const createCsvStringifier = require('csv-writer').createObjectCsvStringifier;
        const csvStringifier = createCsvStringifier({
            header: [
                { id: 'Transaction ID', title: 'Transaction ID' },
                { id: 'Payment ID', title: 'Payment ID' },
                { id: 'User', title: 'User' },
                { id: 'Email', title: 'Email' },
                { id: 'Phone', title: 'Phone' },
                { id: 'Amount', title: 'Amount' },
                { id: 'Currency', title: 'Currency' },
                { id: 'Status', title: 'Status' },
                { id: 'Payment Method', title: 'Payment Method' },
                { id: 'Product Type', title: 'Product Type' },
                { id: 'Created At', title: 'Created At' },
                { id: 'Updated At', title: 'Updated At' }
            ]
        });

        const csvHeader = csvStringifier.getHeaderString();
        const csvBody = csvStringifier.stringifyRecords(csvData);
        const csv = csvHeader + csvBody;

        // Set headers for file download
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename=transactions-${new Date().toISOString().split('T')[0]}.csv`);

        res.status(200).send(csv);
    } catch (error) {
        console.error("Error exporting transactions:", error);
        next(error);
    }
};

/**
 * @description Export revenue report as CSV
 * @route GET /api/admin/financial/reports/revenue/export/csv
 */
exports.exportRevenueReportCSV = async (req, res, next) => {
    const prisma = req.prisma;
    const {
        period = 'monthly',
        startDate = '',
        endDate = ''
    } = req.query;

    try {
        // Build date range filter
        let dateFilter = {};
        if (startDate && endDate) {
            dateFilter = {
                createdAt: {
                    gte: new Date(startDate),
                    lte: new Date(endDate)
                }
            };
        } else {
            // Default to last 12 months if no date range specified
            const endDate = new Date();
            const startDate = new Date();
            startDate.setMonth(startDate.getMonth() - 12);

            dateFilter = {
                createdAt: {
                    gte: startDate,
                    lte: endDate
                }
            };
        }

        // Only include successful payments
        const statusFilter = {
            status: {
                in: ['COMPLETED', 'SUCCESS']
            }
        };

        const whereClause = {
            ...dateFilter,
            ...statusFilter
        };

        // Get all payments
        const payments = await prisma.payment.findMany({
            where: whereClause,
            select: {
                id: true,
                amount: true,
                createdAt: true,
                productType: true,
                paymentMethod: true,
                currency: true,
                status: true
            },
            orderBy: {
                createdAt: 'asc'
            }
        });

        // Group by period (daily, weekly, monthly, yearly)
        const groupedByPeriod = {};

        payments.forEach(payment => {
            const date = new Date(payment.createdAt);
            let periodKey;

            switch (period) {
                case 'daily':
                    periodKey = date.toISOString().split('T')[0]; // YYYY-MM-DD
                    break;
                case 'weekly':
                    // Get the first day of the week (Sunday)
                    const firstDayOfWeek = new Date(date);
                    const dayOfWeek = date.getDay();
                    firstDayOfWeek.setDate(date.getDate() - dayOfWeek);
                    periodKey = firstDayOfWeek.toISOString().split('T')[0];
                    break;
                case 'yearly':
                    periodKey = date.getFullYear().toString();
                    break;
                case 'monthly':
                default:
                    periodKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
                    break;
            }

            if (!groupedByPeriod[periodKey]) {
                groupedByPeriod[periodKey] = {
                    period: periodKey,
                    revenue: 0,
                    count: 0,
                    byProductType: {},
                    byPaymentMethod: {}
                };
            }

            groupedByPeriod[periodKey].revenue += payment.amount;
            groupedByPeriod[periodKey].count += 1;

            // Group by product type
            const productType = payment.productType || 'Unknown';
            if (!groupedByPeriod[periodKey].byProductType[productType]) {
                groupedByPeriod[periodKey].byProductType[productType] = {
                    revenue: 0,
                    count: 0
                };
            }
            groupedByPeriod[periodKey].byProductType[productType].revenue += payment.amount;
            groupedByPeriod[periodKey].byProductType[productType].count += 1;

            // Group by payment method
            const paymentMethod = payment.paymentMethod || 'Unknown';
            if (!groupedByPeriod[periodKey].byPaymentMethod[paymentMethod]) {
                groupedByPeriod[periodKey].byPaymentMethod[paymentMethod] = {
                    revenue: 0,
                    count: 0
                };
            }
            groupedByPeriod[periodKey].byPaymentMethod[paymentMethod].revenue += payment.amount;
            groupedByPeriod[periodKey].byPaymentMethod[paymentMethod].count += 1;
        });

        // Convert to array and sort
        const revenueByPeriod = Object.values(groupedByPeriod).sort((a, b) => a.period.localeCompare(b.period));

        // Format data for CSV
        const csvData = revenueByPeriod.map(periodData => ({
            'Period': periodData.period,
            'Revenue': periodData.revenue.toFixed(2),
            'Transactions': periodData.count,
            'Average Transaction': (periodData.revenue / periodData.count).toFixed(2)
        }));

        // Convert to CSV
        const createCsvStringifier = require('csv-writer').createObjectCsvStringifier;
        const csvStringifier = createCsvStringifier({
            header: [
                { id: 'Period', title: 'Period' },
                { id: 'Revenue', title: 'Revenue' },
                { id: 'Transactions', title: 'Transactions' },
                { id: 'Average Transaction', title: 'Average Transaction' }
            ]
        });

        const csvHeader = csvStringifier.getHeaderString();
        const csvBody = csvStringifier.stringifyRecords(csvData);
        const csv = csvHeader + csvBody;

        // Set headers for file download
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename=revenue-report-${new Date().toISOString().split('T')[0]}.csv`);

        res.status(200).send(csv);
    } catch (error) {
        console.error("Error exporting revenue report:", error);
        next(error);
    }
};