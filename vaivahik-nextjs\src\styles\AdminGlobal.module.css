/* Admin Global Styles as CSS Module */
/* This file only imports CSS files that don't contain global selectors like :root */

/* Import only CSS files without global selectors */
@import './admin-custom.css';
@import './feature-management.css';
@import './data-grid.css';
@import './reported-profiles.css';
@import './verification-queue.css';
@import './admin-fixes.css';
@import './admin-responsive.css';
@import './success-analytics.css';
@import './financial.css';
@import './admin-modals.css';
@import './modal-fixes.css';

/* Add a dummy class to make this a valid CSS module */
.adminStyles {
  display: block;
}
