/**
 * Redis Cache Service
 * 
 * This service provides caching functionality using Redis.
 * It includes methods for setting, getting, and invalidating cache entries.
 */

const redis = require('./redisClient');
const logger = require('../src/utils/logger');

// Default cache expiration time in seconds (1 hour)
const DEFAULT_CACHE_TTL = 3600;

// Cache key prefixes for different data types
const CACHE_PREFIXES = {
  USER: 'user:',
  PROFILE: 'profile:',
  MATCHES: 'matches:',
  CONVERSATIONS: 'conversations:',
  MESSAGES: 'messages:',
  DASHBOARD_STATS: 'dashboard:stats:',
  SEARCH_RESULTS: 'search:',
  NOTIFICATIONS: 'notifications:',
  PHOTOS: 'photos:',
  PREFERENCES: 'preferences:'
};

/**
 * Set a value in the cache
 * @param {string} key - Cache key
 * @param {any} value - Value to cache (will be JSON stringified)
 * @param {number} ttl - Time to live in seconds (optional)
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
const setCache = async (key, value, ttl = DEFAULT_CACHE_TTL) => {
  try {
    const client = await redis.getClient();
    const serializedValue = JSON.stringify(value);
    await client.set(key, serializedValue, 'EX', ttl);
    logger.debug(`Cache set: ${key} (TTL: ${ttl}s)`);
    return true;
  } catch (error) {
    logger.error(`Error setting cache for key ${key}:`, error);
    return false;
  }
};

/**
 * Get a value from the cache
 * @param {string} key - Cache key
 * @returns {Promise<any|null>} - Cached value or null if not found
 */
const getCache = async (key) => {
  try {
    const client = await redis.getClient();
    const cachedValue = await client.get(key);
    
    if (!cachedValue) {
      logger.debug(`Cache miss: ${key}`);
      return null;
    }
    
    logger.debug(`Cache hit: ${key}`);
    return JSON.parse(cachedValue);
  } catch (error) {
    logger.error(`Error getting cache for key ${key}:`, error);
    return null;
  }
};

/**
 * Delete a value from the cache
 * @param {string} key - Cache key
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
const deleteCache = async (key) => {
  try {
    const client = await redis.getClient();
    await client.del(key);
    logger.debug(`Cache deleted: ${key}`);
    return true;
  } catch (error) {
    logger.error(`Error deleting cache for key ${key}:`, error);
    return false;
  }
};

/**
 * Delete multiple values from the cache using a pattern
 * @param {string} pattern - Cache key pattern (e.g., "user:*")
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
const deleteCachePattern = async (pattern) => {
  try {
    const client = await redis.getClient();
    const keys = await client.keys(pattern);
    
    if (keys.length > 0) {
      await client.del(keys);
      logger.debug(`Cache pattern deleted: ${pattern} (${keys.length} keys)`);
    } else {
      logger.debug(`No keys found for pattern: ${pattern}`);
    }
    
    return true;
  } catch (error) {
    logger.error(`Error deleting cache pattern ${pattern}:`, error);
    return false;
  }
};

/**
 * Get or set cache with a function to generate the value if not found
 * @param {string} key - Cache key
 * @param {Function} fn - Function to generate the value if not in cache
 * @param {number} ttl - Time to live in seconds (optional)
 * @returns {Promise<any>} - Cached or generated value
 */
const getOrSetCache = async (key, fn, ttl = DEFAULT_CACHE_TTL) => {
  try {
    // Try to get from cache first
    const cachedValue = await getCache(key);
    
    // If found in cache, return it
    if (cachedValue !== null) {
      return cachedValue;
    }
    
    // Not in cache, generate the value
    const generatedValue = await fn();
    
    // Store in cache for future requests
    await setCache(key, generatedValue, ttl);
    
    return generatedValue;
  } catch (error) {
    logger.error(`Error in getOrSetCache for key ${key}:`, error);
    // If cache fails, still try to return the generated value
    return await fn();
  }
};

/**
 * Invalidate user-related caches when user data is updated
 * @param {string} userId - User ID
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
const invalidateUserCache = async (userId) => {
  try {
    // Delete user profile cache
    await deleteCache(`${CACHE_PREFIXES.USER}${userId}`);
    await deleteCache(`${CACHE_PREFIXES.PROFILE}${userId}`);
    
    // Delete user's matches cache
    await deleteCache(`${CACHE_PREFIXES.MATCHES}${userId}`);
    
    // Delete user's conversations cache
    await deleteCachePattern(`${CACHE_PREFIXES.CONVERSATIONS}${userId}:*`);
    
    // Delete user's notifications cache
    await deleteCache(`${CACHE_PREFIXES.NOTIFICATIONS}${userId}`);
    
    // Delete user's photos cache
    await deleteCache(`${CACHE_PREFIXES.PHOTOS}${userId}`);
    
    // Delete user's preferences cache
    await deleteCache(`${CACHE_PREFIXES.PREFERENCES}${userId}`);
    
    logger.info(`User cache invalidated for user ${userId}`);
    return true;
  } catch (error) {
    logger.error(`Error invalidating user cache for user ${userId}:`, error);
    return false;
  }
};

/**
 * Invalidate conversation-related caches when messages are updated
 * @param {string} conversationId - Conversation ID
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
const invalidateConversationCache = async (conversationId) => {
  try {
    // Delete conversation cache
    await deleteCache(`${CACHE_PREFIXES.CONVERSATIONS}${conversationId}`);
    
    // Delete messages cache for this conversation
    await deleteCachePattern(`${CACHE_PREFIXES.MESSAGES}${conversationId}:*`);
    
    logger.info(`Conversation cache invalidated for conversation ${conversationId}`);
    return true;
  } catch (error) {
    logger.error(`Error invalidating conversation cache for conversation ${conversationId}:`, error);
    return false;
  }
};

/**
 * Invalidate dashboard statistics cache
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
const invalidateDashboardStatsCache = async () => {
  try {
    await deleteCachePattern(`${CACHE_PREFIXES.DASHBOARD_STATS}*`);
    logger.info('Dashboard statistics cache invalidated');
    return true;
  } catch (error) {
    logger.error('Error invalidating dashboard statistics cache:', error);
    return false;
  }
};

/**
 * Invalidate search results cache
 * @param {string} searchQuery - Search query or pattern
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
const invalidateSearchCache = async (searchQuery = '*') => {
  try {
    await deleteCachePattern(`${CACHE_PREFIXES.SEARCH_RESULTS}${searchQuery}`);
    logger.info(`Search cache invalidated for query: ${searchQuery}`);
    return true;
  } catch (error) {
    logger.error(`Error invalidating search cache for query ${searchQuery}:`, error);
    return false;
  }
};

module.exports = {
  CACHE_PREFIXES,
  DEFAULT_CACHE_TTL,
  setCache,
  getCache,
  deleteCache,
  deleteCachePattern,
  getOrSetCache,
  invalidateUserCache,
  invalidateConversationCache,
  invalidateDashboardStatsCache,
  invalidateSearchCache
};
