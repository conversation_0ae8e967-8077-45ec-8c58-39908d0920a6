/* Referral Programs Page Styles */

/* Variables */
:root {
  --primary: #5e35b1;
  --primary-light: #7e57c2;
  --primary-dark: #4527a0;
  --success: #4caf50;
  --success-light: #e8f5e9;
  --warning: #ff9800;
  --warning-light: #fff3e0;
  --danger: #f44336;
  --danger-light: #ffebee;
  --text-dark: #333;
  --text-light: #fff;
  --border-color: #e0e0e0;
  --background-light: #f5f5f5;
}

/* Stats Overview */
.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stats-card {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-5px);
}

.stats-icon {
  font-size: 2rem;
  margin-bottom: 10px;
}

.stats-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 5px;
}

.stats-label {
  color: #666;
  font-size: 0.9rem;
}

/* Content Header */
.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  color: var(--text-dark);
  font-size: 1.5rem;
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: var(--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #333;
}

.btn-secondary:hover {
  background-color: #e0e0e0;
}

.btn-danger {
  background-color: var(--danger);
  color: white;
}

.btn-danger:hover {
  background-color: #d32f2f;
}

.btn-sm {
  padding: 5px 10px;
  font-size: 0.85rem;
}

.btn-outline-primary {
  border: 1px solid var(--primary);
  color: var(--primary);
  background-color: transparent;
}

.btn-outline-primary:hover {
  background-color: var(--primary);
  color: white;
}

.btn-outline-secondary {
  border: 1px solid #666;
  color: #666;
  background-color: transparent;
}

.btn-outline-secondary:hover {
  background-color: #666;
  color: white;
}

.btn-outline-danger {
  border: 1px solid var(--danger);
  color: var(--danger);
  background-color: transparent;
}

.btn-outline-danger:hover {
  background-color: var(--danger);
  color: white;
}

.btn-icon {
  margin-right: 8px;
}

/* Programs Container */
.programs-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.program-card {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-top: 5px solid var(--primary);
}

.program-card.active {
  border-top-color: var(--success);
}

.program-card.inactive {
  border-top-color: var(--danger);
}

.program-card.scheduled {
  border-top-color: var(--warning);
}

.program-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.program-header {
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
}

.program-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: var(--text-dark);
}

.status-badge {
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-badge.active {
  background-color: var(--success-light);
  color: var(--success);
}

.status-badge.inactive {
  background-color: var(--danger-light);
  color: var(--danger);
}

.status-badge.scheduled {
  background-color: var(--warning-light);
  color: var(--warning);
}

.program-description {
  padding: 15px 20px;
  color: #666;
  font-size: 0.9rem;
  border-bottom: 1px solid #eee;
}

.program-details {
  padding: 15px 20px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.detail-label {
  color: #666;
  font-weight: 500;
}

.detail-value {
  color: var(--text-dark);
  font-weight: 600;
}

.program-stats {
  display: flex;
  justify-content: space-around;
  padding: 15px 20px;
  background-color: #f9f9f9;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary);
  display: block;
}

.stat-label {
  font-size: 0.8rem;
  color: #666;
}

.program-actions {
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
}

/* Empty State */
.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 50px 20px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  color: #ccc;
}

.empty-state h3 {
  margin-bottom: 10px;
  color: var(--text-dark);
}

.empty-state p {
  color: #666;
  margin-bottom: 20px;
}

/* Loading State */
.loading-container {
  grid-column: 1 / -1;
  text-align: center;
  padding: 50px 20px;
}

.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid rgba(94, 53, 177, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background-color: white;
  border-radius: 10px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.confirmation-modal {
  max-width: 400px;
}

.modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  margin: 0;
  font-size: 1.2rem;
  color: var(--text-dark);
}

.modal-close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #999;
}

.modal-close-button:hover {
  color: var(--danger);
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* Confirmation Message */
.confirmation-message {
  text-align: center;
  padding: 20px 0;
}

.confirmation-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

/* Form Styles */
.form-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.1rem;
  margin-bottom: 15px;
  color: var(--primary);
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--text-dark);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 2px rgba(94, 53, 177, 0.2);
}

.form-row {
  display: flex;
  gap: 15px;
}

.form-row .form-group {
  flex: 1;
}

.form-hint {
  font-size: 0.8rem;
  color: #666;
  margin-top: 5px;
}

.reward-config {
  background-color: #f9f9ff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.reward-config h5 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1rem;
  color: var(--text-dark);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .programs-container {
    grid-template-columns: 1fr;
  }
  
  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .form-row {
    flex-direction: column;
    gap: 0;
  }
}
