/**
 * Dashboard Router - Redirects to Website Dashboard
 * This file redirects users to the main dashboard in the website folder
 * Admin dashboard is at /admin/dashboard
 */

import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { CircularProgress, Box } from '@mui/material';

export default function DashboardRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the website dashboard (correct path)
    router.replace('/website/dashboard');
  }, [router]);

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        background: 'linear-gradient(135deg, rgba(255, 182, 193, 0.1), rgba(255, 240, 245, 0.2))'
      }}
    >
      <CircularProgress
        sx={{
          color: '#FF69B4',
          '& .MuiCircularProgress-circle': {
            strokeLinecap: 'round'
          }
        }}
      />
    </Box>
  );
}
