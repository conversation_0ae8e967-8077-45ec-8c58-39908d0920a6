/**
 * Error Analytics Service
 * 
 * This service tracks and analyzes errors in the application.
 * It provides methods for logging errors, tracking error trends,
 * and generating error reports.
 */

const { Sentry } = require('../config/sentry');

// In-memory cache for error analytics (will be replaced with Redis in production)
let errorCache = {
  // Count of errors by type
  errorCounts: {},
  // Count of errors by endpoint
  endpointErrors: {},
  // Count of errors by user
  userErrors: {},
  // Recent errors (last 100)
  recentErrors: [],
  // Error trends (hourly counts for the last 24 hours)
  hourlyTrends: Array(24).fill(0),
  // Last reset time
  lastReset: Date.now()
};

/**
 * Track an error in the analytics system
 * @param {Error} error - The error object
 * @param {Object} context - Additional context for the error
 * @param {Object} context.req - Express request object (optional)
 * @param {Object} context.user - User object (optional)
 * @param {string} context.source - Source of the error (optional)
 */
const trackError = (error, context = {}) => {
  const { req, user, source } = context;
  const timestamp = Date.now();
  const errorType = error.name || 'UnknownError';
  const errorMessage = error.message || 'Unknown error';
  const errorStack = error.stack || '';
  
  // Extract endpoint from request if available
  const endpoint = req ? `${req.method} ${req.path}` : source || 'unknown';
  
  // Extract user ID if available
  const userId = user?.userId || req?.user?.userId || 'anonymous';
  
  // Create error record
  const errorRecord = {
    type: errorType,
    message: errorMessage,
    endpoint,
    userId,
    timestamp,
    stack: errorStack,
    // Include request details if available
    request: req ? {
      method: req.method,
      path: req.path,
      query: req.query,
      headers: filterSensitiveHeaders(req.headers),
      ip: req.ip
    } : null
  };
  
  // Update error counts by type
  errorCache.errorCounts[errorType] = (errorCache.errorCounts[errorType] || 0) + 1;
  
  // Update error counts by endpoint
  if (!errorCache.endpointErrors[endpoint]) {
    errorCache.endpointErrors[endpoint] = { count: 0, errors: {} };
  }
  errorCache.endpointErrors[endpoint].count += 1;
  errorCache.endpointErrors[endpoint].errors[errorType] = 
    (errorCache.endpointErrors[endpoint].errors[errorType] || 0) + 1;
  
  // Update error counts by user
  if (!errorCache.userErrors[userId]) {
    errorCache.userErrors[userId] = { count: 0, errors: {} };
  }
  errorCache.userErrors[userId].count += 1;
  errorCache.userErrors[userId].errors[errorType] = 
    (errorCache.userErrors[userId].errors[errorType] || 0) + 1;
  
  // Add to recent errors (keep only the last 100)
  errorCache.recentErrors.unshift(errorRecord);
  if (errorCache.recentErrors.length > 100) {
    errorCache.recentErrors.pop();
  }
  
  // Update hourly trends
  const hourIndex = new Date().getHours();
  errorCache.hourlyTrends[hourIndex] += 1;
  
  // Reset daily stats if it's been more than 24 hours
  if (timestamp - errorCache.lastReset > 24 * 60 * 60 * 1000) {
    resetDailyStats();
  }
  
  // Also send to Sentry if available
  try {
    if (Sentry) {
      Sentry.withScope(scope => {
        // Add additional context
        scope.setTag('endpoint', endpoint);
        scope.setTag('error_type', errorType);
        
        if (userId !== 'anonymous') {
          scope.setUser({ id: userId });
        }
        
        if (req) {
          scope.addEventProcessor(event => Sentry.Handlers.parseRequest(event, req));
        }
        
        Sentry.captureException(error);
      });
    }
  } catch (sentryError) {
    console.error('Error sending to Sentry:', sentryError);
  }
};

/**
 * Get error analytics data
 * @param {Object} options - Options for filtering the data
 * @param {string} options.timeframe - Timeframe for the data (hour, day, week, month)
 * @param {string} options.type - Type of data to return (overview, byType, byEndpoint, byUser, recent)
 * @param {number} options.limit - Maximum number of items to return
 * @returns {Object} Error analytics data
 */
const getErrorAnalytics = (options = {}) => {
  const { 
    timeframe = 'day', 
    type = 'overview',
    limit = 10
  } = options;
  
  // Return different data based on the type
  switch (type) {
    case 'overview':
      return {
        totalErrors: Object.values(errorCache.errorCounts).reduce((sum, count) => sum + count, 0),
        byType: sortAndLimit(errorCache.errorCounts, limit),
        byEndpoint: sortAndLimit(
          Object.entries(errorCache.endpointErrors).reduce((acc, [endpoint, data]) => {
            acc[endpoint] = data.count;
            return acc;
          }, {}), 
          limit
        ),
        hourlyTrends: errorCache.hourlyTrends,
        lastReset: errorCache.lastReset
      };
      
    case 'byType':
      return sortAndLimit(errorCache.errorCounts, limit);
      
    case 'byEndpoint':
      return sortAndLimit(
        Object.entries(errorCache.endpointErrors).reduce((acc, [endpoint, data]) => {
          acc[endpoint] = {
            count: data.count,
            errors: sortAndLimit(data.errors, limit)
          };
          return acc;
        }, {}),
        limit,
        entry => entry[1].count
      );
      
    case 'byUser':
      return sortAndLimit(
        Object.entries(errorCache.userErrors).reduce((acc, [userId, data]) => {
          acc[userId] = {
            count: data.count,
            errors: sortAndLimit(data.errors, limit)
          };
          return acc;
        }, {}),
        limit,
        entry => entry[1].count
      );
      
    case 'recent':
      return errorCache.recentErrors.slice(0, limit);
      
    default:
      return { error: 'Invalid analytics type' };
  }
};

/**
 * Reset daily error statistics
 */
const resetDailyStats = () => {
  errorCache.hourlyTrends = Array(24).fill(0);
  errorCache.lastReset = Date.now();
};

/**
 * Filter sensitive information from request headers
 * @param {Object} headers - Request headers
 * @returns {Object} Filtered headers
 */
const filterSensitiveHeaders = (headers) => {
  const filtered = { ...headers };
  const sensitiveHeaders = ['authorization', 'cookie', 'set-cookie'];
  
  sensitiveHeaders.forEach(header => {
    if (filtered[header]) {
      filtered[header] = '[FILTERED]';
    }
  });
  
  return filtered;
};

/**
 * Sort an object by values and limit the number of results
 * @param {Object} obj - Object to sort
 * @param {number} limit - Maximum number of items to return
 * @param {Function} getValue - Function to get the value for sorting
 * @returns {Object} Sorted and limited object
 */
const sortAndLimit = (obj, limit, getValue = entry => entry[1]) => {
  return Object.fromEntries(
    Object.entries(obj)
      .sort((a, b) => getValue(b) - getValue(a))
      .slice(0, limit)
  );
};

module.exports = {
  trackError,
  getErrorAnalytics,
  resetDailyStats
};
