const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { authenticateToken } = require('../../middleware/auth.middleware');
const multer = require('multer');
const path = require('path');

// Configure multer for success story photos
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/success-stories');
  },
  filename: function (req, file, cb) {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: function (req, file, cb) {
    const filetypes = /jpeg|jpg|png|webp/;
    const mimetype = filetypes.test(file.mimetype);
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

    if (mimetype && extname) {
      cb(null, true);
    } else {
      cb(new Error('Only JPEG, JPG, PNG, or WEBP images are allowed!'), false);
    }
  }
});
const authenticateAdmin = require('../../middleware/adminAuth.middleware');

/**
 * @route POST /api/analytics/success-stories
 * @desc Submit a success story
 * @access Private
 *
 * Request body:
 * {
 *   userId2: string,
 *   status: "MATCHED" | "TALKING" | "MET" | "ENGAGED" | "MARRIED",
 *   storyText?: string,
 *   testimonyText?: string,
 *   rating?: number,
 *   engagementDate?: string,
 *   marriageDate?: string,
 *   isPublic: boolean,
 *   successFactors?: string[]
 * }
 */
router.post('/', authenticateToken, upload.array('photos', 5), async (req, res) => {
  try {
    const userId1 = req.user.id;

    // Get request data
    const {
      userId2,
      status,
      storyText,
      testimonyText,
      rating,
      engagementDate,
      marriageDate,
      isPublic,
      successFactors
    } = req.body;

    // Validate required fields
    if (!userId2 || !status) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: userId2 and status are required'
      });
    }

    // Process uploaded photos
    const photoUrls = req.files ? req.files.map(file => file.path) : [];

    // Create the success story record
    const successStory = await prisma.successStory.create({
      data: {
        userId1,
        userId2,
        status,
        storyText: storyText || null,
        testimonyText: testimonyText || null,
        rating: rating ? parseFloat(rating) : null,
        engagementDate: engagementDate ? new Date(engagementDate) : null,
        marriageDate: marriageDate ? new Date(marriageDate) : null,
        photos: photoUrls.length > 0 ? photoUrls : null,
        isPublic: isPublic === 'true' || isPublic === true,
        successFactors: successFactors ? JSON.parse(successFactors) : null,
      },
    });

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Success story submitted successfully',
      successStoryId: successStory.id
    });

  } catch (error) {
    console.error('Error submitting success story:', error);
    return res.status(500).json({
      success: false,
      message: 'Error submitting success story',
      error: error.message
    });
  }
});

/**
 * @route GET /api/analytics/success-stories
 * @desc Get public success stories
 * @access Public
 */
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const skip = (page - 1) * limit;

    // Get public success stories
    const successStories = await prisma.successStory.findMany({
      where: { isPublic: true },
      include: {
        user1: {
          select: {
            id: true,
            profile: {
              select: {
                fullName: true,
                city: true,
                state: true
              }
            }
          }
        },
        user2: {
          select: {
            id: true,
            profile: {
              select: {
                fullName: true,
                city: true,
                state: true
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: parseInt(limit)
    });

    const total = await prisma.successStory.count({
      where: { isPublic: true }
    });

    // Return success stories
    return res.status(200).json({
      success: true,
      data: successStories,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error getting success stories:', error);
    return res.status(500).json({
      success: false,
      message: 'Error getting success stories',
      error: error.message
    });
  }
});

/**
 * @route GET /api/analytics/success-stories/admin
 * @desc Get all success stories (admin only)
 * @access Private (Admin)
 */
router.get('/admin', function(req, res, next) {
  authenticateToken(req, res, function() {
    authenticateAdmin(req, res, async function() {
  try {
    const { page = 1, limit = 10, status } = req.query;
    const skip = (page - 1) * limit;

    const where = status ? { status } : {};

    // Get all success stories
    const successStories = await prisma.successStory.findMany({
      where,
      include: {
        user1: {
          select: {
            id: true,
            profile: {
              select: {
                fullName: true,
                city: true,
                state: true
              }
            }
          }
        },
        user2: {
          select: {
            id: true,
            profile: {
              select: {
                fullName: true,
                city: true,
                state: true
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: parseInt(limit)
    });

    const total = await prisma.successStory.count({ where });

    // Return success stories
    return res.status(200).json({
      success: true,
      data: successStories,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error getting success stories:', error);
    return res.status(500).json({
      success: false,
      message: 'Error getting success stories',
      error: error.message
    });
  }
    });
  });
});

module.exports = router;
