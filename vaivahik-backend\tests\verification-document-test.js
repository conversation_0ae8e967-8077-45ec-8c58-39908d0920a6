// tests/verification-document-test.js

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testVerificationDocumentModel() {
  console.log('Starting VerificationDocument model test...');

  try {
    // 1. Check if we can create a test user
    console.log('Creating test user...');
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        phone: '+************',
        password: 'hashedpassword123',
        profileStatus: 'PENDING_APPROVAL'
      }
    });
    console.log('Test user created with ID:', testUser.id);

    // 2. Create a test verification document
    console.log('Creating test verification document...');
    const testDocument = await prisma.verificationDocument.create({
      data: {
        userId: testUser.id,
        type: 'AADHAR_CARD',
        url: '/uploads/verification_documents/test-document.jpg',
        filename: 'test-document.jpg',
        filesize: 1024,
        mimeType: 'image/jpeg',
        status: 'PENDING_REVIEW'
      }
    });
    console.log('Test document created:', testDocument);

    // 3. Test relationship by fetching user with documents
    console.log('Testing relationship by fetching user with documents...');
    const userWithDocuments = await prisma.user.findUnique({
      where: { id: testUser.id },
      include: { verificationDocuments: true }
    });
    console.log('User with documents:',
      JSON.stringify({
        id: userWithDocuments.id,
        email: userWithDocuments.email,
        documentCount: userWithDocuments.verificationDocuments.length,
        documents: userWithDocuments.verificationDocuments.map(doc => ({
          id: doc.id,
          type: doc.type,
          status: doc.status
        }))
      }, null, 2)
    );

    // 4. Test document approval
    console.log('Testing document approval...');
    const approvedDocument = await prisma.verificationDocument.update({
      where: { id: testDocument.id },
      data: {
        status: 'APPROVED',
        reviewedAt: new Date(),
        reviewedBy: 'test-admin'
      }
    });
    console.log('Document approved:', approvedDocument.status);

    // 5. Clean up test data
    console.log('Cleaning up test data...');
    await prisma.verificationDocument.delete({
      where: { id: testDocument.id }
    });
    await prisma.user.delete({
      where: { id: testUser.id }
    });
    console.log('Test data cleaned up successfully');

    console.log('VerificationDocument model test completed successfully!');
    return true;
  } catch (error) {
    console.error('Error testing VerificationDocument model:', error);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testVerificationDocumentModel()
  .then(success => {
    console.log(`Test ${success ? 'passed' : 'failed'}`);
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Unhandled error during test:', error);
    process.exit(1);
  });
