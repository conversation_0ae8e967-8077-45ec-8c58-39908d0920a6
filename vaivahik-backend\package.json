{"name": "vaivahik-backend", "version": "1.0.0", "description": "Vaivahik AI Matrimony Backend using PostgreSQL + Redis", "main": "server.js", "scripts": {"dev": "cross-env PORT=8080 nodemon server.js", "start": "node server.js", "seed": "ts-node prisma/seed.ts", "prisma": "prisma", "cache:monitor": "node scripts/monitor-cache.js", "cache:clear": "node scripts/clear-cache.js", "cache:clear:dashboard": "node scripts/clear-cache.js dashboard:stats:*", "cache:clear:users": "node scripts/clear-cache.js user:*", "test:otp": "node scripts/test-msg91-otp.js", "test:msg91": "node scripts/test-msg91-config.js", "test:6digit": "node scripts/test-6digit-otp.js", "test:4digit": "node scripts/test-4digit-otp.js"}, "dependencies": {"@prisma/client": "^6.8.2", "@sentry/integrations": "^7.114.0", "@sentry/node": "^9.22.0", "@sentry/profiling-node": "^9.22.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.0.3", "exceljs": "^4.4.0", "express": "^4.18.2", "express-validator": "^7.2.1", "firebase-admin": "^13.4.0", "form-data": "^4.0.2", "helmet": "^6.2.0", "ioredis": "^5.3.2", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.2", "node-cron": "^3.0.3", "nodemailer": "^6.9.14", "otp-generator": "^4.0.1", "razorpay": "^2.9.6", "redis": "^4.6.7", "sharp": "^0.34.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1"}, "devDependencies": {"cross-env": "^7.0.3", "nodemon": "^3.1.10", "prisma": "^6.8.2", "ts-node": "^10.9.1"}}