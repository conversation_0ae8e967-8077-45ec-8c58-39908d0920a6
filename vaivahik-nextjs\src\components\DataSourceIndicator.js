'use client';

import { useEffect, useState } from 'react';
import { Chip } from '@mui/material';

/**
 * Client-side only component to display the current data source
 * This helps avoid hydration errors by ensuring the component
 * only renders on the client side
 */
export default function DataSourceIndicator({ dataSource }) {
  const [mounted, setMounted] = useState(false);

  // Only show after component has mounted on client
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted || process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <Chip
      label={`Using ${dataSource} data`}
      color={dataSource === 'mock' ? 'warning' : 'success'}
      size="medium"
      sx={{
        mt: 2,
        mb: 1,
        py: 1.5,
        fontWeight: 'bold',
        fontSize: '0.9rem',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        border: '1px solid',
        borderColor: dataSource === 'mock' ? 'warning.main' : 'success.main',
      }}
    />
  );
}
