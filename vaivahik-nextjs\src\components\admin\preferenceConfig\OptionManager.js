import { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  Grid,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemSecondaryAction,
  ListItemText,
  Paper,
  Switch,
  TextField,
  Tooltip,
  Typography,
  FormControlLabel
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIcon,
  Label as LabelIcon
} from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { toast } from 'react-toastify';
import axiosInstance from '@/utils/axiosConfig';

const OptionManager = ({ field, refreshData, onClose }) => {
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [currentOption, setCurrentOption] = useState(null);
  const [formData, setFormData] = useState({
    value: '',
    displayText: '',
    description: '',
    displayOrder: 0,
    isActive: true
  });
  const [errors, setErrors] = useState({});

  // Fetch options on component mount
  useEffect(() => {
    fetchOptions();
  }, [field.id]);

  // Fetch options for the field
  const fetchOptions = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(`/api/admin/preference-config/options?fieldId=${field.id}`);
      
      if (response.data.success) {
        setOptions(response.data.options);
      } else {
        toast.error(response.data.message || 'Failed to fetch options');
      }
    } catch (error) {
      console.error('Error fetching options:', error);
      toast.error('Failed to fetch options');
      
      // For development, use mock data if API fails
      if (process.env.NODE_ENV === 'development') {
        setOptions(field.options || []);
      }
    } finally {
      setLoading(false);
    }
  };

  // Open dialog for creating a new option
  const handleAddOption = () => {
    setCurrentOption(null);
    setFormData({
      value: '',
      displayText: '',
      description: '',
      displayOrder: options.length,
      isActive: true
    });
    setErrors({});
    setOpenDialog(true);
  };

  // Open dialog for editing an existing option
  const handleEditOption = (option) => {
    setCurrentOption(option);
    setFormData({
      value: option.value,
      displayText: option.displayText,
      description: option.description || '',
      displayOrder: option.displayOrder,
      isActive: option.isActive
    });
    setErrors({});
    setOpenDialog(true);
  };

  // Open dialog for deleting an option
  const handleDeleteClick = (option) => {
    setCurrentOption(option);
    setDeleteDialog(true);
  };

  // Close all dialogs
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setDeleteDialog(false);
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, checked, type } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
    
    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };

  // Validate form data
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.value) {
      newErrors.value = 'Value is required';
    }
    
    if (!formData.displayText) {
      newErrors.displayText = 'Display text is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Submit form data
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }
    
    try {
      if (currentOption) {
        // Update existing option
        const response = await axiosInstance.put(
          `/api/admin/preference-config/options/${currentOption.id}`,
          formData
        );
        
        if (response.data.success) {
          toast.success('Option updated successfully');
          fetchOptions();
          refreshData();
          handleCloseDialog();
        } else {
          toast.error(response.data.message || 'Failed to update option');
        }
      } else {
        // Create new option
        const response = await axiosInstance.post(
          '/api/admin/preference-config/options',
          {
            ...formData,
            fieldId: field.id
          }
        );
        
        if (response.data.success) {
          toast.success('Option created successfully');
          fetchOptions();
          refreshData();
          handleCloseDialog();
        } else {
          toast.error(response.data.message || 'Failed to create option');
        }
      }
    } catch (error) {
      console.error('Error saving option:', error);
      toast.error(error.response?.data?.message || 'An error occurred while saving the option');
    }
  };

  // Delete an option
  const handleDeleteOption = async () => {
    try {
      const response = await axiosInstance.delete(
        `/api/admin/preference-config/options/${currentOption.id}`
      );
      
      if (response.data.success) {
        toast.success('Option deleted successfully');
        fetchOptions();
        refreshData();
        handleCloseDialog();
      } else {
        toast.error(response.data.message || 'Failed to delete option');
      }
    } catch (error) {
      console.error('Error deleting option:', error);
      toast.error(error.response?.data?.message || 'An error occurred while deleting the option');
    }
  };

  // Handle drag and drop for reordering options
  const handleDragEnd = async (result) => {
    if (!result.destination) return;
    
    const items = Array.from(options);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    
    // Update display order for all affected items
    const updatedItems = items.map((item, index) => ({
      ...item,
      displayOrder: index
    }));
    
    // Update the UI immediately
    setOptions(updatedItems);
    
    // Update the display order in the database
    try {
      // This would be a batch update in a real implementation
      for (const item of updatedItems) {
        if (item.displayOrder !== item.originalDisplayOrder) {
          await axiosInstance.put(
            `/api/admin/preference-config/options/${item.id}`,
            { displayOrder: item.displayOrder }
          );
        }
      }
      
      toast.success('Options reordered successfully');
      refreshData();
    } catch (error) {
      console.error('Error reordering options:', error);
      toast.error('Failed to update option order');
      fetchOptions(); // Refresh to revert to original order
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h6">
          Options for {field.displayName}
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleAddOption}
        >
          Add Option
        </Button>
      </Box>

      <Paper sx={{ p: 2 }}>
        {options.length === 0 ? (
          <Typography variant="body2" color="textSecondary" sx={{ p: 2, textAlign: 'center' }}>
            No options found. Click "Add Option" to create one.
          </Typography>
        ) : (
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="options">
              {(provided) => (
                <List {...provided.droppableProps} ref={provided.innerRef}>
                  {options
                    .sort((a, b) => a.displayOrder - b.displayOrder)
                    .map((option, index) => (
                      <Draggable key={option.id} draggableId={option.id} index={index}>
                        {(provided) => (
                          <ListItem
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            divider
                          >
                            <ListItemIcon {...provided.dragHandleProps}>
                              <DragIcon />
                            </ListItemIcon>
                            <ListItemIcon>
                              <LabelIcon />
                            </ListItemIcon>
                            <ListItemText
                              primary={option.displayText}
                              secondary={
                                <>
                                  <Typography variant="caption" component="span" color="textSecondary">
                                    Value: {option.value}
                                  </Typography>
                                  {option.description && (
                                    <Typography variant="body2" color="textSecondary">
                                      {option.description}
                                    </Typography>
                                  )}
                                </>
                              }
                            />
                            <ListItemSecondaryAction>
                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={option.isActive}
                                    onChange={async (e) => {
                                      try {
                                        const response = await axiosInstance.put(
                                          `/api/admin/preference-config/options/${option.id}`,
                                          { isActive: e.target.checked }
                                        );
                                        
                                        if (response.data.success) {
                                          toast.success(`Option ${e.target.checked ? 'activated' : 'deactivated'} successfully`);
                                          fetchOptions();
                                          refreshData();
                                        } else {
                                          toast.error(response.data.message || 'Failed to update option');
                                        }
                                      } catch (error) {
                                        console.error('Error updating option:', error);
                                        toast.error('Failed to update option status');
                                      }
                                    }}
                                    color="primary"
                                  />
                                }
                                label="Active"
                              />
                              <Tooltip title="Edit">
                                <IconButton
                                  edge="end"
                                  aria-label="edit"
                                  onClick={() => handleEditOption(option)}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete">
                                <IconButton
                                  edge="end"
                                  aria-label="delete"
                                  onClick={() => handleDeleteClick(option)}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            </ListItemSecondaryAction>
                          </ListItem>
                        )}
                      </Draggable>
                    ))}
                  {provided.placeholder}
                </List>
              )}
            </Droppable>
          </DragDropContext>
        )}
      </Paper>

      {/* Add/Edit Option Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {currentOption ? 'Edit Option' : 'Add Option'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                name="value"
                label="Value"
                value={formData.value}
                onChange={handleInputChange}
                fullWidth
                required
                error={!!errors.value}
                helperText={errors.value || 'Internal value used in the system'}
                disabled={currentOption !== null} // Disable editing value for existing options
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="displayText"
                label="Display Text"
                value={formData.displayText}
                onChange={handleInputChange}
                fullWidth
                required
                error={!!errors.displayText}
                helperText={errors.displayText || 'Text shown to users'}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="description"
                label="Description"
                value={formData.description}
                onChange={handleInputChange}
                fullWidth
                multiline
                rows={2}
                helperText="Optional description"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="displayOrder"
                label="Display Order"
                type="number"
                value={formData.displayOrder}
                onChange={handleInputChange}
                fullWidth
                InputProps={{
                  inputProps: { min: 0 }
                }}
                helperText="Order in which options are displayed"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleInputChange}
                    color="primary"
                  />
                }
                label="Active"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} color="primary" variant="contained">
            {currentOption ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog} onClose={handleCloseDialog}>
        <DialogTitle>Delete Option</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the option "{currentOption?.displayText}"?
            This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleDeleteOption} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default OptionManager;
