import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  TextField,
  Button,
  Card,
  CardContent,
  Grid,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Switch,
  FormControlLabel,
  Collapse,
  IconButton,
  styled
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  <PERSON>ne as AdvancedFilterIcon,
  ExpandMoreIcon as ExpandIcon,
  ExpandLessIcon as CollapseIcon,
  WorkspacePremium as PremiumIcon,
  Psychology as AIIcon,
  Star as StarIcon
} from '@mui/icons-material';

const SearchCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: 24,
  boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
    background: 'linear-gradient(90deg, #FF5F6D, #FFC371)',
    borderRadius: '24px 24px 0 0'
  }
}));

const PremiumFeatureCard = styled(Card)(({ theme }) => ({
  background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 160, 0, 0.1) 100%)',
  border: '2px solid rgba(255, 215, 0, 0.3)',
  borderRadius: 16,
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 12px 24px rgba(255, 215, 0, 0.2)'
  }
}));

const SearchWidget = ({ isPremium = false, onPremiumFeatureClick }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [filters, setFilters] = useState({
    ageRange: [25, 35],
    heightRange: [5.0, 6.0],
    education: '',
    occupation: '',
    income: '',
    location: '',
    caste: '',
    maritalStatus: '',
    diet: '',
    smoking: '',
    drinking: ''
  });

  const handleSearch = () => {
    console.log('Searching with:', { searchQuery, filters });
    // Implement search logic
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({ ...prev, [field]: value }));
  };

  const clearFilters = () => {
    setFilters({
      ageRange: [25, 35],
      heightRange: [5.0, 6.0],
      education: '',
      occupation: '',
      income: '',
      location: '',
      caste: '',
      maritalStatus: '',
      diet: '',
      smoking: '',
      drinking: ''
    });
  };

  return (
    <Box>
      {/* AI-Powered Search Header */}
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        mb: 3,
        p: 3,
        background: 'linear-gradient(135deg, rgba(255, 95, 109, 0.1) 0%, rgba(255, 195, 113, 0.1) 100%)',
        borderRadius: 3
      }}>
        <AIIcon sx={{ fontSize: 32, color: '#FF5F6D', mr: 2 }} />
        <Box>
          <Typography variant="h5" fontWeight="700" color="#FF5F6D">
            🤖 AI-Powered Smart Search
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Find your perfect match using advanced AI algorithms and intelligent filtering
          </Typography>
        </Box>
      </Box>

      <SearchCard>
        <CardContent sx={{ p: 4 }}>
          {/* Basic Search */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" fontWeight="600" gutterBottom>
              Quick Search
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
              <TextField
                fullWidth
                placeholder="Search by name, profession, location..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ color: '#FF5F6D', mr: 1 }} />,
                  sx: { borderRadius: 3 }
                }}
              />
              <Button
                variant="contained"
                size="large"
                onClick={handleSearch}
                sx={{
                  background: 'linear-gradient(135deg, #FF5F6D, #FFC371)',
                  borderRadius: 3,
                  px: 4,
                  minWidth: 120,
                  '&:hover': {
                    background: 'linear-gradient(135deg, #FF1493, #DC143C)',
                  }
                }}
              >
                Search
              </Button>
            </Box>

            {/* Quick Filter Chips */}
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {['Age 25-30', 'Mumbai', 'Engineer', 'Never Married', 'Vegetarian'].map((chip) => (
                <Chip
                  key={chip}
                  label={chip}
                  onClick={() => console.log('Quick filter:', chip)}
                  sx={{
                    backgroundColor: 'rgba(255, 95, 109, 0.1)',
                    color: '#FF5F6D',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 95, 109, 0.2)',
                    }
                  }}
                />
              ))}
            </Box>
          </Box>

          {/* Advanced Filters Toggle */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Button
              startIcon={<AdvancedFilterIcon />}
              endIcon={showAdvancedFilters ? <CollapseIcon /> : <ExpandIcon />}
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              sx={{ color: '#FF5F6D' }}
            >
              Advanced Filters
            </Button>
            <Button onClick={clearFilters} size="small" sx={{ color: 'text.secondary' }}>
              Clear All
            </Button>
          </Box>

          {/* Advanced Filters */}
          <Collapse in={showAdvancedFilters}>
            <Box sx={{ 
              p: 3, 
              background: 'rgba(255, 95, 109, 0.05)', 
              borderRadius: 2,
              mb: 3
            }}>
              <Grid container spacing={3}>
                {/* Age Range */}
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Age Range: {filters.ageRange[0]} - {filters.ageRange[1]} years
                  </Typography>
                  <Slider
                    value={filters.ageRange}
                    onChange={(e, value) => handleFilterChange('ageRange', value)}
                    valueLabelDisplay="auto"
                    min={18}
                    max={60}
                    sx={{ color: '#FF5F6D' }}
                  />
                </Grid>

                {/* Height Range */}
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Height: {filters.heightRange[0]}' - {filters.heightRange[1]}'
                  </Typography>
                  <Slider
                    value={filters.heightRange}
                    onChange={(e, value) => handleFilterChange('heightRange', value)}
                    valueLabelDisplay="auto"
                    min={4.5}
                    max={7.0}
                    step={0.1}
                    sx={{ color: '#FF5F6D' }}
                  />
                </Grid>

                {/* Education */}
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Education</InputLabel>
                    <Select
                      value={filters.education}
                      onChange={(e) => handleFilterChange('education', e.target.value)}
                      label="Education"
                    >
                      <MenuItem value="">Any</MenuItem>
                      <MenuItem value="high-school">High School</MenuItem>
                      <MenuItem value="bachelors">Bachelor's Degree</MenuItem>
                      <MenuItem value="masters">Master's Degree</MenuItem>
                      <MenuItem value="doctorate">Doctorate</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                {/* Occupation */}
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Occupation</InputLabel>
                    <Select
                      value={filters.occupation}
                      onChange={(e) => handleFilterChange('occupation', e.target.value)}
                      label="Occupation"
                    >
                      <MenuItem value="">Any</MenuItem>
                      <MenuItem value="engineer">Engineer</MenuItem>
                      <MenuItem value="doctor">Doctor</MenuItem>
                      <MenuItem value="teacher">Teacher</MenuItem>
                      <MenuItem value="business">Business</MenuItem>
                      <MenuItem value="government">Government</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                {/* Location */}
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Location</InputLabel>
                    <Select
                      value={filters.location}
                      onChange={(e) => handleFilterChange('location', e.target.value)}
                      label="Location"
                    >
                      <MenuItem value="">Any</MenuItem>
                      <MenuItem value="mumbai">Mumbai</MenuItem>
                      <MenuItem value="pune">Pune</MenuItem>
                      <MenuItem value="nashik">Nashik</MenuItem>
                      <MenuItem value="nagpur">Nagpur</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                {/* Marital Status */}
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Marital Status</InputLabel>
                    <Select
                      value={filters.maritalStatus}
                      onChange={(e) => handleFilterChange('maritalStatus', e.target.value)}
                      label="Marital Status"
                    >
                      <MenuItem value="">Any</MenuItem>
                      <MenuItem value="never-married">Never Married</MenuItem>
                      <MenuItem value="divorced">Divorced</MenuItem>
                      <MenuItem value="widowed">Widowed</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </Box>
          </Collapse>

          {/* Premium Features */}
          {!isPremium && (
            <PremiumFeatureCard onClick={() => onPremiumFeatureClick('advanced-search')}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <PremiumIcon sx={{ color: '#FFD700', fontSize: 32, mr: 2 }} />
                    <Box>
                      <Typography variant="h6" fontWeight="600" color="#FF8F00">
                        🚀 Unlock Premium Search Features
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        AI recommendations, compatibility scores, and advanced filters
                      </Typography>
                    </Box>
                  </Box>
                  <Button
                    variant="contained"
                    sx={{
                      background: 'linear-gradient(135deg, #FFD700, #FFA000)',
                      color: '#000',
                      fontWeight: 600
                    }}
                  >
                    Upgrade
                  </Button>
                </Box>
              </CardContent>
            </PremiumFeatureCard>
          )}
        </CardContent>
      </SearchCard>
    </Box>
  );
};

export default SearchWidget;
