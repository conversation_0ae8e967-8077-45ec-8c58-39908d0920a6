import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);
import { ToastContainer } from 'react-toastify';
import { useRouter } from 'next/router';
import axios from 'axios';
import { toast } from 'react-toastify';

export default function ApiViewer() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [endpoint, setEndpoint] = useState('');
  const [method, setMethod] = useState('GET');
  const [params, setParams] = useState({});
  const [body, setBody] = useState('');
  const [response, setResponse] = useState(null);
  const [responseTime, setResponseTime] = useState(null);
  const [responseStatus, setResponseStatus] = useState(null);
  const [history, setHistory] = useState([]);

  // Load history from localStorage on component mount
  useEffect(() => {
    const savedHistory = localStorage.getItem('apiViewerHistory');
    if (savedHistory) {
      try {
        setHistory(JSON.parse(savedHistory));
      } catch (error) {
        console.error('Error parsing API history:', error);
      }
    }
  }, []);

  // Save history to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('apiViewerHistory', JSON.stringify(history));
  }, [history]);

  const handleParamChange = (key, value) => {
    setParams(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const addParam = () => {
    setParams(prev => ({
      ...prev,
      '': ''
    }));
  };

  const removeParam = (key) => {
    setParams(prev => {
      const newParams = { ...prev };
      delete newParams[key];
      return newParams;
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setResponse(null);
    setResponseTime(null);
    setResponseStatus(null);

    try {
      // Build URL with query parameters
      let url = endpoint;
      if (method === 'GET' && Object.keys(params).length > 0) {
        const queryParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
          if (key && value) {
            queryParams.append(key, value);
          }
        });
        url = `${url}?${queryParams.toString()}`;
      }

      // Start timer
      const startTime = Date.now();

      // Make request
      const config = {
        method,
        url,
        headers: {
          'Content-Type': 'application/json'
        }
      };

      if (method !== 'GET' && body) {
        try {
          config.data = JSON.parse(body);
        } catch (error) {
          toast.error('Invalid JSON in request body');
          setLoading(false);
          return;
        }
      }

      const result = await axios(config);

      // Calculate response time
      const endTime = Date.now();
      setResponseTime(endTime - startTime);

      // Set response data
      setResponse(result.data);
      setResponseStatus(result.status);

      // Add to history
      const historyItem = {
        timestamp: new Date().toISOString(),
        method,
        endpoint,
        params: { ...params },
        body: body || null,
        status: result.status
      };

      setHistory(prev => [historyItem, ...prev.slice(0, 9)]);

      toast.success('API request successful');
    } catch (error) {
      console.error('API request failed:', error);
      setResponseStatus(error.response?.status || 500);
      setResponse(error.response?.data || { error: error.message });
      toast.error(`API request failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const loadFromHistory = (item) => {
    setEndpoint(item.endpoint);
    setMethod(item.method);
    setParams(item.params || {});
    setBody(item.body || '');
  };

  const clearHistory = () => {
    setHistory([]);
    localStorage.removeItem('apiViewerHistory');
    toast.info('History cleared');
  };

  return (
    <EnhancedAdminLayout>
      <ToastContainer position="top-right" autoClose={5000} />
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">API Viewer</h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Request Panel */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Request</h2>

            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label className="block text-sm font-medium mb-1">Endpoint</label>
                <div className="flex">
                  <select
                    className="bg-gray-100 border border-gray-300 rounded-l px-3 py-2"
                    value={method}
                    onChange={(e) => setMethod(e.target.value)}
                  >
                    <option value="GET">GET</option>
                    <option value="POST">POST</option>
                    <option value="PUT">PUT</option>
                    <option value="DELETE">DELETE</option>
                    <option value="PATCH">PATCH</option>
                  </select>
                  <input
                    type="text"
                    className="flex-1 border border-gray-300 rounded-r px-3 py-2"
                    placeholder="/api/admin/users"
                    value={endpoint}
                    onChange={(e) => setEndpoint(e.target.value)}
                    required
                  />
                </div>
              </div>

              {method === 'GET' && (
                <div className="mb-4">
                  <div className="flex justify-between items-center mb-2">
                    <label className="block text-sm font-medium">Query Parameters</label>
                    <button
                      type="button"
                      className="text-sm text-purple-600 hover:text-purple-800"
                      onClick={addParam}
                    >
                      + Add Parameter
                    </button>
                  </div>

                  {Object.entries(params).map(([key, value], index) => (
                    <div key={index} className="flex mb-2">
                      <input
                        type="text"
                        className="w-1/3 border border-gray-300 rounded-l px-3 py-2"
                        placeholder="Key"
                        value={key}
                        onChange={(e) => {
                          const newParams = { ...params };
                          delete newParams[key];
                          newParams[e.target.value] = value;
                          setParams(newParams);
                        }}
                      />
                      <input
                        type="text"
                        className="w-2/3 border-t border-b border-r border-gray-300 rounded-r px-3 py-2"
                        placeholder="Value"
                        value={value}
                        onChange={(e) => handleParamChange(key, e.target.value)}
                      />
                      <button
                        type="button"
                        className="ml-2 text-red-500 hover:text-red-700"
                        onClick={() => removeParam(key)}
                      >
                        ✕
                      </button>
                    </div>
                  ))}
                </div>
              )}

              {method !== 'GET' && (
                <div className="mb-4">
                  <label className="block text-sm font-medium mb-1">Request Body (JSON)</label>
                  <textarea
                    className="w-full h-40 border border-gray-300 rounded px-3 py-2 font-mono text-sm"
                    placeholder='{\n  "key": "value"\n}'
                    value={body}
                    onChange={(e) => setBody(e.target.value)}
                  />
                </div>
              )}

              <button
                type="submit"
                className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 disabled:opacity-50"
                disabled={loading || !endpoint}
              >
                {loading ? 'Sending...' : 'Send Request'}
              </button>
            </form>
          </div>

          {/* Response Panel */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Response</h2>

            {responseStatus ? (
              <>
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center">
                    <span className={`inline-block w-3 h-3 rounded-full mr-2 ${
                      responseStatus >= 200 && responseStatus < 300
                        ? 'bg-green-500'
                        : 'bg-red-500'
                    }`}></span>
                    <span className="font-medium">Status: {responseStatus}</span>
                  </div>
                  {responseTime && (
                    <span className="text-sm text-gray-500">{responseTime}ms</span>
                  )}
                </div>

                <div className="border border-gray-300 rounded bg-gray-50 p-4 h-80 overflow-auto">
                  <pre className="text-sm font-mono whitespace-pre-wrap">
                    {JSON.stringify(response, null, 2)}
                  </pre>
                </div>
              </>
            ) : (
              <div className="border border-gray-300 rounded bg-gray-50 p-4 h-80 flex items-center justify-center text-gray-500">
                No response yet
              </div>
            )}
          </div>
        </div>

        {/* History Panel */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Request History</h2>
            {history.length > 0 && (
              <button
                type="button"
                className="text-sm text-red-600 hover:text-red-800"
                onClick={clearHistory}
              >
                Clear History
              </button>
            )}
          </div>

          {history.length === 0 ? (
            <p className="text-gray-500">No request history yet</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Endpoint</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {history.map((item, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(item.timestamp).toLocaleTimeString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          item.method === 'GET'
                            ? 'bg-blue-100 text-blue-800'
                            : item.method === 'POST'
                              ? 'bg-green-100 text-green-800'
                              : item.method === 'PUT'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-red-100 text-red-800'
                        }`}>
                          {item.method}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.endpoint}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          item.status >= 200 && item.status < 300
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {item.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <button
                          className="text-purple-600 hover:text-purple-900"
                          onClick={() => loadFromHistory(item)}
                        >
                          Load
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </EnhancedAdminLayout>
  );
}

