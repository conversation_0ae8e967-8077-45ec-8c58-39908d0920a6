/**
 * Privacy Aware Display Component
 * Applies privacy settings to user information display
 */

import React from 'react';
import { Box, Typography, Chip } from '@mui/material';
import { DISPLAY_NAME_OPTIONS } from '@/config';

// Privacy-aware name display
export const PrivacyAwareName = ({ 
  user, 
  viewerUser, 
  context = 'general',
  variant = 'body1',
  showPrivacyIndicator = false 
}) => {
  const getDisplayName = () => {
    if (!user) return 'Unknown User';
    
    // If viewing own profile, always show full name
    if (viewerUser?.id === user.id) {
      return user.fullName || user.name || 'Your Profile';
    }

    const preference = user.displayNamePreference || 'FIRST_NAME';
    const option = DISPLAY_NAME_OPTIONS[preference];

    switch (preference) {
      case 'FULL_NAME':
        return user.fullName || user.name || 'User';
        
      case 'FIRST_NAME':
        const firstName = user.fullName?.split(' ')[0] || user.name?.split(' ')[0] || 'User';
        return firstName;
        
      case 'PROFILE_ID':
        const gender = user.gender?.toLowerCase() === 'female' ? 'F' : 'M';
        return `Profile ${gender}${user.id || '1234'}`;
        
      case 'ANONYMOUS':
        return 'Someone';
        
      default:
        return user.fullName?.split(' ')[0] || 'User';
    }
  };

  const displayName = getDisplayName();
  const privacyOption = DISPLAY_NAME_OPTIONS[user?.displayNamePreference || 'FIRST_NAME'];

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      <Typography variant={variant}>
        {displayName}
      </Typography>
      {showPrivacyIndicator && privacyOption && (
        <Chip
          label={privacyOption.icon}
          size="small"
          sx={{
            fontSize: '0.7rem',
            height: 20,
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            color: '#667eea'
          }}
          title={`Privacy: ${privacyOption.privacy}`}
        />
      )}
    </Box>
  );
};

// Privacy-aware content display
export const PrivacyAwareContent = ({ 
  content, 
  contentType, 
  user, 
  viewerUser, 
  fallbackText = 'Hidden for privacy',
  children 
}) => {
  const canView = checkContentVisibility(contentType, user, viewerUser);

  if (!canView) {
    return (
      <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
        {fallbackText}
      </Typography>
    );
  }

  return children || <Typography>{content}</Typography>;
};

// Privacy visibility checker
const checkContentVisibility = (contentType, targetUser, viewerUser) => {
  if (!targetUser || !viewerUser) return false;
  
  // Users can always view their own content
  if (viewerUser.id === targetUser.id) return true;
  
  const privacySetting = targetUser.privacySettings?.[`${contentType}Privacy`] || 'ALL_USERS';
  
  switch (privacySetting) {
    case 'ALL_USERS':
      return true;
      
    case 'PREMIUM_USERS':
      return viewerUser.isPremium;
      
    case 'ACCEPTED_INTEREST':
      // Check if there's an accepted interest between users
      const hasAcceptedInterest = viewerUser.acceptedInterests?.includes(targetUser.id) ||
                                 targetUser.acceptedInterests?.includes(viewerUser.id);
      return viewerUser.isPremium && hasAcceptedInterest;
      
    case 'HIDDEN':
      return false;
      
    default:
      return false;
  }
};

// Privacy status indicator
export const PrivacyStatusIndicator = ({ user, compact = false }) => {
  if (!user?.displayNamePreference) return null;
  
  const option = DISPLAY_NAME_OPTIONS[user.displayNamePreference];
  
  if (compact) {
    return (
      <Chip
        label={option.icon}
        size="small"
        sx={{
          fontSize: '0.7rem',
          height: 18,
          backgroundColor: getPrivacyColor(option.privacy),
          color: 'white'
        }}
        title={`Privacy Level: ${option.privacy}`}
      />
    );
  }

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      <Typography variant="body2" sx={{ fontSize: '1.2rem' }}>
        {option.icon}
      </Typography>
      <Box>
        <Typography variant="caption" color="text.secondary">
          Privacy Level
        </Typography>
        <Typography variant="body2" fontWeight="600">
          {option.privacy}
        </Typography>
      </Box>
    </Box>
  );
};

// Get privacy level color
const getPrivacyColor = (level) => {
  switch (level) {
    case 'Low': return '#F44336';
    case 'Medium': return '#FF9800';
    case 'High': return '#4CAF50';
    case 'Maximum': return '#9C27B0';
    default: return '#9E9E9E';
  }
};

// Privacy-aware profile card
export const PrivacyAwareProfileCard = ({ user, viewerUser, children, ...props }) => {
  return (
    <Box {...props}>
      {/* Privacy indicator in top-right corner */}
      <Box sx={{ position: 'relative' }}>
        <Box sx={{ position: 'absolute', top: 8, right: 8, zIndex: 1 }}>
          <PrivacyStatusIndicator user={user} compact />
        </Box>
        {children}
      </Box>
    </Box>
  );
};

// Privacy settings summary
export const PrivacySettingsSummary = ({ settings }) => {
  if (!settings) return null;

  const displayOption = DISPLAY_NAME_OPTIONS[settings.displayNamePreference || 'FIRST_NAME'];
  
  const privacyStats = {
    public: 0,
    premium: 0,
    restricted: 0,
    hidden: 0
  };

  // Count privacy levels
  Object.keys(settings).forEach(key => {
    if (key.endsWith('Privacy')) {
      switch (settings[key]) {
        case 'ALL_USERS':
          privacyStats.public++;
          break;
        case 'PREMIUM_USERS':
          privacyStats.premium++;
          break;
        case 'ACCEPTED_INTEREST':
          privacyStats.restricted++;
          break;
        case 'HIDDEN':
          privacyStats.hidden++;
          break;
      }
    }
  });

  return (
    <Box>
      <Typography variant="subtitle2" fontWeight="600" gutterBottom>
        Privacy Summary
      </Typography>
      
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
        <Typography variant="h4">{displayOption.icon}</Typography>
        <Box>
          <Typography variant="body2" fontWeight="600">
            {displayOption.label}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {displayOption.privacy} Privacy Level
          </Typography>
        </Box>
      </Box>

      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
        {privacyStats.public > 0 && (
          <Chip
            label={`${privacyStats.public} Public`}
            size="small"
            sx={{ backgroundColor: '#4CAF50', color: 'white' }}
          />
        )}
        {privacyStats.premium > 0 && (
          <Chip
            label={`${privacyStats.premium} Premium Only`}
            size="small"
            sx={{ backgroundColor: '#FF9800', color: 'white' }}
          />
        )}
        {privacyStats.restricted > 0 && (
          <Chip
            label={`${privacyStats.restricted} Restricted`}
            size="small"
            sx={{ backgroundColor: '#F44336', color: 'white' }}
          />
        )}
        {privacyStats.hidden > 0 && (
          <Chip
            label={`${privacyStats.hidden} Hidden`}
            size="small"
            sx={{ backgroundColor: '#9E9E9E', color: 'white' }}
          />
        )}
      </Box>
    </Box>
  );
};

export default {
  PrivacyAwareName,
  PrivacyAwareContent,
  PrivacyStatusIndicator,
  PrivacyAwareProfileCard,
  PrivacySettingsSummary
};
