/**
 * Smart Call Button Component
 * Enterprise-grade contact reveal with security and fraud prevention
 * Cross-platform support: Web, Android, iOS
 * Advanced security features and user protection
 */

import React, { useState, useEffect } from 'react';
import {
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  Chip,
  Alert,
  CircularProgress,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Phone as PhoneIcon,
  Security as SecurityIcon,
  Premium as PremiumIcon,
  AccessTime as AccessTimeIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { contactApi, contactUtils } from '@/services/contactApiService';

// Security event tracking
const trackSecurityEvent = (eventType, data) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventType, {
      event_category: 'Contact_Security',
      event_label: data.reason || 'unknown',
      custom_parameters: {
        target_user_id: data.targetUserId,
        risk_score: data.riskScore,
        platform: data.platform || 'WEB'
      }
    });
  }
  console.log(`Contact Security Event: ${eventType}`, data);
};

const SmartCallButton = ({
  targetUserId,
  targetUserName = 'User',
  variant = 'contained',
  size = 'medium',
  fullWidth = false,
  showLabel = true,
  onCallInitiated = () => {},
  onError = () => {}
}) => {
  const [loading, setLoading] = useState(false);
  const [canAccess, setCanAccess] = useState(null);
  const [showContactDialog, setShowContactDialog] = useState(false);
  const [contactDetails, setContactDetails] = useState(null);
  const [accessError, setAccessError] = useState(null);

  // Check access permission on component mount
  useEffect(() => {
    checkContactAccess();
  }, [targetUserId]);

  const checkContactAccess = async () => {
    try {
      const result = await contactApi.canAccessContact(targetUserId);
      setCanAccess(result.canAccess);
      if (!result.canAccess) {
        setAccessError(result);
      }
    } catch (error) {
      console.error('Error checking contact access:', error);
      setCanAccess(false);
      setAccessError({
        reason: 'SERVER_ERROR',
        message: 'Unable to check contact access'
      });
    }
  };

  const handleCallClick = async () => {
    if (!canAccess) {
      // Show upgrade or error dialog
      setShowContactDialog(true);
      return;
    }

    setLoading(true);
    try {
      // Get platform info
      const platform = getPlatform();

      // Reveal contact details
      const result = await contactApi.revealContact(targetUserId, platform);

      if (result.success) {
        setContactDetails(result);
        setShowContactDialog(true);

        // Track call initiation
        onCallInitiated({
          targetUserId,
          contactNumber: result.contactNumber,
          platform
        });

        // Auto-open dialer after short delay
        setTimeout(() => {
          openNativeDialer(result.dialerUrl);
        }, 1000);
      } else {
        setAccessError(result);
        setShowContactDialog(true);
        onError(result);
      }
    } catch (error) {
      console.error('Error revealing contact:', error);

      // Track security events
      if (error.message.includes('security') || error.message.includes('risk')) {
        trackSecurityEvent('contact_access_blocked', {
          targetUserId,
          reason: error.message,
          riskScore: error.riskScore,
          platform: getPlatform()
        });
      } else if (error.message.includes('premium') || error.message.includes('subscription')) {
        trackSecurityEvent('premium_upgrade_required', {
          targetUserId,
          reason: error.message,
          platform: getPlatform()
        });
      }

      setAccessError({
        reason: error.message.includes('security') ? 'SECURITY_BLOCK' :
                error.message.includes('premium') ? 'PREMIUM_REQUIRED' : 'SERVER_ERROR',
        message: error.message || 'Unable to reveal contact details',
        riskScore: error.riskScore,
        requiresManualReview: error.requiresManualReview
      });
      setShowContactDialog(true);
      onError(error);
    } finally {
      setLoading(false);
    }
  };

  const openNativeDialer = (dialerUrl) => {
    try {
      // For web browsers
      if (typeof window !== 'undefined') {
        window.location.href = dialerUrl;
      }

      // For React Native (if using WebView)
      if (window.ReactNativeWebView) {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'OPEN_DIALER',
          url: dialerUrl
        }));
      }
    } catch (error) {
      console.error('Error opening native dialer:', error);
    }
  };

  const getPlatform = () => {
    if (typeof window === 'undefined') return 'WEB';

    const userAgent = window.navigator.userAgent;
    if (/Android/i.test(userAgent)) return 'ANDROID';
    if (/iPhone|iPad|iPod/i.test(userAgent)) return 'IOS';
    return 'WEB';
  };

  const getCallAvailabilityText = (availability) => {
    switch (availability) {
      case 'BUSINESS_HOURS': return '9 AM - 6 PM';
      case 'EVENING_ONLY': return '6 PM - 10 PM';
      case 'WEEKEND_ONLY': return 'Weekends Only';
      default: return 'Anytime';
    }
  };

  const getAccessErrorIcon = (reason) => {
    switch (reason) {
      case 'PREMIUM_REQUIRED': return <PremiumIcon color="warning" />;
      case 'MUTUAL_INTEREST_REQUIRED': return '💕';
      case 'CALLS_DISABLED': return <SecurityIcon color="error" />;
      default: return <SecurityIcon color="info" />;
    }
  };

  const handleUpgrade = () => {
    // Navigate to premium upgrade page
    window.location.href = '/premium/upgrade';
  };

  const handleCloseDialog = () => {
    setShowContactDialog(false);
    setContactDetails(null);
    setAccessError(null);
  };

  return (
    <>
      <Button
        variant={variant}
        size={size}
        fullWidth={fullWidth}
        startIcon={loading ? <CircularProgress size={16} /> : <PhoneIcon />}
        onClick={handleCallClick}
        disabled={loading}
        color={canAccess ? 'primary' : 'secondary'}
        sx={{
          minWidth: showLabel ? 120 : 48,
          ...(canAccess === false && {
            backgroundColor: 'warning.light',
            '&:hover': {
              backgroundColor: 'warning.main'
            }
          })
        }}
      >
        {showLabel && (loading ? 'Connecting...' : 'Call')}
      </Button>

      {/* Contact Reveal Dialog */}
      <Dialog
        open={showContactDialog}
        onClose={handleCloseDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <PhoneIcon />
            <Typography variant="h6">
              {contactDetails ? 'Contact Details' : 'Call Access'}
            </Typography>
          </Box>
          <IconButton onClick={handleCloseDialog} size="small">
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent>
          {contactDetails ? (
            // Success - Show contact details
            <Box>
              <Alert severity="success" sx={{ mb: 2 }}>
                Contact details revealed successfully!
              </Alert>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Calling: {contactDetails.contactOwnerName}
                </Typography>
                <Typography variant="h5" sx={{ fontFamily: 'monospace', mb: 1 }}>
                  {contactDetails.contactNumber}
                </Typography>
                <Chip
                  icon={<AccessTimeIcon />}
                  label={`Available: ${getCallAvailabilityText(contactDetails.callAvailability)}`}
                  size="small"
                  color="info"
                />
              </Box>

              <Alert severity="info">
                Your phone's dialer will open automatically. If it doesn't,
                tap the number above to call.
              </Alert>
            </Box>
          ) : accessError ? (
            // Error - Show access restrictions
            <Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                {getAccessErrorIcon(accessError.reason)}
                <Typography variant="h6">
                  {accessError.reason === 'PREMIUM_REQUIRED' ? 'Premium Feature' : 'Access Restricted'}
                </Typography>
              </Box>

              <Typography variant="body1" sx={{ mb: 2 }}>
                {accessError.message}
              </Typography>

              {accessError.reason === 'PREMIUM_REQUIRED' && (
                <Alert severity="warning">
                  Upgrade to Premium to access contact details and call users directly.
                </Alert>
              )}

              {accessError.reason === 'MUTUAL_INTEREST_REQUIRED' && (
                <Alert severity="info">
                  Both you and {targetUserName} need to show interest before contact details can be shared.
                </Alert>
              )}
            </Box>
          ) : null}
        </DialogContent>

        <DialogActions>
          {accessError?.reason === 'PREMIUM_REQUIRED' && (
            <Button
              variant="contained"
              color="warning"
              startIcon={<PremiumIcon />}
              onClick={handleUpgrade}
            >
              Upgrade to Premium
            </Button>
          )}

          {contactDetails && (
            <Button
              variant="outlined"
              startIcon={<PhoneIcon />}
              onClick={() => openNativeDialer(contactDetails.dialerUrl)}
            >
              Open Dialer
            </Button>
          )}

          <Button onClick={handleCloseDialog}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default SmartCallButton;
