# Node.js dependencies
node_modules/
*/node_modules/
**/node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnp/
.pnp.js
package-lock.json
yarn.lock

# Next.js build output
.next/
out/
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Debug logs
logs/
*.log

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
*.sublime-project
*.sublime-workspace

# Testing
coverage/
.nyc_output/

# Production build files
/build
/dist

# Misc
.DS_Store
*.pem

# Typescript
*.tsbuildinfo
next-env.d.ts

# Prisma
prisma/*.db
prisma/migrations/

# Redis
dump.rdb

# Temporary files
tmp/
temp/

# Cache
.cache/
.eslintcache

# Local Netlify folder
.netlify

# Vercel
.vercel

# PWA files
public/sw.js
public/workbox-*.js

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Large directories and files
uploads/
*/uploads/
**/uploads/
backup-old-files/
test-results/
test-screenshots/

# Git files
.git/
*.orig

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini
