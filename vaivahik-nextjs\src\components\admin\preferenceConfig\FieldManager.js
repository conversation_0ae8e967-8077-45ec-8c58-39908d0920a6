import { useState } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  <PERSON>alog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  IconButton,
  InputLabel,
  List,
  ListItem,
  ListItemIcon,
  ListItemSecondaryAction,
  ListItemText,
  MenuItem,
  Paper,
  Select,
  Switch,
  Tab,
  Tabs,
  TextField,
  Tooltip,
  Typography
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  FilterList as FilterIcon,
  Settings as SettingsIcon,
  List as ListIcon,
  TextFields as TextFieldsIcon,
  Numbers as NumbersIcon,
  CheckBox as CheckBoxIcon,
  TuneRounded as TuneIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import axiosInstance from '@/utils/axiosConfig';
import OptionManager from './OptionManager';

const FIELD_TYPES = [
  { value: 'TEXT', label: 'Text', icon: <TextFieldsIcon /> },
  { value: 'NUMBER', label: 'Number', icon: <NumbersIcon /> },
  { value: 'RANGE', label: 'Range', icon: <TuneIcon /> },
  { value: 'SELECT', label: 'Select (Single)', icon: <FilterIcon /> },
  { value: 'MULTI_SELECT', label: 'Select (Multiple)', icon: <ListIcon /> },
  { value: 'BOOLEAN', label: 'Boolean', icon: <CheckBoxIcon /> }
];

const FieldManager = ({ fields, categories, refreshData }) => {
  const [openDialog, setOpenDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [optionsDialog, setOptionsDialog] = useState(false);
  const [currentField, setCurrentField] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [formData, setFormData] = useState({
    name: '',
    displayName: '',
    description: '',
    fieldType: 'TEXT',
    displayOrder: 0,
    isActive: true,
    isRequired: false,
    isSearchable: true,
    isMatchCriteria: true,
    defaultValue: '',
    validationRules: '',
    minValue: null,
    maxValue: null,
    stepValue: null,
    categoryId: '',
    options: []
  });
  const [errors, setErrors] = useState({});

  // Filter fields by category
  const filteredFields = selectedCategory === 'all'
    ? fields
    : fields.filter(field => field.categoryId === selectedCategory);

  // Group fields by category
  const fieldsByCategory = {};
  filteredFields.forEach(field => {
    const categoryId = field.categoryId;
    if (!fieldsByCategory[categoryId]) {
      fieldsByCategory[categoryId] = [];
    }
    fieldsByCategory[categoryId].push(field);
  });

  // Open dialog for creating a new field
  const handleAddField = () => {
    setCurrentField(null);
    setFormData({
      name: '',
      displayName: '',
      description: '',
      fieldType: 'TEXT',
      displayOrder: 0,
      isActive: true,
      isRequired: false,
      isSearchable: true,
      isMatchCriteria: true,
      defaultValue: '',
      validationRules: '',
      minValue: null,
      maxValue: null,
      stepValue: null,
      categoryId: selectedCategory !== 'all' ? selectedCategory : '',
      options: []
    });
    setErrors({});
    setOpenDialog(true);
  };

  // Open dialog for editing an existing field
  const handleEditField = (field) => {
    setCurrentField(field);
    setFormData({
      name: field.name,
      displayName: field.displayName,
      description: field.description || '',
      fieldType: field.fieldType,
      displayOrder: field.displayOrder,
      isActive: field.isActive,
      isRequired: field.isRequired,
      isSearchable: field.isSearchable,
      isMatchCriteria: field.isMatchCriteria,
      defaultValue: field.defaultValue || '',
      validationRules: field.validationRules || '',
      minValue: field.minValue,
      maxValue: field.maxValue,
      stepValue: field.stepValue,
      categoryId: field.categoryId,
      options: field.options || []
    });
    setErrors({});
    setOpenDialog(true);
  };

  // Open dialog for managing options
  const handleManageOptions = (field) => {
    setCurrentField(field);
    setOptionsDialog(true);
  };

  // Open dialog for deleting a field
  const handleDeleteClick = (field) => {
    setCurrentField(field);
    setDeleteDialog(true);
  };

  // Close all dialogs
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setDeleteDialog(false);
    setOptionsDialog(false);
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, checked, type } = e.target;
    
    // Handle different input types
    const newValue = type === 'checkbox' ? checked : value;
    
    setFormData({
      ...formData,
      [name]: newValue
    });
    
    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
    
    // Reset range values when field type changes
    if (name === 'fieldType' && value !== 'RANGE') {
      setFormData(prev => ({
        ...prev,
        minValue: null,
        maxValue: null,
        stepValue: null
      }));
    }
    
    // Reset options when field type changes
    if (name === 'fieldType' && value !== 'SELECT' && value !== 'MULTI_SELECT') {
      setFormData(prev => ({
        ...prev,
        options: []
      }));
    }
  };

  // Validate form data
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name) {
      newErrors.name = 'Name is required';
    } else if (!/^[a-z0-9_]+$/.test(formData.name)) {
      newErrors.name = 'Name must contain only lowercase letters, numbers, and underscores';
    }
    
    if (!formData.displayName) {
      newErrors.displayName = 'Display name is required';
    }
    
    if (!formData.categoryId) {
      newErrors.categoryId = 'Category is required';
    }
    
    if (formData.fieldType === 'RANGE') {
      if (formData.minValue === null || formData.minValue === '') {
        newErrors.minValue = 'Minimum value is required for range fields';
      }
      
      if (formData.maxValue === null || formData.maxValue === '') {
        newErrors.maxValue = 'Maximum value is required for range fields';
      }
      
      if (formData.stepValue === null || formData.stepValue === '') {
        newErrors.stepValue = 'Step value is required for range fields';
      }
      
      if (formData.minValue !== null && formData.maxValue !== null && 
          parseFloat(formData.minValue) >= parseFloat(formData.maxValue)) {
        newErrors.maxValue = 'Maximum value must be greater than minimum value';
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Submit form data
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }
    
    try {
      // Prepare data for submission
      const fieldData = { ...formData };
      
      // Convert numeric strings to numbers
      if (fieldData.fieldType === 'RANGE') {
        fieldData.minValue = parseFloat(fieldData.minValue);
        fieldData.maxValue = parseFloat(fieldData.maxValue);
        fieldData.stepValue = parseFloat(fieldData.stepValue);
      } else {
        // Remove range-specific fields for non-range types
        delete fieldData.minValue;
        delete fieldData.maxValue;
        delete fieldData.stepValue;
      }
      
      if (currentField) {
        // Update existing field
        const response = await axiosInstance.put(
          `/api/admin/preference-config/fields/${currentField.id}`,
          fieldData
        );
        
        if (response.data.success) {
          toast.success('Field updated successfully');
          refreshData();
          handleCloseDialog();
        } else {
          toast.error(response.data.message || 'Failed to update field');
        }
      } else {
        // Create new field
        const response = await axiosInstance.post(
          '/api/admin/preference-config/fields',
          fieldData
        );
        
        if (response.data.success) {
          toast.success('Field created successfully');
          refreshData();
          handleCloseDialog();
        } else {
          toast.error(response.data.message || 'Failed to create field');
        }
      }
    } catch (error) {
      console.error('Error saving field:', error);
      toast.error(error.response?.data?.message || 'An error occurred while saving the field');
    }
  };

  // Delete a field
  const handleDeleteField = async () => {
    try {
      const response = await axiosInstance.delete(
        `/api/admin/preference-config/fields/${currentField.id}`
      );
      
      if (response.data.success) {
        toast.success('Field deleted successfully');
        refreshData();
        handleCloseDialog();
      } else {
        toast.error(response.data.message || 'Failed to delete field');
      }
    } catch (error) {
      console.error('Error deleting field:', error);
      toast.error(error.response?.data?.message || 'An error occurred while deleting the field');
    }
  };

  // Get field type icon
  const getFieldTypeIcon = (fieldType) => {
    const type = FIELD_TYPES.find(type => type.value === fieldType);
    return type ? type.icon : <SettingsIcon />;
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel id="category-filter-label">Filter by Category</InputLabel>
          <Select
            labelId="category-filter-label"
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            label="Filter by Category"
          >
            <MenuItem value="all">All Categories</MenuItem>
            {categories.map((category) => (
              <MenuItem key={category.id} value={category.id}>
                {category.displayName}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleAddField}
        >
          Add Field
        </Button>
      </Box>

      {Object.keys(fieldsByCategory).length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1" color="textSecondary">
            No fields found. {selectedCategory !== 'all' ? 'Try selecting a different category or ' : ''}
            Click "Add Field" to create one.
          </Typography>
        </Paper>
      ) : (
        Object.entries(fieldsByCategory).map(([categoryId, categoryFields]) => {
          const category = categories.find(c => c.id === categoryId);
          return (
            <Card key={categoryId} sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {category ? category.displayName : 'Unknown Category'}
                </Typography>
                <Divider sx={{ mb: 2 }} />
                <List>
                  {categoryFields
                    .sort((a, b) => a.displayOrder - b.displayOrder)
                    .map((field) => (
                      <ListItem key={field.id} divider>
                        <ListItemIcon>
                          {getFieldTypeIcon(field.fieldType)}
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              {field.displayName}
                              {field.isRequired && (
                                <Typography variant="caption" color="primary" sx={{ ml: 1 }}>
                                  (Required)
                                </Typography>
                              )}
                            </Box>
                          }
                          secondary={
                            <>
                              <Typography variant="caption" component="span" color="textSecondary">
                                {field.name} - {FIELD_TYPES.find(t => t.value === field.fieldType)?.label || field.fieldType}
                              </Typography>
                              {field.description && (
                                <Typography variant="body2" color="textSecondary">
                                  {field.description}
                                </Typography>
                              )}
                            </>
                          }
                        />
                        <ListItemSecondaryAction>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={field.isActive}
                                onChange={async (e) => {
                                  try {
                                    const response = await axiosInstance.put(
                                      `/api/admin/preference-config/fields/${field.id}`,
                                      { isActive: e.target.checked }
                                    );
                                    
                                    if (response.data.success) {
                                      toast.success(`Field ${e.target.checked ? 'activated' : 'deactivated'} successfully`);
                                      refreshData();
                                    } else {
                                      toast.error(response.data.message || 'Failed to update field');
                                    }
                                  } catch (error) {
                                    console.error('Error updating field:', error);
                                    toast.error('Failed to update field status');
                                  }
                                }}
                                color="primary"
                              />
                            }
                            label="Active"
                          />
                          {(field.fieldType === 'SELECT' || field.fieldType === 'MULTI_SELECT') && (
                            <Tooltip title="Manage Options">
                              <IconButton
                                edge="end"
                                aria-label="options"
                                onClick={() => handleManageOptions(field)}
                              >
                                <ListIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                          <Tooltip title="Edit">
                            <IconButton
                              edge="end"
                              aria-label="edit"
                              onClick={() => handleEditField(field)}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete">
                            <IconButton
                              edge="end"
                              aria-label="delete"
                              onClick={() => handleDeleteClick(field)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                </List>
              </CardContent>
            </Card>
          );
        })
      )}

      {/* Add/Edit Field Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {currentField ? 'Edit Field' : 'Add Field'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                name="name"
                label="Internal Name"
                value={formData.name}
                onChange={handleInputChange}
                fullWidth
                required
                error={!!errors.name}
                helperText={errors.name || 'Use lowercase letters, numbers, and underscores only'}
                disabled={currentField !== null} // Disable editing name for existing fields
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="displayName"
                label="Display Name"
                value={formData.displayName}
                onChange={handleInputChange}
                fullWidth
                required
                error={!!errors.displayName}
                helperText={errors.displayName}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="description"
                label="Description"
                value={formData.description}
                onChange={handleInputChange}
                fullWidth
                multiline
                rows={2}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required error={!!errors.categoryId}>
                <InputLabel id="category-label">Category</InputLabel>
                <Select
                  labelId="category-label"
                  name="categoryId"
                  value={formData.categoryId}
                  onChange={handleInputChange}
                  label="Category"
                  disabled={currentField !== null} // Disable editing category for existing fields
                >
                  {categories.map((category) => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.displayName}
                    </MenuItem>
                  ))}
                </Select>
                {errors.categoryId && <FormHelperText>{errors.categoryId}</FormHelperText>}
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel id="field-type-label">Field Type</InputLabel>
                <Select
                  labelId="field-type-label"
                  name="fieldType"
                  value={formData.fieldType}
                  onChange={handleInputChange}
                  label="Field Type"
                  disabled={currentField !== null} // Disable editing field type for existing fields
                >
                  {FIELD_TYPES.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{ mr: 1 }}>{type.icon}</Box>
                        {type.label}
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="displayOrder"
                label="Display Order"
                type="number"
                value={formData.displayOrder}
                onChange={handleInputChange}
                fullWidth
                InputProps={{
                  inputProps: { min: 0 }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="defaultValue"
                label="Default Value"
                value={formData.defaultValue}
                onChange={handleInputChange}
                fullWidth
                helperText="JSON format for complex types"
              />
            </Grid>

            {/* Range-specific fields */}
            {formData.fieldType === 'RANGE' && (
              <>
                <Grid item xs={12} sm={4}>
                  <TextField
                    name="minValue"
                    label="Minimum Value"
                    type="number"
                    value={formData.minValue !== null ? formData.minValue : ''}
                    onChange={handleInputChange}
                    fullWidth
                    required
                    error={!!errors.minValue}
                    helperText={errors.minValue}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    name="maxValue"
                    label="Maximum Value"
                    type="number"
                    value={formData.maxValue !== null ? formData.maxValue : ''}
                    onChange={handleInputChange}
                    fullWidth
                    required
                    error={!!errors.maxValue}
                    helperText={errors.maxValue}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    name="stepValue"
                    label="Step Value"
                    type="number"
                    value={formData.stepValue !== null ? formData.stepValue : ''}
                    onChange={handleInputChange}
                    fullWidth
                    required
                    error={!!errors.stepValue}
                    helperText={errors.stepValue}
                    InputProps={{
                      inputProps: { min: 0.01, step: 0.01 }
                    }}
                  />
                </Grid>
              </>
            )}

            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleInputChange}
                    color="primary"
                  />
                }
                label="Active"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    name="isRequired"
                    checked={formData.isRequired}
                    onChange={handleInputChange}
                    color="primary"
                  />
                }
                label="Required"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    name="isSearchable"
                    checked={formData.isSearchable}
                    onChange={handleInputChange}
                    color="primary"
                  />
                }
                label="Searchable"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    name="isMatchCriteria"
                    checked={formData.isMatchCriteria}
                    onChange={handleInputChange}
                    color="primary"
                  />
                }
                label="Use in Matching"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} color="primary" variant="contained">
            {currentField ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog} onClose={handleCloseDialog}>
        <DialogTitle>Delete Field</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the field "{currentField?.displayName}"?
            This will also delete all options and importance settings associated with this field.
            This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleDeleteField} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Options Dialog */}
      {currentField && (
        <Dialog open={optionsDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
          <DialogTitle>
            Manage Options for {currentField.displayName}
          </DialogTitle>
          <DialogContent>
            <OptionManager
              field={currentField}
              refreshData={refreshData}
              onClose={handleCloseDialog}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog} color="primary">
              Close
            </Button>
          </DialogActions>
        </Dialog>
      )}
    </Box>
  );
};

export default FieldManager;
