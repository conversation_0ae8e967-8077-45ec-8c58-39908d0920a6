/**
 * Real-time Metrics API Endpoint
 * Provides live system metrics for monitoring dashboard
 */

// Simple rate limiting
const requestCounts = new Map();

const simpleRateLimit = (ip, limit = 120, windowMs = 60 * 1000) => {
  const now = Date.now();
  const windowStart = now - windowMs;

  if (!requestCounts.has(ip)) {
    requestCounts.set(ip, []);
  }

  const requests = requestCounts.get(ip);
  const validRequests = requests.filter(time => time > windowStart);

  if (validRequests.length >= limit) {
    return { allowed: false, resetTime: Math.min(...validRequests) + windowMs };
  }

  validRequests.push(now);
  requestCounts.set(ip, validRequests);
  return { allowed: true };
};

export default async function handler(req, res) {
  // Apply rate limiting for real-time endpoint
  const rateLimitResult = simpleRateLimit(req.ip || req.connection.remoteAddress);

  if (!rateLimitResult.allowed) {
    return res.status(429).json({
      success: false,
      message: 'Too many requests',
      retryAfter: Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000)
    });
  }

  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    // Generate real-time metrics
    const metrics = generateRealTimeMetrics();

    res.status(200).json({
      success: true,
      ...metrics,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Real-time metrics API Error:', error);

    res.status(500).json({
      success: false,
      message: 'Failed to fetch real-time metrics',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
}

function generateRealTimeMetrics() {
  const now = new Date();
  const hour = now.getHours();
  
  // Simulate realistic patterns based on time of day
  const timeMultiplier = getTimeMultiplier(hour);
  
  return {
    // User metrics
    activeUsers: Math.floor((Math.random() * 100 + 150) * timeMultiplier),
    onlineUsers: Math.floor((Math.random() * 30 + 40) * timeMultiplier),
    newRegistrationsToday: Math.floor(Math.random() * 25 + 15),
    
    // Activity metrics
    matchesToday: Math.floor((Math.random() * 50 + 75) * timeMultiplier),
    messagesToday: Math.floor((Math.random() * 200 + 300) * timeMultiplier),
    profileViewsToday: Math.floor((Math.random() * 500 + 800) * timeMultiplier),
    
    // Revenue metrics
    revenueToday: Math.floor((Math.random() * 5000 + 8000) * timeMultiplier),
    premiumUpgradesToday: Math.floor(Math.random() * 8 + 3),
    
    // Performance metrics
    avgResponseTime: Math.floor(Math.random() * 100 + 200),
    serverLoad: Math.floor(Math.random() * 30 + 40),
    databaseConnections: Math.floor(Math.random() * 20 + 15),
    
    // System health
    systemStatus: 'healthy',
    uptime: '99.9%',
    errorRate: (Math.random() * 0.5).toFixed(2) + '%',
    
    // Real-time activity feed
    recentActivity: generateRecentActivity(),
    
    // Geographic distribution (live)
    liveUsersByLocation: [
      { city: 'Mumbai', count: Math.floor((Math.random() * 50 + 80) * timeMultiplier) },
      { city: 'Pune', count: Math.floor((Math.random() * 30 + 50) * timeMultiplier) },
      { city: 'Nashik', count: Math.floor((Math.random() * 20 + 30) * timeMultiplier) },
      { city: 'Kolhapur', count: Math.floor((Math.random() * 15 + 25) * timeMultiplier) },
      { city: 'Aurangabad', count: Math.floor((Math.random() * 12 + 20) * timeMultiplier) }
    ],
    
    // Current trends
    trendingSearches: [
      'Software Engineer',
      'Doctor',
      'Teacher',
      'Business Owner',
      'Government Job'
    ],
    
    // Live chat metrics
    activeChatSessions: Math.floor((Math.random() * 25 + 35) * timeMultiplier),
    averageResponseTime: Math.floor(Math.random() * 120 + 180) + 's',
    
    // Security metrics
    securityEvents: {
      rateLimitHits: Math.floor(Math.random() * 5),
      suspiciousLogins: Math.floor(Math.random() * 2),
      blockedIPs: Math.floor(Math.random() * 3)
    }
  };
}

function getTimeMultiplier(hour) {
  // Simulate realistic user activity patterns
  if (hour >= 6 && hour <= 9) return 1.2; // Morning peak
  if (hour >= 10 && hour <= 12) return 1.0; // Late morning
  if (hour >= 13 && hour <= 17) return 0.8; // Afternoon
  if (hour >= 18 && hour <= 22) return 1.5; // Evening peak
  if (hour >= 23 || hour <= 5) return 0.3; // Night
  return 1.0;
}

function generateRecentActivity() {
  const activities = [
    'New user registration',
    'Profile verification completed',
    'Match made',
    'Message sent',
    'Premium upgrade',
    'Profile photo uploaded',
    'Interest expressed',
    'Contact details revealed',
    'Success story submitted',
    'Biodata downloaded'
  ];
  
  const locations = ['Mumbai', 'Pune', 'Nashik', 'Kolhapur', 'Aurangabad', 'Solapur'];
  
  const recentActivity = [];
  for (let i = 0; i < 10; i++) {
    const activity = activities[Math.floor(Math.random() * activities.length)];
    const location = locations[Math.floor(Math.random() * locations.length)];
    const minutesAgo = Math.floor(Math.random() * 30);
    
    recentActivity.push({
      id: Date.now() + i,
      activity,
      location,
      timestamp: new Date(Date.now() - minutesAgo * 60 * 1000).toISOString(),
      minutesAgo
    });
  }
  
  return recentActivity.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
}
