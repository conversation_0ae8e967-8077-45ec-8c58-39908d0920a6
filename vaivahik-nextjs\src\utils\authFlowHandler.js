/**
 * Smart Authentication Flow Handler
 * Routes users based on their registration completion status
 */

import { calculateProfileCompletion } from './gamification';

// Registration step mapping
export const REGISTRATION_STEPS = {
  PHONE_VERIFICATION: 0,
  BASIC_DETAILS: 1,
  PERSONAL_INFO: 2,
  E<PERSON><PERSON><PERSON><PERSON>_CAREER: 3,
  LOCATION_BIRTH: 4,
  PROFILE_PHOTO: 5
};

// Profile status mapping
export const PROFILE_STATUS = {
  INCOMPLETE: 'INCOMPLETE',
  PENDING_APPROVAL: 'PENDING_APPROVAL',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  SUSPENDED: 'SUSPENDED'
};

// User journey states
export const USER_STATES = {
  NEW_USER: 'NEW_USER',
  REGISTRATION_INCOMPLETE: 'REGISTRATION_INCOMPLETE',
  REGISTRATION_COMPLETE: 'REGISTRATION_COMPLETE',
  PROFILE_PENDING: 'PROFILE_PENDING',
  PROFILE_APPROVED: 'PROFILE_APPROVED',
  PROFILE_REJECTED: 'PROFILE_REJECTED'
};

/**
 * Determine user state based on profile data
 * @param {Object} userData - User data from API
 * @returns {Object} - User state and routing information
 */
export const determineUserState = (userData) => {
  if (!userData) {
    return {
      state: USER_STATES.NEW_USER,
      redirectTo: '/register',
      message: 'Please complete registration'
    };
  }

  const { profile, profileStatus, photos = [], isVerified } = userData;

  // Check if phone is verified
  if (!isVerified) {
    return {
      state: USER_STATES.NEW_USER,
      redirectTo: '/register',
      step: REGISTRATION_STEPS.PHONE_VERIFICATION,
      message: 'Please verify your phone number'
    };
  }

  // Calculate profile completion
  const completion = calculateProfileCompletion(userData);
  const hasPhotos = photos && photos.length > 0;

  // Determine which step user left off at
  const incompleteStep = getIncompleteRegistrationStep(userData);

  // User state logic
  switch (profileStatus) {
    case PROFILE_STATUS.INCOMPLETE:
      if (incompleteStep !== null) {
        return {
          state: USER_STATES.REGISTRATION_INCOMPLETE,
          redirectTo: '/register',
          step: incompleteStep,
          completion: completion.overall,
          message: `Continue registration from step ${incompleteStep + 1}`
        };
      } else if (!hasPhotos) {
        return {
          state: USER_STATES.REGISTRATION_INCOMPLETE,
          redirectTo: '/register',
          step: REGISTRATION_STEPS.PROFILE_PHOTO,
          completion: completion.overall,
          message: 'Please upload at least one photo to complete your profile'
        };
      } else {
        return {
          state: USER_STATES.REGISTRATION_COMPLETE,
          redirectTo: '/profile/review',
          completion: completion.overall,
          message: 'Please review and submit your profile for approval'
        };
      }

    case PROFILE_STATUS.PENDING_APPROVAL:
      // Note: With auto-approval, this state should rarely occur
      return {
        state: USER_STATES.PROFILE_PENDING,
        redirectTo: '/profile/pending',
        completion: completion.overall,
        message: 'Your profile is under review'
      };

    case PROFILE_STATUS.APPROVED:
    case 'ACTIVE': // Handle both APPROVED and ACTIVE status
      return {
        state: USER_STATES.PROFILE_APPROVED,
        redirectTo: '/website/pages/dashboard',
        completion: completion.overall,
        message: 'Welcome to your dashboard'
      };

    case PROFILE_STATUS.REJECTED:
      return {
        state: USER_STATES.PROFILE_REJECTED,
        redirectTo: '/profile/rejected',
        completion: completion.overall,
        message: 'Your profile needs updates'
      };

    default:
      return {
        state: USER_STATES.REGISTRATION_INCOMPLETE,
        redirectTo: '/register',
        step: 0,
        completion: completion.overall,
        message: 'Please complete your registration'
      };
  }
};

/**
 * Determine which registration step is incomplete
 * @param {Object} userData - User data
 * @returns {number|null} - Step number or null if all steps complete
 */
export const getIncompleteRegistrationStep = (userData) => {
  const { profile } = userData;

  if (!profile) return REGISTRATION_STEPS.BASIC_DETAILS;

  // Step 1: Basic Details
  const basicFields = ['fullName', 'gender', 'dateOfBirth', 'maritalStatus'];
  const basicComplete = basicFields.every(field => profile[field]);
  if (!basicComplete) return REGISTRATION_STEPS.BASIC_DETAILS;

  // Step 2: Personal Information
  const personalFields = ['height', 'religion', 'caste'];
  const personalComplete = personalFields.every(field => profile[field]);
  if (!personalComplete) return REGISTRATION_STEPS.PERSONAL_INFO;

  // Step 3: Education & Career
  const educationFields = ['education', 'occupation', 'incomeRange'];
  const educationComplete = educationFields.every(field => profile[field]);
  if (!educationComplete) return REGISTRATION_STEPS.EDUCATION_CAREER;

  // Step 4: Location & Birth Details
  const locationFields = ['city', 'state', 'birthPlace'];
  const locationComplete = locationFields.every(field => profile[field]);
  if (!locationComplete) return REGISTRATION_STEPS.LOCATION_BIRTH;

  // All steps complete
  return null;
};

/**
 * Get registration progress information
 * @param {Object} userData - User data
 * @returns {Object} - Progress information
 */
export const getRegistrationProgress = (userData) => {
  const completion = calculateProfileCompletion(userData);
  const incompleteStep = getIncompleteRegistrationStep(userData);
  const totalSteps = Object.keys(REGISTRATION_STEPS).length;

  let currentStep = totalSteps;
  if (incompleteStep !== null) {
    currentStep = incompleteStep;
  }

  return {
    currentStep,
    totalSteps,
    completion: completion.overall,
    sections: completion.sections,
    nextAction: getNextAction(userData)
  };
};

/**
 * Get next action for user
 * @param {Object} userData - User data
 * @returns {Object} - Next action information
 */
export const getNextAction = (userData) => {
  const userState = determineUserState(userData);

  switch (userState.state) {
    case USER_STATES.NEW_USER:
      return {
        action: 'START_REGISTRATION',
        title: 'Start Registration',
        description: 'Create your matrimony profile',
        buttonText: 'Get Started',
        redirectTo: '/register'
      };

    case USER_STATES.REGISTRATION_INCOMPLETE:
      return {
        action: 'CONTINUE_REGISTRATION',
        title: 'Continue Registration',
        description: `Complete step ${userState.step + 1} of your profile`,
        buttonText: 'Continue',
        redirectTo: `/register?step=${userState.step}`
      };

    case USER_STATES.REGISTRATION_COMPLETE:
      return {
        action: 'SUBMIT_PROFILE',
        title: 'Submit Profile',
        description: 'Review and submit your profile for approval',
        buttonText: 'Submit for Review',
        redirectTo: '/profile/review'
      };

    case USER_STATES.PROFILE_PENDING:
      return {
        action: 'WAIT_APPROVAL',
        title: 'Profile Under Review',
        description: 'Your profile is being reviewed by our team',
        buttonText: 'View Status',
        redirectTo: '/profile/pending'
      };

    case USER_STATES.PROFILE_APPROVED:
      return {
        action: 'ACCESS_DASHBOARD',
        title: 'Explore Matches',
        description: 'Your profile is approved. Start finding matches!',
        buttonText: 'Go to Dashboard',
        redirectTo: '/website/pages/dashboard'
      };

    case USER_STATES.PROFILE_REJECTED:
      return {
        action: 'UPDATE_PROFILE',
        title: 'Update Profile',
        description: 'Your profile needs some updates',
        buttonText: 'Update Profile',
        redirectTo: '/profile/edit'
      };

    default:
      return {
        action: 'START_REGISTRATION',
        title: 'Get Started',
        description: 'Create your matrimony profile',
        buttonText: 'Start Registration',
        redirectTo: '/register'
      };
  }
};

/**
 * Check if user can access a specific route
 * @param {Object} userData - User data
 * @param {string} route - Route to check
 * @returns {Object} - Access information
 */
export const checkRouteAccess = (userData, route) => {
  const userState = determineUserState(userData);

  // Public routes
  const publicRoutes = ['/', '/login', '/register', '/about', '/contact'];
  if (publicRoutes.includes(route)) {
    return { allowed: true };
  }

  // Dashboard access - only for approved profiles
  if (route.startsWith('/dashboard') || route.startsWith('/website/pages/dashboard')) {
    if (userState.state === USER_STATES.PROFILE_APPROVED) {
      return { allowed: true };
    } else {
      return {
        allowed: false,
        redirectTo: userState.redirectTo,
        message: 'Complete your profile to access dashboard'
      };
    }
  }

  // Profile routes
  if (route.startsWith('/profile')) {
    return { allowed: true }; // Users can always access profile routes
  }

  // Default: allow access
  return { allowed: true };
};

/**
 * Get welcome message based on user state
 * @param {Object} userData - User data
 * @returns {string} - Welcome message
 */
export const getWelcomeMessage = (userData) => {
  const userState = determineUserState(userData);
  const { profile } = userData || {};
  const firstName = profile?.fullName?.split(' ')[0] || 'there';

  switch (userState.state) {
    case USER_STATES.NEW_USER:
      return `Welcome! Let's create your matrimony profile.`;

    case USER_STATES.REGISTRATION_INCOMPLETE:
      return `Welcome back, ${firstName}! Let's continue building your profile.`;

    case USER_STATES.REGISTRATION_COMPLETE:
      return `Great job, ${firstName}! Your profile is ready for review.`;

    case USER_STATES.PROFILE_PENDING:
      return `Hi ${firstName}! Your profile is being reviewed.`;

    case USER_STATES.PROFILE_APPROVED:
      return `Welcome back, ${firstName}! Ready to find your perfect match?`;

    case USER_STATES.PROFILE_REJECTED:
      return `Hi ${firstName}! Please update your profile to continue.`;

    default:
      return `Welcome, ${firstName}!`;
  }
};
