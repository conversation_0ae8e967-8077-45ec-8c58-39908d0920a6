// Admin Feature Management JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the feature management UI
    initFeatureManagement();
});

// Global variables
let features = [];
let subscriptionPlans = [];
let currentFeature = null;

// Initialize the feature management UI
async function initFeatureManagement() {
    try {
        // Show loading state
        document.getElementById('feature-list-container').innerHTML = '<div class="text-center p-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">Loading features...</p></div>';
        
        // Fetch features and subscription plans
        await Promise.all([
            fetchFeatures(),
            fetchSubscriptionPlans()
        ]);
        
        // Render the feature list
        renderFeatureList();
        
        // Set up event listeners
        setupEventListeners();
    } catch (error) {
        console.error('Error initializing feature management:', error);
        showAlert('error', 'Failed to initialize feature management. Please try again.');
    }
}

// Fetch all features from the API
async function fetchFeatures() {
    try {
        const response = await fetch('/api/admin/feature-management/features', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            }
        });
        
        if (!response.ok) {
            throw new Error(`Failed to fetch features: ${response.status}`);
        }
        
        const data = await response.json();
        features = data.features;
    } catch (error) {
        console.error('Error fetching features:', error);
        showAlert('error', 'Failed to fetch features. Please try again.');
        features = [];
    }
}

// Fetch all subscription plans from the API
async function fetchSubscriptionPlans() {
    try {
        const response = await fetch('/api/admin/feature-management/subscription-plans', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            }
        });
        
        if (!response.ok) {
            throw new Error(`Failed to fetch subscription plans: ${response.status}`);
        }
        
        const data = await response.json();
        subscriptionPlans = data.plans;
    } catch (error) {
        console.error('Error fetching subscription plans:', error);
        showAlert('error', 'Failed to fetch subscription plans. Please try again.');
        subscriptionPlans = [];
    }
}

// Render the feature list
function renderFeatureList() {
    const container = document.getElementById('feature-list-container');
    
    if (features.length === 0) {
        container.innerHTML = `
            <div class="text-center p-5">
                <p>No features found. Create your first feature to get started.</p>
                <button class="btn btn-primary" id="btn-add-feature">
                    <i class="fas fa-plus"></i> Add Feature
                </button>
            </div>
        `;
        return;
    }
    
    // Group features by category
    const featuresByCategory = {};
    
    features.forEach(feature => {
        if (!featuresByCategory[feature.category]) {
            featuresByCategory[feature.category] = [];
        }
        
        featuresByCategory[feature.category].push(feature);
    });
    
    // Build the HTML
    let html = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h3>Feature Management</h3>
            <button class="btn btn-primary" id="btn-add-feature">
                <i class="fas fa-plus"></i> Add Feature
            </button>
        </div>
    `;
    
    // Add feature cards grouped by category
    Object.keys(featuresByCategory).forEach(category => {
        html += `
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">${category}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
        `;
        
        featuresByCategory[category].forEach(feature => {
            const isActive = feature.isActive ? 'bg-light' : 'bg-light-subtle text-muted';
            
            html += `
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="card h-100 ${isActive}" data-feature-id="${feature.id}">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start">
                                <h5 class="card-title">${feature.displayName}</h5>
                                <div class="form-check form-switch">
                                    <input class="form-check-input feature-toggle" type="checkbox" role="switch" 
                                        id="feature-toggle-${feature.id}" 
                                        data-feature-id="${feature.id}" 
                                        ${feature.isActive ? 'checked' : ''}>
                                </div>
                            </div>
                            <h6 class="card-subtitle mb-2 text-muted">${feature.name}</h6>
                            <p class="card-text">${feature.description || 'No description'}</p>
                            
                            <div class="mt-3">
                                <h6>Access Rules:</h6>
                                <ul class="list-group list-group-flush">
            `;
            
            // Add access rules
            const tiers = ['BASIC', 'VERIFIED', 'PREMIUM'];
            
            tiers.forEach(tier => {
                const rule = feature.accessRules.find(r => r.userTier === tier);
                
                if (rule) {
                    const enabled = rule.isEnabled ? 'text-success' : 'text-danger';
                    const limitText = rule.dailyLimit ? `${rule.dailyLimit} per day` : 
                                     (rule.totalLimit ? `${rule.totalLimit} total` : 'Unlimited');
                    
                    html += `
                        <li class="list-group-item d-flex justify-content-between align-items-center p-2">
                            <span>${tier}</span>
                            <span class="${enabled}">${rule.isEnabled ? 'Enabled' : 'Disabled'}</span>
                            <span>${limitText}</span>
                        </li>
                    `;
                }
            });
            
            html += `
                                </ul>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent">
                            <button class="btn btn-sm btn-outline-primary edit-feature" data-feature-id="${feature.id}">
                                Edit Feature
                            </button>
                            <button class="btn btn-sm btn-outline-secondary edit-access" data-feature-id="${feature.id}">
                                Edit Access Rules
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += `
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// Set up event listeners
function setupEventListeners() {
    // Add feature button
    const addFeatureBtn = document.getElementById('btn-add-feature');
    if (addFeatureBtn) {
        addFeatureBtn.addEventListener('click', showAddFeatureModal);
    }
    
    // Edit feature buttons
    const editFeatureBtns = document.querySelectorAll('.edit-feature');
    editFeatureBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
            const featureId = e.target.getAttribute('data-feature-id');
            showEditFeatureModal(featureId);
        });
    });
    
    // Edit access rules buttons
    const editAccessBtns = document.querySelectorAll('.edit-access');
    editAccessBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
            const featureId = e.target.getAttribute('data-feature-id');
            showEditAccessModal(featureId);
        });
    });
    
    // Feature toggle switches
    const featureToggles = document.querySelectorAll('.feature-toggle');
    featureToggles.forEach(toggle => {
        toggle.addEventListener('change', (e) => {
            const featureId = e.target.getAttribute('data-feature-id');
            const isActive = e.target.checked;
            toggleFeatureStatus(featureId, isActive);
        });
    });
}

// Show the add feature modal
function showAddFeatureModal() {
    // Reset current feature
    currentFeature = null;
    
    // Set modal title
    document.getElementById('featureModalLabel').textContent = 'Add New Feature';
    
    // Clear form fields
    document.getElementById('feature-name').value = '';
    document.getElementById('feature-display-name').value = '';
    document.getElementById('feature-description').value = '';
    document.getElementById('feature-category').value = 'BASIC';
    document.getElementById('feature-active').checked = true;
    
    // Show the modal
    const featureModal = new bootstrap.Modal(document.getElementById('featureModal'));
    featureModal.show();
    
    // Set up form submission
    const featureForm = document.getElementById('feature-form');
    featureForm.removeEventListener('submit', handleFeatureFormSubmit);
    featureForm.addEventListener('submit', handleFeatureFormSubmit);
}

// Show the edit feature modal
function showEditFeatureModal(featureId) {
    // Find the feature
    const feature = features.find(f => f.id === featureId);
    
    if (!feature) {
        showAlert('error', 'Feature not found.');
        return;
    }
    
    // Set current feature
    currentFeature = feature;
    
    // Set modal title
    document.getElementById('featureModalLabel').textContent = 'Edit Feature';
    
    // Fill form fields
    document.getElementById('feature-name').value = feature.name;
    document.getElementById('feature-display-name').value = feature.displayName;
    document.getElementById('feature-description').value = feature.description || '';
    document.getElementById('feature-category').value = feature.category;
    document.getElementById('feature-active').checked = feature.isActive;
    
    // Show the modal
    const featureModal = new bootstrap.Modal(document.getElementById('featureModal'));
    featureModal.show();
    
    // Set up form submission
    const featureForm = document.getElementById('feature-form');
    featureForm.removeEventListener('submit', handleFeatureFormSubmit);
    featureForm.addEventListener('submit', handleFeatureFormSubmit);
}

// Show the edit access rules modal
function showEditAccessModal(featureId) {
    // Find the feature
    const feature = features.find(f => f.id === featureId);
    
    if (!feature) {
        showAlert('error', 'Feature not found.');
        return;
    }
    
    // Set current feature
    currentFeature = feature;
    
    // Set modal title
    document.getElementById('accessModalLabel').textContent = `Edit Access Rules: ${feature.displayName}`;
    
    // Build the form HTML
    let html = '';
    
    // Add tabs for each user tier
    html += `
        <ul class="nav nav-tabs" id="accessTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic-tab-pane" type="button" role="tab" aria-controls="basic-tab-pane" aria-selected="true">Basic</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="verified-tab" data-bs-toggle="tab" data-bs-target="#verified-tab-pane" type="button" role="tab" aria-controls="verified-tab-pane" aria-selected="false">Verified</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="premium-tab" data-bs-toggle="tab" data-bs-target="#premium-tab-pane" type="button" role="tab" aria-controls="premium-tab-pane" aria-selected="false">Premium</button>
            </li>
        </ul>
        <div class="tab-content" id="accessTabsContent">
    `;
    
    // Add tab panes for each user tier
    const tiers = ['BASIC', 'VERIFIED', 'PREMIUM'];
    
    tiers.forEach((tier, index) => {
        const rule = feature.accessRules.find(r => r.userTier === tier) || {
            id: '',
            userTier: tier,
            isEnabled: false,
            dailyLimit: null,
            totalLimit: null,
            limitPeriod: 'DAILY',
            allowedFilters: null,
            upgradeMessage: ''
        };
        
        const active = index === 0 ? 'show active' : '';
        const tierLower = tier.toLowerCase();
        
        html += `
            <div class="tab-pane fade ${active}" id="${tierLower}-tab-pane" role="tabpanel" aria-labelledby="${tierLower}-tab" tabindex="0">
                <div class="p-3">
                    <input type="hidden" id="${tierLower}-rule-id" value="${rule.id || ''}">
                    <input type="hidden" id="${tierLower}-user-tier" value="${tier}">
                    
                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" role="switch" id="${tierLower}-enabled" ${rule.isEnabled ? 'checked' : ''}>
                        <label class="form-check-label" for="${tierLower}-enabled">Enable for ${tier} users</label>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="${tierLower}-limit-period" class="form-label">Limit Period</label>
                            <select class="form-select" id="${tierLower}-limit-period">
                                <option value="DAILY" ${rule.limitPeriod === 'DAILY' ? 'selected' : ''}>Daily</option>
                                <option value="WEEKLY" ${rule.limitPeriod === 'WEEKLY' ? 'selected' : ''}>Weekly</option>
                                <option value="MONTHLY" ${rule.limitPeriod === 'MONTHLY' ? 'selected' : ''}>Monthly</option>
                                <option value="TOTAL" ${rule.limitPeriod === 'TOTAL' ? 'selected' : ''}>Total (Lifetime)</option>
                                <option value="NONE" ${!rule.limitPeriod ? 'selected' : ''}>No Limit</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="${tierLower}-daily-limit" class="form-label">Usage Limit</label>
                            <input type="number" class="form-control" id="${tierLower}-daily-limit" value="${rule.dailyLimit || ''}" min="0" placeholder="Leave empty for unlimited">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="${tierLower}-upgrade-message" class="form-label">Upgrade Message</label>
                        <textarea class="form-control" id="${tierLower}-upgrade-message" rows="2" placeholder="Message shown when users need to upgrade">${rule.upgradeMessage || ''}</textarea>
                    </div>
        `;
        
        // Add subscription plan selector for premium tier
        if (tier === 'PREMIUM' && subscriptionPlans.length > 0) {
            html += `
                <div class="mb-3">
                    <label for="${tierLower}-subscription-plan" class="form-label">Subscription Plan (Optional)</label>
                    <select class="form-select" id="${tierLower}-subscription-plan">
                        <option value="">All Premium Plans</option>
            `;
            
            subscriptionPlans.forEach(plan => {
                const selected = rule.subscriptionPlanId === plan.id ? 'selected' : '';
                html += `<option value="${plan.id}" ${selected}>${plan.name} (${plan.planType})</option>`;
            });
            
            html += `
                    </select>
                    <div class="form-text">If selected, this rule will only apply to users with this specific plan.</div>
                </div>
            `;
        }
        
        // Add allowed filters for search features
        if (feature.name === 'search') {
            let allowedFilters = [];
            
            try {
                if (rule.allowedFilters) {
                    allowedFilters = JSON.parse(rule.allowedFilters);
                }
            } catch (error) {
                console.error('Error parsing allowed filters:', error);
            }
            
            const allFilters = [
                { id: 'age', name: 'Age' },
                { id: 'gender', name: 'Gender' },
                { id: 'city', name: 'City' },
                { id: 'education', name: 'Education' },
                { id: 'income', name: 'Income' },
                { id: 'occupation', name: 'Occupation' },
                { id: 'religion', name: 'Religion' },
                { id: 'caste', name: 'Caste' },
                { id: 'subcaste', name: 'Subcaste' },
                { id: 'maritalStatus', name: 'Marital Status' },
                { id: 'height', name: 'Height' },
                { id: 'diet', name: 'Diet' }
            ];
            
            html += `
                <div class="mb-3">
                    <label class="form-label">Allowed Search Filters</label>
                    <div class="row">
            `;
            
            allFilters.forEach(filter => {
                const checked = allowedFilters.includes(filter.id) ? 'checked' : '';
                
                html += `
                    <div class="col-md-4 mb-2">
                        <div class="form-check">
                            <input class="form-check-input ${tierLower}-filter" type="checkbox" value="${filter.id}" id="${tierLower}-filter-${filter.id}" ${checked}>
                            <label class="form-check-label" for="${tierLower}-filter-${filter.id}">
                                ${filter.name}
                            </label>
                        </div>
                    </div>
                `;
            });
            
            html += `
                    </div>
                </div>
            `;
        }
        
        html += `
                </div>
            </div>
        `;
    });
    
    html += `</div>`;
    
    // Set the form HTML
    document.getElementById('access-form-content').innerHTML = html;
    
    // Show the modal
    const accessModal = new bootstrap.Modal(document.getElementById('accessModal'));
    accessModal.show();
    
    // Set up form submission
    const accessForm = document.getElementById('access-form');
    accessForm.removeEventListener('submit', handleAccessFormSubmit);
    accessForm.addEventListener('submit', handleAccessFormSubmit);
}

// Handle feature form submission
async function handleFeatureFormSubmit(e) {
    e.preventDefault();
    
    // Get form data
    const name = document.getElementById('feature-name').value.trim();
    const displayName = document.getElementById('feature-display-name').value.trim();
    const description = document.getElementById('feature-description').value.trim();
    const category = document.getElementById('feature-category').value;
    const isActive = document.getElementById('feature-active').checked;
    
    // Validate form data
    if (!name || !displayName || !category) {
        showAlert('error', 'Please fill in all required fields.');
        return;
    }
    
    // Prepare request data
    const requestData = {
        name,
        displayName,
        description,
        category,
        isActive
    };
    
    try {
        let response;
        
        if (currentFeature) {
            // Update existing feature
            response = await fetch(`/api/admin/feature-management/features/${currentFeature.id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${getAuthToken()}`
                },
                body: JSON.stringify(requestData)
            });
        } else {
            // Create new feature
            response = await fetch('/api/admin/feature-management/features', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${getAuthToken()}`
                },
                body: JSON.stringify(requestData)
            });
        }
        
        if (!response.ok) {
            throw new Error(`Failed to ${currentFeature ? 'update' : 'create'} feature: ${response.status}`);
        }
        
        // Hide the modal
        const featureModal = bootstrap.Modal.getInstance(document.getElementById('featureModal'));
        featureModal.hide();
        
        // Show success message
        showAlert('success', `Feature ${currentFeature ? 'updated' : 'created'} successfully.`);
        
        // Refresh the feature list
        await fetchFeatures();
        renderFeatureList();
        setupEventListeners();
    } catch (error) {
        console.error('Error saving feature:', error);
        showAlert('error', `Failed to ${currentFeature ? 'update' : 'create'} feature. Please try again.`);
    }
}

// Handle access form submission
async function handleAccessFormSubmit(e) {
    e.preventDefault();
    
    if (!currentFeature) {
        showAlert('error', 'No feature selected.');
        return;
    }
    
    // Prepare access rules
    const accessRules = [];
    const tiers = ['BASIC', 'VERIFIED', 'PREMIUM'];
    
    tiers.forEach(tier => {
        const tierLower = tier.toLowerCase();
        
        const ruleId = document.getElementById(`${tierLower}-rule-id`).value;
        const isEnabled = document.getElementById(`${tierLower}-enabled`).checked;
        const limitPeriod = document.getElementById(`${tierLower}-limit-period`).value;
        const dailyLimit = document.getElementById(`${tierLower}-daily-limit`).value ? 
                          parseInt(document.getElementById(`${tierLower}-daily-limit`).value) : null;
        const upgradeMessage = document.getElementById(`${tierLower}-upgrade-message`).value;
        
        // Get subscription plan for premium tier
        let subscriptionPlanId = null;
        if (tier === 'PREMIUM' && document.getElementById(`${tierLower}-subscription-plan`)) {
            subscriptionPlanId = document.getElementById(`${tierLower}-subscription-plan`).value || null;
        }
        
        // Get allowed filters for search feature
        let allowedFilters = null;
        if (currentFeature.name === 'search') {
            const filterCheckboxes = document.querySelectorAll(`.${tierLower}-filter:checked`);
            allowedFilters = Array.from(filterCheckboxes).map(cb => cb.value);
        }
        
        // Create the rule object
        const rule = {
            id: ruleId || undefined,
            userTier: tier,
            isEnabled,
            dailyLimit: limitPeriod !== 'NONE' ? dailyLimit : null,
            totalLimit: null, // Not implemented in the UI yet
            limitPeriod: limitPeriod !== 'NONE' ? limitPeriod : null,
            allowedFilters,
            upgradeMessage: upgradeMessage || null,
            subscriptionPlanId
        };
        
        accessRules.push(rule);
    });
    
    try {
        // Update access rules
        const response = await fetch(`/api/admin/feature-management/features/${currentFeature.id}/access`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            },
            body: JSON.stringify({ accessRules })
        });
        
        if (!response.ok) {
            throw new Error(`Failed to update access rules: ${response.status}`);
        }
        
        // Hide the modal
        const accessModal = bootstrap.Modal.getInstance(document.getElementById('accessModal'));
        accessModal.hide();
        
        // Show success message
        showAlert('success', 'Access rules updated successfully.');
        
        // Refresh the feature list
        await fetchFeatures();
        renderFeatureList();
        setupEventListeners();
    } catch (error) {
        console.error('Error updating access rules:', error);
        showAlert('error', 'Failed to update access rules. Please try again.');
    }
}

// Toggle feature status
async function toggleFeatureStatus(featureId, isActive) {
    try {
        // Find the feature
        const feature = features.find(f => f.id === featureId);
        
        if (!feature) {
            showAlert('error', 'Feature not found.');
            return;
        }
        
        // Update the feature
        const response = await fetch(`/api/admin/feature-management/features/${featureId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            },
            body: JSON.stringify({
                name: feature.name,
                displayName: feature.displayName,
                description: feature.description,
                category: feature.category,
                isActive
            })
        });
        
        if (!response.ok) {
            throw new Error(`Failed to update feature status: ${response.status}`);
        }
        
        // Show success message
        showAlert('success', `Feature ${isActive ? 'enabled' : 'disabled'} successfully.`);
        
        // Update the feature in the local array
        feature.isActive = isActive;
        
        // Update the UI
        const featureCard = document.querySelector(`.card[data-feature-id="${featureId}"]`);
        if (featureCard) {
            if (isActive) {
                featureCard.classList.remove('bg-light-subtle', 'text-muted');
                featureCard.classList.add('bg-light');
            } else {
                featureCard.classList.remove('bg-light');
                featureCard.classList.add('bg-light-subtle', 'text-muted');
            }
        }
    } catch (error) {
        console.error('Error toggling feature status:', error);
        showAlert('error', 'Failed to update feature status. Please try again.');
        
        // Reset the toggle
        const toggle = document.getElementById(`feature-toggle-${featureId}`);
        if (toggle) {
            toggle.checked = !isActive;
        }
    }
}

// Helper function to get the auth token
function getAuthToken() {
    return localStorage.getItem('adminToken') || '';
}

// Helper function to show an alert
function showAlert(type, message) {
    const alertContainer = document.getElementById('alert-container');
    
    if (!alertContainer) {
        console.error('Alert container not found.');
        return;
    }
    
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    alertContainer.innerHTML = alertHtml;
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = alertContainer.querySelector('.alert');
        if (alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }
    }, 5000);
}
