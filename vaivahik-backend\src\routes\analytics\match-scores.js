const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { authenticateToken } = require('../../middleware/auth.middleware');
const authenticateAdmin = require('../../middleware/adminAuth.middleware');

/**
 * @route POST /api/analytics/match-scores
 * @desc Record match scores between users
 * @access Private (System)
 *
 * This endpoint is primarily for internal use by the matching algorithm
 *
 * Request body:
 * {
 *   userId: string,
 *   targetUserId: string,
 *   score: number,
 *   factorScores: object,
 *   algorithm: string
 * }
 */
router.post('/', function(req, res, next) {
  authenticateToken(req, res, function() {
    authenticateAdmin(req, res, async function() {
  try {
    // Get request data
    const {
      userId,
      targetUserId,
      score,
      factorScores,
      algorithm
    } = req.body;

    // Validate required fields
    if (!userId || !targetUserId || score === undefined || !factorScores || !algorithm) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: userId, targetUserId, score, factorScores, and algorithm are required'
      });
    }

    // Create the match score record
    const matchScore = await prisma.matchScore.create({
      data: {
        userId,
        targetUserId,
        score,
        factorScores,
        algorithm,
      },
    });

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Match score recorded successfully',
      matchScoreId: matchScore.id
    });

  } catch (error) {
    console.error('Error recording match score:', error);
    return res.status(500).json({
      success: false,
      message: 'Error recording match score',
      error: error.message
    });
  }
    });
  });
});

/**
 * @route GET /api/analytics/match-scores/:userId/:targetUserId
 * @desc Get match score between two users
 * @access Private (Admin or Self)
 */
router.get('/:userId/:targetUserId', function(req, res, next) {
  authenticateToken(req, res, async function() {
  try {
    const { userId, targetUserId } = req.params;

    // Check if user is admin or requesting their own data
    if (req.user.id !== userId && req.user.role !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Unauthorized to access this data'
      });
    }

    // Get latest match score
    const matchScore = await prisma.matchScore.findFirst({
      where: {
        userId,
        targetUserId
      },
      orderBy: { timestamp: 'desc' }
    });

    if (!matchScore) {
      return res.status(404).json({
        success: false,
        message: 'Match score not found'
      });
    }

    // Return match score
    return res.status(200).json({
      success: true,
      data: matchScore
    });

  } catch (error) {
    console.error('Error getting match score:', error);
    return res.status(500).json({
      success: false,
      message: 'Error getting match score',
      error: error.message
    });
  }
  });
});

/**
 * @route GET /api/analytics/match-scores/history/:userId/:targetUserId
 * @desc Get match score history between two users
 * @access Private (Admin)
 */
router.get('/history/:userId/:targetUserId', function(req, res, next) {
  authenticateToken(req, res, function() {
    authenticateAdmin(req, res, async function() {
  try {
    const { userId, targetUserId } = req.params;

    // Get match score history
    const matchScores = await prisma.matchScore.findMany({
      where: {
        userId,
        targetUserId
      },
      orderBy: { timestamp: 'desc' }
    });

    // Return match scores
    return res.status(200).json({
      success: true,
      data: matchScores
    });

  } catch (error) {
    console.error('Error getting match score history:', error);
    return res.status(500).json({
      success: false,
      message: 'Error getting match score history',
      error: error.message
    });
  }
    });
  });
});

/**
 * @route GET /api/analytics/match-scores/algorithm-performance
 * @desc Get performance metrics for different algorithm versions
 * @access Private (Admin)
 */
router.get('/algorithm-performance', function(req, res, next) {
  authenticateToken(req, res, function() {
    authenticateAdmin(req, res, async function() {
  try {
    // Get algorithm performance metrics
    const algorithms = await prisma.matchScore.groupBy({
      by: ['algorithm'],
      _avg: {
        score: true
      },
      _count: {
        id: true
      }
    });

    // Return algorithm performance metrics
    return res.status(200).json({
      success: true,
      data: algorithms.map(alg => ({
        algorithm: alg.algorithm,
        averageScore: alg._avg.score,
        count: alg._count.id
      }))
    });

  } catch (error) {
    console.error('Error getting algorithm performance:', error);
    return res.status(500).json({
      success: false,
      message: 'Error getting algorithm performance',
      error: error.message
    });
  }
    });
  });
});

module.exports = router;
