import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  Alert,
  CircularProgress,
  Switch,
  FormControlLabel,
  Tooltip,
  Avatar,
  Stack,
  Divider,
  Paper,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Badge,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Security as SecurityIcon,
  Shield as ShieldIcon,
  Lock as LockIcon,
  Key as KeyIcon,
  Gavel as GavelIcon,
  Visibility as VisibilityIcon,
  Block as BlockIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  ExpandMore as ExpandMoreIcon,
  VpnKey as VpnKeyIcon,
  AdminPanelSettings as AdminPanelSettingsIcon,
  Fingerprint as FingerprintIcon,
  VerifiedUser as VerifiedUserIcon,
  Policy as PolicyIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { adminGet, adminPost, adminPut } from '@/services/adminApiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';

// Dynamic import for EnhancedAdminLayout to avoid SSR issues
const EnhancedAdminLayout = dynamic(() => import('@/components/admin/EnhancedAdminLayout'), {
  ssr: false
});

export default function SecuritySettingsPanel() {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [securityStats, setSecurityStats] = useState({});
  const [authSettings, setAuthSettings] = useState({});
  const [passwordPolicy, setPasswordPolicy] = useState({});
  const [sessionSettings, setSessionSettings] = useState({});
  const [auditSettings, setAuditSettings] = useState({});
  const [threatProtection, setThreatProtection] = useState({});
  const [adminSettings, setAdminSettings] = useState({});
  const [securityLogs, setSecurityLogs] = useState([]);
  const [saveDialogOpen, setSaveDialogOpen] = useState(false);
  const [pendingChanges, setPendingChanges] = useState(false);

  // Security level options
  const securityLevels = [
    { value: 'LOW', label: 'Low Security', color: 'success', description: 'Basic security measures' },
    { value: 'MEDIUM', label: 'Medium Security', color: 'warning', description: 'Balanced security and usability' },
    { value: 'HIGH', label: 'High Security', color: 'error', description: 'Maximum security measures' }
  ];

  useEffect(() => {
    fetchSecurityStats();
    fetchSecuritySettings();
    fetchSecurityLogs();
  }, []);

  const fetchSecurityStats = async () => {
    try {
      // Mock stats for now - can be implemented in backend later
      setSecurityStats({
        totalSecurityEvents: 1250,
        blockedAttempts: 89,
        activeAdmins: 5,
        lastSecurityAudit: new Date().toISOString(),
        systemSecurityScore: 92.5,
        vulnerabilitiesFound: 2,
        securityIncidents: 0
      });
    } catch (error) {
      console.error('Error fetching security stats:', error);
    }
  };

  const fetchSecuritySettings = async () => {
    try {
      setLoading(true);
      
      // Mock settings for now - replace with actual API calls
      setAuthSettings({
        requireEmailVerification: true,
        requirePhoneVerification: true,
        enableTwoFactorAuth: false,
        allowSocialLogin: true,
        maxLoginAttempts: 5,
        accountLockoutDuration: 30, // minutes
        enableCaptcha: true,
        captchaThreshold: 3
      });

      setPasswordPolicy({
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: true,
        passwordExpiryDays: 90,
        preventPasswordReuse: 5,
        enablePasswordStrengthMeter: true
      });

      setSessionSettings({
        sessionTimeoutMinutes: 30,
        maxConcurrentSessions: 3,
        enableSessionTracking: true,
        forceLogoutOnPasswordChange: true,
        rememberMeDuration: 30, // days
        enableDeviceTracking: true
      });

      setAuditSettings({
        enableSecurityAudit: true,
        auditLogRetentionDays: 365,
        enableRealTimeAlerts: true,
        alertThreshold: 'MEDIUM',
        enableAutomatedReports: true,
        reportFrequency: 'WEEKLY'
      });

      setThreatProtection({
        enableRateLimiting: true,
        rateLimitRequests: 100,
        rateLimitWindow: 15, // minutes
        enableIPBlocking: true,
        enableGeoBlocking: false,
        blockedCountries: [],
        enableBruteForceProtection: true,
        enableDDOSProtection: true
      });

      setAdminSettings({
        requireAdminApproval: true,
        adminSessionTimeout: 15, // minutes
        enableAdminAuditLog: true,
        restrictAdminAccess: true,
        allowedAdminIPs: [],
        enableAdminNotifications: true
      });

    } catch (error) {
      console.error('Error fetching security settings:', error);
      toast.error('Error fetching security settings');
    } finally {
      setLoading(false);
    }
  };

  const fetchSecurityLogs = async () => {
    try {
      // Mock security logs
      const mockLogs = [
        {
          id: '1',
          event: 'FAILED_LOGIN_ATTEMPT',
          severity: 'WARNING',
          description: 'Multiple failed login attempts from IP *************',
          timestamp: new Date().toISOString(),
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0...'
        },
        {
          id: '2',
          event: 'ADMIN_LOGIN',
          severity: 'INFO',
          description: 'Admin user logged in successfully',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          ipAddress: '************',
          userAgent: 'Mozilla/5.0...'
        }
      ];
      setSecurityLogs(mockLogs);
    } catch (error) {
      console.error('Error fetching security logs:', error);
    }
  };

  const handleSaveSettings = async () => {
    try {
      // Mock API calls for now
      console.log('Saving security settings:', {
        authSettings,
        passwordPolicy,
        sessionSettings,
        auditSettings,
        threatProtection,
        adminSettings
      });
      
      toast.success('Security settings saved successfully');
      setPendingChanges(false);
      setSaveDialogOpen(false);
    } catch (error) {
      console.error('Error saving security settings:', error);
      toast.error('Error saving security settings');
    }
  };

  const getSeverityChip = (severity) => {
    const config = {
      'INFO': { color: 'info', icon: <InfoIcon /> },
      'WARNING': { color: 'warning', icon: <WarningIcon /> },
      'ERROR': { color: 'error', icon: <ErrorIcon /> },
      'CRITICAL': { color: 'error', icon: <BlockIcon /> }
    };
    
    const severityConfig = config[severity] || config['INFO'];
    
    return (
      <Chip
        icon={severityConfig.icon}
        label={severity}
        color={severityConfig.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const TabPanel = ({ children, value, index, ...other }) => (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`security-tabpanel-${index}`}
      aria-labelledby={`security-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );

  return (
    <EnhancedAdminLayout title="Security Settings Panel">
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Security Settings Panel
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={() => {
                fetchSecurityStats();
                fetchSecuritySettings();
                fetchSecurityLogs();
              }}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={<SaveIcon />}
              onClick={() => setSaveDialogOpen(true)}
              disabled={!pendingChanges}
            >
              Save Changes
            </Button>
          </Box>
        </Box>

        {/* Security Overview Alert */}
        <Alert
          severity={securityStats.systemSecurityScore >= 90 ? 'success' : securityStats.systemSecurityScore >= 70 ? 'warning' : 'error'}
          sx={{ mb: 3 }}
          icon={<ShieldIcon />}
        >
          <Typography variant="h6" gutterBottom>
            System Security Score: {securityStats.systemSecurityScore || 0}%
          </Typography>
          <Typography variant="body2">
            {securityStats.systemSecurityScore >= 90
              ? 'Your system security is excellent. All critical security measures are in place.'
              : securityStats.systemSecurityScore >= 70
              ? 'Your system security is good but could be improved. Review the recommendations below.'
              : 'Your system security needs immediate attention. Please review and update security settings.'
            }
          </Typography>
        </Alert>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Security Events
                    </Typography>
                    <Typography variant="h4" component="div">
                      {securityStats.totalSecurityEvents || 0}
                    </Typography>
                  </Box>
                  <SecurityIcon color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Blocked Attempts
                    </Typography>
                    <Typography variant="h4" component="div">
                      {securityStats.blockedAttempts || 0}
                    </Typography>
                  </Box>
                  <BlockIcon color="error" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Active Admins
                    </Typography>
                    <Typography variant="h4" component="div">
                      {securityStats.activeAdmins || 0}
                    </Typography>
                  </Box>
                  <AdminPanelSettingsIcon color="info" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Security Score
                    </Typography>
                    <Typography variant="h4" component="div">
                      {securityStats.systemSecurityScore?.toFixed(1) || 0}%
                    </Typography>
                  </Box>
                  <AssessmentIcon color="success" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Vulnerabilities
                    </Typography>
                    <Typography variant="h4" component="div">
                      {securityStats.vulnerabilitiesFound || 0}
                    </Typography>
                  </Box>
                  <WarningIcon color="warning" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Incidents
                    </Typography>
                    <Typography variant="h4" component="div">
                      {securityStats.securityIncidents || 0}
                    </Typography>
                  </Box>
                  <ErrorIcon color={securityStats.securityIncidents > 0 ? 'error' : 'success'} sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Tabs */}
        <Paper sx={{ mb: 3 }}>
          <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
            <Tab label="Authentication" icon={<VpnKeyIcon />} />
            <Tab label="Password Policy" icon={<KeyIcon />} />
            <Tab label="Session Management" icon={<FingerprintIcon />} />
            <Tab label="Threat Protection" icon={<ShieldIcon />} />
            <Tab label="Audit & Logging" icon={<VisibilityIcon />} />
            <Tab label="Admin Controls" icon={<AdminPanelSettingsIcon />} />
          </Tabs>
        </Paper>

        {/* Tab Panels */}
        <TabPanel value={activeTab} index={0}>
          {/* Authentication Settings */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Authentication Requirements
                  </Typography>
                  <Stack spacing={2}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={authSettings.requireEmailVerification || false}
                          onChange={(e) => {
                            setAuthSettings({
                              ...authSettings,
                              requireEmailVerification: e.target.checked
                            });
                            setPendingChanges(true);
                          }}
                        />
                      }
                      label="Require Email Verification"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={authSettings.requirePhoneVerification || false}
                          onChange={(e) => {
                            setAuthSettings({
                              ...authSettings,
                              requirePhoneVerification: e.target.checked
                            });
                            setPendingChanges(true);
                          }}
                        />
                      }
                      label="Require Phone Verification"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={authSettings.enableTwoFactorAuth || false}
                          onChange={(e) => {
                            setAuthSettings({
                              ...authSettings,
                              enableTwoFactorAuth: e.target.checked
                            });
                            setPendingChanges(true);
                          }}
                        />
                      }
                      label="Enable Two-Factor Authentication"
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Login Security
                  </Typography>
                  <Stack spacing={3}>
                    <TextField
                      label="Max Login Attempts"
                      type="number"
                      value={authSettings.maxLoginAttempts || 5}
                      onChange={(e) => {
                        setAuthSettings({
                          ...authSettings,
                          maxLoginAttempts: parseInt(e.target.value)
                        });
                        setPendingChanges(true);
                      }}
                      helperText="Number of failed attempts before account lockout"
                    />
                    <TextField
                      label="Account Lockout Duration"
                      type="number"
                      value={authSettings.accountLockoutDuration || 30}
                      onChange={(e) => {
                        setAuthSettings({
                          ...authSettings,
                          accountLockoutDuration: parseInt(e.target.value)
                        });
                        setPendingChanges(true);
                      }}
                      InputProps={{ endAdornment: 'minutes' }}
                      helperText="How long to lock account after max attempts"
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Password Policy Tab */}
        <TabPanel value={activeTab} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Password Requirements
                  </Typography>
                  <Stack spacing={3}>
                    <TextField
                      label="Minimum Password Length"
                      type="number"
                      value={passwordPolicy.minLength || 8}
                      onChange={(e) => {
                        setPasswordPolicy({
                          ...passwordPolicy,
                          minLength: parseInt(e.target.value)
                        });
                        setPendingChanges(true);
                      }}
                    />
                    <Stack spacing={2}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={passwordPolicy.requireUppercase || false}
                            onChange={(e) => {
                              setPasswordPolicy({
                                ...passwordPolicy,
                                requireUppercase: e.target.checked
                              });
                              setPendingChanges(true);
                            }}
                          />
                        }
                        label="Require Uppercase Letters"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={passwordPolicy.requireLowercase || false}
                            onChange={(e) => {
                              setPasswordPolicy({
                                ...passwordPolicy,
                                requireLowercase: e.target.checked
                              });
                              setPendingChanges(true);
                            }}
                          />
                        }
                        label="Require Lowercase Letters"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={passwordPolicy.requireNumbers || false}
                            onChange={(e) => {
                              setPasswordPolicy({
                                ...passwordPolicy,
                                requireNumbers: e.target.checked
                              });
                              setPendingChanges(true);
                            }}
                          />
                        }
                        label="Require Numbers"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={passwordPolicy.requireSpecialChars || false}
                            onChange={(e) => {
                              setPasswordPolicy({
                                ...passwordPolicy,
                                requireSpecialChars: e.target.checked
                              });
                              setPendingChanges(true);
                            }}
                          />
                        }
                        label="Require Special Characters"
                      />
                    </Stack>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Password Management
                  </Typography>
                  <Stack spacing={3}>
                    <TextField
                      label="Password Expiry Days"
                      type="number"
                      value={passwordPolicy.passwordExpiryDays || 90}
                      onChange={(e) => {
                        setPasswordPolicy({
                          ...passwordPolicy,
                          passwordExpiryDays: parseInt(e.target.value)
                        });
                        setPendingChanges(true);
                      }}
                      helperText="Days before password expires (0 = never)"
                    />
                    <TextField
                      label="Prevent Password Reuse"
                      type="number"
                      value={passwordPolicy.preventPasswordReuse || 5}
                      onChange={(e) => {
                        setPasswordPolicy({
                          ...passwordPolicy,
                          preventPasswordReuse: parseInt(e.target.value)
                        });
                        setPendingChanges(true);
                      }}
                      helperText="Number of previous passwords to remember"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={passwordPolicy.enablePasswordStrengthMeter || false}
                          onChange={(e) => {
                            setPasswordPolicy({
                              ...passwordPolicy,
                              enablePasswordStrengthMeter: e.target.checked
                            });
                            setPendingChanges(true);
                          }}
                        />
                      }
                      label="Enable Password Strength Meter"
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Session Management Tab */}
        <TabPanel value={activeTab} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Session Settings
                  </Typography>
                  <Stack spacing={3}>
                    <TextField
                      label="Session Timeout"
                      type="number"
                      value={sessionSettings.sessionTimeoutMinutes || 30}
                      onChange={(e) => {
                        setSessionSettings({
                          ...sessionSettings,
                          sessionTimeoutMinutes: parseInt(e.target.value)
                        });
                        setPendingChanges(true);
                      }}
                      InputProps={{ endAdornment: 'minutes' }}
                      helperText="Automatic logout after inactivity"
                    />
                    <TextField
                      label="Max Concurrent Sessions"
                      type="number"
                      value={sessionSettings.maxConcurrentSessions || 3}
                      onChange={(e) => {
                        setSessionSettings({
                          ...sessionSettings,
                          maxConcurrentSessions: parseInt(e.target.value)
                        });
                        setPendingChanges(true);
                      }}
                      helperText="Maximum sessions per user"
                    />
                    <TextField
                      label="Remember Me Duration"
                      type="number"
                      value={sessionSettings.rememberMeDuration || 30}
                      onChange={(e) => {
                        setSessionSettings({
                          ...sessionSettings,
                          rememberMeDuration: parseInt(e.target.value)
                        });
                        setPendingChanges(true);
                      }}
                      InputProps={{ endAdornment: 'days' }}
                      helperText="How long to remember login"
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Session Controls
                  </Typography>
                  <Stack spacing={2}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={sessionSettings.enableSessionTracking || false}
                          onChange={(e) => {
                            setSessionSettings({
                              ...sessionSettings,
                              enableSessionTracking: e.target.checked
                            });
                            setPendingChanges(true);
                          }}
                        />
                      }
                      label="Enable Session Tracking"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={sessionSettings.forceLogoutOnPasswordChange || false}
                          onChange={(e) => {
                            setSessionSettings({
                              ...sessionSettings,
                              forceLogoutOnPasswordChange: e.target.checked
                            });
                            setPendingChanges(true);
                          }}
                        />
                      }
                      label="Force Logout on Password Change"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={sessionSettings.enableDeviceTracking || false}
                          onChange={(e) => {
                            setSessionSettings({
                              ...sessionSettings,
                              enableDeviceTracking: e.target.checked
                            });
                            setPendingChanges(true);
                          }}
                        />
                      }
                      label="Enable Device Tracking"
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Threat Protection Tab */}
        <TabPanel value={activeTab} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Rate Limiting
                  </Typography>
                  <Stack spacing={3}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={threatProtection.enableRateLimiting || false}
                          onChange={(e) => {
                            setThreatProtection({
                              ...threatProtection,
                              enableRateLimiting: e.target.checked
                            });
                            setPendingChanges(true);
                          }}
                        />
                      }
                      label="Enable Rate Limiting"
                    />
                    <TextField
                      label="Rate Limit Requests"
                      type="number"
                      value={threatProtection.rateLimitRequests || 100}
                      onChange={(e) => {
                        setThreatProtection({
                          ...threatProtection,
                          rateLimitRequests: parseInt(e.target.value)
                        });
                        setPendingChanges(true);
                      }}
                      disabled={!threatProtection.enableRateLimiting}
                      helperText="Maximum requests per window"
                    />
                    <TextField
                      label="Rate Limit Window"
                      type="number"
                      value={threatProtection.rateLimitWindow || 15}
                      onChange={(e) => {
                        setThreatProtection({
                          ...threatProtection,
                          rateLimitWindow: parseInt(e.target.value)
                        });
                        setPendingChanges(true);
                      }}
                      InputProps={{ endAdornment: 'minutes' }}
                      disabled={!threatProtection.enableRateLimiting}
                      helperText="Time window for rate limiting"
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Protection Features
                  </Typography>
                  <Stack spacing={2}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={threatProtection.enableIPBlocking || false}
                          onChange={(e) => {
                            setThreatProtection({
                              ...threatProtection,
                              enableIPBlocking: e.target.checked
                            });
                            setPendingChanges(true);
                          }}
                        />
                      }
                      label="Enable IP Blocking"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={threatProtection.enableBruteForceProtection || false}
                          onChange={(e) => {
                            setThreatProtection({
                              ...threatProtection,
                              enableBruteForceProtection: e.target.checked
                            });
                            setPendingChanges(true);
                          }}
                        />
                      }
                      label="Enable Brute Force Protection"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={threatProtection.enableDDOSProtection || false}
                          onChange={(e) => {
                            setThreatProtection({
                              ...threatProtection,
                              enableDDOSProtection: e.target.checked
                            });
                            setPendingChanges(true);
                          }}
                        />
                      }
                      label="Enable DDoS Protection"
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Audit & Logging Tab */}
        <TabPanel value={activeTab} index={4}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Audit Settings
                  </Typography>
                  <Stack spacing={3}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={auditSettings.enableSecurityAudit || false}
                          onChange={(e) => {
                            setAuditSettings({
                              ...auditSettings,
                              enableSecurityAudit: e.target.checked
                            });
                            setPendingChanges(true);
                          }}
                        />
                      }
                      label="Enable Security Audit"
                    />
                    <TextField
                      label="Audit Log Retention"
                      type="number"
                      value={auditSettings.auditLogRetentionDays || 365}
                      onChange={(e) => {
                        setAuditSettings({
                          ...auditSettings,
                          auditLogRetentionDays: parseInt(e.target.value)
                        });
                        setPendingChanges(true);
                      }}
                      InputProps={{ endAdornment: 'days' }}
                      helperText="How long to keep audit logs"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={auditSettings.enableRealTimeAlerts || false}
                          onChange={(e) => {
                            setAuditSettings({
                              ...auditSettings,
                              enableRealTimeAlerts: e.target.checked
                            });
                            setPendingChanges(true);
                          }}
                        />
                      }
                      label="Enable Real-time Alerts"
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Recent Security Logs
                  </Typography>
                  <List>
                    {securityLogs.slice(0, 5).map((log) => (
                      <ListItem key={log.id}>
                        <ListItemIcon>
                          {getSeverityChip(log.severity)}
                        </ListItemIcon>
                        <ListItemText
                          primary={log.description}
                          secondary={formatDate(log.timestamp)}
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Admin Controls Tab */}
        <TabPanel value={activeTab} index={5}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Admin Access Controls
                  </Typography>
                  <Stack spacing={2}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={adminSettings.requireAdminApproval || false}
                          onChange={(e) => {
                            setAdminSettings({
                              ...adminSettings,
                              requireAdminApproval: e.target.checked
                            });
                            setPendingChanges(true);
                          }}
                        />
                      }
                      label="Require Admin Approval for New Admins"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={adminSettings.enableAdminAuditLog || false}
                          onChange={(e) => {
                            setAdminSettings({
                              ...adminSettings,
                              enableAdminAuditLog: e.target.checked
                            });
                            setPendingChanges(true);
                          }}
                        />
                      }
                      label="Enable Admin Audit Log"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={adminSettings.restrictAdminAccess || false}
                          onChange={(e) => {
                            setAdminSettings({
                              ...adminSettings,
                              restrictAdminAccess: e.target.checked
                            });
                            setPendingChanges(true);
                          }}
                        />
                      }
                      label="Restrict Admin Access by IP"
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Admin Session Settings
                  </Typography>
                  <Stack spacing={3}>
                    <TextField
                      label="Admin Session Timeout"
                      type="number"
                      value={adminSettings.adminSessionTimeout || 15}
                      onChange={(e) => {
                        setAdminSettings({
                          ...adminSettings,
                          adminSessionTimeout: parseInt(e.target.value)
                        });
                        setPendingChanges(true);
                      }}
                      InputProps={{ endAdornment: 'minutes' }}
                      helperText="Admin session timeout (shorter than regular users)"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={adminSettings.enableAdminNotifications || false}
                          onChange={(e) => {
                            setAdminSettings({
                              ...adminSettings,
                              enableAdminNotifications: e.target.checked
                            });
                            setPendingChanges(true);
                          }}
                        />
                      }
                      label="Enable Admin Notifications"
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Save Confirmation Dialog */}
        <Dialog open={saveDialogOpen} onClose={() => setSaveDialogOpen(false)}>
          <DialogTitle>Save Security Settings</DialogTitle>
          <DialogContent>
            <Typography variant="body1" gutterBottom>
              Are you sure you want to save these security settings? This will affect all users on the platform.
            </Typography>
            <Alert severity="warning" sx={{ mt: 2 }}>
              Some changes may require users to re-authenticate or may temporarily affect system performance.
            </Alert>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setSaveDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSaveSettings} variant="contained" color="primary">
              Save Settings
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </EnhancedAdminLayout>
  );
}
