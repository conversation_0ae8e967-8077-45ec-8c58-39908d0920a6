"""
Photo Moderation Service using PyTorch

This module provides functionality for moderating photos using PyTorch models.
It includes NSFW content detection, face detection, and image quality assessment.
"""

import os
import json
import torch
import torchvision
import torchvision.transforms as transforms
from PIL import Image
import numpy as np
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PhotoModerationService:
    """PyTorch-based photo moderation service"""
    
    def __init__(self, config=None):
        """
        Initialize the photo moderation service
        
        Args:
            config (dict): Configuration parameters
        """
        # Default configuration
        self.default_config = {
            'nsfw_model_path': os.path.join(os.path.dirname(__file__), '../../models/nsfw_model.pt'),
            'face_model_path': os.path.join(os.path.dirname(__file__), '../../models/face_detection_model.pt'),
            'device': 'cpu',  # 'cuda' if available
            'image_size': 224,
            'nsfw_threshold': 0.7,
            'operation_mode': 2,  # 0: Manual, 1: Shadow, 2: Limited Auto, 3: Full Auto
            'automation_percentage': 70  # For Limited Auto mode
        }
        
        # Use provided config or default
        self.config = config if config else self.default_config
        
        # Set device (CPU or GPU)
        self.device = torch.device(self.config['device'] if torch.cuda.is_available() and self.config['device'] == 'cuda' else 'cpu')
        
        # Initialize models
        self.nsfw_model = None
        self.face_model = None
        
        # Transformation for images
        self.transform = transforms.Compose([
            transforms.Resize((self.config['image_size'], self.config['image_size'])),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        logger.info(f"Photo moderation service initialized with device: {self.device}")
    
    async def load_models(self):
        """Load the moderation models"""
        try:
            # Load NSFW detection model if not already loaded
            if self.nsfw_model is None:
                if os.path.exists(self.config['nsfw_model_path']):
                    logger.info(f"Loading NSFW model from {self.config['nsfw_model_path']}")
                    self.nsfw_model = torch.jit.load(self.config['nsfw_model_path'], map_location=self.device)
                    self.nsfw_model.eval()
                else:
                    logger.warning(f"NSFW model not found at {self.config['nsfw_model_path']}")
                    # Use a pre-trained model as fallback
                    logger.info("Loading pre-trained model as fallback")
                    self.nsfw_model = torchvision.models.resnet50(pretrained=True)
                    # Modify the last layer for NSFW classification (5 classes)
                    num_ftrs = self.nsfw_model.fc.in_features
                    self.nsfw_model.fc = torch.nn.Linear(num_ftrs, 5)  # 5 classes: drawings, hentai, neutral, porn, sexy
                    self.nsfw_model.to(self.device)
                    self.nsfw_model.eval()
            
            # Load face detection model if not already loaded
            if self.face_model is None:
                if os.path.exists(self.config['face_model_path']):
                    logger.info(f"Loading face detection model from {self.config['face_model_path']}")
                    self.face_model = torch.jit.load(self.config['face_model_path'], map_location=self.device)
                    self.face_model.eval()
                else:
                    logger.warning(f"Face detection model not found at {self.config['face_model_path']}")
                    # Use a pre-trained model as fallback
                    logger.info("Loading pre-trained face detection model as fallback")
                    self.face_model = torchvision.models.detection.facedetection_resnet50_fpn(pretrained=True)
                    self.face_model.to(self.device)
                    self.face_model.eval()
            
            return True
        except Exception as e:
            logger.error(f"Error loading models: {str(e)}")
            return False
    
    async def check_nsfw_content(self, image_path):
        """
        Check if an image contains NSFW content
        
        Args:
            image_path (str): Path to the image file
            
        Returns:
            dict: NSFW detection results
        """
        try:
            # Ensure models are loaded
            if self.nsfw_model is None:
                await self.load_models()
            
            # Open and preprocess the image
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.transform(image).unsqueeze(0).to(self.device)
            
            # Get predictions
            with torch.no_grad():
                outputs = self.nsfw_model(image_tensor)
                probabilities = torch.nn.functional.softmax(outputs, dim=1)[0]
            
            # Convert to list of class probabilities
            class_names = ['drawings', 'hentai', 'neutral', 'porn', 'sexy']
            classifications = [
                {'className': class_name, 'probability': float(prob)}
                for class_name, prob in zip(class_names, probabilities.cpu().numpy())
            ]
            
            # Calculate NSFW probability (porn + sexy + hentai)
            nsfw_classes = ['porn', 'sexy', 'hentai']
            nsfw_probability = sum(
                float(prob) for class_name, prob in zip(class_names, probabilities.cpu().numpy())
                if class_name in nsfw_classes
            )
            
            return {
                'safe': nsfw_probability < self.config['nsfw_threshold'],
                'nsfwProbability': float(nsfw_probability),
                'classifications': classifications
            }
        except Exception as e:
            logger.error(f"Error checking NSFW content: {str(e)}")
            # Return safe by default to avoid blocking legitimate content
            return {
                'safe': True,
                'error': str(e),
                'classifications': []
            }
    
    async def detect_faces(self, image_path):
        """
        Detect faces in an image
        
        Args:
            image_path (str): Path to the image file
            
        Returns:
            dict: Face detection results
        """
        try:
            # Ensure models are loaded
            if self.face_model is None:
                await self.load_models()
            
            # Open and preprocess the image
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.transform(image).unsqueeze(0).to(self.device)
            
            # Get predictions
            with torch.no_grad():
                predictions = self.face_model(image_tensor)
            
            # Process face detections
            if isinstance(predictions, list):
                # For detection models that return a list of dictionaries
                boxes = predictions[0]['boxes'].cpu().numpy()
                scores = predictions[0]['scores'].cpu().numpy()
                
                # Filter by confidence
                confident_detections = scores > 0.5
                boxes = boxes[confident_detections]
                scores = scores[confident_detections]
                
                face_count = len(boxes)
            else:
                # For classification models
                # This is a simplified approach - in practice, you'd use a proper face detection model
                face_count = 1 if torch.max(predictions).item() > 0.5 else 0
                boxes = []
                scores = []
            
            return {
                'faceCount': face_count,
                'multipleFaces': face_count > 1,
                'faceDetails': [
                    {'box': box.tolist(), 'score': float(score)}
                    for box, score in zip(boxes, scores)
                ]
            }
        except Exception as e:
            logger.error(f"Error detecting faces: {str(e)}")
            return {
                'faceCount': 0,
                'multipleFaces': False,
                'error': str(e),
                'faceDetails': []
            }
    
    async def check_image_quality(self, image_path):
        """
        Check the quality of an image
        
        Args:
            image_path (str): Path to the image file
            
        Returns:
            dict: Image quality assessment
        """
        try:
            # Open the image
            image = Image.open(image_path).convert('RGB')
            
            # Get image dimensions
            width, height = image.size
            
            # Check resolution
            resolution = width * height
            is_low_resolution = resolution < 100000  # Less than roughly 316x316
            
            # Convert to numpy for further analysis
            img_np = np.array(image)
            
            # Check brightness
            brightness = np.mean(img_np)
            is_too_dark = brightness < 50
            is_too_bright = brightness > 200
            
            # Check contrast
            contrast = np.std(img_np)
            is_low_contrast = contrast < 20
            
            # Determine overall quality
            if is_low_resolution or is_too_dark or is_too_bright or is_low_contrast:
                quality = 'poor'
            else:
                quality = 'good'
            
            return {
                'quality': quality,
                'width': width,
                'height': height,
                'resolution': resolution,
                'brightness': float(brightness),
                'contrast': float(contrast),
                'issues': {
                    'lowResolution': is_low_resolution,
                    'tooDark': is_too_dark,
                    'tooBright': is_too_bright,
                    'lowContrast': is_low_contrast
                }
            }
        except Exception as e:
            logger.error(f"Error checking image quality: {str(e)}")
            return {
                'quality': 'unknown',
                'error': str(e)
            }
    
    async def moderate_photo(self, image_path):
        """
        Moderate a photo - combines all checks
        
        Args:
            image_path (str): Path to the image file
            
        Returns:
            dict: Moderation results
        """
        try:
            # Run all checks in parallel
            nsfw_check = await self.check_nsfw_content(image_path)
            face_check = await self.detect_faces(image_path)
            quality_check = await self.check_image_quality(image_path)
            
            # Determine if the photo should be approved
            approved = True
            reason = None
            flags = []
            
            if not nsfw_check.get('safe', True):
                approved = False
                reason = 'NSFW content detected'
                flags.append('nsfw_content')
            
            if face_check.get('faceCount', 0) == 0:
                approved = False
                reason = 'No face detected'
                flags.append('no_face')
            
            if face_check.get('multipleFaces', False):
                approved = False
                reason = 'Multiple faces detected'
                flags.append('multiple_faces')
            
            if quality_check.get('quality') == 'poor':
                approved = False
                reason = 'Poor image quality'
                flags.append('poor_quality')
            
            # Create the result
            result = {
                'decision': 'APPROVED' if approved else 'REJECTED',
                'flags': flags,
                'confidence': 100 - (nsfw_check.get('nsfwProbability', 0) * 100) if approved else nsfw_check.get('nsfwProbability', 0) * 100,
                'details': {
                    'nsfw': nsfw_check,
                    'face': face_check,
                    'quality': quality_check
                }
            }
            
            return result
        except Exception as e:
            logger.error(f"Error moderating photo: {str(e)}")
            # Default to manual review if automatic moderation fails
            return {
                'decision': 'PENDING',
                'flags': ['moderation_error'],
                'confidence': 0,
                'details': {
                    'error': str(e)
                }
            }
    
    async def process_photo(self, image_path, photo_id=None):
        """
        Process a photo through the moderation system
        This is the main entry point for the moderation service
        
        Args:
            image_path (str): Path to the image file
            photo_id (str): Optional photo ID if already in database
            
        Returns:
            dict: Processing result with decision
        """
        # Get current operation mode
        operation_mode = self.config['operation_mode']
        
        # If in manual mode (0), skip AI processing
        if operation_mode == 0:
            return {
                'decision': 'PENDING',
                'flags': ['manual_mode'],
                'confidence': 0,
                'details': {'mode': 'Manual'}
            }
        
        # Perform AI moderation
        ai_result = await self.moderate_photo(image_path)
        
        # In shadow mode (1), always return PENDING but still do analysis
        if operation_mode == 1:
            return {
                'decision': 'PENDING',
                'flags': ai_result['flags'],
                'confidence': ai_result['confidence'],
                'details': {
                    **ai_result['details'],
                    'aiDecision': ai_result['decision'],
                    'mode': 'Shadow'
                }
            }
        
        # For limited auto mode (2)
        if operation_mode == 2:
            # Randomly determine if this photo should be auto-processed
            use_automation = np.random.random() * 100 < self.config['automation_percentage']
            
            if not use_automation:
                return {
                    'decision': 'PENDING',
                    'flags': ai_result['flags'],
                    'confidence': ai_result['confidence'],
                    'details': {
                        **ai_result['details'],
                        'aiDecision': ai_result['decision'],
                        'mode': 'Limited Auto (Manual Selected)'
                    }
                }
        }
        
        # For full auto mode (3) or limited auto mode with automation selected
        return ai_result
