import { useState } from 'react';
import {
  Box,
  Typography,
  Chip,
  Paper,
  Grid,
  Divider,
  IconButton,
  Tooltip,
  useTheme
} from '@mui/material';
import {
  SportsBasketball as SportsIcon,
  Brush as ArtIcon,
  MusicNote as MusicIcon,
  MenuBook as ReadingIcon,
  Hiking as OutdoorIcon,
  Computer as TechIcon,
  Interests as InterestsIcon
} from '@mui/icons-material';

// Interest categories for coloring
const INTEREST_CATEGORIES = {
  // Sports & Fitness
  'Sports': { bgcolor: '#e3f2fd', color: '#0d47a1', icon: <SportsIcon fontSize="small" /> },
  'Fitness': { bgcolor: '#e3f2fd', color: '#0d47a1', icon: <SportsIcon fontSize="small" /> },
  'Yoga': { bgcolor: '#e3f2fd', color: '#0d47a1', icon: <SportsIcon fontSize="small" /> },
  'Cricket': { bgcolor: '#e3f2fd', color: '#0d47a1', icon: <SportsIcon fontSize="small" /> },
  'Swimming': { bgcolor: '#e3f2fd', color: '#0d47a1', icon: <SportsIcon fontSize="small" /> },
  'Hiking': { bgcolor: '#e3f2fd', color: '#0d47a1', icon: <SportsIcon fontSize="small" /> },
  'Trekking': { bgcolor: '#e3f2fd', color: '#0d47a1', icon: <SportsIcon fontSize="small" /> },
  'Running': { bgcolor: '#e3f2fd', color: '#0d47a1', icon: <SportsIcon fontSize="small" /> },
  'Gym': { bgcolor: '#e3f2fd', color: '#0d47a1', icon: <SportsIcon fontSize="small" /> },
  'Football': { bgcolor: '#e3f2fd', color: '#0d47a1', icon: <SportsIcon fontSize="small" /> },
  'Badminton': { bgcolor: '#e3f2fd', color: '#0d47a1', icon: <SportsIcon fontSize="small" /> },
  'Tennis': { bgcolor: '#e3f2fd', color: '#0d47a1', icon: <SportsIcon fontSize="small" /> },
  
  // Arts & Creativity
  'Painting': { bgcolor: '#f3e5f5', color: '#6a1b9a', icon: <ArtIcon fontSize="small" /> },
  'Drawing': { bgcolor: '#f3e5f5', color: '#6a1b9a', icon: <ArtIcon fontSize="small" /> },
  'Photography': { bgcolor: '#f3e5f5', color: '#6a1b9a', icon: <ArtIcon fontSize="small" /> },
  'Dancing': { bgcolor: '#f3e5f5', color: '#6a1b9a', icon: <ArtIcon fontSize="small" /> },
  'Singing': { bgcolor: '#f3e5f5', color: '#6a1b9a', icon: <ArtIcon fontSize="small" /> },
  'Writing': { bgcolor: '#f3e5f5', color: '#6a1b9a', icon: <ArtIcon fontSize="small" /> },
  'Sketching': { bgcolor: '#f3e5f5', color: '#6a1b9a', icon: <ArtIcon fontSize="small" /> },
  'Crafts': { bgcolor: '#f3e5f5', color: '#6a1b9a', icon: <ArtIcon fontSize="small" /> },
  'Pottery': { bgcolor: '#f3e5f5', color: '#6a1b9a', icon: <ArtIcon fontSize="small" /> },
  'Playing': { bgcolor: '#f3e5f5', color: '#6a1b9a', icon: <ArtIcon fontSize="small" /> },
  'Poetry': { bgcolor: '#f3e5f5', color: '#6a1b9a', icon: <ArtIcon fontSize="small" /> },
  
  // Entertainment
  'Movies': { bgcolor: '#fff3e0', color: '#e65100', icon: <MusicIcon fontSize="small" /> },
  'Theatre': { bgcolor: '#fff3e0', color: '#e65100', icon: <MusicIcon fontSize="small" /> },
  'Music': { bgcolor: '#fff3e0', color: '#e65100', icon: <MusicIcon fontSize="small" /> },
  'Gaming': { bgcolor: '#fff3e0', color: '#e65100', icon: <MusicIcon fontSize="small" /> },
  'Chess': { bgcolor: '#fff3e0', color: '#e65100', icon: <MusicIcon fontSize="small" /> },
  'Concerts': { bgcolor: '#fff3e0', color: '#e65100', icon: <MusicIcon fontSize="small" /> },
  'TV': { bgcolor: '#fff3e0', color: '#e65100', icon: <MusicIcon fontSize="small" /> },
  'Board': { bgcolor: '#fff3e0', color: '#e65100', icon: <MusicIcon fontSize="small" /> },
  'Carrom': { bgcolor: '#fff3e0', color: '#e65100', icon: <MusicIcon fontSize="small" /> },
  'Card': { bgcolor: '#fff3e0', color: '#e65100', icon: <MusicIcon fontSize="small" /> },
  
  // Knowledge & Learning
  'Reading': { bgcolor: '#e8f5e9', color: '#1b5e20', icon: <ReadingIcon fontSize="small" /> },
  'Learning': { bgcolor: '#e8f5e9', color: '#1b5e20', icon: <ReadingIcon fontSize="small" /> },
  'History': { bgcolor: '#e8f5e9', color: '#1b5e20', icon: <ReadingIcon fontSize="small" /> },
  'Science': { bgcolor: '#e8f5e9', color: '#1b5e20', icon: <ReadingIcon fontSize="small" /> },
  'Technology': { bgcolor: '#e8f5e9', color: '#1b5e20', icon: <ReadingIcon fontSize="small" /> },
  'Coding': { bgcolor: '#e8f5e9', color: '#1b5e20', icon: <ReadingIcon fontSize="small" /> },
  'Blogging': { bgcolor: '#e8f5e9', color: '#1b5e20', icon: <ReadingIcon fontSize="small" /> },
  'Podcasts': { bgcolor: '#e8f5e9', color: '#1b5e20', icon: <ReadingIcon fontSize="small" /> },
  'Documentaries': { bgcolor: '#e8f5e9', color: '#1b5e20', icon: <ReadingIcon fontSize="small" /> },
  
  // Lifestyle
  'Cooking': { bgcolor: '#fbe9e7', color: '#bf360c', icon: <OutdoorIcon fontSize="small" /> },
  'Baking': { bgcolor: '#fbe9e7', color: '#bf360c', icon: <OutdoorIcon fontSize="small" /> },
  'Gardening': { bgcolor: '#fbe9e7', color: '#bf360c', icon: <OutdoorIcon fontSize="small" /> },
  'Traveling': { bgcolor: '#fbe9e7', color: '#bf360c', icon: <OutdoorIcon fontSize="small" /> },
  'Food': { bgcolor: '#fbe9e7', color: '#bf360c', icon: <OutdoorIcon fontSize="small" /> },
  'Interior': { bgcolor: '#fbe9e7', color: '#bf360c', icon: <OutdoorIcon fontSize="small" /> },
  'Fashion': { bgcolor: '#fbe9e7', color: '#bf360c', icon: <OutdoorIcon fontSize="small" /> },
  'Shopping': { bgcolor: '#fbe9e7', color: '#bf360c', icon: <OutdoorIcon fontSize="small" /> },
  'Exploring': { bgcolor: '#fbe9e7', color: '#bf360c', icon: <OutdoorIcon fontSize="small" /> },
  
  // Spiritual & Wellness
  'Meditation': { bgcolor: '#e0f7fa', color: '#006064', icon: <TechIcon fontSize="small" /> },
  'Spirituality': { bgcolor: '#e0f7fa', color: '#006064', icon: <TechIcon fontSize="small" /> },
  'Temple': { bgcolor: '#e0f7fa', color: '#006064', icon: <TechIcon fontSize="small" /> },
  'Religious': { bgcolor: '#e0f7fa', color: '#006064', icon: <TechIcon fontSize="small" /> },
  'Volunteering': { bgcolor: '#e0f7fa', color: '#006064', icon: <TechIcon fontSize="small" /> },
  'Social': { bgcolor: '#e0f7fa', color: '#006064', icon: <TechIcon fontSize="small" /> },
  'Charity': { bgcolor: '#e0f7fa', color: '#006064', icon: <TechIcon fontSize="small" /> }
};

/**
 * Component to display user's hobbies and interests with colorful chips
 * 
 * @param {Object} props
 * @param {Array} props.hobbies - Array of user's hobbies
 * @param {string} props.interests - User's other interests (text)
 * @param {boolean} props.showTitle - Whether to show the section title
 * @param {boolean} props.compact - Whether to show in compact mode
 */
const InterestsDisplay = ({ hobbies = [], interests = '', showTitle = true, compact = false }) => {
  const theme = useTheme();
  
  // Get chip style based on interest category
  const getChipStyle = (hobby) => {
    // Find matching category
    for (const [key, value] of Object.entries(INTEREST_CATEGORIES)) {
      if (hobby.includes(key)) {
        return value;
      }
    }
    
    // Default style
    return {
      bgcolor: theme.palette.grey[200],
      color: theme.palette.text.primary,
      icon: <InterestsIcon fontSize="small" />
    };
  };
  
  // Group hobbies by category
  const groupHobbiesByCategory = () => {
    const groups = {
      sports: [],
      arts: [],
      entertainment: [],
      knowledge: [],
      lifestyle: [],
      spiritual: []
    };
    
    hobbies.forEach(hobby => {
      if (hobby.match(/sports|fitness|yoga|cricket|swimming|hiking|trekking|running|gym|football|badminton|tennis/i)) {
        groups.sports.push(hobby);
      } else if (hobby.match(/painting|drawing|photography|dancing|singing|writing|sketching|crafts|pottery|playing|poetry/i)) {
        groups.arts.push(hobby);
      } else if (hobby.match(/movies|theatre|music|gaming|chess|concerts|tv|board|carrom|card/i)) {
        groups.entertainment.push(hobby);
      } else if (hobby.match(/reading|learning|history|science|technology|coding|blogging|podcasts|documentaries/i)) {
        groups.knowledge.push(hobby);
      } else if (hobby.match(/cooking|baking|gardening|traveling|food|interior|fashion|shopping|exploring/i)) {
        groups.lifestyle.push(hobby);
      } else if (hobby.match(/meditation|spirituality|temple|religious|volunteering|social|charity/i)) {
        groups.spiritual.push(hobby);
      } else {
        // Default to lifestyle if no match
        groups.lifestyle.push(hobby);
      }
    });
    
    return groups;
  };
  
  const hobbyGroups = groupHobbiesByCategory();
  
  if (compact) {
    // Compact display for profile cards
    return (
      <Box sx={{ mt: 1 }}>
        {hobbies.length > 0 && (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
            {hobbies.slice(0, 5).map((hobby, index) => {
              const style = getChipStyle(hobby);
              return (
                <Chip
                  key={index}
                  label={hobby}
                  size="small"
                  icon={style.icon}
                  sx={{
                    bgcolor: style.bgcolor,
                    color: style.color,
                    fontSize: '0.7rem',
                    height: 24
                  }}
                />
              );
            })}
            {hobbies.length > 5 && (
              <Chip
                label={`+${hobbies.length - 5} more`}
                size="small"
                sx={{
                  bgcolor: theme.palette.grey[200],
                  color: theme.palette.text.secondary,
                  fontSize: '0.7rem',
                  height: 24
                }}
              />
            )}
          </Box>
        )}
      </Box>
    );
  }
  
  return (
    <Box sx={{ mb: 3 }}>
      {showTitle && (
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <InterestsIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
          <Typography variant="h6">Hobbies & Interests</Typography>
        </Box>
      )}
      
      {hobbies.length === 0 && !interests && (
        <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
          No hobbies or interests specified
        </Typography>
      )}
      
      {hobbies.length > 0 && (
        <Grid container spacing={2}>
          {/* Sports & Fitness */}
          {hobbyGroups.sports.length > 0 && (
            <Grid item xs={12} sm={6} md={4}>
              <Paper 
                elevation={0} 
                sx={{ 
                  p: 1.5, 
                  bgcolor: '#e3f2fd',
                  borderRadius: 2,
                  height: '100%'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <SportsIcon sx={{ mr: 0.5, color: '#0d47a1', fontSize: 20 }} />
                  <Typography variant="subtitle2" color="#0d47a1">
                    Sports & Fitness
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {hobbyGroups.sports.map((hobby, index) => (
                    <Chip
                      key={index}
                      label={hobby}
                      size="small"
                      sx={{
                        bgcolor: 'rgba(255,255,255,0.7)',
                        color: '#0d47a1',
                        '&:hover': { bgcolor: 'rgba(255,255,255,0.9)' }
                      }}
                    />
                  ))}
                </Box>
              </Paper>
            </Grid>
          )}
          
          {/* Arts & Creativity */}
          {hobbyGroups.arts.length > 0 && (
            <Grid item xs={12} sm={6} md={4}>
              <Paper 
                elevation={0} 
                sx={{ 
                  p: 1.5, 
                  bgcolor: '#f3e5f5',
                  borderRadius: 2,
                  height: '100%'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <ArtIcon sx={{ mr: 0.5, color: '#6a1b9a', fontSize: 20 }} />
                  <Typography variant="subtitle2" color="#6a1b9a">
                    Arts & Creativity
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {hobbyGroups.arts.map((hobby, index) => (
                    <Chip
                      key={index}
                      label={hobby}
                      size="small"
                      sx={{
                        bgcolor: 'rgba(255,255,255,0.7)',
                        color: '#6a1b9a',
                        '&:hover': { bgcolor: 'rgba(255,255,255,0.9)' }
                      }}
                    />
                  ))}
                </Box>
              </Paper>
            </Grid>
          )}
          
          {/* Entertainment */}
          {hobbyGroups.entertainment.length > 0 && (
            <Grid item xs={12} sm={6} md={4}>
              <Paper 
                elevation={0} 
                sx={{ 
                  p: 1.5, 
                  bgcolor: '#fff3e0',
                  borderRadius: 2,
                  height: '100%'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <MusicIcon sx={{ mr: 0.5, color: '#e65100', fontSize: 20 }} />
                  <Typography variant="subtitle2" color="#e65100">
                    Entertainment
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {hobbyGroups.entertainment.map((hobby, index) => (
                    <Chip
                      key={index}
                      label={hobby}
                      size="small"
                      sx={{
                        bgcolor: 'rgba(255,255,255,0.7)',
                        color: '#e65100',
                        '&:hover': { bgcolor: 'rgba(255,255,255,0.9)' }
                      }}
                    />
                  ))}
                </Box>
              </Paper>
            </Grid>
          )}
          
          {/* Knowledge & Learning */}
          {hobbyGroups.knowledge.length > 0 && (
            <Grid item xs={12} sm={6} md={4}>
              <Paper 
                elevation={0} 
                sx={{ 
                  p: 1.5, 
                  bgcolor: '#e8f5e9',
                  borderRadius: 2,
                  height: '100%'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <ReadingIcon sx={{ mr: 0.5, color: '#1b5e20', fontSize: 20 }} />
                  <Typography variant="subtitle2" color="#1b5e20">
                    Knowledge & Learning
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {hobbyGroups.knowledge.map((hobby, index) => (
                    <Chip
                      key={index}
                      label={hobby}
                      size="small"
                      sx={{
                        bgcolor: 'rgba(255,255,255,0.7)',
                        color: '#1b5e20',
                        '&:hover': { bgcolor: 'rgba(255,255,255,0.9)' }
                      }}
                    />
                  ))}
                </Box>
              </Paper>
            </Grid>
          )}
          
          {/* Lifestyle */}
          {hobbyGroups.lifestyle.length > 0 && (
            <Grid item xs={12} sm={6} md={4}>
              <Paper 
                elevation={0} 
                sx={{ 
                  p: 1.5, 
                  bgcolor: '#fbe9e7',
                  borderRadius: 2,
                  height: '100%'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <OutdoorIcon sx={{ mr: 0.5, color: '#bf360c', fontSize: 20 }} />
                  <Typography variant="subtitle2" color="#bf360c">
                    Lifestyle
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {hobbyGroups.lifestyle.map((hobby, index) => (
                    <Chip
                      key={index}
                      label={hobby}
                      size="small"
                      sx={{
                        bgcolor: 'rgba(255,255,255,0.7)',
                        color: '#bf360c',
                        '&:hover': { bgcolor: 'rgba(255,255,255,0.9)' }
                      }}
                    />
                  ))}
                </Box>
              </Paper>
            </Grid>
          )}
          
          {/* Spiritual & Wellness */}
          {hobbyGroups.spiritual.length > 0 && (
            <Grid item xs={12} sm={6} md={4}>
              <Paper 
                elevation={0} 
                sx={{ 
                  p: 1.5, 
                  bgcolor: '#e0f7fa',
                  borderRadius: 2,
                  height: '100%'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <TechIcon sx={{ mr: 0.5, color: '#006064', fontSize: 20 }} />
                  <Typography variant="subtitle2" color="#006064">
                    Spiritual & Wellness
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {hobbyGroups.spiritual.map((hobby, index) => (
                    <Chip
                      key={index}
                      label={hobby}
                      size="small"
                      sx={{
                        bgcolor: 'rgba(255,255,255,0.7)',
                        color: '#006064',
                        '&:hover': { bgcolor: 'rgba(255,255,255,0.9)' }
                      }}
                    />
                  ))}
                </Box>
              </Paper>
            </Grid>
          )}
        </Grid>
      )}
      
      {interests && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Other Interests
          </Typography>
          <Typography variant="body2">
            {interests}
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default InterestsDisplay;
