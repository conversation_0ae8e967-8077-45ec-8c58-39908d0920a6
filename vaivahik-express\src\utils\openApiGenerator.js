/**
 * OpenAPI Documentation Generator
 * 
 * This utility automatically generates OpenAPI documentation for the API.
 * It scans the routes directory and generates documentation based on JSDoc comments.
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');
const swaggerJsdoc = require('swagger-jsdoc');
const logger = require('./logger');

/**
 * Generate OpenAPI specification
 * @returns {Object} OpenAPI specification
 */
function generateOpenApiSpec() {
  try {
    // Define Swagger options
    const options = {
      definition: {
        openapi: '3.0.0',
        info: {
          title: 'Vaivahik API',
          version: '1.0.0',
          description: 'API documentation for Vaivahik Matrimony Platform',
          contact: {
            name: 'Vaivahik Support',
            email: '<EMAIL>',
            url: 'https://vaivahik.com/contact'
          },
          license: {
            name: 'Private',
            url: 'https://vaivahik.com/terms'
          }
        },
        servers: [
          {
            url: process.env.API_URL || 'http://localhost:5000',
            description: 'Development server'
          },
          {
            url: 'https://api.vaivahik.com',
            description: 'Production server'
          }
        ],
        components: {
          securitySchemes: {
            bearerAuth: {
              type: 'http',
              scheme: 'bearer',
              bearerFormat: 'JWT'
            }
          },
          schemas: getCommonSchemas()
        },
        security: [
          {
            bearerAuth: []
          }
        ],
        tags: [
          { name: 'Auth', description: 'Authentication endpoints' },
          { name: 'Users', description: 'User management endpoints' },
          { name: 'Profiles', description: 'Profile management endpoints' },
          { name: 'Matches', description: 'Match-related endpoints' },
          { name: 'Messages', description: 'Messaging endpoints' },
          { name: 'Notifications', description: 'Notification endpoints' },
          { name: 'Search', description: 'Search endpoints' },
          { name: 'Subscriptions', description: 'Subscription management endpoints' },
          { name: 'Payments', description: 'Payment processing endpoints' },
          { name: 'Documents', description: 'Document management endpoints' },
          { name: 'Reports', description: 'Reporting endpoints' },
          { name: 'Admin', description: 'Admin-only endpoints' }
        ]
      },
      // Path to the API routes with JSDoc comments
      apis: [
        path.join(__dirname, '../routes/**/*.js'),
        path.join(__dirname, '../controllers/**/*.js'),
        path.join(__dirname, '../models/**/*.js')
      ]
    };

    // Generate OpenAPI specification
    const openApiSpec = swaggerJsdoc(options);
    
    // Add custom paths that might not be captured by JSDoc
    addCustomPaths(openApiSpec);
    
    return openApiSpec;
  } catch (error) {
    logger.error('Error generating OpenAPI specification', { error: error.message });
    throw error;
  }
}

/**
 * Add custom paths to the OpenAPI specification
 * @param {Object} openApiSpec - OpenAPI specification
 */
function addCustomPaths(openApiSpec) {
  // Add health check endpoint
  openApiSpec.paths['/health'] = {
    get: {
      tags: ['System'],
      summary: 'Health check endpoint',
      description: 'Check if the API is running',
      responses: {
        '200': {
          description: 'API is healthy',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  status: {
                    type: 'string',
                    example: 'ok'
                  },
                  timestamp: {
                    type: 'string',
                    format: 'date-time',
                    example: '2023-07-25T12:34:56.789Z'
                  },
                  version: {
                    type: 'string',
                    example: '1.0.0'
                  },
                  environment: {
                    type: 'string',
                    example: 'development'
                  }
                }
              }
            }
          }
        }
      }
    }
  };
}

/**
 * Get common schemas for the OpenAPI specification
 * @returns {Object} Common schemas
 */
function getCommonSchemas() {
  return {
    Error: {
      type: 'object',
      properties: {
        success: {
          type: 'boolean',
          example: false
        },
        message: {
          type: 'string',
          example: 'Error message'
        },
        timestamp: {
          type: 'string',
          format: 'date-time',
          example: '2023-07-25T12:34:56.789Z'
        },
        errors: {
          type: 'object',
          additionalProperties: {
            type: 'string'
          },
          example: {
            email: 'Invalid email format',
            password: 'Password must be at least 6 characters'
          }
        },
        errorCode: {
          type: 'string',
          example: 'VALIDATION_ERROR'
        }
      }
    },
    SuccessResponse: {
      type: 'object',
      properties: {
        success: {
          type: 'boolean',
          example: true
        },
        message: {
          type: 'string',
          example: 'Operation successful'
        },
        timestamp: {
          type: 'string',
          format: 'date-time',
          example: '2023-07-25T12:34:56.789Z'
        },
        data: {
          type: 'object',
          example: {}
        }
      }
    },
    PaginatedResponse: {
      type: 'object',
      properties: {
        success: {
          type: 'boolean',
          example: true
        },
        message: {
          type: 'string',
          example: 'Operation successful'
        },
        timestamp: {
          type: 'string',
          format: 'date-time',
          example: '2023-07-25T12:34:56.789Z'
        },
        data: {
          type: 'array',
          items: {
            type: 'object'
          }
        },
        meta: {
          type: 'object',
          properties: {
            pagination: {
              type: 'object',
              properties: {
                page: {
                  type: 'integer',
                  example: 1
                },
                limit: {
                  type: 'integer',
                  example: 10
                },
                totalItems: {
                  type: 'integer',
                  example: 100
                },
                totalPages: {
                  type: 'integer',
                  example: 10
                },
                hasNextPage: {
                  type: 'boolean',
                  example: true
                },
                hasPrevPage: {
                  type: 'boolean',
                  example: false
                }
              }
            }
          }
        }
      }
    },
    User: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          example: 'user123'
        },
        email: {
          type: 'string',
          format: 'email',
          example: '<EMAIL>'
        },
        fullName: {
          type: 'string',
          example: 'John Doe'
        },
        phone: {
          type: 'string',
          example: '+919876543210'
        },
        gender: {
          type: 'string',
          enum: ['MALE', 'FEMALE', 'OTHER'],
          example: 'MALE'
        },
        dateOfBirth: {
          type: 'string',
          format: 'date',
          example: '1990-01-01'
        },
        role: {
          type: 'string',
          enum: ['USER', 'ADMIN', 'MODERATOR'],
          example: 'USER'
        },
        isVerified: {
          type: 'boolean',
          example: true
        },
        isActive: {
          type: 'boolean',
          example: true
        },
        createdAt: {
          type: 'string',
          format: 'date-time',
          example: '2023-01-01T00:00:00.000Z'
        },
        updatedAt: {
          type: 'string',
          format: 'date-time',
          example: '2023-01-01T00:00:00.000Z'
        }
      }
    }
  };
}

/**
 * Save OpenAPI specification to a file
 * @param {Object} spec - OpenAPI specification
 * @param {string} outputPath - Output file path
 */
function saveOpenApiSpec(spec, outputPath) {
  try {
    // Create directory if it doesn't exist
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    // Write specification to file
    fs.writeFileSync(outputPath, JSON.stringify(spec, null, 2));
    logger.info(`OpenAPI specification saved to ${outputPath}`);
  } catch (error) {
    logger.error('Error saving OpenAPI specification', { error: error.message });
    throw error;
  }
}

module.exports = {
  generateOpenApiSpec,
  saveOpenApiSpec
};
