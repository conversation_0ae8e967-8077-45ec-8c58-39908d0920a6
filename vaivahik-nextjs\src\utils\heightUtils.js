/**
 * Utility functions for handling height in feet and inches
 */

/**
 * Validates height in feet and inches
 * @param {number} feet - Height in feet
 * @param {number} inches - Height in inches
 * @param {string} gender - Gender (MALE or FEMALE)
 * @returns {Object} - { isValid: boolean, message: string, heightInInches: number }
 */
export const validateHeight = (feet, inches, gender) => {
  // Check if values are provided
  if (feet === '' || inches === '') {
    return {
      isValid: false,
      message: 'Height is required',
      heightInInches: 0
    };
  }

  // Convert to numbers if they're strings
  const feetNum = typeof feet === 'string' ? parseInt(feet) : feet;
  const inchesNum = typeof inches === 'string' ? parseInt(inches) : inches;

  // Calculate total height in inches
  const totalInches = (feetNum * 12) + inchesNum;

  // Universal height range: 4'5" (53") to 6'5" (77")
  const MIN_HEIGHT = 53; // 4'5"
  const MAX_HEIGHT = 77; // 6'5"

  if (totalInches < MIN_HEIGHT) {
    return {
      isValid: false,
      message: `Height must be at least 4'5" (${formatHeightToCm(MIN_HEIGHT)} cm)`,
      heightInInches: totalInches
    };
  }

  if (totalInches > MAX_HEIGHT) {
    return {
      isValid: false,
      message: `Height must be at most 6'5" (${formatHeightToCm(MAX_HEIGHT)} cm)`,
      heightInInches: totalInches
    };
  }

  // Additional gender-specific recommendations (not validation errors)
  let recommendation = '';
  if (gender === 'FEMALE') {
    if (totalInches < 58) { // Less than 4'10"
      recommendation = 'Below average height for females';
    } else if (totalInches > 67) { // More than 5'7"
      recommendation = 'Above average height for females';
    }
  } else { // MALE
    if (totalInches < 65) { // Less than 5'5"
      recommendation = 'Below average height for males';
    } else if (totalInches > 73) { // More than 6'1"
      recommendation = 'Above average height for males';
    }
  }

  return {
    isValid: true,
    message: recommendation,
    heightInInches: totalInches
  };
};

/**
 * Formats height in inches to centimeters
 * @param {number} inches - Total height in inches
 * @returns {number} - Height in centimeters
 */
export const formatHeightToCm = (inches) => {
  return Math.round(inches * 2.54); // 1 inch = 2.54 cm
};

/**
 * Converts height from feet/inches to centimeters
 * @param {number} feet - Height in feet
 * @param {number} inches - Height in inches
 * @returns {number} - Height in centimeters
 */
export const convertToCentimeters = (feet, inches) => {
  const totalInches = (feet * 12) + inches;
  return formatHeightToCm(totalInches);
};

/**
 * Converts height from centimeters to feet/inches
 * @param {number} cm - Height in centimeters
 * @returns {Object} - { feet: number, inches: number }
 */
export const convertFromCentimeters = (cm) => {
  const totalInches = cm / 2.54;
  const feet = Math.floor(totalInches / 12);
  const inches = Math.round(totalInches % 12);

  // Handle case where inches is 12 (should roll over to next foot)
  if (inches === 12) {
    return { feet: feet + 1, inches: 0 };
  }

  return { feet, inches };
};

/**
 * Formats height as a string in feet and inches
 * @param {number} feet - Height in feet
 * @param {number} inches - Height in inches
 * @returns {string} - Formatted height string (e.g., "5'10"")
 */
export const formatHeight = (feet, inches) => {
  if (feet === '' || inches === '') {
    return '';
  }
  return `${feet}'${inches}"`;
};

/**
 * Formats total inches as a string in feet and inches
 * @param {number} totalInches - Total height in inches
 * @returns {string} - Formatted height string (e.g., "5'10"")
 */
export const formatTotalInches = (totalInches) => {
  if (totalInches === undefined || totalInches === null) {
    return '';
  }

  const feet = Math.floor(totalInches / 12);
  const inches = totalInches % 12;

  return `${feet}'${inches}"`;
};

/**
 * Parses a height string into feet and inches
 * @param {string} heightStr - Height string (e.g., "5'10"")
 * @returns {Object} - { feet: number, inches: number }
 */
export const parseHeight = (heightStr) => {
  if (!heightStr) {
    return { feet: '', inches: '' };
  }

  const regex = /(\d+)'(\d+)"/;
  const match = heightStr.match(regex);

  if (match) {
    return {
      feet: parseInt(match[1]),
      inches: parseInt(match[2])
    };
  }

  return { feet: '', inches: '' };
};
