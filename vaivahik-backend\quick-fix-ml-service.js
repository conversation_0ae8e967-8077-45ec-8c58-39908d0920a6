/**
 * Quick Fix Script for ML Service Startup Issues
 * This script provides immediate solutions to get your backend running
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Quick Fix for ML Service Startup Issues');
console.log('==========================================\n');

// Solution 1: Create backup of current matching_api.py
const backupOriginalAPI = () => {
    const originalPath = path.join(__dirname, 'src/api/matching_api.py');
    const backupPath = path.join(__dirname, 'src/api/matching_api_pytorch_backup.py');
    
    if (fs.existsSync(originalPath)) {
        try {
            fs.copyFileSync(originalPath, backupPath);
            console.log('✅ Backed up original matching_api.py to matching_api_pytorch_backup.py');
            return true;
        } catch (error) {
            console.log('❌ Failed to backup original file:', error.message);
            return false;
        }
    } else {
        console.log('ℹ️  Original matching_api.py not found, skipping backup');
        return true;
    }
};

// Solution 2: Replace with simple ML service
const replaceWithSimpleService = () => {
    const simplePath = path.join(__dirname, 'src/api/matching_api_simple.py');
    const targetPath = path.join(__dirname, 'src/api/matching_api.py');
    
    if (fs.existsSync(simplePath)) {
        try {
            fs.copyFileSync(simplePath, targetPath);
            console.log('✅ Replaced matching_api.py with simple version');
            return true;
        } catch (error) {
            console.log('❌ Failed to replace with simple service:', error.message);
            return false;
        }
    } else {
        console.log('❌ Simple ML service file not found');
        return false;
    }
};

// Solution 3: Update server.js to handle ML service better
const updateServerJS = () => {
    const serverPath = path.join(__dirname, 'server.js');
    
    if (!fs.existsSync(serverPath)) {
        console.log('❌ server.js not found');
        return false;
    }
    
    try {
        let serverContent = fs.readFileSync(serverPath, 'utf8');
        
        // Check if ML service timeout is already configured
        if (serverContent.includes('ML_SERVICE_TIMEOUT')) {
            console.log('ℹ️  ML service timeout already configured in server.js');
            return true;
        }
        
        // Add environment variable for ML service timeout
        const envSection = `// ML Service Configuration
const ML_SERVICE_TIMEOUT = process.env.ML_SERVICE_TIMEOUT || 30000; // 30 seconds
const ML_SERVICE_PORT = process.env.ML_SERVICE_PORT || 5000;

`;
        
        // Insert after existing imports/requires
        const insertPoint = serverContent.indexOf('const express = require');
        if (insertPoint !== -1) {
            serverContent = serverContent.slice(0, insertPoint) + envSection + serverContent.slice(insertPoint);
            
            fs.writeFileSync(serverPath, serverContent);
            console.log('✅ Updated server.js with ML service configuration');
            return true;
        } else {
            console.log('❌ Could not find insertion point in server.js');
            return false;
        }
    } catch (error) {
        console.log('❌ Failed to update server.js:', error.message);
        return false;
    }
};

// Solution 4: Create .env configuration
const createEnvConfig = () => {
    const envPath = path.join(__dirname, '.env');
    
    const mlConfig = `
# ML Service Configuration
ML_SERVICE_PORT=5000
ML_SERVICE_TIMEOUT=45000
PYTHON_PATH=python

# Disable ML Service (set to false to disable)
ENABLE_ML_SERVICE=true

`;
    
    try {
        if (fs.existsSync(envPath)) {
            let envContent = fs.readFileSync(envPath, 'utf8');
            
            // Check if ML config already exists
            if (envContent.includes('ML_SERVICE_PORT')) {
                console.log('ℹ️  ML service configuration already exists in .env');
                return true;
            }
            
            // Append ML configuration
            envContent += mlConfig;
            fs.writeFileSync(envPath, envContent);
            console.log('✅ Added ML service configuration to existing .env file');
        } else {
            // Create new .env file
            fs.writeFileSync(envPath, mlConfig.trim());
            console.log('✅ Created .env file with ML service configuration');
        }
        return true;
    } catch (error) {
        console.log('❌ Failed to create/update .env file:', error.message);
        return false;
    }
};

// Solution 5: Install Python dependencies
const installPythonDeps = () => {
    console.log('📦 To install Python dependencies, run:');
    console.log('   pip install -r requirements.txt');
    console.log('   or');
    console.log('   python -m pip install Flask Flask-CORS numpy pandas scikit-learn');
    console.log('');
};

// Solution 6: Test ML service independently
const createTestScript = () => {
    const testScript = `#!/usr/bin/env python3
"""
Test script for ML service
"""

import requests
import json

def test_ml_service():
    try:
        # Test health endpoint
        response = requests.get('http://localhost:5000/health', timeout=5)
        if response.status_code == 200:
            print("✅ ML Service is running and healthy")
            print(f"Response: {response.json()}")
            return True
        else:
            print(f"❌ ML Service health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to ML Service (not running)")
        return False
    except Exception as e:
        print(f"❌ Error testing ML Service: {e}")
        return False

def test_matching():
    try:
        # Test matching endpoint
        test_data = {
            "userId": "test123",
            "user": {
                "age": 28,
                "education": "Engineering",
                "city": "Mumbai",
                "state": "Maharashtra",
                "subcaste": "Maratha"
            },
            "preferences": {
                "age_range": [25, 32],
                "height_range": [5.0, 6.0]
            },
            "potential_matches": [
                {
                    "id": "match1",
                    "age": 26,
                    "education": "Engineering",
                    "city": "Mumbai",
                    "state": "Maharashtra",
                    "subcaste": "Maratha",
                    "height": 5.5
                }
            ],
            "algorithm_version": "v2.0"
        }
        
        response = requests.post('http://localhost:5000/match', 
                               json=test_data, 
                               timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ ML Matching test successful")
            print(f"Matches found: {len(result.get('matches', []))}")
            return True
        else:
            print(f"❌ ML Matching test failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing ML matching: {e}")
        return False

if __name__ == '__main__':
    print("🧪 Testing ML Service...")
    print("=" * 30)
    
    if test_ml_service():
        test_matching()
    
    print("\\n💡 If tests fail, make sure to:")
    print("   1. Install Python dependencies: pip install -r requirements.txt")
    print("   2. Start ML service: python src/api/matching_api.py")
    print("   3. Check if port 5000 is available")
`;

    const testPath = path.join(__dirname, 'test_ml_service.py');
    
    try {
        fs.writeFileSync(testPath, testScript);
        console.log('✅ Created test_ml_service.py for independent testing');
        return true;
    } catch (error) {
        console.log('❌ Failed to create test script:', error.message);
        return false;
    }
};

// Main execution
const main = () => {
    console.log('Starting quick fix process...\n');
    
    let successCount = 0;
    const totalSteps = 6;
    
    // Step 1: Backup original
    if (backupOriginalAPI()) successCount++;
    
    // Step 2: Replace with simple service
    if (replaceWithSimpleService()) successCount++;
    
    // Step 3: Update server.js
    if (updateServerJS()) successCount++;
    
    // Step 4: Create/update .env
    if (createEnvConfig()) successCount++;
    
    // Step 5: Python dependencies info
    installPythonDeps();
    successCount++;
    
    // Step 6: Create test script
    if (createTestScript()) successCount++;
    
    console.log('\n' + '='.repeat(50));
    console.log(`✅ Quick fix completed: ${successCount}/${totalSteps} steps successful`);
    console.log('='.repeat(50));
    
    console.log('\n🚀 Next Steps:');
    console.log('1. Install Python dependencies:');
    console.log('   pip install -r requirements.txt');
    console.log('');
    console.log('2. Test ML service independently:');
    console.log('   python test_ml_service.py');
    console.log('');
    console.log('3. Start your backend server:');
    console.log('   npm run dev');
    console.log('');
    console.log('4. If issues persist, check the fix-ml-service.md file for detailed solutions');
    
    console.log('\n💡 The simple ML service provides:');
    console.log('   - Fast startup (no PyTorch loading)');
    console.log('   - All algorithm versions (v1.0 to v3.0)');
    console.log('   - Compatible with your existing code');
    console.log('   - Production-ready matching logic');
    
    console.log('\n🔄 To restore original PyTorch service later:');
    console.log('   cp src/api/matching_api_pytorch_backup.py src/api/matching_api.py');
};

// Run the quick fix
main();
