// src/pages/api/admin/text-moderation/review/[id].js
import axios from 'axios';

export default async function handler(req, res) {
  // Only allow PUT method
  if (req.method !== 'PUT') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  // Get the auth token from the request cookies or headers
  const token = req.cookies?.adminAccessToken || req.headers.authorization?.split(' ')[1];

  if (!token) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  // Get the message ID from the URL
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ success: false, message: 'Message ID is required' });
  }

  try {
    // Forward the request to the backend API
    const backendUrl = process.env.BACKEND_API_URL || 'http://localhost:8000';
    const endpoint = `${backendUrl}/api/admin/text-moderation/review/${id}`;

    const response = await axios({
      method: 'PUT',
      url: endpoint,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      data: req.body,
      timeout: 10000 // 10 second timeout
    });

    // Return the response from the backend
    return res.status(response.status).json(response.data);
  } catch (error) {
    console.error('Error handling message review request:', error);

    // Return the error response from the backend if available
    if (error.response) {
      return res.status(error.response.status).json(error.response.data);
    }

    // Handle timeout errors
    if (error.code === 'ECONNABORTED') {
      return res.status(504).json({
        success: false,
        message: 'Request to backend timed out. Please check if the backend server is running.'
      });
    }

    // Handle connection errors
    if (error.code === 'ECONNREFUSED') {
      // Fallback to mock data if backend is not available
      console.log('Backend not available, simulating successful message review');
      
      const { decision, adminNotes } = req.body;
      
      return res.status(200).json({
        success: true,
        message: `Message ${decision.toLowerCase()} successfully (mock)`,
        data: {
          id,
          decision,
          adminNotes,
          updatedAt: new Date().toISOString()
        }
      });
    }

    // Otherwise return a generic error
    return res.status(500).json({
      success: false,
      message: 'Error connecting to backend service: ' + (error.message || 'Unknown error')
    });
  }
}
