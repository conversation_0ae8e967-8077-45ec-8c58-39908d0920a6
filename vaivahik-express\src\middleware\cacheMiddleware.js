/**
 * Redis Cache Middleware
 * 
 * This middleware provides caching for API responses using Redis.
 * It can be applied to routes that return data that doesn't change frequently.
 */

const redis = require('redis');
const logger = require('../utils/logger');

// Initialize Redis client
let redisClient;

// Connect to Redis
const connectRedis = async () => {
  try {
    if (!redisClient) {
      redisClient = redis.createClient({
        url: process.env.REDIS_URL || 'redis://localhost:6379'
      });

      redisClient.on('error', (err) => {
        logger.error('Redis client error', { error: err.message });
      });

      redisClient.on('connect', () => {
        logger.info('Redis client connecting...');
      });

      redisClient.on('ready', () => {
        logger.info('Redis client connected and ready');
      });

      await redisClient.connect();
    }

    return redisClient;
  } catch (error) {
    logger.error('Error connecting to Redis', { error: error.message });
    return null;
  }
};

// Initialize Redis connection
connectRedis();

/**
 * Generate a cache key from request
 * @param {object} req - Express request object
 * @returns {string} Cache key
 */
const generateCacheKey = (req) => {
  const path = req.originalUrl || req.url;
  
  // For GET requests, include query parameters in the key
  if (req.method === 'GET') {
    return `cache:${req.method}:${path}`;
  }
  
  // For POST requests with a body, include a hash of the body
  if (req.method === 'POST' && req.body) {
    const bodyString = JSON.stringify(req.body);
    const hash = require('crypto')
      .createHash('md5')
      .update(bodyString)
      .digest('hex');
    return `cache:${req.method}:${path}:${hash}`;
  }
  
  // Default key
  return `cache:${req.method}:${path}`;
};

/**
 * Cache middleware factory
 * @param {number} duration - Cache duration in seconds
 * @returns {function} Express middleware
 */
const cache = (duration = 60) => {
  return async (req, res, next) => {
    // Skip caching if Redis is not available
    if (!redisClient || !redisClient.isReady) {
      return next();
    }
    
    // Skip caching for non-GET requests unless explicitly enabled for POST
    if (req.method !== 'GET' && req.method !== 'POST') {
      return next();
    }
    
    // Skip caching if the request has a no-cache header
    if (req.headers['cache-control'] === 'no-cache') {
      return next();
    }
    
    try {
      // Generate cache key
      const key = generateCacheKey(req);
      
      // Check if response is in cache
      const cachedResponse = await redisClient.get(key);
      
      if (cachedResponse) {
        // Parse cached response
        const parsedResponse = JSON.parse(cachedResponse);
        
        // Send cached response
        logger.debug('Cache hit', { key });
        return res.status(200).json(parsedResponse);
      }
      
      // Cache miss, capture the response
      const originalSend = res.send;
      
      res.send = function (body) {
        // Only cache successful responses
        if (res.statusCode === 200) {
          try {
            // Store response in cache
            const responseBody = JSON.parse(body);
            redisClient.set(key, body, { EX: duration });
            logger.debug('Cache set', { key, duration });
          } catch (error) {
            logger.error('Error caching response', { error: error.message });
          }
        }
        
        // Call original send
        originalSend.call(this, body);
      };
      
      next();
    } catch (error) {
      logger.error('Error in cache middleware', { error: error.message });
      next();
    }
  };
};

/**
 * Clear cache for a specific key pattern
 * @param {string} pattern - Key pattern to clear
 * @returns {Promise<number>} Number of keys cleared
 */
const clearCache = async (pattern) => {
  try {
    if (!redisClient || !redisClient.isReady) {
      await connectRedis();
    }
    
    if (!redisClient || !redisClient.isReady) {
      throw new Error('Redis client not available');
    }
    
    // Get keys matching pattern
    const keys = await redisClient.keys(`cache:${pattern}`);
    
    if (keys.length === 0) {
      return 0;
    }
    
    // Delete keys
    await redisClient.del(keys);
    
    logger.info('Cache cleared', { pattern, count: keys.length });
    
    return keys.length;
  } catch (error) {
    logger.error('Error clearing cache', { error: error.message });
    throw error;
  }
};

/**
 * Clear all cache
 * @returns {Promise<number>} Number of keys cleared
 */
const clearAllCache = async () => {
  try {
    return await clearCache('*');
  } catch (error) {
    logger.error('Error clearing all cache', { error: error.message });
    throw error;
  }
};

/**
 * Middleware to clear cache for specific routes
 * @param {string} pattern - Key pattern to clear
 * @returns {function} Express middleware
 */
const clearCacheMiddleware = (pattern) => {
  return async (req, res, next) => {
    try {
      await clearCache(pattern);
      next();
    } catch (error) {
      logger.error('Error in clear cache middleware', { error: error.message });
      next();
    }
  };
};

module.exports = {
  cache,
  clearCache,
  clearAllCache,
  clearCacheMiddleware
};
