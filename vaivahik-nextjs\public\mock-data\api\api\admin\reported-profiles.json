{"success": true, "reportedProfiles": [{"id": 1, "reportedUser": {"id": 201, "name": "<PERSON><PERSON>", "age": 29, "location": "Mumbai", "registrationDate": "2023-01-10", "photo": null, "email": "<EMAIL>", "phone": "+91 9876543220", "occupation": "Software Engineer", "education": "B.Tech in Computer Science", "maritalStatus": "Never Married", "religion": "Hindu", "caste": "<PERSON><PERSON>", "subcaste": "<PERSON><PERSON><PERSON><PERSON>", "verified": true, "premium": false}, "reportedBy": {"id": 301, "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "reason": "Fake profile", "description": "The person is using someone else's photos and providing false information.", "status": "pending", "reportedOn": "2023-07-15", "evidence": [{"id": 1, "type": "Screenshot", "url": "/img/evidence-placeholder.jpg", "description": "Screenshot of fake information"}]}, {"id": 2, "reportedUser": {"id": 202, "name": "<PERSON><PERSON>", "age": 31, "location": "Delhi", "registrationDate": "2023-02-05", "photo": null, "email": "<EMAIL>", "phone": "+91 9876543221", "occupation": "Business Owner", "education": "MBA in Finance", "maritalStatus": "Never Married", "religion": "Hindu", "caste": "<PERSON><PERSON>", "subcaste": "<PERSON><PERSON>", "verified": true, "premium": true}, "reportedBy": {"id": 302, "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "reason": "Inappropriate behavior", "description": "Sent inappropriate messages and was disrespectful in conversation.", "status": "pending", "reportedOn": "2023-07-16", "evidence": [{"id": 2, "type": "Chat Screenshot", "url": "/img/evidence-placeholder.jpg", "description": "Screenshot of inappropriate messages"}]}, {"id": 3, "reportedUser": {"id": 203, "name": "<PERSON>", "age": 27, "location": "Pune", "registrationDate": "2023-03-12", "photo": null, "email": "<EMAIL>", "phone": "+91 9876543222", "occupation": "Teacher", "education": "<PERSON><PERSON>", "maritalStatus": "Never Married", "religion": "Hindu", "caste": "<PERSON><PERSON>", "subcaste": "<PERSON><PERSON><PERSON><PERSON>", "verified": false, "premium": false}, "reportedBy": {"id": 303, "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "reason": "Misleading information", "description": "The profile contains misleading information about education and occupation.", "status": "resolved", "reportedOn": "2023-07-10", "resolvedOn": "2023-07-12", "resolution": "Warning issued", "resolvedBy": "Admin User", "evidence": [{"id": 3, "type": "Document", "url": "/img/evidence-placeholder.jpg", "description": "Proof of false information"}]}, {"id": 4, "reportedUser": {"id": 204, "name": "<PERSON><PERSON><PERSON>", "age": 33, "location": "Bangalore", "registrationDate": "2023-04-18", "photo": null, "email": "<EMAIL>", "phone": "+91 9876543223", "occupation": "IT Manager", "education": "M.Tech in Computer Science", "maritalStatus": "Never Married", "religion": "Hindu", "caste": "<PERSON><PERSON>", "subcaste": "<PERSON><PERSON><PERSON><PERSON>", "verified": true, "premium": true}, "reportedBy": {"id": 304, "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "reason": "Harassment", "description": "Continued to contact after being asked to stop.", "status": "resolved", "reportedOn": "2023-07-08", "resolvedOn": "2023-07-09", "resolution": "Profile suspended", "resolvedBy": "Admin User", "evidence": [{"id": 4, "type": "Chat Screenshot", "url": "/img/evidence-placeholder.jpg", "description": "Screenshot of harassment messages"}]}, {"id": 5, "reportedUser": {"id": 205, "name": "<PERSON><PERSON><PERSON>", "age": 26, "location": "Mumbai", "registrationDate": "2023-05-20", "photo": null, "email": "<EMAIL>", "phone": "+91 9876543224", "occupation": "Doctor", "education": "MBBS", "maritalStatus": "Never Married", "religion": "Hindu", "caste": "<PERSON><PERSON>", "subcaste": "<PERSON><PERSON>", "verified": true, "premium": false}, "reportedBy": {"id": 305, "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "reason": "Commercial activity", "description": "Using the platform to promote business services.", "status": "pending", "reportedOn": "2023-07-17", "evidence": [{"id": 5, "type": "Chat Screenshot", "url": "/img/evidence-placeholder.jpg", "description": "Screenshot of commercial messages"}]}], "pagination": {"totalReports": 5, "page": 1, "limit": 10, "totalPages": 1}, "message": "Reported profiles retrieved successfully"}