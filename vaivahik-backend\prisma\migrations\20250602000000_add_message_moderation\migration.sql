-- Add moderation fields to messages table
ALTER TABLE "messages" ADD COLUMN "is_moderated" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "messages" ADD COLUMN "moderation_status" TEXT;
ALTER TABLE "messages" ADD COLUMN "moderated_content" TEXT;
ALTER TABLE "messages" ADD COLUMN "moderation_flags" TEXT;

-- Update moderation_logs table
ALTER TABLE "moderation_logs" ADD COLUMN "content_type" TEXT;
ALTER TABLE "moderation_logs" ADD COLUMN "content_id" TEXT;
ALTER TABLE "moderation_logs" ADD COLUMN "user_id" TEXT;
ALTER TABLE "moderation_logs" ADD COLUMN "decision" TEXT;
ALTER TABLE "moderation_logs" ADD COLUMN "flags" TEXT;
ALTER TABLE "moderation_logs" ADD COLUMN "confidence" DOUBLE PRECISION;
ALTER TABLE "moderation_logs" ADD COLUMN "reviewed_by" TEXT;
ALTER TABLE "moderation_logs" ADD COLUMN "reviewed_at" TIMESTAMP(3);
ALTER TABLE "moderation_logs" ADD COLUMN "admin_notes" TEXT;

-- Make photoId optional
ALTER TABLE "moderation_logs" ALTER COLUMN "photo_id" DROP NOT NULL;
ALTER TABLE "moderation_logs" ALTER COLUMN "ai_decision" DROP NOT NULL;

-- Update foreign key constraint
ALTER TABLE "moderation_logs" DROP CONSTRAINT IF EXISTS "moderation_logs_photo_id_fkey";
ALTER TABLE "moderation_logs" ADD CONSTRAINT "moderation_logs_photo_id_fkey" 
  FOREIGN KEY ("photo_id") REFERENCES "photos"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Add new indexes
CREATE INDEX "moderation_logs_content_type_content_id_idx" ON "moderation_logs"("content_type", "content_id");
CREATE INDEX "moderation_logs_user_id_idx" ON "moderation_logs"("user_id");

-- Update existing records
UPDATE "moderation_logs" SET 
  "content_type" = 'PHOTO', 
  "content_id" = "photo_id",
  "decision" = "ai_decision"
WHERE "content_type" IS NULL;
