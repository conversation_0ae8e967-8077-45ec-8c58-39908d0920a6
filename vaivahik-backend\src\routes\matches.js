// src/routes/matches.js

const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth.middleware');
const mlMatchingService = require('../services/mlMatchingService');

const prisma = new PrismaClient();

// Simple rule-based matching function for testing
const getSimpleMatches = async (userId, limit = 10, offset = 0) => {
  try {
    // Get current user's profile and preferences
    const currentUser = await prisma.user.findUnique({
      where: { id: userId },
      include: { profile: true, preference: true }
    });

    console.log('Current user:', {
      id: userId,
      gender: currentUser?.profile?.gender,
      religion: currentUser?.profile?.religion,
      caste: currentUser?.profile?.caste
    });

    if (!currentUser || !currentUser.profile) {
      console.log('No current user or profile found');
      return [];
    }

    // Determine opposite gender
    const targetGender = currentUser.profile.gender === 'Male' ? 'Female' : 'Male';
    console.log('Looking for target gender:', targetGender);

    // Get potential matches (simplified for testing - just opposite gender)
    const potentialMatches = await prisma.user.findMany({
      where: {
        id: { not: userId },
        isVerified: true,
        profile: {
          gender: targetGender
        }
      },
      include: {
        profile: true
      },
      skip: offset,
      take: limit
    });

    console.log('Found potential matches:', potentialMatches.length);
    potentialMatches.forEach(match => {
      console.log('Match:', {
        id: match.id,
        gender: match.profile?.gender,
        religion: match.profile?.religion,
        caste: match.profile?.caste
      });
    });

    // Calculate simple compatibility scores
    const matches = potentialMatches.map(match => {
      let score = 50; // Base score

      // Same sub-caste bonus
      if (match.profile.subCaste === currentUser.profile.subCaste) {
        score += 20;
      }

      // Education compatibility
      if (match.profile.highestEducation && currentUser.profile.highestEducation) {
        score += 10;
      }

      // Age compatibility (within 5 years)
      if (match.profile.dateOfBirth && currentUser.profile.dateOfBirth) {
        const matchAge = new Date().getFullYear() - new Date(match.profile.dateOfBirth).getFullYear();
        const currentAge = new Date().getFullYear() - new Date(currentUser.profile.dateOfBirth).getFullYear();
        const ageDiff = Math.abs(matchAge - currentAge);

        if (ageDiff <= 5) {
          score += 15;
        } else if (ageDiff <= 10) {
          score += 5;
        }
      }

      // Premium user bonus
      if (match.isPremium) {
        score += 5;
      }

      // Ensure score is between 0 and 100
      score = Math.min(100, Math.max(0, score));

      return {
        userId: match.id,
        score: score,
        profile: {
          fullName: match.profile.fullName,
          age: match.profile.dateOfBirth ?
            new Date().getFullYear() - new Date(match.profile.dateOfBirth).getFullYear() : null,
          height: match.profile.height,
          education: match.profile.highestEducation,
          occupation: match.profile.occupation,
          city: match.profile.city,
          religion: match.profile.religion,
          caste: match.profile.caste,
          subCaste: match.profile.subCaste
        },
        isPremium: match.isPremium,
        isVerified: match.isVerified
      };
    });

    // Sort by score (descending)
    matches.sort((a, b) => b.score - a.score);

    return matches;
  } catch (error) {
    console.error('Error in getSimpleMatches:', error);
    return [];
  }
};

/**
 * @route GET /api/matches/test
 * @desc Test authentication
 * @access Private
 */
router.get('/test', authenticateToken, async (req, res) => {
  console.log('Test endpoint hit, user:', req.user);
  res.json({
    success: true,
    message: 'Authentication working',
    user: req.user
  });
});

/**
 * @route GET /api/matches
 * @desc Get matches for the authenticated user
 * @access Private
 */
router.get('/', authenticateToken, async (req, res) => {
  try {
    console.log('Matches endpoint hit, user:', req.user);
    const userId = req.user.id;
    const { limit = 10, offset = 0, minScore } = req.query;

    // Get matches using ML service (with fallback to rule-based)
    const matches = await mlMatchingService.getMatches(
      userId,
      parseInt(limit),
      parseInt(offset),
      minScore ? parseInt(minScore) : null
    );

    res.json({
      success: true,
      matches: matches,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: matches.length
      },
      usingML: mlMatchingService.isServiceAvailable
    });
  } catch (error) {
    console.error('Error getting matches:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting matches'
    });
  }
});

/**
 * @route GET /api/matches/explanation
 * @desc Get explanation for a specific match
 * @access Private
 */
router.get('/explanation', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { matchId } = req.query;

    if (!matchId) {
      return res.status(400).json({
        success: false,
        message: 'Match ID is required'
      });
    }

    // Get match explanation using ML service
    const explanation = await mlMatchingService.getMatchExplanation(userId, matchId);

    res.json({
      success: true,
      explanation
    });
  } catch (error) {
    console.error('Error getting match explanation:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting match explanation'
    });
  }
});

/**
 * @route POST /api/matches/interaction
 * @desc Record user interaction with a match
 * @access Private
 */
router.post('/interaction', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { matchId, interactionType, value = 1 } = req.body;

    if (!matchId || !interactionType) {
      return res.status(400).json({
        success: false,
        message: 'Match ID and interaction type are required'
      });
    }

    // Valid interaction types
    const validInteractionTypes = ['view', 'like', 'message', 'connect', 'reject'];

    if (!validInteractionTypes.includes(interactionType)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid interaction type'
      });
    }

    // Record interaction in database
    await prisma.userInteraction.create({
      data: {
        userId,
        targetUserId: matchId,
        type: interactionType.toUpperCase(),
        value: parseFloat(value),
        createdAt: new Date()
      }
    });

    // Record event for A/B testing (simplified for testing)
    console.log(`A/B Test Event: User ${userId} performed ${interactionType} with value ${value}`);

    res.json({
      success: true,
      message: 'Interaction recorded successfully'
    });
  } catch (error) {
    console.error('Error recording interaction:', error);
    res.status(500).json({
      success: false,
      message: 'Error recording interaction'
    });
  }
});

/**
 * @route GET /api/matches/feedback
 * @desc Get feedback for matches
 * @access Private (Admin only)
 */
router.get('/feedback', authenticateToken, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Unauthorized'
      });
    }

    const { experimentId = 'matching_model' } = req.query;

    // Get experiment results (simplified for testing)
    const results = {
      experimentId,
      totalUsers: 100,
      variants: [
        { name: 'control', users: 50, conversionRate: 0.15 },
        { name: 'variant_a', users: 50, conversionRate: 0.18 }
      ]
    };

    res.json({
      success: true,
      results
    });
  } catch (error) {
    console.error('Error getting feedback:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting feedback'
    });
  }
});

/**
 * @route POST /api/matches/satisfaction
 * @desc Record user satisfaction with matches
 * @access Private
 */
router.post('/satisfaction', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { rating, feedback } = req.body;

    if (rating === undefined) {
      return res.status(400).json({
        success: false,
        message: 'Rating is required'
      });
    }

    // Rating should be between 1 and 5
    const parsedRating = parseInt(rating);
    if (parsedRating < 1 || parsedRating > 5) {
      return res.status(400).json({
        success: false,
        message: 'Rating should be between 1 and 5'
      });
    }

    // Record satisfaction in database
    await prisma.userFeedback.create({
      data: {
        userId,
        type: 'MATCH_SATISFACTION',
        rating: parsedRating,
        feedback: feedback || '',
        createdAt: new Date()
      }
    });

    // Record event for A/B testing (simplified for testing)
    console.log(`A/B Test Event: User ${userId} rated satisfaction ${parsedRating}`);

    res.json({
      success: true,
      message: 'Satisfaction recorded successfully'
    });
  } catch (error) {
    console.error('Error recording satisfaction:', error);
    res.status(500).json({
      success: false,
      message: 'Error recording satisfaction'
    });
  }
});

module.exports = router;
