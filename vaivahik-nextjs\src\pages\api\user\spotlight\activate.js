import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  // Check if user is authenticated
  const session = await getServerSession(req, res, authOptions);
  
  if (!session || !session.user) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { userSpotlightId } = req.body;
    const userId = session.user.id;

    if (!userSpotlightId) {
      return res.status(400).json({ success: false, message: 'Spotlight purchase ID is required' });
    }

    // Check if the spotlight purchase exists and belongs to the user
    const spotlightPurchase = await prisma.userSpotlight.findFirst({
      where: {
        id: userSpotlightId,
        userId
      },
      include: {
        spotlight: true
      }
    });

    if (!spotlightPurchase) {
      return res.status(404).json({ success: false, message: 'Spotlight purchase not found' });
    }

    // Check if user has available spotlights to use
    if (spotlightPurchase.availableCount <= spotlightPurchase.usedCount) {
      return res.status(400).json({ 
        success: false, 
        message: 'No available spotlights to activate'
      });
    }

    // Check if user already has an active spotlight
    const now = new Date();
    const activeSpotlight = await prisma.spotlightActivation.findFirst({
      where: {
        userId,
        isActive: true,
        endTime: {
          gt: now
        }
      }
    });

    if (activeSpotlight) {
      return res.status(400).json({ 
        success: false, 
        message: 'You already have an active spotlight',
        spotlightActivation: activeSpotlight
      });
    }

    // Calculate start and end times (24 hours)
    const startTime = new Date();
    const endTime = new Date(startTime.getTime() + (24 * 60 * 60 * 1000)); // 24 hours

    // Create activation record
    const spotlightActivation = await prisma.spotlightActivation.create({
      data: {
        userSpotlightId,
        userId,
        startTime,
        endTime,
        isActive: true
      }
    });

    // Update the used count on the purchase
    await prisma.userSpotlight.update({
      where: { id: userSpotlightId },
      data: {
        usedCount: spotlightPurchase.usedCount + 1
      }
    });

    return res.status(201).json({ 
      success: true, 
      message: 'Spotlight activated successfully',
      spotlightActivation: {
        ...spotlightActivation,
        spotlight: {
          name: spotlightPurchase.spotlight.name
        },
        remainingTime: '24 hours'
      }
    });
  } catch (error) {
    console.error('Error activating spotlight:', error);
    return res.status(500).json({ success: false, message: 'Failed to activate spotlight' });
  }
}
