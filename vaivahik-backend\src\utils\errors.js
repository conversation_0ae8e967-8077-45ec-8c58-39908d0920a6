/**
 * Custom Error Classes
 * 
 * This module provides custom error classes for the application.
 */

/**
 * Base error class for application errors
 */
class AppError extends Error {
  /**
   * Create a new AppError
   * @param {string} message - Error message
   * @param {number} status - HTTP status code
   */
  constructor(message, status = 500) {
    super(message);
    this.name = this.constructor.name;
    this.status = status;
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Validation error class
 */
class ValidationError extends AppError {
  /**
   * Create a new ValidationError
   * @param {string} message - Error message
   * @param {object} errors - Validation errors
   */
  constructor(message = 'Validation failed', errors = {}) {
    super(message, 422);
    this.name = 'ValidationError';
    this.errors = errors;
  }
}

/**
 * Not found error class
 */
class NotFoundError extends AppError {
  /**
   * Create a new NotFoundError
   * @param {string} message - Error message
   */
  constructor(message = 'Resource not found') {
    super(message, 404);
    this.name = 'NotFoundError';
  }
}

/**
 * Unauthorized error class
 */
class UnauthorizedError extends AppError {
  /**
   * Create a new UnauthorizedError
   * @param {string} message - Error message
   */
  constructor(message = 'Unauthorized') {
    super(message, 401);
    this.name = 'UnauthorizedError';
  }
}

/**
 * Forbidden error class
 */
class ForbiddenError extends AppError {
  /**
   * Create a new ForbiddenError
   * @param {string} message - Error message
   */
  constructor(message = 'Forbidden') {
    super(message, 403);
    this.name = 'ForbiddenError';
  }
}

/**
 * Conflict error class
 */
class ConflictError extends AppError {
  /**
   * Create a new ConflictError
   * @param {string} message - Error message
   */
  constructor(message = 'Resource already exists') {
    super(message, 409);
    this.name = 'ConflictError';
  }
}

/**
 * Bad request error class
 */
class BadRequestError extends AppError {
  /**
   * Create a new BadRequestError
   * @param {string} message - Error message
   */
  constructor(message = 'Bad request') {
    super(message, 400);
    this.name = 'BadRequestError';
  }
}

module.exports = {
  AppError,
  ValidationError,
  NotFoundError,
  UnauthorizedError,
  ForbiddenError,
  ConflictError,
  BadRequestError
};
