#!/usr/bin/env node

/**
 * Test Google Places API Configuration and Functionality
 */

// Load environment variables
require('dotenv').config();

const axios = require('axios');

async function testGooglePlacesAPI() {
    console.log('🗺️  Testing Google Places API...\n');

    // Check environment variables
    const apiKey = process.env.GOOGLE_PLACES_API_KEY || process.env.GOOGLE_MAPS_API_KEY || process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

    if (!apiKey) {
        console.log('❌ Google Maps API Key not found in environment variables');
        console.log('Expected variables: GOOGLE_PLACES_API_KEY, GOOGLE_MAPS_API_KEY, or NEXT_PUBLIC_GOOGLE_MAPS_API_KEY');
        return false;
    }

    console.log('✅ API Key found in environment');
    console.log(`Key: ${apiKey.substring(0, 10)}...${apiKey.substring(apiKey.length - 5)}`);

    try {
        // Test 1: Places API - Text Search
        console.log('\n1️⃣ Testing Places API - Text Search...');
        const textSearchUrl = `https://maps.googleapis.com/maps/api/place/textsearch/json?query=Mumbai&key=${apiKey}`;

        const textSearchResponse = await axios.get(textSearchUrl, { timeout: 10000 });

        if (textSearchResponse.data.status === 'OK') {
            console.log('✅ Text Search API working');
            console.log(`Found ${textSearchResponse.data.results.length} results for "Mumbai"`);
        } else {
            console.log(`❌ Text Search API error: ${textSearchResponse.data.status}`);
            if (textSearchResponse.data.error_message) {
                console.log(`Error: ${textSearchResponse.data.error_message}`);
            }
        }

        // Test 2: Places API - Autocomplete
        console.log('\n2️⃣ Testing Places API - Autocomplete...');
        const autocompleteUrl = `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=Pune&types=(cities)&components=country:in&key=${apiKey}`;

        const autocompleteResponse = await axios.get(autocompleteUrl, { timeout: 10000 });

        if (autocompleteResponse.data.status === 'OK') {
            console.log('✅ Autocomplete API working');
            console.log(`Found ${autocompleteResponse.data.predictions.length} predictions for "Pune"`);

            // Show first prediction
            if (autocompleteResponse.data.predictions.length > 0) {
                const firstPrediction = autocompleteResponse.data.predictions[0];
                console.log(`First result: ${firstPrediction.description}`);
            }
        } else {
            console.log(`❌ Autocomplete API error: ${autocompleteResponse.data.status}`);
            if (autocompleteResponse.data.error_message) {
                console.log(`Error: ${autocompleteResponse.data.error_message}`);
            }
        }

        // Test 3: Check API quotas and billing
        console.log('\n3️⃣ Testing API Quotas...');

        // Make multiple requests to check quota
        const testRequests = [];
        for (let i = 0; i < 5; i++) {
            testRequests.push(
                axios.get(`https://maps.googleapis.com/maps/api/place/autocomplete/json?input=test${i}&key=${apiKey}`, { timeout: 5000 })
            );
        }

        const results = await Promise.allSettled(testRequests);
        const successCount = results.filter(r => r.status === 'fulfilled' && r.value.data.status === 'OK').length;

        console.log(`✅ ${successCount}/5 quota test requests successful`);

        if (successCount === 5) {
            console.log('✅ API quota is working properly');
        } else {
            console.log('⚠️  Some requests failed - check your quota limits');
        }

        return true;

    } catch (error) {
        console.log('\n❌ Google Places API test failed:');

        if (error.response) {
            console.log(`HTTP Status: ${error.response.status}`);
            console.log(`Response: ${JSON.stringify(error.response.data, null, 2)}`);

            if (error.response.status === 403) {
                console.log('\n💡 Solutions for 403 Forbidden:');
                console.log('1. Check if Places API is enabled in Google Cloud Console');
                console.log('2. Verify API key has proper permissions');
                console.log('3. Check if billing is enabled for your project');
                console.log('4. Ensure API key restrictions allow your domain/IP');
            }
        } else {
            console.log(`Error: ${error.message}`);
        }

        return false;
    }
}

// Run the test
testGooglePlacesAPI().then(success => {
    if (success) {
        console.log('\n🎉 Google Places API is working correctly!');
        console.log('\n📝 Next steps:');
        console.log('1. Your API is ready for production use');
        console.log('2. Monitor usage in Google Cloud Console');
        console.log('3. Set up billing alerts if needed');
        console.log('4. Consider API key restrictions for security');
    } else {
        console.log('\n❌ Google Places API needs configuration.');
        console.log('\n🔧 Setup steps:');
        console.log('1. Go to Google Cloud Console');
        console.log('2. Enable Places API');
        console.log('3. Create API key');
        console.log('4. Add key to .env file');
        console.log('5. Enable billing (required for Places API)');
    }

    process.exit(success ? 0 : 1);
});
