/**
 * Admin API Service
 *
 * This service provides methods for making API requests to the admin endpoints.
 * It uses the adminApi instance from apiService.js for authenticated requests.
 */

import { adminGet, adminPost, adminPut, adminDel } from './apiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';

/**
 * Dashboard API
 */
export const dashboardApi = {
  /**
   * Get dashboard statistics
   * @param {Object} params - Query parameters (refresh)
   * @returns {Promise} Promise with dashboard data
   */
  getDashboardStats: async (params = {}) => {
    return adminGet(ADMIN_ENDPOINTS.DASHBOARD, params);
  },

  /**
   * Get recent users
   * @param {Object} params - Query parameters (page, limit, filter)
   * @returns {Promise} Promise with recent users data
   */
  getRecentUsers: async (params = {}) => {
    return adminGet(ADMIN_ENDPOINTS.RECENT_USERS, params);
  },

  /**
   * Get recent activity
   * @param {Object} params - Query parameters (limit, types)
   * @returns {Promise} Promise with recent activity data
   */
  getRecentActivity: async (params = {}) => {
    return adminGet(ADMIN_ENDPOINTS.RECENT_ACTIVITY, params);
  },
};

/**
 * User Management API
 */
export const userManagementApi = {
  /**
   * Get all users
   * @param {Object} params - Query parameters (page, limit, search, filter)
   * @returns {Promise} Promise with users data
   */
  getUsers: async (params = {}) => {
    return adminGet(ADMIN_ENDPOINTS.USERS, params);
  },

  /**
   * Get user details
   * @param {string} userId - User ID
   * @returns {Promise} Promise with user details
   */
  getUserDetails: async (userId) => {
    return adminGet(ADMIN_ENDPOINTS.USER_DETAILS(userId));
  },

  /**
   * Update user
   * @param {string} userId - User ID
   * @param {Object} userData - Updated user data
   * @returns {Promise} Promise with update result
   */
  updateUser: async (userId, userData) => {
    return adminPut(ADMIN_ENDPOINTS.USER_DETAILS(userId), userData);
  },

  /**
   * Delete user
   * @param {string} userId - User ID
   * @returns {Promise} Promise with delete result
   */
  deleteUser: async (userId) => {
    return adminDel(ADMIN_ENDPOINTS.USER_DETAILS(userId));
  },
};

/**
 * Verification API
 */
export const verificationApi = {
  /**
   * Get verification queue
   * @param {Object} params - Query parameters (page, limit, status)
   * @returns {Promise} Promise with verification queue data
   */
  getVerificationQueue: async (params = {}) => {
    return adminGet(ADMIN_ENDPOINTS.VERIFICATION_QUEUE, params);
  },

  /**
   * Get verification document details
   * @param {string} docId - Document ID
   * @returns {Promise} Promise with document details
   */
  getDocumentDetails: async (docId) => {
    return adminGet(ADMIN_ENDPOINTS.VERIFICATION_DOCUMENT(docId));
  },

  /**
   * Approve verification document
   * @param {string} docId - Document ID
   * @param {Object} data - Approval data (notes)
   * @returns {Promise} Promise with approval result
   */
  approveDocument: async (docId, data = {}) => {
    return adminPut(ADMIN_ENDPOINTS.VERIFICATION_DOCUMENT(docId), {
      status: 'APPROVED',
      ...data,
    });
  },

  /**
   * Reject verification document
   * @param {string} docId - Document ID
   * @param {Object} data - Rejection data (reason, notes)
   * @returns {Promise} Promise with rejection result
   */
  rejectDocument: async (docId, data = {}) => {
    return adminPut(ADMIN_ENDPOINTS.VERIFICATION_DOCUMENT(docId), {
      status: 'REJECTED',
      ...data,
    });
  },
};

/**
 * Biodata Templates API
 */
export const biodataTemplatesApi = {
  /**
   * Get all biodata templates
   * @param {Object} params - Query parameters (page, limit)
   * @returns {Promise} Promise with templates data
   */
  getTemplates: async (params = {}) => {
    return adminGet(ADMIN_ENDPOINTS.BIODATA_TEMPLATES, params);
  },

  /**
   * Get template details
   * @param {string} templateId - Template ID
   * @returns {Promise} Promise with template details
   */
  getTemplateDetails: async (templateId) => {
    return adminGet(ADMIN_ENDPOINTS.BIODATA_TEMPLATE_DETAILS(templateId));
  },

  /**
   * Create template
   * @param {Object} templateData - Template data
   * @returns {Promise} Promise with creation result
   */
  createTemplate: async (templateData) => {
    // For file uploads, use FormData
    const formData = new FormData();

    // Add all fields to the form data
    Object.keys(templateData).forEach(key => {
      if (key === 'previewImage' || key === 'designFile') {
        if (templateData[key] instanceof File) {
          formData.append(key, templateData[key]);
        }
      } else {
        formData.append(key, templateData[key]);
      }
    });

    return adminPost(ADMIN_ENDPOINTS.BIODATA_TEMPLATES, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  /**
   * Update template
   * @param {string} templateId - Template ID
   * @param {Object} templateData - Updated template data
   * @returns {Promise} Promise with update result
   */
  updateTemplate: async (templateId, templateData) => {
    // For file uploads, use FormData
    const formData = new FormData();

    // Add all fields to the form data
    Object.keys(templateData).forEach(key => {
      if (key === 'previewImage' || key === 'designFile') {
        if (templateData[key] instanceof File) {
          formData.append(key, templateData[key]);
        }
      } else {
        formData.append(key, templateData[key]);
      }
    });

    return adminPut(ADMIN_ENDPOINTS.BIODATA_TEMPLATE_DETAILS(templateId), formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  /**
   * Delete template
   * @param {string} templateId - Template ID
   * @returns {Promise} Promise with delete result
   */
  deleteTemplate: async (templateId) => {
    return adminDel(ADMIN_ENDPOINTS.BIODATA_TEMPLATE_DETAILS(templateId));
  },
};

/**
 * Spotlight Features API
 */
export const spotlightFeaturesApi = {
  /**
   * Get all spotlight features
   * @param {Object} params - Query parameters (page, limit)
   * @returns {Promise} Promise with features data
   */
  getFeatures: async (params = {}) => {
    return adminGet(ADMIN_ENDPOINTS.SPOTLIGHT_FEATURES, params);
  },

  /**
   * Get feature details
   * @param {string} featureId - Feature ID
   * @returns {Promise} Promise with feature details
   */
  getFeatureDetails: async (featureId) => {
    return adminGet(ADMIN_ENDPOINTS.SPOTLIGHT_FEATURE_DETAILS(featureId));
  },

  /**
   * Create feature
   * @param {Object} featureData - Feature data
   * @returns {Promise} Promise with creation result
   */
  createFeature: async (featureData) => {
    return adminPost(ADMIN_ENDPOINTS.SPOTLIGHT_FEATURES, featureData);
  },

  /**
   * Update feature
   * @param {string} featureId - Feature ID
   * @param {Object} featureData - Updated feature data
   * @returns {Promise} Promise with update result
   */
  updateFeature: async (featureId, featureData) => {
    return adminPut(ADMIN_ENDPOINTS.SPOTLIGHT_FEATURE_DETAILS(featureId), featureData);
  },

  /**
   * Delete feature
   * @param {string} featureId - Feature ID
   * @returns {Promise} Promise with delete result
   */
  deleteFeature: async (featureId) => {
    return adminDel(ADMIN_ENDPOINTS.SPOTLIGHT_FEATURE_DETAILS(featureId));
  },
};
