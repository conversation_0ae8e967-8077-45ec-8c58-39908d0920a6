/**
 * Search Hook
 * 
 * Custom hook for managing search state and operations
 */

import { useState, useEffect, useCallback } from 'react';
import { searchProfiles, advancedSearch } from '../api';
import { isFeatureEnabled } from '@/utils/featureFlags';

/**
 * Hook for managing search functionality
 * @param {Object} initialParams - Initial search parameters
 * @param {Object} options - Additional options
 * @returns {Object} Search state and functions
 */
const useSearch = (initialParams = {}, options = {}) => {
  // Default options
  const {
    resultsPerPage = 9,
    autoSearch = false,
    useAdvancedSearch = false,
    saveHistory = true
  } = options;

  // Search state
  const [searchParams, setSearchParams] = useState(initialParams);
  const [currentSearchParams, setCurrentSearchParams] = useState(null);
  const [allResults, setAllResults] = useState([]);
  const [displayedResults, setDisplayedResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchPerformed, setSearchPerformed] = useState(false);
  const [error, setError] = useState(null);
  const [searchHistory, setSearchHistory] = useState([]);
  
  // Pagination state
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalResults, setTotalResults] = useState(0);
  
  // Sorting state
  const [sortOption, setSortOption] = useState('relevance');

  // Load search history from localStorage on mount
  useEffect(() => {
    if (saveHistory) {
      try {
        const storedHistory = localStorage.getItem('searchHistory');
        if (storedHistory) {
          setSearchHistory(JSON.parse(storedHistory));
        }
      } catch (error) {
        console.error('Error loading search history:', error);
      }
    }
  }, [saveHistory]);

  // Perform search when autoSearch is true and initialParams are provided
  useEffect(() => {
    if (autoSearch && Object.keys(initialParams).length > 0) {
      handleSearch(initialParams);
    }
  }, [autoSearch, initialParams]);

  // Update displayed results when page or sort option changes
  useEffect(() => {
    if (allResults.length > 0) {
      // Apply sorting
      let sortedResults = [...allResults];
      
      switch (sortOption) {
        case 'newest':
          sortedResults.sort((a, b) => new Date(b.createdAt || 0) - new Date(a.createdAt || 0));
          break;
        case 'age_asc':
          sortedResults.sort((a, b) => a.age - b.age);
          break;
        case 'age_desc':
          sortedResults.sort((a, b) => b.age - a.age);
          break;
        // Default is relevance, which is the order returned from the API
      }
      
      // Apply pagination
      const startIndex = (page - 1) * resultsPerPage;
      const endIndex = startIndex + resultsPerPage;
      setDisplayedResults(sortedResults.slice(startIndex, endIndex));
      setTotalPages(Math.ceil(sortedResults.length / resultsPerPage));
    } else {
      setDisplayedResults([]);
      setTotalPages(0);
    }
  }, [allResults, page, resultsPerPage, sortOption]);

  /**
   * Perform search with the given parameters
   * @param {Object} params - Search parameters
   */
  const handleSearch = useCallback(async (params) => {
    setIsSearching(true);
    setError(null);
    setPage(1); // Reset to first page on new search
    setCurrentSearchParams(params); // Store current search parameters

    try {
      // Determine whether to use advanced search
      const searchFunction = useAdvancedSearch && isFeatureEnabled('enableAdvancedSearch')
        ? advancedSearch
        : searchProfiles;
      
      // Call the appropriate search function
      const result = await searchFunction(params, {
        page: 1,
        limit: 100 // Get more results for client-side pagination
      });
      
      setAllResults(result.profiles || []);
      setTotalResults(result.total || 0);
      setSearchPerformed(true);
      
      // Add to search history if enabled
      if (saveHistory) {
        addToSearchHistory(params, result.total || 0);
      }
      
      return result;
    } catch (error) {
      console.error('Search error:', error);
      setError(error.message || 'An error occurred while searching');
      return { profiles: [], total: 0 };
    } finally {
      setIsSearching(false);
    }
  }, [useAdvancedSearch, saveHistory]);

  /**
   * Add search to history
   * @param {Object} params - Search parameters
   * @param {number} resultCount - Number of results found
   */
  const addToSearchHistory = useCallback((params, resultCount) => {
    // Create a history entry with timestamp
    const historyEntry = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      searchParams: params,
      resultCount
    };
    
    // Add to history (limit to 10 entries)
    const updatedHistory = [historyEntry, ...searchHistory].slice(0, 10);
    setSearchHistory(updatedHistory);
    
    // Save to localStorage
    try {
      localStorage.setItem('searchHistory', JSON.stringify(updatedHistory));
    } catch (error) {
      console.error('Error saving search history:', error);
    }
  }, [searchHistory]);

  /**
   * Handle page change
   * @param {Event} event - Event object
   * @param {number} newPage - New page number
   */
  const handlePageChange = useCallback((event, newPage) => {
    setPage(newPage);
    // Scroll to top of results
    document.getElementById('search-results-top')?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  /**
   * Handle removing a filter
   * @param {string} filterId - ID of the filter to remove
   */
  const handleRemoveFilter = useCallback((filterId) => {
    if (!currentSearchParams) return;
    
    const updatedParams = { ...currentSearchParams };
    
    // Handle different filter types
    switch (filterId) {
      case 'age':
        delete updatedParams.ageFrom;
        delete updatedParams.ageTo;
        break;
      case 'height':
        delete updatedParams.heightFrom;
        delete updatedParams.heightTo;
        break;
      case 'location':
        delete updatedParams.location;
        break;
      case 'religion':
        updatedParams.religion = 'ANY';
        break;
      case 'caste':
        updatedParams.caste = 'ANY';
        break;
      case 'education':
        updatedParams.education = [];
        break;
      case 'occupation':
        updatedParams.occupation = [];
        break;
      case 'incomeRange':
        updatedParams.incomeRange = 'ANY';
        break;
      case 'maritalStatus':
        updatedParams.maritalStatus = [];
        break;
      case 'diet':
        updatedParams.diet = 'ANY';
        break;
      case 'withPhoto':
        updatedParams.withPhoto = false;
        break;
      case 'profileCreatedWithin':
        delete updatedParams.profileCreatedWithin;
        break;
      default:
        break;
    }
    
    // Perform search with updated parameters
    handleSearch(updatedParams);
  }, [currentSearchParams, handleSearch]);

  /**
   * Handle clearing all filters
   */
  const handleClearAllFilters = useCallback(() => {
    // Reset to basic search with minimal parameters
    const basicParams = {
      searchType: 'REGULAR',
      targetGender: initialParams.targetGender || 'FEMALE',
      ageFrom: initialParams.ageFrom || 18,
      ageTo: initialParams.ageTo || 35
    };
    
    handleSearch(basicParams);
  }, [initialParams, handleSearch]);

  /**
   * Load a search from history
   * @param {Object} historyEntry - History entry to load
   */
  const loadFromHistory = useCallback((historyEntry) => {
    handleSearch(historyEntry.searchParams);
  }, [handleSearch]);

  /**
   * Clear search history
   */
  const clearHistory = useCallback(() => {
    setSearchHistory([]);
    localStorage.removeItem('searchHistory');
  }, []);

  return {
    // State
    searchParams,
    setSearchParams,
    currentSearchParams,
    allResults,
    displayedResults,
    isSearching,
    searchPerformed,
    error,
    searchHistory,
    page,
    totalPages,
    totalResults,
    sortOption,
    setSortOption,
    
    // Functions
    handleSearch,
    handlePageChange,
    handleRemoveFilter,
    handleClearAllFilters,
    loadFromHistory,
    clearHistory
  };
};

export default useSearch;
