// src/controllers/admin/subscriptions.controller.js

const logger = require('../../utils/logger');

// Mock data for subscription plans
const mockSubscriptionPlans = [
    {
        id: 1,
        name: "Basic Plan",
        planType: "BASIC",
        price: 999,
        currency: "INR",
        duration: 30,
        description: "Perfect for getting started with premium features",
        features: [
            "View up to 10 contacts per month",
            "Basic matching algorithm",
            "Limited chat messages",
            "Standard customer support"
        ],
        isActive: true,
        subscriberCount: 245,
        revenue: 244755,
        createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
        updatedAt: new Date()
    },
    {
        id: 2,
        name: "Premium Plan",
        planType: "PREMIUM",
        price: 2999,
        currency: "INR",
        duration: 90,
        description: "Most popular plan with advanced features",
        features: [
            "Unlimited contact views",
            "Advanced AI matching",
            "Unlimited chat messages",
            "Priority customer support",
            "Profile boost feature",
            "Advanced search filters"
        ],
        isActive: true,
        subscriberCount: 189,
        revenue: 566811,
        createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
        updatedAt: new Date()
    },
    {
        id: 3,
        name: "Gold Plan",
        planType: "GOLD",
        price: 4999,
        currency: "INR",
        duration: 180,
        description: "Premium plan with exclusive features",
        features: [
            "All Premium features",
            "Spotlight feature included",
            "Personalized matchmaking assistance",
            "Priority verification",
            "Exclusive events access",
            "Dedicated relationship manager"
        ],
        isActive: true,
        subscriberCount: 78,
        revenue: 389922,
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        updatedAt: new Date()
    }
];

// Check if mock data should be used
const shouldUseMockData = () => {
    return process.env.NODE_ENV === 'development' || process.env.USE_MOCK_DATA === 'true';
};

/**
 * @description Get all subscription plans
 * @route GET /api/admin/subscriptions
 */
exports.getSubscriptionPlans = async (req, res, next) => {
    try {
        const { 
            page = 1, 
            limit = 10, 
            search = '',
            planType = '',
            isActive = '',
            sortBy = 'createdAt', 
            order = 'desc'
        } = req.query;

        if (shouldUseMockData()) {
            // Return mock data
            let filteredPlans = [...mockSubscriptionPlans];

            // Apply search filter
            if (search) {
                filteredPlans = filteredPlans.filter(plan => 
                    plan.name.toLowerCase().includes(search.toLowerCase()) ||
                    plan.description.toLowerCase().includes(search.toLowerCase())
                );
            }

            // Apply planType filter
            if (planType) {
                filteredPlans = filteredPlans.filter(plan => plan.planType === planType);
            }

            // Apply isActive filter
            if (isActive !== '') {
                filteredPlans = filteredPlans.filter(plan => 
                    plan.isActive === (isActive === 'true')
                );
            }

            // Apply sorting
            const sortOrder = order.toLowerCase() === 'asc' ? 1 : -1;
            filteredPlans.sort((a, b) => {
                const aValue = a[sortBy];
                const bValue = b[sortBy];
                if (aValue < bValue) return -1 * sortOrder;
                if (aValue > bValue) return 1 * sortOrder;
                return 0;
            });

            // Apply pagination
            const pageNum = parseInt(page);
            const limitNum = parseInt(limit);
            const startIndex = (pageNum - 1) * limitNum;
            const endIndex = startIndex + limitNum;
            const paginatedPlans = filteredPlans.slice(startIndex, endIndex);

            return res.status(200).json({
                success: true,
                message: "Subscription plans fetched successfully (Mock Data)",
                plans: paginatedPlans,
                pagination: {
                    currentPage: pageNum,
                    limit: limitNum,
                    totalPages: Math.ceil(filteredPlans.length / limitNum),
                    totalPlans: filteredPlans.length
                },
                useMockData: true
            });
        }

        // Real database implementation would go here
        const prisma = req.prisma;
        
        return res.status(200).json({
            success: true,
            message: "Subscription plans fetched successfully (Real Data - Not Implemented Yet)",
            plans: [],
            pagination: {
                currentPage: 1,
                limit: parseInt(limit),
                totalPages: 0,
                totalPlans: 0
            },
            useMockData: false
        });

    } catch (error) {
        logger.error('Error fetching subscription plans:', error);
        next(error);
    }
};

/**
 * @description Get subscription plan by ID
 * @route GET /api/admin/subscriptions/:id
 */
exports.getSubscriptionPlanById = async (req, res, next) => {
    try {
        const { id } = req.params;

        if (shouldUseMockData()) {
            const plan = mockSubscriptionPlans.find(p => p.id === parseInt(id));
            if (!plan) {
                return res.status(404).json({
                    success: false,
                    message: "Subscription plan not found"
                });
            }

            return res.status(200).json({
                success: true,
                message: "Subscription plan fetched successfully",
                plan,
                useMockData: true
            });
        }

        // Real database implementation would go here
        return res.status(404).json({
            success: false,
            message: "Subscription plan not found (Real Data - Not Implemented Yet)"
        });

    } catch (error) {
        logger.error('Error fetching subscription plan:', error);
        next(error);
    }
};

/**
 * @description Create new subscription plan
 * @route POST /api/admin/subscriptions
 */
exports.createSubscriptionPlan = async (req, res, next) => {
    try {
        const planData = req.body;

        if (shouldUseMockData()) {
            const newPlan = {
                id: Date.now(),
                ...planData,
                subscriberCount: 0,
                revenue: 0,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            mockSubscriptionPlans.push(newPlan);

            return res.status(201).json({
                success: true,
                message: "Subscription plan created successfully (Mock Data)",
                plan: newPlan,
                useMockData: true
            });
        }

        // Real database implementation would go here
        return res.status(501).json({
            success: false,
            message: "Create subscription plan not implemented for real data yet"
        });

    } catch (error) {
        logger.error('Error creating subscription plan:', error);
        next(error);
    }
};

/**
 * @description Update subscription plan
 * @route PUT /api/admin/subscriptions/:id
 */
exports.updateSubscriptionPlan = async (req, res, next) => {
    try {
        const { id } = req.params;
        const updateData = req.body;

        if (shouldUseMockData()) {
            const planIndex = mockSubscriptionPlans.findIndex(p => p.id === parseInt(id));
            if (planIndex === -1) {
                return res.status(404).json({
                    success: false,
                    message: "Subscription plan not found"
                });
            }

            mockSubscriptionPlans[planIndex] = {
                ...mockSubscriptionPlans[planIndex],
                ...updateData,
                updatedAt: new Date().toISOString()
            };

            return res.status(200).json({
                success: true,
                message: "Subscription plan updated successfully (Mock Data)",
                plan: mockSubscriptionPlans[planIndex],
                useMockData: true
            });
        }

        // Real database implementation would go here
        return res.status(501).json({
            success: false,
            message: "Update subscription plan not implemented for real data yet"
        });

    } catch (error) {
        logger.error('Error updating subscription plan:', error);
        next(error);
    }
};

/**
 * @description Delete subscription plan
 * @route DELETE /api/admin/subscriptions/:id
 */
exports.deleteSubscriptionPlan = async (req, res, next) => {
    try {
        const { id } = req.params;

        if (shouldUseMockData()) {
            const planIndex = mockSubscriptionPlans.findIndex(p => p.id === parseInt(id));
            if (planIndex === -1) {
                return res.status(404).json({
                    success: false,
                    message: "Subscription plan not found"
                });
            }

            mockSubscriptionPlans.splice(planIndex, 1);

            return res.status(200).json({
                success: true,
                message: "Subscription plan deleted successfully (Mock Data)",
                useMockData: true
            });
        }

        // Real database implementation would go here
        return res.status(501).json({
            success: false,
            message: "Delete subscription plan not implemented for real data yet"
        });

    } catch (error) {
        logger.error('Error deleting subscription plan:', error);
        next(error);
    }
};

/**
 * @description Get subscription statistics
 * @route GET /api/admin/subscriptions/stats/overview
 */
exports.getSubscriptionStats = async (req, res, next) => {
    try {
        if (shouldUseMockData()) {
            const stats = {
                totalPlans: mockSubscriptionPlans.length,
                activePlans: mockSubscriptionPlans.filter(p => p.isActive).length,
                totalSubscribers: mockSubscriptionPlans.reduce((sum, p) => sum + p.subscriberCount, 0),
                totalRevenue: mockSubscriptionPlans.reduce((sum, p) => sum + p.revenue, 0),
                averagePrice: mockSubscriptionPlans.reduce((sum, p) => sum + p.price, 0) / mockSubscriptionPlans.length,
                mostPopularPlan: mockSubscriptionPlans.reduce((prev, current) => 
                    (prev.subscriberCount > current.subscriberCount) ? prev : current
                )
            };

            return res.status(200).json({
                success: true,
                message: "Subscription statistics fetched successfully (Mock Data)",
                stats,
                useMockData: true
            });
        }

        // Real database implementation would go here
        return res.status(200).json({
            success: true,
            message: "Subscription statistics fetched successfully (Real Data - Not Implemented Yet)",
            stats: {
                totalPlans: 0,
                activePlans: 0,
                totalSubscribers: 0,
                totalRevenue: 0,
                averagePrice: 0,
                mostPopularPlan: null
            },
            useMockData: false
        });

    } catch (error) {
        logger.error('Error fetching subscription statistics:', error);
        next(error);
    }
};
