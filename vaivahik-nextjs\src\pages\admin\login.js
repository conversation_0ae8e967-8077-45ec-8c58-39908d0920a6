import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { adminLogin } from '@/services/authService';
import { isUsingRealBackend } from '@/utils/featureFlags';

export default function AdminLogin() {
  const [credentials, setCredentials] = useState({
    email: '',
    password: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState('mock');
  const router = useRouter();

  // Set data source on mount
  useEffect(() => {
    setDataSource(isUsingRealBackend() ? 'real' : 'mock');
  }, []);

  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setCredentials(prev => ({ ...prev, [name]: value }));

    // Clear error when user types
    if (error) {
      setError('');
    }
  };

  const handleLogin = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // Validate inputs
      if (!credentials.email || !credentials.password) {
        setError('Email and password are required');
        setLoading(false);
        return;
      }

      // Call admin login API
      const response = await fetch('/api/auth/admin-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      const result = await response.json();

      if (result.success) {
        // Store admin info in localStorage (only in browser environment)
        if (result.user && typeof window !== 'undefined') {
          localStorage.setItem('adminName', result.user.name);
          localStorage.setItem('adminRole', result.user.role);
          // Store token
          localStorage.setItem('adminAccessToken', result.token);
        }

        // Redirect to dashboard
        router.push('/admin/dashboard');
      } else {
        setError(result.message || 'Invalid email or password');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('An error occurred during login. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Admin Login - Vaivahik</title>
        <meta name="description" content="Vaivahik Admin Login" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <style jsx>{`
        .data-source-indicator {
          display: inline-block;
          background-color: #f0f0f0;
          color: #333;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          margin-bottom: 15px;
          text-align: center;
        }
        .data-source-indicator strong {
          color: ${dataSource === 'mock' ? '#ff9800' : '#4caf50'};
          font-weight: bold;
        }
      `}</style>

      <div className="login-container">
        <div className="login-card">
          <div className="login-header">
            <div className="logo">
              <div className="logo-icon">V</div>
              <span>Vaivahik Admin</span>
            </div>
          </div>

          <div className="login-body">
            <h2>Admin Login</h2>
            <p className="login-subtitle">Enter your credentials to access the admin panel</p>

            <div className="login-help">
              <p>Valid credentials for testing:</p>
              <ul>
                <li>Email: <strong><EMAIL></strong> / Password: <strong>admin123</strong></li>
                <li>Email: <strong><EMAIL></strong> / Password: <strong>moderator123</strong></li>
              </ul>
            </div>

            {/* Data source indicator */}
            <div className="data-source-indicator">
              Using <strong>{dataSource}</strong> data
            </div>

            {error && <div className="error-message">{error}</div>}

            <form onSubmit={handleLogin}>
              <div className="form-group">
                <label htmlFor="email">Email</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={credentials.email}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="password">Password</label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={credentials.password}
                  onChange={handleChange}
                  required
                />
              </div>

              <button
                type="submit"
                className="login-button"
                disabled={loading}
              >
                {loading ? 'Logging in...' : 'Login'}
              </button>
            </form>
          </div>

          <div className="login-footer">
            <p>© {new Date().getFullYear()} Vaivahik. All rights reserved.</p>
          </div>
        </div>
      </div>

      <style jsx>{`
        .login-container {
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 100vh;
          background: linear-gradient(135deg, #5e35b1, #7e57c2);
          padding: 20px;
        }

        .login-card {
          background-color: white;
          border-radius: 12px;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
          width: 100%;
          max-width: 400px;
          overflow: hidden;
        }

        .login-header {
          padding: 20px;
          border-bottom: 1px solid #eee;
        }

        .logo {
          display: flex;
          align-items: center;
          gap: 10px;
          font-size: 1.5rem;
          font-weight: 600;
        }

        .logo-icon {
          background-color: #ff5722;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 6px;
          font-weight: bold;
          color: white;
        }

        .login-body {
          padding: 30px;
        }

        .login-body h2 {
          margin-bottom: 10px;
          color: #333;
        }

        .login-subtitle {
          color: #666;
          margin-bottom: 15px;
        }

        .login-help {
          background-color: #e8eaf6;
          border-radius: 6px;
          padding: 12px;
          margin-bottom: 20px;
          font-size: 0.85rem;
        }

        .login-help p {
          margin-bottom: 8px;
          font-weight: 500;
          color: #3949ab;
        }

        .login-help ul {
          margin: 0;
          padding-left: 20px;
        }

        .login-help li {
          margin-bottom: 4px;
        }

        .login-help strong {
          font-family: monospace;
          background-color: #fff;
          padding: 2px 4px;
          border-radius: 3px;
        }

        .form-group {
          margin-bottom: 20px;
        }

        .form-group label {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
          color: #444;
        }

        .form-group input {
          width: 100%;
          padding: 12px;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 1rem;
          transition: border-color 0.3s ease;
        }

        .form-group input:focus {
          border-color: #5e35b1;
          outline: none;
        }

        .login-button {
          width: 100%;
          padding: 12px;
          background-color: #5e35b1;
          color: white;
          border: none;
          border-radius: 6px;
          font-size: 1rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.3s ease;
        }

        .login-button:hover {
          background-color: #4527a0;
        }

        .login-button:disabled {
          background-color: #9e9e9e;
          cursor: not-allowed;
        }

        .error-message {
          background-color: #ffebee;
          color: #d32f2f;
          padding: 10px;
          border-radius: 6px;
          margin-bottom: 20px;
          font-size: 0.9rem;
        }

        .login-footer {
          padding: 15px 20px;
          border-top: 1px solid #eee;
          text-align: center;
          font-size: 0.8rem;
          color: #666;
        }
      `}</style>
    </>
  );
}

