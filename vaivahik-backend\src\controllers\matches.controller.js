// src/controllers/matches.controller.js

const autoTriggers = require('../../services/notification/auto-notification-triggers');

/**
 * @description Get matches for the current user with verification-based limits
 * @route GET /api/users/matches
 */
exports.getMatches = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Get user verification status
        const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { isVerified: true }
        });

        // Determine limit based on verification status
        // This is also handled by the applyUnverifiedLimits middleware,
        // but we're adding it here for clarity and as a fallback
        const limit = req.limit || (user.isVerified ? 20 : 5);

        // Get matches
        const matches = await prisma.match.findMany({
            where: {
                OR: [
                    { user1Id: userId },
                    { user2Id: userId }
                ],
                status: 'ACCEPTED'
            },
            include: {
                user1: {
                    select: {
                        id: true,
                        isVerified: true,
                        profile: {
                            select: {
                                fullName: true,
                                gender: true,
                                dateOfBirth: true,
                                city: true,
                                occupation: true
                            }
                        },
                        photos: {
                            where: { isProfilePic: true },
                            select: { url: true },
                            take: 1
                        }
                    }
                },
                user2: {
                    select: {
                        id: true,
                        isVerified: true,
                        profile: {
                            select: {
                                fullName: true,
                                gender: true,
                                dateOfBirth: true,
                                city: true,
                                occupation: true
                            }
                        },
                        photos: {
                            where: { isProfilePic: true },
                            select: { url: true },
                            take: 1
                        }
                    }
                }
            },
            orderBy: { updatedAt: 'desc' },
            take: limit
        });

        // Format matches for response
        const formattedMatches = matches.map(match => {
            // Determine which user is the match (not the current user)
            const matchUser = match.user1Id === userId ? match.user2 : match.user1;

            return {
                matchId: match.id,
                userId: matchUser.id,
                isVerified: matchUser.isVerified,
                fullName: matchUser.profile?.fullName || 'User',
                age: matchUser.profile?.dateOfBirth ? calculateAge(matchUser.profile.dateOfBirth) : null,
                city: matchUser.profile?.city || 'Unknown',
                occupation: matchUser.profile?.occupation || 'Not specified',
                profilePicUrl: matchUser.photos?.[0]?.url || null,
                compatibilityScore: match.compatibilityScore || null,
                matchReason: match.matchReason || null,
                lastMessageAt: match.lastMessageAt || null
            };
        });

        // Add verification notice for unverified users
        const verificationNotice = !user.isVerified ? {
            message: `You're seeing ${limit} matches. Verify your profile to see more matches.`,
            verificationRequired: true,
            currentLimit: limit,
            fullLimit: 20
        } : null;

        res.status(200).json({
            success: true,
            matches: formattedMatches,
            total: formattedMatches.length,
            verificationNotice
        });
    } catch (error) {
        console.error('Error fetching matches:', error);
        next(error);
    }
};

// Helper function to calculate age from date of birth
function calculateAge(dateOfBirth) {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }

    return age;
}
