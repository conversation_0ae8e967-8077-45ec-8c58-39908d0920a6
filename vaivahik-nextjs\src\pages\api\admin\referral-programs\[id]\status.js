import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../auth/[...nextauth]';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  // Check if user is authenticated and is an admin
  const session = await getServerSession(req, res, authOptions);
  
  if (!session || !session.user || session.user.role !== 'ADMIN') {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ success: false, message: 'Program ID is required' });
  }

  // Only allow PUT method
  if (req.method !== 'PUT') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { status } = req.body;

    if (!status || !['active', 'inactive', 'scheduled'].includes(status)) {
      return res.status(400).json({ success: false, message: 'Valid status is required (active, inactive, or scheduled)' });
    }

    // Check if program exists
    const program = await prisma.referralProgram.findUnique({
      where: { id }
    });

    if (!program) {
      return res.status(404).json({ success: false, message: 'Referral program not found' });
    }

    // Update the program status
    const updatedProgram = await prisma.referralProgram.update({
      where: { id },
      data: {
        status,
        updatedAt: new Date()
      }
    });

    return res.status(200).json({ 
      success: true, 
      message: `Program status updated to ${status}`,
      program: updatedProgram
    });
  } catch (error) {
    console.error('Error updating referral program status:', error);
    return res.status(500).json({ success: false, message: 'Failed to update referral program status' });
  }
}
