import { BACKEND_API_URL } from '@/config';

// Server-side function to check if we should use real backend
const isUsingRealBackend = (req) => {
  // In production, always use real backend
  if (process.env.NODE_ENV === 'production') {
    return true;
  }

  // Check for header sent from client-side
  const useMockData = req.headers['x-use-mock-data'] === 'true';
  return !useMockData;
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { phone } = req.body;

    // Validate phone number
    if (!phone) {
      return res.status(400).json({
        success: false,
        message: 'Phone number is required'
      });
    }

    if (!/^[0-9]{10}$/.test(phone)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid phone number format'
      });
    }

    // Check if we should use real backend or mock data
    if (isUsingRealBackend(req)) {
      try {
        // Call the real backend API
        const apiUrl = `${BACKEND_API_URL}/users/request-otp`;
        console.log(`[API] Requesting OTP from real backend: ${apiUrl}`);
        
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ phone })
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `Backend API error: ${response.status}`);
        }
        
        const data = await response.json();
        return res.status(200).json({
          success: true,
          message: data.message || 'OTP sent successfully',
          source: 'real'
        });
      } catch (error) {
        console.error(`[API] Real backend error, falling back to mock data:`, error);
        // Fall back to mock implementation if real backend fails
        return res.status(200).json({
          success: true,
          message: 'OTP sent successfully (mock)',
          otp: '1234', // Only for development/testing
          source: 'mock',
          fallbackReason: error.message
        });
      }
    } else {
      // Use mock implementation
      console.log(`[API] Using mock implementation (feature flag set to mock)`);
      
      // In a real application, you would:
      // 1. Generate a random OTP
      // 2. Store the OTP with the phone number and expiration time
      // 3. Send the OTP via SMS
      
      // For development/testing, we'll just return a success response with a fixed OTP
      return res.status(200).json({
        success: true,
        message: 'OTP sent successfully (mock)',
        otp: '1234', // Only for development/testing
        source: 'mock'
      });
    }
  } catch (error) {
    console.error('Error sending OTP:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while sending OTP',
      error: error.message || 'Unknown error'
    });
  }
}
