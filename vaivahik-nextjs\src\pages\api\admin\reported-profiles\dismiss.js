// API endpoint for dismissing reported profiles
import { generateMockReportedProfiles } from '@/utils/mockData';

export default function handler(req, res) {
  // Set proper content type header
  res.setHeader('Content-Type', 'application/json');

  try {
    // Only allow POST method
    if (req.method !== 'POST') {
      return res.status(405).json({
        success: false,
        message: 'Method not allowed'
      });
    }

    // Get data from request body
    const { reportId, reason } = req.body;

    // Validate required fields
    if (!reportId) {
      return res.status(400).json({
        success: false,
        message: 'Report ID is required'
      });
    }

    // In a real implementation, this would update the database
    // For now, we'll just return a success response
    return res.status(200).json({
      success: true,
      message: 'Report dismissed successfully',
      data: {
        reportId,
        reason: reason || 'No reason provided',
        status: 'DISMISSED',
        dismissedAt: new Date().toISOString(),
        dismissedBy: 'Admin User'
      }
    });
  } catch (error) {
    console.error('Error dismissing report:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to dismiss report'
    });
  }
}
