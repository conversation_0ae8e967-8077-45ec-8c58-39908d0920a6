# Vaivahik Matrimony Project

This is the main repository for the Vaivahik Matrimony platform, designed specifically for the Maratha community.

## Project Structure

The project consists of two main components:

### 1. Frontend (vaivahik-nextjs)

The frontend is built with Next.js and includes:
- Admin panel for managing users, content, and settings
- Website for users to register, create profiles, and find matches
- Feature flag system for toggling between mock and real data

**Directory**: `vaivahik-nextjs`

### 2. Backend (vaivahik-backend)

The backend is built with Express.js and includes:
- RESTful API endpoints for all functionality
- Authentication and authorization
- Database integration with Prisma ORM
- AI-powered matching algorithm
- Redis for caching and real-time features

**Directory**: `vaivahik-backend`

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- PostgreSQL database
- Redis server

### Setup

1. Clone this repository
2. Set up the frontend:
   ```bash
   cd vaivahik-nextjs
   npm install
   npm run dev
   ```

3. Set up the backend:
   ```bash
   cd vaivahik-backend
   npm install
   npm run dev
   ```

4. Access the application:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000/api

## Development Guidelines

1. **Code Organization**:
   - Keep frontend code in the vaivahik-nextjs directory
   - Keep backend code in the VaivahikAI directory
   - Use feature branches for new development

2. **Testing**:
   - Write unit tests for critical functionality
   - Test both frontend and backend components

3. **Deployment**:
   - Use environment variables for configuration
   - Set up proper error handling and logging

## Features

- User registration and profile creation
- Profile verification and moderation
- AI-powered matching algorithm
- Admin panel for managing users and content
- Real-time notifications and messaging
- Payment integration
- Biodata generation

## Backup and Maintenance

- Regular database backups
- Dependency updates
- Security audits
- Performance monitoring

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
