import { useState } from 'react';
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);
import { mockDataUtils } from '@/config/apiConfig';
import {
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Checkbox,
  Divider,
  Button,
  Alert,
  AlertTitle,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Chip,
  LinearProgress
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Security as SecurityIcon,
  Speed as SpeedIcon,
  Code as CodeIcon,
  Storage as StorageIcon,
  BugReport as BugReportIcon,
  Devices as DevicesIcon,
  CloudUpload as CloudUploadIcon
} from '@mui/icons-material';

export default function ProductionChecklist() {
  const [useMockData, setUseMockData] = useState(mockDataUtils.isMockDataEnabled());
  const [checklist, setChecklist] = useState({
    mockData: {
      title: 'Mock Data',
      items: [
        { id: 'mock-1', text: 'All admin pages have mock data', checked: true },
        { id: 'mock-2', text: 'Mock data structure matches expected API responses', checked: true },
        { id: 'mock-3', text: 'Toggle mechanism for switching between mock and real data works', checked: true },
        { id: 'mock-4', text: 'Backend connector is configured for easy transition', checked: true }
      ]
    },
    security: {
      title: 'Security',
      items: [
        { id: 'sec-1', text: 'Authentication is properly implemented', checked: true },
        { id: 'sec-2', text: 'Authorization checks are in place', checked: true },
        { id: 'sec-3', text: 'Sensitive data is not exposed in client-side code', checked: true },
        { id: 'sec-4', text: 'API endpoints are protected', checked: true },
        { id: 'sec-5', text: 'CSRF protection is implemented', checked: false }
      ]
    },
    performance: {
      title: 'Performance',
      items: [
        { id: 'perf-1', text: 'Code splitting is implemented', checked: true },
        { id: 'perf-2', text: 'Images are optimized', checked: true },
        { id: 'perf-3', text: 'Caching is implemented where appropriate', checked: true },
        { id: 'perf-4', text: 'Bundle size is optimized', checked: false },
        { id: 'perf-5', text: 'Lazy loading is implemented for large components', checked: true }
      ]
    },
    responsiveness: {
      title: 'Responsiveness',
      items: [
        { id: 'resp-1', text: 'All admin pages are mobile-responsive', checked: true },
        { id: 'resp-2', text: 'Tables have horizontal scrolling on mobile', checked: true },
        { id: 'resp-3', text: 'Forms are usable on mobile devices', checked: true },
        { id: 'resp-4', text: 'Sidebar collapses properly on small screens', checked: true }
      ]
    },
    testing: {
      title: 'Testing',
      items: [
        { id: 'test-1', text: 'All forms have validation', checked: true },
        { id: 'test-2', text: 'Error handling is implemented', checked: true },
        { id: 'test-3', text: 'Edge cases are handled', checked: false },
        { id: 'test-4', text: 'Cross-browser testing is completed', checked: false }
      ]
    },
    deployment: {
      title: 'Deployment',
      items: [
        { id: 'dep-1', text: 'Environment variables are configured', checked: false },
        { id: 'dep-2', text: 'Build process is tested', checked: false },
        { id: 'dep-3', text: 'Deployment documentation is created', checked: true },
        { id: 'dep-4', text: 'Backup strategy is in place', checked: false }
      ]
    }
  });

  const handleToggle = (category, itemId) => {
    setChecklist({
      ...checklist,
      [category]: {
        ...checklist[category],
        items: checklist[category].items.map(item => 
          item.id === itemId ? { ...item, checked: !item.checked } : item
        )
      }
    });
  };

  const calculateProgress = (category) => {
    const items = checklist[category].items;
    const checkedCount = items.filter(item => item.checked).length;
    return (checkedCount / items.length) * 100;
  };

  const calculateTotalProgress = () => {
    let totalItems = 0;
    let checkedItems = 0;

    Object.keys(checklist).forEach(category => {
      totalItems += checklist[category].items.length;
      checkedItems += checklist[category].items.filter(item => item.checked).length;
    });

    return (checkedItems / totalItems) * 100;
  };

  const getStatusColor = (progress) => {
    if (progress < 50) return 'error';
    if (progress < 80) return 'warning';
    return 'success';
  };

  const handleToggleMockData = () => {
    mockDataUtils.toggleMockData();
  };

  return (
    <EnhancedAdminLayout title="Production Readiness Checklist">
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Production Readiness Checklist
        </Typography>

        <Alert severity="info" sx={{ mb: 3 }}>
          <AlertTitle>Preparing for Production</AlertTitle>
          Use this checklist to ensure the admin panel is ready for production deployment. Check off items as they are completed.
        </Alert>

        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardHeader 
                title="Overall Progress" 
                action={
                  <Chip 
                    label={`${Math.round(calculateTotalProgress())}%`} 
                    color={getStatusColor(calculateTotalProgress())} 
                  />
                }
              />
              <CardContent>
                <LinearProgress 
                  variant="determinate" 
                  value={calculateTotalProgress()} 
                  color={getStatusColor(calculateTotalProgress())}
                  sx={{ height: 10, borderRadius: 5, mb: 2 }}
                />
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>
                  {Object.keys(checklist).map(category => (
                    <Chip
                      key={category}
                      label={`${checklist[category].title}: ${Math.round(calculateProgress(category))}%`}
                      color={getStatusColor(calculateProgress(category))}
                      variant="outlined"
                    />
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card>
              <CardHeader title="Mock Data Status" />
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Typography variant="body1" sx={{ mr: 2 }}>
                    Currently using: <strong>{useMockData ? 'Mock Data' : 'Real API'}</strong>
                  </Typography>
                  <Button 
                    variant="contained" 
                    color={useMockData ? 'warning' : 'success'}
                    onClick={handleToggleMockData}
                    size="small"
                  >
                    {useMockData ? 'Switch to Real API' : 'Switch to Mock Data'}
                  </Button>
                </Box>
                
                <Alert severity={useMockData ? 'warning' : 'success'}>
                  {useMockData 
                    ? 'You are currently using mock data. Switch to real API before deploying to production.' 
                    : 'You are currently using the real API. The system is ready for production.'}
                </Alert>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Stepper orientation="vertical" sx={{ mb: 3 }}>
          {Object.keys(checklist).map((category) => (
            <Step key={category} active={true} completed={calculateProgress(category) === 100}>
              <StepLabel>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography variant="h6">{checklist[category].title}</Typography>
                  <Chip 
                    label={`${Math.round(calculateProgress(category))}%`} 
                    color={getStatusColor(calculateProgress(category))} 
                    size="small"
                    sx={{ ml: 2 }}
                  />
                </Box>
              </StepLabel>
              <StepContent>
                <Paper sx={{ p: 2 }}>
                  <List>
                    {checklist[category].items.map((item) => (
                      <ListItem key={item.id} dense button onClick={() => handleToggle(category, item.id)}>
                        <ListItemIcon>
                          <Checkbox
                            edge="start"
                            checked={item.checked}
                            tabIndex={-1}
                            disableRipple
                            color="primary"
                          />
                        </ListItemIcon>
                        <ListItemText primary={item.text} />
                      </ListItem>
                    ))}
                  </List>
                </Paper>
              </StepContent>
            </Step>
          ))}
        </Stepper>

        <Box sx={{ mt: 4 }}>
          <Typography variant="h5" gutterBottom>
            Next Steps
          </Typography>
          <Divider sx={{ mb: 2 }} />
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardHeader 
                  title="Connect to Real Backend" 
                  avatar={<StorageIcon color="primary" />}
                />
                <CardContent>
                  <Typography paragraph>
                    Once the backend is ready, follow these steps to connect to the real API:
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                      <ListItemText primary="Configure environment variables in .env.local" />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                      <ListItemText primary="Set NEXT_PUBLIC_USE_MOCK_DATA to 'false'" />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                      <ListItemText primary="Set NEXT_PUBLIC_API_BASE_URL to your backend URL" />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                      <ListItemText primary="Test all API endpoints with the real backend" />
                    </ListItem>
                  </List>
                  <Button 
                    variant="contained" 
                    color="primary" 
                    fullWidth
                    sx={{ mt: 2 }}
                    href="/admin/documentation/backend-connection"
                  >
                    View Backend Connection Guide
                  </Button>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardHeader 
                  title="Deploy to Production" 
                  avatar={<CloudUploadIcon color="primary" />}
                />
                <CardContent>
                  <Typography paragraph>
                    Follow these steps to deploy the admin panel to production:
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                      <ListItemText primary="Run build process: npm run build" />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                      <ListItemText primary="Test the production build locally: npm run start" />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                      <ListItemText primary="Configure production environment variables" />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                      <ListItemText primary="Deploy to your hosting provider" />
                    </ListItem>
                  </List>
                  <Button 
                    variant="contained" 
                    color="primary" 
                    fullWidth
                    sx={{ mt: 2 }}
                    href="/admin/documentation/deployment"
                  >
                    View Deployment Guide
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </Box>
    </EnhancedAdminLayout>
  );
}
