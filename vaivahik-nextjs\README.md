# Vaivahik Matrimony App

This is a [Next.js](https://nextjs.org) project for the Vaivahik Matrimony platform, designed specifically for the Maratha community.

## Directory Structure

This project uses a structured organization for better maintainability:

### Main Directories
- `/src/pages` - Contains admin panel pages and API routes
- `/src/website/pages` - Contains website-specific pages (user-facing matrimony platform)
- `/src/components` - Shared components used across the application
- `/src/utils` - Utility functions and helpers
- `/src/styles` - CSS and styling files
- `/src/contexts` - React context providers
- `/src/hooks` - Custom React hooks

### Key Features
- **Admin Panel**: Located in `/src/pages/admin`
- **Website**: Located in `/src/website/pages`
- **API Routes**: Located in `/src/pages/api`
- **Feature Flags**: Toggle between mock and real data using the feature flags system

**Important**: Please read the [Directory Structure Policy](./DIRECTORY_POLICY.md) before adding or modifying pages.

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `pages/index.js`. The page auto-updates as you edit the file.

[API routes](https://nextjs.org/docs/pages/building-your-application/routing/api-routes) can be accessed on [http://localhost:3000/api/hello](http://localhost:3000/api/hello). This endpoint can be edited in `pages/api/hello.js`.

The `pages/api` directory is mapped to `/api/*`. Files in this directory are treated as [API routes](https://nextjs.org/docs/pages/building-your-application/routing/api-routes) instead of React pages.

This project uses [`next/font`](https://nextjs.org/docs/pages/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn-pages-router) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/pages/building-your-application/deploying) for more details.
