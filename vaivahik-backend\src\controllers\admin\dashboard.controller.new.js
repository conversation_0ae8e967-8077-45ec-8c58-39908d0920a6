/**
 * Admin Dashboard Controller
 *
 * This controller handles requests for the admin dashboard,
 * including dashboard statistics, recent activity, and recent users.
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const {
  getCache,
  setCache,
  getOrSetCache,
  invalidateDashboardStatsCache,
  CACHE_PREFIXES
} = require('../../../redis/cacheService');

/**
 * Get dashboard data for the admin dashboard
 *
 * @route GET /api/admin/dashboard
 * @access Admin
 */
exports.getDashboardData = async (req, res) => {
  try {
    const { timeframe = 'day', refresh = 'false' } = req.query;
    const shouldRefresh = refresh === 'true';

    // Create a cache key based on the timeframe
    const cacheKey = `${CACHE_PREFIXES.DASHBOARD_STATS}${timeframe}`;

    // If refresh is requested, invalidate the cache
    if (shouldRefresh) {
      await invalidateDashboardStatsCache();
    }

    // If not refreshing, try to get from cache
    if (!shouldRefresh) {
      const cachedData = await getOrSetCache(
        cacheKey,
        async () => {
          return await fetchDashboardData(timeframe);
        },
        1800 // Cache for 30 minutes
      );

      return res.status(200).json(cachedData);
    }

    // If refresh is requested, fetch fresh data
    const dashboardData = await fetchDashboardData(timeframe);
    return res.status(200).json(dashboardData);
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    res.status(500).json({
      message: 'Failed to fetch dashboard data',
      error: error.message
    });
  }
};

/**
 * Fetch dashboard data from the database
 * @param {string} timeframe - Timeframe for the data (day, week, month, year)
 * @returns {Object} Dashboard data
 */
async function fetchDashboardData(timeframe) {
  // Calculate date range based on timeframe
  const now = new Date();
  let startDate;

  switch (timeframe) {
    case 'week':
      startDate = new Date(now);
      startDate.setDate(now.getDate() - 7);
      break;
    case 'month':
      startDate = new Date(now);
      startDate.setMonth(now.getMonth() - 1);
      break;
    case 'year':
      startDate = new Date(now);
      startDate.setFullYear(now.getFullYear() - 1);
      break;
    case 'day':
    default:
      startDate = new Date(now);
      startDate.setDate(now.getDate() - 1);
      break;
  }

  // Get total users
  const totalUsers = await prisma.user.count();

  // Get new users in the timeframe
  const newUsers = await prisma.user.count({
    where: {
      createdAt: {
        gte: startDate
      }
    }
  });

  // Get premium users
  const premiumUsers = await prisma.user.count({
    where: {
      isPremium: true
    }
  });

  // Get new premium users in the timeframe
  const newPremiumUsers = await prisma.user.count({
    where: {
      isPremium: true,
      premiumSince: {
        gte: startDate
      }
    }
  });

  // Get total matches
  const totalMatches = await prisma.match.count();

  // Get new matches in the timeframe
  const newMatches = await prisma.match.count({
    where: {
      createdAt: {
        gte: startDate
      }
    }
  });

  // Get total conversations
  const totalConversations = await prisma.conversation.count();

  // Get new conversations in the timeframe
  const newConversations = await prisma.conversation.count({
    where: {
      createdAt: {
        gte: startDate
      }
    }
  });

  // Get total messages
  const totalMessages = await prisma.message.count();

  // Get new messages in the timeframe
  const newMessages = await prisma.message.count({
    where: {
      createdAt: {
        gte: startDate
      }
    }
  });

  // Get total revenue
  const totalRevenue = await prisma.payment.aggregate({
    _sum: {
      amount: true
    },
    where: {
      status: 'COMPLETED'
    }
  });

  // Get revenue in the timeframe
  const periodRevenue = await prisma.payment.aggregate({
    _sum: {
      amount: true
    },
    where: {
      status: 'COMPLETED',
      createdAt: {
        gte: startDate
      }
    }
  });

  // Get previous period revenue for comparison
  let previousStartDate;
  let previousEndDate = startDate;

  switch (timeframe) {
    case 'week':
      previousStartDate = new Date(startDate);
      previousStartDate.setDate(startDate.getDate() - 7);
      break;
    case 'month':
      previousStartDate = new Date(startDate);
      previousStartDate.setMonth(startDate.getMonth() - 1);
      break;
    case 'year':
      previousStartDate = new Date(startDate);
      previousStartDate.setFullYear(startDate.getFullYear() - 1);
      break;
    case 'day':
    default:
      previousStartDate = new Date(startDate);
      previousStartDate.setDate(startDate.getDate() - 1);
      break;
  }

  const previousPeriodRevenue = await prisma.payment.aggregate({
    _sum: {
      amount: true
    },
    where: {
      status: 'COMPLETED',
      createdAt: {
        gte: previousStartDate,
        lt: previousEndDate
      }
    }
  });

  // Calculate growth percentages
  const userGrowth = totalUsers > 0 ? (newUsers / totalUsers) * 100 : 0;
  const premiumGrowth = premiumUsers > 0 ? (newPremiumUsers / premiumUsers) * 100 : 0;
  const matchGrowth = totalMatches > 0 ? (newMatches / totalMatches) * 100 : 0;
  const conversationGrowth = totalConversations > 0 ? (newConversations / totalConversations) * 100 : 0;
  const messageGrowth = totalMessages > 0 ? (newMessages / totalMessages) * 100 : 0;

  // Calculate revenue growth
  const currentRevenue = periodRevenue._sum.amount || 0;
  const previousRevenue = previousPeriodRevenue._sum.amount || 0;
  const revenueGrowth = previousRevenue > 0
    ? ((currentRevenue - previousRevenue) / previousRevenue) * 100
    : (currentRevenue > 0 ? 100 : 0);

  // Prepare dashboard data
  const dashboardData = {
    timeframe,
    users: {
      total: totalUsers,
      new: newUsers,
      growth: parseFloat(userGrowth.toFixed(2))
    },
    premium: {
      total: premiumUsers,
      new: newPremiumUsers,
      growth: parseFloat(premiumGrowth.toFixed(2))
    },
    matches: {
      total: totalMatches,
      new: newMatches,
      growth: parseFloat(matchGrowth.toFixed(2))
    },
    conversations: {
      total: totalConversations,
      new: newConversations,
      growth: parseFloat(conversationGrowth.toFixed(2))
    },
    messages: {
      total: totalMessages,
      new: newMessages,
      growth: parseFloat(messageGrowth.toFixed(2))
    },
    revenue: {
      total: parseFloat((totalRevenue._sum.amount || 0).toFixed(2)),
      period: parseFloat(currentRevenue.toFixed(2)),
      previous: parseFloat(previousRevenue.toFixed(2)),
      growth: parseFloat(revenueGrowth.toFixed(2))
    }
  };

  return dashboardData;
}

/**
 * Get recent activity for the admin dashboard
 *
 * @route GET /api/admin/dashboard/recent-activity
 * @access Admin
 */
exports.getRecentActivity = async (req, res) => {
  try {
    const { limit = 10, types = '', refresh = 'false' } = req.query;
    const limitNum = parseInt(limit, 10);
    const shouldRefresh = refresh === 'true';

    // Create a cache key based on the query parameters
    const cacheKey = `${CACHE_PREFIXES.DASHBOARD_STATS}activity:limit:${limitNum}:types:${types}`;

    // If refresh is requested, invalidate the cache
    if (shouldRefresh) {
      await invalidateDashboardStatsCache();
    }

    // If not refreshing, try to get from cache
    if (!shouldRefresh) {
      const cachedData = await getOrSetCache(
        cacheKey,
        async () => {
          return await fetchRecentActivity(limitNum, types);
        },
        300 // Cache for 5 minutes
      );

      return res.status(200).json(cachedData);
    }

    // If refresh is requested, fetch fresh data
    const activityData = await fetchRecentActivity(limitNum, types);
    return res.status(200).json(activityData);
  } catch (error) {
    console.error('Error fetching recent activity:', error);
    res.status(500).json({
      message: 'Failed to fetch recent activity',
      error: error.message
    });
  }
};

/**
 * Fetch recent activity data from the database
 * @param {number} limitNum - Number of activities to fetch
 * @param {string} types - Comma-separated list of activity types
 * @returns {Object} Activity data
 */
async function fetchRecentActivity(limitNum, types) {
  // Parse activity types if provided
  const activityTypes = types ? types.split(',') : [];

  // Build where clause for activity types
  const whereClause = activityTypes.length > 0
    ? { type: { in: activityTypes } }
    : {};

  // Get recent activities
  const activities = await prisma.userInteraction.findMany({
    where: whereClause,
    orderBy: {
      createdAt: 'desc'
    },
    take: limitNum,
    include: {
      user: {
        select: {
          id: true,
          profile: {
            select: {
              firstName: true,
              lastName: true,
              photos: {
                where: { isMain: true },
                select: { url: true },
                take: 1
              }
            }
          }
        }
      },
      targetUser: {
        select: {
          id: true,
          profile: {
            select: {
              firstName: true,
              lastName: true,
              photos: {
                where: { isMain: true },
                select: { url: true },
                take: 1
              }
            }
          }
        }
      }
    }
  });

  // Format activities for response
  const formattedActivities = activities.map(activity => ({
    id: activity.id,
    type: activity.type,
    createdAt: activity.createdAt,
    user: {
      id: activity.user.id,
      name: `${activity.user.profile?.firstName || ''} ${activity.user.profile?.lastName || ''}`.trim(),
      photoUrl: activity.user.profile?.photos[0]?.url || null
    },
    targetUser: activity.targetUser ? {
      id: activity.targetUser.id,
      name: `${activity.targetUser.profile?.firstName || ''} ${activity.targetUser.profile?.lastName || ''}`.trim(),
      photoUrl: activity.targetUser.profile?.photos[0]?.url || null
    } : null,
    data: activity.data
  }));

  return {
    activities: formattedActivities,
    total: formattedActivities.length
  };
}

/**
 * Get recent users for the admin dashboard
 *
 * @route GET /api/admin/dashboard/recent-users
 * @access Admin
 */
exports.getRecentUsers = async (req, res) => {
  try {
    const { page = 1, limit = 10, filter = '', refresh = 'false' } = req.query;
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const shouldRefresh = refresh === 'true';

    // Create a cache key based on the query parameters
    const cacheKey = `${CACHE_PREFIXES.DASHBOARD_STATS}users:page:${pageNum}:limit:${limitNum}:filter:${filter}`;

    // If refresh is requested, invalidate the cache
    if (shouldRefresh) {
      await invalidateDashboardStatsCache();
    }

    // If not refreshing, try to get from cache
    if (!shouldRefresh) {
      const cachedData = await getOrSetCache(
        cacheKey,
        async () => {
          return await fetchRecentUsers(pageNum, limitNum, filter);
        },
        300 // Cache for 5 minutes
      );

      return res.status(200).json(cachedData);
    }

    // If refresh is requested, fetch fresh data
    const userData = await fetchRecentUsers(pageNum, limitNum, filter);
    return res.status(200).json(userData);
  } catch (error) {
    console.error('Error fetching recent users:', error);
    res.status(500).json({
      message: 'Failed to fetch recent users',
      error: error.message
    });
  }
};

/**
 * Fetch recent users data from the database
 * @param {number} pageNum - Page number
 * @param {number} limitNum - Number of users per page
 * @param {string} filter - Filter string
 * @returns {Object} User data
 */
async function fetchRecentUsers(pageNum, limitNum, filter) {
  const skip = (pageNum - 1) * limitNum;

  // Build where clause for filtering
  const whereClause = {};

  if (filter) {
    whereClause.OR = [
      { profile: { firstName: { contains: filter, mode: 'insensitive' } } },
      { profile: { lastName: { contains: filter, mode: 'insensitive' } } },
      { phone: { contains: filter } },
      { email: { contains: filter } }
    ];
  }

  // Get recent users
  const users = await prisma.user.findMany({
    where: whereClause,
    orderBy: {
      createdAt: 'desc'
    },
    skip,
    take: limitNum,
    include: {
      profile: {
        select: {
          firstName: true,
          lastName: true,
          gender: true,
          dateOfBirth: true,
          city: true,
          state: true,
          photos: {
            where: { isMain: true },
            select: { url: true },
            take: 1
          }
        }
      }
    }
  });

  // Get total count for pagination
  const totalUsers = await prisma.user.count({
    where: whereClause
  });

  // Format users for response
  const formattedUsers = users.map(user => ({
    id: user.id,
    name: `${user.profile?.firstName || ''} ${user.profile?.lastName || ''}`.trim(),
    photoUrl: user.profile?.photos[0]?.url || null,
    gender: user.profile?.gender || 'UNKNOWN',
    age: user.profile?.dateOfBirth ? calculateAge(user.profile.dateOfBirth) : null,
    location: user.profile?.city && user.profile?.state
      ? `${user.profile.city}, ${user.profile.state}`
      : user.profile?.city || user.profile?.state || 'Unknown',
    phone: user.phone,
    email: user.email,
    isPremium: user.isPremium,
    isVerified: user.isVerified,
    status: user.profileStatus,
    createdAt: user.createdAt
  }));

  return {
    users: formattedUsers,
    total: totalUsers,
    totalPages: Math.ceil(totalUsers / limitNum),
    currentPage: pageNum
  };
}

/**
 * Calculate age from date of birth
 *
 * @param {Date} dateOfBirth - Date of birth
 * @returns {number} Age in years
 */
function calculateAge(dateOfBirth) {
  const today = new Date();
  const birthDate = new Date(dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }

  return age;
}
