import { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  FormLabel,
  Select,
  MenuItem,
  InputLabel,
  FormHelperText,
  Divider,
  Alert,
  LinearProgress,
  Paper,
  Chip,
  Slider,
  Snackbar,
  IconButton,
  Tooltip,
  Checkbox,
  FormControlLabel,
  FormGroup
} from '@mui/material';
import {
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon,
  Help as HelpIcon,
  Favorite as FavoriteIcon,
  School as SchoolIcon,
  Work as WorkIcon,
  LocationOn as LocationIcon,
  Restaurant as RestaurantIcon,
  Group as CommunityIcon
} from '@mui/icons-material';
import FilterChips from '@/components/common/FilterChips';
import { useRouter } from 'next/router';

// Education options with readable labels
const EDUCATION_OPTIONS = [
  'High School',
  'Diploma',
  'Bachelor\'s Degree',
  'Master\'s Degree',
  'Doctorate',
  'Professional Degree',
  'Engineering',
  'Medical Degree',
  'Law Degree',
  'MBA',
  'CA/CS/ICWA'
];

// Education categories for coloring
const EDUCATION_CATEGORIES = {
  'High School': { bgcolor: '#e8f5e9', color: '#1b5e20' },
  'Diploma': { bgcolor: '#e8f5e9', color: '#1b5e20' },
  'Bachelor': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'Master': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'Doctorate': { bgcolor: '#f3e5f5', color: '#6a1b9a' },
  'Professional': { bgcolor: '#f3e5f5', color: '#6a1b9a' },
  'Engineering': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'Medical': { bgcolor: '#f3e5f5', color: '#6a1b9a' },
  'Law': { bgcolor: '#f3e5f5', color: '#6a1b9a' },
  'MBA': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'CA': { bgcolor: '#e3f2fd', color: '#0d47a1' }
};

// Occupation options with readable labels
const OCCUPATION_OPTIONS = [
  // IT & Engineering
  'Software Engineer',
  'IT Professional',
  'Data Scientist',
  'System Administrator',
  'Web Developer',
  'Engineering Professional',
  'Mechanical Engineer',
  'Civil Engineer',
  'Electrical Engineer',

  // Medical & Healthcare
  'Doctor',
  'Dentist',
  'Nurse',
  'Pharmacist',
  'Healthcare Professional',
  'Medical Researcher',

  // Finance & Business
  'Chartered Accountant',
  'Financial Analyst',
  'Investment Banker',
  'Business Owner',
  'Manager',
  'Consultant',
  'Marketing Professional',
  'HR Professional',

  // Education & Research
  'Teacher',
  'Professor',
  'Researcher',
  'Scientist',
  'Education Professional',

  // Government & Public Service
  'Government Employee',
  'Civil Servant',
  'Defense Personnel',
  'Police Officer',

  // Others
  'Lawyer',
  'Architect',
  'Designer',
  'Artist',
  'Writer',
  'Self Employed',
  'Homemaker'
];

// Occupation categories for coloring
const OCCUPATION_CATEGORIES = {
  'Software': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'IT': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'Data': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'System': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'Web': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'Engineer': { bgcolor: '#e3f2fd', color: '#0d47a1' },

  'Doctor': { bgcolor: '#f3e5f5', color: '#6a1b9a' },
  'Dentist': { bgcolor: '#f3e5f5', color: '#6a1b9a' },
  'Nurse': { bgcolor: '#f3e5f5', color: '#6a1b9a' },
  'Pharmacist': { bgcolor: '#f3e5f5', color: '#6a1b9a' },
  'Healthcare': { bgcolor: '#f3e5f5', color: '#6a1b9a' },
  'Medical': { bgcolor: '#f3e5f5', color: '#6a1b9a' },

  'Chartered': { bgcolor: '#fff3e0', color: '#e65100' },
  'Financial': { bgcolor: '#fff3e0', color: '#e65100' },
  'Investment': { bgcolor: '#fff3e0', color: '#e65100' },
  'Business': { bgcolor: '#fff3e0', color: '#e65100' },
  'Manager': { bgcolor: '#fff3e0', color: '#e65100' },
  'Consultant': { bgcolor: '#fff3e0', color: '#e65100' },
  'Marketing': { bgcolor: '#fff3e0', color: '#e65100' },
  'HR': { bgcolor: '#fff3e0', color: '#e65100' },

  'Teacher': { bgcolor: '#e8f5e9', color: '#1b5e20' },
  'Professor': { bgcolor: '#e8f5e9', color: '#1b5e20' },
  'Researcher': { bgcolor: '#e8f5e9', color: '#1b5e20' },
  'Scientist': { bgcolor: '#e8f5e9', color: '#1b5e20' },
  'Education': { bgcolor: '#e8f5e9', color: '#1b5e20' },

  'Government': { bgcolor: '#e0f7fa', color: '#006064' },
  'Civil': { bgcolor: '#e0f7fa', color: '#006064' },
  'Defense': { bgcolor: '#e0f7fa', color: '#006064' },
  'Police': { bgcolor: '#e0f7fa', color: '#006064' },

  'Lawyer': { bgcolor: '#fbe9e7', color: '#bf360c' },
  'Architect': { bgcolor: '#fbe9e7', color: '#bf360c' },
  'Designer': { bgcolor: '#fbe9e7', color: '#bf360c' },
  'Artist': { bgcolor: '#fbe9e7', color: '#bf360c' },
  'Writer': { bgcolor: '#fbe9e7', color: '#bf360c' },
  'Self': { bgcolor: '#fbe9e7', color: '#bf360c' },
  'Homemaker': { bgcolor: '#fbe9e7', color: '#bf360c' }
};

const INCOME_OPTIONS = [
  'ANY', 'UPTO_3L', '3L_5L', '5L_10L', '10L_20L', 'ABOVE_20L'
];

const DIET_OPTIONS = [
  'ANY', 'VEGETARIAN', 'NON_VEGETARIAN', 'EGGETARIAN', 'VEGAN', 'JAIN'
];

const CITIES = [
  'Mumbai', 'Pune', 'Nagpur', 'Thane', 'Nashik', 'Aurangabad', 'Solapur',
  'Kolhapur', 'Amravati', 'Delhi', 'Bangalore', 'Hyderabad', 'Chennai', 'Kolkata'
];

const STATES = [
  'Maharashtra', 'Delhi', 'Karnataka', 'Telangana', 'Tamil Nadu', 'West Bengal',
  'Gujarat', 'Rajasthan', 'Uttar Pradesh', 'Madhya Pradesh'
];

const SUB_CASTES = [
  '96 Kuli Maratha', 'Deshmukh', 'Patil', 'Jadhav', 'Kshatriya Maratha',
  'Kunbi Maratha', 'Gomantak Maratha', 'Other'
];

const PartnerPreferencesForm = ({ userData, onSave, isLoading }) => {
  const router = useRouter();
  const [formData, setFormData] = useState({
    // Age range
    ageMin: 18,
    ageMax: 40,

    // Height range
    heightMin: '4.5',
    heightMax: '6.5',

    // Education & Career
    educationLevel: [],
    occupations: [],
    incomeMin: '',

    // Location preferences
    preferredCities: [],
    preferredStates: [],

    // Maratha specific
    acceptSubCastes: [],
    gotraPreference: '',

    // Lifestyle
    dietPreference: '',

    // Hobbies & Interests
    hobbiesPreference: '',
    interestsPreference: '',

    // Other preferences
    otherPreferences: ''
  });

  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [completionPercentage, setCompletionPercentage] = useState(0);
  const [success, setSuccess] = useState('');

  // Initialize form with user data if available
  useEffect(() => {
    if (userData && userData.preferences) {
      setFormData({
        ...formData,
        ...userData.preferences
      });
    }
  }, [userData]);

  // Calculate completion percentage
  useEffect(() => {
    const requiredFields = ['ageMin', 'ageMax', 'heightMin', 'heightMax'];
    const optionalFields = [
      'educationLevel', 'occupations', 'incomeMin', 'preferredCities',
      'preferredStates', 'acceptSubCastes', 'gotraPreference', 'dietPreference',
      'hobbiesPreference', 'interestsPreference', 'otherPreferences'
    ];

    const totalFields = requiredFields.length + optionalFields.length;
    let completedFields = 0;

    // Count required fields
    requiredFields.forEach(field => {
      if (formData[field] !== undefined && formData[field] !== '') completedFields++;
    });

    // Count optional fields
    optionalFields.forEach(field => {
      if (Array.isArray(formData[field])) {
        if (formData[field].length > 0) completedFields++;
      } else if (formData[field] !== undefined && formData[field] !== '') {
        completedFields++;
      }
    });

    const percentage = Math.round((completedFields / totalFields) * 100);
    setCompletionPercentage(percentage);
  }, [formData]);

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Mark field as touched
    setTouched({ ...touched, [name]: true });

    // Clear error for this field if it exists
    if (errors[name]) {
      const { [name]: _, ...restErrors } = errors;
      setErrors(restErrors);
    }
  };

  // Handle array input change
  const handleArrayChange = (name, value) => {
    setFormData({ ...formData, [name]: value });
    setTouched({ ...touched, [name]: true });

    if (errors[name]) {
      const { [name]: _, ...restErrors } = errors;
      setErrors(restErrors);
    }
  };

  // Handle age range change
  const handleAgeRangeChange = (event, newValue) => {
    setFormData({
      ...formData,
      ageMin: newValue[0],
      ageMax: newValue[1]
    });
    setTouched({ ...touched, ageMin: true, ageMax: true });
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    // Age validation
    if (formData.ageMin < 18) {
      newErrors.ageMin = 'Minimum age must be at least 18';
    }

    if (formData.ageMax > 70) {
      newErrors.ageMax = 'Maximum age must be at most 70';
    }

    if (formData.ageMin > formData.ageMax) {
      newErrors.ageRange = 'Minimum age cannot be greater than maximum age';
    }

    // Height validation
    const heightMin = parseFloat(formData.heightMin);
    const heightMax = parseFloat(formData.heightMax);

    if (isNaN(heightMin) || heightMin < 4.0 || heightMin > 7.0) {
      newErrors.heightMin = 'Minimum height must be between 4.0 and 7.0 feet';
    }

    if (isNaN(heightMax) || heightMax < 4.0 || heightMax > 7.0) {
      newErrors.heightMax = 'Maximum height must be between 4.0 and 7.0 feet';
    }

    if (heightMin > heightMax) {
      newErrors.heightRange = 'Minimum height cannot be greater than maximum height';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Call the onSave function with the form data
    onSave(formData);
    setSuccess('Partner preferences saved successfully!');
  };

  // Handle back button
  const handleBack = () => {
    router.push('/profile');
  };

  return (
    <Box sx={{ mb: 4 }}>
      <Paper elevation={0} sx={{ p: 2, mb: 3, bgcolor: 'primary.light', color: 'primary.contrastText', borderRadius: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <FavoriteIcon sx={{ mr: 1 }} />
          <Typography variant="h5" component="h1">
            Partner Preferences
          </Typography>
        </Box>
        <Typography variant="body2" sx={{ mt: 1 }}>
          Define your preferences to receive more relevant match suggestions.
        </Typography>
        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
          <Box sx={{ flexGrow: 1, mr: 2 }}>
            <LinearProgress
              variant="determinate"
              value={completionPercentage}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: 'rgba(255,255,255,0.3)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: 'white'
                }
              }}
            />
          </Box>
          <Typography variant="body2" fontWeight="bold">
            {completionPercentage}% Complete
          </Typography>
        </Box>
      </Paper>

      <form onSubmit={handleSubmit}>
        <Card elevation={3} sx={{ mb: 3, borderRadius: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              Age & Height Preferences
              <Tooltip title="Specify the age and height range you're looking for">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth error={!!errors.ageRange}>
                  <FormLabel id="age-range-label">
                    Age Range: {formData.ageMin} - {formData.ageMax} years
                  </FormLabel>
                  <Box sx={{ px: 2, py: 1 }}>
                    <Slider
                      value={[formData.ageMin, formData.ageMax]}
                      onChange={handleAgeRangeChange}
                      valueLabelDisplay="auto"
                      min={18}
                      max={70}
                      marks={[
                        { value: 18, label: '18' },
                        { value: 30, label: '30' },
                        { value: 50, label: '50' },
                        { value: 70, label: '70' }
                      ]}
                    />
                  </Box>
                  {errors.ageRange && <FormHelperText error>{errors.ageRange}</FormHelperText>}
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.heightMin}>
                  <FormLabel htmlFor="heightMin">Minimum Height</FormLabel>
                  <Select
                    id="heightMin"
                    name="heightMin"
                    value={formData.heightMin}
                    onChange={handleInputChange}
                    size="small"
                  >
                    <MenuItem value="4.0">4'0" (122 cm)</MenuItem>
                    <MenuItem value="4.5">4'6" (137 cm)</MenuItem>
                    <MenuItem value="5.0">5'0" (152 cm)</MenuItem>
                    <MenuItem value="5.5">5'6" (168 cm)</MenuItem>
                    <MenuItem value="6.0">6'0" (183 cm)</MenuItem>
                    <MenuItem value="6.5">6'6" (198 cm)</MenuItem>
                  </Select>
                  {errors.heightMin && <FormHelperText error>{errors.heightMin}</FormHelperText>}
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.heightMax}>
                  <FormLabel htmlFor="heightMax">Maximum Height</FormLabel>
                  <Select
                    id="heightMax"
                    name="heightMax"
                    value={formData.heightMax}
                    onChange={handleInputChange}
                    size="small"
                  >
                    <MenuItem value="5.0">5'0" (152 cm)</MenuItem>
                    <MenuItem value="5.5">5'6" (168 cm)</MenuItem>
                    <MenuItem value="6.0">6'0" (183 cm)</MenuItem>
                    <MenuItem value="6.5">6'6" (198 cm)</MenuItem>
                    <MenuItem value="7.0">7'0" (213 cm)</MenuItem>
                  </Select>
                  {errors.heightMax && <FormHelperText error>{errors.heightMax}</FormHelperText>}
                </FormControl>
              </Grid>
              {errors.heightRange && (
                <Grid item xs={12}>
                  <Alert severity="error">{errors.heightRange}</Alert>
                </Grid>
              )}
            </Grid>
          </CardContent>
        </Card>

        <Card elevation={3} sx={{ mb: 3, borderRadius: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              Education & Career
              <Tooltip title="Specify education and career preferences">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <SchoolIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" fontWeight="medium">
                    Education Level
                  </Typography>
                </Box>
                <FilterChips
                  label="Preferred Education"
                  helperText="Select the education levels you prefer in a partner"
                  options={EDUCATION_OPTIONS}
                  selectedOptions={formData.educationLevel}
                  onChange={(newValue) => handleArrayChange('educationLevel', newValue)}
                  allowCustom={false}
                  maxSelections={5}
                  categoryColors={EDUCATION_CATEGORIES}
                  showSelectedCount={true}
                />
              </Grid>

              <Grid item xs={12} sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <WorkIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" fontWeight="medium">
                    Occupation
                  </Typography>
                </Box>
                <FilterChips
                  label="Preferred Occupations"
                  helperText="Select the occupations you prefer in a partner"
                  options={OCCUPATION_OPTIONS}
                  selectedOptions={formData.occupations}
                  onChange={(newValue) => handleArrayChange('occupations', newValue)}
                  allowCustom={true}
                  customPlaceholder="Add another occupation..."
                  maxSelections={8}
                  categoryColors={OCCUPATION_CATEGORIES}
                  showSelectedCount={true}
                />
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth>
                  <FormLabel id="incomeMin-label">Minimum Annual Income</FormLabel>
                  <Select
                    labelId="incomeMin-label"
                    id="incomeMin"
                    name="incomeMin"
                    value={formData.incomeMin}
                    onChange={handleInputChange}
                    size="small"
                  >
                    <MenuItem value="">No Preference</MenuItem>
                    <MenuItem value="ANY">Any Income</MenuItem>
                    <MenuItem value="UPTO_3L">Upto 3 Lakhs</MenuItem>
                    <MenuItem value="3L_5L">3 - 5 Lakhs</MenuItem>
                    <MenuItem value="5L_10L">5 - 10 Lakhs</MenuItem>
                    <MenuItem value="10L_20L">10 - 20 Lakhs</MenuItem>
                    <MenuItem value="ABOVE_20L">Above 20 Lakhs</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Card elevation={3} sx={{ mb: 3, borderRadius: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              Location Preferences
              <Tooltip title="Specify preferred locations for your partner">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <LocationIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" fontWeight="medium">
                    Preferred Cities
                  </Typography>
                </Box>
                <FilterChips
                  label="Cities"
                  helperText="Select cities where you'd prefer your partner to be located"
                  options={CITIES}
                  selectedOptions={formData.preferredCities}
                  onChange={(newValue) => handleArrayChange('preferredCities', newValue)}
                  allowCustom={true}
                  customPlaceholder="Add another city..."
                  maxSelections={10}
                  showSelectedCount={true}
                />
              </Grid>

              <Grid item xs={12} sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <LocationIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" fontWeight="medium">
                    Preferred States
                  </Typography>
                </Box>
                <FilterChips
                  label="States"
                  helperText="Select states where you'd prefer your partner to be located"
                  options={STATES}
                  selectedOptions={formData.preferredStates}
                  onChange={(newValue) => handleArrayChange('preferredStates', newValue)}
                  allowCustom={true}
                  customPlaceholder="Add another state..."
                  maxSelections={5}
                  showSelectedCount={true}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Card elevation={3} sx={{ mb: 3, borderRadius: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              Community Preferences
              <Tooltip title="Specify community preferences">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <FormLabel id="acceptSubCastes-label">Acceptable Sub-castes</FormLabel>
                  <Autocomplete
                    multiple
                    id="acceptSubCastes"
                    options={SUB_CASTES}
                    value={formData.acceptSubCastes}
                    onChange={(event, newValue) => handleArrayChange('acceptSubCastes', newValue)}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        placeholder="Select acceptable sub-castes"
                        size="small"
                      />
                    )}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip
                          label={option}
                          {...getTagProps({ index })}
                          size="small"
                        />
                      ))
                    }
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth>
                  <FormLabel htmlFor="gotraPreference">Gotra Preference (Optional)</FormLabel>
                  <TextField
                    id="gotraPreference"
                    name="gotraPreference"
                    value={formData.gotraPreference}
                    onChange={handleInputChange}
                    placeholder="Enter preferred gotra if any"
                    size="small"
                  />
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Card elevation={3} sx={{ mb: 3, borderRadius: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              Lifestyle & Interests
              <Tooltip title="Specify lifestyle and interest preferences">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <FormLabel id="dietPreference-label">Diet Preference</FormLabel>
                  <Select
                    labelId="dietPreference-label"
                    id="dietPreference"
                    name="dietPreference"
                    value={formData.dietPreference}
                    onChange={handleInputChange}
                    size="small"
                  >
                    <MenuItem value="">No Preference</MenuItem>
                    <MenuItem value="ANY">Any Diet</MenuItem>
                    <MenuItem value="VEGETARIAN">Vegetarian</MenuItem>
                    <MenuItem value="NON_VEGETARIAN">Non-Vegetarian</MenuItem>
                    <MenuItem value="EGGETARIAN">Eggetarian</MenuItem>
                    <MenuItem value="VEGAN">Vegan</MenuItem>
                    <MenuItem value="JAIN">Jain</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth>
                  <FormLabel htmlFor="hobbiesPreference">Hobbies Preference (Optional)</FormLabel>
                  <TextField
                    id="hobbiesPreference"
                    name="hobbiesPreference"
                    value={formData.hobbiesPreference}
                    onChange={handleInputChange}
                    placeholder="Describe preferred hobbies (e.g., reading, traveling, cooking)"
                    multiline
                    rows={2}
                    size="small"
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth>
                  <FormLabel htmlFor="interestsPreference">Interests Preference (Optional)</FormLabel>
                  <TextField
                    id="interestsPreference"
                    name="interestsPreference"
                    value={formData.interestsPreference}
                    onChange={handleInputChange}
                    placeholder="Describe preferred interests (e.g., music, art, sports)"
                    multiline
                    rows={2}
                    size="small"
                  />
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Card elevation={3} sx={{ mb: 3, borderRadius: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              Additional Preferences
              <Tooltip title="Any other preferences you'd like to specify">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <FormLabel htmlFor="otherPreferences">Other Preferences (Optional)</FormLabel>
                  <TextField
                    id="otherPreferences"
                    name="otherPreferences"
                    value={formData.otherPreferences}
                    onChange={handleInputChange}
                    placeholder="Describe any other preferences you have for your partner"
                    multiline
                    rows={4}
                    size="small"
                  />
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={handleBack}
          >
            Back to Profile
          </Button>

          <Button
            type="submit"
            variant="contained"
            color="primary"
            startIcon={isLoading ? null : <SaveIcon />}
            disabled={isLoading}
          >
            {isLoading ? 'Saving...' : 'Save Preferences'}
          </Button>
        </Box>
      </form>

      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess('')}
        message={success}
      />
    </Box>
  );
};

export default PartnerPreferencesForm;
