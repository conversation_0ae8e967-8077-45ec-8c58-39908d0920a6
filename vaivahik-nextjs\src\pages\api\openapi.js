/**
 * OpenAPI Redirect
 *
 * This API route redirects to the OpenAPI specification endpoint.
 * It ensures compatibility with the API Viewer tool in the admin panel.
 */

export default function handler(req, res) {
  try {
    // Redirect to the static OpenAPI specification file
    res.redirect('/api-docs/openapi.json');
  } catch (error) {
    console.error('Error redirecting to OpenAPI specification:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to redirect to OpenAPI specification'
    });
  }
}
