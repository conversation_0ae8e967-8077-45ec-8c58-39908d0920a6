{"success": true, "templates": [{"id": 1, "name": "Traditional Heritage", "description": "A traditional template with classic Maratha design elements", "thumbnail": "/images/biodata-templates/traditional-heritage-thumb.jpg", "previewImage": "/images/biodata-templates/traditional-heritage-preview.jpg", "designFile": "/templates/biodata/traditional-heritage.html", "genderOrientation": "male", "category": "traditional", "isActive": true, "isPremium": false, "price": 0, "currency": "INR", "headerText": "<PERSON><PERSON><PERSON>", "footerText": "Powered by <PERSON><PERSON><PERSON><PERSON><PERSON> - The Premier Maratha Matrimony Platform", "primaryColor": "#8B0000", "secondaryColor": "#FFD700", "fontFamily": "<PERSON><PERSON><PERSON>, sans-serif", "sections": [{"id": "personal", "name": "Personal Information", "order": 1, "isRequired": true, "fields": ["name", "gender", "dob", "birthTime", "birthPlace", "height", "weight", "bloodGroup", "complexion", "maritalStatus", "manglik"]}, {"id": "contact", "name": "Contact Information", "order": 2, "isRequired": true, "fields": ["address", "city", "state", "country", "pinCode", "phone", "email"]}, {"id": "education", "name": "Education & Career", "order": 3, "isRequired": true, "fields": ["education", "university", "occupation", "employerName", "jobTitle", "annualIncome"]}, {"id": "family", "name": "Family Details", "order": 4, "isRequired": true, "fields": ["<PERSON><PERSON><PERSON>", "fatherOccupation", "<PERSON><PERSON><PERSON>", "motherOccupation", "siblings", "familyType", "family<PERSON><PERSON>ues", "familyStatus"]}, {"id": "horoscope", "name": "Horoscope Details", "order": 5, "isRequired": true, "fields": ["rashi", "nakshatra", "gotra", "gan", "nadi", "charan"]}, {"id": "preferences", "name": "Partner Preferences", "order": 6, "isRequired": false, "fields": ["<PERSON><PERSON><PERSON><PERSON>", "heightRange", "educationPref", "occupationPref", "locationPref", "maritalStatusPref"]}, {"id": "lifestyle", "name": "Lifestyle & Habits", "order": 7, "isRequired": false, "fields": ["diet", "smoking", "drinking", "hobbies", "interests"]}], "createdAt": "2023-01-15T10:30:00Z", "updatedAt": "2023-01-15T10:30:00Z", "downloadCount": 1250}, {"id": 2, "name": "Modern Minimalist", "description": "A sleek, modern template with minimalist design", "thumbnail": "/images/biodata-templates/modern-minimalist-thumb.jpg", "previewImage": "/images/biodata-templates/modern-minimalist-preview.jpg", "designFile": "/templates/biodata/modern-minimalist.html", "genderOrientation": "neutral", "category": "modern", "isActive": true, "isPremium": false, "price": 0, "currency": "INR", "headerText": "<PERSON><PERSON><PERSON>", "footerText": "Powered by <PERSON><PERSON><PERSON><PERSON><PERSON> - The Premier Maratha Matrimony Platform", "primaryColor": "#4A148C", "secondaryColor": "#E1BEE7", "fontFamily": "Montserrat, sans-serif", "sections": [{"id": "personal", "name": "Personal Information", "order": 1, "isRequired": true, "fields": ["name", "gender", "dob", "birthTime", "birthPlace", "height", "weight", "bloodGroup", "complexion", "maritalStatus", "manglik"]}, {"id": "education", "name": "Education & Career", "order": 2, "isRequired": true, "fields": ["education", "university", "occupation", "employerName", "jobTitle", "annualIncome"]}, {"id": "family", "name": "Family Background", "order": 3, "isRequired": true, "fields": ["<PERSON><PERSON><PERSON>", "fatherOccupation", "<PERSON><PERSON><PERSON>", "motherOccupation", "siblings", "familyType", "family<PERSON><PERSON>ues", "familyStatus"]}, {"id": "contact", "name": "Contact Details", "order": 4, "isRequired": true, "fields": ["address", "city", "state", "country", "pinCode", "phone", "email"]}, {"id": "horoscope", "name": "Horoscope Information", "order": 5, "isRequired": true, "fields": ["rashi", "nakshatra", "gotra", "gan", "nadi", "charan"]}, {"id": "lifestyle", "name": "Lifestyle & Interests", "order": 6, "isRequired": false, "fields": ["diet", "smoking", "drinking", "hobbies", "interests"]}, {"id": "preferences", "name": "Partner Preferences", "order": 7, "isRequired": false, "fields": ["<PERSON><PERSON><PERSON><PERSON>", "heightRange", "educationPref", "occupationPref", "locationPref", "maritalStatusPref"]}], "createdAt": "2023-02-10T14:45:00Z", "updatedAt": "2023-02-10T14:45:00Z", "downloadCount": 980}, {"id": 3, "name": "Professional Classic", "description": "A professional template highlighting career achievements", "thumbnail": "/images/biodata-templates/professional-classic-thumb.jpg", "previewImage": "/images/biodata-templates/professional-classic-preview.jpg", "designFile": "/templates/biodata/professional-classic.html", "genderOrientation": "male", "category": "professional", "isActive": true, "isPremium": true, "price": 199, "currency": "INR", "headerText": "<PERSON><PERSON><PERSON>", "footerText": "Powered by <PERSON><PERSON><PERSON><PERSON><PERSON> - The Premier Maratha Matrimony Platform", "primaryColor": "#01579B", "secondaryColor": "#81D4FA", "fontFamily": "Roboto, sans-serif", "sections": [{"id": "personal", "name": "Personal Profile", "order": 1, "isRequired": true, "fields": ["name", "gender", "dob", "birthPlace", "height", "weight", "bloodGroup", "maritalStatus"]}, {"id": "professional", "name": "Professional Background", "order": 2, "isRequired": true, "fields": ["education", "university", "occupation", "employerName", "jobTitle", "annualIncome", "workExperience", "achievements"]}, {"id": "family", "name": "Family Information", "order": 3, "isRequired": true, "fields": ["<PERSON><PERSON><PERSON>", "fatherOccupation", "<PERSON><PERSON><PERSON>", "motherOccupation", "siblings", "familyType", "family<PERSON><PERSON>ues"]}, {"id": "horoscope", "name": "Horoscope Details", "order": 4, "isRequired": true, "fields": ["rashi", "nakshatra", "gotra", "manglik"]}, {"id": "lifestyle", "name": "Lifestyle & Interests", "order": 5, "isRequired": false, "fields": ["diet", "smoking", "drinking", "hobbies", "interests", "languages"]}, {"id": "preferences", "name": "Partner Preferences", "order": 6, "isRequired": false, "fields": ["<PERSON><PERSON><PERSON><PERSON>", "heightRange", "educationPref", "occupationPref", "locationPref"]}, {"id": "contact", "name": "Contact Information", "order": 7, "isRequired": true, "fields": ["address", "city", "state", "country", "phone", "email"]}], "createdAt": "2023-03-20T09:15:00Z", "updatedAt": "2023-03-20T09:15:00Z", "downloadCount": 450}, {"id": 4, "name": "Cultural Grace", "description": "A template celebrating Maratha cultural heritage", "thumbnail": "/images/biodata-templates/cultural-grace-thumb.jpg", "previewImage": "/images/biodata-templates/cultural-grace-preview.jpg", "designFile": "/templates/biodata/cultural-grace.html", "genderOrientation": "female", "category": "traditional", "isActive": true, "isPremium": true, "price": 199, "currency": "INR", "headerText": "<PERSON><PERSON><PERSON>", "footerText": "Powered by <PERSON><PERSON><PERSON><PERSON><PERSON> - The Premier Maratha Matrimony Platform", "primaryColor": "#BF360C", "secondaryColor": "#FFCCBC", "fontFamily": "Lato, sans-serif", "sections": [{"id": "personal", "name": "Personal Details", "order": 1, "isRequired": true, "fields": ["name", "gender", "dob", "birthTime", "birthPlace", "height", "weight", "bloodGroup", "complexion", "maritalStatus", "manglik"]}, {"id": "cultural", "name": "Cultural Background", "order": 2, "isRequired": true, "fields": ["religion", "caste", "subCaste", "gotra", "mother<PERSON><PERSON>ue", "languages", "culturalValues"]}, {"id": "education", "name": "Education & Profession", "order": 3, "isRequired": true, "fields": ["education", "university", "occupation", "employerName", "jobTitle", "annualIncome"]}, {"id": "family", "name": "Family Details", "order": 4, "isRequired": true, "fields": ["<PERSON><PERSON><PERSON>", "fatherOccupation", "<PERSON><PERSON><PERSON>", "motherOccupation", "siblings", "familyType", "family<PERSON><PERSON>ues", "familyStatus"]}, {"id": "horoscope", "name": "Horoscope Information", "order": 5, "isRequired": true, "fields": ["rashi", "nakshatra", "gotra", "gan", "nadi", "charan"]}, {"id": "lifestyle", "name": "Lifestyle & Habits", "order": 6, "isRequired": false, "fields": ["diet", "smoking", "drinking", "hobbies", "interests"]}, {"id": "preferences", "name": "Partner Preferences", "order": 7, "isRequired": false, "fields": ["<PERSON><PERSON><PERSON><PERSON>", "heightRange", "educationPref", "occupationPref", "locationPref", "maritalStatusPref"]}, {"id": "contact", "name": "Contact Information", "order": 8, "isRequired": true, "fields": ["address", "city", "state", "country", "pinCode", "phone", "email"]}], "createdAt": "2023-04-05T11:30:00Z", "updatedAt": "2023-04-05T11:30:00Z", "downloadCount": 320}, {"id": 5, "name": "Contemporary Chic", "description": "A clean, contemporary template with modern aesthetics", "thumbnail": "/images/biodata-templates/contemporary-chic-thumb.jpg", "previewImage": "/images/biodata-templates/contemporary-chic-preview.jpg", "designFile": "/templates/biodata/contemporary-chic.html", "genderOrientation": "female", "category": "modern", "isActive": true, "isPremium": false, "price": 0, "currency": "INR", "headerText": "<PERSON><PERSON><PERSON>", "footerText": "Powered by <PERSON><PERSON><PERSON><PERSON><PERSON> - The Premier Maratha Matrimony Platform", "primaryColor": "#212121", "secondaryColor": "#F5F5F5", "fontFamily": "Open Sans, sans-serif", "sections": [{"id": "personal", "name": "About Me", "order": 1, "isRequired": true, "fields": ["name", "gender", "dob", "age", "height", "weight", "bloodGroup", "maritalStatus"]}, {"id": "education", "name": "Education & Career", "order": 2, "isRequired": true, "fields": ["education", "university", "occupation", "employerName", "jobTitle", "annualIncome"]}, {"id": "family", "name": "Family", "order": 3, "isRequired": true, "fields": ["<PERSON><PERSON><PERSON>", "fatherOccupation", "<PERSON><PERSON><PERSON>", "motherOccupation", "siblings", "familyType"]}, {"id": "horoscope", "name": "Horoscope", "order": 4, "isRequired": true, "fields": ["rashi", "nakshatra", "gotra", "manglik"]}, {"id": "lifestyle", "name": "Lifestyle", "order": 5, "isRequired": false, "fields": ["diet", "smoking", "drinking", "hobbies", "interests"]}, {"id": "preferences", "name": "Looking For", "order": 6, "isRequired": false, "fields": ["<PERSON><PERSON><PERSON><PERSON>", "heightRange", "educationPref", "occupationPref", "locationPref"]}, {"id": "contact", "name": "Contact", "order": 7, "isRequired": true, "fields": ["city", "state", "country", "phone", "email"]}], "createdAt": "2023-05-12T15:45:00Z", "updatedAt": "2023-05-12T15:45:00Z", "downloadCount": 780}, {"id": 6, "name": "<PERSON><PERSON>t <PERSON>", "description": "An elegant template with floral design elements", "thumbnail": "/images/biodata-templates/elegant-floral-thumb.jpg", "previewImage": "/images/biodata-templates/elegant-floral-preview.jpg", "designFile": "/templates/biodata/elegant-floral.html", "genderOrientation": "female", "category": "premium", "isActive": true, "isPremium": true, "price": 299, "currency": "INR", "headerText": "<PERSON><PERSON><PERSON>", "footerText": "Powered by <PERSON><PERSON><PERSON><PERSON><PERSON> - The Premier Maratha Matrimony Platform", "primaryColor": "#B71C1C", "secondaryColor": "#FFD700", "fontFamily": "Playfair Display, serif", "sections": [{"id": "personal", "name": "Personal Profile", "order": 1, "isRequired": true, "fields": ["name", "gender", "dob", "birthTime", "birthPlace", "height", "weight", "bloodGroup", "complexion", "maritalStatus", "manglik"]}, {"id": "family", "name": "Family Lineage", "order": 2, "isRequired": true, "fields": ["<PERSON><PERSON><PERSON>", "fatherOccupation", "<PERSON><PERSON><PERSON>", "motherOccupation", "siblings", "familyType", "family<PERSON><PERSON>ues", "familyStatus", "<PERSON><PERSON><PERSON>"]}, {"id": "education", "name": "Education & Profession", "order": 3, "isRequired": true, "fields": ["education", "university", "occupation", "employerName", "jobTitle", "annualIncome", "assets"]}, {"id": "horoscope", "name": "Astrological Details", "order": 4, "isRequired": true, "fields": ["rashi", "nakshatra", "gotra", "gan", "nadi", "charan", "manglik", "horoscopeMatch"]}, {"id": "lifestyle", "name": "Lifestyle & Interests", "order": 5, "isRequired": false, "fields": ["diet", "smoking", "drinking", "hobbies", "interests", "languages", "values"]}, {"id": "preferences", "name": "Partner Preferences", "order": 6, "isRequired": false, "fields": ["<PERSON><PERSON><PERSON><PERSON>", "heightRange", "educationPref", "occupationPref", "locationPref", "maritalStatusPref", "familyValuesPref"]}, {"id": "contact", "name": "Contact Information", "order": 7, "isRequired": true, "fields": ["address", "city", "state", "country", "pinCode", "phone", "email"]}], "createdAt": "2023-06-08T10:20:00Z", "updatedAt": "2023-06-08T10:20:00Z", "downloadCount": 290}, {"id": 7, "name": "Modern Artistic", "description": "A creative template with artistic design elements", "thumbnail": "/images/biodata-templates/modern-artistic-thumb.jpg", "previewImage": "/images/biodata-templates/modern-artistic-preview.jpg", "designFile": "/templates/biodata/modern-artistic.html", "genderOrientation": "neutral", "category": "modern", "isActive": true, "isPremium": false, "price": 0, "currency": "INR", "headerText": "<PERSON><PERSON><PERSON>", "footerText": "Powered by <PERSON><PERSON><PERSON><PERSON><PERSON> - The Premier Maratha Matrimony Platform", "primaryColor": "#3F51B5", "secondaryColor": "#C5CAE9", "fontFamily": "Ra<PERSON><PERSON>, sans-serif", "sections": [{"id": "personal", "name": "Personal Information", "order": 1, "isRequired": true, "fields": ["name", "gender", "dob", "age", "height", "weight", "bloodGroup", "maritalStatus"]}, {"id": "education", "name": "Education & Career", "order": 2, "isRequired": true, "fields": ["education", "university", "occupation", "employerName", "jobTitle", "annualIncome"]}, {"id": "family", "name": "Family", "order": 3, "isRequired": true, "fields": ["<PERSON><PERSON><PERSON>", "fatherOccupation", "<PERSON><PERSON><PERSON>", "motherOccupation", "siblings", "familyType"]}, {"id": "horoscope", "name": "Horoscope", "order": 4, "isRequired": true, "fields": ["rashi", "nakshatra", "gotra", "manglik"]}, {"id": "lifestyle", "name": "Lifestyle", "order": 5, "isRequired": false, "fields": ["diet", "smoking", "drinking", "hobbies", "interests"]}], "createdAt": "2023-07-15T13:25:00Z", "updatedAt": "2023-07-15T13:25:00Z", "downloadCount": 420}, {"id": 8, "name": "Executive Premium", "description": "A premium template for professionals with executive style", "thumbnail": "/images/biodata-templates/executive-premium-thumb.jpg", "previewImage": "/images/biodata-templates/executive-premium-preview.jpg", "designFile": "/templates/biodata/executive-premium.html", "genderOrientation": "male", "category": "premium", "isActive": true, "isPremium": true, "price": 299, "currency": "INR", "headerText": "<PERSON><PERSON><PERSON>", "footerText": "Powered by <PERSON><PERSON><PERSON><PERSON><PERSON> - The Premier Maratha Matrimony Platform", "primaryColor": "#1A237E", "secondaryColor": "#C5CAE9", "fontFamily": "Montserrat, sans-serif", "sections": [{"id": "personal", "name": "Personal Profile", "order": 1, "isRequired": true, "fields": ["name", "gender", "dob", "birthPlace", "height", "weight", "bloodGroup", "maritalStatus"]}, {"id": "professional", "name": "Professional Background", "order": 2, "isRequired": true, "fields": ["education", "university", "occupation", "employerName", "jobTitle", "annualIncome", "workExperience", "achievements"]}, {"id": "family", "name": "Family Information", "order": 3, "isRequired": true, "fields": ["<PERSON><PERSON><PERSON>", "fatherOccupation", "<PERSON><PERSON><PERSON>", "motherOccupation", "siblings", "familyType", "family<PERSON><PERSON>ues"]}, {"id": "horoscope", "name": "Horoscope Details", "order": 4, "isRequired": true, "fields": ["rashi", "nakshatra", "gotra", "manglik"]}, {"id": "lifestyle", "name": "Lifestyle & Interests", "order": 5, "isRequired": false, "fields": ["diet", "smoking", "drinking", "hobbies", "interests", "languages"]}], "createdAt": "2023-08-20T09:40:00Z", "updatedAt": "2023-08-20T09:40:00Z", "downloadCount": 180}], "pagination": {"total": 8, "page": 1, "limit": 10, "totalPages": 1}, "categories": [{"id": "traditional", "name": "Traditional", "description": "Templates with traditional Maratha design elements", "count": 2}, {"id": "modern", "name": "Modern", "description": "Contemporary templates with modern aesthetics", "count": 3}, {"id": "professional", "name": "Professional", "description": "Templates highlighting professional achievements", "count": 1}, {"id": "premium", "name": "Premium", "description": "Exclusive premium templates with unique designs", "count": 2}], "genderOrientations": [{"id": "male", "name": "Male-oriented", "count": 3}, {"id": "female", "name": "Female-oriented", "count": 3}, {"id": "neutral", "name": "Gender-neutral", "count": 2}], "stats": {"totalTemplates": 8, "freeTemplates": 4, "premiumTemplates": 4, "totalDownloads": 4670, "mostPopularTemplate": "Traditional Heritage"}}