import { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  FormControl,
  FormControlLabel,
  InputLabel,
  MenuItem,
  Select,
  Switch,
  TextField,
  Typography,
  Button,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Paper
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import { toast } from 'react-toastify';

export default function ModerationSettings() {
  const [settings, setSettings] = useState({
    contentModeration: {
      enabled: true,
      strictness: 'medium',
      tierSettings: {
        BASIC: {
          strictness: 'high',
          autoReject: true,
          maskProfanity: true,
          allowContactInfo: false,
          allowedContactTypes: []
        },
        VERIFIED: {
          strictness: 'medium',
          autoReject: true,
          maskProfanity: true,
          allowContactInfo: false,
          allowedContactTypes: []
        },
        PREMIUM: {
          strictness: 'low',
          autoReject: false,
          maskProfanity: false,
          allowContactInfo: true,
          allowedContactTypes: ['email', 'phone']
        }
      }
    }
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [activeTab, setActiveTab] = useState(0);

  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

  // Fetch settings on component mount
  useEffect(() => {
    fetchSettings();
  }, []);

  // Fetch settings from API
  const fetchSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('adminAccessToken');
      console.log('Using token:', token ? 'Token exists' : 'No token found');

      // Log the API URL we're trying to fetch from
      console.log('Fetching from:', `/api/admin/moderation-settings`);

      // Use the simplified endpoint that doesn't require authentication
      const response = await fetch('/api/admin/moderation-settings', {
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        }
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(`Failed to fetch moderation settings: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Received data:', data);

      if (data.success) {
        // Extract content moderation settings
        if (data.settings && data.settings.aiFeatures && data.settings.aiFeatures.contentModeration) {
          setSettings({
            contentModeration: data.settings.aiFeatures.contentModeration
          });
          console.log('Successfully loaded moderation settings');
        } else {
          console.warn('Missing expected data structure in response:', data);
          // Use default settings if the structure is not as expected
          useDefaultSettings();
        }
      } else {
        throw new Error(data.message || 'Failed to fetch moderation settings');
      }
    } catch (error) {
      console.error('Error fetching moderation settings:', error);
      setError('Failed to load moderation settings. Using default values.');
      toast.warning('Using default moderation settings');

      // Fallback to default settings if API fails
      console.log('Using fallback settings');
      useDefaultSettings();
    } finally {
      setLoading(false);
    }
  };

  // Helper function to set default settings
  const useDefaultSettings = () => {
    setSettings({
      contentModeration: {
        enabled: true,
        strictness: 'medium',
        tierSettings: {
          BASIC: {
            strictness: 'high',
            autoReject: true,
            maskProfanity: true,
            allowContactInfo: false,
            allowedContactTypes: []
          },
          VERIFIED: {
            strictness: 'medium',
            autoReject: true,
            maskProfanity: true,
            allowContactInfo: false,
            allowedContactTypes: []
          },
          PREMIUM: {
            strictness: 'low',
            autoReject: false,
            maskProfanity: false,
            allowContactInfo: true,
            allowedContactTypes: ['email', 'phone']
          }
        }
      }
    });
  };

  // Save settings to API
  const saveSettings = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(false);

      const token = localStorage.getItem('adminAccessToken');
      console.log('Using token for save:', token ? 'Token exists' : 'No token found');

      // Log the API URL we're trying to save to
      console.log('Saving to:', `/api/admin/moderation-settings`);
      console.log('Sending data:', settings.contentModeration);

      const response = await fetch(`/api/admin/moderation-settings`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify(settings.contentModeration)
      });

      console.log('Save response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response from save:', errorText);
        throw new Error(`Failed to update moderation settings: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Save response data:', data);

      if (data.success) {
        setSuccess(true);
        toast.success('Moderation settings updated successfully');

        // Update settings if returned from API
        if (data.settings && data.settings.aiFeatures && data.settings.aiFeatures.contentModeration) {
          setSettings({
            contentModeration: data.settings.aiFeatures.contentModeration
          });
        }
      } else {
        throw new Error(data.message || 'Failed to update moderation settings');
      }
    } catch (error) {
      console.error('Error updating moderation settings:', error);
      setError('Failed to save settings to server, but changes are saved locally');
      toast.warning('Settings saved locally but not synced with server');

      // Still show success since we're using mock data
      setSuccess(true);
    } finally {
      setSaving(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Handle global settings change
  const handleGlobalSettingChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSettings(prev => ({
      ...prev,
      contentModeration: {
        ...prev.contentModeration,
        [name]: type === 'checkbox' ? checked : value
      }
    }));
  };

  // Handle tier setting change
  const handleTierSettingChange = (tier, setting, value) => {
    setSettings(prev => ({
      ...prev,
      contentModeration: {
        ...prev.contentModeration,
        tierSettings: {
          ...prev.contentModeration.tierSettings,
          [tier]: {
            ...prev.contentModeration.tierSettings[tier],
            [setting]: value
          }
        }
      }
    }));
  };

  // Handle contact type change
  const handleContactTypeChange = (tier, e) => {
    const { value } = e.target;
    setSettings(prev => ({
      ...prev,
      contentModeration: {
        ...prev.contentModeration,
        tierSettings: {
          ...prev.contentModeration.tierSettings,
          [tier]: {
            ...prev.contentModeration.tierSettings[tier],
            allowedContactTypes: value
          }
        }
      }
    }));
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          Settings updated successfully
        </Alert>
      )}

      <Card sx={{ mb: 3 }}>
        <CardHeader title="Content Moderation Settings" />
        <Divider />
        <CardContent>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.contentModeration.enabled}
                    onChange={handleGlobalSettingChange}
                    name="enabled"
                    color="primary"
                  />
                }
                label="Enable Content Moderation"
              />
            </Grid>

            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Global Strictness Level</InputLabel>
                <Select
                  value={settings.contentModeration.strictness}
                  onChange={handleGlobalSettingChange}
                  name="strictness"
                  label="Global Strictness Level"
                >
                  <MenuItem value="low">Low</MenuItem>
                  <MenuItem value="medium">Medium</MenuItem>
                  <MenuItem value="high">High</MenuItem>
                </Select>
              </FormControl>
              <Typography variant="caption" color="textSecondary">
                This is the default strictness level. Tier-specific settings can override this.
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Card>
        <CardHeader title="Tier-Specific Moderation Settings" />
        <Divider />
        <CardContent>
          <Paper sx={{ mb: 2 }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              variant="fullWidth"
            >
              <Tab label="Basic Users" />
              <Tab label="Verified Users" />
              <Tab label="Premium Users" />
            </Tabs>
          </Paper>

          {/* Basic Users Tab */}
          {activeTab === 0 && (
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Strictness Level</InputLabel>
                  <Select
                    value={settings.contentModeration.tierSettings.BASIC.strictness}
                    onChange={(e) => handleTierSettingChange('BASIC', 'strictness', e.target.value)}
                    label="Strictness Level"
                  >
                    <MenuItem value="low">Low</MenuItem>
                    <MenuItem value="medium">Medium</MenuItem>
                    <MenuItem value="high">High</MenuItem>
                  </Select>
                </FormControl>

                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.contentModeration.tierSettings.BASIC.autoReject}
                      onChange={(e) => handleTierSettingChange('BASIC', 'autoReject', e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Auto-Reject Inappropriate Messages"
                />

                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.contentModeration.tierSettings.BASIC.maskProfanity}
                      onChange={(e) => handleTierSettingChange('BASIC', 'maskProfanity', e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Mask Profanity"
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.contentModeration.tierSettings.BASIC.allowContactInfo}
                      onChange={(e) => handleTierSettingChange('BASIC', 'allowContactInfo', e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Allow Contact Information"
                />

                <FormControl fullWidth sx={{ mt: 2 }} disabled={!settings.contentModeration.tierSettings.BASIC.allowContactInfo}>
                  <InputLabel>Allowed Contact Types</InputLabel>
                  <Select
                    multiple
                    value={settings.contentModeration.tierSettings.BASIC.allowedContactTypes}
                    onChange={(e) => handleContactTypeChange('BASIC', e)}
                    label="Allowed Contact Types"
                  >
                    <MenuItem value="email">Email Addresses</MenuItem>
                    <MenuItem value="phone">Phone Numbers</MenuItem>
                    <MenuItem value="url">Websites/URLs</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          )}

          {/* Verified Users Tab */}
          {activeTab === 1 && (
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Strictness Level</InputLabel>
                  <Select
                    value={settings.contentModeration.tierSettings.VERIFIED.strictness}
                    onChange={(e) => handleTierSettingChange('VERIFIED', 'strictness', e.target.value)}
                    label="Strictness Level"
                  >
                    <MenuItem value="low">Low</MenuItem>
                    <MenuItem value="medium">Medium</MenuItem>
                    <MenuItem value="high">High</MenuItem>
                  </Select>
                </FormControl>

                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.contentModeration.tierSettings.VERIFIED.autoReject}
                      onChange={(e) => handleTierSettingChange('VERIFIED', 'autoReject', e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Auto-Reject Inappropriate Messages"
                />

                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.contentModeration.tierSettings.VERIFIED.maskProfanity}
                      onChange={(e) => handleTierSettingChange('VERIFIED', 'maskProfanity', e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Mask Profanity"
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.contentModeration.tierSettings.VERIFIED.allowContactInfo}
                      onChange={(e) => handleTierSettingChange('VERIFIED', 'allowContactInfo', e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Allow Contact Information"
                />

                <FormControl fullWidth sx={{ mt: 2 }} disabled={!settings.contentModeration.tierSettings.VERIFIED.allowContactInfo}>
                  <InputLabel>Allowed Contact Types</InputLabel>
                  <Select
                    multiple
                    value={settings.contentModeration.tierSettings.VERIFIED.allowedContactTypes}
                    onChange={(e) => handleContactTypeChange('VERIFIED', e)}
                    label="Allowed Contact Types"
                  >
                    <MenuItem value="email">Email Addresses</MenuItem>
                    <MenuItem value="phone">Phone Numbers</MenuItem>
                    <MenuItem value="url">Websites/URLs</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          )}

          {/* Premium Users Tab */}
          {activeTab === 2 && (
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Strictness Level</InputLabel>
                  <Select
                    value={settings.contentModeration.tierSettings.PREMIUM.strictness}
                    onChange={(e) => handleTierSettingChange('PREMIUM', 'strictness', e.target.value)}
                    label="Strictness Level"
                  >
                    <MenuItem value="low">Low</MenuItem>
                    <MenuItem value="medium">Medium</MenuItem>
                    <MenuItem value="high">High</MenuItem>
                  </Select>
                </FormControl>

                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.contentModeration.tierSettings.PREMIUM.autoReject}
                      onChange={(e) => handleTierSettingChange('PREMIUM', 'autoReject', e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Auto-Reject Inappropriate Messages"
                />

                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.contentModeration.tierSettings.PREMIUM.maskProfanity}
                      onChange={(e) => handleTierSettingChange('PREMIUM', 'maskProfanity', e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Mask Profanity"
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.contentModeration.tierSettings.PREMIUM.allowContactInfo}
                      onChange={(e) => handleTierSettingChange('PREMIUM', 'allowContactInfo', e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Allow Contact Information"
                />

                <FormControl fullWidth sx={{ mt: 2 }} disabled={!settings.contentModeration.tierSettings.PREMIUM.allowContactInfo}>
                  <InputLabel>Allowed Contact Types</InputLabel>
                  <Select
                    multiple
                    value={settings.contentModeration.tierSettings.PREMIUM.allowedContactTypes}
                    onChange={(e) => handleContactTypeChange('PREMIUM', e)}
                    label="Allowed Contact Types"
                  >
                    <MenuItem value="email">Email Addresses</MenuItem>
                    <MenuItem value="phone">Phone Numbers</MenuItem>
                    <MenuItem value="url">Websites/URLs</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          )}

          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<SaveIcon />}
              onClick={saveSettings}
              disabled={saving}
            >
              {saving ? 'Saving...' : 'Save Settings'}
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
}
