#!/bin/bash

# VPS Deployment Script for Vaivahik Matrimony Platform
# Comprehensive deployment with MCP server and AI features

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="vaivahik"
DOMAIN="your-domain.com"  # Replace with your actual domain
DB_NAME="vaivahik_db"
DB_USER="vaivahik_user"
NODE_VERSION="18"
PM2_APP_NAME="vaivahik-backend"
MCP_APP_NAME="vaivahik-mcp"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to install Node.js
install_nodejs() {
    print_status "Installing Node.js ${NODE_VERSION}..."
    
    if command_exists node; then
        NODE_CURRENT=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$NODE_CURRENT" -ge "$NODE_VERSION" ]; then
            print_success "Node.js ${NODE_CURRENT} is already installed"
            return
        fi
    fi
    
    # Install Node.js using NodeSource repository
    curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | sudo -E bash -
    sudo apt-get install -y nodejs
    
    print_success "Node.js installed successfully"
}

# Function to install PostgreSQL
install_postgresql() {
    print_status "Installing PostgreSQL..."
    
    if command_exists psql; then
        print_success "PostgreSQL is already installed"
        return
    fi
    
    sudo apt-get update
    sudo apt-get install -y postgresql postgresql-contrib
    
    # Start and enable PostgreSQL
    sudo systemctl start postgresql
    sudo systemctl enable postgresql
    
    print_success "PostgreSQL installed and started"
}

# Function to install Redis
install_redis() {
    print_status "Installing Redis..."
    
    if command_exists redis-server; then
        print_success "Redis is already installed"
        return
    fi
    
    sudo apt-get install -y redis-server
    
    # Configure Redis
    sudo sed -i 's/supervised no/supervised systemd/' /etc/redis/redis.conf
    
    # Start and enable Redis
    sudo systemctl restart redis.service
    sudo systemctl enable redis
    
    print_success "Redis installed and configured"
}

# Function to install Nginx
install_nginx() {
    print_status "Installing Nginx..."
    
    if command_exists nginx; then
        print_success "Nginx is already installed"
        return
    fi
    
    sudo apt-get install -y nginx
    
    # Start and enable Nginx
    sudo systemctl start nginx
    sudo systemctl enable nginx
    
    print_success "Nginx installed and started"
}

# Function to install PM2
install_pm2() {
    print_status "Installing PM2..."
    
    if command_exists pm2; then
        print_success "PM2 is already installed"
        return
    fi
    
    sudo npm install -g pm2
    
    # Setup PM2 startup script
    pm2 startup
    
    print_success "PM2 installed successfully"
}

# Function to setup database
setup_database() {
    print_status "Setting up PostgreSQL database..."
    
    # Generate random password
    DB_PASSWORD=$(openssl rand -base64 32)
    
    # Create database and user
    sudo -u postgres psql << EOF
CREATE DATABASE ${DB_NAME};
CREATE USER ${DB_USER} WITH ENCRYPTED PASSWORD '${DB_PASSWORD}';
GRANT ALL PRIVILEGES ON DATABASE ${DB_NAME} TO ${DB_USER};
ALTER USER ${DB_USER} CREATEDB;
\q
EOF
    
    # Save database credentials
    echo "DATABASE_URL=\"postgresql://${DB_USER}:${DB_PASSWORD}@localhost:5432/${DB_NAME}\"" > .env.database
    
    print_success "Database setup completed"
    print_warning "Database credentials saved to .env.database"
}

# Function to setup SSL with Let's Encrypt
setup_ssl() {
    print_status "Setting up SSL with Let's Encrypt..."
    
    # Install Certbot
    sudo apt-get install -y certbot python3-certbot-nginx
    
    # Get SSL certificate
    sudo certbot --nginx -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email admin@$DOMAIN
    
    # Setup auto-renewal
    sudo crontab -l | { cat; echo "0 12 * * * /usr/bin/certbot renew --quiet"; } | sudo crontab -
    
    print_success "SSL certificate installed and auto-renewal configured"
}

# Function to configure Nginx
configure_nginx() {
    print_status "Configuring Nginx..."
    
    # Create Nginx configuration
    sudo tee /etc/nginx/sites-available/$PROJECT_NAME << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN www.$DOMAIN;

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Frontend (Next.js)
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    # Socket.IO
    location /socket.io/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # MCP Server (WebSocket)
    location /mcp/ {
        proxy_pass http://localhost:8001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;
}
EOF

    # Enable the site
    sudo ln -sf /etc/nginx/sites-available/$PROJECT_NAME /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # Test Nginx configuration
    sudo nginx -t
    
    # Reload Nginx
    sudo systemctl reload nginx
    
    print_success "Nginx configured successfully"
}

# Function to deploy application
deploy_application() {
    print_status "Deploying application..."
    
    # Create application directory
    sudo mkdir -p /var/www/$PROJECT_NAME
    sudo chown -R $USER:$USER /var/www/$PROJECT_NAME
    
    # Copy application files (assuming they're in current directory)
    cp -r . /var/www/$PROJECT_NAME/
    cd /var/www/$PROJECT_NAME
    
    # Install backend dependencies
    cd vaivahik-backend
    npm install --production
    
    # Install frontend dependencies
    cd ../vaivahik-nextjs
    npm install
    npm run build
    
    # Setup environment variables
    cd ../vaivahik-backend
    cp .env.example .env
    
    # Add database URL from setup
    if [ -f .env.database ]; then
        cat .env.database >> .env
    fi
    
    # Generate Prisma client
    npx prisma generate
    
    # Run database migrations
    npx prisma migrate deploy
    
    print_success "Application deployed successfully"
}

# Function to setup PM2 processes
setup_pm2_processes() {
    print_status "Setting up PM2 processes..."
    
    cd /var/www/$PROJECT_NAME
    
    # Create PM2 ecosystem file
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [
    {
      name: '${PM2_APP_NAME}',
      script: 'vaivahik-backend/server.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 8000,
        ENABLE_ML_SERVICE: 'true',
        ENABLE_MCP_SERVER: 'true'
      },
      error_file: './logs/backend-error.log',
      out_file: './logs/backend-out.log',
      log_file: './logs/backend-combined.log',
      time: true
    },
    {
      name: 'vaivahik-frontend',
      script: 'npm',
      args: 'start',
      cwd: './vaivahik-nextjs',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      error_file: './logs/frontend-error.log',
      out_file: './logs/frontend-out.log',
      log_file: './logs/frontend-combined.log',
      time: true
    }
  ]
};
EOF

    # Create logs directory
    mkdir -p logs
    
    # Start applications with PM2
    pm2 start ecosystem.config.js
    
    # Save PM2 configuration
    pm2 save
    
    print_success "PM2 processes configured and started"
}

# Function to setup monitoring
setup_monitoring() {
    print_status "Setting up monitoring..."
    
    # Install PM2 monitoring
    pm2 install pm2-logrotate
    
    # Configure log rotation
    pm2 set pm2-logrotate:max_size 10M
    pm2 set pm2-logrotate:retain 30
    pm2 set pm2-logrotate:compress true
    
    print_success "Monitoring setup completed"
}

# Function to setup firewall
setup_firewall() {
    print_status "Setting up firewall..."
    
    # Install and configure UFW
    sudo ufw --force reset
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    
    # Allow SSH
    sudo ufw allow ssh
    
    # Allow HTTP and HTTPS
    sudo ufw allow 80
    sudo ufw allow 443
    
    # Enable firewall
    sudo ufw --force enable
    
    print_success "Firewall configured successfully"
}

# Main deployment function
main() {
    print_status "Starting VPS deployment for Vaivahik Matrimony Platform..."
    
    # Update system
    print_status "Updating system packages..."
    sudo apt-get update && sudo apt-get upgrade -y
    
    # Install required packages
    sudo apt-get install -y curl wget git build-essential software-properties-common
    
    # Install all components
    install_nodejs
    install_postgresql
    install_redis
    install_nginx
    install_pm2
    
    # Setup database
    setup_database
    
    # Deploy application
    deploy_application
    
    # Configure Nginx
    configure_nginx
    
    # Setup SSL (comment out if domain is not ready)
    # setup_ssl
    
    # Setup PM2 processes
    setup_pm2_processes
    
    # Setup monitoring
    setup_monitoring
    
    # Setup firewall
    setup_firewall
    
    print_success "Deployment completed successfully!"
    print_status "Your application should now be accessible at:"
    print_status "Frontend: http://$DOMAIN"
    print_status "Backend API: http://$DOMAIN/api"
    print_status "MCP Server: ws://$DOMAIN/mcp"
    
    print_warning "Next steps:"
    print_warning "1. Update your domain DNS to point to this server"
    print_warning "2. Run SSL setup: sudo certbot --nginx -d $DOMAIN"
    print_warning "3. Configure your environment variables in /var/www/$PROJECT_NAME/vaivahik-backend/.env"
    print_warning "4. Monitor logs with: pm2 logs"
    print_warning "5. Check status with: pm2 status"
}

# Run main function
main "$@"
