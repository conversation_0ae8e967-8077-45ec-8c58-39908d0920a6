/**
 * Performance Metrics API Endpoint
 * Collects and analyzes performance data
 */

// Simple in-memory storage for demo (replace with database in production)
let performanceMetrics = [];
const MAX_METRICS = 1000;

export default async function handler(req, res) {
  if (req.method === 'POST') {
    try {
      const metricData = req.body;
      
      // Add metadata
      const metricEntry = {
        id: Date.now() + Math.random(),
        ...metricData,
        ip: req.ip || req.connection.remoteAddress,
        userAgent: req.headers['user-agent'],
        receivedAt: new Date().toISOString()
      };

      // Add to metrics
      performanceMetrics.unshift(metricEntry);
      
      // Keep only recent metrics
      if (performanceMetrics.length > MAX_METRICS) {
        performanceMetrics = performanceMetrics.slice(0, MAX_METRICS);
      }

      res.status(200).json({
        success: true,
        message: 'Performance metric logged successfully'
      });

    } catch (error) {
      console.error('Performance metric logging failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to log performance metric'
      });
    }
  } else if (req.method === 'GET') {
    try {
      const { timeRange = '24h' } = req.query;
      
      // Calculate time filter
      const now = new Date();
      let startTime;
      
      switch (timeRange) {
        case '1h':
          startTime = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case '24h':
          startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        default:
          startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      }

      // Filter metrics by time range
      const filteredMetrics = performanceMetrics.filter(metric => 
        new Date(metric.timestamp || metric.receivedAt) >= startTime
      );

      // Generate analytics
      const analytics = generatePerformanceAnalytics(filteredMetrics);

      res.status(200).json({
        success: true,
        data: analytics,
        timeRange,
        totalMetrics: filteredMetrics.length
      });

    } catch (error) {
      console.error('Performance analytics failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get performance analytics'
      });
    }
  } else {
    res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }
}

function generatePerformanceAnalytics(metrics) {
  // Group metrics by type
  const metricsByType = metrics.reduce((acc, metric) => {
    const type = metric.metric || 'Unknown';
    if (!acc[type]) acc[type] = [];
    acc[type].push(metric.value);
    return acc;
  }, {});

  // Calculate statistics for each metric type
  const metricStats = {};
  Object.entries(metricsByType).forEach(([type, values]) => {
    const sorted = values.sort((a, b) => a - b);
    metricStats[type] = {
      count: values.length,
      average: values.reduce((sum, val) => sum + val, 0) / values.length,
      median: sorted[Math.floor(sorted.length / 2)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      min: Math.min(...values),
      max: Math.max(...values)
    };
  });

  // Performance trends over time
  const hourlyPerformance = metrics.reduce((acc, metric) => {
    const hour = new Date(metric.timestamp || metric.receivedAt).getHours();
    if (!acc[hour]) acc[hour] = { LCP: [], FID: [], CLS: [] };
    
    if (metric.metric && acc[hour][metric.metric]) {
      acc[hour][metric.metric].push(metric.value);
    }
    
    return acc;
  }, {});

  // Calculate hourly averages
  const hourlyAverages = Array.from({ length: 24 }, (_, hour) => {
    const hourData = hourlyPerformance[hour] || { LCP: [], FID: [], CLS: [] };
    
    return {
      hour,
      LCP: hourData.LCP.length > 0 ? 
        hourData.LCP.reduce((sum, val) => sum + val, 0) / hourData.LCP.length : 0,
      FID: hourData.FID.length > 0 ? 
        hourData.FID.reduce((sum, val) => sum + val, 0) / hourData.FID.length : 0,
      CLS: hourData.CLS.length > 0 ? 
        hourData.CLS.reduce((sum, val) => sum + val, 0) / hourData.CLS.length : 0
    };
  });

  // Performance score calculation
  const calculateScore = (lcp, fid, cls) => {
    let score = 100;
    
    // LCP scoring (good: <2.5s, needs improvement: 2.5-4s, poor: >4s)
    if (lcp > 4000) score -= 40;
    else if (lcp > 2500) score -= 20;
    
    // FID scoring (good: <100ms, needs improvement: 100-300ms, poor: >300ms)
    if (fid > 300) score -= 30;
    else if (fid > 100) score -= 15;
    
    // CLS scoring (good: <0.1, needs improvement: 0.1-0.25, poor: >0.25)
    if (cls > 0.25) score -= 30;
    else if (cls > 0.1) score -= 15;
    
    return Math.max(0, score);
  };

  const avgLCP = metricStats.LCP?.average || 0;
  const avgFID = metricStats.FID?.average || 0;
  const avgCLS = metricStats.CLS?.average || 0;
  const performanceScore = calculateScore(avgLCP, avgFID, avgCLS);

  // Performance recommendations
  const recommendations = [];
  
  if (avgLCP > 2500) {
    recommendations.push({
      type: 'LCP',
      severity: avgLCP > 4000 ? 'high' : 'medium',
      message: 'Largest Contentful Paint is slow. Consider optimizing images and reducing server response time.',
      value: avgLCP
    });
  }
  
  if (avgFID > 100) {
    recommendations.push({
      type: 'FID',
      severity: avgFID > 300 ? 'high' : 'medium',
      message: 'First Input Delay is high. Consider reducing JavaScript execution time.',
      value: avgFID
    });
  }
  
  if (avgCLS > 0.1) {
    recommendations.push({
      type: 'CLS',
      severity: avgCLS > 0.25 ? 'high' : 'medium',
      message: 'Cumulative Layout Shift is high. Ensure images and ads have dimensions.',
      value: avgCLS
    });
  }

  return {
    summary: {
      totalMetrics: metrics.length,
      performanceScore: Math.round(performanceScore),
      avgLCP: Math.round(avgLCP),
      avgFID: Math.round(avgFID),
      avgCLS: Math.round(avgCLS * 1000) / 1000
    },
    metricStats,
    hourlyAverages,
    recommendations,
    recentMetrics: metrics.slice(0, 20).map(metric => ({
      id: metric.id,
      metric: metric.metric,
      value: metric.value,
      timestamp: metric.timestamp || metric.receivedAt
    }))
  };
}
