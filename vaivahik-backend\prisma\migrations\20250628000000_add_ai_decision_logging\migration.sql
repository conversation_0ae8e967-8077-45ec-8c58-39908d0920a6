-- Add AI Decision Logging table
CREATE TABLE "ai_decision_logs" (
    "id" TEXT NOT NULL,
    "algorithm" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "input_data" TEXT NOT NULL,
    "result" TEXT NOT NULL,
    "metadata" TEXT,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "session_id" TEXT,
    "request_id" TEXT,
    "execution_time_ms" INTEGER,
    "success" BOOLEAN NOT NULL DEFAULT true,
    "error_message" TEXT,

    CONSTRAINT "ai_decision_logs_pkey" PRIMARY KEY ("id")
);

-- Add indexes for performance
CREATE INDEX "ai_decision_logs_user_id_idx" ON "ai_decision_logs"("user_id");
CREATE INDEX "ai_decision_logs_algorithm_idx" ON "ai_decision_logs"("algorithm");
CREATE INDEX "ai_decision_logs_timestamp_idx" ON "ai_decision_logs"("timestamp");
CREATE INDEX "ai_decision_logs_session_id_idx" ON "ai_decision_logs"("session_id");

-- Add foreign key constraint to users table
ALTER TABLE "ai_decision_logs" ADD CONSTRAINT "ai_decision_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
