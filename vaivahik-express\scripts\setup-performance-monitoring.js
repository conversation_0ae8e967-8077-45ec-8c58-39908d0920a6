/**
 * Performance Monitoring Setup Script
 * 
 * This script sets up performance monitoring for the API.
 * It creates a dashboard for monitoring performance metrics.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  routesDir: path.join(__dirname, '../src/routes'),
  controllersDir: path.join(__dirname, '../src/controllers'),
  publicDir: path.join(__dirname, '../public')
};

// Create public directory if it doesn't exist
if (!fs.existsSync(config.publicDir)) {
  fs.mkdirSync(config.publicDir, { recursive: true });
}

// Create performance dashboard directory
const dashboardDir = path.join(config.publicDir, 'performance-dashboard');
if (!fs.existsSync(dashboardDir)) {
  fs.mkdirSync(dashboardDir, { recursive: true });
}

/**
 * Create performance dashboard HTML
 */
function createDashboard() {
  const dashboardPath = path.join(dashboardDir, 'index.html');
  
  const dashboardContent = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vaivahik API Performance Dashboard</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      padding: 20px;
      background-color: #f8f9fa;
    }
    .dashboard-header {
      background-color: #6200ea;
      color: white;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .card {
      margin-bottom: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    .card-header {
      background-color: #7e57c2;
      color: white;
      font-weight: bold;
    }
    .metric-card {
      text-align: center;
      padding: 20px;
    }
    .metric-value {
      font-size: 2rem;
      font-weight: bold;
      color: #6200ea;
    }
    .metric-label {
      font-size: 1rem;
      color: #666;
    }
    .slow-request {
      background-color: #fff8e1;
      border-left: 4px solid #ffc107;
      padding: 10px;
      margin-bottom: 10px;
      border-radius: 4px;
    }
    .very-slow-request {
      background-color: #ffebee;
      border-left: 4px solid #f44336;
      padding: 10px;
      margin-bottom: 10px;
      border-radius: 4px;
    }
    .chart-container {
      position: relative;
      height: 300px;
      margin-bottom: 20px;
    }
    .refresh-button {
      background-color: #6200ea;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
    }
    .refresh-button:hover {
      background-color: #7e57c2;
    }
    .settings-panel {
      background-color: #fff;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
  </style>
</head>
<body>
  <div class="container-fluid">
    <div class="dashboard-header">
      <div class="row align-items-center">
        <div class="col-md-6">
          <h1>Vaivahik API Performance Dashboard</h1>
          <p>Monitor API performance metrics and identify slow endpoints</p>
        </div>
        <div class="col-md-6 text-end">
          <button id="refreshButton" class="refresh-button">
            <i class="bi bi-arrow-clockwise"></i> Refresh Data
          </button>
          <span id="lastUpdated" class="ms-3 text-light"></span>
        </div>
      </div>
    </div>
    
    <div class="settings-panel">
      <div class="row">
        <div class="col-md-3">
          <div class="form-group">
            <label for="slowThreshold">Slow Request Threshold (ms)</label>
            <input type="number" id="slowThreshold" class="form-control" value="500">
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label for="verySlowThreshold">Very Slow Request Threshold (ms)</label>
            <input type="number" id="verySlowThreshold" class="form-control" value="1000">
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label for="refreshInterval">Auto Refresh Interval (seconds)</label>
            <input type="number" id="refreshInterval" class="form-control" value="30">
          </div>
        </div>
        <div class="col-md-3 d-flex align-items-end">
          <button id="applySettings" class="btn btn-primary w-100">Apply Settings</button>
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-md-3">
        <div class="card metric-card">
          <div class="metric-value" id="totalRequests">0</div>
          <div class="metric-label">Total Requests</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card metric-card">
          <div class="metric-value" id="avgResponseTime">0 ms</div>
          <div class="metric-label">Average Response Time</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card metric-card">
          <div class="metric-value" id="slowRequests">0</div>
          <div class="metric-label">Slow Requests</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card metric-card">
          <div class="metric-value" id="errorRate">0%</div>
          <div class="metric-label">Error Rate</div>
        </div>
      </div>
    </div>
    
    <div class="row mt-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">Response Time by Endpoint</div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="responseTimeChart"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">Request Volume by Method</div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="requestVolumeChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="row mt-4">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">Slow Requests</div>
          <div class="card-body">
            <div id="slowRequestsList">
              <div class="text-center text-muted">No slow requests detected</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="row mt-4">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">Endpoint Performance</div>
          <div class="card-body">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>Endpoint</th>
                  <th>Method</th>
                  <th>Avg. Response Time</th>
                  <th>Min</th>
                  <th>Max</th>
                  <th>Count</th>
                  <th>Error Rate</th>
                </tr>
              </thead>
              <tbody id="endpointTable">
                <tr>
                  <td colspan="7" class="text-center">No data available</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    // Configuration
    let config = {
      slowThreshold: 500,
      verySlowThreshold: 1000,
      refreshInterval: 30
    };
    
    // Charts
    let responseTimeChart;
    let requestVolumeChart;
    
    // Initialize dashboard
    document.addEventListener('DOMContentLoaded', function() {
      // Load settings from localStorage
      loadSettings();
      
      // Initialize charts
      initCharts();
      
      // Load initial data
      fetchPerformanceData();
      
      // Set up refresh button
      document.getElementById('refreshButton').addEventListener('click', fetchPerformanceData);
      
      // Set up settings button
      document.getElementById('applySettings').addEventListener('click', applySettings);
      
      // Set up auto refresh
      setAutoRefresh();
    });
    
    // Load settings from localStorage
    function loadSettings() {
      const savedSettings = localStorage.getItem('performanceDashboardSettings');
      if (savedSettings) {
        config = JSON.parse(savedSettings);
        document.getElementById('slowThreshold').value = config.slowThreshold;
        document.getElementById('verySlowThreshold').value = config.verySlowThreshold;
        document.getElementById('refreshInterval').value = config.refreshInterval;
      }
    }
    
    // Save settings to localStorage
    function applySettings() {
      config.slowThreshold = parseInt(document.getElementById('slowThreshold').value);
      config.verySlowThreshold = parseInt(document.getElementById('verySlowThreshold').value);
      config.refreshInterval = parseInt(document.getElementById('refreshInterval').value);
      
      localStorage.setItem('performanceDashboardSettings', JSON.stringify(config));
      
      // Update auto refresh
      setAutoRefresh();
      
      // Refresh data
      fetchPerformanceData();
    }
    
    // Set up auto refresh
    let autoRefreshInterval;
    function setAutoRefresh() {
      // Clear existing interval
      if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
      }
      
      // Set new interval
      if (config.refreshInterval > 0) {
        autoRefreshInterval = setInterval(fetchPerformanceData, config.refreshInterval * 1000);
      }
    }
    
    // Initialize charts
    function initCharts() {
      // Response time chart
      const responseTimeCtx = document.getElementById('responseTimeChart').getContext('2d');
      responseTimeChart = new Chart(responseTimeCtx, {
        type: 'bar',
        data: {
          labels: [],
          datasets: [{
            label: 'Average Response Time (ms)',
            data: [],
            backgroundColor: '#7e57c2',
            borderColor: '#6200ea',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      });
      
      // Request volume chart
      const requestVolumeCtx = document.getElementById('requestVolumeChart').getContext('2d');
      requestVolumeChart = new Chart(requestVolumeCtx, {
        type: 'pie',
        data: {
          labels: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
          datasets: [{
            data: [0, 0, 0, 0, 0],
            backgroundColor: ['#4caf50', '#2196f3', '#ff9800', '#f44336', '#9c27b0']
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false
        }
      });
    }
    
    // Fetch performance data from API
    function fetchPerformanceData() {
      fetch('/api/v1/admin/performance')
        .then(response => response.json())
        .then(data => {
          updateDashboard(data);
          document.getElementById('lastUpdated').textContent = 'Last updated: ' + new Date().toLocaleTimeString();
        })
        .catch(error => {
          console.error('Error fetching performance data:', error);
        });
    }
    
    // Update dashboard with new data
    function updateDashboard(data) {
      // Update metrics
      document.getElementById('totalRequests').textContent = data.totalRequests;
      document.getElementById('avgResponseTime').textContent = data.avgResponseTime + ' ms';
      document.getElementById('slowRequests').textContent = data.slowRequests;
      document.getElementById('errorRate').textContent = data.errorRate + '%';
      
      // Update response time chart
      updateResponseTimeChart(data.endpoints);
      
      // Update request volume chart
      updateRequestVolumeChart(data.requestsByMethod);
      
      // Update slow requests list
      updateSlowRequestsList(data.slowRequestsList);
      
      // Update endpoint table
      updateEndpointTable(data.endpoints);
    }
    
    // Update response time chart
    function updateResponseTimeChart(endpoints) {
      // Sort endpoints by response time
      const sortedEndpoints = [...endpoints].sort((a, b) => b.avgResponseTime - a.avgResponseTime).slice(0, 10);
      
      // Update chart data
      responseTimeChart.data.labels = sortedEndpoints.map(endpoint => endpoint.path);
      responseTimeChart.data.datasets[0].data = sortedEndpoints.map(endpoint => endpoint.avgResponseTime);
      
      // Update chart
      responseTimeChart.update();
    }
    
    // Update request volume chart
    function updateRequestVolumeChart(requestsByMethod) {
      // Update chart data
      requestVolumeChart.data.datasets[0].data = [
        requestsByMethod.GET || 0,
        requestsByMethod.POST || 0,
        requestsByMethod.PUT || 0,
        requestsByMethod.DELETE || 0,
        requestsByMethod.PATCH || 0
      ];
      
      // Update chart
      requestVolumeChart.update();
    }
    
    // Update slow requests list
    function updateSlowRequestsList(slowRequests) {
      const slowRequestsList = document.getElementById('slowRequestsList');
      
      if (slowRequests.length === 0) {
        slowRequestsList.innerHTML = '<div class="text-center text-muted">No slow requests detected</div>';
        return;
      }
      
      let html = '';
      
      for (const request of slowRequests) {
        const requestClass = request.responseTime >= config.verySlowThreshold ? 'very-slow-request' : 'slow-request';
        
        html += \`
          <div class="\${requestClass}">
            <div class="d-flex justify-content-between">
              <strong>\${request.method} \${request.path}</strong>
              <span>\${request.responseTime} ms</span>
            </div>
            <div class="text-muted small">
              \${request.timestamp} | Status: \${request.statusCode}
            </div>
          </div>
        \`;
      }
      
      slowRequestsList.innerHTML = html;
    }
    
    // Update endpoint table
    function updateEndpointTable(endpoints) {
      const endpointTable = document.getElementById('endpointTable');
      
      if (endpoints.length === 0) {
        endpointTable.innerHTML = '<tr><td colspan="7" class="text-center">No data available</td></tr>';
        return;
      }
      
      let html = '';
      
      for (const endpoint of endpoints) {
        const rowClass = endpoint.avgResponseTime >= config.slowThreshold ? 'table-warning' : '';
        
        html += \`
          <tr class="\${rowClass}">
            <td>\${endpoint.path}</td>
            <td>\${endpoint.method}</td>
            <td>\${endpoint.avgResponseTime} ms</td>
            <td>\${endpoint.min} ms</td>
            <td>\${endpoint.max} ms</td>
            <td>\${endpoint.count}</td>
            <td>\${endpoint.errorRate}%</td>
          </tr>
        \`;
      }
      
      endpointTable.innerHTML = html;
    }
  </script>
</body>
</html>`;
  
  fs.writeFileSync(dashboardPath, dashboardContent);
  console.log(`Created performance dashboard at ${path.relative(path.join(__dirname, '..'), dashboardPath)}`);
}

/**
 * Create performance API route
 */
function createPerformanceRoute() {
  const routePath = path.join(config.routesDir, 'admin', 'performance.js');
  
  // Create directory if it doesn't exist
  const routeDir = path.dirname(routePath);
  if (!fs.existsSync(routeDir)) {
    fs.mkdirSync(routeDir, { recursive: true });
  }
  
  const routeContent = `/**
 * Performance Monitoring Routes
 * 
 * This file contains routes for performance monitoring.
 */

const express = require('express');
const router = express.Router();
const { getMetrics, resetMetrics, updateConfig } = require('../../middleware/performanceMonitor');
const { authenticateToken } = require('../../middleware/auth');
const apiResponse = require('../../utils/apiResponse');

/**
 * @swagger
 * /api/v1/admin/performance:
 *   get:
 *     summary: Get performance metrics
 *     description: Get performance metrics for the API
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  '/',
  authenticateToken,
  (req, res, next) => {
    try {
      // Check if user has admin role
      if (req.user.role !== 'ADMIN') {
        return apiResponse.forbidden(res, 'Only administrators can access performance metrics');
      }
      
      // Get metrics
      const metrics = getMetrics();
      
      // Calculate additional metrics
      const totalRequests = Object.values(metrics.requests).reduce((sum, count) => sum + count, 0);
      const avgResponseTime = metrics.endpoints.length > 0
        ? Math.round(metrics.endpoints.reduce((sum, endpoint) => sum + endpoint.avgResponseTime, 0) / metrics.endpoints.length)
        : 0;
      const slowRequests = metrics.slowRequests.length;
      const errorRate = totalRequests > 0
        ? Math.round((metrics.endpoints.reduce((sum, endpoint) => sum + (endpoint.errorCount || 0), 0) / totalRequests) * 100)
        : 0;
      
      // Return success response
      return apiResponse.success(res, 'Performance metrics retrieved successfully', {
        totalRequests,
        avgResponseTime,
        slowRequests,
        errorRate,
        requestsByMethod: metrics.requests,
        endpoints: metrics.endpoints,
        slowRequestsList: metrics.slowRequests
      });
    } catch (error) {
      return next(error);
    }
  }
);

/**
 * @swagger
 * /api/v1/admin/performance/reset:
 *   post:
 *     summary: Reset performance metrics
 *     description: Reset all performance metrics
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  '/reset',
  authenticateToken,
  (req, res, next) => {
    try {
      // Check if user has admin role
      if (req.user.role !== 'ADMIN') {
        return apiResponse.forbidden(res, 'Only administrators can reset performance metrics');
      }
      
      // Reset metrics
      resetMetrics();
      
      // Return success response
      return apiResponse.success(res, 'Performance metrics reset successfully');
    } catch (error) {
      return next(error);
    }
  }
);

/**
 * @swagger
 * /api/v1/admin/performance/config:
 *   put:
 *     summary: Update performance monitoring configuration
 *     description: Update performance monitoring configuration
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               slowThreshold:
 *                 type: integer
 *                 description: Threshold for slow requests in milliseconds
 *               maxSlowRequests:
 *                 type: integer
 *                 description: Maximum number of slow requests to track
 *               logInterval:
 *                 type: integer
 *                 description: Interval for logging metrics in milliseconds
 *               enabled:
 *                 type: boolean
 *                 description: Whether performance monitoring is enabled
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put(
  '/config',
  authenticateToken,
  (req, res, next) => {
    try {
      // Check if user has admin role
      if (req.user.role !== 'ADMIN') {
        return apiResponse.forbidden(res, 'Only administrators can update performance monitoring configuration');
      }
      
      // Update configuration
      updateConfig(req.body);
      
      // Return success response
      return apiResponse.success(res, 'Performance monitoring configuration updated successfully');
    } catch (error) {
      return next(error);
    }
  }
);

module.exports = router;`;
  
  fs.writeFileSync(routePath, routeContent);
  console.log(`Created performance route at ${path.relative(path.join(__dirname, '..'), routePath)}`);
}

/**
 * Update admin routes to include performance route
 */
function updateAdminRoutes() {
  const adminRoutesPath = path.join(config.routesDir, 'admin', 'index.js');
  
  // Check if file exists
  if (!fs.existsSync(adminRoutesPath)) {
    console.log(`Skipping ${path.relative(path.join(__dirname, '..'), adminRoutesPath)} (file not found)`);
    return;
  }
  
  // Read file
  const content = fs.readFileSync(adminRoutesPath, 'utf8');
  
  // Skip if already updated
  if (content.includes('performanceRoutes')) {
    console.log(`Skipping ${path.relative(path.join(__dirname, '..'), adminRoutesPath)} (already updated)`);
    return;
  }
  
  // Update content
  const updatedContent = content.replace(
    '// Import route modules',
    '// Import route modules\nconst performanceRoutes = require(\'./performance\');'
  ).replace(
    '// Mount route modules',
    '// Mount route modules\nrouter.use(\'/performance\', performanceRoutes);'
  );
  
  // Write updated content
  fs.writeFileSync(adminRoutesPath, updatedContent);
  
  console.log(`Updated ${path.relative(path.join(__dirname, '..'), adminRoutesPath)}`);
}

/**
 * Main function
 */
function main() {
  console.log('Setting up performance monitoring...');
  
  // Create performance dashboard
  createDashboard();
  
  // Create performance API route
  createPerformanceRoute();
  
  // Update admin routes
  updateAdminRoutes();
  
  console.log('Done!');
}

// Run main function
main();
