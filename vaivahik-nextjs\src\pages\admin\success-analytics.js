import { useState, useEffect } from 'react';
import EnhancedAdminLayout from '@/components/admin/EnhancedAdminLayout';
import { adminGet } from '@/services/apiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';
import { Line, Bar, Radar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  RadialLinearScale,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { toast } from 'react-toastify';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  RadialLinearScale,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

export default function SuccessAnalytics() {
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('month');
  const [metric, setMetric] = useState('all');
  const [analyticsData, setAnalyticsData] = useState(null);
  const [useMockData, setUseMockData] = useState(false);

  // Initialize mock data state from localStorage on component mount
  useEffect(() => {
    const storedMockDataState = localStorage.getItem('useMockData') === 'true';
    setUseMockData(storedMockDataState);
  }, []);

  useEffect(() => {
    fetchAnalyticsData();
  }, [period, metric, useMockData]);

  // Generate mock analytics data
  const generateMockAnalyticsData = () => {
    // Mock labels based on period
    let labels = [];
    switch (period) {
      case 'week':
        labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
        break;
      case 'month':
        labels = Array.from({ length: 30 }, (_, i) => `Day ${i + 1}`);
        break;
      case 'year':
        labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        break;
      case '5years':
        labels = ['2019', '2020', '2021', '2022', '2023'];
        break;
      default:
        labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    }

    // Generate random data
    const generateRandomData = (min, max, length) => {
      return Array.from({ length }, () => Math.floor(Math.random() * (max - min + 1)) + min);
    };

    // Mock data for different metrics
    const mockData = {
      summaryMetrics: {
        totalSuccessStories: Math.floor(Math.random() * 500) + 100,
        averageMatchScore: Math.floor(Math.random() * 30) + 70,
        averageUserSatisfaction: Math.floor(Math.random() * 20) + 80,
        algorithmAccuracy: Math.floor(Math.random() * 15) + 85,
        improvementFromLastPeriod: Math.floor(Math.random() * 10) + 1
      }
    };

    // Only include requested metrics or all if 'all' is selected
    if (metric === 'all' || metric === 'matchSuccess') {
      mockData.matchSuccessRate = {
        labels,
        datasets: [
          {
            label: 'Match Success Rate (%)',
            data: generateRandomData(60, 95, labels.length),
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.5)',
            tension: 0.3,
            fill: false
          }
        ]
      };
    }

    if (metric === 'all' || metric === 'userSatisfaction') {
      mockData.userSatisfaction = {
        labels,
        datasets: [
          {
            label: 'User Satisfaction (%)',
            data: generateRandomData(70, 98, labels.length),
            borderColor: 'rgb(153, 102, 255)',
            backgroundColor: 'rgba(153, 102, 255, 0.5)',
            tension: 0.3,
            fill: false
          }
        ]
      };
    }

    if (metric === 'all' || metric === 'successStories') {
      mockData.successStories = {
        labels,
        datasets: [
          {
            label: 'Success Stories',
            data: generateRandomData(5, 30, labels.length),
            backgroundColor: 'rgba(255, 99, 132, 0.5)',
            borderColor: 'rgb(255, 99, 132)',
            borderWidth: 1
          }
        ]
      };
    }

    if (metric === 'all' || metric === 'algorithmPerformance') {
      mockData.algorithmPerformance = {
        labels: ['Match Quality', 'Response Time', 'Preference Alignment', 'Diversity', 'User Feedback', 'Long-term Success'],
        datasets: [
          {
            label: 'Current Performance',
            data: generateRandomData(0.7, 0.95, 6),
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgb(54, 162, 235)',
            pointBackgroundColor: 'rgb(54, 162, 235)',
            pointBorderColor: '#fff',
            pointHoverBackgroundColor: '#fff',
            pointHoverBorderColor: 'rgb(54, 162, 235)'
          }
        ]
      };
    }

    if (metric === 'all' || metric === 'demographicBreakdown') {
      mockData.demographicBreakdown = {
        labels: ['18-25', '26-30', '31-35', '36-40', '41-45', '46+'],
        datasets: [
          {
            label: 'Success Rate by Age Group',
            data: generateRandomData(10, 30, 6),
            backgroundColor: [
              'rgba(255, 99, 132, 0.5)',
              'rgba(54, 162, 235, 0.5)',
              'rgba(255, 206, 86, 0.5)',
              'rgba(75, 192, 192, 0.5)',
              'rgba(153, 102, 255, 0.5)',
              'rgba(255, 159, 64, 0.5)'
            ],
            borderColor: [
              'rgba(255, 99, 132, 1)',
              'rgba(54, 162, 235, 1)',
              'rgba(255, 206, 86, 1)',
              'rgba(75, 192, 192, 1)',
              'rgba(153, 102, 255, 1)',
              'rgba(255, 159, 64, 1)'
            ],
            borderWidth: 1
          }
        ]
      };
    }

    return mockData;
  };

  const fetchAnalyticsData = async () => {
    setLoading(true);
    try {
      if (useMockData) {
        // Use mock data
        console.log('Using mock data for success analytics');
        const mockData = generateMockAnalyticsData();
        setAnalyticsData(mockData);
      } else {
        try {
          // Try to fetch real data
          const response = await adminGet(ADMIN_ENDPOINTS.SUCCESS_ANALYTICS, {
            period,
            metric
          });

          if (response.success) {
            setAnalyticsData(response.data);
          } else {
            console.warn('API returned error:', response.message);
            throw new Error(response.message || 'Failed to fetch analytics data');
          }
        } catch (apiError) {
          console.warn('API error, falling back to mock data:', apiError.message);
          // Fallback to mock data when API fails
          const mockData = generateMockAnalyticsData();
          setAnalyticsData(mockData);
          toast.warning('Could not connect to API. Using mock data instead.');
        }
      }
    } catch (error) {
      console.error('Error in fetchAnalyticsData:', error);
      // Final fallback to mock data for any other errors
      const mockData = generateMockAnalyticsData();
      setAnalyticsData(mockData);
      toast.error('Error occurred. Using mock data as fallback.');
    } finally {
      setLoading(false);
    }
  };

  // Toggle between mock and live data
  const toggleMockData = () => {
    const newState = !useMockData;
    setUseMockData(newState);
    localStorage.setItem('useMockData', newState.toString());

    toast.info(newState
      ? 'Switched to mock data for analytics visualization'
      : 'Switched to live data for analytics visualization');

    // No need to call fetchAnalyticsData here as it will be triggered by the useEffect
  };

  const handlePeriodChange = (e) => {
    setPeriod(e.target.value);
  };

  const handleMetricChange = (e) => {
    setMetric(e.target.value);
  };

  // Chart options
  const lineChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Trend Over Time',
      },
    },
    scales: {
      y: {
        min: 0,
        max: 100,
      },
    },
  };

  const barChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Success Stories',
      },
    },
  };

  const radarChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Algorithm Performance',
      },
    },
    scales: {
      r: {
        min: 0,
        max: 1,
      },
    },
  };

  const doughnutChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'right',
      },
      title: {
        display: true,
        text: 'Success Rate by Age Group',
      },
    },
  };

  return (
    <EnhancedAdminLayout title="Success Analytics">
      <div className="success-analytics-page">
        <div className="page-header">
          <h1>Success Analytics</h1>
          <p>Analyze the success metrics of the matching algorithm and user satisfaction</p>
        </div>

        <div className="filters-container">
          <div className="filter-group">
            <label htmlFor="period">Time Period:</label>
            <select id="period" value={period} onChange={handlePeriodChange}>
              <option value="week">Last 7 Days</option>
              <option value="month">Last 30 Days</option>
              <option value="year">Last 12 Months</option>
              <option value="5years">Last 5 Years</option>
            </select>
          </div>
          <div className="filter-group">
            <label htmlFor="metric">Metric:</label>
            <select id="metric" value={metric} onChange={handleMetricChange}>
              <option value="all">All Metrics</option>
              <option value="matchSuccess">Match Success Rate</option>
              <option value="userSatisfaction">User Satisfaction</option>
              <option value="successStories">Success Stories</option>
              <option value="algorithmPerformance">Algorithm Performance</option>
            </select>
          </div>
          <div className="filter-group mock-data-toggle">
            <label htmlFor="mockDataToggle">Data Source:</label>
            <div className="toggle-container">
              <span className={!useMockData ? 'active' : ''}>Live</span>
              <div className={`toggle-switch ${useMockData ? 'active-bg' : ''}`} onClick={toggleMockData}>
                <div className={`toggle-slider ${useMockData ? 'active' : ''}`}></div>
              </div>
              <span className={useMockData ? 'active' : ''}>Mock</span>
            </div>
          </div>
          <button className="refresh-btn" onClick={fetchAnalyticsData} disabled={loading}>
            {loading ? 'Loading...' : 'Refresh Data'}
          </button>
        </div>

        {loading ? (
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading analytics data...</p>
          </div>
        ) : analyticsData && Object.keys(analyticsData).length > 0 ? (
          <div className="analytics-content">
            {/* Summary Metrics */}
            <div className="summary-metrics">
              {analyticsData.summaryMetrics ? (
                <>
                  <div className="metric-card">
                    <h3>Total Success Stories</h3>
                    <div className="metric-value">{analyticsData.summaryMetrics.totalSuccessStories || 0}</div>
                  </div>
                  <div className="metric-card">
                    <h3>Average Match Score</h3>
                    <div className="metric-value">{analyticsData.summaryMetrics.averageMatchScore || 0}%</div>
                  </div>
                  <div className="metric-card">
                    <h3>User Satisfaction</h3>
                    <div className="metric-value">{analyticsData.summaryMetrics.averageUserSatisfaction || 0}%</div>
                  </div>
                  <div className="metric-card">
                    <h3>Algorithm Accuracy</h3>
                    <div className="metric-value">{analyticsData.summaryMetrics.algorithmAccuracy || 0}%</div>
                  </div>
                  <div className="metric-card">
                    <h3>Improvement</h3>
                    <div className="metric-value positive">+{analyticsData.summaryMetrics.improvementFromLastPeriod || 0}%</div>
                  </div>
                </>
              ) : (
                <div className="no-data-message">
                  <p>No summary metrics available yet. They will appear as users interact with the platform.</p>
                </div>
              )}
            </div>

            {/* Charts */}
            <div className="charts-container">
              {Object.keys(analyticsData).length > 0 ? (
                <>
                  {(metric === 'all' || metric === 'matchSuccess') && analyticsData.matchSuccessRate && (
                    <div className="chart-card">
                      <h3>Match Success Rate</h3>
                      <div className="chart-container">
                        <Line data={analyticsData.matchSuccessRate} options={lineChartOptions} />
                      </div>
                    </div>
                  )}

                  {(metric === 'all' || metric === 'userSatisfaction') && analyticsData.userSatisfaction && (
                    <div className="chart-card">
                      <h3>User Satisfaction</h3>
                      <div className="chart-container">
                        <Line data={analyticsData.userSatisfaction} options={lineChartOptions} />
                      </div>
                    </div>
                  )}

                  {(metric === 'all' || metric === 'successStories') && analyticsData.successStories && (
                    <div className="chart-card">
                      <h3>Success Stories</h3>
                      <div className="chart-container">
                        <Bar data={analyticsData.successStories} options={barChartOptions} />
                      </div>
                    </div>
                  )}

                  {(metric === 'all' || metric === 'algorithmPerformance') && analyticsData.algorithmPerformance && (
                    <div className="chart-card">
                      <h3>Algorithm Performance</h3>
                      <div className="chart-container">
                        <Radar data={analyticsData.algorithmPerformance} options={radarChartOptions} />
                      </div>
                    </div>
                  )}

                  {(metric === 'all' || metric === 'demographicBreakdown') && analyticsData.demographicBreakdown && (
                    <div className="chart-card">
                      <h3>Success Rate by Age Group</h3>
                      <div className="chart-container">
                        <Doughnut data={analyticsData.demographicBreakdown} options={doughnutChartOptions} />
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <div className="no-data-message full-width">
                  <p>No chart data available yet. Charts will appear as users interact with the platform.</p>
                  <p className="text-muted">The system is collecting data from user interactions and will generate analytics automatically.</p>
                </div>
              )}
            </div>

            {/* Insights Section */}
            <div className="insights-section">
              <h2>Key Insights</h2>
              {analyticsData.summaryMetrics ? (
                <div className="insights-container">
                  <div className="insight-card">
                    <div className="insight-icon">💡</div>
                    <div className="insight-content">
                      <h3>Match Quality</h3>
                      <p>
                        {analyticsData.summaryMetrics.improvementFromLastPeriod > 0
                          ? `The match success rate has improved by ${analyticsData.summaryMetrics.improvementFromLastPeriod}% compared to the previous period.`
                          : 'Waiting for more data to analyze match quality trends.'}
                      </p>
                    </div>
                  </div>
                  <div className="insight-card">
                    <div className="insight-icon">👥</div>
                    <div className="insight-content">
                      <h3>User Engagement</h3>
                      <p>
                        {analyticsData.summaryMetrics.totalSuccessStories > 0
                          ? `${analyticsData.summaryMetrics.totalSuccessStories} success stories have been recorded so far.`
                          : 'Users are beginning to engage with the platform. Success stories will be tracked as they occur.'}
                      </p>
                    </div>
                  </div>
                  <div className="insight-card">
                    <div className="insight-icon">📈</div>
                    <div className="insight-content">
                      <h3>System Performance</h3>
                      <p>
                        {analyticsData.summaryMetrics.algorithmAccuracy > 0
                          ? `The matching algorithm is currently operating at ${analyticsData.summaryMetrics.algorithmAccuracy}% accuracy.`
                          : 'The system is collecting data to measure algorithm performance.'}
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="no-data-message">
                  <p>No insights available yet. As users interact with the platform, the system will automatically generate insights based on the collected data.</p>
                  <p className="text-muted">Insights will help you understand user behavior and improve the matching algorithm.</p>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="empty-state">
            <div className="empty-icon">📊</div>
            <h3>No Analytics Data Available Yet</h3>
            <p>The system is waiting for user interactions to generate analytics data.</p>
            <p className="text-muted">As users register, create profiles, and interact with matches, the system will automatically collect and analyze data.</p>
            <div className="empty-state-actions">
              <button className="btn-primary" onClick={fetchAnalyticsData}>Refresh</button>
              {!useMockData && (
                <button className="btn-secondary" onClick={toggleMockData}>
                  Use Mock Data
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        .success-analytics-page {
          padding: 20px;
        }

        .page-header {
          margin-bottom: 20px;
        }

        .page-header h1 {
          font-size: 1.8rem;
          margin-bottom: 5px;
          color: var(--text-dark);
        }

        .page-header p {
          color: var(--text-muted);
        }

        .filters-container {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 20px;
          padding: 15px;
          background-color: var(--bg-white);
          border-radius: var(--border-radius);
          box-shadow: var(--shadow-sm);
        }

        .filter-group {
          display: flex;
          flex-direction: column;
          min-width: 200px;
        }

        .filter-group label {
          margin-bottom: 5px;
          font-weight: 500;
          color: var(--text-dark);
        }

        .filter-group select {
          padding: 8px 12px;
          border: 1px solid var(--border-color);
          border-radius: var(--border-radius);
          background-color: var(--bg-white);
          color: var(--text-dark);
        }

        .refresh-btn {
          padding: 8px 16px;
          background-color: var(--primary);
          color: white;
          border: none;
          border-radius: var(--border-radius);
          cursor: pointer;
          align-self: flex-end;
          margin-left: auto;
        }

        .refresh-btn:hover {
          background-color: var(--primary-dark);
        }

        .refresh-btn:disabled {
          background-color: var(--text-muted);
          cursor: not-allowed;
        }

        .loading-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 50px 0;
        }

        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 3px solid rgba(0, 0, 0, 0.1);
          border-radius: 50%;
          border-top-color: var(--primary);
          animation: spin 1s linear infinite;
          margin-bottom: 15px;
        }

        @keyframes spin {
          to {
            transform: rotate(360deg);
          }
        }

        .summary-metrics {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 15px;
          margin-bottom: 20px;
        }

        .metric-card {
          background-color: var(--bg-white);
          border-radius: var(--border-radius);
          padding: 15px;
          box-shadow: var(--shadow-sm);
          text-align: center;
        }

        .metric-card h3 {
          font-size: 0.9rem;
          color: var(--text-muted);
          margin-bottom: 10px;
        }

        .metric-value {
          font-size: 1.8rem;
          font-weight: 600;
          color: var(--text-dark);
        }

        .metric-value.positive {
          color: var(--success);
        }

        .metric-value.negative {
          color: var(--danger);
        }

        .charts-container {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
          gap: 20px;
          margin-bottom: 30px;
        }

        @media (max-width: 768px) {
          .charts-container {
            grid-template-columns: 1fr;
          }
        }

        .chart-card {
          background-color: var(--bg-white);
          border-radius: var(--border-radius);
          padding: 20px;
          box-shadow: var(--shadow-sm);
        }

        .chart-card h3 {
          font-size: 1.1rem;
          margin-bottom: 15px;
          color: var(--text-dark);
        }

        .chart-container {
          height: 300px;
          position: relative;
        }

        .insights-section {
          margin-top: 30px;
        }

        .insights-section h2 {
          font-size: 1.4rem;
          margin-bottom: 15px;
          color: var(--text-dark);
        }

        .insights-container {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 20px;
        }

        .insight-card {
          background-color: var(--bg-white);
          border-radius: var(--border-radius);
          padding: 20px;
          box-shadow: var(--shadow-sm);
          display: flex;
          gap: 15px;
        }

        .insight-icon {
          font-size: 2rem;
          color: var(--primary);
        }

        .insight-content h3 {
          font-size: 1.1rem;
          margin-bottom: 10px;
          color: var(--text-dark);
        }

        .insight-content p {
          color: var(--text-muted);
          line-height: 1.5;
        }

        .empty-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 50px 0;
          text-align: center;
          background-color: var(--bg-white);
          border-radius: var(--border-radius);
          box-shadow: var(--shadow-sm);
          margin: 20px 0;
        }

        .empty-icon {
          font-size: 3rem;
          color: var(--text-muted);
          margin-bottom: 1rem;
        }

        .empty-state h3 {
          font-size: 1.4rem;
          margin-bottom: 1rem;
          color: var(--text-dark);
        }

        .empty-state p {
          margin-bottom: 15px;
          color: var(--text-muted);
          max-width: 600px;
        }

        .text-muted {
          color: var(--text-muted);
          font-size: 0.9rem;
        }

        .no-data-message {
          background-color: var(--bg-white);
          border-radius: var(--border-radius);
          padding: 20px;
          text-align: center;
          margin: 10px 0;
          box-shadow: var(--shadow-sm);
        }

        .no-data-message.full-width {
          grid-column: 1 / -1;
          padding: 40px 20px;
        }

        .btn-primary {
          padding: 8px 16px;
          background-color: var(--primary);
          color: white;
          border: none;
          border-radius: var(--border-radius);
          cursor: pointer;
          margin-top: 10px;
        }

        .btn-primary:hover {
          background-color: var(--primary-dark);
        }

        .btn-secondary {
          padding: 8px 16px;
          background-color: white;
          color: var(--primary);
          border: 1px solid var(--primary);
          border-radius: var(--border-radius);
          cursor: pointer;
          margin-top: 10px;
          margin-left: 10px;
        }

        .btn-secondary:hover {
          background-color: var(--bg-light);
        }

        .empty-state-actions {
          display: flex;
          justify-content: center;
          margin-top: 10px;
        }

        /* Toggle Switch Styles */
        .mock-data-toggle {
          display: flex;
          flex-direction: column;
        }

        .toggle-container {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .toggle-container span {
          font-size: 0.9rem;
          color: var(--text-muted);
          transition: color 0.3s ease;
        }

        .toggle-container span.active {
          color: var(--text-dark);
          font-weight: 500;
        }

        .toggle-switch {
          position: relative;
          width: 50px;
          height: 24px;
          background-color: #e0e0e0;
          border-radius: 12px;
          cursor: pointer;
          transition: background-color 0.3s ease;
        }

        .toggle-switch:hover {
          background-color: #d0d0d0;
        }

        .toggle-switch.active-bg {
          background-color: rgba(103, 58, 183, 0.2); /* Using the primary color with opacity */
        }

        .toggle-slider {
          position: absolute;
          top: 2px;
          left: 2px;
          width: 20px;
          height: 20px;
          background-color: white;
          border-radius: 50%;
          transition: transform 0.3s ease, background-color 0.3s ease;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .toggle-slider.active {
          transform: translateX(26px);
          background-color: var(--primary);
        }
      `}</style>
    </EnhancedAdminLayout>
  );
}
