// src/middleware/documentUpload.js

const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Ensure upload directory exists
const DOCUMENT_UPLOAD_DIR = path.join(__dirname, '../../uploads/verification_documents');
if (!fs.existsSync(DOCUMENT_UPLOAD_DIR)) {
  fs.mkdirSync(DOCUMENT_UPLOAD_DIR, { recursive: true });
}

// Configure storage
const storage = multer.memoryStorage(); // Store in memory for processing

// Configure Multer upload options
const uploadDocuments = multer({
  storage: storage, // Store file in memory as a buffer
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit per file
  },
  fileFilter: function (req, file, cb) {
    // Check file type (allow jpeg, jpg, png, webp, pdf)
    const filetypes = /jpeg|jpg|png|webp|pdf/;
    const mimetype = filetypes.test(file.mimetype);
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

    if (mimetype && extname) {
      cb(null, true); // Accept the file
    } else {
      // Reject the file - create an error object for better handling
      const err = new Error('Error: Only JPEG, JPG, PNG, WebP, or PDF files are allowed!');
      err.code = 'INVALID_FILE_TYPE'; // Custom error code
      cb(err, false);
    }
  }
});

// Create middleware function that handles file upload
const handleDocumentUpload = (req, res, next) => {
  const upload = uploadDocuments.single('document'); // 'document' is the field name in the form

  upload(req, res, function (err) {
    if (err) {
      if (err.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({
          success: false,
          message: 'Document file size cannot exceed 5MB.'
        });
      } else if (err.code === 'INVALID_FILE_TYPE') {
        return res.status(400).json({
          success: false,
          message: err.message
        });
      } else {
        console.error('Document upload error:', err);
        return res.status(500).json({
          success: false,
          message: 'Error uploading document. Please try again.'
        });
      }
    }
    
    // If no file was uploaded
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No document file uploaded. Please select a file.'
      });
    }
    
    // If everything is fine, proceed
    next();
  });
};

module.exports = handleDocumentUpload;
