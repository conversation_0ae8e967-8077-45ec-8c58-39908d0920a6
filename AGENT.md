# AGENT.md - Vaivahik Matrimony Project

## Build/Test Commands
- **Frontend (Next.js)**: `cd vaivahik-nextjs && npm run dev` (dev), `npm run build` (build), `npm run lint` (lint)
- **Backend (Express)**: `cd vaivahik-backend && npm run dev` (dev on port 8080), `npm start` (production)
- **Express Tests**: `cd vaivahik-express && npm test` (Jest tests with 70% coverage threshold)
- **Single Test**: `cd vaivahik-express && npx jest tests/unit/specific-test.test.js`

## Code Style Guidelines
- **Imports**: Backend uses CommonJS (`require`/`module.exports`), Frontend uses ES6 (`import`/`export`)
- **Naming**: camelCase for variables/functions, PascalCase for components/classes, UPPER_SNAKE_CASE for constants
- **Components**: PascalCase React components with matching filenames (`ModernLoginForm.js`)
- **Error Handling**: Custom error classes extending `AppError`, structured validation with `express-validator`
- **File Organization**: Feature-based directories (`auth/`, `admin/`, `notification/`), separate controllers/services/routes
- **Documentation**: JSDoc comments for functions with `@param` and `@returns` annotations
- **Styling**: Material-UI components with styled-components pattern (prefix with "Styled")

## Project Structure
- `vaivahik-nextjs/`: Next.js frontend (port 3000)
- `vaivahik-backend/`: Express.js backend (port 8080)  
- `vaivahik-express/`: Express.js with Jest testing setup
- Uses Prisma ORM, Redis caching, PostgreSQL database

## Special Scripts
- Cache management: `npm run cache:clear`, `npm run cache:monitor`
- OTP testing: `npm run test:otp`, `npm run test:msg91`

## Implementation Progress
### ✅ COMPLETED - High Priority #1: Preference Config Backend API
- **Frontend API Route**: `vaivahik-nextjs/src/pages/api/admin/preference-config.js`
- **Backend Controllers**: Added `getPreferenceConfiguration`, `updatePreferenceConfiguration`, `deletePreferenceConfiguration`
- **Backend Routes**: Added `/api/admin/preference-config` (GET/PUT/DELETE) in `admin.routes.js`
- **Test Script**: `vaivahik-backend/scripts/test-preference-config-api.js`
- **Status**: Mock data implementation complete, ready for database integration

### 🚧 NEXT UP - High Priority #2: Real-time Error Alerts
- **Target**: Email/SMS/Slack notifications for critical errors
- **Components**: Alert service, notification templates, error thresholds
