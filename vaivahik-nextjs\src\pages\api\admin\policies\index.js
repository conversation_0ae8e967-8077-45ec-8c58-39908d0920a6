/**
 * Admin Policies API
 * Handles CRUD operations for dynamic policy management
 */

import { authenticateAdmin } from '@/utils/adminAuth';

// Mock policy storage (in production, this would be in a database)
let policies = {
  'privacy-policy': {
    id: 'privacy-policy',
    title: 'Privacy Policy',
    content: `# Privacy Policy

**Last Updated:** ${new Date().toLocaleDateString()}

## Information We Collect

We collect information you provide directly to us, such as when you create an account, fill out a profile, or contact us.

### Personal Information
- Name, age, gender, contact information
- Profile information including education, occupation, family details
- Birth details for horoscope matching
- Photos and other media you upload

### Technical Information
- Device information, IP address, browser type
- Usage data and interaction patterns
- Location data (with your consent)

## How We Use Your Information

We use the information we collect to:
- Provide and improve our matrimony services
- Find and suggest compatible matches
- Enable communication between members
- Ensure platform safety and security
- Send important updates and notifications

## Information Sharing

We never sell your personal information. We may share information only:
- With other members (controlled by your privacy settings)
- With service providers under strict confidentiality
- When required by law or to protect rights and safety

## Your Rights

You have the right to:
- Access and download your personal data
- Correct inaccurate information
- Delete your account and data
- Control your privacy settings
- Withdraw consent for optional processing

## Contact Us

For privacy questions: <EMAIL>`,
    lastUpdated: new Date().toISOString(),
    version: '1.0',
    status: 'published',
    isPublished: true
  },
  'terms-of-service': {
    id: 'terms-of-service',
    title: 'Terms of Service',
    content: `# Terms of Service

**Last Updated:** ${new Date().toLocaleDateString()}

## Acceptance of Terms

By using Vaivahik matrimony platform, you agree to these terms and our Privacy Policy.

## Eligibility

To use our service, you must:
- Be at least 18 years old (females) or 21 years old (males)
- Be legally eligible for marriage
- Provide accurate and truthful information
- Not be prohibited from using the service

## User Responsibilities

You agree to:
- Provide accurate profile information
- Use the platform solely for matrimonial purposes
- Treat all members with respect
- Not engage in fraudulent or inappropriate behavior
- Respect privacy and confidentiality

## Prohibited Activities

The following are strictly prohibited:
- Creating fake profiles or providing false information
- Harassment or inappropriate behavior
- Commercial use or advertising
- Data mining or collecting user information
- Multiple accounts for the same person

## Premium Services

Premium subscriptions are billed in advance and auto-renew unless disabled. Refunds are subject to our refund policy.

## Disclaimers

We provide a platform to meet potential partners but cannot guarantee marriage outcomes. Users are responsible for their own due diligence.

## Contact Us

For support: <EMAIL>`,
    lastUpdated: new Date().toISOString(),
    version: '1.0',
    status: 'published',
    isPublished: true
  },
  'refund-policy': {
    id: 'refund-policy',
    title: 'Refund Policy',
    content: `# Refund Policy

**Last Updated:** ${new Date().toLocaleDateString()}

## Overview

This refund policy outlines the terms and conditions for refunds on Vaivahik matrimony platform.

## Premium Subscription Refunds

### Eligibility for Refunds
- Refunds may be requested within 7 days of purchase
- Account must not have been used extensively
- No successful matches or meaningful interactions

### Non-Refundable Situations
- Subscriptions used for more than 7 days
- Accounts with successful matches or extensive usage
- Violation of terms of service
- Technical issues resolved within reasonable time

## Process for Requesting Refunds

1. Contact our support <NAME_EMAIL>
2. Provide your account details and reason for refund
3. Allow 5-7 business days for review
4. Refunds will be processed to the original payment method

## Partial Refunds

In certain circumstances, we may offer partial refunds:
- Technical issues affecting service quality
- Significant changes to service features
- Exceptional circumstances at our discretion

## Processing Time

- Refund approval: 5-7 business days
- Processing to payment method: 7-14 business days
- Bank processing may take additional time

## Contact Us

For refund requests: <EMAIL>
Phone: +91-XXXX-XXXXXX`,
    lastUpdated: new Date().toISOString(),
    version: '1.0',
    status: 'published',
    isPublished: true
  }
};

export default async function handler(req, res) {
  try {
    // Authenticate admin
    const adminData = await authenticateAdmin(req);
    if (!adminData.success) {
      return res.status(401).json({
        success: false,
        message: 'Unauthorized access'
      });
    }

    switch (req.method) {
      case 'GET':
        return handleGet(req, res);
      case 'POST':
        return handlePost(req, res, adminData);
      default:
        return res.status(405).json({
          success: false,
          message: 'Method not allowed'
        });
    }
  } catch (error) {
    console.error('Policies API error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}

async function handleGet(req, res) {
  try {
    return res.status(200).json({
      success: true,
      policies
    });
  } catch (error) {
    console.error('Error fetching policies:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch policies'
    });
  }
}

async function handlePost(req, res, adminData) {
  try {
    const { policyId, content, title, version } = req.body;

    if (!policyId || !content || !title) {
      return res.status(400).json({
        success: false,
        message: 'Policy ID, content, and title are required'
      });
    }

    // Update the policy
    if (policies[policyId]) {
      policies[policyId] = {
        ...policies[policyId],
        content,
        title,
        version: version || policies[policyId].version,
        lastUpdated: new Date().toISOString(),
        status: 'saved',
        updatedBy: adminData.admin.name
      };
    } else {
      // Create new policy
      policies[policyId] = {
        id: policyId,
        title,
        content,
        version: version || '1.0',
        lastUpdated: new Date().toISOString(),
        status: 'draft',
        isPublished: false,
        createdBy: adminData.admin.name
      };
    }

    return res.status(200).json({
      success: true,
      message: 'Policy saved successfully',
      policy: policies[policyId]
    });
  } catch (error) {
    console.error('Error saving policy:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to save policy'
    });
  }
}
