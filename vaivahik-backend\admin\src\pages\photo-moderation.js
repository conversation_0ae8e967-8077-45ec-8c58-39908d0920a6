import React, { useState } from 'react';
import { Box, Tabs, Tab, Container } from '@mui/material';
import AdminLayout from '../components/layouts/AdminLayout';
import PhotoModerationSettings from '../components/PhotoModerationSettings';
import PhotoModerationQueue from '../components/PhotoModerationQueue';

const PhotoModerationPage = () => {
  const [currentTab, setCurrentTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  return (
    <AdminLayout title="Photo Moderation">
      <Container maxWidth="xl">
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs
            value={currentTab}
            onChange={handleTabChange}
            aria-label="photo moderation tabs"
          >
            <Tab label="Moderation Queue" />
            <Tab label="Settings" />
          </Tabs>
        </Box>

        <Box>
          {currentTab === 0 && <PhotoModerationQueue />}
          {currentTab === 1 && <PhotoModerationSettings />}
        </Box>
      </Container>
    </AdminLayout>
  );
};

export default PhotoModerationPage;
