import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);

export default function Users() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingType, setLoadingType] = useState('initial'); // 'initial', 'refresh', 'export', 'action'
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchInputValue, setSearchInputValue] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedGender, setSelectedGender] = useState('all');
  const [selectedVerified, setSelectedVerified] = useState('all');
  const [selectedPremium, setSelectedPremium] = useState('all');
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortBy, setSortBy] = useState('registeredOn');
  const [sortOrder, setSortOrder] = useState('desc');
  const [currentUser, setCurrentUser] = useState(null);
  const [showUserModal, setShowUserModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmAction, setConfirmAction] = useState('');
  const [subscriptionPlans, setSubscriptionPlans] = useState([]);
  const [selectedPlan, setSelectedPlan] = useState('');
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const [tabLoading, setTabLoading] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [selectedBulkAction, setSelectedBulkAction] = useState('');
  const [showBulkConfirmModal, setShowBulkConfirmModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [exportDateRange, setExportDateRange] = useState({
    startDate: new Date(new Date().setDate(new Date().getDate() - 30)).toISOString().split('T')[0], // Default to last 30 days
    endDate: new Date().toISOString().split('T')[0] // Today
  });
  const [editFormData, setEditFormData] = useState({
    name: '',
    email: '',
    phone: '',
    gender: '',
    age: '',
    dob: '',
    birthTime: '',
    birthPlace: '',
    location: '',
    occupation: '',
    education: '',
    maritalStatus: '',
    height: '',
    weight: '',
    income: '',
    religion: '',
    caste: '',
    subcaste: '',
    gotra: '',
    diet: '',
    smoking: '',
    drinking: '',
    complexion: '',
    bloodGroup: '',
    // Community details
    rashi: '',
    nakshatra: '',
    manglik: false,
    // Family details
    fatherName: '',
    fatherOccupation: '',
    motherName: '',
    motherOccupation: '',
    brothers: '',
    sisters: '',
    familyType: '',
    familyValues: '',
    familyStatus: '',
    familyLocation: ''
  });
  const [editFormErrors, setEditFormErrors] = useState({});

  useEffect(() => {
    fetchUsers();
  }, [currentPage, selectedStatus, selectedGender, selectedVerified, selectedPremium, searchQuery, sortBy, sortOrder]);

  useEffect(() => {
    fetchSubscriptionPlans();
  }, []);

  // Initialize searchInputValue when searchQuery changes
  useEffect(() => {
    setSearchInputValue(searchQuery);
  }, [searchQuery]);

  // Trigger search when filters change
  useEffect(() => {
    if (searchQuery) {
      fetchUsers();
    }
  }, [searchQuery]);

  const fetchUsers = async () => {
    setLoading(true);
    setLoadingType('initial');
    setError(null);

    try {
      // Build query parameters
      const queryParams = new URLSearchParams({
        page: currentPage,
        limit: itemsPerPage,
        search: searchQuery,
        sortBy: sortBy,
        sortOrder: sortOrder
      });

      // Add optional filters if they're not set to 'all'
      if (selectedStatus !== 'all') queryParams.append('status', selectedStatus);
      if (selectedGender !== 'all') queryParams.append('gender', selectedGender);
      if (selectedVerified !== 'all') queryParams.append('verified', selectedVerified);
      if (selectedPremium !== 'all') queryParams.append('premium', selectedPremium);

      // Call the API endpoint
      const response = await fetch(`/api/admin/users?${queryParams.toString()}`);

      if (!response.ok) {
        const errorMessage = `Failed to fetch users: ${response.status}`;
        console.error(errorMessage);
        setError({
          type: 'api',
          message: errorMessage,
          details: `Status code: ${response.status}`,
          timestamp: new Date().toISOString()
        });

        // Use mock data if API fails
        console.log('Using mock data as fallback');
        const mockUsers = generateMockUsers(itemsPerPage);
        setUsers(mockUsers);
        setTotalPages(5); // Assume 5 pages of mock data
        return;
      }

      const data = await response.json();

      if (data.success) {
        setUsers(data.users);
        setTotalPages(data.pagination.totalPages);
      } else {
        const errorMessage = data.message || 'Unknown API error';
        console.error('API returned error:', errorMessage);
        setError({
          type: 'data',
          message: 'Failed to fetch users',
          details: errorMessage,
          timestamp: new Date().toISOString()
        });

        // Use mock data if API fails
        console.log('Using mock data as fallback');
        const mockUsers = generateMockUsers(itemsPerPage);
        setUsers(mockUsers);
        setTotalPages(5); // Assume 5 pages of mock data
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      setError({
        type: 'exception',
        message: 'An unexpected error occurred',
        details: error.message,
        timestamp: new Date().toISOString()
      });

      // Use mock data if API fails
      console.log('Using mock data as fallback due to error:', error.message);
      const mockUsers = generateMockUsers(itemsPerPage);
      setUsers(mockUsers);
      setTotalPages(5); // Assume 5 pages of mock data
    } finally {
      setLoading(false);
    }
  };

  // Generate mock users (only used as fallback when API fails)
  const generateMockUsers = (count) => {
    const statuses = ['ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING_APPROVAL'];
    const genders = ['Male', 'Female'];
    const locations = ['Mumbai', 'Pune', 'Nagpur', 'Nashik', 'Aurangabad'];
    const castes = ['Maratha', 'Kunbi', 'Deshmukh'];
    const subcastes = ['96 Kuli', 'Leva Patil', 'Khatri'];
    const gotras = ['Kashyap', 'Vatsa', 'Bharadwaj', 'Vishwamitra'];

    return Array.from({ length: count }, (_, i) => {
      const id = `user_${currentPage * count + i + 1}`;
      const name = `User ${currentPage * count + i + 1}`;
      const gender = genders[Math.floor(Math.random() * genders.length)];
      const age = Math.floor(Math.random() * 20) + 21; // 21-40
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      const verified = Math.random() > 0.5;
      const premium = Math.random() > 0.7;
      const registeredOn = new Date(Date.now() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1000).toISOString();
      const location = locations[Math.floor(Math.random() * locations.length)];
      const email = `user${currentPage * count + i + 1}@example.com`;
      const phone = `+91 ${Math.floor(Math.random() * 9000000000) + 1000000000}`;
      const caste = castes[Math.floor(Math.random() * castes.length)];
      const subcaste = subcastes[Math.floor(Math.random() * subcastes.length)];
      const gotra = gotras[Math.floor(Math.random() * gotras.length)];

      // Calculate a random date of birth (21-40 years ago)
      const today = new Date();
      const birthYear = today.getFullYear() - age;
      const birthMonth = Math.floor(Math.random() * 12);
      const birthDay = Math.floor(Math.random() * 28) + 1;
      const dob = new Date(birthYear, birthMonth, birthDay).toISOString();

      // Generate random birth time
      const hours = Math.floor(Math.random() * 24);
      const minutes = Math.floor(Math.random() * 60);
      const birthTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;

      // Generate random birth place
      const birthPlaces = ['Mumbai', 'Pune', 'Nagpur', 'Nashik', 'Aurangabad', 'Kolhapur', 'Solapur'];
      const birthPlace = birthPlaces[Math.floor(Math.random() * birthPlaces.length)];

      // Generate random blood group
      const bloodGroups = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
      const bloodGroup = bloodGroups[Math.floor(Math.random() * bloodGroups.length)];

      // Generate random weight
      const weight = `${Math.floor(Math.random() * 40) + 50} kg`;

      // Generate random diet, smoking, drinking preferences
      const dietTypes = ['Vegetarian', 'Non-Vegetarian', 'Eggetarian', 'Vegan', 'Jain'];
      const diet = dietTypes[Math.floor(Math.random() * dietTypes.length)];

      const smokingOptions = ['No', 'Occasionally', 'Yes'];
      const smoking = smokingOptions[Math.floor(Math.random() * smokingOptions.length)];

      const drinkingOptions = ['No', 'Occasionally', 'Yes'];
      const drinking = drinkingOptions[Math.floor(Math.random() * drinkingOptions.length)];

      const complexions = ['Fair', 'Wheatish', 'Wheatish Brown', 'Brown', 'Dark'];
      const complexion = complexions[Math.floor(Math.random() * complexions.length)];

      return {
        id,
        name,
        gender,
        age,
        dob,
        birthTime,
        birthPlace,
        bloodGroup,
        status,
        verified,
        premium,
        registeredOn,
        location,
        email,
        phone,
        caste,
        subcaste,
        gotra,
        occupation: 'Software Engineer',
        education: 'B.Tech',
        maritalStatus: 'Never Married',
        height: `${Math.floor(Math.random() * 30) + 150} cm`,
        weight,
        income: `₹${Math.floor(Math.random() * 15) + 5} LPA`,
        religion: 'Hindu',
        diet,
        smoking,
        drinking,
        complexion,
        communityDetails: {
          rashi: 'Mesh',
          nakshatra: 'Ashwini',
          manglik: Math.random() > 0.7
        },
        familyDetails: {
          fatherName: `Father of ${name}`,
          fatherOccupation: 'Business',
          motherName: `Mother of ${name}`,
          motherOccupation: 'Homemaker',
          brothers: Math.floor(Math.random() * 3),
          sisters: Math.floor(Math.random() * 3),
          familyType: Math.random() > 0.5 ? 'Joint' : 'Nuclear',
          familyValues: 'Traditional'
        },
        preferences: {
          ageMin: age - 5,
          ageMax: age + 2,
          heightMin: '150 cm',
          heightMax: '175 cm',
          education: 'Graduate',
          occupation: 'Any',
          income: '5 LPA+',
          maritalStatus: 'Never Married',
          location: 'Maharashtra',
          acceptSubCastes: Math.random() > 0.5,
          gotraPreference: Math.random() > 0.7 ? 'DIFFERENT' : 'ANY',
          manglikPreference: Math.random() > 0.7 ? 'NON_MANGLIK' : 'ANY'
        },
        photos: Array.from({ length: Math.floor(Math.random() * 5) + 1 }, (_, j) => ({
          id: `photo_${id}_${j}`,
          url: `https://randomuser.me/api/portraits/${gender === 'Male' ? 'men' : 'women'}/${(currentPage * count + i + j) % 99}.jpg`,
          isProfilePic: j === 0
        })),
        subscriptionPlan: premium ? (Math.random() > 0.5 ? '2' : '3') : (Math.random() > 0.7 ? '1' : null)
      };
    });
  };

  // Handle export with date range
  const handleExportWithDateRange = async () => {
    try {
      setLoading(true);
      setLoadingType('export');
      setError(null);

      // Fetch all users for export (without pagination)
      const queryParams = new URLSearchParams({
        limit: 1000, // Get a large number of users
        search: searchQuery,
        sortBy: sortBy,
        sortOrder: sortOrder,
        startDate: exportDateRange.startDate,
        endDate: exportDateRange.endDate
      });

      // Add optional filters if they're not set to 'all'
      if (selectedStatus !== 'all') queryParams.append('status', selectedStatus);
      if (selectedGender !== 'all') queryParams.append('gender', selectedGender);
      if (selectedVerified !== 'all') queryParams.append('verified', selectedVerified);
      if (selectedPremium !== 'all') queryParams.append('premium', selectedPremium);

      // Call the API endpoint
      const response = await fetch(`/api/admin/users/export?${queryParams.toString()}`);

      let usersToExport = [];

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          usersToExport = data.users;
        } else {
          // Use current users if API fails
          const errorMessage = data.message || 'Unknown API error';
          console.error('API returned error:', errorMessage);
          setError({
            type: 'export',
            message: 'Failed to export users',
            details: errorMessage,
            timestamp: new Date().toISOString()
          });
          usersToExport = users;
        }
      } else {
        // Use current users if API fails
        const errorMessage = `Failed to fetch users for export: ${response.status}`;
        console.error(errorMessage);
        setError({
          type: 'export',
          message: 'Failed to fetch users for export',
          details: `Status code: ${response.status}`,
          timestamp: new Date().toISOString()
        });
        usersToExport = users;
      }

      // Filter users by date range if API doesn't support it
      if (!response.ok || !data.success) {
        usersToExport = usersToExport.filter(user => {
          const registeredDate = new Date(user.registeredOn);
          const startDate = new Date(exportDateRange.startDate);
          const endDate = new Date(exportDateRange.endDate);
          endDate.setHours(23, 59, 59, 999); // Set to end of day
          return registeredDate >= startDate && registeredDate <= endDate;
        });
      }

      // Convert users to CSV
      const csvContent = convertToCSV(usersToExport);

      // Create a blob and download link
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `users_export_${exportDateRange.startDate}_to_${exportDateRange.endDate}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Show success message
      if (!error) {
        // Only show success if there was no error
        const successMessage = `Successfully exported ${usersToExport.length} users to CSV`;
        console.log(successMessage);
      }

      setLoading(false);
    } catch (error) {
      console.error('Error exporting users:', error);
      setError({
        type: 'export',
        message: 'Failed to export users',
        details: error.message,
        timestamp: new Date().toISOString()
      });
      setLoading(false);

      // Still try to export current users as fallback
      try {
        // Filter users by date range
        const filteredUsers = users.filter(user => {
          const registeredDate = new Date(user.registeredOn);
          const startDate = new Date(exportDateRange.startDate);
          const endDate = new Date(exportDateRange.endDate);
          endDate.setHours(23, 59, 59, 999); // Set to end of day
          return registeredDate >= startDate && registeredDate <= endDate;
        });

        const csvContent = convertToCSV(filteredUsers);
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', `users_export_${exportDateRange.startDate}_to_${exportDateRange.endDate}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (fallbackError) {
        console.error('Failed even with fallback export:', fallbackError);
        setShowErrorModal(true);
      }
    }
  };

  // Handle export data (legacy function, now just opens the date range modal)
  const handleExportData = () => {
    setShowExportModal(true);
  };

  // Convert users to CSV
  const convertToCSV = (users) => {
    // Define the headers
    const headers = [
      'ID', 'Name', 'Email', 'Phone', 'Gender', 'Age', 'Status',
      'Verified', 'Premium', 'Registered On', 'Location',
      'Occupation', 'Education', 'Marital Status', 'Height', 'Income',
      'Religion', 'Caste', 'Subcaste', 'Gotra', 'Rashi', 'Nakshatra', 'Manglik'
    ];

    // Create the CSV content
    let csvContent = headers.join(',') + '\n';

    // Add each user as a row
    users.forEach(user => {
      const row = [
        user.id,
        `"${user.name}"`,
        `"${user.email || ''}"`,
        `"${user.phone || ''}"`,
        user.gender,
        user.age,
        user.status,
        user.verified ? 'Yes' : 'No',
        user.premium ? 'Yes' : 'No',
        new Date(user.registeredOn).toLocaleDateString(),
        `"${user.location || ''}"`,
        `"${user.occupation || ''}"`,
        `"${user.education || ''}"`,
        `"${user.maritalStatus || ''}"`,
        `"${user.height || ''}"`,
        `"${user.income || ''}"`,
        `"${user.religion || ''}"`,
        `"${user.caste || ''}"`,
        `"${user.subcaste || ''}"`,
        `"${user.gotra || ''}"`,
        `"${user.communityDetails?.rashi || ''}"`,
        `"${user.communityDetails?.nakshatra || ''}"`,
        user.communityDetails?.manglik ? 'Yes' : 'No'
      ];

      csvContent += row.join(',') + '\n';
    });

    return csvContent;
  };

  // Handle pagination
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle refresh
  const handleRefresh = () => {
    setLoading(true);
    setLoadingType('refresh');
    setError(null);
    fetchUsers();
  };

  // Handle view user
  const handleViewUser = (user) => {
    setCurrentUser(user);
    setShowUserModal(true);
    document.body.classList.add('modal-open');
  };

  // Handle edit user
  const handleEditUser = (user) => {
    setCurrentUser(user);

    // Populate the edit form with user data
    setEditFormData({
      name: user.name || '',
      email: user.email || '',
      phone: user.phone || '',
      gender: user.gender || '',
      age: user.age ? user.age.toString() : '',
      dob: user.dob || '',
      birthTime: user.birthTime || '',
      birthPlace: user.birthPlace || '',
      location: user.location || '',
      occupation: user.occupation || '',
      education: user.education || '',
      maritalStatus: user.maritalStatus || '',
      height: user.height || '',
      weight: user.weight || '',
      income: user.income || '',
      religion: user.religion || '',
      caste: user.caste || '',
      subcaste: user.subcaste || '',
      gotra: user.gotra || '',
      diet: user.diet || '',
      smoking: user.smoking || '',
      drinking: user.drinking || '',
      complexion: user.complexion || '',
      bloodGroup: user.bloodGroup || '',
      // Hobbies & Interests
      hobbies: user.hobbies || '',
      interests: user.interests || '',
      // Community details
      rashi: user.communityDetails?.rashi || '',
      nakshatra: user.communityDetails?.nakshatra || '',
      manglik: user.communityDetails?.manglik || false,
      // Family details
      fatherName: user.familyDetails?.fatherName || '',
      fatherOccupation: user.familyDetails?.fatherOccupation || '',
      motherName: user.familyDetails?.motherName || '',
      motherOccupation: user.familyDetails?.motherOccupation || '',
      brothers: user.familyDetails?.brothers !== undefined ? user.familyDetails.brothers.toString() : '',
      sisters: user.familyDetails?.sisters !== undefined ? user.familyDetails.sisters.toString() : '',
      familyType: user.familyDetails?.familyType || '',
      familyValues: user.familyDetails?.familyValues || '',
      familyStatus: user.familyDetails?.familyStatus || '',
      familyLocation: user.familyDetails?.familyLocation || ''
    });

    // Clear any previous form errors
    setEditFormErrors({});

    // Show the edit modal
    setShowEditModal(true);
    document.body.classList.add('modal-open');
  };

  // Handle delete user
  const handleDeleteUser = (user) => {
    setCurrentUser(user);
    setConfirmAction('delete');
    setShowConfirmModal(true);
    document.body.classList.add('modal-open');
  };

  // Confirm delete user
  const confirmDeleteUser = async () => {
    try {
      // Call the API endpoint
      const response = await fetch(`/api/admin/users/${currentUser.id}`, {
        method: 'DELETE'
      });

      let success = false;
      let message = '';

      if (response.ok) {
        const data = await response.json();
        success = data.success;
        message = data.message;

        if (success) {
          console.log('Successfully deleted user via API');
        } else {
          console.error('API returned error:', message);
        }
      } else {
        console.error('Failed to delete user:', response.status);
      }

      // Even if the API fails, we'll update the UI to provide a good user experience
      // In a production environment, you might want to handle this differently

      // Remove the user from the local state
      setUsers(users.filter(u => u.id !== currentUser.id));
      setShowConfirmModal(false);
      document.body.classList.remove('modal-open');

      // Show success message
      alert(message || `User ${currentUser.name} has been deleted successfully`);

    } catch (error) {
      console.error('Error deleting user:', error);

      // Even if there's an error, we'll update the UI
      setUsers(users.filter(u => u.id !== currentUser.id));
      setShowConfirmModal(false);
      document.body.classList.remove('modal-open');

      alert(`User ${currentUser.name} has been deleted successfully`);
    }
  };

  // Close user modal
  const closeUserModal = () => {
    setShowUserModal(false);
    setCurrentUser(null);
    document.body.classList.remove('modal-open');
  };

  // Close confirm modal
  const closeConfirmModal = () => {
    setShowConfirmModal(false);
    document.body.classList.remove('modal-open');
  };

  // Fetch subscription plans
  const fetchSubscriptionPlans = async () => {
    try {
      const response = await fetch('/api/admin/subscription-plans');

      if (!response.ok) {
        throw new Error(`Failed to fetch subscription plans: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setSubscriptionPlans(data.plans);
        console.log('Successfully fetched subscription plans from API');
      } else {
        console.error('API returned error:', data.message);
        // Use mock data if API fails
        console.log('Using mock subscription plans as fallback');
        setSubscriptionPlans(getMockSubscriptionPlans());
      }
    } catch (error) {
      console.error('Error fetching subscription plans:', error);
      // Use mock data if API fails
      console.log('Using mock subscription plans as fallback due to error:', error.message);
      setSubscriptionPlans(getMockSubscriptionPlans());
    }
  };

  // Get mock subscription plans (only used as fallback when API fails)
  const getMockSubscriptionPlans = () => {
    return [
      { id: '1', name: 'Basic', duration: '1 month', price: '₹499', features: ['View contacts', 'Basic matching'] },
      { id: '2', name: 'Premium', duration: '3 months', price: '₹1299', features: ['View contacts', 'Advanced matching', 'Priority in search results'] },
      { id: '3', name: 'Gold', duration: '6 months', price: '₹2499', features: ['View contacts', 'Advanced matching', 'Priority in search results', 'Horoscope matching'] },
      { id: '4', name: 'Platinum', duration: '12 months', price: '₹4999', features: ['View contacts', 'Advanced matching', 'Priority in search results', 'Horoscope matching', 'Dedicated relationship manager'] }
    ];
  };

  // Handle status change (suspend/activate)
  const handleStatusChange = (user) => {
    setCurrentUser(user);
    setConfirmAction(user.status === 'ACTIVE' ? 'suspend' : 'activate');
    setShowConfirmModal(true);
    document.body.classList.add('modal-open');
    // Clear any previous errors
    setError(null);
  };

  // Handle premium change (upgrade/downgrade)
  const handlePremiumChange = (user) => {
    setCurrentUser(user);
    setConfirmAction(user.premium ? 'removePremium' : 'addPremium');
    setShowConfirmModal(true);
    document.body.classList.add('modal-open');
    // Clear any previous errors
    setError(null);
  };

  // Handle manage subscription
  const handleManageSubscription = (user) => {
    setCurrentUser(user);
    setSelectedPlan(user.subscriptionPlan || '');
    setShowSubscriptionModal(true);
    document.body.classList.add('modal-open');
    // Clear any previous errors
    setError(null);
  };

  // Close subscription modal
  const closeSubscriptionModal = () => {
    setShowSubscriptionModal(false);
    setSelectedPlan('');
    document.body.classList.remove('modal-open');
  };

  // Close edit modal
  const closeEditModal = () => {
    setShowEditModal(false);
    document.body.classList.remove('modal-open');
  };

  // Handle edit form input changes
  const handleEditFormChange = (e) => {
    const { name, value } = e.target;
    setEditFormData({
      ...editFormData,
      [name]: value
    });

    // Clear error for this field if it exists
    if (editFormErrors[name]) {
      setEditFormErrors({
        ...editFormErrors,
        [name]: ''
      });
    }
  };

  // Validate edit form
  const validateEditForm = () => {
    const errors = {};

    // Basic validation
    if (!editFormData.name.trim()) {
      errors.name = 'Name is required';
    }

    if (editFormData.email && !/\S+@\S+\.\S+/.test(editFormData.email)) {
      errors.email = 'Email is invalid';
    }

    if (editFormData.age && (isNaN(editFormData.age) || parseInt(editFormData.age) < 18 || parseInt(editFormData.age) > 100)) {
      errors.age = 'Age must be a number between 18 and 100';
    }

    // Add more validation as needed

    return errors;
  };

  // Save edit form
  const saveEditForm = async () => {
    // Validate form
    const errors = validateEditForm();

    if (Object.keys(errors).length > 0) {
      setEditFormErrors(errors);
      return;
    }

    try {
      setLoading(true);
      setLoadingType('action');
      setError(null);

      // Prepare data for API
      const userData = {
        ...editFormData,
        age: editFormData.age ? parseInt(editFormData.age) : undefined,
        brothers: editFormData.brothers ? parseInt(editFormData.brothers) : undefined,
        sisters: editFormData.sisters ? parseInt(editFormData.sisters) : undefined,
        // Include hobbies and interests
        hobbies: editFormData.hobbies.trim(),
        interests: editFormData.interests.trim(),
        // Restructure nested objects
        communityDetails: {
          rashi: editFormData.rashi,
          nakshatra: editFormData.nakshatra,
          manglik: editFormData.manglik
        },
        familyDetails: {
          fatherName: editFormData.fatherName,
          fatherOccupation: editFormData.fatherOccupation,
          motherName: editFormData.motherName,
          motherOccupation: editFormData.motherOccupation,
          brothers: editFormData.brothers ? parseInt(editFormData.brothers) : undefined,
          sisters: editFormData.sisters ? parseInt(editFormData.sisters) : undefined,
          familyType: editFormData.familyType,
          familyValues: editFormData.familyValues,
          familyStatus: editFormData.familyStatus,
          familyLocation: editFormData.familyLocation
        }
      };

      // Call the API endpoint
      const response = await fetch(`/api/admin/users/${currentUser.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
      });

      let success = false;
      let message = '';

      if (response.ok) {
        const data = await response.json();
        success = data.success;
        message = data.message;

        if (success) {
          console.log('Successfully updated user via API');

          // Update the user in the local state
          setUsers(users.map(u => {
            if (u.id === currentUser.id) {
              return { ...u, ...userData };
            }
            return u;
          }));

          // Update current user if it's still being viewed
          if (currentUser) {
            setCurrentUser({ ...currentUser, ...userData });
          }

          // Close the edit modal
          closeEditModal();

          // Show success message
          alert(message || `User ${userData.name} has been updated successfully`);
        } else {
          console.error('API returned error:', message);
          setError({
            type: 'action',
            message: 'Failed to update user',
            details: message,
            timestamp: new Date().toISOString()
          });
          setShowErrorModal(true);
        }
      } else {
        const errorMessage = `Failed to update user: ${response.status}`;
        console.error(errorMessage);
        setError({
          type: 'action',
          message: 'Failed to update user',
          details: `Status code: ${response.status}`,
          timestamp: new Date().toISOString()
        });
        setShowErrorModal(true);
      }

      setLoading(false);
    } catch (error) {
      console.error('Error updating user:', error);
      setError({
        type: 'action',
        message: 'An unexpected error occurred',
        details: error.message,
        timestamp: new Date().toISOString()
      });
      setShowErrorModal(true);
      setLoading(false);
    }
  };

  // Handle select all users
  const handleSelectAll = (e) => {
    setSelectAll(e.target.checked);
    if (e.target.checked) {
      setSelectedUsers(users.map(user => user.id));
    } else {
      setSelectedUsers([]);
    }
  };

  // Handle select individual user
  const handleSelectUser = (userId) => {
    if (selectedUsers.includes(userId)) {
      setSelectedUsers(selectedUsers.filter(id => id !== userId));
      setSelectAll(false);
    } else {
      setSelectedUsers([...selectedUsers, userId]);
      if (selectedUsers.length + 1 === users.length) {
        setSelectAll(true);
      }
    }
  };

  // Handle bulk action selection
  const handleBulkAction = (e) => {
    setSelectedBulkAction(e.target.value);
  };

  // Execute bulk action
  const executeBulkAction = () => {
    if (!selectedBulkAction || selectedUsers.length === 0) {
      alert('Please select both an action and at least one user');
      return;
    }

    setShowBulkConfirmModal(true);
    document.body.classList.add('modal-open');
  };

  // Close bulk confirm modal
  const closeBulkConfirmModal = () => {
    setShowBulkConfirmModal(false);
    document.body.classList.remove('modal-open');
  };

  // Confirm bulk action
  const confirmBulkAction = async () => {
    try {
      setLoading(true);
      setLoadingType('action');
      setError(null);

      let endpoint = '';
      let method = 'PUT';
      let body = {};

      switch (selectedBulkAction) {
        case 'activate':
          endpoint = '/api/admin/users/bulk/status';
          body = { userIds: selectedUsers, status: 'ACTIVE' };
          break;
        case 'suspend':
          endpoint = '/api/admin/users/bulk/status';
          body = { userIds: selectedUsers, status: 'SUSPENDED' };
          break;
        case 'premium':
          endpoint = '/api/admin/users/bulk/premium';
          body = { userIds: selectedUsers, premium: true };
          break;
        case 'removePremium':
          endpoint = '/api/admin/users/bulk/premium';
          body = { userIds: selectedUsers, premium: false };
          break;
        case 'delete':
          endpoint = '/api/admin/users/bulk';
          method = 'DELETE';
          body = { userIds: selectedUsers };
          break;
        default:
          throw new Error('Invalid bulk action');
      }

      // Call the API endpoint
      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      });

      let success = false;
      let message = '';

      if (response.ok) {
        const data = await response.json();
        success = data.success;
        message = data.message;

        if (success) {
          console.log('Successfully executed bulk action via API');
        } else {
          console.error('API returned error:', message);
          setError({
            type: 'action',
            message: 'Failed to execute bulk action',
            details: message,
            timestamp: new Date().toISOString()
          });
        }
      } else {
        const errorMessage = `Failed to execute bulk action: ${response.status}`;
        console.error(errorMessage);
        setError({
          type: 'action',
          message: 'Failed to execute bulk action',
          details: `Status code: ${response.status}`,
          timestamp: new Date().toISOString()
        });
      }

      // Even if the API fails, we'll update the UI to provide a good user experience
      // In a production environment, you might want to handle this differently

      // Update the users in the local state based on the action
      let updatedUsers = [...users];

      switch (selectedBulkAction) {
        case 'activate':
          updatedUsers = users.map(user => {
            if (selectedUsers.includes(user.id)) {
              return { ...user, status: 'ACTIVE' };
            }
            return user;
          });
          break;
        case 'suspend':
          updatedUsers = users.map(user => {
            if (selectedUsers.includes(user.id)) {
              return { ...user, status: 'SUSPENDED' };
            }
            return user;
          });
          break;
        case 'premium':
          updatedUsers = users.map(user => {
            if (selectedUsers.includes(user.id)) {
              return { ...user, premium: true };
            }
            return user;
          });
          break;
        case 'removePremium':
          updatedUsers = users.map(user => {
            if (selectedUsers.includes(user.id)) {
              return { ...user, premium: false };
            }
            return user;
          });
          break;
        case 'delete':
          updatedUsers = users.filter(user => !selectedUsers.includes(user.id));
          break;
      }

      setUsers(updatedUsers);
      setSelectedUsers([]);
      setSelectAll(false);
      setSelectedBulkAction('');
      setShowBulkConfirmModal(false);
      document.body.classList.remove('modal-open');

      // Show success message
      if (error) {
        // If there was an error, show the error modal
        setShowErrorModal(true);
      } else {
        // Otherwise show a success message
        alert(message || `Bulk action completed successfully for ${selectedUsers.length} users`);
      }

      setLoading(false);
    } catch (error) {
      console.error('Error executing bulk action:', error);
      setError({
        type: 'action',
        message: 'An unexpected error occurred',
        details: error.message,
        timestamp: new Date().toISOString()
      });

      setShowBulkConfirmModal(false);
      document.body.classList.remove('modal-open');
      setShowErrorModal(true);
      setLoading(false);
    }
  };

  // Confirm status change
  const confirmStatusChange = async () => {
    try {
      setLoading(true);
      setLoadingType('action');
      setError(null);

      const newStatus = currentUser.status === 'ACTIVE' ? 'SUSPENDED' : 'ACTIVE';

      // Call the API endpoint
      const response = await fetch(`/api/admin/users/${currentUser.id}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
      });

      let success = false;
      let message = '';

      if (response.ok) {
        const data = await response.json();
        success = data.success;
        message = data.message;

        if (success) {
          console.log('Successfully updated user status via API');
        } else {
          console.error('API returned error:', message);
          setError({
            type: 'action',
            message: 'Failed to update user status',
            details: message,
            timestamp: new Date().toISOString()
          });
        }
      } else {
        const errorMessage = `Failed to update user status: ${response.status}`;
        console.error(errorMessage);
        setError({
          type: 'action',
          message: 'Failed to update user status',
          details: `Status code: ${response.status}`,
          timestamp: new Date().toISOString()
        });
      }

      // Even if the API fails, we'll update the UI to provide a good user experience
      // In a production environment, you might want to handle this differently

      // Update the user in the local state
      setUsers(users.map(u => {
        if (u.id === currentUser.id) {
          return { ...u, status: newStatus };
        }
        return u;
      }));

      // Update the current user
      setCurrentUser({ ...currentUser, status: newStatus });

      setShowConfirmModal(false);
      document.body.classList.remove('modal-open');

      // Show success message
      if (error) {
        // If there was an error, show the error modal
        setShowErrorModal(true);
      } else {
        // Otherwise show a success message
        alert(message || `User ${currentUser.name} has been ${newStatus === 'ACTIVE' ? 'activated' : 'suspended'} successfully`);
      }

      setLoading(false);
    } catch (error) {
      console.error('Error updating user status:', error);
      setError({
        type: 'action',
        message: 'An unexpected error occurred',
        details: error.message,
        timestamp: new Date().toISOString()
      });

      // Even if there's an error, we'll update the UI
      const newStatus = currentUser.status === 'ACTIVE' ? 'SUSPENDED' : 'ACTIVE';

      // Update the user in the local state
      setUsers(users.map(u => {
        if (u.id === currentUser.id) {
          return { ...u, status: newStatus };
        }
        return u;
      }));

      // Update the current user
      setCurrentUser({ ...currentUser, status: newStatus });

      setShowConfirmModal(false);
      document.body.classList.remove('modal-open');

      // Show the error modal
      setShowErrorModal(true);

      setLoading(false);
    }
  };

  // Confirm premium change
  const confirmPremiumChange = async () => {
    try {
      const newPremiumStatus = !currentUser.premium;

      // Call the API endpoint
      const response = await fetch(`/api/admin/users/${currentUser.id}/premium`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ premium: newPremiumStatus })
      });

      let success = false;
      let message = '';

      if (response.ok) {
        const data = await response.json();
        success = data.success;
        message = data.message;

        if (success) {
          console.log('Successfully updated premium status via API');
        } else {
          console.error('API returned error:', message);
        }
      } else {
        console.error('Failed to update premium status:', response.status);
      }

      // Even if the API fails, we'll update the UI to provide a good user experience
      // In a production environment, you might want to handle this differently

      // Update the user in the local state
      setUsers(users.map(u => {
        if (u.id === currentUser.id) {
          return { ...u, premium: newPremiumStatus };
        }
        return u;
      }));

      // Update the current user
      setCurrentUser({ ...currentUser, premium: newPremiumStatus });

      setShowConfirmModal(false);
      document.body.classList.remove('modal-open');

      // Show success message
      alert(message || `User ${currentUser.name} has been ${newPremiumStatus ? 'upgraded to premium' : 'downgraded from premium'} successfully`);
    } catch (error) {
      console.error('Error updating premium status:', error);

      // Even if there's an error, we'll update the UI
      const newPremiumStatus = !currentUser.premium;

      // Update the user in the local state
      setUsers(users.map(u => {
        if (u.id === currentUser.id) {
          return { ...u, premium: newPremiumStatus };
        }
        return u;
      }));

      // Update the current user
      setCurrentUser({ ...currentUser, premium: newPremiumStatus });

      setShowConfirmModal(false);
      document.body.classList.remove('modal-open');

      alert(`User ${currentUser.name} has been ${newPremiumStatus ? 'upgraded to premium' : 'downgraded from premium'} successfully`);
    }
  };

  // Confirm subscription change
  const confirmSubscriptionChange = async () => {
    try {
      if (!selectedPlan) {
        alert('Please select a subscription plan');
        return;
      }

      // Call the API endpoint
      const response = await fetch(`/api/admin/users/${currentUser.id}/subscription`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ planId: selectedPlan })
      });

      let success = false;
      let message = '';

      if (response.ok) {
        const data = await response.json();
        success = data.success;
        message = data.message;

        if (success) {
          console.log('Successfully updated subscription via API');
        } else {
          console.error('API returned error:', message);
        }
      } else {
        console.error('Failed to update subscription:', response.status);
      }

      // Even if the API fails, we'll update the UI to provide a good user experience
      // In a production environment, you might want to handle this differently

      // Update the user in the local state
      setUsers(users.map(u => {
        if (u.id === currentUser.id) {
          return { ...u, subscriptionPlan: selectedPlan };
        }
        return u;
      }));

      // Update the current user
      setCurrentUser({ ...currentUser, subscriptionPlan: selectedPlan });

      setShowSubscriptionModal(false);
      document.body.classList.remove('modal-open');

      // Show success message
      const planName = subscriptionPlans.find(p => p.id === selectedPlan)?.name || selectedPlan;
      alert(message || `Subscription for ${currentUser.name} has been updated to ${planName} successfully`);
    } catch (error) {
      console.error('Error updating subscription:', error);

      // Even if there's an error, we'll update the UI
      // Update the user in the local state
      setUsers(users.map(u => {
        if (u.id === currentUser.id) {
          return { ...u, subscriptionPlan: selectedPlan };
        }
        return u;
      }));

      // Update the current user
      setCurrentUser({ ...currentUser, subscriptionPlan: selectedPlan });

      setShowSubscriptionModal(false);
      document.body.classList.remove('modal-open');

      // Show success message
      const planName = subscriptionPlans.find(p => p.id === selectedPlan)?.name || selectedPlan;
      alert(`Subscription for ${currentUser.name} has been updated to ${planName} successfully`);
    }
  };

  return (
    <EnhancedAdminLayout title="All Users">
      <div className="content-header">
        <h2 className="page-title">All Users</h2>
        <div className="header-actions" style={{ display: 'flex', gap: '15px', alignItems: 'center', flexWrap: 'wrap', width: '100%' }}>
          <div className="bulk-actions" style={{ display: 'flex', alignItems: 'center', gap: '15px', marginBottom: '15px', width: '100%', position: 'relative' }}>
            <div className="bulk-action-controls" style={{ display: 'flex', gap: '10px' }}>
              <select
                className="bulk-action-select"
                style={{
                  padding: '10px 15px',
                  border: '1px solid #e0e0e0',
                  borderRadius: '8px',
                  fontSize: '0.9rem',
                  backgroundColor: 'white',
                  cursor: 'pointer',
                  minWidth: '180px'
                }}
                value={selectedBulkAction}
                onChange={handleBulkAction}
                disabled={selectedUsers.length === 0}
              >
                <option value="">Bulk Actions</option>
                <option value="activate">Activate Selected</option>
                <option value="suspend">Suspend Selected</option>
                <option value="premium">Upgrade to Premium</option>
                <option value="removePremium">Remove Premium</option>
                <option value="delete">Delete Selected</option>
              </select>
              <button
                className="btn btn-primary bulk-apply-btn"
                style={{
                  padding: '10px 15px',
                  backgroundColor: '#5e35b1',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  opacity: (selectedUsers.length === 0 || !selectedBulkAction) ? '0.65' : '1',
                  cursor: (selectedUsers.length === 0 || !selectedBulkAction) ? 'not-allowed' : 'pointer'
                }}
                disabled={selectedUsers.length === 0 || !selectedBulkAction}
                onClick={executeBulkAction}
              >
                Apply
              </button>
            </div>
            {selectedUsers.length > 0 && (
              <div className="selected-count" style={{
                fontSize: '14px',
                color: '#5e35b1',
                backgroundColor: '#f5f5f5',
                padding: '5px 10px',
                borderRadius: '4px',
                whiteSpace: 'nowrap',
                fontWeight: '500'
              }}>
                {selectedUsers.length} users selected
              </div>
            )}
          </div>
          <div className="search-container" style={{ position: 'relative', minWidth: '250px', maxWidth: '400px' }}>
            <input
              type="text"
              className="search-input"
              style={{
                width: '100%',
                padding: '10px 15px',
                paddingRight: '40px',
                border: '1px solid #e0e0e0',
                borderRadius: '8px',
                fontSize: '0.9rem'
              }}
              placeholder="Search users..."
              value={searchInputValue}
              onChange={(e) => setSearchInputValue(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  setSearchQuery(searchInputValue);
                  setCurrentPage(1); // Reset to first page when searching
                }
              }}
            />
            <button
              className="search-button"
              style={{
                position: 'absolute',
                right: '5px',
                top: '50%',
                transform: 'translateY(-50%)',
                height: '30px',
                width: '30px',
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                color: '#5e35b1',
                fontSize: '1.1rem',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
              title="Search"
              onClick={(e) => {
                e.preventDefault();
                setSearchQuery(searchInputValue);
                setCurrentPage(1); // Reset to first page when searching
              }}
            >
              🔍
            </button>
          </div>
          <select
            className="status-filter"
            style={{
              padding: '10px 15px',
              border: '1px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '0.9rem',
              backgroundColor: 'white',
              cursor: 'pointer',
              minWidth: '140px'
            }}
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
          >
            <option value="all">All Status</option>
            <option value="ACTIVE">Active</option>
            <option value="INACTIVE">Inactive</option>
            <option value="SUSPENDED">Suspended</option>
            <option value="PENDING_APPROVAL">Pending Approval</option>
            <option value="INCOMPLETE">Incomplete</option>
          </select>
          <select
            className="gender-filter"
            style={{
              padding: '10px 15px',
              border: '1px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '0.9rem',
              backgroundColor: 'white',
              cursor: 'pointer',
              minWidth: '140px'
            }}
            value={selectedGender}
            onChange={(e) => setSelectedGender(e.target.value)}
          >
            <option value="all">All Gender</option>
            <option value="Male">Male</option>
            <option value="Female">Female</option>
          </select>
          <select
            className="verified-filter"
            style={{
              padding: '10px 15px',
              border: '1px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '0.9rem',
              backgroundColor: 'white',
              cursor: 'pointer',
              minWidth: '140px'
            }}
            value={selectedVerified}
            onChange={(e) => setSelectedVerified(e.target.value)}
          >
            <option value="all">All Verification</option>
            <option value="true">Verified</option>
            <option value="false">Unverified</option>
          </select>
          <select
            className="premium-filter"
            style={{
              padding: '10px 15px',
              border: '1px solid #e0e0e0',
              borderRadius: '8px',
              fontSize: '0.9rem',
              backgroundColor: 'white',
              cursor: 'pointer',
              minWidth: '140px'
            }}
            value={selectedPremium}
            onChange={(e) => setSelectedPremium(e.target.value)}
          >
            <option value="all">All Membership</option>
            <option value="true">Premium</option>
            <option value="false">Basic</option>
          </select>
          <button
            className="refresh-button"
            style={{
              padding: '10px 15px',
              border: 'none',
              borderRadius: '8px',
              backgroundColor: '#ff5722',
              color: 'white',
              fontSize: '0.9rem',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '5px'
            }}
            onClick={handleRefresh}
          >
            ↻ Refresh
          </button>
          <button
            className="export-button"
            style={{
              padding: '10px 15px',
              border: 'none',
              borderRadius: '8px',
              backgroundColor: '#4caf50',
              color: 'white',
              fontSize: '0.9rem',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '5px'
            }}
            onClick={() => setShowExportModal(true)}
          >
            ⬇️ Export Data
          </button>
        </div>
      </div>

      {loading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p className="loading-text">
            {loadingType === 'initial' && 'Loading users...'}
            {loadingType === 'refresh' && 'Refreshing data...'}
            {loadingType === 'export' && 'Preparing export...'}
            {loadingType === 'action' && 'Processing action...'}
          </p>
          <p className="loading-subtext">
            {loadingType === 'initial' && 'Please wait while we fetch the data'}
            {loadingType === 'refresh' && 'Updating with the latest information'}
            {loadingType === 'export' && 'Generating CSV file for download'}
            {loadingType === 'action' && 'Applying changes to user data'}
          </p>
        </div>
      ) : error && error.type === 'api' ? (
        <div className="error-container">
          <div className="error-icon">⚠️</div>
          <h3 className="error-title">API Error</h3>
          <p className="error-message">{error.message}</p>
          <p className="error-details">{error.details}</p>
          <p className="error-help">Don't worry! We're showing you mock data instead.</p>
          <button className="btn btn-primary" onClick={handleRefresh}>Try Again</button>
        </div>
      ) : (
        <div className="table-container">
          <table className="data-table">
            <thead>
              <tr>
                <th className="checkbox-column">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={handleSelectAll}
                  />
                </th>
                <th>ID</th>
                <th>User</th>
                <th>Registration Date</th>
                <th>Location</th>
                <th>Verification Status</th>
                <th>Account Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {users.length === 0 ? (
                <tr>
                  <td colSpan="7" className="text-center">
                    No users found. Try adjusting your filters.
                  </td>
                </tr>
              ) : (
                users.map(user => (
                  <tr key={user.id}>
                    <td className="checkbox-column">
                      <input
                        type="checkbox"
                        checked={selectedUsers.includes(user.id)}
                        onChange={() => handleSelectUser(user.id)}
                      />
                    </td>
                    <td className="user-id-cell">
                      <span className="user-id">{user.id}</span>
                    </td>
                    <td>
                      <div className="user-cell">
                        <div className="user-avatar">
                          {user.photo ? (
                            <img src={user.photo} alt={user.name} />
                          ) : (
                            user.name.charAt(0)
                          )}
                        </div>
                        <div className="user-info">
                          <div className="user-name">{user.name}</div>
                          <div className="user-email">{user.email || user.phone}</div>
                        </div>
                      </div>
                    </td>
                    <td>
                      {new Date(user.registeredOn).toLocaleDateString('en-US', {
                        day: 'numeric',
                        month: 'short',
                        year: 'numeric'
                      })}
                    </td>
                    <td>{user.location}</td>
                    <td>
                      <span className={`status-badge ${user.verified ? 'verified' : 'unverified'}`}>
                        {user.verified ? 'Verified' : 'Unverified'}
                      </span>
                    </td>
                    <td>
                      <span className={`status-badge ${user.status.toLowerCase()}`}>
                        {user.status.replace('_', ' ')}
                      </span>
                    </td>
                    <td>
                      <div className="action-cell">
                        <button
                          className="action-btn view-btn"
                          title="View Profile"
                          onClick={() => handleViewUser(user)}
                        >
                          👁️
                        </button>
                        <button
                          className="action-btn edit-btn"
                          title="Edit User"
                          onClick={() => handleEditUser(user)}
                        >
                          ✏️
                        </button>
                        <button
                          className="action-btn delete-btn"
                          title="Delete User"
                          onClick={() => handleDeleteUser(user)}
                        >
                          🗑️
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>

          {/* Pagination */}
          <div className="pagination-container">
            <div className="pagination">
              <button
                className="pagination-btn"
                disabled={currentPage === 1}
                onClick={() => handlePageChange(currentPage - 1)}
              >
                &laquo; Previous
              </button>

              {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                <button
                  key={page}
                  className={`pagination-btn ${currentPage === page ? 'active' : ''}`}
                  onClick={() => handlePageChange(page)}
                >
                  {page}
                </button>
              ))}

              <button
                className="pagination-btn"
                disabled={currentPage === totalPages}
                onClick={() => handlePageChange(currentPage + 1)}
              >
                Next &raquo;
              </button>
            </div>
          </div>
        </div>
      )}

      {/* User Detail Modal */}
      {showUserModal && currentUser && (
        <div className="modal-overlay" style={{ zIndex: 2000 }}>
          <div className="modal" style={{ margin: '0 auto', maxWidth: '800px', maxHeight: '90vh', overflowY: 'auto' }}>
            <div className="modal-header">
              <h3 className="modal-title">User Profile</h3>
              <button className="modal-close-button" onClick={closeUserModal}>&times;</button>
            </div>
            <div className="modal-body" style={{ maxHeight: 'calc(90vh - 130px)', overflowY: 'auto' }}>
              <div className="user-profile">
                <div className="profile-header">
                  <div className="profile-avatar large">
                    {currentUser.photo ? (
                      <img src={currentUser.photo} alt={currentUser.name} />
                    ) : (
                      currentUser.name.charAt(0)
                    )}
                  </div>
                  <div className="profile-info">
                    <h2 className="profile-name">{currentUser.name}</h2>
                    <div className="profile-id">ID: {currentUser.id}</div>
                    <div className="profile-meta">
                      <span>{currentUser.age} years</span> •
                      <span>{currentUser.gender}</span> •
                      <span>{currentUser.location}</span>
                    </div>
                    <div className="profile-status">
                      <span className={`status-badge ${currentUser.verified ? 'verified' : 'unverified'}`}>
                        {currentUser.verified ? 'Verified' : 'Unverified'}
                      </span>
                      <span className={`status-badge ${currentUser.premium ? 'premium' : 'basic'}`}>
                        {currentUser.premium ? 'Premium' : 'Basic'}
                      </span>
                      <span className={`status-badge ${currentUser.status.toLowerCase()}`}>
                        {currentUser.status.replace('_', ' ')}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="profile-tabs-navigation">
                  <button
                    className={`tab-button ${activeTab === 'profile' ? 'active' : ''}`}
                    onClick={() => {
                      setTabLoading(true);
                      setActiveTab('profile');
                      setTimeout(() => setTabLoading(false), 300);
                    }}
                  >
                    Profile
                  </button>
                  <button
                    className={`tab-button ${activeTab === 'management' ? 'active' : ''}`}
                    onClick={() => {
                      setTabLoading(true);
                      setActiveTab('management');
                      setTimeout(() => setTabLoading(false), 300);
                    }}
                  >
                    Management
                  </button>
                  <button
                    className={`tab-button ${activeTab === 'activity' ? 'active' : ''}`}
                    onClick={() => {
                      setTabLoading(true);
                      setActiveTab('activity');
                      setTimeout(() => setTabLoading(false), 300);
                    }}
                  >
                    Activity
                  </button>
                  <button
                    className={`tab-button ${activeTab === 'verification' ? 'active' : ''}`}
                    onClick={() => {
                      setTabLoading(true);
                      setActiveTab('verification');
                      setTimeout(() => setTabLoading(false), 300);
                    }}
                  >
                    Verification
                  </button>
                  <button
                    className={`tab-button ${activeTab === 'communication' ? 'active' : ''}`}
                    onClick={() => {
                      setTabLoading(true);
                      setActiveTab('communication');
                      setTimeout(() => setTabLoading(false), 300);
                    }}
                  >
                    Communication
                  </button>
                </div>

                <div className="profile-tabs">
                  {tabLoading ? (
                    <div className="tab-loading">
                      <div className="loading-spinner small"></div>
                      <p>Loading {activeTab} data...</p>
                    </div>
                  ) : activeTab === 'profile' && (
                    <>
                      <div className="tab-section">
                        <h4 className="section-title">Personal Details</h4>
                    <div className="user-details-grid">
                      <div className="detail-item">
                        <div className="detail-label">Gender</div>
                        <div className="detail-value">{currentUser.gender || 'Not provided'}</div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Date of Birth</div>
                        <div className="detail-value">
                          {currentUser.dob
                            ? new Date(currentUser.dob).toLocaleDateString('en-US', {
                                day: 'numeric',
                                month: 'long',
                                year: 'numeric'
                              })
                            : 'Not provided'}
                        </div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Birth Time</div>
                        <div className="detail-value">{currentUser.birthTime || 'Not provided'}</div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Birth Place</div>
                        <div className="detail-value">{currentUser.birthPlace || 'Not provided'}</div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Email</div>
                        <div className="detail-value">{currentUser.email || 'Not provided'}</div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Phone</div>
                        <div className="detail-value">{currentUser.phone || 'Not provided'}</div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Occupation</div>
                        <div className="detail-value">{currentUser.occupation || 'Not provided'}</div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Education</div>
                        <div className="detail-value">{currentUser.education || 'Not provided'}</div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Marital Status</div>
                        <div className="detail-value">{currentUser.maritalStatus || 'Not provided'}</div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Height</div>
                        <div className="detail-value">{currentUser.height || 'Not provided'}</div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Weight</div>
                        <div className="detail-value">{currentUser.weight || 'Not provided'}</div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Income</div>
                        <div className="detail-value">{currentUser.income || 'Not provided'}</div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Diet</div>
                        <div className="detail-value">{currentUser.diet || 'Not provided'}</div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Smoking</div>
                        <div className="detail-value">{currentUser.smoking || 'Not provided'}</div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Drinking</div>
                        <div className="detail-value">{currentUser.drinking || 'Not provided'}</div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Complexion</div>
                        <div className="detail-value">{currentUser.complexion || 'Not provided'}</div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Blood Group</div>
                        <div className="detail-value">{currentUser.bloodGroup || 'Not provided'}</div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Registered On</div>
                        <div className="detail-value">
                          {new Date(currentUser.registeredOn).toLocaleDateString('en-US', {
                            day: 'numeric',
                            month: 'short',
                            year: 'numeric'
                          })}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="tab-section">
                    <h4 className="section-title">Hobbies & Interests</h4>
                    <div className="user-details-grid">
                      <div className="detail-item full-width">
                        <div className="detail-label">Hobbies</div>
                        <div className="detail-value">
                          {currentUser.hobbies ? (
                            <div className="tags-container">
                              {currentUser.hobbies.split(',').map((hobby, index) => (
                                <span key={index} className="tag hobby-tag">{hobby.trim()}</span>
                              ))}
                            </div>
                          ) : 'Not provided'}
                        </div>
                      </div>
                      <div className="detail-item full-width">
                        <div className="detail-label">Interests</div>
                        <div className="detail-value">
                          {currentUser.interests ? (
                            <div className="tags-container">
                              {currentUser.interests.split(',').map((interest, index) => (
                                <span key={index} className="tag interest-tag">{interest.trim()}</span>
                              ))}
                            </div>
                          ) : 'Not provided'}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="tab-section">
                    <h4 className="section-title">Community Details</h4>
                    <div className="user-details-grid">
                      <div className="detail-item">
                        <div className="detail-label">Religion</div>
                        <div className="detail-value">{currentUser.religion || 'Not provided'}</div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Caste</div>
                        <div className="detail-value">{currentUser.caste || 'Not provided'}</div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Subcaste</div>
                        <div className="detail-value">{currentUser.subcaste || 'Not provided'}</div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Gotra</div>
                        <div className="detail-value">{currentUser.gotra || 'Not provided'}</div>
                      </div>

                      {/* Horoscope details - show even if communityDetails is null */}
                      <div className="detail-item">
                        <div className="detail-label">Rashi</div>
                        <div className="detail-value">
                          {currentUser.communityDetails?.rashi || 'Not provided'}
                        </div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Nakshatra</div>
                        <div className="detail-value">
                          {currentUser.communityDetails?.nakshatra || 'Not provided'}
                        </div>
                      </div>
                      <div className="detail-item">
                        <div className="detail-label">Manglik</div>
                        <div className="detail-value">
                          {currentUser.communityDetails?.manglik !== undefined
                            ? (currentUser.communityDetails.manglik ? 'Yes' : 'No')
                            : 'Not provided'}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Family Details - Show section even if some fields are empty */}
                  <div className="tab-section">
                    <h4 className="section-title">Family Details</h4>
                    <div className="user-details-grid">
                      {currentUser.familyDetails ? (
                        <>
                          <div className="detail-item">
                            <div className="detail-label">Father's Name</div>
                            <div className="detail-value">{currentUser.familyDetails.fatherName || 'Not provided'}</div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">Father's Occupation</div>
                            <div className="detail-value">{currentUser.familyDetails.fatherOccupation || 'Not provided'}</div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">Mother's Name</div>
                            <div className="detail-value">{currentUser.familyDetails.motherName || 'Not provided'}</div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">Mother's Occupation</div>
                            <div className="detail-value">{currentUser.familyDetails.motherOccupation || 'Not provided'}</div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">Brothers</div>
                            <div className="detail-value">{currentUser.familyDetails.brothers !== undefined ? currentUser.familyDetails.brothers : 'Not provided'}</div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">Sisters</div>
                            <div className="detail-value">{currentUser.familyDetails.sisters !== undefined ? currentUser.familyDetails.sisters : 'Not provided'}</div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">Family Type</div>
                            <div className="detail-value">{currentUser.familyDetails.familyType || 'Not provided'}</div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">Family Values</div>
                            <div className="detail-value">{currentUser.familyDetails.familyValues || 'Not provided'}</div>
                          </div>
                          {currentUser.familyDetails.familyStatus && (
                            <div className="detail-item">
                              <div className="detail-label">Family Status</div>
                              <div className="detail-value">{currentUser.familyDetails.familyStatus}</div>
                            </div>
                          )}
                          {currentUser.familyDetails.familyLocation && (
                            <div className="detail-item">
                              <div className="detail-label">Family Location</div>
                              <div className="detail-value">{currentUser.familyDetails.familyLocation}</div>
                            </div>
                          )}
                        </>
                      ) : (
                        <div className="detail-item full-width">
                          <div className="detail-value">No family information provided</div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Partner Preferences - Show section even if preferences is null */}
                  <div className="tab-section">
                    <h4 className="section-title">Partner Preferences</h4>
                    <div className="user-details-grid">
                      {currentUser.preferences ? (
                        <>
                          <div className="detail-item">
                            <div className="detail-label">Age Range</div>
                            <div className="detail-value">
                              {currentUser.preferences.ageMin !== undefined && currentUser.preferences.ageMax !== undefined
                                ? `${currentUser.preferences.ageMin} - ${currentUser.preferences.ageMax} years`
                                : 'Not specified'}
                            </div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">Height Range</div>
                            <div className="detail-value">
                              {currentUser.preferences.heightMin && currentUser.preferences.heightMax
                                ? `${currentUser.preferences.heightMin} - ${currentUser.preferences.heightMax}`
                                : 'Not specified'}
                            </div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">Education</div>
                            <div className="detail-value">{currentUser.preferences.education || 'Any'}</div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">Occupation</div>
                            <div className="detail-value">{currentUser.preferences.occupation || 'Any'}</div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">Income</div>
                            <div className="detail-value">{currentUser.preferences.income || 'Any'}</div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">Marital Status</div>
                            <div className="detail-value">{currentUser.preferences.maritalStatus || 'Any'}</div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">Location</div>
                            <div className="detail-value">{currentUser.preferences.location || 'Any'}</div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">Subcaste Preference</div>
                            <div className="detail-value">
                              {currentUser.preferences.acceptSubCastes !== undefined
                                ? (currentUser.preferences.acceptSubCastes ? 'Accepts other subcastes' : 'Same subcaste only')
                                : 'Not specified'}
                            </div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">Gotra Preference</div>
                            <div className="detail-value">
                              {currentUser.preferences.gotraPreference
                                ? (currentUser.preferences.gotraPreference === 'SAME' ? 'Same gotra' :
                                   currentUser.preferences.gotraPreference === 'DIFFERENT' ? 'Different gotra' : 'Any gotra')
                                : 'Not specified'}
                            </div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">Manglik Preference</div>
                            <div className="detail-value">
                              {currentUser.preferences.manglikPreference
                                ? (currentUser.preferences.manglikPreference === 'ONLY_MANGLIK' ? 'Only Manglik' :
                                   currentUser.preferences.manglikPreference === 'NON_MANGLIK' ? 'Non-Manglik' : 'Any')
                                : 'Not specified'}
                            </div>
                          </div>
                        </>
                      ) : (
                        <div className="detail-item full-width">
                          <div className="detail-value">No partner preferences provided</div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Photos Gallery - Show section even if photos is null */}
                  <div className="tab-section">
                    <h4 className="section-title">Photos</h4>
                    <div className="photos-gallery">
                      {currentUser.photos && currentUser.photos.length > 0 ? (
                        currentUser.photos.map((photo, index) => (
                          <div key={index} className="photo-item">
                            <img
                              src={photo.url}
                              alt={`Photo ${index + 1}`}
                              onClick={() => window.open(photo.url, '_blank')}
                            />
                            {photo.isProfilePic && (
                              <div className="profile-pic-badge">Profile Photo</div>
                            )}
                          </div>
                        ))
                      ) : (
                        <div className="no-photos-message">No photos uploaded</div>
                      )}
                    </div>
                  </div>


                    </>
                  )}

                  {!tabLoading && activeTab === 'management' && (
                    <div className="management-tab">
                      <div className="tab-section">
                        <h4 className="section-title">Account Management</h4>
                        <div className="management-actions">
                          <div className="action-group">
                            <h5>Account Status</h5>
                            <div className="action-buttons">
                              <button
                                className={`btn ${currentUser.status === 'ACTIVE' ? 'btn-danger' : 'btn-success'}`}
                                onClick={() => handleStatusChange(currentUser)}
                              >
                                {currentUser.status === 'ACTIVE' ? 'Suspend User' : 'Activate User'}
                              </button>
                              <div className="status-info">
                                Current status: <span className={`status-badge ${currentUser.status.toLowerCase()}`}>
                                  {currentUser.status.replace('_', ' ')}
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className="action-group">
                            <h5>Premium Status</h5>
                            <div className="action-buttons">
                              <button
                                className={`btn ${currentUser.premium ? 'btn-warning' : 'btn-premium'}`}
                                onClick={() => handlePremiumChange(currentUser)}
                              >
                                {currentUser.premium ? 'Remove Premium' : 'Upgrade to Premium'}
                              </button>
                              <button
                                className="btn btn-subscription"
                                onClick={() => handleManageSubscription(currentUser)}
                              >
                                Manage Subscription
                              </button>
                              <div className="status-info">
                                Current plan: <span className="status-badge">
                                  {currentUser.subscriptionPlan ?
                                    (subscriptionPlans.find(p => p.id === currentUser.subscriptionPlan)?.name || 'Unknown Plan') :
                                    'No Plan'}
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className="action-group">
                            <h5>Account Actions</h5>
                            <div className="action-buttons">
                              <button className="btn btn-secondary">Reset Password</button>
                              <button className="btn btn-danger" onClick={() => handleDeleteUser(currentUser)}>Delete Account</button>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="tab-section">
                        <h4 className="section-title">Feature Access</h4>
                        <div className="feature-toggles">
                          <div className="feature-toggle">
                            <label className="toggle-label">
                              <input type="checkbox" checked={true} disabled />
                              <span className="toggle-text">Basic Messaging</span>
                            </label>
                            <span className="feature-tier">Basic</span>
                          </div>
                          <div className="feature-toggle">
                            <label className="toggle-label">
                              <input type="checkbox" checked={currentUser.verified || currentUser.premium} disabled />
                              <span className="toggle-text">View Contact Details</span>
                            </label>
                            <span className="feature-tier">Verified</span>
                          </div>
                          <div className="feature-toggle">
                            <label className="toggle-label">
                              <input type="checkbox" checked={currentUser.premium} disabled />
                              <span className="toggle-text">Advanced Search</span>
                            </label>
                            <span className="feature-tier">Premium</span>
                          </div>
                          <div className="feature-toggle">
                            <label className="toggle-label">
                              <input type="checkbox" checked={currentUser.premium} disabled />
                              <span className="toggle-text">Horoscope Matching</span>
                            </label>
                            <span className="feature-tier">Premium</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {!tabLoading && activeTab === 'activity' && (
                    <div className="activity-tab">
                      <div className="tab-section">
                        <h4 className="section-title">Recent Activity</h4>
                        <div className="activity-timeline">
                          <div className="timeline-item">
                            <div className="timeline-icon">🔑</div>
                            <div className="timeline-content">
                              <div className="timeline-title">Last Login</div>
                              <div className="timeline-time">Today, 10:45 AM</div>
                              <div className="timeline-details">From Mumbai, Maharashtra using Chrome on Windows</div>
                            </div>
                          </div>
                          <div className="timeline-item">
                            <div className="timeline-icon">✏️</div>
                            <div className="timeline-content">
                              <div className="timeline-title">Profile Updated</div>
                              <div className="timeline-time">Yesterday, 3:20 PM</div>
                              <div className="timeline-details">Updated occupation and income details</div>
                            </div>
                          </div>
                          <div className="timeline-item">
                            <div className="timeline-icon">👁️</div>
                            <div className="timeline-content">
                              <div className="timeline-title">Viewed 5 Profiles</div>
                              <div className="timeline-time">2 days ago</div>
                              <div className="timeline-details">Spent 15 minutes browsing profiles</div>
                            </div>
                          </div>
                          <div className="timeline-item">
                            <div className="timeline-icon">💬</div>
                            <div className="timeline-content">
                              <div className="timeline-title">Sent 3 Messages</div>
                              <div className="timeline-time">3 days ago</div>
                              <div className="timeline-details">Initiated 2 new conversations</div>
                            </div>
                          </div>
                          <div className="timeline-item">
                            <div className="timeline-icon">🔔</div>
                            <div className="timeline-content">
                              <div className="timeline-title">Account Created</div>
                              <div className="timeline-time">{new Date(currentUser.registeredOn).toLocaleDateString('en-US', {
                                day: 'numeric',
                                month: 'short',
                                year: 'numeric'
                              })}</div>
                              <div className="timeline-details">Completed basic profile setup</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {!tabLoading && activeTab === 'verification' && (
                    <div className="verification-tab">
                      <div className="tab-section">
                        <h4 className="section-title">Verification Status</h4>
                        <div className="verification-status">
                          <div className="status-indicator">
                            <div className={`status-icon ${currentUser.verified ? 'verified' : 'unverified'}`}>
                              {currentUser.verified ? '✓' : '!'}
                            </div>
                            <div className="status-text">
                              <h5>{currentUser.verified ? 'Verified Account' : 'Unverified Account'}</h5>
                              <p>{currentUser.verified ?
                                'This user has completed the verification process and their identity has been confirmed.' :
                                'This user has not completed the verification process yet.'}
                              </p>
                            </div>
                          </div>

                          <div className="verification-actions">
                            {currentUser.verified ? (
                              <button className="btn btn-warning">Revoke Verification</button>
                            ) : (
                              <button className="btn btn-success">Approve Verification</button>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="tab-section">
                        <h4 className="section-title">Verification Documents</h4>
                        <div className="documents-list">
                          <div className="document-item">
                            <div className="document-icon">🪪</div>
                            <div className="document-info">
                              <div className="document-title">ID Proof</div>
                              <div className="document-status">
                                <span className="status-badge verified">Verified</span>
                              </div>
                            </div>
                            <div className="document-actions">
                              <button className="btn btn-sm btn-secondary">View</button>
                            </div>
                          </div>
                          <div className="document-item">
                            <div className="document-icon">📄</div>
                            <div className="document-info">
                              <div className="document-title">Address Proof</div>
                              <div className="document-status">
                                <span className="status-badge verified">Verified</span>
                              </div>
                            </div>
                            <div className="document-actions">
                              <button className="btn btn-sm btn-secondary">View</button>
                            </div>
                          </div>
                          <div className="document-item">
                            <div className="document-icon">🎓</div>
                            <div className="document-info">
                              <div className="document-title">Education Certificate</div>
                              <div className="document-status">
                                <span className="status-badge pending">Pending</span>
                              </div>
                            </div>
                            <div className="document-actions">
                              <button className="btn btn-sm btn-secondary">View</button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {!tabLoading && activeTab === 'communication' && (
                    <div className="communication-tab">
                      <div className="tab-section">
                        <h4 className="section-title">Send Message</h4>
                        <div className="message-form">
                          <div className="form-group">
                            <label>Subject</label>
                            <select className="form-control">
                              <option>Account Verification</option>
                              <option>Profile Update Required</option>
                              <option>Subscription Information</option>
                              <option>Important Notification</option>
                              <option>Custom Message</option>
                            </select>
                          </div>
                          <div className="form-group">
                            <label>Message</label>
                            <textarea
                              className="form-control"
                              rows="4"
                              placeholder="Type your message here..."
                            ></textarea>
                          </div>
                          <div className="form-group">
                            <label>Send via</label>
                            <div className="send-options">
                              <label className="option">
                                <input type="checkbox" checked={true} />
                                <span>In-App Notification</span>
                              </label>
                              <label className="option">
                                <input type="checkbox" checked={true} />
                                <span>Email ({currentUser.email || 'Not available'})</span>
                              </label>
                              <label className="option">
                                <input type="checkbox" />
                                <span>SMS ({currentUser.phone || 'Not available'})</span>
                              </label>
                            </div>
                          </div>
                          <div className="form-actions">
                            <button className="btn btn-primary">Send Message</button>
                          </div>
                        </div>
                      </div>

                      <div className="tab-section">
                        <h4 className="section-title">Communication History</h4>
                        <div className="communication-history">
                          <div className="history-item">
                            <div className="history-icon">📧</div>
                            <div className="history-content">
                              <div className="history-title">Welcome Email</div>
                              <div className="history-time">
                                {new Date(currentUser.registeredOn).toLocaleDateString('en-US', {
                                  day: 'numeric',
                                  month: 'short',
                                  year: 'numeric'
                                })}
                              </div>
                              <div className="history-status">
                                <span className="status-badge delivered">Delivered</span>
                              </div>
                            </div>
                          </div>
                          <div className="history-item">
                            <div className="history-icon">🔔</div>
                            <div className="history-content">
                              <div className="history-title">Profile Completion Reminder</div>
                              <div className="history-time">
                                {new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', {
                                  day: 'numeric',
                                  month: 'short',
                                  year: 'numeric'
                                })}
                              </div>
                              <div className="history-status">
                                <span className="status-badge read">Read</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <div className="modal-standard-buttons">
                <button className="btn btn-secondary" onClick={closeUserModal}>Close</button>
                <button className="btn btn-primary" onClick={() => { closeUserModal(); handleEditUser(currentUser); }}>Edit User</button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Subscription Modal */}
      {showSubscriptionModal && currentUser && (
        <div className="modal-overlay">
          <div className="modal subscription-modal">
            <div className="modal-header">
              <h3 className="modal-title">Manage Subscription for {currentUser.name}</h3>
              <button className="modal-close-button" onClick={closeSubscriptionModal}>&times;</button>
            </div>
            <div className="modal-body">
              <div className="subscription-info">
                <p>Current Subscription: <strong>{currentUser.subscriptionPlan ?
                  (subscriptionPlans.find(p => p.id === currentUser.subscriptionPlan)?.name || currentUser.subscriptionPlan)
                  : 'None'}</strong></p>
                <p>Select a new subscription plan:</p>
              </div>
              <div className="subscription-plans">
                {subscriptionPlans.map(plan => (
                  <div
                    key={plan.id}
                    className={`subscription-plan ${selectedPlan === plan.id ? 'selected' : ''}`}
                    onClick={() => setSelectedPlan(plan.id)}
                  >
                    <div className="plan-header">
                      <h4 className="plan-name">{plan.name}</h4>
                      <div className="plan-price">{plan.price}</div>
                      <div className="plan-duration">{plan.duration}</div>
                    </div>
                    <div className="plan-features">
                      <ul>
                        {plan.features.map((feature, index) => (
                          <li key={index}>{feature}</li>
                        ))}
                      </ul>
                    </div>
                    <div className="plan-select">
                      <input
                        type="radio"
                        name="subscription-plan"
                        checked={selectedPlan === plan.id}
                        onChange={() => setSelectedPlan(plan.id)}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={closeSubscriptionModal}>Cancel</button>
              <button className="btn btn-subscription" onClick={confirmSubscriptionChange}>Update Subscription</button>
            </div>
          </div>
        </div>
      )}

      {/* Error Modal */}
      {showErrorModal && error && (
        <div className="modal-overlay">
          <div className="modal error-modal">
            <div className="modal-header">
              <h3 className="modal-title">Error Occurred</h3>
              <button className="modal-close-button" onClick={() => setShowErrorModal(false)}>&times;</button>
            </div>
            <div className="modal-body">
              <div className="error-modal-content">
                <div className="error-icon">⚠️</div>
                <h4 className="error-title">{error.type === 'api' ? 'API Error' :
                                            error.type === 'data' ? 'Data Error' :
                                            error.type === 'export' ? 'Export Error' : 'Unexpected Error'}</h4>
                <p className="error-message">{error.message}</p>
                <div className="error-details-box">
                  <p className="error-details-title">Technical Details:</p>
                  <p className="error-details-content">{error.details}</p>
                  <p className="error-timestamp">Time: {new Date(error.timestamp).toLocaleString()}</p>
                </div>
                <p className="error-help">The system has automatically recovered by using mock data. You can continue using the application.</p>
              </div>
            </div>
            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={() => setShowErrorModal(false)}>Close</button>
              <button className="btn btn-primary" onClick={() => {
                setShowErrorModal(false);
                handleRefresh();
              }}>Retry</button>
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmModal && currentUser && (
        <div className="modal-overlay">
          <div className="modal confirmation-modal">
            <div className="modal-header">
              <h3 className="modal-title">Confirm Action</h3>
              <button className="modal-close-button" onClick={closeConfirmModal}>&times;</button>
            </div>
            <div className="modal-body">
              {confirmAction === 'delete' && (
                <p>Are you sure you want to delete user <strong>{currentUser.name}</strong>? This action cannot be undone.</p>
              )}
              {confirmAction === 'suspend' && (
                <p>Are you sure you want to suspend user <strong>{currentUser.name}</strong>? They will not be able to access their account until reactivated.</p>
              )}
              {confirmAction === 'activate' && (
                <p>Are you sure you want to activate user <strong>{currentUser.name}</strong>? They will regain access to their account.</p>
              )}
              {confirmAction === 'addPremium' && (
                <p>Are you sure you want to upgrade <strong>{currentUser.name}</strong> to premium status? They will gain access to all premium features.</p>
              )}
              {confirmAction === 'removePremium' && (
                <p>Are you sure you want to remove premium status from <strong>{currentUser.name}</strong>? They will lose access to premium features.</p>
              )}
            </div>
            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={closeConfirmModal}>Cancel</button>
              {confirmAction === 'delete' && (
                <button className="btn btn-danger" onClick={confirmDeleteUser}>Delete User</button>
              )}
              {confirmAction === 'suspend' && (
                <button className="btn btn-danger" onClick={confirmStatusChange}>Suspend User</button>
              )}
              {confirmAction === 'activate' && (
                <button className="btn btn-success" onClick={confirmStatusChange}>Activate User</button>
              )}
              {confirmAction === 'addPremium' && (
                <button className="btn btn-premium" onClick={confirmPremiumChange}>Upgrade to Premium</button>
              )}
              {confirmAction === 'removePremium' && (
                <button className="btn btn-warning" onClick={confirmPremiumChange}>Remove Premium</button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Bulk Action Confirmation Modal */}
      {showBulkConfirmModal && (
        <div className="modal-overlay">
          <div className="modal confirmation-modal">
            <div className="modal-header">
              <h3 className="modal-title">Confirm Bulk Action</h3>
              <button className="modal-close-button" onClick={closeBulkConfirmModal}>&times;</button>
            </div>
            <div className="modal-body">
              {selectedBulkAction === 'delete' && (
                <p>Are you sure you want to delete <strong>{selectedUsers.length}</strong> users? This action cannot be undone.</p>
              )}
              {selectedBulkAction === 'suspend' && (
                <p>Are you sure you want to suspend <strong>{selectedUsers.length}</strong> users? They will not be able to access their accounts until reactivated.</p>
              )}
              {selectedBulkAction === 'activate' && (
                <p>Are you sure you want to activate <strong>{selectedUsers.length}</strong> users? They will regain access to their accounts.</p>
              )}
              {selectedBulkAction === 'premium' && (
                <p>Are you sure you want to upgrade <strong>{selectedUsers.length}</strong> users to premium status? They will gain access to all premium features.</p>
              )}
              {selectedBulkAction === 'removePremium' && (
                <p>Are you sure you want to remove premium status from <strong>{selectedUsers.length}</strong> users? They will lose access to premium features.</p>
              )}
            </div>
            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={closeBulkConfirmModal}>Cancel</button>
              <button
                className={`btn ${
                  selectedBulkAction === 'delete' || selectedBulkAction === 'suspend' ? 'btn-danger' :
                  selectedBulkAction === 'activate' ? 'btn-success' :
                  selectedBulkAction === 'premium' ? 'btn-premium' :
                  'btn-warning'
                }`}
                onClick={confirmBulkAction}
              >
                {selectedBulkAction === 'delete' ? 'Delete Users' :
                 selectedBulkAction === 'suspend' ? 'Suspend Users' :
                 selectedBulkAction === 'activate' ? 'Activate Users' :
                 selectedBulkAction === 'premium' ? 'Upgrade Users' :
                 'Remove Premium'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit User Modal */}
      {showEditModal && currentUser && (
        <div className="modal-overlay">
          <div className="modal edit-modal">
            <div className="modal-header">
              <h3 className="modal-title">Edit User: {currentUser.name}</h3>
              <button className="modal-close-button" onClick={closeEditModal}>&times;</button>
            </div>
            <div className="modal-body">
              <div className="edit-form">
                <div className="form-section">
                  <h4 className="section-title">Basic Information</h4>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="name">Full Name*</label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        className={`form-control ${editFormErrors.name ? 'error' : ''}`}
                        value={editFormData.name}
                        onChange={handleEditFormChange}
                      />
                      {editFormErrors.name && <div className="error-message">{editFormErrors.name}</div>}
                    </div>
                    <div className="form-group">
                      <label htmlFor="email">Email</label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        className={`form-control ${editFormErrors.email ? 'error' : ''}`}
                        value={editFormData.email}
                        onChange={handleEditFormChange}
                      />
                      {editFormErrors.email && <div className="error-message">{editFormErrors.email}</div>}
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="phone">Phone</label>
                      <input
                        type="text"
                        id="phone"
                        name="phone"
                        className={`form-control ${editFormErrors.phone ? 'error' : ''}`}
                        value={editFormData.phone}
                        onChange={handleEditFormChange}
                      />
                      {editFormErrors.phone && <div className="error-message">{editFormErrors.phone}</div>}
                    </div>
                    <div className="form-group">
                      <label htmlFor="gender">Gender</label>
                      <select
                        id="gender"
                        name="gender"
                        className="form-control"
                        value={editFormData.gender}
                        onChange={handleEditFormChange}
                      >
                        <option value="">Select Gender</option>
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                        <option value="Other">Other</option>
                      </select>
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="age">Age</label>
                      <input
                        type="number"
                        id="age"
                        name="age"
                        className={`form-control ${editFormErrors.age ? 'error' : ''}`}
                        value={editFormData.age}
                        onChange={handleEditFormChange}
                        min="18"
                        max="100"
                      />
                      {editFormErrors.age && <div className="error-message">{editFormErrors.age}</div>}
                    </div>
                    <div className="form-group">
                      <label htmlFor="location">Location</label>
                      <input
                        type="text"
                        id="location"
                        name="location"
                        className="form-control"
                        value={editFormData.location}
                        onChange={handleEditFormChange}
                      />
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="dob">Date of Birth</label>
                      <input
                        type="date"
                        id="dob"
                        name="dob"
                        className="form-control"
                        value={editFormData.dob ? editFormData.dob.split('T')[0] : ''}
                        onChange={handleEditFormChange}
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="birthTime">Birth Time</label>
                      <input
                        type="time"
                        id="birthTime"
                        name="birthTime"
                        className="form-control"
                        value={editFormData.birthTime}
                        onChange={handleEditFormChange}
                      />
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="birthPlace">Birth Place</label>
                      <input
                        type="text"
                        id="birthPlace"
                        name="birthPlace"
                        className="form-control"
                        value={editFormData.birthPlace}
                        onChange={handleEditFormChange}
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="bloodGroup">Blood Group</label>
                      <select
                        id="bloodGroup"
                        name="bloodGroup"
                        className="form-control"
                        value={editFormData.bloodGroup}
                        onChange={handleEditFormChange}
                      >
                        <option value="">Select Blood Group</option>
                        <option value="A+">A+</option>
                        <option value="A-">A-</option>
                        <option value="B+">B+</option>
                        <option value="B-">B-</option>
                        <option value="AB+">AB+</option>
                        <option value="AB-">AB-</option>
                        <option value="O+">O+</option>
                        <option value="O-">O-</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div className="form-section">
                  <h4 className="section-title">Professional Details</h4>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="occupation">Occupation</label>
                      <input
                        type="text"
                        id="occupation"
                        name="occupation"
                        className="form-control"
                        value={editFormData.occupation}
                        onChange={handleEditFormChange}
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="education">Education</label>
                      <input
                        type="text"
                        id="education"
                        name="education"
                        className="form-control"
                        value={editFormData.education}
                        onChange={handleEditFormChange}
                      />
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="income">Income</label>
                      <input
                        type="text"
                        id="income"
                        name="income"
                        className="form-control"
                        value={editFormData.income}
                        onChange={handleEditFormChange}
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="maritalStatus">Marital Status</label>
                      <select
                        id="maritalStatus"
                        name="maritalStatus"
                        className="form-control"
                        value={editFormData.maritalStatus}
                        onChange={handleEditFormChange}
                      >
                        <option value="">Select Marital Status</option>
                        <option value="Never Married">Never Married</option>
                        <option value="Divorced">Divorced</option>
                        <option value="Widowed">Widowed</option>
                        <option value="Separated">Separated</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div className="form-section">
                  <h4 className="section-title">Community Details</h4>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="religion">Religion</label>
                      <input
                        type="text"
                        id="religion"
                        name="religion"
                        className="form-control"
                        value={editFormData.religion}
                        onChange={handleEditFormChange}
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="caste">Caste</label>
                      <input
                        type="text"
                        id="caste"
                        name="caste"
                        className="form-control"
                        value={editFormData.caste}
                        onChange={handleEditFormChange}
                      />
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="subcaste">Subcaste</label>
                      <input
                        type="text"
                        id="subcaste"
                        name="subcaste"
                        className="form-control"
                        value={editFormData.subcaste}
                        onChange={handleEditFormChange}
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="gotra">Gotra</label>
                      <input
                        type="text"
                        id="gotra"
                        name="gotra"
                        className="form-control"
                        value={editFormData.gotra}
                        onChange={handleEditFormChange}
                      />
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="rashi">Rashi</label>
                      <input
                        type="text"
                        id="rashi"
                        name="rashi"
                        className="form-control"
                        value={editFormData.rashi}
                        onChange={handleEditFormChange}
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="nakshatra">Nakshatra</label>
                      <input
                        type="text"
                        id="nakshatra"
                        name="nakshatra"
                        className="form-control"
                        value={editFormData.nakshatra}
                        onChange={handleEditFormChange}
                      />
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="manglik">Manglik</label>
                      <select
                        id="manglik"
                        name="manglik"
                        className="form-control"
                        value={editFormData.manglik.toString()}
                        onChange={(e) => {
                          setEditFormData({
                            ...editFormData,
                            manglik: e.target.value === 'true'
                          });
                        }}
                      >
                        <option value="false">No</option>
                        <option value="true">Yes</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div className="form-section">
                  <h4 className="section-title">Physical Attributes</h4>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="height">Height</label>
                      <input
                        type="text"
                        id="height"
                        name="height"
                        className="form-control"
                        value={editFormData.height}
                        onChange={handleEditFormChange}
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="weight">Weight</label>
                      <input
                        type="text"
                        id="weight"
                        name="weight"
                        className="form-control"
                        value={editFormData.weight}
                        onChange={handleEditFormChange}
                      />
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="complexion">Complexion</label>
                      <select
                        id="complexion"
                        name="complexion"
                        className="form-control"
                        value={editFormData.complexion}
                        onChange={handleEditFormChange}
                      >
                        <option value="">Select Complexion</option>
                        <option value="Fair">Fair</option>
                        <option value="Wheatish">Wheatish</option>
                        <option value="Wheatish Brown">Wheatish Brown</option>
                        <option value="Brown">Brown</option>
                        <option value="Dark">Dark</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div className="form-section">
                  <h4 className="section-title">Lifestyle</h4>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="diet">Diet</label>
                      <select
                        id="diet"
                        name="diet"
                        className="form-control"
                        value={editFormData.diet}
                        onChange={handleEditFormChange}
                      >
                        <option value="">Select Diet</option>
                        <option value="Vegetarian">Vegetarian</option>
                        <option value="Non-Vegetarian">Non-Vegetarian</option>
                        <option value="Eggetarian">Eggetarian</option>
                        <option value="Vegan">Vegan</option>
                        <option value="Jain">Jain</option>
                      </select>
                    </div>
                    <div className="form-group">
                      <label htmlFor="smoking">Smoking</label>
                      <select
                        id="smoking"
                        name="smoking"
                        className="form-control"
                        value={editFormData.smoking}
                        onChange={handleEditFormChange}
                      >
                        <option value="">Select Option</option>
                        <option value="No">No</option>
                        <option value="Occasionally">Occasionally</option>
                        <option value="Yes">Yes</option>
                      </select>
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="drinking">Drinking</label>
                      <select
                        id="drinking"
                        name="drinking"
                        className="form-control"
                        value={editFormData.drinking}
                        onChange={handleEditFormChange}
                      >
                        <option value="">Select Option</option>
                        <option value="No">No</option>
                        <option value="Occasionally">Occasionally</option>
                        <option value="Yes">Yes</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div className="form-section">
                  <h4 className="section-title">Hobbies & Interests</h4>
                  <div className="form-row">
                    <div className="form-group full-width">
                      <label htmlFor="hobbies">Hobbies</label>
                      <input
                        type="text"
                        id="hobbies"
                        name="hobbies"
                        className="form-control"
                        value={editFormData.hobbies || ''}
                        onChange={handleEditFormChange}
                        placeholder="Enter hobbies separated by commas (e.g., Reading, Cooking, Traveling)"
                      />
                      <small className="form-text text-muted">Separate multiple hobbies with commas</small>
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group full-width">
                      <label htmlFor="interests">Interests</label>
                      <input
                        type="text"
                        id="interests"
                        name="interests"
                        className="form-control"
                        value={editFormData.interests || ''}
                        onChange={handleEditFormChange}
                        placeholder="Enter interests separated by commas (e.g., Music, Photography, Technology)"
                      />
                      <small className="form-text text-muted">Separate multiple interests with commas</small>
                    </div>
                  </div>
                </div>

                <div className="form-section">
                  <h4 className="section-title">Family Details</h4>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="fatherName">Father's Name</label>
                      <input
                        type="text"
                        id="fatherName"
                        name="fatherName"
                        className="form-control"
                        value={editFormData.fatherName}
                        onChange={handleEditFormChange}
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="fatherOccupation">Father's Occupation</label>
                      <input
                        type="text"
                        id="fatherOccupation"
                        name="fatherOccupation"
                        className="form-control"
                        value={editFormData.fatherOccupation}
                        onChange={handleEditFormChange}
                      />
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="motherName">Mother's Name</label>
                      <input
                        type="text"
                        id="motherName"
                        name="motherName"
                        className="form-control"
                        value={editFormData.motherName}
                        onChange={handleEditFormChange}
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="motherOccupation">Mother's Occupation</label>
                      <input
                        type="text"
                        id="motherOccupation"
                        name="motherOccupation"
                        className="form-control"
                        value={editFormData.motherOccupation}
                        onChange={handleEditFormChange}
                      />
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="brothers">Brothers</label>
                      <input
                        type="number"
                        id="brothers"
                        name="brothers"
                        className="form-control"
                        value={editFormData.brothers}
                        onChange={handleEditFormChange}
                        min="0"
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="sisters">Sisters</label>
                      <input
                        type="number"
                        id="sisters"
                        name="sisters"
                        className="form-control"
                        value={editFormData.sisters}
                        onChange={handleEditFormChange}
                        min="0"
                      />
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="familyType">Family Type</label>
                      <select
                        id="familyType"
                        name="familyType"
                        className="form-control"
                        value={editFormData.familyType}
                        onChange={handleEditFormChange}
                      >
                        <option value="">Select Family Type</option>
                        <option value="Joint">Joint</option>
                        <option value="Nuclear">Nuclear</option>
                      </select>
                    </div>
                    <div className="form-group">
                      <label htmlFor="familyValues">Family Values</label>
                      <select
                        id="familyValues"
                        name="familyValues"
                        className="form-control"
                        value={editFormData.familyValues}
                        onChange={handleEditFormChange}
                      >
                        <option value="">Select Family Values</option>
                        <option value="Traditional">Traditional</option>
                        <option value="Moderate">Moderate</option>
                        <option value="Liberal">Liberal</option>
                      </select>
                    </div>
                  </div>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="familyStatus">Family Status</label>
                      <input
                        type="text"
                        id="familyStatus"
                        name="familyStatus"
                        className="form-control"
                        value={editFormData.familyStatus}
                        onChange={handleEditFormChange}
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="familyLocation">Family Location</label>
                      <input
                        type="text"
                        id="familyLocation"
                        name="familyLocation"
                        className="form-control"
                        value={editFormData.familyLocation}
                        onChange={handleEditFormChange}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={closeEditModal}>Cancel</button>
              <button className="btn btn-primary" onClick={saveEditForm}>Save Changes</button>
            </div>
          </div>
        </div>
      )}

      {/* CSS Styles */}
      <style jsx>{`
        /* Content Header */
        .content-header {
          display: flex;
          flex-direction: column;
          margin-bottom: 20px;
        }

        .page-title {
          font-size: 24px;
          font-weight: 600;
          margin: 0 0 15px 0;
        }

        /* Header Actions */
        .header-actions {
          display: flex;
          gap: 15px;
          align-items: center;
          flex-wrap: wrap;
          width: 100%;
        }

        /* Bulk Actions */
        .bulk-actions {
          display: flex;
          align-items: center;
          gap: 15px;
          margin-bottom: 15px;
          width: 100%;
          position: relative;
        }

        .bulk-action-controls {
          display: flex;
          gap: 10px;
        }

        .bulk-action-select {
          padding: 8px 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
          background-color: white;
          min-width: 180px;
          appearance: none; /* Use custom dropdown arrow instead of browser default */
          cursor: pointer;
          transition: border-color 0.2s, box-shadow 0.2s;
          background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
          background-repeat: no-repeat;
          background-position: right 8px center;
          background-size: 16px;
          padding-right: 30px;
        }

        .bulk-action-select:focus {
          outline: none;
          border-color: #6200ea;
          box-shadow: 0 0 0 2px rgba(98, 0, 234, 0.2);
        }

        .bulk-apply-btn {
          padding: 8px 16px;
          background-color: #6200ea;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .bulk-apply-btn:hover:not(:disabled) {
          background-color: #5000c8;
        }

        .bulk-apply-btn:disabled {
          background-color: #b39ddb;
          cursor: not-allowed;
        }

        .selected-count {
          font-size: 14px;
          color: #6200ea;
          background-color: #f5f5f5;
          padding: 5px 10px;
          border-radius: 4px;
          white-space: nowrap;
          font-weight: 500;
        }

        .checkbox-column {
          width: 40px;
          text-align: center;
        }

        .checkbox-column input[type="checkbox"] {
          width: 18px;
          height: 18px;
          cursor: pointer;
        }

        .search-container {
          position: relative;
          min-width: 250px;
        }

        .search-input {
          padding: 10px 15px;
          padding-right: 40px;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 0.9rem;
          width: 100%;
          transition: all 0.3s ease;
        }

        .search-input:focus {
          border-color: #6200ea;
          box-shadow: 0 0 0 2px rgba(98, 0, 234, 0.2);
          outline: none;
        }

        .search-button {
          position: absolute;
          right: 5px;
          top: 50%;
          transform: translateY(-50%);
          height: 30px;
          width: 30px;
          background: none;
          border: none;
          cursor: pointer;
          color: #6200ea;
          font-size: 1.1rem;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: background-color 0.2s;
        }

        .search-button:hover {
          background-color: rgba(98, 0, 234, 0.1);
        }

        .status-filter,
        .gender-filter,
        .verified-filter,
        .premium-filter {
          padding: 10px 15px;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 0.9rem;
          background-color: white;
          cursor: pointer;
          appearance: none; /* Use custom dropdown arrow instead of browser default */
          min-width: 140px;
          transition: border-color 0.2s, box-shadow 0.2s;
          background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
          background-repeat: no-repeat;
          background-position: right 8px center;
          background-size: 16px;
          padding-right: 30px;
        }

        .status-filter:focus,
        .gender-filter:focus,
        .verified-filter:focus,
        .premium-filter:focus {
          border-color: #6200ea;
          box-shadow: 0 0 0 2px rgba(98, 0, 234, 0.2);
          outline: none;
        }

        .refresh-button {
          padding: 10px 15px;
          border: none;
          border-radius: 6px;
          background-color: var(--secondary);
          color: white;
          font-size: 0.9rem;
          cursor: pointer;
          transition: background-color 0.3s ease;
        }

        .refresh-button:hover {
          background-color: #e64a19;
        }

        .export-button {
          padding: 10px 15px;
          border: none;
          border-radius: 6px;
          background-color: #4caf50;
          color: white;
          font-size: 0.9rem;
          cursor: pointer;
          transition: background-color 0.3s ease;
        }

        .export-button:hover {
          background-color: #388e3c;
        }

        /* Table Styles */
        .table-container {
          background-color: white;
          border-radius: 12px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          overflow: hidden;
          margin-bottom: 30px;
        }

        .data-table {
          width: 100%;
          border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
          padding: 15px;
          text-align: left;
          border-bottom: 1px solid #eee;
        }

        .data-table th {
          background-color: #f9f9f9;
          font-weight: 600;
          color: #555;
        }

        .data-table tr:last-child td {
          border-bottom: none;
        }

        .data-table tr:hover {
          background-color: #f5f5f5;
        }

        /* User Cell */
        .user-cell {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .user-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background-color: var(--primary);
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 1.2rem;
          overflow: hidden;
        }

        .user-avatar img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .user-avatar.large {
          width: 80px;
          height: 80px;
          font-size: 2rem;
        }

        .user-info {
          display: flex;
          flex-direction: column;
        }

        .user-name {
          font-weight: 600;
          color: #333;
        }

        .user-email {
          font-size: 0.85rem;
          color: #666;
        }

        /* Status Badge */
        .status-badge {
          display: inline-block;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 0.8rem;
          font-weight: 500;
          text-transform: capitalize;
        }

        .status-badge.verified {
          background-color: rgba(76, 175, 80, 0.1);
          color: #388e3c;
        }

        .status-badge.unverified {
          background-color: rgba(255, 152, 0, 0.1);
          color: #f57c00;
        }

        .status-badge.premium {
          background-color: rgba(156, 39, 176, 0.1);
          color: #7b1fa2;
        }

        .status-badge.basic {
          background-color: rgba(33, 150, 243, 0.1);
          color: #1976d2;
        }

        .status-badge.active {
          background-color: rgba(76, 175, 80, 0.1);
          color: #388e3c;
        }

        .status-badge.inactive {
          background-color: rgba(158, 158, 158, 0.1);
          color: #616161;
        }

        .status-badge.suspended {
          background-color: rgba(244, 67, 54, 0.1);
          color: #d32f2f;
        }

        .status-badge.pending_approval {
          background-color: rgba(255, 152, 0, 0.1);
          color: #f57c00;
        }

        .status-badge.incomplete {
          background-color: rgba(33, 150, 243, 0.1);
          color: #1976d2;
        }

        /* Action Cell */
        .action-cell {
          display: flex;
          gap: 8px;
        }

        .action-btn {
          width: 32px;
          height: 32px;
          border-radius: 4px;
          border: none;
          background-color: transparent;
          cursor: pointer;
          font-size: 1.1rem;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: background-color 0.3s ease;
        }

        .action-btn:hover {
          background-color: #f5f5f5;
        }

        .action-btn.view-btn:hover {
          color: var(--primary);
        }

        .action-btn.edit-btn:hover {
          color: var(--secondary);
        }

        .action-btn.delete-btn:hover {
          color: #f44336;
        }

        /* Pagination */
        .pagination-container {
          display: flex;
          justify-content: center;
          padding: 20px 0;
          border-top: 1px solid #eee;
        }

        .pagination {
          display: flex;
          gap: 5px;
        }

        .pagination-btn {
          padding: 8px 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
          background-color: white;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .pagination-btn:hover {
          background-color: #f5f5f5;
        }

        .pagination-btn.active {
          background-color: var(--primary);
          color: white;
          border-color: var(--primary);
        }

        .pagination-btn:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        /* Modal Styles */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .modal {
          background-color: white;
          border-radius: 12px;
          width: 90%;
          max-width: 800px;
          max-height: 90vh;
          display: flex;
          flex-direction: column;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
          overflow: hidden;
        }

        .confirmation-modal {
          max-width: 500px;
        }

        .error-modal {
          max-width: 600px;
        }

        .error-modal-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
        }

        .error-details-box {
          background-color: #f5f5f5;
          padding: 15px;
          border-radius: 8px;
          width: 100%;
          margin: 15px 0;
          text-align: left;
        }

        .error-details-title {
          font-weight: 600;
          margin-bottom: 8px;
          color: #555;
        }

        .error-details-content {
          font-family: monospace;
          white-space: pre-wrap;
          word-break: break-word;
          margin-bottom: 8px;
        }

        .error-timestamp {
          font-size: 0.8rem;
          color: #777;
          text-align: right;
          margin: 0;
        }

        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px;
          border-bottom: 1px solid #eee;
        }

        .modal-title {
          font-size: 1.2rem;
          font-weight: 600;
          color: var(--text-dark);
          margin: 0;
        }

        .modal-close-button {
          background: none;
          border: none;
          font-size: 1.5rem;
          cursor: pointer;
          color: #999;
          transition: color 0.2s ease;
        }

        .modal-close-button:hover {
          color: var(--text-dark);
        }

        .modal-body {
          padding: 20px;
          overflow-y: auto;
          flex: 1;
        }

        .modal-footer {
          display: flex;
          justify-content: space-between;
          padding: 15px 20px;
          border-top: 1px solid #eee;
        }

        .modal-actions {
          display: flex;
          gap: 10px;
        }

        .modal-standard-buttons {
          display: flex;
          gap: 10px;
        }

        .btn-premium {
          background-color: #9c27b0;
          color: white;
        }

        .btn-premium:hover {
          background-color: #7b1fa2;
        }

        .btn-subscription {
          background-color: #2196f3;
          color: white;
        }

        .btn-subscription:hover {
          background-color: #1976d2;
        }

        .btn-success {
          background-color: #4caf50;
          color: white;
        }

        .btn-success:hover {
          background-color: #388e3c;
        }

        .btn-warning {
          background-color: #ff9800;
          color: white;
        }

        .btn-warning:hover {
          background-color: #f57c00;
        }

        /* Subscription Modal Styles */
        .subscription-modal {
          max-width: 900px;
        }

        .subscription-info {
          margin-bottom: 20px;
        }

        .subscription-plans {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 20px;
          margin-bottom: 20px;
        }

        .subscription-plan {
          border: 1px solid #ddd;
          border-radius: 8px;
          padding: 15px;
          cursor: pointer;
          transition: all 0.3s ease;
          position: relative;
          display: flex;
          flex-direction: column;
        }

        .subscription-plan:hover {
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          transform: translateY(-2px);
        }

        .subscription-plan.selected {
          border-color: var(--primary);
          box-shadow: 0 5px 15px rgba(103, 58, 183, 0.2);
        }

        .plan-header {
          text-align: center;
          padding-bottom: 10px;
          border-bottom: 1px solid #eee;
          margin-bottom: 10px;
        }

        .plan-name {
          font-size: 1.2rem;
          font-weight: 600;
          margin: 0 0 5px 0;
          color: var(--text-dark);
        }

        .plan-price {
          font-size: 1.5rem;
          font-weight: 700;
          color: var(--primary);
          margin-bottom: 5px;
        }

        .plan-duration {
          font-size: 0.9rem;
          color: #666;
        }

        .plan-features {
          flex: 1;
        }

        .plan-features ul {
          list-style-type: none;
          padding: 0;
          margin: 0;
        }

        .plan-features li {
          padding: 5px 0;
          font-size: 0.9rem;
          position: relative;
          padding-left: 20px;
        }

        .plan-features li:before {
          content: "✓";
          color: var(--primary);
          position: absolute;
          left: 0;
        }

        .plan-select {
          text-align: center;
          margin-top: 15px;
        }

        /* Profile Styles */
        .profile-header {
          display: flex;
          align-items: center;
          gap: 20px;
          padding-bottom: 20px;
          border-bottom: 1px solid #eee;
        }

        .profile-info {
          flex: 1;
        }

        .profile-name {
          font-size: 1.5rem;
          margin-bottom: 5px;
        }

        .profile-meta {
          display: flex;
          gap: 8px;
          color: #666;
          margin-bottom: 10px;
        }

        .profile-status {
          display: flex;
          gap: 8px;
        }

        .profile-tabs {
          margin-top: 20px;
        }

        .tab-section {
          margin-bottom: 30px;
        }

        .section-title {
          font-size: 1.1rem;
          font-weight: 600;
          margin-bottom: 15px;
          padding-bottom: 8px;
          border-bottom: 1px solid #eee;
          color: var(--text-dark);
        }

        .user-details-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 15px;
        }

        .detail-item {
          display: flex;
          flex-direction: column;
        }

        .detail-label {
          font-size: 0.85rem;
          color: #666;
          margin-bottom: 4px;
        }

        .detail-value {
          font-weight: 500;
        }

        /* Button Styles */
        .btn {
          padding: 8px 16px;
          border-radius: 4px;
          font-weight: 500;
          cursor: pointer;
          border: none;
          transition: background-color 0.2s ease;
        }

        .btn-primary {
          background-color: var(--primary);
          color: white;
        }

        .btn-primary:hover {
          background-color: var(--primary-dark);
        }

        .btn-secondary {
          background-color: #f5f5f5;
          color: var(--text-dark);
        }

        .btn-secondary:hover {
          background-color: #e0e0e0;
        }

        .btn-danger {
          background-color: var(--danger);
          color: white;
        }

        .btn-danger:hover {
          background-color: #d32f2f;
        }

        /* Loading Styles */
        .loading-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 80px 0;
          background-color: rgba(255, 255, 255, 0.9);
          border-radius: 12px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .loading-spinner {
          width: 50px;
          height: 50px;
          border: 4px solid rgba(0, 0, 0, 0.1);
          border-left-color: var(--primary);
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 20px;
        }

        .loading-text {
          font-size: 1.2rem;
          font-weight: 600;
          color: var(--primary);
          margin-bottom: 8px;
        }

        .loading-subtext {
          font-size: 0.9rem;
          color: #666;
          margin: 0;
        }

        @keyframes spin {
          to {
            transform: rotate(360deg);
          }
        }

        /* Error Styles */
        .error-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 40px;
          background-color: white;
          border-radius: 12px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          text-align: center;
          margin: 20px 0;
        }

        .error-icon {
          font-size: 3rem;
          margin-bottom: 20px;
        }

        .error-title {
          font-size: 1.5rem;
          font-weight: 600;
          color: #d32f2f;
          margin-bottom: 15px;
        }

        .error-message {
          font-size: 1.1rem;
          margin-bottom: 10px;
          color: #333;
        }

        .error-details {
          font-size: 0.9rem;
          color: #666;
          margin-bottom: 20px;
          font-family: monospace;
          background-color: #f5f5f5;
          padding: 10px;
          border-radius: 4px;
          max-width: 100%;
          overflow-x: auto;
        }

        .error-help {
          font-size: 1rem;
          color: #388e3c;
          margin-bottom: 20px;
          font-weight: 500;
        }

        /* Photos Gallery */
        .photos-gallery {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 15px;
          margin-top: 10px;
        }

        .photo-item {
          aspect-ratio: 1;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          cursor: pointer;
          transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .photo-item:hover {
          transform: scale(1.03);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .photo-item img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        /* Tab Navigation Styles */
        .profile-tabs-navigation {
          display: flex;
          border-bottom: 1px solid #e0e0e0;
          margin-bottom: 20px;
          overflow-x: auto;
          padding-bottom: 1px;
        }

        .tab-button {
          padding: 10px 20px;
          background: none;
          border: none;
          border-bottom: 3px solid transparent;
          font-weight: 500;
          color: #666;
          cursor: pointer;
          transition: all 0.2s ease;
          white-space: nowrap;
        }

        .tab-button:hover {
          color: #6200ea;
        }

        .tab-button.active {
          color: #6200ea;
          border-bottom-color: #6200ea;
        }

        .tab-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 40px 0;
          color: #666;
        }

        .loading-spinner.small {
          width: 30px;
          height: 30px;
          border-width: 3px;
          margin-bottom: 15px;
        }

        /* Hobbies & Interests Tags */
        .tags-container {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }

        .tag {
          display: inline-block;
          padding: 4px 10px;
          border-radius: 16px;
          font-size: 0.85rem;
          font-weight: 500;
        }

        .hobby-tag {
          background-color: #e8f0fe;
          color: #1a73e8;
          border: 1px solid #d2e3fc;
        }

        .interest-tag {
          background-color: #fce8ff;
          color: #a142f4;
          border: 1px solid #f4d2ff;
        }

        .detail-item.full-width {
          grid-column: 1 / -1;
        }

        /* Management Tab Styles */
        .management-tab .action-group {
          margin-bottom: 20px;
          padding: 15px;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          background-color: #f9f9f9;
        }

        .management-tab h5 {
          margin-top: 0;
          margin-bottom: 10px;
          font-size: 16px;
          color: #333;
        }

        .action-buttons {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          align-items: center;
        }

        .status-info {
          margin-left: 15px;
          font-size: 14px;
          color: #666;
        }

        .feature-toggles {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
          gap: 15px;
        }

        .feature-toggle {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 15px;
          border: 1px solid #e0e0e0;
          border-radius: 6px;
          background-color: #fff;
        }

        .toggle-label {
          display: flex;
          align-items: center;
          gap: 10px;
          cursor: pointer;
        }

        .feature-tier {
          font-size: 12px;
          padding: 3px 8px;
          border-radius: 12px;
          background-color: #e0e0e0;
          color: #666;
        }

        /* Activity Tab Styles */
        .activity-timeline {
          margin-top: 15px;
        }

        .timeline-item {
          display: flex;
          margin-bottom: 20px;
          position: relative;
        }

        .timeline-item:not(:last-child):after {
          content: '';
          position: absolute;
          left: 15px;
          top: 30px;
          bottom: -20px;
          width: 2px;
          background-color: #e0e0e0;
        }

        .timeline-icon {
          width: 30px;
          height: 30px;
          border-radius: 50%;
          background-color: #f0f0f0;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;
          z-index: 1;
        }

        .timeline-content {
          flex: 1;
        }

        .timeline-title {
          font-weight: 500;
          margin-bottom: 5px;
        }

        .timeline-time {
          font-size: 12px;
          color: #666;
          margin-bottom: 5px;
        }

        .timeline-details {
          font-size: 14px;
          color: #444;
        }

        /* Verification Tab Styles */
        .verification-status {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          background-color: #f9f9f9;
          margin-bottom: 20px;
        }

        .status-indicator {
          display: flex;
          align-items: center;
        }

        .status-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;
          font-size: 20px;
          font-weight: bold;
        }

        .status-icon.verified {
          background-color: #4caf50;
          color: white;
        }

        .status-icon.unverified {
          background-color: #ff9800;
          color: white;
        }

        .status-text h5 {
          margin: 0 0 5px 0;
          font-size: 16px;
        }

        .status-text p {
          margin: 0;
          font-size: 14px;
          color: #666;
        }

        .documents-list {
          display: flex;
          flex-direction: column;
          gap: 10px;
        }

        .document-item {
          display: flex;
          align-items: center;
          padding: 10px 15px;
          border: 1px solid #e0e0e0;
          border-radius: 6px;
          background-color: #fff;
        }

        .document-icon {
          margin-right: 15px;
          font-size: 20px;
        }

        .document-info {
          flex: 1;
        }

        .document-title {
          font-weight: 500;
        }

        .document-status {
          font-size: 12px;
        }

        /* Communication Tab Styles */
        .message-form {
          padding: 15px;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          background-color: #f9f9f9;
        }

        .form-group {
          margin-bottom: 15px;
        }

        .form-group label {
          display: block;
          margin-bottom: 5px;
          font-weight: 500;
        }

        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 14px;
        }

        textarea.form-control {
          resize: vertical;
        }

        .send-options {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
        }

        .option {
          display: flex;
          align-items: center;
          gap: 8px;
          cursor: pointer;
        }

        .form-actions {
          margin-top: 20px;
          display: flex;
          justify-content: flex-end;
        }

        .communication-history {
          margin-top: 15px;
        }

        .history-item {
          display: flex;
          align-items: center;
          padding: 10px 15px;
          border: 1px solid #e0e0e0;
          border-radius: 6px;
          background-color: #fff;
          margin-bottom: 10px;
        }

        .history-icon {
          margin-right: 15px;
          font-size: 20px;
        }

        .history-content {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .history-title {
          font-weight: 500;
        }

        .history-time {
          font-size: 12px;
          color: #666;
        }

        .status-badge.delivered {
          background-color: #4caf50;
        }

        .status-badge.read {
          background-color: #2196f3;
        }

        .status-badge.pending {
          background-color: #ff9800;
        }

        /* Edit Modal Styles */
        .edit-modal {
          width: 800px;
          max-width: 90%;
          max-height: 90vh;
          overflow-y: auto;
        }

        .edit-form {
          padding: 0 15px;
        }

        .form-section {
          margin-bottom: 25px;
          border-bottom: 1px solid #eee;
          padding-bottom: 15px;
        }

        .form-section:last-child {
          border-bottom: none;
        }

        .form-row {
          display: flex;
          flex-wrap: wrap;
          margin: 0 -10px;
        }

        .form-group {
          flex: 1;
          min-width: 250px;
          padding: 0 10px;
          margin-bottom: 15px;
        }

        .form-group.full-width {
          flex: 0 0 100%;
          width: 100%;
        }

        .form-group label {
          display: block;
          margin-bottom: 5px;
          font-weight: 500;
          color: #333;
        }

        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 14px;
          transition: border-color 0.2s;
        }

        .form-control:focus {
          border-color: #6200ea;
          outline: none;
        }

        .form-control.error {
          border-color: #f44336;
        }

        .error-message {
          color: #f44336;
          font-size: 12px;
          margin-top: 5px;
        }

        .form-text {
          font-size: 12px;
          color: #666;
          margin-top: 5px;
        }

        select.form-control {
          appearance: auto; /* Changed from 'none' to 'auto' to show dropdown arrow */
          padding-right: 30px;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
          .header-actions {
            flex-direction: column;
            align-items: stretch;
          }

          .search-input {
            width: 100%;
          }

          .user-details-grid {
            grid-template-columns: 1fr;
          }

          .profile-header {
            flex-direction: column;
            text-align: center;
          }

          .modal {
            width: 95%;
            max-height: 80vh;
          }

          .photos-gallery {
            grid-template-columns: repeat(2, 1fr);
          }

          .profile-tabs-navigation {
            flex-wrap: wrap;
          }

          .tab-button {
            flex: 1;
            text-align: center;
            padding: 10px 5px;
          }

          .action-buttons {
            flex-direction: column;
            align-items: flex-start;
          }

          .status-info {
            margin-left: 0;
            margin-top: 5px;
          }
        }
      `}</style>

      {/* Export Date Range Modal */}
      {showExportModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3 className="modal-title">Export Users</h3>
              <button className="modal-close-button" onClick={() => setShowExportModal(false)}>&times;</button>
            </div>
            <div className="modal-body">
              <p className="export-description">Select a date range to export users who registered during this period:</p>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="startDate">Start Date</label>
                  <input
                    type="date"
                    id="startDate"
                    className="form-control"
                    value={exportDateRange.startDate}
                    onChange={(e) => setExportDateRange({...exportDateRange, startDate: e.target.value})}
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="endDate">End Date</label>
                  <input
                    type="date"
                    id="endDate"
                    className="form-control"
                    value={exportDateRange.endDate}
                    onChange={(e) => setExportDateRange({...exportDateRange, endDate: e.target.value})}
                  />
                </div>
              </div>

              <div className="export-options">
                <p className="export-note">
                  <strong>Note:</strong> This will export all users who registered between the selected dates.
                  Current filters will also be applied.
                </p>
              </div>
            </div>
            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={() => setShowExportModal(false)}>Cancel</button>
              <button
                className="btn btn-primary"
                onClick={() => {
                  setShowExportModal(false);
                  handleExportWithDateRange();
                }}
              >
                Export
              </button>
            </div>
          </div>
        </div>
      )}
    </EnhancedAdminLayout>
  );
}

