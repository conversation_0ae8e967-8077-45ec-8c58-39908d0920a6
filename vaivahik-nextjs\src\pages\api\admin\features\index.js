// API endpoint for feature management
import axios from 'axios';
import { handleApiError } from '@/utils/errorHandler';
import { withAuth } from '@/utils/authHandler';

// Backend API URL - adjust this to your actual backend URL
const BACKEND_API_URL = 'http://localhost:3001/api';

async function handler(req, res) {
  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getFeatures(req, res);
      case 'POST':
        return await createFeature(req, res);
      case 'PUT':
        return await updateFeature(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    return handleApiError(error, res, 'Features API');
  }
}

// In development mode, export the handler directly without authentication
// In production, use the authentication middleware
export default process.env.NODE_ENV === 'development'
  ? handler
  : withAuth(handler, 'ADMIN');

// GET /api/admin/features
async function getFeatures(req, res) {
  try {
    // Construct the API URL
    const apiUrl = `${BACKEND_API_URL}/admin/features`;

    try {
      // Fetch data from the backend API
      const response = await axios.get(apiUrl);

      // Return the response directly from the backend
      return res.status(200).json(response.data);
    } catch (apiError) {
      // Log the error
      console.error('Error fetching features from backend API:', apiError.message);

      // Return mock data instead of error
      console.log('Returning mock feature data');
      return res.status(200).json({
        success: true,
        message: 'Mock features retrieved successfully',
        features: getMockFeatures()
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Get features');
  }
}

// Function to generate mock features
function getMockFeatures() {
  return [
    {
      id: 'feature-1',
      name: 'view-contacts',
      displayName: 'View Contact Details',
      description: 'View phone numbers and email addresses of other users',
      category: 'COMMUNICATION',
      isActive: true,
      accessRules: {
        basic: { isEnabled: false, limitType: 'none', limitValue: 0, upgradeMessage: 'Upgrade to view contact details' },
        verified: { isEnabled: true, limitType: 'daily', limitValue: 5, upgradeMessage: 'Upgrade to view more contacts' },
        premium: { isEnabled: true, limitType: 'none', limitValue: 0, upgradeMessage: '' }
      }
    },
    {
      id: 'feature-2',
      name: 'advanced-search',
      displayName: 'Advanced Search Filters',
      description: 'Use additional filters like education, profession, and income',
      category: 'SEARCH',
      isActive: true,
      accessRules: {
        basic: { isEnabled: false, limitType: 'none', limitValue: 0, upgradeMessage: 'Upgrade to use advanced search' },
        verified: { isEnabled: true, limitType: 'none', limitValue: 0, upgradeMessage: '' },
        premium: { isEnabled: true, limitType: 'none', limitValue: 0, upgradeMessage: '' }
      }
    },
    {
      id: 'feature-3',
      name: 'send-messages',
      displayName: 'Send Messages',
      description: 'Initiate conversations with other users',
      category: 'COMMUNICATION',
      isActive: true,
      accessRules: {
        basic: { isEnabled: true, limitType: 'daily', limitValue: 3, upgradeMessage: 'Upgrade to send more messages' },
        verified: { isEnabled: true, limitType: 'daily', limitValue: 10, upgradeMessage: 'Upgrade to send unlimited messages' },
        premium: { isEnabled: true, limitType: 'none', limitValue: 0, upgradeMessage: '' }
      }
    },
    {
      id: 'feature-4',
      name: 'view-photos',
      displayName: 'View Full Photos',
      description: 'View full-size photos of other users',
      category: 'PROFILE',
      isActive: true,
      accessRules: {
        basic: { isEnabled: true, limitType: 'daily', limitValue: 5, upgradeMessage: 'Upgrade to view more photos' },
        verified: { isEnabled: true, limitType: 'none', limitValue: 0, upgradeMessage: '' },
        premium: { isEnabled: true, limitType: 'none', limitValue: 0, upgradeMessage: '' }
      }
    },
    {
      id: 'feature-5',
      name: 'priority-matching',
      displayName: 'Priority Matching',
      description: 'Get priority in matching algorithm and appear higher in search results',
      category: 'MATCHING',
      isActive: true,
      accessRules: {
        basic: { isEnabled: false, limitType: 'none', limitValue: 0, upgradeMessage: 'Upgrade for priority matching' },
        verified: { isEnabled: false, limitType: 'none', limitValue: 0, upgradeMessage: 'Upgrade to premium for priority matching' },
        premium: { isEnabled: true, limitType: 'none', limitValue: 0, upgradeMessage: '' }
      }
    },
    {
      id: 'feature-6',
      name: 'profile-visibility',
      displayName: 'Enhanced Profile Visibility',
      description: 'Make your profile more visible to potential matches',
      category: 'VISIBILITY',
      isActive: true,
      accessRules: {
        basic: { isEnabled: false, limitType: 'none', limitValue: 0, upgradeMessage: 'Upgrade for enhanced visibility' },
        verified: { isEnabled: true, limitType: 'none', limitValue: 0, upgradeMessage: '' },
        premium: { isEnabled: true, limitType: 'none', limitValue: 0, upgradeMessage: '' }
      }
    }
  ];
}

// POST /api/admin/features
async function createFeature(req, res) {
  try {
    // Get feature data from request body
    const featureData = req.body;

    // Validate required fields
    if (!featureData.name || !featureData.displayName || !featureData.category) {
      return res.status(400).json({
        success: false,
        message: 'Name, display name, and category are required'
      });
    }

    try {
      // Send the create request to the backend API
      const response = await axios.post(`${BACKEND_API_URL}/admin/features`, featureData);

      // Return the response from the backend
      return res.status(201).json(response.data);
    } catch (apiError) {
      // Log the error
      console.error('Error creating feature via backend API:', apiError.message);

      // Return a meaningful error message
      return res.status(500).json({
        success: false,
        message: 'Failed to create feature via backend API.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available',
        note: "Please ensure the backend server is running and accessible."
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Create feature');
  }
}

// PUT /api/admin/features
async function updateFeature(req, res) {
  try {
    // Get feature data from request body
    const featureData = req.body;

    // Validate required fields
    if (!featureData.id) {
      return res.status(400).json({
        success: false,
        message: 'Feature ID is required for update'
      });
    }

    if (!featureData.name || !featureData.displayName || !featureData.category) {
      return res.status(400).json({
        success: false,
        message: 'Name, display name, and category are required'
      });
    }

    try {
      // Send the update request to the backend API
      const response = await axios.put(`${BACKEND_API_URL}/admin/features`, featureData);

      // Return the response from the backend
      return res.status(200).json(response.data);
    } catch (apiError) {
      // Log the error
      console.error('Error updating feature via backend API:', apiError.message);

      // Return a meaningful error message
      return res.status(500).json({
        success: false,
        message: 'Failed to update feature via backend API.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available',
        note: "Please ensure the backend server is running and accessible."
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Update feature');
  }
}
