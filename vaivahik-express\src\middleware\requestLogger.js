/**
 * Request Logger Middleware
 * 
 * This middleware logs all incoming HTTP requests.
 * It provides detailed information about each request for debugging and monitoring.
 */

const logger = require('../utils/logger');

/**
 * Log HTTP request details
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 */
const requestLogger = (req, res, next) => {
  // Get request start time
  const start = Date.now();
  
  // Log request details
  logger.http(`${req.method} ${req.originalUrl}`, {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    body: shouldLogBody(req) ? sanitizeBody(req.body) : undefined,
    query: req.query,
    params: req.params
  });
  
  // Log response details when the response is finished
  res.on('finish', () => {
    const duration = Date.now() - start;
    const level = res.statusCode >= 400 ? 'warn' : 'http';
    
    logger[level](`${req.method} ${req.originalUrl} ${res.statusCode} - ${duration}ms`, {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration: `${duration}ms`
    });
  });
  
  next();
};

/**
 * Determine if request body should be logged
 * @param {object} req - Express request object
 * @returns {boolean} - Whether to log the body
 */
const shouldLogBody = (req) => {
  // Don't log body for GET, HEAD, or OPTIONS requests
  if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return false;
  }
  
  // Don't log body for file uploads
  const contentType = req.get('Content-Type') || '';
  if (contentType.includes('multipart/form-data')) {
    return false;
  }
  
  // Don't log body for authentication requests to avoid logging credentials
  if (req.originalUrl.includes('/auth/login') || 
      req.originalUrl.includes('/auth/register') ||
      req.originalUrl.includes('/auth/refresh-token')) {
    return false;
  }
  
  return true;
};

/**
 * Sanitize request body to remove sensitive information
 * @param {object} body - Request body
 * @returns {object} - Sanitized body
 */
const sanitizeBody = (body) => {
  if (!body || typeof body !== 'object') {
    return body;
  }
  
  // Create a copy of the body
  const sanitized = { ...body };
  
  // List of sensitive fields to redact
  const sensitiveFields = [
    'password',
    'newPassword',
    'currentPassword',
    'confirmPassword',
    'token',
    'refreshToken',
    'accessToken',
    'apiKey',
    'secret',
    'otp',
    'pin',
    'cvv',
    'cardNumber',
    'ssn',
    'socialSecurityNumber'
  ];
  
  // Redact sensitive fields
  sensitiveFields.forEach(field => {
    if (sanitized[field] !== undefined) {
      sanitized[field] = '[REDACTED]';
    }
  });
  
  return sanitized;
};

module.exports = requestLogger;
