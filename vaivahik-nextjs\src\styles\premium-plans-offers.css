/* Premium Plans Offer System Styles */

/* Offer Badge */
.plan-badge.offer {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #ff5722;
  color: white;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Price Display with Discount */
.plan-price-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 10px 0;
}

.plan-original-price {
  text-decoration: line-through;
  color: #999;
  font-size: 1rem;
  margin-bottom: 5px;
}

.plan-price {
  font-size: 1.8rem;
  font-weight: 700;
  color: #5e35b1;
}

.plan-savings {
  background-color: #4caf50;
  color: white;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  margin-top: 5px;
}

/* Discount Fields in Form */
#discountFields {
  background-color: #f9f9ff;
  border: 1px solid #e0e0ff;
  border-radius: 8px;
  padding: 15px;
  margin: 10px 0;
  position: relative;
}

#discountFields::before {
  content: 'Offer Settings';
  position: absolute;
  top: -10px;
  left: 10px;
  background-color: white;
  padding: 0 10px;
  font-size: 0.8rem;
  color: #5e35b1;
}

.form-check {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.form-check input[type="checkbox"] {
  margin-right: 10px;
}

.form-hint {
  font-size: 0.8rem;
  color: #666;
  margin-top: 5px;
}

/* Dark Mode Support */
body.dark-mode .plan-original-price {
  color: #777;
}

body.dark-mode #discountFields {
  background-color: #2a2a3a;
  border-color: #3a3a4a;
}

body.dark-mode #discountFields::before {
  background-color: #1a1a2a;
  color: #a387e9;
}

body.dark-mode .form-hint {
  color: #aaa;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .plan-badge.offer {
    font-size: 0.7rem;
    padding: 3px 8px;
  }
  
  .plan-price-container {
    margin: 5px 0;
  }
  
  .plan-original-price {
    font-size: 0.9rem;
  }
  
  .plan-price {
    font-size: 1.5rem;
  }
  
  .plan-savings {
    font-size: 0.7rem;
    padding: 2px 6px;
  }
}
