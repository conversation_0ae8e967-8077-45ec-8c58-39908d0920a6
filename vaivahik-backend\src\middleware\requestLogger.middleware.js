/**
 * Request Logger Middleware
 * 
 * This middleware logs all incoming requests and their responses.
 * It provides detailed information for debugging and monitoring.
 */

const logger = require('../utils/logger');

/**
 * Sanitize request body to remove sensitive information
 * @param {object} body - Request body
 * @returns {object} - Sanitized body
 */
const sanitizeBody = (body) => {
  if (!body || typeof body !== 'object') {
    return body;
  }
  
  // Create a copy of the body
  const sanitized = { ...body };
  
  // List of sensitive fields to redact
  const sensitiveFields = [
    'password',
    'newPassword',
    'currentPassword',
    'confirmPassword',
    'token',
    'refreshToken',
    'accessToken',
    'apiKey',
    'secret',
    'otp',
    'pin',
    'cvv',
    'cardNumber',
    'ssn',
    'socialSecurityNumber'
  ];
  
  // Redact sensitive fields
  sensitiveFields.forEach(field => {
    if (sanitized[field] !== undefined) {
      sanitized[field] = '[REDACTED]';
    }
  });
  
  return sanitized;
};

/**
 * Request logger middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const requestLogger = (req, res, next) => {
  // Skip logging for health check endpoints
  if (req.path === '/health' || req.path === '/api/health') {
    return next();
  }

  // Get request start time
  const startTime = Date.now();
  
  // Generate request ID
  const requestId = Math.random().toString(36).substring(2, 15);
  
  // Attach request ID to request object
  req.requestId = requestId;
  
  // Log request details
  logger.info(`[${requestId}] ${req.method} ${req.originalUrl} - Request received`);
  
  // Log request body if present (sanitized)
  if (req.body && Object.keys(req.body).length > 0) {
    logger.debug(`[${requestId}] Request Body: ${JSON.stringify(sanitizeBody(req.body))}`);
  }
  
  // Log request query if present
  if (req.query && Object.keys(req.query).length > 0) {
    logger.debug(`[${requestId}] Request Query: ${JSON.stringify(req.query)}`);
  }
  
  // Log request params if present
  if (req.params && Object.keys(req.params).length > 0) {
    logger.debug(`[${requestId}] Request Params: ${JSON.stringify(req.params)}`);
  }
  
  // Capture response
  const originalSend = res.send;
  res.send = function(body) {
    // Calculate response time
    const responseTime = Date.now() - startTime;
    
    // Log response details
    logger.info(`[${requestId}] ${req.method} ${req.originalUrl} - ${res.statusCode} - ${responseTime}ms`);
    
    // Log response body for non-binary responses
    if (typeof body === 'string' && body.length < 1000) {
      try {
        // Try to parse as JSON
        const parsedBody = JSON.parse(body);
        logger.debug(`[${requestId}] Response Body: ${JSON.stringify(sanitizeBody(parsedBody))}`);
      } catch (e) {
        // Not JSON, log as is if not too large
        if (body.length < 200) {
          logger.debug(`[${requestId}] Response Body: ${body}`);
        } else {
          logger.debug(`[${requestId}] Response Body: [Large response - ${body.length} bytes]`);
        }
      }
    } else if (body && typeof body === 'object') {
      logger.debug(`[${requestId}] Response Body: ${JSON.stringify(sanitizeBody(body))}`);
    }
    
    // Call original send
    originalSend.call(this, body);
  };
  
  next();
};

module.exports = requestLogger;
