import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Card,
  CardContent,
  Grid,
  Avatar,
  Button,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Paper,
  Divider,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  DatePicker
} from '@mui/material';
import {
  History as HistoryIcon,
  Visibility as ViewIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as DislikeIcon,
  Star as SuperLikeIcon,
  Message as MessageIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon,
  FilterList as FilterIcon,
  Download as ExportIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import { format, subDays } from 'date-fns';

const INTERACTION_TYPES = {
  'PROFILE_VIEW': { label: 'Profile View', icon: ViewIcon, color: 'info' },
  'PROFILE_LIKE': { label: 'Liked', icon: FavoriteIcon, color: 'success' },
  'PROFILE_DISLIKE': { label: 'Disliked', icon: DislikeIcon, color: 'error' },
  'PROFILE_SUPER_LIKE': { label: 'Super Liked', icon: SuperLikeIcon, color: 'warning' },
  'INTEREST_SENT': { label: 'Interest Sent', icon: FavoriteIcon, color: 'primary' },
  'INTEREST_RECEIVED': { label: 'Interest Received', icon: FavoriteIcon, color: 'secondary' },
  'SHORTLISTED': { label: 'Shortlisted', icon: FavoriteIcon, color: 'info' },
  'CONTACT_REQUESTED': { label: 'Contact Requested', icon: MessageIcon, color: 'warning' }
};

export default function InteractionHistoryPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [interactions, setInteractions] = useState({
    all: [],
    views: [],
    likes: [],
    interests: [],
    contacts: []
  });
  const [filteredInteractions, setFilteredInteractions] = useState([]);
  const [filters, setFilters] = useState({
    type: '',
    dateRange: '7d',
    startDate: null,
    endDate: null
  });
  const [exportDialog, setExportDialog] = useState(false);
  const [totalStats, setTotalStats] = useState({});

  useEffect(() => {
    fetchInteractionHistory();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [interactions, filters, activeTab]);

  const fetchInteractionHistory = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/user/interaction-history', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setInteractions(data.data || {});
        setTotalStats(data.stats || {});
      }
    } catch (error) {
      console.error('Error fetching interaction history:', error);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let dataToFilter = [];
    
    switch (activeTab) {
      case 0: dataToFilter = interactions.all || []; break;
      case 1: dataToFilter = interactions.views || []; break;
      case 2: dataToFilter = interactions.likes || []; break;
      case 3: dataToFilter = interactions.interests || []; break;
      case 4: dataToFilter = interactions.contacts || []; break;
      default: dataToFilter = interactions.all || [];
    }

    let filtered = [...dataToFilter];

    // Filter by type
    if (filters.type) {
      filtered = filtered.filter(item => item.interactionType === filters.type);
    }

    // Filter by date range
    if (filters.dateRange && filters.dateRange !== 'custom') {
      const days = parseInt(filters.dateRange.replace('d', ''));
      const cutoffDate = subDays(new Date(), days);
      filtered = filtered.filter(item => new Date(item.timestamp) >= cutoffDate);
    }

    // Filter by custom date range
    if (filters.dateRange === 'custom' && filters.startDate && filters.endDate) {
      filtered = filtered.filter(item => {
        const itemDate = new Date(item.timestamp);
        return itemDate >= filters.startDate && itemDate <= filters.endDate;
      });
    }

    setFilteredInteractions(filtered);
  };

  const handleExportData = async () => {
    try {
      const response = await fetch('/api/user/interaction-history/export', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          filters: filters,
          format: 'csv'
        })
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `interaction-history-${format(new Date(), 'yyyy-MM-dd')}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        setExportDialog(false);
      }
    } catch (error) {
      console.error('Error exporting data:', error);
    }
  };

  const clearFilters = () => {
    setFilters({
      type: '',
      dateRange: '7d',
      startDate: null,
      endDate: null
    });
  };

  const InteractionCard = ({ interaction }) => {
    const typeConfig = INTERACTION_TYPES[interaction.interactionType] || {
      label: interaction.interactionType,
      icon: HistoryIcon,
      color: 'default'
    };

    return (
      <Card sx={{ mb: 2, '&:hover': { boxShadow: 4 } }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={1}>
              <Avatar sx={{ bgcolor: `${typeConfig.color}.main` }}>
                <typeConfig.icon />
              </Avatar>
            </Grid>
            
            <Grid item xs={12} sm={3}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Avatar
                  src={interaction.targetUser?.profilePicture}
                  sx={{ width: 40, height: 40, mr: 1 }}
                >
                  <PersonIcon />
                </Avatar>
                <Box>
                  <Typography variant="subtitle2">
                    {interaction.targetUser?.firstName} {interaction.targetUser?.lastName}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {interaction.targetUser?.age} years • {interaction.targetUser?.location}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} sm={3}>
              <Chip
                label={typeConfig.label}
                color={typeConfig.color}
                size="small"
                icon={<typeConfig.icon />}
                sx={{ mb: 1 }}
              />
              <Typography variant="caption" display="block" color="text.secondary">
                <ScheduleIcon sx={{ fontSize: 12, mr: 0.5 }} />
                {format(new Date(interaction.timestamp), 'MMM dd, yyyy HH:mm')}
              </Typography>
            </Grid>

            <Grid item xs={12} sm={3}>
              {interaction.duration && (
                <Typography variant="caption" color="text.secondary">
                  Duration: {Math.round(interaction.duration / 1000)}s
                </Typography>
              )}
              {interaction.metadata && (
                <Typography variant="caption" display="block" color="text.secondary">
                  {JSON.parse(interaction.metadata).deviceType && 
                    `Device: ${JSON.parse(interaction.metadata).deviceType}`}
                </Typography>
              )}
            </Grid>

            <Grid item xs={12} sm={2}>
              <Button
                size="small"
                variant="outlined"
                startIcon={<ViewIcon />}
                onClick={() => router.push(`/profile/${interaction.targetUserId}`)}
                fullWidth
              >
                View Profile
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
        <CircularProgress />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading your interaction history...
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        <HistoryIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
        Interaction History
      </Typography>

      {/* Stats Overview */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={6} sm={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" color="primary">
              {totalStats.totalViews || 0}
            </Typography>
            <Typography variant="caption">Profile Views</Typography>
          </Paper>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" color="success.main">
              {totalStats.totalLikes || 0}
            </Typography>
            <Typography variant="caption">Likes Given</Typography>
          </Paper>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" color="warning.main">
              {totalStats.totalInterests || 0}
            </Typography>
            <Typography variant="caption">Interests Sent</Typography>
          </Paper>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" color="info.main">
              {totalStats.totalContacts || 0}
            </Typography>
            <Typography variant="caption">Contacts Requested</Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          <FilterIcon sx={{ mr: 1 }} />
          Filters
        </Typography>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Interaction Type</InputLabel>
              <Select
                value={filters.type}
                onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}
                label="Interaction Type"
              >
                <MenuItem value="">All Types</MenuItem>
                {Object.entries(INTERACTION_TYPES).map(([key, config]) => (
                  <MenuItem key={key} value={key}>{config.label}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Date Range</InputLabel>
              <Select
                value={filters.dateRange}
                onChange={(e) => setFilters(prev => ({ ...prev, dateRange: e.target.value }))}
                label="Date Range"
              >
                <MenuItem value="7d">Last 7 days</MenuItem>
                <MenuItem value="30d">Last 30 days</MenuItem>
                <MenuItem value="90d">Last 90 days</MenuItem>
                <MenuItem value="custom">Custom Range</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={2}>
            <Button
              variant="outlined"
              startIcon={<ClearIcon />}
              onClick={clearFilters}
              fullWidth
            >
              Clear
            </Button>
          </Grid>
          <Grid item xs={12} sm={2}>
            <Button
              variant="contained"
              startIcon={<ExportIcon />}
              onClick={() => setExportDialog(true)}
              fullWidth
            >
              Export
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label={`All (${interactions.all?.length || 0})`} />
          <Tab label={`Views (${interactions.views?.length || 0})`} />
          <Tab label={`Likes (${interactions.likes?.length || 0})`} />
          <Tab label={`Interests (${interactions.interests?.length || 0})`} />
          <Tab label={`Contacts (${interactions.contacts?.length || 0})`} />
        </Tabs>
      </Paper>

      {/* Interaction List */}
      <Box>
        {filteredInteractions.length > 0 ? (
          filteredInteractions.map((interaction) => (
            <InteractionCard
              key={interaction.id}
              interaction={interaction}
            />
          ))
        ) : (
          <Alert severity="info">
            No interactions found for the selected filters.
          </Alert>
        )}
      </Box>

      {/* Export Dialog */}
      <Dialog
        open={exportDialog}
        onClose={() => setExportDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Export Interaction History</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Export your interaction history data in CSV format. This includes all interactions based on your current filters.
          </Typography>
          <Alert severity="info" sx={{ mt: 2 }}>
            The exported file will contain: timestamps, interaction types, target user IDs, and metadata.
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setExportDialog(false)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleExportData}
          >
            Export CSV
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
