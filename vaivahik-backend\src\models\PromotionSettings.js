// src/models/PromotionSettings.js

/**
 * PromotionSettings model for storing promotional configuration parameters
 * This model will be used to configure various promotional aspects of the system
 */

const PromotionSettings = {
  // Active promotions
  activePromotions: {
    // Free chat promotion
    freeChat: {
      isActive: false,
      startDate: null, // Will be set when activated
      endDate: null,   // Will be set when activated
      durationDays: 7, // Default duration in days

      // Feature overrides during promotion
      featureOverrides: {
        // BASIC tier overrides
        BASIC: {
          dailyMessageLimit: 20, // Increased from 10
          canStartNewConversations: true, // Normally false
          canSendImages: false, // Keep as is
          messageRetentionDays: 30 // Keep as is
        },
        // VERIFIED tier overrides
        VERIFIED: {
          dailyMessageLimit: 50, // Increased from 30
          canStartNewConversations: true, // Keep as is
          canSendImages: true, // Keep as is
          messageRetentionDays: 60 // Keep as is
        }
        // PREMIUM tier is not affected by promotions
      },

      // Moderation overrides during promotion
      moderationOverrides: {
        BASIC: {
          strictness: 'medium', // Relaxed from 'high'
          autoReject: false, // Allow messages with some flags
          maskProfanity: true
        },
        VERIFIED: {
          strictness: 'low', // Relaxed from 'medium'
          autoReject: false,
          allowContactInfo: true, // Allow sharing contact info during promotion
          allowedContactTypes: ['email'] // But only email addresses
        }
      },

      // Promotion display settings
      display: {
        title: "Free Chat Week!",
        description: "Enjoy unlimited messaging for a limited time!",
        bannerColor: "#FF5722",
        bannerTextColor: "#FFFFFF",
        showCountdown: true,
        showOnHomepage: true,
        showInApp: true
      }
    },

    // New user promotion
    newUserBoost: {
      isActive: false,
      // This promotion applies to users who registered within the last X days
      eligibilityDays: 7,

      // Feature overrides during promotion
      featureOverrides: {
        BASIC: {
          dailyMessageLimit: 30, // Higher than regular promotion
          canStartNewConversations: true,
          canSendImages: true, // Allow image sharing for new users
          messageRetentionDays: 60 // Extended retention
        },
        VERIFIED: {
          dailyMessageLimit: 60,
          canStartNewConversations: true,
          canSendImages: true,
          messageRetentionDays: 90
        }
      },

      // Moderation overrides for new users
      moderationOverrides: {
        BASIC: {
          strictness: 'low', // Very relaxed for new users
          autoReject: false,
          maskProfanity: true,
          allowContactInfo: true,
          allowedContactTypes: ['email'] // Allow email sharing for new users
        },
        VERIFIED: {
          strictness: 'low',
          autoReject: false,
          allowContactInfo: true,
          allowedContactTypes: ['email', 'phone'] // Allow more contact info for verified new users
        }
      },

      // Promotion display settings
      display: {
        title: "Welcome Bonus!",
        description: "As a new user, enjoy enhanced messaging features!",
        bannerColor: "#4CAF50",
        bannerTextColor: "#FFFFFF",
        showCountdown: false,
        showOnHomepage: true,
        showInApp: true
      }
    },

    // Festival promotion (template for seasonal promotions)
    festivalPromotion: {
      isActive: false,
      startDate: null,
      endDate: null,
      durationDays: 3,

      // Feature overrides during promotion
      featureOverrides: {
        BASIC: {
          dailyMessageLimit: 25,
          canStartNewConversations: true,
          canSendImages: true,
          messageRetentionDays: 45
        },
        VERIFIED: {
          dailyMessageLimit: 75,
          canStartNewConversations: true,
          canSendImages: true,
          messageRetentionDays: 90
        }
      },

      // Moderation overrides for festival promotion
      moderationOverrides: {
        BASIC: {
          strictness: 'medium',
          autoReject: false,
          maskProfanity: true
        },
        VERIFIED: {
          strictness: 'low',
          autoReject: false,
          maskProfanity: false // No masking for verified users during festivals
        }
      },

      // Promotion display settings
      display: {
        title: "Festival Special!",
        description: "Celebrate with enhanced messaging features!",
        bannerColor: "#FFC107",
        bannerTextColor: "#000000",
        showCountdown: true,
        showOnHomepage: true,
        showInApp: true
      }
    }
  },

  // Helper functions for promotions
  isPromotionActive: function(promotionName) {
    const promotion = this.activePromotions[promotionName];
    if (!promotion || !promotion.isActive) return false;

    // If no dates are set, just check isActive flag
    if (!promotion.startDate || !promotion.endDate) return promotion.isActive;

    // Check if current date is within promotion period
    const now = new Date();
    return now >= promotion.startDate && now <= promotion.endDate;
  },

  getActivePromotions: function() {
    const activePromos = {};

    for (const [name, promotion] of Object.entries(this.activePromotions)) {
      if (this.isPromotionActive(name)) {
        activePromos[name] = promotion;
      }
    }

    return activePromos;
  },

  // Get tier settings with active promotion overrides applied
  getTierSettingsWithPromotions: function(userTier, userCreatedAt) {
    // Start with base settings from ChatSettings
    const ChatSettings = require('./ChatSettings');
    let tierSettings = { ...ChatSettings.tierLimits[userTier] };

    // Don't apply promotions to PREMIUM tier
    if (userTier === 'PREMIUM') return tierSettings;

    // Apply active promotions
    for (const [name, promotion] of Object.entries(this.activePromotions)) {
      if (!this.isPromotionActive(name)) continue;

      // Special handling for new user promotion
      if (name === 'newUserBoost') {
        if (!userCreatedAt) continue;

        const daysSinceRegistration = Math.floor((new Date() - new Date(userCreatedAt)) / (1000 * 60 * 60 * 24));
        if (daysSinceRegistration > promotion.eligibilityDays) continue;
      }

      // Apply promotion overrides if they exist for this tier
      if (promotion.featureOverrides && promotion.featureOverrides[userTier]) {
        const overrides = promotion.featureOverrides[userTier];

        // Apply each override, taking the more generous value
        for (const [key, value] of Object.entries(overrides)) {
          // For numeric limits, take the higher value (or null for unlimited)
          if (typeof value === 'number' && tierSettings[key] !== null) {
            tierSettings[key] = tierSettings[key] === null ? null : Math.max(tierSettings[key], value);
          }
          // For boolean flags, take the more permissive value
          else if (typeof value === 'boolean') {
            tierSettings[key] = tierSettings[key] || value;
          }
          // For other types, just override
          else {
            tierSettings[key] = value;
          }
        }
      }
    }

    return tierSettings;
  },

  // Activate a promotion
  activatePromotion: function(promotionName, durationDays) {
    const promotion = this.activePromotions[promotionName];
    if (!promotion) return false;

    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + (durationDays || promotion.durationDays || 7));

    promotion.isActive = true;
    promotion.startDate = startDate;
    promotion.endDate = endDate;

    return true;
  },

  // Deactivate a promotion
  deactivatePromotion: function(promotionName) {
    const promotion = this.activePromotions[promotionName];
    if (!promotion) return false;

    promotion.isActive = false;

    return true;
  }
};

module.exports = PromotionSettings;
