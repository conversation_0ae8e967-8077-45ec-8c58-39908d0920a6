{"success": true, "templates": [{"id": 1, "name": "Traditional Maratha", "description": "A traditional template with classic Maratha design elements", "previewImage": "/img/biodata-templates/traditional-maratha-preview.jpg", "gender": "NEUTRAL", "isActive": true, "isDefault": true, "createdAt": "2023-01-15T10:30:00Z", "updatedAt": "2023-06-10T15:45:00Z", "sections": [{"id": 1, "name": "header", "displayName": "Header", "content": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Biodata"}, "style": {"fontFamily": "Marathi", "fontSize": "24px", "color": "#8A2BE2", "backgroundColor": "#FFF5E6", "borderColor": "#FFD700", "borderWidth": "2px", "borderStyle": "solid"}}, {"id": 2, "name": "personalInfo", "displayName": "Personal Information", "fields": ["name", "gender", "dateOfBirth", "birthTime", "birthPlace", "height", "weight", "bloodGroup", "complexion", "education", "occupation", "income", "address", "contactNumber", "email"], "style": {"fontFamily": "<PERSON><PERSON>", "fontSize": "16px", "color": "#333333", "backgroundColor": "#FFFFFF", "padding": "15px"}}, {"id": 3, "name": "familyInfo", "displayName": "Family Information", "fields": ["<PERSON><PERSON><PERSON>", "fatherOccupation", "<PERSON><PERSON><PERSON>", "motherOccupation", "brothers", "sisters", "familyType", "family<PERSON><PERSON>ues", "familyStatus"], "style": {"fontFamily": "<PERSON><PERSON>", "fontSize": "16px", "color": "#333333", "backgroundColor": "#F8F8FF", "padding": "15px"}}, {"id": 4, "name": "horoscope", "displayName": "Horoscope Details", "fields": ["rashi", "nakshatra", "gan", "nadi", "gotra", "manglik"], "style": {"fontFamily": "<PERSON><PERSON>", "fontSize": "16px", "color": "#333333", "backgroundColor": "#FFF0F5", "padding": "15px"}}, {"id": 5, "name": "partnerPreferences", "displayName": "Partner Preferences", "fields": ["<PERSON><PERSON><PERSON><PERSON>", "heightRange", "education", "occupation", "incomeRange", "location", "maritalStatus", "dietPreference"], "style": {"fontFamily": "<PERSON><PERSON>", "fontSize": "16px", "color": "#333333", "backgroundColor": "#F0F8FF", "padding": "15px"}}, {"id": 6, "name": "footer", "displayName": "Footer", "content": {"text": "Powered by Vaivahik - AI Powered Matrimony for Maratha Community", "contactInfo": "For any inquiries, please contact: <EMAIL>"}, "style": {"fontFamily": "<PERSON><PERSON>", "fontSize": "14px", "color": "#666666", "backgroundColor": "#F5F5F5", "padding": "10px", "textAlign": "center"}}]}, {"id": 2, "name": "Modern Elegance", "description": "A modern and elegant template with clean design", "previewImage": "/img/biodata-templates/modern-elegance-preview.jpg", "gender": "FEMALE", "isActive": true, "isDefault": false, "createdAt": "2023-02-20T11:15:00Z", "updatedAt": "2023-06-15T09:30:00Z", "sections": [{"id": 7, "name": "header", "displayName": "Header", "content": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Biodata"}, "style": {"fontFamily": "Roboto", "fontSize": "28px", "color": "#FF1493", "backgroundColor": "#FFF0F5", "borderColor": "#FF69B4", "borderWidth": "1px", "borderStyle": "solid"}}]}, {"id": 3, "name": "Professional", "description": "A professional template with formal design", "previewImage": "/img/biodata-templates/professional-preview.jpg", "gender": "MALE", "isActive": true, "isDefault": false, "createdAt": "2023-03-10T14:20:00Z", "updatedAt": "2023-06-20T16:45:00Z", "sections": [{"id": 13, "name": "header", "displayName": "Header", "content": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Biodata"}, "style": {"fontFamily": "Times New Roman", "fontSize": "26px", "color": "#000080", "backgroundColor": "#F0F8FF", "borderColor": "#4169E1", "borderWidth": "1px", "borderStyle": "solid"}}]}, {"id": 4, "name": "Royal Heritage", "description": "A royal template inspired by Maratha heritage", "previewImage": "/img/biodata-templates/royal-heritage-preview.jpg", "gender": "NEUTRAL", "isActive": true, "isDefault": false, "createdAt": "2023-04-05T09:45:00Z", "updatedAt": "2023-06-25T11:30:00Z", "sections": [{"id": 19, "name": "header", "displayName": "Header", "content": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Biodata"}, "style": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontSize": "30px", "color": "#8B4513", "backgroundColor": "#FAEBD7", "borderColor": "#D2B48C", "borderWidth": "3px", "borderStyle": "double"}}]}], "pagination": {"totalTemplates": 4, "page": 1, "limit": 10, "totalPages": 1}, "message": "Biodata templates retrieved successfully"}