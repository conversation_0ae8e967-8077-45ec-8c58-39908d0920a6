// src/controllers/admin/featureManagement.controller.js

/**
 * @description Get all features with their access rules
 * @route GET /api/admin/features
 */
exports.getAllFeatures = async (req, res, next) => {
    const prisma = req.prisma;
    
    try {
        const features = await prisma.feature.findMany({
            include: {
                accessRules: {
                    include: {
                        subscriptionPlan: {
                            select: {
                                id: true,
                                name: true,
                                planType: true
                            }
                        }
                    }
                }
            },
            orderBy: [
                { category: 'asc' },
                { displayName: 'asc' }
            ]
        });
        
        res.status(200).json({
            success: true,
            features
        });
    } catch (error) {
        console.error('Error fetching features:', error);
        next(error);
    }
};

/**
 * @description Get a specific feature by ID
 * @route GET /api/admin/features/:id
 */
exports.getFeatureById = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;
    
    try {
        const feature = await prisma.feature.findUnique({
            where: { id },
            include: {
                accessRules: {
                    include: {
                        subscriptionPlan: {
                            select: {
                                id: true,
                                name: true,
                                planType: true
                            }
                        }
                    }
                }
            }
        });
        
        if (!feature) {
            return res.status(404).json({
                success: false,
                message: 'Feature not found'
            });
        }
        
        res.status(200).json({
            success: true,
            feature
        });
    } catch (error) {
        console.error('Error fetching feature:', error);
        next(error);
    }
};

/**
 * @description Create a new feature
 * @route POST /api/admin/features
 */
exports.createFeature = async (req, res, next) => {
    const prisma = req.prisma;
    const { name, displayName, description, category, isActive } = req.body;
    
    try {
        // Check if feature with this name already exists
        const existingFeature = await prisma.feature.findUnique({
            where: { name }
        });
        
        if (existingFeature) {
            return res.status(400).json({
                success: false,
                message: 'A feature with this name already exists'
            });
        }
        
        // Create the feature
        const feature = await prisma.feature.create({
            data: {
                name,
                displayName,
                description,
                category,
                isActive: isActive !== undefined ? isActive : true
            }
        });
        
        // Create default access rules for all user tiers
        const userTiers = ['BASIC', 'VERIFIED', 'PREMIUM'];
        
        for (const tier of userTiers) {
            await prisma.featureAccess.create({
                data: {
                    featureId: feature.id,
                    userTier: tier,
                    isEnabled: tier === 'PREMIUM', // Enable by default only for premium
                    dailyLimit: tier === 'PREMIUM' ? null : (tier === 'VERIFIED' ? 10 : 5),
                    limitPeriod: tier === 'PREMIUM' ? null : 'DAILY'
                }
            });
        }
        
        // Get the feature with its access rules
        const createdFeature = await prisma.feature.findUnique({
            where: { id: feature.id },
            include: {
                accessRules: true
            }
        });
        
        res.status(201).json({
            success: true,
            feature: createdFeature
        });
    } catch (error) {
        console.error('Error creating feature:', error);
        next(error);
    }
};

/**
 * @description Update a feature
 * @route PUT /api/admin/features/:id
 */
exports.updateFeature = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;
    const { name, displayName, description, category, isActive } = req.body;
    
    try {
        // Check if feature exists
        const existingFeature = await prisma.feature.findUnique({
            where: { id }
        });
        
        if (!existingFeature) {
            return res.status(404).json({
                success: false,
                message: 'Feature not found'
            });
        }
        
        // Check if name is being changed and if it conflicts with another feature
        if (name !== existingFeature.name) {
            const nameConflict = await prisma.feature.findUnique({
                where: { name }
            });
            
            if (nameConflict) {
                return res.status(400).json({
                    success: false,
                    message: 'A feature with this name already exists'
                });
            }
        }
        
        // Update the feature
        const updatedFeature = await prisma.feature.update({
            where: { id },
            data: {
                name,
                displayName,
                description,
                category,
                isActive
            }
        });
        
        res.status(200).json({
            success: true,
            feature: updatedFeature
        });
    } catch (error) {
        console.error('Error updating feature:', error);
        next(error);
    }
};

/**
 * @description Delete a feature
 * @route DELETE /api/admin/features/:id
 */
exports.deleteFeature = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;
    
    try {
        // Check if feature exists
        const existingFeature = await prisma.feature.findUnique({
            where: { id }
        });
        
        if (!existingFeature) {
            return res.status(404).json({
                success: false,
                message: 'Feature not found'
            });
        }
        
        // Delete the feature (this will cascade delete access rules)
        await prisma.feature.delete({
            where: { id }
        });
        
        res.status(200).json({
            success: true,
            message: 'Feature deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting feature:', error);
        next(error);
    }
};

/**
 * @description Update feature access rules
 * @route PUT /api/admin/features/:id/access
 */
exports.updateFeatureAccess = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;
    const { accessRules } = req.body;
    
    try {
        // Check if feature exists
        const existingFeature = await prisma.feature.findUnique({
            where: { id }
        });
        
        if (!existingFeature) {
            return res.status(404).json({
                success: false,
                message: 'Feature not found'
            });
        }
        
        // Update or create access rules
        const updatedRules = [];
        
        for (const rule of accessRules) {
            const { id: ruleId, userTier, subscriptionPlanId, isEnabled, dailyLimit, totalLimit, limitPeriod, allowedFilters, upgradeMessage } = rule;
            
            // Prepare data for update or create
            const data = {
                userTier,
                subscriptionPlanId,
                isEnabled,
                dailyLimit,
                totalLimit,
                limitPeriod,
                allowedFilters: allowedFilters ? JSON.stringify(allowedFilters) : null,
                upgradeMessage
            };
            
            let updatedRule;
            
            if (ruleId) {
                // Update existing rule
                updatedRule = await prisma.featureAccess.update({
                    where: { id: ruleId },
                    data
                });
            } else {
                // Create new rule
                updatedRule = await prisma.featureAccess.create({
                    data: {
                        ...data,
                        featureId: id
                    }
                });
            }
            
            updatedRules.push(updatedRule);
        }
        
        // Get the updated feature with its access rules
        const updatedFeature = await prisma.feature.findUnique({
            where: { id },
            include: {
                accessRules: {
                    include: {
                        subscriptionPlan: {
                            select: {
                                id: true,
                                name: true,
                                planType: true
                            }
                        }
                    }
                }
            }
        });
        
        res.status(200).json({
            success: true,
            feature: updatedFeature
        });
    } catch (error) {
        console.error('Error updating feature access:', error);
        next(error);
    }
};

/**
 * @description Get all subscription plans
 * @route GET /api/admin/subscription-plans
 */
exports.getAllSubscriptionPlans = async (req, res, next) => {
    const prisma = req.prisma;
    
    try {
        const plans = await prisma.subscriptionPlan.findMany({
            orderBy: { price: 'asc' }
        });
        
        res.status(200).json({
            success: true,
            plans
        });
    } catch (error) {
        console.error('Error fetching subscription plans:', error);
        next(error);
    }
};

/**
 * @description Create a new subscription plan
 * @route POST /api/admin/subscription-plans
 */
exports.createSubscriptionPlan = async (req, res, next) => {
    const prisma = req.prisma;
    const { name, planType, price, currency, duration, description, features, isActive } = req.body;
    
    try {
        // Create the subscription plan
        const plan = await prisma.subscriptionPlan.create({
            data: {
                name,
                planType,
                price,
                currency: currency || 'INR',
                duration,
                description,
                features: features ? JSON.stringify(features) : '[]',
                isActive: isActive !== undefined ? isActive : true
            }
        });
        
        res.status(201).json({
            success: true,
            plan
        });
    } catch (error) {
        console.error('Error creating subscription plan:', error);
        next(error);
    }
};

/**
 * @description Update a subscription plan
 * @route PUT /api/admin/subscription-plans/:id
 */
exports.updateSubscriptionPlan = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;
    const { name, planType, price, currency, duration, description, features, isActive } = req.body;
    
    try {
        // Check if plan exists
        const existingPlan = await prisma.subscriptionPlan.findUnique({
            where: { id }
        });
        
        if (!existingPlan) {
            return res.status(404).json({
                success: false,
                message: 'Subscription plan not found'
            });
        }
        
        // Update the plan
        const updatedPlan = await prisma.subscriptionPlan.update({
            where: { id },
            data: {
                name,
                planType,
                price,
                currency,
                duration,
                description,
                features: features ? JSON.stringify(features) : existingPlan.features,
                isActive
            }
        });
        
        res.status(200).json({
            success: true,
            plan: updatedPlan
        });
    } catch (error) {
        console.error('Error updating subscription plan:', error);
        next(error);
    }
};

/**
 * @description Delete a subscription plan
 * @route DELETE /api/admin/subscription-plans/:id
 */
exports.deleteSubscriptionPlan = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;
    
    try {
        // Check if plan exists
        const existingPlan = await prisma.subscriptionPlan.findUnique({
            where: { id }
        });
        
        if (!existingPlan) {
            return res.status(404).json({
                success: false,
                message: 'Subscription plan not found'
            });
        }
        
        // Check if plan is in use
        const activeSubscriptions = await prisma.subscription.count({
            where: {
                planId: id,
                isActive: true
            }
        });
        
        if (activeSubscriptions > 0) {
            return res.status(400).json({
                success: false,
                message: `Cannot delete plan with ${activeSubscriptions} active subscriptions`
            });
        }
        
        // Delete the plan
        await prisma.subscriptionPlan.delete({
            where: { id }
        });
        
        res.status(200).json({
            success: true,
            message: 'Subscription plan deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting subscription plan:', error);
        next(error);
    }
};
