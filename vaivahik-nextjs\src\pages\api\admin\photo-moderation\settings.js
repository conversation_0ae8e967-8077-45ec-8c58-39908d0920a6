// src/pages/api/admin/photo-moderation/settings.js
import axios from 'axios';

// Mock photo moderation settings
const mockSettings = {
  autoModeration: {
    enabled: true,
    confidence_threshold: 0.8,
    auto_approve_threshold: 0.9,
    auto_reject_threshold: 0.3
  },
  aiModels: {
    nudity_detection: true,
    face_detection: true,
    inappropriate_content: true,
    quality_check: true
  },
  manualReview: {
    required_for_low_confidence: true,
    queue_limit: 100,
    reviewer_assignment: 'round_robin'
  },
  notifications: {
    email_on_rejection: true,
    email_on_approval: false,
    admin_alerts: true
  }
};

export default async function handler(req, res) {
  // In development mode, skip authentication and return mock data
  if (process.env.NODE_ENV === 'development') {
    if (req.method === 'GET') {
      return res.status(200).json({
        success: true,
        settings: mockSettings,
        message: 'Photo moderation settings retrieved successfully (mock data)'
      });
    } else if (req.method === 'PUT') {
      return res.status(200).json({
        success: true,
        settings: { ...mockSettings, ...req.body.settings },
        message: 'Photo moderation settings updated successfully (mock data)'
      });
    }
  }

  // Get the auth token from the request cookies or headers
  const token = req.cookies?.adminToken || req.headers.authorization?.split(' ')[1];

  if (!token) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  try {
    // Forward the request to the backend API
    const response = await axios({
      method: req.method,
      url: `${process.env.BACKEND_API_URL}/admin/photo-moderation/settings`,
      data: req.body,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    // Return the response from the backend
    return res.status(response.status).json(response.data);
  } catch (error) {
    console.error('Error proxying request to backend:', error);

    // Return mock data as fallback
    if (req.method === 'GET') {
      return res.status(200).json({
        success: true,
        settings: mockSettings,
        message: 'Photo moderation settings retrieved successfully (fallback)'
      });
    } else if (req.method === 'PUT') {
      return res.status(200).json({
        success: true,
        settings: { ...mockSettings, ...req.body.settings },
        message: 'Photo moderation settings updated successfully (fallback)'
      });
    }

    // Return the error response from the backend if available
    if (error.response) {
      return res.status(error.response.status).json(error.response.data);
    }

    // Otherwise return a generic error
    return res.status(500).json({
      success: false,
      message: 'Error connecting to backend service'
    });
  }
}
