// src/socket/chatHandler.js

/**
 * Socket.IO handler for chat functionality
 * This module handles real-time chat events using Socket.IO
 */

const jwt = require('jsonwebtoken');
const ChatSettings = require('../models/ChatSettings');
const textModerationService = require('../services/textModeration.service');

// Map to store active user connections
const activeUsers = new Map(); // userId -> socketId
const userSockets = new Map(); // socketId -> userId

/**
 * Initialize chat socket handlers
 * @param {Object} io - Socket.IO instance
 * @param {Object} prisma - Prisma client instance
 */
function initializeChatHandlers(io, prisma) {
    // Middleware to authenticate socket connections
    io.use(async (socket, next) => {
        try {
            const token = socket.handshake.auth.token;

            if (!token) {
                return next(new Error('Authentication error: Token missing'));
            }

            // Verify JWT token
            const decoded = jwt.verify(token, process.env.JWT_SECRET);

            if (!decoded || !decoded.userId) {
                return next(new Error('Authentication error: Invalid token'));
            }

            // Attach user ID to socket
            socket.userId = decoded.userId;

            // Check if user exists
            const user = await prisma.user.findUnique({
                where: { id: decoded.userId },
                select: { id: true, isVerified: true, isPremium: true }
            });

            if (!user) {
                return next(new Error('Authentication error: User not found'));
            }

            // Attach user info to socket
            socket.user = user;

            next();
        } catch (error) {
            console.error('Socket authentication error:', error);
            next(new Error('Authentication error: ' + (error.message || 'Unknown error')));
        }
    });

    // Handle socket connections
    io.on('connection', (socket) => {
        console.log(`User connected: ${socket.userId} (Socket ID: ${socket.id})`);

        // Store user connection
        activeUsers.set(socket.userId, socket.id);
        userSockets.set(socket.id, socket.userId);

        // Emit user online status to relevant users
        emitUserStatus(io, prisma, socket.userId, true);

        // Join user to their personal room for direct messages
        socket.join(`user:${socket.userId}`);

        // Handle join conversation
        socket.on('join-conversation', async (conversationId) => {
            try {
                // Verify the user is part of this conversation
                const conversation = await prisma.conversation.findFirst({
                    where: {
                        id: conversationId,
                        OR: [
                            { user1Id: socket.userId },
                            { user2Id: socket.userId }
                        ]
                    }
                });

                if (!conversation) {
                    socket.emit('error', {
                        event: 'join-conversation',
                        message: "You don't have access to this conversation."
                    });
                    return;
                }

                // Join the conversation room
                socket.join(`conversation:${conversationId}`);
                console.log(`User ${socket.userId} joined conversation ${conversationId}`);

                // Emit typing status reset
                socket.to(`conversation:${conversationId}`).emit('typing-status', {
                    conversationId,
                    userId: socket.userId,
                    isTyping: false
                });

                // Mark messages as read
                if (ChatSettings.enableReadReceipts) {
                    const result = await prisma.message.updateMany({
                        where: {
                            conversationId,
                            receiverId: socket.userId,
                            isRead: false
                        },
                        data: {
                            isRead: true,
                            readAt: new Date()
                        }
                    });

                    if (result.count > 0) {
                        // Notify the sender that messages were read
                        const otherUserId = conversation.user1Id === socket.userId
                            ? conversation.user2Id
                            : conversation.user1Id;

                        io.to(`user:${otherUserId}`).emit('messages-read', {
                            conversationId,
                            readBy: socket.userId,
                            count: result.count
                        });
                    }
                }
            } catch (error) {
                console.error('Error joining conversation:', error);
                socket.emit('error', {
                    event: 'join-conversation',
                    message: 'Failed to join conversation.'
                });
            }
        });

        // Handle leave conversation
        socket.on('leave-conversation', (conversationId) => {
            socket.leave(`conversation:${conversationId}`);
            console.log(`User ${socket.userId} left conversation ${conversationId}`);
        });

        // Handle new message
        socket.on('send-message', async (data) => {
            try {
                const { conversationId, content, messageType = 'TEXT', metadata = null } = data;

                // Validate input
                if (!conversationId || !content || content.trim() === '') {
                    socket.emit('error', {
                        event: 'send-message',
                        message: 'Conversation ID and message content are required.'
                    });
                    return;
                }

                // Check message length
                if (content.length > ChatSettings.maxMessageLength) {
                    socket.emit('error', {
                        event: 'send-message',
                        message: `Message exceeds maximum length of ${ChatSettings.maxMessageLength} characters.`
                    });
                    return;
                }

                // Verify the user is part of this conversation
                const conversation = await prisma.conversation.findFirst({
                    where: {
                        id: conversationId,
                        OR: [
                            { user1Id: socket.userId },
                            { user2Id: socket.userId }
                        ]
                    }
                });

                if (!conversation) {
                    socket.emit('error', {
                        event: 'send-message',
                        message: "You don't have access to this conversation."
                    });
                    return;
                }

                // Determine the receiver
                const receiverId = conversation.user1Id === socket.userId
                    ? conversation.user2Id
                    : conversation.user1Id;

                // Get user's tier for moderation settings
                let userTier = 'BASIC';
                if (socket.user.isVerified) userTier = 'VERIFIED';
                if (socket.user.isPremium) userTier = 'PREMIUM';

                // Get tier-specific moderation settings with any active promotion overrides
                const moderationSettings = ChatSettings.getModerationSettingsForTier(userTier);

                // Moderate the message content
                const moderationResult = await textModerationService.moderateText(content, {
                    userId: socket.userId,
                    strictness: moderationSettings.strictness,
                    autoReject: moderationSettings.autoReject,
                    maskProfanity: moderationSettings.maskProfanity,
                    allowContactInfo: moderationSettings.allowContactInfo,
                    allowedContactTypes: moderationSettings.allowedContactTypes
                });

                // If message is rejected by moderation
                if (!moderationResult.isApproved) {
                    socket.emit('error', {
                        event: 'send-message',
                        message: 'Your message contains inappropriate content and cannot be sent.',
                        moderationFlags: moderationResult.flags
                    });
                    return;
                }

                // Use moderated text if available
                const finalContent = moderationResult.moderatedText || content;

                // Create the message with moderation info
                const message = await prisma.message.create({
                    data: {
                        senderId: socket.userId,
                        receiverId,
                        conversationId,
                        content: finalContent,
                        messageType,
                        metadata: metadata ? JSON.stringify(metadata) : null,
                        sentAt: new Date(),
                        // Add moderation fields
                        isModerated: moderationResult.flags.length > 0,
                        moderationStatus: moderationResult.isApproved ? 'APPROVED' : 'REJECTED',
                        moderatedContent: moderationResult.moderatedText,
                        moderationFlags: moderationResult.flags.join(',')
                    }
                });

                // Update the conversation's lastMessageAt
                await prisma.conversation.update({
                    where: { id: conversationId },
                    data: {
                        lastMessageAt: new Date(),
                        isActive: true
                    }
                });

                // Update user's messagesSent count
                await prisma.user.update({
                    where: { id: socket.userId },
                    data: {
                        messagesSent: {
                            increment: 1
                        }
                    }
                });

                // Create a notification for the receiver
                await prisma.notification.create({
                    data: {
                        userId: receiverId,
                        title: 'New Message',
                        message: 'You have received a new message',
                        type: 'MESSAGE_RECEIVED',
                        actionUrl: `/messages/${conversationId}`
                    }
                });

                // Emit the message to the conversation room
                io.to(`conversation:${conversationId}`).emit('new-message', {
                    ...message,
                    metadata: metadata
                });

                // Emit to the receiver's personal room if they're not in the conversation
                const receiverSocketId = activeUsers.get(receiverId);
                if (receiverSocketId) {
                    const receiverSocket = io.sockets.sockets.get(receiverSocketId);
                    if (receiverSocket && !receiverSocket.rooms.has(`conversation:${conversationId}`)) {
                        io.to(`user:${receiverId}`).emit('new-conversation-message', {
                            conversationId,
                            message: {
                                ...message,
                                metadata: metadata
                            }
                        });
                    }
                }

                // Emit success to sender
                socket.emit('message-sent', {
                    messageId: message.id,
                    conversationId,
                    timestamp: message.sentAt
                });
            } catch (error) {
                console.error('Error sending message:', error);
                socket.emit('error', {
                    event: 'send-message',
                    message: 'Failed to send message.'
                });
            }
        });

        // Handle typing status
        socket.on('typing', async (data) => {
            try {
                const { conversationId, isTyping } = data;

                // Verify the user is part of this conversation
                const conversation = await prisma.conversation.findFirst({
                    where: {
                        id: conversationId,
                        OR: [
                            { user1Id: socket.userId },
                            { user2Id: socket.userId }
                        ]
                    }
                });

                if (!conversation) {
                    return; // Silently fail
                }

                // Only emit if typing indicators are enabled
                if (ChatSettings.enableTypingIndicators) {
                    // Emit typing status to the conversation room
                    socket.to(`conversation:${conversationId}`).emit('typing-status', {
                        conversationId,
                        userId: socket.userId,
                        isTyping
                    });
                }
            } catch (error) {
                console.error('Error handling typing status:', error);
            }
        });

        // Handle read receipts
        socket.on('mark-read', async (data) => {
            try {
                const { conversationId, messageIds } = data;

                // Verify the user is part of this conversation
                const conversation = await prisma.conversation.findFirst({
                    where: {
                        id: conversationId,
                        OR: [
                            { user1Id: socket.userId },
                            { user2Id: socket.userId }
                        ]
                    }
                });

                if (!conversation) {
                    socket.emit('error', {
                        event: 'mark-read',
                        message: "You don't have access to this conversation."
                    });
                    return;
                }

                // Only process if read receipts are enabled
                if (ChatSettings.enableReadReceipts) {
                    // Build the query to mark messages as read
                    const whereClause = {
                        conversationId,
                        receiverId: socket.userId,
                        isRead: false
                    };

                    // If specific message IDs were provided, add them to the query
                    if (messageIds && messageIds.length > 0) {
                        whereClause.id = { in: messageIds };
                    }

                    // Mark messages as read
                    const result = await prisma.message.updateMany({
                        where: whereClause,
                        data: {
                            isRead: true,
                            readAt: new Date()
                        }
                    });

                    if (result.count > 0) {
                        // Determine the other user in the conversation
                        const otherUserId = conversation.user1Id === socket.userId
                            ? conversation.user2Id
                            : conversation.user1Id;

                        // Emit read receipt to the other user
                        io.to(`user:${otherUserId}`).emit('messages-read', {
                            conversationId,
                            readBy: socket.userId,
                            count: result.count,
                            messageIds
                        });

                        // Emit success to the current user
                        socket.emit('marked-read', {
                            conversationId,
                            count: result.count
                        });
                    }
                }
            } catch (error) {
                console.error('Error marking messages as read:', error);
                socket.emit('error', {
                    event: 'mark-read',
                    message: 'Failed to mark messages as read.'
                });
            }
        });

        // Handle disconnect
        socket.on('disconnect', () => {
            console.log(`User disconnected: ${socket.userId} (Socket ID: ${socket.id})`);

            // Remove user connection
            activeUsers.delete(socket.userId);
            userSockets.delete(socket.id);

            // Emit user offline status to relevant users
            emitUserStatus(io, prisma, socket.userId, false);
        });
    });
}

/**
 * Emit user online status to relevant users
 * @param {Object} io - Socket.IO instance
 * @param {Object} prisma - Prisma client instance
 * @param {string} userId - User ID
 * @param {boolean} isOnline - Whether the user is online
 */
async function emitUserStatus(io, prisma, userId, isOnline) {
    try {
        // Find all conversations the user is part of
        const conversations = await prisma.conversation.findMany({
            where: {
                OR: [
                    { user1Id: userId },
                    { user2Id: userId }
                ],
                isActive: true
            },
            select: {
                id: true,
                user1Id: true,
                user2Id: true
            }
        });

        // Emit status to all users in these conversations
        for (const conversation of conversations) {
            const otherUserId = conversation.user1Id === userId
                ? conversation.user2Id
                : conversation.user1Id;

            // Check if the other user is online
            if (activeUsers.has(otherUserId)) {
                io.to(`user:${otherUserId}`).emit('user-status', {
                    userId,
                    isOnline,
                    timestamp: new Date()
                });
            }
        }
    } catch (error) {
        console.error('Error emitting user status:', error);
    }
}

module.exports = {
    initializeChatHandlers
};
