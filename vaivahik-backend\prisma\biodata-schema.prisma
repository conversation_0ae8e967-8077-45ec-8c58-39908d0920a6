// Biodata Template Model
model BiodataTemplate {
  id                String    @id @default(cuid())
  name              String    // Template name (e.g., "Classic", "Modern", "Traditional")
  description       String?   // Template description
  previewImage      String    // URL to template preview image
  designFile        String    // Path to the template design file (HTML/CSS)
  isActive          Boolean   @default(true)
  price             Float     // Regular price
  discountedPrice   Float?    // Discounted price (if applicable)
  discountPercent   Int?      // Discount percentage
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @default(now()) @updatedAt @map("updated_at")
  
  // Relationships
  userBiodatas      UserBiodata[]
  
  @@map("biodata_templates")
}

// User Biodata Purchase Model
model UserBiodata {
  id                String    @id @default(cuid())
  userId            String    @map("user_id")
  user              User      @relation(fields: [userId], references: [id])
  templateId        String    @map("template_id")
  template          BiodataTemplate @relation(fields: [templateId], references: [id])
  purchaseDate      DateTime  @default(now()) @map("purchase_date")
  expiryDate        DateTime? @map("expiry_date") // If applicable
  pricePaid         Float     @map("price_paid")
  downloadCount     Int       @default(0) @map("download_count")
  lastDownloaded    DateTime? @map("last_downloaded")
  transactionId     String?   @map("transaction_id")
  
  @@index([userId])
  @@index([templateId])
  @@map("user_biodatas")
}
