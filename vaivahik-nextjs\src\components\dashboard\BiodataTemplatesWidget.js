import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Grid,
  Button,
  Chip,
  styled,
  GlobalStyles
} from '@mui/material';
import {
  Description as BiodataIcon,
  Visibility as PreviewIcon,
  ShoppingCart as PurchaseIcon,
  Star as StarIcon,
  Male as MaleIcon,
  Female as FemaleIcon
} from '@mui/icons-material';
import BiodataTemplatePreview from './BiodataTemplatePreview';

const TemplateCard = styled(Card)(({ theme }) => ({
  borderRadius: 20,
  overflow: 'hidden',
  cursor: 'pointer',
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
  border: '1px solid rgba(102, 126, 234, 0.1)',
  position: 'relative',
  '&:hover': {
    transform: 'translateY(-15px) scale(1.03)',
    boxShadow: '0 30px 70px rgba(102, 126, 234, 0.3)',
    '& .template-overlay': {
      opacity: 1
    },
    '& .template-image': {
      transform: 'scale(1.1)'
    },
    '& .premium-glow': {
      opacity: 1
    }
  },
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    borderRadius: '20px 20px 0 0'
  }
}));

const PremiumBadge = styled(Chip)(({ theme }) => ({
  position: 'absolute',
  top: 12,
  right: 12,
  background: 'linear-gradient(135deg, #FFD700, #FFA000)',
  color: '#000',
  fontWeight: 600,
  zIndex: 1
}));

const BiodataTemplatesWidget = ({ userId, userGender = 'male' }) => {
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [filterGender, setFilterGender] = useState('all');

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      // Premium biodata templates with stunning designs
      const mockTemplates = [
        {
          id: 1,
          name: '🏛️ Traditional Heritage',
          description: 'Timeless Maratha design with cultural elegance and traditional motifs',
          price: 399,
          originalPrice: 599,
          discount: 33,
          isPremium: false,
          targetGender: 'male',
          thumbnail: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=400&fit=crop&crop=center',
          downloads: 2850,
          rating: 4.9,
          features: ['🎨 Traditional Motifs', '📜 Heritage Elements', '👑 Royal Typography', '🌟 Premium Layout'],
          category: 'Traditional',
          designFile: '/templates/biodata/traditional-heritage.html'
        },
        {
          id: 2,
          name: '🌹 Elegant Floral',
          description: 'Sophisticated floral design with delicate patterns and feminine grace',
          price: 499,
          originalPrice: 699,
          discount: 29,
          isPremium: true,
          targetGender: 'female',
          thumbnail: 'https://images.unsplash.com/photo-1490750967868-88aa4486c946?w=300&h=400&fit=crop&crop=center',
          downloads: 1980,
          rating: 4.8,
          features: ['🌸 Floral Patterns', '💎 Elegant Typography', '📸 Photo Gallery', '✨ Luxury Finish'],
          category: 'Elegant',
          designFile: '/templates/biodata/elegant-floral.html'
        },
        {
          id: 3,
          name: '💼 Executive Premium',
          description: 'Ultra-luxury design for high-profile professionals with gold accents',
          price: 799,
          originalPrice: 1199,
          discount: 33,
          isPremium: true,
          targetGender: 'male',
          thumbnail: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=400&fit=crop&crop=center',
          downloads: 1250,
          rating: 4.9,
          features: ['🏆 Luxury Design', '💰 Gold Accents', '📊 Career Timeline', '🎯 Executive Layout'],
          category: 'Premium',
          designFile: '/templates/biodata/executive-premium.html'
        },
        {
          id: 4,
          name: '👑 Luxury Royal',
          description: 'Majestic royal design with premium gradients and stunning animations',
          price: 999,
          originalPrice: 1499,
          discount: 33,
          isPremium: true,
          targetGender: 'unisex',
          thumbnail: 'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=300&h=400&fit=crop&crop=center',
          downloads: 890,
          rating: 5.0,
          features: ['👑 Royal Design', '✨ Animations', '🎨 Premium Gradients', '💎 Luxury Elements'],
          category: 'Royal',
          designFile: '/templates/biodata/luxury-royal.html'
        },
        {
          id: 5,
          name: '🎨 Modern Artistic',
          description: 'Contemporary artistic design with creative layouts and vibrant colors',
          price: 599,
          originalPrice: 799,
          discount: 25,
          isPremium: true,
          targetGender: 'female',
          thumbnail: 'https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=300&h=400&fit=crop&crop=center',
          downloads: 1650,
          rating: 4.7,
          features: ['🎨 Artistic Layout', '🌈 Vibrant Colors', '📐 Modern Design', '🖼️ Creative Sections'],
          category: 'Modern',
          designFile: '/templates/biodata/modern-artistic.html'
        },
        {
          id: 6,
          name: '🌟 Contemporary Chic',
          description: 'Trendy and stylish design perfect for modern millennials',
          price: 449,
          originalPrice: 599,
          discount: 25,
          isPremium: false,
          targetGender: 'female',
          thumbnail: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300&h=400&fit=crop&crop=center',
          downloads: 2100,
          rating: 4.6,
          features: ['✨ Trendy Design', '📱 Modern Layout', '🎯 Millennial Style', '🌟 Chic Elements'],
          category: 'Contemporary',
          designFile: '/templates/biodata/contemporary-chic.html'
        },
        {
          id: 7,
          name: '🏢 Professional Classic',
          description: 'Clean professional design ideal for corporate professionals',
          price: 349,
          originalPrice: 499,
          discount: 30,
          isPremium: false,
          targetGender: 'unisex',
          thumbnail: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=400&fit=crop&crop=center',
          downloads: 3200,
          rating: 4.5,
          features: ['💼 Professional Look', '📋 Clean Layout', '🎯 Corporate Style', '📊 Achievement Focus'],
          category: 'Professional',
          designFile: '/templates/biodata/professional-classic.html'
        },
        {
          id: 8,
          name: '🕉️ Cultural Grace',
          description: 'Spiritual and cultural design with traditional Indian elements',
          price: 399,
          originalPrice: 549,
          discount: 27,
          isPremium: false,
          targetGender: 'unisex',
          thumbnail: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=300&h=400&fit=crop&crop=center',
          downloads: 1850,
          rating: 4.8,
          features: ['🕉️ Spiritual Elements', '🎨 Cultural Motifs', '📿 Traditional Colors', '🌺 Sacred Symbols'],
          category: 'Cultural',
          designFile: '/templates/biodata/cultural-grace.html'
        }
      ];
      
      setTemplates(mockTemplates);
    } catch (error) {
      console.error('Error fetching templates:', error);
    }
  };

  const handlePreview = (template) => {
    setSelectedTemplate(template);
    setPreviewOpen(true);
  };

  const handlePurchase = (template) => {
    console.log('Purchasing template:', template);
    // Implement purchase logic with payment gateway
  };

  const handleDownload = (template) => {
    console.log('Downloading template:', template);
    // Implement download logic
  };

  const filteredTemplates = templates.filter(template => {
    if (filterGender === 'all') return true;
    return template.targetGender === filterGender || template.targetGender === 'unisex';
  });

  const getGenderIcon = (gender) => {
    switch (gender) {
      case 'male':
        return <MaleIcon sx={{ color: '#2196F3' }} />;
      case 'female':
        return <FemaleIcon sx={{ color: '#E91E63' }} />;
      default:
        return <StarIcon sx={{ color: '#FF9800' }} />;
    }
  };

  return (
    <Box>
      {/* Premium Animations */}
      <GlobalStyles
        styles={{
          '@keyframes pulse': {
            '0%': {
              transform: 'scale(1)',
            },
            '50%': {
              transform: 'scale(1.05)',
            },
            '100%': {
              transform: 'scale(1)',
            },
          },
          '@keyframes shimmer': {
            '0%': {
              backgroundPosition: '-200px 0',
            },
            '100%': {
              backgroundPosition: 'calc(200px + 100%) 0',
            },
          },
        }}
      />

      {/* Success Message */}
      <Box sx={{
        mb: 3,
        p: 3,
        background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(139, 195, 74, 0.1) 100%)',
        borderRadius: 3,
        border: '1px solid rgba(76, 175, 80, 0.2)',
        textAlign: 'center'
      }}>
        <Typography variant="h6" fontWeight="700" sx={{ color: '#4CAF50', mb: 1 }}>
          🎉 World-Class Biodata Templates
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Premium designs that make users want to purchase! Eye-catching templates with stunning visuals.
        </Typography>
      </Box>
      {/* Header */}
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        mb: 3,
        p: 3,
        background: 'linear-gradient(135deg, rgba(255, 95, 109, 0.1) 0%, rgba(255, 195, 113, 0.1) 100%)',
        borderRadius: 3
      }}>
        <BiodataIcon sx={{ fontSize: 32, color: '#FF5F6D', mr: 2 }} />
        <Box>
          <Typography variant="h5" fontWeight="700" color="#FF5F6D">
            📄 Biodata Templates
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Choose from beautiful, professionally designed biodata templates
          </Typography>
        </Box>
      </Box>

      {/* Filter Buttons */}
      <Box sx={{ display: 'flex', gap: 2, mb: 4, justifyContent: 'center' }}>
        {[
          { key: 'all', label: 'All Templates', icon: <StarIcon /> },
          { key: 'male', label: 'Male Oriented', icon: <MaleIcon /> },
          { key: 'female', label: 'Female Oriented', icon: <FemaleIcon /> }
        ].map((filter) => (
          <Button
            key={filter.key}
            variant={filterGender === filter.key ? 'contained' : 'outlined'}
            startIcon={filter.icon}
            onClick={() => setFilterGender(filter.key)}
            sx={{
              borderRadius: 3,
              px: 3,
              ...(filterGender === filter.key ? {
                background: 'linear-gradient(135deg, #FF5F6D, #FFC371)',
              } : {
                borderColor: '#FF5F6D',
                color: '#FF5F6D'
              })
            }}
          >
            {filter.label}
          </Button>
        ))}
      </Box>

      {/* Templates Grid */}
      <Grid container spacing={3}>
        {filteredTemplates.map((template) => (
          <Grid item xs={12} sm={6} md={4} key={template.id}>
            <TemplateCard>
              <Box sx={{ position: 'relative', overflow: 'hidden' }}>
                {/* Premium Glow Effect */}
                {template.isPremium && (
                  <Box
                    className="premium-glow"
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      background: 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 193, 7, 0.1))',
                      opacity: 0,
                      transition: 'opacity 0.3s ease',
                      zIndex: 1
                    }}
                  />
                )}

                <CardMedia
                  component="img"
                  height="320"
                  image={template.thumbnail}
                  alt={template.name}
                  className="template-image"
                  sx={{
                    transition: 'transform 0.4s ease',
                    filter: 'brightness(1.05) contrast(1.1)'
                  }}
                />

                {/* Overlay with gradient */}
                <Box
                  className="template-overlay"
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: 'linear-gradient(180deg, transparent 0%, rgba(0,0,0,0.7) 100%)',
                    opacity: 0,
                    transition: 'opacity 0.3s ease',
                    zIndex: 2
                  }}
                />

                {/* Premium Badge */}
                {template.isPremium && (
                  <Box sx={{
                    position: 'absolute',
                    top: 12,
                    right: 12,
                    zIndex: 3
                  }}>
                    <Chip
                      label="💎 PREMIUM"
                      size="small"
                      sx={{
                        background: 'linear-gradient(135deg, #FFD700, #FFA000)',
                        color: '#000',
                        fontWeight: 700,
                        fontSize: '0.7rem',
                        boxShadow: '0 4px 15px rgba(255, 215, 0, 0.4)',
                        animation: 'pulse 2s infinite'
                      }}
                    />
                  </Box>
                )}

                {/* Category Badge */}
                <Box sx={{
                  position: 'absolute',
                  top: 12,
                  left: 12,
                  zIndex: 3
                }}>
                  <Chip
                    label={template.category}
                    size="small"
                    sx={{
                      backgroundColor: 'rgba(102, 126, 234, 0.9)',
                      color: 'white',
                      fontWeight: 600,
                      backdropFilter: 'blur(10px)'
                    }}
                  />
                </Box>

                {/* Rating and Downloads */}
                <Box sx={{
                  position: 'absolute',
                  bottom: 12,
                  left: 12,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  zIndex: 3
                }}>
                  {getGenderIcon(template.targetGender)}
                  <Chip
                    label={`${template.rating} ⭐`}
                    size="small"
                    sx={{
                      backgroundColor: 'rgba(255, 255, 255, 0.95)',
                      color: '#333',
                      fontWeight: 600,
                      backdropFilter: 'blur(10px)'
                    }}
                  />
                  <Chip
                    label={`${(template.downloads / 1000).toFixed(1)}k downloads`}
                    size="small"
                    sx={{
                      backgroundColor: 'rgba(76, 175, 80, 0.9)',
                      color: 'white',
                      fontWeight: 600,
                      backdropFilter: 'blur(10px)'
                    }}
                  />
                </Box>
              </Box>
              
              <CardContent sx={{ p: 4 }}>
                <Typography variant="h6" fontWeight="700" gutterBottom sx={{
                  fontSize: '1.1rem',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>
                  {template.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3, lineHeight: 1.6 }}>
                  {template.description}
                </Typography>

                {/* Premium Pricing Display */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  mb: 3,
                  p: 2,
                  background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',
                  borderRadius: 2,
                  border: '1px solid rgba(102, 126, 234, 0.1)'
                }}>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="h5" fontWeight="800" sx={{
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      backgroundClip: 'text',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent'
                    }}>
                      ₹{template.price}
                    </Typography>
                    {template.discount && (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                        <Typography
                          variant="body2"
                          sx={{
                            textDecoration: 'line-through',
                            color: 'text.secondary',
                            fontSize: '0.9rem'
                          }}
                        >
                          ₹{template.originalPrice}
                        </Typography>
                        <Chip
                          label={`${template.discount}% OFF`}
                          size="small"
                          sx={{
                            background: 'linear-gradient(135deg, #4CAF50, #8BC34A)',
                            color: 'white',
                            fontWeight: 700,
                            fontSize: '0.7rem',
                            animation: 'pulse 2s infinite'
                          }}
                        />
                      </Box>
                    )}
                  </Box>
                  <Box sx={{ textAlign: 'right' }}>
                    <Typography variant="caption" color="text.secondary" display="block">
                      Downloads
                    </Typography>
                    <Typography variant="h6" fontWeight="600" color="success.main">
                      {(template.downloads / 1000).toFixed(1)}k+
                    </Typography>
                  </Box>
                </Box>

                {/* Features Grid */}
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" fontWeight="600" gutterBottom sx={{ color: '#667eea' }}>
                    ✨ Key Features
                  </Typography>
                  <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: 1 }}>
                    {template.features.map((feature, index) => (
                      <Chip
                        key={index}
                        label={feature}
                        size="small"
                        sx={{
                          backgroundColor: 'rgba(102, 126, 234, 0.1)',
                          color: '#667eea',
                          fontSize: '0.7rem',
                          fontWeight: 600,
                          justifyContent: 'flex-start',
                          '& .MuiChip-label': {
                            px: 1
                          }
                        }}
                      />
                    ))}
                  </Box>
                </Box>

                {/* Action Buttons */}
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Button
                    variant="outlined"
                    startIcon={<PreviewIcon />}
                    onClick={() => handlePreview(template)}
                    sx={{
                      borderColor: '#667eea',
                      color: '#667eea',
                      borderRadius: 3,
                      flex: 1,
                      py: 1.5,
                      fontWeight: 600,
                      textTransform: 'none',
                      '&:hover': {
                        borderColor: '#667eea',
                        background: 'rgba(102, 126, 234, 0.05)',
                        transform: 'translateY(-2px)',
                        boxShadow: '0 8px 25px rgba(102, 126, 234, 0.2)'
                      }
                    }}
                  >
                    👁️ Preview
                  </Button>
                  <Button
                    variant="contained"
                    startIcon={<PurchaseIcon />}
                    onClick={() => handlePurchase(template)}
                    sx={{
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      borderRadius: 3,
                      flex: 1,
                      py: 1.5,
                      fontWeight: 700,
                      textTransform: 'none',
                      boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)',
                      '&:hover': {
                        background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                        transform: 'translateY(-2px)',
                        boxShadow: '0 12px 35px rgba(102, 126, 234, 0.4)'
                      }
                    }}
                  >
                    💎 Buy Now
                  </Button>
                </Box>
              </CardContent>
            </TemplateCard>
          </Grid>
        ))}
      </Grid>

      {/* Enhanced Preview Dialog */}
      <BiodataTemplatePreview
        template={selectedTemplate}
        open={previewOpen}
        onClose={() => setPreviewOpen(false)}
        onPurchase={(template) => {
          handlePurchase(template);
          setPreviewOpen(false);
        }}
      />
    </Box>
  );
};

export default BiodataTemplatesWidget;
