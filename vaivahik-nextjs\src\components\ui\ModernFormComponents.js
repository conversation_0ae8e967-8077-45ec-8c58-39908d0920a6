/**
 * Modern Form Components
 * 
 * Shared styled components for all forms in the application.
 * These components provide a consistent modern UI across all forms,
 * including registration, login, and profile completion forms.
 */

import React from 'react';
import { 
  Box, 
  Paper, 
  TextField, 
  Button, 
  Select, 
  FormControl,
  FormLabel,
  FormControlLabel,
  Radio,
  Stepper,
  Typography,
  Divider,
  alpha,
  styled
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';

// Floating decorative element
export const FloatingElement = styled(Box)(({ theme, position }) => ({
  position: 'absolute',
  width: '200px',
  height: '200px',
  borderRadius: '50%',
  background: 'linear-gradient(135deg, rgba(255, 95, 109, 0.05) 0%, rgba(255, 195, 113, 0.07) 100%)',
  zIndex: 0,
  ...(position === 'top-right' && {
    top: '-100px',
    right: '-100px',
  }),
  ...(position === 'bottom-left' && {
    bottom: '-100px',
    left: '-100px',
  }),
}));

// Main container for forms
export const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  borderRadius: theme.shape.borderRadius * 2,
  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
  background: 'linear-gradient(to bottom, #ffffff, #f8f9fa)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '5px',
    background: 'var(--primary-gradient)',
  }
}));

// Modern styled text field
export const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: 12,
    transition: 'all 0.3s ease',
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: 'var(--primary-color)',
    },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      borderColor: 'var(--primary-color)',
      borderWidth: 2,
      boxShadow: '0 0 0 3px rgba(255, 95, 109, 0.2)',
    },
  },
}));

// Modern styled button
export const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: 50,
  padding: '12px 30px',
  textTransform: 'none',
  fontWeight: 600,
  boxShadow: '0 4px 14px rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-3px)',
    boxShadow: '0 6px 20px rgba(0, 0, 0, 0.15)',
  },
  '&.MuiButton-contained': {
    background: 'var(--primary-gradient)',
  },
  '&.MuiButton-outlined': {
    borderWidth: 2,
    '&:hover': {
      borderWidth: 2,
    },
  },
}));

// Modern styled select
export const StyledSelect = styled(Select)(({ theme }) => ({
  '& .MuiOutlinedInput-notchedOutline': {
    borderRadius: 12,
  },
  '&.MuiOutlinedInput-root': {
    borderRadius: 12,
    transition: 'all 0.3s ease',
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: 'var(--primary-color)',
    },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      borderColor: 'var(--primary-color)',
      borderWidth: 2,
      boxShadow: '0 0 0 3px rgba(255, 95, 109, 0.2)',
    },
  },
}));

// Modern styled form label
export const StyledFormLabel = styled(FormLabel)(({ theme }) => ({
  fontWeight: 500,
  color: 'var(--text-color-dark)',
  marginBottom: theme.spacing(1),
  display: 'block',
}));

// Modern styled radio button
export const StyledRadio = styled(Radio)(({ theme }) => ({
  '&.Mui-checked': {
    color: 'var(--primary-color)',
  },
}));

// Modern styled form control label
export const StyledFormControlLabel = styled(FormControlLabel)(({ theme }) => ({
  '& .MuiFormControlLabel-label': {
    fontSize: '0.9rem',
  },
}));

// Modern styled stepper
export const StyledStepper = styled(Stepper)(({ theme }) => ({
  '& .MuiStepIcon-root': {
    color: alpha(theme.palette.primary.main, 0.3),
    '&.Mui-active': {
      color: 'var(--primary-color)',
    },
    '&.Mui-completed': {
      color: 'var(--primary-color)',
    },
  },
  '& .MuiStepConnector-line': {
    borderColor: alpha(theme.palette.primary.main, 0.2),
  },
}));

// Modern styled date picker
export const StyledDatePicker = styled(DatePicker)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: 12,
    transition: 'all 0.3s ease',
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: 'var(--primary-color)',
    },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      borderColor: 'var(--primary-color)',
      borderWidth: 2,
      boxShadow: '0 0 0 3px rgba(255, 95, 109, 0.2)',
    },
  },
}));

// Modern styled divider
export const StyledDivider = styled(Divider)(({ theme }) => ({
  margin: theme.spacing(3, 0),
  '&::before, &::after': {
    borderColor: alpha(theme.palette.primary.main, 0.2),
  },
}));

// Modern styled section title
export const StyledSectionTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 600,
  fontSize: '1.25rem',
  marginBottom: theme.spacing(3),
  color: 'var(--text-color-dark)',
  position: 'relative',
  paddingBottom: theme.spacing(1),
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: '40px',
    height: '3px',
    background: 'var(--primary-gradient)',
    borderRadius: '10px',
  },
}));

// Modern styled step title
export const StyledStepTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 600,
  fontSize: '1.25rem',
  marginBottom: theme.spacing(3),
  color: 'var(--text-color-dark)',
}));

// Modern styled card
export const StyledCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: theme.shape.borderRadius * 2,
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: '0 8px 30px rgba(0, 0, 0, 0.1)',
  },
}));

// Modern styled form section
export const FormSection = ({ title, children, ...props }) => (
  <Box sx={{ mb: 4 }} {...props}>
    <StyledSectionTitle variant="h6">{title}</StyledSectionTitle>
    {children}
  </Box>
);

// Modern styled form row
export const FormRow = ({ children, spacing = 2, ...props }) => (
  <Box
    sx={{
      display: 'flex',
      flexDirection: { xs: 'column', sm: 'row' },
      gap: spacing,
      mb: 2,
      '& > *': { flex: 1 },
    }}
    {...props}
  >
    {children}
  </Box>
);

export default {
  FloatingElement,
  StyledPaper,
  StyledTextField,
  StyledButton,
  StyledSelect,
  StyledFormLabel,
  StyledRadio,
  StyledFormControlLabel,
  StyledStepper,
  StyledDatePicker,
  StyledDivider,
  StyledSectionTitle,
  StyledStepTitle,
  StyledCard,
  FormSection,
  FormRow
};
