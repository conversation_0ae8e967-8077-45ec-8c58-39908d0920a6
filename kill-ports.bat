@echo off
echo Killing processes on ports 3000, 3001, and 3002...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000') do (
    echo Killing process with PID: %%a
    taskkill /F /PID %%a
)
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3001') do (
    echo Killing process with PID: %%a
    taskkill /F /PID %%a
)
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3002') do (
    echo Killing process with PID: %%a
    taskkill /F /PID %%a
)
echo Port cleanup complete.
