/**
 * Redis Cache Monitoring Script
 * 
 * This script monitors Redis cache usage and provides statistics.
 * Run it with: node scripts/monitor-cache.js
 */

const redis = require('../redis/redisClient');
const { CACHE_PREFIXES } = require('../redis/cacheService');
const logger = require('../src/utils/logger');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',
  
  fg: {
    black: '\x1b[30m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m'
  },
  
  bg: {
    black: '\x1b[40m',
    red: '\x1b[41m',
    green: '\x1b[42m',
    yellow: '\x1b[43m',
    blue: '\x1b[44m',
    magenta: '\x1b[45m',
    cyan: '\x1b[46m',
    white: '\x1b[47m'
  }
};

/**
 * Format bytes to human-readable format
 * @param {number} bytes - Bytes to format
 * @returns {string} Formatted string
 */
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Format milliseconds to human-readable format
 * @param {number} ms - Milliseconds to format
 * @returns {string} Formatted string
 */
function formatTime(ms) {
  if (ms < 1000) return ms + ' ms';
  if (ms < 60000) return (ms / 1000).toFixed(2) + ' sec';
  if (ms < 3600000) return (ms / 60000).toFixed(2) + ' min';
  return (ms / 3600000).toFixed(2) + ' hr';
}

/**
 * Get Redis server info
 * @returns {Promise<Object>} Redis server info
 */
async function getRedisInfo() {
  try {
    const client = await redis.getClient();
    const info = await client.info();
    
    // Parse Redis INFO command output
    const sections = {};
    let currentSection = '';
    
    info.split('\r\n').forEach(line => {
      if (line.startsWith('#')) {
        currentSection = line.substring(2).toLowerCase();
        sections[currentSection] = {};
      } else if (line.includes(':')) {
        const [key, value] = line.split(':');
        if (currentSection && key) {
          sections[currentSection][key] = value;
        }
      }
    });
    
    return sections;
  } catch (error) {
    logger.error('Error getting Redis info:', error);
    throw error;
  }
}

/**
 * Get cache statistics by prefix
 * @returns {Promise<Object>} Cache statistics
 */
async function getCacheStats() {
  try {
    const client = await redis.getClient();
    const stats = {};
    
    // Get all keys
    const allKeys = await client.keys('*');
    stats.totalKeys = allKeys.length;
    
    // Group keys by prefix
    stats.byPrefix = {};
    
    for (const prefix of Object.values(CACHE_PREFIXES)) {
      const keys = await client.keys(`${prefix}*`);
      
      if (keys.length > 0) {
        // Get TTL and size for each key
        let totalSize = 0;
        let totalTtl = 0;
        let minTtl = Infinity;
        let maxTtl = 0;
        
        for (const key of keys) {
          const [size, ttl] = await Promise.all([
            client.sendCommand(['MEMORY', 'USAGE', key]),
            client.ttl(key)
          ]);
          
          totalSize += size || 0;
          
          if (ttl > 0) {
            totalTtl += ttl;
            minTtl = Math.min(minTtl, ttl);
            maxTtl = Math.max(maxTtl, ttl);
          }
        }
        
        const avgTtl = keys.length > 0 ? totalTtl / keys.length : 0;
        
        stats.byPrefix[prefix] = {
          count: keys.length,
          totalSize,
          avgSize: totalSize / keys.length,
          minTtl: minTtl === Infinity ? 0 : minTtl,
          maxTtl,
          avgTtl
        };
      } else {
        stats.byPrefix[prefix] = {
          count: 0,
          totalSize: 0,
          avgSize: 0,
          minTtl: 0,
          maxTtl: 0,
          avgTtl: 0
        };
      }
    }
    
    return stats;
  } catch (error) {
    logger.error('Error getting cache statistics:', error);
    throw error;
  }
}

/**
 * Display cache statistics
 */
async function displayStats() {
  try {
    console.log(`\n${colors.bright}${colors.fg.cyan}===== Redis Cache Monitor =====${colors.reset}\n`);
    
    // Get Redis server info
    const info = await getRedisInfo();
    const memory = info.memory || {};
    const server = info.server || {};
    const stats = info.stats || {};
    
    console.log(`${colors.bright}Server Info:${colors.reset}`);
    console.log(`  Redis Version: ${colors.fg.green}${server.redis_version || 'Unknown'}${colors.reset}`);
    console.log(`  Uptime: ${colors.fg.green}${formatTime((server.uptime_in_seconds || 0) * 1000)}${colors.reset}`);
    console.log(`  Connected Clients: ${colors.fg.green}${stats.connected_clients || 0}${colors.reset}`);
    console.log(`  Memory Usage: ${colors.fg.green}${formatBytes(parseInt(memory.used_memory || 0))}${colors.reset}`);
    console.log(`  Peak Memory: ${colors.fg.green}${formatBytes(parseInt(memory.used_memory_peak || 0))}${colors.reset}`);
    
    // Get cache statistics
    const cacheStats = await getCacheStats();
    
    console.log(`\n${colors.bright}Cache Statistics:${colors.reset}`);
    console.log(`  Total Keys: ${colors.fg.green}${cacheStats.totalKeys}${colors.reset}`);
    
    console.log(`\n${colors.bright}Cache by Prefix:${colors.reset}`);
    
    // Sort prefixes by count (descending)
    const sortedPrefixes = Object.entries(cacheStats.byPrefix)
      .sort(([, a], [, b]) => b.count - a.count);
    
    for (const [prefix, stats] of sortedPrefixes) {
      if (stats.count > 0) {
        console.log(`\n  ${colors.fg.yellow}${prefix}${colors.reset}`);
        console.log(`    Count: ${colors.fg.green}${stats.count}${colors.reset}`);
        console.log(`    Total Size: ${colors.fg.green}${formatBytes(stats.totalSize)}${colors.reset}`);
        console.log(`    Avg Size: ${colors.fg.green}${formatBytes(stats.avgSize)}${colors.reset}`);
        console.log(`    TTL Range: ${colors.fg.green}${formatTime(stats.minTtl * 1000)} - ${formatTime(stats.maxTtl * 1000)}${colors.reset}`);
        console.log(`    Avg TTL: ${colors.fg.green}${formatTime(stats.avgTtl * 1000)}${colors.reset}`);
      }
    }
    
    console.log(`\n${colors.bright}${colors.fg.cyan}===============================${colors.reset}\n`);
  } catch (error) {
    console.error(`${colors.fg.red}Error displaying cache statistics:${colors.reset}`, error);
  }
}

// Run the script
(async () => {
  try {
    await displayStats();
    process.exit(0);
  } catch (error) {
    console.error('Error running cache monitor:', error);
    process.exit(1);
  }
})();
