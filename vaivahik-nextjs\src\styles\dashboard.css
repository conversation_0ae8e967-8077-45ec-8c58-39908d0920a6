/* Admin Dashboard Styles */

/* Dashboard layout */
.dashboard-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.dashboard-content {
  padding: 20px;
}

/* Dashboard cards */
.dashboard-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.dashboard-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.dashboard-card-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* Dashboard stats */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #6200ea;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

/* Dashboard charts */
.chart-container {
  height: 300px;
  margin-bottom: 30px;
}

/* Dashboard tables */
.dashboard-table {
  width: 100%;
  border-collapse: collapse;
}

.dashboard-table th {
  background-color: #f5f5f5;
  padding: 12px 15px;
  text-align: left;
  font-weight: 600;
  color: #333;
}

.dashboard-table td {
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
}

.dashboard-table tr:hover {
  background-color: #f9f9f9;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .stats-container {
    grid-template-columns: 1fr;
  }
  
  .dashboard-content {
    padding: 15px;
  }
}
