/**
 * Modern Location Details Form
 *
 * A modern UI form for collecting location details as part of the profile completion process.
 * Uses the shared styled components for consistent UI across the application.
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Grid,
  MenuItem,
  FormHelperText,
  CircularProgress,
  Alert,
  InputAdornment,
  Typography
} from '@mui/material';
import {
  LocationOn as LocationIcon,
  Home as HomeIcon,
  Search as SearchIcon,
  Public as CountryIcon,
  Place as PlaceIcon
} from '@mui/icons-material';
import { validateField, VALIDATION_RULES } from '@/utils/validationUtils';
import { formatError, getUserFriendlyMessage, isValidationError } from '@/utils/errorHandling';
import {
  StyledPaper,
  StyledTextField,
  StyledSelect,
  StyledButton,
  StyledFormLabel,
  FloatingElement,
  FormSection,
  FormRow,
  StyledSectionTitle,
  StyledDatePicker
} from '@/components/ui/ModernFormComponents';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import { initPlacesAutocomplete } from '@/utils/googleMapsLoader';

// Constants for form options
const COUNTRY_OPTIONS = [
  { value: 'India', label: 'India' },
  { value: 'United States', label: 'United States' },
  { value: 'United Kingdom', label: 'United Kingdom' },
  { value: 'Canada', label: 'Canada' },
  { value: 'Australia', label: 'Australia' },
  { value: 'Other', label: 'Other' }
];

const STATE_OPTIONS = [
  { value: 'Maharashtra', label: 'Maharashtra' },
  { value: 'Gujarat', label: 'Gujarat' },
  { value: 'Karnataka', label: 'Karnataka' },
  { value: 'Tamil Nadu', label: 'Tamil Nadu' },
  { value: 'Delhi', label: 'Delhi' },
  { value: 'Other', label: 'Other' }
];

const ModernLocationDetailsForm = ({ userData, onSave, isLoading = false }) => {
  const [formData, setFormData] = useState({
    city: '',
    state: 'Maharashtra',
    country: 'India',
    pincode: '',
    birthPlace: '',
    birthTime: null,
    currentLocation: '',
    permanentLocation: ''
  });

  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  // Refs for Google Places Autocomplete
  const cityInputRef = useRef(null);
  const birthPlaceInputRef = useRef(null);
  const currentLocationInputRef = useRef(null);
  const permanentLocationInputRef = useRef(null);

  // Initialize form with user data if available
  useEffect(() => {
    if (userData?.locationDetails) {
      setFormData({
        ...formData,
        ...userData.locationDetails,
        birthTime: userData.locationDetails.birthTime ? new Date(userData.locationDetails.birthTime) : null
      });
    }
  }, [userData]);

  // Initialize Google Places Autocomplete
  useEffect(() => {
    const initAutocomplete = async () => {
      try {
        // Initialize Google Places Autocomplete for city input
        if (cityInputRef.current) {
          const autocomplete = await initPlacesAutocomplete(cityInputRef.current);
          autocomplete.addListener('place_changed', () => {
            const place = autocomplete.getPlace();
            if (place.formatted_address) {
              setFormData(prev => ({
                ...prev,
                city: place.name || ''
              }));
            }
          });
        }

        // Initialize Google Places Autocomplete for birth place input
        if (birthPlaceInputRef.current) {
          const autocomplete = await initPlacesAutocomplete(birthPlaceInputRef.current);
          autocomplete.addListener('place_changed', () => {
            const place = autocomplete.getPlace();
            if (place.formatted_address) {
              setFormData(prev => ({
                ...prev,
                birthPlace: place.name || ''
              }));
            }
          });
        }

        // Initialize Google Places Autocomplete for current location input
        if (currentLocationInputRef.current) {
          const autocomplete = await initPlacesAutocomplete(currentLocationInputRef.current);
          autocomplete.addListener('place_changed', () => {
            const place = autocomplete.getPlace();
            if (place.formatted_address) {
              setFormData(prev => ({
                ...prev,
                currentLocation: place.formatted_address || ''
              }));
            }
          });
        }

        // Initialize Google Places Autocomplete for permanent location input
        if (permanentLocationInputRef.current) {
          const autocomplete = await initPlacesAutocomplete(permanentLocationInputRef.current);
          autocomplete.addListener('place_changed', () => {
            const place = autocomplete.getPlace();
            if (place.formatted_address) {
              setFormData(prev => ({
                ...prev,
                permanentLocation: place.formatted_address || ''
              }));
            }
          });
        }
      } catch (error) {
        console.error('Error initializing Google Places Autocomplete:', error);
      }
    };

    initAutocomplete();
  }, []);

  // Validate a single field
  const validateSingleField = (name, value) => {
    let rule;

    switch (name) {
      case 'city':
        rule = VALIDATION_RULES.CITY;
        break;
      case 'state':
        rule = VALIDATION_RULES.STATE;
        break;
      case 'country':
        rule = VALIDATION_RULES.COUNTRY;
        break;
      case 'pincode':
        rule = VALIDATION_RULES.PINCODE;
        break;
      case 'birthPlace':
        rule = VALIDATION_RULES.BIRTH_PLACE;
        break;
      default:
        return null;
    }

    return validateField(name, value, rule, formData);
  };

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    const newFormData = {
      ...formData,
      [name]: value
    };

    setFormData(newFormData);

    // Mark field as touched
    setTouched({
      ...touched,
      [name]: true
    });

    // Validate field on change if it's been touched
    if (touched[name]) {
      const fieldError = validateSingleField(name, value);
      setErrors(prevErrors => {
        if (fieldError) {
          return { ...prevErrors, [name]: fieldError };
        } else {
          // Remove error if it exists
          const { [name]: _, ...restErrors } = prevErrors;
          return restErrors;
        }
      });
    }
  };

  // Handle time change
  const handleTimeChange = (time) => {
    const newFormData = {
      ...formData,
      birthTime: time
    };

    setFormData(newFormData);

    // Mark field as touched
    setTouched({
      ...touched,
      birthTime: true
    });

    // Validate field on change if it's been touched
    if (touched.birthTime) {
      const fieldError = validateSingleField('birthTime', time);
      setErrors(prevErrors => {
        if (fieldError) {
          return { ...prevErrors, birthTime: fieldError };
        } else {
          // Remove error if it exists
          const { birthTime: _, ...restErrors } = prevErrors;
          return restErrors;
        }
      });
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    // Validate all fields
    Object.keys(formData).forEach(fieldName => {
      const error = validateSingleField(fieldName, formData[fieldName]);
      if (error) {
        newErrors[fieldName] = error;
      }
    });

    // Required fields
    if (!formData.city) {
      newErrors.city = 'City is required';
    }

    if (!formData.state) {
      newErrors.state = 'State is required';
    }

    if (!formData.country) {
      newErrors.country = 'Country is required';
    }

    // Pincode validation (6 digits for India)
    if (formData.pincode && formData.country === 'India') {
      const pincodeRegex = /^\d{6}$/;
      if (!pincodeRegex.test(formData.pincode)) {
        newErrors.pincode = 'Pincode must be 6 digits';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Format birth time to ISO string for API
    const formattedData = {
      ...formData,
      birthTime: formData.birthTime ? formData.birthTime.toISOString() : null
    };

    // Call the onSave function with the form data
    onSave(formattedData);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <StyledPaper>
        {/* Decorative elements */}
        <FloatingElement position="top-right" />
        <FloatingElement position="bottom-left" />

        <Box sx={{ position: 'relative', zIndex: 1 }}>
          <StyledSectionTitle>Location Details</StyledSectionTitle>

          {/* Form content */}
          <form onSubmit={handleSubmit}>
            <FormSection title="Current Location">
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <StyledFormLabel>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <LocationIcon fontSize="small" sx={{ mr: 1 }} />
                      City*
                    </Box>
                  </StyledFormLabel>
                  <StyledTextField
                    name="city"
                    value={formData.city}
                    onChange={handleChange}
                    fullWidth
                    placeholder="Search for your city"
                    error={!!errors.city}
                    helperText={errors.city || "Use Google Places to search for your city"}
                    inputRef={cityInputRef}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <StyledFormLabel>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <PlaceIcon fontSize="small" sx={{ mr: 1 }} />
                      State*
                    </Box>
                  </StyledFormLabel>
                  <StyledSelect
                    name="state"
                    value={formData.state}
                    onChange={handleChange}
                    fullWidth
                    error={!!errors.state}
                  >
                    {STATE_OPTIONS.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </StyledSelect>
                  {errors.state && <FormHelperText error>{errors.state}</FormHelperText>}
                </Grid>

                <Grid item xs={12} sm={6}>
                  <StyledFormLabel>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <CountryIcon fontSize="small" sx={{ mr: 1 }} />
                      Country*
                    </Box>
                  </StyledFormLabel>
                  <StyledSelect
                    name="country"
                    value={formData.country}
                    onChange={handleChange}
                    fullWidth
                    error={!!errors.country}
                  >
                    {COUNTRY_OPTIONS.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </StyledSelect>
                  {errors.country && <FormHelperText error>{errors.country}</FormHelperText>}
                </Grid>

                <Grid item xs={12} sm={6}>
                  <StyledFormLabel>Pincode</StyledFormLabel>
                  <StyledTextField
                    name="pincode"
                    value={formData.pincode}
                    onChange={handleChange}
                    fullWidth
                    placeholder="Enter your pincode"
                    error={!!errors.pincode}
                    helperText={errors.pincode}
                  />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection title="Birth Details">
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <StyledFormLabel>Birth Place</StyledFormLabel>
                  <StyledTextField
                    name="birthPlace"
                    value={formData.birthPlace}
                    onChange={handleChange}
                    fullWidth
                    placeholder="City/Town where you were born"
                    inputRef={birthPlaceInputRef}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <StyledFormLabel>Birth Time</StyledFormLabel>
                  <TimePicker
                    value={formData.birthTime}
                    onChange={handleTimeChange}
                    renderInput={(params) => (
                      <StyledTextField
                        {...params}
                        fullWidth
                        placeholder="Select birth time (optional)"
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection title="Additional Locations">
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <StyledFormLabel>Current Location (Full Address)</StyledFormLabel>
                  <StyledTextField
                    name="currentLocation"
                    value={formData.currentLocation}
                    onChange={handleChange}
                    fullWidth
                    placeholder="Search for your current location"
                    inputRef={currentLocationInputRef}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <StyledFormLabel>Permanent Location (Full Address)</StyledFormLabel>
                  <StyledTextField
                    name="permanentLocation"
                    value={formData.permanentLocation}
                    onChange={handleChange}
                    fullWidth
                    placeholder="Search for your permanent location"
                    inputRef={permanentLocationInputRef}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
              </Grid>
            </FormSection>

            <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
              <StyledButton
                type="submit"
                variant="contained"
                disabled={isLoading}
                startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : null}
              >
                {isLoading ? 'Saving...' : 'Save Location Details'}
              </StyledButton>
            </Box>
          </form>
        </Box>
      </StyledPaper>
    </LocalizationProvider>
  );
};

export default ModernLocationDetailsForm;
