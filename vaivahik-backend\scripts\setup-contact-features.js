/**
 * Setup Contact Reveal Features in Admin Panel
 * Adds contact reveal and calling features to the feature management system
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function setupContactFeatures() {
  console.log('🔧 Setting up Contact Reveal Features in Admin Panel...\n');

  try {
    // 1. Create Contact Reveal Feature
    console.log('1️⃣ Creating Contact Reveal feature...');
    const contactRevealFeature = await prisma.feature.upsert({
      where: { name: 'contact-reveal' },
      update: {},
      create: {
        name: 'contact-reveal',
        displayName: 'Contact Number Reveal',
        description: 'Access contact numbers of other users for direct calling',
        category: 'COMMUNICATION',
        isActive: true
      }
    });

    // 2. Create Calling Feature
    console.log('2️⃣ Creating Direct Calling feature...');
    const callingFeature = await prisma.feature.upsert({
      where: { name: 'direct-calling' },
      update: {},
      create: {
        name: 'direct-calling',
        displayName: 'Direct Calling',
        description: 'Make direct phone calls to other users via native dialer',
        category: 'COMMUNICATION',
        isActive: true
      }
    });

    // 3. Create Contact History Feature
    console.log('3️⃣ Creating Contact History feature...');
    const contactHistoryFeature = await prisma.feature.upsert({
      where: { name: 'contact-history' },
      update: {},
      create: {
        name: 'contact-history',
        displayName: 'Contact Access History',
        description: 'View history of contact numbers you have accessed',
        category: 'COMMUNICATION',
        isActive: true
      }
    });

    // 4. Create Premium Chat Feature (Enhanced)
    console.log('4️⃣ Creating Enhanced Chat feature...');
    const enhancedChatFeature = await prisma.feature.upsert({
      where: { name: 'enhanced-chat' },
      update: {},
      create: {
        name: 'enhanced-chat',
        displayName: 'Enhanced Chat Features',
        description: 'Advanced chat with voice messages, file sharing, and priority support',
        category: 'COMMUNICATION',
        isActive: true
      }
    });

    // 5. Get or create a default subscription plan for access rules
    console.log('5️⃣ Setting up subscription plan for access rules...');

    let defaultPlan = await prisma.subscriptionPlan.findFirst({
      where: {
        planType: 'PREMIUM',
        name: 'Monthly Premium'
      }
    });

    if (!defaultPlan) {
      defaultPlan = await prisma.subscriptionPlan.create({
        data: {
          name: 'Monthly Premium',
          planType: 'PREMIUM',
          price: 999,
          currency: 'INR',
          duration: 30,
          description: 'Premium features for 30 days',
          features: JSON.stringify([
            'Contact Number Access',
            'Direct Calling',
            'Contact History',
            'Enhanced Chat'
          ]),
          isActive: true
        }
      });
    }

    // 6. Setup Feature Access Rules
    console.log('6️⃣ Setting up feature access rules...');

    const features = [
      {
        feature: contactRevealFeature,
        rules: {
          BASIC: { isEnabled: false, dailyLimit: 0, upgradeMessage: 'Upgrade to Premium to access contact numbers' },
          VERIFIED: { isEnabled: false, dailyLimit: 0, upgradeMessage: 'Premium subscription required for contact access' },
          PREMIUM: { isEnabled: true, dailyLimit: 50, upgradeMessage: '' }
        }
      },
      {
        feature: callingFeature,
        rules: {
          BASIC: { isEnabled: false, dailyLimit: 0, upgradeMessage: 'Upgrade to Premium to make direct calls' },
          VERIFIED: { isEnabled: false, dailyLimit: 0, upgradeMessage: 'Premium subscription required for calling' },
          PREMIUM: { isEnabled: true, dailyLimit: 100, upgradeMessage: '' }
        }
      },
      {
        feature: contactHistoryFeature,
        rules: {
          BASIC: { isEnabled: false, dailyLimit: 0, upgradeMessage: 'Upgrade to view contact history' },
          VERIFIED: { isEnabled: true, dailyLimit: 10, upgradeMessage: 'Upgrade to Premium for unlimited history' },
          PREMIUM: { isEnabled: true, dailyLimit: null, upgradeMessage: '' }
        }
      },
      {
        feature: enhancedChatFeature,
        rules: {
          BASIC: { isEnabled: false, dailyLimit: 0, upgradeMessage: 'Upgrade for enhanced chat features' },
          VERIFIED: { isEnabled: true, dailyLimit: 20, upgradeMessage: 'Upgrade to Premium for unlimited chat' },
          PREMIUM: { isEnabled: true, dailyLimit: null, upgradeMessage: '' }
        }
      }
    ];

    for (const { feature, rules } of features) {
      for (const [userTier, rule] of Object.entries(rules)) {
        // Use subscription plan for PREMIUM tier, general plan for others
        const subscriptionPlanId = userTier === 'PREMIUM' ? defaultPlan.id : defaultPlan.id;

        // Check if access rule already exists
        const existingRule = await prisma.featureAccess.findFirst({
          where: {
            featureId: feature.id,
            userTier: userTier,
            subscriptionPlanId: subscriptionPlanId
          }
        });

        if (existingRule) {
          // Update existing rule
          await prisma.featureAccess.update({
            where: { id: existingRule.id },
            data: rule
          });
        } else {
          // Create new rule
          await prisma.featureAccess.create({
            data: {
              featureId: feature.id,
              userTier: userTier,
              subscriptionPlanId: subscriptionPlanId,
              ...rule
            }
          });
        }
      }
    }

    // 7. Update Subscription Plans with Contact Features
    console.log('7️⃣ Updating subscription plans...');

    // Get existing premium plans
    const premiumPlans = await prisma.subscriptionPlan.findMany({
      where: {
        planType: 'PREMIUM'
      }
    });

    for (const plan of premiumPlans) {
      const existingFeatures = plan.features ? JSON.parse(plan.features) : [];

      const newFeatures = [
        ...existingFeatures,
        {
          id: 'contact-reveal',
          name: 'Contact Number Access',
          description: 'Access contact numbers of interested users',
          limit: 'Unlimited',
          icon: '📞'
        },
        {
          id: 'direct-calling',
          name: 'Direct Calling',
          description: 'Make direct phone calls via native dialer',
          limit: 'Unlimited',
          icon: '☎️'
        },
        {
          id: 'contact-history',
          name: 'Contact History',
          description: 'View your contact access history',
          limit: 'Unlimited',
          icon: '📋'
        },
        {
          id: 'enhanced-chat',
          name: 'Enhanced Chat',
          description: 'Advanced chat features with priority support',
          limit: 'Unlimited',
          icon: '💬'
        }
      ];

      // Remove duplicates
      const uniqueFeatures = newFeatures.filter((feature, index, self) =>
        index === self.findIndex(f => f.id === feature.id)
      );

      await prisma.subscriptionPlan.update({
        where: { id: plan.id },
        data: {
          features: JSON.stringify(uniqueFeatures)
        }
      });
    }

    // 8. Create Contact Security Settings
    console.log('8️⃣ Creating contact security settings...');

    await prisma.systemConfig.upsert({
      where: { configKey: 'contact_security_enabled' },
      update: {},
      create: {
        configKey: 'contact_security_enabled',
        configValue: 'true',
        description: 'Enable advanced security checks for contact access',
        isActive: true
      }
    });

    await prisma.systemConfig.upsert({
      where: { configKey: 'contact_daily_limit_basic' },
      update: {},
      create: {
        configKey: 'contact_daily_limit_basic',
        configValue: '0',
        description: 'Daily contact access limit for basic users',
        isActive: true
      }
    });

    await prisma.systemConfig.upsert({
      where: { configKey: 'contact_daily_limit_verified' },
      update: {},
      create: {
        configKey: 'contact_daily_limit_verified',
        configValue: '0',
        description: 'Daily contact access limit for verified users',
        isActive: true
      }
    });

    await prisma.systemConfig.upsert({
      where: { configKey: 'contact_daily_limit_premium' },
      update: {},
      create: {
        configKey: 'contact_daily_limit_premium',
        configValue: '50',
        description: 'Daily contact access limit for premium users',
        isActive: true
      }
    });

    await prisma.systemConfig.upsert({
      where: { configKey: 'contact_risk_threshold' },
      update: {},
      create: {
        configKey: 'contact_risk_threshold',
        configValue: '80',
        description: 'Risk score threshold for blocking contact access',
        isActive: true
      }
    });

    console.log('\n🎉 Contact Reveal Features Setup Complete!\n');

    console.log('📊 Features Created:');
    console.log('✅ Contact Number Reveal - Premium only');
    console.log('✅ Direct Calling - Premium only');
    console.log('✅ Contact History - Verified & Premium');
    console.log('✅ Enhanced Chat - Verified & Premium');

    console.log('\n🔐 Security Settings:');
    console.log('✅ Advanced security checks enabled');
    console.log('✅ Daily limits configured');
    console.log('✅ Risk threshold set to 80');

    console.log('\n💰 Business Model:');
    console.log('✅ Premium subscription benefits');
    console.log('✅ Contact access tracking');
    console.log('✅ Usage analytics ready');

    console.log('\n📱 Admin Panel Integration:');
    console.log('✅ Features visible in feature management');
    console.log('✅ Access rules configured');
    console.log('✅ Subscription plans updated');

    return true;

  } catch (error) {
    console.error('❌ Setup failed:', error);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the setup
setupContactFeatures().then(success => {
  process.exit(success ? 0 : 1);
});
