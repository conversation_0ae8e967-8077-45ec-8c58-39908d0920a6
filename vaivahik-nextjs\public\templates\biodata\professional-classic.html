<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Classic Biodata</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Roboto:wght@300;400;500&display=swap');
        
        :root {
            --primary-color: #0a3d62;
            --secondary-color: #4a69bd;
            --accent-color: #b8860b;
            --text-color: #333;
            --light-text: #666;
            --border-color: #ddd;
            --light-bg: #f9f9f9;
            --header-font: 'Playfair Display', serif;
            --body-font: 'Roboto', sans-serif;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--body-font);
            color: var(--text-color);
            line-height: 1.6;
            background-color: white;
            padding: 0;
            margin: 0;
        }
        
        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20px;
            background-color: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            position: relative;
        }
        
        /* Invocation */
        .invocation {
            text-align: center;
            font-family: var(--header-font);
            color: var(--primary-color);
            padding: 10px 0;
            font-weight: 600;
            font-size: 18px;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
        }
        
        /* Header Section */
        .header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 20px;
            position: relative;
        }
        
        .header:after {
            content: '';
            position: absolute;
            bottom: -6px;
            left: 0;
            right: 0;
            height: 1px;
            background-color: var(--accent-color);
        }
        
        .profile-photo {
            width: 150px;
            height: 200px;
            object-fit: cover;
            border: 3px solid var(--primary-color);
            margin-right: 30px;
        }
        
        .header-content {
            flex: 1;
        }
        
        .name {
            font-family: var(--header-font);
            font-size: 32px;
            color: var(--primary-color);
            margin-bottom: 5px;
            font-weight: 700;
        }
        
        .tagline {
            font-size: 16px;
            color: var(--light-text);
            margin-bottom: 15px;
            font-style: italic;
        }
        
        .quick-info {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 10px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            font-size: 14px;
            background-color: var(--light-bg);
            padding: 5px 10px;
            border-radius: 4px;
        }
        
        .info-label {
            font-weight: 500;
            margin-right: 5px;
            color: var(--primary-color);
        }
        
        /* Section Styling */
        .section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-family: var(--header-font);
            color: var(--primary-color);
            font-size: 22px;
            padding-bottom: 8px;
            margin-bottom: 15px;
            border-bottom: 1px solid var(--accent-color);
            position: relative;
        }
        
        .section-title:after {
            content: '';
            position: absolute;
            left: 0;
            bottom: -1px;
            width: 60px;
            height: 3px;
            background-color: var(--primary-color);
        }
        
        .section-content {
            padding: 0 10px;
        }
        
        /* Two Column Layout */
        .two-column {
            display: flex;
            gap: 30px;
            margin-bottom: 25px;
        }
        
        .column {
            flex: 1;
        }
        
        /* Details Table */
        .details-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .details-table tr {
            border-bottom: 1px solid var(--border-color);
        }
        
        .details-table tr:last-child {
            border-bottom: none;
        }
        
        .details-table td {
            padding: 8px 5px;
            vertical-align: top;
        }
        
        .details-table td:first-child {
            width: 40%;
            font-weight: 500;
            color: var(--secondary-color);
        }
        
        /* Education & Career */
        .timeline-item {
            position: relative;
            padding-left: 20px;
            margin-bottom: 15px;
            border-left: 2px solid var(--secondary-color);
        }
        
        .timeline-title {
            font-weight: 500;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .timeline-subtitle {
            font-size: 14px;
            color: var(--light-text);
            margin-bottom: 5px;
        }
        
        .timeline-content {
            font-size: 14px;
        }
        
        /* Expectations */
        .expectations {
            background-color: var(--light-bg);
            padding: 15px;
            border-radius: 5px;
            border-left: 3px solid var(--accent-color);
        }
        
        /* Footer */
        .footer {
            margin-top: 30px;
            text-align: center;
            padding-top: 15px;
            border-top: 1px solid var(--border-color);
            font-size: 14px;
            color: var(--light-text);
        }
        
        .branding {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 10px;
        }
        
        .brand-logo {
            height: 30px;
            margin-right: 10px;
        }
        
        .brand-name {
            font-weight: 500;
            color: var(--primary-color);
        }
        
        /* Print Styles */
        @media print {
            body {
                background-color: white;
            }
            
            .container {
                box-shadow: none;
                padding: 0;
                max-width: 100%;
            }
            
            @page {
                size: A4;
                margin: 1cm;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Invocation -->
        <div class="invocation">
            ॥ श्री गणेशाय नमः ॥
        </div>
        
        <!-- Header Section -->
        <div class="header">
            <img src="{{profilePicture}}" alt="Profile Photo" class="profile-photo">
            <div class="header-content">
                <h1 class="name">{{name}}</h1>
                <p class="tagline">{{tagline}}</p>
                <div class="quick-info">
                    <div class="info-item">
                        <span class="info-label">Age:</span> {{age}} years
                    </div>
                    <div class="info-item">
                        <span class="info-label">Height:</span> {{height}}
                    </div>
                    <div class="info-item">
                        <span class="info-label">Education:</span> {{education}}
                    </div>
                    <div class="info-item">
                        <span class="info-label">Profession:</span> {{occupation}}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Two Column Layout for Personal Details and Family Background -->
        <div class="two-column">
            <!-- Personal Details -->
            <div class="column">
                <div class="section">
                    <h2 class="section-title">Personal Details</h2>
                    <div class="section-content">
                        <table class="details-table">
                            <tr>
                                <td>Date of Birth</td>
                                <td>{{dateOfBirth}}</td>
                            </tr>
                            <tr>
                                <td>Birth Time</td>
                                <td>{{birthTime}}</td>
                            </tr>
                            <tr>
                                <td>Birth Place</td>
                                <td>{{birthPlace}}</td>
                            </tr>
                            <tr>
                                <td>Religion</td>
                                <td>{{religion}}</td>
                            </tr>
                            <tr>
                                <td>Caste</td>
                                <td>{{caste}}</td>
                            </tr>
                            <tr>
                                <td>Sub-caste</td>
                                <td>{{subCaste}}</td>
                            </tr>
                            <tr>
                                <td>Gotra</td>
                                <td>{{gotra}}</td>
                            </tr>
                            <tr>
                                <td>Marital Status</td>
                                <td>{{maritalStatus}}</td>
                            </tr>
                            <tr>
                                <td>Diet</td>
                                <td>{{diet}}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Family Background -->
            <div class="column">
                <div class="section">
                    <h2 class="section-title">Family Background</h2>
                    <div class="section-content">
                        <table class="details-table">
                            <tr>
                                <td>Father's Name</td>
                                <td>{{fatherName}}</td>
                            </tr>
                            <tr>
                                <td>Father's Occupation</td>
                                <td>{{fatherOccupation}}</td>
                            </tr>
                            <tr>
                                <td>Mother's Name</td>
                                <td>{{motherName}}</td>
                            </tr>
                            <tr>
                                <td>Mother's Occupation</td>
                                <td>{{motherOccupation}}</td>
                            </tr>
                            <tr>
                                <td>Family Type</td>
                                <td>{{familyType}}</td>
                            </tr>
                            <tr>
                                <td>Family Status</td>
                                <td>{{familyStatus}}</td>
                            </tr>
                            <tr>
                                <td>Siblings</td>
                                <td>{{siblings}}</td>
                            </tr>
                            <tr>
                                <td>Native Place</td>
                                <td>{{nativePlace}}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Education & Career -->
        <div class="section">
            <h2 class="section-title">Education & Career</h2>
            <div class="section-content">
                <div class="timeline-item">
                    <div class="timeline-title">{{education}}</div>
                    <div class="timeline-subtitle">{{educationDetails}}</div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-title">{{occupation}}</div>
                    <div class="timeline-subtitle">{{company}}</div>
                    <div class="timeline-content">{{occupationDetails}}</div>
                </div>
                
                <table class="details-table" style="margin-top: 15px;">
                    <tr>
                        <td>Annual Income</td>
                        <td>{{annualIncome}}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- Contact Information -->
        <div class="section">
            <h2 class="section-title">Contact Information</h2>
            <div class="section-content">
                <table class="details-table">
                    <tr>
                        <td>Current Location</td>
                        <td>{{city}}, {{state}}, {{country}}</td>
                    </tr>
                    <tr>
                        <td>Email</td>
                        <td>{{email}}</td>
                    </tr>
                    <tr>
                        <td>Phone</td>
                        <td>{{phone}}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- About Me & Expectations -->
        <div class="two-column">
            <!-- About Me -->
            <div class="column">
                <div class="section">
                    <h2 class="section-title">About Me</h2>
                    <div class="section-content">
                        <p>{{aboutMe}}</p>
                        
                        <div style="margin-top: 15px;">
                            <div style="font-weight: 500; color: var(--secondary-color);">Hobbies & Interests:</div>
                            <p>{{hobbies}}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Partner Expectations -->
            <div class="column">
                <div class="section">
                    <h2 class="section-title">Partner Expectations</h2>
                    <div class="section-content">
                        <div class="expectations">
                            <p>{{partnerPreferences}}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Footer with Branding -->
        <div class="footer">
            <div class="branding">
                <img src="{{brandLogo}}" alt="Brand Logo" class="brand-logo">
                <span class="brand-name">{{brandName}}</span>
            </div>
            <p>{{brandTagline}}</p>
            <p>Created on {{createdAt}}</p>
        </div>
    </div>
</body>
</html>
