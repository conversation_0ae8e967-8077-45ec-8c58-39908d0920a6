/**
 * Enhanced Logger Module
 *
 * This module provides comprehensive logging functionality for the application.
 * It supports console logging in development and file logging in production.
 */

const fs = require('fs');
const path = require('path');

// Define log levels
const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  HTTP: 3,
  DEBUG: 4
};

// Get current log level from environment or default to INFO
const currentLogLevel = process.env.LOG_LEVEL
  ? LOG_LEVELS[process.env.LOG_LEVEL.toUpperCase()]
  : (process.env.NODE_ENV === 'production' ? LOG_LEVELS.INFO : LOG_LEVELS.DEBUG);

// Get log directory from environment or default to logs
const logDir = process.env.LOG_DIR || path.join(process.cwd(), 'logs');

// Create logs directory if it doesn't exist
if (process.env.NODE_ENV === 'production' && !fs.existsSync(logDir)) {
  try {
    fs.mkdirSync(logDir, { recursive: true });
  } catch (err) {
    console.error(`Failed to create log directory: ${err.message}`);
  }
}

/**
 * Format the log message with timestamp, level, and additional metadata
 * @param {string} level - Log level
 * @param {string} message - Log message
 * @param {object} meta - Additional metadata
 * @returns {string} Formatted log message
 */
const formatLogMessage = (level, message, meta = {}) => {
  const timestamp = new Date().toISOString();
  let formattedMessage = `[${timestamp}] [${level}] ${message}`;

  // Add metadata if present
  if (meta && Object.keys(meta).length > 0) {
    try {
      formattedMessage += ` ${JSON.stringify(meta)}`;
    } catch (err) {
      formattedMessage += ` [Error serializing metadata: ${err.message}]`;
    }
  }

  return formattedMessage;
};

/**
 * Write log to file in production
 * @param {string} formattedMessage - Formatted log message
 * @param {string} level - Log level
 */
const writeToFile = (formattedMessage, level) => {
  if (process.env.NODE_ENV !== 'production') {
    return; // Only write to file in production
  }

  try {
    // Determine log file based on level
    const logFile = level === 'ERROR'
      ? path.join(logDir, 'error.log')
      : path.join(logDir, 'combined.log');

    // Append to log file
    fs.appendFileSync(logFile, formattedMessage + '\n');

    // Also write errors to a separate file
    if (level === 'ERROR') {
      fs.appendFileSync(path.join(logDir, 'error.log'), formattedMessage + '\n');
    }
  } catch (err) {
    console.error(`Failed to write to log file: ${err.message}`);
  }
};

/**
 * Log an error message
 * @param {string} message - Error message
 * @param {object} meta - Additional metadata
 */
const error = (message, meta = {}) => {
  if (currentLogLevel >= LOG_LEVELS.ERROR) {
    const formattedMessage = formatLogMessage('ERROR', message, meta);
    console.error(formattedMessage);
    writeToFile(formattedMessage, 'ERROR');
  }
};

/**
 * Log a warning message
 * @param {string} message - Warning message
 * @param {object} meta - Additional metadata
 */
const warn = (message, meta = {}) => {
  if (currentLogLevel >= LOG_LEVELS.WARN) {
    const formattedMessage = formatLogMessage('WARN', message, meta);
    console.warn(formattedMessage);
    writeToFile(formattedMessage, 'WARN');
  }
};

/**
 * Log an info message
 * @param {string} message - Info message
 * @param {object} meta - Additional metadata
 */
const info = (message, meta = {}) => {
  if (currentLogLevel >= LOG_LEVELS.INFO) {
    const formattedMessage = formatLogMessage('INFO', message, meta);
    console.log(formattedMessage);
    writeToFile(formattedMessage, 'INFO');
  }
};

/**
 * Log an HTTP message
 * @param {string} message - HTTP message
 * @param {object} meta - Additional metadata
 */
const http = (message, meta = {}) => {
  if (currentLogLevel >= LOG_LEVELS.HTTP) {
    const formattedMessage = formatLogMessage('HTTP', message, meta);
    console.log(formattedMessage);
    writeToFile(formattedMessage, 'HTTP');
  }
};

/**
 * Log a debug message
 * @param {string} message - Debug message
 * @param {object} meta - Additional metadata
 */
const debug = (message, meta = {}) => {
  if (currentLogLevel >= LOG_LEVELS.DEBUG) {
    const formattedMessage = formatLogMessage('DEBUG', message, meta);
    console.log(formattedMessage);
    writeToFile(formattedMessage, 'DEBUG');
  }
};

module.exports = {
  error,
  warn,
  info,
  http,
  debug
};
