// src/services/otp/enhancedOtpService.js

/**
 * Enhanced OTP Service with multiple providers and fallback mechanisms
 * This service provides robust OTP functionality with MSG91 as primary and Redis as backup
 */

const msg91Service = require('../sms/msg91.service');
const { generateOTP, setOTP, verifyOTP, deleteOTP, getOTP, getOTPTTL } = require('../../../redis/otpService');
const logger = require('../../utils/logger');

// OTP Configuration
const OTP_CONFIG = {
  length: 6,
  expirySeconds: 900, // 15 minutes
  maxAttempts: 3,
  resendCooldown: 60, // 1 minute
  providers: ['MSG91', 'REDIS_FALLBACK']
};

// Rate limiting storage (in production, use Redis)
const rateLimitStore = new Map();

/**
 * Enhanced OTP sending with multiple providers and fallback
 * @param {string} phone - Phone number to send OTP to
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Result object
 */
const sendOtpEnhanced = async (phone, options = {}) => {
  try {
    // Validate phone number
    if (!phone) {
      throw new Error('Phone number is required');
    }

    // Format phone number
    const formattedPhone = msg91Service.formatPhoneNumber(phone);

    // Check rate limiting
    const rateLimitResult = checkRateLimit(formattedPhone);
    if (!rateLimitResult.allowed) {
      return {
        success: false,
        message: `Too many OTP requests. Please wait ${rateLimitResult.waitTime} seconds.`,
        errorType: 'RATE_LIMITED',
        waitTime: rateLimitResult.waitTime
      };
    }

    // Generate OTP
    const otp = generateOTP(OTP_CONFIG.length);
    
    // Store OTP in Redis first (as backup)
    await setOTP(formattedPhone, otp, OTP_CONFIG.expirySeconds);
    logger.info(`OTP stored in Redis for ${formattedPhone}`);

    // Try to send via MSG91 (primary provider)
    let smsResult = null;
    let primaryProviderSuccess = false;

    try {
      smsResult = await msg91Service.sendOtp(formattedPhone, otp);
      primaryProviderSuccess = smsResult.success;
      
      if (primaryProviderSuccess) {
        logger.info(`OTP sent successfully via MSG91 to ${formattedPhone}`);
      } else {
        logger.warn(`MSG91 failed to send OTP to ${formattedPhone}: ${smsResult.message}`);
      }
    } catch (error) {
      logger.error(`MSG91 service error for ${formattedPhone}:`, error);
      smsResult = {
        success: false,
        message: error.message,
        errorCategory: 'SERVICE_ERROR'
      };
    }

    // Update rate limiting
    updateRateLimit(formattedPhone);

    // Determine response based on primary provider result
    if (primaryProviderSuccess) {
      return {
        success: true,
        message: 'OTP sent successfully',
        phone: formattedPhone,
        provider: 'MSG91',
        expirySeconds: OTP_CONFIG.expirySeconds,
        timestamp: new Date().toISOString(),
        // In development, include OTP for testing
        ...(process.env.NODE_ENV === 'development' && { otp })
      };
    } else {
      // Primary provider failed, but OTP is stored in Redis
      logger.info(`Fallback mode: OTP stored in Redis for ${formattedPhone} despite SMS failure`);
      
      return {
        success: true, // Still successful because OTP is available
        message: 'OTP generated successfully. SMS delivery may be delayed.',
        phone: formattedPhone,
        provider: 'REDIS_FALLBACK',
        expirySeconds: OTP_CONFIG.expirySeconds,
        timestamp: new Date().toISOString(),
        warning: 'SMS service temporarily unavailable',
        smsError: smsResult?.message,
        // In development, include OTP for testing
        ...(process.env.NODE_ENV === 'development' && { otp })
      };
    }

  } catch (error) {
    logger.error('Enhanced OTP service error:', error);
    return {
      success: false,
      message: 'Failed to generate OTP',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * Enhanced OTP verification with multiple validation methods
 * @param {string} phone - Phone number to verify OTP for
 * @param {string} otp - OTP to verify
 * @returns {Promise<Object>} Verification result
 */
const verifyOtpEnhanced = async (phone, otp) => {
  try {
    // Validate inputs
    if (!phone || !otp) {
      throw new Error('Phone number and OTP are required');
    }

    // Format phone number
    const formattedPhone = msg91Service.formatPhoneNumber(phone);

    // First, try Redis verification (always available)
    const redisVerified = await verifyOTP(formattedPhone, otp);
    
    if (redisVerified) {
      // OTP verified via Redis, clean up
      await deleteOTP(formattedPhone);
      logger.info(`OTP verified successfully via Redis for ${formattedPhone}`);
      
      return {
        success: true,
        message: 'OTP verified successfully',
        phone: formattedPhone,
        provider: 'REDIS',
        timestamp: new Date().toISOString()
      };
    }

    // If Redis verification fails, try MSG91 verification as backup
    try {
      const msg91Result = await msg91Service.verifyOtp(formattedPhone, otp);
      
      if (msg91Result.success) {
        // Also clean up Redis OTP if MSG91 verification succeeds
        await deleteOTP(formattedPhone);
        logger.info(`OTP verified successfully via MSG91 for ${formattedPhone}`);
        
        return {
          success: true,
          message: 'OTP verified successfully',
          phone: formattedPhone,
          provider: 'MSG91',
          timestamp: new Date().toISOString()
        };
      }
    } catch (error) {
      logger.warn(`MSG91 verification failed for ${formattedPhone}:`, error.message);
    }

    // Both verification methods failed
    logger.warn(`OTP verification failed for ${formattedPhone} - invalid or expired OTP`);
    
    return {
      success: false,
      message: 'Invalid or expired OTP',
      phone: formattedPhone,
      errorType: 'INVALID_OTP',
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    logger.error('Enhanced OTP verification error:', error);
    return {
      success: false,
      message: 'Failed to verify OTP',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * Resend OTP with enhanced logic
 * @param {string} phone - Phone number to resend OTP to
 * @param {string} retryType - Type of retry (voice/text)
 * @returns {Promise<Object>} Result object
 */
const resendOtpEnhanced = async (phone, retryType = 'text') => {
  try {
    const formattedPhone = msg91Service.formatPhoneNumber(phone);

    // Check if there's an existing OTP
    const existingOtp = await getOTP(formattedPhone);
    const ttl = await getOTPTTL(formattedPhone);

    if (existingOtp && ttl > 0) {
      // Check resend cooldown
      const rateLimitResult = checkRateLimit(formattedPhone, true);
      if (!rateLimitResult.allowed) {
        return {
          success: false,
          message: `Please wait ${rateLimitResult.waitTime} seconds before requesting another OTP.`,
          errorType: 'RESEND_COOLDOWN',
          waitTime: rateLimitResult.waitTime
        };
      }

      // Try to resend existing OTP via MSG91
      try {
        const resendResult = await msg91Service.resendOtp(formattedPhone, retryType);
        updateRateLimit(formattedPhone);

        return {
          success: true,
          message: `OTP resent successfully via ${retryType}`,
          phone: formattedPhone,
          provider: 'MSG91',
          retryType,
          expirySeconds: ttl,
          timestamp: new Date().toISOString(),
          // In development, include OTP for testing
          ...(process.env.NODE_ENV === 'development' && { otp: existingOtp })
        };
      } catch (error) {
        logger.warn(`Failed to resend OTP via MSG91 for ${formattedPhone}:`, error.message);
        
        // Fallback: OTP still exists in Redis
        return {
          success: true,
          message: 'OTP is still valid. SMS resend may be delayed.',
          phone: formattedPhone,
          provider: 'REDIS_FALLBACK',
          expirySeconds: ttl,
          timestamp: new Date().toISOString(),
          warning: 'SMS service temporarily unavailable',
          // In development, include OTP for testing
          ...(process.env.NODE_ENV === 'development' && { otp: existingOtp })
        };
      }
    } else {
      // No existing OTP, generate new one
      return await sendOtpEnhanced(phone);
    }

  } catch (error) {
    logger.error('Enhanced OTP resend error:', error);
    return {
      success: false,
      message: 'Failed to resend OTP',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * Check rate limiting for OTP requests
 * @param {string} phone - Phone number
 * @param {boolean} isResend - Whether this is a resend request
 * @returns {Object} Rate limit result
 */
const checkRateLimit = (phone, isResend = false) => {
  const now = Date.now();
  const key = `rate_limit:${phone}`;
  const cooldown = isResend ? OTP_CONFIG.resendCooldown * 1000 : 60000; // 1 minute for new requests
  
  const lastRequest = rateLimitStore.get(key);
  
  if (lastRequest && (now - lastRequest) < cooldown) {
    const waitTime = Math.ceil((cooldown - (now - lastRequest)) / 1000);
    return { allowed: false, waitTime };
  }
  
  return { allowed: true, waitTime: 0 };
};

/**
 * Update rate limiting for OTP requests
 * @param {string} phone - Phone number
 */
const updateRateLimit = (phone) => {
  const key = `rate_limit:${phone}`;
  rateLimitStore.set(key, Date.now());
  
  // Clean up old entries (simple cleanup)
  if (rateLimitStore.size > 1000) {
    const cutoff = Date.now() - (24 * 60 * 60 * 1000); // 24 hours
    for (const [k, v] of rateLimitStore.entries()) {
      if (v < cutoff) {
        rateLimitStore.delete(k);
      }
    }
  }
};

/**
 * Get OTP status for a phone number
 * @param {string} phone - Phone number
 * @returns {Promise<Object>} OTP status
 */
const getOtpStatus = async (phone) => {
  try {
    const formattedPhone = msg91Service.formatPhoneNumber(phone);
    const ttl = await getOTPTTL(formattedPhone);
    
    return {
      hasActiveOtp: ttl > 0,
      expirySeconds: ttl > 0 ? ttl : 0,
      phone: formattedPhone
    };
  } catch (error) {
    logger.error('Error getting OTP status:', error);
    return {
      hasActiveOtp: false,
      expirySeconds: 0,
      phone: phone
    };
  }
};

module.exports = {
  sendOtpEnhanced,
  verifyOtpEnhanced,
  resendOtpEnhanced,
  getOtpStatus,
  OTP_CONFIG
};
