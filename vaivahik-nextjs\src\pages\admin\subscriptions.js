import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);
import { toast } from 'react-toastify';
import { adminGet, adminPost, adminPut, adminDelete } from '@/services/apiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';

export default function Subscriptions() {
  const [loading, setLoading] = useState(true);
  const [subscriptions, setSubscriptions] = useState([]);
  const [totalSubscriptions, setTotalSubscriptions] = useState(0);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [statusFilter, setStatusFilter] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [currentSubscription, setCurrentSubscription] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    duration: '',
    durationUnit: 'days',
    features: '',
    status: 'active'
  });

  useEffect(() => {
    fetchSubscriptions();
  }, [page, limit, statusFilter, searchQuery]);

  const fetchSubscriptions = async () => {
    setLoading(true);
    try {
      const params = {
        page,
        limit,
        ...(statusFilter && { isActive: statusFilter === 'active' }),
        ...(searchQuery && { search: searchQuery })
      };

      const response = await adminGet(ADMIN_ENDPOINTS.SUBSCRIPTION_PLANS, params);

      if (response.success) {
        setSubscriptions(response.plans || []);
        setTotalSubscriptions(response.pagination?.totalPlans || 0);
      } else {
        toast.error(response.message || 'Failed to fetch subscription plans');
      }
    } catch (error) {
      console.error('Error fetching subscription plans:', error);
      toast.error('Error fetching subscription plans: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (newPage) => {
    setPage(newPage);
  };

  const handleLimitChange = (e) => {
    setLimit(parseInt(e.target.value));
    setPage(1);
  };

  const handleStatusFilterChange = (e) => {
    setStatusFilter(e.target.value);
    setPage(1);
  };

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setPage(1);
    fetchSubscriptions();
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const openAddModal = () => {
    setFormData({
      name: '',
      description: '',
      price: '',
      duration: '',
      durationUnit: 'days',
      features: '',
      status: 'active'
    });
    setShowAddModal(true);
  };

  const openEditModal = (subscription) => {
    setCurrentSubscription(subscription);
    setFormData({
      name: subscription.name,
      description: subscription.description,
      price: subscription.price.toString(),
      duration: subscription.duration.toString(),
      durationUnit: subscription.durationUnit || 'days',
      features: Array.isArray(subscription.features) 
        ? subscription.features.join('\n') 
        : subscription.features || '',
      status: subscription.status
    });
    setShowEditModal(true);
  };

  const closeModal = () => {
    setShowAddModal(false);
    setShowEditModal(false);
    setCurrentSubscription(null);
  };

  const handleAddSubscription = async (e) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem('adminAccessToken');
      
      if (!token) {
        toast.error('Authentication token not found');
        return;
      }
      
      const subscriptionData = {
        ...formData,
        price: parseFloat(formData.price),
        duration: parseInt(formData.duration),
        features: formData.features.split('\n').filter(feature => feature.trim() !== '')
      };
      
      const response = await fetch('/api/admin/subscriptions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(subscriptionData)
      });
      
      const data = await response.json();
      
      if (data.success) {
        toast.success('Subscription plan added successfully');
        closeModal();
        fetchSubscriptions();
      } else {
        toast.error(data.message || 'Failed to add subscription plan');
      }
    } catch (error) {
      console.error('Error adding subscription plan:', error);
      toast.error('Error adding subscription plan: ' + error.message);
    }
  };

  const handleUpdateSubscription = async (e) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem('adminAccessToken');
      
      if (!token) {
        toast.error('Authentication token not found');
        return;
      }
      
      const subscriptionData = {
        id: currentSubscription.id,
        ...formData,
        price: parseFloat(formData.price),
        duration: parseInt(formData.duration),
        features: formData.features.split('\n').filter(feature => feature.trim() !== '')
      };
      
      const response = await fetch('/api/admin/subscriptions', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(subscriptionData)
      });
      
      const data = await response.json();
      
      if (data.success) {
        toast.success('Subscription plan updated successfully');
        closeModal();
        fetchSubscriptions();
      } else {
        toast.error(data.message || 'Failed to update subscription plan');
      }
    } catch (error) {
      console.error('Error updating subscription plan:', error);
      toast.error('Error updating subscription plan: ' + error.message);
    }
  };

  const handleDeleteSubscription = async (id) => {
    if (!confirm('Are you sure you want to delete this subscription plan?')) {
      return;
    }
    
    try {
      const token = localStorage.getItem('adminAccessToken');
      
      if (!token) {
        toast.error('Authentication token not found');
        return;
      }
      
      const response = await fetch(`/api/admin/subscriptions?id=${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      const data = await response.json();
      
      if (data.success) {
        toast.success('Subscription plan deleted successfully');
        fetchSubscriptions();
      } else {
        toast.error(data.message || 'Failed to delete subscription plan');
      }
    } catch (error) {
      console.error('Error deleting subscription plan:', error);
      toast.error('Error deleting subscription plan: ' + error.message);
    }
  };

  // Calculate pagination
  const totalPages = Math.ceil(totalSubscriptions / limit);
  const paginationItems = [];
  for (let i = 1; i <= totalPages; i++) {
    paginationItems.push(i);
  }

  return (
    <EnhancedAdminLayout title="Subscription Plans">
      <div className="subscriptions-page">
        <div className="page-header">
          <h1>Subscription Plans</h1>
          <button className="btn-primary" onClick={openAddModal}>
            <i className="fas fa-plus"></i> Add New Plan
          </button>
        </div>

        <div className="filters-container">
          <div className="search-box">
            <form onSubmit={handleSearch}>
              <input
                type="text"
                placeholder="Search plans..."
                value={searchQuery}
                onChange={handleSearchChange}
              />
              <button type="submit">
                <i className="fas fa-search"></i>
              </button>
            </form>
          </div>
          
          <div className="filter-group">
            <label htmlFor="statusFilter">Status:</label>
            <select
              id="statusFilter"
              value={statusFilter}
              onChange={handleStatusFilterChange}
            >
              <option value="">All</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
          
          <div className="filter-group">
            <label htmlFor="limitFilter">Show:</label>
            <select
              id="limitFilter"
              value={limit}
              onChange={handleLimitChange}
            >
              <option value="10">10</option>
              <option value="25">25</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
          </div>
        </div>

        {loading ? (
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading subscription plans...</p>
          </div>
        ) : subscriptions.length > 0 ? (
          <>
            <div className="table-container">
              <table className="data-table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Price</th>
                    <th>Duration</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {subscriptions.map((subscription) => (
                    <tr key={subscription.id}>
                      <td>{subscription.name}</td>
                      <td>₹{subscription.price.toFixed(2)}</td>
                      <td>{subscription.duration} {subscription.durationUnit}</td>
                      <td>
                        <span className={`status-badge ${subscription.status}`}>
                          {subscription.status}
                        </span>
                      </td>
                      <td className="action-cell">
                        <button
                          className="btn-icon"
                          onClick={() => openEditModal(subscription)}
                          title="Edit"
                        >
                          <i className="fas fa-edit"></i>
                        </button>
                        <button
                          className="btn-icon delete"
                          onClick={() => handleDeleteSubscription(subscription.id)}
                          title="Delete"
                        >
                          <i className="fas fa-trash-alt"></i>
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="pagination-container">
              <div className="pagination-info">
                Showing {(page - 1) * limit + 1} to {Math.min(page * limit, totalSubscriptions)} of {totalSubscriptions} plans
              </div>
              <div className="pagination">
                <button
                  className="pagination-btn"
                  disabled={page === 1}
                  onClick={() => handlePageChange(page - 1)}
                >
                  <i className="fas fa-chevron-left"></i>
                </button>
                {paginationItems.map((pageNum) => (
                  <button
                    key={pageNum}
                    className={`pagination-btn ${pageNum === page ? 'active' : ''}`}
                    onClick={() => handlePageChange(pageNum)}
                  >
                    {pageNum}
                  </button>
                ))}
                <button
                  className="pagination-btn"
                  disabled={page === totalPages}
                  onClick={() => handlePageChange(page + 1)}
                >
                  <i className="fas fa-chevron-right"></i>
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="empty-state">
            <div className="empty-icon">💰</div>
            <h3>No Subscription Plans Found</h3>
            <p>There are no subscription plans matching your filters.</p>
            <button className="btn-primary" onClick={openAddModal}>
              Add New Plan
            </button>
          </div>
        )}

        {/* Add Subscription Modal */}
        {showAddModal && (
          <div className="modal-overlay">
            <div className="modal-content">
              <div className="modal-header">
                <h2>Add New Subscription Plan</h2>
                <button className="close-modal" onClick={closeModal}>×</button>
              </div>
              <form onSubmit={handleAddSubscription}>
                <div className="modal-body">
                  <div className="form-group">
                    <label htmlFor="name">Plan Name</label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="form-group">
                    <label htmlFor="description">Description</label>
                    <textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows="3"
                    ></textarea>
                  </div>
                  
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="price">Price (₹)</label>
                      <input
                        type="number"
                        id="price"
                        name="price"
                        value={formData.price}
                        onChange={handleInputChange}
                        min="0"
                        step="0.01"
                        required
                      />
                    </div>
                    
                    <div className="form-group">
                      <label htmlFor="duration">Duration</label>
                      <div className="duration-inputs">
                        <input
                          type="number"
                          id="duration"
                          name="duration"
                          value={formData.duration}
                          onChange={handleInputChange}
                          min="1"
                          required
                        />
                        <select
                          name="durationUnit"
                          value={formData.durationUnit}
                          onChange={handleInputChange}
                        >
                          <option value="days">Days</option>
                          <option value="months">Months</option>
                          <option value="years">Years</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  
                  <div className="form-group">
                    <label htmlFor="features">Features (one per line)</label>
                    <textarea
                      id="features"
                      name="features"
                      value={formData.features}
                      onChange={handleInputChange}
                      rows="5"
                      placeholder="Enter one feature per line"
                    ></textarea>
                  </div>
                  
                  <div className="form-group">
                    <label htmlFor="status">Status</label>
                    <select
                      id="status"
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  </div>
                </div>
                
                <div className="modal-footer">
                  <button type="button" className="btn-secondary" onClick={closeModal}>
                    Cancel
                  </button>
                  <button type="submit" className="btn-primary">
                    Add Plan
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Edit Subscription Modal */}
        {showEditModal && (
          <div className="modal-overlay">
            <div className="modal-content">
              <div className="modal-header">
                <h2>Edit Subscription Plan</h2>
                <button className="close-modal" onClick={closeModal}>×</button>
              </div>
              <form onSubmit={handleUpdateSubscription}>
                <div className="modal-body">
                  <div className="form-group">
                    <label htmlFor="edit-name">Plan Name</label>
                    <input
                      type="text"
                      id="edit-name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="form-group">
                    <label htmlFor="edit-description">Description</label>
                    <textarea
                      id="edit-description"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows="3"
                    ></textarea>
                  </div>
                  
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="edit-price">Price (₹)</label>
                      <input
                        type="number"
                        id="edit-price"
                        name="price"
                        value={formData.price}
                        onChange={handleInputChange}
                        min="0"
                        step="0.01"
                        required
                      />
                    </div>
                    
                    <div className="form-group">
                      <label htmlFor="edit-duration">Duration</label>
                      <div className="duration-inputs">
                        <input
                          type="number"
                          id="edit-duration"
                          name="duration"
                          value={formData.duration}
                          onChange={handleInputChange}
                          min="1"
                          required
                        />
                        <select
                          name="durationUnit"
                          value={formData.durationUnit}
                          onChange={handleInputChange}
                        >
                          <option value="days">Days</option>
                          <option value="months">Months</option>
                          <option value="years">Years</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  
                  <div className="form-group">
                    <label htmlFor="edit-features">Features (one per line)</label>
                    <textarea
                      id="edit-features"
                      name="features"
                      value={formData.features}
                      onChange={handleInputChange}
                      rows="5"
                      placeholder="Enter one feature per line"
                    ></textarea>
                  </div>
                  
                  <div className="form-group">
                    <label htmlFor="edit-status">Status</label>
                    <select
                      id="edit-status"
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  </div>
                </div>
                
                <div className="modal-footer">
                  <button type="button" className="btn-secondary" onClick={closeModal}>
                    Cancel
                  </button>
                  <button type="submit" className="btn-primary">
                    Update Plan
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </EnhancedAdminLayout>
  );
}
