import { useState, useEffect } from 'react';
import { mockDataUtils } from '@/config/apiConfig';
import { Box, Switch, FormControlLabel, Tooltip, Typography, Chip } from '@mui/material';
import { Info as InfoIcon, Storage as StorageIcon, Cloud as CloudIcon } from '@mui/icons-material';

/**
 * A component for toggling between mock and real data
 * This is useful during development to test the application with mock data
 * before the real backend is ready.
 */
const MockDataToggle = () => {
  const [mockDataStatus, setMockDataStatus] = useState({
    enabled: true,
    label: 'Using Mock Data',
    color: 'warning'
  });

  // Initialize state from localStorage on component mount
  useEffect(() => {
    setMockDataStatus(mockDataUtils.getMockDataStatus());
  }, []);

  const handleToggle = () => {
    mockDataUtils.toggleMockData();
    // The page will reload, so no need to update state
  };

  // Only show in development mode
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <Box sx={{
      display: 'flex',
      alignItems: 'center',
      bgcolor: 'background.paper',
      p: 1.5,
      borderRadius: 2,
      boxShadow: 3,
      position: 'fixed',
      bottom: 16,
      right: 16,
      zIndex: 1000,
      border: `2px solid ${mockDataStatus.enabled ? '#ff9800' : '#4caf50'}`,
      minWidth: 280
    }}>
      <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
        {mockDataStatus.enabled ? (
          <StorageIcon sx={{ color: '#ff9800', mr: 1 }} />
        ) : (
          <CloudIcon sx={{ color: '#4caf50', mr: 1 }} />
        )}

        <Box sx={{ flex: 1 }}>
          <Typography variant="body2" fontWeight="bold">
            Data Source
          </Typography>
          <Chip
            label={mockDataStatus.label}
            color={mockDataStatus.color}
            size="small"
            sx={{ mt: 0.5 }}
          />
        </Box>

        <FormControlLabel
          control={
            <Switch
              checked={mockDataStatus.enabled}
              onChange={handleToggle}
              color="primary"
              size="small"
            />
          }
          label=""
          sx={{ m: 0, ml: 1 }}
        />
      </Box>

      <Tooltip title="Toggle between mock data and real API. The page will reload after toggling. Mock data is useful for development and testing.">
        <InfoIcon fontSize="small" color="action" sx={{ ml: 1 }} />
      </Tooltip>
    </Box>
  );
};

export default MockDataToggle;
