/**
 * API Response Utility Tests
 * 
 * This file contains tests for the API response utility.
 */

const apiResponse = require('../../src/utils/apiResponse');

describe('API Response Utility', () => {
  let mockRes;
  
  beforeEach(() => {
    // Mock Express response object
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      end: jest.fn().mockReturnThis()
    };
  });
  
  describe('success', () => {
    it('should return a success response with default values', () => {
      apiResponse.success(mockRes);
      
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        message: 'Success',
        timestamp: expect.any(String)
      });
    });
    
    it('should return a success response with custom values', () => {
      const message = 'Custom success message';
      const data = { id: 1, name: 'Test' };
      const statusCode = 201;
      const meta = { count: 1 };
      
      apiResponse.success(mockRes, message, data, statusCode, meta);
      
      expect(mockRes.status).toHaveBeenCalledWith(statusCode);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        message,
        data,
        timestamp: expect.any(String),
        meta
      });
    });
  });
  
  describe('error', () => {
    it('should return an error response with default values', () => {
      apiResponse.error(mockRes);
      
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Error',
        timestamp: expect.any(String)
      });
    });
    
    it('should return an error response with custom values', () => {
      const message = 'Custom error message';
      const statusCode = 404;
      const errors = { field: 'Error message' };
      const errorCode = 'NOT_FOUND';
      
      apiResponse.error(mockRes, message, statusCode, errors, errorCode);
      
      expect(mockRes.status).toHaveBeenCalledWith(statusCode);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message,
        errors,
        errorCode,
        timestamp: expect.any(String)
      });
    });
  });
  
  describe('created', () => {
    it('should return a created response with default values', () => {
      apiResponse.created(mockRes);
      
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        message: 'Resource created successfully',
        timestamp: expect.any(String)
      });
    });
    
    it('should return a created response with custom values', () => {
      const message = 'Custom created message';
      const data = { id: 1, name: 'Test' };
      
      apiResponse.created(mockRes, message, data);
      
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        message,
        data,
        timestamp: expect.any(String)
      });
    });
  });
  
  describe('noContent', () => {
    it('should return a no content response', () => {
      apiResponse.noContent(mockRes);
      
      expect(mockRes.status).toHaveBeenCalledWith(204);
      expect(mockRes.end).toHaveBeenCalled();
    });
  });
  
  describe('badRequest', () => {
    it('should return a bad request response with default values', () => {
      apiResponse.badRequest(mockRes);
      
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Bad request',
        errorCode: 'BAD_REQUEST',
        timestamp: expect.any(String)
      });
    });
    
    it('should return a bad request response with custom values', () => {
      const message = 'Custom bad request message';
      const errors = { field: 'Error message' };
      
      apiResponse.badRequest(mockRes, message, errors);
      
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message,
        errors,
        errorCode: 'BAD_REQUEST',
        timestamp: expect.any(String)
      });
    });
  });
});
