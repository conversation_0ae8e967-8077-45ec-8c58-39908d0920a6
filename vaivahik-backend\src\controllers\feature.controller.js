// src/controllers/feature.controller.js
const redisClient = require('../utils/redisClient');
const logger = require('../utils/logger');
const similaritySearch = require('../utils/similaritySearch');
const compatibilityScore = require('../utils/compatibilityScore');
const { getOrSetCache, CACHE_PREFIXES } = require('../../redis/cacheService');

/**
 * @description Browse profiles with basic information
 * @route GET /api/users/browse
 */
exports.browseProfiles = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Get basic profile information for browsing
        const profiles = await prisma.user.findMany({
            where: {
                id: { not: userId }, // Exclude current user
                profileStatus: 'ACTIVE', // Only show active profiles
            },
            select: {
                id: true,
                isVerified: true,
                profile: {
                    select: {
                        fullName: true,
                        gender: true,
                        dateOfBirth: true,
                        city: true,
                        occupation: true
                    }
                },
                photos: {
                    where: {
                        isProfilePic: true,
                        visibility: 'PUBLIC'
                    },
                    select: { url: true },
                    take: 1
                }
            },
            take: 20
        });

        // Format profiles for response
        const formattedProfiles = profiles.map(profile => ({
            id: profile.id,
            isVerified: profile.isVerified,
            fullName: profile.profile?.fullName || 'User',
            age: profile.profile?.dateOfBirth ? calculateAge(profile.profile.dateOfBirth) : null,
            city: profile.profile?.city || 'Unknown',
            occupation: profile.profile?.occupation || 'Not specified',
            profilePicUrl: profile.photos?.[0]?.url || null
        }));

        res.status(200).json({
            success: true,
            profiles: formattedProfiles,
            total: formattedProfiles.length
        });
    } catch (error) {
        console.error('Error browsing profiles:', error);
        next(error);
    }
};

/**
 * @description Search profiles with basic filters
 * @route GET /api/users/search
 */
exports.searchProfiles = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;
    const {
        gender,
        minAge,
        maxAge,
        city,
        page = 1,
        limit = 20,
        skipCache = false
    } = req.query;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Create a cache key based on search parameters
        const searchParams = {
            userId,
            gender,
            minAge,
            maxAge,
            city,
            page,
            limit
        };

        const cacheKey = `${CACHE_PREFIXES.SEARCH_RESULTS}${JSON.stringify(searchParams)}`;

        // Skip cache if requested
        if (skipCache === 'true') {
            logger.info(`Skipping cache for search: ${cacheKey}`);
        }

        // Use cache for search results unless skipCache is true
        if (skipCache !== 'true') {
            const cachedResponse = await getOrSetCache(
                cacheKey,
                async () => {
                    return await executeSearch();
                },
                300 // Cache for 5 minutes
            );

            // Add cache indicator to response
            return res.status(200).json({
                ...cachedResponse,
                fromCache: true
            });
        } else {
            // Execute search without caching
            const response = await executeSearch();
            return res.status(200).json({
                ...response,
                fromCache: false
            });
        }

        // Function to execute the search
        async function executeSearch() {
            // Calculate pagination
            const pageNum = parseInt(page, 10);
            const limitNum = parseInt(limit, 10);
            const skip = (pageNum - 1) * limitNum;

            // Build search filters
            const filters = {
                id: { not: userId }, // Exclude current user
                profileStatus: 'ACTIVE', // Only show active profiles
            };

        // Add gender filter if provided
        if (gender) {
            filters.profile = {
                ...filters.profile,
                gender: gender
            };
        }

        // Add age filters if provided
        if (minAge || maxAge) {
            const today = new Date();

            if (minAge) {
                const maxBirthDate = new Date(today);
                maxBirthDate.setFullYear(today.getFullYear() - parseInt(minAge));

                filters.profile = {
                    ...filters.profile,
                    dateOfBirth: {
                        ...filters.profile?.dateOfBirth,
                        lte: maxBirthDate
                    }
                };
            }

            if (maxAge) {
                const minBirthDate = new Date(today);
                minBirthDate.setFullYear(today.getFullYear() - parseInt(maxAge) - 1);
                minBirthDate.setDate(minBirthDate.getDate() + 1);

                filters.profile = {
                    ...filters.profile,
                    dateOfBirth: {
                        ...filters.profile?.dateOfBirth,
                        gte: minBirthDate
                    }
                };
            }
        }

        // Add city filter if provided
        if (city) {
            filters.profile = {
                ...filters.profile,
                city: {
                    contains: city,
                    mode: 'insensitive'
                }
            };
        }

        // Get total count for pagination
        const totalCount = await prisma.user.count({
            where: filters
        });

        // Execute search with pagination
        const profiles = await prisma.user.findMany({
            where: filters,
            select: {
                id: true,
                isVerified: true,
                isPremium: true,
                createdAt: true,
                profile: {
                    select: {
                        fullName: true,
                        gender: true,
                        dateOfBirth: true,
                        city: true,
                        occupation: true,
                        education: true,
                        religion: true,
                        caste: true,
                        height: true
                    }
                },
                photos: {
                    where: {
                        isProfilePic: true,
                        visibility: 'PUBLIC'
                    },
                    select: { url: true, id: true },
                    take: 1
                },
                spotlightFeatures: {
                    where: {
                        endTime: {
                            gt: new Date()
                        }
                    },
                    select: {
                        id: true,
                        endTime: true,
                        feature: {
                            select: {
                                name: true,
                                displayName: true
                            }
                        }
                    },
                    take: 1
                }
            },
            orderBy: [
                // Spotlight profiles first
                {
                    spotlightFeatures: {
                        _count: 'desc'
                    }
                },
                // Then premium users
                { isPremium: 'desc' },
                // Then newest profiles
                { createdAt: 'desc' }
            ],
            skip,
            take: limitNum
        });

        // Format profiles for response
        const formattedProfiles = profiles.map(profile => ({
            id: profile.id,
            isVerified: profile.isVerified,
            isPremium: profile.isPremium,
            fullName: profile.profile?.fullName || 'User',
            age: profile.profile?.dateOfBirth ? calculateAge(profile.profile.dateOfBirth) : null,
            height: profile.profile?.height || null,
            city: profile.profile?.city || 'Unknown',
            education: profile.profile?.education || null,
            occupation: profile.profile?.occupation || 'Not specified',
            religion: profile.profile?.religion || null,
            caste: profile.profile?.caste || null,
            profilePicUrl: profile.photos?.[0]?.url || null,
            photoId: profile.photos?.[0]?.id || null,
            isSpotlighted: profile.spotlightFeatures?.length > 0,
            spotlightEndTime: profile.spotlightFeatures?.[0]?.endTime || null,
            spotlightFeature: profile.spotlightFeatures?.[0]?.feature?.displayName || null,
            createdAt: profile.createdAt
        }));

            // Create pagination info
            const pagination = {
                currentPage: pageNum,
                totalPages: Math.ceil(totalCount / limitNum),
                totalResults: totalCount,
                resultsPerPage: limitNum
            };

            // Prepare response
            return {
                success: true,
                profiles: formattedProfiles,
                total: totalCount,
                pagination
            };
        }
    } catch (error) {
        logger.error('Error searching profiles:', error);
        next(error);
    }
};

/**
 * @description View a specific user's profile
 * @route GET /api/users/view-profile/:userId
 */
exports.viewUserProfile = async (req, res, next) => {
    const prisma = req.prisma;
    const currentUserId = req.user?.userId;
    const { userId } = req.params;

    if (!currentUserId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    if (!userId) {
        const error = new Error('User ID parameter is required.');
        error.status = 400;
        return next(error);
    }

    try {
        // Get user profile
        const userProfile = await prisma.user.findUnique({
            where: { id: userId },
            select: {
                id: true,
                isVerified: true,
                profileStatus: true,
                profile: {
                    select: {
                        fullName: true,
                        gender: true,
                        dateOfBirth: true,
                        birthPlace: true,
                        height: true,
                        city: true,
                        education: true,
                        occupation: true,
                        incomeRange: true
                    }
                },
                photos: {
                    where: {
                        visibility: 'PUBLIC'
                    },
                    select: {
                        id: true,
                        url: true,
                        isProfilePic: true
                    },
                    orderBy: { isProfilePic: 'desc' }
                }
            }
        });

        if (!userProfile) {
            const error = new Error('User profile not found.');
            error.status = 404;
            return next(error);
        }

        // Format profile for response
        const formattedProfile = {
            id: userProfile.id,
            isVerified: userProfile.isVerified,
            fullName: userProfile.profile?.fullName || 'User',
            age: userProfile.profile?.dateOfBirth ? calculateAge(userProfile.profile.dateOfBirth) : null,
            gender: userProfile.profile?.gender || null,
            birthPlace: userProfile.profile?.birthPlace || null,
            height: userProfile.profile?.height || null,
            city: userProfile.profile?.city || 'Unknown',
            education: userProfile.profile?.education || null,
            occupation: userProfile.profile?.occupation || 'Not specified',
            incomeRange: userProfile.profile?.incomeRange || null,
            photos: userProfile.photos || []
        };

        res.status(200).json({
            success: true,
            profile: formattedProfile
        });
    } catch (error) {
        console.error('Error viewing user profile:', error);
        next(error);
    }
};

/**
 * @description Get connections for the current user
 * @route GET /api/users/connections
 */
exports.getConnections = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Get user verification status
        const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { isVerified: true }
        });

        // Determine limit based on verification status
        const limit = req.limit || (user.isVerified ? 20 : 3);

        // Get connections (simplified example - in a real app, you'd have a Connection model)
        const connections = await prisma.match.findMany({
            where: {
                OR: [
                    { user1Id: userId },
                    { user2Id: userId }
                ],
                status: 'ACCEPTED'
            },
            include: {
                user1: {
                    select: {
                        id: true,
                        isVerified: true,
                        profile: {
                            select: {
                                fullName: true,
                                gender: true,
                                dateOfBirth: true,
                                city: true
                            }
                        },
                        photos: {
                            where: { isProfilePic: true },
                            select: { url: true },
                            take: 1
                        }
                    }
                },
                user2: {
                    select: {
                        id: true,
                        isVerified: true,
                        profile: {
                            select: {
                                fullName: true,
                                gender: true,
                                dateOfBirth: true,
                                city: true
                            }
                        },
                        photos: {
                            where: { isProfilePic: true },
                            select: { url: true },
                            take: 1
                        }
                    }
                }
            },
            orderBy: { updatedAt: 'desc' },
            take: limit
        });

        // Format connections for response
        const formattedConnections = connections.map(connection => {
            // Determine which user is the connection (not the current user)
            const connectionUser = connection.user1Id === userId ? connection.user2 : connection.user1;

            return {
                connectionId: connection.id,
                userId: connectionUser.id,
                isVerified: connectionUser.isVerified,
                fullName: connectionUser.profile?.fullName || 'User',
                age: connectionUser.profile?.dateOfBirth ? calculateAge(connectionUser.profile.dateOfBirth) : null,
                city: connectionUser.profile?.city || 'Unknown',
                profilePicUrl: connectionUser.photos?.[0]?.url || null,
                connectedAt: connection.updatedAt
            };
        });

        // Add verification notice for unverified users
        const verificationNotice = !user.isVerified ? {
            message: `You're seeing ${limit} connections. Verify your profile to see more connections.`,
            verificationRequired: true,
            currentLimit: limit,
            fullLimit: 20
        } : null;

        res.status(200).json({
            success: true,
            connections: formattedConnections,
            total: formattedConnections.length,
            verificationNotice
        });
    } catch (error) {
        console.error('Error fetching connections:', error);
        next(error);
    }
};

/**
 * @description Get messages for the current user
 * @route GET /api/users/messages
 */
exports.getMessages = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Get user verification status
        const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { isVerified: true }
        });

        // Determine limit based on verification status
        const limit = req.limit || (user.isVerified ? 50 : 10);

        // For this example, we'll return a placeholder response
        // In a real app, you'd query your messages table

        res.status(200).json({
            success: true,
            messages: [],
            total: 0,
            verificationNotice: !user.isVerified ? {
                message: `You can view up to ${limit} messages. Verify your profile for unlimited messaging.`,
                verificationRequired: true,
                currentLimit: limit,
                fullLimit: 'unlimited'
            } : null
        });
    } catch (error) {
        console.error('Error fetching messages:', error);
        next(error);
    }
};

/**
 * @description Advanced search with more filters (premium feature)
 * @route GET /api/users/advanced-search
 */
exports.advancedSearch = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;
    const {
        // Basic filters
        gender,
        minAge,
        maxAge,
        city,

        // Advanced filters
        religion,
        caste,
        subCaste,
        education,
        occupation,
        incomeRange,
        maritalStatus,
        heightFrom,
        heightTo,
        diet,
        smoking,
        drinking,
        gotra,
        manglik,

        // Pagination
        page = 1,
        limit = 20,

        // Cache control
        skipCache = false
    } = req.query;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Check if user is premium
        const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { isPremium: true }
        });

        // If not premium, return upgrade message
        if (!user?.isPremium) {
            return res.status(403).json({
                success: false,
                message: 'Advanced search is a premium feature',
                upgradeRequired: true,
                featureName: 'advanced-search'
            });
        }

        // Create a cache key based on all search parameters
        const searchParams = {
            userId,
            gender,
            minAge,
            maxAge,
            city,
            religion,
            caste,
            subCaste,
            education,
            occupation,
            incomeRange,
            maritalStatus,
            heightFrom,
            heightTo,
            diet,
            smoking,
            drinking,
            gotra,
            manglik,
            page,
            limit
        };

        const cacheKey = redisClient.generateSearchCacheKey(searchParams);

        // Try to get results from cache first (unless skipCache is true)
        if (!skipCache) {
            const cachedResults = await redisClient.getCache(cacheKey);
            if (cachedResults) {
                logger.info(`Advanced search results found in cache for key: ${cacheKey}`);

                // Add cache indicator to response
                return res.status(200).json({
                    success: true,
                    profiles: cachedResults.profiles,
                    total: cachedResults.total,
                    pagination: cachedResults.pagination,
                    fromCache: true
                });
            }
        }

        // Calculate pagination
        const pageNum = parseInt(page, 10);
        const limitNum = parseInt(limit, 10);
        const skip = (pageNum - 1) * limitNum;

        // Build advanced search filters
        const filters = {
            id: { not: userId }, // Exclude current user
            profileStatus: 'ACTIVE', // Only show active profiles
            profile: {}
        };

        // Add basic filters
        if (gender) {
            filters.profile.gender = gender;
        }

        // Add age filters
        if (minAge || maxAge) {
            const today = new Date();
            filters.profile.dateOfBirth = {};

            if (minAge) {
                const maxBirthDate = new Date(today);
                maxBirthDate.setFullYear(today.getFullYear() - parseInt(minAge));
                filters.profile.dateOfBirth.lte = maxBirthDate;
            }

            if (maxAge) {
                const minBirthDate = new Date(today);
                minBirthDate.setFullYear(today.getFullYear() - parseInt(maxAge) - 1);
                minBirthDate.setDate(minBirthDate.getDate() + 1);
                filters.profile.dateOfBirth.gte = minBirthDate;
            }
        }

        // Add city filter
        if (city) {
            filters.profile.city = {
                contains: city,
                mode: 'insensitive'
            };
        }

        // Add religion filter (flexible - include similar religions)
        if (religion && religion !== 'ANY') {
            if (religion === 'Hindu') {
                // Include all Hindu denominations
                filters.profile.religion = {
                    in: ['Hindu', 'Jain', 'Buddhist', 'Sikh']
                };
            } else {
                filters.profile.religion = religion;
            }
        }

        // Add caste filter (flexible - include related castes)
        if (caste && caste !== 'ANY') {
            if (caste === 'Maratha') {
                // Include all Maratha sub-communities
                filters.profile.caste = {
                    in: ['Maratha', 'Kunbi', 'Mali']
                };
            } else {
                filters.profile.caste = caste;
            }
        }

        // Add subcaste filter (optional - make it a preference, not requirement)
        if (subCaste && subCaste !== 'ANY') {
            // Store as preference for scoring, don't filter out
            filters.profile.subCaste = subCaste;
        }

        // Add education filter
        if (education && education.length > 0) {
            filters.profile.education = {
                in: Array.isArray(education) ? education : [education]
            };
        }

        // Add occupation filter
        if (occupation && occupation.length > 0) {
            filters.profile.occupation = {
                in: Array.isArray(occupation) ? occupation : [occupation]
            };
        }

        // Add income range filter
        if (incomeRange && incomeRange !== 'ANY') {
            filters.profile.incomeRange = incomeRange;
        }

        // Add marital status filter
        if (maritalStatus && maritalStatus.length > 0) {
            filters.profile.maritalStatus = {
                in: Array.isArray(maritalStatus) ? maritalStatus : [maritalStatus]
            };
        }

        // Add height range filter
        if (heightFrom || heightTo) {
            filters.profile.height = {};

            if (heightFrom) {
                filters.profile.height.gte = parseFloat(heightFrom);
            }

            if (heightTo) {
                filters.profile.height.lte = parseFloat(heightTo);
            }
        }

        // Add diet filter
        if (diet && diet !== 'ANY') {
            filters.profile.diet = diet;
        }

        // Add smoking filter
        if (smoking && smoking !== 'DOESNT_MATTER') {
            filters.profile.smoking = smoking;
        }

        // Add drinking filter
        if (drinking && drinking !== 'DOESNT_MATTER') {
            filters.profile.drinking = drinking;
        }

        // Add gotra filter
        if (gotra && gotra !== 'ANY') {
            filters.profile.gotra = gotra;
        }

        // Add manglik filter
        if (manglik && manglik !== 'DOESNT_MATTER') {
            filters.profile.manglik = manglik;
        }

        // Get total count for pagination
        const totalCount = await prisma.user.count({
            where: filters
        });

        // Execute advanced search with pagination
        const profiles = await prisma.user.findMany({
            where: filters,
            select: {
                id: true,
                isVerified: true,
                isPremium: true,
                createdAt: true,
                profile: {
                    select: {
                        fullName: true,
                        gender: true,
                        dateOfBirth: true,
                        city: true,
                        occupation: true,
                        education: true,
                        religion: true,
                        caste: true,
                        subCaste: true,
                        height: true,
                        maritalStatus: true,
                        diet: true,
                        smoking: true,
                        drinking: true,
                        gotra: true,
                        manglik: true,
                        incomeRange: true
                    }
                },
                photos: {
                    where: {
                        isProfilePic: true,
                        visibility: 'PUBLIC'
                    },
                    select: { url: true, id: true },
                    take: 1
                },
                spotlightFeatures: {
                    where: {
                        endTime: {
                            gt: new Date()
                        }
                    },
                    select: {
                        id: true,
                        endTime: true,
                        feature: {
                            select: {
                                name: true,
                                displayName: true
                            }
                        }
                    },
                    take: 1
                }
            },
            orderBy: [
                // Spotlight profiles first
                {
                    spotlightFeatures: {
                        _count: 'desc'
                    }
                },
                // Then premium users
                { isPremium: 'desc' },
                // Then newest profiles
                { createdAt: 'desc' }
            ],
            skip,
            take: limitNum
        });

        // Format profiles for response
        const formattedProfiles = profiles.map(profile => ({
            id: profile.id,
            isVerified: profile.isVerified,
            isPremium: profile.isPremium,
            fullName: profile.profile?.fullName || 'User',
            age: profile.profile?.dateOfBirth ? calculateAge(profile.profile.dateOfBirth) : null,
            height: profile.profile?.height || null,
            city: profile.profile?.city || 'Unknown',
            education: profile.profile?.education || null,
            occupation: profile.profile?.occupation || 'Not specified',
            religion: profile.profile?.religion || null,
            caste: profile.profile?.caste || null,
            subCaste: profile.profile?.subCaste || null,
            maritalStatus: profile.profile?.maritalStatus || null,
            diet: profile.profile?.diet || null,
            smoking: profile.profile?.smoking || null,
            drinking: profile.profile?.drinking || null,
            gotra: profile.profile?.gotra || null,
            manglik: profile.profile?.manglik || null,
            incomeRange: profile.profile?.incomeRange || null,
            profilePicUrl: profile.photos?.[0]?.url || null,
            photoId: profile.photos?.[0]?.id || null,
            isSpotlighted: profile.spotlightFeatures?.length > 0,
            spotlightEndTime: profile.spotlightFeatures?.[0]?.endTime || null,
            spotlightFeature: profile.spotlightFeatures?.[0]?.feature?.displayName || null,
            createdAt: profile.createdAt
        }));

        // Create pagination info
        const pagination = {
            currentPage: pageNum,
            totalPages: Math.ceil(totalCount / limitNum),
            totalResults: totalCount,
            resultsPerPage: limitNum
        };

        // Prepare response
        const response = {
            success: true,
            profiles: formattedProfiles,
            total: totalCount,
            pagination,
            fromCache: false
        };

        // Cache the results for 5 minutes (300 seconds)
        await redisClient.setCache(cacheKey, response, 300);
        logger.info(`Cached advanced search results for key: ${cacheKey}`);

        res.status(200).json(response);
    } catch (error) {
        logger.error('Error performing advanced search:', error);
        next(error);
    }
};

/**
 * @description Get contact details for a specific user (premium feature)
 * @route GET /api/users/contact-details/:userId
 */
exports.getContactDetails = async (req, res, next) => {
    const prisma = req.prisma;
    const currentUserId = req.user?.userId;
    const { userId } = req.params;

    if (!currentUserId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    if (!userId) {
        const error = new Error('User ID parameter is required.');
        error.status = 400;
        return next(error);
    }

    try {
        // This is a premium feature, so we'll return a placeholder response
        // In a real app, you'd check if users have mutual interest before showing contact details

        res.status(200).json({
            success: true,
            contactDetails: {
                userId: userId,
                phone: '**********', // Masked for privacy
                email: '****@****.com', // Masked for privacy
                note: 'This is a placeholder. In a real app, actual contact details would be shown for verified users.'
            }
        });
    } catch (error) {
        console.error('Error fetching contact details:', error);
        next(error);
    }
};

/**
 * @description Get priority matches (premium feature)
 * @route GET /api/users/priority-matching
 */
exports.getPriorityMatches = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // This is a premium feature, so we'll return a placeholder response
        // In a real app, you'd implement priority matching algorithm

        res.status(200).json({
            success: true,
            priorityMatches: [],
            total: 0
        });
    } catch (error) {
        console.error('Error fetching priority matches:', error);
        next(error);
    }
};

/**
 * @description Get horoscope match details (premium feature)
 * @route GET /api/users/horoscope-matching/:userId
 */
exports.getHoroscopeMatch = async (req, res, next) => {
    const prisma = req.prisma;
    const currentUserId = req.user?.userId;
    const { userId } = req.params;

    if (!currentUserId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    if (!userId) {
        const error = new Error('User ID parameter is required.');
        error.status = 400;
        return next(error);
    }

    try {
        // This is a premium feature, so we'll return a placeholder response
        // In a real app, you'd calculate horoscope compatibility

        res.status(200).json({
            success: true,
            horoscopeMatch: {
                overallCompatibility: '75%',
                aspects: [
                    { name: 'Emotional Compatibility', score: '80%' },
                    { name: 'Communication', score: '70%' },
                    { name: 'Trust & Values', score: '85%' },
                    { name: 'Physical Attraction', score: '65%' }
                ],
                note: 'This is a placeholder. In a real app, actual horoscope compatibility would be calculated.'
            }
        });
    } catch (error) {
        console.error('Error calculating horoscope match:', error);
        next(error);
    }
};

/**
 * @description Edit user profile
 * @route GET /api/users/edit-profile
 */
exports.editUserProfile = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // Get user profile for editing
        const user = await prisma.user.findUnique({
            where: { id: userId },
            include: {
                profile: true
            }
        });

        if (!user) {
            const error = new Error('User not found.');
            error.status = 404;
            return next(error);
        }

        res.status(200).json({
            success: true,
            user: {
                id: user.id,
                phone: user.phone,
                email: user.email,
                isVerified: user.isVerified,
                profileStatus: user.profileStatus,
                profile: user.profile
            }
        });
    } catch (error) {
        console.error('Error fetching user profile for editing:', error);
        next(error);
    }
};

/**
 * @description Send a message to another user
 * @route POST /api/users/send-message
 */
exports.sendMessage = async (req, res, next) => {
    const prisma = req.prisma;
    const senderId = req.user?.userId;
    const { receiverId, content } = req.body;

    if (!senderId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    if (!receiverId) {
        const error = new Error('Receiver ID is required.');
        error.status = 400;
        return next(error);
    }

    if (!content || content.trim() === '') {
        const error = new Error('Message content cannot be empty.');
        error.status = 400;
        return next(error);
    }

    try {
        // Check if there's a connection between the users
        const connection = await prisma.match.findFirst({
            where: {
                OR: [
                    { user1Id: senderId, user2Id: receiverId },
                    { user1Id: receiverId, user2Id: senderId }
                ],
                status: 'ACCEPTED'
            }
        });

        if (!connection) {
            return res.status(403).json({
                success: false,
                message: "You can only send messages to users you've connected with."
            });
        }

        // Create the message
        const message = await prisma.message.create({
            data: {
                senderId,
                receiverId,
                content,
                sentAt: new Date()
            }
        });

        // Update the match's lastMessageAt
        await prisma.match.update({
            where: { id: connection.id },
            data: { lastMessageAt: new Date() }
        });

        res.status(201).json({
            success: true,
            message: {
                id: message.id,
                content: message.content,
                sentAt: message.sentAt,
                isRead: message.isRead
            }
        });
    } catch (error) {
        console.error('Error sending message:', error);
        next(error);
    }
};

/**
 * @description Boost user profile (premium feature)
 * @route POST /api/users/profile-boost
 */
exports.boostProfile = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // This is a premium feature
        // In a real app, you'd implement profile boosting logic

        // Update user's boost status
        await prisma.user.update({
            where: { id: userId },
            data: {
                // Add a boostedUntil field to your User model
                // boostedUntil: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
            }
        });

        res.status(200).json({
            success: true,
            message: "Your profile has been boosted for 24 hours!",
            boostExpiry: new Date(Date.now() + 24 * 60 * 60 * 1000)
        });
    } catch (error) {
        console.error('Error boosting profile:', error);
        next(error);
    }
};

/**
 * @description Toggle incognito browsing (premium feature)
 * @route POST /api/users/incognito-browsing
 */
exports.toggleIncognitoBrowsing = async (req, res, next) => {
    const prisma = req.prisma;
    const userId = req.user?.userId;
    const { enabled } = req.body;

    if (!userId) {
        const error = new Error('Authentication error: User ID missing.');
        error.status = 401;
        return next(error);
    }

    try {
        // This is a premium feature
        // In a real app, you'd implement incognito browsing logic

        // Update user's incognito status
        await prisma.user.update({
            where: { id: userId },
            data: {
                // Add an isIncognito field to your User model
                // isIncognito: enabled
            }
        });

        res.status(200).json({
            success: true,
            message: enabled ? "Incognito browsing enabled." : "Incognito browsing disabled.",
            isIncognito: enabled
        });
    } catch (error) {
        console.error('Error toggling incognito browsing:', error);
        next(error);
    }
};

// Helper function to calculate age from date of birth
function calculateAge(dateOfBirth) {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }

    return age;
}
