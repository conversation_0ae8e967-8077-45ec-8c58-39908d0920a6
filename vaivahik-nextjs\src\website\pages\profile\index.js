import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Button,
  Card,
  CardContent,
  CardActions,
  Divider,
  Avatar,
  Chip,
  CircularProgress,
  Alert,
  Tabs,
  Tab
} from '@mui/material';
import {
  Edit as EditIcon,
  Person as PersonIcon,
  School as EducationIcon,
  Work as WorkIcon,
  LocationOn as LocationIcon,
  Favorite as HeartIcon,
  Restaurant as DiningIcon,
  SportsBasketball as HobbyIcon
} from '@mui/icons-material';
import InterestsDisplay from '@/website/components/profile/InterestsDisplay';
import SmartCallButton from '@/components/contact/SmartCallButton';
import { isUsingRealBackend } from '@/utils/featureFlags';

// Mock user data for development
const MOCK_USER_DATA = {
  id: 'user123',
  fullName: '<PERSON><PERSON>',
  gender: 'MALE',
  dateOfBirth: '1990-05-15',
  age: 33,
  email: '<EMAIL>',
  phone: '+91 9876543210',
  profileCompletionPercentage: 85,

  // Personal details
  height: '5.10',
  religion: 'Hindu',
  caste: '<PERSON>tha',
  subCaste: '96 Kuli Maratha',
  maritalStatus: 'NEVER_MARRIED',

  // Location
  city: 'Mumbai',
  state: 'Maharashtra',
  country: 'India',

  // Education & Career
  education: 'Master\'s Degree',
  occupation: 'Software Engineer',
  company: 'Tech Solutions Ltd',
  income: '10L_20L',

  // Lifestyle
  diet: 'VEGETARIAN',
  smoking: 'NO',
  drinking: 'OCCASIONALLY',
  hobbies: ['Reading', 'Traveling', 'Music', 'Photography', 'Cricket'],
  interests: 'I enjoy watching documentaries about history and science. I also like to explore new cuisines and restaurants.',

  // About
  aboutMe: 'I am a software engineer with a passion for technology and innovation. I enjoy spending time with family and friends, traveling to new places, and learning about different cultures. Looking for a life partner who shares similar values and interests.',

  // Photos
  profilePicUrl: 'https://randomuser.me/api/portraits/men/32.jpg',

  // Preferences
  preferences: {
    ageMin: 25,
    ageMax: 32,
    heightMin: '5.0',
    heightMax: '5.8',
    educationLevel: ['Bachelor\'s Degree', 'Master\'s Degree'],
    occupations: ['Software Engineer', 'Teacher', 'Doctor'],
    incomeMin: '5L_10L',
    preferredCities: ['Mumbai', 'Pune'],
    preferredStates: ['Maharashtra'],
    acceptSubCastes: ['96 Kuli Maratha', 'Deshmukh'],
    gotraPreference: '',
    dietPreference: 'VEGETARIAN'
  }
};

export default function ProfilePage() {
  const router = useRouter();
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState(0);

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      setLoading(true);
      setError('');

      try {
        if (isUsingRealBackend()) {
          // Call real API
          const response = await fetch('/api/user/profile', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          });

          if (!response.ok) {
            throw new Error('Failed to fetch user data');
          }

          const data = await response.json();
          setUserData(data);
        } else {
          // Use mock data
          setTimeout(() => {
            setUserData(MOCK_USER_DATA);
            setLoading(false);
          }, 500); // Simulate API delay
        }
      } catch (err) {
        console.error('Error fetching user data:', err);
        setError('Failed to load your profile data. Please try again later.');
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Handle contact reveal success
  const handleCallInitiated = (data) => {
    console.log('Contact revealed:', data);

    // Track in analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'contact_reveal_success', {
        event_category: 'User_Engagement',
        event_label: userData?.id,
        custom_parameters: {
          target_user_id: data.targetUserId,
          access_reason: data.accessReason,
          platform: data.platform
        }
      });
    }

    // Show success notification (you can add a snackbar here)
    alert(`Contact revealed for ${data.targetUserName}! Opening dialer...`);
  };

  // Handle security blocks
  const handleSecurityBlock = (data) => {
    console.log('Security block:', data);

    // Track security events
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'security_block', {
        event_category: 'Security',
        event_label: data.reason,
        custom_parameters: {
          risk_score: data.riskScore,
          target_user_id: data.targetUserId
        }
      });
    }

    // Show security alert
    alert(`Access blocked for security reasons: ${data.reason}`);
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
        <CircularProgress />
        <Typography variant="body1" sx={{ mt: 2 }}>
          Loading your profile...
        </Typography>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button variant="contained" onClick={() => router.reload()}>
          Try Again
        </Button>
      </Container>
    );
  }

  return (
    <>
      <Head>
        <title>My Profile | Vaivahik</title>
        <meta name="description" content="View and edit your profile on Vaivahik matrimony" />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Grid container spacing={4}>
          {/* Profile Header */}
          <Grid item xs={12}>
            <Paper elevation={3} sx={{ p: 3, borderRadius: 2 }}>
              <Grid container spacing={3} alignItems="center">
                <Grid item xs={12} sm={3} md={2} sx={{ textAlign: 'center' }}>
                  <Avatar
                    src={userData?.profilePicUrl}
                    alt={userData?.fullName}
                    sx={{ width: 120, height: 120, mx: 'auto', border: '4px solid #f0f0f0' }}
                  />
                </Grid>
                <Grid item xs={12} sm={9} md={7}>
                  <Typography variant="h4" gutterBottom>
                    {userData?.fullName}
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 1 }}>
                    <Chip
                      icon={<LocationIcon fontSize="small" />}
                      label={`${userData?.city}, ${userData?.state}`}
                      size="small"
                    />
                    <Chip
                      icon={<WorkIcon fontSize="small" />}
                      label={userData?.occupation}
                      size="small"
                    />
                    <Chip
                      icon={<EducationIcon fontSize="small" />}
                      label={userData?.education}
                      size="small"
                    />
                  </Box>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    {userData?.aboutMe?.substring(0, 150)}
                    {userData?.aboutMe?.length > 150 ? '...' : ''}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={3} sx={{ display: 'flex', flexDirection: 'column', alignItems: { xs: 'center', md: 'flex-end' }, gap: 2 }}>
                  <Box>
                    <Typography variant="subtitle2" align="center" gutterBottom>
                      Profile Completion
                    </Typography>
                    <Box sx={{ position: 'relative', display: 'inline-flex' }}>
                      <CircularProgress
                        variant="determinate"
                        value={userData?.profileCompletionPercentage || 0}
                        size={80}
                        thickness={4}
                        sx={{ color: 'primary.main' }}
                      />
                      <Box
                        sx={{
                          top: 0,
                          left: 0,
                          bottom: 0,
                          right: 0,
                          position: 'absolute',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <Typography variant="caption" component="div" color="text.secondary">
                          {`${userData?.profileCompletionPercentage || 0}%`}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>

                  {/* Smart Call Button */}
                  <Box sx={{ mt: 2 }}>
                    <SmartCallButton
                      targetUserId={userData?.id}
                      targetUserName={userData?.fullName}
                      onCallInitiated={handleCallInitiated}
                      onSecurityBlock={handleSecurityBlock}
                      variant="contained"
                      size="medium"
                      fullWidth
                    />
                  </Box>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {/* Profile Navigation */}
          <Grid item xs={12}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                mb: 3,
                '& .MuiTab-root': {
                  minWidth: 120,
                  fontWeight: 'medium'
                }
              }}
            >
              <Tab label="Basic Info" />
              <Tab label="Lifestyle" />
              <Tab label="Partner Preferences" />
              <Tab label="Photos" />
            </Tabs>
          </Grid>

          {/* Profile Content */}
          <Grid item xs={12}>
            {activeTab === 0 && (
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6">Personal Information</Typography>
                    <Button
                      variant="outlined"
                      startIcon={<EditIcon />}
                      size="small"
                      component={Link}
                      href="/website/pages/profile/edit/personal"
                    >
                      Edit
                    </Button>
                  </Box>
                  <Divider sx={{ mb: 3 }} />

                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6} md={4}>
                      <Typography variant="subtitle2" color="text.secondary">Full Name</Typography>
                      <Typography variant="body1">{userData?.fullName}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <Typography variant="subtitle2" color="text.secondary">Age</Typography>
                      <Typography variant="body1">{userData?.age} years</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <Typography variant="subtitle2" color="text.secondary">Height</Typography>
                      <Typography variant="body1">{userData?.height} ft</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <Typography variant="subtitle2" color="text.secondary">Religion</Typography>
                      <Typography variant="body1">{userData?.religion}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <Typography variant="subtitle2" color="text.secondary">Caste</Typography>
                      <Typography variant="body1">{userData?.caste}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <Typography variant="subtitle2" color="text.secondary">Sub-Caste</Typography>
                      <Typography variant="body1">{userData?.subCaste || 'Not specified'}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <Typography variant="subtitle2" color="text.secondary">Marital Status</Typography>
                      <Typography variant="body1">
                        {userData?.maritalStatus?.replace('_', ' ').toLowerCase()
                          .split(' ')
                          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                          .join(' ')}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            )}

            {activeTab === 1 && (
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6">Lifestyle & Interests</Typography>
                    <Button
                      variant="outlined"
                      startIcon={<EditIcon />}
                      size="small"
                      component={Link}
                      href="/website/pages/profile/edit/lifestyle"
                    >
                      Edit
                    </Button>
                  </Box>
                  <Divider sx={{ mb: 3 }} />

                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6} md={4}>
                      <Typography variant="subtitle2" color="text.secondary">Diet Preference</Typography>
                      <Typography variant="body1">
                        {userData?.diet?.replace('_', ' ').toLowerCase()
                          .split(' ')
                          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                          .join(' ')}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <Typography variant="subtitle2" color="text.secondary">Smoking</Typography>
                      <Typography variant="body1">{userData?.smoking}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <Typography variant="subtitle2" color="text.secondary">Drinking</Typography>
                      <Typography variant="body1">{userData?.drinking}</Typography>
                    </Grid>
                  </Grid>

                  <Box sx={{ mt: 3 }}>
                    <InterestsDisplay
                      hobbies={userData?.hobbies || []}
                      interests={userData?.interests || ''}
                      showTitle={true}
                    />
                  </Box>
                </CardContent>
              </Card>
            )}

            {activeTab === 2 && (
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6">Partner Preferences</Typography>
                    <Button
                      variant="outlined"
                      startIcon={<EditIcon />}
                      size="small"
                      component={Link}
                      href="/website/pages/profile/edit/preferences"
                    >
                      Edit
                    </Button>
                  </Box>
                  <Divider sx={{ mb: 3 }} />

                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6} md={4}>
                      <Typography variant="subtitle2" color="text.secondary">Age Range</Typography>
                      <Typography variant="body1">
                        {userData?.preferences?.ageMin} - {userData?.preferences?.ageMax} years
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <Typography variant="subtitle2" color="text.secondary">Height Range</Typography>
                      <Typography variant="body1">
                        {userData?.preferences?.heightMin} - {userData?.preferences?.heightMax} ft
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <Typography variant="subtitle2" color="text.secondary">Diet Preference</Typography>
                      <Typography variant="body1">
                        {userData?.preferences?.dietPreference?.replace('_', ' ').toLowerCase()
                          .split(' ')
                          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                          .join(' ') || 'Any'}
                      </Typography>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" color="text.secondary">Education</Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                        {userData?.preferences?.educationLevel?.map((edu, index) => (
                          <Chip key={index} label={edu} size="small" />
                        )) || <Typography variant="body2">Not specified</Typography>}
                      </Box>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" color="text.secondary">Occupation</Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                        {userData?.preferences?.occupations?.map((occ, index) => (
                          <Chip key={index} label={occ} size="small" />
                        )) || <Typography variant="body2">Not specified</Typography>}
                      </Box>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" color="text.secondary">Preferred Cities</Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                        {userData?.preferences?.preferredCities?.map((city, index) => (
                          <Chip key={index} label={city} size="small" />
                        )) || <Typography variant="body2">Not specified</Typography>}
                      </Box>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" color="text.secondary">Preferred States</Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                        {userData?.preferences?.preferredStates?.map((state, index) => (
                          <Chip key={index} label={state} size="small" />
                        )) || <Typography variant="body2">Not specified</Typography>}
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            )}

            {activeTab === 3 && (
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6">Photos</Typography>
                    <Button
                      variant="outlined"
                      startIcon={<EditIcon />}
                      size="small"
                      component={Link}
                      href="/website/pages/profile/edit/photos"
                    >
                      Edit
                    </Button>
                  </Box>
                  <Divider sx={{ mb: 3 }} />

                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="body1" color="text.secondary">
                      Photo management will be implemented soon.
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            )}
          </Grid>
        </Grid>
      </Container>
    </>
  );
}
