/**
 * Policy Management Admin Page
 * Dynamic editing of Privacy Policy, Terms & Conditions, Refund Policy
 */

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Tabs,
  Tab,
  TextField,
  Alert,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Save as SaveIcon,
  Preview as PreviewIcon,
  History as HistoryIcon,
  Publish as PublishIcon,
  Edit as EditIcon,
  Gavel as LegalIcon
} from '@mui/icons-material';
import { ToastContainer, toast } from 'react-toastify';
import axios from 'axios';

// Import EnhancedAdminLayout with SSR disabled
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);

// Rich text editor (you can replace with your preferred editor)
const RichTextEditor = ({ value, onChange, placeholder }) => (
  <TextField
    fullWidth
    multiline
    rows={20}
    value={value}
    onChange={(e) => onChange(e.target.value)}
    placeholder={placeholder}
    variant="outlined"
    sx={{
      '& .MuiInputBase-root': {
        fontFamily: 'monospace',
        fontSize: '0.9rem'
      }
    }}
  />
);

export default function PolicyManagement() {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [previewDialog, setPreviewDialog] = useState(false);
  const [policies, setPolicies] = useState({
    privacyPolicy: {
      id: 'privacy-policy',
      title: 'Privacy Policy',
      content: '',
      lastUpdated: new Date().toISOString(),
      version: '1.0',
      status: 'draft',
      isPublished: false
    },
    termsOfService: {
      id: 'terms-of-service',
      title: 'Terms of Service',
      content: '',
      lastUpdated: new Date().toISOString(),
      version: '1.0',
      status: 'draft',
      isPublished: false
    },
    refundPolicy: {
      id: 'refund-policy',
      title: 'Refund Policy',
      content: '',
      lastUpdated: new Date().toISOString(),
      version: '1.0',
      status: 'draft',
      isPublished: false
    }
  });

  const policyTabs = [
    { key: 'privacyPolicy', label: 'Privacy Policy', icon: '🔒' },
    { key: 'termsOfService', label: 'Terms of Service', icon: '📋' },
    { key: 'refundPolicy', label: 'Refund Policy', icon: '💰' }
  ];

  const currentPolicy = policyTabs[activeTab];
  const currentPolicyData = policies[currentPolicy.key];

  useEffect(() => {
    fetchPolicies();
  }, []);

  const fetchPolicies = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/admin/policies');
      
      if (response.data.success) {
        setPolicies(response.data.policies);
      } else {
        // Load default templates if no policies exist
        loadDefaultTemplates();
      }
    } catch (error) {
      console.error('Error fetching policies:', error);
      loadDefaultTemplates();
    } finally {
      setLoading(false);
    }
  };

  const loadDefaultTemplates = () => {
    setPolicies(prev => ({
      ...prev,
      privacyPolicy: {
        ...prev.privacyPolicy,
        content: getDefaultPrivacyPolicy()
      },
      termsOfService: {
        ...prev.termsOfService,
        content: getDefaultTermsOfService()
      },
      refundPolicy: {
        ...prev.refundPolicy,
        content: getDefaultRefundPolicy()
      }
    }));
  };

  const savePolicy = async (policyKey) => {
    try {
      setSaving(true);
      const policyData = policies[policyKey];
      
      const response = await axios.post('/api/admin/policies', {
        policyId: policyData.id,
        content: policyData.content,
        title: policyData.title,
        version: policyData.version
      });
      
      if (response.data.success) {
        setPolicies(prev => ({
          ...prev,
          [policyKey]: {
            ...prev[policyKey],
            lastUpdated: new Date().toISOString(),
            status: 'saved'
          }
        }));
        toast.success(`${policyData.title} saved successfully`);
      } else {
        throw new Error(response.data.message);
      }
    } catch (error) {
      console.error('Error saving policy:', error);
      toast.error(`Failed to save ${policies[policyKey].title}`);
    } finally {
      setSaving(false);
    }
  };

  const publishPolicy = async (policyKey) => {
    try {
      const policyData = policies[policyKey];
      
      const response = await axios.post('/api/admin/policies/publish', {
        policyId: policyData.id
      });
      
      if (response.data.success) {
        setPolicies(prev => ({
          ...prev,
          [policyKey]: {
            ...prev[policyKey],
            isPublished: true,
            status: 'published',
            version: incrementVersion(prev[policyKey].version)
          }
        }));
        toast.success(`${policyData.title} published successfully`);
      } else {
        throw new Error(response.data.message);
      }
    } catch (error) {
      console.error('Error publishing policy:', error);
      toast.error(`Failed to publish ${policies[policyKey].title}`);
    }
  };

  const updatePolicyContent = (policyKey, content) => {
    setPolicies(prev => ({
      ...prev,
      [policyKey]: {
        ...prev[policyKey],
        content,
        status: 'modified'
      }
    }));
  };

  const incrementVersion = (version) => {
    const parts = version.split('.');
    const minor = parseInt(parts[1]) + 1;
    return `${parts[0]}.${minor}`;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'published': return 'success';
      case 'saved': return 'info';
      case 'modified': return 'warning';
      case 'draft': return 'default';
      default: return 'default';
    }
  };

  const getDefaultPrivacyPolicy = () => `# Privacy Policy

**Last Updated:** ${new Date().toLocaleDateString()}

## Information We Collect

We collect information you provide directly to us, such as when you create an account, fill out a profile, or contact us.

### Personal Information
- Name, age, gender, contact information
- Profile information including education, occupation, family details
- Birth details for horoscope matching
- Photos and other media you upload

### Technical Information
- Device information, IP address, browser type
- Usage data and interaction patterns
- Location data (with your consent)

## How We Use Your Information

We use the information we collect to:
- Provide and improve our matrimony services
- Find and suggest compatible matches
- Enable communication between members
- Ensure platform safety and security
- Send important updates and notifications

## Information Sharing

We never sell your personal information. We may share information only:
- With other members (controlled by your privacy settings)
- With service providers under strict confidentiality
- When required by law or to protect rights and safety

## Your Rights

You have the right to:
- Access and download your personal data
- Correct inaccurate information
- Delete your account and data
- Control your privacy settings
- Withdraw consent for optional processing

## Contact Us

For privacy questions: <EMAIL>`;

  const getDefaultTermsOfService = () => `# Terms of Service

**Last Updated:** ${new Date().toLocaleDateString()}

## Acceptance of Terms

By using Vaivahik matrimony platform, you agree to these terms and our Privacy Policy.

## Eligibility

To use our service, you must:
- Be at least 18 years old (females) or 21 years old (males)
- Be legally eligible for marriage
- Provide accurate and truthful information
- Not be prohibited from using the service

## User Responsibilities

You agree to:
- Provide accurate profile information
- Use the platform solely for matrimonial purposes
- Treat all members with respect
- Not engage in fraudulent or inappropriate behavior
- Respect privacy and confidentiality

## Prohibited Activities

The following are strictly prohibited:
- Creating fake profiles or providing false information
- Harassment or inappropriate behavior
- Commercial use or advertising
- Data mining or collecting user information
- Multiple accounts for the same person

## Premium Services

Premium subscriptions are billed in advance and auto-renew unless disabled. Refunds are subject to our refund policy.

## Disclaimers

We provide a platform to meet potential partners but cannot guarantee marriage outcomes. Users are responsible for their own due diligence.

## Contact Us

For support: <EMAIL>`;

  const getDefaultRefundPolicy = () => `# Refund Policy

**Last Updated:** ${new Date().toLocaleDateString()}

## Overview

This refund policy outlines the terms and conditions for refunds on Vaivahik matrimony platform.

## Premium Subscription Refunds

### Eligibility for Refunds
- Refunds may be requested within 7 days of purchase
- Account must not have been used extensively
- No successful matches or meaningful interactions

### Non-Refundable Situations
- Subscriptions used for more than 7 days
- Accounts with successful matches or extensive usage
- Violation of terms of service
- Technical issues resolved within reasonable time

## Process for Requesting Refunds

1. Contact our support <NAME_EMAIL>
2. Provide your account details and reason for refund
3. Allow 5-7 business days for review
4. Refunds will be processed to the original payment method

## Partial Refunds

In certain circumstances, we may offer partial refunds:
- Technical issues affecting service quality
- Significant changes to service features
- Exceptional circumstances at our discretion

## Processing Time

- Refund approval: 5-7 business days
- Processing to payment method: 7-14 business days
- Bank processing may take additional time

## Contact Us

For refund requests: <EMAIL>
Phone: +91-XXXX-XXXXXX`;

  if (loading) {
    return (
      <EnhancedAdminLayout title="Policy Management">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
          <Typography>Loading policies...</Typography>
        </Box>
      </EnhancedAdminLayout>
    );
  }

  return (
    <EnhancedAdminLayout title="Policy Management">
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            Policy Management
          </Typography>
          <Box>
            <Button
              variant="outlined"
              startIcon={<PreviewIcon />}
              onClick={() => setPreviewDialog(true)}
              sx={{ mr: 2 }}
            >
              Preview
            </Button>
            <Button
              variant="contained"
              startIcon={<SaveIcon />}
              onClick={() => savePolicy(currentPolicy.key)}
              loading={saving}
              sx={{ mr: 2 }}
            >
              Save Draft
            </Button>
            <Button
              variant="contained"
              color="success"
              startIcon={<PublishIcon />}
              onClick={() => publishPolicy(currentPolicy.key)}
              disabled={currentPolicyData.status === 'modified'}
            >
              Publish
            </Button>
          </Box>
        </Box>

        {/* Policy Tabs */}
        <Card sx={{ mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={(e, newValue) => setActiveTab(newValue)}
            variant="fullWidth"
          >
            {policyTabs.map((tab, index) => (
              <Tab
                key={tab.key}
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <span style={{ marginRight: 8 }}>{tab.icon}</span>
                    {tab.label}
                    <Chip
                      label={policies[tab.key].status}
                      size="small"
                      color={getStatusColor(policies[tab.key].status)}
                      sx={{ ml: 1 }}
                    />
                  </Box>
                }
              />
            ))}
          </Tabs>
        </Card>

        {/* Policy Editor */}
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6">
                    {currentPolicyData.title}
                  </Typography>
                  <Box>
                    <Chip
                      label={`Version ${currentPolicyData.version}`}
                      size="small"
                      variant="outlined"
                      sx={{ mr: 1 }}
                    />
                    <Chip
                      label={currentPolicyData.isPublished ? 'Published' : 'Draft'}
                      size="small"
                      color={currentPolicyData.isPublished ? 'success' : 'default'}
                    />
                  </Box>
                </Box>

                <RichTextEditor
                  value={currentPolicyData.content}
                  onChange={(content) => updatePolicyContent(currentPolicy.key, content)}
                  placeholder={`Enter ${currentPolicyData.title} content here...`}
                />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Policy Information
                </Typography>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Last Updated
                  </Typography>
                  <Typography variant="body1">
                    {new Date(currentPolicyData.lastUpdated).toLocaleString()}
                  </Typography>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Status
                  </Typography>
                  <Chip
                    label={currentPolicyData.status}
                    color={getStatusColor(currentPolicyData.status)}
                    size="small"
                  />
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Version
                  </Typography>
                  <Typography variant="body1">
                    {currentPolicyData.version}
                  </Typography>
                </Box>

                <FormControlLabel
                  control={
                    <Switch
                      checked={currentPolicyData.isPublished}
                      disabled
                    />
                  }
                  label="Published"
                />

                <Alert severity="info" sx={{ mt: 2 }}>
                  <Typography variant="body2">
                    Save your changes before publishing. Published policies will be visible to all users.
                  </Typography>
                </Alert>
              </CardContent>
            </Card>

            <Card sx={{ mt: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Quick Actions
                </Typography>
                
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<HistoryIcon />}
                  sx={{ mb: 1 }}
                >
                  View History
                </Button>
                
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<EditIcon />}
                  sx={{ mb: 1 }}
                >
                  Load Template
                </Button>
                
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<LegalIcon />}
                >
                  Legal Review
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Preview Dialog */}
        <Dialog open={previewDialog} onClose={() => setPreviewDialog(false)} maxWidth="md" fullWidth>
          <DialogTitle>
            Preview: {currentPolicyData.title}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ whiteSpace: 'pre-wrap', fontFamily: 'monospace', fontSize: '0.9rem' }}>
              {currentPolicyData.content}
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setPreviewDialog(false)}>Close</Button>
          </DialogActions>
        </Dialog>

        <ToastContainer position="top-right" />
      </Box>
    </EnhancedAdminLayout>
  );
}
