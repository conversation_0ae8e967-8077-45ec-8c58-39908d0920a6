// redis/otpService.js

/**
 * Redis OTP Service
 *
 * This service provides functions for generating, storing, retrieving, and verifying OTPs using Redis.
 * It serves as a backup and primary storage for OTPs, working alongside the MSG91 service.
 */

// Import the OTP generation library
const otpGenerator = require('otp-generator');
// Import the shared Redis client instance from the config file
const redisClient = require('../src/config/redisClient');
// Import logger for better logging
const logger = require('../src/utils/logger');

/**
 * Generates a numeric OTP using the otp-generator library.
 * @param {number} length - The desired length of the OTP (default: 6).
 * @returns {string} The generated OTP.
 */
const generateOTP = (length = 6) => {
  return otpGenerator.generate(length, {
    upperCaseAlphabets: false,
    lowerCaseAlphabets: false,
    specialChars: false,
    digits: true,
  });
};

/**
 * Stores the OTP in Redis using the shared client, with an expiration time.
 * @param {string} key - The key to store the OTP against (e.g., phone number).
 * @param {string} otp - The OTP value.
 * @param {number} expiryInSeconds - The expiration time in seconds (default: 900 - 15 minutes).
 * @returns {Promise<void>}
 */
const setOTP = async (key, otp, expiryInSeconds = 900) => {
  try {
    // Prefix keys for better organization in Redis (optional but good practice)
    const redisKey = `otp:${key}`;

    // Use the 'set' command with the 'EX' option for expiration (redis v4 syntax)
    await redisClient.set(redisKey, otp, {
      EX: expiryInSeconds,
    });

    logger.info(`OTP for key ${key} stored in Redis. Expires in ${expiryInSeconds}s.`);
  } catch (error) {
    logger.error(`Error setting OTP in Redis for key ${key}:`, error);
    // Rethrow or handle as needed, ensures the calling function knows about the failure
    throw new Error('Could not store OTP');
  }
};

/**
 * Verifies the provided OTP against the one stored in Redis using the shared client.
 * @param {string} key - The key the OTP was stored against (e.g., phone number).
 * @param {string} otpToVerify - The OTP provided by the user.
 * @returns {Promise<boolean>} True if the OTP is valid, false otherwise.
 */
const verifyOTP = async (key, otpToVerify) => {
  try {
    const redisKey = `otp:${key}`;
    const storedOTP = await redisClient.get(redisKey);

    if (!storedOTP) {
      logger.debug(`No OTP found in Redis for key ${key} or it has expired.`);
      return false; // No OTP found or it expired
    }

    if (storedOTP === otpToVerify) {
      logger.info(`OTP verified successfully for key ${key}`);
      return true; // OTP matches
    } else {
      logger.warn(`Invalid OTP provided for key ${key}. Expected ${storedOTP}, got ${otpToVerify}`);
      return false; // OTP does not match
    }
  } catch (error) {
    logger.error(`Error verifying OTP from Redis for key ${key}:`, error);
    return false; // Assume invalid on error for security
  }
};

/**
 * Retrieves an OTP from Redis without verifying it.
 * @param {string} key - The key the OTP was stored against (e.g., phone number).
 * @returns {Promise<string|null>} The stored OTP or null if not found.
 */
const getOTP = async (key) => {
  try {
    const redisKey = `otp:${key}`;
    const storedOTP = await redisClient.get(redisKey);

    if (!storedOTP) {
      logger.debug(`No OTP found in Redis for key ${key} or it has expired.`);
      return null;
    }

    logger.debug(`Retrieved OTP from Redis for key ${key}`);
    return storedOTP;
  } catch (error) {
    logger.error(`Error retrieving OTP from Redis for key ${key}:`, error);
    return null;
  }
};

/**
 * Deletes an OTP from Redis using the shared client after successful verification.
 * @param {string} key - The key the OTP was stored against.
 * @returns {Promise<void>}
 */
const deleteOTP = async (key) => {
  try {
    const redisKey = `otp:${key}`;
    const result = await redisClient.del(redisKey);
    if (result > 0) {
      logger.info(`OTP deleted from Redis for key ${key}`);
    } else {
      logger.debug(`Attempted to delete OTP for key ${key}, but it was not found (already deleted or expired).`);
    }
  } catch (error) {
    logger.error(`Error deleting OTP from Redis for key ${key}:`, error);
    // Log error but don't necessarily throw, as the main flow might have succeeded
  }
};

/**
 * Gets the remaining time to live (TTL) for an OTP in Redis.
 * @param {string} key - The key the OTP was stored against.
 * @returns {Promise<number>} The remaining time in seconds, -1 if no expiry, -2 if key doesn't exist.
 */
const getOTPTTL = async (key) => {
  try {
    const redisKey = `otp:${key}`;
    const ttl = await redisClient.ttl(redisKey);

    if (ttl === -2) {
      logger.debug(`No OTP found in Redis for key ${key}.`);
    } else if (ttl === -1) {
      logger.debug(`OTP for key ${key} exists but has no expiry.`);
    } else {
      logger.debug(`OTP for key ${key} expires in ${ttl} seconds.`);
    }

    return ttl;
  } catch (error) {
    logger.error(`Error getting TTL for OTP from Redis for key ${key}:`, error);
    return -2; // Assume key doesn't exist on error
  }
};

module.exports = {
  generateOTP,
  setOTP,
  getOTP,
  verifyOTP,
  deleteOTP,
  getOTPTTL
};

