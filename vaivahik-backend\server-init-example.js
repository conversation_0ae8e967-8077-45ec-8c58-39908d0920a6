/**
 * Example of how to initialize the WebSocket server with your Express app
 * This is a sample implementation to demonstrate how to integrate WebSockets
 */
const express = require('express');
const http = require('http');
const { initWebSocketServer } = require('./services/notification/websocket-server');
const notificationScheduler = require('./services/notification/notification-scheduler');

// Create Express app
const app = express();

// Set up middleware, routes, etc.
// ...

// Create HTTP server
const server = http.createServer(app);

// Initialize WebSocket server
const wss = initWebSocketServer(server);

// Initialize notification scheduler
notificationScheduler.initScheduler();

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
