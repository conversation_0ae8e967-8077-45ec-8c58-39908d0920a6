// src/middleware/dynamicFeatureAccess.middleware.js

/**
 * Middleware to control feature access based on user verification and subscription status
 * This uses dynamic configuration from the database
 */
const dynamicFeatureAccessMiddleware = async (req, res, next) => {
    // Skip for public routes or if no user is logged in
    if (!req.user || !req.user.userId) {
        return next();
    }

    const prisma = req.prisma;
    const userId = req.user.userId;
    
    try {
        // Get the requested feature from the route
        const requestedFeaturePath = req.originalUrl.split('/').pop();
        
        // Get user's complete profile with subscription info
        const user = await prisma.user.findUnique({
            where: { id: userId },
            include: {
                subscriptions: {
                    where: {
                        isActive: true,
                        endDate: { gt: new Date() }
                    },
                    orderBy: { endDate: 'desc' },
                    take: 1
                }
            }
        });
        
        if (!user) {
            return res.status(401).json({
                success: false,
                message: "User not found"
            });
        }
        
        // Determine user tier
        const isVerified = user.isVerified;
        const hasPremium = user.subscriptions && user.subscriptions.length > 0;
        const userTier = hasPremium ? 'PREMIUM' : (isVerified ? 'VERIFIED' : 'BASIC');
        const subscriptionPlanId = hasPremium ? user.subscriptions[0].id : null;
        
        // Get feature by name
        const feature = await prisma.feature.findUnique({
            where: { name: requestedFeaturePath },
            include: {
                accessRules: true
            }
        });
        
        if (!feature || !feature.isActive) {
            // Feature not found or not active, proceed without restrictions
            return next();
        }
        
        // Find the applicable access rule
        let accessRule = feature.accessRules.find(rule => 
            rule.userTier === userTier && 
            (userTier !== 'PREMIUM' || !rule.subscriptionPlanId || rule.subscriptionPlanId === subscriptionPlanId)
        );
        
        // If no specific rule for premium with this plan, try to find a generic premium rule
        if (userTier === 'PREMIUM' && !accessRule) {
            accessRule = feature.accessRules.find(rule => 
                rule.userTier === 'PREMIUM' && !rule.subscriptionPlanId
            );
        }
        
        // If still no rule, use the most restrictive tier's rule
        if (!accessRule) {
            accessRule = feature.accessRules.find(rule => rule.userTier === 'BASIC');
        }
        
        // If no access rule found at all, proceed without restrictions
        if (!accessRule) {
            return next();
        }
        
        // Get user's usage metrics for this feature
        const userMetrics = await getUserFeatureMetrics(prisma, userId, feature.name);
        
        // Store feature access info in request for controllers to use
        req.featureAccess = {
            isVerified,
            isPremium: hasPremium,
            userTier,
            subscriptionPlanId,
            feature: feature.name,
            accessRule,
            userMetrics
        };
        
        // Check if feature is enabled for this user tier
        if (!accessRule.isEnabled) {
            // Determine which tier has this feature enabled
            const enabledTiers = await getEnabledTiersForFeature(prisma, feature.id);
            
            // Prepare upgrade message
            let upgradeMessage = accessRule.upgradeMessage || `This feature requires ${enabledTiers.join(' or ')} access.`;
            
            if (enabledTiers.includes('VERIFIED') && userTier === 'BASIC') {
                return res.status(403).json({
                    success: false,
                    message: upgradeMessage,
                    verificationRequired: true,
                    verificationBenefits: await getVerificationBenefits(prisma)
                });
            } else if (enabledTiers.includes('PREMIUM')) {
                const subscriptionPlans = await getSubscriptionPlans(prisma);
                
                return res.status(403).json({
                    success: false,
                    message: upgradeMessage,
                    subscriptionRequired: true,
                    subscriptionPlans
                });
            } else {
                return res.status(403).json({
                    success: false,
                    message: "This feature is currently unavailable."
                });
            }
        }
        
        // Check usage limits
        if (accessRule.dailyLimit || accessRule.totalLimit) {
            const limitPeriod = accessRule.limitPeriod || 'DAILY';
            let limitExceeded = false;
            let currentUsage = 0;
            let limit = 0;
            
            if (limitPeriod === 'DAILY' && accessRule.dailyLimit) {
                currentUsage = userMetrics.dailyUsage;
                limit = accessRule.dailyLimit;
                limitExceeded = currentUsage >= limit;
            } else if (limitPeriod === 'TOTAL' && accessRule.totalLimit) {
                currentUsage = userMetrics.totalUsage;
                limit = accessRule.totalLimit;
                limitExceeded = currentUsage >= limit;
            }
            
            if (limitExceeded) {
                // Determine which tier has higher limits
                const higherTiers = await getHigherLimitTiersForFeature(prisma, feature.id, userTier);
                
                if (higherTiers.includes('VERIFIED') && userTier === 'BASIC') {
                    return res.status(403).json({
                        success: false,
                        message: `You've reached your ${limitPeriod.toLowerCase()} limit for this feature. Verify your profile for higher limits.`,
                        verificationRequired: true,
                        currentUsage,
                        limit,
                        verificationBenefits: await getVerificationBenefits(prisma)
                    });
                } else if (higherTiers.includes('PREMIUM')) {
                    const subscriptionPlans = await getSubscriptionPlans(prisma);
                    
                    return res.status(403).json({
                        success: false,
                        message: `You've reached your ${limitPeriod.toLowerCase()} limit for this feature. Upgrade to Premium for unlimited access.`,
                        subscriptionRequired: true,
                        currentUsage,
                        limit,
                        subscriptionPlans
                    });
                } else {
                    return res.status(403).json({
                        success: false,
                        message: `You've reached your ${limitPeriod.toLowerCase()} limit for this feature.`,
                        currentUsage,
                        limit
                    });
                }
            }
            
            // Set limit in request for controllers
            req.limit = limit;
        }
        
        // Set allowed filters for search features
        if (accessRule.allowedFilters) {
            try {
                req.allowedFilters = JSON.parse(accessRule.allowedFilters);
            } catch (error) {
                console.error('Error parsing allowed filters:', error);
            }
        }
        
        next();
    } catch (error) {
        console.error('Error in dynamic feature access middleware:', error);
        return res.status(500).json({
            success: false,
            message: "An error occurred while checking feature access."
        });
    }
};

/**
 * Get user's usage metrics for a specific feature
 * @param {PrismaClient} prisma - Prisma client
 * @param {string} userId - User ID
 * @param {string} featureName - Feature name
 * @returns {Promise<Object>} User metrics
 */
async function getUserFeatureMetrics(prisma, userId, featureName) {
    try {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        // Default metrics
        const metrics = {
            dailyUsage: 0,
            weeklyUsage: 0,
            monthlyUsage: 0,
            totalUsage: 0
        };
        
        // Get feature-specific metrics
        switch (featureName) {
            case 'send-message':
                // Daily message count
                metrics.dailyUsage = await prisma.message.count({
                    where: {
                        senderId: userId,
                        sentAt: { gte: today }
                    }
                });
                
                // Total message count
                metrics.totalUsage = await prisma.message.count({
                    where: { senderId: userId }
                });
                break;
                
            case 'view-profile':
                // Daily profile views
                metrics.dailyUsage = await prisma.profileView.count({
                    where: {
                        viewerId: userId,
                        viewedAt: { gte: today }
                    }
                });
                
                // Total profile views
                metrics.totalUsage = await prisma.profileView.count({
                    where: { viewerId: userId }
                });
                break;
                
            case 'matches':
                // Daily match views
                metrics.dailyUsage = await prisma.matchView.count({
                    where: {
                        userId: userId,
                        viewedAt: { gte: today }
                    }
                });
                
                // Total match views
                metrics.totalUsage = await prisma.matchView.count({
                    where: { userId: userId }
                });
                break;
                
            case 'connections':
                // Active connections count
                metrics.totalUsage = await prisma.match.count({
                    where: {
                        OR: [
                            { user1Id: userId },
                            { user2Id: userId }
                        ],
                        status: 'ACCEPTED'
                    }
                });
                
                metrics.dailyUsage = metrics.totalUsage; // Same as total for connections
                break;
                
            default:
                // For other features, try to get from system usage logs if implemented
                break;
        }
        
        return metrics;
    } catch (error) {
        console.error(`Error getting metrics for feature ${featureName}:`, error);
        return {
            dailyUsage: 0,
            weeklyUsage: 0,
            monthlyUsage: 0,
            totalUsage: 0
        };
    }
}

/**
 * Get tiers that have a feature enabled
 * @param {PrismaClient} prisma - Prisma client
 * @param {string} featureId - Feature ID
 * @returns {Promise<string[]>} Enabled tiers
 */
async function getEnabledTiersForFeature(prisma, featureId) {
    try {
        const accessRules = await prisma.featureAccess.findMany({
            where: {
                featureId,
                isEnabled: true
            },
            select: {
                userTier: true
            }
        });
        
        return accessRules.map(rule => rule.userTier);
    } catch (error) {
        console.error('Error getting enabled tiers:', error);
        return [];
    }
}

/**
 * Get tiers that have higher limits for a feature
 * @param {PrismaClient} prisma - Prisma client
 * @param {string} featureId - Feature ID
 * @param {string} currentTier - Current user tier
 * @returns {Promise<string[]>} Tiers with higher limits
 */
async function getHigherLimitTiersForFeature(prisma, featureId, currentTier) {
    try {
        // Get current tier's limits
        const currentRule = await prisma.featureAccess.findFirst({
            where: {
                featureId,
                userTier: currentTier
            }
        });
        
        if (!currentRule) {
            return [];
        }
        
        const currentDailyLimit = currentRule.dailyLimit || 0;
        const currentTotalLimit = currentRule.totalLimit || 0;
        
        // Get rules with higher limits
        const higherRules = await prisma.featureAccess.findMany({
            where: {
                featureId,
                isEnabled: true,
                OR: [
                    {
                        dailyLimit: {
                            gt: currentDailyLimit
                        }
                    },
                    {
                        dailyLimit: null
                    },
                    {
                        totalLimit: {
                            gt: currentTotalLimit
                        }
                    },
                    {
                        totalLimit: null
                    }
                ]
            },
            select: {
                userTier: true
            }
        });
        
        return higherRules.map(rule => rule.userTier);
    } catch (error) {
        console.error('Error getting higher limit tiers:', error);
        return [];
    }
}

/**
 * Get verification benefits
 * @param {PrismaClient} prisma - Prisma client
 * @returns {Promise<string[]>} Verification benefits
 */
async function getVerificationBenefits(prisma) {
    try {
        // Try to get from system config
        const configEntry = await prisma.systemConfig.findUnique({
            where: { configKey: 'VERIFICATION_BENEFITS' }
        });
        
        if (configEntry) {
            try {
                return JSON.parse(configEntry.configValue);
            } catch (error) {
                console.error('Error parsing verification benefits:', error);
            }
        }
        
        // Default benefits
        return [
            "Verification badge on your profile",
            "Send up to 5 messages",
            "Connect with up to 5 profiles",
            "View up to 10 matches per day"
        ];
    } catch (error) {
        console.error('Error getting verification benefits:', error);
        return [
            "Verification badge on your profile",
            "Enhanced messaging capabilities",
            "More connections",
            "More matches"
        ];
    }
}

/**
 * Get subscription plans
 * @param {PrismaClient} prisma - Prisma client
 * @returns {Promise<Array>} Subscription plans
 */
async function getSubscriptionPlans(prisma) {
    try {
        const plans = await prisma.subscriptionPlan.findMany({
            where: { isActive: true },
            orderBy: { price: 'asc' }
        });
        
        return plans.map(plan => ({
            id: plan.id,
            name: plan.name,
            planType: plan.planType,
            price: plan.price,
            currency: plan.currency,
            duration: plan.duration,
            features: plan.features ? JSON.parse(plan.features) : []
        }));
    } catch (error) {
        console.error('Error getting subscription plans:', error);
        return [
            { name: "Monthly", planType: "PREMIUM", price: 999, duration: 30 },
            { name: "Quarterly", planType: "PREMIUM", price: 2499, duration: 90, savings: "17%" },
            { name: "Annual", planType: "PREMIUM", price: 7999, duration: 365, savings: "33%" }
        ];
    }
}

/**
 * Helper middleware to apply limits based on user status
 * Use this in controllers to limit results based on verification and subscription status
 */
const applyDynamicAccessLimits = (req, res, next) => {
    if (req.limit) {
        // Add headers to indicate the limit was applied
        if (req.featureAccess) {
            res.setHeader('X-User-Tier', req.featureAccess.userTier);
            res.setHeader('X-Access-Limit', req.limit);
        }
    }
    
    // Add allowed filters for search
    if (req.allowedFilters) {
        res.setHeader('X-Allowed-Filters', JSON.stringify(req.allowedFilters));
    }
    
    next();
};

/**
 * Track user feature usage
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const trackFeatureUsage = async (req, res, next) => {
    if (!req.user || !req.user.userId || !req.featureAccess) {
        return next();
    }
    
    const prisma = req.prisma;
    const userId = req.user.userId;
    const feature = req.featureAccess.feature;
    
    try {
        // Track different metrics based on the feature
        switch (feature) {
            case 'view-profile':
                // Track profile view if a profile ID is provided
                if (req.params.userId) {
                    await prisma.profileView.create({
                        data: {
                            viewerId: userId,
                            viewedId: req.params.userId,
                            viewedAt: new Date()
                        }
                    });
                }
                break;
                
            case 'matches':
                // Track match view
                await prisma.matchView.create({
                    data: {
                        userId: userId,
                        viewedAt: new Date()
                    }
                });
                break;
                
            // Other features are tracked in their respective controllers
        }
        
        // Continue with the request
        next();
    } catch (error) {
        console.error('Error tracking feature usage:', error);
        // Don't block the request if tracking fails
        next();
    }
};

module.exports = {
    dynamicFeatureAccessMiddleware,
    applyDynamicAccessLimits,
    trackFeatureUsage
};
