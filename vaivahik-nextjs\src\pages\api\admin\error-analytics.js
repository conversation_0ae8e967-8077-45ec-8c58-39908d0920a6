/**
 * Error Analytics API Endpoint
 * Collects and stores error data for analytics dashboard
 */

// Simple in-memory storage for demo (replace with database in production)
let errorLogs = [];
const MAX_LOGS = 1000;

export default async function handler(req, res) {
  if (req.method === 'POST') {
    try {
      const errorData = req.body;
      
      // Add timestamp and ID
      const logEntry = {
        id: Date.now() + Math.random(),
        ...errorData,
        ip: req.ip || req.connection.remoteAddress,
        userAgent: req.headers['user-agent'],
        receivedAt: new Date().toISOString()
      };

      // Add to logs
      errorLogs.unshift(logEntry);
      
      // Keep only recent logs
      if (errorLogs.length > MAX_LOGS) {
        errorLogs = errorLogs.slice(0, MAX_LOGS);
      }

      res.status(200).json({
        success: true,
        message: 'Error logged successfully'
      });

    } catch (error) {
      console.error('Error logging failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to log error'
      });
    }
  } else if (req.method === 'GET') {
    try {
      const { timeRange = '24h' } = req.query;
      
      // Calculate time filter
      const now = new Date();
      let startTime;
      
      switch (timeRange) {
        case '1h':
          startTime = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case '24h':
          startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        default:
          startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      }

      // Filter logs by time range
      const filteredLogs = errorLogs.filter(log => 
        new Date(log.timestamp || log.receivedAt) >= startTime
      );

      // Generate analytics
      const analytics = generateErrorAnalytics(filteredLogs);

      res.status(200).json({
        success: true,
        data: analytics,
        timeRange,
        totalLogs: filteredLogs.length
      });

    } catch (error) {
      console.error('Error analytics failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get error analytics'
      });
    }
  } else {
    res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }
}

function generateErrorAnalytics(logs) {
  // Error count by type
  const errorsByType = logs.reduce((acc, log) => {
    const type = log.type || 'Unknown';
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {});

  // Error count by hour
  const errorsByHour = logs.reduce((acc, log) => {
    const hour = new Date(log.timestamp || log.receivedAt).getHours();
    acc[hour] = (acc[hour] || 0) + 1;
    return acc;
  }, {});

  // Top error messages
  const errorMessages = logs.reduce((acc, log) => {
    const message = log.message || 'Unknown error';
    acc[message] = (acc[message] || 0) + 1;
    return acc;
  }, {});

  const topErrors = Object.entries(errorMessages)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .map(([message, count]) => ({ message, count }));

  // Error trends (last 24 hours by hour)
  const hourlyTrends = Array.from({ length: 24 }, (_, i) => ({
    hour: i,
    count: errorsByHour[i] || 0
  }));

  // Browser/User Agent analysis
  const browsers = logs.reduce((acc, log) => {
    if (log.userAgent) {
      let browser = 'Unknown';
      if (log.userAgent.includes('Chrome')) browser = 'Chrome';
      else if (log.userAgent.includes('Firefox')) browser = 'Firefox';
      else if (log.userAgent.includes('Safari')) browser = 'Safari';
      else if (log.userAgent.includes('Edge')) browser = 'Edge';
      
      acc[browser] = (acc[browser] || 0) + 1;
    }
    return acc;
  }, {});

  return {
    summary: {
      totalErrors: logs.length,
      uniqueErrorTypes: Object.keys(errorsByType).length,
      mostCommonType: Object.entries(errorsByType).sort(([,a], [,b]) => b - a)[0]?.[0] || 'None',
      errorRate: logs.length > 0 ? (logs.length / 1000 * 100).toFixed(2) + '%' : '0%'
    },
    errorsByType,
    topErrors,
    hourlyTrends,
    browsers,
    recentErrors: logs.slice(0, 20).map(log => ({
      id: log.id,
      type: log.type,
      message: log.message,
      timestamp: log.timestamp || log.receivedAt,
      ip: log.ip
    }))
  };
}
