import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Tabs,
  Tab,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  Button,
  Grid,
  Select,
  IconButton,
  Tooltip,
  Snackbar,
  Alert,
  Divider,
  Switch,
  Chip
} from '@mui/material';
import {
  PhotoLibrary as PhotoLibraryIcon,
  ContactPhone as ContactPhoneIcon,
  Person as PersonIcon,
  AccessTime as AccessTimeIcon,
  Save as SaveIcon,
  InfoOutlined as InfoOutlinedIcon,
  Badge as BadgeIcon
} from '@mui/icons-material';
import { PRIVACY_OPTIONS, DISPLAY_NAME_OPTIONS } from '@/config';

/**
 * Privacy Settings Component
 *
 * Allows users to control visibility of their profile information,
 * photos, contact details, and activity status
 */
const PrivacySettings = ({ user, onSave }) => {
  // Active tab state
  const [activeTab, setActiveTab] = useState(0);

  // Privacy settings state
  const [settings, setSettings] = useState({
    // Photo Privacy
    photoPrivacy: 'ALL_USERS',

    // Contact Details Privacy
    phonePrivacy: 'PREMIUM_USERS',
    emailPrivacy: 'PREMIUM_USERS',
    socialMediaPrivacy: 'PREMIUM_USERS',

    // Profile Information Privacy
    educationPrivacy: 'ALL_USERS',
    careerPrivacy: 'ALL_USERS',
    familyPrivacy: 'ALL_USERS',
    birthDetailsPrivacy: 'ALL_USERS',

    // Activity Privacy
    onlineStatusPrivacy: 'ALL_USERS',
    lastActivePrivacy: 'ALL_USERS',

    // Display Name Privacy (NEW)
    displayNamePreference: 'FIRST_NAME',
    showNameInNotifications: true,
    showNameInSearch: true,
    showNameInMatches: true,
    showNameInMessages: true,
    allowProfileViews: true,
    showOnlineStatus: false,
    showLastSeen: false,
    allowDirectMessages: true,
    showContactInfo: false
  });

  // Notification state
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Load user's privacy settings on mount
  useEffect(() => {
    if (user && user.privacySettings) {
      setSettings(user.privacySettings);
    }
  }, [user]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Handle setting change
  const handleSettingChange = (event) => {
    const { name, value } = event.target;
    setSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle save
  const handleSave = async () => {
    try {
      if (onSave) {
        const result = await onSave(settings);

        if (result.success) {
          setNotification({
            open: true,
            message: 'Privacy settings saved successfully',
            severity: 'success'
          });
        } else {
          setNotification({
            open: true,
            message: result.error || 'Failed to save privacy settings',
            severity: 'error'
          });
        }
      }
    } catch (error) {
      setNotification({
        open: true,
        message: 'An error occurred while saving privacy settings',
        severity: 'error'
      });
      console.error(error);
    }
  };

  // Close notification
  const handleCloseNotification = () => {
    setNotification(prev => ({
      ...prev,
      open: false
    }));
  };

  return (
    <Paper elevation={0} sx={{ p: 3, borderRadius: 2 }}>
      <Typography variant="h5" gutterBottom>
        Privacy Settings
      </Typography>
      <Typography variant="body2" color="text.secondary" paragraph>
        Control who can see your information and how your profile appears to others.
      </Typography>

      <Tabs
        value={activeTab}
        onChange={handleTabChange}
        sx={{ mb: 3 }}
        variant="scrollable"
        scrollButtons="auto"
      >
        <Tab icon={<PhotoLibraryIcon />} label="Photos" />
        <Tab icon={<ContactPhoneIcon />} label="Contact Details" />
        <Tab icon={<PersonIcon />} label="Profile Info" />
        <Tab icon={<AccessTimeIcon />} label="Activity" />
        <Tab icon={<BadgeIcon />} label="Display Name" />
      </Tabs>

      {/* Photo Privacy Tab */}
      {activeTab === 0 && (
        <Box>
          <Typography variant="subtitle1" gutterBottom>
            Photo Privacy
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Control who can see your photos and albums.
          </Typography>

          <FormControl component="fieldset" sx={{ width: '100%' }}>
            <RadioGroup
              name="photoPrivacy"
              value={settings.photoPrivacy}
              onChange={handleSettingChange}
            >
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                    <FormControlLabel
                      value="ALL_USERS"
                      control={<Radio />}
                      label={
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="body1">Visible to all users</Typography>
                          <Tooltip title="All users can see your photos">
                            <IconButton size="small" sx={{ ml: 1 }}>
                              <InfoOutlinedIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      }
                    />
                  </Paper>
                </Grid>

                <Grid item xs={12}>
                  <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                    <FormControlLabel
                      value="PREMIUM_USERS"
                      control={<Radio />}
                      label="Visible only to premium users"
                    />
                  </Paper>
                </Grid>

                <Grid item xs={12}>
                  <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                    <FormControlLabel
                      value="ACCEPTED_INTEREST"
                      control={<Radio />}
                      label="Visible only after interest is accepted"
                    />
                  </Paper>
                </Grid>

                <Grid item xs={12}>
                  <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                    <FormControlLabel
                      value="HIDDEN"
                      control={<Radio />}
                      label="Hidden from all users"
                    />
                  </Paper>
                </Grid>
              </Grid>
            </RadioGroup>
          </FormControl>
        </Box>
      )}

      {/* Contact Details Privacy Tab */}
      {activeTab === 1 && (
        <Box>
          <Typography variant="subtitle1" gutterBottom>
            Contact Details Privacy
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Control who can see your contact information.
          </Typography>

          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Phone Number
            </Typography>
            <FormControl component="fieldset" sx={{ width: '100%' }}>
              <RadioGroup
                name="phonePrivacy"
                value={settings.phonePrivacy}
                onChange={handleSettingChange}
              >
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <FormControlLabel
                        value="PREMIUM_USERS"
                        control={<Radio />}
                        label={
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Typography variant="body1">Visible only to premium users</Typography>
                            <Tooltip title="Only premium users can see your phone number">
                              <IconButton size="small" sx={{ ml: 1 }}>
                                <InfoOutlinedIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        }
                      />
                    </Paper>
                  </Grid>

                  <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <FormControlLabel
                        value="ACCEPTED_INTEREST"
                        control={<Radio />}
                        label="Visible only after interest is accepted"
                      />
                    </Paper>
                  </Grid>

                  <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <FormControlLabel
                        value="HIDDEN"
                        control={<Radio />}
                        label="Hidden from all users"
                      />
                    </Paper>
                  </Grid>
                </Grid>
              </RadioGroup>
            </FormControl>
          </Box>

          <Divider sx={{ my: 3 }} />

          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Email Address
            </Typography>
            <FormControl component="fieldset" sx={{ width: '100%' }}>
              <RadioGroup
                name="emailPrivacy"
                value={settings.emailPrivacy}
                onChange={handleSettingChange}
              >
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <FormControlLabel
                        value="PREMIUM_USERS"
                        control={<Radio />}
                        label="Visible only to premium users"
                      />
                    </Paper>
                  </Grid>

                  <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <FormControlLabel
                        value="ACCEPTED_INTEREST"
                        control={<Radio />}
                        label="Visible only after interest is accepted"
                      />
                    </Paper>
                  </Grid>

                  <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <FormControlLabel
                        value="HIDDEN"
                        control={<Radio />}
                        label="Hidden from all users"
                      />
                    </Paper>
                  </Grid>
                </Grid>
              </RadioGroup>
            </FormControl>
          </Box>
        </Box>
      )}

      {/* Profile Info Privacy Tab */}
      {activeTab === 2 && (
        <Box>
          <Typography variant="subtitle1" gutterBottom>
            Profile Information Privacy
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Control who can see different sections of your profile.
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" gutterBottom>
                Education Details
              </Typography>
              <Select
                native
                name="educationPrivacy"
                value={settings.educationPrivacy}
                onChange={handleSettingChange}
                fullWidth
                sx={{ mb: 2 }}
              >
                <option value="ALL_USERS">Visible to all users</option>
                <option value="PREMIUM_USERS">Visible only to premium users</option>
                <option value="ACCEPTED_INTEREST">Visible only after interest is accepted</option>
              </Select>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" gutterBottom>
                Career & Income
              </Typography>
              <Select
                native
                name="careerPrivacy"
                value={settings.careerPrivacy}
                onChange={handleSettingChange}
                fullWidth
                sx={{ mb: 2 }}
              >
                <option value="ALL_USERS">Visible to all users</option>
                <option value="PREMIUM_USERS">Visible only to premium users</option>
                <option value="ACCEPTED_INTEREST">Visible only after interest is accepted</option>
              </Select>
            </Grid>
          </Grid>
        </Box>
      )}

      {/* Activity Privacy Tab */}
      {activeTab === 3 && (
        <Box>
          <Typography variant="subtitle1" gutterBottom>
            Activity Privacy
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Control who can see your online status and activity.
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" gutterBottom>
                Online Status
              </Typography>
              <Select
                native
                name="onlineStatusPrivacy"
                value={settings.onlineStatusPrivacy}
                onChange={handleSettingChange}
                fullWidth
                sx={{ mb: 2 }}
              >
                <option value="ALL_USERS">Visible to all users</option>
                <option value="PREMIUM_USERS">Visible only to premium users</option>
                <option value="ACCEPTED_INTEREST">Visible only after interest is accepted</option>
                <option value="HIDDEN">Hidden from all users</option>
              </Select>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" gutterBottom>
                Last Active
              </Typography>
              <Select
                native
                name="lastActivePrivacy"
                value={settings.lastActivePrivacy}
                onChange={handleSettingChange}
                fullWidth
                sx={{ mb: 2 }}
              >
                <option value="ALL_USERS">Visible to all users</option>
                <option value="PREMIUM_USERS">Visible only to premium users</option>
                <option value="ACCEPTED_INTEREST">Visible only after interest is accepted</option>
                <option value="HIDDEN">Hidden from all users</option>
              </Select>
            </Grid>
          </Grid>
        </Box>
      )}

      {/* Display Name Privacy Tab */}
      {activeTab === 4 && (
        <Box>
          <Typography variant="subtitle1" gutterBottom>
            Display Name Preferences
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Choose how your name appears to other users across the platform.
          </Typography>

          {/* Display Name Preference Selection */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="subtitle2" gutterBottom sx={{ mb: 2 }}>
              How should your name be displayed?
            </Typography>
            <FormControl component="fieldset" sx={{ width: '100%' }}>
              <RadioGroup
                name="displayNamePreference"
                value={settings.displayNamePreference}
                onChange={handleSettingChange}
              >
                <Grid container spacing={2}>
                  {Object.values(DISPLAY_NAME_OPTIONS).map((option) => (
                    <Grid item xs={12} key={option.value}>
                      <Paper
                        variant="outlined"
                        sx={{
                          p: 2,
                          mb: 1,
                          border: settings.displayNamePreference === option.value ? 2 : 1,
                          borderColor: settings.displayNamePreference === option.value ? 'primary.main' : 'divider'
                        }}
                      >
                        <FormControlLabel
                          value={option.value}
                          control={<Radio />}
                          label={
                            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                              <Box sx={{ mr: 2, fontSize: '1.5rem' }}>{option.icon}</Box>
                              <Box sx={{ flexGrow: 1 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                                  <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                                    {option.label}
                                  </Typography>
                                  {option.recommended && (
                                    <Chip
                                      label="Recommended"
                                      size="small"
                                      color="primary"
                                      sx={{ ml: 1, fontSize: '0.7rem' }}
                                    />
                                  )}
                                </Box>
                                <Typography variant="body2" color="text.secondary">
                                  {option.description}
                                </Typography>
                                <Typography variant="caption" color={
                                  option.privacy === 'Low' ? 'error.main' :
                                  option.privacy === 'Medium' ? 'warning.main' :
                                  option.privacy === 'High' ? 'info.main' : 'success.main'
                                }>
                                  Privacy Level: {option.privacy}
                                </Typography>
                              </Box>
                            </Box>
                          }
                        />
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              </RadioGroup>
            </FormControl>
          </Box>

          {/* Context-specific Settings */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom sx={{ mb: 2 }}>
              Where should your name be visible?
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.showNameInNotifications}
                      onChange={(e) => setSettings({
                        ...settings,
                        showNameInNotifications: e.target.checked
                      })}
                      name="showNameInNotifications"
                    />
                  }
                  label="Email Notifications"
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.showNameInSearch}
                      onChange={(e) => setSettings({
                        ...settings,
                        showNameInSearch: e.target.checked
                      })}
                      name="showNameInSearch"
                    />
                  }
                  label="Search Results"
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.showNameInMatches}
                      onChange={(e) => setSettings({
                        ...settings,
                        showNameInMatches: e.target.checked
                      })}
                      name="showNameInMatches"
                    />
                  }
                  label="Match Listings"
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.allowDirectMessages}
                      onChange={(e) => setSettings({
                        ...settings,
                        allowDirectMessages: e.target.checked
                      })}
                      name="allowDirectMessages"
                    />
                  }
                  label="Allow Messages"
                />
              </Grid>
            </Grid>
          </Box>

          {/* Privacy Recommendations */}
          {settings.displayNamePreference === 'FULL_NAME' && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>Privacy Tip:</strong> For enhanced security, especially for female users,
                consider using "First Name Only" or "Profile ID" instead of your full name.
              </Typography>
            </Alert>
          )}
        </Box>
      )}

      {/* Save Button */}
      <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<SaveIcon />}
          onClick={handleSave}
        >
          Save Privacy Settings
        </Button>
      </Box>

      {/* Notification */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default PrivacySettings;
