/**
 * Protected Route Component
 * 
 * This component wraps routes that require authentication.
 * It redirects to the login page if the user is not authenticated.
 */

import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '@/contexts/AuthContext';
import LoadingScreen from '@/components/common/LoadingScreen';

/**
 * Protected Route Component
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render if authenticated
 * @param {string} [props.redirectTo='/login'] - Path to redirect to if not authenticated
 * @param {string[]} [props.roles] - Roles allowed to access this route
 * @returns {React.ReactNode} - Protected route component
 */
const ProtectedRoute = ({ 
  children, 
  redirectTo = '/login', 
  roles = [] 
}) => {
  const { user, loading, isAuthenticated } = useAuth();
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    // Skip check if auth context is still loading
    if (loading) return;

    const checkAuth = async () => {
      // Check if user is authenticated
      const authenticated = isAuthenticated();

      if (!authenticated) {
        // Redirect to login page with return URL
        router.push({
          pathname: redirectTo,
          query: { returnUrl: router.asPath }
        });
        return;
      }

      // If roles are specified, check if user has required role
      if (roles.length > 0 && user) {
        const hasRole = roles.includes(user.role);
        if (!hasRole) {
          // Redirect to unauthorized page
          router.push('/unauthorized');
          return;
        }
      }

      // User is authenticated and has required role
      setIsChecking(false);
    };

    checkAuth();
  }, [user, loading, isAuthenticated, router, redirectTo, roles]);

  // Show loading screen while checking authentication
  if (loading || isChecking) {
    return <LoadingScreen message="Checking authentication..." />;
  }

  // Render children if authenticated
  return children;
};

export default ProtectedRoute;
