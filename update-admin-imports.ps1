# PowerShell script to update AdminLayout imports
$adminPages = Get-ChildItem -Path "vaivahik-nextjs\src\pages\admin" -Filter "*.js" -Recurse

foreach ($page in $adminPages) {
    $content = Get-Content -Path $page.FullName -Raw
    $updatedContent = $content -replace "import AdminLayout from '@/components/layouts/AdminLayout';", "import AdminLayout from '@/components/admin/AdminLayout';"
    Set-Content -Path $page.FullName -Value $updatedContent
    Write-Host "Updated imports in $($page.Name)"
}

Write-Host "All admin page imports updated successfully!"
