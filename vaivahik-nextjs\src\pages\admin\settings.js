import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);
import { adminGet, adminPut } from '@/services/apiService';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  TextField,
  Switch,
  FormControlLabel,
  Button,
  Divider,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Alert,
  Snackbar,
  CircularProgress,
  InputAdornment,
  IconButton,
  Select,
  MenuItem,
  FormControl,
  InputLabel
} from '@mui/material';
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Settings as SettingsIcon,
  Security as SecurityIcon,
  Person as PersonIcon,
  Payment as PaymentIcon,
  Language as LanguageIcon,
  Computer as ComputerIcon,
  Article as ArticleIcon
} from '@mui/icons-material';

// Tab Panel Component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

export default function Settings() {
  const [tabValue, setTabValue] = useState(0);
  const [settings, setSettings] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState({});
  const [editedSettings, setEditedSettings] = useState({});

  // Fetch settings
  const fetchSettings = async () => {
    setLoading(true);
    try {
      const data = await adminGet('/settings');
      
      if (data.success) {
        setSettings(data.settings || []);
        setCategories(data.categories || []);
        
        // Initialize edited settings
        const initialEdited = {};
        data.settings.forEach(setting => {
          initialEdited[setting.key] = setting.value;
        });
        setEditedSettings(initialEdited);
      } else {
        setError('Failed to fetch settings');
      }
    } catch (err) {
      console.error('Error fetching settings:', err);
      setError('An error occurred while fetching settings');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle setting change
  const handleSettingChange = (key, value) => {
    setEditedSettings({
      ...editedSettings,
      [key]: value
    });
  };

  // Handle toggle for boolean settings
  const handleToggle = (key) => {
    const currentValue = editedSettings[key] === 'true';
    handleSettingChange(key, (!currentValue).toString());
  };

  // Handle save settings
  const handleSaveSettings = async (category) => {
    setSaving(true);
    try {
      // Get settings for the current category
      const categorySettings = settings.filter(s => s.category === category);
      
      // Create an array of settings to update
      const updatedSettings = categorySettings.map(setting => ({
        key: setting.key,
        value: editedSettings[setting.key]
      }));
      
      const response = await adminPut('/settings', { settings: updatedSettings });
      
      if (response.success) {
        setSuccess(true);
        // Update the settings with the new values
        setSettings(settings.map(setting => 
          updatedSettings.some(s => s.key === setting.key)
            ? { ...setting, value: editedSettings[setting.key] }
            : setting
        ));
      } else {
        setError('Failed to save settings');
      }
    } catch (err) {
      console.error('Error saving settings:', err);
      setError('An error occurred while saving settings');
    } finally {
      setSaving(false);
    }
  };

  // Toggle password visibility
  const togglePasswordVisibility = (key) => {
    setShowPassword({
      ...showPassword,
      [key]: !showPassword[key]
    });
  };

  // Get settings for a specific category
  const getCategorySettings = (category) => {
    return settings.filter(setting => setting.category === category);
  };

  // Get category name by ID
  const getCategoryName = (categoryId) => {
    const category = categories.find(c => c.id === categoryId);
    return category ? category.name : categoryId;
  };

  // Get category icon by ID
  const getCategoryIcon = (categoryId) => {
    switch (categoryId) {
      case 'general':
        return <SettingsIcon />;
      case 'security':
        return <SecurityIcon />;
      case 'registration':
        return <PersonIcon />;
      case 'payment':
        return <PaymentIcon />;
      case 'social':
        return <LanguageIcon />;
      case 'system':
        return <ComputerIcon />;
      case 'content':
        return <ArticleIcon />;
      default:
        return <SettingsIcon />;
    }
  };

  // Render setting input based on type
  const renderSettingInput = (setting) => {
    const { key, type, description, value } = setting;
    
    switch (type) {
      case 'boolean':
        return (
          <FormControlLabel
            control={
              <Switch
                checked={editedSettings[key] === 'true'}
                onChange={() => handleToggle(key)}
                color="primary"
              />
            }
            label={description}
          />
        );
      case 'number':
        return (
          <TextField
            fullWidth
            label={description}
            type="number"
            value={editedSettings[key]}
            onChange={(e) => handleSettingChange(key, e.target.value)}
            margin="normal"
            variant="outlined"
          />
        );
      case 'password':
        return (
          <TextField
            fullWidth
            label={description}
            type={showPassword[key] ? 'text' : 'password'}
            value={editedSettings[key]}
            onChange={(e) => handleSettingChange(key, e.target.value)}
            margin="normal"
            variant="outlined"
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => togglePasswordVisibility(key)}
                    edge="end"
                  >
                    {showPassword[key] ? <VisibilityOffIcon /> : <VisibilityIcon />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        );
      case 'json':
        try {
          const parsedValue = JSON.parse(editedSettings[key]);
          return (
            <TextField
              fullWidth
              label={description}
              multiline
              rows={4}
              value={JSON.stringify(parsedValue, null, 2)}
              onChange={(e) => {
                try {
                  // Validate JSON
                  JSON.parse(e.target.value);
                  handleSettingChange(key, e.target.value);
                } catch (err) {
                  // Allow invalid JSON during editing, but don't update state
                  console.log('Invalid JSON');
                }
              }}
              margin="normal"
              variant="outlined"
            />
          );
        } catch (err) {
          return (
            <TextField
              fullWidth
              label={description}
              multiline
              rows={4}
              value={editedSettings[key]}
              onChange={(e) => handleSettingChange(key, e.target.value)}
              margin="normal"
              variant="outlined"
              error
              helperText="Invalid JSON format"
            />
          );
        }
      case 'text':
        return (
          <TextField
            fullWidth
            label={description}
            multiline
            rows={3}
            value={editedSettings[key]}
            onChange={(e) => handleSettingChange(key, e.target.value)}
            margin="normal"
            variant="outlined"
          />
        );
      default:
        return (
          <TextField
            fullWidth
            label={description}
            value={editedSettings[key]}
            onChange={(e) => handleSettingChange(key, e.target.value)}
            margin="normal"
            variant="outlined"
          />
        );
    }
  };

  return (
    <EnhancedAdminLayout title="Settings">
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          System Settings
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Paper sx={{ width: '100%' }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                variant="scrollable"
                scrollButtons="auto"
                aria-label="settings tabs"
              >
                {categories.map((category, index) => (
                  <Tab
                    key={category.id}
                    label={category.name}
                    icon={getCategoryIcon(category.id)}
                    iconPosition="start"
                  />
                ))}
              </Tabs>
            </Box>

            {categories.map((category, index) => (
              <TabPanel key={category.id} value={tabValue} index={index}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">
                    {category.name} Settings
                  </Typography>
                  <Box>
                    <Button
                      variant="outlined"
                      startIcon={<RefreshIcon />}
                      onClick={fetchSettings}
                      sx={{ mr: 1 }}
                    >
                      Refresh
                    </Button>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<SaveIcon />}
                      onClick={() => handleSaveSettings(category.id)}
                      disabled={saving}
                    >
                      {saving ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </Box>
                </Box>
                
                <Divider sx={{ mb: 3 }} />
                
                <Grid container spacing={3}>
                  {getCategorySettings(category.id).map((setting) => (
                    <Grid item xs={12} md={6} key={setting.key}>
                      <Card variant="outlined">
                        <CardHeader
                          title={setting.key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          subheader={setting.isPublic ? 'Public Setting' : 'Private Setting'}
                        />
                        <CardContent>
                          {renderSettingInput(setting)}
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </TabPanel>
            ))}
          </Paper>
        )}
      </Box>

      <Snackbar
        open={success}
        autoHideDuration={6000}
        onClose={() => setSuccess(false)}
      >
        <Alert onClose={() => setSuccess(false)} severity="success">
          Settings saved successfully!
        </Alert>
      </Snackbar>
    </EnhancedAdminLayout>
  );
}
