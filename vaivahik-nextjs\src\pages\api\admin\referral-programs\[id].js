import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  // Check if user is authenticated and is an admin
  const session = await getServerSession(req, res, authOptions);
  
  if (!session || !session.user || session.user.role !== 'ADMIN') {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ success: false, message: 'Program ID is required' });
  }

  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getReferralProgram(req, res, id);
      case 'DELETE':
        return await deleteReferralProgram(req, res, id);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    console.error(`Error in referral-programs/${id} API:`, error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
}

// Get a specific referral program
async function getReferralProgram(req, res, id) {
  try {
    const program = await prisma.referralProgram.findUnique({
      where: { id },
      include: {
        _count: {
          select: { referrals: true }
        }
      }
    });

    if (!program) {
      return res.status(404).json({ success: false, message: 'Referral program not found' });
    }

    // Get additional stats
    const totalReferrals = program._count.referrals;
    
    // Get conversion rate
    const completedReferrals = await prisma.referral.count({
      where: {
        referralProgramId: id,
        status: 'completed'
      }
    });
    
    const conversionRate = totalReferrals > 0 
      ? Math.round((completedReferrals / totalReferrals) * 100) 
      : 0;
    
    // Get total rewards given
    const totalRewards = await prisma.referralReward.aggregate({
      where: {
        referral: {
          referralProgramId: id
        },
        rewardType: 'cash'
      },
      _sum: {
        rewardAmount: true
      }
    });
    
    const enhancedProgram = {
      ...program,
      totalReferrals,
      conversionRate,
      totalRewardsGiven: totalRewards._sum?.rewardAmount || 0
    };

    return res.status(200).json({ success: true, program: enhancedProgram });
  } catch (error) {
    console.error('Error fetching referral program:', error);
    return res.status(500).json({ success: false, message: 'Failed to fetch referral program' });
  }
}

// Delete a referral program
async function deleteReferralProgram(req, res, id) {
  try {
    // Check if program exists
    const program = await prisma.referralProgram.findUnique({
      where: { id },
      include: {
        _count: {
          select: { referrals: true }
        }
      }
    });

    if (!program) {
      return res.status(404).json({ success: false, message: 'Referral program not found' });
    }

    // Check if program has active referrals
    if (program._count.referrals > 0) {
      // Instead of deleting, mark as inactive
      await prisma.referralProgram.update({
        where: { id },
        data: {
          status: 'inactive',
          updatedAt: new Date()
        }
      });

      return res.status(200).json({ 
        success: true, 
        message: 'Program has active referrals and has been marked as inactive instead of deleted' 
      });
    }

    // Delete the program if no referrals
    await prisma.referralProgram.delete({
      where: { id }
    });

    return res.status(200).json({ success: true, message: 'Referral program deleted successfully' });
  } catch (error) {
    console.error('Error deleting referral program:', error);
    return res.status(500).json({ success: false, message: 'Failed to delete referral program' });
  }
}
