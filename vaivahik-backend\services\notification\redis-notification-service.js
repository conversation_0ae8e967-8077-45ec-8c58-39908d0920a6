/**
 * Redis-Based Notification Service
 * 
 * This service handles storing, retrieving, and publishing notifications using Redis.
 */
const { redisClient, redisPubSub } = require('./redis-config');
const { v4: uuidv4 } = require('uuid');

// Redis key prefixes
const NOTIFICATION_KEY_PREFIX = 'notification:';
const USER_NOTIFICATIONS_KEY_PREFIX = 'user:notifications:';
const TOPIC_NOTIFICATIONS_KEY_PREFIX = 'topic:notifications:';
const NOTIFICATION_CHANNEL_PREFIX = 'channel:notification:';

/**
 * Store a notification in Redis
 * 
 * @param {Object} notification - The notification object
 * @returns {Promise<string>} The notification ID
 */
const storeNotification = async (notification) => {
  try {
    // Generate a unique ID if not provided
    const notificationId = notification.id || uuidv4();
    notification.id = notificationId;
    
    // Add timestamp if not provided
    if (!notification.createdAt) {
      notification.createdAt = new Date().toISOString();
    }
    
    // Store the notification
    const key = `${NOTIFICATION_KEY_PREFIX}${notificationId}`;
    await redisClient.set(key, JSON.stringify(notification));
    
    // Set expiration if TTL is provided (in seconds)
    if (notification.ttl) {
      await redisClient.expire(key, notification.ttl);
    }
    
    return notificationId;
  } catch (error) {
    console.error('Error storing notification in Redis:', error);
    throw error;
  }
};

/**
 * Add a notification to a user's notification list
 * 
 * @param {string} userId - The user ID
 * @param {string} notificationId - The notification ID
 * @returns {Promise<number>} The number of notifications in the user's list
 */
const addNotificationToUser = async (userId, notificationId) => {
  try {
    const key = `${USER_NOTIFICATIONS_KEY_PREFIX}${userId}`;
    
    // Add to sorted set with timestamp as score for time-based ordering
    const score = Date.now();
    await redisClient.zadd(key, score, notificationId);
    
    // Get the count
    return await redisClient.zcard(key);
  } catch (error) {
    console.error(`Error adding notification ${notificationId} to user ${userId}:`, error);
    throw error;
  }
};

/**
 * Add a notification to a topic's notification list
 * 
 * @param {string} topic - The topic name
 * @param {string} notificationId - The notification ID
 * @returns {Promise<number>} The number of notifications in the topic's list
 */
const addNotificationToTopic = async (topic, notificationId) => {
  try {
    const key = `${TOPIC_NOTIFICATIONS_KEY_PREFIX}${topic}`;
    
    // Add to sorted set with timestamp as score for time-based ordering
    const score = Date.now();
    await redisClient.zadd(key, score, notificationId);
    
    // Get the count
    return await redisClient.zcard(key);
  } catch (error) {
    console.error(`Error adding notification ${notificationId} to topic ${topic}:`, error);
    throw error;
  }
};

/**
 * Get a notification by ID
 * 
 * @param {string} notificationId - The notification ID
 * @returns {Promise<Object|null>} The notification object or null if not found
 */
const getNotification = async (notificationId) => {
  try {
    const key = `${NOTIFICATION_KEY_PREFIX}${notificationId}`;
    const notification = await redisClient.get(key);
    
    return notification ? JSON.parse(notification) : null;
  } catch (error) {
    console.error(`Error getting notification ${notificationId}:`, error);
    throw error;
  }
};

/**
 * Get notifications for a user
 * 
 * @param {string} userId - The user ID
 * @param {Object} options - Options for retrieving notifications
 * @param {number} options.limit - Maximum number of notifications to retrieve
 * @param {number} options.offset - Number of notifications to skip
 * @param {boolean} options.unreadOnly - Whether to retrieve only unread notifications
 * @returns {Promise<Array>} Array of notification objects
 */
const getUserNotifications = async (userId, options = {}) => {
  try {
    const key = `${USER_NOTIFICATIONS_KEY_PREFIX}${userId}`;
    const { limit = 20, offset = 0, unreadOnly = false } = options;
    
    // Get notification IDs from sorted set (newest first)
    const notificationIds = await redisClient.zrevrange(key, offset, offset + limit - 1);
    
    // Get notification objects
    const notifications = [];
    for (const notificationId of notificationIds) {
      const notification = await getNotification(notificationId);
      if (notification && (!unreadOnly || !notification.isRead)) {
        notifications.push(notification);
      }
    }
    
    return notifications;
  } catch (error) {
    console.error(`Error getting notifications for user ${userId}:`, error);
    throw error;
  }
};

/**
 * Get the count of unread notifications for a user
 * 
 * @param {string} userId - The user ID
 * @returns {Promise<number>} The count of unread notifications
 */
const getUnreadCount = async (userId) => {
  try {
    const notifications = await getUserNotifications(userId, { unreadOnly: true, limit: 100 });
    return notifications.length;
  } catch (error) {
    console.error(`Error getting unread count for user ${userId}:`, error);
    throw error;
  }
};

/**
 * Mark a notification as read
 * 
 * @param {string} notificationId - The notification ID
 * @param {string} userId - The user ID
 * @returns {Promise<boolean>} Whether the operation was successful
 */
const markNotificationAsRead = async (notificationId, userId) => {
  try {
    const key = `${NOTIFICATION_KEY_PREFIX}${notificationId}`;
    const notification = await redisClient.get(key);
    
    if (!notification) {
      return false;
    }
    
    const notificationObj = JSON.parse(notification);
    
    // Only mark as read if it belongs to the user
    if (notificationObj.userId === userId) {
      notificationObj.isRead = true;
      notificationObj.readAt = new Date().toISOString();
      
      await redisClient.set(key, JSON.stringify(notificationObj));
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error marking notification ${notificationId} as read:`, error);
    throw error;
  }
};

/**
 * Mark all notifications as read for a user
 * 
 * @param {string} userId - The user ID
 * @returns {Promise<number>} The number of notifications marked as read
 */
const markAllNotificationsAsRead = async (userId) => {
  try {
    const key = `${USER_NOTIFICATIONS_KEY_PREFIX}${userId}`;
    const notificationIds = await redisClient.zrange(key, 0, -1);
    
    let count = 0;
    for (const notificationId of notificationIds) {
      const success = await markNotificationAsRead(notificationId, userId);
      if (success) {
        count++;
      }
    }
    
    return count;
  } catch (error) {
    console.error(`Error marking all notifications as read for user ${userId}:`, error);
    throw error;
  }
};

/**
 * Publish a notification to a channel
 * 
 * @param {string} channel - The channel name (e.g., 'user:123', 'topic:matches')
 * @param {Object} notification - The notification object
 * @returns {Promise<number>} The number of clients that received the notification
 */
const publishNotification = async (channel, notification) => {
  try {
    const channelName = `${NOTIFICATION_CHANNEL_PREFIX}${channel}`;
    return await redisPubSub.publish(channelName, JSON.stringify(notification));
  } catch (error) {
    console.error(`Error publishing notification to channel ${channel}:`, error);
    throw error;
  }
};

/**
 * Subscribe to a notification channel
 * 
 * @param {string} channel - The channel name (e.g., 'user:123', 'topic:matches')
 * @param {Function} callback - The callback function to handle notifications
 * @returns {Promise<void>}
 */
const subscribeToChannel = async (channel, callback) => {
  try {
    const channelName = `${NOTIFICATION_CHANNEL_PREFIX}${channel}`;
    await redisPubSub.subscribe(channelName);
    
    redisPubSub.on('message', (receivedChannel, message) => {
      if (receivedChannel === channelName) {
        callback(JSON.parse(message));
      }
    });
  } catch (error) {
    console.error(`Error subscribing to channel ${channel}:`, error);
    throw error;
  }
};

/**
 * Unsubscribe from a notification channel
 * 
 * @param {string} channel - The channel name
 * @returns {Promise<void>}
 */
const unsubscribeFromChannel = async (channel) => {
  try {
    const channelName = `${NOTIFICATION_CHANNEL_PREFIX}${channel}`;
    await redisPubSub.unsubscribe(channelName);
  } catch (error) {
    console.error(`Error unsubscribing from channel ${channel}:`, error);
    throw error;
  }
};

module.exports = {
  storeNotification,
  addNotificationToUser,
  addNotificationToTopic,
  getNotification,
  getUserNotifications,
  getUnreadCount,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  publishNotification,
  subscribeToChannel,
  unsubscribeFromChannel
};
