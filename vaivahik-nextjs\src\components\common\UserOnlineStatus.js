import React, { useState, useEffect, useContext } from 'react';
import { Box, Typography, Tooltip, Badge } from '@mui/material';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import LockIcon from '@mui/icons-material/Lock';
import { 
  subscribeToUserStatus, 
  getUserOnlineStatus, 
  formatLastActive 
} from '@/services/onlineStatusService';
import { usePrivacy } from '@/contexts/PrivacyContext';
import { AuthContext } from '@/contexts/AuthContext';

/**
 * User Online Status Component
 * 
 * Displays a user's online status and last active time
 * Respects privacy settings
 */
const UserOnlineStatus = ({ userId, showLastActive = true, size = 'medium' }) => {
  const { user } = useContext(AuthContext);
  const { canViewContent } = usePrivacy();
  const [isOnline, setIsOnline] = useState(false);
  const [lastActive, setLastActive] = useState(null);
  const [loading, setLoading] = useState(true);
  
  // Determine if the current user can view this user's online status
  const canViewOnlineStatus = canViewContent('onlineStatus', { id: userId });
  const canViewLastActive = canViewContent('lastActive', { id: userId });
  
  useEffect(() => {
    // Skip if user can't view online status
    if (!canViewOnlineStatus) {
      setLoading(false);
      return;
    }
    
    // Fetch initial status
    const fetchStatus = async () => {
      try {
        const { isOnline } = await getUserOnlineStatus(userId);
        setIsOnline(isOnline);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching online status:', error);
        setLoading(false);
      }
    };
    
    fetchStatus();
    
    // Subscribe to status updates
    const unsubscribe = subscribeToUserStatus(userId, (online, lastActiveTime) => {
      setIsOnline(online);
      setLastActive(lastActiveTime);
    });
    
    // Cleanup subscription on unmount
    return () => {
      unsubscribe();
    };
  }, [userId, canViewOnlineStatus]);
  
  // Size configurations
  const sizeConfig = {
    small: {
      iconSize: 'small',
      badgeSize: 8,
      typography: 'caption'
    },
    medium: {
      iconSize: 'small',
      badgeSize: 10,
      typography: 'body2'
    },
    large: {
      iconSize: 'medium',
      badgeSize: 12,
      typography: 'body1'
    }
  };
  
  const { iconSize, badgeSize, typography } = sizeConfig[size] || sizeConfig.medium;
  
  // If user can't view online status
  if (!canViewOnlineStatus) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <LockIcon fontSize={iconSize} sx={{ color: 'text.secondary', mr: 0.5 }} />
        <Typography variant={typography} color="text.secondary">
          Status hidden
        </Typography>
      </Box>
    );
  }
  
  // If loading
  if (loading) {
    return (
      <Typography variant={typography} color="text.secondary">
        Loading status...
      </Typography>
    );
  }
  
  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Badge
          variant="dot"
          overlap="circular"
          sx={{
            '& .MuiBadge-badge': {
              backgroundColor: isOnline ? 'success.main' : 'text.disabled',
              width: badgeSize,
              height: badgeSize,
              borderRadius: '50%'
            }
          }}
        >
          <FiberManualRecordIcon 
            fontSize={iconSize} 
            sx={{ 
              color: 'transparent',
              mr: 0.5
            }} 
          />
        </Badge>
        <Typography variant={typography} color={isOnline ? 'success.main' : 'text.secondary'}>
          {isOnline ? 'Online' : 'Offline'}
        </Typography>
      </Box>
      
      {showLastActive && !isOnline && lastActive && canViewLastActive && (
        <Tooltip title={new Date(lastActive).toLocaleString()}>
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
            <AccessTimeIcon fontSize={iconSize} sx={{ color: 'text.secondary', mr: 0.5 }} />
            <Typography variant={typography} color="text.secondary">
              Last active {formatLastActive(lastActive)}
            </Typography>
          </Box>
        </Tooltip>
      )}
    </Box>
  );
};

export default UserOnlineStatus;
