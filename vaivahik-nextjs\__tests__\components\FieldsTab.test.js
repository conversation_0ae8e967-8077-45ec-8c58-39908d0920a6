/**
 * Component tests for FieldsTab
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import FieldsTab from '@/components/admin/preferenceConfig/FieldsTab';

// Mock data
const mockCategories = [
  {
    id: 'cat1',
    name: 'physical_attributes',
    displayName: 'Physical Attributes',
    description: 'Physical characteristics preferences',
    displayOrder: 1,
    icon: 'person',
    isActive: true,
    isRequired: true
  }
];

const mockFields = [
  {
    id: 'field1',
    name: 'age_range',
    displayName: 'Age Range',
    description: 'Preferred age range of partner',
    fieldType: 'RANGE',
    displayOrder: 1,
    isActive: true,
    isRequired: true,
    isSearchable: true,
    isMatchCriteria: true,
    defaultValue: '{"min": 21, "max": 35}',
    validationRules: '{"minValue": 18, "maxValue": 70}',
    minValue: 18,
    maxValue: 70,
    stepValue: 1,
    categoryId: 'cat1'
  },
  {
    id: 'field2',
    name: 'education_level',
    displayName: 'Education Level',
    description: 'Preferred education level of partner',
    fieldType: 'MULTI_SELECT',
    displayOrder: 1,
    isActive: true,
    isRequired: false,
    isSearchable: true,
    isMatchCriteria: true,
    defaultValue: '[]',
    categoryId: 'cat1'
  }
];

const mockOptions = [
  {
    id: 'opt1',
    value: 'GRADUATE',
    displayText: 'Graduate',
    description: 'Bachelor\'s degree',
    displayOrder: 1,
    isActive: true,
    fieldId: 'field2'
  },
  {
    id: 'opt2',
    value: 'POST_GRADUATE',
    displayText: 'Post Graduate',
    description: 'Master\'s degree',
    displayOrder: 2,
    isActive: true,
    fieldId: 'field2'
  }
];

// Mock functions
const mockOnUpdate = jest.fn();
const mockOnUpdateOptions = jest.fn();
const mockOnDeleteField = jest.fn();
const mockOnDeleteOption = jest.fn();

describe('FieldsTab Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders fields correctly', async () => {
    render(
      <FieldsTab 
        fields={mockFields} 
        categories={mockCategories} 
        options={mockOptions}
        onUpdate={mockOnUpdate} 
        onUpdateOptions={mockOnUpdateOptions}
        onDeleteField={mockOnDeleteField}
        onDeleteOption={mockOnDeleteOption}
      />
    );

    // Expand the category accordion
    fireEvent.click(screen.getByText('Physical Attributes'));

    // Check if fields are rendered
    await waitFor(() => {
      expect(screen.getByText('Age Range')).toBeInTheDocument();
      expect(screen.getByText('Education Level')).toBeInTheDocument();
    });
  });

  test('opens add field dialog when add button is clicked', async () => {
    render(
      <FieldsTab 
        fields={mockFields} 
        categories={mockCategories} 
        options={mockOptions}
        onUpdate={mockOnUpdate} 
        onUpdateOptions={mockOnUpdateOptions}
        onDeleteField={mockOnDeleteField}
        onDeleteOption={mockOnDeleteOption}
      />
    );

    // Expand the category accordion
    fireEvent.click(screen.getByText('Physical Attributes'));

    // Click add button
    await waitFor(() => {
      fireEvent.click(screen.getByText('Add Field'));
    });

    // Check if dialog is open
    expect(screen.getByText('Add Field')).toBeInTheDocument();
    expect(screen.getByLabelText('Internal Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Display Name')).toBeInTheDocument();
  });

  test('opens edit field dialog when edit button is clicked', async () => {
    render(
      <FieldsTab 
        fields={mockFields} 
        categories={mockCategories} 
        options={mockOptions}
        onUpdate={mockOnUpdate} 
        onUpdateOptions={mockOnUpdateOptions}
        onDeleteField={mockOnDeleteField}
        onDeleteOption={mockOnDeleteOption}
      />
    );

    // Expand the category accordion
    fireEvent.click(screen.getByText('Physical Attributes'));

    // Find and click edit button for first field
    await waitFor(() => {
      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      fireEvent.click(editButtons[0]);
    });

    // Check if dialog is open with correct values
    expect(screen.getByText('Edit Field')).toBeInTheDocument();
    expect(screen.getByDisplayValue('age_range')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Age Range')).toBeInTheDocument();
  });

  test('opens delete confirmation dialog when delete button is clicked', async () => {
    render(
      <FieldsTab 
        fields={mockFields} 
        categories={mockCategories} 
        options={mockOptions}
        onUpdate={mockOnUpdate} 
        onUpdateOptions={mockOnUpdateOptions}
        onDeleteField={mockOnDeleteField}
        onDeleteOption={mockOnDeleteOption}
      />
    );

    // Expand the category accordion
    fireEvent.click(screen.getByText('Physical Attributes'));

    // Find and click delete button for first field
    await waitFor(() => {
      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
      fireEvent.click(deleteButtons[0]);
    });

    // Check if confirmation dialog is open
    expect(screen.getByText('Confirm Delete')).toBeInTheDocument();
    expect(screen.getByText(/Are you sure you want to delete the field/)).toBeInTheDocument();
  });

  test('calls onDeleteField when delete is confirmed', async () => {
    render(
      <FieldsTab 
        fields={mockFields} 
        categories={mockCategories} 
        options={mockOptions}
        onUpdate={mockOnUpdate} 
        onUpdateOptions={mockOnUpdateOptions}
        onDeleteField={mockOnDeleteField}
        onDeleteOption={mockOnDeleteOption}
      />
    );

    // Expand the category accordion
    fireEvent.click(screen.getByText('Physical Attributes'));

    // Find and click delete button for first field
    await waitFor(() => {
      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
      fireEvent.click(deleteButtons[0]);
    });

    // Confirm deletion
    fireEvent.click(screen.getByRole('button', { name: /delete$/i }));

    // Check if onDeleteField was called with correct ID
    expect(mockOnDeleteField).toHaveBeenCalledWith('field1');
  });

  test('opens manage options dialog when manage options button is clicked', async () => {
    render(
      <FieldsTab 
        fields={mockFields} 
        categories={mockCategories} 
        options={mockOptions}
        onUpdate={mockOnUpdate} 
        onUpdateOptions={mockOnUpdateOptions}
        onDeleteField={mockOnDeleteField}
        onDeleteOption={mockOnDeleteOption}
      />
    );

    // Expand the category accordion
    fireEvent.click(screen.getByText('Physical Attributes'));

    // Find and click manage options button for the multi-select field
    await waitFor(() => {
      const manageOptionsButtons = screen.getAllByTitle('Manage Options');
      fireEvent.click(manageOptionsButtons[0]);
    });

    // Check if options dialog is open
    expect(screen.getByText(/Manage Options for/)).toBeInTheDocument();
    expect(screen.getByText('Graduate')).toBeInTheDocument();
    expect(screen.getByText('Post Graduate')).toBeInTheDocument();
  });

  test('calls onUpdate when field is added', async () => {
    render(
      <FieldsTab 
        fields={mockFields} 
        categories={mockCategories} 
        options={mockOptions}
        onUpdate={mockOnUpdate} 
        onUpdateOptions={mockOnUpdateOptions}
        onDeleteField={mockOnDeleteField}
        onDeleteOption={mockOnDeleteOption}
      />
    );

    // Expand the category accordion
    fireEvent.click(screen.getByText('Physical Attributes'));

    // Click add button
    await waitFor(() => {
      fireEvent.click(screen.getByText('Add Field'));
    });

    // Fill in form
    fireEvent.change(screen.getByLabelText('Internal Name'), {
      target: { value: 'new_field' }
    });
    fireEvent.change(screen.getByLabelText('Display Name'), {
      target: { value: 'New Field' }
    });
    
    // Select field type
    fireEvent.mouseDown(screen.getByLabelText('Field Type'));
    fireEvent.click(screen.getByText('Text'));
    
    // Save the field
    fireEvent.click(screen.getByRole('button', { name: /save$/i }));

    // Check if onUpdate was called
    expect(mockOnUpdate).toHaveBeenCalled();
  });
});
