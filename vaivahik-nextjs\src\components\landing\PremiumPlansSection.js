import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Container,
  styled,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Check as CheckIcon,
  Star as StarIcon,
  Diamond as DiamondIcon,
  Crown as CrownIcon
} from '@mui/icons-material';
import { adminGet } from '@/utils/adminApi';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';

const PremiumSection = styled(Box)(({ theme }) => ({
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  padding: '100px 0',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat',
    animation: 'float 20s ease-in-out infinite'
  }
}));

const PremiumCard = styled(Card)(({ theme, isPopular, isAnnual }) => ({
  position: 'relative',
  height: '100%',
  borderRadius: 20,
  overflow: 'hidden',
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  border: isPopular ? '3px solid #FFD700' : '2px solid rgba(255, 255, 255, 0.1)',
  background: isPopular 
    ? 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)'
    : 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(10px)',
  transform: isPopular ? 'scale(1.05)' : 'scale(1)',
  '&:hover': {
    transform: isPopular ? 'scale(1.08) translateY(-10px)' : 'scale(1.03) translateY(-8px)',
    boxShadow: '0 25px 60px rgba(0, 0, 0, 0.2)',
    '& .premium-glow': {
      opacity: 1
    }
  },
  ...(isPopular && {
    '&::before': {
      content: '"Most Popular"',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      background: 'linear-gradient(135deg, #FFD700, #FFA000)',
      color: '#000',
      textAlign: 'center',
      padding: '12px',
      fontWeight: 700,
      fontSize: '0.875rem',
      zIndex: 2,
      textTransform: 'uppercase',
      letterSpacing: '1px'
    }
  })
}));

const PricingToggle = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: '60px',
  background: 'rgba(255, 255, 255, 0.1)',
  borderRadius: '50px',
  padding: '8px',
  backdropFilter: 'blur(10px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  maxWidth: '400px',
  margin: '0 auto 60px auto'
}));

const PremiumPlansSection = () => {
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isAnnual, setIsAnnual] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchPremiumPlans();
  }, []);

  const fetchPremiumPlans = async () => {
    try {
      setLoading(true);

      // Try to fetch from public API first, then fallback to admin API
      let response;
      try {
        // Use public endpoint (no authentication required)
        const publicResponse = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:8000/api'}/premium-plans`);
        if (publicResponse.ok) {
          response = await publicResponse.json();
        } else {
          throw new Error('Public API failed');
        }
      } catch (publicError) {
        console.log('Public API failed, trying admin API:', publicError.message);
        // Fallback to admin API
        response = await adminGet(ADMIN_ENDPOINTS.PREMIUM_PLANS);
      }
      
      if (response.success && response.plans) {
        // Transform admin plans for landing page display
        const transformedPlans = response.plans
          .filter(plan => plan.isActive)
          .map(plan => ({
            id: plan.id,
            name: plan.name,
            planType: plan.planType,
            price: plan.amount || plan.price,
            originalPrice: plan.hasDiscount ? plan.amount : null,
            discountedPrice: plan.hasDiscount ? plan.discountedAmount : null,
            discount: plan.hasDiscount ? plan.discountValue : null,
            duration: getDurationText(plan.durationDays),
            description: plan.description,
            features: plan.features || [],
            isPopular: plan.isPopular,
            currency: plan.currency || 'INR'
          }));
        
        setPlans(transformedPlans);
      } else {
        // Fallback to default plans if admin API fails
        setPlans(getDefaultPlans());
      }
    } catch (error) {
      console.error('Error fetching premium plans:', error);
      setError('Failed to load plans');
      // Use default plans as fallback
      setPlans(getDefaultPlans());
    } finally {
      setLoading(false);
    }
  };

  const getDefaultPlans = () => [
    {
      id: 'basic',
      name: 'Basic',
      planType: 'MONTHLY',
      price: 499,
      originalPrice: 699,
      discount: 29,
      duration: '1 Month',
      description: 'Perfect for getting started with premium features',
      features: [
        'View contact details (5 per day)',
        'Send messages (10 per day)', 
        'Basic search filters',
        'Profile views tracking',
        'Customer support'
      ],
      isPopular: false,
      currency: 'INR'
    },
    {
      id: 'premium',
      name: 'Premium',
      planType: 'QUARTERLY',
      price: 1299,
      originalPrice: 1899,
      discount: 32,
      duration: '3 Months',
      description: 'Most popular choice for serious matrimony seekers',
      features: [
        'Unlimited contact details',
        'Unlimited messages',
        'Advanced search filters',
        'Priority in search results',
        'Profile highlighting',
        'Horoscope matching',
        'Chat with verified users',
        'Priority customer support'
      ],
      isPopular: true,
      currency: 'INR'
    },
    {
      id: 'elite',
      name: 'Elite',
      planType: 'ANNUAL',
      price: 4999,
      originalPrice: 7999,
      discount: 38,
      duration: '12 Months',
      description: 'Ultimate matrimony experience with exclusive benefits',
      features: [
        'All Premium features',
        'Dedicated relationship manager',
        'Profile verification priority',
        'Exclusive elite member events',
        'AI-powered match recommendations',
        'Video call feature',
        'Profile boost (weekly)',
        'Success guarantee',
        'VIP customer support'
      ],
      isPopular: false,
      currency: 'INR'
    }
  ];

  const getDurationText = (days) => {
    if (days <= 31) return '1 Month';
    if (days <= 93) return '3 Months';
    if (days <= 186) return '6 Months';
    return '12 Months';
  };

  const getPlanIcon = (planType) => {
    switch (planType) {
      case 'ANNUAL':
        return <DiamondIcon sx={{ fontSize: 48, color: '#9C27B0' }} />;
      case 'QUARTERLY':
        return <CrownIcon sx={{ fontSize: 48, color: '#FFD700' }} />;
      default:
        return <StarIcon sx={{ fontSize: 48, color: '#667eea' }} />;
    }
  };

  const handlePlanSelect = (plan) => {
    // Redirect to registration or login with selected plan
    window.location.href = `/register?plan=${plan.id}`;
  };

  if (loading) {
    return (
      <PremiumSection>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Typography variant="h4" sx={{ color: 'white', mb: 2 }}>
              Loading Premium Plans...
            </Typography>
          </Box>
        </Container>
      </PremiumSection>
    );
  }

  return (
    <PremiumSection id="pricing">
      <Container maxWidth="lg">
        {/* Section Header */}
        <Box sx={{ textAlign: 'center', mb: 8 }}>
          <Typography 
            variant="h2" 
            fontWeight="800" 
            sx={{ 
              color: 'white', 
              mb: 3,
              background: 'linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}
          >
            💎 Premium Plans
          </Typography>
          <Typography 
            variant="h5" 
            sx={{ 
              color: 'rgba(255, 255, 255, 0.9)', 
              mb: 4,
              maxWidth: '600px',
              mx: 'auto',
              lineHeight: 1.6
            }}
          >
            Choose the perfect plan to find your life partner with our advanced AI-powered matching
          </Typography>
        </Box>

        {/* Pricing Toggle */}
        <PricingToggle>
          <FormControlLabel
            control={
              <Switch
                checked={isAnnual}
                onChange={(e) => setIsAnnual(e.target.checked)}
                sx={{
                  '& .MuiSwitch-switchBase.Mui-checked': {
                    color: '#FFD700',
                  },
                  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                    backgroundColor: '#FFD700',
                  },
                }}
              />
            }
            label={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Typography sx={{ color: 'white', fontWeight: 600 }}>
                  Monthly
                </Typography>
                <Typography sx={{ color: 'white', fontWeight: 600 }}>
                  Annual
                </Typography>
                <Chip 
                  label="Save 38%" 
                  size="small" 
                  sx={{ 
                    background: 'linear-gradient(135deg, #4CAF50, #8BC34A)',
                    color: 'white',
                    fontWeight: 700
                  }} 
                />
              </Box>
            }
            sx={{ color: 'white' }}
          />
        </PricingToggle>

        {/* Plans Grid */}
        <Grid container spacing={4} justifyContent="center">
          {plans.map((plan) => (
            <Grid item xs={12} md={4} key={plan.id}>
              <PremiumCard isPopular={plan.isPopular} isAnnual={isAnnual}>
                <CardContent sx={{ p: 4, pt: plan.isPopular ? 6 : 4, height: '100%', display: 'flex', flexDirection: 'column' }}>
                  {/* Plan Header */}
                  <Box sx={{ textAlign: 'center', mb: 4 }}>
                    <Box sx={{ mb: 2 }}>
                      {getPlanIcon(plan.planType)}
                    </Box>
                    <Typography variant="h4" fontWeight="700" gutterBottom>
                      {plan.name}
                    </Typography>
                    <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                      {plan.description}
                    </Typography>
                    
                    {/* Pricing */}
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="h2" fontWeight="800" sx={{
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        backgroundClip: 'text',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent'
                      }}>
                        ₹{plan.discountedPrice || plan.price}
                      </Typography>
                      <Typography variant="body1" color="text.secondary">
                        {plan.duration}
                      </Typography>
                      
                      {plan.discount && (
                        <Box sx={{ mt: 1 }}>
                          <Typography 
                            variant="body2" 
                            sx={{ 
                              textDecoration: 'line-through',
                              color: 'text.secondary',
                              display: 'inline',
                              mr: 1
                            }}
                          >
                            ₹{plan.originalPrice}
                          </Typography>
                          <Chip
                            label={`${plan.discount}% OFF`}
                            color="success"
                            size="small"
                            sx={{ fontWeight: 700 }}
                          />
                        </Box>
                      )}
                    </Box>
                  </Box>

                  {/* Features List */}
                  <List dense sx={{ flex: 1 }}>
                    {plan.features.map((feature, index) => (
                      <ListItem key={index} sx={{ px: 0, py: 0.5 }}>
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          <CheckIcon sx={{ color: '#4CAF50', fontSize: 20 }} />
                        </ListItemIcon>
                        <ListItemText 
                          primary={typeof feature === 'string' ? feature : feature.name}
                          primaryTypographyProps={{
                            fontSize: '0.875rem',
                            fontWeight: 500
                          }}
                        />
                      </ListItem>
                    ))}
                  </List>

                  {/* CTA Button */}
                  <Button
                    variant="contained"
                    size="large"
                    fullWidth
                    onClick={() => handlePlanSelect(plan)}
                    sx={{
                      mt: 3,
                      py: 2,
                      borderRadius: 3,
                      fontWeight: 700,
                      fontSize: '1.1rem',
                      background: plan.isPopular 
                        ? 'linear-gradient(135deg, #FFD700, #FFA000)'
                        : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      color: plan.isPopular ? '#000' : 'white',
                      '&:hover': {
                        background: plan.isPopular
                          ? 'linear-gradient(135deg, #FFC107, #FF8F00)'
                          : 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                        transform: 'translateY(-2px)',
                        boxShadow: '0 8px 25px rgba(0,0,0,0.2)'
                      }
                    }}
                  >
                    {plan.isPopular ? '🚀 Get Started' : 'Choose Plan'}
                  </Button>
                </CardContent>
              </PremiumCard>
            </Grid>
          ))}
        </Grid>

        {/* Bottom CTA */}
        <Box sx={{ textAlign: 'center', mt: 8 }}>
          <Typography variant="body1" sx={{ color: 'rgba(255, 255, 255, 0.8)', mb: 2 }}>
            All plans include 24/7 customer support and 30-day money-back guarantee
          </Typography>
          <Button
            variant="outlined"
            size="large"
            sx={{
              borderColor: 'white',
              color: 'white',
              borderRadius: 3,
              px: 4,
              py: 1.5,
              '&:hover': {
                borderColor: '#FFD700',
                color: '#FFD700',
                background: 'rgba(255, 215, 0, 0.1)'
              }
            }}
          >
            Compare All Features
          </Button>
        </Box>
      </Container>
    </PremiumSection>
  );
};

export default PremiumPlansSection;
