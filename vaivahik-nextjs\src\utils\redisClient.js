/**
 * Redis Client Utility
 * 
 * This module provides a Redis client for caching data and reducing database load.
 * It uses the ioredis library for Redis operations and includes utility functions
 * for common caching operations.
 */

import Redis from 'ioredis';

// Redis configuration
const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';
const REDIS_PASSWORD = process.env.REDIS_PASSWORD || '';

// Cache TTL defaults (in seconds)
export const CACHE_TTL = {
  DASHBOARD_STATS: 300, // 5 minutes
  DASHBOARD_CHARTS: 600, // 10 minutes
  USER_DATA: 1800, // 30 minutes
  ACTIVITY_DATA: 60, // 1 minute
  DEFAULT: 300 // 5 minutes
};

// Redis client instance
let redisClient = null;

/**
 * Initialize Redis client
 * @returns {Redis} Redis client instance
 */
export const getRedisClient = () => {
  if (!redisClient) {
    try {
      // Create Redis client with configuration
      redisClient = new Redis(REDIS_URL, {
        password: REDIS_PASSWORD,
        retryStrategy: (times) => {
          // Retry connection with exponential backoff
          const delay = Math.min(times * 50, 2000);
          return delay;
        },
        maxRetriesPerRequest: 3,
        enableReadyCheck: true,
        connectTimeout: 10000,
        keepAlive: 10000,
      });

      // Log connection status
      redisClient.on('connect', () => {
        console.log('Redis client connected');
      });

      redisClient.on('error', (err) => {
        console.error('Redis client error:', err);
      });

      redisClient.on('reconnecting', () => {
        console.log('Redis client reconnecting');
      });
    } catch (error) {
      console.error('Failed to initialize Redis client:', error);
      return null;
    }
  }

  return redisClient;
};

/**
 * Set data in Redis cache
 * @param {string} key - Cache key
 * @param {any} data - Data to cache (will be JSON stringified)
 * @param {number} ttl - Time to live in seconds
 * @returns {Promise<boolean>} Success status
 */
export const setCache = async (key, data, ttl = CACHE_TTL.DEFAULT) => {
  try {
    const client = getRedisClient();
    if (!client) return false;

    // Stringify data for storage
    const serializedData = JSON.stringify(data);
    
    // Set with expiration
    await client.setex(key, ttl, serializedData);
    return true;
  } catch (error) {
    console.error(`Redis setCache error for key ${key}:`, error);
    return false;
  }
};

/**
 * Get data from Redis cache
 * @param {string} key - Cache key
 * @returns {Promise<any>} Cached data or null if not found
 */
export const getCache = async (key) => {
  try {
    const client = getRedisClient();
    if (!client) return null;

    // Get data from Redis
    const data = await client.get(key);
    
    // Return null if no data found
    if (!data) return null;
    
    // Parse and return data
    return JSON.parse(data);
  } catch (error) {
    console.error(`Redis getCache error for key ${key}:`, error);
    return null;
  }
};

/**
 * Delete data from Redis cache
 * @param {string} key - Cache key
 * @returns {Promise<boolean>} Success status
 */
export const deleteCache = async (key) => {
  try {
    const client = getRedisClient();
    if (!client) return false;

    await client.del(key);
    return true;
  } catch (error) {
    console.error(`Redis deleteCache error for key ${key}:`, error);
    return false;
  }
};

/**
 * Delete multiple keys by pattern
 * @param {string} pattern - Key pattern to match (e.g., "dashboard:*")
 * @returns {Promise<boolean>} Success status
 */
export const deleteCacheByPattern = async (pattern) => {
  try {
    const client = getRedisClient();
    if (!client) return false;

    // Find keys matching pattern
    const keys = await client.keys(pattern);
    
    // If keys found, delete them
    if (keys.length > 0) {
      await client.del(...keys);
      console.log(`Deleted ${keys.length} keys matching pattern: ${pattern}`);
    }
    
    return true;
  } catch (error) {
    console.error(`Redis deleteCacheByPattern error for pattern ${pattern}:`, error);
    return false;
  }
};

/**
 * Get time-to-live for a key
 * @param {string} key - Cache key
 * @returns {Promise<number>} TTL in seconds or -1 if key doesn't exist
 */
export const getCacheTTL = async (key) => {
  try {
    const client = getRedisClient();
    if (!client) return -1;

    return await client.ttl(key);
  } catch (error) {
    console.error(`Redis getCacheTTL error for key ${key}:`, error);
    return -1;
  }
};

export default {
  getRedisClient,
  setCache,
  getCache,
  deleteCache,
  deleteCacheByPattern,
  getCacheTTL,
  CACHE_TTL
};
