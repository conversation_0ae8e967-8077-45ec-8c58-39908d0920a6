// src/controllers/admin/biodata.controller.js

const logger = require('../../utils/logger');

// Premium biodata templates with world-class designs
const mockBiodataTemplates = [
    {
        id: 1,
        name: "🏛️ Traditional Heritage",
        description: "Timeless Maratha design with cultural elegance and traditional motifs",
        previewImage: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=400&fit=crop&crop=center",
        designFile: "/templates/biodata/traditional-heritage.html",
        price: 399,
        discountPercent: 33,
        discountedPrice: 267,
        isActive: true,
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        updatedAt: new Date(),
        purchaseCount: 285,
        downloadCount: 238,
        revenue: 113715,
        category: 'Traditional',
        targetGender: 'male',
        isPremium: false
    },
    {
        id: 2,
        name: "🌹 Elegant Floral",
        description: "Sophisticated floral design with delicate patterns and feminine grace",
        previewImage: "https://images.unsplash.com/photo-1490750967868-88aa4486c946?w=300&h=400&fit=crop&crop=center",
        designFile: "/templates/biodata/elegant-floral.html",
        price: 499,
        discountPercent: 29,
        discountedPrice: 354,
        isActive: true,
        createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000),
        updatedAt: new Date(),
        purchaseCount: 198,
        downloadCount: 178,
        revenue: 70092,
        category: 'Elegant',
        targetGender: 'female',
        isPremium: true
    },
    {
        id: 3,
        name: "💼 Executive Premium",
        description: "Ultra-luxury design for high-profile professionals with gold accents",
        previewImage: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=400&fit=crop&crop=center",
        designFile: "/templates/biodata/executive-premium.html",
        price: 799,
        discountPercent: 33,
        discountedPrice: 535,
        isActive: true,
        createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
        updatedAt: new Date(),
        purchaseCount: 125,
        downloadCount: 115,
        revenue: 66875,
        category: 'Premium',
        targetGender: 'male',
        isPremium: true
    },
    {
        id: 4,
        name: "👑 Luxury Royal",
        description: "Majestic royal design with premium gradients and stunning animations",
        previewImage: "https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=300&h=400&fit=crop&crop=center",
        designFile: "/templates/biodata/luxury-royal.html",
        price: 999,
        discountPercent: 33,
        discountedPrice: 669,
        isActive: true,
        createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
        updatedAt: new Date(),
        purchaseCount: 89,
        downloadCount: 82,
        revenue: 59541,
        category: 'Royal',
        targetGender: 'unisex',
        isPremium: true
    },
    {
        id: 5,
        name: "🎨 Modern Artistic",
        description: "Contemporary artistic design with creative layouts and vibrant colors",
        previewImage: "https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=300&h=400&fit=crop&crop=center",
        designFile: "/templates/biodata/modern-artistic.html",
        price: 599,
        discountPercent: 25,
        discountedPrice: 449,
        isActive: true,
        createdAt: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000),
        updatedAt: new Date(),
        purchaseCount: 165,
        downloadCount: 152,
        revenue: 74085,
        category: 'Modern',
        targetGender: 'female',
        isPremium: true
    },
    {
        id: 6,
        name: "🌟 Contemporary Chic",
        description: "Trendy and stylish design perfect for modern millennials",
        previewImage: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300&h=400&fit=crop&crop=center",
        designFile: "/templates/biodata/contemporary-chic.html",
        price: 449,
        discountPercent: 25,
        discountedPrice: 337,
        isActive: true,
        createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
        updatedAt: new Date(),
        purchaseCount: 210,
        downloadCount: 195,
        revenue: 70770,
        category: 'Contemporary',
        targetGender: 'female',
        isPremium: false
    },
    {
        id: 7,
        name: "🏢 Professional Classic",
        description: "Clean professional design ideal for corporate professionals",
        previewImage: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=400&fit=crop&crop=center",
        designFile: "/templates/biodata/professional-classic.html",
        price: 349,
        discountPercent: 30,
        discountedPrice: 244,
        isActive: true,
        createdAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000),
        updatedAt: new Date(),
        purchaseCount: 320,
        downloadCount: 298,
        revenue: 78080,
        category: 'Professional',
        targetGender: 'unisex',
        isPremium: false
    },
    {
        id: 8,
        name: "🕉️ Cultural Grace",
        description: "Spiritual and cultural design with traditional Indian elements",
        previewImage: "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=300&h=400&fit=crop&crop=center",
        designFile: "/templates/biodata/cultural-grace.html",
        price: 399,
        discountPercent: 27,
        discountedPrice: 291,
        isActive: true,
        createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
        updatedAt: new Date(),
        purchaseCount: 185,
        downloadCount: 172,
        revenue: 53835,
        category: 'Cultural',
        targetGender: 'unisex',
        isPremium: false
    }
];

// Check if mock data should be used
const shouldUseMockData = () => {
    return process.env.NODE_ENV === 'development' || process.env.USE_MOCK_DATA === 'true';
};

/**
 * @description Get all biodata templates
 * @route GET /api/admin/biodata-templates
 */
exports.getBiodataTemplates = async (req, res, next) => {
    try {
        const {
            page = 1,
            limit = 10,
            search = '',
            sortBy = 'createdAt',
            order = 'desc',
            isActive = ''
        } = req.query;

        const pageNum = parseInt(page, 10);
        const limitNum = parseInt(limit, 10);

        if (isNaN(pageNum) || pageNum < 1 || isNaN(limitNum) || limitNum < 1) {
            const error = new Error("Invalid page or limit parameter.");
            error.status = 400;
            return next(error);
        }

        if (shouldUseMockData()) {
            // Return mock data
            let filteredTemplates = [...mockBiodataTemplates];

            // Apply search filter
            if (search) {
                filteredTemplates = filteredTemplates.filter(template =>
                    template.name.toLowerCase().includes(search.toLowerCase()) ||
                    template.description.toLowerCase().includes(search.toLowerCase())
                );
            }

            // Apply isActive filter
            if (isActive !== '') {
                filteredTemplates = filteredTemplates.filter(template =>
                    template.isActive === (isActive === 'true')
                );
            }

            // Apply sorting
            const sortOrder = order.toLowerCase() === 'asc' ? 1 : -1;
            filteredTemplates.sort((a, b) => {
                const aValue = a[sortBy];
                const bValue = b[sortBy];
                if (aValue < bValue) return -1 * sortOrder;
                if (aValue > bValue) return 1 * sortOrder;
                return 0;
            });

            // Apply pagination
            const skip = (pageNum - 1) * limitNum;
            const paginatedTemplates = filteredTemplates.slice(skip, skip + limitNum);

            return res.status(200).json({
                success: true,
                message: "Biodata templates fetched successfully (Mock Data)",
                templates: paginatedTemplates,
                pagination: {
                    currentPage: pageNum,
                    limit: limitNum,
                    totalPages: Math.ceil(filteredTemplates.length / limitNum),
                    totalTemplates: filteredTemplates.length
                },
                useMockData: true
            });
        }

        // Real database implementation
        const prisma = req.prisma;
        const skip = (pageNum - 1) * limitNum;
        const take = limitNum;
        const sortOrder = order.toLowerCase() === 'asc' ? 'asc' : 'desc';

        // Check if BiodataTemplate model exists
        let templateModelExists = true;
        try {
            await prisma.biodataTemplate.findFirst();
        } catch (e) {
            if (e.message.includes('does not exist in the current database')) {
                templateModelExists = false;
            } else {
                throw e;
            }
        }

        if (!templateModelExists) {
            return res.status(200).json({
                success: true,
                message: "Biodata template data not yet available. Please run database migrations first.",
                templates: [],
                pagination: { currentPage: 1, limit: take, totalPages: 0, totalTemplates: 0 },
                useMockData: false
            });
        }

        // Build where clause based on filters
        let whereClause = {};

        if (isActive !== '') {
            whereClause.isActive = isActive === 'true';
        }
        
        if (search) {
            whereClause.OR = [
                {
                    name: {
                        contains: search,
                        mode: 'insensitive'
                    }
                },
                {
                    description: {
                        contains: search,
                        mode: 'insensitive'
                    }
                }
            ];
        }

        // Get total count for pagination
        const totalTemplates = await prisma.biodataTemplate.count({
            where: whereClause
        });

        // Get templates with pagination, sorting, and filtering
        const templates = await prisma.biodataTemplate.findMany({
            where: whereClause,
            orderBy: {
                [sortBy]: sortOrder
            },
            skip,
            take
        });

        // Get usage statistics for each template
        const enhancedTemplates = await Promise.all(templates.map(async (template) => {
            // Get purchase count
            const purchaseCount = await prisma.userBiodata.count({
                where: {
                    templateId: template.id
                }
            });

            // Get download count
            const downloadStats = await prisma.userBiodata.aggregate({
                where: {
                    templateId: template.id
                },
                _sum: {
                    downloadCount: true
                }
            });

            // Get revenue
            const revenueStats = await prisma.userBiodata.aggregate({
                where: {
                    templateId: template.id
                },
                _sum: {
                    pricePaid: true
                }
            });

            return {
                ...template,
                purchaseCount: purchaseCount || 0,
                downloadCount: downloadStats._sum?.downloadCount || 0,
                revenue: revenueStats._sum?.pricePaid || 0
            };
        }));

        const totalPages = Math.ceil(totalTemplates / take);

        res.status(200).json({
            message: "Biodata templates fetched successfully.",
            templates: enhancedTemplates,
            pagination: {
                currentPage: pageNum,
                limit: take,
                totalPages,
                totalTemplates
            }
        });
    } catch (error) {
        console.error("Error fetching biodata templates:", error);
        next(error);
    }
};

/**
 * @description Get biodata template by ID
 * @route GET /api/admin/biodata/templates/:id
 */
exports.getBiodataTemplateById = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;

    try {
        const template = await prisma.biodataTemplate.findUnique({
            where: { id }
        });

        if (!template) {
            return res.status(404).json({
                message: "Biodata template not found."
            });
        }

        // Get usage statistics
        const purchaseCount = await prisma.userBiodata.count({
            where: {
                templateId: template.id
            }
        });

        const downloadStats = await prisma.userBiodata.aggregate({
            where: {
                templateId: template.id
            },
            _sum: {
                downloadCount: true
            }
        });

        const revenueStats = await prisma.userBiodata.aggregate({
            where: {
                templateId: template.id
            },
            _sum: {
                pricePaid: true
            }
        });

        const enhancedTemplate = {
            ...template,
            purchaseCount: purchaseCount || 0,
            downloadCount: downloadStats._sum?.downloadCount || 0,
            revenue: revenueStats._sum?.pricePaid || 0
        };

        res.status(200).json({
            message: "Biodata template fetched successfully.",
            template: enhancedTemplate
        });
    } catch (error) {
        console.error("Error fetching biodata template:", error);
        next(error);
    }
};

/**
 * @description Create a new biodata template
 * @route POST /api/admin/biodata/templates
 */
exports.createBiodataTemplate = async (req, res, next) => {
    const prisma = req.prisma;
    const { 
        name, 
        description, 
        previewImage, 
        designFile, 
        price, 
        discountPercent, 
        isActive 
    } = req.body;

    try {
        // Validate required fields
        if (!name || !previewImage || !designFile || !price) {
            return res.status(400).json({
                message: "Name, preview image, design file, and price are required."
            });
        }

        // Parse numeric values
        const parsedPrice = parseFloat(price);
        const parsedDiscountPercent = discountPercent ? parseInt(discountPercent) : null;

        if (isNaN(parsedPrice) || parsedPrice < 0) {
            return res.status(400).json({
                message: "Price must be a valid number greater than or equal to 0."
            });
        }

        if (parsedDiscountPercent !== null && (isNaN(parsedDiscountPercent) || parsedDiscountPercent < 0 || parsedDiscountPercent > 100)) {
            return res.status(400).json({
                message: "Discount percent must be a valid number between 0 and 100."
            });
        }

        // Calculate discounted price if discount percent is provided
        const discountedPrice = parsedDiscountPercent 
            ? parsedPrice * (1 - parsedDiscountPercent / 100) 
            : null;

        // Create the template
        const template = await prisma.biodataTemplate.create({
            data: {
                name,
                description,
                previewImage,
                designFile,
                price: parsedPrice,
                discountPercent: parsedDiscountPercent,
                discountedPrice,
                isActive: isActive === true || isActive === 'true'
            }
        });

        res.status(201).json({
            message: "Biodata template created successfully.",
            template
        });
    } catch (error) {
        console.error("Error creating biodata template:", error);
        next(error);
    }
};

/**
 * @description Update a biodata template
 * @route PUT /api/admin/biodata/templates/:id
 */
exports.updateBiodataTemplate = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;
    const { 
        name, 
        description, 
        previewImage, 
        designFile, 
        price, 
        discountPercent, 
        isActive 
    } = req.body;

    try {
        // Check if template exists
        const existingTemplate = await prisma.biodataTemplate.findUnique({
            where: { id }
        });

        if (!existingTemplate) {
            return res.status(404).json({
                message: "Biodata template not found."
            });
        }

        // Prepare update data
        const updateData = {};

        if (name !== undefined) updateData.name = name;
        if (description !== undefined) updateData.description = description;
        if (previewImage !== undefined) updateData.previewImage = previewImage;
        if (designFile !== undefined) updateData.designFile = designFile;
        if (isActive !== undefined) updateData.isActive = isActive === true || isActive === 'true';

        // Handle price and discount updates
        if (price !== undefined) {
            const parsedPrice = parseFloat(price);
            if (isNaN(parsedPrice) || parsedPrice < 0) {
                return res.status(400).json({
                    message: "Price must be a valid number greater than or equal to 0."
                });
            }
            updateData.price = parsedPrice;
        }

        if (discountPercent !== undefined) {
            const parsedDiscountPercent = discountPercent ? parseInt(discountPercent) : null;
            if (parsedDiscountPercent !== null && (isNaN(parsedDiscountPercent) || parsedDiscountPercent < 0 || parsedDiscountPercent > 100)) {
                return res.status(400).json({
                    message: "Discount percent must be a valid number between 0 and 100."
                });
            }
            updateData.discountPercent = parsedDiscountPercent;

            // Recalculate discounted price
            const priceToUse = updateData.price !== undefined ? updateData.price : existingTemplate.price;
            updateData.discountedPrice = parsedDiscountPercent 
                ? priceToUse * (1 - parsedDiscountPercent / 100) 
                : null;
        } else if (updateData.price !== undefined && existingTemplate.discountPercent) {
            // Recalculate discounted price if price changed but discount percent didn't
            updateData.discountedPrice = updateData.price * (1 - existingTemplate.discountPercent / 100);
        }

        // Update the template
        const updatedTemplate = await prisma.biodataTemplate.update({
            where: { id },
            data: updateData
        });

        res.status(200).json({
            message: "Biodata template updated successfully.",
            template: updatedTemplate
        });
    } catch (error) {
        console.error("Error updating biodata template:", error);
        next(error);
    }
};

/**
 * @description Delete a biodata template
 * @route DELETE /api/admin/biodata/templates/:id
 */
exports.deleteBiodataTemplate = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;

    try {
        // Check if template exists
        const existingTemplate = await prisma.biodataTemplate.findUnique({
            where: { id }
        });

        if (!existingTemplate) {
            return res.status(404).json({
                message: "Biodata template not found."
            });
        }

        // Check if template is in use
        const usageCount = await prisma.userBiodata.count({
            where: {
                templateId: id
            }
        });

        if (usageCount > 0) {
            return res.status(400).json({
                message: "Cannot delete template as it is in use by users.",
                usageCount
            });
        }

        // Delete the template
        await prisma.biodataTemplate.delete({
            where: { id }
        });

        res.status(200).json({
            message: "Biodata template deleted successfully."
        });
    } catch (error) {
        console.error("Error deleting biodata template:", error);
        next(error);
    }
};
