/**
 * Modern Family Details Page
 *
 * This page allows users to update their family details with a modern UI.
 * Part of the profile completion flow after registration.
 */

import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Container,
  Box,
  Alert,
  CircularProgress,
  Snackbar,
  Typography,
  Breadcrumbs,
  Link as MuiLink
} from '@mui/material';
import { NavigateNext as NavigateNextIcon } from '@mui/icons-material';
import Link from 'next/link';
import ModernFamilyDetailsForm from '@/components/profile/ModernFamilyDetailsForm';
import { useAuth } from '@/contexts/AuthContext';
import { isUsingRealBackend } from '@/utils/apiUtils';
import { updateFamilyDetails } from '@/services/userApiService';
import { formatError, getUserFriendlyMessage, isNetworkError } from '@/utils/errorHandling';
import { withRetry } from '@/utils/retryLogic';

const ModernFamilyDetailsPage = () => {
  const router = useRouter();
  const { userData, setUserData } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Fetch user data if not available in context
  useEffect(() => {
    const fetchUserData = async () => {
      if (!userData) {
        try {
          const response = await api.get('/api/users/profile');
          setUserData(response.data);
        } catch (err) {
          setError('Failed to load user data. Please try again later.');
        }
      }
      setLoading(false);
    };

    fetchUserData();
  }, [userData, setUserData]);

  // Handle save
  const handleSave = async (formData) => {
    try {
      setSaving(true);
      setError(''); // Clear previous errors

      if (isUsingRealBackend()) {
        // Define the API call function
        const saveData = async () => {
          try {
            return await updateFamilyDetails(formData);
          } catch (apiError) {
            // Format the error for better user feedback
            const formattedError = formatError(apiError);
            throw formattedError; // Rethrow for retry logic to catch
          }
        };

        // Call API with retry logic for network errors
        const response = await withRetry(saveData, {
          maxRetries: 3,
          retryCondition: isNetworkError
        });

        setSuccess('Family details saved successfully!');

        // Update local user data
        setUserData(prev => ({
          ...prev,
          familyDetails: response.data?.familyDetails || formData
        }));
      } else {
        // Simulate API call
        setTimeout(() => {
          setSuccess('Family details saved successfully!');

          // Update local user data
          setUserData(prev => ({
            ...prev,
            familyDetails: formData,
            profileCompletionPercentage: Math.min(85, (prev?.profileCompletionPercentage || 60) + 5)
          }));

          setSaving(false);
        }, 1000);
        return;
      }

      setSaving(false);

      // Redirect to next step after a short delay
      setTimeout(() => {
        router.push('/website/pages/profile/edit/preferences');
      }, 2000);
    } catch (err) {
      console.error('Error saving family details:', err);

      // Get user-friendly error message
      const errorMessage = getUserFriendlyMessage(err);
      setError(errorMessage);

      setSaving(false);
    }
  };

  return (
    <>
      <Head>
        <title>Family Details | Vaivahik</title>
        <meta name="description" content="Update your family details on Vaivahik matrimony" />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Breadcrumbs */}
        <Breadcrumbs
          separator={<NavigateNextIcon fontSize="small" />}
          aria-label="breadcrumb"
          sx={{ mb: 3 }}
        >
          <Link href="/website/pages/profile/dashboard" passHref>
            <MuiLink underline="hover" color="inherit">Profile</MuiLink>
          </Link>
          <Link href="/website/pages/profile/dashboard" passHref>
            <MuiLink underline="hover" color="inherit">Complete Profile</MuiLink>
          </Link>
          <Typography color="text.primary">Family Details</Typography>
        </Breadcrumbs>

        {/* Progress indicator */}
        <Box sx={{ mb: 4, display: 'flex', alignItems: 'center' }}>
          <Box sx={{ position: 'relative', display: 'inline-flex', mr: 2 }}>
            <CircularProgress
              variant="determinate"
              value={userData?.profileCompletionPercentage || 0}
              size={60}
              thickness={4}
              sx={{ color: 'var(--primary-color)' }}
            />
            <Box
              sx={{
                top: 0,
                left: 0,
                bottom: 0,
                right: 0,
                position: 'absolute',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Typography variant="caption" component="div" color="text.secondary">
                {`${userData?.profileCompletionPercentage || 0}%`}
              </Typography>
            </Box>
          </Box>
          <Box>
            <Typography variant="subtitle1" fontWeight={600}>
              Profile Completion
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Complete your profile to get more matches
            </Typography>
          </Box>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
            <CircularProgress />
          </Box>
        ) : (
          <ModernFamilyDetailsForm
            userData={userData}
            onSave={handleSave}
            isLoading={saving}
          />
        )}
      </Container>

      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess('')}
        message={success}
      />
    </>
  );
};

export default ModernFamilyDetailsPage;
