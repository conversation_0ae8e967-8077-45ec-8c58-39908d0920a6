/*
  Warnings:

  - You are about to drop the column `birth_date` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `birth_place` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `birth_time` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `city` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `education` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `family_contact` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `father_name` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `fullName` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `gender` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `height` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `income_range` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `married_siblings` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `mother_name` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `native_place` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `occupation` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `profile_picture_url` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `profile_picture_visibility` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `total_siblings` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `uncle_name` on the `users` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "users" DROP COLUMN "birth_date",
DROP COLUMN "birth_place",
DROP COLUMN "birth_time",
DROP COLUMN "city",
DROP COLUMN "education",
DROP COLUMN "family_contact",
DROP COLUMN "father_name",
DROP COLUMN "fullName",
DROP COLUMN "gender",
DROP COLUMN "height",
DROP COLUMN "income_range",
DROP COLUMN "married_siblings",
DROP COLUMN "mother_name",
DROP COLUMN "native_place",
DROP COLUMN "occupation",
DROP COLUMN "profile_picture_url",
DROP COLUMN "profile_picture_visibility",
DROP COLUMN "total_siblings",
DROP COLUMN "uncle_name";

-- CreateTable
CREATE TABLE "profiles" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "fullName" TEXT,
    "gender" TEXT,
    "birth_date" TIMESTAMP(3),
    "birth_time" TEXT,
    "birth_place" TEXT,
    "height" TEXT,
    "city" TEXT,
    "education" TEXT,
    "occupation" TEXT,
    "income_range" TEXT,
    "profile_picture_url" TEXT,
    "profile_picture_visibility" "PhotoVisibility" NOT NULL DEFAULT 'PUBLIC',
    "father_name" TEXT,
    "mother_name" TEXT,
    "uncle_name" TEXT,
    "native_place" TEXT,
    "total_siblings" INTEGER,
    "married_siblings" INTEGER,
    "family_contact" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "profiles_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "profiles_user_id_key" ON "profiles"("user_id");

-- AddForeignKey
ALTER TABLE "profiles" ADD CONSTRAINT "profiles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
