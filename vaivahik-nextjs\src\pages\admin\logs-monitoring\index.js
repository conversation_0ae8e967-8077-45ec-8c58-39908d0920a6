import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  Alert,
  CircularProgress,
  Switch,
  FormControlLabel,
  Tooltip,
  Stack,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  InputAdornment,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Assignment as LogsIcon,
  Monitor as MonitorIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Settings as SettingsIcon,
  Timeline as TimelineIcon,
  Speed as SpeedIcon,
  Memory as MemoryIcon,
  Storage as StorageIcon,
  NetworkCheck as NetworkIcon,
  ExpandMore as ExpandMoreIcon,
  Visibility as VisibilityIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { toast } from 'react-toastify';

// Dynamic import for EnhancedAdminLayout to avoid SSR issues
const EnhancedAdminLayout = dynamic(() => import('@/components/admin/EnhancedAdminLayout'), {
  ssr: false
});

export default function LogsMonitoring() {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [logs, setLogs] = useState([]);
  const [systemMetrics, setSystemMetrics] = useState({});
  const [alerts, setAlerts] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [totalCount, setTotalCount] = useState(0);
  const [filters, setFilters] = useState({
    level: '',
    service: '',
    search: '',
    startDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
    endDate: new Date()
  });

  // Log levels
  const logLevels = [
    { value: 'ERROR', label: 'Error', color: 'error', icon: <ErrorIcon /> },
    { value: 'WARN', label: 'Warning', color: 'warning', icon: <WarningIcon /> },
    { value: 'INFO', label: 'Info', color: 'info', icon: <InfoIcon /> },
    { value: 'DEBUG', label: 'Debug', color: 'default', icon: <InfoIcon /> }
  ];

  // Services
  const services = [
    'API Server',
    'Database',
    'Redis',
    'ML Service',
    'Email Service',
    'SMS Service',
    'File Upload',
    'Authentication',
    'Payment Gateway'
  ];

  useEffect(() => {
    fetchLogsData();
  }, [page, rowsPerPage, filters]);

  const fetchLogsData = async () => {
    try {
      setLoading(true);
      
      // Mock logs data
      const mockLogs = [
        {
          id: '1',
          timestamp: new Date().toISOString(),
          level: 'ERROR',
          service: 'API Server',
          message: 'Database connection timeout',
          details: 'Connection to PostgreSQL database timed out after 30 seconds',
          userId: null,
          requestId: 'req_123456',
          ip: '*************'
        },
        {
          id: '2',
          timestamp: new Date(Date.now() - 300000).toISOString(),
          level: 'WARN',
          service: 'ML Service',
          message: 'High memory usage detected',
          details: 'Memory usage is at 85% of available RAM',
          userId: null,
          requestId: null,
          ip: null
        },
        {
          id: '3',
          timestamp: new Date(Date.now() - 600000).toISOString(),
          level: 'INFO',
          service: 'Authentication',
          message: 'User login successful',
          details: 'User user123 logged in successfully',
          userId: 'user123',
          requestId: 'req_789012',
          ip: '************'
        },
        {
          id: '4',
          timestamp: new Date(Date.now() - 900000).toISOString(),
          level: 'ERROR',
          service: 'Payment Gateway',
          message: 'Payment processing failed',
          details: 'Razorpay API returned error: insufficient_funds',
          userId: 'user456',
          requestId: 'req_345678',
          ip: '************'
        }
      ];

      setLogs(mockLogs);
      setTotalCount(mockLogs.length);

      // Mock system metrics
      setSystemMetrics({
        cpu: {
          usage: 45.2,
          cores: 2,
          loadAverage: [1.2, 1.5, 1.8]
        },
        memory: {
          used: 6.2,
          total: 8.0,
          usage: 77.5
        },
        disk: {
          used: 18.5,
          total: 100,
          usage: 18.5
        },
        network: {
          inbound: 125.6,
          outbound: 89.3
        },
        database: {
          connections: 45,
          maxConnections: 100,
          queryTime: 12.5
        },
        redis: {
          memory: 256,
          connections: 12,
          hitRate: 98.5
        }
      });

      // Mock alerts
      setAlerts([
        {
          id: '1',
          type: 'ERROR',
          title: 'High Error Rate',
          message: 'Error rate has exceeded 5% in the last 10 minutes',
          timestamp: new Date(Date.now() - 600000).toISOString(),
          acknowledged: false
        },
        {
          id: '2',
          type: 'WARN',
          title: 'Memory Usage High',
          message: 'Memory usage is above 80%',
          timestamp: new Date(Date.now() - 1200000).toISOString(),
          acknowledged: true
        }
      ]);

    } catch (error) {
      console.error('Error fetching logs data:', error);
      toast.error('Error fetching logs data');
    } finally {
      setLoading(false);
    }
  };

  const handleExportLogs = () => {
    // Mock export functionality
    toast.info('Exporting logs...');
  };

  const handleClearLogs = () => {
    // Mock clear logs functionality
    toast.info('Clearing old logs...');
  };

  const getLogLevelChip = (level) => {
    const levelConfig = logLevels.find(l => l.value === level) || logLevels[2];
    return (
      <Chip
        icon={levelConfig.icon}
        label={levelConfig.label}
        color={levelConfig.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const getAlertChip = (type) => {
    const config = {
      ERROR: { color: 'error', icon: <ErrorIcon /> },
      WARN: { color: 'warning', icon: <WarningIcon /> },
      INFO: { color: 'info', icon: <InfoIcon /> }
    };
    
    const alertConfig = config[type] || config.INFO;
    return (
      <Chip
        icon={alertConfig.icon}
        label={type}
        color={alertConfig.color}
        size="small"
      />
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const TabPanel = ({ children, value, index, ...other }) => (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`logs-tabpanel-${index}`}
      aria-labelledby={`logs-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );

  return (
    <EnhancedAdminLayout title="Logs & Monitoring">
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Logs & Monitoring
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchLogsData}
            >
              Refresh
            </Button>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={handleExportLogs}
            >
              Export
            </Button>
            <Button
              variant="outlined"
              startIcon={<DeleteIcon />}
              onClick={handleClearLogs}
              color="error"
            >
              Clear Old Logs
            </Button>
          </Box>
        </Box>

        {/* System Metrics Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      CPU Usage
                    </Typography>
                    <Typography variant="h4" component="div">
                      {systemMetrics.cpu?.usage?.toFixed(1) || 0}%
                    </Typography>
                  </Box>
                  <SpeedIcon color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Memory Usage
                    </Typography>
                    <Typography variant="h4" component="div">
                      {systemMetrics.memory?.usage?.toFixed(1) || 0}%
                    </Typography>
                  </Box>
                  <MemoryIcon color="warning" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Disk Usage
                    </Typography>
                    <Typography variant="h4" component="div">
                      {systemMetrics.disk?.usage?.toFixed(1) || 0}%
                    </Typography>
                  </Box>
                  <StorageIcon color="info" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      DB Connections
                    </Typography>
                    <Typography variant="h4" component="div">
                      {systemMetrics.database?.connections || 0}
                    </Typography>
                  </Box>
                  <NetworkIcon color="success" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Redis Hit Rate
                    </Typography>
                    <Typography variant="h4" component="div">
                      {systemMetrics.redis?.hitRate?.toFixed(1) || 0}%
                    </Typography>
                  </Box>
                  <CheckCircleIcon color="success" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Query Time
                    </Typography>
                    <Typography variant="h4" component="div">
                      {systemMetrics.database?.queryTime?.toFixed(1) || 0}ms
                    </Typography>
                  </Box>
                  <TimelineIcon color="secondary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Active Alerts */}
        {alerts.filter(alert => !alert.acknowledged).length > 0 && (
          <Alert severity="warning" sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Active Alerts ({alerts.filter(alert => !alert.acknowledged).length})
            </Typography>
            {alerts.filter(alert => !alert.acknowledged).map(alert => (
              <Typography key={alert.id} variant="body2">
                • {alert.title}: {alert.message}
              </Typography>
            ))}
          </Alert>
        )}

        {/* Tabs */}
        <Paper sx={{ mb: 3 }}>
          <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
            <Tab label="Application Logs" icon={<LogsIcon />} />
            <Tab label="System Monitoring" icon={<MonitorIcon />} />
            <Tab label="Alerts" icon={<WarningIcon />} />
            <Tab label="Settings" icon={<SettingsIcon />} />
          </Tabs>
        </Paper>

        {/* Tab Panels */}
        <TabPanel value={activeTab} index={0}>
          {/* Filters */}
          <Paper sx={{ mb: 3, p: 2 }}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={3}>
                  <TextField
                    label="Search Logs"
                    variant="outlined"
                    size="small"
                    fullWidth
                    value={filters.search}
                    onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={2}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Log Level</InputLabel>
                    <Select
                      value={filters.level}
                      label="Log Level"
                      onChange={(e) => setFilters({ ...filters, level: e.target.value })}
                    >
                      <MenuItem value="">All Levels</MenuItem>
                      {logLevels.map((level) => (
                        <MenuItem key={level.value} value={level.value}>
                          {level.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={2}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Service</InputLabel>
                    <Select
                      value={filters.service}
                      label="Service"
                      onChange={(e) => setFilters({ ...filters, service: e.target.value })}
                    >
                      <MenuItem value="">All Services</MenuItem>
                      {services.map((service) => (
                        <MenuItem key={service} value={service}>
                          {service}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={2}>
                  <DateTimePicker
                    label="Start Date"
                    value={filters.startDate}
                    onChange={(newValue) => setFilters({ ...filters, startDate: newValue })}
                    renderInput={(params) => <TextField {...params} size="small" fullWidth />}
                  />
                </Grid>
                <Grid item xs={12} md={2}>
                  <DateTimePicker
                    label="End Date"
                    value={filters.endDate}
                    onChange={(newValue) => setFilters({ ...filters, endDate: newValue })}
                    renderInput={(params) => <TextField {...params} size="small" fullWidth />}
                  />
                </Grid>
                <Grid item xs={12} md={1}>
                  <Button
                    variant="outlined"
                    fullWidth
                    onClick={() => setFilters({ level: '', service: '', search: '', startDate: new Date(Date.now() - 24 * 60 * 60 * 1000), endDate: new Date() })}
                  >
                    Clear
                  </Button>
                </Grid>
              </Grid>
            </LocalizationProvider>
          </Paper>

          {/* Logs Table */}
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Timestamp</TableCell>
                  <TableCell>Level</TableCell>
                  <TableCell>Service</TableCell>
                  <TableCell>Message</TableCell>
                  <TableCell>User ID</TableCell>
                  <TableCell>IP Address</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                ) : logs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <Typography variant="body2" color="text.secondary">
                        No logs found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  logs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {formatDate(log.timestamp)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {getLogLevelChip(log.level)}
                      </TableCell>
                      <TableCell>
                        <Chip label={log.service} size="small" variant="outlined" />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {log.message}
                        </Typography>
                        {log.details && (
                          <Typography variant="caption" color="text.secondary" display="block">
                            {log.details}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {log.userId || '-'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {log.ip || '-'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Tooltip title="View Details">
                          <IconButton size="small" color="primary">
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
            <TablePagination
              rowsPerPageOptions={[10, 25, 50, 100]}
              component="div"
              count={totalCount}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={(e, newPage) => setPage(newPage)}
              onRowsPerPageChange={(e) => {
                setRowsPerPage(parseInt(e.target.value, 10));
                setPage(0);
              }}
            />
          </TableContainer>
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    System Resources
                  </Typography>
                  <Stack spacing={2}>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        CPU Usage: {systemMetrics.cpu?.usage?.toFixed(1)}%
                      </Typography>
                      <Typography variant="caption">
                        Load Average: {systemMetrics.cpu?.loadAverage?.join(', ')}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Memory: {systemMetrics.memory?.used}GB / {systemMetrics.memory?.total}GB ({systemMetrics.memory?.usage?.toFixed(1)}%)
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Disk: {systemMetrics.disk?.used}GB / {systemMetrics.disk?.total}GB ({systemMetrics.disk?.usage?.toFixed(1)}%)
                      </Typography>
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Database & Cache
                  </Typography>
                  <Stack spacing={2}>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        DB Connections: {systemMetrics.database?.connections} / {systemMetrics.database?.maxConnections}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Avg Query Time: {systemMetrics.database?.queryTime}ms
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Redis Memory: {systemMetrics.redis?.memory}MB
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Redis Hit Rate: {systemMetrics.redis?.hitRate}%
                      </Typography>
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={activeTab} index={2}>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Type</TableCell>
                  <TableCell>Title</TableCell>
                  <TableCell>Message</TableCell>
                  <TableCell>Timestamp</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {alerts.map((alert) => (
                  <TableRow key={alert.id}>
                    <TableCell>{getAlertChip(alert.type)}</TableCell>
                    <TableCell>
                      <Typography variant="body1" fontWeight="600">
                        {alert.title}
                      </Typography>
                    </TableCell>
                    <TableCell>{alert.message}</TableCell>
                    <TableCell>{formatDate(alert.timestamp)}</TableCell>
                    <TableCell>
                      <Chip
                        label={alert.acknowledged ? 'Acknowledged' : 'Active'}
                        color={alert.acknowledged ? 'default' : 'warning'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {!alert.acknowledged && (
                        <Button size="small" variant="outlined">
                          Acknowledge
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={activeTab} index={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Logging & Monitoring Settings
              </Typography>
              <Alert severity="info" sx={{ mb: 2 }}>
                Configure log levels, retention policies, and monitoring thresholds.
              </Alert>
              <Typography variant="body2" color="text.secondary">
                Logging and monitoring configuration options would be displayed here,
                including log level settings, retention policies, alert thresholds, and notification preferences.
              </Typography>
            </CardContent>
          </Card>
        </TabPanel>
      </Box>
    </EnhancedAdminLayout>
  );
}
