/* Custom Admin Styles */

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000; /* Higher than sidebar z-index */
  padding: 20px;
  overflow-y: auto;
}

.modalOpenBody {
  overflow: hidden;
}

.modal {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  margin-left: auto;
  margin-right: auto;
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.modal-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0;
}

.modal-close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #999;
  transition: color 0.2s ease;
}

.modal-close-button:hover {
  color: var(--text-dark);
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 15px 20px;
  border-top: 1px solid #eee;
  gap: 10px;
}

/* Document viewer modal */
.document-viewer-modal {
  z-index: 3000;
}

.document-modal {
  max-width: 90vw;
}

.document-modal-body {
  padding: 0;
  text-align: center;
  background-color: #f5f5f5;
}

.document-full-image {
  max-width: 100%;
  max-height: 70vh;
  display: block;
  margin: 0 auto;
}

/* Override the sidebar active item style */
.nav-item.active {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-left: 4px solid var(--secondary) !important;
  color: white;
}

/* Add a stronger orange highlight effect */
.nav-item:hover {
  border-left: 4px solid var(--secondary) !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Action buttons in modals */
.action-section {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
}

.action-buttons-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.action-description {
  color: #555;
  font-size: 0.9rem;
}

.action-buttons-row {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.action-icon {
  margin-right: 5px;
}

/* Evidence Grid and Document Grid Styles */
.evidence-grid,
.documents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.document-card {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.document-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.document-preview {
  height: 150px;
  overflow: hidden;
}

.document-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.document-info {
  padding: 10px;
  background-color: #f9f9f9;
}

.document-type {
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 5px;
}

.document-name {
  font-size: 0.8rem;
  color: #666;
}

.evidence-item {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.evidence-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.evidence-preview {
  height: 150px;
  overflow: hidden;
}

.evidence-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.evidence-info {
  padding: 10px;
  background-color: #f9f9f9;
}

.evidence-type {
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 5px;
}

.evidence-date {
  font-size: 0.8rem;
  color: #666;
}

.no-evidence {
  grid-column: 1 / -1;
  padding: 15px;
  text-align: center;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 8px;
}

/* Confirmation modal styles */
.confirmation-modal {
  max-width: 500px;
}

.confirmation-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 15px;
}

.confirmation-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.confirmation-icon:first-child {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.confirmation-icon:last-child {
  background-color: #ffebee;
  color: #c62828;
}

.action-details {
  width: 100%;
  margin-top: 15px;
  text-align: left;
}

.action-details label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.action-details select,
.action-details textarea,
.action-details input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  margin-bottom: 15px;
}

.feature-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 10px;
  margin: 10px 0 15px;
}

.feature-checkboxes label {
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: normal;
  margin-bottom: 0;
}

/* Tab sections in modals */
.tab-section {
  margin-bottom: 25px;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.user-details-grid,
.report-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.detail-item {
  margin-bottom: 10px;
}

.detail-label {
  font-weight: 500;
  color: #666;
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.detail-value {
  color: var(--text-dark);
}

.user-profile-section {
  margin-bottom: 20px;
}

.user-profile-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.user-avatar.large {
  width: 80px;
  height: 80px;
  font-size: 2rem;
}

.user-profile-info h3 {
  margin: 0 0 5px 0;
  font-size: 1.3rem;
}

.user-meta {
  color: #666;
  font-size: 0.9rem;
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.report-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.report-date {
  font-size: 0.8rem;
  color: #666;
}

/* Responsive styles for tabs */
@media (max-width: 768px) {
  .user-details-grid,
  .report-info-grid,
  .evidence-grid,
  .documents-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons-row {
    flex-direction: column;
  }

  .user-profile-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .modal {
    width: 95%;
    max-height: 85vh;
  }
}

/* Focus styles for inputs - moved to admin-global-styles.css */

/* Button styles */
.refresh-button {
  background-color: var(--secondary) !important;
}

.btn {
  display: inline-block;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  line-height: 1.5;
  border-radius: 6px;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.btn-primary {
  color: #fff;
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-primary:hover {
  background-color: #e64a19;
  border-color: #e64a19;
}

.btn-secondary {
  color: #333;
  background-color: #f5f5f5;
  border-color: #ddd;
}

.btn-secondary:hover {
  background-color: #e0e0e0;
  border-color: #ccc;
}

.btn-success {
  color: #fff;
  background-color: #4caf50;
  border-color: #4caf50;
}

.btn-success:hover {
  background-color: #43a047;
  border-color: #388e3c;
}

.btn-danger {
  color: #fff;
  background-color: #f44336;
  border-color: #f44336;
}

.btn-danger:hover {
  background-color: #e53935;
  border-color: #d32f2f;
}

.refresh-button:hover {
  background-color: #e64a19 !important;
}

/* Search button hover */
.search-button:hover {
  color: var(--secondary) !important;
}

/* Table row highlight strip */
.data-table tr {
  position: relative;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.data-table tr:hover {
  border-left: 3px solid var(--secondary);
  background-color: rgba(255, 87, 34, 0.05) !important;
}

/* Selected row in tables */
.data-table tr.selected {
  border-left: 3px solid var(--secondary);
  background-color: rgba(255, 87, 34, 0.1) !important;
}

/* Status badges with left border instead of background */
.status-badge {
  border-left: 3px solid;
  padding-left: 8px;
  background-color: transparent !important;
}

.status-badge.pending {
  border-left-color: var(--warning);
}

.status-badge.approved, .status-badge.resolved, .status-badge.enabled {
  border-left-color: var(--success);
}

.status-badge.rejected, .status-badge.dismissed, .status-badge.disabled {
  border-left-color: var(--danger);
}

/* Action buttons with orange highlight on hover */
.action-btn:hover {
  color: var(--secondary) !important;
}

/* Tab styles with left border highlight */
.tab-nav-item {
  position: relative;
  border-left: 3px solid transparent;
  transition: all 0.2s ease;
}

.tab-nav-item.active, .tab-nav-item:hover {
  border-left: 3px solid var(--secondary) !important;
  background-color: rgba(255, 87, 34, 0.05) !important;
}

/* Modal tabs */
.modal .tab-nav-item.active {
  border-left: 3px solid var(--secondary) !important;
  background-color: rgba(255, 87, 34, 0.05) !important;
  color: var(--secondary) !important;
  font-weight: 600;
}


