import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  Alert,
  CircularProgress,
  Switch,
  FormControlLabel,
  Tooltip,
  Avatar,
  Stack,
  Divider,
  Paper,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Badge,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  LinearProgress
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
  Assessment as AssessmentIcon,
  Timeline as TimelineIcon,
  Pie<PERSON>hart as PieChartIcon,
  BarChart as BarChartIcon,
  Show<PERSON>hart as ShowChartIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  DateRange as DateRangeIcon,
  FilterList as FilterListIcon,
  Insights as InsightsIcon,
  Psychology as PsychologyIcon,
  Speed as SpeedIcon,
  Star as StarIcon,
  Favorite as FavoriteIcon,
  Phone as PhoneIcon,
  Message as MessageIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { toast } from 'react-toastify';
import { adminGet } from '@/services/adminApiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';

// Dynamic import for EnhancedAdminLayout to avoid SSR issues
const EnhancedAdminLayout = dynamic(() => import('@/components/admin/EnhancedAdminLayout'), {
  ssr: false
});

// Dynamic import for charts
const Chart = dynamic(() => import('react-apexcharts'), {
  ssr: false,
  loading: () => <CircularProgress />
});

export default function AdvancedAnalyticsDashboard() {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [analyticsData, setAnalyticsData] = useState({});
  const [mlReadinessData, setMlReadinessData] = useState({});
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    endDate: new Date()
  });
  const [filters, setFilters] = useState({
    timeframe: '30d',
    userType: 'all',
    platform: 'all'
  });

  // Timeframe options
  const timeframeOptions = [
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '90d', label: 'Last 3 Months' },
    { value: '1y', label: 'Last Year' },
    { value: 'custom', label: 'Custom Range' }
  ];

  useEffect(() => {
    fetchAnalyticsData();
    fetchMLReadinessData();
  }, [filters, dateRange]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      const response = await adminGet('/api/admin/advanced-analytics');
      
      if (response.success) {
        setAnalyticsData(response.data || {});
      } else {
        toast.error('Failed to fetch analytics data');
      }
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      toast.error('Error fetching analytics data');
    } finally {
      setLoading(false);
    }
  };

  const fetchMLReadinessData = async () => {
    try {
      const response = await adminGet(`${ADMIN_ENDPOINTS.DATA_ANALYTICS}/ml-readiness`);
      
      if (response.success) {
        setMlReadinessData(response.data || {});
      }
    } catch (error) {
      console.error('Error fetching ML readiness data:', error);
    }
  };

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num?.toString() || '0';
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0
    }).format(amount || 0);
  };

  const getMLReadinessColor = (percentage) => {
    if (percentage >= 80) return 'success';
    if (percentage >= 60) return 'warning';
    return 'error';
  };

  const TabPanel = ({ children, value, index, ...other }) => (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`analytics-tabpanel-${index}`}
      aria-labelledby={`analytics-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );

  // Chart configurations
  const userGrowthChartOptions = {
    chart: {
      type: 'area',
      height: 350,
      toolbar: { show: false }
    },
    colors: ['#1976d2', '#dc004e'],
    dataLabels: { enabled: false },
    stroke: { curve: 'smooth', width: 2 },
    xaxis: {
      categories: analyticsData.userBehavior?.dailyStats?.map(d => d.date) || []
    },
    yaxis: {
      title: { text: 'Users' }
    },
    legend: {
      position: 'top'
    },
    fill: {
      type: 'gradient',
      gradient: {
        shadeIntensity: 1,
        opacityFrom: 0.7,
        opacityTo: 0.3
      }
    }
  };

  const userGrowthChartSeries = [
    {
      name: 'New Users',
      data: analyticsData.userBehavior?.dailyStats?.map(d => d.newUsers) || []
    },
    {
      name: 'Active Users',
      data: analyticsData.userBehavior?.dailyStats?.map(d => d.activeUsers) || []
    }
  ];

  const revenueChartOptions = {
    chart: {
      type: 'bar',
      height: 350,
      toolbar: { show: false }
    },
    colors: ['#4caf50'],
    plotOptions: {
      bar: {
        borderRadius: 4,
        horizontal: false
      }
    },
    dataLabels: { enabled: false },
    xaxis: {
      categories: analyticsData.revenueAnalytics?.revenueByPlan?.map(p => p.plan) || []
    },
    yaxis: {
      title: { text: 'Revenue (₹)' }
    }
  };

  const revenueChartSeries = [
    {
      name: 'Revenue',
      data: analyticsData.revenueAnalytics?.revenueByPlan?.map(p => p.revenue) || []
    }
  ];

  return (
    <EnhancedAdminLayout title="Advanced Analytics Dashboard">
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Advanced Analytics Dashboard
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <FormControl size="small" sx={{ minWidth: 150 }}>
              <InputLabel>Timeframe</InputLabel>
              <Select
                value={filters.timeframe}
                label="Timeframe"
                onChange={(e) => setFilters({ ...filters, timeframe: e.target.value })}
              >
                {timeframeOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={() => {
                fetchAnalyticsData();
                fetchMLReadinessData();
              }}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={<DownloadIcon />}
              onClick={() => toast.info('Export functionality coming soon')}
            >
              Export
            </Button>
          </Box>
        </Box>

        {/* Overview Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Total Users
                    </Typography>
                    <Typography variant="h4" component="div">
                      {formatNumber(analyticsData.summary?.totalUsers)}
                    </Typography>
                    <Typography variant="body2" color="success.main">
                      +{analyticsData.summary?.growthRate || 0}% this month
                    </Typography>
                  </Box>
                  <PeopleIcon color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Total Revenue
                    </Typography>
                    <Typography variant="h4" component="div">
                      {formatCurrency(analyticsData.summary?.totalRevenue)}
                    </Typography>
                    <Typography variant="body2" color="success.main">
                      +{analyticsData.revenueAnalytics?.revenueGrowthRate || 0}% growth
                    </Typography>
                  </Box>
                  <MoneyIcon color="success" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Total Matches
                    </Typography>
                    <Typography variant="h4" component="div">
                      {formatNumber(analyticsData.summary?.totalMatches)}
                    </Typography>
                    <Typography variant="body2" color="info.main">
                      {analyticsData.algorithmPerformance?.matchAccuracyRate || 0}% accuracy
                    </Typography>
                  </Box>
                  <FavoriteIcon color="error" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Success Stories
                    </Typography>
                    <Typography variant="h4" component="div">
                      {analyticsData.summary?.successStories || 0}
                    </Typography>
                    <Typography variant="body2" color="warning.main">
                      {analyticsData.summary?.satisfactionScore || 0}/5 rating
                    </Typography>
                  </Box>
                  <StarIcon color="warning" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* ML Readiness Alert */}
        {mlReadinessData.mlReadiness && (
          <Alert
            severity={mlReadinessData.mlReadiness.v2_0?.isReady ? 'success' : 'warning'}
            sx={{ mb: 3 }}
            icon={<PsychologyIcon />}
          >
            <Typography variant="h6" gutterBottom>
              ML Algorithm Readiness Status
            </Typography>
            <Typography variant="body2">
              {mlReadinessData.mlReadiness.v2_0?.isReady
                ? 'Your platform has sufficient data for ML algorithm v2.0 deployment!'
                : `Current readiness: ${mlReadinessData.mlReadiness.v2_0?.readinessPercentage || 0}%. Need more user interactions for optimal performance.`
              }
            </Typography>
          </Alert>
        )}

        {/* Tabs */}
        <Paper sx={{ mb: 3 }}>
          <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
            <Tab label="User Analytics" icon={<PeopleIcon />} />
            <Tab label="Revenue Analytics" icon={<MoneyIcon />} />
            <Tab label="Matching Performance" icon={<FavoriteIcon />} />
            <Tab label="ML Readiness" icon={<PsychologyIcon />} />
            <Tab label="Real-time Metrics" icon={<SpeedIcon />} />
          </Tabs>
        </Paper>

        {/* Tab Panels */}
        <TabPanel value={activeTab} index={0}>
          {/* User Analytics */}
          <Grid container spacing={3}>
            <Grid item xs={12} lg={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    User Growth Trend
                  </Typography>
                  {loading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                      <CircularProgress />
                    </Box>
                  ) : (
                    <Chart
                      options={userGrowthChartOptions}
                      series={userGrowthChartSeries}
                      type="area"
                      height={350}
                    />
                  )}
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} lg={4}>
              <Card sx={{ mb: 2 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    User Engagement
                  </Typography>
                  <Stack spacing={2}>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Profile Completion Rate
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={analyticsData.engagementMetrics?.profileCompletionRate || 0}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                      <Typography variant="body2" sx={{ mt: 0.5 }}>
                        {analyticsData.engagementMetrics?.profileCompletionRate || 0}%
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Message Response Rate
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={analyticsData.engagementMetrics?.messageResponseRate || 0}
                        sx={{ height: 8, borderRadius: 4 }}
                        color="success"
                      />
                      <Typography variant="body2" sx={{ mt: 0.5 }}>
                        {analyticsData.engagementMetrics?.messageResponseRate || 0}%
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Premium Conversion Rate
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={analyticsData.engagementMetrics?.premiumConversionRate || 0}
                        sx={{ height: 8, borderRadius: 4 }}
                        color="warning"
                      />
                      <Typography variant="body2" sx={{ mt: 0.5 }}>
                        {analyticsData.engagementMetrics?.premiumConversionRate || 0}%
                      </Typography>
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Revenue Analytics Tab */}
        <TabPanel value={activeTab} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} lg={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Revenue by Plan
                  </Typography>
                  {loading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                      <CircularProgress />
                    </Box>
                  ) : (
                    <Chart
                      options={revenueChartOptions}
                      series={revenueChartSeries}
                      type="bar"
                      height={350}
                    />
                  )}
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} lg={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Revenue Metrics
                  </Typography>
                  <Stack spacing={2}>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Monthly Recurring Revenue
                      </Typography>
                      <Typography variant="h5">
                        {formatCurrency(analyticsData.revenueAnalytics?.monthlyRecurringRevenue)}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Average Revenue Per User
                      </Typography>
                      <Typography variant="h5">
                        {formatCurrency(analyticsData.revenueAnalytics?.averageRevenuePerUser)}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Customer Lifetime Value
                      </Typography>
                      <Typography variant="h5">
                        {formatCurrency(analyticsData.revenueAnalytics?.customerLifetimeValue)}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Churn Rate
                      </Typography>
                      <Typography variant="h5" color="error.main">
                        {analyticsData.revenueAnalytics?.churnRate || 0}%
                      </Typography>
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Matching Performance Tab */}
        <TabPanel value={activeTab} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Algorithm Performance
                  </Typography>
                  <Stack spacing={2}>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Match Accuracy Rate
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={analyticsData.algorithmPerformance?.matchAccuracyRate || 0}
                        sx={{ height: 8, borderRadius: 4 }}
                        color="success"
                      />
                      <Typography variant="body2" sx={{ mt: 0.5 }}>
                        {analyticsData.algorithmPerformance?.matchAccuracyRate || 0}%
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        User Satisfaction Score
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={(analyticsData.algorithmPerformance?.userSatisfactionScore || 0) * 20}
                        sx={{ height: 8, borderRadius: 4 }}
                        color="warning"
                      />
                      <Typography variant="body2" sx={{ mt: 0.5 }}>
                        {analyticsData.algorithmPerformance?.userSatisfactionScore || 0}/5
                      </Typography>
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Match Statistics
                  </Typography>
                  <Stack spacing={2}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Total Matches</Typography>
                      <Typography variant="body2" fontWeight="600">
                        {formatNumber(analyticsData.algorithmPerformance?.totalMatches)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Successful Matches</Typography>
                      <Typography variant="body2" fontWeight="600">
                        {formatNumber(analyticsData.algorithmPerformance?.successfulMatches)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Average Match Score</Typography>
                      <Typography variant="body2" fontWeight="600">
                        {analyticsData.algorithmPerformance?.averageMatchScore || 0}%
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Algorithm Version</Typography>
                      <Typography variant="body2" fontWeight="600">
                        {analyticsData.algorithmPerformance?.algorithmVersion || 'v1.0'}
                      </Typography>
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* ML Readiness Tab */}
        <TabPanel value={activeTab} index={3}>
          <Grid container spacing={3}>
            {mlReadinessData.mlReadiness && Object.entries(mlReadinessData.mlReadiness).map(([version, data]) => (
              <Grid item xs={12} md={4} key={version}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      ML Algorithm {version.replace('_', '.')}
                    </Typography>
                    <Box sx={{ textAlign: 'center', mb: 2 }}>
                      <Typography variant="h3" color={getMLReadinessColor(data.readinessPercentage)}>
                        {data.readinessPercentage || 0}%
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Readiness
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={data.readinessPercentage || 0}
                      sx={{ height: 8, borderRadius: 4, mb: 2 }}
                      color={getMLReadinessColor(data.readinessPercentage)}
                    />
                    <Chip
                      label={data.isReady ? 'READY' : 'NOT READY'}
                      color={data.isReady ? 'success' : 'error'}
                      sx={{ width: '100%' }}
                    />
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
          {mlReadinessData.recommendations && (
            <Card sx={{ mt: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Recommendations
                </Typography>
                <List>
                  {mlReadinessData.recommendations.map((recommendation, index) => (
                    <ListItem key={index}>
                      <ListItemText primary={recommendation} />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          )}
        </TabPanel>

        {/* Real-time Metrics Tab */}
        <TabPanel value={activeTab} index={4}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Live Activity
                  </Typography>
                  <Stack spacing={2}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2">Active Users</Typography>
                      <Badge badgeContent="LIVE" color="success">
                        <Typography variant="h6">
                          {analyticsData.realTimeMetrics?.activeUsers || 0}
                        </Typography>
                      </Badge>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2">Messages Sent (Last Hour)</Typography>
                      <Typography variant="h6">
                        {analyticsData.realTimeMetrics?.messagesLastHour || 0}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2">New Registrations (Today)</Typography>
                      <Typography variant="h6">
                        {analyticsData.realTimeMetrics?.newRegistrationsToday || 0}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2">Matches Made (Today)</Typography>
                      <Typography variant="h6">
                        {analyticsData.realTimeMetrics?.matchesToday || 0}
                      </Typography>
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    System Performance
                  </Typography>
                  <Stack spacing={2}>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Server Response Time
                      </Typography>
                      <Typography variant="h6" color="success.main">
                        {analyticsData.realTimeMetrics?.serverResponseTime || '120ms'}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Database Performance
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={95}
                        sx={{ height: 8, borderRadius: 4 }}
                        color="success"
                      />
                      <Typography variant="body2" sx={{ mt: 0.5 }}>
                        95% Optimal
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        API Uptime
                      </Typography>
                      <Typography variant="h6" color="success.main">
                        99.9%
                      </Typography>
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>
      </Box>
    </EnhancedAdminLayout>
  );
}
