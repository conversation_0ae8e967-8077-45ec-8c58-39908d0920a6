// src/routes/admin/subscriptions.routes.js

const express = require('express');
const router = express.Router();
const subscriptionsController = require('../../controllers/admin/subscriptions.controller');
const { authenticateAdmin } = require('../../middleware/auth.middleware');

// Apply admin authentication middleware to all routes
router.use(authenticateAdmin);

// Get all subscription plans
router.get('/', subscriptionsController.getSubscriptionPlans);

// Get subscription plan by ID
router.get('/:id', subscriptionsController.getSubscriptionPlanById);

// Create new subscription plan
router.post('/', subscriptionsController.createSubscriptionPlan);

// Update subscription plan
router.put('/:id', subscriptionsController.updateSubscriptionPlan);

// Delete subscription plan
router.delete('/:id', subscriptionsController.deleteSubscriptionPlan);

// Get subscription statistics
router.get('/stats/overview', subscriptionsController.getSubscriptionStats);

module.exports = router;
