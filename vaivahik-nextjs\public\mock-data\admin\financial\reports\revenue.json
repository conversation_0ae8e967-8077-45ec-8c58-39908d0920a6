{"success": true, "message": "Revenue data fetched successfully", "revenue": {"total": 2450000, "totalTransactions": 1250, "averageTransactionValue": 1960, "growthMetrics": {"revenueGrowth": 15.5, "transactionGrowth": 12.3, "averageValueGrowth": 2.8}, "byPeriod": [{"period": "2024-01", "revenue": 180000, "transactions": 95, "averageValue": 1895}, {"period": "2024-02", "revenue": 195000, "transactions": 102, "averageValue": 1912}, {"period": "2024-03", "revenue": 210000, "transactions": 108, "averageValue": 1944}, {"period": "2024-04", "revenue": 225000, "transactions": 115, "averageValue": 1956}, {"period": "2024-05", "revenue": 240000, "transactions": 122, "averageValue": 1967}, {"period": "2024-06", "revenue": 255000, "transactions": 128, "averageValue": 1992}, {"period": "2024-07", "revenue": 270000, "transactions": 135, "averageValue": 2000}, {"period": "2024-08", "revenue": 285000, "transactions": 142, "averageValue": 2007}, {"period": "2024-09", "revenue": 300000, "transactions": 148, "averageValue": 2027}, {"period": "2024-10", "revenue": 315000, "transactions": 155, "averageValue": 2032}, {"period": "2024-11", "revenue": 330000, "transactions": 162, "averageValue": 2037}, {"period": "2024-12", "revenue": 345000, "transactions": 168, "averageValue": 2054}], "byProductType": [{"productType": "Premium Monthly", "revenue": 980000, "transactions": 500, "percentage": 40.0}, {"productType": "Premium Quarterly", "revenue": 735000, "transactions": 375, "percentage": 30.0}, {"productType": "Premium Annual", "revenue": 490000, "transactions": 250, "percentage": 20.0}, {"productType": "Contact Views", "revenue": 147000, "transactions": 75, "percentage": 6.0}, {"productType": "Profile Boost", "revenue": 98000, "transactions": 50, "percentage": 4.0}], "byPaymentMethod": [{"paymentMethod": "UPI", "revenue": 1225000, "transactions": 625, "percentage": 50.0}, {"paymentMethod": "Credit Card", "revenue": 735000, "transactions": 375, "percentage": 30.0}, {"paymentMethod": "Debit Card", "revenue": 367500, "transactions": 187, "percentage": 15.0}, {"paymentMethod": "Net Banking", "revenue": 122500, "transactions": 63, "percentage": 5.0}], "topTransactions": [{"id": "txn-001", "userId": "user-101", "userName": "<PERSON><PERSON>", "amount": 4999, "productType": "Premium Annual", "paymentMethod": "UPI", "date": "2024-01-15T10:30:00Z", "status": "COMPLETED"}, {"id": "txn-002", "userId": "user-102", "userName": "<PERSON>", "amount": 1299, "productType": "Premium Quarterly", "paymentMethod": "Credit Card", "date": "2024-01-15T11:15:00Z", "status": "COMPLETED"}, {"id": "txn-003", "userId": "user-103", "userName": "<PERSON>", "amount": 499, "productType": "Premium Monthly", "paymentMethod": "UPI", "date": "2024-01-14T14:20:00Z", "status": "COMPLETED"}, {"id": "txn-004", "userId": "user-104", "userName": "<PERSON><PERSON><PERSON>", "amount": 4999, "productType": "Premium Annual", "paymentMethod": "Net Banking", "date": "2024-01-14T16:45:00Z", "status": "COMPLETED"}, {"id": "txn-005", "userId": "user-105", "userName": "<PERSON><PERSON><PERSON>", "amount": 1299, "productType": "Premium Quarterly", "paymentMethod": "Debit Card", "date": "2024-01-13T09:10:00Z", "status": "COMPLETED"}]}}