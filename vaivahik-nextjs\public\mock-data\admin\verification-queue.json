{"success": true, "verificationRequests": [{"id": 1, "userId": 1004, "userName": "<PERSON><PERSON><PERSON>hav", "userImage": "/images/profiles/female2.jpg", "documentType": "id_proof", "documentUrl": "/mock-data/documents/aadhar_card.jpg", "status": "pending", "submittedAt": "2023-07-30T14:20:00Z", "reviewedAt": null, "reviewedBy": null, "notes": null, "userDetails": {"age": 25, "gender": "female", "location": "Mumbai", "occupation": "Architect", "education": "B.Arch", "registeredAt": "2023-07-18T14:10:00Z"}}, {"id": 2, "userId": 1005, "userName": "<PERSON><PERSON><PERSON>", "userImage": "/images/profiles/male3.jpg", "documentType": "id_proof", "documentUrl": "/mock-data/documents/pan_card.jpg", "status": "pending", "submittedAt": "2023-07-30T16:45:00Z", "reviewedAt": null, "reviewedBy": null, "notes": null, "userDetails": {"age": 32, "gender": "male", "location": "<PERSON><PERSON>", "occupation": "Bank Manager", "education": "MBA", "registeredAt": "2023-07-19T11:30:00Z"}}, {"id": 3, "userId": 1008, "userName": "<PERSON><PERSON><PERSON>", "userImage": "/images/profiles/female4.jpg", "documentType": "id_proof", "documentUrl": "/mock-data/documents/aadhar_card.jpg", "status": "pending", "submittedAt": "2023-07-31T09:15:00Z", "reviewedAt": null, "reviewedBy": null, "notes": null, "userDetails": {"age": 24, "gender": "female", "location": "Nagpur", "occupation": "Software Developer", "education": "B.Tech", "registeredAt": "2023-07-22T12:15:00Z"}}, {"id": 4, "userId": 1011, "userName": "<PERSON><PERSON>", "userImage": "/images/profiles/male6.jpg", "documentType": "id_proof", "documentUrl": "/mock-data/documents/aadhar_card.jpg", "status": "pending", "submittedAt": "2023-07-31T10:30:00Z", "reviewedAt": null, "reviewedBy": null, "notes": null, "userDetails": {"age": 33, "gender": "male", "location": "Mumbai", "occupation": "IT Manager", "education": "MCA", "registeredAt": "2023-07-25T08:45:00Z"}}, {"id": 5, "userId": 1012, "userName": "<PERSON>", "userImage": "/images/profiles/female6.jpg", "documentType": "id_proof", "documentUrl": "/mock-data/documents/pan_card.jpg", "status": "pending", "submittedAt": "2023-07-31T11:45:00Z", "reviewedAt": null, "reviewedBy": null, "notes": null, "userDetails": {"age": 27, "gender": "female", "location": "Pune", "occupation": "Accountant", "education": "<PERSON><PERSON>", "registeredAt": "2023-07-26T14:20:00Z"}}, {"id": 6, "userId": 1013, "userName": "<PERSON><PERSON>", "userImage": "/images/profiles/male7.jpg", "documentType": "id_proof", "documentUrl": "/mock-data/documents/aadhar_card.jpg", "status": "approved", "submittedAt": "2023-07-29T09:30:00Z", "reviewedAt": "2023-07-30T10:15:00Z", "reviewedBy": "Admin User", "notes": "Valid ID proof verified", "userDetails": {"age": 30, "gender": "male", "location": "<PERSON><PERSON>", "occupation": "Teacher", "education": "<PERSON><PERSON>", "registeredAt": "2023-07-20T10:30:00Z"}}, {"id": 7, "userId": 1014, "userName": "<PERSON><PERSON><PERSON>", "userImage": "/images/profiles/female7.jpg", "documentType": "id_proof", "documentUrl": "/mock-data/documents/pan_card.jpg", "status": "approved", "submittedAt": "2023-07-29T11:45:00Z", "reviewedAt": "2023-07-30T14:30:00Z", "reviewedBy": "Admin User", "notes": "Valid ID proof verified", "userDetails": {"age": 26, "gender": "female", "location": "Mumbai", "occupation": "Fashion Designer", "education": "<PERSON><PERSON>", "registeredAt": "2023-07-21T09:15:00Z"}}, {"id": 8, "userId": 1015, "userName": "<PERSON>", "userImage": "/images/profiles/male8.jpg", "documentType": "id_proof", "documentUrl": "/mock-data/documents/aadhar_card.jpg", "status": "rejected", "submittedAt": "2023-07-29T14:20:00Z", "reviewedAt": "2023-07-30T16:45:00Z", "reviewedBy": "Admin User", "notes": "Document is not clearly visible. Please upload a clearer image.", "userDetails": {"age": 29, "gender": "male", "location": "<PERSON><PERSON>", "occupation": "Sales Manager", "education": "MBA", "registeredAt": "2023-07-22T11:30:00Z"}}, {"id": 9, "userId": 1016, "userName": "<PERSON><PERSON><PERSON>", "userImage": "/images/profiles/female8.jpg", "documentType": "id_proof", "documentUrl": "/mock-data/documents/pan_card.jpg", "status": "rejected", "submittedAt": "2023-07-29T16:30:00Z", "reviewedAt": "2023-07-31T09:15:00Z", "reviewedBy": "Admin User", "notes": "Document details do not match profile information. Please upload correct document.", "userDetails": {"age": 28, "gender": "female", "location": "Pune", "occupation": "Software Engineer", "education": "B.Tech", "registeredAt": "2023-07-23T10:45:00Z"}}, {"id": 10, "userId": 1017, "userName": "<PERSON><PERSON>", "userImage": "/images/profiles/male9.jpg", "documentType": "id_proof", "documentUrl": "/mock-data/documents/aadhar_card.jpg", "status": "pending", "submittedAt": "2023-08-01T08:45:00Z", "reviewedAt": null, "reviewedBy": null, "notes": null, "userDetails": {"age": 31, "gender": "male", "location": "Mumbai", "occupation": "Doctor", "education": "MBBS", "registeredAt": "2023-07-24T14:30:00Z"}}], "pagination": {"total": 45, "page": 1, "limit": 10, "totalPages": 5}, "filters": {"status": ["pending", "approved", "rejected"], "documentType": ["id_proof", "address_proof", "education_proof", "income_proof"]}}