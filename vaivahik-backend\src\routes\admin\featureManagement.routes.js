// src/routes/admin/featureManagement.routes.js

const express = require('express');
const router = express.Router();

// Import the controller
const featureManagementController = require('../../controllers/admin/featureManagement.controller');

// Import middleware
const { authenticateToken, isAdmin } = require('../../middleware/auth.middleware');

// For now, we'll skip authentication for testing
// router.use(authenticateToken);
// router.use(isAdmin);

// Feature routes
router.get('/features', featureManagementController.getAllFeatures);
router.get('/features/:id', featureManagementController.getFeatureById);
router.post('/features', featureManagementController.createFeature);
router.put('/features/:id', featureManagementController.updateFeature);
router.delete('/features/:id', featureManagementController.deleteFeature);
router.put('/features/:id/access', featureManagementController.updateFeatureAccess);

// Subscription plan routes
router.get('/subscription-plans', featureManagementController.getAllSubscriptionPlans);
router.post('/subscription-plans', featureManagementController.createSubscriptionPlan);
router.put('/subscription-plans/:id', featureManagementController.updateSubscriptionPlan);
router.delete('/subscription-plans/:id', featureManagementController.deleteSubscriptionPlan);

module.exports = router;
