import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  if (req.method === 'GET') {
    return await getShortlist(req, res);
  } else if (req.method === 'POST') {
    return await addToShortlist(req, res);
  } else {
    return res.status(405).json({ message: 'Method not allowed' });
  }
}

async function getShortlist(req, res) {
  try {
    // Verify JWT token
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ success: false, message: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const userId = decoded.userId;

    // Get shortlisted profiles
    const shortlistItems = await prisma.shortlist.findMany({
      where: {
        userId: userId
      },
      include: {
        targetUser: {
          include: {
            profile: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Check if interests have been sent to shortlisted users
    const targetUserIds = shortlistItems.map(item => item.targetUserId);
    const sentInterests = await prisma.interest.findMany({
      where: {
        userId: userId,
        targetUserId: {
          in: targetUserIds
        }
      }
    });

    const interestMap = sentInterests.reduce((acc, interest) => {
      acc[interest.targetUserId] = interest.status;
      return acc;
    }, {});

    // Format the data
    const formattedShortlist = shortlistItems.map(item => ({
      id: item.id,
      note: item.note,
      createdAt: item.createdAt,
      interestSent: !!interestMap[item.targetUserId],
      interestStatus: interestMap[item.targetUserId] || null,
      profile: {
        id: item.targetUser.id,
        firstName: item.targetUser.profile?.firstName || 'Unknown',
        lastName: item.targetUser.profile?.lastName || '',
        age: item.targetUser.profile?.age,
        location: `${item.targetUser.profile?.city || ''}, ${item.targetUser.profile?.state || ''}`.trim().replace(/^,|,$/, ''),
        education: item.targetUser.profile?.education,
        occupation: item.targetUser.profile?.occupation,
        profilePicture: item.targetUser.profile?.profilePicUrl,
        compatibility: item.targetUser.profile?.compatibilityScore || Math.floor(Math.random() * 30) + 70 // Mock compatibility for now
      }
    }));

    return res.status(200).json({
      success: true,
      data: formattedShortlist
    });

  } catch (error) {
    console.error('Error fetching shortlist:', error);
    return res.status(500).json({
      success: false,
      message: 'Error fetching shortlist',
      error: error.message
    });
  }
}

async function addToShortlist(req, res) {
  try {
    // Verify JWT token
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ success: false, message: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const userId = decoded.userId;

    const { profileId, note } = req.body;

    if (!profileId) {
      return res.status(400).json({
        success: false,
        message: 'Profile ID is required'
      });
    }

    // Check if already shortlisted
    const existingShortlist = await prisma.shortlist.findFirst({
      where: {
        userId: userId,
        targetUserId: profileId
      }
    });

    if (existingShortlist) {
      return res.status(400).json({
        success: false,
        message: 'Profile already in shortlist'
      });
    }

    // Check if target user exists
    const targetUser = await prisma.user.findUnique({
      where: { id: profileId },
      include: {
        profile: true
      }
    });

    if (!targetUser) {
      return res.status(404).json({
        success: false,
        message: 'Profile not found'
      });
    }

    // Add to shortlist
    const shortlistItem = await prisma.shortlist.create({
      data: {
        userId: userId,
        targetUserId: profileId,
        note: note || null
      }
    });

    // Track user interaction
    await prisma.userInteraction.create({
      data: {
        userId: userId,
        targetUserId: profileId,
        interactionType: 'SHORTLISTED',
        metadata: JSON.stringify({
          note: note
        })
      }
    });

    return res.status(201).json({
      success: true,
      message: 'Profile added to shortlist successfully',
      data: {
        shortlistId: shortlistItem.id,
        targetUser: {
          id: targetUser.id,
          firstName: targetUser.profile?.firstName,
          lastName: targetUser.profile?.lastName
        }
      }
    });

  } catch (error) {
    console.error('Error adding to shortlist:', error);
    return res.status(500).json({
      success: false,
      message: 'Error adding to shortlist',
      error: error.message
    });
  }
}
