// src/routes/admin/promotions.routes.js

const express = require('express');
const router = express.Router();
const promotionsController = require('../../controllers/admin/promotions.controller');
const { authenticateAdmin } = require('../../middleware/auth.middleware');

// Get all promotions
router.get('/', authenticateAdmin, promotionsController.getPromotions);

// Get a specific promotion
router.get('/:name', authenticateAdmin, promotionsController.getPromotion);

// Activate a promotion
router.post('/:name/activate', authenticateAdmin, promotionsController.activatePromotion);

// Deactivate a promotion
router.post('/:name/deactivate', authenticateAdmin, promotionsController.deactivatePromotion);

// Update a promotion
router.put('/:name', authenticateAdmin, promotionsController.updatePromotion);

// Create a custom promotion
router.post('/', authenticateAdmin, promotionsController.createPromotion);

module.exports = router;
