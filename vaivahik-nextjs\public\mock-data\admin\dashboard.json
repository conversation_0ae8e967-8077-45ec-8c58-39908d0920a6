{"success": true, "stats": {"totalUsers": 5842, "newUsers": {"count": 127, "change": 12.4}, "premiumUsers": {"count": 843, "change": 8.7}, "pendingVerifications": {"count": 45, "change": -5.2}, "totalRevenue": {"amount": 125000, "currency": "INR", "change": 15.3}, "successfulMatches": {"count": 320, "change": 22.1}}, "charts": {"userGrowth": {"labels": ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"], "datasets": [{"label": "New Users", "data": [120, 145, 132, 165, 178, 190, 210, 232, 256, 278, 290, 310], "borderColor": "rgb(94, 53, 177)", "backgroundColor": "rgba(94, 53, 177, 0.5)"}, {"label": "Premium Conversions", "data": [45, 52, 48, 58, 63, 72, 85, 93, 102, 110, 118, 125], "borderColor": "rgb(33, 150, 243)", "backgroundColor": "rgba(33, 150, 243, 0.5)"}]}, "revenueData": {"labels": ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"], "datasets": [{"label": "Revenue (₹)", "data": [85000, 92000, 88000, 95000, 102000, 110000, 118000, 125000, 132000, 140000, 148000, 155000], "borderColor": "rgb(76, 175, 80)", "backgroundColor": "rgba(76, 175, 80, 0.5)"}]}, "matchSuccess": {"labels": ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"], "datasets": [{"label": "Match Success Rate (%)", "data": [72, 74, 73, 75, 78, 80, 82, 83, 85, 86, 87, 88], "borderColor": "rgb(255, 152, 0)", "backgroundColor": "rgba(255, 152, 0, 0.5)"}]}, "userDemographics": {"labels": ["18-25", "26-30", "31-35", "36-40", "41-45", "46+"], "datasets": [{"label": "Male", "data": [15, 25, 20, 18, 12, 10], "backgroundColor": "rgba(33, 150, 243, 0.7)"}, {"label": "Female", "data": [18, 28, 22, 15, 10, 7], "backgroundColor": "rgba(233, 30, 99, 0.7)"}]}, "subscriptionTypes": {"labels": ["Basic", "Silver", "Gold", "Platinum"], "datasets": [{"label": "Subscription Distribution", "data": [45, 30, 15, 10], "backgroundColor": ["rgba(33, 150, 243, 0.7)", "rgba(76, 175, 80, 0.7)", "rgba(255, 152, 0, 0.7)", "rgba(94, 53, 177, 0.7)"]}]}}, "recentUsers": [{"id": 5842, "name": "<PERSON><PERSON>", "age": 28, "location": "Mumbai", "occupation": "Software Engineer", "registeredAt": "2023-08-01T10:30:00Z", "profileImage": "/images/profiles/male1.jpg", "isPremium": false, "isVerified": false}, {"id": 5841, "name": "<PERSON><PERSON>", "age": 26, "location": "Pune", "occupation": "Doctor", "registeredAt": "2023-08-01T09:45:00Z", "profileImage": "/images/profiles/female1.jpg", "isPremium": true, "isVerified": true}, {"id": 5840, "name": "<PERSON><PERSON>", "age": 30, "location": "Nagpur", "occupation": "Business Owner", "registeredAt": "2023-08-01T08:20:00Z", "profileImage": "/images/profiles/male2.jpg", "isPremium": false, "isVerified": true}, {"id": 5839, "name": "<PERSON><PERSON><PERSON>hav", "age": 25, "location": "Mumbai", "occupation": "Architect", "registeredAt": "2023-07-31T22:15:00Z", "profileImage": "/images/profiles/female2.jpg", "isPremium": true, "isVerified": false}, {"id": 5838, "name": "<PERSON><PERSON><PERSON>", "age": 32, "location": "<PERSON><PERSON>", "occupation": "Bank Manager", "registeredAt": "2023-07-31T20:30:00Z", "profileImage": "/images/profiles/male3.jpg", "isPremium": false, "isVerified": false}], "recentActivity": [{"id": 1, "type": "USER_REGISTRATION", "userId": 5842, "userName": "<PERSON><PERSON>", "timestamp": "2023-08-01T10:30:00Z", "details": {"location": "Mumbai"}}, {"id": 2, "type": "PREMIUM_SUBSCRIPTION", "userId": 5841, "userName": "<PERSON><PERSON>", "timestamp": "2023-08-01T10:15:00Z", "details": {"plan": "Gold", "amount": 2999, "duration": "3 months"}}, {"id": 3, "type": "VERIFICATION_APPROVED", "userId": 5840, "userName": "<PERSON><PERSON>", "timestamp": "2023-08-01T09:45:00Z", "details": {"documentType": "<PERSON><PERSON><PERSON>"}}, {"id": 4, "type": "INTEREST_SENT", "userId": 5835, "userName": "<PERSON><PERSON>", "timestamp": "2023-08-01T09:30:00Z", "details": {"toUserId": 5820, "toUserName": "<PERSON><PERSON><PERSON>"}}, {"id": 5, "type": "MATCH_SUCCESS", "userId": 5810, "userName": "<PERSON><PERSON>", "timestamp": "2023-08-01T09:15:00Z", "details": {"matchedUserId": 5795, "matchedUserName": "<PERSON><PERSON><PERSON>", "matchScore": 92}}]}