import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  StepLabel,
  StepContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  LinearProgress,
  styled,
  Alert
} from '@mui/material';
import {
  Security as SecurityIcon,
  Upload as UploadIcon,
  CheckCircle as CheckIcon,
  Schedule as PendingIcon,
  Warning as WarningIcon,
  Description as DocumentIcon,
  CameraAlt as PhotoIcon,
  Phone as PhoneIcon,
  Email as EmailIcon
} from '@mui/icons-material';

const VerificationCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: 24,
  boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
    background: 'linear-gradient(90deg, #FF5F6D, #FFC371)',
    borderRadius: '24px 24px 0 0'
  }
}));

const StatusCard = styled(Card)(({ theme, status }) => ({
  border: `2px solid ${
    status === 'completed' ? '#4CAF50' :
    status === 'pending' ? '#FF9800' :
    status === 'rejected' ? '#F44336' : 'rgba(255, 95, 109, 0.2)'
  }`,
  borderRadius: 16,
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)'
  }
}));

const VerificationQueueWidget = ({ userId, isVerified = false }) => {
  const [verificationStatus, setVerificationStatus] = useState('not_started');
  const [activeStep, setActiveStep] = useState(0);
  const [documents, setDocuments] = useState([]);

  useEffect(() => {
    fetchVerificationStatus();
  }, [userId]);

  const fetchVerificationStatus = async () => {
    try {
      // Mock verification data
      const mockStatus = {
        status: 'in_progress',
        activeStep: 2,
        documents: [
          {
            id: 1,
            type: 'ID Proof',
            name: 'Aadhaar Card',
            status: 'approved',
            uploadedAt: '2024-01-10',
            feedback: null
          },
          {
            id: 2,
            type: 'Address Proof',
            name: 'Passport',
            status: 'approved',
            uploadedAt: '2024-01-10',
            feedback: null
          },
          {
            id: 3,
            type: 'Photo Verification',
            name: 'Selfie with ID',
            status: 'pending',
            uploadedAt: '2024-01-12',
            feedback: null
          },
          {
            id: 4,
            type: 'Income Proof',
            name: 'Salary Slip',
            status: 'not_uploaded',
            uploadedAt: null,
            feedback: null
          }
        ]
      };
      
      setVerificationStatus(mockStatus.status);
      setActiveStep(mockStatus.activeStep);
      setDocuments(mockStatus.documents);
    } catch (error) {
      console.error('Error fetching verification status:', error);
    }
  };

  const verificationSteps = [
    {
      label: 'Phone Verification',
      description: 'Verify your phone number with OTP',
      icon: <PhoneIcon />,
      status: 'completed'
    },
    {
      label: 'Email Verification',
      description: 'Verify your email address',
      icon: <EmailIcon />,
      status: 'completed'
    },
    {
      label: 'Document Upload',
      description: 'Upload ID proof and address proof',
      icon: <DocumentIcon />,
      status: 'pending'
    },
    {
      label: 'Photo Verification',
      description: 'Upload selfie with ID for verification',
      icon: <PhotoIcon />,
      status: 'pending'
    },
    {
      label: 'Admin Review',
      description: 'Our team will review your documents',
      icon: <SecurityIcon />,
      status: 'not_started'
    }
  ];

  const handleDocumentUpload = (documentType) => {
    console.log('Upload document:', documentType);
    // Implement document upload logic
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'approved':
      case 'completed':
        return <CheckIcon sx={{ color: '#4CAF50' }} />;
      case 'pending':
        return <PendingIcon sx={{ color: '#FF9800' }} />;
      case 'rejected':
        return <WarningIcon sx={{ color: '#F44336' }} />;
      default:
        return <UploadIcon sx={{ color: '#666' }} />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'approved':
      case 'completed':
        return '#4CAF50';
      case 'pending':
        return '#FF9800';
      case 'rejected':
        return '#F44336';
      default:
        return '#666';
    }
  };

  if (isVerified) {
    return (
      <Box>
        <Alert severity="success" sx={{ mb: 3, borderRadius: 3 }}>
          <Typography variant="h6" fontWeight="600">
            🎉 Your profile is verified!
          </Typography>
          <Typography variant="body2">
            You now enjoy all verification benefits including increased visibility and trust badge.
          </Typography>
        </Alert>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        mb: 3,
        p: 3,
        background: 'linear-gradient(135deg, rgba(255, 95, 109, 0.1) 0%, rgba(255, 195, 113, 0.1) 100%)',
        borderRadius: 3
      }}>
        <SecurityIcon sx={{ fontSize: 32, color: '#FF5F6D', mr: 2 }} />
        <Box>
          <Typography variant="h5" fontWeight="700" color="#FF5F6D">
            Profile Verification
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Complete verification to get 300% more profile views and build trust
          </Typography>
        </Box>
      </Box>

      <VerificationCard>
        <CardContent sx={{ p: 4 }}>
          {/* Progress Overview */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" fontWeight="600">
                Verification Progress
              </Typography>
              <Chip
                label={`${Math.round((activeStep / verificationSteps.length) * 100)}% Complete`}
                sx={{
                  backgroundColor: getStatusColor('pending'),
                  color: 'white',
                  fontWeight: 600
                }}
              />
            </Box>
            
            <LinearProgress
              variant="determinate"
              value={(activeStep / verificationSteps.length) * 100}
              sx={{
                height: 12,
                borderRadius: 6,
                backgroundColor: 'rgba(255, 95, 109, 0.1)',
                '& .MuiLinearProgress-bar': {
                  background: 'linear-gradient(90deg, #FF5F6D, #FFC371)',
                  borderRadius: 6
                }
              }}
            />
          </Box>

          {/* Verification Steps */}
          <Stepper activeStep={activeStep} orientation="vertical">
            {verificationSteps.map((step, index) => (
              <Step key={step.label}>
                <StepLabel
                  icon={step.icon}
                  sx={{
                    '& .MuiStepIcon-root': {
                      color: index <= activeStep ? '#FF5F6D' : '#ccc'
                    }
                  }}
                >
                  <Typography variant="subtitle1" fontWeight="600">
                    {step.label}
                  </Typography>
                </StepLabel>
                <StepContent>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {step.description}
                  </Typography>
                  {index === activeStep && (
                    <Button
                      variant="contained"
                      size="small"
                      sx={{
                        background: 'linear-gradient(135deg, #FF5F6D, #FFC371)',
                        borderRadius: 2
                      }}
                    >
                      {step.label === 'Document Upload' ? 'Upload Documents' : 'Continue'}
                    </Button>
                  )}
                </StepContent>
              </Step>
            ))}
          </Stepper>

          {/* Document Status */}
          <Box sx={{ mt: 4 }}>
            <Typography variant="h6" fontWeight="600" gutterBottom>
              Document Status
            </Typography>
            <Grid container spacing={2}>
              {documents.map((doc) => (
                <Grid item xs={12} md={6} key={doc.id}>
                  <StatusCard status={doc.status}>
                    <CardContent sx={{ p: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {getStatusIcon(doc.status)}
                          <Box sx={{ ml: 2 }}>
                            <Typography variant="subtitle2" fontWeight="600">
                              {doc.type}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {doc.name || 'Not uploaded'}
                            </Typography>
                          </Box>
                        </Box>
                        {doc.status === 'not_uploaded' && (
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => handleDocumentUpload(doc.type)}
                            sx={{
                              borderColor: '#FF5F6D',
                              color: '#FF5F6D',
                              borderRadius: 2
                            }}
                          >
                            Upload
                          </Button>
                        )}
                      </Box>
                      {doc.feedback && (
                        <Alert severity="warning" sx={{ mt: 2, fontSize: '0.875rem' }}>
                          {doc.feedback}
                        </Alert>
                      )}
                    </CardContent>
                  </StatusCard>
                </Grid>
              ))}
            </Grid>
          </Box>

          {/* Benefits Reminder */}
          <Box sx={{ 
            mt: 4, 
            p: 3, 
            background: 'rgba(76, 175, 80, 0.1)', 
            borderRadius: 2,
            border: '1px solid rgba(76, 175, 80, 0.2)'
          }}>
            <Typography variant="subtitle1" fontWeight="600" color="#4CAF50" gutterBottom>
              🎯 Verification Benefits
            </Typography>
            <List dense>
              {[
                '300% more profile views',
                'Trusted profile badge',
                'Priority in search results',
                'Access to verified users only',
                'Higher match success rate'
              ].map((benefit, index) => (
                <ListItem key={index} sx={{ py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 32 }}>
                    <CheckIcon sx={{ color: '#4CAF50', fontSize: 20 }} />
                  </ListItemIcon>
                  <ListItemText 
                    primary={benefit}
                    primaryTypographyProps={{ fontSize: '0.875rem' }}
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        </CardContent>
      </VerificationCard>
    </Box>
  );
};

export default VerificationQueueWidget;
