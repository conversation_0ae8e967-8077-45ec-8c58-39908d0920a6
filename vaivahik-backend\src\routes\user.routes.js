// routes/user.routes.js

const express = require('express');
const router = express.Router();

// Import the controller functions
const userController = require('../controllers/user.controller.js');
const verificationController = require('../controllers/verification.controller.js');
const featureController = require('../controllers/feature.controller.js');
const matchesController = require('../controllers/matches.controller.js');
const chatController = require('../controllers/chat.controller.js');
const similarityController = require('../controllers/similarityController.js');

// Import the authentication middleware
const { authenticateToken } = require('../middleware/auth.middleware.js');
// Import the configured Multer upload middleware for multiple photos
const handleUploadMiddleware = require('../middleware/upload.js');
// Import the configured Multer upload middleware for verification documents
const handleDocumentUpload = require('../middleware/documentUpload.js');
// Import the feature access middleware
const { featureAccessMiddleware, applyAccessLimits, trackFeatureUsage } = require('../middleware/featureAccess.middleware.js');
// Import the validation middleware
const { validateForm, preventLockedFieldChanges } = require('../middleware/validation.middleware');
// Import the cache middleware
const {
    cacheMiddleware,
    cacheUserProfile,
    cacheUserMatches,
    cacheConversations,
    cacheMessages,
    cacheSearchResults,
    cacheDashboardStats
} = require('../middleware/cache.middleware');


// --- Public Routes ---
router.post('/request-otp', userController.sendOtp);
router.post('/resend-otp', userController.resendOtp);
router.post('/verify-otp', userController.verifyOtpAndSignIn);
router.post('/refresh-token', userController.refreshToken);
router.post('/register', userController.registerWithPassword); // Optional registration route

// --- Protected Routes (Require Authentication via Access Token) ---

// Profile GET and PUT
router.get('/profile', authenticateToken, cacheUserProfile(3600), userController.getUserProfile);
router.put('/profile', authenticateToken, userController.updateUserProfile);

// Get critical field status
router.get('/critical-fields', authenticateToken, userController.getCriticalFieldStatus);

// Profile Completion Endpoints
router.put('/family-details', authenticateToken, preventLockedFieldChanges(), validateForm('familyDetails'), userController.updateFamilyDetails);
router.put('/partner-preferences', authenticateToken, validateForm('partnerPreferences'), userController.updatePartnerPreferences);
router.put('/basic-details', authenticateToken, preventLockedFieldChanges(), validateForm('basicDetails'), userController.updateBasicDetails);
router.put('/education-career', authenticateToken, validateForm('educationCareer'), userController.updateEducationCareer);
router.put('/location-details', authenticateToken, preventLockedFieldChanges(), validateForm('locationDetails'), userController.updateLocationDetails);
router.put('/lifestyle-habits', authenticateToken, validateForm('lifestyleHabits'), userController.updateLifestyleHabits);
router.put('/about-me', authenticateToken, validateForm('aboutMe'), userController.updateAboutMe);

// Profile Photo Upload Route (Handles multiple files)
router.post(
    '/photos', // Endpoint for uploading one or more photos (up to 3)
    authenticateToken,        // 1. Ensure user is logged in
    handleUploadMiddleware,   // 2. Handle file uploads using the configured Multer middleware
    userController.handleProfilePhotoUpload // 3. Controller function to process and save photos
);

// --- Photo Management Routes ---

// Set a specific photo as the primary profile picture
router.post(
    '/photos/:photoId/set-primary', // Use photoId parameter
    authenticateToken,
    userController.setPrimaryPhoto
);

// Update visibility of a specific photo
router.put(
    '/photos/:photoId/visibility', // Use photoId parameter
    authenticateToken,
    userController.updatePhotoVisibility
);

// Delete a specific photo
router.delete(
    '/photos/:photoId', // Use photoId parameter
    authenticateToken,
    userController.deletePhoto
);

// --- End Photo Management Routes ---

// --- Verification Document Routes ---

// Upload a verification document
router.post(
    '/verification/documents',
    authenticateToken,
    handleDocumentUpload,
    verificationController.uploadVerificationDocument
);

// Get all verification documents for the current user
router.get(
    '/verification/documents',
    authenticateToken,
    verificationController.getUserVerificationDocuments
);

// Delete a verification document
router.delete(
    '/verification/documents/:documentId',
    authenticateToken,
    verificationController.deleteVerificationDocument
);

// --- End Verification Document Routes ---

// Logout
router.post('/logout', authenticateToken, userController.logout);


// --- Feature-specific routes with access control ---

// TIER 1: Basic features - available to all users (with limitations)
router.get('/browse', authenticateToken, featureAccessMiddleware, applyAccessLimits, cacheMiddleware({ ttl: 300 }), featureController.browseProfiles);
router.get('/search', authenticateToken, featureAccessMiddleware, applyAccessLimits, cacheSearchResults(300), featureController.searchProfiles);
router.get('/view-profile/:userId', authenticateToken, featureAccessMiddleware, trackFeatureUsage, applyAccessLimits, cacheMiddleware({ ttl: 1800 }), featureController.viewUserProfile);
router.get('/edit-profile', authenticateToken, featureAccessMiddleware, featureController.editUserProfile);

// TIER 2: Enhanced features - available to verified users (with limitations)
router.get('/matches', authenticateToken, featureAccessMiddleware, trackFeatureUsage, applyAccessLimits, cacheUserMatches(1800), matchesController.getMatches);
router.get('/connections', authenticateToken, featureAccessMiddleware, applyAccessLimits, cacheMiddleware({ ttl: 1800 }), featureController.getConnections);

// Legacy message endpoints (deprecated, will be removed)
router.get('/messages', authenticateToken, featureAccessMiddleware, applyAccessLimits, featureController.getMessages);
router.post('/send-message', authenticateToken, featureAccessMiddleware, trackFeatureUsage, featureController.sendMessage);

// --- Chat Routes ---
// Get all conversations
router.get('/conversations', authenticateToken, featureAccessMiddleware, applyAccessLimits, cacheConversations(300), chatController.getConversations);

// Start a new conversation
router.post('/conversations', authenticateToken, featureAccessMiddleware, trackFeatureUsage, chatController.startConversation);

// Delete a conversation
router.delete('/conversations/:conversationId', authenticateToken, chatController.deleteConversation);

// Get messages for a conversation
router.get('/conversations/:conversationId/messages', authenticateToken, featureAccessMiddleware, applyAccessLimits, cacheMessages(60), chatController.getMessages);

// Send a message in a conversation
router.post('/conversations/:conversationId/messages', authenticateToken, featureAccessMiddleware, trackFeatureUsage, chatController.sendMessage);

// Mark messages as read
router.put('/conversations/:conversationId/read', authenticateToken, chatController.markMessagesAsRead);

// Get unread message count
router.get('/unread-messages', authenticateToken, chatController.getUnreadMessageCount);

// Get chat settings
router.get('/chat-settings', authenticateToken, chatController.getChatSettings);

// TIER 3: Premium features - require subscription
router.get('/advanced-search', authenticateToken, featureAccessMiddleware, cacheSearchResults(300), featureController.advancedSearch);
router.get('/contact-details/:userId', authenticateToken, featureAccessMiddleware, cacheMiddleware({ ttl: 1800 }), featureController.getContactDetails);
router.get('/priority-matching', authenticateToken, featureAccessMiddleware, cacheMiddleware({ ttl: 1800 }), featureController.getPriorityMatches);
router.get('/horoscope-matching/:userId', authenticateToken, featureAccessMiddleware, cacheMiddleware({ ttl: 3600 }), featureController.getHoroscopeMatch);
router.post('/profile-boost', authenticateToken, featureAccessMiddleware, featureController.boostProfile);
router.post('/incognito-browsing', authenticateToken, featureAccessMiddleware, featureController.toggleIncognitoBrowsing);

// --- Similarity and Compatibility Routes (Premium Features) ---
router.get('/similar-profiles/:userId', authenticateToken, featureAccessMiddleware, cacheMiddleware({ ttl: 3600 }), similarityController.findSimilarProfiles);
router.get('/compatibility/:userId', authenticateToken, featureAccessMiddleware, cacheMiddleware({ ttl: 3600 }), similarityController.getCompatibilityScore);

// --- Add other user-related routes later ---

// --- Mock data routes for testing ---
// GET /api/users/profiles - Get all user profiles (for testing)
router.get('/profiles', (req, res) => {
    // Return mock user profiles data for testing
    res.status(200).json({
        success: true,
        message: "User profiles fetched successfully.",
        profiles: [
            {
                id: "user1",
                fullName: "Rahul Sharma",
                age: 28,
                gender: "MALE",
                city: "Mumbai",
                state: "Maharashtra",
                occupation: "Software Engineer",
                education: "B.Tech",
                maritalStatus: "NEVER_MARRIED",
                religion: "Hindu",
                caste: "Maratha",
                height: 175,
                profilePicture: "https://placehold.co/400x400/3f51b5/ffffff?text=RS",
                isVerified: true,
                isPremium: true,
                lastActive: "2023-06-25T14:30:00Z"
            },
            {
                id: "user2",
                fullName: "Priya Patil",
                age: 26,
                gender: "FEMALE",
                city: "Pune",
                state: "Maharashtra",
                occupation: "Doctor",
                education: "MBBS",
                maritalStatus: "NEVER_MARRIED",
                religion: "Hindu",
                caste: "Maratha",
                height: 165,
                profilePicture: "https://placehold.co/400x400/e91e63/ffffff?text=PP",
                isVerified: true,
                isPremium: false,
                lastActive: "2023-06-24T18:45:00Z"
            },
            {
                id: "user3",
                fullName: "Amit Deshmukh",
                age: 30,
                gender: "MALE",
                city: "Nagpur",
                state: "Maharashtra",
                occupation: "Business Owner",
                education: "MBA",
                maritalStatus: "NEVER_MARRIED",
                religion: "Hindu",
                caste: "Maratha",
                height: 180,
                profilePicture: "https://placehold.co/400x400/009688/ffffff?text=AD",
                isVerified: true,
                isPremium: true,
                lastActive: "2023-06-25T09:15:00Z"
            }
        ]
    });
});

// GET /api/users/matches - Get user matches (for testing)
router.get('/matches', (req, res) => {
    // Return mock matches data for testing
    res.status(200).json({
        success: true,
        message: "Matches fetched successfully.",
        matches: [
            {
                id: "match1",
                userId: "user1",
                matchedUserId: "user2",
                compatibilityScore: 85,
                matchDate: "2023-06-20T10:30:00Z",
                status: "PENDING",
                matchFactors: ["Location", "Education", "Interests"],
                profile: {
                    id: "user2",
                    fullName: "Priya Patil",
                    age: 26,
                    gender: "FEMALE",
                    city: "Pune",
                    state: "Maharashtra",
                    occupation: "Doctor",
                    education: "MBBS",
                    maritalStatus: "NEVER_MARRIED",
                    religion: "Hindu",
                    caste: "Maratha",
                    height: 165,
                    profilePicture: "https://placehold.co/400x400/e91e63/ffffff?text=PP"
                }
            },
            {
                id: "match2",
                userId: "user1",
                matchedUserId: "user3",
                compatibilityScore: 78,
                matchDate: "2023-06-18T14:45:00Z",
                status: "ACCEPTED",
                matchFactors: ["Profession", "Hobbies", "Family Values"],
                profile: {
                    id: "user3",
                    fullName: "Sneha Joshi",
                    age: 27,
                    gender: "FEMALE",
                    city: "Mumbai",
                    state: "Maharashtra",
                    occupation: "Architect",
                    education: "B.Arch",
                    maritalStatus: "NEVER_MARRIED",
                    religion: "Hindu",
                    caste: "Maratha",
                    height: 162,
                    profilePicture: "https://placehold.co/400x400/ff9800/ffffff?text=SJ"
                }
            }
        ]
    });
});

// GET /api/users/feature-flags - Get feature flags (for testing)
router.get('/feature-flags', (req, res) => {
    // Return mock feature flags data for testing
    res.status(200).json({
        success: true,
        message: "Feature flags fetched successfully.",
        flags: {
            useRealBackend: false,
            enableBiodataTemplates: true,
            enableSpotlightFeatures: true,
            enablePaymentGateway: false,
            enableNotifications: true,
            enableMatchingAlgorithm: true,
            enableDarkMode: false,
            enableAnimations: true,
            showMockDataIndicator: true
        }
    });
});

module.exports = router;
