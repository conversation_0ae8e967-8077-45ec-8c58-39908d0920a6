/**
 * Modern Basic Details Page
 *
 * This page allows users to update their basic profile details using a modern UI form.
 */

import React, { useState } from 'react';
import { Box, Container, Typography, Alert, Breadcrumbs } from '@mui/material';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import ModernBasicDetailsForm from '@/components/profile/ModernBasicDetailsForm';
import { useAuth } from '@/contexts/AuthContext';
import { isUsingRealBackend } from '@/utils/apiUtils';
import { updateBasicDetails } from '@/services/userApiService';
import { formatError, getUserFriendlyMessage, isNetworkError } from '@/utils/errorHandling';
import { withRetry } from '@/utils/retryLogic';

const ModernBasicDetailsPage = () => {
  const router = useRouter();
  const { userData, setUserData } = useAuth();
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Handle form submission
  const handleSave = async (formData) => {
    try {
      setSaving(true);
      setError(''); // Clear previous errors

      if (isUsingRealBackend()) {
        // Define the API call function
        const saveData = async () => {
          try {
            return await updateBasicDetails(formData);
          } catch (apiError) {
            // Format the error for better user feedback
            const formattedError = formatError(apiError);
            throw formattedError; // Rethrow for retry logic to catch
          }
        };

        // Call API with retry logic for network errors
        const response = await withRetry(saveData, {
          maxRetries: 3,
          retryCondition: isNetworkError
        });

        setSuccess('Basic details saved successfully!');

        // Update local user data
        setUserData(prev => ({
          ...prev,
          basicDetails: response.data?.basicDetails || formData
        }));
      } else {
        // Simulate API call
        setTimeout(() => {
          setSuccess('Basic details saved successfully!');

          // Update local user data
          setUserData(prev => ({
            ...prev,
            basicDetails: formData,
            profileCompletionPercentage: Math.min(70, (prev?.profileCompletionPercentage || 40) + 10)
          }));

          setSaving(false);
        }, 1000);
        return;
      }

      setSaving(false);
    } catch (err) {
      console.error('Error saving basic details:', err);

      // Get user-friendly error message
      const errorMessage = getUserFriendlyMessage(err);
      setError(errorMessage);

      setSaving(false);
    }
  };

  return (
    <>
      <Head>
        <title>Basic Details | Vaivahik</title>
        <meta name="description" content="Update your basic profile details" />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box sx={{ mb: 4 }}>
          <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
            <Link href="/website/pages/profile" passHref>
              <Typography color="inherit" sx={{ textDecoration: 'none', cursor: 'pointer' }}>
                Profile
              </Typography>
            </Link>
            <Typography color="text.primary">Basic Details</Typography>
          </Breadcrumbs>

          <Typography variant="h4" component="h1" gutterBottom>
            Basic Details
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Update your basic information to help us find the best matches for you.
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError('')}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess('')}>
              {success}
            </Alert>
          )}
        </Box>

        <ModernBasicDetailsForm
          userData={userData}
          onSave={handleSave}
          isLoading={saving}
        />

        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Next: Update your <Link href="/website/pages/profile/edit/modern-education">Education & Career</Link>
          </Typography>
        </Box>
      </Container>
    </>
  );
};

export default ModernBasicDetailsPage;
