// src/routes/admin/transactions.routes.js

const express = require('express');
const router = express.Router();
const transactionsController = require('../../controllers/admin/transactions.controller');
const { authenticateAdmin } = require('../../middleware/auth.middleware');

// Apply admin authentication middleware to all routes
router.use(authenticateAdmin);

// Get all transactions
router.get('/', transactionsController.getTransactions);

// Get transaction by ID
router.get('/:id', transactionsController.getTransactionById);

// Update transaction status
router.put('/:id/status', transactionsController.updateTransactionStatus);

// Get transaction statistics
router.get('/stats/overview', transactionsController.getTransactionStats);

// Get revenue analytics
router.get('/analytics/revenue', transactionsController.getRevenueAnalytics);

module.exports = router;
