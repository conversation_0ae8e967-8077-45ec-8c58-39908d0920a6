/**
 * Example of integrating event notifications with the matching algorithm
 * This is a sample implementation to demonstrate how to integrate notifications
 */
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const eventNotifications = require('../notification/event-notifications');

/**
 * Process new matches and send notifications
 * This function would be called after the matching algorithm runs
 * 
 * @param {string} userId - ID of the user to process matches for
 * @param {Array} matches - Array of match objects with scores
 */
const processNewMatches = async (userId, matches) => {
  try {
    // Get user's profile
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        profile: {
          select: {
            firstName: true,
            lastName: true,
            photos: {
              where: { isMain: true },
              select: { url: true }
            }
          }
        }
      }
    });
    
    if (!user || !user.profile) {
      console.log(`Cannot process matches: User ${userId} not found or has no profile`);
      return;
    }
    
    // Process each match
    for (const match of matches) {
      // Check if this is a new match (not previously shown to the user)
      const existingMatch = await prisma.match.findFirst({
        where: {
          OR: [
            { userId1: userId, userId2: match.userId },
            { userId1: match.userId, userId2: userId }
          ]
        }
      });
      
      if (!existingMatch) {
        // Create a new match record
        await prisma.match.create({
          data: {
            userId1: userId,
            userId2: match.userId,
            score: match.score,
            createdAt: new Date()
          }
        });
        
        // Get match user's profile
        const matchUser = await prisma.user.findUnique({
          where: { id: match.userId },
          select: {
            profile: {
              select: {
                firstName: true,
                lastName: true,
                photos: {
                  where: { isMain: true },
                  select: { url: true }
                }
              }
            }
          }
        });
        
        if (matchUser && matchUser.profile) {
          // Send notification to both users
          
          // Notification to the original user
          const matchData = {
            matchId: match.userId,
            matchName: `${matchUser.profile.firstName} ${matchUser.profile.lastName || ''}`.trim(),
            matchPhotoUrl: matchUser.profile.photos[0]?.url || null,
            matchPercentage: Math.round(match.score * 100)
          };
          
          eventNotifications.notifyNewMatch(userId, matchData);
          
          // Notification to the matched user
          const userData = {
            matchId: userId,
            matchName: `${user.profile.firstName} ${user.profile.lastName || ''}`.trim(),
            matchPhotoUrl: user.profile.photos[0]?.url || null,
            matchPercentage: Math.round(match.score * 100)
          };
          
          eventNotifications.notifyNewMatch(match.userId, userData);
        }
      }
    }
  } catch (error) {
    console.error('Error processing new matches:', error);
  }
};

/**
 * Example function that would be called when a new user registers
 * This would trigger the matching algorithm and send notifications
 * 
 * @param {string} userId - ID of the newly registered user
 */
const processNewUserMatches = async (userId) => {
  try {
    // This is where your matching algorithm would run
    // For this example, we'll just simulate some matches
    
    // Simulate finding matches
    const potentialMatches = await prisma.user.findMany({
      where: {
        id: { not: userId },
        // Add your matching criteria here
      },
      select: {
        id: true,
        profile: {
          select: {
            gender: true,
            dateOfBirth: true
            // Other relevant fields
          }
        }
      },
      take: 5 // Limit to 5 matches for this example
    });
    
    // Calculate match scores (this would be your algorithm)
    const matches = potentialMatches.map(match => ({
      userId: match.id,
      score: Math.random() * 0.5 + 0.5 // Random score between 0.5 and 1.0
    }));
    
    // Process matches and send notifications
    await processNewMatches(userId, matches);
    
    return matches;
  } catch (error) {
    console.error('Error processing new user matches:', error);
    return [];
  }
};

module.exports = {
  processNewMatches,
  processNewUserMatches
};
