import { useState, useEffect } from 'react';
import Head from 'next/head';
import {
  Container,
  Grid,
  Typo<PERSON>,
  Box,
  Card,
  CardContent,
  Divider,
  Chip,
  <PERSON>ton,
  Alert,
  Snackbar,
  useTheme,
  keyframes,
  Pagination,
  CircularProgress,
  IconButton,
  Paper,
  Stack,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  FlashOn as FlashOnIcon,
  History as HistoryIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  FilterList as FilterListIcon,
  Clear as ClearIcon,
  Sort as SortIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';

// Import from the new website structure
import { useSearch, FilterChips } from '@/website/search';
import { isUsingRealBackend } from '@/utils/featureFlags';
import SpotlightIndicator from '@/components/profile/SpotlightIndicator';
import PremiumSearchBar from '@/components/search/PremiumSearchBar';

/**
 * Search Page (New Version)
 * 
 * This page uses the reorganized search components and hooks
 */
export default function SearchPage() {
  const theme = useTheme();

  // Current user's gender (would come from auth context in a real app)
  const userGender = 'MALE';

  // Notification state
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });
  
  // Recent locations (would come from API in a real app)
  const recentLocations = ['Mumbai', 'Pune', 'Nagpur', 'Nashik', 'Aurangabad'];
  
  // Saved searches (would come from API in a real app)
  const [savedSearches, setSavedSearches] = useState([]);
  
  // Search history menu state
  const [historyAnchorEl, setHistoryAnchorEl] = useState(null);
  const historyMenuOpen = Boolean(historyAnchorEl);
  
  // Sort menu state
  const [sortAnchorEl, setSortAnchorEl] = useState(null);
  const sortMenuOpen = Boolean(sortAnchorEl);

  // Use the search hook
  const {
    searchParams,
    setSearchParams,
    currentSearchParams,
    allResults,
    displayedResults: searchResults,
    isSearching,
    searchPerformed,
    error,
    searchHistory,
    page,
    totalPages,
    totalResults,
    sortOption,
    setSortOption,
    handleSearch,
    handlePageChange,
    handleRemoveFilter,
    handleClearAllFilters,
    loadFromHistory,
    clearHistory
  } = useSearch({
    // Initial search parameters
    searchType: 'REGULAR',
    targetGender: userGender === 'MALE' ? 'FEMALE' : 'MALE',
    ageFrom: userGender === 'MALE' ? 18 : 21,
    ageTo: userGender === 'MALE' ? 35 : 40,
    heightFrom: 53, // 4'5"
    heightTo: 77, // 6'5"
  }, {
    resultsPerPage: 9,
    autoSearch: false,
    useAdvancedSearch: false,
    saveHistory: true
  });

  // Handle saving a search
  const handleSaveSearch = (searchData) => {
    // In a real app, this would save to the backend
    const newSavedSearch = {
      id: Date.now(),
      name: `Search ${savedSearches.length + 1}`,
      params: searchData,
      createdAt: new Date().toISOString()
    };
    
    setSavedSearches([...savedSearches, newSavedSearch]);
    
    setNotification({
      open: true,
      message: 'Search saved successfully',
      severity: 'success'
    });
  };

  // Show search history menu
  const handleHistoryClick = (event) => {
    setHistoryAnchorEl(event.currentTarget);
  };
  
  // Close search history menu
  const handleHistoryClose = () => {
    setHistoryAnchorEl(null);
  };
  
  // Load a search from history
  const handleLoadFromHistory = (historyEntry) => {
    loadFromHistory(historyEntry);
    handleHistoryClose();
  };
  
  // Clear search history
  const handleClearHistory = () => {
    clearHistory();
    handleHistoryClose();
    
    setNotification({
      open: true,
      message: 'Search history cleared',
      severity: 'success'
    });
  };

  // Close notification
  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  return (
    <>
      <Head>
        <title>Search Profiles | Vaivahik</title>
        <meta name="description" content="Find your perfect match on Vaivahik" />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Search Bar */}
        <Box
          id="search-container"
          sx={{
            mb: 4,
            position: 'relative',
            zIndex: 10
          }}
        >
          <PremiumSearchBar
            onSearch={handleSearch}
            savedSearches={savedSearches}
            onSaveSearch={handleSaveSearch}
            userGender={userGender}
            recentSearches={recentLocations}
          />
        </Box>

        {/* Search Results Section */}
        <Box id="search-results-top">
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h5" component="h1">
              {searchPerformed ? (
                `${allResults.length} Matches Found`
              ) : (
                'Search Results'
              )}
            </Typography>

            {searchPerformed && (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {/* Search History Button */}
                <Tooltip title="Search History">
                  <IconButton 
                    size="small" 
                    onClick={handleHistoryClick}
                    sx={{ mr: 1 }}
                  >
                    <HistoryIcon />
                  </IconButton>
                </Tooltip>
                
                {/* Sort Button */}
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<SortIcon />}
                  onClick={(e) => setSortAnchorEl(e.currentTarget)}
                  sx={{ mr: 1 }}
                >
                  Sort
                </Button>
                
                {/* Filter Button */}
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<FilterListIcon />}
                >
                  Filter
                </Button>
              </Box>
            )}
          </Box>
          
          {/* Search History Menu */}
          <Menu
            anchorEl={historyAnchorEl}
            open={historyMenuOpen}
            onClose={handleHistoryClose}
            PaperProps={{
              sx: { width: 320, maxHeight: 400 }
            }}
          >
            <Box sx={{ px: 2, py: 1, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="subtitle1">Recent Searches</Typography>
              <Button 
                size="small" 
                startIcon={<DeleteIcon />}
                onClick={handleClearHistory}
              >
                Clear
              </Button>
            </Box>
            <Divider />
            
            {searchHistory.length === 0 ? (
              <Box sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  No recent searches
                </Typography>
              </Box>
            ) : (
              searchHistory.map((entry) => (
                <MenuItem 
                  key={entry.id} 
                  onClick={() => handleLoadFromHistory(entry)}
                  sx={{ py: 1.5 }}
                >
                  <Box sx={{ width: '100%' }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                      <Typography variant="body2" fontWeight="medium">
                        {entry.searchParams.searchType === 'ID' 
                          ? `ID Search: ${entry.searchParams.userId}`
                          : `${entry.searchParams.targetGender} Profiles`}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(entry.timestamp).toLocaleDateString()}
                      </Typography>
                    </Box>
                    <Typography variant="caption" color="text.secondary">
                      {entry.searchParams.searchType === 'ID' 
                        ? 'Search by user ID'
                        : `Age: ${entry.searchParams.ageFrom}-${entry.searchParams.ageTo}, ${entry.searchParams.location || 'Any location'}`}
                    </Typography>
                    <Typography variant="caption" color="primary" sx={{ display: 'block' }}>
                      {entry.resultCount} results
                    </Typography>
                  </Box>
                </MenuItem>
              ))
            )}
          </Menu>
          
          {/* Sort Menu */}
          <Menu
            anchorEl={sortAnchorEl}
            open={sortMenuOpen}
            onClose={() => setSortAnchorEl(null)}
          >
            <MenuItem onClick={() => { setSortOption('relevance'); setSortAnchorEl(null); }}>
              <ListItemText primary="Relevance" />
              {sortOption === 'relevance' && <ListItemIcon sx={{ ml: 1 }}>✓</ListItemIcon>}
            </MenuItem>
            <MenuItem onClick={() => { setSortOption('newest'); setSortAnchorEl(null); }}>
              <ListItemText primary="Newest First" />
              {sortOption === 'newest' && <ListItemIcon sx={{ ml: 1 }}>✓</ListItemIcon>}
            </MenuItem>
            <MenuItem onClick={() => { setSortOption('age_asc'); setSortAnchorEl(null); }}>
              <ListItemText primary="Age: Low to High" />
              {sortOption === 'age_asc' && <ListItemIcon sx={{ ml: 1 }}>✓</ListItemIcon>}
            </MenuItem>
            <MenuItem onClick={() => { setSortOption('age_desc'); setSortAnchorEl(null); }}>
              <ListItemText primary="Age: High to Low" />
              {sortOption === 'age_desc' && <ListItemIcon sx={{ ml: 1 }}>✓</ListItemIcon>}
            </MenuItem>
          </Menu>
          
          <Divider sx={{ mb: 2 }} />
          
          {/* Active Filters */}
          {searchPerformed && currentSearchParams && (
            <FilterChips 
              searchParams={currentSearchParams}
              onRemoveFilter={handleRemoveFilter}
              onClearAllFilters={handleClearAllFilters}
            />
          )}

          {/* Loading indicator */}
          {isSearching && (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
              <CircularProgress />
            </Box>
          )}
          
          {/* Search results grid */}
          {!isSearching && (
            <Grid container spacing={3}>
              {searchResults.map(profile => (
                <Grid item xs={12} sm={6} md={4} key={profile.id}>
                  <Card
                    variant="outlined"
                    sx={{
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        boxShadow: theme.shadows[4],
                        transform: 'translateY(-4px)'
                      },
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      position: 'relative',
                      ...(profile.isSpotlighted && {
                        boxShadow: `0 0 15px ${theme.palette.secondary.main}`,
                        border: `1px solid ${theme.palette.secondary.main}`,
                        transform: 'scale(1.02)',
                        zIndex: 1
                      })
                    }}
                  >
                    <Box
                      sx={{
                        position: 'relative',
                        pt: '75%', // 4:3 Aspect ratio
                        backgroundImage: `url(${profile.photoUrl})`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center'
                      }}
                    >
                      {/* Spotlight indicator */}
                      {profile.isSpotlighted && (
                        <SpotlightIndicator
                          position="top-right"
                          size="medium"
                          tooltipText="Spotlight Profile - Featured for 24 hours"
                        />
                      )}

                      <Box
                        sx={{
                          position: 'absolute',
                          top: 8,
                          right: profile.isSpotlighted ? 48 : 8, // Move to the left if there's a spotlight indicator
                          bgcolor: 'rgba(255,255,255,0.9)',
                          borderRadius: 1,
                          px: 1,
                          py: 0.5
                        }}
                      >
                        <Typography variant="caption" fontWeight="medium">
                          {profile.id}
                        </Typography>
                      </Box>

                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          right: 0,
                          bgcolor: 'rgba(0,0,0,0.6)',
                          color: 'white',
                          p: 1.5
                        }}
                      >
                        <Typography variant="subtitle1" component="h2" fontWeight="medium">
                          {profile.name}
                        </Typography>
                        <Typography variant="body2">
                          {profile.age} yrs, {profile.height} • {profile.location}
                        </Typography>
                      </Box>
                    </Box>

                    <CardContent sx={{ flexGrow: 1 }}>
                      <Typography variant="body2" gutterBottom>
                        {profile.education}, {profile.occupation}
                      </Typography>

                      <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {profile.isVerified && (
                          <Chip
                            label="Verified"
                            size="small"
                            color="success"
                            variant="outlined"
                          />
                        )}
                        {profile.isPremium && (
                          <Chip
                            label="Premium"
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        )}
                        {profile.isSpotlighted && (
                          <Chip
                            size="small"
                            label="Spotlight"
                            color="secondary"
                            icon={<FlashOnIcon style={{ fontSize: 16 }} />}
                            sx={{
                              animation: `${keyframes`
                                0% { box-shadow: 0 0 0 0 rgba(233, 30, 99, 0.4); }
                                70% { box-shadow: 0 0 0 6px rgba(233, 30, 99, 0); }
                                100% { box-shadow: 0 0 0 0 rgba(233, 30, 99, 0); }
                              `} 2s infinite`
                            }}
                          />
                        )}
                        <Chip
                          label={profile.religion}
                          size="small"
                          variant="outlined"
                        />
                      </Box>
                    </CardContent>

                    <Box sx={{ display: 'flex', p: 1.5, pt: 0 }}>
                      <Button
                        size="small"
                        variant="outlined"
                        sx={{ flexGrow: 1, mr: 1 }}
                      >
                        View Profile
                      </Button>
                      <Button
                        size="small"
                        variant="contained"
                        color="primary"
                        sx={{ flexGrow: 1 }}
                      >
                        Connect
                      </Button>
                    </Box>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
          
          {/* Pagination */}
          {searchPerformed && allResults.length > 0 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Pagination 
                count={totalPages} 
                page={page} 
                onChange={handlePageChange}
                color="primary"
                size="large"
                showFirstButton
                showLastButton
              />
            </Box>
          )}
          
          {/* Mock data indicator */}
          {searchPerformed && !isUsingRealBackend() && (
            <Paper 
              variant="outlined" 
              sx={{ 
                mt: 4, 
                p: 2, 
                bgcolor: 'rgba(255, 244, 229, 0.5)',
                border: '1px dashed #FF9800'
              }}
            >
              <Typography variant="body2" color="warning.dark" align="center">
                <b>Development Mode:</b> Using mock data. Toggle to real data in the admin panel.
              </Typography>
            </Paper>
          )}
        </Box>
      </Container>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleCloseNotification} 
          severity={notification.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </>
  );
}
