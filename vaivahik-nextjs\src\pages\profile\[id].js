/**
 * Dynamic Profile View Page
 * 
 * This page displays a user's profile based on the profile ID in the URL.
 * Route: /profile/[id]
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Avatar,
  Chip,
  Button,
  Card,
  CardContent,
  Divider,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  Share as ShareIcon,
  Report as ReportIcon,
  Phone as PhoneIcon,
  Message as MessageIcon,
  Star as StarIcon
} from '@mui/icons-material';
import { isUsingRealBackend } from '@/utils/featureFlags';
import { useAuth } from '@/contexts/AuthContext';

// Mock profile data for development
const MOCK_PROFILE_DATA = {
  id: '1',
  fullName: '<PERSON><PERSON> <PERSON>',
  age: 26,
  gender: 'FEMALE',
  city: 'Mumbai',
  state: 'Maharashtra',
  occupation: 'Software Engineer',
  education: 'B.Tech Computer Science',
  maritalStatus: 'NEVER_MARRIED',
  religion: 'Hindu',
  caste: '<PERSON><PERSON>',
  subCaste: 'Ku<PERSON><PERSON>',
  height: 165,
  profilePicture: '/api/placeholder/400/400',
  photos: [
    '/api/placeholder/400/400',
    '/api/placeholder/400/400',
    '/api/placeholder/400/400'
  ],
  aboutMe: 'I am a software engineer working in Mumbai. I love traveling, reading books, and spending time with family. Looking for a life partner who shares similar values and interests.',
  interests: ['Reading', 'Traveling', 'Cooking', 'Music'],
  isVerified: true,
  isPremium: false,
  lastActive: '2024-01-15T10:30:00Z',
  compatibility: 94,
  isOnline: true,
  privacySettings: {
    showFullName: true,
    showContactInfo: false,
    showPhotos: true
  }
};

export default function ProfileView() {
  const router = useRouter();
  const { id } = router.query;
  const { user } = useAuth();
  
  const [profileData, setProfileData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isShortlisted, setIsShortlisted] = useState(false);
  const [showContactInfo, setShowContactInfo] = useState(false);

  // Fetch profile data
  useEffect(() => {
    if (!id) return;

    const fetchProfile = async () => {
      try {
        setLoading(true);
        setError('');

        if (isUsingRealBackend()) {
          // Call real API
          const response = await fetch(`/api/users/view-profile/${id}`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          });

          if (!response.ok) {
            throw new Error('Failed to fetch profile');
          }

          const data = await response.json();
          setProfileData(data.profile);
        } else {
          // Use mock data
          setTimeout(() => {
            setProfileData({ ...MOCK_PROFILE_DATA, id });
            setLoading(false);
          }, 1000);
        }
      } catch (err) {
        console.error('Error fetching profile:', err);
        setError('Failed to load profile. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, [id]);

  const handleBack = () => {
    router.back();
  };

  const handleShortlist = () => {
    setIsShortlisted(!isShortlisted);
    // TODO: Call API to add/remove from shortlist
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: `${profileData.fullName}'s Profile`,
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      // TODO: Show toast notification
    }
  };

  const handleReport = () => {
    // TODO: Open report modal
    console.log('Report profile:', id);
  };

  const handleContact = () => {
    setShowContactInfo(true);
    // TODO: Call API to reveal contact info (premium feature)
  };

  const handleMessage = () => {
    router.push(`/messages/${id}`);
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
          <CircularProgress size={60} />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button variant="outlined" onClick={handleBack} startIcon={<ArrowBackIcon />}>
          Go Back
        </Button>
      </Container>
    );
  }

  if (!profileData) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning" sx={{ mb: 2 }}>
          Profile not found
        </Alert>
        <Button variant="outlined" onClick={handleBack} startIcon={<ArrowBackIcon />}>
          Go Back
        </Button>
      </Container>
    );
  }

  return (
    <>
      <Head>
        <title>{profileData.fullName} - Profile | Vaivahik</title>
        <meta name="description" content={`View ${profileData.fullName}'s profile on Vaivahik`} />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header */}
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={handleBack}
          >
            Back
          </Button>
          
          <Box display="flex" gap={1}>
            <Tooltip title={isShortlisted ? "Remove from Shortlist" : "Add to Shortlist"}>
              <IconButton onClick={handleShortlist} color={isShortlisted ? "primary" : "default"}>
                {isShortlisted ? <FavoriteIcon /> : <FavoriteBorderIcon />}
              </IconButton>
            </Tooltip>
            <Tooltip title="Share Profile">
              <IconButton onClick={handleShare}>
                <ShareIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Report Profile">
              <IconButton onClick={handleReport}>
                <ReportIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <Grid container spacing={3}>
          {/* Profile Card */}
          <Grid item xs={12} md={4}>
            <Card elevation={3}>
              <CardContent sx={{ textAlign: 'center' }}>
                <Avatar
                  src={profileData.profilePicture}
                  sx={{ width: 200, height: 200, mx: 'auto', mb: 2 }}
                />
                
                <Typography variant="h5" gutterBottom>
                  {profileData.privacySettings?.showFullName ? profileData.fullName : 'Profile Hidden'}
                </Typography>
                
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {profileData.age} years • {profileData.city}, {profileData.state}
                </Typography>

                {profileData.compatibility && (
                  <Chip
                    label={`${profileData.compatibility}% Match`}
                    color="primary"
                    sx={{ mb: 2 }}
                  />
                )}

                <Box display="flex" justifyContent="center" gap={1} mb={2}>
                  {profileData.isVerified && (
                    <Chip label="Verified" color="success" size="small" />
                  )}
                  {profileData.isPremium && (
                    <Chip label="Premium" color="warning" size="small" />
                  )}
                  {profileData.isOnline && (
                    <Chip label="Online" color="info" size="small" />
                  )}
                </Box>

                <Box display="flex" gap={1} justifyContent="center">
                  <Button
                    variant="contained"
                    startIcon={<PhoneIcon />}
                    onClick={handleContact}
                    fullWidth
                  >
                    Contact
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<MessageIcon />}
                    onClick={handleMessage}
                    fullWidth
                  >
                    Message
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Profile Details */}
          <Grid item xs={12} md={8}>
            <Card elevation={3}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  About
                </Typography>
                <Typography variant="body1" paragraph>
                  {profileData.aboutMe}
                </Typography>

                <Divider sx={{ my: 2 }} />

                <Typography variant="h6" gutterBottom>
                  Basic Information
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Education</Typography>
                    <Typography variant="body1">{profileData.education}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Occupation</Typography>
                    <Typography variant="body1">{profileData.occupation}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Religion</Typography>
                    <Typography variant="body1">{profileData.religion}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Caste</Typography>
                    <Typography variant="body1">{profileData.caste}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Height</Typography>
                    <Typography variant="body1">{profileData.height} cm</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Marital Status</Typography>
                    <Typography variant="body1">{profileData.maritalStatus.replace('_', ' ')}</Typography>
                  </Grid>
                </Grid>

                {profileData.interests && profileData.interests.length > 0 && (
                  <>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="h6" gutterBottom>
                      Interests
                    </Typography>
                    <Box display="flex" flexWrap="wrap" gap={1}>
                      {profileData.interests.map((interest, index) => (
                        <Chip key={index} label={interest} variant="outlined" />
                      ))}
                    </Box>
                  </>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </>
  );
}
