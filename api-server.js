console.log('Starting API server...');

const http = require('http');
const fs = require('fs');
const path = require('path');

const server = http.createServer((req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // Parse the URL
  const url = req.url;
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] Received ${req.method} request for: ${url}`);

  // Handle POST requests
  if (req.method === 'POST' || req.method === 'PUT') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      const timestamp = new Date().toISOString();
      console.log(`[${timestamp}] Request body: ${body}`);

      // Handle specific POST/PUT endpoints
      if (url === '/api/admin/verification-queue/1/approve' || url === '/api/admin/verification-queue/1/approve/') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: true,
          message: "User verification approved successfully"
        }));
      } else if (url === '/api/admin/verification-queue/1/reject' || url === '/api/admin/verification-queue/1/reject/') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: true,
          message: "User verification rejected successfully"
        }));
      } else if (url === '/api/admin/reported-profiles/1/resolve' || url === '/api/admin/reported-profiles/1/resolve/') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: true,
          message: "Reported profile resolved successfully"
        }));
      } else if (url === '/api/admin/reported-profiles/1/dismiss' || url === '/api/admin/reported-profiles/1/dismiss/') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: true,
          message: "Reported profile dismissed successfully"
        }));
      } else {
        res.writeHead(404, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: false,
          message: "Endpoint not found"
        }));
      }
    });
    return; // Important: return here to prevent further processing
  }

  // Handle GET requests
  if (url === '/api/hello') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      message: "API is working!",
      data: {
        name: "John Doe",
        timestamp: new Date().toISOString()
      }
    }));
  } else if (url === '/api/admin/premium-plans') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      plans: [
        { id: 1, name: "Basic", price: 999, features: ["Feature 1", "Feature 2"] },
        { id: 2, name: "Premium", price: 1999, features: ["Feature 1", "Feature 2", "Feature 3"] },
        { id: 3, name: "Gold", price: 2999, features: ["Feature 1", "Feature 2", "Feature 3", "Feature 4"] }
      ]
    }));
  } else if (url === '/api/admin/verification-queue') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      queue: [
        { id: 1, name: "User 1", status: "Pending" },
        { id: 2, name: "User 2", status: "Approved" },
        { id: 3, name: "User 3", status: "Rejected" }
      ]
    }));
  } else if (url === '/api/admin/reported-profiles') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      reports: [
        { id: 1, reportedUser: "User 1", reportedBy: "User 2", reason: "Fake Profile" },
        { id: 2, reportedUser: "User 3", reportedBy: "User 4", reason: "Inappropriate Content" },
        { id: 3, reportedUser: "User 5", reportedBy: "User 6", reason: "Harassment" }
      ]
    }));
  } else if (url === '/api-viewer.html') {
    fs.readFile(path.join(__dirname, 'api-viewer.html'), (err, data) => {
      if (err) {
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end('Internal Server Error');
        return;
      }
      res.writeHead(200, { 'Content-Type': 'text/html' });
      res.end(data);
    });
  } else if (url === '/admin') {
    fs.readFile(path.join(__dirname, 'admin.html'), (err, data) => {
      if (err) {
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end('Internal Server Error');
        return;
      }
      res.writeHead(200, { 'Content-Type': 'text/html' });
      res.end(data);
    });
  } else if (url.startsWith('/vaivahik-admin-ui/')) {
    const filePath = path.join(__dirname, url);
    fs.readFile(filePath, (err, data) => {
      if (err) {
        res.writeHead(404, { 'Content-Type': 'text/plain' });
        res.end('File not found');
        return;
      }

      // Set the correct content type based on file extension
      const ext = path.extname(filePath).toLowerCase();
      let contentType = 'text/plain';

      switch (ext) {
        case '.html':
          contentType = 'text/html';
          break;
        case '.css':
          contentType = 'text/css';
          break;
        case '.js':
          contentType = 'application/javascript';
          break;
        case '.json':
          contentType = 'application/json';
          break;
        case '.png':
          contentType = 'image/png';
          break;
        case '.jpg':
        case '.jpeg':
          contentType = 'image/jpeg';
          break;
      }

      res.writeHead(200, { 'Content-Type': contentType });
      res.end(data);
    });
  } else if (url === '/') {
    res.writeHead(302, { 'Location': '/admin' });
    res.end();
  } else {
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: false,
      message: "Endpoint not found"
    }));
  }
});

const PORT = 3001;
server.listen(PORT, () => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] Server running at http://localhost:${PORT}/`);
  console.log(`[${timestamp}] API Viewer: http://localhost:${PORT}/api-viewer.html`);
  console.log(`\nAPI endpoints:`);
  console.log(`- http://localhost:${PORT}/api/hello`);
  console.log(`- http://localhost:${PORT}/api/admin/premium-plans`);
  console.log(`- http://localhost:${PORT}/api/admin/verification-queue`);
  console.log(`- http://localhost:${PORT}/api/admin/reported-profiles`);
  console.log(`\nPOST endpoints:`);
  console.log(`- http://localhost:${PORT}/api/admin/verification-queue/1/approve`);
  console.log(`- http://localhost:${PORT}/api/admin/verification-queue/1/reject`);
  console.log(`- http://localhost:${PORT}/api/admin/reported-profiles/1/resolve`);
  console.log(`- http://localhost:${PORT}/api/admin/reported-profiles/1/dismiss`);
  console.log(`\nServer is ready to accept requests.`);
});

// Log any errors
server.on('error', (err) => {
  console.error('Server error:', err);
});
