// src/controllers/admin/transactions.controller.js

const logger = require('../../utils/logger');

// Mock data for transactions
const mockTransactions = [
    {
        id: 'txn_001',
        userId: 'user_123',
        userName: '<PERSON><PERSON>',
        userEmail: '<EMAIL>',
        amount: 1299,
        currency: 'INR',
        status: 'SUCCESS',
        paymentMethod: 'UPI',
        paymentId: 'pay_razorpay_001',
        orderId: 'order_001',
        planName: 'Premium Plan',
        planDuration: 90,
        description: 'Premium subscription for 3 months',
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
    },
    {
        id: 'txn_002',
        userId: 'user_456',
        userName: 'Rahul Patil',
        userEmail: '<EMAIL>',
        amount: 499,
        currency: 'INR',
        status: 'SUCCESS',
        paymentMethod: 'Credit Card',
        paymentId: 'pay_razorpay_002',
        orderId: 'order_002',
        planName: 'Basic Plan',
        planDuration: 30,
        description: 'Basic subscription for 1 month',
        createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
        updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
    },
    {
        id: 'txn_003',
        userId: 'user_789',
        userName: 'Anjali Desai',
        userEmail: '<EMAIL>',
        amount: 199,
        currency: 'INR',
        status: 'PENDING',
        paymentMethod: 'Net Banking',
        paymentId: 'pay_razorpay_003',
        orderId: 'order_003',
        planName: 'Profile Boost',
        planDuration: 1,
        description: 'Profile spotlight for 24 hours',
        createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
    },
    {
        id: 'txn_004',
        userId: 'user_101',
        userName: 'Vikram Jadhav',
        userEmail: '<EMAIL>',
        amount: 4999,
        currency: 'INR',
        status: 'FAILED',
        paymentMethod: 'UPI',
        paymentId: 'pay_razorpay_004',
        orderId: 'order_004',
        planName: 'Elite Plan',
        planDuration: 365,
        description: 'Elite subscription for 12 months',
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        updatedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    }
];

// Check if mock data should be used
const shouldUseMockData = () => {
    return process.env.NODE_ENV === 'development' || process.env.USE_MOCK_DATA === 'true';
};

/**
 * @description Get all transactions
 * @route GET /api/admin/transactions
 */
exports.getTransactions = async (req, res, next) => {
    try {
        const { 
            page = 1, 
            limit = 10, 
            search = '',
            status = '',
            paymentMethod = '',
            sortBy = 'createdAt', 
            order = 'desc'
        } = req.query;

        if (shouldUseMockData()) {
            // Return mock data
            let filteredTransactions = [...mockTransactions];

            // Apply search filter
            if (search) {
                filteredTransactions = filteredTransactions.filter(txn => 
                    txn.userName.toLowerCase().includes(search.toLowerCase()) ||
                    txn.userEmail.toLowerCase().includes(search.toLowerCase()) ||
                    txn.id.toLowerCase().includes(search.toLowerCase()) ||
                    txn.planName.toLowerCase().includes(search.toLowerCase())
                );
            }

            // Apply status filter
            if (status) {
                filteredTransactions = filteredTransactions.filter(txn => txn.status === status);
            }

            // Apply payment method filter
            if (paymentMethod) {
                filteredTransactions = filteredTransactions.filter(txn => txn.paymentMethod === paymentMethod);
            }

            // Apply sorting
            const sortOrder = order.toLowerCase() === 'asc' ? 1 : -1;
            filteredTransactions.sort((a, b) => {
                const aValue = a[sortBy];
                const bValue = b[sortBy];
                if (aValue < bValue) return -1 * sortOrder;
                if (aValue > bValue) return 1 * sortOrder;
                return 0;
            });

            // Apply pagination
            const pageNum = parseInt(page);
            const limitNum = parseInt(limit);
            const startIndex = (pageNum - 1) * limitNum;
            const endIndex = startIndex + limitNum;
            const paginatedTransactions = filteredTransactions.slice(startIndex, endIndex);

            return res.status(200).json({
                success: true,
                message: "Transactions fetched successfully (Mock Data)",
                transactions: paginatedTransactions,
                pagination: {
                    currentPage: pageNum,
                    limit: limitNum,
                    totalPages: Math.ceil(filteredTransactions.length / limitNum),
                    totalTransactions: filteredTransactions.length
                },
                useMockData: true
            });
        }

        // Real database implementation would go here
        const prisma = req.prisma;
        
        return res.status(200).json({
            success: true,
            message: "Transactions fetched successfully (Real Data - Not Implemented Yet)",
            transactions: [],
            pagination: {
                currentPage: 1,
                limit: parseInt(limit),
                totalPages: 0,
                totalTransactions: 0
            },
            useMockData: false
        });

    } catch (error) {
        logger.error('Error fetching transactions:', error);
        next(error);
    }
};

/**
 * @description Get transaction by ID
 * @route GET /api/admin/transactions/:id
 */
exports.getTransactionById = async (req, res, next) => {
    try {
        const { id } = req.params;

        if (shouldUseMockData()) {
            const transaction = mockTransactions.find(t => t.id === id);
            if (!transaction) {
                return res.status(404).json({
                    success: false,
                    message: "Transaction not found"
                });
            }

            return res.status(200).json({
                success: true,
                message: "Transaction fetched successfully",
                transaction,
                useMockData: true
            });
        }

        // Real database implementation would go here
        return res.status(404).json({
            success: false,
            message: "Transaction not found (Real Data - Not Implemented Yet)"
        });

    } catch (error) {
        logger.error('Error fetching transaction:', error);
        next(error);
    }
};

/**
 * @description Update transaction status
 * @route PUT /api/admin/transactions/:id/status
 */
exports.updateTransactionStatus = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { status } = req.body;

        if (shouldUseMockData()) {
            const transactionIndex = mockTransactions.findIndex(t => t.id === id);
            if (transactionIndex === -1) {
                return res.status(404).json({
                    success: false,
                    message: "Transaction not found"
                });
            }

            mockTransactions[transactionIndex].status = status;
            mockTransactions[transactionIndex].updatedAt = new Date();

            return res.status(200).json({
                success: true,
                message: "Transaction status updated successfully (Mock Data)",
                transaction: mockTransactions[transactionIndex],
                useMockData: true
            });
        }

        // Real database implementation would go here
        return res.status(501).json({
            success: false,
            message: "Update transaction status not implemented for real data yet"
        });

    } catch (error) {
        logger.error('Error updating transaction status:', error);
        next(error);
    }
};

/**
 * @description Get transaction statistics
 * @route GET /api/admin/transactions/stats/overview
 */
exports.getTransactionStats = async (req, res, next) => {
    try {
        if (shouldUseMockData()) {
            const stats = {
                totalTransactions: mockTransactions.length,
                successfulTransactions: mockTransactions.filter(t => t.status === 'SUCCESS').length,
                pendingTransactions: mockTransactions.filter(t => t.status === 'PENDING').length,
                failedTransactions: mockTransactions.filter(t => t.status === 'FAILED').length,
                totalRevenue: mockTransactions
                    .filter(t => t.status === 'SUCCESS')
                    .reduce((sum, t) => sum + t.amount, 0),
                averageTransactionValue: mockTransactions
                    .filter(t => t.status === 'SUCCESS')
                    .reduce((sum, t) => sum + t.amount, 0) / mockTransactions.filter(t => t.status === 'SUCCESS').length || 0
            };

            return res.status(200).json({
                success: true,
                message: "Transaction statistics fetched successfully (Mock Data)",
                stats,
                useMockData: true
            });
        }

        // Real database implementation would go here
        return res.status(200).json({
            success: true,
            message: "Transaction statistics fetched successfully (Real Data - Not Implemented Yet)",
            stats: {
                totalTransactions: 0,
                successfulTransactions: 0,
                pendingTransactions: 0,
                failedTransactions: 0,
                totalRevenue: 0,
                averageTransactionValue: 0
            },
            useMockData: false
        });

    } catch (error) {
        logger.error('Error fetching transaction statistics:', error);
        next(error);
    }
};

/**
 * @description Get revenue analytics
 * @route GET /api/admin/transactions/analytics/revenue
 */
exports.getRevenueAnalytics = async (req, res, next) => {
    try {
        if (shouldUseMockData()) {
            const analytics = {
                dailyRevenue: [
                    { date: '2024-01-01', revenue: 1299 },
                    { date: '2024-01-02', revenue: 499 },
                    { date: '2024-01-03', revenue: 0 },
                    { date: '2024-01-04', revenue: 199 }
                ],
                monthlyRevenue: [
                    { month: 'Jan 2024', revenue: 15000 },
                    { month: 'Feb 2024', revenue: 18500 },
                    { month: 'Mar 2024', revenue: 22000 }
                ],
                paymentMethodBreakdown: [
                    { method: 'UPI', count: 2, revenue: 1498 },
                    { method: 'Credit Card', count: 1, revenue: 499 },
                    { method: 'Net Banking', count: 1, revenue: 0 }
                ]
            };

            return res.status(200).json({
                success: true,
                message: "Revenue analytics fetched successfully (Mock Data)",
                analytics,
                useMockData: true
            });
        }

        // Real database implementation would go here
        return res.status(200).json({
            success: true,
            message: "Revenue analytics fetched successfully (Real Data - Not Implemented Yet)",
            analytics: {
                dailyRevenue: [],
                monthlyRevenue: [],
                paymentMethodBreakdown: []
            },
            useMockData: false
        });

    } catch (error) {
        logger.error('Error fetching revenue analytics:', error);
        next(error);
    }
};
