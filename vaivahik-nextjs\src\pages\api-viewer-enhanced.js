import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';

// Dynamically import SwaggerUI to avoid SSR issues
const SwaggerUI = dynamic(
  () => import('swagger-ui-react').then((mod) => mod.default),
  { ssr: false }
);

export default function ApiViewerEnhanced() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [authToken, setAuthToken] = useState('');
  const [showAuthModal, setShowAuthModal] = useState(false);
  const router = useRouter();

  // Function to handle authentication
  const handleAuth = (e) => {
    e.preventDefault();
    localStorage.setItem('api_auth_token', authToken);
    setShowAuthModal(false);
  };

  // Function to clear authentication
  const clearAuth = () => {
    localStorage.removeItem('api_auth_token');
    setAuthToken('');
    alert('Authentication token cleared');
  };

  // Load auth token from localStorage on component mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedToken = localStorage.getItem('api_auth_token');
      if (savedToken) {
        setAuthToken(savedToken);
      }
    }
    setLoading(false);
  }, []);

  // Custom request interceptor for Swagger UI
  const requestInterceptor = (req) => {
    if (authToken) {
      req.headers.Authorization = `Bearer ${authToken}`;
    }
    return req;
  };

  return (
    <div className="container">
      <Head>
        <title>Enhanced API Viewer - Vaivahik Admin</title>
        <meta name="description" content="Interactive API documentation and testing tool" />
        <link rel="icon" href="/favicon.ico" />
        {/* Import Swagger UI styles */}
        <link
          rel="stylesheet"
          href="https://unpkg.com/swagger-ui-dist@4.5.0/swagger-ui.css"
        />
      </Head>

      <main className="main">
        <div className="header">
          <h1 className="title">Vaivahik API Documentation</h1>
          
          <div className="actions">
            <button 
              className="auth-button"
              onClick={() => setShowAuthModal(true)}
            >
              {authToken ? 'Update Auth Token' : 'Set Auth Token'}
            </button>
            
            {authToken && (
              <button 
                className="clear-auth-button"
                onClick={clearAuth}
              >
                Clear Auth Token
              </button>
            )}
            
            <Link href="/api-discovery" className="tool-link">
              API Discovery Tool
            </Link>
            
            <Link href="/admin/dashboard" className="tool-link">
              Back to Dashboard
            </Link>
          </div>
        </div>

        {loading ? (
          <div className="loading">Loading API documentation...</div>
        ) : (
          <div className="swagger-container">
            <SwaggerUI
              url="/api/openapi"
              docExpansion="list"
              defaultModelsExpandDepth={1}
              requestInterceptor={requestInterceptor}
              onComplete={() => console.log('Swagger UI loaded')}
            />
          </div>
        )}

        {/* Authentication Modal */}
        {showAuthModal && (
          <div className="modal-overlay">
            <div className="auth-modal">
              <h2>Set Authentication Token</h2>
              <p>Enter your JWT token to authenticate API requests:</p>
              
              <form onSubmit={handleAuth}>
                <textarea
                  value={authToken}
                  onChange={(e) => setAuthToken(e.target.value)}
                  placeholder="Enter your JWT token here"
                  rows={4}
                />
                
                <div className="modal-actions">
                  <button type="button" onClick={() => setShowAuthModal(false)} className="cancel-button">
                    Cancel
                  </button>
                  <button type="submit" className="save-button">
                    Save Token
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </main>

      <style jsx>{`
        .container {
          min-height: 100vh;
          padding: 0;
          display: flex;
          flex-direction: column;
          background-color: #f5f5f5;
        }

        .main {
          flex: 1;
          display: flex;
          flex-direction: column;
          width: 100%;
        }

        .header {
          background-color: #5e35b1;
          color: white;
          padding: 1rem 2rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: wrap;
          gap: 1rem;
        }

        .title {
          margin: 0;
          font-size: 1.5rem;
          font-weight: 600;
        }

        .actions {
          display: flex;
          gap: 0.75rem;
          flex-wrap: wrap;
        }

        .auth-button, .clear-auth-button {
          padding: 0.5rem 1rem;
          border: none;
          border-radius: 4px;
          font-size: 0.9rem;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .auth-button {
          background-color: white;
          color: #5e35b1;
        }

        .auth-button:hover {
          background-color: #f0f0f0;
        }

        .clear-auth-button {
          background-color: rgba(255, 255, 255, 0.2);
          color: white;
        }

        .clear-auth-button:hover {
          background-color: rgba(255, 255, 255, 0.3);
        }

        .tool-link {
          padding: 0.5rem 1rem;
          background-color: rgba(255, 255, 255, 0.2);
          color: white;
          border-radius: 4px;
          text-decoration: none;
          font-size: 0.9rem;
          transition: background-color 0.2s;
        }

        .tool-link:hover {
          background-color: rgba(255, 255, 255, 0.3);
        }

        .loading {
          padding: 2rem;
          text-align: center;
          color: #666;
        }

        .swagger-container {
          flex: 1;
          padding: 0;
          background-color: white;
        }

        .swagger-container :global(.swagger-ui) {
          margin: 0;
          padding: 1rem;
        }

        /* Modal styles */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 1000;
        }

        .auth-modal {
          background-color: white;
          border-radius: 8px;
          padding: 1.5rem;
          width: 90%;
          max-width: 500px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .auth-modal h2 {
          margin-top: 0;
          color: #333;
        }

        .auth-modal p {
          color: #666;
          margin-bottom: 1rem;
        }

        .auth-modal textarea {
          width: 100%;
          padding: 0.75rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-family: monospace;
          margin-bottom: 1rem;
          resize: vertical;
        }

        .modal-actions {
          display: flex;
          justify-content: flex-end;
          gap: 0.75rem;
        }

        .cancel-button, .save-button {
          padding: 0.5rem 1rem;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        }

        .cancel-button {
          background-color: #f0f0f0;
          color: #333;
        }

        .save-button {
          background-color: #5e35b1;
          color: white;
        }

        @media (max-width: 768px) {
          .header {
            flex-direction: column;
            align-items: flex-start;
          }

          .actions {
            width: 100%;
            justify-content: flex-start;
          }
        }
      `}</style>
    </div>
  );
}
