// Node.js script to find AdminLayout usage in admin pages
const fs = require('fs');
const path = require('path');

// Path to admin pages directory
const adminPagesDir = path.join(__dirname, 'vaivahik-nextjs', 'src', 'pages', 'admin');

// Arrays to store results
const usingAdminLayout = [];
const usingEnhancedAdminLayout = [];
const usingBoth = [];
const usingNeither = [];

// Function to check file content
function checkFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fileName = path.relative(adminPagesDir, filePath);
    
    const hasAdminLayout = content.includes('import AdminLayout from');
    const hasEnhancedAdminLayout = content.includes('import EnhancedAdminLayout from') || 
                                  content.includes('() => import(\'@/components/admin/EnhancedAdminLayout\')');
    
    if (hasAdminLayout && hasEnhancedAdminLayout) {
      usingBoth.push(fileName);
    } else if (hasAdminLayout) {
      usingAdminLayout.push(fileName);
    } else if (hasEnhancedAdminLayout) {
      usingEnhancedAdminLayout.push(fileName);
    } else {
      // Check if it's a React component (has export default or export function)
      if (content.includes('export default') || content.includes('export function')) {
        usingNeither.push(fileName);
      }
    }
  } catch (err) {
    console.error(`Error reading file ${filePath}:`, err);
  }
}

// Function to recursively scan directory
function scanDirectory(directory) {
  const files = fs.readdirSync(directory);
  
  for (const file of files) {
    const fullPath = path.join(directory, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      scanDirectory(fullPath);
    } else if (file.endsWith('.js') || file.endsWith('.jsx') || file.endsWith('.tsx')) {
      checkFile(fullPath);
    }
  }
}

// Start scanning
console.log('Scanning admin pages for layout usage...');
scanDirectory(adminPagesDir);

// Print results
console.log('\n=== RESULTS ===\n');

console.log(`Pages using AdminLayout (${usingAdminLayout.length}):`);
usingAdminLayout.forEach(file => console.log(`- ${file}`));

console.log(`\nPages using EnhancedAdminLayout (${usingEnhancedAdminLayout.length}):`);
usingEnhancedAdminLayout.forEach(file => console.log(`- ${file}`));

console.log(`\nPages using BOTH layouts (${usingBoth.length}):`);
usingBoth.forEach(file => console.log(`- ${file}`));

console.log(`\nPages using NEITHER layout (${usingNeither.length}):`);
usingNeither.forEach(file => console.log(`- ${file}`));

console.log('\nDone!');
