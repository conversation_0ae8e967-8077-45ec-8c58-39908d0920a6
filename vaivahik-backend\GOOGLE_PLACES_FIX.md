# 🗺️ Google Places API Configuration Fix

## Issue
Your Google Places API key has **referer restrictions** that prevent server-side usage.

## Error
```
REQUEST_DENIED: API keys with referer restrictions cannot be used with this API.
```

## Solutions

### Option 1: Create Separate API Keys (Recommended)
1. **Frontend API Key** (with referer restrictions)
   - Use for client-side JavaScript
   - Restrict to your domain: `https://yourdomain.com/*`
   
2. **Backend API Key** (with IP restrictions)
   - Use for server-side calls
   - Restrict to your server IP address

### Option 2: Remove Referer Restrictions (Less Secure)
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to **APIs & Services > Credentials**
3. Click on your API key
4. Under **Application restrictions**, select **None**
5. Save changes

### Option 3: Update Restrictions (Recommended)
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to **APIs & Services > Credentials**
3. Click on your API key
4. Under **Application restrictions**:
   - Select **IP addresses**
   - Add your server IP address
   - Add `127.0.0.1` for local testing

## Current Status
- ✅ API Key exists: `AIzaSyC_YQ...8h7aE`
- ✅ Places API enabled
- ❌ Restrictions blocking server usage

## Next Steps
1. Fix API key restrictions
2. Test again with: `node scripts/test-google-places.js`
3. Verify frontend integration works
