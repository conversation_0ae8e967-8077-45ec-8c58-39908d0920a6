"""
Data Augmentation and Sampling for Matrimony Matching

This module provides functions for data augmentation and balanced sampling
to improve the training of the matrimony matching model.
"""

import random
import numpy as np
import logging
from collections import defaultdict

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataAugmentation:
    """Data augmentation and sampling for matrimony matching data"""
    
    def __init__(self, random_seed=42):
        """
        Initialize the data augmentation module
        
        Args:
            random_seed (int): Random seed for reproducibility
        """
        self.random_seed = random_seed
        random.seed(random_seed)
        np.random.seed(random_seed)
    
    def generate_hard_negatives(self, user_profiles, match_profiles, positive_pairs):
        """
        Generate hard negative examples (similar profiles that are not matches)
        
        Args:
            user_profiles (list): List of user profile dictionaries
            match_profiles (list): List of match profile dictionaries
            positive_pairs (list): List of (user_id, match_id) tuples that are positive matches
            
        Returns:
            list: List of (user_id, match_id) tuples for hard negative examples
        """
        # Create a set of positive pairs for quick lookup
        positive_set = set((u, m) for u, m in positive_pairs)
        
        # Create a dictionary of users by key attributes
        users_by_attributes = defaultdict(list)
        for user in user_profiles:
            # Create a key based on important attributes
            key = (
                self._get_age_bucket(user.get('age')),
                user.get('religion'),
                user.get('caste'),
                self._get_education_level(user.get('education')),
                self._get_income_bucket(user.get('income'))
            )
            users_by_attributes[key].append(user['id'])
        
        # Generate hard negatives
        hard_negatives = []
        
        for match in match_profiles:
            # Create a key based on important attributes
            match_key = (
                self._get_age_bucket(match.get('age')),
                match.get('religion'),
                match.get('caste'),
                self._get_education_level(match.get('education')),
                self._get_income_bucket(match.get('income'))
            )
            
            # Find similar users
            similar_user_ids = []
            for key, user_ids in users_by_attributes.items():
                # Calculate similarity score between keys
                similarity = self._calculate_key_similarity(match_key, key)
                if similarity >= 0.7:  # High similarity threshold
                    similar_user_ids.extend(user_ids)
            
            # Filter out positive pairs
            hard_negative_candidates = [
                (user_id, match['id']) for user_id in similar_user_ids
                if (user_id, match['id']) not in positive_set
            ]
            
            # Sample a limited number of hard negatives per match
            if hard_negative_candidates:
                sampled = random.sample(
                    hard_negative_candidates,
                    min(5, len(hard_negative_candidates))
                )
                hard_negatives.extend(sampled)
        
        return hard_negatives
    
    def _get_age_bucket(self, age):
        """Get age bucket for a given age"""
        if age is None:
            return None
        return age // 5  # 5-year buckets
    
    def _get_education_level(self, education):
        """Map education to a standardized level"""
        if education is None:
            return None
        
        education_lower = education.lower()
        
        if any(term in education_lower for term in ['phd', 'doctorate']):
            return 4
        elif any(term in education_lower for term in ['master', 'post graduate', 'pg']):
            return 3
        elif any(term in education_lower for term in ['bachelor', 'graduate', 'b.', 'engineering']):
            return 2
        elif any(term in education_lower for term in ['diploma', '12th', 'higher secondary']):
            return 1
        else:
            return 0
    
    def _get_income_bucket(self, income):
        """Get income bucket for a given income"""
        if income is None:
            return None
        
        # Convert to lakhs per annum
        income_lakhs = income / 100000
        
        if income_lakhs < 3:
            return 0
        elif income_lakhs < 6:
            return 1
        elif income_lakhs < 10:
            return 2
        elif income_lakhs < 20:
            return 3
        else:
            return 4
    
    def _calculate_key_similarity(self, key1, key2):
        """Calculate similarity between two attribute keys"""
        # Count matching non-None attributes
        matches = 0
        total = 0
        
        for k1, k2 in zip(key1, key2):
            if k1 is not None and k2 is not None:
                total += 1
                if k1 == k2:
                    matches += 1
                elif isinstance(k1, (int, float)) and isinstance(k2, (int, float)):
                    # For numeric values, calculate similarity based on difference
                    diff = abs(k1 - k2)
                    if diff <= 1:  # Close enough
                        matches += 0.5
        
        # Return similarity score
        return matches / total if total > 0 else 0
    
    def balance_dataset(self, user_features, match_features, labels, pos_neg_ratio=1.0):
        """
        Balance the dataset to have a specific positive-to-negative ratio
        
        Args:
            user_features (list): List of user feature dictionaries
            match_features (list): List of match feature dictionaries
            labels (list): List of match labels (1 for match, 0 for non-match)
            pos_neg_ratio (float): Desired ratio of positive to negative examples
            
        Returns:
            tuple: Balanced user_features, match_features, and labels
        """
        # Count positive and negative examples
        pos_indices = [i for i, label in enumerate(labels) if label == 1]
        neg_indices = [i for i, label in enumerate(labels) if label == 0]
        
        num_pos = len(pos_indices)
        num_neg = len(neg_indices)
        
        logger.info(f"Original dataset: {num_pos} positive, {num_neg} negative examples")
        
        # Calculate target number of negative examples
        target_neg = int(num_pos / pos_neg_ratio)
        
        # Sample negative examples
        if num_neg > target_neg:
            # Downsample negatives
            sampled_neg_indices = random.sample(neg_indices, target_neg)
            selected_indices = pos_indices + sampled_neg_indices
        elif num_neg < target_neg:
            # Upsample negatives (with replacement)
            additional_neg_indices = random.choices(neg_indices, k=target_neg - num_neg)
            selected_indices = pos_indices + neg_indices + additional_neg_indices
        else:
            # Already balanced
            selected_indices = pos_indices + neg_indices
        
        # Shuffle indices
        random.shuffle(selected_indices)
        
        # Select examples
        balanced_user_features = [user_features[i] for i in selected_indices]
        balanced_match_features = [match_features[i] for i in selected_indices]
        balanced_labels = [labels[i] for i in selected_indices]
        
        logger.info(f"Balanced dataset: {balanced_labels.count(1)} positive, {balanced_labels.count(0)} negative examples")
        
        return balanced_user_features, balanced_match_features, balanced_labels
    
    def add_noise(self, features, noise_level=0.05):
        """
        Add random noise to numerical features for data augmentation
        
        Args:
            features (list): List of feature dictionaries
            noise_level (float): Standard deviation of Gaussian noise as a fraction of feature value
            
        Returns:
            list: Features with added noise
        """
        # Numerical features to add noise to
        numerical_features = ['age', 'height', 'income']
        
        # Create a copy of the features
        noisy_features = []
        
        for feature_dict in features:
            noisy_dict = feature_dict.copy()
            
            for feature in numerical_features:
                if feature in noisy_dict and isinstance(noisy_dict[feature], (int, float)):
                    # Add Gaussian noise
                    value = noisy_dict[feature]
                    noise = np.random.normal(0, abs(value) * noise_level)
                    noisy_dict[feature] = value + noise
            
            noisy_features.append(noisy_dict)
        
        return noisy_features
    
    def feature_dropout(self, features, dropout_prob=0.1):
        """
        Randomly set some feature values to zero for regularization
        
        Args:
            features (list): List of feature dictionaries
            dropout_prob (float): Probability of dropping a feature
            
        Returns:
            list: Features with some values dropped
        """
        # Create a copy of the features
        dropout_features = []
        
        for feature_dict in features:
            dropout_dict = feature_dict.copy()
            
            # Randomly drop features
            for key in dropout_dict.keys():
                if random.random() < dropout_prob:
                    dropout_dict[key] = 0.0
            
            dropout_features.append(dropout_dict)
        
        return dropout_features
