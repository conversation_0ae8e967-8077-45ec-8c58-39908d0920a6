// src/pages/api/admin/financial/reports/revenue.js
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../auth/[...nextauth]';

export default async function handler(req, res) {
  // Check if user is authenticated and is an admin
  const session = await getServerSession(req, res, authOptions);
  
  if (!session || !session.user || !session.user.isAdmin) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  // Handle GET request - fetch revenue reports
  if (req.method === 'GET') {
    try {
      // Extract query parameters
      const { period = 'monthly', startDate = '', endDate = '' } = req.query;
      
      // Build query parameters
      const queryParams = new URLSearchParams({
        period,
        ...(startDate && { startDate }),
        ...(endDate && { endDate })
      }).toString();

      // Fetch revenue reports from backend API
      const response = await fetch(`${process.env.BACKEND_API_URL}/api/admin/financial/reports/revenue?${queryParams}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.accessToken}`
        }
      }).catch(() => null);
      
      if (response && response.ok) {
        const data = await response.json();
        return res.status(200).json({
          success: true,
          revenue: data.revenue
        });
      } else {
        // Fallback to mock data if backend API fails
        return res.status(200).json({
          success: true,
          revenue: getMockRevenueData(period, startDate, endDate)
        });
      }
    } catch (error) {
      console.error('Error fetching revenue reports:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch revenue reports',
        error: error.message
      });
    }
  }
  
  // Handle unsupported methods
  return res.status(405).json({ success: false, message: 'Method not allowed' });
}

// Helper function to get mock revenue data
function getMockRevenueData(period, startDate, endDate) {
  // Generate periods based on the selected period type
  const periods = generatePeriods(period, startDate, endDate);
  
  // Generate mock revenue data for each period
  const byPeriod = periods.map((periodLabel, index) => {
    const baseRevenue = 50000 + Math.random() * 30000;
    const count = 100 + Math.floor(Math.random() * 100);
    
    // Add some trend - increasing over time with some randomness
    const trendFactor = 1 + (index / periods.length) * 0.5;
    const randomFactor = 0.8 + Math.random() * 0.4;
    
    return {
      period: periodLabel,
      revenue: Math.round(baseRevenue * trendFactor * randomFactor),
      count: Math.round(count * trendFactor * randomFactor)
    };
  });
  
  // Calculate total revenue and transactions
  const total = byPeriod.reduce((sum, item) => sum + item.revenue, 0);
  const totalTransactions = byPeriod.reduce((sum, item) => sum + item.count, 0);
  
  // Generate product type distribution
  const productTypes = ['Premium Plan', 'Standard Plan', 'Basic Plan', 'Spotlight Feature', 'Biodata PDF'];
  const byProductType = productTypes.map(type => {
    const revenue = Math.round(total * (0.1 + Math.random() * 0.3));
    const count = Math.round(totalTransactions * (0.1 + Math.random() * 0.3));
    
    return {
      productType: type,
      revenue,
      count
    };
  });
  
  // Generate payment method distribution
  const paymentMethods = ['Credit Card', 'UPI', 'Net Banking', 'Wallet', 'Other'];
  const byPaymentMethod = paymentMethods.map(method => {
    const revenue = Math.round(total * (0.1 + Math.random() * 0.3));
    const count = Math.round(totalTransactions * (0.1 + Math.random() * 0.3));
    
    return {
      paymentMethod: method,
      revenue,
      count
    };
  });
  
  // Calculate growth metrics
  let growthMetrics = {
    revenueGrowth: 0,
    transactionGrowth: 0
  };
  
  if (byPeriod.length >= 2) {
    const currentPeriodRevenue = byPeriod[byPeriod.length - 1].revenue;
    const previousPeriodRevenue = byPeriod[byPeriod.length - 2].revenue;
    
    const currentPeriodCount = byPeriod[byPeriod.length - 1].count;
    const previousPeriodCount = byPeriod[byPeriod.length - 2].count;
    
    if (previousPeriodRevenue > 0) {
      growthMetrics.revenueGrowth = ((currentPeriodRevenue - previousPeriodRevenue) / previousPeriodRevenue) * 100;
    }
    
    if (previousPeriodCount > 0) {
      growthMetrics.transactionGrowth = ((currentPeriodCount - previousPeriodCount) / previousPeriodCount) * 100;
    }
  }
  
  return {
    total,
    totalTransactions,
    averageTransaction: totalTransactions > 0 ? total / totalTransactions : 0,
    conversionRate: 68.5, // Mock conversion rate
    byPeriod,
    byProductType,
    byPaymentMethod,
    growthMetrics
  };
}

// Helper function to generate period labels
function generatePeriods(periodType, startDate, endDate) {
  const periods = [];
  let start, end;
  
  if (startDate && endDate) {
    start = new Date(startDate);
    end = new Date(endDate);
  } else {
    // Default to last 12 months
    end = new Date();
    start = new Date();
    start.setMonth(start.getMonth() - 12);
  }
  
  // Format date based on period type
  switch (periodType) {
    case 'daily':
      // Generate daily periods
      for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
        periods.push(formatDate(d, 'YYYY-MM-DD'));
      }
      break;
      
    case 'weekly':
      // Generate weekly periods
      for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 7)) {
        periods.push(`Week of ${formatDate(d, 'YYYY-MM-DD')}`);
      }
      break;
      
    case 'yearly':
      // Generate yearly periods
      for (let y = start.getFullYear(); y <= end.getFullYear(); y++) {
        periods.push(y.toString());
      }
      break;
      
    case 'monthly':
    default:
      // Generate monthly periods
      for (let y = start.getFullYear(); y <= end.getFullYear(); y++) {
        const startMonth = y === start.getFullYear() ? start.getMonth() : 0;
        const endMonth = y === end.getFullYear() ? end.getMonth() : 11;
        
        for (let m = startMonth; m <= endMonth; m++) {
          periods.push(`${y}-${(m + 1).toString().padStart(2, '0')}`);
        }
      }
      break;
  }
  
  return periods;
}

// Helper function to format date
function formatDate(date, format) {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day);
}
