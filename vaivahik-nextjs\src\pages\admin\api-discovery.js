import React, { useState, useEffect, useMemo } from 'react';
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);
import { useRouter } from 'next/router';
import axios from 'axios';
import { toast, ToastContainer } from 'react-toastify';
import Link from 'next/link';

export default function ApiDiscovery() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [endpoints, setEndpoints] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedEndpoint, setSelectedEndpoint] = useState(null);

  useEffect(() => {
    fetchEndpoints();
  }, []);

  const fetchEndpoints = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/admin/api-discovery');
      if (response.data.success) {
        setEndpoints(response.data.endpoints);
      } else {
        toast.error('Failed to fetch API endpoints');
      }
    } catch (error) {
      console.error('Error fetching API endpoints:', error);
      toast.error(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Group endpoints by category
  const categorizedEndpoints = useMemo(() => {
    const categories = {
      'User Management': [],
      'Premium Features': [],
      'Notifications': [],
      'Biodata Templates': [],
      'Spotlight Features': [],
      'AI & Matching': [],
      'Content': [],
      'Financial': [],
      'System': [],
      'Other': []
    };

    endpoints.forEach(endpoint => {
      const path = endpoint.path.toLowerCase();

      if (path.includes('user') || path.includes('verification') || path.includes('reported')) {
        categories['User Management'].push(endpoint);
      } else if (path.includes('premium') || path.includes('feature') || path.includes('promotion')) {
        categories['Premium Features'].push(endpoint);
      } else if (path.includes('notification')) {
        categories['Notifications'].push(endpoint);
      } else if (path.includes('biodata')) {
        categories['Biodata Templates'].push(endpoint);
      } else if (path.includes('spotlight')) {
        categories['Spotlight Features'].push(endpoint);
      } else if (path.includes('algorithm') || path.includes('preference') || path.includes('match')) {
        categories['AI & Matching'].push(endpoint);
      } else if (path.includes('photo') || path.includes('text') || path.includes('moderation') || path.includes('blog') || path.includes('success-stories')) {
        categories['Content'].push(endpoint);
      } else if (path.includes('subscription') || path.includes('transaction') || path.includes('revenue') || path.includes('referral')) {
        categories['Financial'].push(endpoint);
      } else if (path.includes('settings') || path.includes('admin')) {
        categories['System'].push(endpoint);
      } else {
        categories['Other'].push(endpoint);
      }
    });

    return categories;
  }, [endpoints]);

  // Filter endpoints based on search term
  const filteredEndpoints = useMemo(() => {
    return endpoints.filter(endpoint =>
      endpoint.path.toLowerCase().includes(searchTerm.toLowerCase()) ||
      endpoint.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [endpoints, searchTerm]);

  const handleEndpointClick = (endpoint) => {
    setSelectedEndpoint(endpoint);
  };

  const tryInApiViewer = (endpoint) => {
    // Extract base path without parameters
    const basePath = endpoint.path.split('/:')[0];
    router.push({
      pathname: '/admin/api-viewer',
      query: {
        endpoint: basePath,
        method: endpoint.methods[0]
      }
    });
  };

  return (
    <EnhancedAdminLayout>
      <ToastContainer position="top-right" autoClose={5000} />
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold">API Discovery</h1>
            <p className="text-gray-600 mt-1">
              {loading ? 'Loading endpoints...' : `${endpoints.length} endpoints available`}
            </p>
          </div>
          <div className="flex space-x-2">
            <button
              className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700"
              onClick={() => setSearchTerm('notification')}
            >
              Notifications
            </button>
            <button
              className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700"
              onClick={() => setSearchTerm('biodata')}
            >
              Biodata
            </button>
            <button
              className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700"
              onClick={() => setSearchTerm('spotlight')}
            >
              Spotlight
            </button>
            <Link href="/admin/api-viewer">
              <button className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                Open API Viewer
              </button>
            </Link>
          </div>
        </div>

        <div className="mb-6 relative">
          <input
            type="text"
            className="w-full border border-gray-300 rounded px-4 py-2 pr-10"
            placeholder="Search endpoints..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          {searchTerm && (
            <button
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
              onClick={() => setSearchTerm('')}
              aria-label="Clear search"
            >
              ✕
            </button>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Endpoints List */}
          <div className="lg:col-span-1 bg-white rounded-lg shadow overflow-hidden">
            <div className="p-4 bg-purple-600 text-white font-semibold">
              Available Endpoints
            </div>

            {loading ? (
              <div className="p-4 text-center">Loading endpoints...</div>
            ) : filteredEndpoints.length === 0 ? (
              <div className="p-4 text-center text-gray-500">No endpoints found</div>
            ) : searchTerm ? (
              // Show flat list when searching
              <div className="divide-y divide-gray-200 max-h-[600px] overflow-y-auto">
                {filteredEndpoints.map((endpoint, index) => (
                  <div
                    key={index}
                    className={`p-4 cursor-pointer hover:bg-gray-50 ${selectedEndpoint?.path === endpoint.path ? 'bg-purple-50 border-l-4 border-purple-600' : ''}`}
                    onClick={() => handleEndpointClick(endpoint)}
                  >
                    <div className="font-medium text-gray-800">{endpoint.path}</div>
                    <div className="text-sm text-gray-500 mt-1">{endpoint.description}</div>
                    <div className="flex mt-2 space-x-2">
                      {endpoint.methods.map(method => (
                        <span
                          key={method}
                          className={`px-2 py-1 text-xs rounded-full ${
                            method === 'GET' ? 'bg-blue-100 text-blue-800' :
                            method === 'POST' ? 'bg-green-100 text-green-800' :
                            method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                            method === 'DELETE' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {method}
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              // Show categorized list when not searching
              <div className="max-h-[600px] overflow-y-auto">
                {Object.entries(categorizedEndpoints).map(([category, categoryEndpoints]) =>
                  categoryEndpoints.length > 0 ? (
                    <div key={category} className="mb-2">
                      <div className="p-2 bg-gray-100 font-medium text-gray-700">
                        {category} ({categoryEndpoints.length})
                      </div>
                      <div className="divide-y divide-gray-200">
                        {categoryEndpoints.map((endpoint, index) => (
                          <div
                            key={`${category}-${index}`}
                            className={`p-4 cursor-pointer hover:bg-gray-50 ${selectedEndpoint?.path === endpoint.path ? 'bg-purple-50 border-l-4 border-purple-600' : ''}`}
                            onClick={() => handleEndpointClick(endpoint)}
                          >
                            <div className="font-medium text-gray-800">{endpoint.path}</div>
                            <div className="text-sm text-gray-500 mt-1">{endpoint.description}</div>
                            <div className="flex mt-2 space-x-2">
                              {endpoint.methods.map(method => (
                                <span
                                  key={method}
                                  className={`px-2 py-1 text-xs rounded-full ${
                                    method === 'GET' ? 'bg-blue-100 text-blue-800' :
                                    method === 'POST' ? 'bg-green-100 text-green-800' :
                                    method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                                    method === 'DELETE' ? 'bg-red-100 text-red-800' :
                                    'bg-gray-100 text-gray-800'
                                  }`}
                                >
                                  {method}
                                </span>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : null
                )}
              </div>
            )}
          </div>

          {/* Endpoint Details */}
          <div className="lg:col-span-2 bg-white rounded-lg shadow">
            {selectedEndpoint ? (
              <div>
                <div className="p-4 bg-purple-600 text-white font-semibold flex justify-between items-center">
                  <span>Endpoint Details</span>
                  <button
                    className="bg-white text-purple-600 px-3 py-1 rounded text-sm hover:bg-purple-100"
                    onClick={() => tryInApiViewer(selectedEndpoint)}
                  >
                    Try in API Viewer
                  </button>
                </div>

                <div className="p-6">
                  <div className="mb-6">
                    <h2 className="text-xl font-semibold mb-2">{selectedEndpoint.path}</h2>
                    <p className="text-gray-600">{selectedEndpoint.description}</p>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium mb-2">Methods</h3>
                    <div className="flex space-x-2">
                      {selectedEndpoint.methods.map(method => (
                        <span
                          key={method}
                          className={`px-3 py-1 rounded-full ${
                            method === 'GET' ? 'bg-blue-100 text-blue-800' :
                            method === 'POST' ? 'bg-green-100 text-green-800' :
                            method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                            method === 'DELETE' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {method}
                        </span>
                      ))}
                    </div>
                  </div>

                  {selectedEndpoint.parameters && selectedEndpoint.parameters.length > 0 && (
                    <div className="mb-6">
                      <h3 className="text-lg font-medium mb-2">Parameters</h3>
                      <div className="bg-gray-50 rounded overflow-hidden">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-100">
                            <tr>
                              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Name</th>
                              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">In</th>
                              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Required</th>
                              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-gray-200">
                            {selectedEndpoint.parameters.map((param, index) => (
                              <tr key={index}>
                                <td className="px-4 py-2 text-sm">{param.name}</td>
                                <td className="px-4 py-2 text-sm">{param.in}</td>
                                <td className="px-4 py-2 text-sm">{param.type}</td>
                                <td className="px-4 py-2 text-sm">
                                  {param.required ? (
                                    <span className="text-red-600">Yes</span>
                                  ) : (
                                    <span className="text-gray-500">No</span>
                                  )}
                                </td>
                                <td className="px-4 py-2 text-sm">{param.description}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                  {selectedEndpoint.responses && (
                    <div>
                      <h3 className="text-lg font-medium mb-2">Responses</h3>
                      <div className="space-y-4">
                        {Object.entries(selectedEndpoint.responses).map(([status, response]) => (
                          <div key={status} className="bg-gray-50 p-4 rounded">
                            <div className="flex items-center mb-2">
                              <span className={`px-2 py-1 rounded text-xs font-medium ${
                                status.startsWith('2') ? 'bg-green-100 text-green-800' :
                                status.startsWith('4') ? 'bg-red-100 text-red-800' :
                                status.startsWith('5') ? 'bg-orange-100 text-orange-800' :
                                'bg-blue-100 text-blue-800'
                              }`}>
                                {status}
                              </span>
                              <span className="ml-2 text-sm text-gray-600">{response.description}</span>
                            </div>

                            {response.schema && (
                              <div className="mt-2">
                                <div className="text-sm font-medium mb-1">Schema:</div>
                                <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-40">
                                  {JSON.stringify(response.schema, null, 2)}
                                </pre>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="p-6 text-center text-gray-500">
                <p>Select an endpoint to view details</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </EnhancedAdminLayout>
  );
}

