/**
 * Enhanced Error Monitoring Integration
 * Works with your existing Sentry setup and adds additional monitoring capabilities
 */

// Enhanced error monitoring that integrates with your existing Sentry setup
export class EnhancedErrorMonitor {
  constructor() {
    this.errorQueue = [];
    this.maxQueueSize = 100;
    this.isInitialized = false;
  }

  // Initialize with your existing Sentry configuration
  initialize() {
    if (this.isInitialized) return;
    
    // Check if Sentry is already initialized (your existing setup)
    if (typeof window !== 'undefined' && window.Sentry) {
      console.log('✅ Enhanced Error Monitor: Using existing Sentry setup');
      this.isInitialized = true;
      return;
    }

    // Fallback error handling if Sentry is not available
    this.setupFallbackErrorHandling();
    this.isInitialized = true;
  }

  setupFallbackErrorHandling() {
    if (typeof window === 'undefined') return;

    // Global error handler
    window.addEventListener('error', (event) => {
      this.logError({
        type: 'JavaScript Error',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
        timestamp: new Date().toISOString()
      });
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      this.logError({
        type: 'Unhandled Promise Rejection',
        message: event.reason?.message || 'Unhandled promise rejection',
        reason: event.reason,
        timestamp: new Date().toISOString()
      });
    });
  }

  logError(errorData) {
    // Add to local queue
    this.errorQueue.unshift(errorData);
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.pop();
    }

    // Send to your existing Sentry if available
    if (typeof window !== 'undefined' && window.Sentry) {
      window.Sentry.captureException(new Error(errorData.message), {
        extra: errorData
      });
    }

    // Console log for development
    if (process.env.NODE_ENV === 'development') {
      console.error('🚨 Enhanced Error Monitor:', errorData);
    }

    // Send to your backend analytics endpoint
    this.sendToAnalytics(errorData);
  }

  async sendToAnalytics(errorData) {
    try {
      await fetch('/api/admin/error-analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(errorData)
      });
    } catch (error) {
      // Silently fail to avoid infinite error loops
      console.warn('Failed to send error analytics:', error);
    }
  }

  getErrorSummary() {
    const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recentErrors = this.errorQueue.filter(
      error => new Date(error.timestamp) > last24Hours
    );

    const errorsByType = recentErrors.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1;
      return acc;
    }, {});

    return {
      totalErrors: this.errorQueue.length,
      recentErrors: recentErrors.length,
      errorsByType,
      latestErrors: this.errorQueue.slice(0, 10)
    };
  }

  clearErrors() {
    this.errorQueue = [];
  }
}

// Performance monitoring utilities
export class PerformanceMonitor {
  static measurePageLoad() {
    if (typeof window === 'undefined' || !window.performance) return null;

    const navigation = performance.getEntriesByType('navigation')[0];
    if (!navigation) return null;

    return {
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.navigationStart,
      loadComplete: navigation.loadEventEnd - navigation.navigationStart,
      firstByte: navigation.responseStart - navigation.requestStart,
      domInteractive: navigation.domInteractive - navigation.navigationStart
    };
  }

  static measureLCP() {
    if (typeof window === 'undefined' || !window.PerformanceObserver) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      
      // Send LCP data to analytics
      fetch('/api/admin/performance-metrics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          metric: 'LCP',
          value: lastEntry.startTime,
          timestamp: new Date().toISOString()
        })
      }).catch(() => {}); // Silently fail
    });

    observer.observe({ entryTypes: ['largest-contentful-paint'] });
  }

  static measureFID() {
    if (typeof window === 'undefined' || !window.PerformanceObserver) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        // Send FID data to analytics
        fetch('/api/admin/performance-metrics', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            metric: 'FID',
            value: entry.processingStart - entry.startTime,
            timestamp: new Date().toISOString()
          })
        }).catch(() => {}); // Silently fail
      });
    });

    observer.observe({ entryTypes: ['first-input'] });
  }
}

// User analytics utilities
export class UserAnalytics {
  static trackPageView(page) {
    // Send to your analytics endpoint
    fetch('/api/admin/user-analytics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        event: 'page_view',
        page,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        referrer: document.referrer
      })
    }).catch(() => {}); // Silently fail
  }

  static trackUserAction(action, data = {}) {
    fetch('/api/admin/user-analytics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        event: 'user_action',
        action,
        data,
        timestamp: new Date().toISOString()
      })
    }).catch(() => {}); // Silently fail
  }

  static trackConversion(type, value = 0) {
    fetch('/api/admin/user-analytics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        event: 'conversion',
        type,
        value,
        timestamp: new Date().toISOString()
      })
    }).catch(() => {}); // Silently fail
  }
}

// Global instance
export const errorMonitor = new EnhancedErrorMonitor();

// Initialize on client side
if (typeof window !== 'undefined') {
  errorMonitor.initialize();
  PerformanceMonitor.measureLCP();
  PerformanceMonitor.measureFID();
}

// React hook for error monitoring
export const useErrorMonitoring = () => {
  const logError = (error, context = {}) => {
    errorMonitor.logError({
      type: 'React Error',
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString()
    });
  };

  const getErrorSummary = () => errorMonitor.getErrorSummary();

  return { logError, getErrorSummary };
};

// React hook for performance monitoring
export const usePerformanceMonitoring = () => {
  const measurePageLoad = () => PerformanceMonitor.measurePageLoad();
  
  const trackPageView = (page) => UserAnalytics.trackPageView(page);
  
  const trackUserAction = (action, data) => UserAnalytics.trackUserAction(action, data);
  
  const trackConversion = (type, value) => UserAnalytics.trackConversion(type, value);

  return {
    measurePageLoad,
    trackPageView,
    trackUserAction,
    trackConversion
  };
};

export default {
  EnhancedErrorMonitor,
  PerformanceMonitor,
  UserAnalytics,
  errorMonitor,
  useErrorMonitoring,
  usePerformanceMonitoring
};
