// test-verification-mock.js
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const prisma = new PrismaClient();

async function testVerificationFlow() {
  try {
    console.log('=== VERIFICATION FLOW TEST (MOCK) ===');

    // Step 1: Create or get test user
    console.log('\n1. Creating/getting test user...');
    const testUser = await getOrCreateTestUser();
    console.log(`Test user ID: ${testUser.id}`);
    console.log(`Initial verification status: ${testUser.isVerified ? 'Verified' : 'Not Verified'}`);
    console.log(`Initial profile status: ${testUser.profileStatus}`);

    // Step 2: Create verification document
    console.log('\n2. Creating verification document...');
    const document = await createVerificationDocument(testUser.id);
    console.log(`Document created with ID: ${document.id}`);
    console.log(`Document type: ${document.documentType}`);
    console.log(`Document status: ${document.status}`);

    // Step 3: Check user status after document upload
    console.log('\n3. Checking user status after document upload...');
    const updatedUser = await prisma.user.findUnique({
      where: { id: testUser.id }
    });
    console.log(`Updated verification status: ${updatedUser.isVerified ? 'Verified' : 'Not Verified'}`);
    console.log(`Updated profile status: ${updatedUser.profileStatus}`);

    // Step 4: Approve the document (admin action)
    console.log('\n4. Approving the document (admin action)...');
    await approveDocument(document.id);

    // Step 5: Check user status after approval
    console.log('\n5. Checking user status after approval...');
    const finalUser = await prisma.user.findUnique({
      where: { id: testUser.id }
    });
    console.log(`Final verification status: ${finalUser.isVerified ? 'Verified' : 'Not Verified'}`);
    console.log(`Final profile status: ${finalUser.profileStatus}`);

    console.log('\n=== TEST COMPLETED SUCCESSFULLY ===');
  } catch (error) {
    console.error('\n=== TEST FAILED ===');
    console.error(error);
  } finally {
    await prisma.$disconnect();
  }
}

// Create or get test user
async function getOrCreateTestUser() {
  // Check if test user already exists
  let testUser = await prisma.user.findUnique({
    where: { phone: '9999999999' },
  });

  if (!testUser) {
    // Create a new test user
    testUser = await prisma.user.create({
      data: {
        phone: '9999999999',
        email: '<EMAIL>',
        isVerified: false,
        profileStatus: 'INCOMPLETE',
        profile: {
          create: {
            fullName: 'Test User',
            gender: 'MALE',
            dateOfBirth: new Date('1990-01-01'),
            city: 'Mumbai'
          }
        }
      },
    });
  } else {
    // Reset verification status for testing
    testUser = await prisma.user.update({
      where: { id: testUser.id },
      data: {
        isVerified: false,
        profileStatus: 'INCOMPLETE'
      }
    });
  }

  return testUser;
}

// Create verification document
async function createVerificationDocument(userId) {
  // Create a mock document path
  const mockFilename = 'mock-document.jpg';
  const mockUrl = 'uploads/verification/' + mockFilename;

  // Create the document in the database
  const document = await prisma.verificationDocument.create({
    data: {
      userId,
      type: 'AADHAR_CARD',
      url: mockUrl,
      filename: mockFilename,
      filesize: 12345, // Mock file size in bytes
      mimeType: 'image/jpeg',
      status: 'PENDING_REVIEW'
    }
  });

  // Update user status to PENDING_APPROVAL
  await prisma.user.update({
    where: { id: userId },
    data: {
      profileStatus: 'PENDING_APPROVAL'
    }
  });

  return document;
}

// Approve document (admin action)
async function approveDocument(documentId) {
  // Update document status
  const document = await prisma.verificationDocument.update({
    where: { id: documentId },
    data: {
      status: 'APPROVED',
      reviewedAt: new Date(),
      reviewedBy: 'Admin'
    }
  });

  // Update user verification status
  await prisma.user.update({
    where: { id: document.userId },
    data: {
      isVerified: true,
      profileStatus: 'ACTIVE'
    }
  });

  return document;
}

// Run the test
testVerificationFlow().catch(console.error);
