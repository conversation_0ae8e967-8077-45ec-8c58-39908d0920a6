import { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  FormLabel,
  Select,
  MenuItem,
  InputLabel,
  FormHelperText,
  Divider,
  Alert,
  LinearProgress,
  Paper,
  RadioGroup,
  Radio,
  FormControlLabel,
  Snackbar,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon,
  Help as HelpIcon,
  LocalDining as DiningIcon,
  SportsBasketball as SportsIcon,
  Brush as ArtIcon,
  MusicNote as MusicIcon,
  MenuBook as ReadingIcon,
  Hiking as OutdoorIcon,
  Computer as TechIcon
} from '@mui/icons-material';
import { useRouter } from 'next/router';
import FilterChips from '@/website/components/common/FilterChips';

// Constants for form options
const DIET_OPTIONS = [
  { value: 'VEGETARIAN', label: 'Vegetarian' },
  { value: 'NON_VEGETARIAN', label: 'Non-Vegetarian' },
  { value: 'EGGETARIAN', label: 'Eggetarian' },
  { value: 'VEGAN', label: 'Vegan' },
  { value: 'JAIN', label: 'Jain' }
];

const SMOKING_OPTIONS = [
  { value: 'NO', label: 'No' },
  { value: 'OCCASIONALLY', label: 'Occasionally' },
  { value: 'YES', label: 'Yes' }
];

const DRINKING_OPTIONS = [
  { value: 'NO', label: 'No' },
  { value: 'OCCASIONALLY', label: 'Occasionally' },
  { value: 'YES', label: 'Yes' }
];

// Hobby options with categories
const HOBBY_OPTIONS = [
  // Sports & Fitness
  'Sports', 'Fitness', 'Yoga', 'Gym', 'Running', 'Cricket', 'Football',
  'Swimming', 'Cycling', 'Badminton', 'Tennis', 'Hiking', 'Trekking',

  // Arts & Creativity
  'Painting', 'Drawing', 'Sketching', 'Photography', 'Crafts', 'Pottery',
  'Dancing', 'Singing', 'Playing Instruments', 'Writing', 'Poetry',

  // Entertainment
  'Movies', 'Theatre', 'Music', 'Concerts', 'TV Shows', 'Gaming',
  'Board Games', 'Chess', 'Carrom', 'Card Games',

  // Knowledge & Learning
  'Reading', 'Learning Languages', 'History', 'Science', 'Technology',
  'Coding', 'Blogging', 'Podcasts', 'Documentaries',

  // Lifestyle
  'Cooking', 'Baking', 'Gardening', 'Interior Design', 'Fashion',
  'Shopping', 'Traveling', 'Exploring New Places', 'Food Tasting',

  // Spiritual & Wellness
  'Meditation', 'Spirituality', 'Temple Visits', 'Religious Activities',
  'Volunteering', 'Social Work', 'Charity'
];

// Interest categories for coloring
const INTEREST_CATEGORIES = {
  'Sports': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'Fitness': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'Yoga': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'Cricket': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'Swimming': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'Hiking': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'Trekking': { bgcolor: '#e3f2fd', color: '#0d47a1' },

  'Painting': { bgcolor: '#f3e5f5', color: '#6a1b9a' },
  'Drawing': { bgcolor: '#f3e5f5', color: '#6a1b9a' },
  'Photography': { bgcolor: '#f3e5f5', color: '#6a1b9a' },
  'Dancing': { bgcolor: '#f3e5f5', color: '#6a1b9a' },
  'Singing': { bgcolor: '#f3e5f5', color: '#6a1b9a' },
  'Writing': { bgcolor: '#f3e5f5', color: '#6a1b9a' },

  'Movies': { bgcolor: '#fff3e0', color: '#e65100' },
  'Theatre': { bgcolor: '#fff3e0', color: '#e65100' },
  'Music': { bgcolor: '#fff3e0', color: '#e65100' },
  'Gaming': { bgcolor: '#fff3e0', color: '#e65100' },
  'Chess': { bgcolor: '#fff3e0', color: '#e65100' },

  'Reading': { bgcolor: '#e8f5e9', color: '#1b5e20' },
  'Learning': { bgcolor: '#e8f5e9', color: '#1b5e20' },
  'History': { bgcolor: '#e8f5e9', color: '#1b5e20' },
  'Science': { bgcolor: '#e8f5e9', color: '#1b5e20' },
  'Technology': { bgcolor: '#e8f5e9', color: '#1b5e20' },
  'Coding': { bgcolor: '#e8f5e9', color: '#1b5e20' },

  'Cooking': { bgcolor: '#fbe9e7', color: '#bf360c' },
  'Baking': { bgcolor: '#fbe9e7', color: '#bf360c' },
  'Gardening': { bgcolor: '#fbe9e7', color: '#bf360c' },
  'Traveling': { bgcolor: '#fbe9e7', color: '#bf360c' },
  'Food': { bgcolor: '#fbe9e7', color: '#bf360c' },

  'Meditation': { bgcolor: '#e0f7fa', color: '#006064' },
  'Spirituality': { bgcolor: '#e0f7fa', color: '#006064' },
  'Temple': { bgcolor: '#e0f7fa', color: '#006064' },
  'Religious': { bgcolor: '#e0f7fa', color: '#006064' },
  'Volunteering': { bgcolor: '#e0f7fa', color: '#006064' }
};

const LifestyleForm = ({ userData, onSave, isLoading }) => {
  const router = useRouter();
  const [formData, setFormData] = useState({
    // Diet
    diet: '',

    // Habits
    smoking: 'NO',
    drinking: 'NO',

    // Hobbies & Interests
    hobbies: [],
    interests: ''
  });

  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [completionPercentage, setCompletionPercentage] = useState(0);
  const [success, setSuccess] = useState('');

  // Initialize form with user data if available
  useEffect(() => {
    if (userData) {
      setFormData({
        ...formData,
        diet: userData.diet || '',
        smoking: userData.smoking || 'NO',
        drinking: userData.drinking || 'NO',
        hobbies: userData.hobbies || [],
        interests: userData.interests || ''
      });
    }
  }, [userData]);

  // Calculate completion percentage
  useEffect(() => {
    const requiredFields = ['diet'];
    const optionalFields = ['smoking', 'drinking', 'hobbies', 'interests'];

    const totalFields = requiredFields.length + optionalFields.length;
    let completedFields = 0;

    // Count required fields
    requiredFields.forEach(field => {
      if (formData[field]) completedFields++;
    });

    // Count optional fields
    optionalFields.forEach(field => {
      if (Array.isArray(formData[field])) {
        if (formData[field].length > 0) completedFields++;
      } else if (formData[field] !== undefined && formData[field] !== '') {
        completedFields++;
      }
    });

    const percentage = Math.round((completedFields / totalFields) * 100);
    setCompletionPercentage(percentage);
  }, [formData]);

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Mark field as touched
    setTouched({ ...touched, [name]: true });

    // Clear error for this field if it exists
    if (errors[name]) {
      const { [name]: _, ...restErrors } = errors;
      setErrors(restErrors);
    }
  };

  // Handle array input change
  const handleArrayChange = (name, value) => {
    setFormData({ ...formData, [name]: value });
    setTouched({ ...touched, [name]: true });

    if (errors[name]) {
      const { [name]: _, ...restErrors } = errors;
      setErrors(restErrors);
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    // Required fields
    if (!formData.diet) {
      newErrors.diet = 'Diet preference is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Call the onSave function with the form data
    onSave(formData);
    setSuccess('Lifestyle details saved successfully!');
  };

  // Handle back button
  const handleBack = () => {
    router.push('/website/pages/profile');
  };

  return (
    <Box sx={{ mb: 4 }}>
      <Paper elevation={0} sx={{ p: 2, mb: 3, bgcolor: 'primary.light', color: 'primary.contrastText', borderRadius: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <DiningIcon sx={{ mr: 1 }} />
          <Typography variant="h5" component="h1">
            Lifestyle & Habits
          </Typography>
        </Box>
        <Typography variant="body2" sx={{ mt: 1 }}>
          Share your lifestyle preferences to find compatible partners.
        </Typography>
        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
          <Box sx={{ flexGrow: 1, mr: 2 }}>
            <LinearProgress
              variant="determinate"
              value={completionPercentage}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: 'rgba(255,255,255,0.3)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: 'white'
                }
              }}
            />
          </Box>
          <Typography variant="body2" fontWeight="bold">
            {completionPercentage}% Complete
          </Typography>
        </Box>
      </Paper>

      <form onSubmit={handleSubmit}>
        <Card elevation={3} sx={{ mb: 3, borderRadius: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              Diet & Habits
              <Tooltip title="Information about your diet and lifestyle habits">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth error={!!errors.diet}>
                  <FormLabel id="diet-label">Diet Preference*</FormLabel>
                  <Select
                    labelId="diet-label"
                    id="diet"
                    name="diet"
                    value={formData.diet}
                    onChange={handleInputChange}
                    size="small"
                  >
                    <MenuItem value="">Select Diet Preference</MenuItem>
                    {DIET_OPTIONS.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.diet && <FormHelperText error>{errors.diet}</FormHelperText>}
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <FormLabel id="smoking-label">Smoking</FormLabel>
                  <RadioGroup
                    aria-labelledby="smoking-label"
                    name="smoking"
                    value={formData.smoking}
                    onChange={handleInputChange}
                    row
                  >
                    {SMOKING_OPTIONS.map(option => (
                      <FormControlLabel
                        key={option.value}
                        value={option.value}
                        control={<Radio />}
                        label={option.label}
                      />
                    ))}
                  </RadioGroup>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <FormLabel id="drinking-label">Drinking</FormLabel>
                  <RadioGroup
                    aria-labelledby="drinking-label"
                    name="drinking"
                    value={formData.drinking}
                    onChange={handleInputChange}
                    row
                  >
                    {DRINKING_OPTIONS.map(option => (
                      <FormControlLabel
                        key={option.value}
                        value={option.value}
                        control={<Radio />}
                        label={option.label}
                      />
                    ))}
                  </RadioGroup>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Card elevation={3} sx={{ mb: 3, borderRadius: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              Hobbies & Interests
              <Tooltip title="Share your hobbies and interests">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FilterChips
                  label="Hobbies"
                  helperText="Select hobbies that you enjoy in your free time"
                  options={HOBBY_OPTIONS}
                  selectedOptions={formData.hobbies}
                  onChange={(newValue) => handleArrayChange('hobbies', newValue)}
                  allowCustom={true}
                  customPlaceholder="Add a hobby not in the list..."
                  maxSelections={10}
                  categoryColors={INTEREST_CATEGORIES}
                  showSelectedCount={true}
                />
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                    Interest Categories
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 3 }}>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 1,
                        display: 'flex',
                        alignItems: 'center',
                        bgcolor: '#e3f2fd',
                        color: '#0d47a1',
                        borderRadius: 2
                      }}
                    >
                      <SportsIcon sx={{ mr: 1 }} />
                      <Typography variant="body2">Sports & Fitness</Typography>
                    </Paper>

                    <Paper
                      elevation={0}
                      sx={{
                        p: 1,
                        display: 'flex',
                        alignItems: 'center',
                        bgcolor: '#f3e5f5',
                        color: '#6a1b9a',
                        borderRadius: 2
                      }}
                    >
                      <ArtIcon sx={{ mr: 1 }} />
                      <Typography variant="body2">Arts & Creativity</Typography>
                    </Paper>

                    <Paper
                      elevation={0}
                      sx={{
                        p: 1,
                        display: 'flex',
                        alignItems: 'center',
                        bgcolor: '#fff3e0',
                        color: '#e65100',
                        borderRadius: 2
                      }}
                    >
                      <MusicIcon sx={{ mr: 1 }} />
                      <Typography variant="body2">Entertainment</Typography>
                    </Paper>

                    <Paper
                      elevation={0}
                      sx={{
                        p: 1,
                        display: 'flex',
                        alignItems: 'center',
                        bgcolor: '#e8f5e9',
                        color: '#1b5e20',
                        borderRadius: 2
                      }}
                    >
                      <ReadingIcon sx={{ mr: 1 }} />
                      <Typography variant="body2">Knowledge & Learning</Typography>
                    </Paper>

                    <Paper
                      elevation={0}
                      sx={{
                        p: 1,
                        display: 'flex',
                        alignItems: 'center',
                        bgcolor: '#fbe9e7',
                        color: '#bf360c',
                        borderRadius: 2
                      }}
                    >
                      <OutdoorIcon sx={{ mr: 1 }} />
                      <Typography variant="body2">Lifestyle</Typography>
                    </Paper>

                    <Paper
                      elevation={0}
                      sx={{
                        p: 1,
                        display: 'flex',
                        alignItems: 'center',
                        bgcolor: '#e0f7fa',
                        color: '#006064',
                        borderRadius: 2
                      }}
                    >
                      <TechIcon sx={{ mr: 1 }} />
                      <Typography variant="body2">Spiritual & Wellness</Typography>
                    </Paper>
                  </Box>
                </Box>
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth>
                  <FormLabel htmlFor="interests">Other Interests</FormLabel>
                  <TextField
                    id="interests"
                    name="interests"
                    value={formData.interests}
                    onChange={handleInputChange}
                    placeholder="Describe your other interests and passions"
                    multiline
                    rows={3}
                    size="small"
                  />
                  <FormHelperText>
                    Share any other interests or passions not covered by the hobbies above
                  </FormHelperText>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={handleBack}
          >
            Back to Profile
          </Button>

          <Button
            type="submit"
            variant="contained"
            color="primary"
            startIcon={isLoading ? null : <SaveIcon />}
            disabled={isLoading}
          >
            {isLoading ? 'Saving...' : 'Save Lifestyle Details'}
          </Button>
        </Box>
      </form>

      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess('')}
        message={success}
      />
    </Box>
  );
};

export default LifestyleForm;
