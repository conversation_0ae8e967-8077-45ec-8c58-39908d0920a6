/**
 * Privacy Settings Widget for Professional Dashboard
 * Integrates with existing privacy control structure
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Switch,
  FormControl,
  FormControlLabel,
  Select,
  MenuItem,
  Chip,
  Button,
  Alert,
  Tabs,
  Tab,
  Divider,
  styled,
  CircularProgress
} from '@mui/material';

// Icons
import {
  Security as SecurityIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Person as PersonIcon,
  PhotoLibrary as PhotoIcon,
  ContactPhone as ContactIcon,
  AccessTime as ActivityIcon,
  Badge as BadgeIcon,
  Shield as ShieldIcon,
  Lock as LockIcon,
  Public as PublicIcon
} from '@mui/icons-material';

// Import existing privacy constants
import { DISPLAY_NAME_OPTIONS, DEFAULT_PRIVACY_SETTINGS } from '@/config';
import { PRIVACY_OPTIONS } from '@/website/profile/constants';

// Styled components
const PrivacyCard = styled(Card)(({ theme }) => ({
  borderRadius: 16,
  boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
  border: '1px solid rgba(255,255,255,0.1)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 16px 48px rgba(0,0,0,0.12)'
  }
}));

const PrivacyLevel = styled(Chip)(({ level }) => ({
  fontWeight: 600,
  background: level === 'Low' ? 'linear-gradient(135deg, #F44336, #FF5722)' :
             level === 'Medium' ? 'linear-gradient(135deg, #FF9800, #FFC107)' :
             level === 'High' ? 'linear-gradient(135deg, #4CAF50, #8BC34A)' :
             'linear-gradient(135deg, #9C27B0, #E91E63)',
  color: 'white'
}));

export default function PrivacySettingsWidget({ userId }) {
  const [activeTab, setActiveTab] = useState(0);
  const [settings, setSettings] = useState(DEFAULT_PRIVACY_SETTINGS);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState(null);

  useEffect(() => {
    fetchPrivacySettings();
  }, [userId]);

  const fetchPrivacySettings = async () => {
    setLoading(true);
    try {
      // This would call your existing privacy API
      const response = await fetch('/api/privacy/settings', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('userToken')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setSettings({ ...DEFAULT_PRIVACY_SETTINGS, ...data.privacySettings });
      }
    } catch (error) {
      console.error('Error fetching privacy settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    setSaving(true);
    try {
      const response = await fetch('/api/privacy/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('userToken')}`
        },
        body: JSON.stringify(settings)
      });

      if (response.ok) {
        setMessage({ type: 'success', text: 'Privacy settings updated successfully!' });
      } else {
        setMessage({ type: 'error', text: 'Failed to update privacy settings.' });
      }
    } catch (error) {
      console.error('Error saving privacy settings:', error);
      setMessage({ type: 'error', text: 'An error occurred while saving settings.' });
    } finally {
      setSaving(false);
      setTimeout(() => setMessage(null), 3000);
    }
  };

  const handleSettingChange = (field, value) => {
    setSettings(prev => ({ ...prev, [field]: value }));
  };

  const renderDisplayNameSettings = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom>
          Display Name Preference
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Choose how your name appears to other users across the platform
        </Typography>
      </Grid>

      {Object.values(DISPLAY_NAME_OPTIONS).map((option) => (
        <Grid item xs={12} md={6} key={option.value}>
          <Card
            sx={{
              p: 2,
              cursor: 'pointer',
              border: settings.displayNamePreference === option.value ? '2px solid #667eea' : '1px solid #e0e0e0',
              '&:hover': { borderColor: '#667eea' }
            }}
            onClick={() => handleSettingChange('displayNamePreference', option.value)}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Typography variant="h4" sx={{ mr: 2 }}>{option.icon}</Typography>
              <Box sx={{ flexGrow: 1 }}>
                <Typography variant="subtitle1" fontWeight="600">
                  {option.label}
                  {option.recommended && (
                    <Chip label="Recommended" size="small" sx={{ ml: 1, backgroundColor: '#4CAF50', color: 'white' }} />
                  )}
                </Typography>
                <PrivacyLevel label={`${option.privacy} Privacy`} size="small" level={option.privacy} />
              </Box>
            </Box>
            <Typography variant="body2" color="text.secondary">
              {option.description}
            </Typography>
          </Card>
        </Grid>
      ))}

      <Grid item xs={12}>
        <Divider sx={{ my: 2 }} />
        <Typography variant="subtitle1" fontWeight="600" gutterBottom>
          Name Display Controls
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={settings.showNameInSearch}
                  onChange={(e) => handleSettingChange('showNameInSearch', e.target.checked)}
                />
              }
              label="Show name in search results"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={settings.showNameInMatches}
                  onChange={(e) => handleSettingChange('showNameInMatches', e.target.checked)}
                />
              }
              label="Show name in matches"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={settings.showNameInMessages}
                  onChange={(e) => handleSettingChange('showNameInMessages', e.target.checked)}
                />
              }
              label="Show name in messages"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={settings.showNameInNotifications}
                  onChange={(e) => handleSettingChange('showNameInNotifications', e.target.checked)}
                />
              }
              label="Show name in notifications"
            />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );

  const renderContentPrivacy = () => (
    <Grid container spacing={3}>
      {[
        { key: 'photoPrivacy', label: 'Photos', icon: <PhotoIcon /> },
        { key: 'phonePrivacy', label: 'Phone Number', icon: <ContactIcon /> },
        { key: 'emailPrivacy', label: 'Email Address', icon: <ContactIcon /> },
        { key: 'educationPrivacy', label: 'Education Details', icon: <PersonIcon /> },
        { key: 'careerPrivacy', label: 'Career Information', icon: <PersonIcon /> },
        { key: 'familyPrivacy', label: 'Family Details', icon: <PersonIcon /> }
      ].map((item) => (
        <Grid item xs={12} md={6} key={item.key}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            {item.icon}
            <Typography variant="subtitle1" fontWeight="600" sx={{ ml: 1 }}>
              {item.label}
            </Typography>
          </Box>
          <FormControl fullWidth>
            <Select
              value={settings[item.key]}
              onChange={(e) => handleSettingChange(item.key, e.target.value)}
              size="small"
            >
              <MenuItem value="ALL_USERS">
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <PublicIcon sx={{ mr: 1, fontSize: 16 }} />
                  Visible to all users
                </Box>
              </MenuItem>
              <MenuItem value="PREMIUM_USERS">
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <ShieldIcon sx={{ mr: 1, fontSize: 16 }} />
                  Premium users only
                </Box>
              </MenuItem>
              <MenuItem value="ACCEPTED_INTEREST">
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <LockIcon sx={{ mr: 1, fontSize: 16 }} />
                  After interest accepted
                </Box>
              </MenuItem>
              <MenuItem value="HIDDEN">
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <VisibilityOffIcon sx={{ mr: 1, fontSize: 16 }} />
                  Hidden from all
                </Box>
              </MenuItem>
            </Select>
          </FormControl>
        </Grid>
      ))}
    </Grid>
  );

  const renderActivitySettings = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom>
          Activity & Online Status
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Control who can see your online status and activity information
        </Typography>
      </Grid>

      <Grid item xs={12} sm={6}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.showOnlineStatus}
              onChange={(e) => handleSettingChange('showOnlineStatus', e.target.checked)}
            />
          }
          label="Show online status"
        />
        <Typography variant="body2" color="text.secondary">
          Let others see when you're online
        </Typography>
      </Grid>

      <Grid item xs={12} sm={6}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.showLastSeen}
              onChange={(e) => handleSettingChange('showLastSeen', e.target.checked)}
            />
          }
          label="Show last seen"
        />
        <Typography variant="body2" color="text.secondary">
          Show when you were last active
        </Typography>
      </Grid>

      <Grid item xs={12} sm={6}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.allowProfileViews}
              onChange={(e) => handleSettingChange('allowProfileViews', e.target.checked)}
            />
          }
          label="Allow profile views"
        />
        <Typography variant="body2" color="text.secondary">
          Let others view your profile
        </Typography>
      </Grid>

      <Grid item xs={12} sm={6}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.allowDirectMessages}
              onChange={(e) => handleSettingChange('allowDirectMessages', e.target.checked)}
            />
          }
          label="Allow direct messages"
        />
        <Typography variant="body2" color="text.secondary">
          Receive messages from other users
        </Typography>
      </Grid>
    </Grid>
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
        <SecurityIcon sx={{ fontSize: 32, color: '#667eea', mr: 2 }} />
        <Typography variant="h5" fontWeight="700">
          Privacy & Security Settings
        </Typography>
      </Box>

      {/* Message Alert */}
      {message && (
        <Alert severity={message.type} sx={{ mb: 3 }}>
          {message.text}
        </Alert>
      )}

      {/* Privacy Overview */}
      <PrivacyCard sx={{ mb: 4 }}>
        <CardContent sx={{ p: 4 }}>
          <Typography variant="h6" fontWeight="600" gutterBottom>
            Your Privacy Level
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <PrivacyLevel 
              label={DISPLAY_NAME_OPTIONS[settings.displayNamePreference]?.privacy || 'Medium'} 
              level={DISPLAY_NAME_OPTIONS[settings.displayNamePreference]?.privacy || 'Medium'} 
            />
            <Typography variant="body2" color="text.secondary">
              Based on your current display name preference
            </Typography>
          </Box>
          <Typography variant="body2" color="text.secondary">
            {DISPLAY_NAME_OPTIONS[settings.displayNamePreference]?.description}
          </Typography>
        </CardContent>
      </PrivacyCard>

      {/* Privacy Settings Tabs */}
      <PrivacyCard>
        <CardContent sx={{ p: 4 }}>
          <Tabs
            value={activeTab}
            onChange={(e, newValue) => setActiveTab(newValue)}
            sx={{ mb: 4 }}
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab icon={<BadgeIcon />} label="Display Name" />
            <Tab icon={<VisibilityIcon />} label="Content Privacy" />
            <Tab icon={<ActivityIcon />} label="Activity & Status" />
          </Tabs>

          {/* Tab Content */}
          {activeTab === 0 && renderDisplayNameSettings()}
          {activeTab === 1 && renderContentPrivacy()}
          {activeTab === 2 && renderActivitySettings()}

          {/* Save Button */}
          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <Button
              variant="contained"
              size="large"
              onClick={handleSaveSettings}
              disabled={saving}
              sx={{
                background: 'linear-gradient(135deg, #667eea, #764ba2)',
                px: 6,
                py: 1.5
              }}
            >
              {saving ? <CircularProgress size={20} color="inherit" /> : 'Save Privacy Settings'}
            </Button>
          </Box>
        </CardContent>
      </PrivacyCard>
    </Box>
  );
}
