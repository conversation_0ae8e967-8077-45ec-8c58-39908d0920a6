{"name": "vaivahik-express", "version": "1.0.0", "description": "Vaivahik Matrimony API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "docs": "node scripts/generate-docs.js", "enhance-api": "node scripts/enhance-api.js", "update-routes": "node scripts/update-routes.js", "optimize-caching": "node scripts/optimize-caching.js", "setup-monitoring": "node scripts/setup-performance-monitoring.js", "enhance-docs": "node scripts/enhance-documentation.js", "generate-tests": "node scripts/generate-tests.js"}, "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "express-validator": "^7.0.1", "glob": "^10.3.3", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "prisma": "^4.14.0", "redis": "^4.6.7", "socket.io": "^4.6.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^4.6.3", "winston": "^3.9.0"}, "devDependencies": {"eslint": "^8.40.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "nodemon": "^2.0.22", "prettier": "^2.8.8", "supertest": "^6.3.3"}, "engines": {"node": ">=14.0.0"}}