# Vaivahik Admin API Documentation

## Overview

This document provides detailed information about the Vaivahik Admin API endpoints, request/response formats, and authentication requirements.

## Base URL

```
https://api.vaivahik.com/v1
```

## Authentication

All admin API endpoints require authentication using a JW<PERSON> token.

### Headers

```
Authorization: Bearer {token}
```

### Obtaining a Token

```
POST /admin/auth/login
```

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

**Response:**

```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "admin": {
    "id": 1,
    "name": "Admin User",
    "email": "<EMAIL>",
    "role": "SUPER_ADMIN"
  }
}
```

## Error Handling

All endpoints return standardized error responses:

```json
{
  "success": false,
  "message": "Error message",
  "errors": [
    {
      "field": "email",
      "message": "Invalid email format"
    }
  ],
  "code": "VALIDATION_ERROR"
}
```

Common error codes:

- `AUTHENTICATION_ERROR`: Invalid or expired token
- `AUTHORIZATION_ERROR`: Insufficient permissions
- `VALIDATION_ERROR`: Invalid request data
- `RESOURCE_NOT_FOUND`: Requested resource not found
- `INTERNAL_SERVER_ERROR`: Server-side error

## API Endpoints

### Dashboard

#### Get Dashboard Statistics

```
GET /admin/dashboard/stats
```

**Query Parameters:**

- `period`: Time period (day, week, month, year)

**Response:**

```json
{
  "success": true,
  "stats": {
    "totalUsers": 5000,
    "newUsers": 120,
    "premiumUsers": 850,
    "pendingVerifications": 45,
    "totalRevenue": 125000,
    "successfulMatches": 320
  }
}
```

#### Get Recent Activity

```
GET /admin/dashboard/activity
```

**Query Parameters:**

- `limit`: Number of activities to return (default: 10)

**Response:**

```json
{
  "success": true,
  "activities": [
    {
      "id": 1,
      "type": "USER_REGISTRATION",
      "userId": 1001,
      "userName": "John Doe",
      "timestamp": "2023-08-01T10:30:00Z",
      "details": {
        "email": "<EMAIL>"
      }
    },
    // More activities...
  ]
}
```

### User Management

#### Get Users

```
GET /admin/users
```

**Query Parameters:**

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `search`: Search term
- `status`: User status (active, inactive)
- `verified`: Verification status (true, false)
- `premium`: Premium status (true, false)

**Response:**

```json
{
  "success": true,
  "users": [
    {
      "id": 1001,
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+************",
      "gender": "male",
      "age": 28,
      "location": "Mumbai",
      "status": "active",
      "isVerified": true,
      "isPremium": false,
      "registeredAt": "2023-07-15T08:30:00Z",
      "lastLogin": "2023-08-01T14:20:00Z"
    },
    // More users...
  ],
  "pagination": {
    "total": 5000,
    "page": 1,
    "limit": 10,
    "totalPages": 500
  }
}
```

#### Get User Details

```
GET /admin/users/{id}
```

**Response:**

```json
{
  "success": true,
  "user": {
    "id": 1001,
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+************",
    "gender": "male",
    "dateOfBirth": "1995-05-15",
    "age": 28,
    "height": 175,
    "maritalStatus": "never_married",
    "religion": "Hindu",
    "caste": "Maratha",
    "subCaste": "Deshmukh",
    "education": "B.Tech",
    "occupation": "Software Engineer",
    "income": "10-15 LPA",
    "location": {
      "city": "Mumbai",
      "state": "Maharashtra",
      "country": "India"
    },
    "about": "I am a software engineer...",
    "preferences": {
      "ageRange": [25, 32],
      "heightRange": [150, 170],
      "maritalStatus": ["never_married"],
      "education": ["graduate", "post_graduate"],
      "location": ["Mumbai", "Pune"]
    },
    "photos": [
      {
        "id": 1,
        "url": "https://example.com/photos/1.jpg",
        "isApproved": true,
        "isPrimary": true
      }
    ],
    "documents": [
      {
        "id": 1,
        "type": "id_proof",
        "url": "https://example.com/docs/1.pdf",
        "isApproved": true,
        "uploadedAt": "2023-07-16T10:20:00Z"
      }
    ],
    "subscription": {
      "plan": "Gold",
      "startDate": "2023-07-20T00:00:00Z",
      "endDate": "2023-10-20T00:00:00Z",
      "isActive": true,
      "autoRenew": true
    },
    "status": "active",
    "isVerified": true,
    "isPremium": true,
    "registeredAt": "2023-07-15T08:30:00Z",
    "lastLogin": "2023-08-01T14:20:00Z"
  }
}
```

#### Update User

```
PUT /admin/users/{id}
```

**Request Body:**

```json
{
  "status": "inactive",
  "isVerified": true,
  "notes": "User verified with valid ID proof"
}
```

**Response:**

```json
{
  "success": true,
  "message": "User updated successfully",
  "user": {
    "id": 1001,
    "name": "John Doe",
    "status": "inactive",
    "isVerified": true
    // Other user fields...
  }
}
```

#### Delete User

```
DELETE /admin/users/{id}
```

**Response:**

```json
{
  "success": true,
  "message": "User deleted successfully"
}
```

### Verification Queue

#### Get Verification Requests

```
GET /admin/verification-queue
```

**Query Parameters:**

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `status`: Request status (pending, approved, rejected)

**Response:**

```json
{
  "success": true,
  "verificationRequests": [
    {
      "id": 1,
      "userId": 1001,
      "userName": "John Doe",
      "documentType": "id_proof",
      "documentUrl": "https://example.com/docs/1.pdf",
      "status": "pending",
      "submittedAt": "2023-07-16T10:20:00Z",
      "reviewedAt": null,
      "reviewedBy": null,
      "notes": null
    },
    // More requests...
  ],
  "pagination": {
    "total": 45,
    "page": 1,
    "limit": 10,
    "totalPages": 5
  }
}
```

#### Process Verification Request

```
PUT /admin/verification-queue/{id}
```

**Request Body:**

```json
{
  "status": "approved",
  "notes": "Valid ID proof verified"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Verification request processed successfully",
  "verificationRequest": {
    "id": 1,
    "userId": 1001,
    "status": "approved",
    "reviewedAt": "2023-08-02T11:30:00Z",
    "reviewedBy": "Admin User",
    "notes": "Valid ID proof verified"
  }
}
```

### Blog Posts

#### Get Blog Posts

```
GET /admin/blog-posts
```

**Query Parameters:**

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `search`: Search term
- `category`: Blog category
- `status`: Post status (published, draft)

**Response:**

```json
{
  "success": true,
  "posts": [
    {
      "id": 1,
      "title": "Finding Your Perfect Match",
      "slug": "finding-your-perfect-match",
      "excerpt": "Tips for creating an attractive profile...",
      "content": "Creating an attractive profile is the first step...",
      "featuredImage": "https://example.com/images/blog/1.jpg",
      "category": "Profile Tips",
      "tags": ["profile", "tips", "matching"],
      "author": "Admin User",
      "authorId": 1,
      "status": "published",
      "publishedAt": "2023-07-15T10:30:00Z",
      "createdAt": "2023-07-10T08:15:00Z",
      "updatedAt": "2023-07-15T10:30:00Z",
      "viewCount": 1250,
      "commentCount": 18
    },
    // More posts...
  ],
  "pagination": {
    "total": 25,
    "page": 1,
    "limit": 10,
    "totalPages": 3
  },
  "categories": ["Profile Tips", "Success Stories", "Dating Advice", "Relationship Tips"]
}
```

#### Get Blog Post

```
GET /admin/blog-posts/{id}
```

**Response:**

```json
{
  "success": true,
  "post": {
    "id": 1,
    "title": "Finding Your Perfect Match",
    "slug": "finding-your-perfect-match",
    "excerpt": "Tips for creating an attractive profile...",
    "content": "Creating an attractive profile is the first step...",
    "featuredImage": "https://example.com/images/blog/1.jpg",
    "category": "Profile Tips",
    "tags": ["profile", "tips", "matching"],
    "author": "Admin User",
    "authorId": 1,
    "status": "published",
    "publishedAt": "2023-07-15T10:30:00Z",
    "createdAt": "2023-07-10T08:15:00Z",
    "updatedAt": "2023-07-15T10:30:00Z",
    "viewCount": 1250,
    "commentCount": 18
  }
}
```

#### Create Blog Post

```
POST /admin/blog-posts
```

**Request Body:**

```json
{
  "title": "10 Tips for a Successful First Meeting",
  "excerpt": "Essential tips for making a great impression...",
  "content": "The first meeting with a potential match can be nerve-wracking...",
  "category": "Dating Tips",
  "tags": ["meeting", "first impression", "dating"],
  "status": "draft"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Blog post created successfully",
  "post": {
    "id": 6,
    "title": "10 Tips for a Successful First Meeting",
    "slug": "10-tips-for-a-successful-first-meeting",
    "excerpt": "Essential tips for making a great impression...",
    "content": "The first meeting with a potential match can be nerve-wracking...",
    "featuredImage": null,
    "category": "Dating Tips",
    "tags": ["meeting", "first impression", "dating"],
    "author": "Admin User",
    "authorId": 1,
    "status": "draft",
    "publishedAt": null,
    "createdAt": "2023-08-02T09:45:00Z",
    "updatedAt": "2023-08-02T09:45:00Z",
    "viewCount": 0,
    "commentCount": 0
  }
}
```

#### Update Blog Post

```
PUT /admin/blog-posts/{id}
```

**Request Body:**

```json
{
  "title": "10 Essential Tips for a Successful First Meeting",
  "status": "published"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Blog post updated successfully",
  "post": {
    "id": 6,
    "title": "10 Essential Tips for a Successful First Meeting",
    "status": "published",
    "publishedAt": "2023-08-02T10:15:00Z",
    "updatedAt": "2023-08-02T10:15:00Z"
    // Other post fields...
  }
}
```

#### Delete Blog Post

```
DELETE /admin/blog-posts/{id}
```

**Response:**

```json
{
  "success": true,
  "message": "Blog post deleted successfully"
}
```

### Email Templates

#### Get Email Templates

```
GET /admin/email-templates
```

**Query Parameters:**

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `search`: Search term
- `category`: Template category

**Response:**

```json
{
  "success": true,
  "templates": [
    {
      "id": 1,
      "name": "Welcome Email",
      "subject": "Welcome to Vaivahik!",
      "content": "<p>Dear {{name}},</p><p>Welcome to Vaivahik...</p>",
      "category": "Onboarding",
      "variables": ["name", "email"],
      "isActive": true,
      "createdAt": "2023-01-15T10:30:00Z",
      "updatedAt": "2023-05-20T14:45:00Z",
      "lastSent": "2023-08-01T09:15:00Z",
      "sendCount": 1250
    },
    // More templates...
  ],
  "pagination": {
    "total": 15,
    "page": 1,
    "limit": 10,
    "totalPages": 2
  },
  "categories": ["Onboarding", "Verification", "Matching", "Subscription", "Interaction", "Account"]
}
```

#### Get Email Template

```
GET /admin/email-templates/{id}
```

**Response:**

```json
{
  "success": true,
  "template": {
    "id": 1,
    "name": "Welcome Email",
    "subject": "Welcome to Vaivahik!",
    "content": "<p>Dear {{name}},</p><p>Welcome to Vaivahik...</p>",
    "category": "Onboarding",
    "variables": ["name", "email"],
    "isActive": true,
    "createdAt": "2023-01-15T10:30:00Z",
    "updatedAt": "2023-05-20T14:45:00Z",
    "lastSent": "2023-08-01T09:15:00Z",
    "sendCount": 1250
  }
}
```

#### Create Email Template

```
POST /admin/email-templates
```

**Request Body:**

```json
{
  "name": "Profile View Notification",
  "subject": "Someone viewed your profile on Vaivahik",
  "content": "<p>Dear {{name}},</p><p>{{viewer_name}} has viewed your profile...</p>",
  "category": "Interaction",
  "variables": ["name", "email", "viewer_name", "viewer_profile_link"],
  "isActive": true
}
```

**Response:**

```json
{
  "success": true,
  "message": "Email template created successfully",
  "template": {
    "id": 7,
    "name": "Profile View Notification",
    "subject": "Someone viewed your profile on Vaivahik",
    "content": "<p>Dear {{name}},</p><p>{{viewer_name}} has viewed your profile...</p>",
    "category": "Interaction",
    "variables": ["name", "email", "viewer_name", "viewer_profile_link"],
    "isActive": true,
    "createdAt": "2023-08-02T11:30:00Z",
    "updatedAt": "2023-08-02T11:30:00Z",
    "lastSent": null,
    "sendCount": 0
  }
}
```

#### Update Email Template

```
PUT /admin/email-templates/{id}
```

**Request Body:**

```json
{
  "subject": "Someone just viewed your profile on Vaivahik",
  "isActive": true
}
```

**Response:**

```json
{
  "success": true,
  "message": "Email template updated successfully",
  "template": {
    "id": 7,
    "subject": "Someone just viewed your profile on Vaivahik",
    "isActive": true,
    "updatedAt": "2023-08-02T11:45:00Z"
    // Other template fields...
  }
}
```

#### Delete Email Template

```
DELETE /admin/email-templates/{id}
```

**Response:**

```json
{
  "success": true,
  "message": "Email template deleted successfully"
}
```

### Admin Users

#### Get Admin Users

```
GET /admin/admin-users
```

**Query Parameters:**

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `search`: Search term
- `role`: Admin role

**Response:**

```json
{
  "success": true,
  "adminUsers": [
    {
      "id": 1,
      "name": "Super Admin",
      "email": "<EMAIL>",
      "role": "SUPER_ADMIN",
      "permissions": ["all"],
      "lastLogin": "2023-08-01T10:15:00Z",
      "createdAt": "2023-01-01T00:00:00Z",
      "status": "active",
      "avatar": null
    },
    // More admin users...
  ],
  "pagination": {
    "total": 6,
    "page": 1,
    "limit": 10,
    "totalPages": 1
  },
  "roles": ["SUPER_ADMIN", "CONTENT_MANAGER", "USER_MANAGER", "FINANCE_ADMIN", "SUPPORT_STAFF"]
}
```

#### Create Admin User

```
POST /admin/admin-users
```

**Request Body:**

```json
{
  "name": "Marketing Manager",
  "email": "<EMAIL>",
  "role": "CONTENT_MANAGER",
  "permissions": ["blog_posts", "success_stories"],
  "password": "securepassword"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Admin user created successfully",
  "adminUser": {
    "id": 7,
    "name": "Marketing Manager",
    "email": "<EMAIL>",
    "role": "CONTENT_MANAGER",
    "permissions": ["blog_posts", "success_stories"],
    "lastLogin": null,
    "createdAt": "2023-08-02T12:30:00Z",
    "status": "active",
    "avatar": null
  }
}
```

---

This documentation covers the core API endpoints for the Vaivahik Admin Panel. For additional endpoints or detailed information, please contact the development team.

Last Updated: [Current Date]
