import { useState } from 'react';
import {
  Box,
  Chip,
  Typography,
  TextField,
  InputAdornment,
  Paper,
  Divider,
  IconButton,
  Tooltip,
  useTheme
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Clear as ClearIcon,
  Info as InfoIcon
} from '@mui/icons-material';

/**
 * FilterChips component for selecting multiple options with chips
 * 
 * @param {Object} props
 * @param {string} props.label - Label for the filter chips section
 * @param {string} props.helperText - Helper text to display below the chips
 * @param {Array} props.options - Array of available options
 * @param {Array} props.selectedOptions - Array of currently selected options
 * @param {Function} props.onChange - Callback when selected options change
 * @param {boolean} props.allowCustom - Whether to allow custom options to be added
 * @param {string} props.customPlaceholder - Placeholder for custom option input
 * @param {number} props.maxSelections - Maximum number of selections allowed (0 for unlimited)
 * @param {Object} props.categoryColors - Object mapping categories to colors (optional)
 * @param {boolean} props.showSelectedCount - Whether to show the count of selected options
 */
const FilterChips = ({
  label,
  helperText,
  options = [],
  selectedOptions = [],
  onChange,
  allowCustom = true,
  customPlaceholder = 'Add custom option...',
  maxSelections = 0,
  categoryColors = {},
  showSelectedCount = true
}) => {
  const theme = useTheme();
  const [searchText, setSearchText] = useState('');
  const [customOption, setCustomOption] = useState('');
  
  // Filter options based on search text
  const filteredOptions = options.filter(option => 
    option.toLowerCase().includes(searchText.toLowerCase()) && 
    !selectedOptions.includes(option)
  );
  
  // Handle selecting an option
  const handleSelectOption = (option) => {
    if (maxSelections > 0 && selectedOptions.length >= maxSelections) {
      return; // Maximum selections reached
    }
    
    const newSelectedOptions = [...selectedOptions, option];
    onChange(newSelectedOptions);
    setSearchText('');
  };
  
  // Handle removing an option
  const handleRemoveOption = (option) => {
    const newSelectedOptions = selectedOptions.filter(item => item !== option);
    onChange(newSelectedOptions);
  };
  
  // Handle adding a custom option
  const handleAddCustomOption = () => {
    if (!customOption.trim() || selectedOptions.includes(customOption.trim())) {
      return;
    }
    
    if (maxSelections > 0 && selectedOptions.length >= maxSelections) {
      return; // Maximum selections reached
    }
    
    const newSelectedOptions = [...selectedOptions, customOption.trim()];
    onChange(newSelectedOptions);
    setCustomOption('');
  };
  
  // Handle key press in custom option input
  const handleCustomKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddCustomOption();
    }
  };
  
  // Get chip color based on category
  const getChipColor = (option) => {
    // If no category colors are provided, use default colors
    if (Object.keys(categoryColors).length === 0) {
      return {
        bgcolor: theme.palette.primary.light,
        color: theme.palette.primary.contrastText
      };
    }
    
    // Find the category for this option
    const category = Object.keys(categoryColors).find(cat => 
      option.toLowerCase().includes(cat.toLowerCase())
    );
    
    if (category) {
      return categoryColors[category];
    }
    
    // Default color
    return {
      bgcolor: theme.palette.primary.light,
      color: theme.palette.primary.contrastText
    };
  };
  
  return (
    <Box sx={{ mb: 2 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
        <Typography variant="subtitle1" fontWeight="medium">
          {label}
        </Typography>
        {showSelectedCount && maxSelections > 0 && (
          <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
            ({selectedOptions.length}/{maxSelections})
          </Typography>
        )}
        {helperText && (
          <Tooltip title={helperText}>
            <IconButton size="small" sx={{ ml: 0.5 }}>
              <InfoIcon fontSize="small" color="action" />
            </IconButton>
          </Tooltip>
        )}
      </Box>
      
      {/* Selected options */}
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
        {selectedOptions.length === 0 ? (
          <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
            No {label.toLowerCase()} selected
          </Typography>
        ) : (
          selectedOptions.map((option) => (
            <Chip
              key={option}
              label={option}
              onDelete={() => handleRemoveOption(option)}
              sx={{
                ...getChipColor(option),
                '& .MuiChip-deleteIcon': {
                  color: 'inherit',
                  opacity: 0.7,
                  '&:hover': {
                    opacity: 1
                  }
                }
              }}
            />
          ))
        )}
      </Box>
      
      {/* Search and add options */}
      <Paper variant="outlined" sx={{ p: 2, borderRadius: 1 }}>
        {/* Search existing options */}
        <TextField
          fullWidth
          size="small"
          placeholder={`Search ${label.toLowerCase()}...`}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon fontSize="small" color="action" />
              </InputAdornment>
            ),
            endAdornment: searchText && (
              <InputAdornment position="end">
                <IconButton
                  size="small"
                  onClick={() => setSearchText('')}
                  edge="end"
                >
                  <ClearIcon fontSize="small" />
                </IconButton>
              </InputAdornment>
            )
          }}
          sx={{ mb: 2 }}
        />
        
        {/* Filtered options */}
        {filteredOptions.length > 0 && (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
            {filteredOptions.map((option) => (
              <Chip
                key={option}
                label={option}
                onClick={() => handleSelectOption(option)}
                clickable
                sx={{
                  ...getChipColor(option),
                  opacity: 0.8,
                  '&:hover': {
                    opacity: 1
                  }
                }}
              />
            ))}
          </Box>
        )}
        
        {/* No results message */}
        {searchText && filteredOptions.length === 0 && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2, fontStyle: 'italic' }}>
            No matching {label.toLowerCase()} found
          </Typography>
        )}
        
        {/* Add custom option */}
        {allowCustom && (
          <>
            <Divider sx={{ my: 2 }} />
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <TextField
                fullWidth
                size="small"
                placeholder={customPlaceholder}
                value={customOption}
                onChange={(e) => setCustomOption(e.target.value)}
                onKeyPress={handleCustomKeyPress}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <AddIcon fontSize="small" color="action" />
                    </InputAdornment>
                  )
                }}
              />
              <IconButton
                color="primary"
                onClick={handleAddCustomOption}
                disabled={!customOption.trim()}
                sx={{ ml: 1 }}
              >
                <AddIcon />
              </IconButton>
            </Box>
          </>
        )}
      </Paper>
      
      {helperText && (
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
          {helperText}
        </Typography>
      )}
    </Box>
  );
};

export default FilterChips;
