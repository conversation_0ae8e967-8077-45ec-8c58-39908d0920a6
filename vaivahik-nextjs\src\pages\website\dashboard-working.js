/**
 * Working Website Dashboard - Minimal Version with Backend Integration
 */

import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  Button,
  Avatar,
  Chip,
  IconButton,
  Badge,
  Drawer,
  AppBar,
  Toolbar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
  useMediaQuery,
  Container,
  CircularProgress
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Dashboard as DashboardIcon,
  Search as SearchIcon,
  Person as PersonIcon,
  Message as MessageIcon,
  Favorite as FavoriteIcon,
  Star as StarIcon,
  Menu as MenuIcon,
  Phone as PhoneIcon,
  Chat as ChatIcon,
  Logout as LogoutIcon,
  TrendingUp as TrendingUpIcon,
  Security as SecurityIcon,
  Description as BiodataIcon,
  Spotlight as SpotlightIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  Premium as PremiumIcon
} from '@mui/icons-material';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';

// Styled Components
const DashboardContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  minHeight: '100vh',
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
}));

const Sidebar = styled(Drawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    width: 280,
    background: 'linear-gradient(180deg, #1a1a2e 0%, #16213e 100%)',
    color: 'white',
    border: 'none',
  }
}));

const TopBar = styled(AppBar)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  color: '#333',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
}));

const MainContent = styled(Box)(({ theme }) => ({
  flexGrow: 1,
  marginLeft: 0,
  [theme.breakpoints.up('md')]: {
    marginLeft: 280,
  },
}));

const StatsCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  borderRadius: 20,
  border: '1px solid rgba(255, 255, 255, 0.2)',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s ease',
  textAlign: 'center',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
  }
}));

export default function WorkingDashboard() {
  const { user, logout } = useAuth();
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // State management
  const [activeTab, setActiveTab] = useState(0);
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState({
    profileViews: 127,
    interests: 23,
    messages: 8,
    matches: 45
  });

  // Navigation items
  const navigationItems = [
    { label: 'Dashboard', icon: DashboardIcon, value: 0 },
    { label: 'AI Matches', icon: StarIcon, value: 1, premium: true },
    { label: 'Search', icon: SearchIcon, value: 2 },
    { label: 'My Profile', icon: PersonIcon, value: 3 },
    { label: 'Messages', icon: MessageIcon, value: 4, premium: true },
    { label: 'Interests', icon: FavoriteIcon, value: 5 },
    { label: 'Verification', icon: SecurityIcon, value: 6 },
    { label: 'Biodata', icon: BiodataIcon, value: 7 },
    { label: 'Spotlight', icon: SpotlightIcon, value: 8, premium: true },
    { label: 'Premium', icon: PremiumIcon, value: 9 },
  ];

  const handleTabChange = (newValue) => {
    setActiveTab(newValue);
  };

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/login');
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Failed to logout');
    }
  };

  const renderDashboardOverview = () => (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Welcome Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" fontWeight="700" sx={{ color: 'white', mb: 2 }}>
          Welcome back, {user?.name || 'Beautiful Soul'}! ✨
        </Typography>
        <Typography variant="h6" sx={{ color: 'rgba(255,255,255,0.8)', mb: 4 }}>
          Your perfect match is waiting to meet you. Let's make magic happen! 💕
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={6} md={3}>
          <StatsCard>
            <CardContent sx={{ p: 3 }}>
              <TrendingUpIcon sx={{ fontSize: 48, color: '#2196F3', mb: 2 }} />
              <Typography variant="h4" fontWeight="700" color="#2196F3" gutterBottom>
                {stats.profileViews}
              </Typography>
              <Typography variant="body2" color="text.secondary" fontWeight="500">
                Profile Views
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
        
        <Grid item xs={6} md={3}>
          <StatsCard>
            <CardContent sx={{ p: 3 }}>
              <FavoriteIcon sx={{ fontSize: 48, color: '#E91E63', mb: 2 }} />
              <Typography variant="h4" fontWeight="700" color="#E91E63" gutterBottom>
                {stats.interests}
              </Typography>
              <Typography variant="body2" color="text.secondary" fontWeight="500">
                Interests
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
        
        <Grid item xs={6} md={3}>
          <StatsCard>
            <CardContent sx={{ p: 3 }}>
              <MessageIcon sx={{ fontSize: 48, color: '#4CAF50', mb: 2 }} />
              <Typography variant="h4" fontWeight="700" color="#4CAF50" gutterBottom>
                {stats.messages}
              </Typography>
              <Typography variant="body2" color="text.secondary" fontWeight="500">
                Messages
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
        
        <Grid item xs={6} md={3}>
          <StatsCard>
            <CardContent sx={{ p: 3 }}>
              <StarIcon sx={{ fontSize: 48, color: '#FF9800', mb: 2 }} />
              <Typography variant="h4" fontWeight="700" color="#FF9800" gutterBottom>
                {stats.matches}
              </Typography>
              <Typography variant="body2" color="text.secondary" fontWeight="500">
                AI Matches
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" fontWeight="700" sx={{ color: 'white', mb: 3 }}>
          ⚡ Quick Actions
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <StatsCard onClick={() => setActiveTab(3)} sx={{ cursor: 'pointer' }}>
              <CardContent sx={{ p: 3 }}>
                <PersonIcon sx={{ fontSize: 48, color: '#2196F3', mb: 2 }} />
                <Typography variant="h6" fontWeight="600" gutterBottom>
                  Complete Profile
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Add more details to get better matches
                </Typography>
              </CardContent>
            </StatsCard>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <StatsCard onClick={() => setActiveTab(6)} sx={{ cursor: 'pointer' }}>
              <CardContent sx={{ p: 3 }}>
                <SecurityIcon sx={{ fontSize: 48, color: '#4CAF50', mb: 2 }} />
                <Typography variant="h6" fontWeight="600" gutterBottom>
                  Get Verified
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Increase trust with profile verification
                </Typography>
              </CardContent>
            </StatsCard>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <StatsCard onClick={() => setActiveTab(7)} sx={{ cursor: 'pointer' }}>
              <CardContent sx={{ p: 3 }}>
                <BiodataIcon sx={{ fontSize: 48, color: '#FF9800', mb: 2 }} />
                <Typography variant="h6" fontWeight="600" gutterBottom>
                  Create Biodata
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Generate beautiful biodata templates
                </Typography>
              </CardContent>
            </StatsCard>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <StatsCard onClick={() => setActiveTab(9)} sx={{ cursor: 'pointer' }}>
              <CardContent sx={{ p: 3 }}>
                <PremiumIcon sx={{ fontSize: 48, color: '#E91E63', mb: 2 }} />
                <Typography variant="h6" fontWeight="600" gutterBottom>
                  Go Premium
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Unlock advanced features & priority
                </Typography>
              </CardContent>
            </StatsCard>
          </Grid>
        </Grid>
      </Box>
    </Container>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 0:
        return renderDashboardOverview();
      default:
        return (
          <Container maxWidth="xl" sx={{ py: 4 }}>
            <Typography variant="h4" sx={{ color: 'white', textAlign: 'center', py: 8 }}>
              {navigationItems.find(item => item.value === activeTab)?.label} - Coming Soon
            </Typography>
          </Container>
        );
    }
  };

  return (
    <>
      <Head>
        <title>Dashboard - Vaivahik Matrimony</title>
      </Head>

      <DashboardContainer>
        {/* Sidebar */}
        <Sidebar
          variant={isMobile ? 'temporary' : 'permanent'}
          open={sidebarOpen}
          onClose={handleSidebarToggle}
        >
          <Box sx={{ p: 3, borderBottom: '1px solid rgba(255,255,255,0.1)' }}>
            <Typography variant="h6" fontWeight="700">
              Vaivahik
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.7 }}>
              AI Matrimony Platform
            </Typography>
          </Box>

          <List sx={{ px: 2, py: 1 }}>
            {navigationItems.map((item) => (
              <ListItem
                key={item.value}
                button
                onClick={() => handleTabChange(item.value)}
                sx={{
                  borderRadius: 2,
                  mb: 1,
                  backgroundColor: activeTab === item.value ? 'rgba(255,255,255,0.1)' : 'transparent',
                  '&:hover': {
                    backgroundColor: 'rgba(255,255,255,0.05)'
                  }
                }}
              >
                <ListItemIcon sx={{ color: 'white', minWidth: 40 }}>
                  <item.icon />
                </ListItemIcon>
                <ListItemText 
                  primary={item.label}
                  sx={{ 
                    '& .MuiListItemText-primary': { 
                      fontWeight: activeTab === item.value ? 600 : 400 
                    }
                  }}
                />
                {item.premium && (
                  <Chip label="💎" size="small" sx={{ backgroundColor: 'rgba(255,215,0,0.2)', color: '#FFD700' }} />
                )}
              </ListItem>
            ))}
          </List>

          <Box sx={{ mt: 'auto', p: 2 }}>
            <Button
              fullWidth
              startIcon={<LogoutIcon />}
              onClick={handleLogout}
              sx={{ color: 'white', borderColor: 'rgba(255,255,255,0.3)' }}
              variant="outlined"
            >
              Logout
            </Button>
          </Box>
        </Sidebar>

        {/* Main Content */}
        <MainContent>
          {/* Top Bar */}
          <TopBar position="fixed">
            <Toolbar>
              {isMobile && (
                <IconButton
                  color="inherit"
                  edge="start"
                  onClick={handleSidebarToggle}
                  sx={{ mr: 2 }}
                >
                  <MenuIcon />
                </IconButton>
              )}

              <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1, fontWeight: 700 }}>
                Vaivahik - AI Matrimony Platform
              </Typography>

              <IconButton color="inherit" sx={{ mr: 1 }}>
                <Badge badgeContent={3} color="error">
                  <NotificationsIcon />
                </Badge>
              </IconButton>

              <Avatar sx={{ width: 32, height: 32 }}>
                {user?.name?.charAt(0) || 'U'}
              </Avatar>
            </Toolbar>
          </TopBar>

          {/* Content Area */}
          <Box sx={{ mt: 8 }}>
            {renderTabContent()}
          </Box>
        </MainContent>
      </DashboardContainer>
    </>
  );
}
