/**
 * User Behavior Tracking Service
 * Comprehensive tracking system for ML-powered matching algorithms
 * Hybrid Redis + PostgreSQL storage for optimal performance
 */

const { PrismaClient } = require('@prisma/client');
const Redis = require('redis');
const logger = require('../utils/logger');

class UserBehaviorTracker {
  constructor() {
    this.prisma = new PrismaClient();
    this.redis = null;
    this.initializeRedis();
    
    // Behavior types and their weights for ML
    this.behaviorTypes = {
      // Profile interactions
      PROFILE_VIEW: { weight: 1, category: 'engagement' },
      PROFILE_LIKE: { weight: 5, category: 'preference' },
      PROFILE_DISLIKE: { weight: -3, category: 'preference' },
      PROFILE_FAVORITE: { weight: 8, category: 'preference' },
      PROFILE_SKIP: { weight: -1, category: 'preference' },
      
      // Communication behaviors
      MESSAGE_SENT: { weight: 10, category: 'communication' },
      MESSAGE_RECEIVED: { weight: 5, category: 'communication' },
      CONVERSATION_STARTED: { weight: 15, category: 'communication' },
      RESPONSE_TIME_FAST: { weight: 3, category: 'engagement' },
      RESPONSE_TIME_SLOW: { weight: -1, category: 'engagement' },
      
      // Search and discovery
      SEARCH_PERFORMED: { weight: 2, category: 'discovery' },
      FILTER_APPLIED: { weight: 1, category: 'preference' },
      MATCH_ACCEPTED: { weight: 12, category: 'preference' },
      MATCH_REJECTED: { weight: -5, category: 'preference' },
      
      // Feature usage
      PREMIUM_FEATURE_USED: { weight: 7, category: 'engagement' },
      CONTACT_REVEALED: { weight: 20, category: 'serious_intent' },
      BIODATA_DOWNLOADED: { weight: 15, category: 'serious_intent' },
      PHOTO_VIEWED: { weight: 2, category: 'engagement' },
      
      // Time-based behaviors
      SESSION_DURATION_LONG: { weight: 4, category: 'engagement' },
      SESSION_DURATION_SHORT: { weight: -1, category: 'engagement' },
      DAILY_ACTIVE: { weight: 2, category: 'engagement' },
      WEEKLY_ACTIVE: { weight: 1, category: 'engagement' }
    };
    
    // Redis keys for different data types
    this.redisKeys = {
      userSession: (userId) => `user:session:${userId}`,
      userPreferences: (userId) => `user:preferences:${userId}`,
      realtimeBehavior: (userId) => `user:behavior:${userId}`,
      behaviorQueue: 'behavior:queue',
      userStats: (userId) => `user:stats:${userId}`
    };
    
    // Start background processing
    this.startBackgroundProcessing();
  }

  async initializeRedis() {
    try {
      this.redis = Redis.createClient({
        host: process.env.REDIS_HOST || 'localhost',
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD,
        db: process.env.REDIS_DB || 0
      });
      
      await this.redis.connect();
      logger.info('Redis connected for behavior tracking');
    } catch (error) {
      logger.error('Failed to connect to Redis:', error);
      // Continue without Redis - will use PostgreSQL only
    }
  }

  /**
   * Track user behavior with hybrid storage
   */
  async trackBehavior(userId, behaviorType, targetUserId = null, metadata = {}) {
    try {
      const timestamp = new Date();
      const behaviorData = {
        userId,
        behaviorType,
        targetUserId,
        metadata: JSON.stringify(metadata),
        timestamp,
        weight: this.behaviorTypes[behaviorType]?.weight || 0,
        category: this.behaviorTypes[behaviorType]?.category || 'unknown'
      };

      // Store in Redis for real-time access (if available)
      if (this.redis) {
        await this.storeInRedis(behaviorData);
      }

      // Queue for PostgreSQL storage
      await this.queueForPersistence(behaviorData);

      // Update real-time user stats
      await this.updateUserStats(userId, behaviorType, behaviorData.weight);

      logger.debug(`Tracked behavior: ${behaviorType} for user ${userId}`);
      
      return { success: true, behaviorId: `${userId}-${timestamp.getTime()}` };
    } catch (error) {
      logger.error('Error tracking behavior:', error);
      throw error;
    }
  }

  /**
   * Store behavior in Redis for real-time access
   */
  async storeInRedis(behaviorData) {
    if (!this.redis) return;

    try {
      const { userId, behaviorType, targetUserId, timestamp, weight, category } = behaviorData;
      
      // Store in user's behavior stream
      const behaviorKey = this.redisKeys.realtimeBehavior(userId);
      await this.redis.zadd(behaviorKey, timestamp.getTime(), JSON.stringify({
        type: behaviorType,
        target: targetUserId,
        weight,
        category,
        timestamp: timestamp.toISOString()
      }));
      
      // Keep only last 1000 behaviors per user
      await this.redis.zremrangebyrank(behaviorKey, 0, -1001);
      
      // Set expiry for 30 days
      await this.redis.expire(behaviorKey, 30 * 24 * 60 * 60);
      
      // Update user preferences in real-time
      if (targetUserId && category === 'preference') {
        await this.updateRealTimePreferences(userId, targetUserId, weight);
      }
    } catch (error) {
      logger.error('Error storing behavior in Redis:', error);
    }
  }

  /**
   * Queue behavior for PostgreSQL persistence
   */
  async queueForPersistence(behaviorData) {
    try {
      if (this.redis) {
        // Add to Redis queue for batch processing
        await this.redis.lpush(this.redisKeys.behaviorQueue, JSON.stringify(behaviorData));
      } else {
        // Direct PostgreSQL storage if Redis unavailable
        await this.storeBehaviorInDB(behaviorData);
      }
    } catch (error) {
      logger.error('Error queuing behavior for persistence:', error);
    }
  }

  /**
   * Store behavior in PostgreSQL database
   */
  async storeBehaviorInDB(behaviorData) {
    try {
      await this.prisma.userBehavior.create({
        data: {
          userId: behaviorData.userId,
          behaviorType: behaviorData.behaviorType,
          targetUserId: behaviorData.targetUserId,
          metadata: behaviorData.metadata,
          weight: behaviorData.weight,
          category: behaviorData.category,
          timestamp: behaviorData.timestamp
        }
      });
    } catch (error) {
      logger.error('Error storing behavior in database:', error);
      throw error;
    }
  }

  /**
   * Update real-time user preferences based on behavior
   */
  async updateRealTimePreferences(userId, targetUserId, weight) {
    if (!this.redis) return;

    try {
      // Get target user's profile characteristics
      const targetProfile = await this.prisma.profile.findUnique({
        where: { userId: targetUserId },
        select: {
          age: true,
          height: true,
          education: true,
          occupation: true,
          location: true,
          caste: true,
          subCaste: true
        }
      });

      if (!targetProfile) return;

      const preferencesKey = this.redisKeys.userPreferences(userId);
      
      // Update preferences based on positive/negative interactions
      for (const [field, value] of Object.entries(targetProfile)) {
        if (value) {
          await this.redis.zincrby(`${preferencesKey}:${field}`, weight, value);
        }
      }
      
      // Set expiry for preferences
      await this.redis.expire(preferencesKey, 7 * 24 * 60 * 60); // 7 days
    } catch (error) {
      logger.error('Error updating real-time preferences:', error);
    }
  }

  /**
   * Update user statistics
   */
  async updateUserStats(userId, behaviorType, weight) {
    if (!this.redis) return;

    try {
      const statsKey = this.redisKeys.userStats(userId);
      const today = new Date().toISOString().split('T')[0];
      
      // Update daily stats
      await this.redis.hincrby(statsKey, `${today}:${behaviorType}`, 1);
      await this.redis.hincrby(statsKey, `${today}:total_weight`, weight);
      await this.redis.hincrby(statsKey, 'lifetime_interactions', 1);
      
      // Set expiry for stats
      await this.redis.expire(statsKey, 30 * 24 * 60 * 60); // 30 days
    } catch (error) {
      logger.error('Error updating user stats:', error);
    }
  }

  /**
   * Get user behavior insights for ML algorithms
   */
  async getUserBehaviorInsights(userId, timeframe = '7d') {
    try {
      const insights = {
        preferences: await this.getUserPreferences(userId),
        recentBehaviors: await this.getRecentBehaviors(userId, timeframe),
        stats: await this.getUserStats(userId),
        patterns: await this.getBehaviorPatterns(userId)
      };

      return insights;
    } catch (error) {
      logger.error('Error getting user behavior insights:', error);
      return null;
    }
  }

  /**
   * Get user preferences from Redis
   */
  async getUserPreferences(userId) {
    if (!this.redis) return {};

    try {
      const preferencesKey = this.redisKeys.userPreferences(userId);
      const preferences = {};
      
      const fields = ['age', 'height', 'education', 'occupation', 'location', 'caste', 'subCaste'];
      
      for (const field of fields) {
        const fieldPreferences = await this.redis.zrevrange(`${preferencesKey}:${field}`, 0, 4, 'WITHSCORES');
        preferences[field] = this.parseRedisZRange(fieldPreferences);
      }
      
      return preferences;
    } catch (error) {
      logger.error('Error getting user preferences:', error);
      return {};
    }
  }

  /**
   * Get recent behaviors from Redis
   */
  async getRecentBehaviors(userId, timeframe) {
    if (!this.redis) return [];

    try {
      const behaviorKey = this.redisKeys.realtimeBehavior(userId);
      const timeframeDays = parseInt(timeframe.replace('d', ''));
      const cutoffTime = Date.now() - (timeframeDays * 24 * 60 * 60 * 1000);
      
      const behaviors = await this.redis.zrevrangebyscore(behaviorKey, '+inf', cutoffTime);
      return behaviors.map(b => JSON.parse(b));
    } catch (error) {
      logger.error('Error getting recent behaviors:', error);
      return [];
    }
  }

  /**
   * Get user statistics
   */
  async getUserStats(userId) {
    if (!this.redis) return {};

    try {
      const statsKey = this.redisKeys.userStats(userId);
      const stats = await this.redis.hgetall(statsKey);
      return stats;
    } catch (error) {
      logger.error('Error getting user stats:', error);
      return {};
    }
  }

  /**
   * Analyze behavior patterns for ML
   */
  async getBehaviorPatterns(userId) {
    try {
      // Get behavior patterns from database for deeper analysis
      const patterns = await this.prisma.userBehavior.groupBy({
        by: ['behaviorType', 'category'],
        where: {
          userId: userId,
          timestamp: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        },
        _count: {
          behaviorType: true
        },
        _avg: {
          weight: true
        }
      });

      return patterns.map(p => ({
        type: p.behaviorType,
        category: p.category,
        frequency: p._count.behaviorType,
        avgWeight: p._avg.weight
      }));
    } catch (error) {
      logger.error('Error getting behavior patterns:', error);
      return [];
    }
  }

  /**
   * Background processing for Redis to PostgreSQL sync
   */
  startBackgroundProcessing() {
    if (!this.redis) return;

    // Process behavior queue every 30 seconds
    setInterval(async () => {
      await this.processBehaviorQueue();
    }, 30000);

    // Cleanup old Redis data every hour
    setInterval(async () => {
      await this.cleanupOldData();
    }, 60 * 60 * 1000);
  }

  /**
   * Process behavior queue from Redis to PostgreSQL
   */
  async processBehaviorQueue() {
    if (!this.redis) return;

    try {
      const batchSize = 100;
      const behaviors = [];
      
      // Get batch of behaviors from queue
      for (let i = 0; i < batchSize; i++) {
        const behaviorData = await this.redis.rpop(this.redisKeys.behaviorQueue);
        if (!behaviorData) break;
        behaviors.push(JSON.parse(behaviorData));
      }

      if (behaviors.length === 0) return;

      // Batch insert into PostgreSQL
      await this.prisma.userBehavior.createMany({
        data: behaviors.map(b => ({
          userId: b.userId,
          behaviorType: b.behaviorType,
          targetUserId: b.targetUserId,
          metadata: b.metadata,
          weight: b.weight,
          category: b.category,
          timestamp: new Date(b.timestamp)
        })),
        skipDuplicates: true
      });

      logger.info(`Processed ${behaviors.length} behaviors to database`);
    } catch (error) {
      logger.error('Error processing behavior queue:', error);
    }
  }

  /**
   * Cleanup old Redis data
   */
  async cleanupOldData() {
    if (!this.redis) return;

    try {
      // Remove old behavior data (older than 30 days)
      const cutoffTime = Date.now() - (30 * 24 * 60 * 60 * 1000);
      
      // Get all user behavior keys
      const keys = await this.redis.keys('user:behavior:*');
      
      for (const key of keys) {
        await this.redis.zremrangebyscore(key, '-inf', cutoffTime);
      }
      
      logger.info('Cleaned up old Redis behavior data');
    } catch (error) {
      logger.error('Error cleaning up old data:', error);
    }
  }

  /**
   * Helper function to parse Redis ZRANGE results
   */
  parseRedisZRange(results) {
    const parsed = [];
    for (let i = 0; i < results.length; i += 2) {
      parsed.push({
        value: results[i],
        score: parseFloat(results[i + 1])
      });
    }
    return parsed;
  }

  /**
   * Get behavior analytics for admin dashboard
   */
  async getBehaviorAnalytics(timeframe = '7d') {
    try {
      const timeframeDays = parseInt(timeframe.replace('d', ''));
      const startDate = new Date(Date.now() - (timeframeDays * 24 * 60 * 60 * 1000));

      const analytics = await this.prisma.userBehavior.groupBy({
        by: ['behaviorType', 'category'],
        where: {
          timestamp: { gte: startDate }
        },
        _count: { behaviorType: true },
        _avg: { weight: true }
      });

      return {
        totalBehaviors: analytics.reduce((sum, a) => sum + a._count.behaviorType, 0),
        behaviorBreakdown: analytics,
        timeframe: timeframe,
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Error getting behavior analytics:', error);
      return null;
    }
  }
}

module.exports = UserBehaviorTracker;
