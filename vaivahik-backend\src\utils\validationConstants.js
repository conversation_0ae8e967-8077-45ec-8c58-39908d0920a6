/**
 * Validation Constants
 * 
 * This module provides constants for validation rules that match the client-side validation.
 * These constants are used by the validation middleware to ensure consistent validation
 * between client and server.
 */

// Gender options
const GENDER_OPTIONS = ['Male', 'Female'];

// Marital status options
const MARITAL_STATUS_OPTIONS = ['Never Married', 'Divorced', 'Widowed', '<PERSON>waiting <PERSON><PERSON><PERSON>', 'Annulled'];

// Family type options
const FAMILY_TYPE_OPTIONS = ['Nuclear Family', 'Joint Family', 'Other'];

// Diet options
const DIET_OPTIONS = ['Vegetarian', 'Non-Vegetarian', 'Eggetarian', 'Vegan', 'Jain'];

// Smoking options
const SMOKING_OPTIONS = ['No', 'Occasionally', 'Yes'];

// Drinking options
const DRINKING_OPTIONS = ['No', 'Occasionally', 'Yes'];

// Education field options
const EDUCATION_FIELD_OPTIONS = [
  'Computer Science', 'Engineering', 'Medicine', 'Business', 
  'Arts', 'Science', 'Law', 'Commerce', 'Other'
];

// Income range options
const INCOME_RANGE_OPTIONS = [
  'Less than 3 LPA', '3-5 LPA', '5-7 LPA', '7-10 LPA', 
  '10-15 LPA', '15-20 LPA', '20-30 LPA', 'Above 30 LPA'
];

// Validation constants
const VALIDATION = {
  // Basic Details
  FULL_NAME: {
    minLength: 3,
    maxLength: 100,
    pattern: /^[A-Za-z\s.'-]+$/
  },
  
  GENDER: {
    options: GENDER_OPTIONS
  },
  
  DATE_OF_BIRTH: {
    minAge: {
      Male: 21,
      Female: 18
    },
    maxAge: 80
  },
  
  HEIGHT: {
    min: 4.5,
    max: 6.5
  },
  
  // Contact Details
  EMAIL: {
    pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  },
  
  PHONE: {
    pattern: /^[+]?[0-9]{10,15}$/
  },
  
  // Location Details
  PINCODE: {
    pattern: /^\d{6}$/
  },
  
  BIRTH_PLACE: {
    maxLength: 100
  },
  
  // Family Details
  FAMILY_TYPE: {
    options: FAMILY_TYPE_OPTIONS
  },
  
  FAMILY_STATUS: {
    options: ['Middle Class', 'Upper Middle Class', 'Rich', 'Affluent']
  },
  
  FATHER_NAME: {
    minLength: 3,
    maxLength: 100
  },
  
  MOTHER_NAME: {
    minLength: 3,
    maxLength: 100
  },
  
  SIBLINGS: {
    min: 0
  },
  
  // Education & Career
  EDUCATION_FIELD: {
    options: EDUCATION_FIELD_OPTIONS
  },
  
  WORKING_WITH: {
    maxLength: 100
  },
  
  INCOME_RANGE: {
    options: INCOME_RANGE_OPTIONS
  },
  
  // Lifestyle & Habits
  DIET: {
    options: DIET_OPTIONS
  },
  
  SMOKING: {
    options: SMOKING_OPTIONS
  },
  
  DRINKING: {
    options: DRINKING_OPTIONS
  },
  
  // Partner Preferences
  AGE_RANGE: {
    min: 18,
    max: 60
  },
  
  HEIGHT_RANGE: {
    min: 4.5,
    max: 6.5
  },
  
  // About Me
  ABOUT_ME: {
    minLength: 50,
    maxLength: 1000
  },
  
  ABOUT_PARTNER: {
    maxLength: 1000
  }
};

module.exports = {
  GENDER_OPTIONS,
  MARITAL_STATUS_OPTIONS,
  FAMILY_TYPE_OPTIONS,
  DIET_OPTIONS,
  SMOKING_OPTIONS,
  DRINKING_OPTIONS,
  EDUCATION_FIELD_OPTIONS,
  INCOME_RANGE_OPTIONS,
  VALIDATION
};
