{"general": {"matchingModel": "TWO_TOWER", "defaultModelId": "production", "minMatchScore": 50, "maxMatchesPerUser": 100, "cacheEnabled": true, "cacheTTL": 3600}, "redis": {"default_ttl": 86400, "embedding_ttl": 604800, "match_ttl": 3600, "enabled": true}, "embedding": {"precompute_interval": 86400, "precompute_limit": 1000, "batch_size": 64, "use_quantized_model": true, "model_path": "models/quantized_model.pt"}, "batch": {"batch_size": 64, "processing_interval": 300, "max_queue_size": 1000, "similarity_threshold": 0.5, "max_matches_per_user": 100}, "monitor": {"monitoring_interval": 300, "metrics_ttl": 604800, "alert_thresholds": {"cpu_usage": 80, "memory_usage": 80, "response_time": 1000, "error_rate": 5}, "log_dir": "logs"}, "ab_testing": {"enabled": true, "experiments": {"matching_model": {"variants": {"A": {"name": "Production Model", "model_id": "production", "traffic_allocation": 80}, "B": {"name": "Experimental Model", "model_id": "experimental", "traffic_allocation": 20}}, "metrics": ["clicks", "messages", "connections", "satisfaction"]}, "explanation_feature": {"variants": {"A": {"name": "Basic Explanation", "feature_enabled": true, "explanation_detail": "basic", "traffic_allocation": 50}, "B": {"name": "Detailed Explanation", "feature_enabled": true, "explanation_detail": "detailed", "traffic_allocation": 50}}, "metrics": ["engagement_time", "clicks", "messages"]}}}, "quantization": {"quantization_method": "dynamic", "quantization_dtype": "qint8", "model_dir": "models", "calibration_samples": 100, "optimize_for_mobile": false, "enable_fusion": true}, "model": {"user_tower_layers": [256, 128], "match_tower_layers": [256, 128], "embedding_size": 128, "dropout_rate": 0.2, "similarity_metrics": ["cosine", "euclidean", "dot"], "similarity_weights": [0.6, 0.2, 0.2]}, "api": {"rate_limit": {"enabled": true, "requests_per_minute": 60, "burst": 10}, "timeout": 30, "max_request_size": "1mb"}, "security": {"cors": {"allowed_origins": ["https://vaivahik.com", "https://admin.vaivahik.com"], "allowed_methods": ["GET", "POST", "PUT", "DELETE"], "allowed_headers": ["Content-Type", "Authorization"], "expose_headers": ["X-Total-Count"], "max_age": 86400}, "jwt": {"expiration": 86400, "refresh_expiration": 604800}}, "notifications": {"match_notification": {"enabled": true, "threshold": 80, "cooldown": 86400}, "view_notification": {"enabled": true, "cooldown": 3600}, "interest_notification": {"enabled": true, "cooldown": 0}}}