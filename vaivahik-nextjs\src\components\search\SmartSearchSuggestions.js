/**
 * Smart Search Suggestions Component
 * AI-powered search suggestions and auto-complete functionality
 */

import { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import {
  Box,
  Paper,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Chip,
  Divider,
  CircularProgress,
  Badge,
  Tooltip
} from '@mui/material';
import {
  Search as SearchIcon,
  TrendingUp as TrendingIcon,
  History as HistoryIcon,
  Psychology as AIIcon,
  LocationOn as LocationIcon,
  Work as WorkIcon,
  School as EducationIcon,
  Favorite as InterestIcon,
  Star as PopularIcon,
  AutoAwesome as SmartIcon
} from '@mui/icons-material';
import { debounce } from 'lodash';
import axios from 'axios';

export default function SmartSearchSuggestions({
  searchQuery = '',
  onSuggestionSelect,
  onSearchExecute,
  userTier = 'free',
  visible = false
}) {
  const { data: session } = useSession();
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchHistory, setSearchHistory] = useState([]);
  const [trendingSearches, setTrendingSearches] = useState([]);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const abortControllerRef = useRef(null);

  // Debounced search function
  const debouncedSearch = useRef(
    debounce(async (query) => {
      if (query.length < 2) {
        setSuggestions([]);
        return;
      }
      
      await fetchSuggestions(query);
    }, 300)
  ).current;

  useEffect(() => {
    if (visible) {
      loadInitialData();
    }
  }, [visible]);

  useEffect(() => {
    if (searchQuery) {
      debouncedSearch(searchQuery);
    } else {
      setSuggestions([]);
    }

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [searchQuery]);

  const loadInitialData = async () => {
    try {
      const [historyResponse, trendingResponse, aiResponse] = await Promise.all([
        axios.get('/api/search/history'),
        axios.get('/api/search/trending'),
        userTier === 'premium' ? axios.get('/api/search/ai-suggestions') : Promise.resolve({ data: { suggestions: [] } })
      ]);

      setSearchHistory(historyResponse.data.history || []);
      setTrendingSearches(trendingResponse.data.trending || []);
      setAiSuggestions(aiResponse.data.suggestions || []);
    } catch (error) {
      console.error('Error loading initial data:', error);
    }
  };

  const fetchSuggestions = async (query) => {
    try {
      setLoading(true);
      
      // Cancel previous request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      
      abortControllerRef.current = new AbortController();

      const response = await axios.get('/api/search/suggestions', {
        params: { 
          q: query,
          includeAI: userTier === 'premium'
        },
        signal: abortControllerRef.current.signal
      });

      if (response.data.success) {
        setSuggestions(response.data.suggestions);
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Error fetching suggestions:', error);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSuggestionClick = (suggestion) => {
    if (onSuggestionSelect) {
      onSuggestionSelect(suggestion);
    }
    
    // Track suggestion usage
    trackSuggestionUsage(suggestion);
  };

  const trackSuggestionUsage = async (suggestion) => {
    try {
      await axios.post('/api/search/track-suggestion', {
        suggestion: suggestion.text,
        type: suggestion.type,
        source: suggestion.source
      });
    } catch (error) {
      console.error('Error tracking suggestion usage:', error);
    }
  };

  const getSuggestionIcon = (type) => {
    switch (type) {
      case 'location': return <LocationIcon />;
      case 'occupation': return <WorkIcon />;
      case 'education': return <EducationIcon />;
      case 'interest': return <InterestIcon />;
      case 'ai': return <AIIcon />;
      case 'trending': return <TrendingIcon />;
      case 'history': return <HistoryIcon />;
      case 'popular': return <PopularIcon />;
      default: return <SearchIcon />;
    }
  };

  const getSuggestionColor = (type) => {
    switch (type) {
      case 'ai': return 'primary';
      case 'trending': return 'success';
      case 'popular': return 'warning';
      case 'history': return 'info';
      default: return 'default';
    }
  };

  const renderSuggestionGroup = (title, items, icon, showBadge = false) => {
    if (!items || items.length === 0) return null;

    return (
      <Box key={title}>
        <Box sx={{ display: 'flex', alignItems: 'center', px: 2, py: 1, bgcolor: 'grey.50' }}>
          {icon}
          <Typography variant="subtitle2" sx={{ ml: 1, fontWeight: 600 }}>
            {title}
          </Typography>
          {showBadge && userTier !== 'premium' && (
            <Chip
              label="Premium"
              size="small"
              color="warning"
              sx={{ ml: 'auto', fontSize: '0.7rem' }}
            />
          )}
        </Box>
        {items.slice(0, userTier === 'premium' ? 5 : 3).map((item, index) => (
          <ListItemButton
            key={`${title}-${index}`}
            onClick={() => handleSuggestionClick(item)}
            sx={{ py: 0.5 }}
          >
            <ListItemIcon sx={{ minWidth: 36 }}>
              {getSuggestionIcon(item.type)}
            </ListItemIcon>
            <ListItemText
              primary={item.text}
              secondary={item.description}
              primaryTypographyProps={{ variant: 'body2' }}
              secondaryTypographyProps={{ variant: 'caption' }}
            />
            {item.count && (
              <Typography variant="caption" color="text.secondary">
                {item.count}
              </Typography>
            )}
          </ListItemButton>
        ))}
        {items.length > (userTier === 'premium' ? 5 : 3) && (
          <ListItem>
            <ListItemText
              primary={
                <Typography variant="caption" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                  +{items.length - (userTier === 'premium' ? 5 : 3)} more suggestions
                  {userTier !== 'premium' && ' (Premium feature)'}
                </Typography>
              }
            />
          </ListItem>
        )}
      </Box>
    );
  };

  if (!visible) return null;

  return (
    <Paper
      elevation={8}
      sx={{
        position: 'absolute',
        top: '100%',
        left: 0,
        right: 0,
        zIndex: 1000,
        maxHeight: 400,
        overflow: 'auto',
        mt: 1,
        borderRadius: 2
      }}
    >
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
          <CircularProgress size={24} />
        </Box>
      )}

      {!loading && searchQuery && suggestions.length > 0 && (
        <List dense>
          {renderSuggestionGroup(
            'Search Suggestions',
            suggestions,
            <SearchIcon fontSize="small" />
          )}
        </List>
      )}

      {!loading && !searchQuery && (
        <List dense>
          {/* AI Suggestions (Premium) */}
          {userTier === 'premium' && aiSuggestions.length > 0 && (
            <>
              {renderSuggestionGroup(
                'AI Recommendations',
                aiSuggestions,
                <SmartIcon fontSize="small" color="primary" />,
                false
              )}
              <Divider />
            </>
          )}

          {/* Trending Searches */}
          {trendingSearches.length > 0 && (
            <>
              {renderSuggestionGroup(
                'Trending Searches',
                trendingSearches,
                <TrendingIcon fontSize="small" color="success" />
              )}
              <Divider />
            </>
          )}

          {/* Search History */}
          {searchHistory.length > 0 && (
            <>
              {renderSuggestionGroup(
                'Recent Searches',
                searchHistory,
                <HistoryIcon fontSize="small" color="info" />
              )}
              <Divider />
            </>
          )}

          {/* Popular Searches */}
          {renderSuggestionGroup(
            'Popular Searches',
            [
              { text: 'Software Engineer in Mumbai', type: 'popular', count: '1.2k' },
              { text: 'Doctor in Delhi', type: 'popular', count: '980' },
              { text: 'Teacher in Pune', type: 'popular', count: '756' },
              { text: 'Business Owner in Bangalore', type: 'popular', count: '654' }
            ],
            <PopularIcon fontSize="small" color="warning" />
          )}

          {/* Premium Upgrade Prompt */}
          {userTier !== 'premium' && (
            <Box sx={{ p: 2, bgcolor: 'primary.light', color: 'primary.contrastText' }}>
              <Typography variant="body2" gutterBottom>
                <SmartIcon fontSize="small" sx={{ mr: 1, verticalAlign: 'middle' }} />
                Get AI-powered search suggestions
              </Typography>
              <Typography variant="caption">
                Upgrade to premium for personalized search recommendations based on your preferences and activity.
              </Typography>
            </Box>
          )}
        </List>
      )}

      {!loading && searchQuery && suggestions.length === 0 && (
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            No suggestions found for "{searchQuery}"
          </Typography>
        </Box>
      )}
    </Paper>
  );
}
