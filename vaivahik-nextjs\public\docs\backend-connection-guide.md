# Connecting to the Real Backend

This guide explains how to connect the Vaivahik Admin Panel to the real backend API.

## Table of Contents

1. [Overview](#overview)
2. [Configuration](#configuration)
3. [Environment Variables](#environment-variables)
4. [Feature Flags](#feature-flags)
5. [Mock Data](#mock-data)
6. [Testing the Connection](#testing-the-connection)
7. [Troubleshooting](#troubleshooting)

## Overview

The Vaivahik Admin Panel is designed to work with both mock data (for development) and a real backend API (for production). The transition between these modes is handled by the `backendConnector` utility, which uses feature flags to determine which mode to use.

## Configuration

The backend connection is configured in two main files:

1. `src/config/backendConfig.js` - Contains configuration for the backend connection
2. `src/utils/backendConnector.js` - Provides the API clients for connecting to the backend

### Backend Configuration

The `backendConfig.js` file contains the following settings:

```javascript
// Backend URLs
const BACKEND_URLS = {
  development: 'http://localhost:5000/api',
  test: 'http://localhost:5000/api',
  production: process.env.NEXT_PUBLIC_API_URL || 'https://api.vaivahik.com/api'
};

// API version
const API_VERSION = 'v1';

// Backend connection settings
const BACKEND_CONFIG = {
  baseUrl: getBackendUrl(),
  apiVersion: API_VERSION,
  timeout: 30000, // 30 seconds
  retryCount: 3,
  retryDelay: 1000, // 1 second
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
};
```

### Backend Connector

The `backendConnector.js` file provides API clients for both mock and real backends:

```javascript
// Backend connector functions
const backendConnector = {
  // User API
  userApi: FEATURE_FLAGS.useRealBackend ? realUserApi : mockUserApi,
  
  // Admin API
  adminApi: FEATURE_FLAGS.useRealBackend ? realAdminApi : mockAdminApi,
  
  // Helper to check if using real backend
  isUsingRealBackend: () => FEATURE_FLAGS.useRealBackend,
  
  // Helper to toggle between real and mock backend (for development)
  toggleBackendMode: () => {
    if (typeof window !== 'undefined') {
      const currentMode = localStorage.getItem('useRealBackend');
      const newMode = currentMode === 'true' ? 'false' : 'true';
      localStorage.setItem('useRealBackend', newMode);
      window.location.reload();
    }
  }
};
```

## Environment Variables

To connect to the real backend, you need to set the following environment variables:

### Development Environment

Create a `.env.local` file in the root of the project with the following variables:

```
# API Configuration
NEXT_PUBLIC_API_URL=https://api.vaivahik.com/api
NEXT_PUBLIC_USE_REAL_BACKEND=true
NEXT_PUBLIC_USE_REAL_AUTH=true
```

### Production Environment

In production, set the following environment variables:

```
NEXT_PUBLIC_API_URL=https://api.vaivahik.com/api
```

In production, the `useRealBackend` flag is automatically set to `true`.

## Feature Flags

The backend connector uses feature flags to determine which mode to use:

```javascript
// Feature flags for backend features
const FEATURE_FLAGS = {
  useRealBackend: isProduction || process.env.NEXT_PUBLIC_USE_REAL_BACKEND === 'true',
  useRealAuth: isProduction || process.env.NEXT_PUBLIC_USE_REAL_AUTH === 'true',
  useRealPayments: isProduction || process.env.NEXT_PUBLIC_USE_REAL_PAYMENTS === 'true',
  useRealNotifications: isProduction || process.env.NEXT_PUBLIC_USE_REAL_NOTIFICATIONS === 'true',
  useRealMatching: isProduction || process.env.NEXT_PUBLIC_USE_REAL_MATCHING === 'true'
};
```

You can enable or disable specific features by setting the corresponding environment variables.

## Mock Data

When using mock data, the backend connector will look for JSON files in the `public/mock-data` directory. The directory structure should match the API endpoints:

```
public/
  mock-data/
    /users.json
    /users/1.json
    /admin/
      users.json
      users/1.json
      blog-posts.json
      blog-posts/1.json
      ...
```

### Creating Mock Data

To create mock data for a new endpoint:

1. Create a JSON file in the `public/mock-data` directory
2. The file name should match the API endpoint (e.g., `/users` -> `users.json`)
3. The file should contain a JSON object with the expected response structure

Example mock data for `/admin/blog-posts`:

```json
{
  "success": true,
  "posts": [
    {
      "id": 1,
      "title": "Finding Your Perfect Match",
      "slug": "finding-your-perfect-match",
      "excerpt": "Tips for creating an attractive profile...",
      "content": "Creating an attractive profile is the first step...",
      "featuredImage": "/images/blog/profile-tips.jpg",
      "category": "Profile Tips",
      "tags": ["profile", "tips", "matching"],
      "author": "Admin User",
      "authorId": 1,
      "status": "published",
      "publishedAt": "2023-05-15T10:30:00Z",
      "createdAt": "2023-05-10T08:15:00Z",
      "updatedAt": "2023-05-15T10:30:00Z",
      "viewCount": 1250,
      "commentCount": 18
    },
    // More posts...
  ],
  "pagination": {
    "total": 25,
    "page": 1,
    "limit": 10,
    "totalPages": 3
  },
  "categories": ["Profile Tips", "Success Stories", "Dating Advice", "Relationship Tips"]
}
```

## Testing the Connection

To test the connection to the real backend:

1. Set the environment variables as described above
2. Start the development server: `npm run dev`
3. Open the browser and navigate to the admin panel
4. Check the browser console for API requests
5. If the connection is successful, you should see requests to the real backend URL

### Toggle Backend Mode

During development, you can toggle between mock and real backend modes using the `toggleBackendMode` function:

```javascript
import backendConnector from '@/utils/backendConnector';

// Toggle between mock and real backend
backendConnector.toggleBackendMode();
```

This function is useful for testing the transition between mock and real backends.

## Troubleshooting

### Common Issues

#### CORS Errors

If you see CORS errors in the browser console, make sure the backend API allows requests from your frontend domain:

```
Access to XMLHttpRequest at 'https://api.vaivahik.com/api/users' from origin 'http://localhost:3000' has been blocked by CORS policy
```

Solution: Configure the backend to allow requests from your frontend domain.

#### Authentication Errors

If you see authentication errors, make sure you're using the correct authentication tokens:

```
401 Unauthorized: Token is invalid or expired
```

Solution: Check the authentication flow and make sure you're using the correct tokens.

#### Network Errors

If you see network errors, make sure the backend API is running and accessible:

```
Failed to fetch: Network error
```

Solution: Check the backend API status and make sure it's running and accessible.

### Getting Help

If you encounter issues connecting to the real backend, please contact the backend team for assistance.

---

This guide is regularly updated to reflect the latest changes to the backend connection process.

Last Updated: [Current Date]
