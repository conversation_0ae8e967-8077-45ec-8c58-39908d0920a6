/**
 * Backend Configuration
 * 
 * This file contains configuration for connecting to the real backend.
 * It provides environment-specific settings and can be easily updated
 * when moving from development to production.
 */

// Environment detection
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';
const isTest = process.env.NODE_ENV === 'test';

// Backend URLs
const BACKEND_URLS = {
  development: 'http://localhost:5000/api',
  test: 'http://localhost:5000/api',
  production: process.env.NEXT_PUBLIC_API_URL || 'https://api.vaivahik.com/api'
};

// Get the appropriate backend URL based on environment
const getBackendUrl = () => {
  if (isDevelopment) return BACKEND_URLS.development;
  if (isTest) return BACKEND_URLS.test;
  return BACKEND_URLS.production;
};

// API version
const API_VERSION = 'v1';

// Backend connection settings
const BACKEND_CONFIG = {
  baseUrl: getBackendUrl(),
  apiVersion: API_VERSION,
  timeout: 30000, // 30 seconds
  retryCount: 3,
  retryDelay: 1000, // 1 second
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
};

// Authentication settings
const AUTH_CONFIG = {
  tokenStorageKey: 'vaivahikToken',
  adminTokenStorageKey: 'adminAccessToken',
  refreshTokenStorageKey: 'vaivahikRefreshToken',
  tokenExpiryKey: 'vaivahikTokenExpiry',
  loginEndpoint: '/auth/login',
  adminLoginEndpoint: '/auth/admin/login',
  refreshEndpoint: '/auth/refresh-token',
  logoutEndpoint: '/auth/logout'
};

// Feature flags for backend features
const FEATURE_FLAGS = {
  useRealBackend: isProduction || process.env.NEXT_PUBLIC_USE_REAL_BACKEND === 'true',
  useRealAuth: isProduction || process.env.NEXT_PUBLIC_USE_REAL_AUTH === 'true',
  useRealPayments: isProduction || process.env.NEXT_PUBLIC_USE_REAL_PAYMENTS === 'true',
  useRealNotifications: isProduction || process.env.NEXT_PUBLIC_USE_REAL_NOTIFICATIONS === 'true',
  useRealMatching: isProduction || process.env.NEXT_PUBLIC_USE_REAL_MATCHING === 'true'
};

// Mock data settings
const MOCK_CONFIG = {
  useMockData: !FEATURE_FLAGS.useRealBackend,
  mockDelay: isDevelopment ? 1000 : 0, // Simulate network delay in development
  mockDataPath: '/mock-data'
};

// Export the configuration
export {
  BACKEND_CONFIG,
  AUTH_CONFIG,
  FEATURE_FLAGS,
  MOCK_CONFIG,
  isDevelopment,
  isProduction,
  isTest
};
