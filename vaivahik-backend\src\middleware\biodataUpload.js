// src/middleware/biodataUpload.js

const multer = require('multer');
const path = require('path');

// Configure storage for biodata template files
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    if (file.fieldname === 'previewImage') {
      cb(null, 'uploads/biodata/previews/');
    } else if (file.fieldname === 'designFile') {
      cb(null, 'uploads/biodata/templates/');
    } else {
      cb(new Error('Invalid field name'), null);
    }
  },
  filename: function (req, file, cb) {
    // Generate unique filename with timestamp
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

// File filter for biodata uploads
const fileFilter = (req, file, cb) => {
  if (file.fieldname === 'previewImage') {
    // Allow only image files for preview
    const allowedImageTypes = /jpeg|jpg|png|webp/;
    const mimetype = allowedImageTypes.test(file.mimetype);
    const extname = allowedImageTypes.test(path.extname(file.originalname).toLowerCase());
    
    if (mimetype && extname) {
      cb(null, true);
    } else {
      cb(new Error('Only JPEG, JPG, PNG, or WEBP images are allowed for preview!'), false);
    }
  } else if (file.fieldname === 'designFile') {
    // Allow HTML files for design templates
    const allowedDesignTypes = /html|htm/;
    const mimetype = file.mimetype === 'text/html' || file.mimetype === 'application/octet-stream';
    const extname = allowedDesignTypes.test(path.extname(file.originalname).toLowerCase());
    
    if (mimetype && extname) {
      cb(null, true);
    } else {
      cb(new Error('Only HTML files are allowed for design templates!'), false);
    }
  } else {
    cb(new Error('Invalid field name'), false);
  }
};

// Configure multer for biodata template uploads
const biodataUpload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit per file
    files: 2 // Maximum 2 files (preview + design)
  },
  fileFilter: fileFilter
});

// Export the configured multer instance
module.exports = biodataUpload;
