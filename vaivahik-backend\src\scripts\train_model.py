"""
Training Script for Enhanced Two-Tower Model

This script trains the enhanced two-tower model with advanced training techniques
and visualizes the training progress.
"""

import os
import sys
import json
import argparse
import logging
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.enhanced_tower_model_pytorch import EnhancedMatrimonyMatchingModel
from services.model_trainer import ModelTrainer
from services.data_augmentation import DataAugmentation
from services.feature_processor import FeatureProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_data(data_path):
    """
    Load training data from JSON file
    
    Args:
        data_path (str): Path to the data file
        
    Returns:
        tuple: user_profiles, match_profiles, positive_pairs
    """
    try:
        with open(data_path, 'r') as f:
            data = json.load(f)
        
        user_profiles = data.get('user_profiles', [])
        match_profiles = data.get('match_profiles', [])
        positive_pairs = data.get('positive_pairs', [])
        
        logger.info(f"Loaded {len(user_profiles)} user profiles, "
                   f"{len(match_profiles)} match profiles, "
                   f"{len(positive_pairs)} positive pairs")
        
        return user_profiles, match_profiles, positive_pairs
    except Exception as e:
        logger.error(f"Error loading data: {str(e)}")
        return [], [], []

def prepare_training_data(user_profiles, match_profiles, positive_pairs):
    """
    Prepare training data from profiles and positive pairs
    
    Args:
        user_profiles (list): List of user profile dictionaries
        match_profiles (list): List of match profile dictionaries
        positive_pairs (list): List of (user_id, match_id) tuples for positive examples
        
    Returns:
        tuple: user_features, match_features, labels
    """
    # Create dictionaries for quick lookup
    user_dict = {user['id']: user for user in user_profiles}
    match_dict = {match['id']: match for match in match_profiles}
    
    # Create feature processor
    feature_processor = FeatureProcessor()
    
    # Compute feature statistics
    feature_processor.compute_feature_stats(user_profiles + match_profiles)
    
    # Create data augmentation
    data_augmentation = DataAugmentation()
    
    # Generate hard negative examples
    hard_negatives = data_augmentation.generate_hard_negatives(
        user_profiles, match_profiles, positive_pairs
    )
    
    # Combine positive and negative examples
    user_features = []
    match_features = []
    labels = []
    
    # Add positive examples
    for user_id, match_id in positive_pairs:
        if user_id in user_dict and match_id in match_dict:
            user = user_dict[user_id]
            match = match_dict[match_id]
            
            # Get user preferences (simplified - in a real system, you'd get this from the database)
            user_preferences = {
                'minAge': user.get('age', 25) - 5 if user.get('age') else None,
                'maxAge': user.get('age', 25) + 5 if user.get('age') else None,
                'religion': user.get('religion'),
                'caste': user.get('caste')
            }
            
            # Process features
            user_feat, match_feat = feature_processor.process_match_pair(
                user, user_preferences, match
            )
            
            user_features.append(user_feat)
            match_features.append(match_feat)
            labels.append(1.0)  # Positive example
    
    # Add hard negative examples
    for user_id, match_id in hard_negatives:
        if user_id in user_dict and match_id in match_dict:
            user = user_dict[user_id]
            match = match_dict[match_id]
            
            # Get user preferences
            user_preferences = {
                'minAge': user.get('age', 25) - 5 if user.get('age') else None,
                'maxAge': user.get('age', 25) + 5 if user.get('age') else None,
                'religion': user.get('religion'),
                'caste': user.get('caste')
            }
            
            # Process features
            user_feat, match_feat = feature_processor.process_match_pair(
                user, user_preferences, match
            )
            
            user_features.append(user_feat)
            match_features.append(match_feat)
            labels.append(0.0)  # Negative example
    
    # Balance the dataset
    user_features, match_features, labels = data_augmentation.balance_dataset(
        user_features, match_features, labels, pos_neg_ratio=1.0
    )
    
    # Add noise for data augmentation
    if len(user_features) < 1000:  # Only augment if dataset is small
        noisy_user_features = data_augmentation.add_noise(user_features)
        noisy_match_features = data_augmentation.add_noise(match_features)
        
        user_features.extend(noisy_user_features)
        match_features.extend(noisy_match_features)
        labels.extend(labels)  # Same labels for augmented data
    
    # Apply feature dropout for regularization
    user_features = data_augmentation.feature_dropout(user_features)
    match_features = data_augmentation.feature_dropout(match_features)
    
    return user_features, match_features, labels

def visualize_training(history, output_dir):
    """
    Visualize training progress
    
    Args:
        history (dict): Training history
        output_dir (str): Directory to save visualizations
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Plot training and validation loss
    plt.figure(figsize=(10, 6))
    plt.plot(history['train_loss'], label='Training Loss')
    if 'val_loss' in history and history['val_loss']:
        plt.plot(history['val_loss'], label='Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, 'loss_curve.png'))
    
    # Plot learning rate
    plt.figure(figsize=(10, 6))
    plt.plot(history['learning_rates'])
    plt.xlabel('Epoch')
    plt.ylabel('Learning Rate')
    plt.title('Learning Rate Schedule')
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, 'learning_rate.png'))
    
    logger.info(f"Training visualizations saved to {output_dir}")

def main():
    """Main function to train the model"""
    parser = argparse.ArgumentParser(description='Train the enhanced two-tower model')
    parser.add_argument('--data', type=str, required=True, help='Path to training data JSON file')
    parser.add_argument('--config', type=str, help='Path to training configuration JSON file')
    parser.add_argument('--output', type=str, default='models', help='Output directory for models and visualizations')
    args = parser.parse_args()
    
    # Load configuration if provided
    config = None
    if args.config:
        try:
            with open(args.config, 'r') as f:
                config = json.load(f)
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
    
    # Create output directory
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    output_dir = os.path.join(args.output, f"training_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    
    # Update config with output directory
    if config is None:
        config = {}
    config['model_dir'] = output_dir
    
    # Load data
    user_profiles, match_profiles, positive_pairs = load_data(args.data)
    
    if not user_profiles or not match_profiles or not positive_pairs:
        logger.error("No data loaded, exiting")
        return
    
    # Prepare training data
    user_features, match_features, labels = prepare_training_data(
        user_profiles, match_profiles, positive_pairs
    )
    
    # Create model and trainer
    model = EnhancedMatrimonyMatchingModel(config)
    model.build_model()
    
    trainer = ModelTrainer(model, config)
    
    # Prepare data loaders
    train_loader, val_loader = trainer.prepare_data(user_features, match_features, labels)
    
    # Train the model
    logger.info("Starting model training...")
    history = trainer.train(train_loader, val_loader)
    
    # Save the final model
    final_model_path = os.path.join(output_dir, "final_model.pt")
    trainer.save_model(final_model_path)
    
    # Visualize training
    visualize_training(history, output_dir)
    
    # Save training history
    history_path = os.path.join(output_dir, "training_history.json")
    with open(history_path, 'w') as f:
        # Convert numpy values to Python types
        serializable_history = {}
        for key, values in history.items():
            serializable_history[key] = [float(v) for v in values]
        
        json.dump(serializable_history, f, indent=2)
    
    logger.info(f"Training completed. Model saved to {final_model_path}")

if __name__ == "__main__":
    main()
