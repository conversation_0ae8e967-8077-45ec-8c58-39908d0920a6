import { useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';

export default function AdminDashboardRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the admin dashboard in src/pages/admin
    console.log('Redirecting to admin dashboard from root pages/admin directory...');
    router.push('/admin/dashboard');
  }, []);

  return (
    <div style={{
      padding: '20px',
      fontFamily: 'Arial, sans-serif',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh'
    }}>
      <Head>
        <title>Redirecting to Admin Dashboard</title>
      </Head>
      <div style={{ textAlign: 'center' }}>
        <h1>Redirecting to Admin Dashboard...</h1>
        <div style={{
          width: '40px',
          height: '40px',
          margin: '20px auto',
          border: '3px solid #f3f3f3',
          borderTop: '3px solid #7e57c2',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    </div>
  );
}
