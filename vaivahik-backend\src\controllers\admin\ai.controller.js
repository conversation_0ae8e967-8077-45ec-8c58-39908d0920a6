// src/controllers/admin/ai.controller.js

/**
 * @description Get AI settings for the matching algorithm
 * @route GET /api/admin/ai/settings
 */
exports.getAiSettings = async (req, res, next) => {
    const prisma = req.prisma;
    const { category } = req.query;

    try {
        // Check if AlgorithmSetting model exists
        let algorithmSettingsModelExists = true;
        try {
            await prisma.algorithmSetting.findFirst();
        } catch (e) {
            if (e.message.includes('does not exist in the current database')) {
                algorithmSettingsModelExists = false;
            } else {
                throw e;
            }
        }

        if (!algorithmSettingsModelExists) {
            // Return default settings if model doesn't exist yet
            return res.status(200).json({
                message: "Algorithm Settings model not yet available. Using default settings.",
                settings: {
                    GENERAL: [
                        {
                            id: "default-matching-algorithm-version",
                            settingKey: "matchingAlgorithmVersion",
                            settingValue: "v1.0",
                            description: "Current version of the matching algorithm",
                            category: "GENERAL",
                            dataType: "STRING"
                        },
                        {
                            id: "default-enable-ai-matching",
                            settingKey: "enableAIMatching",
                            settingValue: true,
                            description: "Enable AI-powered matching",
                            category: "GENERAL",
                            dataType: "BOOLEAN"
                        },
                        {
                            id: "default-matching-model",
                            settingKey: "matchingModel",
                            settingValue: "TWO_TOWER",
                            description: "Type of matching model to use",
                            category: "GENERAL",
                            dataType: "STRING"
                        }
                    ],
                    WEIGHTS: [
                        {
                            id: "default-age-weight",
                            settingKey: "ageWeight",
                            settingValue: 8,
                            description: "Weight for age compatibility in matching algorithm",
                            category: "WEIGHTS",
                            dataType: "NUMBER"
                        },
                        {
                            id: "default-height-weight",
                            settingKey: "heightWeight",
                            settingValue: 6,
                            description: "Weight for height compatibility in matching algorithm",
                            category: "WEIGHTS",
                            dataType: "NUMBER"
                        },
                        {
                            id: "default-education-weight",
                            settingKey: "educationWeight",
                            settingValue: 7,
                            description: "Weight for education compatibility in matching algorithm",
                            category: "WEIGHTS",
                            dataType: "NUMBER"
                        },
                        {
                            id: "default-occupation-weight",
                            settingKey: "occupationWeight",
                            settingValue: 7,
                            description: "Weight for occupation compatibility in matching algorithm",
                            category: "WEIGHTS",
                            dataType: "NUMBER"
                        },
                        {
                            id: "default-location-weight",
                            settingKey: "locationWeight",
                            settingValue: 8,
                            description: "Weight for location proximity in matching algorithm",
                            category: "WEIGHTS",
                            dataType: "NUMBER"
                        },
                        {
                            id: "default-caste-weight",
                            settingKey: "casteWeight",
                            settingValue: 9,
                            description: "Weight for caste compatibility in matching algorithm",
                            category: "WEIGHTS",
                            dataType: "NUMBER"
                        },
                        {
                            id: "default-subcaste-weight",
                            settingKey: "subCasteWeight",
                            settingValue: 5,
                            description: "Weight for subcaste compatibility in matching algorithm",
                            category: "WEIGHTS",
                            dataType: "NUMBER"
                        },
                        {
                            id: "default-gotra-weight",
                            settingKey: "gotraWeight",
                            settingValue: 6,
                            description: "Weight for gotra compatibility in matching algorithm",
                            category: "WEIGHTS",
                            dataType: "NUMBER"
                        },
                        {
                            id: "default-income-weight",
                            settingKey: "incomeWeight",
                            settingValue: 5,
                            description: "Weight for income compatibility in matching algorithm",
                            category: "WEIGHTS",
                            dataType: "NUMBER"
                        },
                        {
                            id: "default-lifestyle-weight",
                            settingKey: "lifestyleWeight",
                            settingValue: 4,
                            description: "Weight for lifestyle compatibility in matching algorithm",
                            category: "WEIGHTS",
                            dataType: "NUMBER"
                        }
                    ],
                    THRESHOLDS: [
                        {
                            id: "default-minimum-match-score",
                            settingKey: "minimumMatchScore",
                            settingValue: 65,
                            description: "Minimum score required to consider a match",
                            category: "THRESHOLDS",
                            dataType: "NUMBER"
                        },
                        {
                            id: "default-high-quality-match-threshold",
                            settingKey: "highQualityMatchThreshold",
                            settingValue: 80,
                            description: "Threshold for high-quality matches",
                            category: "THRESHOLDS",
                            dataType: "NUMBER"
                        }
                    ],
                    AB_TESTING: [
                        {
                            id: "default-ab-testing-enabled",
                            settingKey: "abTestingEnabled",
                            settingValue: false,
                            description: "Enable A/B testing for matching algorithm",
                            category: "AB_TESTING",
                            dataType: "BOOLEAN"
                        },
                        {
                            id: "default-ab-testing-variant",
                            settingKey: "abTestingVariant",
                            settingValue: "A",
                            description: "Active variant for A/B testing",
                            category: "AB_TESTING",
                            dataType: "STRING"
                        },
                        {
                            id: "default-ab-testing-distribution",
                            settingKey: "abTestingDistribution",
                            settingValue: 50,
                            description: "Percentage of users to receive variant B",
                            category: "AB_TESTING",
                            dataType: "NUMBER"
                        }
                    ],
                    ADVANCED: [
                        {
                            id: "default-max-distance-km",
                            settingKey: "maxDistanceKm",
                            settingValue: 100,
                            description: "Maximum distance in kilometers for location matching",
                            category: "ADVANCED",
                            dataType: "NUMBER"
                        },
                        {
                            id: "default-max-age-difference",
                            settingKey: "maxAgeDifference",
                            settingValue: 10,
                            description: "Maximum age difference for age matching",
                            category: "ADVANCED",
                            dataType: "NUMBER"
                        },
                        {
                            id: "default-consider-user-activity",
                            settingKey: "considerUserActivity",
                            settingValue: true,
                            description: "Consider user activity in matching algorithm",
                            category: "ADVANCED",
                            dataType: "BOOLEAN"
                        },
                        {
                            id: "default-boost-new-profiles",
                            settingKey: "boostNewProfiles",
                            settingValue: true,
                            description: "Boost new profiles in matching algorithm",
                            category: "ADVANCED",
                            dataType: "BOOLEAN"
                        },
                        {
                            id: "default-boost-new-profiles-days",
                            settingKey: "boostNewProfilesDays",
                            settingValue: 7,
                            description: "Number of days to boost new profiles",
                            category: "ADVANCED",
                            dataType: "NUMBER"
                        },
                        {
                            id: "default-boost-verified-profiles",
                            settingKey: "boostVerifiedProfiles",
                            settingValue: true,
                            description: "Boost verified profiles in matching algorithm",
                            category: "ADVANCED",
                            dataType: "BOOLEAN"
                        },
                        {
                            id: "default-boost-verified-profiles-amount",
                            settingKey: "boostVerifiedProfilesAmount",
                            settingValue: 10,
                            description: "Percentage boost for verified profiles",
                            category: "ADVANCED",
                            dataType: "NUMBER"
                        },
                        {
                            id: "default-boost-premium-profiles",
                            settingKey: "boostPremiumProfiles",
                            settingValue: true,
                            description: "Boost premium profiles in matching algorithm",
                            category: "ADVANCED",
                            dataType: "BOOLEAN"
                        },
                        {
                            id: "default-boost-premium-profiles-amount",
                            settingKey: "boostPremiumProfilesAmount",
                            settingValue: 15,
                            description: "Percentage boost for premium profiles",
                            category: "ADVANCED",
                            dataType: "NUMBER"
                        }
                    ]
                }
            });
        }

        // Build where clause based on category filter
        let whereClause = {};
        if (category) {
            whereClause.category = category;
        }

        const algorithmSettings = await prisma.algorithmSetting.findMany({
            where: whereClause,
            orderBy: [
                { category: 'asc' },
                { key: 'asc' }
            ]
        });

        // Process settings based on dataType
        const processedSettings = algorithmSettings.map(setting => {
            let value = setting.value;

            // Convert value based on dataType
            switch (setting.dataType) {
                case 'NUMBER':
                    value = parseFloat(value);
                    break;
                case 'BOOLEAN':
                    value = value === 'true';
                    break;
                case 'JSON':
                    try {
                        value = JSON.parse(value);
                    } catch (e) {
                        console.error(`Error parsing JSON for setting ${setting.key}:`, e);
                    }
                    break;
            }

            return {
                id: setting.id,
                settingKey: setting.key,
                settingValue: value,
                description: setting.description,
                category: setting.category,
                dataType: setting.dataType
            };
        });

        // Group by category for easier frontend consumption
        const groupedSettings = processedSettings.reduce((acc, setting) => {
            if (!acc[setting.category]) {
                acc[setting.category] = [];
            }
            acc[setting.category].push(setting);
            return acc;
        }, {});

        res.status(200).json({
            message: "Algorithm settings fetched successfully.",
            settings: groupedSettings
        });
    } catch (error) {
        console.error("Error fetching algorithm settings:", error);
        next(error);
    }
};

/**
 * @description Update AI settings for the matching algorithm
 * @route PUT /api/admin/ai/settings
 */
exports.updateAiSettings = async (req, res, next) => {
    const prisma = req.prisma;
    const { settings } = req.body;

    if (!settings || !Array.isArray(settings)) {
        const error = new Error("Invalid settings data. Expected an array of settings.");
        error.status = 400;
        return next(error);
    }

    try {
        // Check if AlgorithmSetting model exists
        let algorithmSettingsModelExists = true;
        try {
            await prisma.algorithmSetting.findFirst();
        } catch (e) {
            if (e.message.includes('does not exist in the current database')) {
                algorithmSettingsModelExists = false;
            } else {
                throw e;
            }
        }

        if (!algorithmSettingsModelExists) {
            return res.status(400).json({
                message: "Algorithm Settings model not yet available. Please run database migrations first."
            });
        }

        // Use transaction to ensure all updates succeed or fail together
        const results = await prisma.$transaction(
            settings.map(setting => {
                // Convert value based on dataType before saving
                let valueToSave = setting.settingValue;

                if (setting.dataType === 'BOOLEAN') {
                    valueToSave = String(valueToSave);
                } else if (setting.dataType === 'JSON' && typeof valueToSave !== 'string') {
                    valueToSave = JSON.stringify(valueToSave);
                } else {
                    valueToSave = String(valueToSave);
                }

                return prisma.algorithmSetting.upsert({
                    where: { id: setting.id },
                    update: {
                        value: valueToSave,
                        updatedAt: new Date()
                    },
                    create: {
                        id: setting.id,
                        key: setting.settingKey,
                        value: valueToSave,
                        description: setting.description || '',
                        category: setting.category,
                        dataType: setting.dataType,
                        isActive: true
                    }
                });
            })
        );

        res.status(200).json({
            message: "Algorithm settings updated successfully.",
            updated: results.length
        });
    } catch (error) {
        console.error("Error updating algorithm settings:", error);
        next(error);
    }
};

/**
 * @description Get A/B test results
 * @route GET /api/admin/ai/ab-tests
 */
exports.getABTests = async (req, res, next) => {
    try {
        // In a real implementation, this would fetch A/B test results from the database
        // For now, return mock data
        res.status(200).json({
            message: "A/B test results fetched successfully.",
            results: {
                variantA: {
                    matches: 245,
                    conversations: 156,
                    successRate: 63.7
                },
                variantB: {
                    matches: 267,
                    conversations: 182,
                    successRate: 68.2
                }
            }
        });
    } catch (error) {
        console.error("Error fetching A/B test results:", error);
        next(error);
    }
};

/**
 * @description Get success analytics for matches
 * @route GET /api/admin/ai/analytics
 */
exports.getSuccessAnalytics = async (req, res, next) => {
    try {
        // In a real implementation, this would fetch analytics data from the database
        // For now, return mock data
        res.status(200).json({
            message: "Success analytics fetched successfully.",
            metrics: {
                totalMatches: 5842,
                successfulMatches: 2156,
                averageMatchScore: 72.4,
                matchDistribution: [12, 18, 25, 30, 15],
                monthlyTrend: [120, 145, 160, 178, 195, 210]
            }
        });
    } catch (error) {
        console.error("Error fetching success analytics:", error);
        next(error);
    }
};