// src/routes/admin/photoModeration.routes.js

const express = require('express');
const router = express.Router();
const photoModerationController = require('../../controllers/admin/photoModeration.controller');
const { authenticateAdmin } = require('../../middleware/auth.middleware');

// Apply admin authentication middleware to all routes
router.use(authenticateAdmin);

// Get moderation settings
router.get('/settings', photoModerationController.getModerationSettings);

// Update moderation settings
router.put('/settings', photoModerationController.updateModerationSettings);

// Get moderation statistics
router.get('/stats', photoModerationController.getModerationStats);

// Process a batch of pending photos
router.post('/batch-process', photoModerationController.batchProcessPhotos);

// Get photos by status
router.get('/photos', photoModerationController.getPhotosByStatus);

// Update a photo's status
router.put('/photos/:photoId/status', photoModerationController.updatePhotoStatus);

module.exports = router;
