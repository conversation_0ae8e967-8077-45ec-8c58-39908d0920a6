// API endpoint to run admin functionality tests
import { exec } from 'child_process';
import path from 'path';
import fs from 'fs';

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Get the project root directory
    const projectRoot = process.cwd();
    
    // Path to the test script
    const testScriptPath = path.join(projectRoot, 'test-admin-functionality.js');
    
    // Check if the test script exists
    if (!fs.existsSync(testScriptPath)) {
      return res.status(404).json({
        success: false,
        message: 'Test script not found. Please make sure test-admin-functionality.js exists in the project root.'
      });
    }
    
    // Create screenshots directory if it doesn't exist
    const screenshotsDir = path.join(projectRoot, 'test-screenshots');
    if (!fs.existsSync(screenshotsDir)) {
      fs.mkdirSync(screenshotsDir, { recursive: true });
    }
    
    // Run the test script
    exec(`node ${testScriptPath}`, { cwd: projectRoot }, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error running admin functionality test: ${error.message}`);
        return res.status(500).json({
          success: false,
          message: 'Error running admin functionality test',
          error: error.message,
          stdout,
          stderr
        });
      }
      
      if (stderr) {
        console.error(`Test script stderr: ${stderr}`);
      }
      
      // Parse the test results from stdout
      let results = {
        success: true,
        total: 0,
        passed: 0,
        failed: 0,
        results: [],
        output: stdout
      };
      
      try {
        // Try to extract test results from the output
        const summaryMatch = stdout.match(/Total: (\d+) tests, (\d+) passed, (\d+) failed/);
        if (summaryMatch) {
          results.total = parseInt(summaryMatch[1]);
          results.passed = parseInt(summaryMatch[2]);
          results.failed = parseInt(summaryMatch[3]);
        }
        
        // Extract individual test results
        const testResults = [];
        const testResultRegex = /(✅|❌) ([^:]+): (Passed|Failed)/g;
        let match;
        while ((match = testResultRegex.exec(stdout)) !== null) {
          testResults.push({
            status: match[1] === '✅' ? 'passed' : 'failed',
            name: match[2].trim(),
            message: match[3]
          });
        }
        
        if (testResults.length > 0) {
          results.results = testResults;
        }
      } catch (parseError) {
        console.error('Error parsing test results:', parseError);
      }
      
      // Get list of screenshots
      try {
        const screenshots = fs.readdirSync(screenshotsDir)
          .filter(file => file.endsWith('.png'))
          .map(file => `/test-screenshots/${file}`);
        
        results.screenshots = screenshots;
      } catch (fsError) {
        console.error('Error reading screenshots directory:', fsError);
      }
      
      return res.status(200).json(results);
    });
  } catch (error) {
    console.error('Error in admin functionality test handler:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
}
