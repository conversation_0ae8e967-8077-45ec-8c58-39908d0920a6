/**
 * WebSocket Initialization Module
 * 
 * This module initializes the WebSocket server for real-time notifications.
 * It should be imported and called from your main server file.
 */
const http = require('http');
const { initWebSocketServer } = require('./websocket-server');
const notificationScheduler = require('./notification-scheduler');

/**
 * Initialize notification services
 * 
 * @param {Express.Application} app - Express application
 * @returns {http.Server} HTTP server with WebSocket support
 */
const initNotificationServices = (app) => {
  // Create HTTP server from Express app
  const server = http.createServer(app);
  
  // Initialize WebSocket server
  const wss = initWebSocketServer(server);
  
  // Initialize notification scheduler
  notificationScheduler.initScheduler();
  
  console.log('Notification services initialized');
  
  return server;
};

module.exports = {
  initNotificationServices
};
