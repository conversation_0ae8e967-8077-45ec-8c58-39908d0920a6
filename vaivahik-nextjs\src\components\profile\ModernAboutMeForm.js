/**
 * Modern About Me Form
 *
 * A modern UI form for collecting about me details as part of the profile completion process.
 * Uses the shared styled components for consistent UI across the application.
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  FormHelperText,
  CircularProgress,
  Alert,
  Typography
} from '@mui/material';
import {
  Description as DescriptionIcon
} from '@mui/icons-material';
import { validateField, VALIDATION_RULES } from '@/utils/validationUtils';
import { formatError, getUserFriendlyMessage, isValidationError } from '@/utils/errorHandling';
import {
  StyledPaper,
  StyledTextField,
  StyledButton,
  StyledFormLabel,
  FloatingElement,
  FormSection,
  StyledSectionTitle
} from '@/components/ui/ModernFormComponents';

const ModernAboutMeForm = ({ userData, onSave, isLoading = false }) => {
  const [formData, setFormData] = useState({
    aboutMe: ''
  });

  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [charCount, setCharCount] = useState(0);

  // Initialize form with user data if available
  useEffect(() => {
    if (userData?.aboutMe) {
      setFormData({
        aboutMe: userData.aboutMe
      });
      setCharCount(userData.aboutMe.length);
    }
  }, [userData]);

  // Validate a single field
  const validateSingleField = (name, value) => {
    let rule;

    switch (name) {
      case 'aboutMe':
        rule = VALIDATION_RULES.ABOUT_ME;
        break;
      default:
        return null;
    }

    return validateField(name, value, rule, formData);
  };

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    const newFormData = {
      ...formData,
      [name]: value
    };

    setFormData(newFormData);

    // Update character count
    if (name === 'aboutMe') {
      setCharCount(value.length);
    }

    // Mark field as touched
    setTouched({
      ...touched,
      [name]: true
    });

    // Validate field on change if it's been touched
    if (touched[name]) {
      const fieldError = validateSingleField(name, value);
      setErrors(prevErrors => {
        if (fieldError) {
          return { ...prevErrors, [name]: fieldError };
        } else {
          // Remove error if it exists
          const { [name]: _, ...restErrors } = prevErrors;
          return restErrors;
        }
      });
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    // Validate all fields
    Object.keys(formData).forEach(fieldName => {
      const error = validateSingleField(fieldName, formData[fieldName]);
      if (error) {
        newErrors[fieldName] = error;
      }
    });

    // Additional validation for aboutMe
    if (!formData.aboutMe) {
      newErrors.aboutMe = 'About me is required';
    } else if (formData.aboutMe.length < 50) {
      newErrors.aboutMe = 'About me should be at least 50 characters';
    } else if (formData.aboutMe.length > 1000) {
      newErrors.aboutMe = 'About me should not exceed 1000 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Call the onSave function with the form data
    onSave(formData);
  };

  return (
    <StyledPaper>
      {/* Decorative elements */}
      <FloatingElement position="top-right" />
      <FloatingElement position="bottom-left" />

      <Box sx={{ position: 'relative', zIndex: 1 }}>
        <StyledSectionTitle>About Me</StyledSectionTitle>

        {/* Form content */}
        <form onSubmit={handleSubmit}>
          <FormSection title="Tell Us About Yourself">
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <DescriptionIcon fontSize="small" sx={{ mr: 1 }} />
                    About Me*
                  </Box>
                </StyledFormLabel>
                <StyledTextField
                  name="aboutMe"
                  value={formData.aboutMe}
                  onChange={handleChange}
                  fullWidth
                  multiline
                  rows={8}
                  placeholder="Write a brief description about yourself, your interests, values, and what you're looking for in a partner..."
                  error={!!errors.aboutMe}
                  helperText={errors.aboutMe}
                />
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
                  <Typography variant="caption" color={charCount < 50 ? 'error' : 'text.secondary'}>
                    {charCount}/1000 characters (minimum 50)
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12}>
                <Box sx={{
                  p: 2,
                  borderRadius: 2,
                  bgcolor: 'rgba(255, 95, 109, 0.05)',
                  border: '1px solid rgba(255, 95, 109, 0.1)'
                }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Tips for a great About Me:
                  </Typography>
                  <ul style={{ margin: 0, paddingLeft: '1.5rem' }}>
                    <li>
                      <Typography variant="body2">
                        Be authentic and honest about yourself
                      </Typography>
                    </li>
                    <li>
                      <Typography variant="body2">
                        Mention your values, interests, and hobbies
                      </Typography>
                    </li>
                    <li>
                      <Typography variant="body2">
                        Describe what you're looking for in a partner
                      </Typography>
                    </li>
                    <li>
                      <Typography variant="body2">
                        Share something unique about yourself
                      </Typography>
                    </li>
                    <li>
                      <Typography variant="body2">
                        Keep it positive and engaging
                      </Typography>
                    </li>
                  </ul>
                </Box>
              </Grid>
            </Grid>
          </FormSection>

          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
            <StyledButton
              type="submit"
              variant="contained"
              disabled={isLoading}
              startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : null}
            >
              {isLoading ? 'Saving...' : 'Save About Me'}
            </StyledButton>
          </Box>
        </form>
      </Box>
    </StyledPaper>
  );
};

export default ModernAboutMeForm;
