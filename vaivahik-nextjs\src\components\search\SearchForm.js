import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Tabs,
  Tab,
  TextField,
  Button,
  Grid,
  FormControl,
  FormLabel,
  RadioGroup,
  Radio,
  FormControlLabel,
  Divider,
  Chip,
  Select,
  MenuItem,
  InputAdornment,
  IconButton,
  Collapse,
  Autocomplete,
  Switch,
  Tooltip
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import PersonIcon from '@mui/icons-material/Person';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import HeightRangeSelector from './HeightRangeSelector';
import { validateHeight, formatHeight } from '@/utils/heightUtils';

/**
 * Advanced Search Form Component
 * 
 * Features:
 * - Quick search with essential parameters
 * - User ID search
 * - Advanced search with detailed filters
 * - Save search functionality
 * - Responsive design
 */
const SearchForm = ({ onSearch, savedSearches = [], onSaveSearch }) => {
  // Search mode tabs
  const [searchMode, setSearchMode] = useState(0); // 0: Quick Search, 1: ID Search, 2: Advanced
  
  // Basic search parameters
  const [searchParams, setSearchParams] = useState({
    // Quick search params
    gender: 'FEMALE',
    ageFrom: 18,
    ageTo: 35,
    heightFrom: 53, // 4'5"
    heightTo: 77, // 6'5"
    religion: 'HINDU',
    caste: 'MARATHA',
    location: '',
    
    // ID search params
    userId: '',
    
    // Advanced search params
    maritalStatus: [],
    education: [],
    occupation: [],
    incomeRange: '',
    motherTongue: '',
    diet: '',
    manglik: 'DOESNT_MATTER',
    withPhoto: true,
    
    // Additional filters
    subCaste: '',
    gotra: '',
    profileCreatedWithin: '',
    nativePlace: '',
    smoking: 'DOESNT_MATTER',
    drinking: 'DOESNT_MATTER'
  });
  
  // UI state
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  
  // Handle search mode change
  const handleSearchModeChange = (_, newValue) => {
    setSearchMode(newValue);
  };
  
  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams({ ...searchParams, [name]: value });
  };
  
  // Handle checkbox/switch change
  const handleSwitchChange = (e) => {
    const { name, checked } = e.target;
    setSearchParams({ ...searchParams, [name]: checked });
  };
  
  // Handle multi-select change
  const handleMultiSelectChange = (name, value) => {
    setSearchParams({ ...searchParams, [name]: value });
  };
  
  // Handle height range change
  const handleHeightChange = (min, max) => {
    setSearchParams({
      ...searchParams,
      heightFrom: min,
      heightTo: max
    });
  };
  
  // Toggle advanced filters
  const toggleAdvancedFilters = () => {
    setShowAdvancedFilters(!showAdvancedFilters);
  };
  
  // Handle search submission
  const handleSearch = () => {
    // Prepare search data based on active tab
    let searchData = {};
    
    if (searchMode === 0) { // Quick Search
      searchData = {
        searchType: 'QUICK',
        gender: searchParams.gender,
        ageFrom: searchParams.ageFrom,
        ageTo: searchParams.ageTo,
        heightFrom: searchParams.heightFrom,
        heightTo: searchParams.heightTo,
        religion: searchParams.religion,
        caste: searchParams.caste,
        location: searchParams.location
      };
    } else if (searchMode === 1) { // ID Search
      searchData = {
        searchType: 'ID',
        userId: searchParams.userId
      };
    } else { // Advanced Search
      searchData = {
        searchType: 'ADVANCED',
        ...searchParams
      };
    }
    
    // Call the search handler
    onSearch(searchData);
  };
  
  // Save current search
  const handleSaveSearch = () => {
    if (onSaveSearch) {
      onSaveSearch({
        ...searchParams,
        searchMode
      });
      setIsSaved(true);
    }
  };
  
  // Generate age options
  const generateAgeOptions = (min, max) => {
    const options = [];
    for (let i = min; i <= max; i++) {
      options.push(i);
    }
    return options;
  };
  
  // Age options based on gender
  const minAge = searchParams.gender === 'FEMALE' ? 18 : 21;
  const ageFromOptions = generateAgeOptions(minAge, 60);
  const ageToOptions = generateAgeOptions(
    Math.max(minAge, searchParams.ageFrom), 
    70
  );
  
  return (
    <Paper elevation={3} sx={{ p: 3, borderRadius: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5" component="h2">
          Find Your Perfect Match
        </Typography>
        
        {/* Save search button */}
        <Tooltip title={isSaved ? "Search saved" : "Save this search"}>
          <IconButton 
            color={isSaved ? "primary" : "default"} 
            onClick={handleSaveSearch}
            size="small"
          >
            {isSaved ? <BookmarkIcon /> : <BookmarkBorderIcon />}
          </IconButton>
        </Tooltip>
      </Box>
      
      {/* Search mode tabs */}
      <Tabs 
        value={searchMode} 
        onChange={handleSearchModeChange} 
        sx={{ mb: 3 }}
        variant="fullWidth"
      >
        <Tab label="Quick Search" icon={<SearchIcon />} iconPosition="start" />
        <Tab label="Search by ID" icon={<PersonIcon />} iconPosition="start" />
        <Tab label="Advanced Search" icon={<FilterAltIcon />} iconPosition="start" />
      </Tabs>
      
      {/* Quick Search Form */}
      {searchMode === 0 && (
        <Grid container spacing={3}>
          {/* Gender Selection */}
          <Grid item xs={12} sm={6} md={4}>
            <FormControl component="fieldset">
              <FormLabel component="legend">Looking for</FormLabel>
              <RadioGroup
                row
                name="gender"
                value={searchParams.gender}
                onChange={handleInputChange}
              >
                <FormControlLabel value="FEMALE" control={<Radio />} label="Bride" />
                <FormControlLabel value="MALE" control={<Radio />} label="Groom" />
              </RadioGroup>
            </FormControl>
          </Grid>
          
          {/* Age Range */}
          <Grid item xs={12} sm={6} md={4}>
            <FormControl fullWidth>
              <FormLabel>Age</FormLabel>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Select
                  name="ageFrom"
                  value={searchParams.ageFrom}
                  onChange={handleInputChange}
                  size="small"
                  sx={{ width: '45%' }}
                >
                  {ageFromOptions.map(age => (
                    <MenuItem key={`from-${age}`} value={age}>{age}</MenuItem>
                  ))}
                </Select>
                <Typography sx={{ mx: 1 }}>to</Typography>
                <Select
                  name="ageTo"
                  value={searchParams.ageTo}
                  onChange={handleInputChange}
                  size="small"
                  sx={{ width: '45%' }}
                >
                  {ageToOptions.map(age => (
                    <MenuItem key={`to-${age}`} value={age}>{age}</MenuItem>
                  ))}
                </Select>
              </Box>
            </FormControl>
          </Grid>
          
          {/* Religion & Caste */}
          <Grid item xs={12} sm={6} md={4}>
            <FormControl fullWidth>
              <FormLabel>Religion & Community</FormLabel>
              <Select
                name="religion"
                value={searchParams.religion}
                onChange={handleInputChange}
                size="small"
                sx={{ mb: 1 }}
              >
                <MenuItem value="HINDU">Hindu</MenuItem>
                <MenuItem value="MUSLIM">Muslim</MenuItem>
                <MenuItem value="CHRISTIAN">Christian</MenuItem>
                <MenuItem value="SIKH">Sikh</MenuItem>
                <MenuItem value="JAIN">Jain</MenuItem>
                <MenuItem value="BUDDHIST">Buddhist</MenuItem>
                <MenuItem value="OTHER">Other</MenuItem>
              </Select>
              
              <TextField
                name="caste"
                value={searchParams.caste}
                onChange={handleInputChange}
                placeholder="Caste (e.g., Maratha)"
                size="small"
              />
            </FormControl>
          </Grid>
          
          {/* Height Range */}
          <Grid item xs={12}>
            <HeightRangeSelector
              minHeight={searchParams.heightFrom}
              maxHeight={searchParams.heightTo}
              onChange={handleHeightChange}
              gender={searchParams.gender}
            />
          </Grid>
          
          {/* Location */}
          <Grid item xs={12}>
            <FormControl fullWidth>
              <FormLabel>Location</FormLabel>
              <TextField
                name="location"
                value={searchParams.location}
                onChange={handleInputChange}
                placeholder="City, State or Country"
                size="small"
              />
            </FormControl>
          </Grid>
          
          {/* Advanced Filters Toggle */}
          <Grid item xs={12}>
            <Button
              onClick={toggleAdvancedFilters}
              endIcon={showAdvancedFilters ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              sx={{ mt: 1 }}
            >
              {showAdvancedFilters ? 'Hide' : 'Show'} Advanced Filters
            </Button>
          </Grid>
          
          {/* Advanced Filters */}
          <Grid item xs={12}>
            <Collapse in={showAdvancedFilters}>
              <Grid container spacing={3}>
                {/* Marital Status */}
                <Grid item xs={12} sm={6} md={4}>
                  <FormControl fullWidth>
                    <FormLabel>Marital Status</FormLabel>
                    <Select
                      name="maritalStatus"
                      value={searchParams.maritalStatus}
                      onChange={handleInputChange}
                      size="small"
                      multiple
                      renderValue={(selected) => (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => (
                            <Chip key={value} label={value.replace('_', ' ')} size="small" />
                          ))}
                        </Box>
                      )}
                    >
                      <MenuItem value="NEVER_MARRIED">Never Married</MenuItem>
                      <MenuItem value="DIVORCED">Divorced</MenuItem>
                      <MenuItem value="WIDOWED">Widowed</MenuItem>
                      <MenuItem value="AWAITING_DIVORCE">Awaiting Divorce</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                {/* Education */}
                <Grid item xs={12} sm={6} md={4}>
                  <FormControl fullWidth>
                    <FormLabel>Education</FormLabel>
                    <Select
                      name="education"
                      value={searchParams.education}
                      onChange={handleInputChange}
                      size="small"
                      multiple
                      renderValue={(selected) => (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => (
                            <Chip key={value} label={value} size="small" />
                          ))}
                        </Box>
                      )}
                    >
                      <MenuItem value="HIGH_SCHOOL">High School</MenuItem>
                      <MenuItem value="BACHELORS">Bachelor's Degree</MenuItem>
                      <MenuItem value="MASTERS">Master's Degree</MenuItem>
                      <MenuItem value="DOCTORATE">Doctorate</MenuItem>
                      <MenuItem value="DIPLOMA">Diploma</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                {/* With Photo */}
                <Grid item xs={12} sm={6} md={4}>
                  <FormControl fullWidth sx={{ mt: 2 }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={searchParams.withPhoto}
                          onChange={handleSwitchChange}
                          name="withPhoto"
                        />
                      }
                      label="Profiles with Photo"
                    />
                  </FormControl>
                </Grid>
              </Grid>
            </Collapse>
          </Grid>
          
          {/* Search Button */}
          <Grid item xs={12}>
            <Button
              variant="contained"
              color="primary"
              size="large"
              fullWidth
              onClick={handleSearch}
              startIcon={<SearchIcon />}
            >
              Search
            </Button>
          </Grid>
        </Grid>
      )}
      
      {/* ID Search Form */}
      {searchMode === 1 && (
        <Box>
          <Typography variant="body2" color="text.secondary" paragraph>
            Enter the User ID to find a specific profile. User IDs are typically displayed on profiles as "VAI12345".
          </Typography>
          
          <TextField
            fullWidth
            label="User ID"
            name="userId"
            value={searchParams.userId}
            onChange={handleInputChange}
            placeholder="e.g., VAI12345"
            sx={{ mb: 3 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <PersonIcon />
                </InputAdornment>
              ),
            }}
          />
          
          <Button
            variant="contained"
            color="primary"
            size="large"
            fullWidth
            onClick={handleSearch}
            startIcon={<SearchIcon />}
            disabled={!searchParams.userId}
          >
            Find Profile
          </Button>
        </Box>
      )}
      
      {/* Advanced Search Form */}
      {searchMode === 2 && (
        <Typography>
          Advanced search options will be displayed here
        </Typography>
      )}
    </Paper>
  );
};

export default SearchForm;
