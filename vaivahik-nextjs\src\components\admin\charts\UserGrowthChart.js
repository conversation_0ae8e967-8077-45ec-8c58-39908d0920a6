import React, { useEffect, useRef } from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';
import { Bar } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

/**
 * UserGrowthChart Component
 *
 * @param {Object} props - Component props
 * @param {string} props.period - Time period for the chart (week, month, year)
 * @param {Array} props.data - Chart data
 * @param {boolean} props.loading - Loading state
 * @param {function} props.onRefresh - Function to refresh data
 * @returns {JSX.Element} - Chart component
 */
const UserGrowthChart = ({ period = 'month', data = [], loading = false, onRefresh }) => {
  const chartRef = useRef(null);

  // Generate mock data if real data is not available
  const generateMockData = () => {
    switch (period) {
      case 'week':
        return [12, 19, 15, 8, 22, 14, 11];
      case 'month':
        return Array.from({ length: 30 }, () => Math.floor(Math.random() * 20) + 5);
      case 'year':
        return [45, 52, 38, 65, 74, 56, 48, 62, 58, 70, 65, 48];
      default:
        return Array.from({ length: 30 }, () => Math.floor(Math.random() * 20) + 5);
    }
  };

  // Generate labels based on period
  const generateLabels = () => {
    switch (period) {
      case 'week':
        return ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      case 'month':
        return Array.from({ length: 30 }, (_, i) => (i + 1).toString());
      case 'year':
        return ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      default:
        return Array.from({ length: 30 }, (_, i) => (i + 1).toString());
    }
  };

  // Generate chart data
  const chartData = {
    labels: generateLabels(),
    datasets: [
      {
        label: 'New Users',
        data: data.length > 0 ? data : generateMockData(),
        backgroundColor: 'rgba(94, 53, 177, 0.7)',
        borderColor: 'rgba(94, 53, 177, 1)',
        borderWidth: 1,
        borderRadius: 4,
        barThickness: period === 'year' ? 20 : period === 'week' ? 40 : 8,
      },
    ],
  };

  // Chart options
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 15,
          usePointStyle: true,
          pointStyle: 'circle',
        },
      },
      title: {
        display: false,
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#333',
        bodyColor: '#666',
        borderColor: '#ddd',
        borderWidth: 1,
        padding: 10,
        boxPadding: 5,
        usePointStyle: true,
        callbacks: {
          label: (context) => `New Users: ${context.parsed.y}`,
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          maxRotation: 0,
          autoSkip: true,
          maxTicksLimit: period === 'month' ? 10 : undefined,
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          precision: 0,
        },
      },
    },
    animation: {
      duration: 1000,
    },
  };

  // Set up auto-refresh interval
  useEffect(() => {
    // Refresh data every 5 minutes if onRefresh function is provided
    if (onRefresh) {
      const interval = setInterval(() => {
        onRefresh();
      }, 5 * 60 * 1000); // 5 minutes

      return () => clearInterval(interval);
    }
  }, [onRefresh]);

  return (
    <div style={{ position: 'relative', height: '100%', width: '100%', minHeight: '250px' }}>
      {loading ? (
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
          zIndex: 10
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{
              width: '40px',
              height: '40px',
              border: '3px solid #f3f3f3',
              borderTop: '3px solid #5e35b1',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              margin: '0 auto 15px'
            }}></div>
            <p style={{ color: '#666' }}>Loading chart data...</p>
          </div>
        </div>
      ) : null}
      <Bar ref={chartRef} data={chartData} options={options} />
    </div>
  );
};

export default UserGrowthChart;
