import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Button,
  Chip,
  Avatar,
  IconButton,
  Divider,
  Tabs,
  Tab,
  Badge,
  Tooltip,
  CircularProgress,
  Skeleton,
  useTheme,
  alpha,
  Fade
} from '@mui/material';
import VisibilityIcon from '@mui/icons-material/Visibility';
import FavoriteIcon from '@mui/icons-material/Favorite';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import PersonIcon from '@mui/icons-material/Person';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import WorkIcon from '@mui/icons-material/Work';
import SchoolIcon from '@mui/icons-material/School';
import VerifiedIcon from '@mui/icons-material/Verified';
import StarIcon from '@mui/icons-material/Star';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import { getUserActivities, formatActivityTime } from '@/services/activityService';
import UserOnlineStatus from '@/components/common/UserOnlineStatus';
import SmartCallButton from '@/components/contact/SmartCallButton';
import { useRouter } from 'next/router';

/**
 * Profile Visitors Component
 *
 * Advanced UI for displaying profile visitors and visited profiles
 */
const ProfileVisitors = () => {
  const theme = useTheme();
  const router = useRouter();

  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [visitors, setVisitors] = useState([]);
  const [visited, setVisited] = useState([]);
  const [sendingInterest, setSendingInterest] = useState(null);

  // Load visitors and visited profiles on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Load profile visitors
        const visitorsData = await getUserActivities({
          type: 'PROFILE_VISITOR',
          limit: 50
        });

        // Load visited profiles
        const visitedData = await getUserActivities({
          type: 'PROFILE_VISITED',
          limit: 50
        });

        setVisitors(visitorsData.activities || []);
        setVisited(visitedData.activities || []);
      } catch (error) {
        console.error('Error loading profile visitors data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Handle send interest
  const handleSendInterest = async (userId) => {
    try {
      setSendingInterest(userId);

      // Mock API call - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update local state
      // In a real implementation, you would update based on API response

      setSendingInterest(null);
    } catch (error) {
      console.error('Error sending interest:', error);
      setSendingInterest(null);
    }
  };

  // Handle view profile
  const handleViewProfile = (userId) => {
    router.push(`/profile/${userId}`);
  };

  // Handle contact reveal success
  const handleCallInitiated = (data) => {
    console.log('Contact revealed:', data);

    // Track in analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'contact_reveal_success', {
        event_category: 'Profile_Visitors_Engagement',
        event_label: data.targetUserId,
        custom_parameters: {
          source: 'profile_visitors',
          access_reason: data.accessReason,
          platform: data.platform
        }
      });
    }
  };

  // Handle security blocks
  const handleSecurityBlock = (data) => {
    console.log('Security block:', data);

    // Track security events
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'security_block', {
        event_category: 'Security',
        event_label: data.reason,
        custom_parameters: {
          source: 'profile_visitors',
          risk_score: data.riskScore,
          target_user_id: data.targetUserId
        }
      });
    }
  };

  // Render visitor/visited card
  const renderProfileCard = (activity, type) => {
    const user = type === 'visitor' ? activity.targetUser : activity.targetUser;

    if (!user) return null;

    const hasInterest = activity.data?.hasInterest || false;

    return (
      <Card
        elevation={3}
        sx={{
          height: '100%',
          borderRadius: 2,
          transition: 'transform 0.3s ease, box-shadow 0.3s ease',
          '&:hover': {
            transform: 'translateY(-5px)',
            boxShadow: 6
          }
        }}
      >
        <Box sx={{ position: 'relative' }}>
          <CardMedia
            component="img"
            height={200}
            image={user.profilePhoto || '/images/default-profile.jpg'}
            alt={user.name}
            sx={{ objectFit: 'cover' }}
          />

          {/* Premium badge */}
          {user.isPremium && (
            <Chip
              icon={<StarIcon fontSize="small" />}
              label="Premium"
              color="primary"
              size="small"
              sx={{
                position: 'absolute',
                top: 10,
                right: 10,
                backgroundColor: 'rgba(103, 58, 183, 0.85)',
                backdropFilter: 'blur(4px)'
              }}
            />
          )}

          {/* Verified badge */}
          {user.isVerified && (
            <Tooltip title="Verified Profile">
              <Chip
                icon={<VerifiedIcon fontSize="small" />}
                label="Verified"
                color="success"
                size="small"
                sx={{
                  position: 'absolute',
                  top: user.isPremium ? 50 : 10,
                  right: 10,
                  backgroundColor: 'rgba(46, 125, 50, 0.85)',
                  backdropFilter: 'blur(4px)'
                }}
              />
            </Tooltip>
          )}

          {/* Visit time badge */}
          <Chip
            icon={<CalendarTodayIcon fontSize="small" />}
            label={formatActivityTime(activity.createdAt)}
            size="small"
            sx={{
              position: 'absolute',
              bottom: 10,
              left: 10,
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
              color: 'white',
              backdropFilter: 'blur(4px)'
            }}
          />
        </Box>

        <CardContent sx={{ flexGrow: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
            <Typography variant="h6" component="div" noWrap>
              {user.name}
            </Typography>
            <Chip
              label={`ID: ${user._id.substring(0, 6)}`}
              size="small"
              variant="outlined"
            />
          </Box>

          <Box sx={{ mb: 1 }}>
            <UserOnlineStatus userId={user._id} size="small" />
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
            <PersonIcon fontSize="small" color="action" sx={{ mr: 1 }} />
            <Typography variant="body2" color="text.secondary">
              {user.age} yrs, {user.height || '5\'8"'}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
            <WorkIcon fontSize="small" color="action" sx={{ mr: 1 }} />
            <Typography variant="body2" color="text.secondary" noWrap>
              {user.occupation || 'Software Engineer'}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
            <LocationOnIcon fontSize="small" color="action" sx={{ mr: 1 }} />
            <Typography variant="body2" color="text.secondary" noWrap>
              {user.city}{user.state ? `, ${user.state}` : ''}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SchoolIcon fontSize="small" color="action" sx={{ mr: 1 }} />
            <Typography variant="body2" color="text.secondary" noWrap>
              {user.education || 'Bachelor\'s Degree'}
            </Typography>
          </Box>

          {type === 'visitor' && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                <VisibilityIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
                Visited your profile {formatActivityTime(activity.createdAt)}
              </Typography>
            </Box>
          )}

          {type === 'visited' && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                <VisibilityIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
                You visited this profile {formatActivityTime(activity.createdAt)}
              </Typography>
            </Box>
          )}
        </CardContent>

        <Divider />

        <CardActions sx={{ p: 2, flexDirection: 'column', gap: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
            <Button
              variant="outlined"
              startIcon={<VisibilityIcon />}
              size="small"
              onClick={() => handleViewProfile(user._id)}
            >
              View Profile
            </Button>

            {!hasInterest ? (
              <Button
                variant="contained"
                color="primary"
                startIcon={sendingInterest === user._id ? <CircularProgress size={20} color="inherit" /> : <FavoriteBorderIcon />}
                size="small"
                onClick={() => handleSendInterest(user._id)}
                disabled={sendingInterest === user._id}
              >
                Send Interest
              </Button>
            ) : (
              <Chip
                icon={<FavoriteIcon />}
                label="Interest Sent"
                color="primary"
                size="small"
              />
            )}
          </Box>

          {/* Smart Call Button */}
          <Box sx={{ width: '100%' }}>
            <SmartCallButton
              targetUserId={user._id}
              targetUserName={user.name}
              onCallInitiated={handleCallInitiated}
              onSecurityBlock={handleSecurityBlock}
              variant="outlined"
              size="small"
              fullWidth
            />
          </Box>
        </CardActions>
      </Card>
    );
  };

  // Render loading skeleton
  const renderSkeleton = () => {
    return Array(6).fill(0).map((_, index) => (
      <Grid item xs={12} sm={6} md={4} key={index}>
        <Card sx={{ height: '100%', borderRadius: 2 }}>
          <Skeleton variant="rectangular" height={200} />
          <CardContent>
            <Skeleton variant="text" width="70%" height={32} />
            <Skeleton variant="text" width="40%" height={24} />
            <Skeleton variant="text" width="90%" height={24} />
            <Skeleton variant="text" width="60%" height={24} />
            <Skeleton variant="text" width="80%" height={24} />
          </CardContent>
          <CardActions sx={{ p: 2 }}>
            <Skeleton variant="rectangular" width={120} height={36} />
            <Box sx={{ flexGrow: 1 }} />
            <Skeleton variant="rectangular" width={120} height={36} />
          </CardActions>
        </Card>
      </Grid>
    ));
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          Profile Visitors
        </Typography>
        <Typography variant="body2" color="text.secondary">
          See who has viewed your profile and profiles you've visited
        </Typography>
      </Box>

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="standard"
          scrollButtons="auto"
          allowScrollButtonsMobile
        >
          <Tab
            label={
              <Badge
                badgeContent={visitors.length}
                color="primary"
                max={99}
                showZero={false}
              >
                <Box sx={{ px: 1 }}>Profile Visitors</Box>
              </Badge>
            }
          />
          <Tab
            label={
              <Badge
                badgeContent={visited.length}
                color="secondary"
                max={99}
                showZero={false}
              >
                <Box sx={{ px: 1 }}>Profiles You Visited</Box>
              </Badge>
            }
          />
        </Tabs>
      </Box>

      {/* Profile Visitors */}
      {activeTab === 0 && (
        <Fade in={activeTab === 0}>
          <Box>
            {loading ? (
              <Grid container spacing={3}>
                {renderSkeleton()}
              </Grid>
            ) : visitors.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <VisibilityIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No Profile Visitors Yet
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  When someone views your profile, they will appear here.
                </Typography>
              </Box>
            ) : (
              <Grid container spacing={3}>
                {visitors.map(visitor => (
                  <Grid item xs={12} sm={6} md={4} key={visitor._id}>
                    {renderProfileCard(visitor, 'visitor')}
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        </Fade>
      )}

      {/* Profiles You Visited */}
      {activeTab === 1 && (
        <Fade in={activeTab === 1}>
          <Box>
            {loading ? (
              <Grid container spacing={3}>
                {renderSkeleton()}
              </Grid>
            ) : visited.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <VisibilityIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No Profiles Visited Yet
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Profiles you view will appear here.
                </Typography>
              </Box>
            ) : (
              <Grid container spacing={3}>
                {visited.map(visit => (
                  <Grid item xs={12} sm={6} md={4} key={visit._id}>
                    {renderProfileCard(visit, 'visited')}
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        </Fade>
      )}
    </Box>
  );
};

export default ProfileVisitors;
