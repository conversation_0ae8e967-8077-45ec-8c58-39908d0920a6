// src/routes/admin/spotlight.routes.js
const express = require('express');
const router = express.Router();
const spotlightController = require('../../controllers/admin/spotlight.controller');
const { authenticateAdmin } = require('../../middleware/auth.middleware');

// Apply admin authentication middleware to all routes
router.use(authenticateAdmin);

// Get all spotlight features
router.get('/features', spotlightController.getSpotlightFeatures);

// Get spotlight feature by ID
router.get('/features/:id', spotlightController.getSpotlightFeatureById);

// Create a new spotlight feature
router.post('/features', spotlightController.createSpotlightFeature);

// Update a spotlight feature
router.put('/features/:id', spotlightController.updateSpotlightFeature);

// Delete a spotlight feature
router.delete('/features/:id', spotlightController.deleteSpotlightFeature);

// Get active spotlight users
router.get('/active-users', spotlightController.getActiveSpotlightUsers);

module.exports = router;
