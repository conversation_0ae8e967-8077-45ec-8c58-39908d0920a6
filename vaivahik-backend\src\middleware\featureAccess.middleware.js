// src/middleware/featureAccess.middleware.js

/**
 * Middleware to control feature access based on user verification and subscription status
 * This implements a three-tier model: Basic (Unverified), Verified, and Premium
 */
const featureAccessMiddleware = async (req, res, next) => {
    // Skip for public routes or if no user is logged in
    if (!req.user || !req.user.userId) {
        return next();
    }

    const prisma = req.prisma;
    const userId = req.user.userId;

    try {
        // Get the requested feature from the route
        const requestedFeature = req.originalUrl.split('/').pop();

        // Get user's complete profile with subscription info
        const user = await prisma.user.findUnique({
            where: { id: userId },
            include: {
                subscriptions: {
                    where: {
                        isActive: true,
                        endDate: { gt: new Date() }
                    },
                    orderBy: { endDate: 'desc' },
                    take: 1
                }
            }
        });

        if (!user) {
            return res.status(401).json({
                success: false,
                message: "User not found"
            });
        }

        // Check if user has an active subscription
        const hasPremium = user.subscriptions && user.subscriptions.length > 0;
        const subscriptionPlan = hasPremium ? user.subscriptions[0].planType : null;

        // Get user's usage metrics
        const userMetrics = await getUserMetrics(prisma, userId);

        // Define feature access levels with a three-tier model
        const FEATURE_ACCESS = {
            // TIER 1: Basic features - available to all users (even unverified)
            'browse': {
                requiresVerification: false,
                requiresPremium: false,
                unverifiedLimit: 10,  // Unverified users can browse 10 profiles per day
                verifiedLimit: 30,    // Verified users can browse 30 profiles per day
                premiumLimit: null    // Premium users have unlimited browsing
            },
            'search': {
                requiresVerification: false,
                requiresPremium: false,
                searchFilters: {
                    unverified: ['age', 'gender', 'city'],
                    verified: ['age', 'gender', 'city', 'education'],
                    premium: ['age', 'gender', 'city', 'education', 'income', 'occupation', 'religion', 'caste', 'subcaste', 'maritalStatus', 'height', 'diet']
                }
            },
            'view-profile': {
                requiresVerification: false,
                requiresPremium: false,
                unverifiedLimit: 5,   // Unverified users can view 5 profiles per day
                verifiedLimit: 15,    // Verified users can view 15 profiles per day
                premiumLimit: null    // Premium users have unlimited profile views
            },
            'edit-profile': { requiresVerification: false, requiresPremium: false },
            'upload-photo': {
                requiresVerification: false,
                requiresPremium: false,
                unverifiedLimit: 3,   // Unverified users can upload 3 photos
                verifiedLimit: 6,     // Verified users can upload 6 photos
                premiumLimit: 12      // Premium users can upload 12 photos
            },

            // TIER 2: Enhanced features - available to verified users with limitations
            'matches': {
                requiresVerification: true,
                requiresPremium: false,
                verifiedLimit: 10,    // Verified users get 10 matches per day
                premiumLimit: null,   // Premium users get unlimited matches
                usageMetric: 'matchesViewed'
            },
            'connections': {
                requiresVerification: true,
                requiresPremium: false,
                verifiedLimit: 5,     // Verified users can have 5 active connections
                premiumLimit: null,   // Premium users can have unlimited connections
                usageMetric: 'connectionsCount'
            },
            'send-message': {
                requiresVerification: true,
                requiresPremium: userMetrics.messagesSent >= 5, // Require premium after 5 messages for verified users
                verifiedLimit: 5,     // Verified users can send 5 messages total (not per day/month)
                premiumLimit: null,   // Premium users can send unlimited messages
                usageMetric: 'messagesSent',
                upgradeMessage: "You've used all your free messages. Upgrade to Premium for unlimited messaging."
            },
            'receive-message': {
                requiresVerification: false,
                requiresPremium: false
            },

            // Chat features
            'conversations': {
                requiresVerification: true,
                requiresPremium: false,
                verifiedLimit: 10,    // Verified users can view up to 10 conversations
                premiumLimit: null,   // Premium users can view unlimited conversations
                upgradeMessage: "Upgrade to Premium for unlimited conversations and messaging."
            },
            'conversations/:conversationId/messages': {
                requiresVerification: true,
                requiresPremium: false,
                verifiedLimit: 20,    // Verified users can view up to 20 messages per conversation
                premiumLimit: null,   // Premium users can view unlimited messages
                upgradeMessage: "Upgrade to Premium for unlimited message history."
            },
            'conversations/:conversationId/messages/send': {
                requiresVerification: true,
                requiresPremium: userMetrics.messagesSent >= 5, // Require premium after 5 messages for verified users
                verifiedLimit: 5,     // Verified users can send 5 messages total (not per day/month)
                premiumLimit: null,   // Premium users can send unlimited messages
                usageMetric: 'messagesSent',
                upgradeMessage: "You've used all your free messages. Upgrade to Premium for unlimited messaging."
            },

            // TIER 3: Premium features - require subscription
            'advanced-search': {
                requiresVerification: true,
                requiresPremium: true,
                upgradeMessage: "Advanced search helps you find your perfect match faster. Upgrade to Premium to access all filters."
            },
            'contact-details': {
                requiresVerification: true,
                requiresPremium: true,
                upgradeMessage: "Contact details are available only for Premium members. Upgrade now to connect directly."
            },
            'priority-matching': {
                requiresVerification: true,
                requiresPremium: true,
                upgradeMessage: "Get matched with the most compatible profiles first. Upgrade to Premium for priority matching."
            },
            'horoscope-matching': {
                requiresVerification: true,
                requiresPremium: true,
                upgradeMessage: "Detailed horoscope compatibility is a Premium feature. Upgrade now to see your astrological compatibility."
            },
            'profile-boost': {
                requiresVerification: true,
                requiresPremium: true,
                upgradeMessage: "Get 5x more profile views with Profile Boost. Upgrade to Premium to stand out from the crowd."
            },
            'incognito-browsing': {
                requiresVerification: true,
                requiresPremium: true,
                upgradeMessage: "Browse profiles without appearing in their visitor list. Upgrade to Premium for incognito browsing."
            }
        };

        // Check if the feature exists
        const feature = FEATURE_ACCESS[requestedFeature];
        if (feature) {
            // Determine access level based on user status
            const isVerified = user.isVerified;
            const isPremium = hasPremium;

            // Store feature access info in request for controllers to use
            req.featureAccess = {
                isVerified,
                isPremium,
                subscriptionPlan,
                feature: requestedFeature,
                userMetrics
            };

            // Check verification requirement
            if (feature.requiresVerification && !isVerified) {
                return res.status(403).json({
                    success: false,
                    message: "This feature requires verification. Please verify your profile to access it.",
                    verificationRequired: true,
                    verificationBenefits: [
                        "Verification badge on your profile",
                        "Send up to 5 messages",
                        "Connect with up to 5 profiles",
                        "View up to 10 matches per day"
                    ]
                });
            }

            // Check premium requirement
            if (feature.requiresPremium && !isPremium) {
                // Get subscription plans from database or use default ones
                const subscriptionPlans = await getSubscriptionPlans(prisma);

                return res.status(403).json({
                    success: false,
                    message: feature.upgradeMessage || "This feature requires a premium subscription.",
                    subscriptionRequired: true,
                    subscriptionPlans
                });
            }

            // Apply appropriate limits based on user status
            if (!isPremium) {
                if (!isVerified && feature.unverifiedLimit) {
                    req.limit = feature.unverifiedLimit;
                } else if (isVerified && feature.verifiedLimit) {
                    req.limit = feature.verifiedLimit;
                }

                // Add search filter limitations
                if (requestedFeature === 'search' && feature.searchFilters) {
                    req.allowedFilters = isVerified ?
                        feature.searchFilters.verified :
                        feature.searchFilters.unverified;
                }

                // Check usage-based limitations
                if (feature.usageMetric && userMetrics[feature.usageMetric] >= (isVerified ? feature.verifiedLimit : feature.unverifiedLimit)) {
                    return res.status(403).json({
                        success: false,
                        message: feature.upgradeMessage || `You've reached your limit for this feature. Upgrade to Premium for unlimited access.`,
                        subscriptionRequired: true,
                        currentUsage: userMetrics[feature.usageMetric],
                        limit: isVerified ? feature.verifiedLimit : feature.unverifiedLimit,
                        subscriptionPlans: await getSubscriptionPlans(prisma)
                    });
                }
            } else if (isPremium && feature.premiumLimit) {
                // Even premium users might have some limits
                req.limit = feature.premiumLimit;
            }

            // For premium users using premium features, add search filter access
            if (requestedFeature === 'search' && isPremium && feature.searchFilters) {
                req.allowedFilters = feature.searchFilters.premium;
            }
        }

        next();
    } catch (error) {
        console.error('Error in feature access middleware:', error);
        return res.status(500).json({
            success: false,
            message: "An error occurred while checking feature access."
        });
    }
};

/**
 * Get user's usage metrics for feature limitations
 * @param {PrismaClient} prisma - Prisma client
 * @param {string} userId - User ID
 * @returns {Promise<Object>} User metrics
 */
async function getUserMetrics(prisma, userId) {
    try {
        // Get message count
        const messageCount = await prisma.message.count({
            where: { senderId: userId }
        });

        // Get connection count
        const connectionCount = await prisma.match.count({
            where: {
                OR: [
                    { user1Id: userId },
                    { user2Id: userId }
                ],
                status: 'ACCEPTED'
            }
        });

        // Get profile views today
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const profileViewsToday = await prisma.profileView.count({
            where: {
                viewerId: userId,
                viewedAt: { gte: today }
            }
        });

        // Get matches viewed today
        const matchesViewedToday = await prisma.matchView.count({
            where: {
                userId: userId,
                viewedAt: { gte: today }
            }
        });

        return {
            messagesSent: messageCount,
            connectionsCount: connectionCount,
            profileViewsToday: profileViewsToday,
            matchesViewed: matchesViewedToday
        };
    } catch (error) {
        console.error('Error getting user metrics:', error);
        // Return default metrics if there's an error
        return {
            messagesSent: 0,
            connectionsCount: 0,
            profileViewsToday: 0,
            matchesViewed: 0
        };
    }
}

/**
 * Get subscription plans from database
 * @param {PrismaClient} prisma - Prisma client
 * @returns {Promise<Array>} Subscription plans
 */
async function getSubscriptionPlans(prisma) {
    try {
        // Try to get plans from database (implement this when you have a plans table)
        // const plans = await prisma.subscriptionPlan.findMany({
        //     where: { isActive: true },
        //     orderBy: { price: 'asc' }
        // });

        // if (plans.length > 0) {
        //     return plans;
        // }

        // Default plans if none in database
        return [
            {
                name: "Monthly",
                planType: "PREMIUM",
                price: 999,
                duration: 30,
                features: [
                    "Unlimited messaging",
                    "View contact details",
                    "Advanced search filters",
                    "Priority in search results"
                ]
            },
            {
                name: "Quarterly",
                planType: "PREMIUM",
                price: 2499,
                duration: 90,
                savings: "17%",
                features: [
                    "All Monthly features",
                    "Profile boost every month",
                    "17% savings compared to monthly"
                ]
            },
            {
                name: "Annual",
                planType: "PREMIUM",
                price: 7999,
                duration: 365,
                savings: "33%",
                features: [
                    "All Quarterly features",
                    "Dedicated relationship manager",
                    "33% savings compared to monthly"
                ]
            }
        ];
    } catch (error) {
        console.error('Error getting subscription plans:', error);
        // Return default plans if there's an error
        return [
            { name: "Monthly", planType: "PREMIUM", price: 999, duration: 30 },
            { name: "Quarterly", planType: "PREMIUM", price: 2499, duration: 90, savings: "17%" },
            { name: "Annual", planType: "PREMIUM", price: 7999, duration: 365, savings: "33%" }
        ];
    }
}

/**
 * Helper middleware to apply limits based on user status
 * Use this in controllers to limit results based on verification and subscription status
 */
const applyAccessLimits = (req, res, next) => {
    if (req.limit) {
        // Add headers to indicate the limit was applied
        if (!req.featureAccess.isPremium) {
            if (!req.featureAccess.isVerified) {
                res.setHeader('X-Unverified-Limited', 'true');
            } else {
                res.setHeader('X-Verified-Limited', 'true');
            }
            res.setHeader('X-Access-Limit', req.limit);
        }
    }

    // Add allowed filters for search
    if (req.allowedFilters) {
        res.setHeader('X-Allowed-Filters', JSON.stringify(req.allowedFilters));
    }

    next();
};

/**
 * Track user feature usage
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const trackFeatureUsage = async (req, res, next) => {
    if (!req.user || !req.user.userId || !req.featureAccess) {
        return next();
    }

    const prisma = req.prisma;
    const userId = req.user.userId;
    const feature = req.featureAccess.feature;

    try {
        // Track different metrics based on the feature
        switch (feature) {
            case 'view-profile':
                // Track profile view if a profile ID is provided
                if (req.params.userId) {
                    await prisma.profileView.create({
                        data: {
                            viewerId: userId,
                            viewedId: req.params.userId,
                            viewedAt: new Date()
                        }
                    });
                }
                break;

            case 'send-message':
                // Message tracking is handled in the message controller
                break;

            case 'conversations/:conversationId/messages/send':
                // New message tracking is handled in the chat controller
                break;

            case 'matches':
                // Track match view
                await prisma.matchView.create({
                    data: {
                        userId: userId,
                        viewedAt: new Date()
                    }
                });
                break;

            case 'conversations':
                // Track conversation view
                // No specific tracking needed here
                break;

            case 'conversations/:conversationId/messages':
                // Track message view
                // No specific tracking needed here
                break;
        }

        // Continue with the request
        next();
    } catch (error) {
        console.error('Error tracking feature usage:', error);
        // Don't block the request if tracking fails
        next();
    }
};

module.exports = {
    featureAccessMiddleware,
    applyAccessLimits,
    trackFeatureUsage
};
