import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  // Check if user is authenticated
  const session = await getServerSession(req, res, authOptions);
  
  if (!session || !session.user) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  const userId = session.user.id;

  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { referralCode } = req.body;

    if (!referralCode) {
      return res.status(400).json({ 
        success: false, 
        message: 'Referral code is required' 
      });
    }

    // Find the referral
    const referral = await prisma.referral.findUnique({
      where: { referralCode },
      include: {
        referralProgram: true
      }
    });

    if (!referral) {
      return res.status(404).json({ 
        success: false, 
        message: 'Invalid referral code' 
      });
    }

    // Check if referral program is active
    if (referral.referralProgram.status !== 'active') {
      return res.status(400).json({ 
        success: false, 
        message: 'This referral program is no longer active' 
      });
    }

    // Check if referral has already been used
    if (referral.refereeId) {
      return res.status(400).json({ 
        success: false, 
        message: 'This referral code has already been used' 
      });
    }

    // Check if user is trying to refer themselves
    if (referral.referrerId === userId) {
      return res.status(400).json({ 
        success: false, 
        message: 'You cannot use your own referral code' 
      });
    }

    // Update referral with referee ID
    await prisma.referral.update({
      where: { id: referral.id },
      data: {
        refereeId: userId,
        status: 'completed',
        updatedAt: new Date()
      }
    });

    // Process rewards based on conversion requirement
    if (referral.referralProgram.conversionRequirement === 'none') {
      // If no conversion requirement, process rewards immediately
      await processReferralRewards(referral, userId);
    }

    return res.status(200).json({
      success: true,
      message: 'Referral code redeemed successfully',
      program: {
        name: referral.referralProgram.name,
        refereeRewardType: referral.referralProgram.refereeRewardType,
        refereeRewardAmount: referral.referralProgram.refereeRewardAmount,
        conversionRequirement: referral.referralProgram.conversionRequirement
      }
    });
  } catch (error) {
    console.error('Error redeeming referral code:', error);
    return res.status(500).json({ success: false, message: 'Failed to redeem referral code' });
  }
}

// Helper function to process referral rewards
async function processReferralRewards(referral, refereeId) {
  try {
    // Update referral status
    await prisma.referral.update({
      where: { id: referral.id },
      data: {
        status: 'rewarded',
        referrerRewardStatus: 'paid',
        refereeRewardStatus: 'paid',
        updatedAt: new Date()
      }
    });

    // Create reward for referrer
    await prisma.referralReward.create({
      data: {
        referralId: referral.id,
        userId: referral.referrerId,
        rewardType: referral.referralProgram.referrerRewardType,
        rewardAmount: referral.referralProgram.referrerRewardAmount,
        status: 'processed',
        transactionDetails: {
          processedAt: new Date(),
          notes: 'Automatic reward for successful referral'
        }
      }
    });

    // Create reward for referee
    await prisma.referralReward.create({
      data: {
        referralId: referral.id,
        userId: refereeId,
        rewardType: referral.referralProgram.refereeRewardType,
        rewardAmount: referral.referralProgram.refereeRewardAmount,
        status: 'processed',
        transactionDetails: {
          processedAt: new Date(),
          notes: 'Welcome reward for using referral code'
        }
      }
    });

    // Apply rewards based on type
    if (referral.referralProgram.referrerRewardType === 'subscription_days') {
      // Add subscription days to referrer
      // This would typically involve updating the user's subscription
      console.log(`Adding ${referral.referralProgram.referrerRewardAmount} subscription days to referrer ${referral.referrerId}`);
    }

    if (referral.referralProgram.refereeRewardType === 'subscription_days') {
      // Add subscription days to referee
      console.log(`Adding ${referral.referralProgram.refereeRewardAmount} subscription days to referee ${refereeId}`);
    }

    return true;
  } catch (error) {
    console.error('Error processing referral rewards:', error);
    return false;
  }
}
