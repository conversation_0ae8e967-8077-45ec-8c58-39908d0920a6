import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import { io } from 'socket.io-client';
import styles from '@/styles/ChatInterface.module.css';

// Chat message component
const ChatMessage = ({ message, isCurrentUser, showAvatar = true }) => {
  const { content, sentAt, isRead, messageType, metadata } = message;
  const formattedTime = new Date(sentAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  
  return (
    <div className={`${styles.messageContainer} ${isCurrentUser ? styles.outgoing : styles.incoming}`}>
      {!isCurrentUser && showAvatar && (
        <div className={styles.avatar}>
          {message.sender?.photo || message.sender?.name?.charAt(0) || '?'}
        </div>
      )}
      <div className={styles.messageContent}>
        <div className={styles.message}>
          {messageType === 'IMAGE' && metadata?.url && (
            <div className={styles.imageContainer}>
              <img src={metadata.url} alt="Shared image" className={styles.messageImage} />
            </div>
          )}
          {content}
          <span className={styles.messageTime}>
            {formattedTime}
            {isCurrentUser && (
              <span className={styles.readStatus}>
                {isRead ? '✓✓' : '✓'}
              </span>
            )}
          </span>
        </div>
      </div>
    </div>
  );
};

// Typing indicator component
const TypingIndicator = ({ isTyping, userName }) => {
  if (!isTyping) return null;
  
  return (
    <div className={styles.typingIndicator}>
      <div className={styles.typingDots}>
        <span></span>
        <span></span>
        <span></span>
      </div>
      <div className={styles.typingText}>{userName || 'Someone'} is typing...</div>
    </div>
  );
};

// Main chat interface component
const ChatInterface = ({ userId, token, apiBaseUrl, conversationId, otherUser }) => {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isTyping, setIsTyping] = useState(false);
  const [otherUserTyping, setOtherUserTyping] = useState(false);
  const [otherUserOnline, setOtherUserOnline] = useState(false);
  const [socket, setSocket] = useState(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  
  const messagesEndRef = useRef(null);
  const messageListRef = useRef(null);
  const typingTimeoutRef = useRef(null);
  const router = useRouter();
  
  // Initialize socket connection
  useEffect(() => {
    if (!token || !userId || !conversationId) return;
    
    const socketInstance = io(apiBaseUrl, {
      auth: { token },
      transports: ['websocket'],
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    });
    
    socketInstance.on('connect', () => {
      console.log('Socket connected');
      // Join the conversation room
      socketInstance.emit('join-conversation', conversationId);
    });
    
    socketInstance.on('connect_error', (err) => {
      console.error('Socket connection error:', err);
      setError('Failed to connect to chat server. Please try again later.');
    });
    
    socketInstance.on('error', (err) => {
      console.error('Socket error:', err);
      setError(err.message || 'An error occurred in the chat.');
    });
    
    socketInstance.on('new-message', (message) => {
      setMessages(prevMessages => {
        // Check if message already exists
        if (prevMessages.some(m => m.id === message.id)) {
          return prevMessages;
        }
        return [...prevMessages, message];
      });
      
      // Reset typing indicator when a message is received
      setOtherUserTyping(false);
    });
    
    socketInstance.on('typing-status', (data) => {
      if (data.userId !== userId && data.conversationId === conversationId) {
        setOtherUserTyping(data.isTyping);
      }
    });
    
    socketInstance.on('messages-read', (data) => {
      if (data.conversationId === conversationId && data.readBy !== userId) {
        setMessages(prevMessages => 
          prevMessages.map(msg => 
            msg.senderId === userId ? { ...msg, isRead: true } : msg
          )
        );
      }
    });
    
    socketInstance.on('user-status', (data) => {
      if (data.userId === otherUser.id) {
        setOtherUserOnline(data.isOnline);
      }
    });
    
    setSocket(socketInstance);
    
    // Cleanup on unmount
    return () => {
      if (socketInstance) {
        socketInstance.emit('leave-conversation', conversationId);
        socketInstance.disconnect();
      }
    };
  }, [token, userId, conversationId, apiBaseUrl, otherUser?.id]);
  
  // Fetch messages
  useEffect(() => {
    if (!conversationId || !token) return;
    
    const fetchMessages = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${apiBaseUrl}/api/users/conversations/${conversationId}/messages?page=${page}&limit=20`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (!response.ok) {
          throw new Error('Failed to fetch messages');
        }
        
        const data = await response.json();
        
        if (data.success) {
          if (page === 1) {
            setMessages(data.messages);
          } else {
            setMessages(prevMessages => [...data.messages, ...prevMessages]);
          }
          
          setHasMore(data.pagination.currentPage < data.pagination.totalPages);
        } else {
          throw new Error(data.message || 'Failed to fetch messages');
        }
      } catch (error) {
        console.error('Error fetching messages:', error);
        setError(error.message || 'Failed to fetch messages');
      } finally {
        setLoading(false);
      }
    };
    
    fetchMessages();
  }, [conversationId, token, apiBaseUrl, page]);
  
  // Scroll to bottom when messages change
  useEffect(() => {
    if (messages.length > 0 && page === 1) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, page]);
  
  // Mark messages as read
  useEffect(() => {
    if (!socket || !conversationId || messages.length === 0) return;
    
    const unreadMessages = messages.filter(msg => 
      msg.receiverId === userId && !msg.isRead
    );
    
    if (unreadMessages.length > 0) {
      socket.emit('mark-read', {
        conversationId,
        messageIds: unreadMessages.map(msg => msg.id)
      });
    }
  }, [messages, socket, conversationId, userId]);
  
  // Handle sending a message
  const handleSendMessage = async (e) => {
    e.preventDefault();
    
    if (!newMessage.trim() || !socket) return;
    
    try {
      // Emit the message via socket
      socket.emit('send-message', {
        conversationId,
        content: newMessage,
        messageType: 'TEXT'
      });
      
      // Clear the input
      setNewMessage('');
      
      // Reset typing indicator
      handleStopTyping();
    } catch (error) {
      console.error('Error sending message:', error);
      setError('Failed to send message. Please try again.');
    }
  };
  
  // Handle typing indicator
  const handleTyping = () => {
    if (!socket) return;
    
    // Clear any existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    // Only emit if not already typing
    if (!isTyping) {
      setIsTyping(true);
      socket.emit('typing', {
        conversationId,
        isTyping: true
      });
    }
    
    // Set timeout to stop typing indicator after 2 seconds of inactivity
    typingTimeoutRef.current = setTimeout(handleStopTyping, 2000);
  };
  
  // Handle stop typing
  const handleStopTyping = () => {
    if (!socket) return;
    
    setIsTyping(false);
    socket.emit('typing', {
      conversationId,
      isTyping: false
    });
    
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
  };
  
  // Load more messages
  const handleLoadMore = () => {
    if (hasMore && !loading) {
      setPage(prevPage => prevPage + 1);
    }
  };
  
  // Handle input change
  const handleInputChange = (e) => {
    setNewMessage(e.target.value);
    handleTyping();
  };
  
  return (
    <div className={styles.chatInterface}>
      <div className={styles.chatHeader}>
        <div className={styles.userInfo}>
          <div className={styles.userAvatar}>
            {otherUser?.profilePicture ? (
              <img src={otherUser.profilePicture} alt={otherUser.name} />
            ) : (
              otherUser?.name?.charAt(0) || '?'
            )}
            {otherUserOnline && <span className={styles.onlineIndicator}></span>}
          </div>
          <div className={styles.userName}>
            <h3>{otherUser?.name || 'Chat'}</h3>
            <span className={styles.userStatus}>
              {otherUserOnline ? 'Online' : 'Offline'}
            </span>
          </div>
        </div>
        <div className={styles.chatActions}>
          <button 
            className={styles.backButton}
            onClick={() => router.push('/messages')}
          >
            ←
          </button>
        </div>
      </div>
      
      <div className={styles.messageList} ref={messageListRef}>
        {loading && page === 1 ? (
          <div className={styles.loadingContainer}>
            <div className={styles.loadingSpinner}></div>
            <p>Loading messages...</p>
          </div>
        ) : (
          <>
            {hasMore && (
              <div className={styles.loadMoreContainer}>
                <button 
                  className={styles.loadMoreButton}
                  onClick={handleLoadMore}
                  disabled={loading}
                >
                  {loading ? 'Loading...' : 'Load more messages'}
                </button>
              </div>
            )}
            
            {messages.length === 0 ? (
              <div className={styles.emptyState}>
                <p>No messages yet. Start the conversation!</p>
              </div>
            ) : (
              messages.map((message, index) => (
                <ChatMessage
                  key={message.id}
                  message={message}
                  isCurrentUser={message.senderId === userId}
                  showAvatar={index === 0 || messages[index - 1]?.senderId !== message.senderId}
                />
              ))
            )}
            
            <TypingIndicator 
              isTyping={otherUserTyping} 
              userName={otherUser?.name}
            />
            
            <div ref={messagesEndRef} />
          </>
        )}
      </div>
      
      {error && (
        <div className={styles.errorContainer}>
          <p>{error}</p>
          <button onClick={() => setError(null)}>Dismiss</button>
        </div>
      )}
      
      <form className={styles.messageForm} onSubmit={handleSendMessage}>
        <input
          type="text"
          value={newMessage}
          onChange={handleInputChange}
          placeholder="Type a message..."
          className={styles.messageInput}
        />
        <button 
          type="submit" 
          className={styles.sendButton}
          disabled={!newMessage.trim()}
        >
          Send
        </button>
      </form>
    </div>
  );
};

export default ChatInterface;
