import { useState } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  Grid,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemSecondaryAction,
  ListItemText,
  Switch,
  TextField,
  Tooltip,
  Typography,
  FormControlLabel,
  InputAdornment
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIcon,
  Category as CategoryIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { toast } from 'react-toastify';
import axiosInstance from '@/utils/axiosConfig';

const CategoryManager = ({ categories, refreshData }) => {
  const [openDialog, setOpenDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [currentCategory, setCurrentCategory] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    displayName: '',
    description: '',
    displayOrder: 0,
    icon: '',
    isActive: true,
    isRequired: false
  });
  const [errors, setErrors] = useState({});

  // Open dialog for creating a new category
  const handleAddCategory = () => {
    setCurrentCategory(null);
    setFormData({
      name: '',
      displayName: '',
      description: '',
      displayOrder: categories.length + 1,
      icon: '',
      isActive: true,
      isRequired: false
    });
    setErrors({});
    setOpenDialog(true);
  };

  // Open dialog for editing an existing category
  const handleEditCategory = (category) => {
    setCurrentCategory(category);
    setFormData({
      name: category.name,
      displayName: category.displayName,
      description: category.description || '',
      displayOrder: category.displayOrder,
      icon: category.icon || '',
      isActive: category.isActive,
      isRequired: category.isRequired
    });
    setErrors({});
    setOpenDialog(true);
  };

  // Open dialog for deleting a category
  const handleDeleteClick = (category) => {
    setCurrentCategory(category);
    setDeleteDialog(true);
  };

  // Close all dialogs
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setDeleteDialog(false);
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, checked } = e.target;
    setFormData({
      ...formData,
      [name]: name === 'isActive' || name === 'isRequired' ? checked : value
    });
    
    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };

  // Validate form data
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name) {
      newErrors.name = 'Name is required';
    } else if (!/^[a-z0-9_]+$/.test(formData.name)) {
      newErrors.name = 'Name must contain only lowercase letters, numbers, and underscores';
    }
    
    if (!formData.displayName) {
      newErrors.displayName = 'Display name is required';
    }
    
    if (formData.displayOrder === '' || isNaN(formData.displayOrder)) {
      newErrors.displayOrder = 'Display order must be a number';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Submit form data
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }
    
    try {
      if (currentCategory) {
        // Update existing category
        const response = await axiosInstance.put(
          `/api/admin/preference-config/categories/${currentCategory.id}`,
          formData
        );
        
        if (response.data.success) {
          toast.success('Category updated successfully');
          refreshData();
          handleCloseDialog();
        } else {
          toast.error(response.data.message || 'Failed to update category');
        }
      } else {
        // Create new category
        const response = await axiosInstance.post(
          '/api/admin/preference-config/categories',
          formData
        );
        
        if (response.data.success) {
          toast.success('Category created successfully');
          refreshData();
          handleCloseDialog();
        } else {
          toast.error(response.data.message || 'Failed to create category');
        }
      }
    } catch (error) {
      console.error('Error saving category:', error);
      toast.error(error.response?.data?.message || 'An error occurred while saving the category');
    }
  };

  // Delete a category
  const handleDeleteCategory = async () => {
    try {
      const response = await axiosInstance.delete(
        `/api/admin/preference-config/categories/${currentCategory.id}`
      );
      
      if (response.data.success) {
        toast.success('Category deleted successfully');
        refreshData();
        handleCloseDialog();
      } else {
        toast.error(response.data.message || 'Failed to delete category');
      }
    } catch (error) {
      console.error('Error deleting category:', error);
      toast.error(error.response?.data?.message || 'An error occurred while deleting the category');
    }
  };

  // Handle drag and drop for reordering categories
  const handleDragEnd = async (result) => {
    if (!result.destination) return;
    
    const items = Array.from(categories);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    
    // Update display order for all affected items
    const updatedItems = items.map((item, index) => ({
      ...item,
      displayOrder: index + 1
    }));
    
    // Update the UI immediately
    // In a real implementation, you would update the categories state here
    
    // Update the display order in the database
    try {
      // This would be a batch update in a real implementation
      for (const item of updatedItems) {
        if (item.displayOrder !== item.originalDisplayOrder) {
          await axiosInstance.put(
            `/api/admin/preference-config/categories/${item.id}`,
            { displayOrder: item.displayOrder }
          );
        }
      }
      
      toast.success('Categories reordered successfully');
      refreshData();
    } catch (error) {
      console.error('Error reordering categories:', error);
      toast.error('Failed to update category order');
      refreshData(); // Refresh to revert to original order
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h6">Preference Categories</Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleAddCategory}
        >
          Add Category
        </Button>
      </Box>

      <Card>
        <CardContent>
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="categories">
              {(provided) => (
                <List {...provided.droppableProps} ref={provided.innerRef}>
                  {categories.length === 0 ? (
                    <Typography variant="body2" color="textSecondary" sx={{ p: 2 }}>
                      No categories found. Click "Add Category" to create one.
                    </Typography>
                  ) : (
                    categories
                      .sort((a, b) => a.displayOrder - b.displayOrder)
                      .map((category, index) => (
                        <Draggable key={category.id} draggableId={category.id} index={index}>
                          {(provided) => (
                            <ListItem
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              divider
                            >
                              <ListItemIcon {...provided.dragHandleProps}>
                                <DragIcon />
                              </ListItemIcon>
                              <ListItemIcon>
                                <CategoryIcon />
                              </ListItemIcon>
                              <ListItemText
                                primary={
                                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    {category.displayName}
                                    {category.isRequired && (
                                      <Tooltip title="Required category">
                                        <InfoIcon fontSize="small" color="primary" sx={{ ml: 1 }} />
                                      </Tooltip>
                                    )}
                                  </Box>
                                }
                                secondary={
                                  <>
                                    <Typography variant="caption" component="span" color="textSecondary">
                                      {category.name}
                                    </Typography>
                                    {category.description && (
                                      <Typography variant="body2" color="textSecondary">
                                        {category.description}
                                      </Typography>
                                    )}
                                  </>
                                }
                              />
                              <ListItemSecondaryAction>
                                <FormControlLabel
                                  control={
                                    <Switch
                                      checked={category.isActive}
                                      onChange={async (e) => {
                                        try {
                                          const response = await axiosInstance.put(
                                            `/api/admin/preference-config/categories/${category.id}`,
                                            { isActive: e.target.checked }
                                          );
                                          
                                          if (response.data.success) {
                                            toast.success(`Category ${e.target.checked ? 'activated' : 'deactivated'} successfully`);
                                            refreshData();
                                          } else {
                                            toast.error(response.data.message || 'Failed to update category');
                                          }
                                        } catch (error) {
                                          console.error('Error updating category:', error);
                                          toast.error('Failed to update category status');
                                        }
                                      }}
                                      color="primary"
                                    />
                                  }
                                  label="Active"
                                />
                                <Tooltip title="Edit">
                                  <IconButton
                                    edge="end"
                                    aria-label="edit"
                                    onClick={() => handleEditCategory(category)}
                                  >
                                    <EditIcon />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip title="Delete">
                                  <IconButton
                                    edge="end"
                                    aria-label="delete"
                                    onClick={() => handleDeleteClick(category)}
                                  >
                                    <DeleteIcon />
                                  </IconButton>
                                </Tooltip>
                              </ListItemSecondaryAction>
                            </ListItem>
                          )}
                        </Draggable>
                      ))
                  )}
                  {provided.placeholder}
                </List>
              )}
            </Droppable>
          </DragDropContext>
        </CardContent>
      </Card>

      {/* Add/Edit Category Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {currentCategory ? 'Edit Category' : 'Add Category'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                name="name"
                label="Internal Name"
                value={formData.name}
                onChange={handleInputChange}
                fullWidth
                required
                error={!!errors.name}
                helperText={errors.name || 'Use lowercase letters, numbers, and underscores only'}
                disabled={currentCategory !== null} // Disable editing name for existing categories
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="displayName"
                label="Display Name"
                value={formData.displayName}
                onChange={handleInputChange}
                fullWidth
                required
                error={!!errors.displayName}
                helperText={errors.displayName}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="description"
                label="Description"
                value={formData.description}
                onChange={handleInputChange}
                fullWidth
                multiline
                rows={2}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="displayOrder"
                label="Display Order"
                type="number"
                value={formData.displayOrder}
                onChange={handleInputChange}
                fullWidth
                error={!!errors.displayOrder}
                helperText={errors.displayOrder}
                InputProps={{
                  inputProps: { min: 0 }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="icon"
                label="Icon"
                value={formData.icon}
                onChange={handleInputChange}
                fullWidth
                helperText="Icon name or path (optional)"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleInputChange}
                    color="primary"
                  />
                }
                label="Active"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    name="isRequired"
                    checked={formData.isRequired}
                    onChange={handleInputChange}
                    color="primary"
                  />
                }
                label="Required"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} color="primary" variant="contained">
            {currentCategory ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog} onClose={handleCloseDialog}>
        <DialogTitle>Delete Category</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the category "{currentCategory?.displayName}"?
            This will also delete all fields, options, and importance settings associated with this category.
            This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleDeleteCategory} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CategoryManager;
