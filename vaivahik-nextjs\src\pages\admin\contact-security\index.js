import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  Alert,
  CircularProgress,
  Switch,
  FormControlLabel,
  Tooltip,
  Avatar,
  Stack,
  Divider,
  Paper,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Badge,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  LinearProgress
} from '@mui/material';
import {
  Security as SecurityIcon,
  Shield as ShieldIcon,
  Warning as WarningIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  Search as SearchIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  Phone as PhoneIcon,
  FilterList as FilterListIcon,
  Visibility as VisibilityIcon,
  Assessment as AssessmentIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  Flag as FlagIcon,
  ContactPhone as ContactPhoneIcon,
  VerifiedUser as VerifiedUserIcon,
  Report as ReportIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { adminGet, adminPost, adminPut, adminDelete } from '@/services/adminApiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';

// Dynamic import for EnhancedAdminLayout to avoid SSR issues
const EnhancedAdminLayout = dynamic(() => import('@/components/admin/EnhancedAdminLayout'), {
  ssr: false
});

export default function ContactRevealSecurity() {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({});
  const [securityLogs, setSecurityLogs] = useState([]);
  const [riskAssessments, setRiskAssessments] = useState([]);
  const [securitySettings, setSecuritySettings] = useState({});
  const [flaggedUsers, setFlaggedUsers] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [filters, setFilters] = useState({
    riskLevel: '',
    status: '',
    search: ''
  });
  const [settingsDialogOpen, setSettingsDialogOpen] = useState(false);
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);

  // Risk level options
  const riskLevelOptions = [
    { value: 'LOW', label: 'Low Risk', color: 'success', threshold: '0-30' },
    { value: 'MEDIUM', label: 'Medium Risk', color: 'warning', threshold: '31-60' },
    { value: 'HIGH', label: 'High Risk', color: 'error', threshold: '61-100' }
  ];

  // Security flag types
  const securityFlagTypes = [
    { value: 'HIGH_RISK_PROFILE', label: 'High Risk Profile', icon: '🚨' },
    { value: 'INCOMPLETE_PROFILE', label: 'Incomplete Profile', icon: '📝' },
    { value: 'NO_DOCUMENT_VERIFICATION', label: 'No Document Verification', icon: '📄' },
    { value: 'NEW_ACCOUNT', label: 'New Account', icon: '🆕' },
    { value: 'HIGH_CONTACT_ACCESS_FREQUENCY', label: 'High Access Frequency', icon: '📞' },
    { value: 'SUSPICIOUS_INTERACTION_PATTERN', label: 'Suspicious Interactions', icon: '🔍' },
    { value: 'NO_PROFILE_PHOTO', label: 'No Profile Photo', icon: '📷' },
    { value: 'SUSPICIOUS_PHONE_PATTERN', label: 'Suspicious Phone', icon: '📱' },
    { value: 'SUSPICIOUS_EMAIL_DOMAIN', label: 'Suspicious Email', icon: '📧' },
    { value: 'GEOGRAPHIC_INCONSISTENCY', label: 'Geographic Issues', icon: '🌍' },
    { value: 'SUSPICIOUS_BEHAVIOR', label: 'Suspicious Behavior', icon: '⚠️' }
  ];

  useEffect(() => {
    fetchStats();
    fetchSecurityLogs();
    fetchRiskAssessments();
    fetchSecuritySettings();
    fetchFlaggedUsers();
  }, [page, rowsPerPage, filters]);

  const fetchStats = async () => {
    try {
      // Mock stats for now - can be implemented in backend later
      setStats({
        totalContactReveals: 1250,
        blockedAttempts: 89,
        highRiskUsers: 23,
        averageRiskScore: 32.5,
        securityIncidents: 12,
        falsePositives: 3
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const fetchSecurityLogs = async () => {
    try {
      setLoading(true);
      // Mock data for now - replace with actual API call
      const mockLogs = [
        {
          id: '1',
          userId: 'user123',
          userName: 'John Doe',
          action: 'CONTACT_REVEAL_BLOCKED',
          reason: 'HIGH_RISK_PROFILE',
          riskScore: 85,
          timestamp: new Date().toISOString(),
          ipAddress: '***********',
          platform: 'WEB'
        },
        {
          id: '2',
          userId: 'user456',
          userName: 'Jane Smith',
          action: 'CONTACT_REVEAL_APPROVED',
          reason: 'LOW_RISK_APPROVED',
          riskScore: 25,
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          ipAddress: '***********',
          platform: 'MOBILE'
        }
      ];
      setSecurityLogs(mockLogs);
    } catch (error) {
      console.error('Error fetching security logs:', error);
      toast.error('Error fetching security logs');
    } finally {
      setLoading(false);
    }
  };

  const fetchRiskAssessments = async () => {
    try {
      // Mock data for now
      const mockAssessments = [
        {
          id: '1',
          userId: 'user789',
          userName: 'Mike Johnson',
          riskScore: 75,
          riskLevel: 'HIGH',
          flags: ['HIGH_CONTACT_ACCESS_FREQUENCY', 'SUSPICIOUS_INTERACTION_PATTERN'],
          lastAssessment: new Date().toISOString(),
          status: 'UNDER_REVIEW'
        }
      ];
      setRiskAssessments(mockAssessments);
    } catch (error) {
      console.error('Error fetching risk assessments:', error);
    }
  };

  const fetchSecuritySettings = async () => {
    try {
      // Mock settings for now
      setSecuritySettings({
        riskThresholds: {
          low: 30,
          medium: 60,
          high: 80
        },
        autoBlockEnabled: true,
        manualReviewRequired: true,
        maxDailyContactReveals: 10,
        newAccountRestrictionDays: 7,
        requireDocumentVerification: true,
        enableGeographicChecks: true,
        enableBehaviorAnalysis: true
      });
    } catch (error) {
      console.error('Error fetching security settings:', error);
    }
  };

  const fetchFlaggedUsers = async () => {
    try {
      // Mock data for now
      const mockFlaggedUsers = [
        {
          id: 'user999',
          name: 'Suspicious User',
          email: '<EMAIL>',
          phone: '+91 99999 99999',
          riskScore: 90,
          flags: ['SUSPICIOUS_EMAIL_DOMAIN', 'HIGH_CONTACT_ACCESS_FREQUENCY'],
          flaggedAt: new Date().toISOString(),
          status: 'BLOCKED'
        }
      ];
      setFlaggedUsers(mockFlaggedUsers);
    } catch (error) {
      console.error('Error fetching flagged users:', error);
    }
  };

  const handleReviewUser = (user) => {
    setSelectedUser(user);
    setReviewDialogOpen(true);
  };

  const handleUserAction = async (userId, action, reason = '') => {
    try {
      // Mock API call for now
      console.log(`${action} user ${userId} with reason: ${reason}`);
      toast.success(`User ${action.toLowerCase()} successfully`);
      setReviewDialogOpen(false);
      setSelectedUser(null);
      fetchFlaggedUsers();
      fetchStats();
    } catch (error) {
      console.error('Error performing user action:', error);
      toast.error('Error performing action');
    }
  };

  const getRiskLevelChip = (riskScore) => {
    let config = riskLevelOptions[0]; // Default to LOW
    if (riskScore > 60) config = riskLevelOptions[2]; // HIGH
    else if (riskScore > 30) config = riskLevelOptions[1]; // MEDIUM

    return (
      <Chip
        label={`${config.label} (${riskScore})`}
        color={config.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const getSecurityFlagChip = (flagType) => {
    const flagConfig = securityFlagTypes.find(opt => opt.value === flagType) || securityFlagTypes[0];
    return (
      <Chip
        label={`${flagConfig.icon} ${flagConfig.label}`}
        size="small"
        variant="outlined"
        color="warning"
      />
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const TabPanel = ({ children, value, index, ...other }) => (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`security-tabpanel-${index}`}
      aria-labelledby={`security-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );

  return (
    <EnhancedAdminLayout title="Contact Reveal Security">
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Contact Reveal Security
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={() => {
                fetchStats();
                fetchSecurityLogs();
                fetchRiskAssessments();
                fetchFlaggedUsers();
              }}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={<SettingsIcon />}
              onClick={() => setSettingsDialogOpen(true)}
            >
              Security Settings
            </Button>
          </Box>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Contact Reveals
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.totalContactReveals || 0}
                    </Typography>
                  </Box>
                  <ContactPhoneIcon color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Blocked Attempts
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.blockedAttempts || 0}
                    </Typography>
                  </Box>
                  <BlockIcon color="error" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      High Risk Users
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.highRiskUsers || 0}
                    </Typography>
                  </Box>
                  <WarningIcon color="warning" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Avg Risk Score
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.averageRiskScore?.toFixed(1) || 0}
                    </Typography>
                  </Box>
                  <AssessmentIcon color="info" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Security Incidents
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.securityIncidents || 0}
                    </Typography>
                  </Box>
                  <SecurityIcon color="error" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      False Positives
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.falsePositives || 0}
                    </Typography>
                  </Box>
                  <CheckCircleIcon color="success" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Tabs */}
        <Paper sx={{ mb: 3 }}>
          <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
            <Tab label="Security Logs" icon={<SecurityIcon />} />
            <Tab label="Risk Assessments" icon={<AssessmentIcon />} />
            <Tab label="Flagged Users" icon={<FlagIcon />} />
            <Tab label="Settings" icon={<SettingsIcon />} />
          </Tabs>
        </Paper>

        {/* Tab Panels */}
        <TabPanel value={activeTab} index={0}>
          {/* Security Logs Table */}
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>User</TableCell>
                  <TableCell>Action</TableCell>
                  <TableCell>Risk Score</TableCell>
                  <TableCell>Reason</TableCell>
                  <TableCell>Platform</TableCell>
                  <TableCell>Timestamp</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                ) : securityLogs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <Typography variant="body2" color="text.secondary">
                        No security logs found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  securityLogs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Avatar sx={{ width: 32, height: 32 }}>
                            {log.userName?.charAt(0)}
                          </Avatar>
                          <Box>
                            <Typography variant="body2" fontWeight="600">
                              {log.userName}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {log.userId}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={log.action.replace('CONTACT_REVEAL_', '')}
                          color={log.action.includes('BLOCKED') ? 'error' : 'success'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {getRiskLevelChip(log.riskScore)}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {log.reason.replace(/_/g, ' ')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip label={log.platform} size="small" variant="outlined" />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDate(log.timestamp)}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        {/* Risk Assessments Tab */}
        <TabPanel value={activeTab} index={1}>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>User</TableCell>
                  <TableCell>Risk Score</TableCell>
                  <TableCell>Security Flags</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Last Assessment</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {riskAssessments.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <Typography variant="body2" color="text.secondary">
                        No risk assessments found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  riskAssessments.map((assessment) => (
                    <TableRow key={assessment.id}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Avatar sx={{ width: 32, height: 32 }}>
                            {assessment.userName?.charAt(0)}
                          </Avatar>
                          <Typography variant="body2" fontWeight="600">
                            {assessment.userName}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {getRiskLevelChip(assessment.riskScore)}
                          <LinearProgress
                            variant="determinate"
                            value={assessment.riskScore}
                            sx={{ width: 60, height: 6 }}
                            color={assessment.riskScore > 60 ? 'error' : assessment.riskScore > 30 ? 'warning' : 'success'}
                          />
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {assessment.flags?.map((flag, index) => (
                            <Box key={index}>
                              {getSecurityFlagChip(flag)}
                            </Box>
                          ))}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={assessment.status}
                          color={assessment.status === 'UNDER_REVIEW' ? 'warning' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {formatDate(assessment.lastAssessment)}
                      </TableCell>
                      <TableCell>
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => handleReviewUser(assessment)}
                        >
                          Review
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        {/* Flagged Users Tab */}
        <TabPanel value={activeTab} index={2}>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>User</TableCell>
                  <TableCell>Contact Info</TableCell>
                  <TableCell>Risk Score</TableCell>
                  <TableCell>Security Flags</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Flagged Date</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {flaggedUsers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <Typography variant="body2" color="text.secondary">
                        No flagged users found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  flaggedUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Avatar sx={{ width: 32, height: 32 }}>
                            {user.name?.charAt(0)}
                          </Avatar>
                          <Typography variant="body2" fontWeight="600">
                            {user.name}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2">{user.email}</Typography>
                          <Typography variant="body2">{user.phone}</Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        {getRiskLevelChip(user.riskScore)}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {user.flags?.map((flag, index) => (
                            <Box key={index}>
                              {getSecurityFlagChip(flag)}
                            </Box>
                          ))}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={user.status}
                          color={user.status === 'BLOCKED' ? 'error' : 'warning'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {formatDate(user.flaggedAt)}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => handleReviewUser(user)}
                          >
                            Review
                          </Button>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        {/* Settings Tab */}
        <TabPanel value={activeTab} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Risk Thresholds
                  </Typography>
                  <Stack spacing={2}>
                    <TextField
                      label="Low Risk Threshold"
                      type="number"
                      value={securitySettings.riskThresholds?.low || 30}
                      size="small"
                      InputProps={{ endAdornment: '%' }}
                    />
                    <TextField
                      label="Medium Risk Threshold"
                      type="number"
                      value={securitySettings.riskThresholds?.medium || 60}
                      size="small"
                      InputProps={{ endAdornment: '%' }}
                    />
                    <TextField
                      label="High Risk Threshold"
                      type="number"
                      value={securitySettings.riskThresholds?.high || 80}
                      size="small"
                      InputProps={{ endAdornment: '%' }}
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Security Controls
                  </Typography>
                  <Stack spacing={2}>
                    <FormControlLabel
                      control={<Switch checked={securitySettings.autoBlockEnabled || false} />}
                      label="Auto Block High Risk Users"
                    />
                    <FormControlLabel
                      control={<Switch checked={securitySettings.manualReviewRequired || false} />}
                      label="Manual Review Required"
                    />
                    <FormControlLabel
                      control={<Switch checked={securitySettings.requireDocumentVerification || false} />}
                      label="Require Document Verification"
                    />
                    <FormControlLabel
                      control={<Switch checked={securitySettings.enableGeographicChecks || false} />}
                      label="Enable Geographic Checks"
                    />
                    <FormControlLabel
                      control={<Switch checked={securitySettings.enableBehaviorAnalysis || false} />}
                      label="Enable Behavior Analysis"
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Review User Dialog */}
        <Dialog open={reviewDialogOpen} onClose={() => setReviewDialogOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>Review User Security Assessment</DialogTitle>
          <DialogContent>
            {selectedUser && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  User Information
                </Typography>
                <Grid container spacing={2} sx={{ mb: 3 }}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Name:</Typography>
                    <Typography variant="body1">{selectedUser.name || selectedUser.userName}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Risk Score:</Typography>
                    {getRiskLevelChip(selectedUser.riskScore)}
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">Security Flags:</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                      {selectedUser.flags?.map((flag, index) => (
                        <Box key={index}>
                          {getSecurityFlagChip(flag)}
                        </Box>
                      ))}
                    </Box>
                  </Grid>
                </Grid>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Recommended Actions
                </Typography>
                <Alert severity={selectedUser.riskScore > 60 ? 'error' : 'warning'} sx={{ mb: 2 }}>
                  {selectedUser.riskScore > 60
                    ? 'High risk user detected. Consider blocking contact reveal access.'
                    : 'Medium risk user. Additional verification may be required.'
                  }
                </Alert>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setReviewDialogOpen(false)}>Cancel</Button>
            <Button
              onClick={() => handleUserAction(selectedUser?.id, 'APPROVE')}
              color="success"
              variant="outlined"
            >
              Approve
            </Button>
            <Button
              onClick={() => handleUserAction(selectedUser?.id, 'BLOCK')}
              color="error"
              variant="contained"
            >
              Block
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </EnhancedAdminLayout>
  );
}
