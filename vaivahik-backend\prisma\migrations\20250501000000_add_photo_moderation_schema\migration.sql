-- Add aiFlags and aiConfidence fields to Photo model
ALTER TABLE "photos" ADD COLUMN "ai_flags" TEXT;
ALTER TABLE "photos" ADD COLUMN "ai_confidence" FLOAT;

-- Create ModerationLog model for tracking AI decisions
CREATE TABLE "moderation_logs" (
    "id" TEXT NOT NULL,
    "photo_id" TEXT NOT NULL,
    "ai_decision" TEXT NOT NULL,
    "ai_flags" TEXT,
    "ai_confidence" FLOAT,
    "details" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "moderation_logs_pkey" PRIMARY KEY ("id")
);

-- Add foreign key constraint
ALTER TABLE "moderation_logs" ADD CONSTRAINT "moderation_logs_photo_id_fkey" FOREIGN KEY ("photo_id") REFERENCES "photos"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Add index for faster queries
CREATE INDEX "moderation_logs_photo_id_idx" ON "moderation_logs"("photo_id");
