{"success": true, "message": "Features fetched successfully", "features": [{"id": "feature-1", "name": "view-contacts", "displayName": "View Contact Details", "description": "View phone numbers and email addresses of other users", "category": "COMMUNICATION", "isActive": true, "accessRules": [{"userTier": "BASIC", "isEnabled": false}, {"userTier": "VERIFIED", "isEnabled": true, "dailyLimit": 5}, {"userTier": "PREMIUM", "isEnabled": true}]}, {"id": "feature-2", "name": "advanced-search", "displayName": "Advanced Search Filters", "description": "Use additional filters like education, profession, and income", "category": "SEARCH", "isActive": true, "accessRules": [{"userTier": "BASIC", "isEnabled": false}, {"userTier": "VERIFIED", "isEnabled": true}, {"userTier": "PREMIUM", "isEnabled": true}]}, {"id": "feature-3", "name": "priority-search", "displayName": "Priority in Search Results", "description": "Appear higher in search results of other users", "category": "VISIBILITY", "isActive": true, "accessRules": [{"userTier": "BASIC", "isEnabled": false}, {"userTier": "VERIFIED", "isEnabled": false}, {"userTier": "PREMIUM", "isEnabled": true}]}, {"id": "feature-4", "name": "unlimited-messages", "displayName": "Unlimited Messages", "description": "Send unlimited messages to other users", "category": "COMMUNICATION", "isActive": true, "accessRules": [{"userTier": "BASIC", "isEnabled": true, "dailyLimit": 10}, {"userTier": "VERIFIED", "isEnabled": true, "dailyLimit": 30}, {"userTier": "PREMIUM", "isEnabled": true}]}, {"id": "feature-5", "name": "horoscope-matching", "displayName": "Horoscope Matching", "description": "View detailed horoscope compatibility analysis", "category": "MATCHING", "isActive": true, "accessRules": [{"userTier": "BASIC", "isEnabled": false}, {"userTier": "VERIFIED", "isEnabled": false}, {"userTier": "PREMIUM", "isEnabled": true}]}, {"id": "feature-6", "name": "profile-highlight", "displayName": "Profile Highlighting", "description": "Make your profile stand out with special highlighting", "category": "VISIBILITY", "isActive": true, "accessRules": [{"userTier": "BASIC", "isEnabled": false}, {"userTier": "VERIFIED", "isEnabled": false}, {"userTier": "PREMIUM", "isEnabled": true}]}], "pagination": {"page": 1, "limit": 10, "total": 6, "totalPages": 1}}