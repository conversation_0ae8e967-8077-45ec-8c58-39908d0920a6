<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin <PERSON>gin - Vai<PERSON><PERSON>k <PERSON>min</title>
    <link rel="stylesheet" href="css/admin-styles.css">
    <link rel="icon" type="image/png" href="img/favicon.png">
    <style>
        /* Additional styles specific to login page */
        body {
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }

        .login-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            padding: 40px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .login-logo-icon {
            background-color: var(--secondary);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            font-weight: bold;
            color: white;
            margin-right: 10px;
        }

        .login-logo-text {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary);
        }

        .login-title {
            font-size: 1.2rem;
            color: #555;
            margin-top: 0;
        }

        .login-form {
            display: flex;
            flex-direction: column;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid var(--border);
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(94, 53, 177, 0.1);
        }

        .login-button {
            background-color: var(--primary);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-top: 10px;
        }

        .login-button:hover {
            background-color: var(--primary-dark);
        }

        .login-footer {
            text-align: center;
            margin-top: 25px;
            font-size: 0.9rem;
            color: #777;
        }

        .error-message {
            color: var(--danger);
            background-color: rgba(244, 67, 54, 0.1);
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-logo">
                <div class="login-logo-icon">V</div>
                <div class="login-logo-text">Vaivahik Admin</div>
            </div>
            <h1 class="login-title">Sign in to your account</h1>
        </div>

        <div class="error-message" id="errorMessage">
            Invalid username or password. Please try again.
        </div>

        <form class="login-form" id="loginForm">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" class="form-control" placeholder="Enter your username" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" class="form-control" placeholder="Enter your password" required>
            </div>
            <button type="submit" class="login-button">Sign In</button>
        </form>

        <div class="login-footer">
            &copy; 2025 Vaivahik. All rights reserved.
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            // Simple validation for demo purposes
            if (username === 'admin' && password === 'admin123') {
                // Successful login - redirect to dashboard
                window.location.href = 'admin-dashboard.html';
            } else {
                // Show error message
                document.getElementById('errorMessage').style.display = 'block';
            }
        });
    </script>
</body>
</html>
