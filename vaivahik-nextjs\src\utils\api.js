/**
 * API Utility Functions
 * Centralized API handling with error management and authentication
 */

import axios from 'axios';
import { toast } from 'react-toastify';

// API Base URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

// Create axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // Add admin API key for admin requests
    if (config.url?.includes('/admin/')) {
      const adminToken = localStorage.getItem('adminToken');
      if (adminToken) {
        config.headers['X-Admin-Token'] = adminToken;
      }
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          // Unauthorized - redirect to login
          if (typeof window !== 'undefined') {
            localStorage.removeItem('authToken');
            sessionStorage.removeItem('authToken');
            window.location.href = '/login';
          }
          break;
        case 403:
          toast.error('Access denied. Insufficient permissions.');
          break;
        case 404:
          toast.error('Resource not found.');
          break;
        case 429:
          toast.error('Too many requests. Please try again later.');
          break;
        case 500:
          toast.error('Server error. Please try again later.');
          break;
        default:
          toast.error(data?.message || 'An error occurred');
      }
    } else if (error.request) {
      // Network error
      toast.error('Network error. Please check your connection.');
    } else {
      // Other error
      toast.error('An unexpected error occurred.');
    }
    
    return Promise.reject(error);
  }
);

/**
 * Generic API request function
 * @param {string} method - HTTP method
 * @param {string} url - API endpoint
 * @param {Object} data - Request data
 * @param {Object} config - Additional config
 * @returns {Promise} API response
 */
export const apiRequest = async (method, url, data = null, config = {}) => {
  try {
    const response = await apiClient({
      method,
      url,
      data,
      ...config,
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * GET request
 * @param {string} url - API endpoint
 * @param {Object} config - Additional config
 * @returns {Promise} API response
 */
export const apiGet = (url, config = {}) => {
  return apiRequest('GET', url, null, config);
};

/**
 * POST request
 * @param {string} url - API endpoint
 * @param {Object} data - Request data
 * @param {Object} config - Additional config
 * @returns {Promise} API response
 */
export const apiPost = (url, data, config = {}) => {
  return apiRequest('POST', url, data, config);
};

/**
 * PUT request
 * @param {string} url - API endpoint
 * @param {Object} data - Request data
 * @param {Object} config - Additional config
 * @returns {Promise} API response
 */
export const apiPut = (url, data, config = {}) => {
  return apiRequest('PUT', url, data, config);
};

/**
 * PATCH request
 * @param {string} url - API endpoint
 * @param {Object} data - Request data
 * @param {Object} config - Additional config
 * @returns {Promise} API response
 */
export const apiPatch = (url, data, config = {}) => {
  return apiRequest('PATCH', url, data, config);
};

/**
 * DELETE request
 * @param {string} url - API endpoint
 * @param {Object} config - Additional config
 * @returns {Promise} API response
 */
export const apiDelete = (url, config = {}) => {
  return apiRequest('DELETE', url, null, config);
};

/**
 * Upload file
 * @param {string} url - API endpoint
 * @param {FormData} formData - File data
 * @param {Function} onProgress - Progress callback
 * @returns {Promise} API response
 */
export const apiUpload = (url, formData, onProgress = null) => {
  const config = {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  };
  
  if (onProgress) {
    config.onUploadProgress = (progressEvent) => {
      const percentCompleted = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total
      );
      onProgress(percentCompleted);
    };
  }
  
  return apiRequest('POST', url, formData, config);
};

/**
 * Download file
 * @param {string} url - API endpoint
 * @param {string} filename - File name
 * @returns {Promise} Download response
 */
export const apiDownload = async (url, filename) => {
  try {
    const response = await apiClient({
      method: 'GET',
      url,
      responseType: 'blob',
    });
    
    // Create download link
    const downloadUrl = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(downloadUrl);
    
    return response;
  } catch (error) {
    throw error;
  }
};

/**
 * Admin API requests with special handling
 */
export const adminApi = {
  get: (url, config = {}) => apiGet(`/admin${url}`, config),
  post: (url, data, config = {}) => apiPost(`/admin${url}`, data, config),
  put: (url, data, config = {}) => apiPut(`/admin${url}`, data, config),
  patch: (url, data, config = {}) => apiPatch(`/admin${url}`, data, config),
  delete: (url, config = {}) => apiDelete(`/admin${url}`, config),
};

/**
 * MCP Server API requests
 */
export const mcpApi = {
  connect: () => apiPost('/mcp/connect'),
  disconnect: () => apiPost('/mcp/disconnect'),
  sendMessage: (message) => apiPost('/mcp/message', { message }),
  getStatus: () => apiGet('/mcp/status'),
  getLogs: (params = {}) => apiGet('/mcp/logs', { params }),
};

/**
 * Utility functions
 */
export const utils = {
  /**
   * Build query string from object
   * @param {Object} params - Query parameters
   * @returns {string} Query string
   */
  buildQueryString: (params) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        searchParams.append(key, value);
      }
    });
    return searchParams.toString();
  },
  
  /**
   * Handle API error
   * @param {Error} error - Error object
   * @param {string} defaultMessage - Default error message
   */
  handleError: (error, defaultMessage = 'An error occurred') => {
    const message = error.response?.data?.message || error.message || defaultMessage;
    toast.error(message);
    console.error('API Error:', error);
  },
  
  /**
   * Format API response
   * @param {Object} response - API response
   * @returns {Object} Formatted response
   */
  formatResponse: (response) => {
    return {
      success: response.success || false,
      data: response.data || response,
      message: response.message || '',
      meta: response.meta || {},
    };
  },
};

export default apiClient;
