/**
 * Mock Users API Endpoint
 * 
 * This endpoint is only available in development mode and provides
 * a list of available mock users for authentication.
 */

import { getMockUsers } from '@/utils/mockAuth';

export default function handler(req, res) {
  // Only allow this endpoint in development mode
  if (process.env.NODE_ENV !== 'development') {
    return res.status(404).json({ 
      success: false, 
      message: 'This endpoint is only available in development mode' 
    });
  }

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    });
  }

  try {
    // Get all mock users
    const users = getMockUsers();

    // Return success response
    return res.status(200).json({
      success: true,
      users
    });
  } catch (error) {
    console.error('Error fetching mock users:', error);
    return res.status(500).json({
      success: false,
      message: 'Error fetching mock users',
      error: error.message
    });
  }
}
