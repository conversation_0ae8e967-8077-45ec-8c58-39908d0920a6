/* Admin Panel Styles */

body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Sidebar styles */
#sidebar {
    min-height: 100vh;
    background-color: #343a40;
    padding-top: 20px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

#sidebar .nav-link {
    color: #ced4da;
    padding: 10px 15px;
    margin-bottom: 5px;
    border-radius: 5px;
    transition: all 0.3s;
}

#sidebar .nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
}

#sidebar .nav-link.active {
    color: #fff;
    background-color: #6f42c1;
}

/* Main content styles */
main {
    padding-top: 20px;
    padding-bottom: 20px;
}

.card {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    border: none;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
}

/* Feature management styles */
.feature-card {
    transition: all 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Form styles */
.form-control, .form-select {
    border-radius: 6px;
    padding: 10px 15px;
    border: 1px solid #ced4da;
}

.form-control:focus, .form-select:focus {
    border-color: #6f42c1;
    box-shadow: 0 0 0 0.25rem rgba(111, 66, 193, 0.25);
}

/* Button styles */
.btn-primary {
    background-color: #6f42c1;
    border-color: #6f42c1;
}

.btn-primary:hover {
    background-color: #5e35b1;
    border-color: #5e35b1;
}

.btn-outline-primary {
    color: #6f42c1;
    border-color: #6f42c1;
}

.btn-outline-primary:hover {
    background-color: #6f42c1;
    border-color: #6f42c1;
}

/* Modal styles */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

/* Tab styles */
.nav-tabs .nav-link {
    color: #495057;
    border: none;
    padding: 10px 20px;
    border-radius: 0;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    color: #6f42c1;
    background-color: #fff;
    border-bottom: 2px solid #6f42c1;
}

/* Switch styles */
.form-check-input:checked {
    background-color: #6f42c1;
    border-color: #6f42c1;
}

/* Footer styles */
footer {
    border-top: 1px solid #e9ecef;
    padding-top: 20px;
    margin-top: 40px;
    color: #6c757d;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #sidebar {
        min-height: auto;
    }
    
    .card-columns {
        column-count: 1;
    }
}
