// src/routes/chat.routes.js

const express = require('express');
const router = express.Router();
const chatController = require('../controllers/chat.controller');
const { authenticateToken } = require('../middleware/auth.middleware');
const { featureAccessMiddleware, trackFeatureUsage, applyAccessLimits } = require('../middleware/featureAccess.middleware');

// Get all conversations
router.get('/conversations', 
    authenticateToken, 
    featureAccessMiddleware, 
    applyAccessLimits, 
    chatController.getConversations
);

// Start a new conversation
router.post('/conversations', 
    authenticateToken, 
    featureAccessMiddleware, 
    trackFeatureUsage, 
    chatController.startConversation
);

// Delete a conversation
router.delete('/conversations/:conversationId', 
    authenticateToken, 
    chatController.deleteConversation
);

// Get messages for a conversation
router.get('/conversations/:conversationId/messages', 
    authenticateToken, 
    featureAccessMiddleware, 
    applyAccessLimits, 
    chatController.getMessages
);

// Send a message in a conversation
router.post('/conversations/:conversationId/messages', 
    authenticateToken, 
    featureAccessMiddleware, 
    trackFeatureUsage, 
    chatController.sendMessage
);

// Mark messages as read
router.put('/conversations/:conversationId/read', 
    authenticateToken, 
    chatController.markMessagesAsRead
);

// Get unread message count
router.get('/unread-messages', 
    authenticateToken, 
    chatController.getUnreadMessageCount
);

// Get chat settings
router.get('/chat-settings', 
    authenticateToken, 
    chatController.getChatSettings
);

module.exports = router;
