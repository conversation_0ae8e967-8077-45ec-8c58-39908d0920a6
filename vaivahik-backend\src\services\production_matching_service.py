"""
Production Matching Service for Matrimony App

This module provides a production-ready matching service that integrates
all the optimizations from Phase 4.
"""

import os
import json
import asyncio
import logging
import time
import torch
import numpy as np
from prisma.client import PrismaClient
from datetime import datetime, timedelta

from .model_quantizer import ModelQuantizer
from .embedding_service import EmbeddingService
from .batch_processor import BatchProcessor
from .performance_monitor import PerformanceMonitor
from .redis_cache import RedisCache
from .model_interpreter import ModelInterpreter
from .ab_testing import ABTestingFramework

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProductionMatchingService:
    """Production-ready matching service for matrimony app"""
    
    def __init__(self):
        """Initialize the production matching service"""
        # Load settings
        self.settings = self._load_settings()
        
        # Initialize Prisma client
        self.prisma = PrismaClient()
        
        # Initialize Redis cache
        redis_config = self.settings.get('redis', {})
        self.redis_cache = RedisCache(redis_config)
        
        # Initialize embedding service
        embedding_config = self.settings.get('embedding', {})
        self.embedding_service = EmbeddingService(redis_cache=self.redis_cache, config=embedding_config)
        
        # Initialize batch processor
        batch_config = self.settings.get('batch', {})
        self.batch_processor = BatchProcessor(
            embedding_service=self.embedding_service,
            redis_cache=self.redis_cache,
            config=batch_config
        )
        
        # Initialize performance monitor
        monitor_config = self.settings.get('monitor', {})
        self.performance_monitor = PerformanceMonitor(
            redis_cache=self.redis_cache,
            config=monitor_config
        )
        
        # Initialize A/B testing framework
        ab_config = self.settings.get('ab_testing', {})
        self.ab_testing = ABTestingFramework(ab_config, self.redis_cache)
        
        # Initialize model interpreter
        self.interpreter = ModelInterpreter(self.embedding_service.model)
        
        # Start background tasks
        self._start_background_tasks()
    
    def _load_settings(self):
        """Load service settings"""
        try:
            settings_path = os.path.join(os.path.dirname(__file__), '../../config/production_settings.json')
            if os.path.exists(settings_path):
                with open(settings_path, 'r') as f:
                    return json.load(f)
            
            # Default settings
            return {
                'general': {
                    'matchingModel': 'TWO_TOWER',
                    'defaultModelId': 'production',
                    'minMatchScore': 50,
                    'maxMatchesPerUser': 100,
                    'cacheEnabled': True,
                    'cacheTTL': 3600  # 1 hour in seconds
                },
                'redis': {
                    'default_ttl': 86400,
                    'embedding_ttl': 604800,
                    'match_ttl': 3600,
                    'enabled': True
                },
                'embedding': {
                    'precompute_interval': 86400,
                    'precompute_limit': 1000,
                    'batch_size': 64,
                    'use_quantized_model': True,
                    'model_path': os.path.join(os.path.dirname(__file__), '../../models/quantized_model.pt')
                },
                'batch': {
                    'batch_size': 64,
                    'processing_interval': 300,
                    'max_queue_size': 1000
                },
                'monitor': {
                    'monitoring_interval': 300,
                    'metrics_ttl': 604800
                },
                'ab_testing': {
                    'enabled': True
                }
            }
        except Exception as e:
            logger.error(f"Error loading settings: {str(e)}")
            return {
                'general': {
                    'matchingModel': 'TWO_TOWER',
                    'defaultModelId': 'production',
                    'minMatchScore': 50,
                    'maxMatchesPerUser': 100,
                    'cacheEnabled': False
                }
            }
    
    def _start_background_tasks(self):
        """Start background tasks"""
        # Start embedding precomputation
        asyncio.create_task(self.embedding_service.start_precomputation_scheduler())
        
        # Start batch processing
        asyncio.create_task(self.batch_processor.start_processing_scheduler())
        
        # Start performance monitoring
        asyncio.create_task(self.performance_monitor.start_monitoring())
        
        # Schedule active users for batch processing
        asyncio.create_task(self.batch_processor.schedule_active_users())
    
    async def get_matches(self, user_id, limit=10, offset=0, min_score=None, include_explanation=False):
        """
        Get matches for a user
        
        Args:
            user_id (str): User ID
            limit (int): Maximum number of matches to return
            offset (int): Offset for pagination
            min_score (int): Minimum match score
            include_explanation (bool): Whether to include match explanations
            
        Returns:
            list: List of matches
        """
        # Record start time for performance monitoring
        start_time = time.time()
        
        try:
            # Check cache if enabled
            cache_enabled = self.settings.get('general', {}).get('cacheEnabled', False)
            cache_hit = False
            
            if cache_enabled and self.redis_cache.is_connected():
                # Create cache key based on parameters
                cache_params = {
                    'limit': limit,
                    'offset': offset,
                    'min_score': min_score,
                    'include_explanation': include_explanation
                }
                
                # Check cache
                cached_matches = await self.redis_cache.get_match_results(user_id, cache_params)
                if cached_matches:
                    logger.info(f"Using cached matches for user {user_id}")
                    cache_hit = True
                    
                    # Record performance metrics
                    response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
                    self.performance_monitor.record_api_request('/api/matches', response_time, 200)
                    self.performance_monitor.record_model_inference(response_time, batch_size=len(cached_matches), cache_hit=True)
                    
                    return cached_matches
            
            # Get user profile and preferences
            user = await self.prisma.user.find_unique(
                where={'id': user_id},
                include={
                    'profile': True,
                    'preference': True
                }
            )
            
            if not user or not user.profile:
                logger.error(f"User {user_id} not found or has no profile")
                
                # Record performance metrics
                response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
                self.performance_monitor.record_api_request('/api/matches', response_time, 404)
                
                return []
            
            # Determine which model to use based on A/B testing
            variant = await self.ab_testing.assign_variant(user_id, 'matching_model')
            variant_config = self.ab_testing.get_variant_config(variant, 'matching_model')
            
            # Add user to batch processing queue
            await self.batch_processor.add_to_queue(user_id)
            
            # Check if matches are already computed
            if cache_enabled and self.redis_cache.is_connected():
                # Try to get matches without parameters
                cached_matches = await self.redis_cache.get_match_results(user_id)
                if cached_matches:
                    # Filter and paginate matches
                    if min_score is not None:
                        min_score_value = int(min_score)
                        filtered_matches = [m for m in cached_matches if m['score'] >= min_score_value]
                    else:
                        filtered_matches = cached_matches
                    
                    # Sort by score (descending)
                    filtered_matches.sort(key=lambda m: m['score'], reverse=True)
                    
                    # Apply pagination
                    paginated_matches = filtered_matches[offset:offset + limit]
                    
                    # Add explanations if requested
                    if include_explanation:
                        for match in paginated_matches:
                            if 'analysis' not in match or not match['analysis']:
                                # Get match user
                                match_user = await self.prisma.user.find_unique(
                                    where={'id': match['userId']},
                                    include={
                                        'profile': True
                                    }
                                )
                                
                                if match_user and match_user.profile:
                                    # Convert to dictionaries
                                    user_profile = {
                                        'id': user.id,
                                        'name': user.name,
                                        'age': user.profile.age,
                                        'gender': user.profile.gender,
                                        'height': user.profile.height,
                                        'religion': user.profile.religion,
                                        'caste': user.profile.caste,
                                        'subCaste': user.profile.subCaste,
                                        'gotra': user.profile.gotra,
                                        'education': user.profile.education,
                                        'occupation': user.profile.occupation,
                                        'income': user.profile.income,
                                        'city': user.profile.city,
                                        'state': user.profile.state,
                                        'maritalStatus': user.profile.maritalStatus
                                    }
                                    
                                    user_preferences = {}
                                    if user.preference:
                                        user_preferences = {
                                            'minAge': user.preference.minAge,
                                            'maxAge': user.preference.maxAge,
                                            'minHeight': user.preference.minHeight,
                                            'maxHeight': user.preference.maxHeight,
                                            'religion': user.preference.religion,
                                            'caste': user.preference.caste,
                                            'subCaste': user.preference.subCaste,
                                            'education': user.preference.education,
                                            'occupation': user.preference.occupation,
                                            'minIncome': user.preference.minIncome,
                                            'city': user.preference.city,
                                            'state': user.preference.state,
                                            'maritalStatus': user.preference.maritalStatus
                                        }
                                    
                                    match_profile = {
                                        'id': match_user.id,
                                        'name': match_user.name,
                                        'age': match_user.profile.age,
                                        'gender': match_user.profile.gender,
                                        'height': match_user.profile.height,
                                        'religion': match_user.profile.religion,
                                        'caste': match_user.profile.caste,
                                        'subCaste': match_user.profile.subCaste,
                                        'gotra': match_user.profile.gotra,
                                        'education': match_user.profile.education,
                                        'occupation': match_user.profile.occupation,
                                        'income': match_user.profile.income,
                                        'city': match_user.profile.city,
                                        'state': match_user.profile.state,
                                        'maritalStatus': match_user.profile.maritalStatus
                                    }
                                    
                                    # Generate explanation
                                    score = match['score'] / 100.0  # Convert percentage to 0-1 scale
                                    explanation = self.interpreter.explain_match(
                                        user_profile, user_preferences, match_profile, score
                                    )
                                    
                                    match['analysis'] = explanation
                    
                    # Cache results with parameters
                    if cache_enabled and self.redis_cache.is_connected():
                        await self.redis_cache.cache_match_results(user_id, paginated_matches, cache_params)
                    
                    # Record performance metrics
                    response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
                    self.performance_monitor.record_api_request('/api/matches', response_time, 200)
                    self.performance_monitor.record_model_inference(response_time, batch_size=len(paginated_matches), cache_hit=True)
                    
                    # Record A/B testing event
                    await self.ab_testing.record_event(user_id, 'matches_viewed', len(paginated_matches), 'matching_model')
                    
                    return paginated_matches
            
            # Get detailed match data
            # This is a fallback if batch processing hasn't completed yet
            # In a production system, you would return a message to try again later
            # or use a websocket to notify when matches are ready
            
            logger.info(f"No cached matches found for user {user_id}, returning empty list")
            
            # Record performance metrics
            response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            self.performance_monitor.record_api_request('/api/matches', response_time, 200)
            
            # Return empty list
            return []
        except Exception as e:
            logger.error(f"Error getting matches: {str(e)}")
            
            # Record performance metrics
            response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            self.performance_monitor.record_api_request('/api/matches', response_time, 500)
            
            return []
    
    async def get_match_explanation(self, user_id, match_id):
        """
        Get explanation for a match
        
        Args:
            user_id (str): User ID
            match_id (str): Match ID
            
        Returns:
            dict: Match explanation
        """
        # Record start time for performance monitoring
        start_time = time.time()
        
        try:
            # Get user and match profiles
            user = await self.prisma.user.find_unique(
                where={'id': user_id},
                include={
                    'profile': True,
                    'preference': True
                }
            )
            
            match_user = await self.prisma.user.find_unique(
                where={'id': match_id},
                include={
                    'profile': True
                }
            )
            
            if not user or not user.profile or not match_user or not match_user.profile:
                logger.error(f"User {user_id} or match {match_id} not found or has no profile")
                
                # Record performance metrics
                response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
                self.performance_monitor.record_api_request('/api/matches/explanation', response_time, 404)
                
                return {}
            
            # Convert to dictionaries
            user_profile = {
                'id': user.id,
                'name': user.name,
                'age': user.profile.age,
                'gender': user.profile.gender,
                'height': user.profile.height,
                'religion': user.profile.religion,
                'caste': user.profile.caste,
                'subCaste': user.profile.subCaste,
                'gotra': user.profile.gotra,
                'education': user.profile.education,
                'occupation': user.profile.occupation,
                'income': user.profile.income,
                'city': user.profile.city,
                'state': user.profile.state,
                'maritalStatus': user.profile.maritalStatus
            }
            
            user_preferences = {}
            if user.preference:
                user_preferences = {
                    'minAge': user.preference.minAge,
                    'maxAge': user.preference.maxAge,
                    'minHeight': user.preference.minHeight,
                    'maxHeight': user.preference.maxHeight,
                    'religion': user.preference.religion,
                    'caste': user.preference.caste,
                    'subCaste': user.preference.subCaste,
                    'education': user.preference.education,
                    'occupation': user.preference.occupation,
                    'minIncome': user.preference.minIncome,
                    'city': user.preference.city,
                    'state': user.preference.state,
                    'maritalStatus': user.preference.maritalStatus
                }
            
            match_profile = {
                'id': match_user.id,
                'name': match_user.name,
                'age': match_user.profile.age,
                'gender': match_user.profile.gender,
                'height': match_user.profile.height,
                'religion': match_user.profile.religion,
                'caste': match_user.profile.caste,
                'subCaste': match_user.profile.subCaste,
                'gotra': match_user.profile.gotra,
                'education': match_user.profile.education,
                'occupation': match_user.profile.occupation,
                'income': match_user.profile.income,
                'city': match_user.profile.city,
                'state': match_user.profile.state,
                'maritalStatus': match_user.profile.maritalStatus
            }
            
            # Generate explanation
            explanation = self.interpreter.explain_match(
                user_profile, user_preferences, match_profile
            )
            
            # Record performance metrics
            response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            self.performance_monitor.record_api_request('/api/matches/explanation', response_time, 200)
            
            # Record A/B testing event
            await self.ab_testing.record_event(user_id, 'explanation_viewed', 1, 'matching_model')
            
            return explanation
        except Exception as e:
            logger.error(f"Error getting match explanation: {str(e)}")
            
            # Record performance metrics
            response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            self.performance_monitor.record_api_request('/api/matches/explanation', response_time, 500)
            
            return {}
