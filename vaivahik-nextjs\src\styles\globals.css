/* Global CSS */

:root {
  /* Core Colors */
  --primary-color: #FF5F6D; /* Coral Pink */
  --primary-light: #FFC371; /* Light Orange */
  --secondary-color: #8A2BE2; /* Blue Violet */
  --secondary-light: #9370DB; /* Medium Purple */
  --accent-color: #FFD700; /* Gold */

  /* Backgrounds & Text */
  --dark-color: #2D3047; /* Deep Blue/Gray */
  --light-color: #F8F9FA; /* Very Light Gray */
  --light-color-alt: #F0F2F5; /* Slightly different light gray for alternation */
  --white: #FFFFFF;
  --text-color-dark: #333;
  --text-color-medium: #555;
  --text-color-light: #F0F0F0;
  --text-color-light-muted: #B0B0B0;

  /* Gradients */
  --primary-gradient: linear-gradient(135deg, #FF5F6D 0%, #FFC371 100%);
  --secondary-gradient: linear-gradient(135deg, #8A2BE2 0%, #9370DB 100%);
  --subtle-white-gradient: linear-gradient(180deg, var(--white) 0%, #fcfdff 100%);
  --subtle-light-gradient: linear-gradient(180deg, var(--light-color) 0%, #f0f2f5 100%);
  --premium-gold-gradient: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  --text-gradient: linear-gradient(90deg, #5E35B1 0%, #FF5F6D 100%);

  /* Shadows & Effects */
  --shadow-soft: 0 5px 15px rgba(0,0,0,0.05);
  --shadow-medium: 0 10px 25px rgba(0,0,0,0.1);
  --shadow-hard: 0 15px 35px rgba(0,0,0,0.15);
  --shadow-subtle: 0 3px 10px rgba(0,0,0,0.08);
  --shadow-strong: 0 20px 40px rgba(0,0,0,0.2);
  --transition-smooth: all 0.3s ease;
  --border-radius-medium: 20px;
  --border-radius-large: 25px;

  /* Legacy colors for compatibility */
  --light-gray: #F5F5F5;
  --medium-gray: #E0E0E0;
  --dark-gray: #333333;
  --black: #000000;
  --success-color: #4CAF50;
  --warning-color: #FFC107;
  --error-color: #F44336;

  /* UI Elements */
  --border-radius: 12px;
  --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;

  /* 6. Fluid Typography Variables */
  --fluid-min-width: 320;
  --fluid-max-width: 1200;
  --fluid-min-size: 16;
  --fluid-max-size: 20;
  --fluid-min-scale: 1.2;
  --fluid-max-scale: 1.333;

  /* Default theme */
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

/* 5. Custom Scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: var(--light-gray);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}

/* Fix for modal overlays in admin panel */
.modal-overlay {
  z-index: 2000 !important; /* Higher than sidebar z-index */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  padding: 20px !important;
  box-sizing: border-box !important;
}

/* Add this class when modal is open to prevent body scrolling */
body.modal-open {
  overflow: hidden;
}

/* Ensure modal has proper dimensions and styling */
.modal {
  background-color: white !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  width: 100% !important;
  max-width: 800px !important;
  max-height: 90vh !important;
  display: flex !important;
  flex-direction: column !important;
  margin: 0 auto !important;
  position: relative !important;
  overflow: hidden !important;
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

@media (prefers-color-scheme: dark) {
  .loading-screen {
    background-color: rgba(10, 10, 10, 0.8);
  }
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(94, 53, 177, 0.2);
  border-radius: 50%;
  border-top: 4px solid var(--secondary-color);
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-screen p {
  color: var(--secondary-color);
  font-weight: 500;
}

/* Image optimization */
.optimized-image {
  position: relative;
  overflow: hidden;
}

.optimized-image img {
  transition: transform 0.3s ease;
}

.optimized-image.blur-load {
  background-size: cover;
  background-position: center;
}

.optimized-image.blur-load img {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.optimized-image.blur-load.loaded img {
  opacity: 1;
}
