/**
 * Profile Component with Secure Contact Integration
 * Example implementation showing how to integrate the secure calling system
 * into profile pages with enterprise-grade security
 */

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Box,
  Avatar,
  Chip,
  Grid,
  Alert,
  Snackbar,
  Divider
} from '@mui/material';
import {
  Verified as VerifiedIcon,
  Security as SecurityIcon,
  Premium as PremiumIcon,
  Phone as PhoneIcon
} from '@mui/icons-material';
import SmartCallButton from '@/components/contact/SmartCallButton';
import ContactedUsersList from '@/components/contact/ContactedUsersList';

const ProfileWithSecureContact = ({ profile, currentUser }) => {
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });
  const [securityAlerts, setSecurityAlerts] = useState([]);

  const handleCallInitiated = (data) => {
    setSnackbar({
      open: true,
      message: `Contact revealed for ${profile.fullName}. Opening dialer...`,
      severity: 'success'
    });

    // Track successful contact reveal
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'contact_reveal_success', {
        event_category: 'User_Engagement',
        event_label: profile.id,
        value: 1
      });
    }
  };

  const handleSecurityBlock = (data) => {
    setSecurityAlerts(prev => [...prev, {
      id: Date.now(),
      message: `Security system blocked contact access: ${data.reason}`,
      riskScore: data.riskScore,
      timestamp: new Date()
    }]);

    setSnackbar({
      open: true,
      message: 'Contact access blocked for security reasons',
      severity: 'warning'
    });

    // Track security blocks
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'security_block', {
        event_category: 'Security',
        event_label: data.reason,
        custom_parameters: {
          risk_score: data.riskScore,
          target_user_id: data.targetUserId
        }
      });
    }
  };

  const getSecurityBadge = () => {
    if (profile.isVerified && profile.verificationDocuments?.length > 0) {
      return (
        <Chip
          icon={<SecurityIcon />}
          label="Verified & Secure"
          color="success"
          size="small"
          variant="outlined"
        />
      );
    }
    if (profile.isVerified) {
      return (
        <Chip
          icon={<VerifiedIcon />}
          label="Verified"
          color="primary"
          size="small"
          variant="outlined"
        />
      );
    }
    return (
      <Chip
        label="Unverified"
        color="default"
        size="small"
        variant="outlined"
      />
    );
  };

  const getPremiumBadge = () => {
    if (profile.isPremium) {
      return (
        <Chip
          icon={<PremiumIcon />}
          label="Premium"
          color="warning"
          size="small"
        />
      );
    }
    return null;
  };

  return (
    <Box>
      {/* Security Alerts */}
      {securityAlerts.length > 0 && (
        <Alert 
          severity="warning" 
          sx={{ mb: 2 }}
          onClose={() => setSecurityAlerts([])}
        >
          <Typography variant="subtitle2" gutterBottom>
            🛡️ Security Alert
          </Typography>
          {securityAlerts.map(alert => (
            <Typography key={alert.id} variant="body2">
              {alert.message} (Risk Score: {alert.riskScore})
            </Typography>
          ))}
        </Alert>
      )}

      {/* Main Profile Card */}
      <Card elevation={3}>
        <CardContent>
          {/* Profile Header */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <Avatar
              src={profile.profilePic}
              sx={{ width: 80, height: 80, mr: 2 }}
            >
              {profile.fullName?.charAt(0)}
            </Avatar>
            <Box sx={{ flex: 1 }}>
              <Typography variant="h5" gutterBottom>
                {profile.fullName}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                {getSecurityBadge()}
                {getPremiumBadge()}
              </Box>
              <Typography variant="body2" color="text.secondary">
                {profile.age} years • {profile.city}, {profile.state}
              </Typography>
            </Box>
          </Box>

          {/* Profile Details */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={6}>
              <Typography variant="body2" color="text.secondary">
                Occupation
              </Typography>
              <Typography variant="body1">
                {profile.occupation || 'Not specified'}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2" color="text.secondary">
                Education
              </Typography>
              <Typography variant="body1">
                {profile.highestEducation || 'Not specified'}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2" color="text.secondary">
                Religion
              </Typography>
              <Typography variant="body1">
                {profile.religion} - {profile.caste}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2" color="text.secondary">
                Mother Tongue
              </Typography>
              <Typography variant="body1">
                {profile.motherTongue}
              </Typography>
            </Grid>
          </Grid>

          {/* Contact Privacy Information */}
          <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1, mb: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              📞 Contact Information
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Contact access: {profile.contactRevealPreference || 'Premium Only'}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Call availability: {profile.callAvailability || 'Anytime'}
            </Typography>
            {!profile.allowDirectCalls && (
              <Typography variant="body2" color="error">
                Direct calls disabled by user
              </Typography>
            )}
          </Box>

          {/* Security Information */}
          <Box sx={{ p: 2, bgcolor: 'info.light', borderRadius: 1, mb: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              🛡️ Security & Privacy
            </Typography>
            <Typography variant="body2">
              This profile is protected by our advanced security system that prevents 
              fake users and marriage bureau abuse. All contact reveals are monitored 
              and logged for safety.
            </Typography>
          </Box>
        </CardContent>

        <Divider />

        {/* Action Buttons */}
        <CardActions sx={{ p: 2, justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <SmartCallButton
              targetUserId={profile.userId}
              targetUserName={profile.fullName}
              onCallInitiated={handleCallInitiated}
              onSecurityBlock={handleSecurityBlock}
              variant="contained"
              size="large"
            />
          </Box>
          
          <Typography variant="caption" color="text.secondary">
            Secure contact reveal powered by enterprise security
          </Typography>
        </CardActions>
      </Card>

      {/* Contact History (for current user) */}
      {currentUser && (
        <Box sx={{ mt: 3 }}>
          <ContactedUsersList 
            userId={currentUser.id}
            maxItems={5}
            showTitle={true}
          />
        </Box>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        message={snackbar.message}
      />
    </Box>
  );
};

export default ProfileWithSecureContact;
