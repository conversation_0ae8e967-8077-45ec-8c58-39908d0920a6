/**
 * User Mock Data
 * 
 * This file contains mock data for user-related API endpoints.
 * Used for development and testing when the real backend is not available.
 */

// Mock family details data
export const mockFamilyDetails = {
  familyType: 'Joint Family',
  familyValues: 'Traditional',
  familyStatus: 'Middle Class',
  fatherN<PERSON>: '<PERSON><PERSON>',
  fatherOccupation: 'Business',
  mother<PERSON><PERSON>: '<PERSON><PERSON>',
  motherOccupation: 'Homemaker',
  brothers: '1',
  marriedBrothers: '0',
  sisters: '1',
  marriedSisters: '1',
  familyLocation: 'Pune, Maharashtra',
  aboutFamily: 'We are a close-knit family with strong traditional values. We believe in respecting elders and maintaining our cultural heritage.',
  gotra: 'Kashya<PERSON>'
};

// Mock partner preferences data
export const mockPartnerPreferences = {
  ageRange: [24, 32],
  heightRange: [4.5, 6.0],
  maritalStatus: ['Never Married'],
  education: ['Bachelor\'s', 'Master\'s'],
  occupation: ['IT Professional', 'Engineer', 'Doctor', 'Teacher'],
  incomeRange: ['5-7 LPA', '7-10 LPA', '10-15 LPA'],
  location: ['Mumbai', 'Pune', 'Nashik'],
  diet: 'Vegetarian',
  subCaste: ['Kunbi', '96 Kuli'],
  manglik: 'No Preference',
  aboutPartner: 'Looking for a well-educated, family-oriented partner with good values and a kind heart. Someone who respects traditions while having a modern outlook.'
};

// Mock user profile data
export const mockUserProfile = {
  id: 'usr_123456789',
  fullName: 'Rahul Deshmukh',
  email: '<EMAIL>',
  phone: '+91 9876543210',
  gender: 'Male',
  dateOfBirth: '1992-05-15',
  age: 31,
  maritalStatus: 'Never Married',
  height: '5.9',
  religion: 'Hindu',
  caste: 'Maratha',
  subCaste: 'Kunbi',
  education: 'Master\'s',
  educationField: 'Computer Science',
  occupation: 'IT Professional',
  workingWith: 'Tech Solutions Ltd',
  incomeRange: '10-15 LPA',
  city: 'Pune',
  state: 'Maharashtra',
  pincode: '411028',
  aboutMe: 'I am a software engineer with a passion for technology and innovation. I enjoy reading, traveling, and spending time with family. Looking for a life partner who shares similar values and interests.',
  profileStatus: 'APPROVED',
  profileCompletionPercentage: 85,
  membershipType: 'PREMIUM',
  membershipExpiryDate: '2024-12-31',
  createdAt: '2023-06-15T10:30:00Z',
  updatedAt: '2023-11-20T15:45:00Z',
  familyDetails: mockFamilyDetails,
  partnerPreferences: mockPartnerPreferences,
  photos: [
    {
      id: 'photo_1',
      url: 'https://randomuser.me/api/portraits/men/75.jpg',
      isPrimary: true,
      isApproved: true,
      isVisible: true,
      uploadedAt: '2023-06-15T11:00:00Z'
    },
    {
      id: 'photo_2',
      url: 'https://randomuser.me/api/portraits/men/76.jpg',
      isPrimary: false,
      isApproved: true,
      isVisible: true,
      uploadedAt: '2023-06-15T11:05:00Z'
    },
    {
      id: 'photo_3',
      url: 'https://randomuser.me/api/portraits/men/77.jpg',
      isPrimary: false,
      isApproved: true,
      isVisible: false,
      uploadedAt: '2023-06-15T11:10:00Z'
    }
  ]
};

// Mock API response for family details update
export const mockFamilyDetailsResponse = {
  success: true,
  message: 'Family details updated successfully',
  data: {
    familyDetails: mockFamilyDetails
  }
};

// Mock API response for partner preferences update
export const mockPartnerPreferencesResponse = {
  success: true,
  message: 'Partner preferences updated successfully',
  data: {
    preferences: mockPartnerPreferences
  }
};

// Mock API response for user profile
export const mockUserProfileResponse = {
  success: true,
  message: 'User profile retrieved successfully',
  data: mockUserProfile
};

// Mock API response for user profile update
export const mockUserProfileUpdateResponse = {
  success: true,
  message: 'User profile updated successfully',
  data: mockUserProfile
};

// Export all mocks
export default {
  mockUserProfile,
  mockFamilyDetails,
  mockPartnerPreferences,
  mockFamilyDetailsResponse,
  mockPartnerPreferencesResponse,
  mockUserProfileResponse,
  mockUserProfileUpdateResponse
};
