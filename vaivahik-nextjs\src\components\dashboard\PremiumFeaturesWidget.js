/**
 * Premium Features Widget - Showcases and manages premium subscriptions
 * Integrates with existing subscription and payment systems
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  Alert,
  Divider,
  styled,
  CircularProgress
} from '@mui/material';

// Icons
import {
  WorkspacePremium as PremiumIcon,
  Star as StarIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Message as MessageIcon,
  ContactPhone as ContactIcon,
  Search as SearchIcon,
  Visibility as ViewIcon,
  Security as SecurityIcon,
  Analytics as AnalyticsIcon,
  FlashOn as SpotlightIcon,
  Psychology as AIIcon,
  AutoAwesome as DiamondIcon,
  EmojiEvents as CrownIcon,
  Payment as PaymentIcon
} from '@mui/icons-material';

// Styled components
const PremiumCard = styled(Card)(({ theme, featured }) => ({
  borderRadius: 16,
  position: 'relative',
  transition: 'all 0.3s ease',
  border: featured ? '3px solid #FFD700' : '1px solid rgba(255,255,255,0.1)',
  background: featured 
    ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 160, 0, 0.1))'
    : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: featured 
      ? '0 20px 60px rgba(255, 215, 0, 0.3)'
      : '0 16px 48px rgba(0,0,0,0.12)'
  },
  ...(featured && {
    '&::before': {
      content: '"MOST POPULAR"',
      position: 'absolute',
      top: -12,
      left: '50%',
      transform: 'translateX(-50%)',
      background: 'linear-gradient(135deg, #FFD700, #FFA000)',
      color: '#000',
      padding: '4px 16px',
      borderRadius: 12,
      fontSize: '0.75rem',
      fontWeight: 700,
      zIndex: 1
    }
  })
}));

const FeatureItem = styled(ListItem)(({ theme }) => ({
  padding: '8px 0',
  '& .MuiListItemIcon-root': {
    minWidth: 32
  }
}));

const CurrentPlanCard = styled(Card)(({ theme }) => ({
  borderRadius: 16,
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white'
}));

// Premium plans will be fetched from admin API
const defaultPlans = [
  {
    id: 'plan-1',
    name: 'Premium Monthly',
    planType: 'MONTHLY',
    amount: 499,
    currency: 'INR',
    durationDays: 30,
    description: 'Access all premium features for one month.',
    isActive: true,
    isPopular: true,
    features: [
      { id: 'feature-1', name: 'View contact details' },
      { id: 'feature-2', name: 'Advanced search filters' },
      { id: 'feature-3', name: 'Priority in search results' },
      { id: 'feature-4', name: 'Unlimited messages' }
    ]
  },
  {
    id: 'plan-2',
    name: 'Premium Quarterly',
    planType: 'QUARTERLY',
    amount: 1299,
    currency: 'INR',
    durationDays: 90,
    description: 'Access all premium features for three months at a discounted rate.',
    isActive: true,
    isPopular: false,
    features: [
      { id: 'feature-1', name: 'View contact details' },
      { id: 'feature-2', name: 'Advanced search filters' },
      { id: 'feature-3', name: 'Priority in search results' },
      { id: 'feature-4', name: 'Unlimited messages' },
      { id: 'feature-5', name: 'Horoscope matching' }
    ]
  },
  {
    id: 'plan-3',
    name: 'Premium Annual',
    planType: 'ANNUAL',
    amount: 4999,
    currency: 'INR',
    durationDays: 365,
    description: 'Access all premium features for one year at our best value rate.',
    isActive: true,
    isPopular: false,
    features: [
      { id: 'feature-1', name: 'View contact details' },
      { id: 'feature-2', name: 'Advanced search filters' },
      { id: 'feature-3', name: 'Priority in search results' },
      { id: 'feature-4', name: 'Unlimited messages' },
      { id: 'feature-5', name: 'Horoscope matching' },
      { id: 'feature-6', name: 'Profile highlighting' }
    ]
  }
];

export default function PremiumFeaturesWidget({ userId, currentPlan, onUpgrade }) {
  const [plans, setPlans] = useState([]);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [upgradeDialog, setUpgradeDialog] = useState(false);
  const [loading, setLoading] = useState(true);
  const [currentSubscription, setCurrentSubscription] = useState(null);

  useEffect(() => {
    fetchPremiumPlans();
    fetchCurrentSubscription();
  }, []);

  const fetchPremiumPlans = async () => {
    try {
      // Fetch plans from admin API
      const response = await fetch('/api/admin/premium-plans', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('userToken')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setPlans(data.plans || defaultPlans);
      } else {
        // Fallback to default plans
        setPlans(defaultPlans);
      }
    } catch (error) {
      console.error('Error fetching premium plans:', error);
      // Fallback to default plans
      setPlans(defaultPlans);
    } finally {
      setLoading(false);
    }
  };

  const fetchCurrentSubscription = async () => {
    try {
      const response = await fetch(`/api/user/subscription/${userId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('userToken')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCurrentSubscription(data.subscription);
      }
    } catch (error) {
      console.error('Error fetching subscription:', error);
    }
  };

  const handleUpgrade = (plan) => {
    setSelectedPlan(plan);
    setUpgradeDialog(true);
  };

  const confirmUpgrade = async () => {
    setLoading(true);
    try {
      // Integrate with your Razorpay payment system
      const paymentData = {
        planId: selectedPlan.id,
        amount: selectedPlan.price,
        currency: selectedPlan.currency,
        userId: userId
      };

      // This would call your payment API
      console.log('Processing payment:', paymentData);
      
      // Simulate payment processing
      setTimeout(() => {
        setLoading(false);
        setUpgradeDialog(false);
        onUpgrade?.(selectedPlan);
        
        // Update current subscription
        setCurrentSubscription({
          planId: selectedPlan.id,
          planName: selectedPlan.name,
          expiresAt: new Date(Date.now() + selectedPlan.duration * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          daysRemaining: selectedPlan.duration,
          autoRenew: true,
          features: selectedPlan.features.slice(0, 3).map(f => f + ': Active')
        });
      }, 2000);
    } catch (error) {
      console.error('Error processing upgrade:', error);
      setLoading(false);
    }
  };

  const renderCurrentPlan = () => {
    if (!currentSubscription) return null;

    return (
      <CurrentPlanCard sx={{ mb: 4 }}>
        <CardContent sx={{ p: 4 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <CrownIcon sx={{ fontSize: 40, mr: 2 }} />
            <Box>
              <Typography variant="h5" fontWeight="700">
                Current Plan: {currentSubscription.planName}
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                {currentSubscription.daysRemaining} days remaining
              </Typography>
            </Box>
          </Box>

          <Box sx={{ mb: 3 }}>
            <Typography variant="body2" sx={{ opacity: 0.9, mb: 1 }}>
              Plan Usage:
            </Typography>
            <LinearProgress
              variant="determinate"
              value={(currentSubscription.daysRemaining / 30) * 100}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: 'rgba(255,255,255,0.2)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: '#4CAF50',
                  borderRadius: 4
                }
              }}
            />
          </Box>

          <Grid container spacing={2}>
            {currentSubscription.features.map((feature, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <CheckIcon sx={{ fontSize: 16, mr: 1 }} />
                  <Typography variant="body2">{feature}</Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </CurrentPlanCard>
    );
  };

  const renderPlanCard = (plan) => (
    <PremiumCard key={plan.id} featured={plan.isPopular}>
      <CardContent sx={{ p: 4, height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Plan Header */}
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <Box sx={{ mb: 2 }}>
            {plan.planType === 'ANNUAL' ? (
              <DiamondIcon sx={{ fontSize: 48, color: '#9C27B0' }} />
            ) : plan.isPopular ? (
              <CrownIcon sx={{ fontSize: 48, color: '#FFD700' }} />
            ) : (
              <PremiumIcon sx={{ fontSize: 48, color: '#667eea' }} />
            )}
          </Box>
          <Typography variant="h5" fontWeight="700" gutterBottom>
            {plan.name}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'baseline', justifyContent: 'center', mb: 1 }}>
            <Typography variant="h3" fontWeight="700" color="primary">
              {plan.currency === 'INR' ? '₹' : '$'}{plan.amount.toLocaleString()}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
              /{plan.durationDays} days
            </Typography>
          </Box>
          <Typography variant="body2" color="text.secondary">
            {plan.currency === 'INR' ? '₹' : '$'}{Math.round(plan.amount / plan.durationDays)} per day
          </Typography>
        </Box>

        {/* Features List */}
        <Box sx={{ flexGrow: 1, mb: 3 }}>
          <Typography variant="subtitle2" fontWeight="600" gutterBottom>
            Features Included:
          </Typography>
          <List dense>
            {plan.features && plan.features.length > 0 ? (
              plan.features.map((feature, index) => (
                <FeatureItem key={feature.id || index}>
                  <ListItemIcon>
                    <CheckIcon sx={{ color: '#4CAF50', fontSize: 20 }} />
                  </ListItemIcon>
                  <ListItemText
                    primary={typeof feature === 'string' ? feature : feature.name}
                    primaryTypographyProps={{ variant: 'body2' }}
                  />
                </FeatureItem>
              ))
            ) : (
              <FeatureItem>
                <ListItemText
                  primary={plan.description || 'Premium features included'}
                  primaryTypographyProps={{ variant: 'body2' }}
                />
              </FeatureItem>
            )}
          </List>
        </Box>

        {/* Action Button */}
        <Button
          fullWidth
          variant="contained"
          size="large"
          onClick={() => handleUpgrade(plan)}
          disabled={currentSubscription?.planId === plan.id}
          sx={{
            background: plan.featured 
              ? 'linear-gradient(135deg, #FFD700, #FFA000)'
              : 'linear-gradient(135deg, #667eea, #764ba2)',
            color: plan.featured ? '#000' : 'white',
            fontWeight: 600,
            py: 1.5,
            '&:hover': {
              background: plan.featured
                ? 'linear-gradient(135deg, #FFA000, #FF8F00)'
                : 'linear-gradient(135deg, #5a67d8, #6b46c1)'
            }
          }}
        >
          {currentSubscription?.planId === plan.id ? 'Current Plan' : 'Upgrade Now'}
        </Button>
      </CardContent>
    </PremiumCard>
  );

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
        <PremiumIcon sx={{ fontSize: 32, color: '#FFD700', mr: 2 }} />
        <Typography variant="h5" fontWeight="700">
          Premium Features
        </Typography>
      </Box>

      {/* Current Plan */}
      {renderCurrentPlan()}

      {/* Premium Benefits Overview */}
      <Card sx={{ mb: 4, borderRadius: 2 }}>
        <CardContent sx={{ p: 4 }}>
          <Typography variant="h6" fontWeight="600" gutterBottom>
            Why Choose Premium?
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <AIIcon sx={{ fontSize: 48, color: '#667eea', mb: 2 }} />
                <Typography variant="subtitle1" fontWeight="600">
                  AI-Powered Matching
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Advanced algorithms find your perfect match
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <SecurityIcon sx={{ fontSize: 48, color: '#4CAF50', mb: 2 }} />
                <Typography variant="subtitle1" fontWeight="600">
                  Secure Contact Reveal
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Safe and verified contact information
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <AnalyticsIcon sx={{ fontSize: 48, color: '#FF9800', mb: 2 }} />
                <Typography variant="subtitle1" fontWeight="600">
                  Detailed Analytics
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Track your profile performance
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Premium Plans */}
      <Typography variant="h6" fontWeight="600" gutterBottom>
        Choose Your Premium Plan
      </Typography>
      <Grid container spacing={3}>
        {plans.map((plan) => (
          <Grid item xs={12} md={4} key={plan.id}>
            {renderPlanCard(plan)}
          </Grid>
        ))}
      </Grid>

      {/* Upgrade Dialog */}
      <Dialog open={upgradeDialog} onClose={() => setUpgradeDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <PaymentIcon sx={{ mr: 2, color: '#4CAF50' }} />
            Upgrade to {selectedPlan?.name}
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedPlan && (
            <Box>
              <Alert severity="info" sx={{ mb: 3 }}>
                You're upgrading to <strong>{selectedPlan.name}</strong> for ₹{selectedPlan.price.toLocaleString()} 
                valid for {selectedPlan.duration} days.
              </Alert>

              <Typography variant="h6" fontWeight="600" gutterBottom>
                Payment Summary:
              </Typography>
              <Box sx={{ p: 2, backgroundColor: '#f5f5f5', borderRadius: 2, mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Plan:</Typography>
                  <Typography fontWeight="600">{selectedPlan.name}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Duration:</Typography>
                  <Typography>{selectedPlan.duration} days</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Amount:</Typography>
                  <Typography fontWeight="600">₹{selectedPlan.price.toLocaleString()}</Typography>
                </Box>
                <Divider sx={{ my: 1 }} />
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="h6">Total:</Typography>
                  <Typography variant="h6" fontWeight="700">₹{selectedPlan.price.toLocaleString()}</Typography>
                </Box>
              </Box>

              <Typography variant="body2" color="text.secondary">
                Payment will be processed securely through Razorpay. You will receive a confirmation email after successful payment.
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUpgradeDialog(false)} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={confirmUpgrade}
            variant="contained"
            disabled={loading}
            sx={{
              background: 'linear-gradient(135deg, #4CAF50, #8BC34A)',
              minWidth: 120
            }}
          >
            {loading ? <CircularProgress size={20} color="inherit" /> : 'Pay Now'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
