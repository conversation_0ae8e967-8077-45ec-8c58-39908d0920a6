// src/middleware/auth.middleware.js

const jwt = require('jsonwebtoken');

/**
 * Middleware to authenticate requests using JWT Access Token.
 * Verifies the token provided in the 'Authorization: Bearer <token>' header.
 * If valid, attaches the decoded user payload to req.user.
 * If invalid or missing, sends a 401 or 403 error response.
 */
const authenticateToken = (req, res, next) => {
  // Get the authorization header
  const authHeader = req.headers['authorization'];
  // Extract the token part (Bearer <token>)
  const token = authHeader && authHeader.split(' ')[1];

  // Check if token exists
  if (!token) {
    // No token provided
    return res.status(401).json({ message: 'Access denied. No token provided.' });
  }

  try {
    // Verify the token using the JWT_SECRET from environment variables
    // Ensure JWT_SECRET is set in your .env file!
    if (!process.env.JWT_SECRET) {
        console.error("JWT_SECRET is not defined in .env file!");
        // Don't leak internal error details in production
        return res.status(500).json({ message: "Server configuration error." });
    }

    const decodedPayload = jwt.verify(token, process.env.JWT_SECRET);

    // Attach the decoded payload (which should contain user info like userId)
    // to the request object for use in subsequent route handlers
    req.user = decodedPayload;

    // Pass control to the next middleware or route handler
    next();
  } catch (err) {
    // Handle different JWT errors
    if (err.name === 'TokenExpiredError') {
      return res.status(401).json({ message: 'Token expired. Please log in again.' }); // Use 401 for expired token
    }
    if (err.name === 'JsonWebTokenError') {
      return res.status(403).json({ message: 'Invalid token.' }); // Use 403 for invalid token
    }
    // Handle other potential errors during verification
    console.error("Error verifying token:", err);
    return res.status(403).json({ message: 'Token verification failed.' }); // General forbidden
  }
};

/**
 * Middleware to check if the authenticated user is an admin.
 * Must be used after authenticateToken middleware.
 */
const isAdmin = (req, res, next) => {
  // Check if user exists and has admin role
  if (!req.user) {
    return res.status(401).json({ message: 'Authentication required.' });
  }

  // For now, we'll use a simple check - in a real app, you'd check a role field
  // This is a placeholder - implement proper admin check based on your user model
  if (req.user.isAdmin) {
    next();
  } else {
    return res.status(403).json({ message: 'Access denied. Admin privileges required.' });
  }
};

// Export the middleware functions
module.exports = {
  authenticateToken,
  isAdmin,
  authenticateAdmin: isAdmin // Alias for consistency
};
