/**
 * Cache Middleware
 * 
 * This middleware provides caching for API responses.
 * It can be used to cache responses for GET requests to improve performance.
 */

const { getCache, setCache, CACHE_PREFIXES } = require('../../redis/cacheService');
const logger = require('../utils/logger');

/**
 * Generate a cache key based on the request
 * @param {Object} req - Express request object
 * @returns {string} - Cache key
 */
const generateCacheKey = (req) => {
  // Base key on the request path
  let key = `api:${req.method}:${req.originalUrl}`;
  
  // If there's a user ID in the request, include it in the key
  if (req.user?.userId) {
    key += `:user:${req.user.userId}`;
  }
  
  // If there are query parameters, include them in the key
  if (Object.keys(req.query).length > 0) {
    // Sort query parameters to ensure consistent keys
    const sortedQuery = Object.keys(req.query)
      .sort()
      .reduce((acc, key) => {
        acc[key] = req.query[key];
        return acc;
      }, {});
    
    key += `:query:${JSON.stringify(sortedQuery)}`;
  }
  
  return key;
};

/**
 * Cache middleware for API responses
 * @param {Object} options - Cache options
 * @param {number} options.ttl - Time to live in seconds
 * @param {boolean} options.userSpecific - Whether the cache is user-specific
 * @param {Function} options.keyGenerator - Custom function to generate cache key
 * @returns {Function} - Express middleware function
 */
const cacheMiddleware = (options = {}) => {
  const {
    ttl = 3600, // Default TTL: 1 hour
    userSpecific = false, // Default: not user-specific
    keyGenerator = generateCacheKey // Default key generator
  } = options;
  
  return async (req, res, next) => {
    // Only cache GET requests
    if (req.method !== 'GET') {
      return next();
    }
    
    // Skip caching if feature flag is disabled
    if (process.env.ENABLE_REDIS_CACHE !== 'true') {
      return next();
    }
    
    // Skip caching for user-specific requests if no user is authenticated
    if (userSpecific && !req.user?.userId) {
      return next();
    }
    
    try {
      // Generate cache key
      const cacheKey = keyGenerator(req);
      
      // Try to get from cache
      const cachedResponse = await getCache(cacheKey);
      
      if (cachedResponse) {
        // Return cached response
        logger.debug(`Cache hit for ${cacheKey}`);
        return res.status(cachedResponse.status)
          .set('X-Cache', 'HIT')
          .json(cachedResponse.data);
      }
      
      // Cache miss, continue to the route handler
      logger.debug(`Cache miss for ${cacheKey}`);
      
      // Store the original res.json method
      const originalJson = res.json;
      
      // Override res.json method to cache the response
      res.json = function(data) {
        // Cache the response
        const responseToCache = {
          status: res.statusCode,
          data
        };
        
        // Only cache successful responses
        if (res.statusCode >= 200 && res.statusCode < 300) {
          setCache(cacheKey, responseToCache, ttl)
            .catch(error => logger.error(`Error caching response for ${cacheKey}:`, error));
        }
        
        // Set cache header
        res.set('X-Cache', 'MISS');
        
        // Call the original json method
        return originalJson.call(this, data);
      };
      
      next();
    } catch (error) {
      logger.error('Error in cache middleware:', error);
      next();
    }
  };
};

/**
 * Cache middleware for user profile data
 * @param {number} ttl - Time to live in seconds
 * @returns {Function} - Express middleware function
 */
const cacheUserProfile = (ttl = 3600) => {
  return cacheMiddleware({
    ttl,
    userSpecific: true,
    keyGenerator: (req) => `${CACHE_PREFIXES.PROFILE}${req.user.userId}`
  });
};

/**
 * Cache middleware for user matches
 * @param {number} ttl - Time to live in seconds
 * @returns {Function} - Express middleware function
 */
const cacheUserMatches = (ttl = 1800) => { // 30 minutes
  return cacheMiddleware({
    ttl,
    userSpecific: true,
    keyGenerator: (req) => {
      const { page = 1, limit = 10, ...filters } = req.query;
      return `${CACHE_PREFIXES.MATCHES}${req.user.userId}:page:${page}:limit:${limit}:filters:${JSON.stringify(filters)}`;
    }
  });
};

/**
 * Cache middleware for conversations
 * @param {number} ttl - Time to live in seconds
 * @returns {Function} - Express middleware function
 */
const cacheConversations = (ttl = 300) => { // 5 minutes
  return cacheMiddleware({
    ttl,
    userSpecific: true,
    keyGenerator: (req) => `${CACHE_PREFIXES.CONVERSATIONS}${req.user.userId}:list`
  });
};

/**
 * Cache middleware for messages in a conversation
 * @param {number} ttl - Time to live in seconds
 * @returns {Function} - Express middleware function
 */
const cacheMessages = (ttl = 60) => { // 1 minute
  return cacheMiddleware({
    ttl,
    userSpecific: true,
    keyGenerator: (req) => {
      const conversationId = req.params.conversationId;
      const { page = 1, limit = 20 } = req.query;
      return `${CACHE_PREFIXES.MESSAGES}${conversationId}:page:${page}:limit:${limit}`;
    }
  });
};

/**
 * Cache middleware for dashboard statistics
 * @param {number} ttl - Time to live in seconds
 * @returns {Function} - Express middleware function
 */
const cacheDashboardStats = (ttl = 1800) => { // 30 minutes
  return cacheMiddleware({
    ttl,
    keyGenerator: (req) => {
      const timeframe = req.query.timeframe || 'day';
      return `${CACHE_PREFIXES.DASHBOARD_STATS}${timeframe}`;
    }
  });
};

/**
 * Cache middleware for search results
 * @param {number} ttl - Time to live in seconds
 * @returns {Function} - Express middleware function
 */
const cacheSearchResults = (ttl = 300) => { // 5 minutes
  return cacheMiddleware({
    ttl,
    keyGenerator: (req) => {
      const { q, page = 1, limit = 10, ...filters } = req.query;
      return `${CACHE_PREFIXES.SEARCH_RESULTS}q:${q}:page:${page}:limit:${limit}:filters:${JSON.stringify(filters)}`;
    }
  });
};

module.exports = {
  cacheMiddleware,
  cacheUserProfile,
  cacheUserMatches,
  cacheConversations,
  cacheMessages,
  cacheDashboardStats,
  cacheSearchResults
};
