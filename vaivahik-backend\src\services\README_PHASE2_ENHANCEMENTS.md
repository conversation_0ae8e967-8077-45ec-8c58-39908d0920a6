# Phase 2: Training Enhancements for Two-Tower Model

This document explains the Phase 2 enhancements for the Vaivahik matrimony matching system, which focuses on improving the training process for the PyTorch-based two-tower model.

## Overview

Phase 2 enhancements include:

1. **Advanced Training Techniques**
   - Learning rate scheduling
   - Early stopping
   - Gradient clipping
   - Weight decay (L2 regularization)

2. **Data Augmentation and Sampling**
   - Hard negative mining
   - Balanced sampling
   - Feature noise injection
   - Feature dropout

3. **Training and Evaluation Tools**
   - Comprehensive training script
   - Model evaluation with metrics
   - Visualization of training progress
   - Performance analysis

## Files

- `model_trainer.py`: Advanced trainer with learning rate scheduling and early stopping
- `data_augmentation.py`: Data augmentation and sampling techniques
- `train_model.py`: Script for training the model with visualizations
- `evaluate_model.py`: Script for evaluating model performance

## How to Use

### 1. Training the Model

To train the model with the enhanced training techniques:

```bash
python src/scripts/train_model.py --data data/training_data.json --output models
```

Options:
- `--data`: Path to training data JSON file
- `--config`: (Optional) Path to training configuration JSON file
- `--output`: (Optional) Output directory for models and visualizations (default: 'models')

The training data JSON file should have the following structure:

```json
{
  "user_profiles": [
    {
      "id": "user1",
      "age": 28,
      "gender": "MALE",
      "height": 175,
      "religion": "Hindu",
      "caste": "Maratha",
      "education": "Master's Degree",
      "occupation": "Software Engineer",
      "income": 1200000
    },
    ...
  ],
  "match_profiles": [
    {
      "id": "match1",
      "age": 26,
      "gender": "FEMALE",
      "height": 165,
      "religion": "Hindu",
      "caste": "Maratha",
      "education": "Bachelor's Degree",
      "occupation": "Teacher",
      "income": 800000
    },
    ...
  ],
  "positive_pairs": [
    ["user1", "match3"],
    ["user2", "match1"],
    ...
  ]
}
```

### 2. Evaluating the Model

To evaluate the trained model:

```bash
python src/scripts/evaluate_model.py --model models/training_20230615-123456/final_model.pt --data data/test_data.json --output evaluation
```

Options:
- `--model`: Path to the trained model
- `--data`: Path to test data JSON file
- `--output`: (Optional) Output directory for evaluation results (default: 'evaluation')

The test data JSON file should have the following structure:

```json
{
  "user_profiles": [...],
  "match_profiles": [...],
  "test_pairs": [
    ["user1", "match3"],
    ["user2", "match1"],
    ...
  ],
  "labels": [1, 0, ...]
}
```

### 3. Custom Training Configuration

You can customize the training process by providing a configuration JSON file:

```json
{
  "learning_rate": 0.001,
  "weight_decay": 0.01,
  "batch_size": 64,
  "epochs": 50,
  "early_stopping_patience": 5,
  "reduce_lr_patience": 3,
  "reduce_lr_factor": 0.5,
  "gradient_clip_value": 1.0,
  "validation_split": 0.2,
  "user_tower_layers": [256, 128],
  "match_tower_layers": [256, 128],
  "embedding_size": 128,
  "dropout_rate": 0.2,
  "similarity_metrics": ["cosine", "euclidean", "dot"],
  "similarity_weights": [0.6, 0.2, 0.2]
}
```

## Advanced Training Techniques

### Learning Rate Scheduling

The enhanced trainer uses a learning rate scheduler that reduces the learning rate when the validation loss plateaus. This helps the model converge to better minima:

```python
scheduler = optim.lr_scheduler.ReduceLROnPlateau(
    optimizer,
    mode='min',
    factor=self.config['reduce_lr_factor'],
    patience=self.config['reduce_lr_patience'],
    verbose=True
)
```

### Early Stopping

Early stopping prevents overfitting by stopping training when the validation loss stops improving:

```python
if val_loss < best_val_loss:
    best_val_loss = val_loss
    patience_counter = 0
    
    # Save best model
    self.save_model(best_model_path)
else:
    patience_counter += 1
    if patience_counter >= self.config['early_stopping_patience']:
        logger.info(f"Early stopping at epoch {epoch+1}")
        break
```

### Gradient Clipping

Gradient clipping prevents exploding gradients by limiting their magnitude:

```python
torch.nn.utils.clip_grad_norm_(
    self.model.model.parameters(),
    max_norm=self.config['gradient_clip_value']
)
```

### Weight Decay (L2 Regularization)

Weight decay prevents overfitting by penalizing large weights:

```python
optimizer = optim.AdamW(
    self.model.model.parameters(),
    lr=self.config['learning_rate'],
    weight_decay=self.config['weight_decay']
)
```

## Data Augmentation Techniques

### Hard Negative Mining

Hard negative mining generates challenging negative examples that are similar to positive examples:

```python
hard_negatives = data_augmentation.generate_hard_negatives(
    user_profiles, match_profiles, positive_pairs
)
```

### Balanced Sampling

Balanced sampling ensures that the model sees an equal number of positive and negative examples:

```python
user_features, match_features, labels = data_augmentation.balance_dataset(
    user_features, match_features, labels, pos_neg_ratio=1.0
)
```

### Feature Noise Injection

Adding noise to features helps the model generalize better:

```python
noisy_user_features = data_augmentation.add_noise(user_features)
noisy_match_features = data_augmentation.add_noise(match_features)
```

### Feature Dropout

Feature dropout randomly sets some feature values to zero, which acts as a regularization technique:

```python
user_features = data_augmentation.feature_dropout(user_features)
match_features = data_augmentation.feature_dropout(match_features)
```

## Evaluation Metrics

The evaluation script calculates the following metrics:

- **ROC AUC**: Area under the Receiver Operating Characteristic curve
- **PR AUC**: Area under the Precision-Recall curve
- **Accuracy**: Percentage of correctly classified examples
- **Precision**: Percentage of positive predictions that are correct
- **Recall**: Percentage of actual positives that are correctly identified
- **F1 Score**: Harmonic mean of precision and recall
- **Confusion Matrix**: True positives, false positives, true negatives, false negatives

## Visualizations

The training and evaluation scripts generate the following visualizations:

- **Loss Curve**: Training and validation loss over epochs
- **Learning Rate**: Learning rate changes over epochs
- **ROC Curve**: True positive rate vs. false positive rate
- **Precision-Recall Curve**: Precision vs. recall
- **Score Distribution**: Distribution of predicted scores for positive and negative examples
- **Confusion Matrix**: Visual representation of prediction errors

## Next Steps

After implementing Phase 2, consider these future enhancements:

1. **Hyperparameter Optimization**: Automatically find the best hyperparameters
2. **Model Interpretability**: Explain why matches are recommended
3. **A/B Testing Framework**: Compare different model variants with real users
4. **Production Optimization**: Quantize the model for faster inference
