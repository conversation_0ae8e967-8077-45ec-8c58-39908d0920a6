/**
 * Documentation Enhancement Script
 * 
 * This script enhances API documentation with detailed descriptions and examples.
 * It adds more comprehensive JSDoc comments to routes and controllers.
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Configuration
const config = {
  routesDir: path.join(__dirname, '../src/routes'),
  controllersDir: path.join(__dirname, '../src/controllers'),
  backupDir: path.join(__dirname, '../backups', `docs-${new Date().toISOString().replace(/:/g, '-')}`)
};

// Create backup directory
if (!fs.existsSync(config.backupDir)) {
  fs.mkdirSync(config.backupDir, { recursive: true });
}

/**
 * Get all route files
 * @returns {Array<string>} Array of route file paths
 */
function getRouteFiles() {
  return glob.sync('**/*.js', { cwd: config.routesDir, absolute: true });
}

/**
 * Get all controller files
 * @returns {Array<string>} Array of controller file paths
 */
function getControllerFiles() {
  return glob.sync('**/*.js', { cwd: config.controllersDir, absolute: true });
}

/**
 * Backup a file
 * @param {string} filePath - Path to the file
 */
function backupFile(filePath) {
  const relativePath = path.relative(path.join(__dirname, '..'), filePath);
  const backupPath = path.join(config.backupDir, relativePath);
  
  // Create directory if it doesn't exist
  const backupDir = path.dirname(backupPath);
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  // Copy file
  fs.copyFileSync(filePath, backupPath);
  console.log(`Backed up ${relativePath}`);
}

/**
 * Extract routes from file content
 * @param {string} content - File content
 * @returns {Array<Object>} Array of route objects
 */
function extractRoutes(content) {
  const routes = [];
  const routeRegex = /router\.(get|post|put|patch|delete)\(\s*['"]([^'"]+)['"]\s*,\s*([^)]+)\)/g;
  
  let match;
  while ((match = routeRegex.exec(content)) !== null) {
    const method = match[1];
    const path = match[2];
    const handlers = match[3].split(',').map(h => h.trim());
    
    routes.push({
      method,
      path,
      handlers,
      original: match[0]
    });
  }
  
  return routes;
}

/**
 * Extract JSDoc comments from file content
 * @param {string} content - File content
 * @returns {Array<Object>} Array of JSDoc comment objects
 */
function extractJSDocComments(content) {
  const comments = [];
  const commentRegex = /\/\*\*\s*([\s\S]*?)\s*\*\/\s*router\.(get|post|put|patch|delete)\(\s*['"]([^'"]+)['"]/g;
  
  let match;
  while ((match = commentRegex.exec(content)) !== null) {
    const comment = match[1];
    const method = match[2];
    const path = match[3];
    
    comments.push({
      comment,
      method,
      path,
      original: match[0]
    });
  }
  
  return comments;
}

/**
 * Extract controller functions from file content
 * @param {string} content - File content
 * @returns {Array<Object>} Array of controller function objects
 */
function extractControllerFunctions(content) {
  const functions = [];
  const functionRegex = /(?:const|function)\s+([a-zA-Z0-9_]+)\s*=\s*async\s*\(\s*req\s*,\s*res\s*,\s*next\s*\)\s*=>\s*{/g;
  
  let match;
  while ((match = functionRegex.exec(content)) !== null) {
    functions.push({
      name: match[1],
      signature: match[0],
      position: match.index
    });
  }
  
  return functions;
}

/**
 * Extract JSDoc comments for controller functions
 * @param {string} content - File content
 * @returns {Array<Object>} Array of JSDoc comment objects
 */
function extractControllerJSDocComments(content) {
  const comments = [];
  const commentRegex = /\/\*\*\s*([\s\S]*?)\s*\*\/\s*(?:const|function)\s+([a-zA-Z0-9_]+)\s*=\s*async\s*\(\s*req\s*,\s*res\s*,\s*next\s*\)\s*=>/g;
  
  let match;
  while ((match = commentRegex.exec(content)) !== null) {
    const comment = match[1];
    const name = match[2];
    
    comments.push({
      comment,
      name,
      original: match[0]
    });
  }
  
  return comments;
}

/**
 * Enhance route documentation
 * @param {string} filePath - Path to the route file
 */
function enhanceRouteDocumentation(filePath) {
  // Read file
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Extract routes
  const routes = extractRoutes(content);
  
  // Skip if no routes found
  if (routes.length === 0) {
    console.log(`Skipping ${path.relative(path.join(__dirname, '..'), filePath)} (no routes found)`);
    return;
  }
  
  // Extract existing JSDoc comments
  const comments = extractJSDocComments(content);
  
  // Skip if all routes have detailed JSDoc comments
  if (comments.length === routes.length && comments.every(comment => isDetailedJSDocComment(comment.comment))) {
    console.log(`Skipping ${path.relative(path.join(__dirname, '..'), filePath)} (already has detailed documentation)`);
    return;
  }
  
  // Backup file
  backupFile(filePath);
  
  // Enhance documentation
  let updatedContent = content;
  
  for (const route of routes) {
    // Find existing comment
    const existingComment = comments.find(comment => comment.method === route.method && comment.path === route.path);
    
    if (existingComment && isDetailedJSDocComment(existingComment.comment)) {
      // Skip if already has detailed documentation
      continue;
    }
    
    // Generate enhanced JSDoc comment
    const enhancedComment = generateEnhancedJSDocComment(route, filePath);
    
    if (existingComment) {
      // Replace existing comment
      updatedContent = updatedContent.replace(
        existingComment.original,
        enhancedComment + `router.${route.method}('${route.path}'`
      );
    } else {
      // Add new comment
      updatedContent = updatedContent.replace(
        route.original,
        enhancedComment + '\n' + route.original
      );
    }
  }
  
  // Write updated content
  fs.writeFileSync(filePath, updatedContent);
  
  console.log(`Enhanced documentation in ${path.relative(path.join(__dirname, '..'), filePath)}`);
}

/**
 * Enhance controller documentation
 * @param {string} filePath - Path to the controller file
 */
function enhanceControllerDocumentation(filePath) {
  // Read file
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Extract controller functions
  const functions = extractControllerFunctions(content);
  
  // Skip if no functions found
  if (functions.length === 0) {
    console.log(`Skipping ${path.relative(path.join(__dirname, '..'), filePath)} (no controller functions found)`);
    return;
  }
  
  // Extract existing JSDoc comments
  const comments = extractControllerJSDocComments(content);
  
  // Skip if all functions have detailed JSDoc comments
  if (comments.length === functions.length && comments.every(comment => isDetailedControllerJSDocComment(comment.comment))) {
    console.log(`Skipping ${path.relative(path.join(__dirname, '..'), filePath)} (already has detailed documentation)`);
    return;
  }
  
  // Backup file
  backupFile(filePath);
  
  // Enhance documentation
  let updatedContent = content;
  
  for (const fn of functions) {
    // Find existing comment
    const existingComment = comments.find(comment => comment.name === fn.name);
    
    if (existingComment && isDetailedControllerJSDocComment(existingComment.comment)) {
      // Skip if already has detailed documentation
      continue;
    }
    
    // Generate enhanced JSDoc comment
    const enhancedComment = generateEnhancedControllerJSDocComment(fn, filePath);
    
    if (existingComment) {
      // Replace existing comment
      updatedContent = updatedContent.replace(
        existingComment.original,
        enhancedComment + `${fn.name} = async (req, res, next) =>`
      );
    } else {
      // Add new comment
      updatedContent = updatedContent.replace(
        fn.signature,
        enhancedComment + '\n' + fn.signature
      );
    }
  }
  
  // Write updated content
  fs.writeFileSync(filePath, updatedContent);
  
  console.log(`Enhanced documentation in ${path.relative(path.join(__dirname, '..'), filePath)}`);
}

/**
 * Check if a JSDoc comment is detailed
 * @param {string} comment - JSDoc comment
 * @returns {boolean} Whether the comment is detailed
 */
function isDetailedJSDocComment(comment) {
  return comment.includes('@swagger') && 
         comment.includes('description:') && 
         comment.includes('responses:') && 
         comment.includes('schema:');
}

/**
 * Check if a controller JSDoc comment is detailed
 * @param {string} comment - JSDoc comment
 * @returns {boolean} Whether the comment is detailed
 */
function isDetailedControllerJSDocComment(comment) {
  return comment.includes('@param') && 
         comment.includes('@returns') && 
         comment.includes('req - Express request object') && 
         comment.includes('res - Express response object') && 
         comment.includes('next - Express next function');
}

/**
 * Generate enhanced JSDoc comment for a route
 * @param {Object} route - Route object
 * @param {string} filePath - Path to the route file
 * @returns {string} Enhanced JSDoc comment
 */
function generateEnhancedJSDocComment(route, filePath) {
  const method = route.method.toLowerCase();
  const path = route.path;
  const resourceName = getResourceName(path);
  const action = getActionName(method, path);
  const apiPath = getApiPath(filePath, path);
  
  return `/**
 * @swagger
 * ${apiPath}:
 *   ${method}:
 *     summary: ${action} ${resourceName}
 *     description: ${getDetailedDescription(method, resourceName)}
 *     tags: [${getTagName(filePath)}]
 *     parameters:
 *       ${generateSwaggerParameters(route, path)}
 *     ${generateRequestBody(method)}
 *     responses:
 *       ${generateSwaggerResponses(method)}
 */
`;
}

/**
 * Generate enhanced JSDoc comment for a controller function
 * @param {Object} fn - Controller function object
 * @param {string} filePath - Path to the controller file
 * @returns {string} Enhanced JSDoc comment
 */
function generateEnhancedControllerJSDocComment(fn, filePath) {
  const name = fn.name;
  const action = getControllerActionName(name);
  
  return `/**
 * ${action}
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 * @returns {object} - Express response or next function call
 */
`;
}

/**
 * Get resource name from path
 * @param {string} path - Route path
 * @returns {string} Resource name
 */
function getResourceName(path) {
  const parts = path.split('/').filter(Boolean);
  return parts[parts.length - 1].replace(/:[^/]+/, 'item');
}

/**
 * Get action name from method and path
 * @param {string} method - HTTP method
 * @param {string} path - Route path
 * @returns {string} Action name
 */
function getActionName(method, path) {
  const resource = getResourceName(path);
  
  switch (method) {
    case 'get':
      return path.includes(':') ? `Get ${resource}` : `Get all ${resource}s`;
    case 'post':
      return `Create ${resource}`;
    case 'put':
      return `Update ${resource}`;
    case 'patch':
      return `Partially update ${resource}`;
    case 'delete':
      return `Delete ${resource}`;
    default:
      return `${method} ${resource}`;
  }
}

/**
 * Get controller action name from function name
 * @param {string} name - Function name
 * @returns {string} Action name
 */
function getControllerActionName(name) {
  // Convert camelCase to sentence case
  return name
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .trim();
}

/**
 * Get tag name from file path
 * @param {string} filePath - File path
 * @returns {string} Tag name
 */
function getTagName(filePath) {
  const relativePath = path.relative(config.routesDir, filePath);
  const parts = relativePath.split(path.sep);
  
  // Handle admin routes
  if (parts[0] === 'admin' || relativePath.includes('admin')) {
    return 'Admin';
  }
  
  // Handle other routes
  return parts[0].charAt(0).toUpperCase() + parts[0].slice(1);
}

/**
 * Get API path from file path and route path
 * @param {string} filePath - File path
 * @param {string} routePath - Route path
 * @returns {string} Full API path
 */
function getApiPath(filePath, routePath) {
  const relativePath = path.relative(config.routesDir, filePath);
  const parts = relativePath.split(path.sep);
  
  // Remove file extension
  parts[parts.length - 1] = parts[parts.length - 1].replace('.js', '');
  
  // Handle index.js files
  if (parts[parts.length - 1] === 'index') {
    parts.pop();
  }
  
  return '/api/v1/' + parts.join('/') + routePath;
}

/**
 * Get detailed description for a route
 * @param {string} method - HTTP method
 * @param {string} resourceName - Resource name
 * @returns {string} Detailed description
 */
function getDetailedDescription(method, resourceName) {
  switch (method) {
    case 'get':
      return `Retrieve ${resourceName} information with all associated data. This endpoint supports pagination, filtering, and sorting options.`;
    case 'post':
      return `Create a new ${resourceName} with the provided data. All required fields must be included in the request body.`;
    case 'put':
      return `Update all fields of an existing ${resourceName}. All fields must be included in the request body, even if they haven't changed.`;
    case 'patch':
      return `Partially update an existing ${resourceName}. Only the fields that need to be updated should be included in the request body.`;
    case 'delete':
      return `Delete an existing ${resourceName}. This operation cannot be undone, so use with caution.`;
    default:
      return `Perform ${method} operation on ${resourceName}.`;
  }
}

/**
 * Generate Swagger parameters for a route
 * @param {Object} route - Route object
 * @param {string} path - Route path
 * @returns {string} Swagger parameters
 */
function generateSwaggerParameters(route, path) {
  const method = route.method.toLowerCase();
  
  // Generate parameters based on path parameters
  const pathParams = [];
  const pathRegex = /:([^/]+)/g;
  let match;
  
  while ((match = pathRegex.exec(path)) !== null) {
    pathParams.push(match[1]);
  }
  
  // Generate Swagger parameters
  let parameters = '';
  
  // Add path parameter documentation
  if (pathParams.length > 0) {
    for (const param of pathParams) {
      parameters += `- name: ${param}
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: ${getParameterDescription(param)}
 *       `;
    }
  }
  
  // Add query parameter documentation for GET
  if (method === 'get') {
    parameters += `- name: page
 *         in: query
 *         required: false
 *         schema:
 *           type: integer
 *           default: 1
 *           minimum: 1
 *         description: Page number for pagination
 *       - name: limit
 *         in: query
 *         required: false
 *         schema:
 *           type: integer
 *           default: 10
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page (max 100)
 *       - name: sortBy
 *         in: query
 *         required: false
 *         schema:
 *           type: string
 *           default: createdAt
 *         description: Field to sort by
 *       - name: sortOrder
 *         in: query
 *         required: false
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort order (ascending or descending)
 *       - name: search
 *         in: query
 *         required: false
 *         schema:
 *           type: string
 *         description: Search term to filter results`;
  }
  
  return parameters;
}

/**
 * Get parameter description
 * @param {string} param - Parameter name
 * @returns {string} Parameter description
 */
function getParameterDescription(param) {
  if (param === 'id') {
    return 'Unique identifier of the resource';
  }
  
  if (param.endsWith('Id')) {
    const resource = param.replace('Id', '');
    return `Unique identifier of the ${resource}`;
  }
  
  return `${param} parameter`;
}

/**
 * Generate request body for a route
 * @param {string} method - HTTP method
 * @returns {string} Request body documentation
 */
function generateRequestBody(method) {
  if (method === 'get' || method === 'delete') {
    return '';
  }
  
  return `requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               # Add properties based on your data model
 *               name:
 *                 type: string
 *                 description: Name of the resource
 *               description:
 *                 type: string
 *                 description: Description of the resource
 *             example:
 *               name: "Example Name"
 *               description: "Example Description"`;
}

/**
 * Generate Swagger responses for a route
 * @param {string} method - HTTP method
 * @returns {string} Swagger responses
 */
function generateSwaggerResponses(method) {
  // Generate Swagger responses
  let responses = '';
  
  // Add success response
  switch (method) {
    case 'get':
      responses += `200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Data retrieved successfully"
 *                 data:
 *                   type: object
 *                   # Define your data schema here
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                   example: "2023-07-25T12:34:56.789Z"`;
      break;
    case 'post':
      responses += `201:
 *         description: Created
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Resource created successfully"
 *                 data:
 *                   type: object
 *                   # Define your data schema here
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                   example: "2023-07-25T12:34:56.789Z"`;
      break;
    case 'put':
    case 'patch':
      responses += `200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Resource updated successfully"
 *                 data:
 *                   type: object
 *                   # Define your data schema here
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                   example: "2023-07-25T12:34:56.789Z"`;
      break;
    case 'delete':
      responses += `200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Resource deleted successfully"
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                   example: "2023-07-25T12:34:56.789Z"`;
      break;
  }
  
  // Add error responses
  responses += `
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       422:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'`;
  
  return responses;
}

/**
 * Main function
 */
function main() {
  console.log('Enhancing API documentation...');
  
  // Get all route files
  const routeFiles = getRouteFiles();
  
  // Enhance documentation for each route file
  for (const filePath of routeFiles) {
    enhanceRouteDocumentation(filePath);
  }
  
  // Get all controller files
  const controllerFiles = getControllerFiles();
  
  // Enhance documentation for each controller file
  for (const filePath of controllerFiles) {
    enhanceControllerDocumentation(filePath);
  }
  
  console.log('Done!');
}

// Run main function
main();
