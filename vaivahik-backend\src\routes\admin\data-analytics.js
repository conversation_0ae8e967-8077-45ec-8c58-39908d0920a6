/**
 * Admin Data Analytics Routes
 * Provides insights into behavioral data volume and ML readiness
 */

const express = require('express');
const router = express.Router();
const DataAnalyticsService = require('../../services/dataAnalyticsService');
const { authenticateToken, requireAdmin } = require('../../middleware/auth');

const dataAnalyticsService = new DataAnalyticsService();

/**
 * @route GET /api/admin/data-analytics/ml-readiness
 * @desc Get ML readiness assessment based on current data volume
 * @access Admin
 */
router.get('/ml-readiness', authenticateToken, requireAdmin, async (req, res) => {
  try {
    console.log('🔍 Admin requesting ML readiness assessment...');
    
    const analysis = await dataAnalyticsService.analyzeBehavioralDataVolume();
    
    res.json({
      success: true,
      data: analysis,
      message: 'ML readiness analysis completed successfully'
    });

  } catch (error) {
    console.error('Error getting ML readiness:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to analyze ML readiness',
      error: error.message
    });
  }
});

/**
 * @route GET /api/admin/data-analytics/behavioral-data
 * @desc Get detailed behavioral data statistics
 * @access Admin
 */
router.get('/behavioral-data', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const analysis = await dataAnalyticsService.analyzeBehavioralDataVolume();
    
    // Format for admin dashboard
    const dashboardData = {
      overview: {
        totalInteractions: analysis.interactionStats.totalInteractions,
        totalUsers: analysis.engagementStats.totalUsers,
        activeUsers: analysis.engagementStats.activeUsers,
        avgInteractionsPerUser: analysis.interactionStats.avgInteractionsPerUser,
        dataCollectionDays: analysis.interactionStats.dataCollectionPeriod
      },
      
      interactionBreakdown: analysis.interactionStats.interactionsByType,
      
      mlReadiness: {
        v2_0: {
          status: analysis.mlReadiness.v2_0.isReady ? 'READY' : 'NOT_READY',
          percentage: analysis.mlReadiness.v2_0.readinessPercentage,
          requirements: analysis.mlReadiness.v2_0.requirements,
          current: analysis.mlReadiness.v2_0.current
        },
        v2_5: {
          status: analysis.mlReadiness.v2_5.isReady ? 'READY' : 'NOT_READY',
          percentage: analysis.mlReadiness.v2_5.readinessPercentage
        },
        v3_0: {
          status: analysis.mlReadiness.v3_0.isReady ? 'READY' : 'NOT_READY',
          percentage: analysis.mlReadiness.v3_0.readinessPercentage
        }
      },
      
      recommendations: analysis.summary.recommendations,
      nextSteps: analysis.summary.nextSteps
    };
    
    res.json({
      success: true,
      data: dashboardData,
      message: 'Behavioral data analysis completed successfully'
    });

  } catch (error) {
    console.error('Error getting behavioral data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to analyze behavioral data',
      error: error.message
    });
  }
});

/**
 * @route GET /api/admin/data-analytics/user-behavior/:userId
 * @desc Get detailed behavior analysis for a specific user
 * @access Admin
 */
router.get('/user-behavior/:userId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    
    const userAnalysis = await dataAnalyticsService.getUserBehaviorAnalysis(userId);
    
    res.json({
      success: true,
      data: {
        userId,
        ...userAnalysis
      },
      message: 'User behavior analysis completed successfully'
    });

  } catch (error) {
    console.error('Error getting user behavior analysis:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to analyze user behavior',
      error: error.message
    });
  }
});

/**
 * @route GET /api/admin/data-analytics/phase-readiness/:phase
 * @desc Get readiness assessment for a specific phase
 * @access Admin
 */
router.get('/phase-readiness/:phase', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { phase } = req.params;
    
    if (!['v2.0', 'v2.5', 'v3.0'].includes(phase)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid phase. Must be v2.0, v2.5, or v3.0'
      });
    }
    
    const analysis = await dataAnalyticsService.analyzeBehavioralDataVolume();
    const phaseKey = phase.replace('.', '_');
    const phaseReadiness = analysis.mlReadiness[phaseKey];
    
    if (!phaseReadiness) {
      return res.status(404).json({
        success: false,
        message: `Readiness data not found for phase ${phase}`
      });
    }
    
    // Calculate what's needed to reach readiness
    const gaps = {};
    Object.keys(phaseReadiness.checks).forEach(check => {
      if (!phaseReadiness.checks[check]) {
        const required = phaseReadiness.requirements[check.replace('min', '').toLowerCase()];
        const current = phaseReadiness.current[check.replace('min', '').toLowerCase()];
        gaps[check] = {
          required,
          current,
          gap: required - current
        };
      }
    });
    
    res.json({
      success: true,
      data: {
        phase,
        isReady: phaseReadiness.isReady,
        readinessPercentage: phaseReadiness.readinessPercentage,
        requirements: phaseReadiness.requirements,
        current: phaseReadiness.current,
        gaps,
        estimatedTimeToReadiness: calculateTimeToReadiness(gaps, analysis.interactionStats)
      },
      message: `Phase ${phase} readiness assessment completed`
    });

  } catch (error) {
    console.error('Error getting phase readiness:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to assess phase readiness',
      error: error.message
    });
  }
});

/**
 * @route POST /api/admin/data-analytics/simulate-data-growth
 * @desc Simulate future data growth and readiness timeline
 * @access Admin
 */
router.post('/simulate-data-growth', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { 
      dailyNewUsers = 10, 
      avgInteractionsPerUserPerDay = 5,
      simulationDays = 90 
    } = req.body;
    
    const currentAnalysis = await dataAnalyticsService.analyzeBehavioralDataVolume();
    
    // Simulate growth
    const simulation = simulateDataGrowth(
      currentAnalysis,
      dailyNewUsers,
      avgInteractionsPerUserPerDay,
      simulationDays
    );
    
    res.json({
      success: true,
      data: simulation,
      message: 'Data growth simulation completed successfully'
    });

  } catch (error) {
    console.error('Error simulating data growth:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to simulate data growth',
      error: error.message
    });
  }
});

/**
 * Calculate estimated time to readiness
 */
function calculateTimeToReadiness(gaps, currentStats) {
  if (Object.keys(gaps).length === 0) {
    return 0; // Already ready
  }
  
  // Estimate based on current growth rate
  const currentDailyInteractions = currentStats.recentInteractions / 30; // Last 30 days
  
  let maxDaysNeeded = 0;
  
  Object.values(gaps).forEach(gap => {
    if (gap.gap > 0 && currentDailyInteractions > 0) {
      const daysNeeded = Math.ceil(gap.gap / currentDailyInteractions);
      maxDaysNeeded = Math.max(maxDaysNeeded, daysNeeded);
    }
  });
  
  return maxDaysNeeded;
}

/**
 * Simulate future data growth
 */
function simulateDataGrowth(currentAnalysis, dailyNewUsers, avgInteractionsPerUserPerDay, simulationDays) {
  const current = currentAnalysis.interactionStats;
  const timeline = [];
  
  let totalUsers = currentAnalysis.engagementStats.totalUsers;
  let totalInteractions = current.totalInteractions;
  
  for (let day = 1; day <= simulationDays; day++) {
    // Add new users
    totalUsers += dailyNewUsers;
    
    // Add interactions (existing users + new users)
    const dailyInteractions = totalUsers * avgInteractionsPerUserPerDay;
    totalInteractions += dailyInteractions;
    
    // Check readiness every 7 days
    if (day % 7 === 0) {
      const avgInteractionsPerUser = totalInteractions / totalUsers;
      
      timeline.push({
        day,
        totalUsers,
        totalInteractions,
        avgInteractionsPerUser: Math.round(avgInteractionsPerUser * 100) / 100,
        v2_0_ready: avgInteractionsPerUser >= 50 && totalInteractions >= 5000,
        v2_5_ready: avgInteractionsPerUser >= 100 && totalInteractions >= 50000,
        v3_0_ready: avgInteractionsPerUser >= 200 && totalInteractions >= 200000
      });
    }
  }
  
  return {
    currentState: {
      totalUsers: currentAnalysis.engagementStats.totalUsers,
      totalInteractions: current.totalInteractions,
      avgInteractionsPerUser: current.avgInteractionsPerUser
    },
    projectedState: timeline[timeline.length - 1],
    timeline,
    assumptions: {
      dailyNewUsers,
      avgInteractionsPerUserPerDay,
      simulationDays
    }
  };
}

module.exports = router;
