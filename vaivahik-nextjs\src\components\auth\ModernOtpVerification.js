import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  CircularProgress,
  Alert,
  styled,
  InputAdornment,
  IconButton,
  Divider
} from '@mui/material';
import { Phone as PhoneIcon, Refresh as RefreshIcon, CheckCircle as CheckCircleIcon } from '@mui/icons-material';

// Styled components
const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  borderRadius: theme.shape.borderRadius * 2,
  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
  background: 'linear-gradient(to bottom, #ffffff, #f8f9fa)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '5px',
    background: 'var(--primary-gradient)',
  }
}));

const FloatingElement = styled(Box)(({ theme, position }) => ({
  position: 'absolute',
  width: '200px',
  height: '200px',
  borderRadius: '50%',
  background: 'linear-gradient(135deg, rgba(255, 95, 109, 0.05) 0%, rgba(255, 195, 113, 0.07) 100%)',
  zIndex: 0,
  ...(position === 'top-right' && {
    top: '-100px',
    right: '-100px',
  }),
  ...(position === 'bottom-left' && {
    bottom: '-100px',
    left: '-100px',
  }),
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: 12,
    transition: 'all 0.3s ease',
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: 'var(--primary-color)',
    },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      borderColor: 'var(--primary-color)',
      borderWidth: 2,
      boxShadow: '0 0 0 3px rgba(255, 95, 109, 0.2)',
    },
  },
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: 50,
  padding: '12px 30px',
  textTransform: 'none',
  fontWeight: 600,
  boxShadow: '0 4px 14px rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-3px)',
    boxShadow: '0 6px 20px rgba(0, 0, 0, 0.15)',
  },
  '&.MuiButton-contained': {
    background: 'var(--primary-gradient)',
  },
  '&.MuiButton-outlined': {
    borderWidth: 2,
    '&:hover': {
      borderWidth: 2,
    },
  },
}));

const OtpInputContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'center',
  gap: theme.spacing(1),
  margin: theme.spacing(3, 0),
}));

const OtpDigitInput = styled(TextField)(({ theme }) => ({
  width: '50px',
  '& .MuiOutlinedInput-root': {
    borderRadius: 12,
    textAlign: 'center',
    fontSize: '1.5rem',
    fontWeight: 'bold',
    '& input': {
      padding: '12px 0',
      textAlign: 'center',
    },
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: 'var(--primary-color)',
    },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      borderColor: 'var(--primary-color)',
      borderWidth: 2,
      boxShadow: '0 0 0 3px rgba(255, 95, 109, 0.2)',
    },
  },
}));

const TimerText = styled(Typography)(({ theme }) => ({
  color: 'var(--text-color-medium)',
  fontWeight: 500,
  marginTop: theme.spacing(2),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
}));

const ModernOtpVerification = ({ 
  phone, 
  onVerify, 
  onResendOtp, 
  onChangePhone,
  loading = false,
  error = '',
  success = ''
}) => {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [resendTimer, setResendTimer] = useState(0);
  const [resendLoading, setResendLoading] = useState(false);
  const inputRefs = useRef([]);

  // Initialize timer when component mounts
  useEffect(() => {
    setResendTimer(30); // 30 seconds cooldown for resend
  }, []);

  // Timer countdown
  useEffect(() => {
    let interval;
    if (resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer((prev) => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [resendTimer]);

  // Handle OTP input change
  const handleOtpChange = (index, value) => {
    // Allow only numbers
    if (value && !/^\d*$/.test(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1].focus();
    }
  };

  // Handle key press in OTP input
  const handleKeyDown = (index, e) => {
    // Move to previous input on backspace if current input is empty
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1].focus();
    }
  };

  // Handle paste event
  const handlePaste = (e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');
    
    // Check if pasted content is a 6-digit number
    if (/^\d{6}$/.test(pastedData)) {
      const digits = pastedData.split('');
      setOtp(digits);
      
      // Focus the last input
      inputRefs.current[5].focus();
    }
  };

  // Handle resend OTP
  const handleResendOtp = async () => {
    if (resendTimer > 0) return;
    
    setResendLoading(true);
    try {
      await onResendOtp();
      setResendTimer(30); // Reset timer
    } catch (error) {
      console.error('Error resending OTP:', error);
    } finally {
      setResendLoading(false);
    }
  };

  // Handle verify OTP
  const handleVerifyOtp = () => {
    const otpString = otp.join('');
    if (otpString.length === 6) {
      onVerify(otpString);
    }
  };

  return (
    <StyledPaper>
      <FloatingElement position="top-right" />
      <FloatingElement position="bottom-left" />
      
      <Box position="relative" zIndex={1}>
        <Typography 
          variant="h4" 
          align="center" 
          gutterBottom 
          sx={{ 
            fontFamily: 'var(--font-secondary)',
            background: 'var(--primary-gradient)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            fontWeight: 700,
            mb: 1
          }}
        >
          Verify Your Phone
        </Typography>
        
        <Typography variant="body1" align="center" paragraph sx={{ mb: 3, color: 'var(--text-color-medium)' }}>
          We've sent a 6-digit OTP to <strong>{phone}</strong>
        </Typography>
        
        {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
        {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}
        
        <OtpInputContainer onPaste={handlePaste}>
          {otp.map((digit, index) => (
            <OtpDigitInput
              key={index}
              value={digit}
              inputRef={(el) => (inputRefs.current[index] = el)}
              onChange={(e) => handleOtpChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              inputProps={{ maxLength: 1 }}
              autoFocus={index === 0}
            />
          ))}
        </OtpInputContainer>
        
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
          <StyledButton
            variant="contained"
            onClick={handleVerifyOtp}
            disabled={otp.join('').length !== 6 || loading}
            startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <CheckCircleIcon />}
            sx={{ minWidth: '200px' }}
          >
            {loading ? 'Verifying...' : 'Verify OTP'}
          </StyledButton>
        </Box>
        
        <Divider sx={{ my: 3 }} />
        
        <Box sx={{ textAlign: 'center' }}>
          <TimerText variant="body2">
            {resendTimer > 0 ? (
              <>Resend OTP in {resendTimer}s</>
            ) : (
              <StyledButton
                variant="text"
                onClick={handleResendOtp}
                disabled={resendLoading}
                startIcon={resendLoading ? <CircularProgress size={16} /> : <RefreshIcon />}
                sx={{ 
                  boxShadow: 'none',
                  '&:hover': {
                    boxShadow: 'none',
                  }
                }}
              >
                Resend OTP
              </StyledButton>
            )}
          </TimerText>
          
          <Typography variant="body2" sx={{ mt: 2 }}>
            Wrong number?{' '}
            <Button 
              variant="text" 
              onClick={onChangePhone}
              sx={{ 
                textTransform: 'none', 
                fontWeight: 600,
                color: 'var(--primary-color)',
                '&:hover': {
                  background: 'transparent',
                  color: 'var(--secondary-color)',
                }
              }}
            >
              Change Phone Number
            </Button>
          </Typography>
        </Box>
      </Box>
    </StyledPaper>
  );
};

export default ModernOtpVerification;
