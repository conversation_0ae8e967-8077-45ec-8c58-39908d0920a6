import { useState, useEffect } from 'react';
import EnhancedAdminLayout from '@/components/admin/EnhancedAdminLayout';
import ModerationSettings from '@/components/admin/featureManagement/ModerationSettings';
import { ToastContainer } from 'react-toastify';

// Feature categories
const CATEGORIES = {
  COMMUNICATION: 'Communication',
  SEARCH: 'Search',
  MATCHING: 'Matching',
  VISIBILITY: 'Visibility',
  PROFILE: 'Profile',
  OTHER: 'Other'
};

export default function FeatureManagement() {
  const [features, setFeatures] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentFeature, setCurrentFeature] = useState(null);
  const [showFeatureModal, setShowFeatureModal] = useState(false);
  const [showAccessModal, setShowAccessModal] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [confirmationAction, setConfirmationAction] = useState(null);
  const [notification, setNotification] = useState({ show: false, message: '', type: 'success' });
  const [activeAccessTab, setActiveAccessTab] = useState('basic');
  const [activeTab, setActiveTab] = useState('features'); // 'features' or 'moderation'

  // Fetch features on component mount
  useEffect(() => {
    fetchFeatures();
  }, []);

  // Fetch features from API
  const fetchFeatures = async () => {
    setLoading(true);
    try {
      // Call the API endpoint
      const response = await fetch('/api/admin/features');

      if (!response.ok) {
        throw new Error(`Failed to fetch features: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        console.log('Features loaded successfully:', data.features.length);
        setFeatures(data.features);
      } else {
        console.error('API returned error:', data.message);
        showNotification(data.message || 'Failed to load features', 'error');
        // Fallback to empty array if API fails
        setFeatures([]);
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching features:', error);
      setLoading(false);
      showNotification('Failed to load features. Using mock data.', 'warning');

      // Attempt to load mock data directly
      try {
        const mockFeatures = getMockFeatures();
        console.log('Using mock features:', mockFeatures.length);
        setFeatures(mockFeatures);
      } catch (mockError) {
        console.error('Error loading mock features:', mockError);
        setFeatures([]);
      }
    }
  };

  // Function to get mock features if API fails
  const getMockFeatures = () => {
    return [
      {
        id: 'feature-1',
        name: 'view-contacts',
        displayName: 'View Contact Details',
        description: 'View phone numbers and email addresses of other users',
        category: 'COMMUNICATION',
        isActive: true,
        accessRules: {
          basic: { isEnabled: false, limitType: 'none', limitValue: 0, upgradeMessage: 'Upgrade to view contact details' },
          verified: { isEnabled: true, limitType: 'daily', limitValue: 5, upgradeMessage: 'Upgrade to view more contacts' },
          premium: { isEnabled: true, limitType: 'none', limitValue: 0, upgradeMessage: '' }
        }
      },
      {
        id: 'feature-2',
        name: 'advanced-search',
        displayName: 'Advanced Search Filters',
        description: 'Use additional filters like education, profession, and income',
        category: 'SEARCH',
        isActive: true,
        accessRules: {
          basic: { isEnabled: false, limitType: 'none', limitValue: 0, upgradeMessage: 'Upgrade to use advanced search' },
          verified: { isEnabled: true, limitType: 'none', limitValue: 0, upgradeMessage: '' },
          premium: { isEnabled: true, limitType: 'none', limitValue: 0, upgradeMessage: '' }
        }
      },
      {
        id: 'feature-3',
        name: 'send-messages',
        displayName: 'Send Messages',
        description: 'Initiate conversations with other users',
        category: 'COMMUNICATION',
        isActive: true,
        accessRules: {
          basic: { isEnabled: true, limitType: 'daily', limitValue: 3, upgradeMessage: 'Upgrade to send more messages' },
          verified: { isEnabled: true, limitType: 'daily', limitValue: 10, upgradeMessage: 'Upgrade to send unlimited messages' },
          premium: { isEnabled: true, limitType: 'none', limitValue: 0, upgradeMessage: '' }
        }
      }
    ];
  };

  // Group features by category
  const groupedFeatures = features.reduce((acc, feature) => {
    const category = feature.category;
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(feature);
    return acc;
  }, {});

  // Handle adding a new feature
  const handleAddFeature = () => {
    setCurrentFeature({
      name: '',
      displayName: '',
      description: '',
      category: 'COMMUNICATION',
      isActive: true,
      accessRules: {
        basic: { isEnabled: false, limitType: 'none', limitValue: 0, upgradeMessage: '' },
        verified: { isEnabled: true, limitType: 'none', limitValue: 0, upgradeMessage: '' },
        premium: { isEnabled: true, limitType: 'none', limitValue: 0, upgradeMessage: '' }
      }
    });
    setShowFeatureModal(true);
  };

  // Handle editing a feature
  const handleEditFeature = (feature) => {
    setCurrentFeature({ ...feature });
    setShowFeatureModal(true);
  };

  // Handle saving a feature
  const handleSaveFeature = async () => {
    try {
      // Call the API endpoint
      const method = currentFeature.id ? 'PUT' : 'POST';
      const response = await fetch('/api/admin/features', {
        method: method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(currentFeature)
      });

      if (!response.ok) {
        throw new Error(`Failed to ${currentFeature.id ? 'update' : 'create'} feature: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        if (currentFeature.id) {
          // Update existing feature
          setFeatures(features.map(f => f.id === currentFeature.id ? data.feature || currentFeature : f));
          showNotification(data.message || 'Feature updated successfully');
        } else {
          // Add new feature
          setFeatures([...features, data.feature]);
          showNotification(data.message || 'Feature added successfully');
        }

        setShowFeatureModal(false);
      } else {
        console.error('API returned error:', data.message);
        showNotification(data.message || 'Failed to save feature', 'error');
      }
    } catch (error) {
      console.error('Error saving feature:', error);
      showNotification('Failed to save feature', 'error');
    }
  };

  // Handle deleting a feature
  const handleDeleteFeature = (feature) => {
    setCurrentFeature(feature);
    setConfirmationAction('delete');
    setShowConfirmationModal(true);
  };

  // Confirm delete action
  const confirmDelete = async () => {
    try {
      // Call the API endpoint
      const response = await fetch(`/api/admin/features/${currentFeature.id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error(`Failed to delete feature: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Update state
        setFeatures(features.filter(f => f.id !== currentFeature.id));
        showNotification(data.message || 'Feature deleted successfully');
        setShowConfirmationModal(false);
      } else {
        console.error('API returned error:', data.message);
        showNotification(data.message || 'Failed to delete feature', 'error');
      }
    } catch (error) {
      console.error('Error deleting feature:', error);
      showNotification('Failed to delete feature', 'error');
    }
  };

  // Handle editing access rules
  const handleEditAccess = (feature) => {
    setCurrentFeature({ ...feature });
    setShowAccessModal(true);
  };

  // Handle saving access rules
  const handleSaveAccess = async () => {
    try {
      // Call the API endpoint
      const response = await fetch(`/api/admin/features/${currentFeature.id}/access`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(currentFeature.accessRules)
      });

      if (!response.ok) {
        throw new Error(`Failed to update access rules: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Update state
        setFeatures(features.map(f => f.id === currentFeature.id ? currentFeature : f));
        showNotification(data.message || 'Access rules updated successfully');
        setShowAccessModal(false);
      } else {
        console.error('API returned error:', data.message);
        showNotification(data.message || 'Failed to save access rules', 'error');
      }
    } catch (error) {
      console.error('Error saving access rules:', error);
      showNotification('Failed to save access rules', 'error');
    }
  };

  // Show notification
  const showNotification = (message, type = 'success') => {
    setNotification({ show: true, message, type });
    setTimeout(() => {
      setNotification({ ...notification, show: false });
    }, 3000);
  };

  // Get access badge text and class
  const getAccessBadge = (accessRule) => {
    if (!accessRule.isEnabled) {
      return { text: 'Disabled', className: 'disabled' };
    }

    if (accessRule.limitType === 'none') {
      return { text: 'Enabled', className: 'enabled' };
    }

    return {
      text: accessRule.limitType === 'daily'
        ? `${accessRule.limitValue}/day`
        : `${accessRule.limitValue} total`,
      className: 'limited'
    };
  };

  return (
    <EnhancedAdminLayout title="Feature Management">
      <ToastContainer position="top-right" autoClose={5000} />
      {/* Content Header */}
      <div className="content-header">
        <h2 className="page-title">Feature Management</h2>
        <div className="header-actions">
          {activeTab === 'features' && (
            <button className="btn btn-primary" onClick={handleAddFeature}>
              <span className="btn-icon">+</span> Add New Feature
            </button>
          )}
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="tab-container">
        <div className="tab-nav">
          <div
            className={`tab-nav-item ${activeTab === 'features' ? 'active' : ''}`}
            onClick={() => setActiveTab('features')}
          >
            Feature Access
          </div>
          <div
            className={`tab-nav-item ${activeTab === 'moderation' ? 'active' : ''}`}
            onClick={() => setActiveTab('moderation')}
          >
            Content Moderation
          </div>
        </div>
      </div>

      {activeTab === 'moderation' ? (
        <div className="card">
          <div className="card-header">
            <h3 className="table-title">Content Moderation Settings</h3>
            <p className="card-subtitle">Configure how messages are moderated for different user tiers</p>
          </div>
          <div className="card-body">
            <ModerationSettings />
          </div>
        </div>
      ) : (
        /* Feature Management Content */
        <div className="card">
          <div className="card-header">
            <h3 className="table-title">Manage Features</h3>
            <p className="card-subtitle">Configure which features are available to different user tiers</p>
          </div>
          <div className="card-body">
          {loading ? (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>Loading features...</p>
            </div>
          ) : (
            <div id="featuresContainer">
              {Object.keys(groupedFeatures).length === 0 ? (
                <p>No features found. Click "Add New Feature" to create one.</p>
              ) : (
                Object.keys(groupedFeatures).map(category => (
                  <div key={category}>
                    <h4 className="category-header">{CATEGORIES[category]}</h4>
                    <div className="table-container">
                      <table className="data-table feature-access-table">
                        <thead>
                          <tr>
                            <th width="30%">Feature</th>
                            <th width="10%">Status</th>
                            <th width="15%">Basic</th>
                            <th width="15%">Verified</th>
                            <th width="15%">Premium</th>
                            <th width="15%">Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {groupedFeatures[category].map(feature => (
                            <tr key={feature.id}>
                              <td>
                                <div><strong>{feature.displayName}</strong></div>
                                <div className="feature-description">{feature.description}</div>
                              </td>
                              <td>
                                <span className={`access-badge ${feature.isActive ? 'enabled' : 'disabled'}`}>
                                  {feature.isActive ? 'Active' : 'Inactive'}
                                </span>
                              </td>
                              <td>
                                <span className={`access-badge ${getAccessBadge(feature.accessRules.basic).className}`}>
                                  {getAccessBadge(feature.accessRules.basic).text}
                                </span>
                              </td>
                              <td>
                                <span className={`access-badge ${getAccessBadge(feature.accessRules.verified).className}`}>
                                  {getAccessBadge(feature.accessRules.verified).text}
                                </span>
                              </td>
                              <td>
                                <span className={`access-badge ${getAccessBadge(feature.accessRules.premium).className}`}>
                                  {getAccessBadge(feature.accessRules.premium).text}
                                </span>
                              </td>
                              <td>
                                <div className="action-buttons">
                                  <button
                                    className="action-btn access-btn"
                                    title="Edit Access Rules"
                                    onClick={() => handleEditAccess(feature)}
                                  >
                                    🔑
                                  </button>
                                  <button
                                    className="action-btn edit-btn"
                                    title="Edit Feature"
                                    onClick={() => handleEditFeature(feature)}
                                  >
                                    ✏️
                                  </button>
                                  <button
                                    className="action-btn delete-btn"
                                    title="Delete Feature"
                                    onClick={() => handleDeleteFeature(feature)}
                                  >
                                    🗑️
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
          </div>
        </div>
      )}

      {/* Feature Modal */}
      {showFeatureModal ? (
        <div className="modal show">
          <div className="modal-content">
            <div className="modal-header">
              <h3 className="modal-title">
                {currentFeature.id ? 'Edit Feature' : 'Add New Feature'}
              </h3>
              <button
                className="close-modal"
                onClick={() => setShowFeatureModal(false)}
                aria-label="Close"
              >
                &times;
              </button>
            </div>
            <div className="modal-body">
              <form id="featureForm">
                <div className="form-group">
                  <label htmlFor="featureName">Feature Name (API Key)</label>
                  <input
                    type="text"
                    id="featureName"
                    value={currentFeature.name}
                    onChange={(e) => setCurrentFeature({...currentFeature, name: e.target.value})}
                    placeholder="e.g., view-contacts"
                    required
                  />
                  <div className="form-hint">This is used in the API and should be unique. Use kebab-case.</div>
                </div>
                <div className="form-group">
                  <label htmlFor="featureDisplayName">Display Name</label>
                  <input
                    type="text"
                    id="featureDisplayName"
                    value={currentFeature.displayName}
                    onChange={(e) => setCurrentFeature({...currentFeature, displayName: e.target.value})}
                    placeholder="e.g., View Contact Details"
                    required
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="featureDescription">Description</label>
                  <textarea
                    id="featureDescription"
                    value={currentFeature.description}
                    onChange={(e) => setCurrentFeature({...currentFeature, description: e.target.value})}
                    rows="3"
                    placeholder="Describe what this feature does"
                  ></textarea>
                </div>
                <div className="form-group">
                  <label htmlFor="featureCategory">Category</label>
                  <select
                    id="featureCategory"
                    value={currentFeature.category}
                    onChange={(e) => setCurrentFeature({...currentFeature, category: e.target.value})}
                    required
                  >
                    <option value="COMMUNICATION">Communication</option>
                    <option value="SEARCH">Search</option>
                    <option value="MATCHING">Matching</option>
                    <option value="VISIBILITY">Visibility</option>
                    <option value="PROFILE">Profile</option>
                    <option value="OTHER">Other</option>
                  </select>
                </div>
                <div className="form-group">
                  <div className="form-check">
                    <input
                      type="checkbox"
                      id="featureActive"
                      checked={currentFeature.isActive}
                      onChange={(e) => setCurrentFeature({...currentFeature, isActive: e.target.checked})}
                    />
                    <label htmlFor="featureActive">Active</label>
                  </div>
                </div>
              </form>
            </div>
            <div className="modal-footer">
              <button
                className="btn btn-secondary"
                onClick={() => setShowFeatureModal(false)}
              >
                Cancel
              </button>
              <button
                className="btn btn-primary"
                onClick={handleSaveFeature}
              >
                Save Feature
              </button>
            </div>
          </div>
        </div>
      ) : null}

      {/* Access Rules Modal */}
      {showAccessModal && currentFeature && (
        <div className="modal show">
          <div className="modal-content">
            <div className="modal-header">
              <h3 className="modal-title">Edit Access Rules: {currentFeature.displayName}</h3>
              <button
                className="close-modal"
                onClick={() => setShowAccessModal(false)}
                aria-label="Close"
              >
                &times;
              </button>
            </div>
            <div className="modal-body">
              <form id="accessForm">
                <div className="tab-container">
                  <div className="tab-nav">
                    <div
                      className={`tab-nav-item ${activeAccessTab === 'basic' ? 'active' : ''}`}
                      onClick={() => setActiveAccessTab('basic')}
                    >
                      Basic (Free)
                    </div>
                    <div
                      className={`tab-nav-item ${activeAccessTab === 'verified' ? 'active' : ''}`}
                      onClick={() => setActiveAccessTab('verified')}
                    >
                      Verified
                    </div>
                    <div
                      className={`tab-nav-item ${activeAccessTab === 'premium' ? 'active' : ''}`}
                      onClick={() => setActiveAccessTab('premium')}
                    >
                      Premium
                    </div>
                  </div>

                  {/* Basic Tab */}
                  <div className={`tab-content ${activeAccessTab === 'basic' ? 'active' : ''}`}>
                    <div className="form-group">
                      <div className="form-check">
                        <input
                          type="checkbox"
                          id="basicEnabled"
                          checked={currentFeature.accessRules.basic.isEnabled}
                          onChange={(e) => setCurrentFeature({
                            ...currentFeature,
                            accessRules: {
                              ...currentFeature.accessRules,
                              basic: {
                                ...currentFeature.accessRules.basic,
                                isEnabled: e.target.checked
                              }
                            }
                          })}
                        />
                        <label htmlFor="basicEnabled">Enable for Basic users</label>
                      </div>
                    </div>

                    <div className="form-group">
                      <label>Usage Limits</label>
                      <div className="limit-type-container">
                        <div className="form-check">
                          <input
                            type="radio"
                            id="basicLimitTypeNone"
                            name="basicLimitType"
                            value="none"
                            checked={currentFeature.accessRules.basic.limitType === 'none'}
                            onChange={() => setCurrentFeature({
                              ...currentFeature,
                              accessRules: {
                                ...currentFeature.accessRules,
                                basic: {
                                  ...currentFeature.accessRules.basic,
                                  limitType: 'none'
                                }
                              }
                            })}
                          />
                          <label htmlFor="basicLimitTypeNone">No limit</label>
                        </div>
                        <div className="form-check">
                          <input
                            type="radio"
                            id="basicLimitTypeDaily"
                            name="basicLimitType"
                            value="daily"
                            checked={currentFeature.accessRules.basic.limitType === 'daily'}
                            onChange={() => setCurrentFeature({
                              ...currentFeature,
                              accessRules: {
                                ...currentFeature.accessRules,
                                basic: {
                                  ...currentFeature.accessRules.basic,
                                  limitType: 'daily'
                                }
                              }
                            })}
                          />
                          <label htmlFor="basicLimitTypeDaily">Daily limit</label>
                        </div>
                        <div className="form-check">
                          <input
                            type="radio"
                            id="basicLimitTypeTotal"
                            name="basicLimitType"
                            value="total"
                            checked={currentFeature.accessRules.basic.limitType === 'total'}
                            onChange={() => setCurrentFeature({
                              ...currentFeature,
                              accessRules: {
                                ...currentFeature.accessRules,
                                basic: {
                                  ...currentFeature.accessRules.basic,
                                  limitType: 'total'
                                }
                              }
                            })}
                          />
                          <label htmlFor="basicLimitTypeTotal">Total limit</label>
                        </div>
                      </div>

                      {currentFeature.accessRules.basic.limitType !== 'none' && (
                        <div className="form-group" style={{ marginTop: '15px' }}>
                          <label htmlFor="basicLimitValue">Limit Value</label>
                          <input
                            type="number"
                            id="basicLimitValue"
                            value={currentFeature.accessRules.basic.limitValue}
                            onChange={(e) => setCurrentFeature({
                              ...currentFeature,
                              accessRules: {
                                ...currentFeature.accessRules,
                                basic: {
                                  ...currentFeature.accessRules.basic,
                                  limitValue: parseInt(e.target.value) || 0
                                }
                              }
                            })}
                            min="1"
                          />
                        </div>
                      )}
                    </div>

                    <div className="form-group">
                      <label htmlFor="basicUpgradeMessage">Upgrade Message</label>
                      <textarea
                        id="basicUpgradeMessage"
                        value={currentFeature.accessRules.basic.upgradeMessage}
                        onChange={(e) => setCurrentFeature({
                          ...currentFeature,
                          accessRules: {
                            ...currentFeature.accessRules,
                            basic: {
                              ...currentFeature.accessRules.basic,
                              upgradeMessage: e.target.value
                            }
                          }
                        })}
                        rows="2"
                        placeholder="Message shown to users when they reach their limit"
                      ></textarea>
                    </div>
                  </div>

                  {/* Verified Tab */}
                  <div className={`tab-content ${activeAccessTab === 'verified' ? 'active' : ''}`}>
                    <div className="form-group">
                      <div className="form-check">
                        <input
                          type="checkbox"
                          id="verifiedEnabled"
                          checked={currentFeature.accessRules.verified.isEnabled}
                          onChange={(e) => setCurrentFeature({
                            ...currentFeature,
                            accessRules: {
                              ...currentFeature.accessRules,
                              verified: {
                                ...currentFeature.accessRules.verified,
                                isEnabled: e.target.checked
                              }
                            }
                          })}
                        />
                        <label htmlFor="verifiedEnabled">Enable for Verified users</label>
                      </div>
                    </div>

                    <div className="form-group">
                      <label>Usage Limits</label>
                      <div className="limit-type-container">
                        <div className="form-check">
                          <input
                            type="radio"
                            id="verifiedLimitTypeNone"
                            name="verifiedLimitType"
                            value="none"
                            checked={currentFeature.accessRules.verified.limitType === 'none'}
                            onChange={() => setCurrentFeature({
                              ...currentFeature,
                              accessRules: {
                                ...currentFeature.accessRules,
                                verified: {
                                  ...currentFeature.accessRules.verified,
                                  limitType: 'none'
                                }
                              }
                            })}
                          />
                          <label htmlFor="verifiedLimitTypeNone">No limit</label>
                        </div>
                        <div className="form-check">
                          <input
                            type="radio"
                            id="verifiedLimitTypeDaily"
                            name="verifiedLimitType"
                            value="daily"
                            checked={currentFeature.accessRules.verified.limitType === 'daily'}
                            onChange={() => setCurrentFeature({
                              ...currentFeature,
                              accessRules: {
                                ...currentFeature.accessRules,
                                verified: {
                                  ...currentFeature.accessRules.verified,
                                  limitType: 'daily'
                                }
                              }
                            })}
                          />
                          <label htmlFor="verifiedLimitTypeDaily">Daily limit</label>
                        </div>
                        <div className="form-check">
                          <input
                            type="radio"
                            id="verifiedLimitTypeTotal"
                            name="verifiedLimitType"
                            value="total"
                            checked={currentFeature.accessRules.verified.limitType === 'total'}
                            onChange={() => setCurrentFeature({
                              ...currentFeature,
                              accessRules: {
                                ...currentFeature.accessRules,
                                verified: {
                                  ...currentFeature.accessRules.verified,
                                  limitType: 'total'
                                }
                              }
                            })}
                          />
                          <label htmlFor="verifiedLimitTypeTotal">Total limit</label>
                        </div>
                      </div>

                      {currentFeature.accessRules.verified.limitType !== 'none' && (
                        <div className="form-group" style={{ marginTop: '15px' }}>
                          <label htmlFor="verifiedLimitValue">Limit Value</label>
                          <input
                            type="number"
                            id="verifiedLimitValue"
                            value={currentFeature.accessRules.verified.limitValue}
                            onChange={(e) => setCurrentFeature({
                              ...currentFeature,
                              accessRules: {
                                ...currentFeature.accessRules,
                                verified: {
                                  ...currentFeature.accessRules.verified,
                                  limitValue: parseInt(e.target.value) || 0
                                }
                              }
                            })}
                            min="1"
                          />
                        </div>
                      )}
                    </div>

                    <div className="form-group">
                      <label htmlFor="verifiedUpgradeMessage">Upgrade Message</label>
                      <textarea
                        id="verifiedUpgradeMessage"
                        value={currentFeature.accessRules.verified.upgradeMessage}
                        onChange={(e) => setCurrentFeature({
                          ...currentFeature,
                          accessRules: {
                            ...currentFeature.accessRules,
                            verified: {
                              ...currentFeature.accessRules.verified,
                              upgradeMessage: e.target.value
                            }
                          }
                        })}
                        rows="2"
                        placeholder="Message shown to users when they reach their limit"
                      ></textarea>
                    </div>
                  </div>

                  {/* Premium Tab */}
                  <div className={`tab-content ${activeAccessTab === 'premium' ? 'active' : ''}`}>
                    <div className="form-group">
                      <div className="form-check">
                        <input
                          type="checkbox"
                          id="premiumEnabled"
                          checked={currentFeature.accessRules.premium.isEnabled}
                          onChange={(e) => setCurrentFeature({
                            ...currentFeature,
                            accessRules: {
                              ...currentFeature.accessRules,
                              premium: {
                                ...currentFeature.accessRules.premium,
                                isEnabled: e.target.checked
                              }
                            }
                          })}
                        />
                        <label htmlFor="premiumEnabled">Enable for Premium users</label>
                      </div>
                    </div>

                    <div className="form-group">
                      <label>Usage Limits</label>
                      <div className="limit-type-container">
                        <div className="form-check">
                          <input
                            type="radio"
                            id="premiumLimitTypeNone"
                            name="premiumLimitType"
                            value="none"
                            checked={currentFeature.accessRules.premium.limitType === 'none'}
                            onChange={() => setCurrentFeature({
                              ...currentFeature,
                              accessRules: {
                                ...currentFeature.accessRules,
                                premium: {
                                  ...currentFeature.accessRules.premium,
                                  limitType: 'none'
                                }
                              }
                            })}
                          />
                          <label htmlFor="premiumLimitTypeNone">No limit</label>
                        </div>
                        <div className="form-check">
                          <input
                            type="radio"
                            id="premiumLimitTypeDaily"
                            name="premiumLimitType"
                            value="daily"
                            checked={currentFeature.accessRules.premium.limitType === 'daily'}
                            onChange={() => setCurrentFeature({
                              ...currentFeature,
                              accessRules: {
                                ...currentFeature.accessRules,
                                premium: {
                                  ...currentFeature.accessRules.premium,
                                  limitType: 'daily'
                                }
                              }
                            })}
                          />
                          <label htmlFor="premiumLimitTypeDaily">Daily limit</label>
                        </div>
                        <div className="form-check">
                          <input
                            type="radio"
                            id="premiumLimitTypeTotal"
                            name="premiumLimitType"
                            value="total"
                            checked={currentFeature.accessRules.premium.limitType === 'total'}
                            onChange={() => setCurrentFeature({
                              ...currentFeature,
                              accessRules: {
                                ...currentFeature.accessRules,
                                premium: {
                                  ...currentFeature.accessRules.premium,
                                  limitType: 'total'
                                }
                              }
                            })}
                          />
                          <label htmlFor="premiumLimitTypeTotal">Total limit</label>
                        </div>
                      </div>

                      {currentFeature.accessRules.premium.limitType !== 'none' && (
                        <div className="form-group" style={{ marginTop: '15px' }}>
                          <label htmlFor="premiumLimitValue">Limit Value</label>
                          <input
                            type="number"
                            id="premiumLimitValue"
                            value={currentFeature.accessRules.premium.limitValue}
                            onChange={(e) => setCurrentFeature({
                              ...currentFeature,
                              accessRules: {
                                ...currentFeature.accessRules,
                                premium: {
                                  ...currentFeature.accessRules.premium,
                                  limitValue: parseInt(e.target.value) || 0
                                }
                              }
                            })}
                            min="1"
                          />
                        </div>
                      )}
                    </div>

                    <div className="form-group">
                      <label htmlFor="premiumUpgradeMessage">Upgrade Message</label>
                      <textarea
                        id="premiumUpgradeMessage"
                        value={currentFeature.accessRules.premium.upgradeMessage}
                        onChange={(e) => setCurrentFeature({
                          ...currentFeature,
                          accessRules: {
                            ...currentFeature.accessRules,
                            premium: {
                              ...currentFeature.accessRules.premium,
                              upgradeMessage: e.target.value
                            }
                          }
                        })}
                        rows="2"
                        placeholder="Message shown to users when they reach their limit"
                      ></textarea>
                    </div>
                  </div>
                </div>
              </form>
            </div>
            <div className="modal-footer">
              <button
                className="btn btn-secondary"
                onClick={() => setShowAccessModal(false)}
              >
                Cancel
              </button>
              <button
                className="btn btn-primary"
                onClick={handleSaveAccess}
              >
                Save Access Rules
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmationModal && (
        <div className="modal show">
          <div className="modal-content confirmation-modal-content">
            <div className="modal-header">
              <h3 className="modal-title">Confirm Action</h3>
              <button
                className="close-modal"
                onClick={() => setShowConfirmationModal(false)}
                aria-label="Close"
              >
                &times;
              </button>
            </div>
            <div className="modal-body">
              {confirmationAction === 'delete' && (
                <p>
                  Are you sure you want to delete the feature "{currentFeature.displayName}"?
                  This action cannot be undone.
                </p>
              )}
            </div>
            <div className="modal-footer">
              <button
                className="btn btn-secondary"
                onClick={() => setShowConfirmationModal(false)}
              >
                Cancel
              </button>
              {confirmationAction === 'delete' && (
                <button
                  className="btn btn-danger"
                  onClick={confirmDelete}
                >
                  Delete
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Notification Toast */}
      {notification.show && (
        <div className={`notification-toast show notification-${notification.type}`}>
          <div className="notification-content">
            <span>{notification.message}</span>
          </div>
        </div>
      )}
    </EnhancedAdminLayout>
  );
}

