import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Container,
  Grid,
  Box,
  Paper,
  Typography,
  Tabs,
  Tab,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Avatar,
  Button,
  Alert,
  Snackbar
} from '@mui/material';
import SecurityIcon from '@mui/icons-material/Security';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import NotificationsIcon from '@mui/icons-material/Notifications';
import PaymentIcon from '@mui/icons-material/Payment';
import HelpIcon from '@mui/icons-material/Help';
import LogoutIcon from '@mui/icons-material/Logout';
import PrivacySettings from '@/components/settings/PrivacySettings';

// Mock user data (would come from auth context in a real app)
const mockUser = {
  id: 'user123',
  name: '<PERSON><PERSON>',
  email: '<EMAIL>',
  phone: '+91 **********',
  profilePhoto: '/mock-profiles/male1.jpg',
  isPremium: true,
  isVerified: true,
  privacySettings: {
    photoPrivacy: 'ALL_USERS',
    phonePrivacy: 'PREMIUM_USERS',
    emailPrivacy: 'PREMIUM_USERS',
    socialMediaPrivacy: 'PREMIUM_USERS',
    educationPrivacy: 'ALL_USERS',
    careerPrivacy: 'ALL_USERS',
    familyPrivacy: 'ALL_USERS',
    birthDetailsPrivacy: 'ALL_USERS',
    onlineStatusPrivacy: 'ALL_USERS',
    lastActivePrivacy: 'ALL_USERS'
  }
};

export default function SettingsPage() {
  const router = useRouter();
  const [activeSection, setActiveSection] = useState('privacy');
  const [user, setUser] = useState(mockUser);
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'success'
  });
  
  // Handle section change
  const handleSectionChange = (section) => {
    setActiveSection(section);
  };
  
  // Handle privacy settings save
  const handleSavePrivacySettings = (settings) => {
    // In a real app, this would make an API call to update the user's settings
    setUser({
      ...user,
      privacySettings: settings
    });
    
    // Show success notification
    setNotification({
      open: true,
      message: 'Privacy settings saved successfully',
      severity: 'success'
    });
  };
  
  // Close notification
  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false
    });
  };
  
  return (
    <>
      <Head>
        <title>Account Settings | Vaivahik</title>
        <meta name="description" content="Manage your account settings and privacy" />
      </Head>
      
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography variant="h4" gutterBottom>
          Account Settings
        </Typography>
        
        <Grid container spacing={4}>
          {/* Sidebar */}
          <Grid item xs={12} md={3}>
            <Paper elevation={1} sx={{ borderRadius: 2 }}>
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Avatar
                  src={user.profilePhoto}
                  alt={user.name}
                  sx={{ width: 80, height: 80, mx: 'auto', mb: 2 }}
                />
                <Typography variant="h6">{user.name}</Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {user.email}
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mt: 1 }}>
                  {user.isPremium && (
                    <Typography 
                      variant="caption" 
                      sx={{ 
                        bgcolor: 'primary.main', 
                        color: 'white', 
                        px: 1, 
                        py: 0.5, 
                        borderRadius: 1 
                      }}
                    >
                      Premium
                    </Typography>
                  )}
                  {user.isVerified && (
                    <Typography 
                      variant="caption" 
                      sx={{ 
                        bgcolor: 'success.main', 
                        color: 'white', 
                        px: 1, 
                        py: 0.5, 
                        borderRadius: 1 
                      }}
                    >
                      Verified
                    </Typography>
                  )}
                </Box>
              </Box>
              
              <Divider />
              
              <List sx={{ py: 0 }}>
                <ListItemButton 
                  selected={activeSection === 'profile'} 
                  onClick={() => handleSectionChange('profile')}
                >
                  <ListItemIcon>
                    <AccountCircleIcon />
                  </ListItemIcon>
                  <ListItemText primary="Profile" />
                </ListItemButton>
                
                <ListItemButton 
                  selected={activeSection === 'privacy'} 
                  onClick={() => handleSectionChange('privacy')}
                >
                  <ListItemIcon>
                    <SecurityIcon />
                  </ListItemIcon>
                  <ListItemText primary="Privacy" />
                </ListItemButton>
                
                <ListItemButton 
                  selected={activeSection === 'notifications'} 
                  onClick={() => handleSectionChange('notifications')}
                >
                  <ListItemIcon>
                    <NotificationsIcon />
                  </ListItemIcon>
                  <ListItemText primary="Notifications" />
                </ListItemButton>
                
                <ListItemButton 
                  selected={activeSection === 'payments'} 
                  onClick={() => handleSectionChange('payments')}
                >
                  <ListItemIcon>
                    <PaymentIcon />
                  </ListItemIcon>
                  <ListItemText primary="Payments" />
                </ListItemButton>
                
                <ListItemButton 
                  selected={activeSection === 'help'} 
                  onClick={() => handleSectionChange('help')}
                >
                  <ListItemIcon>
                    <HelpIcon />
                  </ListItemIcon>
                  <ListItemText primary="Help & Support" />
                </ListItemButton>
              </List>
              
              <Divider />
              
              <List>
                <ListItemButton onClick={() => router.push('/logout')}>
                  <ListItemIcon>
                    <LogoutIcon />
                  </ListItemIcon>
                  <ListItemText primary="Logout" />
                </ListItemButton>
              </List>
            </Paper>
          </Grid>
          
          {/* Main Content */}
          <Grid item xs={12} md={9}>
            {activeSection === 'privacy' && (
              <PrivacySettings 
                user={user} 
                onSave={handleSavePrivacySettings} 
              />
            )}
            
            {activeSection === 'profile' && (
              <Paper elevation={0} sx={{ p: 3, borderRadius: 2 }}>
                <Typography variant="h5" gutterBottom>
                  Profile Settings
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Profile settings will be implemented here.
                </Typography>
              </Paper>
            )}
            
            {activeSection === 'notifications' && (
              <Paper elevation={0} sx={{ p: 3, borderRadius: 2 }}>
                <Typography variant="h5" gutterBottom>
                  Notification Settings
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Notification settings will be implemented here.
                </Typography>
              </Paper>
            )}
            
            {activeSection === 'payments' && (
              <Paper elevation={0} sx={{ p: 3, borderRadius: 2 }}>
                <Typography variant="h5" gutterBottom>
                  Payment Settings
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Payment settings will be implemented here.
                </Typography>
              </Paper>
            )}
            
            {activeSection === 'help' && (
              <Paper elevation={0} sx={{ p: 3, borderRadius: 2 }}>
                <Typography variant="h5" gutterBottom>
                  Help & Support
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Help and support options will be implemented here.
                </Typography>
              </Paper>
            )}
          </Grid>
        </Grid>
      </Container>
      
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
      >
        <Alert 
          onClose={handleCloseNotification} 
          severity={notification.severity}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </>
  );
}
