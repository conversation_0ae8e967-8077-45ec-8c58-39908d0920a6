// test-verification.js
const { PrismaClient } = require('@prisma/client');
const jwt = require('jsonwebtoken');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    // Check if test user already exists
    let testUser = await prisma.user.findUnique({
      where: { phone: '9999999999' },
    });

    if (!testUser) {
      // Create a new test user
      testUser = await prisma.user.create({
        data: {
          phone: '9999999999',
          email: '<EMAIL>',
          isVerified: false,
          profileStatus: 'INCOMPLETE',
          profile: {
            create: {
              fullName: 'Test User',
              gender: 'MALE',
              dateOfBirth: new Date('1990-01-01'),
              city: 'Mumbai'
            }
          }
        },
      });
      console.log('Test user created:', testUser.id);
    } else {
      console.log('Test user already exists:', testUser.id);
    }

    // Generate a JWT token for the test user
    const token = jwt.sign(
      { userId: testUser.id, phone: testUser.phone },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '1h' }
    );

    console.log('Test user ID:', testUser.id);
    console.log('JWT Token:', token);
    
    return { userId: testUser.id, token };
  } catch (error) {
    console.error('Error creating test user:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser().catch(console.error);
