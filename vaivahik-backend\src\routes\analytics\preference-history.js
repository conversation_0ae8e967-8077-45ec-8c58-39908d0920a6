const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { authenticateToken } = require('../../middleware/auth.middleware');
const authenticateAdmin = require('../../middleware/adminAuth.middleware');

/**
 * @route POST /api/analytics/preference-history
 * @desc Track changes to user preferences
 * @access Private (System)
 *
 * This endpoint is primarily for internal use by the preference update system
 *
 * Request body:
 * {
 *   userId: string,
 *   preferenceType: string,
 *   oldValue: any,
 *   newValue: any,
 *   changeReason?: string
 * }
 */
router.post('/', function(req, res, next) {
  authenticateToken(req, res, function() {
    authenticateAdmin(req, res, async function() {
  try {
    // Get request data
    const {
      userId,
      preferenceType,
      oldValue,
      newValue,
      changeReason
    } = req.body;

    // Validate required fields
    if (!userId || !preferenceType) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: userId and preferenceType are required'
      });
    }

    // Create the preference history record
    const preferenceHistory = await prisma.userPreferenceHistory.create({
      data: {
        userId,
        preferenceType,
        oldValue,
        newValue,
        changeReason: changeReason || null,
      },
    });

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Preference history tracked successfully',
      preferenceHistoryId: preferenceHistory.id
    });

  } catch (error) {
    console.error('Error tracking preference history:', error);
    return res.status(500).json({
      success: false,
      message: 'Error tracking preference history',
      error: error.message
    });
  }
    });
  });
});

/**
 * @route GET /api/analytics/preference-history/:userId
 * @desc Get preference history for a user
 * @access Private (Admin or Self)
 */
router.get('/:userId', function(req, res, next) {
  authenticateToken(req, res, async function() {
  try {
    const { userId } = req.params;

    // Check if user is admin or requesting their own data
    if (req.user.id !== userId && req.user.role !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Unauthorized to access this data'
      });
    }

    const { page = 1, limit = 10, preferenceType } = req.query;
    const skip = (page - 1) * limit;

    const where = { userId };
    if (preferenceType) {
      where.preferenceType = preferenceType;
    }

    // Get preference history
    const preferenceHistory = await prisma.userPreferenceHistory.findMany({
      where,
      orderBy: { timestamp: 'desc' },
      skip,
      take: parseInt(limit)
    });

    const total = await prisma.userPreferenceHistory.count({ where });

    // Return preference history
    return res.status(200).json({
      success: true,
      data: preferenceHistory,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error getting preference history:', error);
    return res.status(500).json({
      success: false,
      message: 'Error getting preference history',
      error: error.message
    });
  }
  });
});

module.exports = router;
