import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Card,
  CardContent,
  CardActions,
  CardMedia,
  Avatar,
  Typography,
  Button,
  IconButton,
  Chip,
  Box,
  Grid,
  Tooltip,
  Badge,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Snackbar,
  Alert,
  LinearProgress,
  Divider,
  Paper,
  Collapse,
  useTheme
} from '@mui/material';
import { styled, alpha } from '@mui/material/styles';
import {
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  ThumbDown as DislikeIcon,
  Star as SuperLikeIcon,
  Visibility as ViewIcon,
  Message as MessageIcon,
  Phone as PhoneIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  Person as PersonIcon,
  LocationOn as LocationIcon,
  Work as WorkIcon,
  School as SchoolIcon,
  Height as HeightIcon,
  Cake as AgeIcon,
  Verified as VerifiedIcon,
  Diamond as PremiumIcon,
  Psychology as AIIcon,
  TrendingUp as CompatibilityIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  AutoAwesome as MagicIcon,
  FiberManualRecord as OnlineIcon,
  Lock as LockIcon,
  Upgrade as UpgradeIcon
} from '@mui/icons-material';

// Styled Components for Premium Design
const ProfileCard = styled(Card)(({ theme, category }) => ({
  height: '100%',
  maxHeight: 600,
  minHeight: 480,
  width: '100%',
  maxWidth: 360,
  display: 'flex',
  flexDirection: 'column',
  position: 'relative',
  overflow: 'hidden',
  borderRadius: 16,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  border: `2px solid transparent`,
  background: theme.palette.background.paper,
  margin: '0 auto',
  [theme.breakpoints.down('md')]: {
    maxWidth: 320,
    minHeight: 440
  },
  [theme.breakpoints.down('sm')]: {
    maxWidth: 300,
    minHeight: 400
  },
  [theme.breakpoints.down('xs')]: {
    maxWidth: '100%',
    minHeight: 360
  },
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 20px 40px ${alpha(category?.color || theme.palette.primary.main, 0.2)}`,
    border: `2px solid ${alpha(category?.color || theme.palette.primary.main, 0.3)}`
  },
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
    background: `linear-gradient(90deg, ${category?.color || theme.palette.primary.main}, ${alpha(category?.color || theme.palette.primary.main, 0.7)})`,
    zIndex: 1
  }
}));

const ProfileImage = styled(CardMedia)(({ theme }) => ({
  height: 280,
  position: 'relative',
  objectFit: 'cover',
  [theme.breakpoints.down('sm')]: {
    height: 240
  },
  [theme.breakpoints.down('xs')]: {
    height: 200
  },
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '50%',
    background: 'linear-gradient(transparent, rgba(0,0,0,0.7))',
    pointerEvents: 'none'
  }
}));

const StatusBadge = styled(Box)(({ theme, status }) => {
  const colors = {
    online: '#4CAF50',
    recently: '#FF9800',
    offline: '#9E9E9E'
  };
  
  return {
    position: 'absolute',
    top: 12,
    right: 12,
    background: colors[status] || colors.offline,
    color: 'white',
    padding: '4px 8px',
    borderRadius: 12,
    fontSize: '0.7rem',
    fontWeight: 600,
    display: 'flex',
    alignItems: 'center',
    gap: 4,
    zIndex: 2
  };
});

const CompatibilityMeter = styled(Box)(({ theme, score }) => ({
  background: `linear-gradient(90deg, 
    ${score >= 90 ? '#4CAF50' : score >= 80 ? '#FF9800' : '#FF5722'} 0%, 
    ${alpha(score >= 90 ? '#4CAF50' : score >= 80 ? '#FF9800' : '#FF5722', 0.3)} 100%)`,
  borderRadius: 8,
  padding: '8px 12px',
  margin: '8px 0',
  position: 'relative',
  overflow: 'hidden'
}));

const ActionButton = styled(Button)(({ theme, variant: buttonVariant }) => {
  const variants = {
    primary: {
      background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
      color: theme.palette.primary.contrastText,
      '&:hover': {
        background: `linear-gradient(135deg, ${theme.palette.primary.dark}, ${alpha(theme.palette.primary.dark, 0.9)})`
      }
    },
    premium: {
      background: 'linear-gradient(135deg, #FFD700, #FFA000)',
      color: '#000',
      '&:hover': {
        background: 'linear-gradient(135deg, #FFA000, #FF8F00)'
      }
    },
    secondary: {
      background: 'transparent',
      border: `2px solid ${theme.palette.primary.main}`,
      color: theme.palette.primary.main,
      '&:hover': {
        background: alpha(theme.palette.primary.main, 0.1)
      }
    }
  };
  
  return {
    borderRadius: 8,
    fontWeight: 600,
    textTransform: 'none',
    minHeight: 40,
    ...variants[buttonVariant] || variants.primary
  };
});

export default function EnhancedProfileCard({ 
  profile, 
  category,
  showAdvancedInsights = false,
  onAction,
  userPremium = false
}) {
  const theme = useTheme();
  const router = useRouter();
  
  const [liked, setLiked] = useState(profile?.isLiked || false);
  const [shortlisted, setShortlisted] = useState(profile?.isShortlisted || false);
  const [showInsights, setShowInsights] = useState(false);
  const [upgradeDialog, setUpgradeDialog] = useState(false);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  // Calculate compatibility breakdown
  const compatibilityBreakdown = profile?.compatibility ? {
    personality: Math.min(100, profile.compatibility + Math.random() * 10 - 5),
    lifestyle: Math.min(100, profile.compatibility + Math.random() * 15 - 7),
    values: Math.min(100, profile.compatibility + Math.random() * 8 - 4),
    goals: Math.min(100, profile.compatibility + Math.random() * 12 - 6)
  } : null;

  const handleAction = async (action) => {
    if (!userPremium && ['message', 'phone', 'contact'].includes(action)) {
      setUpgradeDialog(true);
      return;
    }

    setLoading(true);
    try {
      if (onAction) {
        await onAction(action, profile.id);
      }
      
      if (action === 'like') {
        setLiked(!liked);
        setSnackbar({
          open: true,
          message: liked ? 'Like removed' : 'Profile liked!',
          severity: 'success'
        });
      } else if (action === 'shortlist') {
        setShortlisted(!shortlisted);
        setSnackbar({
          open: true,
          message: shortlisted ? 'Removed from shortlist' : 'Added to shortlist!',
          severity: 'success'
        });
      }
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Action failed. Please try again.',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const getOnlineStatus = () => {
    if (!profile?.lastActive) return 'offline';
    const now = new Date();
    const lastActive = new Date(profile.lastActive);
    const diffMinutes = (now - lastActive) / (1000 * 60);
    
    if (diffMinutes < 5) return 'online';
    if (diffMinutes < 60) return 'recently';
    return 'offline';
  };

  const onlineStatus = getOnlineStatus();
  const statusText = {
    online: 'Online now',
    recently: 'Active recently',
    offline: 'Offline'
  };

  return (
    <>
      <ProfileCard category={category} elevation={3}>
        {/* Profile Image with Overlays */}
        <Box sx={{ position: 'relative' }}>
          <ProfileImage
            component="img"
            image={profile?.profilePicture || '/default-avatar.png'}
            alt={profile?.firstName}
          />
          
          {/* Online Status */}
          <StatusBadge status={onlineStatus}>
            <OnlineIcon sx={{ fontSize: 8 }} />
            {statusText[onlineStatus]}
          </StatusBadge>
          
          {/* Verification & Premium Badges */}
          <Box sx={{ position: 'absolute', top: 12, left: 12, display: 'flex', gap: 1 }}>
            {profile?.isVerified && (
              <Tooltip title="Verified Profile">
                <VerifiedIcon sx={{ color: '#4CAF50', fontSize: 24 }} />
              </Tooltip>
            )}
            {profile?.isPremium && (
              <Tooltip title="Premium Member">
                <PremiumIcon sx={{ color: '#FFD700', fontSize: 24 }} />
              </Tooltip>
            )}
          </Box>

          {/* Quick Actions Overlay */}
          <Box sx={{ 
            position: 'absolute', 
            bottom: 12, 
            right: 12,
            display: 'flex',
            gap: 1
          }}>
            <Tooltip title={shortlisted ? "Remove from Shortlist" : "Add to Shortlist"}>
              <IconButton
                size="small"
                onClick={() => handleAction('shortlist')}
                sx={{
                  background: alpha('#fff', 0.9),
                  color: shortlisted ? theme.palette.primary.main : '#666',
                  '&:hover': { background: '#fff' }
                }}
              >
                {shortlisted ? <BookmarkIcon /> : <BookmarkBorderIcon />}
              </IconButton>
            </Tooltip>
            
            <Tooltip title={liked ? "Unlike" : "Like"}>
              <IconButton
                size="small"
                onClick={() => handleAction('like')}
                sx={{
                  background: alpha('#fff', 0.9),
                  color: liked ? '#FF1744' : '#666',
                  '&:hover': { background: '#fff' }
                }}
              >
                {liked ? <FavoriteIcon /> : <FavoriteBorderIcon />}
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <CardContent sx={{ flexGrow: 1, p: 3 }}>
          {/* Basic Info */}
          <Box sx={{ mb: 2 }}>
            <Typography variant="h6" fontWeight="600" gutterBottom>
              {profile?.firstName} {profile?.lastName}, {profile?.age}
            </Typography>
            
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <LocationIcon sx={{ fontSize: 16, color: '#666' }} />
                <Typography variant="body2" color="text.secondary">
                  {profile?.city}, {profile?.state}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <HeightIcon sx={{ fontSize: 16, color: '#666' }} />
                <Typography variant="body2" color="text.secondary">
                  {profile?.height}cm
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 1 }}>
              <WorkIcon sx={{ fontSize: 16, color: '#666' }} />
              <Typography variant="body2" color="text.secondary">
                {profile?.occupation}
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <SchoolIcon sx={{ fontSize: 16, color: '#666' }} />
              <Typography variant="body2" color="text.secondary">
                {profile?.education}
              </Typography>
            </Box>
          </Box>

          {/* Compatibility Score */}
          {profile?.compatibility && (
            <CompatibilityMeter score={profile.compatibility}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="body2" fontWeight="600" color="white">
                  {profile.compatibility}% Compatible
                </Typography>
                <CompatibilityIcon sx={{ color: 'white', fontSize: 20 }} />
              </Box>
            </CompatibilityMeter>
          )}

          {/* AI Insights */}
          {profile?.aiInsight && (
            <Paper sx={{ 
              p: 2, 
              mt: 2, 
              background: `linear-gradient(135deg, ${alpha(category?.color || theme.palette.primary.main, 0.1)}, ${alpha(category?.color || theme.palette.primary.main, 0.05)})`,
              border: `1px solid ${alpha(category?.color || theme.palette.primary.main, 0.2)}`
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <AIIcon sx={{ fontSize: 18, color: category?.color || theme.palette.primary.main }} />
                <Typography variant="caption" fontWeight="600" color="primary">
                  AI Insight
                </Typography>
              </Box>
              <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
                {profile.aiInsight}
              </Typography>
            </Paper>
          )}

          {/* Advanced Insights Toggle */}
          {showAdvancedInsights && compatibilityBreakdown && (
            <Box sx={{ mt: 2 }}>
              <Button
                size="small"
                onClick={() => setShowInsights(!showInsights)}
                endIcon={showInsights ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                sx={{ textTransform: 'none', p: 0, minHeight: 'auto' }}
              >
                <Typography variant="caption" color="primary">
                  {showInsights ? 'Hide' : 'Show'} Compatibility Details
                </Typography>
              </Button>
              
              <Collapse in={showInsights}>
                <Box sx={{ mt: 2 }}>
                  {Object.entries(compatibilityBreakdown).map(([key, value]) => (
                    <Box key={key} sx={{ mb: 1 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                        <Typography variant="caption" sx={{ textTransform: 'capitalize' }}>
                          {key}
                        </Typography>
                        <Typography variant="caption" fontWeight="600">
                          {Math.round(value)}%
                        </Typography>
                      </Box>
                      <LinearProgress 
                        variant="determinate" 
                        value={value} 
                        sx={{ 
                          height: 4, 
                          borderRadius: 2,
                          backgroundColor: alpha(theme.palette.primary.main, 0.1),
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: value >= 80 ? '#4CAF50' : value >= 60 ? '#FF9800' : '#FF5722'
                          }
                        }} 
                      />
                    </Box>
                  ))}
                </Box>
              </Collapse>
            </Box>
          )}
        </CardContent>

        {/* Action Buttons */}
        <CardActions sx={{ p: 3, pt: 0, gap: 1 }}>
          <ActionButton
            variant="secondary"
            size="small"
            startIcon={<ViewIcon />}
            onClick={() => router.push(`/profile/${profile?.id}`)}
            fullWidth
          >
            View Profile
          </ActionButton>
          
          {userPremium ? (
            <>
              <ActionButton
                variant="primary"
                size="small"
                startIcon={<MessageIcon />}
                onClick={() => handleAction('message')}
                fullWidth
              >
                Message
              </ActionButton>
              <ActionButton
                variant="premium"
                size="small"
                startIcon={<PhoneIcon />}
                onClick={() => handleAction('phone')}
                fullWidth
              >
                Call
              </ActionButton>
            </>
          ) : (
            <ActionButton
              variant="premium"
              size="small"
              startIcon={<UpgradeIcon />}
              onClick={() => setUpgradeDialog(true)}
              fullWidth
            >
              Upgrade to Contact
            </ActionButton>
          )}
        </CardActions>
      </ProfileCard>

      {/* Upgrade Dialog */}
      <Dialog open={upgradeDialog} onClose={() => setUpgradeDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ textAlign: 'center' }}>
          <PremiumIcon sx={{ fontSize: 40, color: '#FFD700', mb: 1 }} />
          <Typography variant="h5" fontWeight="600">
            Upgrade to Premium
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ textAlign: 'center' }}>
          <Typography variant="body1" gutterBottom>
            Unlock unlimited messaging, calling, and contact details with Premium membership.
          </Typography>
          <Box sx={{ mt: 3, p: 2, background: alpha('#FFD700', 0.1), borderRadius: 2 }}>
            <Typography variant="h6" color="primary" gutterBottom>
              Premium Benefits:
            </Typography>
            <Typography variant="body2" component="div">
              • Unlimited messaging & calling<br/>
              • View contact details<br/>
              • Priority in search results<br/>
              • Advanced compatibility insights<br/>
              • Ad-free experience
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 3, gap: 1 }}>
          <Button onClick={() => setUpgradeDialog(false)} variant="outlined" fullWidth>
            Maybe Later
          </Button>
          <ActionButton
            variant="premium"
            onClick={() => router.push('/premium')}
            fullWidth
          >
            Upgrade Now
          </ActionButton>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
}
