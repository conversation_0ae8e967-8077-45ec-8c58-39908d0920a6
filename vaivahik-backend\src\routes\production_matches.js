// src/routes/production_matches.js

const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth.middleware');
const productionMatchingService = require('../services/production_matching_service');
const rateLimit = require('express-rate-limit');

const prisma = new PrismaClient();

// Load settings
const fs = require('fs');
const path = require('path');
const settingsPath = path.join(__dirname, '../../config/production_settings.json');
const settings = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));

// Configure rate limiting
const apiSettings = settings.api || {};
const rateLimitSettings = apiSettings.rate_limit || {};

const limiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: rateLimitSettings.requests_per_minute || 60,
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    success: false,
    message: 'Too many requests, please try again later.'
  }
});

/**
 * @route GET /api/v2/matches
 * @desc Get matches for the authenticated user using the production service
 * @access Private
 */
router.get('/', authenticateToken, limiter, async (req, res) => {
  try {
    const userId = req.user.id;
    const { limit = 10, offset = 0, minScore, includeExplanation = false } = req.query;

    // Record user activity
    await prisma.user.update({
      where: { id: userId },
      data: { lastActive: new Date() }
    });

    // Get matches using production service
    const matches = await productionMatchingService.get_matches(
      userId,
      parseInt(limit),
      parseInt(offset),
      minScore ? parseInt(minScore) : null,
      includeExplanation === 'true'
    );

    res.json({
      success: true,
      matches,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: matches.length // Note: This is not the total count, just the returned count
      }
    });
  } catch (error) {
    console.error('Error getting matches:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting matches'
    });
  }
});

/**
 * @route GET /api/v2/matches/explanation
 * @desc Get explanation for a specific match using the production service
 * @access Private
 */
router.get('/explanation', authenticateToken, limiter, async (req, res) => {
  try {
    const userId = req.user.id;
    const { matchId } = req.query;

    if (!matchId) {
      return res.status(400).json({
        success: false,
        message: 'Match ID is required'
      });
    }

    // Get match explanation
    const explanation = await productionMatchingService.get_match_explanation(userId, matchId);

    res.json({
      success: true,
      explanation
    });
  } catch (error) {
    console.error('Error getting match explanation:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting match explanation'
    });
  }
});

/**
 * @route POST /api/v2/matches/interaction
 * @desc Record user interaction with a match
 * @access Private
 */
router.post('/interaction', authenticateToken, limiter, async (req, res) => {
  try {
    const userId = req.user.id;
    const { matchId, interactionType, value = 1 } = req.body;

    if (!matchId || !interactionType) {
      return res.status(400).json({
        success: false,
        message: 'Match ID and interaction type are required'
      });
    }

    // Valid interaction types
    const validInteractionTypes = ['view', 'like', 'message', 'connect', 'reject'];

    if (!validInteractionTypes.includes(interactionType)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid interaction type'
      });
    }

    // Record interaction in database
    await prisma.userInteraction.create({
      data: {
        userId,
        targetUserId: matchId,
        type: interactionType.toUpperCase(),
        value: parseFloat(value),
        createdAt: new Date()
      }
    });

    // Record event for A/B testing
    try {
      await productionMatchingService.ab_testing.record_event(
        userId,
        interactionType,
        parseFloat(value),
        'matching_model'
      );
    } catch (error) {
      console.error('Error recording A/B test event:', error);
      // Continue even if A/B test recording fails
    }

    // Send notification if applicable
    if (interactionType === 'view' || interactionType === 'like' || interactionType === 'connect') {
      try {
        const notificationSettings = settings.notifications || {};
        const notificationType = `${interactionType}_notification`;
        const notificationConfig = notificationSettings[notificationType] || { enabled: false };

        if (notificationConfig.enabled) {
          // Import notification service
          const enhancedNotificationService = require('../../services/notification/enhanced-notification-service');

          // Get user name
          const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { name: true }
          });

          // Create notification content
          let title, body;

          switch (interactionType) {
            case 'view':
              title = 'Profile Viewed';
              body = `${user.name} viewed your profile`;
              break;
            case 'like':
              title = 'Interest Received';
              body = `${user.name} has shown interest in your profile`;
              break;
            case 'connect':
              title = 'Connection Request';
              body = `${user.name} wants to connect with you`;
              break;
          }

          // Send notification
          await enhancedNotificationService.sendToUser(matchId, {
            title,
            body,
            data: {
              type: interactionType,
              userId
            }
          });
        }
      } catch (error) {
        console.error('Error sending notification:', error);
        // Continue even if notification fails
      }
    }

    res.json({
      success: true,
      message: 'Interaction recorded successfully'
    });
  } catch (error) {
    console.error('Error recording interaction:', error);
    res.status(500).json({
      success: false,
      message: 'Error recording interaction'
    });
  }
});

/**
 * @route POST /api/v2/matches/satisfaction
 * @desc Record user satisfaction with matches
 * @access Private
 */
router.post('/satisfaction', authenticateToken, limiter, async (req, res) => {
  try {
    const userId = req.user.id;
    const { rating, feedback } = req.body;

    if (rating === undefined) {
      return res.status(400).json({
        success: false,
        message: 'Rating is required'
      });
    }

    // Rating should be between 1 and 5
    const parsedRating = parseInt(rating);
    if (parsedRating < 1 || parsedRating > 5) {
      return res.status(400).json({
        success: false,
        message: 'Rating should be between 1 and 5'
      });
    }

    // Record satisfaction in database
    await prisma.userFeedback.create({
      data: {
        userId,
        type: 'MATCH_SATISFACTION',
        rating: parsedRating,
        feedback: feedback || '',
        createdAt: new Date()
      }
    });

    // Record event for A/B testing
    try {
      await productionMatchingService.ab_testing.record_event(
        userId,
        'satisfaction',
        parsedRating,
        'matching_model'
      );
    } catch (error) {
      console.error('Error recording A/B test event:', error);
      // Continue even if A/B test recording fails
    }

    res.json({
      success: true,
      message: 'Satisfaction recorded successfully'
    });
  } catch (error) {
    console.error('Error recording satisfaction:', error);
    res.status(500).json({
      success: false,
      message: 'Error recording satisfaction'
    });
  }
});

/**
 * @route GET /api/v2/matches/feedback
 * @desc Get feedback for matches
 * @access Private (Admin only)
 */
router.get('/feedback', authenticateToken, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Unauthorized'
      });
    }

    const { experimentId = 'matching_model' } = req.query;

    // Get experiment results
    const results = await productionMatchingService.ab_testing.get_experiment_results(experimentId);

    res.json({
      success: true,
      results
    });
  } catch (error) {
    console.error('Error getting feedback:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting feedback'
    });
  }
});

module.exports = router;
