// src/controllers/admin/spotlight.controller.js

const logger = require('../../utils/logger');

// Mock data for spotlight features
const mockSpotlightFeatures = [
    {
        id: 1,
        name: "Premium Spotlight",
        description: "Get your profile featured at the top of search results for 24 hours",
        price: 499,
        discountPercent: 10,
        discountedPrice: 449.1,
        durationHours: 24,
        defaultCount: 1,
        isActive: true,
        purchaseCount: 150,
        activeCount: 12,
        revenue: 67365,
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        updatedAt: new Date()
    },
    {
        id: 2,
        name: "Weekend Spotlight",
        description: "48-hour spotlight feature perfect for weekend visibility",
        price: 799,
        discountPercent: 15,
        discountedPrice: 679.15,
        durationHours: 48,
        defaultCount: 1,
        isActive: true,
        purchaseCount: 89,
        activeCount: 8,
        revenue: 60424,
        createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000),
        updatedAt: new Date()
    },
    {
        id: 3,
        name: "Super Spotlight",
        description: "72-hour premium spotlight with enhanced visibility",
        price: 1199,
        discountPercent: 20,
        discountedPrice: 959.2,
        durationHours: 72,
        defaultCount: 1,
        isActive: true,
        purchaseCount: 45,
        activeCount: 5,
        revenue: 43164,
        createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
        updatedAt: new Date()
    }
];

// Check if mock data should be used
const shouldUseMockData = () => {
    return process.env.NODE_ENV === 'development' || process.env.USE_MOCK_DATA === 'true';
};

/**
 * @description Get all spotlight features
 * @route GET /api/admin/spotlight/features
 */
exports.getSpotlightFeatures = async (req, res, next) => {
    try {
        const {
            page = 1,
            limit = 10,
            search = '',
            sortBy = 'createdAt',
            order = 'desc',
            isActive = ''
        } = req.query;

        const pageNum = parseInt(page, 10);
        const limitNum = parseInt(limit, 10);

        if (isNaN(pageNum) || pageNum < 1 || isNaN(limitNum) || limitNum < 1) {
            const error = new Error("Invalid page or limit parameter.");
            error.status = 400;
            return next(error);
        }

        if (shouldUseMockData()) {
            // Return mock data
            let filteredFeatures = [...mockSpotlightFeatures];

            // Apply search filter
            if (search) {
                filteredFeatures = filteredFeatures.filter(feature =>
                    feature.name.toLowerCase().includes(search.toLowerCase()) ||
                    feature.description.toLowerCase().includes(search.toLowerCase())
                );
            }

            // Apply isActive filter
            if (isActive !== '') {
                filteredFeatures = filteredFeatures.filter(feature =>
                    feature.isActive === (isActive === 'true')
                );
            }

            // Apply sorting
            const sortOrder = order.toLowerCase() === 'asc' ? 1 : -1;
            filteredFeatures.sort((a, b) => {
                const aValue = a[sortBy];
                const bValue = b[sortBy];
                if (aValue < bValue) return -1 * sortOrder;
                if (aValue > bValue) return 1 * sortOrder;
                return 0;
            });

            // Apply pagination
            const skip = (pageNum - 1) * limitNum;
            const paginatedFeatures = filteredFeatures.slice(skip, skip + limitNum);

            return res.status(200).json({
                success: true,
                message: "Spotlight features fetched successfully (Mock Data)",
                features: paginatedFeatures,
                pagination: {
                    currentPage: pageNum,
                    limit: limitNum,
                    totalPages: Math.ceil(filteredFeatures.length / limitNum),
                    totalFeatures: filteredFeatures.length
                },
                useMockData: true
            });
        }

        // Real database implementation
        const prisma = req.prisma;
        const skip = (pageNum - 1) * limitNum;
        const take = limitNum;
        const sortOrder = order.toLowerCase() === 'asc' ? 'asc' : 'desc';

        // Check if SpotlightFeature model exists
        let featureModelExists = true;
        try {
            await prisma.spotlightFeature.findFirst();
        } catch (e) {
            if (e.message.includes('does not exist in the current database')) {
                featureModelExists = false;
            } else {
                throw e;
            }
        }

        if (!featureModelExists) {
            return res.status(200).json({
                success: true,
                message: "Spotlight feature data not yet available. Please run database migrations first.",
                features: [],
                pagination: { currentPage: 1, limit: take, totalPages: 0, totalFeatures: 0 },
                useMockData: false
            });
        }

        // Build where clause based on filters
        let whereClause = {};
        
        if (isActive !== '') {
            whereClause.isActive = isActive === 'true';
        }
        
        if (search) {
            whereClause.OR = [
                {
                    name: {
                        contains: search,
                        mode: 'insensitive'
                    }
                },
                {
                    description: {
                        contains: search,
                        mode: 'insensitive'
                    }
                }
            ];
        }

        // Get total count for pagination
        const totalFeatures = await prisma.spotlightFeature.count({
            where: whereClause
        });

        // Get features with pagination, sorting, and filtering
        const features = await prisma.spotlightFeature.findMany({
            where: whereClause,
            orderBy: {
                [sortBy]: sortOrder
            },
            skip,
            take
        });

        // Get usage statistics for each feature
        const enhancedFeatures = await Promise.all(features.map(async (feature) => {
            // Get purchase count
            const purchaseCount = await prisma.userSpotlight.count({
                where: {
                    spotlightId: feature.id
                }
            });

            // Get active count
            const now = new Date();
            const activeCount = await prisma.userSpotlight.count({
                where: {
                    spotlightId: feature.id,
                    isActive: true,
                    endTime: {
                        gt: now
                    }
                }
            });

            // Get revenue
            const revenueStats = await prisma.userSpotlight.aggregate({
                where: {
                    spotlightId: feature.id
                },
                _sum: {
                    pricePaid: true
                }
            });

            return {
                ...feature,
                purchaseCount: purchaseCount || 0,
                activeCount: activeCount || 0,
                revenue: revenueStats._sum?.pricePaid || 0
            };
        }));

        const totalPages = Math.ceil(totalFeatures / take);

        res.status(200).json({
            message: "Spotlight features fetched successfully.",
            features: enhancedFeatures,
            pagination: {
                currentPage: pageNum,
                limit: take,
                totalPages,
                totalFeatures
            }
        });
    } catch (error) {
        console.error("Error fetching spotlight features:", error);
        next(error);
    }
};

/**
 * @description Get spotlight feature by ID
 * @route GET /api/admin/spotlight/features/:id
 */
exports.getSpotlightFeatureById = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;

    try {
        const feature = await prisma.spotlightFeature.findUnique({
            where: { id }
        });

        if (!feature) {
            return res.status(404).json({
                message: "Spotlight feature not found."
            });
        }

        // Get usage statistics
        const purchaseCount = await prisma.userSpotlight.count({
            where: {
                spotlightId: feature.id
            }
        });

        const now = new Date();
        const activeCount = await prisma.userSpotlight.count({
            where: {
                spotlightId: feature.id,
                isActive: true,
                endTime: {
                    gt: now
                }
            }
        });

        const revenueStats = await prisma.userSpotlight.aggregate({
            where: {
                spotlightId: feature.id
            },
            _sum: {
                pricePaid: true
            }
        });

        const enhancedFeature = {
            ...feature,
            purchaseCount: purchaseCount || 0,
            activeCount: activeCount || 0,
            revenue: revenueStats._sum?.pricePaid || 0
        };

        res.status(200).json({
            message: "Spotlight feature fetched successfully.",
            feature: enhancedFeature
        });
    } catch (error) {
        console.error("Error fetching spotlight feature:", error);
        next(error);
    }
};

/**
 * @description Create a new spotlight feature
 * @route POST /api/admin/spotlight/features
 */
exports.createSpotlightFeature = async (req, res, next) => {
    const prisma = req.prisma;
    const { 
        name, 
        description, 
        price, 
        discountPercent, 
        durationHours = 24,
        defaultCount = 1,
        isActive 
    } = req.body;

    try {
        // Validate required fields
        if (!name || !description || !price) {
            return res.status(400).json({
                message: "Name, description, and price are required."
            });
        }

        // Parse numeric values
        const parsedPrice = parseFloat(price);
        const parsedDiscountPercent = discountPercent ? parseInt(discountPercent) : null;
        const parsedDurationHours = parseInt(durationHours);
        const parsedDefaultCount = parseInt(defaultCount);

        if (isNaN(parsedPrice) || parsedPrice < 0) {
            return res.status(400).json({
                message: "Price must be a valid number greater than or equal to 0."
            });
        }

        if (parsedDiscountPercent !== null && (isNaN(parsedDiscountPercent) || parsedDiscountPercent < 0 || parsedDiscountPercent > 100)) {
            return res.status(400).json({
                message: "Discount percent must be a valid number between 0 and 100."
            });
        }

        if (isNaN(parsedDurationHours) || parsedDurationHours <= 0) {
            return res.status(400).json({
                message: "Duration hours must be a valid number greater than 0."
            });
        }

        if (isNaN(parsedDefaultCount) || parsedDefaultCount <= 0) {
            return res.status(400).json({
                message: "Default count must be a valid number greater than 0."
            });
        }

        // Calculate discounted price if discount percent is provided
        const discountedPrice = parsedDiscountPercent 
            ? parsedPrice * (1 - parsedDiscountPercent / 100) 
            : null;

        // Create the feature
        const feature = await prisma.spotlightFeature.create({
            data: {
                name,
                description,
                price: parsedPrice,
                discountPercent: parsedDiscountPercent,
                discountedPrice,
                durationHours: parsedDurationHours,
                defaultCount: parsedDefaultCount,
                isActive: isActive === true || isActive === 'true'
            }
        });

        res.status(201).json({
            message: "Spotlight feature created successfully.",
            feature
        });
    } catch (error) {
        console.error("Error creating spotlight feature:", error);
        next(error);
    }
};

/**
 * @description Update a spotlight feature
 * @route PUT /api/admin/spotlight/features/:id
 */
exports.updateSpotlightFeature = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;
    const { 
        name, 
        description, 
        price, 
        discountPercent, 
        durationHours,
        defaultCount,
        isActive 
    } = req.body;

    try {
        // Check if feature exists
        const existingFeature = await prisma.spotlightFeature.findUnique({
            where: { id }
        });

        if (!existingFeature) {
            return res.status(404).json({
                message: "Spotlight feature not found."
            });
        }

        // Prepare update data
        const updateData = {};

        if (name !== undefined) updateData.name = name;
        if (description !== undefined) updateData.description = description;
        if (isActive !== undefined) updateData.isActive = isActive === true || isActive === 'true';

        // Handle numeric fields
        if (price !== undefined) {
            const parsedPrice = parseFloat(price);
            if (isNaN(parsedPrice) || parsedPrice < 0) {
                return res.status(400).json({
                    message: "Price must be a valid number greater than or equal to 0."
                });
            }
            updateData.price = parsedPrice;
        }

        if (discountPercent !== undefined) {
            const parsedDiscountPercent = discountPercent ? parseInt(discountPercent) : null;
            if (parsedDiscountPercent !== null && (isNaN(parsedDiscountPercent) || parsedDiscountPercent < 0 || parsedDiscountPercent > 100)) {
                return res.status(400).json({
                    message: "Discount percent must be a valid number between 0 and 100."
                });
            }
            updateData.discountPercent = parsedDiscountPercent;

            // Recalculate discounted price
            const priceToUse = updateData.price !== undefined ? updateData.price : existingFeature.price;
            updateData.discountedPrice = parsedDiscountPercent 
                ? priceToUse * (1 - parsedDiscountPercent / 100) 
                : null;
        } else if (updateData.price !== undefined && existingFeature.discountPercent) {
            // Recalculate discounted price if price changed but discount percent didn't
            updateData.discountedPrice = updateData.price * (1 - existingFeature.discountPercent / 100);
        }

        if (durationHours !== undefined) {
            const parsedDurationHours = parseInt(durationHours);
            if (isNaN(parsedDurationHours) || parsedDurationHours <= 0) {
                return res.status(400).json({
                    message: "Duration hours must be a valid number greater than 0."
                });
            }
            updateData.durationHours = parsedDurationHours;
        }

        if (defaultCount !== undefined) {
            const parsedDefaultCount = parseInt(defaultCount);
            if (isNaN(parsedDefaultCount) || parsedDefaultCount <= 0) {
                return res.status(400).json({
                    message: "Default count must be a valid number greater than 0."
                });
            }
            updateData.defaultCount = parsedDefaultCount;
        }

        // Update the feature
        const updatedFeature = await prisma.spotlightFeature.update({
            where: { id },
            data: updateData
        });

        res.status(200).json({
            message: "Spotlight feature updated successfully.",
            feature: updatedFeature
        });
    } catch (error) {
        console.error("Error updating spotlight feature:", error);
        next(error);
    }
};

/**
 * @description Delete a spotlight feature
 * @route DELETE /api/admin/spotlight/features/:id
 */
exports.deleteSpotlightFeature = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;

    try {
        // Check if feature exists
        const existingFeature = await prisma.spotlightFeature.findUnique({
            where: { id }
        });

        if (!existingFeature) {
            return res.status(404).json({
                message: "Spotlight feature not found."
            });
        }

        // Check if feature is in use
        const usageCount = await prisma.userSpotlight.count({
            where: {
                spotlightId: id
            }
        });

        if (usageCount > 0) {
            return res.status(400).json({
                message: "Cannot delete feature as it is in use by users.",
                usageCount
            });
        }

        // Delete the feature
        await prisma.spotlightFeature.delete({
            where: { id }
        });

        res.status(200).json({
            message: "Spotlight feature deleted successfully."
        });
    } catch (error) {
        console.error("Error deleting spotlight feature:", error);
        next(error);
    }
};

/**
 * @description Get active spotlight users
 * @route GET /api/admin/spotlight/active-users
 */
exports.getActiveSpotlightUsers = async (req, res, next) => {
    const prisma = req.prisma;
    const { 
        page = 1, 
        limit = 10
    } = req.query;
    
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    if (isNaN(pageNum) || pageNum < 1 || isNaN(limitNum) || limitNum < 1) {
        const error = new Error("Invalid page or limit parameter.");
        error.status = 400;
        return next(error);
    }

    const skip = (pageNum - 1) * limitNum;
    const take = limitNum;

    try {
        // Get current time
        const now = new Date();

        // Get active spotlight users
        const activeSpotlights = await prisma.userSpotlight.findMany({
            where: {
                isActive: true,
                endTime: {
                    gt: now
                }
            },
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        phone: true,
                        profile: {
                            select: {
                                gender: true,
                                dateOfBirth: true,
                                profilePic: true
                            }
                        }
                    }
                },
                spotlight: true
            },
            orderBy: {
                endTime: 'asc'
            },
            skip,
            take
        });

        // Get total count for pagination
        const totalActiveSpotlights = await prisma.userSpotlight.count({
            where: {
                isActive: true,
                endTime: {
                    gt: now
                }
            }
        });

        // Format the response
        const formattedSpotlights = activeSpotlights.map(spotlight => {
            const user = spotlight.user;
            const profile = user.profile;
            
            // Calculate age if date of birth is available
            let age = null;
            if (profile?.dateOfBirth) {
                const birthDate = new Date(profile.dateOfBirth);
                const ageDifMs = Date.now() - birthDate.getTime();
                const ageDate = new Date(ageDifMs);
                age = Math.abs(ageDate.getUTCFullYear() - 1970);
            }
            
            // Calculate remaining time
            const remainingMs = spotlight.endTime.getTime() - now.getTime();
            const remainingHours = Math.ceil(remainingMs / (1000 * 60 * 60));
            
            return {
                spotlightId: spotlight.id,
                userId: user.id,
                name: user.name,
                email: user.email,
                phone: user.phone,
                gender: profile?.gender || null,
                age,
                profilePic: profile?.profilePic || null,
                featureName: spotlight.spotlight.name,
                startTime: spotlight.startTime,
                endTime: spotlight.endTime,
                timeRemaining: `${remainingHours} hours`
            };
        });

        const totalPages = Math.ceil(totalActiveSpotlights / take);

        res.status(200).json({
            message: "Active spotlight users fetched successfully.",
            activeUsers: formattedSpotlights,
            pagination: {
                currentPage: pageNum,
                limit: take,
                totalPages,
                totalActiveUsers: totalActiveSpotlights
            }
        });
    } catch (error) {
        console.error("Error fetching active spotlight users:", error);
        next(error);
    }
};
