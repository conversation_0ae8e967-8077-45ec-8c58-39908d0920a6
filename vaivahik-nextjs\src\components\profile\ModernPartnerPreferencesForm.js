/**
 * Modern Partner Preferences Form
 *
 * A modern UI form for collecting partner preferences as part of the profile completion process.
 * Uses the shared styled components for consistent UI across the application.
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  MenuItem,
  FormHelperText,
  CircularProgress,
  Alert,
  InputAdornment,
  Typography,
  Slider,
  Chip,
  Autocomplete
} from '@mui/material';
import {
  Favorite as HeartIcon,
  School as EducationIcon,
  Work as OccupationIcon,
  Height as HeightIcon,
  LocationOn as LocationIcon,
  Cake as AgeIcon,
  AttachMoney as IncomeIcon
} from '@mui/icons-material';
import {
  StyledPaper,
  StyledTextField,
  StyledSelect,
  StyledButton,
  StyledFormLabel,
  FloatingElement,
  FormSection,
  FormRow,
  StyledSectionTitle
} from '@/components/ui/ModernFormComponents';
import { validateField, VALIDATION_RULES } from '@/utils/validationUtils';
import { formatError, getUserFriendlyMessage, isValidationError } from '@/utils/errorHandling';

// Constants for form options
const EDUCATION_LEVELS = [
  'High School',
  'Diploma',
  'Bachelor\'s',
  'Master\'s',
  'Doctorate',
  'Professional Degree'
];

const OCCUPATION_TYPES = [
  'IT Professional',
  'Engineer',
  'Doctor',
  'Teacher',
  'Business Owner',
  'Government Employee',
  'Private Sector',
  'Self Employed',
  'Not Working'
];

const MARITAL_STATUS_OPTIONS = [
  'Never Married',
  'Divorced',
  'Widowed',
  'Separated'
];

const INCOME_RANGES = [
  'No Income',
  'Up to 3 LPA',
  '3-5 LPA',
  '5-7 LPA',
  '7-10 LPA',
  '10-15 LPA',
  '15-20 LPA',
  '20-30 LPA',
  '30-50 LPA',
  '50+ LPA'
];

const DIET_PREFERENCES = [
  'Vegetarian',
  'Non-Vegetarian',
  'Eggetarian',
  'Vegan',
  'Jain',
  'No Preference'
];

const SUBCASTES = [
  'Kunbi',
  '96 Kuli',
  'Deshmukh',
  'Kshatriya',
  'Gomantak',
  'Karade',
  'Koli',
  'Agri',
  'Maratha-Deshmukh',
  'Other'
];

const ModernPartnerPreferencesForm = ({ userData, onSave, isLoading = false }) => {
  const [formData, setFormData] = useState({
    ageRange: [24, 32],
    heightRange: [4.5, 6.0],
    maritalStatus: [],
    education: [],
    occupation: [],
    incomeRange: [],
    location: [],
    diet: '',
    subCaste: [],
    manglik: 'No Preference',
    aboutPartner: ''
  });

  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  // Initialize form with user data if available
  useEffect(() => {
    if (userData?.partnerPreferences) {
      setFormData({
        ...formData,
        ...userData.partnerPreferences
      });
    }
  }, [userData]);

  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    const newFormData = {
      ...formData,
      [name]: value
    };

    setFormData(newFormData);

    // Mark field as touched
    setTouched({
      ...touched,
      [name]: true
    });

    // Validate field on change if it's been touched
    if (touched[name]) {
      const fieldError = validateSingleField(name, value);
      setErrors(prevErrors => {
        if (fieldError) {
          return { ...prevErrors, [name]: fieldError };
        } else {
          // Remove error if it exists
          const { [name]: _, ...restErrors } = prevErrors;
          return restErrors;
        }
      });
    }
  };

  // Handle slider change
  const handleSliderChange = (name, value) => {
    const newFormData = {
      ...formData,
      [name]: value
    };

    setFormData(newFormData);

    // Mark field as touched
    setTouched({
      ...touched,
      [name]: true
    });

    // Validate field on change if it's been touched
    if (touched[name]) {
      const fieldError = validateSingleField(name, value);
      setErrors(prevErrors => {
        if (fieldError) {
          return { ...prevErrors, [name]: fieldError };
        } else {
          // Remove error if it exists
          const { [name]: _, ...restErrors } = prevErrors;
          return restErrors;
        }
      });
    }
  };

  // Handle autocomplete change
  const handleAutocompleteChange = (name, value) => {
    const newFormData = {
      ...formData,
      [name]: value
    };

    setFormData(newFormData);

    // Mark field as touched
    setTouched({
      ...touched,
      [name]: true
    });

    // Validate field on change if it's been touched
    if (touched[name]) {
      const fieldError = validateSingleField(name, value);
      setErrors(prevErrors => {
        if (fieldError) {
          return { ...prevErrors, [name]: fieldError };
        } else {
          // Remove error if it exists
          const { [name]: _, ...restErrors } = prevErrors;
          return restErrors;
        }
      });
    }
  };

  // Validate a single field
  const validateSingleField = (name, value) => {
    let rule;

    switch (name) {
      case 'ageRange':
        rule = VALIDATION_RULES.AGE_RANGE;
        // Custom validation for age range
        if (value && value.length === 2) {
          if (value[0] < 18) {
            return 'Minimum age must be at least 18 years';
          }
          if (value[1] > 60) {
            return 'Maximum age cannot exceed 60 years';
          }
          if (value[0] >= value[1]) {
            return 'Minimum age must be less than maximum age';
          }
        }
        break;

      case 'heightRange':
        rule = VALIDATION_RULES.HEIGHT_RANGE;
        // Custom validation for height range
        if (value && value.length === 2) {
          if (value[0] < 4.0) {
            return 'Minimum height must be at least 4.0 feet';
          }
          if (value[1] > 7.0) {
            return 'Maximum height cannot exceed 7.0 feet';
          }
          if (value[0] >= value[1]) {
            return 'Minimum height must be less than maximum height';
          }
        }
        break;

      case 'diet':
        rule = VALIDATION_RULES.DIET;
        break;

      case 'aboutPartner':
        rule = VALIDATION_RULES.ABOUT_PARTNER;
        break;

      default:
        return null;
    }

    return validateField(name, value, rule, formData);
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    // Validate individual fields
    Object.keys(formData).forEach(fieldName => {
      const error = validateSingleField(fieldName, formData[fieldName]);
      if (error) {
        newErrors[fieldName] = error;
      }
    });

    // At least one preference should be specified
    if (
      formData.education.length === 0 &&
      formData.occupation.length === 0 &&
      formData.maritalStatus.length === 0 &&
      formData.location.length === 0
    ) {
      newErrors.general = 'Please specify at least one partner preference';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateForm()) {
      onSave(formData);
    }
  };

  return (
    <StyledPaper>
      <FloatingElement position="top-right" />
      <FloatingElement position="bottom-left" />

      <Box position="relative" zIndex={1}>
        <Typography
          variant="h4"
          align="center"
          gutterBottom
          sx={{
            fontFamily: 'var(--font-secondary)',
            background: 'var(--primary-gradient)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            fontWeight: 700,
            mb: 1
          }}
        >
          Partner Preferences
        </Typography>

        <Typography variant="body1" align="center" paragraph sx={{ mb: 4, color: 'var(--text-color-medium)' }}>
          Tell us what you're looking for in a life partner
        </Typography>

        {errors.general && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {errors.general}
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <FormSection title="Basic Preferences">
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <AgeIcon fontSize="small" sx={{ mr: 1 }} />
                    Age Range (years)
                  </Box>
                </StyledFormLabel>
                <Box sx={{ px: 2, pt: 1 }}>
                  <Slider
                    value={formData.ageRange}
                    onChange={(e, newValue) => handleSliderChange('ageRange', newValue)}
                    valueLabelDisplay="on"
                    min={18}
                    max={60}
                    sx={{
                      color: 'var(--primary-color)',
                      '& .MuiSlider-thumb': {
                        height: 24,
                        width: 24,
                        backgroundColor: '#fff',
                        border: '2px solid var(--primary-color)',
                        '&:focus, &:hover, &.Mui-active, &.Mui-focusVisible': {
                          boxShadow: '0 0 0 8px rgba(255, 95, 109, 0.16)',
                        },
                      },
                    }}
                  />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      {formData.ageRange[0]} years
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {formData.ageRange[1]} years
                    </Typography>
                  </Box>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <HeightIcon fontSize="small" sx={{ mr: 1 }} />
                    Height Range (feet)
                  </Box>
                </StyledFormLabel>
                <Box sx={{ px: 2, pt: 1 }}>
                  <Slider
                    value={formData.heightRange}
                    onChange={(e, newValue) => handleSliderChange('heightRange', newValue)}
                    valueLabelDisplay="on"
                    min={4.0}
                    max={7.0}
                    step={0.1}
                    sx={{
                      color: 'var(--primary-color)',
                      '& .MuiSlider-thumb': {
                        height: 24,
                        width: 24,
                        backgroundColor: '#fff',
                        border: '2px solid var(--primary-color)',
                        '&:focus, &:hover, &.Mui-active, &.Mui-focusVisible': {
                          boxShadow: '0 0 0 8px rgba(255, 95, 109, 0.16)',
                        },
                      },
                    }}
                  />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      {formData.heightRange[0]} ft
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {formData.heightRange[1]} ft
                    </Typography>
                  </Box>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>Marital Status</StyledFormLabel>
                <Autocomplete
                  multiple
                  options={MARITAL_STATUS_OPTIONS}
                  value={formData.maritalStatus}
                  onChange={(e, newValue) => handleAutocompleteChange('maritalStatus', newValue)}
                  renderInput={(params) => (
                    <StyledTextField
                      {...params}
                      placeholder="Select marital status"
                    />
                  )}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip
                        label={option}
                        {...getTagProps({ index })}
                        sx={{
                          background: 'var(--primary-gradient)',
                          color: 'white',
                        }}
                      />
                    ))
                  }
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>Diet Preference</StyledFormLabel>
                <StyledSelect
                  name="diet"
                  value={formData.diet}
                  onChange={handleChange}
                  fullWidth
                  displayEmpty
                >
                  <MenuItem value="">No Preference</MenuItem>
                  {DIET_PREFERENCES.map(option => (
                    <MenuItem key={option} value={option}>{option}</MenuItem>
                  ))}
                </StyledSelect>
              </Grid>
            </Grid>
          </FormSection>

          <FormSection title="Education & Career">
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <EducationIcon fontSize="small" sx={{ mr: 1 }} />
                    Education
                  </Box>
                </StyledFormLabel>
                <Autocomplete
                  multiple
                  options={EDUCATION_LEVELS}
                  value={formData.education}
                  onChange={(e, newValue) => handleAutocompleteChange('education', newValue)}
                  renderInput={(params) => (
                    <StyledTextField
                      {...params}
                      placeholder="Select education levels"
                    />
                  )}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip
                        label={option}
                        {...getTagProps({ index })}
                        sx={{
                          background: 'var(--primary-gradient)',
                          color: 'white',
                        }}
                      />
                    ))
                  }
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <OccupationIcon fontSize="small" sx={{ mr: 1 }} />
                    Occupation
                  </Box>
                </StyledFormLabel>
                <Autocomplete
                  multiple
                  options={OCCUPATION_TYPES}
                  value={formData.occupation}
                  onChange={(e, newValue) => handleAutocompleteChange('occupation', newValue)}
                  renderInput={(params) => (
                    <StyledTextField
                      {...params}
                      placeholder="Select occupation types"
                    />
                  )}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip
                        label={option}
                        {...getTagProps({ index })}
                        sx={{
                          background: 'var(--primary-gradient)',
                          color: 'white',
                        }}
                      />
                    ))
                  }
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <IncomeIcon fontSize="small" sx={{ mr: 1 }} />
                    Annual Income
                  </Box>
                </StyledFormLabel>
                <Autocomplete
                  multiple
                  options={INCOME_RANGES}
                  value={formData.incomeRange}
                  onChange={(e, newValue) => handleAutocompleteChange('incomeRange', newValue)}
                  renderInput={(params) => (
                    <StyledTextField
                      {...params}
                      placeholder="Select income ranges"
                    />
                  )}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip
                        label={option}
                        {...getTagProps({ index })}
                        sx={{
                          background: 'var(--primary-gradient)',
                          color: 'white',
                        }}
                      />
                    ))
                  }
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <LocationIcon fontSize="small" sx={{ mr: 1 }} />
                    Preferred Locations
                  </Box>
                </StyledFormLabel>
                <StyledTextField
                  name="location"
                  value={formData.location.join(', ')}
                  onChange={(e) => handleAutocompleteChange('location', e.target.value.split(',').map(item => item.trim()).filter(Boolean))}
                  fullWidth
                  placeholder="Enter cities/states separated by commas"
                />
              </Grid>
            </Grid>
          </FormSection>

          <FormSection title="Community Preferences">
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <StyledFormLabel>Sub-caste Preference</StyledFormLabel>
                <Autocomplete
                  multiple
                  options={SUBCASTES}
                  value={formData.subCaste}
                  onChange={(e, newValue) => handleAutocompleteChange('subCaste', newValue)}
                  renderInput={(params) => (
                    <StyledTextField
                      {...params}
                      placeholder="Select sub-castes"
                    />
                  )}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip
                        label={option}
                        {...getTagProps({ index })}
                        sx={{
                          background: 'var(--primary-gradient)',
                          color: 'white',
                        }}
                      />
                    ))
                  }
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>Manglik</StyledFormLabel>
                <StyledSelect
                  name="manglik"
                  value={formData.manglik}
                  onChange={handleChange}
                  fullWidth
                >
                  <MenuItem value="No Preference">No Preference</MenuItem>
                  <MenuItem value="Yes">Yes</MenuItem>
                  <MenuItem value="No">No</MenuItem>
                </StyledSelect>
              </Grid>
            </Grid>
          </FormSection>

          <FormSection title="Additional Preferences">
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <StyledFormLabel>About Your Ideal Partner</StyledFormLabel>
                <StyledTextField
                  name="aboutPartner"
                  value={formData.aboutPartner}
                  onChange={handleChange}
                  fullWidth
                  multiline
                  rows={4}
                  placeholder="Describe qualities, values, and traits you're looking for in your life partner..."
                />
              </Grid>
            </Grid>
          </FormSection>

          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
            <StyledButton
              type="submit"
              variant="contained"
              disabled={isLoading}
              startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : null}
            >
              {isLoading ? 'Saving...' : 'Save Preferences'}
            </StyledButton>
          </Box>
        </form>
      </Box>
    </StyledPaper>
  );
};

export default ModernPartnerPreferencesForm;
