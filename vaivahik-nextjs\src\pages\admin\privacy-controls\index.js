import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  Alert,
  CircularProgress,
  Switch,
  FormControlLabel,
  Tooltip,
  Avatar,
  Stack,
  Divider,
  Paper,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Badge,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Security as SecurityIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Shield as ShieldIcon,
  Lock as LockIcon,
  Public as PublicIcon,
  Group as GroupIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Photo as PhotoIcon,
  Search as SearchIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  FilterList as FilterListIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Info as InfoIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { adminGet, adminPost, adminPut } from '@/services/adminApiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';

// Dynamic import for EnhancedAdminLayout to avoid SSR issues
const EnhancedAdminLayout = dynamic(() => import('@/components/admin/EnhancedAdminLayout'), {
  ssr: false
});

export default function PrivacyControlsPanel() {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({});
  const [privacySettings, setPrivacySettings] = useState({});
  const [userPrivacyData, setUserPrivacyData] = useState([]);
  const [globalSettings, setGlobalSettings] = useState({});
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [filters, setFilters] = useState({
    privacyLevel: '',
    userType: '',
    search: ''
  });
  const [settingsDialogOpen, setSettingsDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState(null);

  // Privacy level options
  const privacyLevelOptions = [
    { value: 'PUBLIC', label: 'Public', icon: <PublicIcon />, color: 'success' },
    { value: 'PREMIUM_ONLY', label: 'Premium Only', icon: <GroupIcon />, color: 'warning' },
    { value: 'MUTUAL_INTEREST', label: 'Mutual Interest', icon: <PersonIcon />, color: 'info' },
    { value: 'PRIVATE', label: 'Private', icon: <LockIcon />, color: 'error' }
  ];

  // Display name preference options
  const displayNameOptions = [
    { value: 'FULL_NAME', label: 'Full Name', description: 'Show complete name' },
    { value: 'FIRST_NAME', label: 'First Name Only', description: 'Show only first name' },
    { value: 'PROFILE_ID', label: 'Profile ID', description: 'Show profile ID instead' },
    { value: 'ANONYMOUS', label: 'Anonymous', description: 'Hide name completely' }
  ];

  // Contact reveal preference options
  const contactRevealOptions = [
    { value: 'PREMIUM_ONLY', label: 'Premium Users Only' },
    { value: 'MUTUAL_INTEREST', label: 'Mutual Interest Required' },
    { value: 'ACCEPTED_INTEREST', label: 'After Interest Acceptance' },
    { value: 'NEVER', label: 'Never Reveal' }
  ];

  useEffect(() => {
    fetchStats();
    fetchPrivacySettings();
    fetchUserPrivacyData();
    fetchGlobalSettings();
  }, [page, rowsPerPage, filters]);

  const fetchStats = async () => {
    try {
      // Mock stats for now - can be implemented in backend later
      setStats({
        totalUsers: 1250,
        publicProfiles: 850,
        privateProfiles: 400,
        premiumOnlyProfiles: 320,
        averagePrivacyScore: 72.5,
        contactRevealsToday: 45,
        privacyViolations: 3
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const fetchPrivacySettings = async () => {
    try {
      setLoading(true);
      // Mock data for now - replace with actual API call
      const mockSettings = {
        defaultDisplayNamePreference: 'FIRST_NAME',
        defaultContactRevealPreference: 'PREMIUM_ONLY',
        allowProfileViews: true,
        showOnlineStatus: false,
        showLastSeen: false,
        allowDirectMessages: true,
        showContactInfo: false,
        requireMutualInterest: true
      };
      setPrivacySettings(mockSettings);
    } catch (error) {
      console.error('Error fetching privacy settings:', error);
      toast.error('Error fetching privacy settings');
    } finally {
      setLoading(false);
    }
  };

  const fetchUserPrivacyData = async () => {
    try {
      // Mock data for now
      const mockUserData = [
        {
          id: 'user1',
          name: 'John Doe',
          email: '<EMAIL>',
          displayNamePreference: 'FIRST_NAME',
          contactRevealPreference: 'PREMIUM_ONLY',
          allowProfileViews: true,
          showOnlineStatus: false,
          privacyScore: 85,
          lastUpdated: new Date().toISOString()
        },
        {
          id: 'user2',
          name: 'Jane Smith',
          email: '<EMAIL>',
          displayNamePreference: 'PROFILE_ID',
          contactRevealPreference: 'MUTUAL_INTEREST',
          allowProfileViews: false,
          showOnlineStatus: false,
          privacyScore: 95,
          lastUpdated: new Date(Date.now() - 86400000).toISOString()
        }
      ];
      setUserPrivacyData(mockUserData);
      setTotalCount(mockUserData.length);
    } catch (error) {
      console.error('Error fetching user privacy data:', error);
    }
  };

  const fetchGlobalSettings = async () => {
    try {
      // Mock global settings
      setGlobalSettings({
        enforceMinimumPrivacy: true,
        allowAnonymousProfiles: false,
        requireContactVerification: true,
        enablePrivacyAudit: true,
        autoPrivacyRecommendations: true,
        privacyScoreThreshold: 70,
        contactRevealCooldown: 24, // hours
        maxContactRevealsPerDay: 10
      });
    } catch (error) {
      console.error('Error fetching global settings:', error);
    }
  };

  const handleUpdateUserPrivacy = async (userId, settings) => {
    try {
      // Mock API call for now
      console.log(`Updating privacy for user ${userId}:`, settings);
      toast.success('User privacy settings updated successfully');
      fetchUserPrivacyData();
    } catch (error) {
      console.error('Error updating user privacy:', error);
      toast.error('Error updating user privacy settings');
    }
  };

  const handleUpdateGlobalSettings = async (newSettings) => {
    try {
      // Mock API call for now
      setGlobalSettings(newSettings);
      toast.success('Global privacy settings updated successfully');
    } catch (error) {
      console.error('Error updating global settings:', error);
      toast.error('Error updating global settings');
    }
  };

  const getPrivacyScoreColor = (score) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'error';
  };

  const getDisplayNameChip = (preference) => {
    const option = displayNameOptions.find(opt => opt.value === preference) || displayNameOptions[0];
    return (
      <Chip
        label={option.label}
        size="small"
        variant="outlined"
        color="primary"
      />
    );
  };

  const getContactRevealChip = (preference) => {
    const option = contactRevealOptions.find(opt => opt.value === preference) || contactRevealOptions[0];
    return (
      <Chip
        label={option.label}
        size="small"
        variant="outlined"
        color="secondary"
      />
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const TabPanel = ({ children, value, index, ...other }) => (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`privacy-tabpanel-${index}`}
      aria-labelledby={`privacy-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );

  return (
    <EnhancedAdminLayout title="Privacy Controls Panel">
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Privacy Controls Panel
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={() => {
                fetchStats();
                fetchPrivacySettings();
                fetchUserPrivacyData();
                fetchGlobalSettings();
              }}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={<SettingsIcon />}
              onClick={() => setSettingsDialogOpen(true)}
            >
              Global Settings
            </Button>
          </Box>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Total Users
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.totalUsers || 0}
                    </Typography>
                  </Box>
                  <PeopleIcon color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Public Profiles
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.publicProfiles || 0}
                    </Typography>
                  </Box>
                  <PublicIcon color="success" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Private Profiles
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.privateProfiles || 0}
                    </Typography>
                  </Box>
                  <LockIcon color="error" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Avg Privacy Score
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.averagePrivacyScore?.toFixed(1) || 0}
                    </Typography>
                  </Box>
                  <ShieldIcon color="info" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Contact Reveals
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.contactRevealsToday || 0}
                    </Typography>
                  </Box>
                  <PhoneIcon color="warning" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Privacy Violations
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.privacyViolations || 0}
                    </Typography>
                  </Box>
                  <WarningIcon color="error" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Tabs */}
        <Paper sx={{ mb: 3 }}>
          <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
            <Tab label="User Privacy Settings" icon={<PersonIcon />} />
            <Tab label="Default Settings" icon={<SettingsIcon />} />
            <Tab label="Privacy Analytics" icon={<TrendingUpIcon />} />
            <Tab label="Global Controls" icon={<SecurityIcon />} />
          </Tabs>
        </Paper>

        {/* Tab Panels */}
        <TabPanel value={activeTab} index={0}>
          {/* User Privacy Table */}
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>User</TableCell>
                  <TableCell>Display Name</TableCell>
                  <TableCell>Contact Reveal</TableCell>
                  <TableCell>Profile Views</TableCell>
                  <TableCell>Privacy Score</TableCell>
                  <TableCell>Last Updated</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                ) : userPrivacyData.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <Typography variant="body2" color="text.secondary">
                        No user privacy data found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  userPrivacyData.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Avatar sx={{ width: 32, height: 32 }}>
                            {user.name?.charAt(0)}
                          </Avatar>
                          <Box>
                            <Typography variant="body2" fontWeight="600">
                              {user.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {user.email}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        {getDisplayNameChip(user.displayNamePreference)}
                      </TableCell>
                      <TableCell>
                        {getContactRevealChip(user.contactRevealPreference)}
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={user.allowProfileViews ? <VisibilityIcon /> : <VisibilityOffIcon />}
                          label={user.allowProfileViews ? 'Allowed' : 'Blocked'}
                          color={user.allowProfileViews ? 'success' : 'error'}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Chip
                            label={`${user.privacyScore}%`}
                            color={getPrivacyScoreColor(user.privacyScore)}
                            size="small"
                          />
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDate(user.lastUpdated)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                          <Tooltip title="Edit Privacy Settings">
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => setEditingUser(user)}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        {/* Default Settings Tab */}
        <TabPanel value={activeTab} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Default Display Settings
                  </Typography>
                  <Stack spacing={3}>
                    <FormControl fullWidth>
                      <InputLabel>Default Display Name Preference</InputLabel>
                      <Select
                        value={privacySettings.defaultDisplayNamePreference || 'FIRST_NAME'}
                        label="Default Display Name Preference"
                        onChange={(e) => setPrivacySettings({
                          ...privacySettings,
                          defaultDisplayNamePreference: e.target.value
                        })}
                      >
                        {displayNameOptions.map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            <Box>
                              <Typography variant="body1">{option.label}</Typography>
                              <Typography variant="caption" color="text.secondary">
                                {option.description}
                              </Typography>
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                    <FormControl fullWidth>
                      <InputLabel>Default Contact Reveal Preference</InputLabel>
                      <Select
                        value={privacySettings.defaultContactRevealPreference || 'PREMIUM_ONLY'}
                        label="Default Contact Reveal Preference"
                        onChange={(e) => setPrivacySettings({
                          ...privacySettings,
                          defaultContactRevealPreference: e.target.value
                        })}
                      >
                        {contactRevealOptions.map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Default Privacy Controls
                  </Typography>
                  <Stack spacing={2}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={privacySettings.allowProfileViews || false}
                          onChange={(e) => setPrivacySettings({
                            ...privacySettings,
                            allowProfileViews: e.target.checked
                          })}
                        />
                      }
                      label="Allow Profile Views by Default"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={privacySettings.showOnlineStatus || false}
                          onChange={(e) => setPrivacySettings({
                            ...privacySettings,
                            showOnlineStatus: e.target.checked
                          })}
                        />
                      }
                      label="Show Online Status by Default"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={privacySettings.showLastSeen || false}
                          onChange={(e) => setPrivacySettings({
                            ...privacySettings,
                            showLastSeen: e.target.checked
                          })}
                        />
                      }
                      label="Show Last Seen by Default"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={privacySettings.allowDirectMessages || false}
                          onChange={(e) => setPrivacySettings({
                            ...privacySettings,
                            allowDirectMessages: e.target.checked
                          })}
                        />
                      }
                      label="Allow Direct Messages by Default"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={privacySettings.requireMutualInterest || false}
                          onChange={(e) => setPrivacySettings({
                            ...privacySettings,
                            requireMutualInterest: e.target.checked
                          })}
                        />
                      }
                      label="Require Mutual Interest by Default"
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Privacy Analytics Tab */}
        <TabPanel value={activeTab} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Privacy Distribution
                  </Typography>
                  <Stack spacing={2}>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Public Profiles
                      </Typography>
                      <Typography variant="h5" color="success.main">
                        {((stats.publicProfiles / stats.totalUsers) * 100).toFixed(1)}%
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Premium Only Profiles
                      </Typography>
                      <Typography variant="h5" color="warning.main">
                        {((stats.premiumOnlyProfiles / stats.totalUsers) * 100).toFixed(1)}%
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Private Profiles
                      </Typography>
                      <Typography variant="h5" color="error.main">
                        {((stats.privateProfiles / stats.totalUsers) * 100).toFixed(1)}%
                      </Typography>
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Privacy Trends
                  </Typography>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Privacy analytics charts would be implemented here with real data
                  </Alert>
                  <Typography variant="body2" color="text.secondary">
                    This section would show trends in privacy settings adoption,
                    contact reveal patterns, and user behavior analytics.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Global Controls Tab */}
        <TabPanel value={activeTab} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Platform Privacy Controls
                  </Typography>
                  <Stack spacing={2}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={globalSettings.enforceMinimumPrivacy || false}
                          onChange={(e) => handleUpdateGlobalSettings({
                            ...globalSettings,
                            enforceMinimumPrivacy: e.target.checked
                          })}
                        />
                      }
                      label="Enforce Minimum Privacy Standards"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={globalSettings.allowAnonymousProfiles || false}
                          onChange={(e) => handleUpdateGlobalSettings({
                            ...globalSettings,
                            allowAnonymousProfiles: e.target.checked
                          })}
                        />
                      }
                      label="Allow Anonymous Profiles"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={globalSettings.requireContactVerification || false}
                          onChange={(e) => handleUpdateGlobalSettings({
                            ...globalSettings,
                            requireContactVerification: e.target.checked
                          })}
                        />
                      }
                      label="Require Contact Verification"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={globalSettings.enablePrivacyAudit || false}
                          onChange={(e) => handleUpdateGlobalSettings({
                            ...globalSettings,
                            enablePrivacyAudit: e.target.checked
                          })}
                        />
                      }
                      label="Enable Privacy Audit Logs"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={globalSettings.autoPrivacyRecommendations || false}
                          onChange={(e) => handleUpdateGlobalSettings({
                            ...globalSettings,
                            autoPrivacyRecommendations: e.target.checked
                          })}
                        />
                      }
                      label="Auto Privacy Recommendations"
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Contact Reveal Limits
                  </Typography>
                  <Stack spacing={3}>
                    <TextField
                      label="Privacy Score Threshold"
                      type="number"
                      value={globalSettings.privacyScoreThreshold || 70}
                      onChange={(e) => handleUpdateGlobalSettings({
                        ...globalSettings,
                        privacyScoreThreshold: parseInt(e.target.value)
                      })}
                      InputProps={{ endAdornment: '%' }}
                      helperText="Minimum privacy score required for contact reveal"
                    />
                    <TextField
                      label="Contact Reveal Cooldown"
                      type="number"
                      value={globalSettings.contactRevealCooldown || 24}
                      onChange={(e) => handleUpdateGlobalSettings({
                        ...globalSettings,
                        contactRevealCooldown: parseInt(e.target.value)
                      })}
                      InputProps={{ endAdornment: 'hours' }}
                      helperText="Time between contact reveals for same user"
                    />
                    <TextField
                      label="Max Contact Reveals Per Day"
                      type="number"
                      value={globalSettings.maxContactRevealsPerDay || 10}
                      onChange={(e) => handleUpdateGlobalSettings({
                        ...globalSettings,
                        maxContactRevealsPerDay: parseInt(e.target.value)
                      })}
                      helperText="Maximum contact reveals allowed per user per day"
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Edit User Privacy Dialog */}
        <Dialog open={!!editingUser} onClose={() => setEditingUser(null)} maxWidth="md" fullWidth>
          <DialogTitle>Edit Privacy Settings - {editingUser?.name}</DialogTitle>
          <DialogContent>
            {editingUser && (
              <Grid container spacing={3} sx={{ mt: 1 }}>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Display Name Preference</InputLabel>
                    <Select
                      value={editingUser.displayNamePreference}
                      label="Display Name Preference"
                      onChange={(e) => setEditingUser({
                        ...editingUser,
                        displayNamePreference: e.target.value
                      })}
                    >
                      {displayNameOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Contact Reveal Preference</InputLabel>
                    <Select
                      value={editingUser.contactRevealPreference}
                      label="Contact Reveal Preference"
                      onChange={(e) => setEditingUser({
                        ...editingUser,
                        contactRevealPreference: e.target.value
                      })}
                    >
                      {contactRevealOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <Stack spacing={2}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={editingUser.allowProfileViews}
                          onChange={(e) => setEditingUser({
                            ...editingUser,
                            allowProfileViews: e.target.checked
                          })}
                        />
                      }
                      label="Allow Profile Views"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={editingUser.showOnlineStatus}
                          onChange={(e) => setEditingUser({
                            ...editingUser,
                            showOnlineStatus: e.target.checked
                          })}
                        />
                      }
                      label="Show Online Status"
                    />
                  </Stack>
                </Grid>
              </Grid>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setEditingUser(null)}>Cancel</Button>
            <Button
              onClick={() => {
                handleUpdateUserPrivacy(editingUser.id, editingUser);
                setEditingUser(null);
              }}
              variant="contained"
            >
              Save Changes
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </EnhancedAdminLayout>
  );
}
