/**
 * Event-Based Notification Service
 * 
 * This service handles sending notifications based on application events.
 * It provides methods for different events like new matches, profile views, etc.
 */
const { PrismaClient } = require('@prisma/client');
const notificationService = require('./notification-service');
const prisma = new PrismaClient();

/**
 * Send notification when a new match is found
 * 
 * @param {string} userId - ID of the user to notify
 * @param {Object} matchData - Data about the match
 * @param {string} matchData.matchId - ID of the matched user
 * @param {string} matchData.matchName - Name of the matched user
 * @param {string} matchData.matchPhotoUrl - URL of the matched user's photo
 * @param {number} matchData.matchPercentage - Match percentage (optional)
 * @returns {Promise<Object>} Result of the notification send operation
 */
const notifyNewMatch = async (userId, matchData) => {
  try {
    // Get user's name
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        profile: {
          select: { firstName: true }
        }
      }
    });

    if (!user || !user.profile) {
      console.log(`Cannot send match notification: User ${userId} not found or has no profile`);
      return { success: false, error: 'User not found or has no profile' };
    }

    // Create notification data
    const notificationData = {
      recipientName: user.profile.firstName,
      matchName: matchData.matchName,
      matchId: matchData.matchId,
      matchPhotoUrl: matchData.matchPhotoUrl,
      matchPercentage: matchData.matchPercentage
    };

    // Create notification content using template
    const notificationContent = notificationService.templates.newMatchTemplate(notificationData);

    // Send notification
    return await notificationService.sendToUser(userId, notificationContent);
  } catch (error) {
    console.error('Error sending new match notification:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Send notification when someone views a user's profile
 * 
 * @param {string} profileOwnerId - ID of the profile owner
 * @param {Object} viewerData - Data about the viewer
 * @param {string} viewerData.viewerId - ID of the viewer
 * @param {string} viewerData.viewerName - Name of the viewer
 * @param {string} viewerData.viewerPhotoUrl - URL of the viewer's photo
 * @returns {Promise<Object>} Result of the notification send operation
 */
const notifyProfileView = async (profileOwnerId, viewerData) => {
  try {
    // Check if user has premium status (only premium users get profile view notifications)
    const user = await prisma.user.findUnique({
      where: { id: profileOwnerId },
      select: { isPremium: true }
    });

    if (!user || !user.isPremium) {
      console.log(`Not sending profile view notification: User ${profileOwnerId} is not premium`);
      return { success: false, error: 'User is not premium' };
    }

    // Create notification data
    const notificationData = {
      viewerName: viewerData.viewerName,
      viewerId: viewerData.viewerId,
      viewerPhotoUrl: viewerData.viewerPhotoUrl
    };

    // Create notification content using template
    const notificationContent = notificationService.templates.profileViewTemplate(notificationData);

    // Send notification
    return await notificationService.sendToUser(profileOwnerId, notificationContent);
  } catch (error) {
    console.error('Error sending profile view notification:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Send notification when a user receives an interest
 * 
 * @param {string} recipientId - ID of the interest recipient
 * @param {Object} senderData - Data about the sender
 * @param {string} senderData.senderId - ID of the sender
 * @param {string} senderData.senderName - Name of the sender
 * @param {string} senderData.senderPhotoUrl - URL of the sender's photo
 * @param {string} senderData.interestId - ID of the interest
 * @returns {Promise<Object>} Result of the notification send operation
 */
const notifyInterestReceived = async (recipientId, senderData) => {
  try {
    // Create notification data
    const notificationData = {
      senderName: senderData.senderName,
      senderId: senderData.senderId,
      senderPhotoUrl: senderData.senderPhotoUrl,
      interestId: senderData.interestId
    };

    // Create notification content using template
    const notificationContent = notificationService.templates.interestReceivedTemplate(notificationData);

    // Send notification
    return await notificationService.sendToUser(recipientId, notificationContent);
  } catch (error) {
    console.error('Error sending interest received notification:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Send notification when a user's interest is accepted
 * 
 * @param {string} senderId - ID of the original interest sender
 * @param {Object} acceptorData - Data about the acceptor
 * @param {string} acceptorData.acceptorId - ID of the acceptor
 * @param {string} acceptorData.acceptorName - Name of the acceptor
 * @param {string} acceptorData.acceptorPhotoUrl - URL of the acceptor's photo
 * @returns {Promise<Object>} Result of the notification send operation
 */
const notifyInterestAccepted = async (senderId, acceptorData) => {
  try {
    // Get user's name
    const user = await prisma.user.findUnique({
      where: { id: senderId },
      select: {
        profile: {
          select: { firstName: true }
        }
      }
    });

    if (!user || !user.profile) {
      console.log(`Cannot send interest accepted notification: User ${senderId} not found or has no profile`);
      return { success: false, error: 'User not found or has no profile' };
    }

    // Create notification data
    const notificationData = {
      recipientName: user.profile.firstName,
      acceptorName: acceptorData.acceptorName,
      acceptorId: acceptorData.acceptorId,
      acceptorPhotoUrl: acceptorData.acceptorPhotoUrl
    };

    // Create notification content using template
    const notificationContent = notificationService.templates.interestAcceptedTemplate(notificationData);

    // Send notification
    return await notificationService.sendToUser(senderId, notificationContent);
  } catch (error) {
    console.error('Error sending interest accepted notification:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Send notification when a user receives a new message
 * 
 * @param {string} recipientId - ID of the message recipient
 * @param {Object} messageData - Data about the message
 * @param {string} messageData.senderId - ID of the sender
 * @param {string} messageData.senderName - Name of the sender
 * @param {string} messageData.senderPhotoUrl - URL of the sender's photo
 * @param {string} messageData.messagePreview - Preview of the message
 * @param {string} messageData.conversationId - ID of the conversation
 * @returns {Promise<Object>} Result of the notification send operation
 */
const notifyNewMessage = async (recipientId, messageData) => {
  try {
    // Create notification data
    const notificationData = {
      senderName: messageData.senderName,
      senderId: messageData.senderId,
      senderPhotoUrl: messageData.senderPhotoUrl,
      messagePreview: messageData.messagePreview,
      conversationId: messageData.conversationId
    };

    // Create notification content using template
    const notificationContent = notificationService.templates.newMessageTemplate(notificationData);

    // Send notification
    return await notificationService.sendToUser(recipientId, notificationContent);
  } catch (error) {
    console.error('Error sending new message notification:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Send notification about verification status update
 * 
 * @param {string} userId - ID of the user
 * @param {Object} verificationData - Data about the verification
 * @param {string} verificationData.status - Status of the verification (APPROVED, REJECTED)
 * @param {string} verificationData.reason - Reason for rejection (if applicable)
 * @returns {Promise<Object>} Result of the notification send operation
 */
const notifyVerificationStatus = async (userId, verificationData) => {
  try {
    // Create notification data
    const notificationData = {
      status: verificationData.status,
      reason: verificationData.reason
    };

    // Create notification content using template
    const notificationContent = notificationService.templates.verificationStatusTemplate(notificationData);

    // Send notification
    return await notificationService.sendToUser(userId, notificationContent);
  } catch (error) {
    console.error('Error sending verification status notification:', error);
    return { success: false, error: error.message };
  }
};

module.exports = {
  notifyNewMatch,
  notifyProfileView,
  notifyInterestReceived,
  notifyInterestAccepted,
  notifyNewMessage,
  notifyVerificationStatus
};
