/**
 * Indian Date Formatting Utilities
 * 
 * This file contains utilities for formatting dates according to Indian standards (DD/MM/YYYY)
 * instead of US standards (MM/DD/YYYY)
 */

import { format, parse, isValid } from 'date-fns';
import { enIN } from 'date-fns/locale';

/**
 * Format date for Indian display (DD/MM/YYYY)
 * @param {Date|string} date - Date to format
 * @returns {string} Formatted date string
 */
export const formatIndianDate = (date) => {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (!isValid(dateObj)) return '';
    
    return format(dateObj, 'dd/MM/yyyy', { locale: enIN });
  } catch (error) {
    console.error('Error formatting Indian date:', error);
    return '';
  }
};

/**
 * Format date for Indian display with month name (DD MMM YYYY)
 * @param {Date|string} date - Date to format
 * @returns {string} Formatted date string
 */
export const formatIndianDateWithMonth = (date) => {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (!isValid(dateObj)) return '';
    
    return format(dateObj, 'dd MMM yyyy', { locale: enIN });
  } catch (error) {
    console.error('Error formatting Indian date with month:', error);
    return '';
  }
};

/**
 * Format date for Indian display with full month name (DD MMMM YYYY)
 * @param {Date|string} date - Date to format
 * @returns {string} Formatted date string
 */
export const formatIndianDateFull = (date) => {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (!isValid(dateObj)) return '';
    
    return format(dateObj, 'dd MMMM yyyy', { locale: enIN });
  } catch (error) {
    console.error('Error formatting Indian date full:', error);
    return '';
  }
};

/**
 * Parse Indian date format (DD/MM/YYYY) to Date object
 * @param {string} dateString - Date string in DD/MM/YYYY format
 * @returns {Date|null} Parsed date object or null if invalid
 */
export const parseIndianDate = (dateString) => {
  if (!dateString) return null;
  
  try {
    const parsed = parse(dateString, 'dd/MM/yyyy', new Date());
    return isValid(parsed) ? parsed : null;
  } catch (error) {
    console.error('Error parsing Indian date:', error);
    return null;
  }
};

/**
 * Convert US date format (MM/DD/YYYY) to Indian format (DD/MM/YYYY)
 * @param {string} usDateString - Date string in MM/DD/YYYY format
 * @returns {string} Date string in DD/MM/YYYY format
 */
export const convertUSToIndianFormat = (usDateString) => {
  if (!usDateString) return '';
  
  try {
    const parsed = parse(usDateString, 'MM/dd/yyyy', new Date());
    if (!isValid(parsed)) return '';
    
    return format(parsed, 'dd/MM/yyyy');
  } catch (error) {
    console.error('Error converting US to Indian format:', error);
    return '';
  }
};

/**
 * Get Indian locale configuration for MUI DatePicker
 * @returns {object} Locale configuration
 */
export const getIndianLocaleConfig = () => ({
  locale: enIN,
  dateFormat: 'dd/MM/yyyy',
  inputFormat: 'DD/MM/YYYY',
  mask: '__/__/____',
  placeholder: 'DD/MM/YYYY'
});

/**
 * Validate Indian date format
 * @param {string} dateString - Date string to validate
 * @returns {boolean} True if valid Indian date format
 */
export const isValidIndianDate = (dateString) => {
  if (!dateString) return false;
  
  // Check format DD/MM/YYYY
  const indianDateRegex = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
  if (!indianDateRegex.test(dateString)) return false;
  
  const parsed = parseIndianDate(dateString);
  return parsed !== null;
};

/**
 * Format birth date for display in profiles
 * @param {Date|string} birthDate - Birth date
 * @returns {string} Formatted birth date for display
 */
export const formatBirthDateDisplay = (birthDate) => {
  if (!birthDate) return 'Not provided';
  
  try {
    const dateObj = typeof birthDate === 'string' ? new Date(birthDate) : birthDate;
    if (!isValid(dateObj)) return 'Invalid date';
    
    // Format as "15 January 1995" (Indian preference)
    return format(dateObj, 'd MMMM yyyy', { locale: enIN });
  } catch (error) {
    console.error('Error formatting birth date display:', error);
    return 'Invalid date';
  }
};

/**
 * Get age from birth date
 * @param {Date|string} birthDate - Birth date
 * @returns {number} Age in years
 */
export const getAgeFromBirthDate = (birthDate) => {
  if (!birthDate) return 0;
  
  try {
    const dateObj = typeof birthDate === 'string' ? new Date(birthDate) : birthDate;
    if (!isValid(dateObj)) return 0;
    
    const today = new Date();
    let age = today.getFullYear() - dateObj.getFullYear();
    const monthDiff = today.getMonth() - dateObj.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dateObj.getDate())) {
      age--;
    }
    
    return age;
  } catch (error) {
    console.error('Error calculating age:', error);
    return 0;
  }
};

/**
 * Format date for API submission (ISO format)
 * @param {Date|string} date - Date to format
 * @returns {string} ISO formatted date string (YYYY-MM-DD)
 */
export const formatDateForAPI = (date) => {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (!isValid(dateObj)) return '';
    
    return format(dateObj, 'yyyy-MM-dd');
  } catch (error) {
    console.error('Error formatting date for API:', error);
    return '';
  }
};

/**
 * Default export with all utilities
 */
export default {
  formatIndianDate,
  formatIndianDateWithMonth,
  formatIndianDateFull,
  parseIndianDate,
  convertUSToIndianFormat,
  getIndianLocaleConfig,
  isValidIndianDate,
  formatBirthDateDisplay,
  getAgeFromBirthDate,
  formatDateForAPI
};
