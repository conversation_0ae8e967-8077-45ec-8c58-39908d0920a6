const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  // Clean up old data for repeatability (optional in dev)
  await prisma.photo.deleteMany();
  await prisma.profile.deleteMany();
  await prisma.user.deleteMany();

  // Create some users with profiles and photos
  const users = await prisma.user.createMany({
    data: [
      {
        id: 'user1',
        phone: '9999999999',
        email: '<EMAIL>',
        password: 'demo1234', // Use a hashed password in production!
        isVerified: true,
        profileStatus: 'ACTIVE',
        isPremium: true
      },
      {
        id: 'user2',
        phone: '8888888888',
        email: '<EMAIL>',
        password: 'demo1234',
        isVerified: false,
        profileStatus: 'PENDING_APPROVAL',
        isPremium: false
      }
    ]
  });

  // Create their profiles
  await prisma.profile.create({
    data: {
      userId: 'user1',
      fullName: '<PERSON><PERSON><PERSON><PERSON>',
      gender: 'Male',
      dateOfBirth: new Date('1990-01-01'),
      height: '175',
      city: 'Pune',
      education: 'B.Tech',
      occupation: 'Engineer',
      incomeRange: '10-15 LPA',
      fatherName: 'Digambar Nilkhan',
      motherName: 'Sunita Nilkhan',
      totalSiblings: 2,
      marriedSiblings: 1,
      familyContact: '9999999999',
    }
  });

  await prisma.profile.create({
    data: {
      userId: 'user2',
      fullName: 'Priya Sharma',
      gender: 'Female',
      dateOfBirth: new Date('1994-05-10'),
      height: '162',
      city: 'Mumbai',
      education: 'MBA',
      occupation: 'Manager',
      incomeRange: '7-10 LPA',
      fatherName: 'Suresh Sharma',
      motherName: 'Lata Sharma',
      totalSiblings: 1,
      marriedSiblings: 0,
      familyContact: '8888888888',
    }
  });

  // Add a profile photo for user1
  await prisma.photo.create({
    data: {
      userId: 'user1',
      url: '/uploads/profile_photos/vaibhav.jpg',
      visibility: 'PUBLIC',
      status: 'APPROVED',
      isProfilePic: true,
    }
  });

  // Add a profile photo for user2
  await prisma.photo.create({
    data: {
      userId: 'user2',
      url: '/uploads/profile_photos/priya.jpg',
      visibility: 'PUBLIC',
      status: 'APPROVED',
      isProfilePic: true,
    }
  });

  console.log('Test users, profiles, and photos added!');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
