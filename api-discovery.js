import React, { useState, useEffect } from 'react';
import AdminLayout from '/components/admin/AdminLayout';
import { useRouter } from 'next/router';
import axios from 'axios';
import { toast, ToastContainer } from 'react-toastify';
import Link from 'next/link';

export default function ApiDiscovery() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [endpoints, setEndpoints] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedEndpoint, setSelectedEndpoint] = useState(null);

  useEffect(() => {
    fetchEndpoints();
  }, []);

  const fetchEndpoints = async () => {
    try {
      setLoading(true);
      const response = await axios.get("/api/admin/api-discovery");
      if (response.data.success) {
        setEndpoints(response.data.endpoints);
      } else {
        toast.error("Failed to fetch API endpoints");
      }
    } catch (error) {
      console.error("Error fetching API endpoints:", error);
      toast.error("An error occurred while fetching API endpoints");
    } finally {
      setLoading(false);
    }
  };

  const handleEndpointClick = (endpoint) => {
    setSelectedEndpoint(endpoint);
  };

  const filteredEndpoints = endpoints.filter(endpoint => 
    endpoint.path.toLowerCase().includes(searchTerm.toLowerCase()) ||
    endpoint.method.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <AdminLayout>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-6">API Discovery</h1>
        
        <div className="mb-6">
          <input
            type="text"
            placeholder="Search endpoints..."
            className="w-full p-2 border border-gray-300 rounded"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        {loading ? (
          <div className="text-center py-8">
            <div className="spinner"></div>
            <p className="mt-2">Loading API endpoints...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-1 bg-white rounded-lg shadow">
              <div className="p-4 border-b">
                <h2 className="text-xl font-semibold">Endpoints</h2>
                <p className="text-sm text-gray-500">{filteredEndpoints.length} endpoints found</p>
              </div>
              <div className="p-4 max-h-[600px] overflow-y-auto">
                {filteredEndpoints.length > 0 ? (
                  <ul className="divide-y">
                    {filteredEndpoints.map((endpoint, index) => (
                      <li 
                        key={index}
                        className={`py-3 px-2 cursor-pointer hover:bg-gray-50 ${selectedEndpoint && selectedEndpoint.path === endpoint.path ? 'bg-blue-50' : ''}`}
                        onClick={() => handleEndpointClick(endpoint)}
                      >
                        <div className="flex items-center">
                          <span className={`inline-block px-2 py-1 text-xs font-semibold rounded mr-2 ${
                            endpoint.method === 'GET' ? 'bg-green-100 text-green-800' :
                            endpoint.method === 'POST' ? 'bg-blue-100 text-blue-800' :
                            endpoint.method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                            endpoint.method === 'DELETE' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {endpoint.method}
                          </span>
                          <span className="text-sm font-medium truncate">{endpoint.path}</span>
                        </div>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div className="text-center py-4">
                    <p className="text-gray-500">No endpoints found</p>
                  </div>
                )}
              </div>
            </div>
            
            <div className="md:col-span-2 bg-white rounded-lg shadow">
              {selectedEndpoint ? (
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <span className={`inline-block px-2 py-1 text-xs font-semibold rounded mr-2 ${
                      selectedEndpoint.method === 'GET' ? 'bg-green-100 text-green-800' :
                      selectedEndpoint.method === 'POST' ? 'bg-blue-100 text-blue-800' :
                      selectedEndpoint.method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                      selectedEndpoint.method === 'DELETE' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {selectedEndpoint.method}
                    </span>
                    <h2 className="text-xl font-semibold">{selectedEndpoint.path}</h2>
                  </div>
                  
                  <div className="mb-6">
                    <h3 className="text-lg font-medium mb-2">Description</h3>
                    <p className="text-gray-700">{selectedEndpoint.description || "No description available"}</p>
                  </div>
                  
                  <div className="mb-6">
                    <h3 className="text-lg font-medium mb-2">Authentication</h3>
                    <p className="text-gray-700">
                      {selectedEndpoint.requiresAuth ? "Authentication required" : "No authentication required"}
                    </p>
                  </div>
                  
                  <div className="mb-6">
                    <h3 className="text-lg font-medium mb-2">Parameters</h3>
                    {selectedEndpoint.parameters && selectedEndpoint.parameters.length > 0 ? (
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Required</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {selectedEndpoint.parameters.map((param, index) => (
                              <tr key={index}>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{param.name}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{param.type}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{param.required ? "Yes" : "No"}</td>
                                <td className="px-6 py-4 text-sm text-gray-500">{param.description}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <p className="text-gray-700">No parameters</p>
                    )}
                  </div>
                  
                  <div className="mb-6">
                    <h3 className="text-lg font-medium mb-2">Response</h3>
                    {selectedEndpoint.response ? (
                      <div className="bg-gray-50 p-4 rounded">
                        <pre className="text-sm overflow-x-auto">{JSON.stringify(selectedEndpoint.response, null, 2)}</pre>
                      </div>
                    ) : (
                      <p className="text-gray-700">No response example available</p>
                    )}
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium mb-2">Try it</h3>
                    <Link href={`/admin/api-tester?endpoint=${encodeURIComponent(selectedEndpoint.path)}&method=${selectedEndpoint.method}`}>
                      <a className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Test this endpoint
                      </a>
                    </Link>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-[600px]">
                  <div className="text-center">
                    <p className="text-gray-500 mb-4">Select an endpoint to view details</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
      <ToastContainer />
    </AdminLayout>
  );
}
