/**
 * Toast Notification Styles
 * 
 * This file contains styles for toast notifications and guided error resolution.
 */

/* Guided Error Toast */
.guided-error {
  font-family: var(--font-primary);
  color: var(--text-primary);
}

.guided-error-header {
  margin-bottom: 8px;
}

.guided-error-header strong {
  display: block;
  font-weight: 600;
  margin-bottom: 4px;
}

.error-code {
  display: inline-block;
  font-size: 0.75rem;
  color: var(--text-secondary);
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2px 6px;
  border-radius: 4px;
  margin-top: 4px;
}

.guided-error-steps {
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
  padding: 8px 12px;
  margin-top: 8px;
}

.steps-header {
  font-weight: 500;
  font-size: 0.875rem;
  margin-bottom: 4px;
}

.guided-error-steps ol {
  margin: 0;
  padding-left: 20px;
}

.guided-error-steps li {
  font-size: 0.875rem;
  margin-bottom: 4px;
  line-height: 1.4;
}

.guided-error-steps li:last-child {
  margin-bottom: 0;
}

/* Form Error Handler */
.form-error-handler {
  margin-bottom: 16px;
}

.form-error-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-error-list {
  margin-top: 8px;
}

.form-error-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 4px;
}

.form-error-icon {
  margin-right: 8px;
  margin-top: 2px;
}

.form-error-message {
  flex: 1;
}

.form-error-field {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.form-error-resolution {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.resolution-title {
  font-weight: 500;
  margin-bottom: 8px;
}

.resolution-steps {
  padding-left: 16px;
}

.resolution-step {
  display: flex;
  align-items: flex-start;
  margin-bottom: 4px;
}

.resolution-step-icon {
  margin-right: 8px;
  margin-top: 2px;
}

.resolution-step-text {
  flex: 1;
  font-size: 0.875rem;
}

.resolution-help {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}

/* Custom Toast Container Styles */
.Toastify__toast-container {
  width: auto;
  max-width: 500px;
}

.Toastify__toast {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.Toastify__toast--success {
  background-color: #e7f7ed;
  color: #0a5d2c;
  border-left: 4px solid #0a5d2c;
}

.Toastify__toast--error {
  background-color: #fdf1f0;
  color: #b71c1c;
  border-left: 4px solid #b71c1c;
}

.Toastify__toast--warning {
  background-color: #fff8e6;
  color: #b45309;
  border-left: 4px solid #b45309;
}

.Toastify__toast--info {
  background-color: #e6f4ff;
  color: #0c53b7;
  border-left: 4px solid #0c53b7;
}

/* Dark Mode Styles */
.dark-mode .Toastify__toast--success {
  background-color: rgba(10, 93, 44, 0.2);
  color: #4caf50;
  border-left: 4px solid #4caf50;
}

.dark-mode .Toastify__toast--error {
  background-color: rgba(183, 28, 28, 0.2);
  color: #ef5350;
  border-left: 4px solid #ef5350;
}

.dark-mode .Toastify__toast--warning {
  background-color: rgba(180, 83, 9, 0.2);
  color: #ffa726;
  border-left: 4px solid #ffa726;
}

.dark-mode .Toastify__toast--info {
  background-color: rgba(12, 83, 183, 0.2);
  color: #42a5f5;
  border-left: 4px solid #42a5f5;
}

.dark-mode .guided-error-steps {
  background-color: rgba(255, 255, 255, 0.05);
}

.dark-mode .error-code {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
}

.dark-mode .form-error-resolution {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}
