// API endpoint for premium plans
import axios from 'axios';
import { handleApiError } from '@/utils/errorHandler';
import { withAuth } from '@/utils/authHandler';

// Backend API URL - adjust this to your actual backend URL
const BACKEND_API_URL = 'http://localhost:3001/api';

async function handler(req, res) {
  // Set proper content type header
  res.setHeader('Content-Type', 'application/json');

  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getPlans(req, res);
      case 'POST':
        return await createPlan(req, res);
      case 'PUT':
        return await updatePlan(req, res);
      default:
        return res.status(405).json({
          success: false,
          message: 'Method not allowed'
        });
    }
  } catch (error) {
    return handleApiError(error, res, 'Premium plans API');
  }
}

// Export the handler with authentication middleware
export default with<PERSON>uth(handler, 'ADMIN');

// GET /api/admin/premium-plans
async function getPlans(req, res) {
  try {
    // Construct the API URL
    const apiUrl = `${BACKEND_API_URL}/admin/premium-plans`;

    try {
      // Fetch data from the backend API
      const response = await axios.get(apiUrl);

      // Return the response directly from the backend
      return res.status(200).json(response.data);
    } catch (apiError) {
      // Log the error
      console.error('Error fetching premium plans from backend API:', apiError.message);

      // Return a meaningful error message
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch premium plans from backend API.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available',
        note: "Please ensure the backend server is running and accessible."
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Get premium plans');
  }
}

// POST /api/admin/premium-plans
async function createPlan(req, res) {
  try {
    // Get plan data from request body
    const planData = req.body;

    // Validate required fields
    if (!planData.name || !planData.planType || !planData.amount || !planData.durationDays) {
      return res.status(400).json({
        success: false,
        message: 'Name, plan type, amount, and duration are required'
      });
    }

    try {
      // Send the create request to the backend API
      const response = await axios.post(`${BACKEND_API_URL}/admin/premium-plans`, planData);

      // Return the response from the backend
      return res.status(201).json(response.data);
    } catch (apiError) {
      // Log the error
      console.error('Error creating premium plan via backend API:', apiError.message);

      // Return a meaningful error message
      return res.status(500).json({
        success: false,
        message: 'Failed to create premium plan via backend API.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available',
        note: "Please ensure the backend server is running and accessible."
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Create premium plan');
  }
}

// PUT /api/admin/premium-plans
async function updatePlan(req, res) {
  try {
    // Get plan data from request body
    const planData = req.body;

    // Validate required fields
    if (!planData.id) {
      return res.status(400).json({
        success: false,
        message: 'Plan ID is required for update'
      });
    }

    if (!planData.name || !planData.planType || !planData.amount || !planData.durationDays) {
      return res.status(400).json({
        success: false,
        message: 'Name, plan type, amount, and duration are required'
      });
    }

    try {
      // Send the update request to the backend API
      const response = await axios.put(`${BACKEND_API_URL}/admin/premium-plans`, planData);

      // Return the response from the backend
      return res.status(200).json(response.data);
    } catch (apiError) {
      // Log the error
      console.error('Error updating premium plan via backend API:', apiError.message);

      // Return a meaningful error message
      return res.status(500).json({
        success: false,
        message: 'Failed to update premium plan via backend API.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available',
        note: "Please ensure the backend server is running and accessible."
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Update premium plan');
  }
}
