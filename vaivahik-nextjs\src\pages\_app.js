import { useEffect, useState } from 'react';
import Head from 'next/head';
import { initDarkMode, applyDarkMode } from '@/utils/darkMode';
import { useRouter } from 'next/router';
import { AuthProvider } from '@/contexts/AuthContext';
import { ToastProvider } from '@/contexts/ToastContext';
import ThemeProvider from '@/components/ThemeProvider';

// Import styles
import "@/styles/globals.css";
import "react-toastify/dist/ReactToastify.css"; // Import React Toastify styles
import "@/styles/toast.css"; // Import custom toast styles
import "@/styles/fonts.css"; // Import font definitions
import "@/styles/landing-variables.css"; // Import landing page variables
import "@/styles/theme.css"; // Import our new theme variables
import "@/styles/admin-global-styles.css"; // Import global admin styles
import "@/styles/admin.css"; // Import admin base styles (contains :root)
import "@/styles/enhanced-admin-layout.css"; // Import enhanced admin layout styles (contains :root)
import "@/styles/admin-ui-components.css"; // Import admin UI components (contains :root)
import "@/styles/referral-programs.css"; // Import referral programs styles (contains :root)

// Import admin styles as CSS modules (these will only be loaded when needed)
import adminStyles from "@/styles/AdminGlobal.module.css";
import adminComponents from "@/styles/AdminComponents.module.css";
import premiumPlans from "@/styles/PremiumPlans.module.css";
import landingVars from "@/styles/LandingPageVars.module.css";

// Loading component
const LoadingScreen = () => (
  <div className="loading-screen">
    <div className="loading-spinner"></div>
    <p>Loading...</p>
  </div>
);

export default function App({ Component, pageProps }) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  // Initialize dark mode on app load - only on client side
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const isDarkMode = initDarkMode();
      applyDarkMode(isDarkMode);
    }
  }, []);

  // Set up loading indicator for route changes
  useEffect(() => {
    const handleStart = () => setLoading(true);
    const handleComplete = () => setLoading(false);

    router.events.on('routeChangeStart', handleStart);
    router.events.on('routeChangeComplete', handleComplete);
    router.events.on('routeChangeError', handleComplete);

    return () => {
      router.events.off('routeChangeStart', handleStart);
      router.events.off('routeChangeComplete', handleComplete);
      router.events.off('routeChangeError', handleComplete);
    };
  }, [router]);

  // Get layout
  const getLayout = Component.getLayout || ((page) => page);

  return (
    <>
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="description" content="Vaivahik - Matrimony App for Maratha Community" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      {/* Admin styles are loaded via CSS modules */}
      <div className={adminStyles.adminStyles} style={{ display: 'none' }}></div>
      <div className={adminComponents.adminComponents} style={{ display: 'none' }}></div>
      <div className={premiumPlans.premiumPlans} style={{ display: 'none' }}></div>
      <div className={landingVars.landingVars} style={{ display: 'none' }}></div>

      {/* Show loading indicator - only on client side */}
      {typeof window !== 'undefined' && loading && <LoadingScreen />}

      {/* Wrap the application with ThemeProvider, AuthProvider, and ToastProvider */}
      <ThemeProvider>
        <AuthProvider>
          <ToastProvider>
            {/* Render page with its layout */}
            {getLayout(<Component {...pageProps} />)}
          </ToastProvider>
        </AuthProvider>
      </ThemeProvider>
    </>
  );
}
