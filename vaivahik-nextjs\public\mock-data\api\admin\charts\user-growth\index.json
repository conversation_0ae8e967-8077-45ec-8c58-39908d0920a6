{"success": true, "chartData": {"title": "User Growth Analytics", "subtitle": "New user registrations and total active users", "type": "mixed", "timeRange": "last_30_days", "data": {"labels": ["Jan 1", "Jan 2", "Jan 3", "Jan 4", "Jan 5", "Jan 6", "Jan 7", "Jan 8", "Jan 9", "Jan 10", "Jan 11", "Jan 12", "Jan 13", "Jan 14", "Jan 15", "Jan 16", "Jan 17", "Jan 18", "Jan 19", "Jan 20", "Jan 21", "Jan 22", "Jan 23", "Jan 24", "Jan 25", "Jan 26", "Jan 27", "Jan 28", "Jan 29", "Jan 30"], "datasets": [{"label": "New Registrations", "type": "bar", "data": [23, 31, 28, 35, 42, 38, 45, 52, 48, 56, 61, 58, 65, 72, 68, 75, 82, 78, 85, 91, 88, 95, 102, 98, 105, 112, 108, 115, 122, 118], "backgroundColor": "rgba(255, 105, 180, 0.7)", "borderColor": "#FF69B4", "borderWidth": 2, "yAxisID": "y"}, {"label": "Total Active Users", "type": "line", "data": [5420, 5451, 5479, 5514, 5556, 5594, 5639, 5691, 5739, 5795, 5856, 5914, 5979, 6051, 6119, 6194, 6276, 6354, 6439, 6530, 6618, 6713, 6815, 6913, 7018, 7130, 7238, 7353, 7475, 7593], "borderColor": "#8A2BE2", "backgroundColor": "rgba(138, 43, 226, 0.1)", "borderWidth": 3, "fill": true, "tension": 0.4, "yAxisID": "y1"}, {"label": "Premium Users", "type": "line", "data": [1084, 1092, 1098, 1107, 1118, 1126, 1139, 1152, 1164, 1179, 1195, 1208, 1224, 1241, 1256, 1273, 1291, 1307, 1325, 1344, 1361, 1379, 1398, 1415, 1434, 1454, 1472, 1492, 1513, 1532], "borderColor": "#FFD700", "backgroundColor": "rgba(255, 215, 0, 0.1)", "borderWidth": 2, "fill": false, "tension": 0.4, "yAxisID": "y1"}]}, "options": {"responsive": true, "maintainAspectRatio": false, "interaction": {"mode": "index", "intersect": false}, "plugins": {"legend": {"position": "top"}, "tooltip": {"mode": "index", "intersect": false}}, "scales": {"x": {"display": true, "title": {"display": true, "text": "Date"}}, "y": {"type": "linear", "display": true, "position": "left", "title": {"display": true, "text": "New Registrations"}, "min": 0}, "y1": {"type": "linear", "display": true, "position": "right", "title": {"display": true, "text": "Total Users"}, "grid": {"drawOnChartArea": false}, "min": 5000}}}}, "statistics": {"totalUsers": 7593, "newUsersToday": 118, "newUsersThisMonth": 2173, "growthRate": {"daily": 1.58, "weekly": 11.2, "monthly": 40.1}, "premiumUsers": 1532, "premiumConversionRate": 20.2, "activeUsers": {"daily": 3847, "weekly": 5621, "monthly": 7593}, "userRetention": {"day1": 85.3, "day7": 67.8, "day30": 45.2}}, "demographics": {"genderDistribution": {"male": 52.3, "female": 47.7}, "ageDistribution": {"18-25": 28.5, "26-30": 35.2, "31-35": 22.8, "36-40": 10.3, "40+": 3.2}, "locationDistribution": {"Mumbai": 32.1, "Pune": 24.7, "Nashik": 15.3, "Nagpur": 12.8, "Other": 15.1}}, "insights": [{"type": "positive", "message": "User growth rate has increased by 40.1% this month"}, {"type": "positive", "message": "Premium conversion rate (20.2%) is above industry average"}, {"type": "warning", "message": "Day 30 retention rate (45.2%) could be improved"}, {"type": "info", "message": "Mumbai and Pune account for 56.8% of total user base"}], "trends": {"registrationTrend": "increasing", "premiumTrend": "stable_growth", "retentionTrend": "needs_improvement", "engagementTrend": "positive"}, "filters": {"timeRanges": ["last_7_days", "last_30_days", "last_90_days", "last_year"], "userTypes": ["all", "premium", "free", "verified", "unverified"], "registrationSources": ["website", "mobile_app", "referral", "social_media"], "locations": ["Mumbai", "Pune", "<PERSON><PERSON>", "Nagpur", "Other"]}, "metadata": {"lastUpdated": "2024-01-15T12:00:00Z", "dataSource": "user_analytics", "reportGenerated": "2024-01-15T12:05:00Z", "nextUpdate": "2024-01-15T18:00:00Z"}}