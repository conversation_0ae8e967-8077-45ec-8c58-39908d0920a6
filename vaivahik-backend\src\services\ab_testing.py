"""
A/B Testing Framework for Matrimony Matching

This module provides A/B testing functionality for the matrimony matching system
to compare different model variants and features.
"""

import os
import json
import logging
import random
import hashlib
import time
from datetime import datetime, timedelta
import numpy as np
from collections import defaultdict

from .redis_cache import RedisCache

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ABTestingFramework:
    """A/B testing framework for matrimony matching"""

    def __init__(self, config=None, redis_cache=None):
        """
        Initialize the A/B testing framework

        Args:
            config (dict): Configuration parameters
            redis_cache: Redis cache instance
        """
        # Default configuration
        self.default_config = {
            'enabled': True,
            'experiments': {
                'matching_model': {
                    'variants': {
                        'A': {
                            'name': 'Base Model',
                            'description': 'Current production model',
                            'model_id': 'base',
                            'traffic_allocation': 50
                        },
                        'B': {
                            'name': 'Enhanced Model',
                            'description': 'Model with advanced features',
                            'model_id': 'enhanced',
                            'traffic_allocation': 50
                        }
                    },
                    'metrics': ['clicks', 'messages', 'connections', 'satisfaction']
                },
                'explanation_feature': {
                    'variants': {
                        'A': {
                            'name': 'No Explanation',
                            'description': 'No match explanation shown',
                            'feature_enabled': False,
                            'traffic_allocation': 50
                        },
                        'B': {
                            'name': 'With Explanation',
                            'description': 'Match explanation shown',
                            'feature_enabled': True,
                            'traffic_allocation': 50
                        }
                    },
                    'metrics': ['engagement_time', 'clicks', 'messages']
                }
            },
            'default_experiment': 'matching_model',
            'storage_path': os.path.join(os.path.dirname(__file__), '../../data/ab_testing')
        }

        # Use provided config or default
        self.config = config if config else self.default_config

        # Create storage directory if it doesn't exist
        os.makedirs(self.config['storage_path'], exist_ok=True)

        # Initialize Redis cache
        self.redis_cache = redis_cache

        # Load experiment data
        self.experiment_data = self._load_experiment_data()

    def _load_experiment_data(self):
        """
        Load experiment data from storage

        Returns:
            dict: Experiment data
        """
        experiment_data = {}

        for experiment_id in self.config['experiments']:
            data_path = os.path.join(self.config['storage_path'], f"{experiment_id}.json")

            if os.path.exists(data_path):
                try:
                    with open(data_path, 'r') as f:
                        experiment_data[experiment_id] = json.load(f)
                except Exception as e:
                    logger.error(f"Error loading experiment data for {experiment_id}: {str(e)}")
                    experiment_data[experiment_id] = self._initialize_experiment_data(experiment_id)
            else:
                experiment_data[experiment_id] = self._initialize_experiment_data(experiment_id)

        return experiment_data

    def _initialize_experiment_data(self, experiment_id):
        """
        Initialize experiment data

        Args:
            experiment_id (str): Experiment ID

        Returns:
            dict: Initialized experiment data
        """
        experiment_config = self.config['experiments'].get(experiment_id, {})
        variants = experiment_config.get('variants', {})
        metrics = experiment_config.get('metrics', [])

        data = {
            'start_time': datetime.now().isoformat(),
            'variants': {},
            'assignments': {},
            'results': {}
        }

        for variant_id, variant_config in variants.items():
            data['variants'][variant_id] = {
                'name': variant_config.get('name', variant_id),
                'description': variant_config.get('description', ''),
                'traffic_allocation': variant_config.get('traffic_allocation', 0),
                'sample_size': 0
            }

            data['results'][variant_id] = {metric: 0 for metric in metrics}

        return data

    def _save_experiment_data(self, experiment_id):
        """
        Save experiment data to storage

        Args:
            experiment_id (str): Experiment ID

        Returns:
            bool: Success status
        """
        if experiment_id not in self.experiment_data:
            logger.error(f"Experiment {experiment_id} not found")
            return False

        data_path = os.path.join(self.config['storage_path'], f"{experiment_id}.json")

        try:
            with open(data_path, 'w') as f:
                json.dump(self.experiment_data[experiment_id], f, indent=2)
            return True
        except Exception as e:
            logger.error(f"Error saving experiment data for {experiment_id}: {str(e)}")
            return False

    async def assign_variant(self, user_id, experiment_id=None):
        """
        Assign a user to a variant

        Args:
            user_id (str): User ID
            experiment_id (str): Experiment ID (if None, uses default)

        Returns:
            str: Variant ID
        """
        if not self.config['enabled']:
            # Return default variant if A/B testing is disabled
            return 'A'

        # Use default experiment if not specified
        if experiment_id is None:
            experiment_id = self.config['default_experiment']

        # Check if experiment exists
        if experiment_id not in self.config['experiments']:
            logger.error(f"Experiment {experiment_id} not found")
            return 'A'

        # Check if user is already assigned
        if experiment_id in self.experiment_data:
            if 'assignments' in self.experiment_data[experiment_id]:
                if user_id in self.experiment_data[experiment_id]['assignments']:
                    variant = self.experiment_data[experiment_id]['assignments'][user_id]
                    return variant

        # Check Redis cache if available
        if self.redis_cache:
            try:
                cached_variant = await self.redis_cache.get_ab_test_assignment(f"{experiment_id}:{user_id}")
                if cached_variant:
                    # Update local assignments
                    if experiment_id in self.experiment_data:
                        if 'assignments' in self.experiment_data[experiment_id]:
                            self.experiment_data[experiment_id]['assignments'][user_id] = cached_variant
                    return cached_variant
            except Exception as e:
                logger.error(f"Error getting A/B test assignment from Redis: {str(e)}")

        # Assign variant based on traffic allocation
        variant = self._assign_variant_by_allocation(user_id, experiment_id)

        # Update experiment data
        if experiment_id in self.experiment_data:
            if 'assignments' in self.experiment_data[experiment_id]:
                self.experiment_data[experiment_id]['assignments'][user_id] = variant

            if 'variants' in self.experiment_data[experiment_id]:
                if variant in self.experiment_data[experiment_id]['variants']:
                    self.experiment_data[experiment_id]['variants'][variant]['sample_size'] += 1

        # Save to Redis cache if available
        if self.redis_cache:
            try:
                await self.redis_cache.cache_ab_test_assignment(f"{experiment_id}:{user_id}", variant)
            except Exception as e:
                logger.error(f"Error caching A/B test assignment to Redis: {str(e)}")

        # Save experiment data
        self._save_experiment_data(experiment_id)

        return variant

    def _assign_variant_by_allocation(self, user_id, experiment_id):
        """
        Assign a variant based on traffic allocation

        Args:
            user_id (str): User ID
            experiment_id (str): Experiment ID

        Returns:
            str: Variant ID
        """
        # Get experiment config
        experiment_config = self.config['experiments'].get(experiment_id, {})
        variants = experiment_config.get('variants', {})

        # Calculate cumulative allocation
        cumulative_allocation = 0
        variant_allocations = []

        for variant_id, variant_config in variants.items():
            allocation = variant_config.get('traffic_allocation', 0)
            cumulative_allocation += allocation
            variant_allocations.append((variant_id, cumulative_allocation))

        # Normalize allocations if they don't sum to 100
        if cumulative_allocation != 100:
            variant_allocations = [
                (variant_id, (allocation / cumulative_allocation) * 100)
                for variant_id, allocation in variant_allocations
            ]

        # Generate a deterministic random number based on user ID
        hash_obj = hashlib.md5(user_id.encode())
        hash_int = int(hash_obj.hexdigest(), 16)
        random_value = (hash_int % 100) + 1  # 1-100

        # Assign variant based on random value
        for variant_id, allocation in variant_allocations:
            if random_value <= allocation:
                return variant_id

        # Default to first variant if something goes wrong
        return list(variants.keys())[0] if variants else 'A'

    def get_variant_config(self, variant_id, experiment_id=None):
        """
        Get configuration for a variant

        Args:
            variant_id (str): Variant ID
            experiment_id (str): Experiment ID (if None, uses default)

        Returns:
            dict: Variant configuration
        """
        # Use default experiment if not specified
        if experiment_id is None:
            experiment_id = self.config['default_experiment']

        # Check if experiment exists
        if experiment_id not in self.config['experiments']:
            logger.error(f"Experiment {experiment_id} not found")
            return {}

        # Get variant config
        experiment_config = self.config['experiments'].get(experiment_id, {})
        variants = experiment_config.get('variants', {})

        return variants.get(variant_id, {})

    async def record_event(self, user_id, metric, value=1, experiment_id=None):
        """
        Record an event for a user

        Args:
            user_id (str): User ID
            metric (str): Metric name
            value (float): Metric value
            experiment_id (str): Experiment ID (if None, uses default)

        Returns:
            bool: Success status
        """
        if not self.config['enabled']:
            return False

        # Use default experiment if not specified
        if experiment_id is None:
            experiment_id = self.config['default_experiment']

        # Check if experiment exists
        if experiment_id not in self.experiment_data:
            logger.error(f"Experiment {experiment_id} not found")
            return False

        # Check if user is assigned to a variant
        if 'assignments' not in self.experiment_data[experiment_id]:
            logger.error(f"No assignments found for experiment {experiment_id}")
            return False

        if user_id not in self.experiment_data[experiment_id]['assignments']:
            # Assign user to a variant
            variant = await self.assign_variant(user_id, experiment_id)
        else:
            variant = self.experiment_data[experiment_id]['assignments'][user_id]

        # Check if metric exists
        experiment_config = self.config['experiments'].get(experiment_id, {})
        metrics = experiment_config.get('metrics', [])

        if metric not in metrics:
            logger.warning(f"Metric {metric} not defined for experiment {experiment_id}")
            # Add metric if it doesn't exist
            for variant_id in self.experiment_data[experiment_id]['results']:
                if metric not in self.experiment_data[experiment_id]['results'][variant_id]:
                    self.experiment_data[experiment_id]['results'][variant_id][metric] = 0

        # Record event
        if variant in self.experiment_data[experiment_id]['results']:
            if metric in self.experiment_data[experiment_id]['results'][variant]:
                # Update with exponential moving average
                alpha = 0.1  # Weight for new data
                old_value = self.experiment_data[experiment_id]['results'][variant][metric]
                new_value = (1 - alpha) * old_value + alpha * value
                self.experiment_data[experiment_id]['results'][variant][metric] = new_value
            else:
                self.experiment_data[experiment_id]['results'][variant][metric] = value

        # Save experiment data
        self._save_experiment_data(experiment_id)

        return True

    def get_experiment_results(self, experiment_id=None):
        """
        Get results for an experiment

        Args:
            experiment_id (str): Experiment ID (if None, uses default)

        Returns:
            dict: Experiment results
        """
        # Use default experiment if not specified
        if experiment_id is None:
            experiment_id = self.config['default_experiment']

        # Check if experiment exists
        if experiment_id not in self.experiment_data:
            logger.error(f"Experiment {experiment_id} not found")
            return {}

        # Get experiment data
        experiment_data = self.experiment_data[experiment_id]

        # Calculate statistical significance
        results = self._calculate_significance(experiment_data)

        return results

    def _calculate_significance(self, experiment_data):
        """
        Calculate statistical significance for experiment results

        Args:
            experiment_data (dict): Experiment data

        Returns:
            dict: Results with significance
        """
        results = {
            'variants': experiment_data['variants'],
            'metrics': {},
            'start_time': experiment_data.get('start_time', ''),
            'duration': self._calculate_duration(experiment_data.get('start_time', ''))
        }

        # Get metrics
        metrics = set()
        for variant_id, variant_metrics in experiment_data['results'].items():
            metrics.update(variant_metrics.keys())

        # Calculate metrics
        for metric in metrics:
            metric_results = {
                'values': {},
                'winner': None,
                'significant': False,
                'improvement': 0
            }

            # Get values for each variant
            for variant_id, variant_metrics in experiment_data['results'].items():
                if metric in variant_metrics:
                    metric_results['values'][variant_id] = variant_metrics[metric]

            # Determine winner
            if len(metric_results['values']) >= 2:
                variant_ids = list(metric_results['values'].keys())
                values = list(metric_results['values'].values())

                # Simple winner determination (highest value)
                max_value = max(values)
                max_index = values.index(max_value)
                winner = variant_ids[max_index]

                # Calculate improvement over baseline (A)
                if 'A' in metric_results['values'] and metric_results['values']['A'] > 0:
                    baseline = metric_results['values']['A']
                    improvement = (max_value - baseline) / baseline * 100
                    metric_results['improvement'] = improvement

                # Determine if significant (simplified)
                # In a real implementation, you would use a statistical test
                significant = abs(metric_results['improvement']) > 10

                metric_results['winner'] = winner
                metric_results['significant'] = significant

            results['metrics'][metric] = metric_results

        return results

    def _calculate_duration(self, start_time_str):
        """
        Calculate experiment duration

        Args:
            start_time_str (str): Start time as ISO format string

        Returns:
            str: Duration as string
        """
        try:
            start_time = datetime.fromisoformat(start_time_str)
            now = datetime.now()
            duration = now - start_time

            days = duration.days
            hours, remainder = divmod(duration.seconds, 3600)
            minutes, seconds = divmod(remainder, 60)

            if days > 0:
                return f"{days} days, {hours} hours"
            elif hours > 0:
                return f"{hours} hours, {minutes} minutes"
            else:
                return f"{minutes} minutes"
        except:
            return "Unknown"
