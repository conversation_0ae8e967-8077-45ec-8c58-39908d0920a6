<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contemporary Chic Biodata</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@400;500;600;700&display=swap');
        
        :root {
            --primary-color: #ff3366; /* Vibrant pink */
            --secondary-color: #6c5ce7; /* Purple */
            --accent-color: #00cec9; /* Teal */
            --text-color: #2d3436;
            --light-text: #636e72;
            --border-color: #dfe6e9;
            --light-bg: #f8f9fa;
            --header-font: 'Montserrat', sans-serif;
            --body-font: 'Poppins', sans-serif;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--body-font);
            color: var(--text-color);
            line-height: 1.6;
            background-color: white;
            padding: 0;
            margin: 0;
            font-size: 14px;
        }
        
        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 30px;
            background-color: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
            position: relative;
        }
        
        /* Accent Bars */
        .accent-bar-top {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 10px;
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
        }
        
        .accent-bar-bottom {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 10px;
            background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
        }
        
        .content-wrapper {
            position: relative;
            z-index: 2;
            padding: 20px 0;
        }
        
        /* Invocation */
        .invocation {
            text-align: center;
            font-family: var(--header-font);
            color: var(--secondary-color);
            padding: 10px 0;
            font-weight: 500;
            font-size: 16px;
            margin-bottom: 30px;
            letter-spacing: 1px;
        }
        
        /* Header Section */
        .header {
            display: flex;
            align-items: stretch;
            margin-bottom: 40px;
            background-color: var(--light-bg);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .profile-photo-container {
            width: 40%;
            position: relative;
            overflow: hidden;
        }
        
        .profile-photo {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }
        
        .header-content {
            width: 60%;
            padding: 30px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .name {
            font-family: var(--header-font);
            font-size: 32px;
            color: var(--primary-color);
            margin-bottom: 10px;
            font-weight: 700;
            letter-spacing: -0.5px;
            line-height: 1.2;
        }
        
        .tagline {
            font-size: 16px;
            color: var(--light-text);
            margin-bottom: 20px;
            font-weight: 300;
        }
        
        .quick-info {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        
        .info-item {
            display: inline-flex;
            align-items: center;
            font-size: 13px;
            background-color: white;
            padding: 5px 12px;
            border-radius: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .info-label {
            font-weight: 600;
            margin-right: 5px;
            color: var(--secondary-color);
        }
        
        /* Section Styling */
        .section {
            margin-bottom: 35px;
        }
        
        .section-title {
            font-family: var(--header-font);
            color: var(--primary-color);
            font-size: 20px;
            margin-bottom: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        
        .section-title:before {
            content: '';
            width: 30px;
            height: 3px;
            background-color: var(--primary-color);
            margin-right: 10px;
            border-radius: 3px;
        }
        
        .section-content {
            padding: 0 5px;
        }
        
        /* Magazine Layout */
        .magazine-layout {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 25px;
        }
        
        /* Info Cards */
        .info-card {
            background-color: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            height: 100%;
        }
        
        .info-card-title {
            font-family: var(--header-font);
            font-size: 16px;
            color: var(--secondary-color);
            margin-bottom: 15px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        
        .info-card-title:before {
            content: '';
            width: 15px;
            height: 2px;
            background-color: var(--secondary-color);
            margin-right: 8px;
        }
        
        /* Details List */
        .details-list {
            list-style: none;
        }
        
        .details-list li {
            padding: 8px 0;
            display: flex;
            border-bottom: 1px solid var(--border-color);
        }
        
        .details-list li:last-child {
            border-bottom: none;
        }
        
        .details-label {
            width: 40%;
            font-weight: 500;
            color: var(--text-color);
        }
        
        .details-value {
            width: 60%;
            color: var(--light-text);
        }
        
        /* Career Section */
        .career-item {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .career-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        
        .career-title {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 5px;
        }
        
        .career-subtitle {
            font-size: 13px;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .career-content {
            font-size: 13px;
            color: var(--light-text);
        }
        
        /* About & Expectations */
        .about-section {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 25px;
            margin-bottom: 35px;
        }
        
        .about-card {
            background-color: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            height: 100%;
        }
        
        .about-me {
            border-top: 3px solid var(--primary-color);
        }
        
        .expectations {
            border-top: 3px solid var(--secondary-color);
        }
        
        .about-title {
            font-family: var(--header-font);
            font-size: 16px;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .about-me .about-title {
            color: var(--primary-color);
        }
        
        .expectations .about-title {
            color: var(--secondary-color);
        }
        
        /* Contact Card */
        .contact-card {
            background-color: var(--light-bg);
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        /* Footer */
        .footer {
            margin-top: 40px;
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
            font-size: 13px;
            color: var(--light-text);
        }
        
        .branding {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 10px;
        }
        
        .brand-logo {
            height: 30px;
            margin-right: 10px;
        }
        
        .brand-name {
            font-weight: 500;
            color: var(--secondary-color);
        }
        
        /* Print Styles */
        @media print {
            body {
                background-color: white;
            }
            
            .container {
                box-shadow: none;
                padding: 0;
                max-width: 100%;
            }
            
            @page {
                size: A4;
                margin: 1cm;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="accent-bar-top"></div>
        <div class="accent-bar-bottom"></div>
        
        <div class="content-wrapper">
            <!-- Invocation -->
            <div class="invocation">
                ॥ श्री गणेशाय नमः ॥
            </div>
            
            <!-- Header Section -->
            <div class="header">
                <div class="profile-photo-container">
                    <img src="{{profilePicture}}" alt="Profile Photo" class="profile-photo">
                </div>
                <div class="header-content">
                    <h1 class="name">{{name}}</h1>
                    <p class="tagline">{{tagline}}</p>
                    <div class="quick-info">
                        <div class="info-item">
                            <span class="info-label">Age:</span> {{age}} years
                        </div>
                        <div class="info-item">
                            <span class="info-label">Height:</span> {{height}}
                        </div>
                        <div class="info-item">
                            <span class="info-label">Education:</span> {{education}}
                        </div>
                        <div class="info-item">
                            <span class="info-label">Profession:</span> {{occupation}}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Personal & Family Details -->
            <div class="section">
                <h2 class="section-title">Personal Information</h2>
                <div class="section-content">
                    <div class="magazine-layout">
                        <!-- Personal Details -->
                        <div class="info-card">
                            <h3 class="info-card-title">Personal Details</h3>
                            <ul class="details-list">
                                <li>
                                    <div class="details-label">Date of Birth</div>
                                    <div class="details-value">{{dateOfBirth}}</div>
                                </li>
                                <li>
                                    <div class="details-label">Birth Place</div>
                                    <div class="details-value">{{birthPlace}}</div>
                                </li>
                                <li>
                                    <div class="details-label">Religion</div>
                                    <div class="details-value">{{religion}}</div>
                                </li>
                                <li>
                                    <div class="details-label">Caste</div>
                                    <div class="details-value">{{caste}}</div>
                                </li>
                                <li>
                                    <div class="details-label">Gotra</div>
                                    <div class="details-value">{{gotra}}</div>
                                </li>
                                <li>
                                    <div class="details-label">Marital Status</div>
                                    <div class="details-value">{{maritalStatus}}</div>
                                </li>
                            </ul>
                        </div>
                        
                        <!-- Family Details -->
                        <div class="info-card">
                            <h3 class="info-card-title">Family Background</h3>
                            <ul class="details-list">
                                <li>
                                    <div class="details-label">Father</div>
                                    <div class="details-value">{{fatherName}}</div>
                                </li>
                                <li>
                                    <div class="details-label">Occupation</div>
                                    <div class="details-value">{{fatherOccupation}}</div>
                                </li>
                                <li>
                                    <div class="details-label">Mother</div>
                                    <div class="details-value">{{motherName}}</div>
                                </li>
                                <li>
                                    <div class="details-label">Occupation</div>
                                    <div class="details-value">{{motherOccupation}}</div>
                                </li>
                                <li>
                                    <div class="details-label">Family Type</div>
                                    <div class="details-value">{{familyType}}</div>
                                </li>
                                <li>
                                    <div class="details-label">Siblings</div>
                                    <div class="details-value">{{siblings}}</div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Education & Career -->
            <div class="section">
                <h2 class="section-title">Education & Career</h2>
                <div class="section-content">
                    <div class="info-card">
                        <div class="career-item">
                            <div class="career-title">{{education}}</div>
                            <div class="career-subtitle">{{educationDetails}}</div>
                        </div>
                        
                        <div class="career-item">
                            <div class="career-title">{{occupation}}</div>
                            <div class="career-subtitle">{{company}}</div>
                            <div class="career-content">{{occupationDetails}}</div>
                        </div>
                        
                        <ul class="details-list" style="margin-top: 15px;">
                            <li>
                                <div class="details-label">Annual Income</div>
                                <div class="details-value">{{annualIncome}}</div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- About Me & Expectations -->
            <div class="about-section">
                <!-- About Me -->
                <div class="about-card about-me">
                    <h3 class="about-title">About Me</h3>
                    <p>{{aboutMe}}</p>
                    
                    <div style="margin-top: 15px;">
                        <h4 style="font-size: 15px; margin-bottom: 5px; color: var(--primary-color);">Hobbies & Interests</h4>
                        <p>{{hobbies}}</p>
                    </div>
                </div>
                
                <!-- Partner Expectations -->
                <div class="about-card expectations">
                    <h3 class="about-title">Partner Expectations</h3>
                    <p>{{partnerPreferences}}</p>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="section">
                <h2 class="section-title">Contact Information</h2>
                <div class="section-content">
                    <div class="contact-card">
                        <ul class="details-list">
                            <li>
                                <div class="details-label">Current Location</div>
                                <div class="details-value">{{city}}, {{state}}, {{country}}</div>
                            </li>
                            <li>
                                <div class="details-label">Email</div>
                                <div class="details-value">{{email}}</div>
                            </li>
                            <li>
                                <div class="details-label">Phone</div>
                                <div class="details-value">{{phone}}</div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Footer with Branding -->
            <div class="footer">
                <div class="branding">
                    <img src="{{brandLogo}}" alt="Brand Logo" class="brand-logo">
                    <span class="brand-name">{{brandName}}</span>
                </div>
                <p>{{brandTagline}}</p>
                <p>Created on {{createdAt}}</p>
            </div>
        </div>
    </div>
</body>
</html>
