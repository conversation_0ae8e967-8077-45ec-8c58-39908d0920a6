// API endpoint for recent users on the admin dashboard
import { handleApiError } from '@/utils/errorHandler';
import { withAuth } from '@/utils/authHandler';

async function handler(req, res) {
  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getRecentUsers(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    return handleApiError(error, res, 'Recent users API');
  }
}

// GET /api/admin/dashboard/recent-users
async function getRecentUsers(req, res) {
  try {
    // Get query parameters
    const { page = 1, limit = 10, filter = '' } = req.query;

    // Mock data for users
    const allUsers = [
      { id: 1, name: '<PERSON><PERSON>', age: 28, location: 'Mumbai', occupation: 'Software Engineer', verified: true, premium: true, photo: null, registeredOn: '2023-05-01' },
      { id: 2, name: '<PERSON><PERSON>', age: 26, location: 'Pune', occupation: 'Doctor', verified: true, premium: false, photo: null, registeredOn: '2023-05-03' },
      { id: 3, name: '<PERSON><PERSON>', age: 30, location: 'Delhi', occupation: 'Business Analyst', verified: false, premium: false, photo: null, registeredOn: '2023-05-05' },
      { id: 4, name: 'Neha Gupta', age: 25, location: 'Bangalore', occupation: 'UI/UX Designer', verified: true, premium: true, photo: null, registeredOn: '2023-05-07' },
      { id: 5, name: 'Vikram Singh', age: 32, location: 'Chennai', occupation: 'Marketing Manager', verified: false, premium: true, photo: null, registeredOn: '2023-05-10' },
      { id: 6, name: 'Ananya Desai', age: 27, location: 'Hyderabad', occupation: 'Data Scientist', verified: true, premium: false, photo: null, registeredOn: '2023-05-12' },
      { id: 7, name: 'Rajesh Khanna', age: 31, location: 'Kolkata', occupation: 'Financial Analyst', verified: true, premium: true, photo: null, registeredOn: '2023-05-15' },
      { id: 8, name: 'Meera Joshi', age: 24, location: 'Ahmedabad', occupation: 'HR Manager', verified: false, premium: false, photo: null, registeredOn: '2023-05-18' },
      { id: 9, name: 'Suresh Patel', age: 29, location: 'Surat', occupation: 'Civil Engineer', verified: true, premium: false, photo: null, registeredOn: '2023-05-20' },
      { id: 10, name: 'Kavita Sharma', age: 26, location: 'Jaipur', occupation: 'Teacher', verified: false, premium: true, photo: null, registeredOn: '2023-05-22' }
    ];

    // Filter users based on the filter parameter
    let filteredUsers = [...allUsers];
    if (filter) {
      if (filter === 'verified') {
        filteredUsers = allUsers.filter(user => user.verified);
      } else if (filter === 'premium') {
        filteredUsers = allUsers.filter(user => user.premium);
      } else if (filter === 'basic') {
        filteredUsers = allUsers.filter(user => !user.premium);
      }
    }

    // Calculate pagination
    const totalUsers = filteredUsers.length;
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

    // Return the paginated users
    return res.status(200).json({
      success: true,
      users: paginatedUsers,
      pagination: {
        totalUsers,
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalUsers / parseInt(limit)),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    return handleApiError(error, res, 'Get recent users');
  }
}

// Export the handler with authentication middleware
export default withAuth(handler, 'ADMIN');
