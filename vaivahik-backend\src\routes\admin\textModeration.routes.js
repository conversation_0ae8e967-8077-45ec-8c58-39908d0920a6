// src/routes/admin/textModeration.routes.js

const express = require('express');
const router = express.Router();
const textModerationController = require('../../controllers/admin/textModeration.controller');
const { authenticateAdmin } = require('../../middleware/auth.middleware');

// Get text moderation settings
router.get('/settings', authenticateAdmin, textModerationController.getModerationSettings);

// Update text moderation settings
router.put('/settings', authenticateAdmin, textModerationController.updateModerationSettings);

// Get banned words list
router.get('/banned-words', authenticateAdmin, textModerationController.getBannedWords);

// Update banned words list
router.put('/banned-words', authenticateAdmin, textModerationController.updateBannedWords);

// Get moderation logs
router.get('/logs', authenticateAdmin, textModerationController.getModerationLogs);

// Get flagged messages
router.get('/flagged-messages', authenticateAdmin, textModerationController.getFlaggedMessages);

// Review a flagged message
router.put('/review/:messageId', authenticateAdmin, textModerationController.reviewMessage);

// Get moderation statistics
router.get('/stats', authenticateAdmin, textModerationController.getModerationStats);

// Test text moderation
router.post('/test', authenticateAdmin, textModerationController.testModeration);

module.exports = router;
