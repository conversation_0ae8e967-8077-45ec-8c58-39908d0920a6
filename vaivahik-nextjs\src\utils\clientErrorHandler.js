/**
 * Client-Side Error Handler Utility
 * 
 * This module provides functions to handle errors on the client side
 * and show appropriate toast notifications with guided resolution.
 */

import { getErrorResolution } from './errorResolutionDatabase';

/**
 * Extract error message from an error object or response
 * @param {Error|Object} error - Error object or response
 * @returns {string} Error message
 */
export const extractErrorMessage = (error) => {
  // If error is a string, return it directly
  if (typeof error === 'string') {
    return error;
  }
  
  // If error is an Axios error
  if (error.isAxiosError) {
    // Try to get the error message from the response
    const responseData = error.response?.data;
    
    if (responseData) {
      // Check for different error message formats
      if (responseData.message) {
        return responseData.message;
      } else if (responseData.error) {
        return typeof responseData.error === 'string' 
          ? responseData.error 
          : responseData.error.message || 'Unknown error';
      } else if (responseData.errors && Array.isArray(responseData.errors)) {
        // Join multiple error messages
        return responseData.errors.map(e => e.message || e).join('. ');
      }
    }
    
    // Fallback to status text or generic message
    return error.response?.statusText || 'Network error occurred';
  }
  
  // If error is a regular Error object
  if (error instanceof Error) {
    return error.message;
  }
  
  // If error is an object with a message property
  if (error && typeof error === 'object' && error.message) {
    return error.message;
  }
  
  // Fallback
  return 'An unknown error occurred';
};

/**
 * Extract error code from an error object or response
 * @param {Error|Object} error - Error object or response
 * @returns {string|null} Error code or null if not found
 */
export const extractErrorCode = (error) => {
  // If error is an Axios error
  if (error.isAxiosError) {
    const responseData = error.response?.data;
    
    if (responseData) {
      // Check for different error code formats
      if (responseData.code) {
        return responseData.code;
      } else if (responseData.error && responseData.error.code) {
        return responseData.error.code;
      } else if (responseData.errorCode) {
        return responseData.errorCode;
      }
    }
    
    // Use HTTP status code as fallback
    if (error.response?.status) {
      return `HTTP_${error.response.status}`;
    }
  }
  
  // If error is an object with a code property
  if (error && typeof error === 'object') {
    if (error.code) {
      return error.code;
    } else if (error.errorCode) {
      return error.errorCode;
    }
  }
  
  // No error code found
  return null;
};

/**
 * Handle an error and show appropriate toast notification
 * @param {Error|Object} error - Error object or response
 * @param {Object} toast - Toast context from useToast hook
 * @param {Object} options - Additional options
 * @param {string} options.fallbackMessage - Fallback message if error message cannot be extracted
 * @param {boolean} options.showGuidedResolution - Whether to show guided resolution (default: true)
 * @param {Function} options.onHelp - Function to call when help button is clicked
 * @param {Function} options.onRetry - Function to call when retry is needed
 */
export const handleError = (error, toast, options = {}) => {
  const {
    fallbackMessage = 'An error occurred',
    showGuidedResolution = true,
    onHelp,
    onRetry
  } = options;
  
  // Extract error message and code
  const errorMessage = extractErrorMessage(error) || fallbackMessage;
  const errorCode = extractErrorCode(error);
  
  // Log the error for debugging
  console.error('Error occurred:', { error, message: errorMessage, code: errorCode });
  
  // If guided resolution is enabled, try to get resolution steps
  if (showGuidedResolution) {
    const resolution = getErrorResolution(errorCode || errorMessage);
    
    if (resolution) {
      // Add retry step if onRetry is provided
      const steps = [...resolution.steps];
      if (onRetry) {
        steps.push('Click "Try Again" to retry the operation');
      }
      
      // Show guided error toast
      toast.showGuidedError(errorMessage, {
        errorCode: resolution.code || errorCode,
        type: resolution.type || 'error',
        steps,
        onHelp: resolution.helpUrl ? () => {
          // If there's a help URL, navigate to it
          window.open(resolution.helpUrl, '_blank');
          // Also call the custom onHelp if provided
          if (onHelp) onHelp();
        } : onHelp
      });
      
      return;
    }
  }
  
  // Fallback to regular error toast if no resolution is found
  toast.showError(errorMessage);
};

/**
 * Handle API calls with loading, success, and error states
 * @param {Function} apiCall - API call function to execute
 * @param {Object} toast - Toast context from useToast hook
 * @param {Object} options - Additional options
 * @param {string} options.loadingMessage - Message to show while loading
 * @param {string} options.successMessage - Message to show on success
 * @param {string} options.errorMessage - Fallback error message
 * @param {boolean} options.showGuidedResolution - Whether to show guided resolution
 * @param {Function} options.onSuccess - Function to call on success
 * @param {Function} options.onError - Function to call on error
 * @returns {Promise} Promise that resolves with the API response or rejects with an error
 */
export const handleApiCall = async (apiCall, toast, options = {}) => {
  const {
    loadingMessage,
    successMessage,
    errorMessage = 'An error occurred',
    showGuidedResolution = true,
    onSuccess,
    onError
  } = options;
  
  try {
    // Show loading toast if provided
    if (loadingMessage) {
      toast.showInfo(loadingMessage);
    }
    
    // Execute the API call
    const response = await apiCall();
    
    // Show success toast if provided
    if (successMessage) {
      toast.showSuccess(successMessage);
    }
    
    // Call onSuccess callback if provided
    if (onSuccess) {
      onSuccess(response);
    }
    
    return response;
  } catch (error) {
    // Handle the error
    handleError(error, toast, {
      fallbackMessage: errorMessage,
      showGuidedResolution,
      onHelp: options.onHelp
    });
    
    // Call onError callback if provided
    if (onError) {
      onError(error);
    }
    
    throw error;
  }
};

export default {
  extractErrorMessage,
  extractErrorCode,
  handleError,
  handleApiCall
};
