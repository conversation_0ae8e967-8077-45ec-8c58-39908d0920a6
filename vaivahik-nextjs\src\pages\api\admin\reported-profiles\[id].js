// API endpoint for specific reported profile
import { generateMockReportedProfiles } from '@/utils/mockData';

export default function handler(req, res) {
  // Get the report ID from the URL
  const { id } = req.query;
  
  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getReportById(req, res, id);
    case 'POST':
      return updateReportStatus(req, res, id);
    default:
      return res.status(405).json({ message: 'Method not allowed' });
  }
}

// GET /api/admin/reported-profiles/[id]
function getReportById(req, res, id) {
  try {
    // In a real implementation, this would fetch data from a database
    // For now, we'll use mock data
    const mockReports = generateMockReportedProfiles();
    
    // Find the report by ID
    const report = mockReports.find(r => r.id.toString() === id);
    
    // If report not found, return 404
    if (!report) {
      return res.status(404).json({ 
        success: false, 
        message: 'Report not found' 
      });
    }
    
    // Return the report
    return res.status(200).json({
      success: true,
      report
    });
  } catch (error) {
    console.error('Error fetching report:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch report' 
    });
  }
}

// POST /api/admin/reported-profiles/[id]
function updateReportStatus(req, res, id) {
  try {
    // Get the action from the URL (resolve or dismiss)
    const { action } = req.query;
    
    // Validate the action
    if (!action || (action !== 'resolve' && action !== 'dismiss')) {
      return res.status(400).json({ 
        success: false, 
        message: 'Invalid action. Must be "resolve" or "dismiss"' 
      });
    }
    
    // Get action details from request body
    const { 
      actionType, 
      actionDetails, 
      suspensionDays, 
      restrictedFeatures 
    } = req.body;
    
    // Validate required fields for resolve action
    if (action === 'resolve') {
      if (!actionType) {
        return res.status(400).json({ 
          success: false, 
          message: 'Action type is required for resolve action' 
        });
      }
      
      if (!actionDetails) {
        return res.status(400).json({ 
          success: false, 
          message: 'Action details are required for resolve action' 
        });
      }
      
      if (actionType === 'RESTRICT_FEATURES' && (!restrictedFeatures || restrictedFeatures.length === 0)) {
        return res.status(400).json({ 
          success: false, 
          message: 'Restricted features are required for RESTRICT_FEATURES action type' 
        });
      }
    }
    
    // In a real implementation, this would update the database
    // For now, we'll just return a success response
    
    return res.status(200).json({
      success: true,
      message: `Report ${action === 'resolve' ? 'resolved' : 'dismissed'} successfully`,
      reportId: id,
      action,
      ...(action === 'resolve' ? {
        actionType,
        actionDetails,
        suspensionDays: actionType === 'TEMP_SUSPENSION' ? suspensionDays : null,
        restrictedFeatures: actionType === 'RESTRICT_FEATURES' ? restrictedFeatures : null
      } : {})
    });
  } catch (error) {
    console.error('Error updating report status:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to update report status' 
    });
  }
}
