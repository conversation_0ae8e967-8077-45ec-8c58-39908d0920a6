# Admin Layout Components

## Overview

This directory contains the admin layout components used in the Vaivahik admin panel. The admin panel uses the `EnhancedAdminLayout` component, which provides a more feature-rich and organized sidebar navigation with collapsible categories.

## Components

### EnhancedAdminLayout

The `EnhancedAdminLayout` component is the main layout component used for all admin pages. It provides:

- Collapsible sidebar categories
- Dark mode toggle
- Mobile-responsive design
- Authentication check
- Feature flag toggle
- Mock data toggle

### Usage

To use the `EnhancedAdminLayout` component in an admin page:

```jsx
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);

export default function AdminPage() {
  return (
    <EnhancedAdminLayout title="Page Title">
      {/* Page content */}
    </EnhancedAdminLayout>
  );
}
```

## CSS Files

The admin layout uses several CSS files:

- `enhanced-admin-layout.css`: Styles specific to the EnhancedAdminLayout component
- `admin.css`: Base admin styles
- `admin-ui-components.css`: Standardized UI components for the admin panel
- `admin-fixes.css`: CSS fixes for layout issues
- `admin-responsive.css`: Responsive styles for the admin panel

## Important Notes

1. **SSR Disabled**: The EnhancedAdminLayout component should be imported with SSR disabled to avoid issues with localStorage access during server-side rendering.

2. **Legacy AdminLayout**: The old AdminLayout component has been deprecated and replaced with EnhancedAdminLayout. A backup of the old component is available in `AdminLayout.backup.js` for reference.

3. **Sidebar Navigation**: The sidebar navigation is organized into categories, which can be collapsed/expanded. The active category is automatically expanded based on the current route.

4. **Feature Flags**: The admin panel includes a feature flag toggle component that allows switching between mock and real data.

## Troubleshooting

If you encounter issues with the admin layout:

1. **CSS Conflicts**: Check for CSS conflicts between the old and new layout styles. The `admin-fixes.css` file contains fixes for common layout issues.

2. **localStorage Errors**: If you see errors related to localStorage, make sure you're importing EnhancedAdminLayout with SSR disabled.

3. **Navigation Issues**: If sidebar navigation links are not working correctly, check that the route paths in the EnhancedAdminLayout component match the actual page paths.

4. **Dark Mode Issues**: If dark mode is not working correctly, check that the dark mode toggle is properly initialized and that the dark mode CSS classes are being applied.

## Future Improvements

- Further optimize the sidebar navigation for better organization
- Improve mobile responsiveness
- Add more customization options for the admin layout
- Implement better error handling for authentication issues
