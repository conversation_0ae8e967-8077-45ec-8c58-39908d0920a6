/**
 * Admin Login API Endpoint
 * 
 * This endpoint handles admin authentication.
 * In development mode, it uses mock credentials.
 * In production, it would connect to the real backend.
 */

import { adminLogin } from '@/services/authService';

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    });
  }

  try {
    // Get credentials from request body
    const { email, password } = req.body;

    // Validate required fields
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email and password are required'
      });
    }

    // Call the admin login service
    const result = await admin<PERSON>ogin(email, password);

    // Return the result
    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(401).json(result);
    }
  } catch (error) {
    console.error('Admin login error:', error);
    return res.status(500).json({
      success: false,
      message: 'Authentication error',
      error: error.message
    });
  }
}
