import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Button,
  Chip,
  Avatar,
  IconButton,
  Badge,
  Divider,
  Tooltip,
  CircularProgress,
  useMediaQuery,
  Fade,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Skeleton
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import FavoriteIcon from '@mui/icons-material/Favorite';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import CloseIcon from '@mui/icons-material/Close';
import CheckIcon from '@mui/icons-material/Check';
import MessageIcon from '@mui/icons-material/Message';
import CallIcon from '@mui/icons-material/Call';
import EmailIcon from '@mui/icons-material/Email';
import WhatsAppIcon from '@mui/icons-material/WhatsApp';
import VisibilityIcon from '@mui/icons-material/Visibility';
import PersonIcon from '@mui/icons-material/Person';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import WorkIcon from '@mui/icons-material/Work';
import SchoolIcon from '@mui/icons-material/School';
import VerifiedIcon from '@mui/icons-material/Verified';
import StarIcon from '@mui/icons-material/Star';
import { getUserActivities, formatActivityTime } from '@/services/activityService';
import UserOnlineStatus from '@/components/common/UserOnlineStatus';
import { useRouter } from 'next/router';

// Mock function to accept interest - replace with actual API call
const acceptInterest = async (interestId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ success: true });
    }, 1000);
  });
};

// Mock function to decline interest - replace with actual API call
const declineInterest = async (interestId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ success: true });
    }, 1000);
  });
};

/**
 * Interest Management Component
 * 
 * Advanced UI for managing interests (sent, received, accepted)
 */
const InterestManagement = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const router = useRouter();
  
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [interests, setInterests] = useState({
    received: [],
    sent: [],
    accepted: []
  });
  const [actionLoading, setActionLoading] = useState(null);
  const [confirmDialog, setConfirmDialog] = useState({
    open: false,
    type: null,
    interest: null
  });
  
  // Load interests on component mount
  useEffect(() => {
    const loadInterests = async () => {
      try {
        setLoading(true);
        
        // Load received interests
        const receivedData = await getUserActivities({
          type: 'INTEREST_RECEIVED',
          limit: 50
        });
        
        // Load sent interests
        const sentData = await getUserActivities({
          type: 'INTEREST_SENT',
          limit: 50
        });
        
        // Load accepted interests
        const acceptedData = await getUserActivities({
          type: ['INTEREST_ACCEPTED'],
          limit: 50
        });
        
        setInterests({
          received: receivedData.activities || [],
          sent: sentData.activities || [],
          accepted: acceptedData.activities || []
        });
      } catch (error) {
        console.error('Error loading interests:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadInterests();
  }, []);
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  // Handle interest acceptance
  const handleAcceptInterest = async (interest) => {
    setConfirmDialog({
      open: true,
      type: 'accept',
      interest
    });
  };
  
  // Handle interest decline
  const handleDeclineInterest = async (interest) => {
    setConfirmDialog({
      open: true,
      type: 'decline',
      interest
    });
  };
  
  // Confirm dialog action
  const handleConfirmAction = async () => {
    const { type, interest } = confirmDialog;
    
    try {
      setActionLoading(interest._id);
      
      if (type === 'accept') {
        await acceptInterest(interest._id);
        
        // Update local state
        const updatedInterest = { ...interest, data: { ...interest.data, status: 'ACCEPTED' } };
        
        setInterests(prev => ({
          received: prev.received.filter(item => item._id !== interest._id),
          sent: prev.sent,
          accepted: [...prev.accepted, updatedInterest]
        }));
      } else if (type === 'decline') {
        await declineInterest(interest._id);
        
        // Update local state
        setInterests(prev => ({
          received: prev.received.filter(item => item._id !== interest._id),
          sent: prev.sent,
          accepted: prev.accepted
        }));
      }
    } catch (error) {
      console.error(`Error ${type === 'accept' ? 'accepting' : 'declining'} interest:`, error);
    } finally {
      setActionLoading(null);
      setConfirmDialog({ open: false, type: null, interest: null });
    }
  };
  
  // Close confirm dialog
  const handleCloseConfirm = () => {
    setConfirmDialog({ open: false, type: null, interest: null });
  };
  
  // View profile
  const handleViewProfile = (userId) => {
    router.push(`/profile/${userId}`);
  };
  
  // Render interest card
  const renderInterestCard = (interest, type) => {
    const user = interest.targetUser || {};
    const status = interest.data?.status || 'PENDING';
    
    return (
      <Card 
        elevation={3} 
        sx={{ 
          display: 'flex', 
          flexDirection: 'column',
          height: '100%',
          borderRadius: 2,
          transition: 'transform 0.3s ease, box-shadow 0.3s ease',
          '&:hover': {
            transform: 'translateY(-5px)',
            boxShadow: 6
          }
        }}
      >
        <Box sx={{ position: 'relative' }}>
          <CardMedia
            component="img"
            height={200}
            image={user.profilePhoto || '/images/default-profile.jpg'}
            alt={user.name}
            sx={{ objectFit: 'cover' }}
          />
          
          {/* Premium badge */}
          {user.isPremium && (
            <Chip
              icon={<StarIcon fontSize="small" />}
              label="Premium"
              color="primary"
              size="small"
              sx={{
                position: 'absolute',
                top: 10,
                right: 10,
                backgroundColor: 'rgba(103, 58, 183, 0.85)',
                backdropFilter: 'blur(4px)'
              }}
            />
          )}
          
          {/* Verified badge */}
          {user.isVerified && (
            <Tooltip title="Verified Profile">
              <Chip
                icon={<VerifiedIcon fontSize="small" />}
                label="Verified"
                color="success"
                size="small"
                sx={{
                  position: 'absolute',
                  top: user.isPremium ? 50 : 10,
                  right: 10,
                  backgroundColor: 'rgba(46, 125, 50, 0.85)',
                  backdropFilter: 'blur(4px)'
                }}
              />
            </Tooltip>
          )}
          
          {/* Status badge for sent interests */}
          {type === 'sent' && (
            <Chip
              label={status === 'PENDING' ? 'Pending' : status === 'ACCEPTED' ? 'Accepted' : 'Declined'}
              color={status === 'PENDING' ? 'warning' : status === 'ACCEPTED' ? 'success' : 'error'}
              size="small"
              sx={{
                position: 'absolute',
                bottom: 10,
                right: 10,
                backgroundColor: status === 'PENDING' 
                  ? 'rgba(237, 108, 2, 0.85)' 
                  : status === 'ACCEPTED' 
                    ? 'rgba(46, 125, 50, 0.85)' 
                    : 'rgba(211, 47, 47, 0.85)',
                backdropFilter: 'blur(4px)'
              }}
            />
          )}
        </Box>
        
        <CardContent sx={{ flexGrow: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
            <Typography variant="h6" component="div" noWrap>
              {user.name}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {formatActivityTime(interest.createdAt)}
            </Typography>
          </Box>
          
          <Box sx={{ mb: 1 }}>
            <UserOnlineStatus userId={user._id} size="small" />
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
            <PersonIcon fontSize="small" color="action" sx={{ mr: 1 }} />
            <Typography variant="body2" color="text.secondary">
              {user.age} yrs, {user.height || '5\'8"'}
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
            <WorkIcon fontSize="small" color="action" sx={{ mr: 1 }} />
            <Typography variant="body2" color="text.secondary" noWrap>
              {user.occupation || 'Software Engineer'}
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
            <LocationOnIcon fontSize="small" color="action" sx={{ mr: 1 }} />
            <Typography variant="body2" color="text.secondary" noWrap>
              {user.city}{user.state ? `, ${user.state}` : ''}
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SchoolIcon fontSize="small" color="action" sx={{ mr: 1 }} />
            <Typography variant="body2" color="text.secondary" noWrap>
              {user.education || 'Bachelor\'s Degree'}
            </Typography>
          </Box>
        </CardContent>
        
        <Divider />
        
        <CardActions sx={{ p: 2, justifyContent: 'space-between' }}>
          <Button
            variant="outlined"
            startIcon={<VisibilityIcon />}
            size="small"
            onClick={() => handleViewProfile(user._id)}
          >
            View Profile
          </Button>
          
          {type === 'received' && status === 'PENDING' && (
            <Box>
              <Tooltip title="Accept Interest">
                <IconButton 
                  color="success" 
                  onClick={() => handleAcceptInterest(interest)}
                  disabled={actionLoading === interest._id}
                >
                  {actionLoading === interest._id ? 
                    <CircularProgress size={24} /> : 
                    <CheckIcon />
                  }
                </IconButton>
              </Tooltip>
              
              <Tooltip title="Decline Interest">
                <IconButton 
                  color="error" 
                  onClick={() => handleDeclineInterest(interest)}
                  disabled={actionLoading === interest._id}
                >
                  <CloseIcon />
                </IconButton>
              </Tooltip>
            </Box>
          )}
          
          {type === 'accepted' && (
            <Box>
              <Tooltip title="Send Message">
                <IconButton color="primary">
                  <MessageIcon />
                </IconButton>
              </Tooltip>
              
              <Tooltip title="Contact Details">
                <IconButton color="secondary">
                  <CallIcon />
                </IconButton>
              </Tooltip>
            </Box>
          )}
          
          {type === 'sent' && status === 'PENDING' && (
            <Chip 
              icon={<FavoriteBorderIcon />} 
              label="Interest Sent" 
              variant="outlined" 
              color="primary" 
              size="small" 
            />
          )}
          
          {type === 'sent' && status === 'ACCEPTED' && (
            <Chip 
              icon={<FavoriteIcon />} 
              label="Interest Accepted" 
              color="success" 
              size="small" 
            />
          )}
        </CardActions>
      </Card>
    );
  };
  
  // Render loading skeleton
  const renderSkeleton = () => {
    return Array(6).fill(0).map((_, index) => (
      <Grid item xs={12} sm={6} md={4} key={index}>
        <Card sx={{ height: '100%', borderRadius: 2 }}>
          <Skeleton variant="rectangular" height={200} />
          <CardContent>
            <Skeleton variant="text" width="70%" height={32} />
            <Skeleton variant="text" width="40%" height={24} />
            <Skeleton variant="text" width="90%" height={24} />
            <Skeleton variant="text" width="60%" height={24} />
            <Skeleton variant="text" width="80%" height={24} />
          </CardContent>
          <CardActions sx={{ p: 2 }}>
            <Skeleton variant="rectangular" width={120} height={36} />
            <Box sx={{ flexGrow: 1 }} />
            <Skeleton variant="circular" width={40} height={40} />
            <Skeleton variant="circular" width={40} height={40} />
          </CardActions>
        </Card>
      </Grid>
    ));
  };
  
  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs 
          value={activeTab} 
          onChange={handleTabChange} 
          variant={isMobile ? "fullWidth" : "standard"}
          scrollButtons="auto"
          allowScrollButtonsMobile
        >
          <Tab 
            label={
              <Badge 
                badgeContent={interests.received.filter(i => i.data?.status === 'PENDING').length} 
                color="error"
                max={99}
                showZero={false}
              >
                <Box sx={{ px: 1 }}>Received</Box>
              </Badge>
            } 
          />
          <Tab 
            label={
              <Badge 
                badgeContent={interests.sent.length} 
                color="primary"
                max={99}
                showZero={false}
              >
                <Box sx={{ px: 1 }}>Sent</Box>
              </Badge>
            } 
          />
          <Tab 
            label={
              <Badge 
                badgeContent={interests.accepted.length} 
                color="success"
                max={99}
                showZero={false}
              >
                <Box sx={{ px: 1 }}>Accepted</Box>
              </Badge>
            } 
          />
        </Tabs>
      </Box>
      
      {/* Received Interests */}
      {activeTab === 0 && (
        <Fade in={activeTab === 0}>
          <Box>
            {loading ? (
              <Grid container spacing={3}>
                {renderSkeleton()}
              </Grid>
            ) : interests.received.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <FavoriteBorderIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No Interests Received
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  When someone expresses interest in your profile, you'll see it here.
                </Typography>
              </Box>
            ) : (
              <Grid container spacing={3}>
                {interests.received.map(interest => (
                  <Grid item xs={12} sm={6} md={4} key={interest._id}>
                    {renderInterestCard(interest, 'received')}
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        </Fade>
      )}
      
      {/* Sent Interests */}
      {activeTab === 1 && (
        <Fade in={activeTab === 1}>
          <Box>
            {loading ? (
              <Grid container spacing={3}>
                {renderSkeleton()}
              </Grid>
            ) : interests.sent.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <FavoriteBorderIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No Interests Sent
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  When you express interest in someone's profile, you'll see it here.
                </Typography>
              </Box>
            ) : (
              <Grid container spacing={3}>
                {interests.sent.map(interest => (
                  <Grid item xs={12} sm={6} md={4} key={interest._id}>
                    {renderInterestCard(interest, 'sent')}
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        </Fade>
      )}
      
      {/* Accepted Interests */}
      {activeTab === 2 && (
        <Fade in={activeTab === 2}>
          <Box>
            {loading ? (
              <Grid container spacing={3}>
                {renderSkeleton()}
              </Grid>
            ) : interests.accepted.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <FavoriteIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No Accepted Interests
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  When you and someone else have mutual interest, you'll see it here.
                </Typography>
              </Box>
            ) : (
              <Grid container spacing={3}>
                {interests.accepted.map(interest => (
                  <Grid item xs={12} sm={6} md={4} key={interest._id}>
                    {renderInterestCard(interest, 'accepted')}
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        </Fade>
      )}
      
      {/* Confirmation Dialog */}
      <Dialog
        open={confirmDialog.open}
        onClose={handleCloseConfirm}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>
          {confirmDialog.type === 'accept' ? 'Accept Interest?' : 'Decline Interest?'}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1">
            {confirmDialog.type === 'accept' 
              ? 'By accepting this interest, you will be able to communicate with this person. Do you want to proceed?'
              : 'Are you sure you want to decline this interest? This action cannot be undone.'}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseConfirm}>Cancel</Button>
          <Button 
            onClick={handleConfirmAction}
            color={confirmDialog.type === 'accept' ? 'success' : 'error'}
            variant="contained"
            disabled={actionLoading !== null}
          >
            {actionLoading !== null ? (
              <CircularProgress size={24} />
            ) : (
              confirmDialog.type === 'accept' ? 'Accept' : 'Decline'
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default InterestManagement;
