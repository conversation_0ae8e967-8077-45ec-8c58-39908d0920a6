/**
 * Error Handling Middleware
 * 
 * This middleware provides centralized error handling for the application.
 * It catches errors from all routes and middleware and returns standardized responses.
 */

const { PrismaClientKnownRequestError, PrismaClientValidationError } = require('@prisma/client/runtime');
const apiResponse = require('../utils/apiResponse');
const logger = require('../utils/logger');

/**
 * Prisma error codes and their descriptions
 */
const PRISMA_ERROR_CODES = {
  P2000: 'The provided value for the column is too long',
  P2001: 'The record searched for does not exist',
  P2002: 'Unique constraint failed',
  P2003: 'Foreign key constraint failed',
  P2004: 'A constraint failed',
  P2005: 'The value stored in the database is invalid for the field type',
  P2006: 'The provided value is not valid',
  P2007: 'Data validation error',
  P2008: 'Failed to parse the query',
  P2009: 'Failed to validate the query',
  P2010: 'Raw query failed',
  P2011: 'Null constraint violation',
  P2012: 'Missing required value',
  P2013: 'Missing required argument',
  P2014: 'The change you are trying to make would violate the required relation',
  P2015: 'A related record could not be found',
  P2016: 'Query interpretation error',
  P2017: 'The records for the relation are not connected',
  P2018: 'The required connected records were not found',
  P2019: 'Input error',
  P2020: 'Value out of range for the type',
  P2021: 'The table does not exist in the current database',
  P2022: 'The column does not exist in the current database',
  P2023: 'Inconsistent column data',
  P2024: 'Connection pool timeout',
  P2025: 'Record not found',
  P2026: 'The current database provider doesn\'t support a feature that the query used',
  P2027: 'Multiple errors occurred during query validation',
  P2028: 'Transaction API error',
  P2030: 'Cannot find a fulltext index to use for the search',
  P2031: 'Prisma needs to perform transactions, which requires your MongoDB server to be run as a replica set',
  P2033: 'A number used in the query does not fit into a 64 bit signed integer',
  P2034: 'Transaction failed due to a write conflict or a deadlock'
};

/**
 * Handle Prisma database errors
 * @param {Error} err - The error object
 * @param {object} res - Express response object
 * @returns {boolean} - Whether the error was handled
 */
const handlePrismaError = (err, res) => {
  // Handle Prisma known request errors (with error codes)
  if (err instanceof PrismaClientKnownRequestError) {
    const errorCode = err.code;
    const errorMessage = PRISMA_ERROR_CODES[errorCode] || 'Database operation failed';
    
    // Log the error
    logger.error(`Prisma error: ${errorCode} - ${errorMessage}`, {
      code: errorCode,
      meta: err.meta,
      message: err.message
    });
    
    // Handle specific error codes
    switch (errorCode) {
      case 'P2002': // Unique constraint failed
        return apiResponse.conflict(res, 'A record with this unique constraint already exists', {
          fields: err.meta?.target || [],
          code: errorCode
        });
        
      case 'P2003': // Foreign key constraint failed
        return apiResponse.badRequest(res, 'Foreign key constraint failed', {
          field: err.meta?.field_name || '',
          code: errorCode
        });
        
      case 'P2025': // Record not found
        return apiResponse.notFound(res, err.meta?.cause || 'Record not found', {
          code: errorCode
        });
        
      case 'P2014': // Relation violation
        return apiResponse.badRequest(res, 'The change would violate a required relation', {
          details: err.meta?.cause || '',
          code: errorCode
        });
        
      case 'P2024': // Connection pool timeout
        return apiResponse.serviceUnavailable(res, 'Database connection timeout', {
          code: errorCode
        });
        
      default:
        // Handle other Prisma errors with known codes
        return apiResponse.badRequest(res, errorMessage, {
          code: errorCode,
          details: process.env.NODE_ENV === 'development' ? err.message : undefined
        });
    }
  }
  
  // Handle Prisma validation errors
  if (err instanceof PrismaClientValidationError) {
    logger.error(`Prisma validation error: ${err.message}`);
    return apiResponse.badRequest(res, 'Database validation error', {
      details: process.env.NODE_ENV === 'development' ? err.message : 'Invalid data provided'
    });
  }
  
  // Not a Prisma error
  return false;
};

/**
 * Handle JWT errors
 * @param {Error} err - The error object
 * @param {object} res - Express response object
 * @returns {boolean} - Whether the error was handled
 */
const handleJwtError = (err, res) => {
  if (err.name === 'TokenExpiredError') {
    logger.warn('JWT token expired');
    return apiResponse.unauthorized(res, 'Token expired. Please log in again.');
  }
  
  if (err.name === 'JsonWebTokenError') {
    logger.warn(`JWT error: ${err.message}`);
    return apiResponse.forbidden(res, 'Invalid token.');
  }
  
  if (err.name === 'NotBeforeError') {
    logger.warn('JWT not before error');
    return apiResponse.unauthorized(res, 'Token not yet valid. Please try again later.');
  }
  
  // Not a JWT error
  return false;
};

/**
 * Handle validation errors from express-validator
 * @param {Error} err - The error object
 * @param {object} res - Express response object
 * @returns {boolean} - Whether the error was handled
 */
const handleValidationError = (err, res) => {
  if (err.name === 'ValidationError' || (err.errors && Array.isArray(err.errors))) {
    logger.warn('Validation error', { errors: err.errors });
    return apiResponse.validationError(res, 'Validation failed', err.errors);
  }
  
  // Not a validation error
  return false;
};

/**
 * Handle file upload errors
 * @param {Error} err - The error object
 * @param {object} res - Express response object
 * @returns {boolean} - Whether the error was handled
 */
const handleFileUploadError = (err, res) => {
  if (err.code === 'LIMIT_FILE_SIZE') {
    logger.warn('File size limit exceeded');
    return apiResponse.badRequest(res, 'File size exceeds the limit', {
      code: 'LIMIT_FILE_SIZE'
    });
  }
  
  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    logger.warn('Unexpected file in upload');
    return apiResponse.badRequest(res, 'Unexpected file or incorrect field name', {
      code: 'LIMIT_UNEXPECTED_FILE'
    });
  }
  
  if (err.code === 'INVALID_FILE_TYPE') {
    logger.warn('Invalid file type');
    return apiResponse.badRequest(res, err.message || 'Invalid file type', {
      code: 'INVALID_FILE_TYPE'
    });
  }
  
  // Not a file upload error
  return false;
};

/**
 * Main error handler middleware
 * @param {Error} err - The error object
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 * @returns {object} - Express response
 */
const errorHandler = (err, req, res, next) => {
  // Log the error
  logger.error('Error caught by global error handler', {
    error: err.message,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined,
    path: req.path,
    method: req.method
  });
  
  // Handle specific error types
  if (handlePrismaError(err, res)) return;
  if (handleJwtError(err, res)) return;
  if (handleValidationError(err, res)) return;
  if (handleFileUploadError(err, res)) return;
  
  // Handle custom status errors
  if (err.status && err.status >= 400 && err.status < 600) {
    return apiResponse.error(res, err.message, err.status, err.errors);
  }
  
  // Handle all other errors as internal server errors
  return apiResponse.serverError(res, 'Internal server error', 
    process.env.NODE_ENV === 'development' ? {
      message: err.message,
      stack: err.stack
    } : undefined
  );
};

/**
 * 404 Not Found handler
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @returns {object} - Express response
 */
const notFoundHandler = (req, res) => {
  logger.warn(`Resource not found: ${req.method} ${req.originalUrl}`);
  return apiResponse.notFound(res, `Resource not found: ${req.method} ${req.originalUrl}`);
};

module.exports = {
  errorHandler,
  notFoundHandler
};
