import React from 'react';
import Head from 'next/head';
import Link from 'next/link';
import {
  Box,
  AppBar,
  Toolbar,
  Typography,
  Button,
  Container,
  styled
} from '@mui/material';

// Styled components for the layout
const StyledAppBar = styled(AppBar)(({ theme }) => ({
  background: 'linear-gradient(135deg, #FF69B4 0%, #FF1493 100%)',
  boxShadow: '0 4px 20px rgba(255, 105, 180, 0.3)',
}));

const LogoTypography = styled(Typography)(({ theme }) => ({
  flexGrow: 1,
  fontWeight: 700,
  fontSize: '1.5rem',
  color: 'white',
  textDecoration: 'none',
  '&:hover': {
    color: 'rgba(255, 255, 255, 0.9)',
  }
}));

const NavButton = styled(Button)(({ theme }) => ({
  color: 'white',
  fontWeight: 600,
  marginLeft: theme.spacing(2),
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  }
}));

const MainContent = styled(Box)(({ theme }) => ({
  minHeight: 'calc(100vh - 64px - 60px)', // Subtract header and footer height
  paddingTop: theme.spacing(2),
  paddingBottom: theme.spacing(4),
}));

const Footer = styled(Box)(({ theme }) => ({
  backgroundColor: '#f8f9fa',
  padding: theme.spacing(2),
  marginTop: 'auto',
  textAlign: 'center',
  borderTop: '1px solid #e9ecef',
}));

const MainLayout = ({ children, title = 'Vaivahik' }) => {
  return (
    <>
      <Head>
        <title>{title} - Vaivahik</title>
        <meta name="description" content="Vaivahik - Matrimony for Maratha Community" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
        <StyledAppBar position="static">
          <Toolbar>
            <Link href="/" passHref style={{ textDecoration: 'none' }}>
              <LogoTypography variant="h6" component="div">
                Vaivahik
              </LogoTypography>
            </Link>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Link href="/matches" passHref>
                <NavButton>
                  Browse Matches
                </NavButton>
              </Link>
              <Link href="/messages" passHref>
                <NavButton>
                  Messages
                </NavButton>
              </Link>
              <Link href="/website/dashboard" passHref>
                <NavButton>
                  Dashboard
                </NavButton>
              </Link>
            </Box>
          </Toolbar>
        </StyledAppBar>

        <MainContent component="main">
          {children}
        </MainContent>

        <Footer component="footer">
          <Container maxWidth="lg">
            <Typography variant="body2" color="text.secondary">
              &copy; {new Date().getFullYear()} Vaivahik. All rights reserved.
            </Typography>
          </Container>
        </Footer>
      </Box>
    </>
  );
};

export default MainLayout;
