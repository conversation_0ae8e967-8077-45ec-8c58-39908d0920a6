// DOM Elements
const searchInput = document.getElementById('searchInput');
const searchBtn = document.getElementById('searchBtn');
const statusFilter = document.getElementById('statusFilter');
const tableInfo = document.getElementById('tableInfo');
const paginationControls = document.getElementById('paginationControls');
const userViewModal = document.getElementById('userViewModal');
const reportDetailsModalOverlay = document.getElementById('reportDetailsModalOverlay');
const confirmationModal = document.getElementById('confirmationModal');
const userViewContent = document.getElementById('userViewContent');
const reportDetailsModalContent = document.getElementById('reportDetailsModalContent');
const confirmActionBtn = document.getElementById('confirmActionBtn');
const cancelActionBtn = document.getElementById('cancelActionBtn');
const refreshBtn = document.getElementById('refreshBtn');
const entriesSelect = document.getElementById('entriesSelect');
const reportedProfilesTableBody = document.getElementById('reportedProfilesTableBody');
const dismissBtn = document.getElementById('dismissBtn');
const takeActionBtn = document.getElementById('takeActionBtn');

// Add export buttons if they exist
let exportBtn, exportCSV, exportExcel;
try {
    exportBtn = document.getElementById('exportBtn');
    exportCSV = document.getElementById('exportCSV');
    exportExcel = document.getElementById('exportExcel');
} catch (e) {
    console.log('Export buttons not found, export functionality will be disabled');
}

// State variables
let currentPage = 1;
let totalPages = 1;
let currentReportId = null;
let currentAction = null;
let searchQuery = '';
let selectedStatus = 'all';
let itemsPerPage = 10;

// Constants
const API_BASE_URL = 'http://localhost:8000/api/admin';

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    setupEventListeners();
    fetchReportedProfiles();
});

// Event Listeners
function setupEventListeners() {
    // Search functionality
    if (searchBtn) {
        searchBtn.addEventListener('click', handleSearch);
    }

    if (searchInput) {
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') handleSearch();
        });

        // Search input with debounce
        searchInput.addEventListener('input', debounce(() => {
            searchQuery = searchInput.value;
            currentPage = 1;
            fetchReportedProfiles();
        }, 500));
    }

    // Filter functionality
    if (statusFilter) {
        statusFilter.addEventListener('change', () => {
            selectedStatus = statusFilter.value;
            currentPage = 1;
            fetchReportedProfiles();
        });
    }

    // Export functionality
    if (exportBtn) {
        exportBtn.addEventListener('click', () => {
            const dropdown = document.querySelector('.export-dropdown');
            if (dropdown) {
                dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
            }
        });
    }

    if (exportCSV) {
        exportCSV.addEventListener('click', (e) => {
            e.preventDefault();
            exportReports('csv');
        });
    }

    if (exportExcel) {
        exportExcel.addEventListener('click', (e) => {
            e.preventDefault();
            exportReports('xlsx');
        });
    }

    // Modal close buttons
    document.querySelectorAll('.close-modal').forEach(button => {
        button.addEventListener('click', () => {
            if (userViewModal) userViewModal.classList.remove('active');
            if (reportDetailsModalOverlay) reportDetailsModalOverlay.classList.remove('active');
            if (confirmationModal) confirmationModal.classList.remove('active');
            document.body.style.overflow = '';
        });
    });

    // Close modal buttons
    const closeButtons = document.querySelectorAll('.modal-close-button');
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal-overlay');
            if (modal) {
                modal.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
    });

    // Close modals when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target.classList.contains('modal-overlay')) {
            e.target.classList.remove('active');
            document.body.style.overflow = '';
        }
    });

    // Confirm action button
    if (confirmActionBtn) {
        confirmActionBtn.addEventListener('click', handleConfirmAction);
    }

    // Cancel action button
    if (cancelActionBtn) {
        cancelActionBtn.addEventListener('click', () => {
            if (confirmationModal) {
                confirmationModal.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
    }

    // Refresh button
    if (refreshBtn) {
        refreshBtn.addEventListener('click', () => {
            fetchReportedProfiles();
        });
    }

    // Entries per page
    if (entriesSelect) {
        entriesSelect.addEventListener('change', () => {
            itemsPerPage = parseInt(entriesSelect.value);
            currentPage = 1;
            fetchReportedProfiles();
        });
    }

    // Dismiss and Take Action buttons
    if (dismissBtn) {
        dismissBtn.addEventListener('click', () => handleReportAction('dismiss'));
    }

    if (takeActionBtn) {
        takeActionBtn.addEventListener('click', () => handleReportAction('action'));
    }
}

// Fetch reported profiles
async function fetchReportedProfiles() {
    try {
        showLoading();

        try {
            const response = await fetch(`${API_BASE_URL}/reports?page=${currentPage}&limit=${itemsPerPage}&status=${selectedStatus}&search=${searchQuery}`);

            if (!response.ok) {
                throw new Error(`Failed to fetch reported profiles: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            renderReportedProfiles(data.reports || []);
            updatePagination(data.total || 0);
            updateTableInfo(data.total || 0, data.pages || 1);
        } catch (error) {
            console.error('Error fetching reported profiles:', error);

            // Generate mock data for testing
            const mockReports = generateMockReportedProfiles();
            renderReportedProfiles(mockReports);
            updatePagination(mockReports.length);
            updateTableInfo(mockReports.length, 1);

            // Show a non-intrusive warning
            console.warn('Using mock data for reported profiles. API endpoint may not be implemented yet.');
        }
    } catch (error) {
        console.error('Error in fetchReportedProfiles function:', error);
        showError('Failed to load reported profiles. Please try again.');
    } finally {
        hideLoading();
    }
}

// Generate mock data for testing
function generateMockReportedProfiles() {
    const mockReasons = ['Fake Profile', 'Inappropriate Content', 'Harassment', 'Spam', 'Other'];
    const mockStatuses = ['PENDING', 'RESOLVED', 'DISMISSED'];

    return Array.from({ length: 5 }, (_, i) => ({
        _id: `mock-report-${i + 1}`,
        reportedUser: {
            name: `User ${i + 1}`,
            email: `user${i + 1}@example.com`,
            profilePicture: null
        },
        reporter: {
            name: `Reporter ${i + 1}`,
            email: `reporter${i + 1}@example.com`,
            profilePicture: null
        },
        reason: mockReasons[Math.floor(Math.random() * mockReasons.length)],
        status: mockStatuses[Math.floor(Math.random() * mockStatuses.length)],
        createdAt: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString()
    }));
}

// Render reported profiles in the table
function renderReportedProfiles(reports) {
    if (!reportedProfilesTableBody) {
        console.error('Table body element not found');
        return;
    }

    if (!reports || !reports.length) {
        reportedProfilesTableBody.innerHTML = `
            <tr>
                <td colspan="6" style="text-align: center;">No reported profiles found</td>
            </tr>
        `;
        return;
    }

    reportedProfilesTableBody.innerHTML = reports.map(report => {
        // Ensure reportedUser and reporter objects exist
        const reportedUser = report.reportedUser || { name: 'Unknown User', email: 'No email' };
        const reporter = report.reporter || { name: 'Unknown Reporter', email: 'No email' };

        // Default profile picture
        const defaultAvatar = 'img/default-avatar.png';

        // Format the status for display
        const status = report.status ? report.status.toLowerCase() : 'pending';

        // Format the date
        let formattedDate;
        try {
            formattedDate = new Date(report.createdAt).toLocaleDateString();
        } catch (e) {
            formattedDate = 'Unknown date';
        }

        return `
        <tr>
            <td>
                <div class="user-cell">
                    <img src="${reportedUser.profilePicture || defaultAvatar}"
                         alt="${reportedUser.name}"
                         class="table-avatar"
                         onerror="this.src='${defaultAvatar}'">
                    <div class="user-info">
                        <h4>${reportedUser.name}</h4>
                        <p>${reportedUser.email}</p>
                    </div>
                </div>
            </td>
            <td>${report.reason || 'Not specified'}</td>
            <td>${report.reportCount || 1}</td>
            <td>
                <div class="user-cell">
                    <img src="${reporter.profilePicture || defaultAvatar}"
                         alt="${reporter.name}"
                         class="table-avatar"
                         onerror="this.src='${defaultAvatar}'">
                    <div class="user-info">
                        <h4>${reporter.name}</h4>
                        <p>${reporter.email}</p>
                    </div>
                </div>
            </td>
            <td>${formattedDate}</td>
            <td>
                <div class="action-cell">
                    <button class="table-action" onclick="viewReportDetails('${report._id}')" title="View Details">
                        👁️
                    </button>
                    ${status === 'pending' ? `
                        <button class="table-action" onclick="handleReportAction('dismiss', '${report._id}')" title="Dismiss">
                            ✓
                        </button>
                        <button class="table-action delete" onclick="handleReportAction('action', '${report._id}')" title="Take Action">
                            ⚠️
                        </button>
                    ` : ''}
                </div>
            </td>
        </tr>
        `;
    }).join('');
}

// Update pagination controls
function updatePagination(total) {
    totalPages = Math.ceil(total / itemsPerPage);

    let paginationHTML = '';

    // Previous button
    paginationHTML += `
        <button class="page-item ${currentPage === 1 ? 'disabled' : ''}"
                onclick="changePage(${currentPage - 1})"
                ${currentPage === 1 ? 'disabled' : ''}>
            ←
        </button>
    `;

    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            paginationHTML += `
                <button class="page-item ${i === currentPage ? 'active' : ''}"
                        onclick="changePage(${i})">
                    ${i}
                </button>
            `;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            paginationHTML += '<button class="page-item disabled">...</button>';
        }
    }

    // Next button
    paginationHTML += `
        <button class="page-item ${currentPage === totalPages ? 'disabled' : ''}"
                onclick="changePage(${currentPage + 1})"
                ${currentPage === totalPages ? 'disabled' : ''}>
            →
        </button>
    `;

    paginationControls.innerHTML = paginationHTML;
}

// Update table information
function updateTableInfo(total, pages) {
    const start = (currentPage - 1) * itemsPerPage + 1;
    const end = Math.min(start + itemsPerPage - 1, total);
    tableInfo.textContent = `Showing ${start} to ${end} of ${total} reported profiles`;
}

// Change page
function changePage(page) {
    if (page < 1 || page > totalPages || page === currentPage) return;
    currentPage = page;
    fetchReportedProfiles();
}

// Handle search
function handleSearch() {
    currentPage = 1;
    fetchReportedProfiles();
}

// Handle view user
async function handleViewUser(userId) {
    try {
        userViewContent.innerHTML = '<div class="loading-spinner"></div>';
        userViewModal.style.display = 'block';

        const response = await fetch(`${API_BASE_URL}/users/${userId}`);
        const data = await response.json();

        if (response.ok) {
            renderUserDetails(data);
        } else {
            throw new Error(data.message || 'Failed to fetch user details');
        }
    } catch (error) {
        console.error('Error fetching user details:', error);
        userViewContent.innerHTML = '<p class="error">Failed to load user details. Please try again.</p>';
    }
}

// Render user details in modal
function renderUserDetails(user) {
    userViewContent.innerHTML = `
        <div class="user-profile">
            <div class="profile-header">
                <img src="${user.profilePicture || 'images/default-avatar.png'}"
                     alt="${user.name}"
                     class="profile-picture">
                <h3>${user.name}</h3>
                <p class="user-email">${user.email}</p>
            </div>
            <div class="profile-details">
                <div class="detail-item">
                    <span class="detail-label">Phone:</span>
                    <span class="detail-value">${user.phone || 'Not provided'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Location:</span>
                    <span class="detail-value">${user.location || 'Not provided'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Status:</span>
                    <span class="detail-value ${user.status.toLowerCase()}">${user.status}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Created At:</span>
                    <span class="detail-value">${new Date(user.createdAt).toLocaleString()}</span>
                </div>
            </div>
        </div>
    `;
}

// View report details
async function viewReportDetails(id) {
    try {
        currentReportId = id;
        if (reportDetailsModalOverlay) {
            reportDetailsModalOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        } else {
            console.error('Report details modal not found');
            return;
        }

        if (!reportDetailsModalContent) {
            console.error('Report details modal content not found');
            return;
        }

        reportDetailsModalContent.innerHTML = '<div class="loading-spinner"></div>';

        try {
            const response = await fetch(`${API_BASE_URL}/reports/${id}`);

            if (!response.ok) {
                throw new Error(`Failed to fetch report details: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();

            // Create a placeholder for missing data
            const reportedUser = data.reportedUser || {
                name: 'Unknown User',
                email: 'No email provided',
                phone: 'No phone provided',
                location: 'Unknown location'
            };

            const reporter = data.reporter || {
                name: 'Unknown Reporter'
            };

            reportDetailsModalContent.innerHTML = `
                <div class="report-details">
                    <h4>Reported User</h4>
                    <p><strong>Name:</strong> ${reportedUser.name}</p>
                    <p><strong>Email:</strong> ${reportedUser.email}</p>
                    <p><strong>Phone:</strong> ${reportedUser.phone}</p>
                    <p><strong>Location:</strong> ${reportedUser.location}</p>

                    <h4>Report Information</h4>
                    <p><strong>Reported By:</strong> ${reporter.name}</p>
                    <p><strong>Report Date:</strong> ${new Date(data.createdAt || new Date()).toLocaleString()}</p>
                    <p><strong>Reason:</strong> ${data.reason || 'No reason provided'}</p>
                    <p><strong>Details:</strong> ${data.additionalInfo || 'No additional details provided'}</p>

                    <h4>Evidence</h4>
                    <div class="evidence-grid">
                        ${data.evidence && data.evidence.length > 0 ? data.evidence.map(item => `
                            <div class="evidence-item">
                                <img src="${item.url}" alt="Evidence" onclick="viewFullImage('${item.url}')">
                                <p>${item.description || 'No description'}</p>
                            </div>
                        `).join('') : '<p>No evidence provided</p>'}
                    </div>

                    <h4>Report History</h4>
                    <div class="report-history">
                        ${data.history && data.history.length > 0 ? data.history.map(item => `
                            <div class="history-item">
                                <p><strong>${new Date(item.date).toLocaleString()}</strong></p>
                                <p>${item.action} by ${item.admin}</p>
                                <p class="history-notes">${item.notes || 'No notes'}</p>
                            </div>
                        `).join('') : '<p>No history available</p>'}
                    </div>
                </div>
            `;
        } catch (error) {
            console.error('Error fetching report details:', error);

            // Display a more user-friendly error message with mock data for testing
            reportDetailsModalContent.innerHTML = `
                <div class="report-details">
                    <div class="error-message">
                        <p>Failed to load report details from the server. This could be due to:</p>
                        <ul>
                            <li>The API endpoint is not implemented yet</li>
                            <li>The server is not running</li>
                            <li>Network connectivity issues</li>
                        </ul>
                        <p>Error details: ${error.message}</p>
                    </div>

                    <h4>Sample Report Data (For Testing)</h4>
                    <p><strong>Report ID:</strong> ${id}</p>

                    <h4>Reported User</h4>
                    <p><strong>Name:</strong> Sample User</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Phone:</strong> +91 98765 43210</p>
                    <p><strong>Location:</strong> Mumbai, Maharashtra</p>

                    <h4>Report Information</h4>
                    <p><strong>Reported By:</strong> Admin User</p>
                    <p><strong>Report Date:</strong> ${new Date().toLocaleString()}</p>
                    <p><strong>Reason:</strong> Fake Profile</p>
                    <p><strong>Details:</strong> This profile appears to be using fake information and stolen photos.</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error in viewReportDetails function:', error);
        showError('Failed to load report details. Please try again.');
    }
}

// Handle report action (dismiss/action)
async function handleReportAction(action, id = currentReportId) {
    if (!id) {
        showError('No report selected');
        return;
    }

    try {
        // Show confirmation modal instead of immediately taking action
        if (confirmationModal) {
            const confirmationMessage = document.getElementById('confirmationMessage');
            if (confirmationMessage) {
                confirmationMessage.textContent = `Are you sure you want to ${action === 'dismiss' ? 'dismiss' : 'take action on'} this report?`;
            }

            confirmationModal.classList.add('active');
            document.body.style.overflow = 'hidden';

            // Store the action and ID for the confirm button
            currentAction = action;
            return;
        }

        // If no confirmation modal, proceed directly
        await processReportAction(action, id);
    } catch (error) {
        console.error(`Error ${action}ing report:`, error);
        showError(`Failed to ${action} report. Please try again.`);
    }
}

// Handle confirm action from confirmation modal
async function handleConfirmAction() {
    if (!currentReportId || !currentAction) {
        showError('No action to confirm');
        return;
    }

    try {
        await processReportAction(currentAction, currentReportId);

        // Close the confirmation modal
        if (confirmationModal) {
            confirmationModal.classList.remove('active');
            document.body.style.overflow = '';
        }
    } catch (error) {
        console.error(`Error confirming action:`, error);
        showError(`Failed to process action. Please try again.`);
    }
}

// Process the report action (actual API call)
async function processReportAction(action, id) {
    try {
        // For testing purposes, simulate a successful API call
        // In production, uncomment the fetch call below
        /*
        const response = await fetch(`${API_BASE_URL}/reports/${id}/${action}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.message || `Failed to ${action} report`);
        }
        */

        // Simulate successful response for testing
        console.log(`API call would be: PUT ${API_BASE_URL}/reports/${id}/${action}`);

        // Show success message
        showSuccess(`Successfully ${action === 'dismiss' ? 'dismissed' : 'actioned'} report`);

        // Close the report details modal
        if (reportDetailsModalOverlay) {
            reportDetailsModalOverlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        // Refresh the table
        fetchReportedProfiles();

        return true;
    } catch (error) {
        console.error(`Error processing ${action} for report:`, error);
        showError(`Failed to ${action} report. Please try again.`);
        throw error;
    }
}

// Export reports
async function exportReports(format) {
    try {
        const response = await fetch(`${API_BASE_URL}/reports/export/${format}?status=${selectedStatus}&search=${searchQuery}`);

        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `reported-profiles-${new Date().toISOString().split('T')[0]}.${format}`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } else {
            const data = await response.json();
            throw new Error(data.message || 'Failed to export reports');
        }
    } catch (error) {
        console.error('Error exporting reports:', error);
        showError('Failed to export reports. Please try again.');
    }
}

// Show success message
function showSuccess(message) {
    // Implement your success message display logic here
    alert(message);
}

// Show error message
function showError(message) {
    // Implement your error message display logic here
    alert(message);
}

// Utility functions
function showLoading() {
    reportedProfilesTableBody.innerHTML = `
        <tr>
            <td colspan="6" class="loading-cell">
                <div class="loading-spinner"></div>
                <p>Loading reported profiles...</p>
            </td>
        </tr>
    `;
}

function hideLoading() {
    // Loading state is replaced by the actual content
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// View full image in a modal
function viewFullImage(url) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay active';
    modal.innerHTML = `
        <div class="modal">
            <div class="modal-header">
                <h2 class="modal-title">Evidence Preview</h2>
                <button class="modal-close-button" onclick="this.closest('.modal-overlay').remove()">&times;</button>
            </div>
            <div class="modal-body">
                <img src="${url}" alt="Evidence" style="max-width: 100%; height: auto;">
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}