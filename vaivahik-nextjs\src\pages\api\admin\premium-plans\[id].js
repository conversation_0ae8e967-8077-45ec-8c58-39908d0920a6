// API endpoint for specific premium plan
import { getMockPlans } from '@/utils/mockData';

export default function handler(req, res) {
  // Set proper content type header
  res.setHeader('Content-Type', 'application/json');

  try {
    // Get the plan ID from the URL
    const { id } = req.query;

    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return getPlanById(req, res, id);
      case 'DELETE':
        return deletePlan(req, res, id);
      default:
        return res.status(405).json({
          success: false,
          message: 'Method not allowed'
        });
    }
  } catch (error) {
    console.error('Error in premium plans API:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}

// GET /api/admin/premium-plans/[id]
function getPlanById(req, res, id) {
  try {
    // In a real implementation, this would fetch data from a database
    // For now, we'll use mock data
    const mockPlans = getMockPlans();

    // Find the plan by ID
    const plan = mockPlans.find(p => p.id.toString() === id);

    // If plan not found, return 404
    if (!plan) {
      return res.status(404).json({
        success: false,
        message: 'Plan not found'
      });
    }

    // Return the plan
    return res.status(200).json({
      success: true,
      plan
    });
  } catch (error) {
    console.error('Error fetching plan:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch plan'
    });
  }
}

// DELETE /api/admin/premium-plans/[id]
function deletePlan(req, res, id) {
  try {
    // In a real implementation, this would delete the plan from the database
    // For now, we'll just return a success response

    return res.status(200).json({
      success: true,
      message: 'Plan deleted successfully',
      planId: id
    });
  } catch (error) {
    console.error('Error deleting plan:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete plan'
    });
  }
}
