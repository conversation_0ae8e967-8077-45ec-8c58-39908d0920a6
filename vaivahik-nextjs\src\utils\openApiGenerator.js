/**
 * OpenAPI Specification Generator
 * 
 * This utility automatically generates OpenAPI specifications based on your API endpoints.
 * It can be used to create documentation for your API and power the API Viewer.
 */

/**
 * Generate an OpenAPI specification for the API
 * @param {Array} endpoints - Array of endpoint objects with path, methods, description, etc.
 * @returns {Object} OpenAPI specification object
 */
export function generateOpenApiSpec(endpoints = []) {
  // Base OpenAPI specification
  const spec = {
    openapi: '3.0.0',
    info: {
      title: 'Vaivahik API',
      description: 'API for Vaivahik Matrimony Platform',
      version: '1.0.0',
      contact: {
        name: 'Vaivahik Support',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000',
        description: 'Current server'
      }
    ],
    paths: {},
    components: {
      schemas: generateCommonSchemas(),
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      }
    }
  };

  // Add paths from endpoints
  endpoints.forEach(endpoint => {
    const { path, methods, description, parameters = [], requestBody, responses = {} } = endpoint;
    
    if (!spec.paths[path]) {
      spec.paths[path] = {};
    }

    // Add each method
    (Array.isArray(methods) ? methods : [methods]).forEach(method => {
      const methodLower = method.toLowerCase();
      
      spec.paths[path][methodLower] = {
        summary: description || `${method} ${path}`,
        description: description || `${method} endpoint for ${path}`,
        tags: [getTagFromPath(path)],
        parameters: generateParameters(parameters),
        responses: generateResponses(responses),
      };

      // Add request body if applicable
      if (['post', 'put', 'patch'].includes(methodLower) && requestBody) {
        spec.paths[path][methodLower].requestBody = {
          content: {
            'application/json': {
              schema: requestBody.schema || {
                type: 'object',
                properties: requestBody.properties || {}
              }
            }
          },
          required: requestBody.required !== false
        };
      }

      // Add security if it's a protected endpoint
      if (isProtectedEndpoint(path)) {
        spec.paths[path][methodLower].security = [{ bearerAuth: [] }];
      }
    });
  });

  return spec;
}

/**
 * Determine if an endpoint is likely to be protected
 * @param {string} path - API path
 * @returns {boolean} True if the endpoint is likely protected
 */
function isProtectedEndpoint(path) {
  // Admin endpoints are always protected
  if (path.includes('/api/admin/')) {
    return true;
  }
  
  // User-specific endpoints are usually protected
  if (path.includes('/api/users/') && 
      !path.endsWith('/register') && 
      !path.endsWith('/login') && 
      !path.includes('/public/')) {
    return true;
  }
  
  // Other protected endpoints
  const protectedPaths = [
    '/api/profiles',
    '/api/matches',
    '/api/preferences',
    '/api/documents',
    '/api/subscriptions',
    '/api/payments',
    '/api/notifications',
    '/api/messages'
  ];
  
  return protectedPaths.some(protectedPath => 
    path.startsWith(protectedPath) || path === protectedPath
  );
}

/**
 * Extract a tag name from the path
 * @param {string} path - API path
 * @returns {string} Tag name
 */
function getTagFromPath(path) {
  if (path.includes('/api/admin/')) {
    const segment = path.split('/api/admin/')[1]?.split('/')[0];
    return segment ? `Admin - ${capitalize(segment)}` : 'Admin';
  }
  
  if (path.includes('/api/users/')) {
    return 'Users';
  }
  
  if (path.startsWith('/api/auth/')) {
    return 'Authentication';
  }
  
  // Extract the first segment after /api/
  const match = path.match(/\/api\/([^\/]+)/);
  return match ? capitalize(match[1]) : 'General';
}

/**
 * Capitalize the first letter of a string
 * @param {string} str - String to capitalize
 * @returns {string} Capitalized string
 */
function capitalize(str) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * Generate OpenAPI parameters from parameter objects
 * @param {Array} parameters - Array of parameter objects
 * @returns {Array} OpenAPI parameters
 */
function generateParameters(parameters) {
  return parameters.map(param => {
    const { name, in: paramIn = 'query', description, required = false, schema } = param;
    
    return {
      name,
      in: paramIn,
      description,
      required,
      schema: schema || { type: 'string' }
    };
  });
}

/**
 * Generate OpenAPI responses from response objects
 * @param {Object} responses - Response objects
 * @returns {Object} OpenAPI responses
 */
function generateResponses(responses) {
  const defaultResponses = {
    '200': {
      description: 'Successful operation',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: {
                type: 'boolean',
                example: true
              },
              message: {
                type: 'string',
                example: 'Operation successful'
              }
            }
          }
        }
      }
    },
    '400': {
      description: 'Bad request',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/Error'
          }
        }
      }
    },
    '401': {
      description: 'Unauthorized',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/Error'
          }
        }
      }
    },
    '404': {
      description: 'Not found',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/Error'
          }
        }
      }
    },
    '500': {
      description: 'Internal server error',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/Error'
          }
        }
      }
    }
  };

  // Merge with provided responses
  const result = { ...defaultResponses };
  
  Object.entries(responses).forEach(([code, response]) => {
    result[code] = {
      description: response.description || defaultResponses[code]?.description || 'Response',
      content: {
        'application/json': {
          schema: response.schema || defaultResponses[code]?.content?.['application/json']?.schema || {
            type: 'object'
          }
        }
      }
    };
  });

  return result;
}

/**
 * Generate common schemas used across the API
 * @returns {Object} Common schemas
 */
function generateCommonSchemas() {
  return {
    Error: {
      type: 'object',
      properties: {
        success: {
          type: 'boolean',
          example: false
        },
        message: {
          type: 'string',
          example: 'Error message'
        },
        errors: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              field: {
                type: 'string',
                example: 'email'
              },
              message: {
                type: 'string',
                example: 'Invalid email format'
              }
            }
          }
        }
      }
    },
    User: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          example: 'usr_123456'
        },
        name: {
          type: 'string',
          example: 'John Doe'
        },
        email: {
          type: 'string',
          example: '<EMAIL>'
        },
        phone: {
          type: 'string',
          example: '+91 9876543210'
        },
        isVerified: {
          type: 'boolean',
          example: true
        },
        isPremium: {
          type: 'boolean',
          example: false
        },
        profileStatus: {
          type: 'string',
          example: 'ACTIVE'
        },
        createdAt: {
          type: 'string',
          format: 'date-time',
          example: '2023-01-15T12:00:00Z'
        }
      }
    },
    Profile: {
      type: 'object',
      properties: {
        userId: {
          type: 'string',
          example: 'usr_123456'
        },
        age: {
          type: 'integer',
          example: 28
        },
        gender: {
          type: 'string',
          example: 'Male'
        },
        height: {
          type: 'string',
          example: '5\'10"'
        },
        location: {
          type: 'string',
          example: 'Mumbai'
        },
        occupation: {
          type: 'string',
          example: 'Software Engineer'
        },
        education: {
          type: 'string',
          example: 'B.Tech in Computer Science'
        },
        maritalStatus: {
          type: 'string',
          example: 'Never Married'
        },
        religion: {
          type: 'string',
          example: 'Hindu'
        },
        caste: {
          type: 'string',
          example: 'Maratha'
        },
        subcaste: {
          type: 'string',
          example: 'Deshmukh'
        },
        gotra: {
          type: 'string',
          example: 'Kashyap'
        }
      }
    },
    VerificationDocument: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          example: 'doc_123456'
        },
        userId: {
          type: 'string',
          example: 'usr_123456'
        },
        type: {
          type: 'string',
          example: 'ID_PROOF'
        },
        url: {
          type: 'string',
          example: '/uploads/documents/id_proof_123456.jpg'
        },
        status: {
          type: 'string',
          example: 'PENDING_REVIEW'
        },
        uploadedAt: {
          type: 'string',
          format: 'date-time',
          example: '2023-07-10T15:30:00Z'
        }
      }
    },
    SubscriptionPlan: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          example: 'plan_123456'
        },
        name: {
          type: 'string',
          example: 'Premium'
        },
        price: {
          type: 'number',
          example: 1999
        },
        currency: {
          type: 'string',
          example: 'INR'
        },
        duration: {
          type: 'integer',
          example: 30
        },
        features: {
          type: 'array',
          items: {
            type: 'string'
          },
          example: ['View contact details', 'Advanced search filters']
        },
        isActive: {
          type: 'boolean',
          example: true
        }
      }
    },
    Feature: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          example: 'feature_123456'
        },
        name: {
          type: 'string',
          example: 'View contact details'
        },
        description: {
          type: 'string',
          example: 'Allows users to view contact details of other users'
        },
        isActive: {
          type: 'boolean',
          example: true
        }
      }
    },
    Report: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          example: 'report_123456'
        },
        reportedUserId: {
          type: 'string',
          example: 'usr_123456'
        },
        reporterId: {
          type: 'string',
          example: 'usr_789012'
        },
        reason: {
          type: 'string',
          example: 'Fake Profile'
        },
        additionalInfo: {
          type: 'string',
          example: 'This person is using someone else\'s photos'
        },
        status: {
          type: 'string',
          example: 'PENDING'
        },
        reportDate: {
          type: 'string',
          format: 'date-time',
          example: '2023-07-10T15:30:00Z'
        }
      }
    }
  };
}

/**
 * Generate API endpoints from the routes in the project
 * This is a simplified version that would need to be expanded
 * @returns {Array} Array of endpoint objects
 */
export function generateApiEndpoints() {
  // This is a simplified version - in a real implementation,
  // you would scan the API routes directory or use reflection
  return [
    // Admin endpoints - Verification Queue
    {
      path: '/api/admin/verification-queue',
      methods: ['GET'],
      description: 'Get all verification requests',
      parameters: [
        { name: 'page', description: 'Page number', schema: { type: 'integer' } },
        { name: 'status', description: 'Filter by status', schema: { type: 'string', enum: ['pending', 'approved', 'rejected', 'all'] } },
        { name: 'search', description: 'Search term', schema: { type: 'string' } },
        { name: 'limit', description: 'Items per page', schema: { type: 'integer' } }
      ],
      responses: {
        '200': {
          description: 'List of verification requests',
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              verifications: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'string', example: '1' },
                    user: { $ref: '#/components/schemas/User' },
                    documents: {
                      type: 'array',
                      items: { $ref: '#/components/schemas/VerificationDocument' }
                    },
                    status: { type: 'string', example: 'pending' },
                    submittedOn: { type: 'string', format: 'date-time', example: '2023-07-10T15:30:00Z' }
                  }
                }
              },
              pagination: {
                type: 'object',
                properties: {
                  totalItems: { type: 'integer', example: 50 },
                  totalPages: { type: 'integer', example: 5 },
                  currentPage: { type: 'integer', example: 1 },
                  itemsPerPage: { type: 'integer', example: 10 }
                }
              }
            }
          }
        }
      }
    },
    {
      path: '/api/admin/verification-queue/{id}',
      methods: ['GET'],
      description: 'Get a specific verification request',
      parameters: [
        { name: 'id', in: 'path', required: true, description: 'Verification request ID', schema: { type: 'string' } }
      ],
      responses: {
        '200': {
          description: 'Verification request details',
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              verification: {
                type: 'object',
                properties: {
                  id: { type: 'string', example: '1' },
                  user: { $ref: '#/components/schemas/User' },
                  documents: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/VerificationDocument' }
                  },
                  status: { type: 'string', example: 'pending' },
                  submittedOn: { type: 'string', format: 'date-time', example: '2023-07-10T15:30:00Z' }
                }
              }
            }
          }
        }
      }
    },
    {
      path: '/api/admin/verification-queue/{id}',
      methods: ['POST'],
      description: 'Update a verification request status',
      parameters: [
        { name: 'id', in: 'path', required: true, description: 'Verification request ID', schema: { type: 'string' } },
        { name: 'action', in: 'query', required: true, description: 'Action to take', schema: { type: 'string', enum: ['approve', 'reject'] } }
      ],
      requestBody: {
        properties: {
          notes: { type: 'string', example: 'Documents verified successfully' }
        }
      },
      responses: {
        '200': {
          description: 'Verification request updated',
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              message: { type: 'string', example: 'Verification approved successfully' }
            }
          }
        }
      }
    },

    // Admin endpoints - Reported Profiles
    {
      path: '/api/admin/reported-profiles',
      methods: ['GET'],
      description: 'Get all reported profiles',
      parameters: [
        { name: 'page', description: 'Page number', schema: { type: 'integer' } },
        { name: 'status', description: 'Filter by status', schema: { type: 'string', enum: ['pending', 'resolved', 'dismissed', 'all'] } },
        { name: 'search', description: 'Search term', schema: { type: 'string' } },
        { name: 'limit', description: 'Items per page', schema: { type: 'integer' } }
      ],
      responses: {
        '200': {
          description: 'List of reported profiles',
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              reports: {
                type: 'array',
                items: { $ref: '#/components/schemas/Report' }
              },
              pagination: {
                type: 'object',
                properties: {
                  totalItems: { type: 'integer', example: 50 },
                  totalPages: { type: 'integer', example: 5 },
                  currentPage: { type: 'integer', example: 1 },
                  itemsPerPage: { type: 'integer', example: 10 }
                }
              }
            }
          }
        }
      }
    },

    // Admin endpoints - Premium Plans
    {
      path: '/api/admin/premium-plans',
      methods: ['GET'],
      description: 'Get all premium plans',
      responses: {
        '200': {
          description: 'List of premium plans',
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              plans: {
                type: 'array',
                items: { $ref: '#/components/schemas/SubscriptionPlan' }
              }
            }
          }
        }
      }
    },
    {
      path: '/api/admin/premium-plans',
      methods: ['POST'],
      description: 'Create a new premium plan',
      requestBody: {
        properties: {
          name: { type: 'string', example: 'Gold Plan' },
          price: { type: 'number', example: 2999 },
          currency: { type: 'string', example: 'INR' },
          duration: { type: 'integer', example: 90 },
          features: { 
            type: 'array', 
            items: { type: 'string' },
            example: ['Feature 1', 'Feature 2', 'Feature 3'] 
          },
          isActive: { type: 'boolean', example: true }
        }
      },
      responses: {
        '201': {
          description: 'Premium plan created',
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              plan: { $ref: '#/components/schemas/SubscriptionPlan' }
            }
          }
        }
      }
    },

    // User endpoints
    {
      path: '/api/users/profile',
      methods: ['GET'],
      description: 'Get the current user\'s profile',
      responses: {
        '200': {
          description: 'User profile',
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              user: { $ref: '#/components/schemas/User' },
              profile: { $ref: '#/components/schemas/Profile' }
            }
          }
        }
      }
    },
    {
      path: '/api/users/profile',
      methods: ['PUT'],
      description: 'Update the current user\'s profile',
      requestBody: {
        properties: {
          name: { type: 'string', example: 'John Doe' },
          age: { type: 'integer', example: 28 },
          height: { type: 'string', example: '5\'10"' },
          location: { type: 'string', example: 'Mumbai' },
          occupation: { type: 'string', example: 'Software Engineer' },
          education: { type: 'string', example: 'B.Tech in Computer Science' },
          maritalStatus: { type: 'string', example: 'Never Married' },
          religion: { type: 'string', example: 'Hindu' },
          caste: { type: 'string', example: 'Maratha' },
          subcaste: { type: 'string', example: 'Deshmukh' },
          gotra: { type: 'string', example: 'Kashyap' }
        }
      },
      responses: {
        '200': {
          description: 'Profile updated',
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              message: { type: 'string', example: 'Profile updated successfully' },
              profile: { $ref: '#/components/schemas/Profile' }
            }
          }
        }
      }
    },

    // Authentication endpoints
    {
      path: '/api/auth/send-otp',
      methods: ['POST'],
      description: 'Send OTP for phone verification',
      requestBody: {
        properties: {
          phone: { type: 'string', example: '+91 9876543210' }
        }
      },
      responses: {
        '200': {
          description: 'OTP sent',
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              message: { type: 'string', example: 'OTP sent successfully' }
            }
          }
        }
      }
    },
    {
      path: '/api/auth/verify-otp',
      methods: ['POST'],
      description: 'Verify OTP and sign in',
      requestBody: {
        properties: {
          phone: { type: 'string', example: '+91 9876543210' },
          otp: { type: 'string', example: '123456' }
        }
      },
      responses: {
        '200': {
          description: 'OTP verified and user signed in',
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              message: { type: 'string', example: 'OTP verified successfully' },
              token: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
              user: { $ref: '#/components/schemas/User' }
            }
          }
        }
      }
    }
  ];
}
