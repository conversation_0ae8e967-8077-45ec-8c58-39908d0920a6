// src/controllers/admin/chatSettings.controller.js

const ChatSettings = require('../../models/ChatSettings');

/**
 * @description Get chat settings
 * @route GET /api/admin/chat-settings
 */
exports.getChatSettings = async (req, res, next) => {
    try {
        // Return the chat settings
        res.status(200).json({
            success: true,
            settings: ChatSettings
        });
    } catch (error) {
        console.error('Error getting chat settings:', error);
        next(error);
    }
};

/**
 * @description Update moderation settings
 * @route PUT /api/admin/chat-settings/moderation
 */
exports.updateModerationSettings = async (req, res, next) => {
    try {
        const { enabled, strictness, tierSettings } = req.body;
        
        // Validate input
        if (typeof enabled !== 'boolean') {
            return res.status(400).json({
                success: false,
                message: 'Enabled must be a boolean'
            });
        }
        
        if (!['low', 'medium', 'high'].includes(strictness)) {
            return res.status(400).json({
                success: false,
                message: 'Strictness must be one of: low, medium, high'
            });
        }
        
        if (!tierSettings || typeof tierSettings !== 'object') {
            return res.status(400).json({
                success: false,
                message: 'Tier settings are required'
            });
        }
        
        // Update the settings
        ChatSettings.aiFeatures.contentModeration.enabled = enabled;
        ChatSettings.aiFeatures.contentModeration.strictness = strictness;
        
        // Update tier settings
        if (tierSettings.BASIC) {
            ChatSettings.aiFeatures.contentModeration.tierSettings.BASIC = {
                ...ChatSettings.aiFeatures.contentModeration.tierSettings.BASIC,
                ...tierSettings.BASIC
            };
        }
        
        if (tierSettings.VERIFIED) {
            ChatSettings.aiFeatures.contentModeration.tierSettings.VERIFIED = {
                ...ChatSettings.aiFeatures.contentModeration.tierSettings.VERIFIED,
                ...tierSettings.VERIFIED
            };
        }
        
        if (tierSettings.PREMIUM) {
            ChatSettings.aiFeatures.contentModeration.tierSettings.PREMIUM = {
                ...ChatSettings.aiFeatures.contentModeration.tierSettings.PREMIUM,
                ...tierSettings.PREMIUM
            };
        }
        
        // Save to database (in a real implementation)
        // For now, we're just updating the in-memory model
        
        res.status(200).json({
            success: true,
            message: 'Moderation settings updated successfully',
            settings: ChatSettings.aiFeatures.contentModeration
        });
    } catch (error) {
        console.error('Error updating moderation settings:', error);
        next(error);
    }
};
