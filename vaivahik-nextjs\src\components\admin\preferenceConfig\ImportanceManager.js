import { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  IconButton,
  InputLabel,
  List,
  ListItem,
  ListItemIcon,
  ListItemSecondaryAction,
  ListItemText,
  MenuItem,
  Paper,
  Select,
  Slider,
  Switch,
  TextField,
  Tooltip,
  Typography
} from '@mui/material';
import {
  Edit as EditIcon,
  TuneRounded as TuneIcon,
  Male as MaleIcon,
  Female as FemaleIcon,
  People as PeopleIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import axiosInstance from '@/utils/axiosConfig';

const ImportanceManager = ({ importanceSettings, fields, refreshData }) => {
  const [openDialog, setOpenDialog] = useState(false);
  const [currentSetting, setCurrentSetting] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [formData, setFormData] = useState({
    fieldId: '',
    gender: 'ALL',
    importanceLevel: 5.0,
    description: '',
    isActive: true
  });
  const [errors, setErrors] = useState({});

  // Get unique categories from fields
  const categories = [...new Set(fields.map(field => field.category?.id))].map(
    categoryId => fields.find(field => field.category?.id === categoryId)?.category
  ).filter(Boolean);

  // Filter fields by category
  const filteredFields = selectedCategory === 'all'
    ? fields
    : fields.filter(field => field.category?.id === selectedCategory);

  // Group importance settings by field
  const settingsByField = {};
  importanceSettings.forEach(setting => {
    const fieldId = setting.field?.id;
    if (!settingsByField[fieldId]) {
      settingsByField[fieldId] = [];
    }
    settingsByField[fieldId].push(setting);
  });

  // Open dialog for editing an importance setting
  const handleEditSetting = (setting) => {
    setCurrentSetting(setting);
    setFormData({
      fieldId: setting.field.id,
      gender: setting.gender || 'ALL',
      importanceLevel: setting.importanceLevel,
      description: setting.description || '',
      isActive: setting.isActive
    });
    setErrors({});
    setOpenDialog(true);
  };

  // Open dialog for creating a new importance setting
  const handleAddSetting = (field) => {
    setCurrentSetting(null);
    setFormData({
      fieldId: field.id,
      gender: 'ALL',
      importanceLevel: 5.0,
      description: '',
      isActive: true
    });
    setErrors({});
    setOpenDialog(true);
  };

  // Close dialog
  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, checked, type } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
    
    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };

  // Handle slider change
  const handleSliderChange = (event, newValue) => {
    setFormData({
      ...formData,
      importanceLevel: newValue
    });
  };

  // Validate form data
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.fieldId) {
      newErrors.fieldId = 'Field is required';
    }
    
    if (!formData.gender) {
      newErrors.gender = 'Gender is required';
    }
    
    if (formData.importanceLevel < 0 || formData.importanceLevel > 10) {
      newErrors.importanceLevel = 'Importance level must be between 0 and 10';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Submit form data
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }
    
    try {
      const response = await axiosInstance.post(
        '/api/admin/preference-config/importance',
        formData
      );
      
      if (response.data.success) {
        toast.success('Importance setting saved successfully');
        refreshData();
        handleCloseDialog();
      } else {
        toast.error(response.data.message || 'Failed to save importance setting');
      }
    } catch (error) {
      console.error('Error saving importance setting:', error);
      toast.error(error.response?.data?.message || 'An error occurred while saving the importance setting');
    }
  };

  // Get gender icon
  const getGenderIcon = (gender) => {
    switch (gender) {
      case 'MALE':
        return <MaleIcon color="primary" />;
      case 'FEMALE':
        return <FemaleIcon color="secondary" />;
      default:
        return <PeopleIcon color="action" />;
    }
  };

  // Format importance level for display
  const formatImportanceLevel = (level) => {
    if (level === 0) return 'Not important';
    if (level < 3) return 'Slightly important';
    if (level < 5) return 'Moderately important';
    if (level < 7) return 'Important';
    if (level < 9) return 'Very important';
    return 'Critical';
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel id="category-filter-label">Filter by Category</InputLabel>
          <Select
            labelId="category-filter-label"
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            label="Filter by Category"
          >
            <MenuItem value="all">All Categories</MenuItem>
            {categories.map((category) => (
              <MenuItem key={category.id} value={category.id}>
                {category.displayName}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      {filteredFields.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1" color="textSecondary">
            No fields found. {selectedCategory !== 'all' ? 'Try selecting a different category.' : ''}
          </Typography>
        </Paper>
      ) : (
        filteredFields
          .filter(field => field.isMatchCriteria) // Only show fields that are used in matching
          .sort((a, b) => {
            // Sort by category first, then by display order
            if (a.category?.displayOrder !== b.category?.displayOrder) {
              return a.category?.displayOrder - b.category?.displayOrder;
            }
            return a.displayOrder - b.displayOrder;
          })
          .map((field) => {
            const fieldSettings = settingsByField[field.id] || [];
            return (
              <Card key={field.id} sx={{ mb: 3 }}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Box>
                      <Typography variant="h6">
                        {field.displayName}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        Category: {field.category?.displayName}
                      </Typography>
                    </Box>
                    <Button
                      variant="outlined"
                      color="primary"
                      onClick={() => handleAddSetting(field)}
                    >
                      Set Importance
                    </Button>
                  </Box>
                  <Divider sx={{ mb: 2 }} />
                  
                  {fieldSettings.length === 0 ? (
                    <Typography variant="body2" color="textSecondary" sx={{ p: 1 }}>
                      No importance settings defined for this field.
                    </Typography>
                  ) : (
                    <List>
                      {fieldSettings.map((setting) => (
                        <ListItem key={setting.id} divider>
                          <ListItemIcon>
                            {getGenderIcon(setting.gender)}
                          </ListItemIcon>
                          <ListItemText
                            primary={
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Typography variant="body1">
                                  {setting.gender === 'MALE' ? 'Male Preference' : 
                                   setting.gender === 'FEMALE' ? 'Female Preference' : 
                                   'All Genders'}
                                </Typography>
                                {!setting.isActive && (
                                  <Typography variant="caption" color="error" sx={{ ml: 1 }}>
                                    (Inactive)
                                  </Typography>
                                )}
                              </Box>
                            }
                            secondary={
                              <>
                                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                                  <Box sx={{ width: '60%', mr: 2 }}>
                                    <Slider
                                      value={setting.importanceLevel}
                                      min={0}
                                      max={10}
                                      step={0.1}
                                      disabled
                                      valueLabelDisplay="auto"
                                      valueLabelFormat={(value) => value.toFixed(1)}
                                    />
                                  </Box>
                                  <Typography variant="body2" color="textSecondary">
                                    {setting.importanceLevel.toFixed(1)} - {formatImportanceLevel(setting.importanceLevel)}
                                  </Typography>
                                </Box>
                                {setting.description && (
                                  <Typography variant="body2" color="textSecondary">
                                    {setting.description}
                                  </Typography>
                                )}
                              </>
                            }
                          />
                          <ListItemSecondaryAction>
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={setting.isActive}
                                  onChange={async (e) => {
                                    try {
                                      const response = await axiosInstance.post(
                                        '/api/admin/preference-config/importance',
                                        {
                                          fieldId: setting.field.id,
                                          gender: setting.gender || 'ALL',
                                          importanceLevel: setting.importanceLevel,
                                          isActive: e.target.checked
                                        }
                                      );
                                      
                                      if (response.data.success) {
                                        toast.success(`Importance setting ${e.target.checked ? 'activated' : 'deactivated'} successfully`);
                                        refreshData();
                                      } else {
                                        toast.error(response.data.message || 'Failed to update importance setting');
                                      }
                                    } catch (error) {
                                      console.error('Error updating importance setting:', error);
                                      toast.error('Failed to update importance setting status');
                                    }
                                  }}
                                  color="primary"
                                />
                              }
                              label="Active"
                            />
                            <Tooltip title="Edit">
                              <IconButton
                                edge="end"
                                aria-label="edit"
                                onClick={() => handleEditSetting(setting)}
                              >
                                <EditIcon />
                              </IconButton>
                            </Tooltip>
                          </ListItemSecondaryAction>
                        </ListItem>
                      ))}
                    </List>
                  )}
                </CardContent>
              </Card>
            );
          })
      )}

      {/* Add/Edit Importance Setting Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {currentSetting ? 'Edit Importance Setting' : 'Add Importance Setting'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth required error={!!errors.gender}>
                <InputLabel id="gender-label">Gender</InputLabel>
                <Select
                  labelId="gender-label"
                  name="gender"
                  value={formData.gender}
                  onChange={handleInputChange}
                  label="Gender"
                >
                  <MenuItem value="ALL">All Genders</MenuItem>
                  <MenuItem value="MALE">Male</MenuItem>
                  <MenuItem value="FEMALE">Female</MenuItem>
                </Select>
                {errors.gender && <FormHelperText>{errors.gender}</FormHelperText>}
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <Typography id="importance-slider-label" gutterBottom>
                Importance Level: {formData.importanceLevel.toFixed(1)} - {formatImportanceLevel(formData.importanceLevel)}
              </Typography>
              <Slider
                value={formData.importanceLevel}
                onChange={handleSliderChange}
                aria-labelledby="importance-slider-label"
                min={0}
                max={10}
                step={0.1}
                marks={[
                  { value: 0, label: '0' },
                  { value: 2, label: '2' },
                  { value: 4, label: '4' },
                  { value: 6, label: '6' },
                  { value: 8, label: '8' },
                  { value: 10, label: '10' }
                ]}
                valueLabelDisplay="auto"
                valueLabelFormat={(value) => value.toFixed(1)}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="description"
                label="Description"
                value={formData.description}
                onChange={handleInputChange}
                fullWidth
                multiline
                rows={2}
                helperText="Optional description of why this preference is important"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleInputChange}
                    color="primary"
                  />
                }
                label="Active"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} color="primary" variant="contained">
            {currentSetting ? 'Update' : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ImportanceManager;
