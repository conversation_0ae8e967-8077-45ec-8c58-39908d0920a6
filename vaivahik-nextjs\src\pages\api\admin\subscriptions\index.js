// API endpoint for managing subscriptions
import axios from 'axios';
import { handleApiError } from '@/utils/errorHandler';
import { withAuth } from '@/utils/authHandler';

// Backend API URL - adjust this to your actual backend URL
const BACKEND_API_URL = 'http://localhost:3001/api';

async function handler(req, res) {
  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getSubscriptions(req, res);
      case 'POST':
        return await createSubscription(req, res);
      case 'PUT':
        return await updateSubscription(req, res);
      case 'DELETE':
        return await deleteSubscription(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    return handleApiError(error, res, 'Subscriptions API');
  }
}

// GET /api/admin/subscriptions
async function getSubscriptions(req, res) {
  try {
    // Get the auth token from the request cookies or headers
    const token = req.cookies?.adminToken || req.headers.authorization?.split(' ')[1];

    if (!token) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    // Get query parameters for pagination and filtering
    const { page = 1, limit = 10, status, search } = req.query;

    try {
      // Fetch subscriptions from the backend API
      const response = await axios({
        method: 'GET',
        url: `${BACKEND_API_URL}/admin/subscriptions`,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        params: {
          page,
          limit,
          status,
          search
        },
        timeout: 10000 // 10 second timeout
      });

      // Return the response from the backend
      return res.status(200).json(response.data);
    } catch (apiError) {
      // Log the error
      console.error('Error fetching subscriptions from backend API:', apiError.message);
      
      // Return a meaningful error message
      return res.status(503).json({
        success: false,
        message: 'Failed to fetch subscriptions from backend API.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available'
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Get subscriptions');
  }
}

// POST /api/admin/subscriptions
async function createSubscription(req, res) {
  try {
    const token = req.cookies?.adminToken || req.headers.authorization?.split(' ')[1];

    if (!token) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    const subscriptionData = req.body;

    try {
      const response = await axios({
        method: 'POST',
        url: `${BACKEND_API_URL}/admin/subscriptions`,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        data: subscriptionData,
        timeout: 10000
      });

      return res.status(201).json(response.data);
    } catch (apiError) {
      console.error('Error creating subscription:', apiError.message);
      
      return res.status(503).json({
        success: false,
        message: 'Failed to create subscription.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available'
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Create subscription');
  }
}

// PUT /api/admin/subscriptions
async function updateSubscription(req, res) {
  try {
    const token = req.cookies?.adminToken || req.headers.authorization?.split(' ')[1];

    if (!token) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    const { id, ...subscriptionData } = req.body;

    if (!id) {
      return res.status(400).json({ success: false, message: 'Subscription ID is required' });
    }

    try {
      const response = await axios({
        method: 'PUT',
        url: `${BACKEND_API_URL}/admin/subscriptions/${id}`,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        data: subscriptionData,
        timeout: 10000
      });

      return res.status(200).json(response.data);
    } catch (apiError) {
      console.error('Error updating subscription:', apiError.message);
      
      return res.status(503).json({
        success: false,
        message: 'Failed to update subscription.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available'
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Update subscription');
  }
}

// DELETE /api/admin/subscriptions
async function deleteSubscription(req, res) {
  try {
    const token = req.cookies?.adminToken || req.headers.authorization?.split(' ')[1];

    if (!token) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    const { id } = req.query;

    if (!id) {
      return res.status(400).json({ success: false, message: 'Subscription ID is required' });
    }

    try {
      const response = await axios({
        method: 'DELETE',
        url: `${BACKEND_API_URL}/admin/subscriptions/${id}`,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      return res.status(200).json(response.data);
    } catch (apiError) {
      console.error('Error deleting subscription:', apiError.message);
      
      return res.status(503).json({
        success: false,
        message: 'Failed to delete subscription.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available'
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Delete subscription');
  }
}

// Export the handler with authentication middleware
export default withAuth(handler, 'ADMIN');
