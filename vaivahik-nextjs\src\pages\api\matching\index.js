/**
 * API endpoint for matching users
 * This endpoint calls the Python matching service
 */
// Commented out next-auth imports as they're not available
// import { getServerSession } from 'next-auth/next';
// import { authOptions } from '../../auth/[...nextauth]';

// Mock session for development
const mockSession = {
  user: {
    id: 'user-123',
    name: 'Test User',
    email: '<EMAIL>'
  }
};
import { PrismaClient } from '@prisma/client';
import axios from 'axios';

// Initialize Prisma client
const prisma = new PrismaClient();

// URL of the Python matching service
const MATCHING_SERVICE_URL = process.env.MATCHING_SERVICE_URL || 'http://localhost:5000';

// Fallback matching function (rule-based)
async function fallbackMatching(userId, limit = 10) {
  try {
    // Get user profile and preferences
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        profile: true,
        preference: true
      }
    });

    if (!user) {
      return { success: false, message: 'User not found' };
    }

    // Get potential matches
    // This is a simplified query - in a real implementation, you would
    // filter based on user preferences
    const potentialMatches = await prisma.user.findMany({
      where: {
        id: { not: userId },
        profileStatus: 'ACTIVE',
        gender: user.preference?.gender || (user.gender === 'MALE' ? 'FEMALE' : 'MALE')
      },
      include: {
        profile: true
      },
      take: 100 // Get more than needed for scoring
    });

    if (!potentialMatches || potentialMatches.length === 0) {
      return { success: true, matches: [] };
    }

    // Simple scoring based on age preference
    const matches = potentialMatches.map(match => {
      // Calculate age
      const calculateAge = (birthDate) => {
        if (!birthDate) return 0;
        const today = new Date();
        const birth = new Date(birthDate);
        let age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
          age--;
        }
        return age;
      };

      // Get ages
      const matchAge = calculateAge(match.profile?.birthDate);

      // Get preferences
      const ageMin = user.preference?.ageMin || 18;
      const ageMax = user.preference?.ageMax || 60;

      // Calculate score
      let score = 0.5; // Default score

      // Age score
      if (matchAge >= ageMin && matchAge <= ageMax) {
        // Age is within preference range
        score += 0.3;
      } else {
        // Age is outside preference range
        const ageDiff = Math.min(Math.abs(matchAge - ageMin), Math.abs(matchAge - ageMax));
        score -= Math.min(0.3, ageDiff * 0.05); // Reduce score based on how far outside the range
      }

      // Boost for premium and verified profiles
      if (match.isPremium) score += 0.1;
      if (match.isVerified) score += 0.1;

      // Ensure score is between 0 and 1
      score = Math.max(0, Math.min(1, score));

      return {
        userId: match.id,
        name: match.profile?.fullName || '',
        age: matchAge,
        city: match.profile?.city || '',
        occupation: match.profile?.occupation || '',
        education: match.profile?.highestEducation || '',
        score: Math.round(score * 100), // Convert to percentage
        isPremium: match.isPremium || false,
        isVerified: match.isVerified || false,
        profilePictureUrl: match.profile?.profilePictureUrl || ''
      };
    });

    // Sort by score (descending)
    matches.sort((a, b) => b.score - a.score);

    // Apply limit
    const limitedMatches = matches.slice(0, limit);

    return { success: true, matches: limitedMatches };
  } catch (error) {
    console.error('Error in fallback matching:', error);
    return { success: false, message: 'Error finding matches' };
  }
}

export default async function handler(req, res) {
  // Use mock session for development
  const session = mockSession;

  // In production, we would use next-auth
  // const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ success: false, message: 'Not authenticated' });
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getMatches(req, res, session);
    default:
      return res.status(405).json({ success: false, message: 'Method not allowed' });
  }
}

// GET /api/matching
async function getMatches(req, res, session) {
  try {
    const { userId, limit = 10, offset = 0, minScore } = req.query;

    // Use the authenticated user's ID if userId is not provided
    const targetUserId = userId || session.user.id;

    // Try to use the Python matching service
    try {
      // Get user profile and preferences
      const user = await prisma.user.findUnique({
        where: { id: targetUserId },
        include: {
          profile: true,
          preference: true
        }
      });

      if (!user) {
        return res.status(404).json({ success: false, message: 'User not found' });
      }

      // Get potential matches
      const potentialMatches = await prisma.user.findMany({
        where: {
          id: { not: targetUserId },
          profileStatus: 'ACTIVE',
          gender: user.preference?.gender || (user.gender === 'MALE' ? 'FEMALE' : 'MALE')
        },
        include: {
          profile: true
        },
        take: 100 // Get more than needed for scoring
      });

      if (!potentialMatches || potentialMatches.length === 0) {
        return res.status(200).json({ success: true, matches: [] });
      }

      // Call the Python matching service
      const response = await axios.post(`${MATCHING_SERVICE_URL}/api/match`, {
        user: {
          id: targetUserId,
          profile: user.profile
        },
        preferences: user.preference || {},
        potentialMatches: potentialMatches.map(match => ({
          id: match.id,
          profile: match.profile
        }))
      }, {
        timeout: 5000 // 5 second timeout
      });

      if (response.data.success) {
        // Get the matches from the response
        let matches = response.data.matches;

        // Filter by minimum score if provided
        if (minScore) {
          matches = matches.filter(match => match.score >= parseInt(minScore));
        }

        // Apply pagination
        matches = matches.slice(parseInt(offset), parseInt(offset) + parseInt(limit));

        // Enhance match data with additional information
        const enhancedMatches = await Promise.all(matches.map(async (match) => {
          const matchUser = potentialMatches.find(m => m.id === match.userId);
          if (!matchUser) return match;

          // Calculate age
          const calculateAge = (birthDate) => {
            if (!birthDate) return null;
            const today = new Date();
            const birth = new Date(birthDate);
            let age = today.getFullYear() - birth.getFullYear();
            const monthDiff = today.getMonth() - birth.getMonth();
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
              age--;
            }
            return age;
          };

          return {
            ...match,
            name: matchUser.profile?.fullName || '',
            age: calculateAge(matchUser.profile?.birthDate),
            city: matchUser.profile?.city || '',
            occupation: matchUser.profile?.occupation || '',
            education: matchUser.profile?.highestEducation || '',
            isPremium: matchUser.isPremium || false,
            isVerified: matchUser.isVerified || false,
            profilePictureUrl: matchUser.profile?.profilePictureUrl || ''
          };
        }));

        return res.status(200).json({ success: true, matches: enhancedMatches });
      } else {
        throw new Error(response.data.message || 'Error from matching service');
      }
    } catch (serviceError) {
      console.error('Error calling matching service:', serviceError);
      console.log('Falling back to rule-based matching');

      // Fall back to rule-based matching
      const result = await fallbackMatching(targetUserId, parseInt(limit));

      if (result.success) {
        return res.status(200).json(result);
      } else {
        throw new Error(result.message || 'Error in fallback matching');
      }
    }
  } catch (error) {
    console.error('Error getting matches:', error);
    return res.status(500).json({ success: false, message: 'Error finding matches' });
  }
}
