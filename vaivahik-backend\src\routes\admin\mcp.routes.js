/**
 * MCP Admin Routes
 * Routes for managing MCP server from admin panel
 */

const express = require('express');
const router = express.Router();
const mcpController = require('../../controllers/admin/mcp.controller');

// Server management routes
router.get('/status', mcpController.getStatus);
router.post('/start', mcpController.startServer);
router.post('/stop', mcpController.stopServer);
router.post('/restart', mcpController.restartServer);
router.get('/health', mcpController.healthCheck);

// Configuration routes
router.put('/config', mcpController.updateConfig);

// Client management routes
router.get('/clients', mcpController.getClients);

// Tool management routes
router.get('/tools', mcpController.getTools);
router.post('/tools', mcpController.registerTool);
router.post('/tools/test', mcpController.testTool);

// Resource management routes
router.get('/resources', mcpController.getResources);
router.post('/resources', mcpController.registerResource);

// Prompt management routes
router.get('/prompts', mcpController.getPrompts);
router.post('/prompts', mcpController.registerPrompt);

// Analytics routes
router.get('/analytics', mcpController.getAnalytics);

module.exports = router;
