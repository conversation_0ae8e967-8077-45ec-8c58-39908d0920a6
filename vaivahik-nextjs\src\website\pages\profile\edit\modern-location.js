/**
 * Modern Location Details Page
 *
 * This page allows users to update their location details using a modern UI form.
 */

import React, { useState } from 'react';
import { Box, Container, Typography, Alert, Breadcrumbs } from '@mui/material';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import ModernLocationDetailsForm from '@/components/profile/ModernLocationDetailsForm';
import { useAuth } from '@/contexts/AuthContext';
import { isUsingRealBackend } from '@/utils/apiUtils';
import { updateLocationDetails } from '@/services/userApiService';
import { formatError, getUserFriendlyMessage, isNetworkError } from '@/utils/errorHandling';
import { withRetry } from '@/utils/retryLogic';

const ModernLocationDetailsPage = () => {
  const router = useRouter();
  const { userData, setUserData } = useAuth();
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Handle form submission
  const handleSave = async (formData) => {
    try {
      setSaving(true);
      setError(''); // Clear previous errors

      if (isUsingRealBackend()) {
        // Define the API call function
        const saveData = async () => {
          try {
            return await updateLocationDetails(formData);
          } catch (apiError) {
            // Format the error for better user feedback
            const formattedError = formatError(apiError);
            throw formattedError; // Rethrow for retry logic to catch
          }
        };

        // Call API with retry logic for network errors
        const response = await withRetry(saveData, {
          maxRetries: 3,
          retryCondition: isNetworkError
        });

        setSuccess('Location details saved successfully!');

        // Update local user data
        setUserData(prev => ({
          ...prev,
          locationDetails: response.data?.locationDetails || formData
        }));
      } else {
        // Simulate API call
        setTimeout(() => {
          setSuccess('Location details saved successfully!');

          // Update local user data
          setUserData(prev => ({
            ...prev,
            locationDetails: formData,
            profileCompletionPercentage: Math.min(80, (prev?.profileCompletionPercentage || 60) + 5)
          }));

          setSaving(false);
        }, 1000);
        return;
      }

      setSaving(false);
    } catch (err) {
      console.error('Error saving location details:', err);

      // Get user-friendly error message
      const errorMessage = getUserFriendlyMessage(err);
      setError(errorMessage);

      setSaving(false);
    }
  };

  return (
    <>
      <Head>
        <title>Location Details | Vaivahik</title>
        <meta name="description" content="Update your location details" />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box sx={{ mb: 4 }}>
          <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
            <Link href="/website/pages/profile" passHref>
              <Typography color="inherit" sx={{ textDecoration: 'none', cursor: 'pointer' }}>
                Profile
              </Typography>
            </Link>
            <Typography color="text.primary">Location Details</Typography>
          </Breadcrumbs>

          <Typography variant="h4" component="h1" gutterBottom>
            Location Details
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Update your location information to help us find matches in your preferred areas.
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError('')}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess('')}>
              {success}
            </Alert>
          )}
        </Box>

        <ModernLocationDetailsForm
          userData={userData}
          onSave={handleSave}
          isLoading={saving}
        />

        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Next: Update your <Link href="/website/pages/profile/edit/modern-family">Family Details</Link>
          </Typography>
        </Box>
      </Container>
    </>
  );
};

export default ModernLocationDetailsPage;
