#!/usr/bin/env node

/**
 * Enhanced OTP Service Test Script
 * Tests the complete OTP functionality with fallback mechanisms
 */

// Load environment variables
require('dotenv').config();

// Import services
const { sendOtpEnhanced, verifyOtpEnhanced, resendOtpEnhanced, getOtpStatus } = require('../src/services/otp/enhancedOtpService');
const msg91Service = require('../src/services/sms/msg91.service');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

// Check if phone number is provided
const phoneNumber = process.argv[2];
if (!phoneNumber) {
  log('❌ Please provide a phone number as an argument.', 'red');
  log('Usage: node scripts/test-enhanced-otp.js <phone_number>', 'yellow');
  process.exit(1);
}

// Initialize MSG91 service
msg91Service.initialize({
  apiKey: process.env.MSG91_API_KEY,
  senderId: process.env.MSG91_SENDER_ID,
  dltTemplateId: process.env.MSG91_DLT_TEMPLATE_ID,
  dltPeId: process.env.MSG91_DLT_PE_ID,
  otpTemplate: process.env.MSG91_OTP_TEMPLATE
});

/**
 * Test OTP sending functionality
 */
async function testSendOtp(phone) {
  log('\n📱 Testing Enhanced OTP Send...', 'cyan');
  log(`Phone: ${phone}`, 'blue');
  
  try {
    const result = await sendOtpEnhanced(phone);
    
    if (result.success) {
      log('✅ OTP sent successfully!', 'green');
      log(`Provider: ${result.provider}`, 'blue');
      log(`Message: ${result.message}`, 'blue');
      log(`Expires in: ${result.expirySeconds} seconds`, 'blue');
      
      if (result.warning) {
        log(`⚠️  Warning: ${result.warning}`, 'yellow');
      }
      
      if (result.otp) {
        log(`🔐 OTP: ${result.otp}`, 'magenta');
      }
      
      return { success: true, otp: result.otp, phone: result.phone };
    } else {
      log('❌ Failed to send OTP', 'red');
      log(`Error: ${result.message}`, 'red');
      log(`Error Type: ${result.errorType}`, 'red');
      
      if (result.waitTime) {
        log(`Wait Time: ${result.waitTime} seconds`, 'yellow');
      }
      
      return { success: false };
    }
  } catch (error) {
    log('❌ Error in testSendOtp:', 'red');
    log(error.message, 'red');
    return { success: false };
  }
}

/**
 * Test OTP verification functionality
 */
async function testVerifyOtp(phone, otp) {
  log('\n🔍 Testing Enhanced OTP Verification...', 'cyan');
  log(`Phone: ${phone}`, 'blue');
  log(`OTP: ${otp}`, 'blue');
  
  try {
    const result = await verifyOtpEnhanced(phone, otp);
    
    if (result.success) {
      log('✅ OTP verified successfully!', 'green');
      log(`Provider: ${result.provider}`, 'blue');
      log(`Message: ${result.message}`, 'blue');
      return { success: true };
    } else {
      log('❌ OTP verification failed', 'red');
      log(`Error: ${result.message}`, 'red');
      log(`Error Type: ${result.errorType}`, 'red');
      return { success: false };
    }
  } catch (error) {
    log('❌ Error in testVerifyOtp:', 'red');
    log(error.message, 'red');
    return { success: false };
  }
}

/**
 * Test OTP resend functionality
 */
async function testResendOtp(phone, retryType = 'text') {
  log('\n🔄 Testing Enhanced OTP Resend...', 'cyan');
  log(`Phone: ${phone}`, 'blue');
  log(`Retry Type: ${retryType}`, 'blue');
  
  try {
    const result = await resendOtpEnhanced(phone, retryType);
    
    if (result.success) {
      log('✅ OTP resent successfully!', 'green');
      log(`Provider: ${result.provider}`, 'blue');
      log(`Message: ${result.message}`, 'blue');
      log(`Expires in: ${result.expirySeconds} seconds`, 'blue');
      
      if (result.warning) {
        log(`⚠️  Warning: ${result.warning}`, 'yellow');
      }
      
      if (result.otp) {
        log(`🔐 OTP: ${result.otp}`, 'magenta');
      }
      
      return { success: true, otp: result.otp };
    } else {
      log('❌ Failed to resend OTP', 'red');
      log(`Error: ${result.message}`, 'red');
      log(`Error Type: ${result.errorType}`, 'red');
      
      if (result.waitTime) {
        log(`Wait Time: ${result.waitTime} seconds`, 'yellow');
      }
      
      return { success: false };
    }
  } catch (error) {
    log('❌ Error in testResendOtp:', 'red');
    log(error.message, 'red');
    return { success: false };
  }
}

/**
 * Test OTP status functionality
 */
async function testOtpStatus(phone) {
  log('\n📊 Testing OTP Status...', 'cyan');
  log(`Phone: ${phone}`, 'blue');
  
  try {
    const result = await getOtpStatus(phone);
    
    log(`Has Active OTP: ${result.hasActiveOtp}`, 'blue');
    log(`Expires in: ${result.expirySeconds} seconds`, 'blue');
    log(`Phone: ${result.phone}`, 'blue');
    
    return result;
  } catch (error) {
    log('❌ Error in testOtpStatus:', 'red');
    log(error.message, 'red');
    return { hasActiveOtp: false };
  }
}

/**
 * Test invalid OTP verification
 */
async function testInvalidOtp(phone) {
  log('\n🚫 Testing Invalid OTP Verification...', 'cyan');
  
  const invalidOtp = '000000';
  const result = await testVerifyOtp(phone, invalidOtp);
  
  if (!result.success) {
    log('✅ Invalid OTP correctly rejected!', 'green');
  } else {
    log('❌ Invalid OTP was incorrectly accepted!', 'red');
  }
  
  return result;
}

/**
 * Main test function
 */
async function runTests() {
  log('🚀 Starting Enhanced OTP Service Tests', 'bright');
  log('=' * 50, 'blue');
  
  const formattedPhone = msg91Service.formatPhoneNumber(phoneNumber);
  log(`Testing with phone: ${formattedPhone}`, 'blue');
  
  let testResults = {
    send: false,
    verify: false,
    resend: false,
    invalid: false,
    status: false
  };
  
  try {
    // Test 1: Send OTP
    const sendResult = await testSendOtp(formattedPhone);
    testResults.send = sendResult.success;
    
    if (!sendResult.success) {
      log('❌ Cannot continue tests without successful OTP send', 'red');
      return;
    }
    
    // Test 2: Check OTP Status
    await testOtpStatus(formattedPhone);
    testResults.status = true;
    
    // Test 3: Test invalid OTP
    await testInvalidOtp(formattedPhone);
    testResults.invalid = true;
    
    // Test 4: Verify correct OTP (if available)
    if (sendResult.otp) {
      const verifyResult = await testVerifyOtp(formattedPhone, sendResult.otp);
      testResults.verify = verifyResult.success;
      
      if (verifyResult.success) {
        // Test 5: Try to resend after verification (should generate new OTP)
        log('\n🔄 Testing resend after verification...', 'cyan');
        const resendResult = await testResendOtp(formattedPhone);
        testResults.resend = resendResult.success;
      }
    } else {
      log('\n⚠️  OTP not available for verification test (production mode)', 'yellow');
      log('Please manually verify the OTP you received via SMS', 'yellow');
    }
    
  } catch (error) {
    log('❌ Test execution error:', 'red');
    log(error.message, 'red');
  }
  
  // Test Summary
  log('\n📋 Test Results Summary', 'bright');
  log('=' * 30, 'blue');
  
  const tests = [
    { name: 'Send OTP', result: testResults.send },
    { name: 'OTP Status', result: testResults.status },
    { name: 'Invalid OTP Rejection', result: testResults.invalid },
    { name: 'Verify OTP', result: testResults.verify },
    { name: 'Resend OTP', result: testResults.resend }
  ];
  
  tests.forEach(test => {
    const status = test.result ? '✅ PASS' : '❌ FAIL';
    const color = test.result ? 'green' : 'red';
    log(`${test.name}: ${status}`, color);
  });
  
  const passedTests = tests.filter(t => t.result).length;
  const totalTests = tests.length;
  
  log(`\nOverall: ${passedTests}/${totalTests} tests passed`, passedTests === totalTests ? 'green' : 'yellow');
  
  if (passedTests === totalTests) {
    log('🎉 All tests passed! Enhanced OTP service is working correctly.', 'green');
  } else {
    log('⚠️  Some tests failed. Please check the configuration and try again.', 'yellow');
  }
}

// Run the tests
runTests().catch(error => {
  log('❌ Fatal error during testing:', 'red');
  log(error.message, 'red');
  process.exit(1);
});
