const http = require('http');

// Function to check if a server is running on a specific port
function checkServer(port, callback) {
  const options = {
    host: 'localhost',
    port: port,
    path: '/',
    timeout: 2000
  };

  const req = http.get(options, (res) => {
    console.log(`Server is running on port ${port}`);
    console.log(`Status code: ${res.statusCode}`);
    callback(true);
  });

  req.on('error', (err) => {
    console.log(`Server is not running on port ${port}`);
    console.log(`Error: ${err.message}`);
    callback(false);
  });

  req.on('timeout', () => {
    console.log(`Request timed out for port ${port}`);
    req.abort();
    callback(false);
  });
}

// Check if the Next.js server is running on port 3000
checkServer(3000, (isRunning) => {
  if (isRunning) {
    console.log('Next.js server is running on port 3000');
  } else {
    console.log('Next.js server is not running on port 3000');
  }
});
