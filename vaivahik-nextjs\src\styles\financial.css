/* Financial Section Styles */

/* Common styles for financial pages */
.subscriptions-page,
.transactions-page,
.revenue-reports-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  font-size: 1.8rem;
  margin: 0;
  color: var(--text-dark);
}

.header-actions {
  display: flex;
  gap: 10px;
}

/* Filters */
.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 150px;
}

.filter-group label {
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--text-dark);
}

.filter-group select,
.filter-group input {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--bg-white);
  color: var(--text-dark);
}

.search-box {
  flex: 1;
  min-width: 250px;
}

.search-box form {
  display: flex;
}

.search-box input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius) 0 0 var(--border-radius);
  background-color: var(--bg-white);
  color: var(--text-dark);
}

.search-box button {
  padding: 8px 12px;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
  cursor: pointer;
}

.duration-inputs {
  display: flex;
}

.duration-inputs input {
  flex: 1;
  border-radius: var(--border-radius) 0 0 var(--border-radius);
  border-right: none;
}

.duration-inputs select {
  width: 100px;
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

/* Tables */
.table-container {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  overflow-x: auto;
  margin-bottom: 20px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.data-table th {
  background-color: var(--bg-light);
  font-weight: 600;
  color: var(--text-dark);
}

.data-table tr:last-child td {
  border-bottom: none;
}

.data-table tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.action-cell {
  display: flex;
  gap: 8px;
}

.btn-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background-color: var(--bg-light);
  color: var(--text-dark);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background-color: var(--primary-light);
  color: white;
}

.btn-icon.delete:hover {
  background-color: var(--danger);
}

/* Status badges */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-badge.active,
.status-badge.completed {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success);
}

.status-badge.inactive,
.status-badge.pending {
  background-color: rgba(255, 152, 0, 0.1);
  color: var(--warning);
}

.status-badge.failed {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--danger);
}

.status-badge.refunded {
  background-color: rgba(33, 150, 243, 0.1);
  color: var(--info);
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.pagination-info {
  color: var(--text-muted);
  font-size: 0.9rem;
}

.pagination {
  display: flex;
  gap: 5px;
}

.pagination-btn {
  width: 36px;
  height: 36px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  background-color: var(--bg-white);
  color: var(--text-dark);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-btn:hover {
  background-color: var(--primary-light);
  color: white;
  border-color: var(--primary-light);
}

.pagination-btn.active {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

.pagination-btn:disabled {
  background-color: var(--bg-light);
  color: var(--text-muted);
  cursor: not-allowed;
  border-color: var(--border-color);
}

/* Modals */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.4rem;
  color: var(--text-dark);
}

.close-modal {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-muted);
  cursor: pointer;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 15px 20px;
  border-top: 1px solid var(--border-color);
}

/* Forms */
.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--text-dark);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--bg-white);
  color: var(--text-dark);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.form-group input:read-only {
  background-color: var(--bg-light);
  cursor: not-allowed;
}

.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

/* Transaction details */
.transaction-details {
  display: grid;
  gap: 10px;
}

.detail-row {
  display: grid;
  grid-template-columns: 150px 1fr;
  gap: 10px;
  padding: 8px 0;
  border-bottom: 1px solid var(--border-color);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 500;
  color: var(--text-dark);
}

/* Buttons */
.btn-primary {
  padding: 8px 16px;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  padding: 8px 16px;
  background-color: var(--bg-light);
  color: var(--text-dark);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background-color: var(--border-color);
}

/* Empty state */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  text-align: center;
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
}

.empty-icon {
  font-size: 3rem;
  color: var(--text-muted);
  margin-bottom: 1rem;
}

.empty-state h3 {
  font-size: 1.4rem;
  margin-bottom: 1rem;
  color: var(--text-dark);
}

.empty-state p {
  margin-bottom: 15px;
  color: var(--text-muted);
}

/* Loading state */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Dark mode styles */
body.dark-mode .loading-spinner {
  border-color: rgba(255, 255, 255, 0.1);
  border-top-color: var(--primary);
}

/* Responsive styles */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .filters-container {
    flex-direction: column;
  }
  
  .filter-group,
  .search-box {
    width: 100%;
  }
  
  .form-row {
    flex-direction: column;
  }
  
  .pagination-container {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .pagination {
    width: 100%;
    justify-content: center;
  }
}
