<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feature Management - Vaivahik Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/admin-styles.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <img src="img/logo.png" alt="Vaivahik Logo" class="img-fluid" style="max-width: 150px;">
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.html">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.html">
                                <i class="fas fa-users me-2"></i>
                                User Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="verification-queue.html">
                                <i class="fas fa-check-circle me-2"></i>
                                Verification Queue
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reported-profiles.html">
                                <i class="fas fa-flag me-2"></i>
                                Reported Profiles
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="photo-moderation.html">
                                <i class="fas fa-image me-2"></i>
                                Photo Moderation
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="feature-management.html">
                                <i class="fas fa-cogs me-2"></i>
                                Feature Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="subscription-plans.html">
                                <i class="fas fa-credit-card me-2"></i>
                                Subscription Plans
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="ai-settings.html">
                                <i class="fas fa-robot me-2"></i>
                                AI Settings
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="system-settings.html">
                                <i class="fas fa-sliders-h me-2"></i>
                                System Settings
                            </a>
                        </li>
                        <li class="nav-item mt-5">
                            <a class="nav-link" href="#" id="logout-btn">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Feature Management</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="refresh-btn">
                            <i class="fas fa-sync-alt me-1"></i> Refresh
                        </button>
                    </div>
                </div>
                
                <!-- Alert container -->
                <div id="alert-container"></div>
                
                <!-- Feature management content -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div id="feature-list-container">
                                    <!-- Feature list will be loaded here -->
                                    <div class="text-center p-5">
                                        <div class="spinner-border text-primary" role="status"></div>
                                        <p class="mt-2">Loading features...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Feature Modal -->
                <div class="modal fade" id="featureModal" tabindex="-1" aria-labelledby="featureModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="featureModalLabel">Add/Edit Feature</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <form id="feature-form">
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <label for="feature-name" class="form-label">Feature Name (API Key)</label>
                                        <input type="text" class="form-control" id="feature-name" required>
                                        <div class="form-text">This is used in the API and should be unique. Use kebab-case (e.g., "advanced-search").</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="feature-display-name" class="form-label">Display Name</label>
                                        <input type="text" class="form-control" id="feature-display-name" required>
                                        <div class="form-text">This is shown to users and admins.</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="feature-description" class="form-label">Description</label>
                                        <textarea class="form-control" id="feature-description" rows="3"></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="feature-category" class="form-label">Category</label>
                                        <select class="form-select" id="feature-category" required>
                                            <option value="BASIC">Basic</option>
                                            <option value="COMMUNICATION">Communication</option>
                                            <option value="MATCHING">Matching</option>
                                            <option value="PREMIUM">Premium</option>
                                        </select>
                                    </div>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" role="switch" id="feature-active" checked>
                                        <label class="form-check-label" for="feature-active">Feature Active</label>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="submit" class="btn btn-primary">Save Feature</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Access Rules Modal -->
                <div class="modal fade" id="accessModal" tabindex="-1" aria-labelledby="accessModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="accessModalLabel">Edit Access Rules</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <form id="access-form">
                                <div class="modal-body">
                                    <div id="access-form-content">
                                        <!-- Access form content will be loaded here -->
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="submit" class="btn btn-primary">Save Access Rules</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Footer -->
                <footer class="pt-5 d-flex justify-content-between">
                    <span>Copyright © 2023 Vaivahik</span>
                    <ul class="nav m-0">
                        <li class="nav-item">
                            <a class="nav-link text-secondary" href="#">Privacy Policy</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-secondary" href="#">Terms of Use</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-secondary" href="#">Contact</a>
                        </li>
                    </ul>
                </footer>
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="js/admin-auth.js"></script>
    <script src="js/feature-management.js"></script>
</body>
</html>
