"""
Feature Processor for Matrimony Matching

This module provides functions for processing and normalizing features
for the matrimony matching system.
"""

import numpy as np
import json
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FeatureProcessor:
    """Feature processor for matrimony matching data"""
    
    def __init__(self, stats_file_path=None):
        """
        Initialize the feature processor
        
        Args:
            stats_file_path (str): Path to feature statistics file
        """
        self.feature_stats = {}
        self.categorical_mappings = {}
        
        # Load feature statistics if file exists
        if stats_file_path and os.path.exists(stats_file_path):
            self.load_feature_stats(stats_file_path)
        
        # Define feature groups
        self.numerical_features = ['age', 'height', 'income']
        self.categorical_features = [
            'gender', 'religion', 'caste', 'subCaste', 'gotra',
            'education', 'occupation', 'city', 'state', 'maritalStatus'
        ]
        self.preference_features = [
            'minAge', 'maxAge', 'minHeight', 'maxHeight', 'minIncome',
            'religion', 'caste', 'subCaste', 'education', 'occupation',
            'city', 'state', 'maritalStatus'
        ]
    
    def load_feature_stats(self, file_path):
        """
        Load feature statistics from file
        
        Args:
            file_path (str): Path to feature statistics file
        """
        try:
            with open(file_path, 'r') as f:
                stats_data = json.load(f)
                self.feature_stats = stats_data.get('numerical', {})
                self.categorical_mappings = stats_data.get('categorical', {})
                logger.info(f"Loaded feature statistics from {file_path}")
        except Exception as e:
            logger.error(f"Error loading feature statistics: {str(e)}")
    
    def save_feature_stats(self, file_path):
        """
        Save feature statistics to file
        
        Args:
            file_path (str): Path to save feature statistics
        """
        try:
            stats_data = {
                'numerical': self.feature_stats,
                'categorical': self.categorical_mappings
            }
            with open(file_path, 'w') as f:
                json.dump(stats_data, f, indent=2)
                logger.info(f"Saved feature statistics to {file_path}")
        except Exception as e:
            logger.error(f"Error saving feature statistics: {str(e)}")
    
    def compute_feature_stats(self, user_profiles):
        """
        Compute statistics for numerical features
        
        Args:
            user_profiles (list): List of user profile dictionaries
        """
        # Collect values for each numerical feature
        feature_values = {feature: [] for feature in self.numerical_features}
        
        for profile in user_profiles:
            for feature in self.numerical_features:
                if feature in profile and profile[feature] is not None:
                    feature_values[feature].append(float(profile[feature]))
        
        # Compute statistics
        for feature, values in feature_values.items():
            if values:
                self.feature_stats[feature] = {
                    'mean': np.mean(values),
                    'std': np.std(values) or 1.0,  # Use 1.0 if std is 0
                    'min': np.min(values),
                    'max': np.max(values)
                }
        
        # Create mappings for categorical features
        for feature in self.categorical_features:
            unique_values = set()
            for profile in user_profiles:
                if feature in profile and profile[feature] is not None:
                    unique_values.add(profile[feature])
            
            # Create mapping from value to index
            self.categorical_mappings[feature] = {
                value: idx for idx, value in enumerate(sorted(unique_values))
            }
    
    def normalize_numerical(self, value, feature):
        """
        Normalize a numerical feature value
        
        Args:
            value: The value to normalize
            feature (str): Feature name
            
        Returns:
            float: Normalized value
        """
        if feature not in self.feature_stats or value is None:
            return 0.0
        
        stats = self.feature_stats[feature]
        return (float(value) - stats['mean']) / stats['std']
    
    def encode_categorical(self, value, feature):
        """
        Encode a categorical feature value
        
        Args:
            value: The value to encode
            feature (str): Feature name
            
        Returns:
            dict: One-hot encoded values
        """
        if feature not in self.categorical_mappings or value is None:
            return {}
        
        mapping = self.categorical_mappings[feature]
        if value not in mapping:
            return {}
        
        # Create one-hot encoding
        encoding = {}
        for val, idx in mapping.items():
            encoding[f"{feature}_{val}"] = 1.0 if val == value else 0.0
        
        return encoding
    
    def process_user_profile(self, profile, preferences=None):
        """
        Process a user profile for the model
        
        Args:
            profile (dict): User profile data
            preferences (dict): User preferences
            
        Returns:
            dict: Processed features
        """
        processed = {}
        
        # Process numerical features
        for feature in self.numerical_features:
            if feature in profile and profile[feature] is not None:
                processed[feature] = self.normalize_numerical(profile[feature], feature)
            else:
                processed[feature] = 0.0
        
        # Process categorical features
        for feature in self.categorical_features:
            if feature in profile and profile[feature] is not None:
                processed.update(self.encode_categorical(profile[feature], feature))
        
        # Add preference features if available
        if preferences:
            for feature in self.preference_features:
                if feature in preferences and preferences[feature] is not None:
                    if feature.startswith('min') or feature.startswith('max'):
                        # Normalize min/max values
                        base_feature = feature[3:].lower()  # e.g., 'minAge' -> 'age'
                        if base_feature in self.feature_stats:
                            processed[feature] = self.normalize_numerical(preferences[feature], base_feature)
                    else:
                        # For categorical preferences, just indicate presence
                        processed[f"pref_{feature}"] = 1.0
        
        return processed
    
    def calculate_compatibility_features(self, user_profile, user_preferences, match_profile):
        """
        Calculate compatibility features between user and match
        
        Args:
            user_profile (dict): User profile data
            user_preferences (dict): User preferences
            match_profile (dict): Match profile data
            
        Returns:
            dict: Compatibility features
        """
        compatibility = {}
        
        # Age compatibility
        if 'minAge' in user_preferences and 'maxAge' in user_preferences and 'age' in match_profile:
            min_age = user_preferences['minAge']
            max_age = user_preferences['maxAge']
            match_age = match_profile['age']
            
            if match_age is not None and min_age is not None and max_age is not None:
                in_range = min_age <= match_age <= max_age
                compatibility['age_in_range'] = 1.0 if in_range else 0.0
                
                # How close to ideal (middle of range)
                ideal_age = (min_age + max_age) / 2
                age_distance = abs(match_age - ideal_age) / ((max_age - min_age) / 2) if max_age > min_age else 1.0
                compatibility['age_ideal_distance'] = 1.0 - min(age_distance, 1.0)
        
        # Height compatibility
        if 'minHeight' in user_preferences and 'maxHeight' in user_preferences and 'height' in match_profile:
            min_height = user_preferences['minHeight']
            max_height = user_preferences['maxHeight']
            match_height = match_profile['height']
            
            if match_height is not None and min_height is not None and max_height is not None:
                in_range = min_height <= match_height <= max_height
                compatibility['height_in_range'] = 1.0 if in_range else 0.0
        
        # Income compatibility
        if 'minIncome' in user_preferences and 'income' in match_profile:
            min_income = user_preferences['minIncome']
            match_income = match_profile['income']
            
            if match_income is not None and min_income is not None:
                meets_min = match_income >= min_income
                compatibility['income_meets_min'] = 1.0 if meets_min else 0.0
                
                # How much above minimum
                if min_income > 0:
                    income_ratio = match_income / min_income
                    compatibility['income_ratio'] = min(income_ratio, 5.0) / 5.0  # Cap at 5x minimum
        
        # Categorical feature matches
        for feature in self.categorical_features:
            if feature in user_preferences and feature in match_profile:
                pref_value = user_preferences[feature]
                match_value = match_profile[feature]
                
                if pref_value is not None and match_value is not None:
                    compatibility[f"{feature}_match"] = 1.0 if pref_value == match_value else 0.0
        
        return compatibility
    
    def process_match_pair(self, user_profile, user_preferences, match_profile):
        """
        Process a user-match pair for the model
        
        Args:
            user_profile (dict): User profile data
            user_preferences (dict): User preferences
            match_profile (dict): Match profile data
            
        Returns:
            tuple: Processed user features and match features
        """
        # Process user profile
        user_features = self.process_user_profile(user_profile, user_preferences)
        
        # Process match profile
        match_features = self.process_user_profile(match_profile)
        
        # Add compatibility features to match features
        compatibility = self.calculate_compatibility_features(user_profile, user_preferences, match_profile)
        match_features.update(compatibility)
        
        return user_features, match_features
