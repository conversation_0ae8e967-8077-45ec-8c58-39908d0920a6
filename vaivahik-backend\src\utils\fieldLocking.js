/**
 * Field Locking Utilities
 *
 * This module provides utilities for locking critical user fields that should not be changed
 * after initial submission, such as gender, date of birth, and birth details.
 */

/**
 * List of fields that should be locked once set
 * These fields are critical for matching and kundali generation
 */
const LOCKED_FIELDS = [
  'gender',
  'dateOfBirth',
  'birthTime',
  'birthPlace'
];

/**
 * Checks if a field should be locked
 * @param {string} fieldName - The name of the field to check
 * @returns {boolean} - Whether the field should be locked
 */
const isLockedField = (fieldName) => {
  return LOCKED_FIELDS.includes(fieldName);
};

/**
 * Checks if a user is attempting to change locked fields
 * @param {Object} existingData - The existing user data
 * @param {Object} newData - The new data being submitted
 * @returns {Object} - Object with locked fields that are being changed
 */
const getLockedFieldChanges = (existingData, newData) => {
  const lockedChanges = {};

  // Check each locked field
  LOCKED_FIELDS.forEach(field => {
    // Skip if the field doesn't exist in the new data
    if (!(field in newData)) {
      return;
    }

    // Skip if the field doesn't exist in the existing data (first-time setting)
    if (!(field in existingData) || existingData[field] === null || existingData[field] === undefined) {
      return;
    }

    // For date fields, compare as strings to avoid time zone issues
    if (field === 'dateOfBirth' || field === 'birthTime') {
      const existingValue = existingData[field] instanceof Date
        ? existingData[field].toISOString().split('T')[0]
        : String(existingData[field]);

      const newValue = newData[field] instanceof Date
        ? newData[field].toISOString().split('T')[0]
        : String(newData[field]);

      if (existingValue !== newValue) {
        lockedChanges[field] = {
          existing: existingValue,
          attempted: newValue
        };
      }
    }
    // For other fields, compare directly
    else if (existingData[field] !== newData[field]) {
      lockedChanges[field] = {
        existing: existingData[field],
        attempted: newData[field]
      };
    }
  });

  return lockedChanges;
};

/**
 * Removes locked fields from update data
 * @param {Object} updateData - The data to be updated
 * @returns {Object} - The update data with locked fields removed
 */
const removeLockedFields = (updateData) => {
  const cleanedData = { ...updateData };

  LOCKED_FIELDS.forEach(field => {
    if (field in cleanedData) {
      delete cleanedData[field];
    }
  });

  return cleanedData;
};

/**
 * Generates error messages for attempted changes to locked fields
 * @param {Object} lockedChanges - Object with locked fields that are being changed
 * @returns {Object} - Object with field names and error messages
 */
const generateLockedFieldErrors = (lockedChanges) => {
  const errors = {};

  Object.keys(lockedChanges).forEach(field => {
    let fieldName = field;

    // Format field name for error message
    switch (field) {
      case 'dateOfBirth':
        fieldName = 'Date of Birth';
        break;
      case 'birthTime':
        fieldName = 'Birth Time';
        break;
      case 'birthPlace':
        fieldName = 'Birth Place';
        break;
      case 'gender':
        fieldName = 'Gender';
        break;
    }

    errors[field] = `${fieldName} cannot be changed once set. This field is locked for profile integrity.`;
  });

  return errors;
};

/**
 * Checks if critical fields are already set in the user profile
 * @param {Object} profile - The user profile
 * @returns {Object} - Object with field names and whether they are set
 */
const getCriticalFieldStatus = (profile) => {
  if (!profile) {
    return {
      gender: false,
      dateOfBirth: false,
      birthTime: false,
      birthPlace: false
    };
  }

  return {
    gender: Boolean(profile.gender),
    dateOfBirth: Boolean(profile.dateOfBirth),
    birthTime: Boolean(profile.birthTime),
    birthPlace: Boolean(profile.birthPlace)
  };
};

module.exports = {
  LOCKED_FIELDS,
  isLockedField,
  getLockedFieldChanges,
  removeLockedFields,
  generateLockedFieldErrors,
  getCriticalFieldStatus
};
