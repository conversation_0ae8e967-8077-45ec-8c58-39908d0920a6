import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import dynamic from 'next/dynamic';
import { toast } from 'react-toastify';

// Import EnhancedAdminLayout with <PERSON> disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);
import { adminGet, adminPost, adminPut, adminDelete } from '@/services/adminApiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  InputAdornment,
  Card,
  CardContent,
  Grid,
  Tooltip,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Rating,
  Avatar,
  Stack,
  Divider,
  CardMedia
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Search as SearchIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  FilterList as FilterListIcon,
  PhotoCamera as PhotoIcon,
  Favorite as FavoriteIcon,
  CalendarToday as CalendarIcon,
  LocationOn as LocationIcon,
  TrendingUp as TrendingUpIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

export default function SuccessStoriesManagement() {
  const router = useRouter();
  const [stories, setStories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [storyToDelete, setStoryToDelete] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [currentStory, setCurrentStory] = useState(null);
  const [filters, setFilters] = useState({
    status: '',
    isPublic: ''
  });
  const [formData, setFormData] = useState({
    user1Name: '',
    user2Name: '',
    status: 'MARRIED',
    storyText: '',
    testimonyText: '',
    rating: 5,
    marriageDate: null,
    engagementDate: null,
    isPublic: true,
    photos: []
  });
  const [stats, setStats] = useState({
    totalStories: 0,
    publishedStories: 0,
    marriedCouples: 0,
    averageRating: 0
  });

  // Status options
  const statusOptions = [
    { value: 'MATCHED', label: 'Matched', color: 'info' },
    { value: 'TALKING', label: 'Talking', color: 'primary' },
    { value: 'MET', label: 'Met', color: 'secondary' },
    { value: 'ENGAGED', label: 'Engaged', color: 'warning' },
    { value: 'MARRIED', label: 'Married', color: 'success' }
  ];

  // Fetch success stories
  const fetchStories = async () => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams();
      queryParams.append('page', page + 1);
      queryParams.append('limit', rowsPerPage);

      if (searchQuery) queryParams.append('search', searchQuery);
      if (filters.status) queryParams.append('status', filters.status);
      if (filters.isPublic !== '') queryParams.append('isPublic', filters.isPublic);

      const response = await adminGet(`${ADMIN_ENDPOINTS.SUCCESS_STORIES}?${queryParams}`);

      if (response.success) {
        setStories(response.stories || []);
        setTotalCount(response.pagination?.totalStories || 0);
        setError(null);
      } else {
        setError('Failed to fetch success stories');
        toast.error('Failed to fetch success stories');
      }
    } catch (err) {
      console.error('Error fetching success stories:', err);
      setError('An error occurred while fetching success stories');
      toast.error('Error fetching success stories');
    } finally {
      setLoading(false);
    }
  };

  // Fetch statistics
  const fetchStats = async () => {
    try {
      const response = await adminGet(`${ADMIN_ENDPOINTS.SUCCESS_STORIES}/stats/overview`);
      if (response.success) {
        setStats(response.stats || {});
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  useEffect(() => {
    fetchStories();
    fetchStats();
  }, [page, rowsPerPage, searchQuery, filters]);

  // Handle create story
  const handleCreateStory = () => {
    setCurrentStory(null);
    setFormData({
      user1Name: '',
      user2Name: '',
      status: 'MARRIED',
      storyText: '',
      testimonyText: '',
      rating: 5,
      marriageDate: null,
      engagementDate: null,
      isPublic: true,
      photos: []
    });
    setDialogOpen(true);
  };

  // Handle edit story
  const handleEditStory = (story) => {
    setCurrentStory(story);
    setFormData({
      user1Name: story.user1Name || '',
      user2Name: story.user2Name || '',
      status: story.status || 'MARRIED',
      storyText: story.storyText || '',
      testimonyText: story.testimonyText || '',
      rating: story.rating || 5,
      marriageDate: story.marriageDate ? new Date(story.marriageDate) : null,
      engagementDate: story.engagementDate ? new Date(story.engagementDate) : null,
      isPublic: story.isPublic !== undefined ? story.isPublic : true,
      photos: story.photos || []
    });
    setDialogOpen(true);
  };

  // Handle save story
  const handleSaveStory = async () => {
    try {
      const storyData = {
        ...formData,
        marriageDate: formData.marriageDate ? formData.marriageDate.toISOString() : null,
        engagementDate: formData.engagementDate ? formData.engagementDate.toISOString() : null
      };

      let response;
      if (currentStory) {
        response = await adminPut(`${ADMIN_ENDPOINTS.SUCCESS_STORIES}/${currentStory.id}`, storyData);
      } else {
        response = await adminPost(ADMIN_ENDPOINTS.SUCCESS_STORIES, storyData);
      }

      if (response.success) {
        toast.success(`Success story ${currentStory ? 'updated' : 'created'} successfully`);
        setDialogOpen(false);
        fetchStories();
        fetchStats();
      } else {
        toast.error(response.message || 'Failed to save success story');
      }
    } catch (error) {
      console.error('Error saving success story:', error);
      toast.error('Error saving success story');
    }
  };

  // Get status chip
  const getStatusChip = (status) => {
    const statusConfig = statusOptions.find(opt => opt.value === status) || statusOptions[0];
    return (
      <Chip
        label={statusConfig.label}
        color={statusConfig.color}
        size="small"
        variant="outlined"
      />
    );
  };

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle search
  const handleSearch = (event) => {
    setSearchQuery(event.target.value);
    setPage(0);
  };

  // Open delete confirmation dialog
  const handleDeleteClick = (story) => {
    setStoryToDelete(story);
    setDeleteDialogOpen(true);
  };

  // Close delete confirmation dialog
  const handleDeleteCancel = () => {
    setStoryToDelete(null);
    setDeleteDialogOpen(false);
  };

  // Delete success story
  const handleDeleteConfirm = async () => {
    if (!storyToDelete) return;

    try {
      const response = await adminDelete(`${ADMIN_ENDPOINTS.SUCCESS_STORIES}/${storyToDelete.id}`);

      if (response.success) {
        toast.success('Success story deleted successfully');
        setDeleteDialogOpen(false);
        setStoryToDelete(null);
        fetchStories();
        fetchStats();
      } else {
        setError('Failed to delete success story');
      }
    } catch (err) {
      console.error('Error deleting success story:', err);
      setError('An error occurred while deleting the success story');
    } finally {
      setStoryToDelete(null);
      setDeleteDialogOpen(false);
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <EnhancedAdminLayout title="Success Stories">
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Success Stories
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleCreateStory}
          >
            Add New Story
          </Button>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Total Stories
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.totalStories || 0}
                    </Typography>
                  </Box>
                  <TrendingUpIcon color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Published
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.publishedStories || 0}
                    </Typography>
                  </Box>
                  <VisibilityIcon color="success" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Married Couples
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.marriedCouples || 0}
                    </Typography>
                  </Box>
                  <FavoriteIcon color="error" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Avg Rating
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.averageRating?.toFixed(1) || '0.0'}
                    </Typography>
                  </Box>
                  <StarIcon color="warning" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Filters and Search */}
        <Paper sx={{ mb: 3, p: 2 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                label="Search Stories"
                variant="outlined"
                size="small"
                fullWidth
                value={searchQuery}
                onChange={handleSearch}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  label="Status"
                  onChange={(e) => setFilters({ ...filters, status: e.target.value })}
                >
                  <MenuItem value="">All Status</MenuItem>
                  {statusOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Visibility</InputLabel>
                <Select
                  value={filters.isPublic}
                  label="Visibility"
                  onChange={(e) => setFilters({ ...filters, isPublic: e.target.value })}
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="true">Public</MenuItem>
                  <MenuItem value="false">Private</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                variant="outlined"
                fullWidth
                onClick={() => setFilters({ status: '', isPublic: '' })}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </Paper>

        <Paper>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : stories.length === 0 ? (
            <Alert severity="info">
              No success stories found. Create your first success story by clicking the "Add New Story" button.
            </Alert>
          ) : (
            <>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>ID</TableCell>
                      <TableCell>Couple</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Rating</TableCell>
                      <TableCell>Marriage Date</TableCell>
                      <TableCell>Visibility</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {stories.map((story) => (
                      <TableRow key={story.id}>
                        <TableCell>
                          <Typography variant="body2" color="text.secondary">
                            #{story.id?.slice(-6)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
                              <FavoriteIcon fontSize="small" />
                            </Avatar>
                            <Box>
                              <Typography variant="body2" fontWeight="600">
                                {story.user1Name} & {story.user2Name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {formatDate(story.createdAt)}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          {getStatusChip(story.status)}
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <Rating value={story.rating || 0} readOnly size="small" />
                            <Typography variant="body2">({story.rating || 0})</Typography>
                          </Box>
                        </TableCell>
                        <TableCell>{formatDate(story.marriageDate)}</TableCell>
                        <TableCell>
                          <Chip
                            label={story.isPublic ? 'Public' : 'Private'}
                            color={story.isPublic ? 'success' : 'default'}
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>{formatDate(story.createdAt)}</TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', gap: 0.5 }}>
                            <Tooltip title="View">
                              <IconButton size="small" color="info">
                                <VisibilityIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Edit">
                              <IconButton size="small" color="primary" onClick={() => handleEditStory(story)}>
                                <EditIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Delete">
                              <IconButton size="small" color="error" onClick={() => handleDeleteClick(story)}>
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              <TablePagination
                rowsPerPageOptions={[5, 10, 25]}
                component="div"
                count={totalCount}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
              />
            </>
          )}
        </Paper>
      </Box>

      {/* Create/Edit Success Story Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {currentStory ? 'Edit Success Story' : 'Create New Success Story'}
        </DialogTitle>
        <DialogContent>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="First Partner Name"
                  value={formData.user1Name}
                  onChange={(e) => setFormData({ ...formData, user1Name: e.target.value })}
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Second Partner Name"
                  value={formData.user2Name}
                  onChange={(e) => setFormData({ ...formData, user2Name: e.target.value })}
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={formData.status}
                    label="Status"
                    onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                  >
                    {statusOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <Box>
                  <Typography component="legend" variant="body2" sx={{ mb: 1 }}>
                    Rating
                  </Typography>
                  <Rating
                    value={formData.rating}
                    onChange={(event, newValue) => {
                      setFormData({ ...formData, rating: newValue });
                    }}
                  />
                </Box>
              </Grid>
              <Grid item xs={12} md={6}>
                <DatePicker
                  label="Engagement Date"
                  value={formData.engagementDate}
                  onChange={(newValue) => setFormData({ ...formData, engagementDate: newValue })}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <DatePicker
                  label="Marriage Date"
                  value={formData.marriageDate}
                  onChange={(newValue) => setFormData({ ...formData, marriageDate: newValue })}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="Success Story"
                  value={formData.storyText}
                  onChange={(e) => setFormData({ ...formData, storyText: e.target.value })}
                  placeholder="Tell us about their journey..."
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Testimony"
                  value={formData.testimonyText}
                  onChange={(e) => setFormData({ ...formData, testimonyText: e.target.value })}
                  placeholder="What they say about the platform..."
                />
              </Grid>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isPublic}
                      onChange={(e) => setFormData({ ...formData, isPublic: e.target.checked })}
                    />
                  }
                  label="Make this story public"
                />
              </Grid>
            </Grid>
          </LocalizationProvider>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSaveStory} variant="contained">
            {currentStory ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Delete Success Story</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the success story of "{storyToDelete?.user1Name} & {storyToDelete?.user2Name}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </EnhancedAdminLayout>
  );
}
