/**
 * API Routes Index
 *
 * This file serves as the central point for all API routes.
 * It organizes routes into logical groups and applies common middleware.
 */

const express = require('express');
const router = express.Router();

// Import route modules
const authRoutes = require('./auth');
const userRoutes = require('./users');
const profileRoutes = require('./profiles');
const matchRoutes = require('./matches');
const messageRoutes = require('./messages');
const notificationRoutes = require('./notifications');
const searchRoutes = require('./search');
const subscriptionRoutes = require('./subscriptions');
const paymentRoutes = require('./payments');
const documentRoutes = require('./documents');
const reportRoutes = require('./reports');
const adminRoutes = require('./admin');
const interestRoutes = require('./interests');
const verificationRoutes = require('./verification');
const privacyRoutes = require('./privacySettings');
const activityRoutes = require('./activities');
const shortlistRoutes = require('./shortlist');
const docsRoutes = require('./docs');

// Import middleware
const { authenticateToken } = require('../middleware/auth');
const { trackActivity } = require('../middleware/activityTracker');

// API version prefix
const API_VERSION = '/v1';

// Mount route modules with version prefix
router.use(`${API_VERSION}/auth`, authRoutes);
router.use(`${API_VERSION}/users`, userRoutes);
router.use(`${API_VERSION}/profiles`, profileRoutes);
router.use(`${API_VERSION}/matches`, authenticateToken, matchRoutes);
router.use(`${API_VERSION}/messages`, authenticateToken, messageRoutes);
router.use(`${API_VERSION}/notifications`, authenticateToken, notificationRoutes);
router.use(`${API_VERSION}/search`, authenticateToken, searchRoutes);
router.use(`${API_VERSION}/subscriptions`, authenticateToken, subscriptionRoutes);
router.use(`${API_VERSION}/payments`, authenticateToken, paymentRoutes);
router.use(`${API_VERSION}/documents`, authenticateToken, documentRoutes);
router.use(`${API_VERSION}/reports`, authenticateToken, reportRoutes);
router.use(`${API_VERSION}/admin`, adminRoutes);
router.use(`${API_VERSION}/interests`, authenticateToken, trackActivity, interestRoutes);
router.use(`${API_VERSION}/verification`, authenticateToken, trackActivity, verificationRoutes);
router.use(`${API_VERSION}/privacy-settings`, authenticateToken, trackActivity, privacyRoutes);
router.use(`${API_VERSION}/activities`, authenticateToken, trackActivity, activityRoutes);
router.use(`${API_VERSION}/shortlist`, authenticateToken, trackActivity, shortlistRoutes);
router.use(`${API_VERSION}/docs`, docsRoutes);

// Health check endpoint
router.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  });
});

// API documentation redirect
router.get('/docs', (req, res) => {
  res.redirect('/api-docs');
});

module.exports = router;
