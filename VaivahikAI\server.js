// File: VaivahikAI/server.js

// Import necessary modules
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const cookieParser = require('cookie-parser');
const path = require('path');
const { PrismaClient, Prisma } = require('@prisma/client');
const { createServer } = require('http'); // Needed for Socket.IO
const { Server } = require('socket.io');  // Needed for Socket.IO
const { initializeChatHandlers } = require('./src/socket/chatHandler'); // Import chat handlers
require('dotenv').config();

// --- Initialize Prisma Client ---
const prisma = new PrismaClient({
    // log: ['query','info','warn','error'], // Uncomment for detailed logs
});

// --- Configuration ---
const PORT = process.env.PORT || 8001; // Changed from 8000 to 8001 to avoid port conflict
// Define the origin for your frontend. Use '*' or 'true' for dev ONLY. Be specific in production.
const FRONTEND_ORIGIN = process.env.FRONTEND_URL || `http://localhost:${PORT}`; // Default to same origin/port if not set

// --- Initialize Express App & HTTP Server for Socket.IO ---
const app = express();
const httpServer = createServer(app); // Use http server
const io = new Server(httpServer, {   // Attach Socket.IO to http server
    cors: {
        origin: FRONTEND_ORIGIN, // Allow frontend origin for Socket.IO
        methods: ["GET", "POST"],
        credentials: true
    }
});

// Initialize chat socket handlers
initializeChatHandlers(io, prisma);


// --- Core Middleware ---

// CORS: allow configured origin (or 'true' for dev flexibility)
app.use(cors({
    origin: true, // Allows requests from the origin the browser is on (useful for dev, file://)
    methods: ['GET','POST','PUT','PATCH','DELETE','OPTIONS'],
    allowedHeaders: ['Content-Type','Authorization'],
    credentials: true
}));

// Basic security headers
app.use(helmet());

// *** THIS IS THE CORRECT BLOCK TO USE ***
// relaxed CSP for development - consider tightening for production

app.use((req, res, next) => {
  res.setHeader(
      "Content-Security-Policy",
      "default-src 'self' data: https:; " +
      "img-src 'self' data: https: blob: placehold.co; " +
      // Added https://cdn.datatables.net here: V V V
      "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://code.jquery.com https://cdn.datatables.net; " +
      // Added https://cdn.datatables.net here: V V V
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://cdn.datatables.net; " +
      "font-src 'self' https://fonts.gstatic.com; " +
      "connect-src *"
  );
  next();
});
// *** END OF CORRECT BLOCK ***

// Only apply MCP middleware in production (Example)
if (process.env.NODE_ENV === 'production') {
    // ensure you have: npm install mcp
    // const { mcpMiddleware } = require('mcp');
    // app.use(mcpMiddleware({
    //   // production MCP config here
    // }));
    console.log("Running in production mode."); // Simple indicator
}

// JSON body parsing & cookie parsing
app.use(express.json());
app.use(cookieParser());


// --- Static File Serving ---

// User uploads (e.g. profile photos)
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Admin API routes are handled by '/api/admin' - no static admin UI files are served from this backend anymore
// The admin UI is now fully implemented in the Next.js frontend


// Public SPA (Single Page Application) - Serve from 'public' directory
app.use(express.static(path.join(__dirname, 'public')));
// Fallback for SPA routing (requests not matching /api or /uploads go to index.html)
app.get('*', (req, res, next) => {
    if (req.path.startsWith('/api') ||
        req.path.startsWith('/uploads')) {
        return next(); // Skip SPA fallback for API and uploads routes
    }
    // Serve the main index.html for SPA routes
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});


// Attach Prisma Client to request object
app.use((req, res, next) => {
    req.prisma = prisma;
    next();
});


// --- API Routes ---
const userRoutes = require('./src/routes/user.routes.js');
const adminRoutes = require('./src/routes/admin.routes.js');
const analyticsRoutes = require('./src/routes/analytics');
const similarityRoutes = require('./src/routes/similarity.routes.js');
const docsRoutes = require('./src/routes/docs.js');
app.use('/api/users', userRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/similarity', similarityRoutes);
app.use('/api/docs', docsRoutes);

// Placeholder OTP auth endpoints
app.post('/api/auth/send-otp', (req, res) => {
    const { phone } = req.body;
    if (!phone) {
        return res.status(400).json({ success:false, message:'Phone number is required.' });
    }
    // TODO: Implement actual OTP generation and sending (e.g., Twilio)
    console.log(`(Simulated) OTP would be sent to ${phone}`);
    res.json({ success:true, message:'OTP sent successfully (Simulated).' });
});
app.post('/api/auth/verify-otp', (req, res) => {
    const { phone, otp } = req.body;
    if (!phone || !otp) {
        return res.status(400).json({ success:false, message:'Phone and OTP are required.' });
    }
    // TODO: Implement actual OTP verification
    console.log(`(Simulated) Verifying OTP ${otp} for ${phone}`);
    // For simulation, assume OTP '123456' is always correct
    if (otp === '123456') {
        // In a real app, you'd find/create user, generate JWT token here
        res.json({ success:true, message:'OTP verified successfully (Simulated).', token: 'dummy-jwt-token-for-simulation' });
    } else {
        res.status(401).json({ success:false, message:'Invalid OTP (Simulated).' });
    }
});


// --- 404 Not Found Handler ---
// This should come after all valid routes and static file handlers
app.use((req, res, next) => {
    res.status(404).json({ message:`Resource not found: ${req.method} ${req.originalUrl}` });
});


// --- Global Error Handler ---
// Must have 4 arguments to be recognized as an error handler by Express
app.use((err, req, res, next) => {
    console.error('Global Error Handler Caught:', err.message);
    if (process.env.NODE_ENV === 'development') {
        console.error(err.stack); // Log stack trace only in development
    }

    // Determine status code
    const status = (typeof err.status === 'number' && err.status >=100 && err.status<600)
                   ? err.status
                   : 500; // Default to 500 Internal Server Error

    // Handle specific Prisma errors if needed
    if (err instanceof Prisma.PrismaClientValidationError) {
        console.error("Prisma Validation Error Details:", err.message);
        return res.status(400).json({ // Bad Request for validation errors
            message:'Invalid request data provided.',
            error:'PrismaValidationError',
            details: err.message // Provide Prisma's message for debugging
        });
    }
    // Add handling for other specific error types if needed (e.g., PrismaClientKnownRequestError)

    // Generic JSON response
    res.status(status).json({
        message: err.message || 'An unexpected server error occurred.',
        // Include stack trace only in development for security
        ...(process.env.NODE_ENV === 'development' && { stack: err.stack }),
        // Include validation errors if passed from express-validator or similar
        ...(err.errors && { errors: err.errors })
    });
});


// --- Graceful Shutdown ---
let serverInstance; // Will hold the httpServer instance

const gracefulShutdown = async (signal) => {
    console.log(`\nReceived ${signal}. Initiating graceful shutdown...`);
    // Stop accepting new connections
    if (httpServer) { // Check if httpServer is initialized
         // Close Socket.IO connections first if necessary (optional, depends on needs)
        io.close((err) => {
            if (err) {
                console.error('Error closing Socket.IO:', err);
            } else {
                console.log('Socket.IO server closed.');
            }

             // Now close the HTTP server
            httpServer.close(async () => {
                console.log('HTTP server closed.');
                // Disconnect Prisma Client
                try {
                    await prisma.$disconnect();
                    console.log('Prisma Client disconnected.');
                } catch (disconnectErr) {
                    console.error('Error disconnecting Prisma:', disconnectErr);
                } finally {
                     process.exit(0); // Exit process
                 }
            });
        });

        // Set a timeout for forceful shutdown if graceful shutdown takes too long
        setTimeout(() => {
            console.error('Graceful shutdown timed out. Forcing exit.');
            process.exit(1);
        }, 10000); // 10 seconds timeout

    } else {
        // If server didn't start, just disconnect Prisma
        try {
            await prisma.$disconnect();
            console.log('Prisma Client disconnected (server was not running).');
        } catch (disconnectErr) {
            console.error('Error disconnecting Prisma (server not running):', disconnectErr);
        } finally {
            process.exit(0);
        }
    }
};

// Listen for termination signals
process.on('SIGINT', () => gracefulShutdown('SIGINT')); // Ctrl+C
process.on('SIGTERM', () => gracefulShutdown('SIGTERM')); // Kill command


// Import scheduled tasks
const { initScheduledTasks } = require('./src/tasks/scheduledTasks');

// Import feature seed script
const seedFeatures = require('./prisma/seed-features');

// --- Start Server ---
const startServer = async () => {
    try {
        // Connect Prisma Client
        await prisma.$connect();
        console.log('Prisma Client connected successfully.');

        // Seed features and access rules if needed
        if (process.env.SEED_FEATURES === 'true') {
            try {
                console.log('Seeding features and access rules...');
                await seedFeatures.main();
                console.log('Features and access rules seeded successfully.');
            } catch (seedError) {
                console.error('Error seeding features:', seedError);
                // Continue with server startup even if seeding fails
            }
        }

        // Initialize scheduled tasks
        initScheduledTasks();
        console.log('Scheduled tasks initialized.');

        // Start HTTP server (which includes Express app and Socket.IO)
        // Store the instance for graceful shutdown
        // serverInstance = httpServer.listen(PORT, () => { // <-- Assign instance here if needed, but httpServer is already accessible
        httpServer.listen(PORT, () => {
            console.log(`Server running successfully on http://localhost:${PORT}`);
            console.log(`API base URL: http://localhost:${PORT}/api`);
            console.log(`Socket.IO listening on port ${PORT}`);
        });

    } catch (error) {
        console.error('Failed to connect Prisma Client or start server:', error);
        try {
            await prisma.$disconnect(); // Attempt disconnect even on startup failure
        } catch (disconnectErr) {
            console.error('Error disconnecting Prisma on startup failure:', disconnectErr);
        }
        process.exit(1); // Exit with error code
    }
};

// Run the server start function
startServer();