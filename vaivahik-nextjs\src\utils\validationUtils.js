/**
 * Validation Utilities
 *
 * This module provides utilities for validating form inputs with detailed error messages.
 */

/**
 * Validation rules for common fields
 */
export const VALIDATION_RULES = {
  // Basic Details
  FULL_NAME: {
    required: true,
    minLength: 3,
    maxLength: 100,
    pattern: /^[A-Za-z\s.'-]+$/,
    messages: {
      required: 'Full name is required',
      minLength: 'Full name must be at least 3 characters',
      maxLength: 'Full name cannot exceed 100 characters',
      pattern: 'Full name can only contain letters, spaces, and characters like . \' -'
    }
  },

  GENDER: {
    required: true,
    options: ['Male', 'Female'],
    messages: {
      required: 'Gender is required',
      options: 'Please select a valid gender'
    }
  },

  DATE_OF_BIRTH: {
    required: true,
    minAge: {
      Male: 21,
      Female: 18
    },
    maxAge: 80,
    messages: {
      required: 'Date of birth is required',
      minAge: (gender) => `Minimum age for ${gender.toLowerCase()} is ${VALIDATION_RULES.DATE_OF_BIRTH.minAge[gender]} years`,
      maxAge: 'Maximum age is 80 years',
      invalid: 'Please enter a valid date'
    }
  },

  HEIGHT: {
    required: true,
    min: 4.5,
    max: 6.5,
    messages: {
      required: 'Height is required',
      min: 'Height must be at least 4.5 feet',
      max: 'Height cannot exceed 6.5 feet',
      invalid: 'Please enter a valid height in feet (e.g., 5.8)'
    }
  },

  // Contact Details
  EMAIL: {
    required: true,
    pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
    messages: {
      required: 'Email is required',
      pattern: 'Please enter a valid email address'
    }
  },

  PHONE: {
    required: true,
    pattern: /^[+]?[0-9]{10,15}$/,
    messages: {
      required: 'Phone number is required',
      pattern: 'Please enter a valid phone number (10-15 digits)'
    }
  },

  // Location Details
  CITY: {
    required: true,
    messages: {
      required: 'City is required'
    }
  },

  STATE: {
    required: true,
    messages: {
      required: 'State is required'
    }
  },

  PINCODE: {
    pattern: /^\d{6}$/,
    messages: {
      pattern: 'Pincode must be 6 digits'
    }
  },

  // Education & Career
  EDUCATION: {
    required: true,
    messages: {
      required: 'Education is required'
    }
  },

  OCCUPATION: {
    required: true,
    messages: {
      required: 'Occupation is required'
    }
  },

  // Family Details
  FAMILY_TYPE: {
    required: true,
    options: ['Nuclear Family', 'Joint Family', 'Other'],
    messages: {
      required: 'Family type is required',
      options: 'Please select a valid family type'
    }
  },

  FAMILY_STATUS: {
    options: ['Middle Class', 'Upper Middle Class', 'Rich', 'Affluent'],
    messages: {
      options: 'Please select a valid family status'
    }
  },

  FATHER_NAME: {
    minLength: 3,
    maxLength: 100,
    messages: {
      minLength: 'Father\'s name must be at least 3 characters',
      maxLength: 'Father\'s name cannot exceed 100 characters'
    }
  },

  MOTHER_NAME: {
    minLength: 3,
    maxLength: 100,
    messages: {
      minLength: 'Mother\'s name must be at least 3 characters',
      maxLength: 'Mother\'s name cannot exceed 100 characters'
    }
  },

  SIBLINGS: {
    pattern: /^[0-9]+$/,
    messages: {
      pattern: 'Please enter a valid number'
    }
  },

  // Education & Career
  EDUCATION_FIELD: {
    options: ['Computer Science', 'Engineering', 'Medicine', 'Business', 'Arts', 'Science', 'Law', 'Commerce', 'Other'],
    messages: {
      options: 'Please select a valid education field'
    }
  },

  WORKING_WITH: {
    maxLength: 100,
    messages: {
      maxLength: 'Company name cannot exceed 100 characters'
    }
  },

  INCOME_RANGE: {
    options: ['Less than 3 LPA', '3-5 LPA', '5-7 LPA', '7-10 LPA', '10-15 LPA', '15-20 LPA', '20-30 LPA', 'Above 30 LPA'],
    messages: {
      options: 'Please select a valid income range'
    }
  },

  // Location Details
  PINCODE: {
    pattern: /^\d{6}$/,
    messages: {
      pattern: 'Pincode must be 6 digits'
    }
  },

  BIRTH_PLACE: {
    maxLength: 100,
    messages: {
      maxLength: 'Birth place cannot exceed 100 characters'
    }
  },

  // Lifestyle & Habits
  DIET: {
    options: ['Vegetarian', 'Non-Vegetarian', 'Eggetarian', 'Vegan', 'Jain'],
    messages: {
      options: 'Please select a valid diet preference'
    }
  },

  SMOKING: {
    options: ['No', 'Occasionally', 'Yes'],
    messages: {
      options: 'Please select a valid option'
    }
  },

  DRINKING: {
    options: ['No', 'Occasionally', 'Yes'],
    messages: {
      options: 'Please select a valid option'
    }
  },

  // Partner Preferences
  AGE_RANGE: {
    min: 18,
    max: 60,
    messages: {
      min: 'Minimum age must be at least 18 years',
      max: 'Maximum age cannot exceed 60 years'
    }
  },

  HEIGHT_RANGE: {
    min: 4.5,
    max: 6.5,
    messages: {
      min: 'Minimum height must be at least 4.5 feet',
      max: 'Maximum height cannot exceed 6.5 feet'
    }
  },

  // About Me
  ABOUT_ME: {
    required: true,
    minLength: 50,
    maxLength: 1000,
    messages: {
      required: 'About me is required',
      minLength: 'About me must be at least 50 characters',
      maxLength: 'About me cannot exceed 1000 characters'
    }
  },

  ABOUT_PARTNER: {
    maxLength: 1000,
    messages: {
      maxLength: 'About partner cannot exceed 1000 characters'
    }
  }
};

/**
 * Validate a single field
 * @param {string} name - Field name
 * @param {any} value - Field value
 * @param {Object} rules - Validation rules
 * @param {Object} formValues - All form values (for cross-field validation)
 * @returns {string|null} Error message or null if valid
 */
export const validateField = (name, value, rules, formValues = {}) => {
  if (!rules) {
    return null;
  }

  // Check if required
  if (rules.required && (value === undefined || value === null || value === '')) {
    return rules.messages.required;
  }

  // Skip other validations if value is empty and not required
  if (value === undefined || value === null || value === '') {
    return null;
  }

  // Check min length for strings
  if (rules.minLength && typeof value === 'string' && value.length < rules.minLength) {
    return rules.messages.minLength;
  }

  // Check max length for strings
  if (rules.maxLength && typeof value === 'string' && value.length > rules.maxLength) {
    return rules.messages.maxLength;
  }

  // Check pattern for strings
  if (rules.pattern && typeof value === 'string' && !rules.pattern.test(value)) {
    return rules.messages.pattern;
  }

  // Check options for enums
  if (rules.options && !rules.options.includes(value)) {
    return rules.messages.options;
  }

  // Check min value for numbers
  if (rules.min !== undefined && typeof value === 'number' && value < rules.min) {
    return rules.messages.min;
  }

  // Check max value for numbers
  if (rules.max !== undefined && typeof value === 'number' && value > rules.max) {
    return rules.messages.max;
  }

  // Special case for date of birth and age validation
  if (name === 'dateOfBirth' && rules.minAge) {
    const dob = new Date(value);
    if (isNaN(dob.getTime())) {
      return rules.messages.invalid;
    }

    const today = new Date();
    const age = today.getFullYear() - dob.getFullYear();
    const monthDiff = today.getMonth() - dob.getMonth();

    // Adjust age if birthday hasn't occurred yet this year
    const adjustedAge = (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate()))
      ? age - 1
      : age;

    const gender = formValues.gender || 'Male'; // Default to Male if not specified
    const minAge = rules.minAge[gender];

    if (adjustedAge < minAge) {
      return typeof rules.messages.minAge === 'function'
        ? rules.messages.minAge(gender)
        : rules.messages.minAge;
    }

    if (rules.maxAge && adjustedAge > rules.maxAge) {
      return rules.messages.maxAge;
    }
  }

  // Special case for height validation
  if (name === 'height' && rules.min && rules.max) {
    const height = parseFloat(value);
    if (isNaN(height)) {
      return rules.messages.invalid;
    }

    if (height < rules.min) {
      return rules.messages.min;
    }

    if (height > rules.max) {
      return rules.messages.max;
    }
  }

  return null;
};

/**
 * Validate an entire form
 * @param {Object} formValues - Form values
 * @param {Object} validationSchema - Schema with field names and rules
 * @returns {Object} Object with field names and error messages
 */
export const validateForm = (formValues, validationSchema) => {
  const errors = {};

  Object.keys(validationSchema).forEach(fieldName => {
    const rules = validationSchema[fieldName];
    const value = formValues[fieldName];

    const error = validateField(fieldName, value, rules, formValues);
    if (error) {
      errors[fieldName] = error;
    }
  });

  return errors;
};

export default {
  VALIDATION_RULES,
  validateField,
  validateForm
};
