/**
 * Modern Family Details Form
 *
 * A modern UI form for collecting family details as part of the profile completion process.
 * Uses the shared styled components for consistent UI across the application.
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  MenuItem,
  FormHelperText,
  CircularProgress,
  Alert,
  InputAdornment,
  Typography
} from '@mui/material';
import {
  People as FamilyIcon,
  Person as PersonIcon,
  Work as WorkIcon,
  Home as HomeIcon,
  Wc as GenderIcon
} from '@mui/icons-material';
import { validateField, VALIDATION_RULES } from '@/utils/validationUtils';
import { formatError, getUserFriendlyMessage, isValidationError } from '@/utils/errorHandling';
import {
  StyledPaper,
  StyledTextField,
  StyledSelect,
  StyledButton,
  StyledFormLabel,
  FloatingElement,
  FormSection,
  FormRow,
  StyledSectionTitle
} from '@/components/ui/ModernFormComponents';

// Constants for form options
const FAMILY_TYPES = ['Joint Family', 'Nuclear Family', 'Extended Family'];
const FAMILY_VALUES = ['Traditional', 'Moderate', 'Liberal'];
const FAMILY_STATUS = ['Middle Class', 'Upper Middle Class', 'Rich / Affluent', 'Business Class'];
const OCCUPATION_OPTIONS = ['Business', 'Service', 'Professional', 'Self Employed', 'Retired', 'Homemaker', 'Not Working', 'Deceased'];

const ModernFamilyDetailsForm = ({ userData, onSave, isLoading = false }) => {
  const [formData, setFormData] = useState({
    // Family Type & Values
    familyType: '',
    familyValues: '',
    familyStatus: '',

    // Parents
    fatherName: '',
    fatherOccupation: '',
    motherName: '',
    motherOccupation: '',

    // Siblings
    brothers: '0',
    marriedBrothers: '0',
    sisters: '0',
    marriedSisters: '0',

    // Extended Family & Cultural Background
    uncleName: '',
    motherTongue: '',
    kul: '',

    // Location & Contact
    familyLocation: '',
    familyContact: '',

    // About Family
    aboutFamily: ''
  });

  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  // Initialize form with user data if available
  useEffect(() => {
    if (userData?.familyDetails) {
      setFormData({
        ...formData,
        ...userData.familyDetails
      });
    }
  }, [userData]);

  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    const newFormData = {
      ...formData,
      [name]: value
    };

    setFormData(newFormData);

    // Mark field as touched
    setTouched({
      ...touched,
      [name]: true
    });

    // Validate field on change if it's been touched
    if (touched[name]) {
      const fieldError = validateSingleField(name, value);
      setErrors(prevErrors => {
        if (fieldError) {
          return { ...prevErrors, [name]: fieldError };
        } else {
          // Remove error if it exists
          const { [name]: _, ...restErrors } = prevErrors;
          return restErrors;
        }
      });
    }
  };

  // Validate a single field
  const validateSingleField = (name, value) => {
    let rule;

    switch (name) {
      case 'familyType':
        rule = VALIDATION_RULES.FAMILY_TYPE;
        break;
      case 'familyStatus':
        rule = VALIDATION_RULES.FAMILY_STATUS;
        break;
      case 'fatherName':
        rule = VALIDATION_RULES.FATHER_NAME;
        break;
      case 'motherName':
        rule = VALIDATION_RULES.MOTHER_NAME;
        break;
      case 'brothers':
      case 'marriedBrothers':
      case 'sisters':
      case 'marriedSisters':
        rule = VALIDATION_RULES.SIBLINGS;
        break;
      default:
        return null;
    }

    // Get basic validation error
    const error = validateField(name, value, rule, formData);
    if (error) return error;

    // Custom validations for siblings
    if (name === 'marriedBrothers' && parseInt(value) > parseInt(formData.brothers)) {
      return 'Married brothers cannot exceed total brothers';
    }

    if (name === 'marriedSisters' && parseInt(value) > parseInt(formData.sisters)) {
      return 'Married sisters cannot exceed total sisters';
    }

    return null;
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    // Required fields
    const requiredFields = ['fatherName', 'motherName', 'familyType', 'familyValues'];
    requiredFields.forEach(field => {
      if (!formData[field]) {
        newErrors[field] = `${field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())} is required`;
      }
    });

    // Validate sibling counts
    const brothers = parseInt(formData.brothers) || 0;
    const marriedBrothers = parseInt(formData.marriedBrothers) || 0;
    const sisters = parseInt(formData.sisters) || 0;
    const marriedSisters = parseInt(formData.marriedSisters) || 0;

    if (marriedBrothers > brothers) {
      newErrors.marriedBrothers = 'Married brothers cannot exceed total brothers';
    }

    if (marriedSisters > sisters) {
      newErrors.marriedSisters = 'Married sisters cannot exceed total sisters';
    }

    // Validate phone number format for family contact
    if (formData.familyContact && !/^[0-9]{10}$/.test(formData.familyContact)) {
      newErrors.familyContact = 'Please enter a valid 10-digit phone number';
    }

    // Validate all fields
    Object.keys(formData).forEach(fieldName => {
      if (!newErrors[fieldName]) { // Skip if already has error from required check
        const error = validateSingleField(fieldName, formData[fieldName]);
        if (error) {
          newErrors[fieldName] = error;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateForm()) {
      onSave(formData);
    }
  };

  return (
    <StyledPaper>
      <FloatingElement position="top-right" />
      <FloatingElement position="bottom-left" />

      <Box position="relative" zIndex={1}>
        <Typography
          variant="h4"
          align="center"
          gutterBottom
          sx={{
            fontFamily: 'var(--font-secondary)',
            background: 'var(--primary-gradient)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            fontWeight: 700,
            mb: 1
          }}
        >
          Family Details
        </Typography>

        <Typography variant="body1" align="center" paragraph sx={{ mb: 4, color: 'var(--text-color-medium)' }}>
          Tell us about your family to help find a compatible match
        </Typography>

        <form onSubmit={handleSubmit}>
          <FormSection title="Family Type & Values">
            <Grid container spacing={3}>
              <Grid item xs={12} sm={4}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <FamilyIcon fontSize="small" sx={{ mr: 1 }} />
                    Family Type*
                  </Box>
                </StyledFormLabel>
                <StyledSelect
                  name="familyType"
                  value={formData.familyType}
                  onChange={handleChange}
                  fullWidth
                  error={!!errors.familyType}
                  displayEmpty
                >
                  <MenuItem value="" disabled>Select Family Type</MenuItem>
                  {FAMILY_TYPES.map(option => (
                    <MenuItem key={option} value={option}>{option}</MenuItem>
                  ))}
                </StyledSelect>
                {errors.familyType && <FormHelperText error>{errors.familyType}</FormHelperText>}
              </Grid>

              <Grid item xs={12} sm={4}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <HomeIcon fontSize="small" sx={{ mr: 1 }} />
                    Family Values*
                  </Box>
                </StyledFormLabel>
                <StyledSelect
                  name="familyValues"
                  value={formData.familyValues}
                  onChange={handleChange}
                  fullWidth
                  error={!!errors.familyValues}
                  displayEmpty
                >
                  <MenuItem value="" disabled>Select Family Values</MenuItem>
                  {FAMILY_VALUES.map(option => (
                    <MenuItem key={option} value={option}>{option}</MenuItem>
                  ))}
                </StyledSelect>
                {errors.familyValues && <FormHelperText error>{errors.familyValues}</FormHelperText>}
              </Grid>

              <Grid item xs={12} sm={4}>
                <StyledFormLabel>Family Status</StyledFormLabel>
                <StyledSelect
                  name="familyStatus"
                  value={formData.familyStatus}
                  onChange={handleChange}
                  fullWidth
                  displayEmpty
                >
                  <MenuItem value="" disabled>Select Family Status</MenuItem>
                  {FAMILY_STATUS.map(option => (
                    <MenuItem key={option} value={option}>{option}</MenuItem>
                  ))}
                </StyledSelect>
              </Grid>
            </Grid>
          </FormSection>

          <FormSection title="Parents Information">
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <PersonIcon fontSize="small" sx={{ mr: 1 }} />
                    Father's Name*
                  </Box>
                </StyledFormLabel>
                <StyledTextField
                  name="fatherName"
                  value={formData.fatherName}
                  onChange={handleChange}
                  fullWidth
                  placeholder="Enter father's name"
                  error={!!errors.fatherName}
                  helperText={errors.fatherName}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>Father's Occupation</StyledFormLabel>
                <StyledSelect
                  name="fatherOccupation"
                  value={formData.fatherOccupation}
                  onChange={handleChange}
                  fullWidth
                  displayEmpty
                >
                  <MenuItem value="" disabled>Select Occupation</MenuItem>
                  {OCCUPATION_OPTIONS.map(option => (
                    <MenuItem key={option} value={option}>{option}</MenuItem>
                  ))}
                </StyledSelect>
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <PersonIcon fontSize="small" sx={{ mr: 1 }} />
                    Mother's Name*
                  </Box>
                </StyledFormLabel>
                <StyledTextField
                  name="motherName"
                  value={formData.motherName}
                  onChange={handleChange}
                  fullWidth
                  placeholder="Enter mother's name"
                  error={!!errors.motherName}
                  helperText={errors.motherName}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>Mother's Occupation</StyledFormLabel>
                <StyledSelect
                  name="motherOccupation"
                  value={formData.motherOccupation}
                  onChange={handleChange}
                  fullWidth
                  displayEmpty
                >
                  <MenuItem value="" disabled>Select Occupation</MenuItem>
                  {OCCUPATION_OPTIONS.map(option => (
                    <MenuItem key={option} value={option}>{option}</MenuItem>
                  ))}
                </StyledSelect>
              </Grid>
            </Grid>
          </FormSection>

          <FormSection title="Siblings Information">
            <Grid container spacing={3}>
              <Grid item xs={12} sm={3}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <GenderIcon fontSize="small" sx={{ mr: 1 }} />
                    Brothers
                  </Box>
                </StyledFormLabel>
                <StyledTextField
                  name="brothers"
                  value={formData.brothers}
                  onChange={handleChange}
                  fullWidth
                  type="number"
                  inputProps={{ min: 0 }}
                  error={!!errors.brothers}
                  helperText={errors.brothers}
                />
              </Grid>

              <Grid item xs={12} sm={3}>
                <StyledFormLabel>Married Brothers</StyledFormLabel>
                <StyledTextField
                  name="marriedBrothers"
                  value={formData.marriedBrothers}
                  onChange={handleChange}
                  fullWidth
                  type="number"
                  inputProps={{ min: 0 }}
                  error={!!errors.marriedBrothers}
                  helperText={errors.marriedBrothers}
                />
              </Grid>

              <Grid item xs={12} sm={3}>
                <StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <GenderIcon fontSize="small" sx={{ mr: 1 }} />
                    Sisters
                  </Box>
                </StyledFormLabel>
                <StyledTextField
                  name="sisters"
                  value={formData.sisters}
                  onChange={handleChange}
                  fullWidth
                  type="number"
                  inputProps={{ min: 0 }}
                  error={!!errors.sisters}
                  helperText={errors.sisters}
                />
              </Grid>

              <Grid item xs={12} sm={3}>
                <StyledFormLabel>Married Sisters</StyledFormLabel>
                <StyledTextField
                  name="marriedSisters"
                  value={formData.marriedSisters}
                  onChange={handleChange}
                  fullWidth
                  type="number"
                  inputProps={{ min: 0 }}
                  error={!!errors.marriedSisters}
                  helperText={errors.marriedSisters}
                />
              </Grid>
            </Grid>
          </FormSection>

          <FormSection title="Cultural Background">
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <StyledFormLabel>Uncle's Name</StyledFormLabel>
                <StyledTextField
                  name="uncleName"
                  value={formData.uncleName}
                  onChange={handleChange}
                  fullWidth
                  placeholder="Enter uncle's name (if applicable)"
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>Mother Tongue</StyledFormLabel>
                <StyledSelect
                  name="motherTongue"
                  value={formData.motherTongue}
                  onChange={handleChange}
                  fullWidth
                  displayEmpty
                >
                  <MenuItem value="">Select Mother Tongue</MenuItem>
                  <MenuItem value="Marathi">Marathi</MenuItem>
                  <MenuItem value="Hindi">Hindi</MenuItem>
                  <MenuItem value="English">English</MenuItem>
                  <MenuItem value="Gujarati">Gujarati</MenuItem>
                  <MenuItem value="Kannada">Kannada</MenuItem>
                  <MenuItem value="Tamil">Tamil</MenuItem>
                  <MenuItem value="Telugu">Telugu</MenuItem>
                  <MenuItem value="Other">Other</MenuItem>
                </StyledSelect>
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>Kul (Family/Clan Name)</StyledFormLabel>
                <StyledTextField
                  name="kul"
                  value={formData.kul}
                  onChange={handleChange}
                  fullWidth
                  placeholder="Enter your kul/clan name"
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <StyledFormLabel>Family Contact</StyledFormLabel>
                <StyledTextField
                  name="familyContact"
                  value={formData.familyContact}
                  onChange={handleChange}
                  fullWidth
                  placeholder="Alternative family contact number"
                />
              </Grid>
            </Grid>
          </FormSection>

          <FormSection title="Additional Information">
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <StyledFormLabel>Family Location</StyledFormLabel>
                <StyledTextField
                  name="familyLocation"
                  value={formData.familyLocation}
                  onChange={handleChange}
                  fullWidth
                  placeholder="Where does your family live?"
                />
              </Grid>

              <Grid item xs={12}>
                <StyledFormLabel>About Your Family</StyledFormLabel>
                <StyledTextField
                  name="aboutFamily"
                  value={formData.aboutFamily}
                  onChange={handleChange}
                  fullWidth
                  multiline
                  rows={4}
                  placeholder="Tell us more about your family background, values, and traditions..."
                />
              </Grid>
            </Grid>
          </FormSection>

          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
            <StyledButton
              type="submit"
              variant="contained"
              disabled={isLoading}
              startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : null}
            >
              {isLoading ? 'Saving...' : 'Save Family Details'}
            </StyledButton>
          </Box>
        </form>
      </Box>
    </StyledPaper>
  );
};

export default ModernFamilyDetailsForm;
