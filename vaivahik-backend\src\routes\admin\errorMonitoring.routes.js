/**
 * Error Monitoring Routes
 * 
 * These routes provide endpoints for monitoring and analyzing errors in the application.
 * They allow administrators to view error trends, search for specific errors,
 * and get detailed information about errors.
 */

const express = require('express');
const router = express.Router();
const { authenticateAdmin } = require('../../middleware/adminAuth.middleware');
const errorMonitoringController = require('../../controllers/admin/errorMonitoring.controller');

// All routes in this file require admin authentication
router.use(authenticateAdmin);

// Get error analytics overview
router.get('/overview', errorMonitoringController.getErrorOverview);

// Get errors by type
router.get('/by-type', errorMonitoringController.getErrorsByType);

// Get errors by endpoint
router.get('/by-endpoint', errorMonitoringController.getErrorsByEndpoint);

// Get errors by user
router.get('/by-user', errorMonitoringController.getErrorsByUser);

// Get recent errors
router.get('/recent', errorMonitoringController.getRecentErrors);

// Reset error statistics
router.post('/reset-stats', errorMonitoringController.resetErrorStats);

// Get Sentry issues (if Sentry is configured)
router.get('/sentry-issues', errorMonitoringController.getSentryIssues);

module.exports = router;
