import { adminGet } from '@/services/apiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';

/**
 * Chart Data Service
 * Provides functions to fetch chart data from the API
 */
const chartDataService = {
  /**
   * Get user growth data
   *
   * @param {string} period - Time period (week, month, year)
   * @param {Object} options - Additional options
   * @param {boolean} options.refresh - Whether to force refresh the data
   * @returns {Promise} Promise with user growth data
   */
  getUserGrowthData: async (period = 'month', options = {}) => {
    try {
      const params = {
        period,
        ...options
      };

      const response = await adminGet(`${ADMIN_ENDPOINTS.CHARTS}/user-growth`, params);
      return response;
    } catch (error) {
      console.error('Error fetching user growth data:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Get match rate data
   *
   * @param {string} period - Time period (week, month, year)
   * @param {Object} options - Additional options
   * @param {boolean} options.refresh - Whether to force refresh the data
   * @returns {Promise} Promise with match rate data
   */
  getMatchRateData: async (period = 'month', options = {}) => {
    try {
      const params = {
        period,
        ...options
      };

      const response = await adminGet(`${ADMIN_ENDPOINTS.CHARTS}/match-rate`, params);
      return response;
    } catch (error) {
      console.error('Error fetching match rate data:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Get gender distribution data
   *
   * @returns {Promise} Promise with gender distribution data
   */
  getGenderDistributionData: async () => {
    try {
      const response = await adminGet(`${ADMIN_ENDPOINTS.CHARTS}/gender-distribution`);
      return response;
    } catch (error) {
      console.error('Error fetching gender distribution data:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Get subscription trend data
   *
   * @param {string} period - Time period (week, month, year)
   * @returns {Promise} Promise with subscription trend data
   */
  getSubscriptionTrendData: async (period = 'month') => {
    try {
      const response = await adminGet(`${ADMIN_ENDPOINTS.CHARTS}/subscription-trend`, { period });
      return response;
    } catch (error) {
      console.error('Error fetching subscription trend data:', error);
      return { success: false, error: error.message };
    }
  }
};

export default chartDataService;
