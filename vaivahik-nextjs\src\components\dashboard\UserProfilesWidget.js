import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Avatar,
  Button,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  Pagination,
  styled
} from '@mui/material';
import {
  Search as SearchIcon,
  Favorite as HeartIcon,
  FavoriteBorder as HeartBorderIcon,
  Message as MessageIcon,
  Visibility as ViewIcon,
  Star as StarIcon,
  Verified as VerifiedIcon,
  WorkspacePremium as PremiumIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';

const ProfileCard = styled(Card)(({ theme }) => ({
  borderRadius: 20,
  overflow: 'hidden',
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  border: '2px solid rgba(255, 95, 109, 0.1)',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 20px 40px rgba(255, 95, 109, 0.2)',
    borderColor: '#FF5F6D'
  }
}));

const ActionButton = styled(IconButton)(({ theme, variant }) => ({
  width: 40,
  height: 40,
  borderRadius: '50%',
  transition: 'all 0.3s ease',
  ...(variant === 'heart' && {
    backgroundColor: 'rgba(233, 30, 99, 0.1)',
    color: '#E91E63',
    '&:hover': {
      backgroundColor: '#E91E63',
      color: 'white',
      transform: 'scale(1.1)'
    }
  }),
  ...(variant === 'message' && {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
    color: '#2196F3',
    '&:hover': {
      backgroundColor: '#2196F3',
      color: 'white',
      transform: 'scale(1.1)'
    }
  }),
  ...(variant === 'view' && {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    color: '#4CAF50',
    '&:hover': {
      backgroundColor: '#4CAF50',
      color: 'white',
      transform: 'scale(1.1)'
    }
  })
}));

const UserProfilesWidget = ({ searchQuery = '', filters = {} }) => {
  const [profiles, setProfiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [likedProfiles, setLikedProfiles] = useState(new Set());

  useEffect(() => {
    fetchProfiles();
  }, [currentPage, searchQuery, filters]);

  const fetchProfiles = async () => {
    setLoading(true);
    try {
      // Mock user profiles data
      const mockProfiles = [
        {
          id: 1,
          name: 'Priya Sharma',
          age: 26,
          location: 'Mumbai, Maharashtra',
          education: 'MBA in Finance',
          occupation: 'Financial Analyst',
          height: '5\'4"',
          photo: '/api/placeholder/200/250',
          isVerified: true,
          isPremium: true,
          isOnline: true,
          compatibility: 94,
          interests: ['Reading', 'Traveling', 'Cooking'],
          subcaste: '96 Kuli Maratha',
          income: '8-12 LPA'
        },
        {
          id: 2,
          name: 'Anita Patil',
          age: 24,
          location: 'Pune, Maharashtra',
          education: 'B.Tech Computer Science',
          occupation: 'Software Engineer',
          height: '5\'2"',
          photo: '/api/placeholder/200/250',
          isVerified: true,
          isPremium: false,
          isOnline: false,
          compatibility: 89,
          interests: ['Music', 'Dancing', 'Photography'],
          subcaste: 'Deshmukh',
          income: '6-10 LPA'
        },
        {
          id: 3,
          name: 'Kavya Desai',
          age: 27,
          location: 'Nashik, Maharashtra',
          education: 'MBBS',
          occupation: 'Doctor',
          height: '5\'5"',
          photo: '/api/placeholder/200/250',
          isVerified: true,
          isPremium: true,
          isOnline: true,
          compatibility: 87,
          interests: ['Yoga', 'Reading', 'Social Work'],
          subcaste: 'Kunbi',
          income: '10-15 LPA'
        },
        {
          id: 4,
          name: 'Sneha Kulkarni',
          age: 25,
          location: 'Nagpur, Maharashtra',
          education: 'CA',
          occupation: 'Chartered Accountant',
          height: '5\'3"',
          photo: '/api/placeholder/200/250',
          isVerified: false,
          isPremium: false,
          isOnline: false,
          compatibility: 82,
          interests: ['Finance', 'Travel', 'Movies'],
          subcaste: 'Maratha-Kshatriya',
          income: '8-12 LPA'
        }
      ];
      
      setProfiles(mockProfiles);
      setTotalPages(3); // Mock pagination
    } catch (error) {
      console.error('Error fetching profiles:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLike = (profileId) => {
    setLikedProfiles(prev => {
      const newSet = new Set(prev);
      if (newSet.has(profileId)) {
        newSet.delete(profileId);
      } else {
        newSet.add(profileId);
      }
      return newSet;
    });
  };

  const handleMessage = (profile) => {
    console.log('Sending message to:', profile.name);
    // Implement messaging logic
  };

  const handleViewProfile = (profile) => {
    console.log('Viewing profile:', profile.name);
    // Implement profile view logic
  };

  const getCompatibilityColor = (score) => {
    if (score >= 90) return '#4CAF50';
    if (score >= 80) return '#FF9800';
    if (score >= 70) return '#2196F3';
    return '#FF5F6D';
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        mb: 3,
        p: 3,
        background: 'linear-gradient(135deg, rgba(255, 95, 109, 0.1) 0%, rgba(255, 195, 113, 0.1) 100%)',
        borderRadius: 3
      }}>
        <SearchIcon sx={{ fontSize: 32, color: '#FF5F6D', mr: 2 }} />
        <Box>
          <Typography variant="h5" fontWeight="700" color="#FF5F6D">
            👥 Discover Profiles
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Browse through verified profiles and find your perfect match
          </Typography>
        </Box>
      </Box>

      {/* Search and Filters */}
      <Box sx={{ mb: 4 }}>
        <TextField
          fullWidth
          placeholder="Search by name, profession, location..."
          value={searchQuery}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon sx={{ color: '#FF5F6D' }} />
              </InputAdornment>
            ),
            endAdornment: (
              <InputAdornment position="end">
                <IconButton>
                  <FilterIcon sx={{ color: '#FF5F6D' }} />
                </IconButton>
              </InputAdornment>
            ),
            sx: { borderRadius: 3 }
          }}
        />
      </Box>

      {/* Profiles Grid */}
      <Grid container spacing={3}>
        {profiles.map((profile) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={profile.id}>
            <ProfileCard onClick={() => handleViewProfile(profile)}>
              <Box sx={{ position: 'relative' }}>
                <Avatar
                  src={profile.photo}
                  sx={{
                    width: '100%',
                    height: 250,
                    borderRadius: 0
                  }}
                />
                
                {/* Online Status */}
                {profile.isOnline && (
                  <Box sx={{
                    position: 'absolute',
                    top: 12,
                    left: 12,
                    width: 12,
                    height: 12,
                    borderRadius: '50%',
                    backgroundColor: '#4CAF50',
                    border: '2px solid white',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.2)'
                  }} />
                )}

                {/* Badges */}
                <Box sx={{
                  position: 'absolute',
                  top: 12,
                  right: 12,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1
                }}>
                  {profile.isVerified && (
                    <Chip
                      icon={<VerifiedIcon />}
                      label="Verified"
                      size="small"
                      sx={{
                        backgroundColor: 'rgba(76, 175, 80, 0.9)',
                        color: 'white',
                        fontSize: '0.75rem'
                      }}
                    />
                  )}
                  {profile.isPremium && (
                    <Chip
                      icon={<PremiumIcon />}
                      label="Premium"
                      size="small"
                      sx={{
                        backgroundColor: 'rgba(255, 215, 0, 0.9)',
                        color: '#000',
                        fontSize: '0.75rem'
                      }}
                    />
                  )}
                </Box>

                {/* Compatibility Score */}
                <Box sx={{
                  position: 'absolute',
                  bottom: 12,
                  left: 12,
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  borderRadius: 2,
                  px: 1.5,
                  py: 0.5
                }}>
                  <Typography 
                    variant="body2" 
                    fontWeight="600"
                    sx={{ color: getCompatibilityColor(profile.compatibility) }}
                  >
                    {profile.compatibility}% Match
                  </Typography>
                </Box>

                {/* Action Buttons */}
                <Box sx={{
                  position: 'absolute',
                  bottom: 12,
                  right: 12,
                  display: 'flex',
                  gap: 1
                }}>
                  <ActionButton
                    variant="heart"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleLike(profile.id);
                    }}
                  >
                    {likedProfiles.has(profile.id) ? <HeartIcon /> : <HeartBorderIcon />}
                  </ActionButton>
                  <ActionButton
                    variant="message"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleMessage(profile);
                    }}
                  >
                    <MessageIcon />
                  </ActionButton>
                </Box>
              </Box>

              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" gutterBottom>
                  {profile.name}, {profile.age}
                </Typography>
                
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  📍 {profile.location}
                </Typography>
                
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  🎓 {profile.education}
                </Typography>
                
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  💼 {profile.occupation}
                </Typography>
                
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  📏 {profile.height} • 💰 {profile.income}
                </Typography>

                <Box sx={{ display: 'flex', gap: 0.5, mt: 2, flexWrap: 'wrap' }}>
                  {profile.interests.slice(0, 2).map((interest, index) => (
                    <Chip
                      key={index}
                      label={interest}
                      size="small"
                      sx={{
                        backgroundColor: 'rgba(255, 95, 109, 0.1)',
                        color: '#FF5F6D',
                        fontSize: '0.75rem'
                      }}
                    />
                  ))}
                  {profile.interests.length > 2 && (
                    <Chip
                      label={`+${profile.interests.length - 2}`}
                      size="small"
                      sx={{
                        backgroundColor: 'rgba(255, 95, 109, 0.1)',
                        color: '#FF5F6D',
                        fontSize: '0.75rem'
                      }}
                    />
                  )}
                </Box>
              </CardContent>
            </ProfileCard>
          </Grid>
        ))}
      </Grid>

      {/* Pagination */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <Pagination
          count={totalPages}
          page={currentPage}
          onChange={(e, page) => setCurrentPage(page)}
          color="primary"
          size="large"
          sx={{
            '& .MuiPaginationItem-root': {
              color: '#FF5F6D',
              '&.Mui-selected': {
                backgroundColor: '#FF5F6D',
                color: 'white'
              }
            }
          }}
        />
      </Box>
    </Box>
  );
};

export default UserProfilesWidget;
