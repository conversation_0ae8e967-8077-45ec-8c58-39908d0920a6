import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Divider,
  FormControl,
  FormLabel,
  RadioGroup,
  Radio,
  FormControlLabel,
  Switch,
  Grid,
  Button,
  Alert,
  Snackbar,
  Tabs,
  Tab,
  Tooltip,
  IconButton,
  Chip
} from '@mui/material';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import LockIcon from '@mui/icons-material/Lock';
import PhotoLibraryIcon from '@mui/icons-material/PhotoLibrary';
import ContactPhoneIcon from '@mui/icons-material/ContactPhone';
import PersonIcon from '@mui/icons-material/Person';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import SaveIcon from '@mui/icons-material/Save';
import BadgeIcon from '@mui/icons-material/Badge';
import PreviewIcon from '@mui/icons-material/Preview';
import { DISPLAY_NAME_OPTIONS } from '@/config';

/**
 * Privacy Settings Component
 *
 * Allows users to control visibility of their profile information,
 * photos, contact details, and activity status
 */
const PrivacySettings = ({ user, onSave }) => {
  // Active tab state
  const [activeTab, setActiveTab] = useState(0);

  // Privacy settings state
  const [settings, setSettings] = useState({
    // Photo Privacy
    photoPrivacy: 'ALL_USERS',

    // Contact Details Privacy
    phonePrivacy: 'PREMIUM_USERS',
    emailPrivacy: 'PREMIUM_USERS',
    socialMediaPrivacy: 'PREMIUM_USERS',

    // Profile Information Privacy
    educationPrivacy: 'ALL_USERS',
    careerPrivacy: 'ALL_USERS',
    familyPrivacy: 'ALL_USERS',
    birthDetailsPrivacy: 'ALL_USERS',

    // Activity Privacy
    onlineStatusPrivacy: 'ALL_USERS',
    lastActivePrivacy: 'ALL_USERS',

    // Display Name Privacy (NEW)
    displayNamePreference: 'FIRST_NAME',
    showNameInNotifications: true,
    showNameInSearch: true,
    showNameInMatches: true,
    showNameInMessages: true,
    allowProfileViews: true,
    showOnlineStatus: false,
    showLastSeen: false,
    allowDirectMessages: true,
    showContactInfo: false
  });

  // Notification state
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Load user settings on component mount
  useEffect(() => {
    if (user && user.privacySettings) {
      setSettings(user.privacySettings);
    }
  }, [user]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Handle setting change
  const handleSettingChange = (event) => {
    const { name, value } = event.target;
    setSettings({
      ...settings,
      [name]: value
    });
  };

  // Handle save
  const handleSave = () => {
    // Call the onSave callback with updated settings
    if (onSave) {
      onSave(settings);
    }

    // Show success notification
    setNotification({
      open: true,
      message: 'Privacy settings saved successfully',
      severity: 'success'
    });
  };

  // Close notification
  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false
    });
  };

  // Get description for privacy option
  const getPrivacyDescription = (option) => {
    switch (option) {
      case 'ALL_USERS':
        return 'Visible to all users';
      case 'PREMIUM_USERS':
        return 'Visible only to premium users';
      case 'ACCEPTED_INTEREST':
        return 'Visible only after interest is accepted';
      case 'HIDDEN':
        return 'Hidden from all users';
      default:
        return '';
    }
  };

  // Get icon for privacy option
  const getPrivacyIcon = (option) => {
    switch (option) {
      case 'ALL_USERS':
        return <VisibilityIcon fontSize="small" />;
      case 'PREMIUM_USERS':
        return <LockIcon fontSize="small" />;
      case 'ACCEPTED_INTEREST':
        return <VisibilityOffIcon fontSize="small" />;
      case 'HIDDEN':
        return <VisibilityOffIcon fontSize="small" />;
      default:
        return null;
    }
  };

  return (
    <Paper elevation={0} sx={{ p: 3, borderRadius: 2 }}>
      <Typography variant="h5" gutterBottom>
        Privacy Settings
      </Typography>
      <Typography variant="body2" color="text.secondary" paragraph>
        Control who can see your information and how your profile appears to others.
      </Typography>

      <Tabs
        value={activeTab}
        onChange={handleTabChange}
        sx={{ mb: 3 }}
        variant="scrollable"
        scrollButtons="auto"
      >
        <Tab icon={<PhotoLibraryIcon />} label="Photos" />
        <Tab icon={<ContactPhoneIcon />} label="Contact Details" />
        <Tab icon={<PersonIcon />} label="Profile Info" />
        <Tab icon={<AccessTimeIcon />} label="Activity" />
        <Tab icon={<BadgeIcon />} label="Display Name" />
      </Tabs>

      {/* Photo Privacy Tab */}
      {activeTab === 0 && (
        <Box>
          <Typography variant="subtitle1" gutterBottom>
            Photo Visibility
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Control who can see your photos. Your primary photo is always visible to all users.
          </Typography>

          <FormControl component="fieldset" sx={{ width: '100%' }}>
            <RadioGroup
              name="photoPrivacy"
              value={settings.photoPrivacy}
              onChange={handleSettingChange}
            >
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                    <FormControlLabel
                      value="ALL_USERS"
                      control={<Radio />}
                      label={
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="body1">Visible to all users</Typography>
                          <Tooltip title="All users can see your photos">
                            <IconButton size="small" sx={{ ml: 1 }}>
                              <InfoOutlinedIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      }
                    />
                  </Paper>
                </Grid>

                <Grid item xs={12}>
                  <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                    <FormControlLabel
                      value="PREMIUM_USERS"
                      control={<Radio />}
                      label={
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="body1">Visible only to premium users</Typography>
                          <Tooltip title="Only premium users can see your photos">
                            <IconButton size="small" sx={{ ml: 1 }}>
                              <InfoOutlinedIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      }
                    />
                  </Paper>
                </Grid>

                <Grid item xs={12}>
                  <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                    <FormControlLabel
                      value="ACCEPTED_INTEREST"
                      control={<Radio />}
                      label={
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="body1">Visible only after interest is accepted</Typography>
                          <Tooltip title="Your photos will be visible only to users whose interest you've accepted">
                            <IconButton size="small" sx={{ ml: 1 }}>
                              <InfoOutlinedIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      }
                    />
                  </Paper>
                </Grid>
              </Grid>
            </RadioGroup>
          </FormControl>
        </Box>
      )}

      {/* Contact Details Privacy Tab */}
      {activeTab === 1 && (
        <Box>
          <Typography variant="subtitle1" gutterBottom>
            Contact Details Visibility
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Control who can see your contact information. Free users cannot see contact details under any circumstances.
          </Typography>

          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Phone Number
            </Typography>
            <FormControl component="fieldset" sx={{ width: '100%' }}>
              <RadioGroup
                name="phonePrivacy"
                value={settings.phonePrivacy}
                onChange={handleSettingChange}
              >
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <FormControlLabel
                        value="PREMIUM_USERS"
                        control={<Radio />}
                        label={
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Typography variant="body1">Visible only to premium users</Typography>
                            <Tooltip title="Only premium users can see your phone number">
                              <IconButton size="small" sx={{ ml: 1 }}>
                                <InfoOutlinedIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        }
                      />
                    </Paper>
                  </Grid>

                  <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <FormControlLabel
                        value="ACCEPTED_INTEREST"
                        control={<Radio />}
                        label={
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Typography variant="body1">Visible only after interest is accepted</Typography>
                            <Tooltip title="Your phone number will be visible only to premium users whose interest you've accepted">
                              <IconButton size="small" sx={{ ml: 1 }}>
                                <InfoOutlinedIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        }
                      />
                    </Paper>
                  </Grid>

                  <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <FormControlLabel
                        value="HIDDEN"
                        control={<Radio />}
                        label={
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Typography variant="body1">Hidden from all users</Typography>
                            <Tooltip title="No one can see your phone number">
                              <IconButton size="small" sx={{ ml: 1 }}>
                                <InfoOutlinedIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        }
                      />
                    </Paper>
                  </Grid>
                </Grid>
              </RadioGroup>
            </FormControl>
          </Box>

          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Email Address
            </Typography>
            <FormControl component="fieldset" sx={{ width: '100%' }}>
              <RadioGroup
                name="emailPrivacy"
                value={settings.emailPrivacy}
                onChange={handleSettingChange}
              >
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <FormControlLabel
                        value="PREMIUM_USERS"
                        control={<Radio />}
                        label="Visible only to premium users"
                      />
                    </Paper>
                  </Grid>

                  <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <FormControlLabel
                        value="ACCEPTED_INTEREST"
                        control={<Radio />}
                        label="Visible only after interest is accepted"
                      />
                    </Paper>
                  </Grid>

                  <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <FormControlLabel
                        value="HIDDEN"
                        control={<Radio />}
                        label="Hidden from all users"
                      />
                    </Paper>
                  </Grid>
                </Grid>
              </RadioGroup>
            </FormControl>
          </Box>
        </Box>
      )}

      {/* Profile Info Privacy Tab */}
      {activeTab === 2 && (
        <Box>
          <Typography variant="subtitle1" gutterBottom>
            Profile Information Visibility
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Control who can see different sections of your profile information.
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" gutterBottom>
                Education Details
              </Typography>
              <FormControl fullWidth>
                <Select
                  native
                  name="educationPrivacy"
                  value={settings.educationPrivacy}
                  onChange={handleSettingChange}
                  sx={{ mb: 2 }}
                >
                  <option value="ALL_USERS">Visible to all users</option>
                  <option value="PREMIUM_USERS">Visible only to premium users</option>
                  <option value="ACCEPTED_INTEREST">Visible only after interest is accepted</option>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" gutterBottom>
                Career & Income
              </Typography>
              <FormControl fullWidth>
                <Select
                  native
                  name="careerPrivacy"
                  value={settings.careerPrivacy}
                  onChange={handleSettingChange}
                  sx={{ mb: 2 }}
                >
                  <option value="ALL_USERS">Visible to all users</option>
                  <option value="PREMIUM_USERS">Visible only to premium users</option>
                  <option value="ACCEPTED_INTEREST">Visible only after interest is accepted</option>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" gutterBottom>
                Family Details
              </Typography>
              <FormControl fullWidth>
                <Select
                  native
                  name="familyPrivacy"
                  value={settings.familyPrivacy}
                  onChange={handleSettingChange}
                  sx={{ mb: 2 }}
                >
                  <option value="ALL_USERS">Visible to all users</option>
                  <option value="PREMIUM_USERS">Visible only to premium users</option>
                  <option value="ACCEPTED_INTEREST">Visible only after interest is accepted</option>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" gutterBottom>
                Birth Details (for horoscope)
              </Typography>
              <FormControl fullWidth>
                <Select
                  native
                  name="birthDetailsPrivacy"
                  value={settings.birthDetailsPrivacy}
                  onChange={handleSettingChange}
                  sx={{ mb: 2 }}
                >
                  <option value="ALL_USERS">Visible to all users</option>
                  <option value="PREMIUM_USERS">Visible only to premium users</option>
                  <option value="ACCEPTED_INTEREST">Visible only after interest is accepted</option>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Box>
      )}

      {/* Activity Privacy Tab */}
      {activeTab === 3 && (
        <Box>
          <Typography variant="subtitle1" gutterBottom>
            Activity Status Visibility
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Control who can see your online status and last active information.
          </Typography>

          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Online Status
            </Typography>
            <FormControl component="fieldset" sx={{ width: '100%' }}>
              <RadioGroup
                name="onlineStatusPrivacy"
                value={settings.onlineStatusPrivacy}
                onChange={handleSettingChange}
              >
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <FormControlLabel
                        value="ALL_USERS"
                        control={<Radio />}
                        label="Visible to all users"
                      />
                    </Paper>
                  </Grid>

                  <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <FormControlLabel
                        value="PREMIUM_USERS"
                        control={<Radio />}
                        label="Visible only to premium users"
                      />
                    </Paper>
                  </Grid>

                  <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <FormControlLabel
                        value="ACCEPTED_INTEREST"
                        control={<Radio />}
                        label="Visible only after interest is accepted"
                      />
                    </Paper>
                  </Grid>

                  <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <FormControlLabel
                        value="HIDDEN"
                        control={<Radio />}
                        label="Hidden from all users"
                      />
                    </Paper>
                  </Grid>
                </Grid>
              </RadioGroup>
            </FormControl>
          </Box>

          <Box>
            <Typography variant="subtitle2" gutterBottom>
              Last Active Status
            </Typography>
            <FormControl component="fieldset" sx={{ width: '100%' }}>
              <RadioGroup
                name="lastActivePrivacy"
                value={settings.lastActivePrivacy}
                onChange={handleSettingChange}
              >
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <FormControlLabel
                        value="ALL_USERS"
                        control={<Radio />}
                        label="Visible to all users"
                      />
                    </Paper>
                  </Grid>

                  <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <FormControlLabel
                        value="PREMIUM_USERS"
                        control={<Radio />}
                        label="Visible only to premium users"
                      />
                    </Paper>
                  </Grid>

                  <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <FormControlLabel
                        value="ACCEPTED_INTEREST"
                        control={<Radio />}
                        label="Visible only after interest is accepted"
                      />
                    </Paper>
                  </Grid>

                  <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 2, mb: 1 }}>
                      <FormControlLabel
                        value="HIDDEN"
                        control={<Radio />}
                        label="Hidden from all users"
                      />
                    </Paper>
                  </Grid>
                </Grid>
              </RadioGroup>
            </FormControl>
          </Box>
        </Box>
      )}

      {/* Display Name Privacy Tab */}
      {activeTab === 4 && (
        <Box>
          <Typography variant="subtitle1" gutterBottom>
            Display Name Preferences
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Choose how your name appears to other users across the platform. This affects notifications, search results, matches, and messages.
          </Typography>

          {/* Display Name Preference Selection */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="subtitle2" gutterBottom sx={{ mb: 2 }}>
              How should your name be displayed?
            </Typography>
            <FormControl component="fieldset" sx={{ width: '100%' }}>
              <RadioGroup
                name="displayNamePreference"
                value={settings.displayNamePreference}
                onChange={handleSettingChange}
              >
                <Grid container spacing={2}>
                  {Object.values(DISPLAY_NAME_OPTIONS).map((option) => (
                    <Grid item xs={12} key={option.value}>
                      <Paper
                        variant="outlined"
                        sx={{
                          p: 2,
                          mb: 1,
                          border: settings.displayNamePreference === option.value ? 2 : 1,
                          borderColor: settings.displayNamePreference === option.value ? 'primary.main' : 'divider'
                        }}
                      >
                        <FormControlLabel
                          value={option.value}
                          control={<Radio />}
                          label={
                            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                              <Box sx={{ mr: 2, fontSize: '1.5rem' }}>{option.icon}</Box>
                              <Box sx={{ flexGrow: 1 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                                  <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                                    {option.label}
                                  </Typography>
                                  {option.recommended && (
                                    <Chip
                                      label="Recommended"
                                      size="small"
                                      color="primary"
                                      sx={{ ml: 1, fontSize: '0.7rem' }}
                                    />
                                  )}
                                </Box>
                                <Typography variant="body2" color="text.secondary">
                                  {option.description}
                                </Typography>
                                <Typography variant="caption" color={
                                  option.privacy === 'Low' ? 'error.main' :
                                  option.privacy === 'Medium' ? 'warning.main' :
                                  option.privacy === 'High' ? 'info.main' : 'success.main'
                                }>
                                  Privacy Level: {option.privacy}
                                </Typography>
                              </Box>
                            </Box>
                          }
                        />
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              </RadioGroup>
            </FormControl>
          </Box>

          {/* Context-specific Settings */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom sx={{ mb: 2 }}>
              Where should your name be visible?
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              You can control where your chosen display name appears. Unchecking these will show "Someone" instead.
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.showNameInNotifications}
                      onChange={(e) => setSettings({
                        ...settings,
                        showNameInNotifications: e.target.checked
                      })}
                      name="showNameInNotifications"
                    />
                  }
                  label="Email Notifications"
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.showNameInSearch}
                      onChange={(e) => setSettings({
                        ...settings,
                        showNameInSearch: e.target.checked
                      })}
                      name="showNameInSearch"
                    />
                  }
                  label="Search Results"
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.showNameInMatches}
                      onChange={(e) => setSettings({
                        ...settings,
                        showNameInMatches: e.target.checked
                      })}
                      name="showNameInMatches"
                    />
                  }
                  label="Match Listings"
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.showNameInMessages}
                      onChange={(e) => setSettings({
                        ...settings,
                        showNameInMessages: e.target.checked
                      })}
                      name="showNameInMessages"
                    />
                  }
                  label="Messages & Chat"
                />
              </Grid>
            </Grid>
          </Box>

          {/* Additional Privacy Controls */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom sx={{ mb: 2 }}>
              Additional Privacy Controls
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.allowProfileViews}
                      onChange={(e) => setSettings({
                        ...settings,
                        allowProfileViews: e.target.checked
                      })}
                      name="allowProfileViews"
                    />
                  }
                  label="Allow Profile Views"
                />
                <Typography variant="caption" display="block" color="text.secondary">
                  Let others view your profile details
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.allowDirectMessages}
                      onChange={(e) => setSettings({
                        ...settings,
                        allowDirectMessages: e.target.checked
                      })}
                      name="allowDirectMessages"
                    />
                  }
                  label="Allow Direct Messages"
                />
                <Typography variant="caption" display="block" color="text.secondary">
                  Let others send you messages
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.showOnlineStatus}
                      onChange={(e) => setSettings({
                        ...settings,
                        showOnlineStatus: e.target.checked
                      })}
                      name="showOnlineStatus"
                    />
                  }
                  label="Show Online Status"
                />
                <Typography variant="caption" display="block" color="text.secondary">
                  Show when you're online
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.showContactInfo}
                      onChange={(e) => setSettings({
                        ...settings,
                        showContactInfo: e.target.checked
                      })}
                      name="showContactInfo"
                    />
                  }
                  label="Show Contact Info"
                />
                <Typography variant="caption" display="block" color="text.secondary">
                  Show contact details to premium users
                </Typography>
              </Grid>
            </Grid>
          </Box>

          {/* Privacy Recommendation Alert */}
          {settings.displayNamePreference === 'FULL_NAME' && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>Privacy Tip:</strong> For enhanced security, especially for female users,
                consider using "First Name Only" or "Profile ID" instead of your full name.
              </Typography>
            </Alert>
          )}

          {settings.displayNamePreference === 'ANONYMOUS' && (
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>Note:</strong> Using "Anonymous" provides maximum privacy but may reduce
                your profile's appeal to potential matches.
              </Typography>
            </Alert>
          )}
        </Box>
      )}

      <Divider sx={{ my: 3 }} />

      <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<SaveIcon />}
          onClick={handleSave}
        >
          Save Privacy Settings
        </Button>
      </Box>

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default PrivacySettings;
