// src/pages/api/admin/biodata/templates/[id].js
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../auth/[...nextauth]';
import formidable from 'formidable';
import fs from 'fs';
import path from 'path';

// Disable the default body parser to handle form data
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  // Check if user is authenticated and is an admin
  const session = await getServerSession(req, res, authOptions);

  if (!session || !session.user || !session.user.isAdmin) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  // Get template ID from the URL
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ success: false, message: 'Template ID is required' });
  }

  // Handle GET request - fetch a specific biodata template
  if (req.method === 'GET') {
    try {
      // Fetch template from backend API
      const response = await fetch(`${process.env.BACKEND_API_URL}/api/admin/biodata/templates/${id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.accessToken}`
        }
      }).catch(() => null);

      if (response && response.ok) {
        const data = await response.json();
        return res.status(200).json({
          success: true,
          template: data.template
        });
      } else {
        // Fallback to mock data if backend API fails
        const mockTemplate = getMockTemplateById(id);

        if (!mockTemplate) {
          return res.status(404).json({
            success: false,
            message: 'Template not found'
          });
        }

        return res.status(200).json({
          success: true,
          template: mockTemplate
        });
      }
    } catch (error) {
      console.error('Error fetching biodata template:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch biodata template',
        error: error.message
      });
    }
  }

  // Handle PUT request - update a biodata template
  if (req.method === 'PUT') {
    try {
      // Parse form data
      const { fields, files } = await parseFormData(req);

      // Prepare data for backend API
      const templateData = {};

      // Add fields if they exist
      if (fields.name) templateData.name = fields.name[0];
      if (fields.description) templateData.description = fields.description[0];
      if (fields.price) templateData.price = parseFloat(fields.price[0]);
      if (fields.discountPercent) templateData.discountPercent = parseInt(fields.discountPercent[0]);
      if (fields.isActive !== undefined) templateData.isActive = fields.isActive[0] === 'true';

      // Upload files if they exist
      if (files.previewImage) {
        const previewImageUrl = await uploadFile(files.previewImage[0]);
        templateData.previewImage = previewImageUrl;
      }

      if (files.designFile) {
        const designFileUrl = await uploadFile(files.designFile[0]);
        templateData.designFile = designFileUrl;
      }

      // Update template in backend API
      const response = await fetch(`${process.env.BACKEND_API_URL}/api/admin/biodata/templates/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.accessToken}`
        },
        body: JSON.stringify(templateData)
      }).catch(() => null);

      if (response && response.ok) {
        const data = await response.json();
        return res.status(200).json({
          success: true,
          message: 'Biodata template updated successfully',
          template: data.template
        });
      } else {
        // Fallback if backend API fails
        const mockTemplate = getMockTemplateById(id);

        if (!mockTemplate) {
          return res.status(404).json({
            success: false,
            message: 'Template not found'
          });
        }

        const updatedTemplate = {
          ...mockTemplate,
          ...templateData,
          updatedAt: new Date().toISOString()
        };

        return res.status(200).json({
          success: true,
          message: 'Biodata template updated successfully',
          template: updatedTemplate
        });
      }
    } catch (error) {
      console.error('Error updating biodata template:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to update biodata template',
        error: error.message
      });
    }
  }

  // Handle DELETE request - delete a biodata template
  if (req.method === 'DELETE') {
    try {
      // Delete template from backend API
      const response = await fetch(`${process.env.BACKEND_API_URL}/api/admin/biodata/templates/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${session.accessToken}`
        }
      }).catch(() => null);

      if (response && response.ok) {
        const data = await response.json();
        return res.status(200).json({
          success: true,
          message: 'Biodata template deleted successfully'
        });
      } else {
        // Fallback if backend API fails
        const mockTemplate = getMockTemplateById(id);

        if (!mockTemplate) {
          return res.status(404).json({
            success: false,
            message: 'Template not found'
          });
        }

        return res.status(200).json({
          success: true,
          message: 'Biodata template deleted successfully'
        });
      }
    } catch (error) {
      console.error('Error deleting biodata template:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to delete biodata template',
        error: error.message
      });
    }
  }

  // Handle unsupported methods
  return res.status(405).json({ success: false, message: 'Method not allowed' });
}

// Helper function to parse form data
const parseFormData = (req) => {
  return new Promise((resolve, reject) => {
    const form = new formidable.IncomingForm({
      multiples: true,
      keepExtensions: true
    });

    form.parse(req, (err, fields, files) => {
      if (err) return reject(err);
      resolve({ fields, files });
    });
  });
};

// Helper function to upload file (mock implementation)
const uploadFile = async (file) => {
  // In a real implementation, this would upload to cloud storage
  // For now, we'll just return a mock URL
  const fileName = path.basename(file.originalFilename || file.newFilename);
  const fileType = fileName.split('.').pop();

  return `https://example.com/uploads/${Date.now()}-${fileName}`;
};

// Function to get template files from the public directory
function getMockTemplates() {
  try {
    // Get the list of HTML files in the public/templates/biodata directory
    const templatesDir = path.join(process.cwd(), 'public', 'templates', 'biodata');
    console.log(`[API] Reading template files from: ${templatesDir}`);

    const files = fs.readdirSync(templatesDir);
    const htmlFiles = files.filter(file => file.endsWith('.html'));

    console.log(`[API] Found ${htmlFiles.length} template files`);

    // Create mock template objects from the files
    return htmlFiles.map((file, index) => {
      const name = file.replace('.html', '').split('-').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1)
      ).join(' ');

      const genderOrientation = file.includes('female') ? 'female' :
                               file.includes('male') ? 'male' : 'neutral';

      const category = file.includes('traditional') ? 'traditional' :
                      file.includes('modern') ? 'modern' :
                      file.includes('professional') ? 'professional' :
                      file.includes('premium') ? 'premium' : 'standard';

      return {
        id: (index + 1).toString(),
        name: name,
        description: `A ${category} biodata template with ${genderOrientation === 'neutral' ? 'gender-neutral' : genderOrientation + '-oriented'} design`,
        previewImage: `/images/biodata-templates/${file.replace('.html', '')}-preview.jpg`,
        thumbnail: `/images/biodata-templates/${file.replace('.html', '')}-thumb.jpg`,
        designFile: `/templates/biodata/${file}`,
        genderOrientation: genderOrientation,
        category: category,
        isActive: true,
        isPremium: category === 'premium',
        price: category === 'premium' ? 299 : 0,
        currency: "INR",
        headerText: "Shree Ganeshay Namah",
        footerText: "Powered by Vaivahik - The Premier Maratha Matrimony Platform",
        createdAt: new Date(Date.now() - (30 - index) * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString(),
        purchaseCount: Math.floor(Math.random() * 50),
        downloadCount: Math.floor(Math.random() * 100),
        revenue: Math.floor(Math.random() * 10000)
      };
    });
  } catch (error) {
    console.error('Error reading template files:', error);
    return [];
  }
}

// Helper function to get mock template by ID
function getMockTemplateById(id) {
  try {
    // First try to get templates from the mock data file
    const filePath = path.join(process.cwd(), 'public', 'mock-data', 'admin', 'biodata-templates.json');

    try {
      const fileData = fs.readFileSync(filePath, 'utf8');
      const mockData = JSON.parse(fileData);

      if (mockData.templates && mockData.templates.length > 0) {
        const template = mockData.templates.find(t => t.id.toString() === id.toString());
        if (template) {
          return template;
        }
      }
    } catch (error) {
      console.error('Error reading mock data file:', error);
    }

    // If no template found in mock data, use template files
    const templates = getMockTemplates();
    return templates.find(t => t.id.toString() === id.toString());
  } catch (error) {
    console.error('Error getting mock template:', error);
    return null;
  }
}
