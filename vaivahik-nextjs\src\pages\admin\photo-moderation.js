import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { adminGet, adminPost, adminPut } from '@/services/apiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);
import PhotoModerationSettings from '@/components/admin/PhotoModerationSettings';
import PhotoModerationStats from '@/components/admin/PhotoModerationStats';

export default function PhotoModeration() {
  const [activeTab, setActiveTab] = useState('photos'); // 'photos', 'settings', or 'stats'
  const [photos, setPhotos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPhoto, setCurrentPhoto] = useState(null);
  const [showPhotoModal, setShowPhotoModal] = useState(false);
  const [filter, setFilter] = useState('PENDING'); // PENDING, APPROVED, REJECTED, ALL
  const [notification, setNotification] = useState({ show: false, message: '', type: 'success' });
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [processingBatch, setProcessingBatch] = useState(false);

  useEffect(() => {
    fetchPhotos();
  }, [filter, page]);

  const fetchPhotos = async () => {
    setLoading(true);
    try {
      // Use the admin API service which handles mock/real data toggle
      const data = await adminGet(`${ADMIN_ENDPOINTS.PHOTO_MODERATION}/photos`, {
        status: filter,
        page: page,
        limit: 20
      });

      if (data.success) {
        // Check if photos array exists and is valid
        if (Array.isArray(data.photos)) {
          setPhotos(data.photos);
          setTotalPages(data.pagination?.pages || 1);
        } else {
          console.error('Invalid photos data format:', data);
          setPhotos([]);
          setTotalPages(1);
          showNotification('Received invalid data format from server', 'error');
        }
      } else {
        console.error('API returned error:', data);
        throw new Error(data.message || 'Failed to fetch photos');
      }
    } catch (error) {
      console.error('Error fetching photos:', error);
      setPhotos([]);
      showNotification('Failed to load photos. Please check if the backend server is running.', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Process a batch of photos with AI
  const processBatch = async () => {
    setProcessingBatch(true);
    try {
      const data = await adminPost(`${ADMIN_ENDPOINTS.PHOTO_MODERATION}/batch-process`, {
        limit: 20
      });

      if (data.success) {
        showNotification(data.message, 'success');
        // Refresh the photos list
        fetchPhotos();
      } else {
        throw new Error('Failed to process batch');
      }
    } catch (error) {
      console.error('Error processing batch:', error);
      showNotification('Failed to process photos with AI', 'error');
    } finally {
      setProcessingBatch(false);
    }
  };

  const handleViewPhoto = (photo) => {
    setCurrentPhoto(photo);
    setShowPhotoModal(true);
  };

  const handleApprovePhoto = async (photo) => {
    try {
      // Use the admin API service which handles mock/real data toggle
      const data = await adminPut(`${ADMIN_ENDPOINTS.PHOTO_MODERATION}/photos/${photo.id}/status`, {
        status: 'APPROVED'
      });

      if (data.success) {
        // Update local state
        setPhotos(photos.map(p => p.id === photo.id ? { ...p, status: 'APPROVED' } : p));

        if (showPhotoModal && currentPhoto?.id === photo.id) {
          setCurrentPhoto({ ...currentPhoto, status: 'APPROVED' });
        }

        showNotification('Photo approved successfully');
      } else {
        throw new Error('Failed to approve photo');
      }
    } catch (error) {
      console.error('Error approving photo:', error);
      showNotification('Failed to approve photo', 'error');
    }
  };

  const handleRejectPhoto = async (photo) => {
    try {
      // Use the admin API service which handles mock/real data toggle
      const data = await adminPut(`${ADMIN_ENDPOINTS.PHOTO_MODERATION}/photos/${photo.id}/status`, {
        status: 'REJECTED'
      });

      if (data.success) {
        // Update local state
        setPhotos(photos.map(p => p.id === photo.id ? { ...p, status: 'REJECTED' } : p));

        if (showPhotoModal && currentPhoto?.id === photo.id) {
          setCurrentPhoto({ ...currentPhoto, status: 'REJECTED' });
        }

        showNotification('Photo rejected successfully');
      } else {
        throw new Error('Failed to reject photo');
      }
    } catch (error) {
      console.error('Error rejecting photo:', error);
      showNotification('Failed to reject photo', 'error');
    }
  };

  const showNotification = (message, type = 'success') => {
    setNotification({ show: true, message, type });
    setTimeout(() => {
      setNotification(prev => ({ ...prev, show: false }));
    }, 3000);
  };

  return (
    <EnhancedAdminLayout title="Photo Moderation">
      <div className="content-header">
        <h2 className="page-title">Photo Moderation</h2>

        <div className="tab-navigation">
          <button
            className={`tab-btn ${activeTab === 'photos' ? 'active' : ''}`}
            onClick={() => setActiveTab('photos')}
          >
            Photos
          </button>
          <button
            className={`tab-btn ${activeTab === 'stats' ? 'active' : ''}`}
            onClick={() => setActiveTab('stats')}
          >
            Statistics
          </button>
          <button
            className={`tab-btn ${activeTab === 'settings' ? 'active' : ''}`}
            onClick={() => setActiveTab('settings')}
          >
            AI Settings
          </button>
        </div>

        {activeTab === 'photos' && (
          <div className="header-actions">
            <div className="action-buttons">
              {filter === 'PENDING' && (
                <button
                  className="btn btn-success"
                  onClick={processBatch}
                  disabled={processingBatch}
                >
                  {processingBatch ? 'Processing...' : 'Process Batch with AI'}
                </button>
              )}
              <button
                className="btn btn-outline-primary"
                onClick={fetchPhotos}
                disabled={loading}
              >
                Refresh
              </button>
            </div>
            <div className="filter-buttons">
              <button
                className={`btn ${filter === 'PENDING' ? 'btn-primary' : 'btn-outline-primary'}`}
                onClick={() => setFilter('PENDING')}
              >
                Pending
              </button>
              <button
                className={`btn ${filter === 'APPROVED' ? 'btn-primary' : 'btn-outline-primary'}`}
                onClick={() => setFilter('APPROVED')}
              >
                Approved
              </button>
              <button
                className={`btn ${filter === 'REJECTED' ? 'btn-primary' : 'btn-outline-primary'}`}
                onClick={() => setFilter('REJECTED')}
              >
                Rejected
              </button>
              <button
                className={`btn ${filter === 'ALL' ? 'btn-primary' : 'btn-outline-primary'}`}
                onClick={() => setFilter('ALL')}
              >
                All
              </button>
            </div>
          </div>
        )}
      </div>

      {activeTab === 'settings' ? (
        <PhotoModerationSettings />
      ) : activeTab === 'stats' ? (
        <PhotoModerationStats />
      ) : loading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading photos...</p>
        </div>
      ) : (
        <>
          {photos.length === 0 ? (
            <div className="empty-state">
              <div className="empty-icon">🖼️</div>
              <h3>No Photos Found</h3>
              <p>There are no {filter !== 'ALL' ? filter.toLowerCase() : ''} photos to moderate at this time.</p>
              <button className="btn btn-outline-primary mt-3" onClick={fetchPhotos}>
                Refresh
              </button>
            </div>
          ) : (
            <div className="photo-grid">
              {photos.map(photo => (
                <div key={photo.id} className="photo-card">
                  <div className="photo-container">
                    <img
                      src={photo.photoUrl}
                      alt={`Photo by ${photo.userName}`}
                      onClick={() => handleViewPhoto(photo)}
                    />
                    <div className="photo-overlay">
                      <button
                        className="overlay-btn view-btn"
                        onClick={() => handleViewPhoto(photo)}
                      >
                        View
                      </button>
                    </div>
                  </div>
                  <div className="photo-info">
                    <div className="photo-user">
                      <div className="user-name">{photo.userName}</div>
                      <div className="user-details">{photo.userAge}, {photo.userLocation}</div>
                    </div>
                    <div className="photo-meta">
                      <div className="photo-type">
                        <span className={`type-badge ${photo.type}`}>
                          {photo.type === 'profile' ? 'Profile Photo' : 'Gallery Photo'}
                        </span>
                      </div>
                      <div className="photo-time">
                        {new Date(photo.uploadedAt).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  <div className="photo-actions">
                    {photo.status === 'PENDING' && (
                      <>
                        <button
                          className="btn btn-sm btn-success"
                          onClick={() => handleApprovePhoto(photo)}
                        >
                          Approve
                        </button>
                        <button
                          className="btn btn-sm btn-danger"
                          onClick={() => handleRejectPhoto(photo)}
                        >
                          Reject
                        </button>
                      </>
                    )}
                    {photo.status === 'APPROVED' && (
                      <span className="status-badge approved">Approved</span>
                    )}
                    {photo.status === 'REJECTED' && (
                      <span className="status-badge rejected">Rejected</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Photo Modal */}
          {showPhotoModal && currentPhoto && (
            <div className="modal show">
              <div className="modal-content photo-modal-content">
                <div className="modal-header">
                  <h3 className="modal-title">Photo Review</h3>
                  <button
                    className="close-modal"
                    onClick={() => setShowPhotoModal(false)}
                    aria-label="Close"
                  >
                    &times;
                  </button>
                </div>
                <div className="modal-body">
                  <div className="photo-review">
                    <div className="photo-large">
                      <img
                        src={currentPhoto.photoUrl}
                        alt={`Photo by ${currentPhoto.userName}`}
                      />
                    </div>
                    <div className="photo-details">
                      <div className="detail-section">
                        <h4>User Information</h4>
                        <div className="detail-grid">
                          <div className="detail-item">
                            <div className="detail-label">Name</div>
                            <div className="detail-value">{currentPhoto.userName}</div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">Age</div>
                            <div className="detail-value">{currentPhoto.userAge}</div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">Location</div>
                            <div className="detail-value">{currentPhoto.userLocation}</div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">User ID</div>
                            <div className="detail-value">{currentPhoto.userId}</div>
                          </div>
                        </div>
                      </div>

                      <div className="detail-section">
                        <h4>Photo Information</h4>
                        <div className="detail-grid">
                          <div className="detail-item">
                            <div className="detail-label">Type</div>
                            <div className="detail-value">
                              {currentPhoto.type === 'profile' ? 'Profile Photo' : 'Gallery Photo'}
                            </div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">Uploaded</div>
                            <div className="detail-value">
                              {new Date(currentPhoto.uploadedAt).toLocaleString()}
                            </div>
                          </div>
                          <div className="detail-item">
                            <div className="detail-label">Status</div>
                            <div className="detail-value">
                              <span className={`status-badge ${currentPhoto.status}`}>
                                {currentPhoto.status.charAt(0).toUpperCase() + currentPhoto.status.slice(1)}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="detail-section">
                        <h4>AI Analysis</h4>
                        <div className="ai-flags">
                          {currentPhoto.aiFlags.map((flag, index) => (
                            <span key={index} className="ai-flag">
                              {flag.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    className="btn btn-secondary"
                    onClick={() => setShowPhotoModal(false)}
                  >
                    Close
                  </button>

                  {currentPhoto.status === 'PENDING' && (
                    <>
                      <button
                        className="btn btn-success"
                        onClick={() => handleApprovePhoto(currentPhoto)}
                      >
                        Approve
                      </button>
                      <button
                        className="btn btn-danger"
                        onClick={() => handleRejectPhoto(currentPhoto)}
                      >
                        Reject
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {/* Notification Toast */}
      {notification.show && (
        <div className={`notification-toast show notification-${notification.type}`}>
          <div className="notification-content">
            <span>{notification.message}</span>
          </div>
        </div>
      )}

      {/* Pagination */}
      {!loading && photos.length > 0 && (
        <div className="pagination-container">
          <div className="pagination">
            <button
              className="pagination-btn"
              disabled={page === 1}
              onClick={() => setPage(page - 1)}
            >
              Previous
            </button>
            <span className="pagination-info">Page {page} of {totalPages}</span>
            <button
              className="pagination-btn"
              disabled={page === totalPages}
              onClick={() => setPage(page + 1)}
            >
              Next
            </button>
          </div>
        </div>
      )}

      <style jsx>{`
        /* Tab Navigation */
        .tab-navigation {
          display: flex;
          margin-bottom: 20px;
          border-bottom: 1px solid #eee;
        }

        .tab-btn {
          padding: 10px 20px;
          background: none;
          border: none;
          border-bottom: 3px solid transparent;
          font-weight: 500;
          color: #666;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .tab-btn:hover {
          color: var(--primary);
        }

        .tab-btn.active {
          color: var(--primary);
          border-bottom-color: var(--primary);
        }

        /* Header Actions */
        .header-actions {
          display: flex;
          flex-direction: column;
          gap: 10px;
        }

        .action-buttons {
          display: flex;
          gap: 10px;
          margin-bottom: 10px;
        }

        /* Filter Buttons */
        .filter-buttons {
          display: flex;
          gap: 10px;
        }

        /* Pagination */
        .pagination-container {
          display: flex;
          justify-content: center;
          margin-top: 20px;
          margin-bottom: 30px;
        }

        .pagination {
          display: flex;
          align-items: center;
          gap: 15px;
        }

        .pagination-btn {
          padding: 8px 16px;
          background-color: var(--primary);
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        }

        .pagination-btn:disabled {
          background-color: #ccc;
          cursor: not-allowed;
        }

        .pagination-info {
          font-size: 0.9rem;
          color: #666;
        }

        /* Empty State */
        .empty-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 60px 20px;
          background-color: white;
          border-radius: 12px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          text-align: center;
        }

        .empty-icon {
          font-size: 3rem;
          margin-bottom: 20px;
          color: #ccc;
        }

        .empty-state h3 {
          font-size: 1.5rem;
          margin-bottom: 10px;
          color: var(--text-dark);
        }

        .empty-state p {
          color: #666;
          margin-bottom: 10px;
        }

        .mt-3 {
          margin-top: 15px;
        }

        /* Photo Grid */
        .photo-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
          gap: 20px;
          margin-bottom: 30px;
        }

        .photo-card {
          background-color: white;
          border-radius: 12px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          overflow: hidden;
          display: flex;
          flex-direction: column;
        }

        .photo-container {
          position: relative;
          aspect-ratio: 1 / 1;
          overflow: hidden;
        }

        .photo-container img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
          cursor: pointer;
        }

        .photo-container:hover img {
          transform: scale(1.05);
        }

        .photo-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        .photo-container:hover .photo-overlay {
          opacity: 1;
        }

        .overlay-btn {
          background-color: white;
          color: var(--text-dark);
          border: none;
          padding: 8px 16px;
          border-radius: 4px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .overlay-btn:hover {
          background-color: var(--primary);
          color: white;
        }

        .photo-info {
          padding: 15px;
          border-bottom: 1px solid #eee;
        }

        .photo-user {
          margin-bottom: 10px;
        }

        .user-name {
          font-weight: 600;
          color: var(--text-dark);
        }

        .user-details {
          font-size: 0.85rem;
          color: #666;
        }

        .photo-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .type-badge {
          display: inline-block;
          padding: 3px 8px;
          border-radius: 12px;
          font-size: 0.75rem;
          font-weight: 500;
        }

        .type-badge.profile {
          background-color: #e8f5e9;
          color: #2e7d32;
        }

        .type-badge.gallery {
          background-color: #e3f2fd;
          color: #1565c0;
        }

        .photo-time {
          font-size: 0.75rem;
          color: #999;
        }

        .photo-actions {
          padding: 15px;
          display: flex;
          justify-content: space-between;
          gap: 10px;
        }

        .status-badge {
          display: inline-block;
          padding: 5px 10px;
          border-radius: 12px;
          font-size: 0.85rem;
          font-weight: 500;
        }

        .status-badge.pending {
          background-color: #fff8e1;
          color: #ff8f00;
        }

        .status-badge.approved {
          background-color: #e8f5e9;
          color: #2e7d32;
        }

        .status-badge.rejected {
          background-color: #ffebee;
          color: #c62828;
        }

        /* Photo Modal */
        .photo-modal-content {
          max-width: 900px;
          max-height: 90vh;
        }

        .photo-review {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;
        }

        .photo-large {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f5f5f5;
          border-radius: 8px;
          overflow: hidden;
        }

        .photo-large img {
          max-width: 100%;
          max-height: 500px;
          object-fit: contain;
        }

        .photo-details {
          display: flex;
          flex-direction: column;
          gap: 20px;
        }

        .detail-section {
          margin-bottom: 15px;
        }

        .detail-section h4 {
          font-size: 1rem;
          font-weight: 600;
          margin-bottom: 10px;
          color: var(--text-dark);
          padding-bottom: 5px;
          border-bottom: 1px solid #eee;
        }

        .detail-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 10px;
        }

        .detail-item {
          display: flex;
          flex-direction: column;
          gap: 3px;
        }

        .detail-label {
          font-size: 0.8rem;
          color: #666;
        }

        .detail-value {
          font-weight: 500;
          color: var(--text-dark);
        }

        .ai-flags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }

        .ai-flag {
          display: inline-block;
          padding: 5px 10px;
          background-color: #f0f0f0;
          border-radius: 12px;
          font-size: 0.8rem;
          color: #555;
        }

        /* Success and Danger Buttons */
        .btn-success {
          background-color: var(--success);
          color: white;
          border-color: var(--success);
        }

        .btn-success:hover {
          background-color: #388e3c;
          border-color: #388e3c;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
          .photo-review {
            grid-template-columns: 1fr;
          }

          .photo-large img {
            max-height: 300px;
          }

          .filter-buttons {
            flex-wrap: wrap;
          }

          .detail-grid {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </EnhancedAdminLayout>
  );
}

