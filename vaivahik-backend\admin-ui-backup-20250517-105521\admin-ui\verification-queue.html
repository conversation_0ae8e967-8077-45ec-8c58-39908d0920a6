<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verification Queue - Vai<PERSON><PERSON>k Admin</title>
    <link rel="stylesheet" href="css/admin-styles.css">
    <link rel="icon" type="image/png" href="img/favicon.png">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>Admin Panel</h2>
                <button id="sidebarToggle" class="sidebar-toggle">☰</button>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item">
                        <a href="admin-dashboard.html" class="nav-link">
                            <span class="nav-icon">📊</span>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="all-users.html" class="nav-link">
                            <span class="nav-icon">👥</span>
                            <span class="nav-text">All Users</span>
                        </a>
                    </li>
                    <li class="nav-item active">
                        <a href="verification-queue.html" class="nav-link">
                            <span class="nav-icon">✅</span>
                            <span class="nav-text">Verification Queue</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="reported-profiles.html" class="nav-link">
                            <span class="nav-icon">⚠️</span>
                            <span class="nav-text">Reported Profiles</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Bar -->
            <div class="topbar">
                <div class="breadcrumb">
                    <a href="admin-dashboard.html">Dashboard</a> / Verification Queue
                </div>
                <div class="topbar-actions">
                    <button id="darkModeToggle" class="dark-mode-toggle" aria-label="Toggle Dark Mode">
                        <span class="light-icon">☀️</span>
                        <span class="dark-icon">🌙</span>
                    </button>
                    <div class="notifications">
                        <button class="notification-btn" aria-label="Notifications">
                            🔔
                            <span class="notification-badge">3</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Content Header -->
            <div class="content-header">
                <h2>Verification Queue</h2>
            </div>

            <!-- Table Controls -->
            <div class="table-controls">
                <div class="table-actions">
                    <select id="entriesSelect" class="entries-select">
                        <option value="10">10 entries</option>
                        <option value="25">25 entries</option>
                        <option value="50">50 entries</option>
                        <option value="100">100 entries</option>
                    </select>
                    <div class="search-box">
                        <input type="text" id="searchInput" placeholder="Search users...">
                        <button id="searchBtn">🔍</button>
                    </div>
                </div>
                <div class="filter-actions">
                    <select id="statusFilter" class="status-filter">
                        <option value="all">All Status</option>
                        <option value="pending">Pending</option>
                        <option value="approved">Approved</option>
                        <option value="rejected">Rejected</option>
                    </select>
                    <button id="refreshBtn" class="refresh-btn">↻ Refresh</button>
                </div>
            </div>

            <!-- Table Container -->
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Registration Date</th>
                            <th>Location</th>
                            <th>Verification Status</th>
                            <th>Submitted On</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="verificationTableBody">
                        <!-- Table content will be dynamically populated -->
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="table-footer">
                <div id="tableInfo" class="table-info">
                    Showing 0 to 0 of 0 entries
                </div>
                <div id="paginationControls" class="pagination">
                    <!-- Pagination will be dynamically populated -->
                </div>
            </div>
        </main>
    </div>

    <!-- Verification Details Modal -->
    <div class="modal-overlay" id="verificationModalOverlay">
        <div class="modal">
            <div class="modal-header">
                <h2 class="modal-title">Verification Details</h2>
                <button class="modal-close-button">&times;</button>
            </div>
            <div class="modal-body" id="verificationModalContent">
                <!-- Content will be dynamically loaded -->
            </div>
            <div class="modal-footer">
                <button class="filter-button" id="approveBtn">Approve</button>
                <button class="filter-button danger" id="rejectBtn">Reject</button>
            </div>
        </div>
    </div>

    <script src="js/verification-queue.js"></script>
</body>
</html>