/**
 * Admin Phase Management Routes
 * Handles switching between different matching algorithm phases
 */

const express = require('express');
const router = express.Router();
const PhaseManager = require('../../services/phaseManager');
const { authenticateToken, requireAdmin } = require('../../middleware/auth');

const phaseManager = new PhaseManager();

/**
 * @route GET /api/admin/phases
 * @desc Get all available phases
 * @access Admin
 */
router.get('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const phases = await phaseManager.getAvailablePhases();
    const currentPhase = await phaseManager.getCurrentPhase();
    
    res.json({
      success: true,
      currentPhase,
      availablePhases: phases,
      message: 'Phases retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting phases:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get phases',
      error: error.message
    });
  }
});

/**
 * @route GET /api/admin/phases/current
 * @desc Get current active phase
 * @access Admin
 */
router.get('/current', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const currentPhase = await phaseManager.getCurrentPhase();
    
    res.json({
      success: true,
      currentPhase,
      message: 'Current phase retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting current phase:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get current phase',
      error: error.message
    });
  }
});

/**
 * @route POST /api/admin/phases/switch
 * @desc Switch to a different phase
 * @access Admin
 */
router.post('/switch', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { targetVersion } = req.body;
    const adminUserId = req.user.id;

    if (!targetVersion) {
      return res.status(400).json({
        success: false,
        message: 'Target version is required'
      });
    }

    // Validate phase readiness
    const readiness = await phaseManager.validatePhaseReadiness(targetVersion);
    
    if (!readiness.isImplemented) {
      return res.status(400).json({
        success: false,
        message: `Phase ${targetVersion} is not yet implemented`,
        readiness
      });
    }

    // Perform phase switch
    const result = await phaseManager.switchPhase(targetVersion, adminUserId);
    
    res.json({
      success: true,
      ...result,
      readiness
    });

  } catch (error) {
    console.error('Error switching phase:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to switch phase',
      error: error.message
    });
  }
});

/**
 * @route GET /api/admin/phases/:version/readiness
 * @desc Check readiness for a specific phase
 * @access Admin
 */
router.get('/:version/readiness', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { version } = req.params;
    const readiness = await phaseManager.validatePhaseReadiness(version);
    
    res.json({
      success: true,
      version,
      readiness,
      message: 'Phase readiness checked successfully'
    });
  } catch (error) {
    console.error('Error checking phase readiness:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check phase readiness',
      error: error.message
    });
  }
});

/**
 * @route GET /api/admin/phases/:version/configuration
 * @desc Get configuration for a specific phase
 * @access Admin
 */
router.get('/:version/configuration', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { version } = req.params;
    const configuration = await phaseManager.getPhaseConfiguration(version);
    
    res.json({
      success: true,
      version,
      configuration,
      message: 'Phase configuration retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting phase configuration:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get phase configuration',
      error: error.message
    });
  }
});

/**
 * @route POST /api/admin/phases/preview
 * @desc Preview the impact of switching to a different phase
 * @access Admin
 */
router.post('/preview', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { targetVersion, sampleUserId } = req.body;

    if (!targetVersion) {
      return res.status(400).json({
        success: false,
        message: 'Target version is required'
      });
    }

    // Get current phase
    const currentPhase = await phaseManager.getCurrentPhase();
    
    // Get phase readiness
    const readiness = await phaseManager.validatePhaseReadiness(targetVersion);
    
    // Simulate impact (this would be more complex in real implementation)
    const impact = await simulatePhaseImpact(currentPhase.version, targetVersion, sampleUserId);
    
    res.json({
      success: true,
      currentPhase,
      targetPhase: {
        version: targetVersion,
        ...phaseManager.phases[targetVersion]
      },
      readiness,
      estimatedImpact: impact,
      message: 'Phase preview generated successfully'
    });

  } catch (error) {
    console.error('Error generating phase preview:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate phase preview',
      error: error.message
    });
  }
});

/**
 * @route GET /api/admin/phases/history
 * @desc Get phase change history
 * @access Admin
 */
router.get('/history', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { limit = 10, offset = 0 } = req.query;
    
    // Get phase change history from audit logs
    const history = await phaseManager.prisma.systemAuditLog.findMany({
      where: {
        action: 'PHASE_CHANGE'
      },
      orderBy: {
        timestamp: 'desc'
      },
      take: parseInt(limit),
      skip: parseInt(offset),
      include: {
        user: {
          select: {
            id: true,
            profile: {
              select: {
                fullName: true
              }
            }
          }
        }
      }
    });

    const total = await phaseManager.prisma.systemAuditLog.count({
      where: {
        action: 'PHASE_CHANGE'
      }
    });
    
    res.json({
      success: true,
      history,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total
      },
      message: 'Phase history retrieved successfully'
    });

  } catch (error) {
    console.error('Error getting phase history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get phase history',
      error: error.message
    });
  }
});

/**
 * Simulate the impact of switching phases
 */
async function simulatePhaseImpact(currentVersion, targetVersion, sampleUserId) {
  const impact = {
    expectedChanges: [],
    estimatedMetrics: {},
    warnings: [],
    benefits: []
  };

  // Phase-specific impact analysis
  switch (targetVersion) {
    case 'v1.5':
      impact.expectedChanges.push('Flexible preference matching enabled');
      impact.expectedChanges.push('Religion and caste compatibility groups activated');
      impact.estimatedMetrics.matchIncrease = '30-50%';
      impact.estimatedMetrics.userSatisfactionIncrease = '15-25%';
      impact.benefits.push('More diverse matches for users');
      impact.benefits.push('Reduced user complaints about restrictive matching');
      break;
      
    case 'v2.0':
      impact.expectedChanges.push('Behavioral learning algorithms activated');
      impact.expectedChanges.push('Dynamic preference adjustment enabled');
      impact.estimatedMetrics.matchRelevanceIncrease = '40-60%';
      impact.estimatedMetrics.engagementIncrease = '25-35%';
      impact.benefits.push('Personalized matching based on user behavior');
      impact.benefits.push('Improved match quality over time');
      impact.warnings.push('Requires sufficient user interaction data');
      break;
      
    case 'v2.5':
      impact.expectedChanges.push('Smart match explanations enabled');
      impact.expectedChanges.push('Predictive compatibility scoring activated');
      impact.estimatedMetrics.userTrustIncrease = '30-40%';
      impact.estimatedMetrics.conversionIncrease = '20-30%';
      impact.benefits.push('Better user understanding of matches');
      impact.benefits.push('Increased confidence in platform recommendations');
      break;
      
    case 'v3.0':
      impact.expectedChanges.push('Advanced AI algorithms activated');
      impact.expectedChanges.push('Multi-modal learning enabled');
      impact.estimatedMetrics.accuracyIncrease = '50-70%';
      impact.estimatedMetrics.successRateIncrease = '40-60%';
      impact.benefits.push('Industry-leading matching accuracy');
      impact.benefits.push('Comprehensive compatibility analysis');
      impact.warnings.push('Requires significant computational resources');
      break;
  }

  return impact;
}

module.exports = router;
