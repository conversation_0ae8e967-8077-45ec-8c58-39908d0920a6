/*
  Warnings:

  - You are about to drop the column `profile_picture_url` on the `profiles` table. All the data in the column will be lost.
  - You are about to drop the column `profile_picture_visibility` on the `profiles` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "profiles" DROP COLUMN "profile_picture_url",
DROP COLUMN "profile_picture_visibility";

-- CreateTable
CREATE TABLE "photos" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "visibility" "PhotoVisibility" NOT NULL DEFAULT 'PUBLIC',
    "is_profile_pic" BOOLEAN NOT NULL DEFAULT false,
    "uploaded_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "photos_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "photos" ADD CONSTRAINT "photos_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
