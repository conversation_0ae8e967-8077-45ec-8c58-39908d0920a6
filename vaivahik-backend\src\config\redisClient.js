// config/redisClient.js

const redis = require('redis'); // Using redis v4+ which is promise-based
require('dotenv').config(); // Ensure environment variables are loaded

// --- Redis Configuration ---
// Use environment variables for connection details
// Example REDIS_URL: redis://:password@hostname:port
// Or use individual variables: REDIS_HOST, REDIS_PORT, REDIS_PASSWORD
const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';

// --- Create Redis Client ---
// Note: redis.createClient() no longer takes connection options directly in v4.
// Use the url property within the options object.
const redisClient = redis.createClient({
    url: redisUrl,
    // You can add other options here if needed, e.g., socket options:
    // socket: {
    //   connectTimeout: 5000, // milliseconds
    //   reconnectStrategy: retries => Math.min(retries * 50, 1000) // Example reconnect strategy
    // }
});

// --- Event Listeners for Logging ---
redisClient.on('connect', () => {
    console.log('Connecting to Redis...');
});

redisClient.on('ready', () => {
    console.log('Redis client connected successfully and ready to use.');
});

redisClient.on('error', (err) => {
    console.error('Redis connection error:', err);
    // Depending on your strategy, you might want to exit the process
    // or implement more robust error handling/reconnection logic here.
    // Consider if the app can function without Redis or if it's critical.
});

redisClient.on('end', () => {
    console.log('Redis client disconnected.');
});

// --- Connect to Redis ---
// Asynchronously connect the client.
// Using an immediately-invoked async function (IIAF) to handle the promise.
(async () => {
    try {
        await redisClient.connect();
    } catch (err) {
        console.error('Failed to connect to Redis on initial startup:', err);
        // Handle connection failure (e.g., exit or retry logic)
        // If Redis is critical, you might want to exit:
        // process.exit(1);
    }
})();

// --- Graceful Shutdown (Recommended) ---
// Ensures Redis client disconnects properly when the Node.js process stops
const shutdown = async () => {
    console.log('\nShutting down Redis client...');
    try {
        // Check if client is connected before trying to quit
        if (redisClient.isReady) {
           await redisClient.quit();
           console.log('Redis client disconnected successfully.');
        } else {
           console.log('Redis client was not connected, no need to quit.');
        }
    } catch (err) {
        console.error('Error during Redis disconnection:', err);
    }
    process.exit(0); // Exit the Node process
};

// Listen for termination signals
process.on('SIGINT', shutdown); // Catches Ctrl+C
process.on('SIGTERM', shutdown); // Catches standard termination signals (e.g., from Docker/Kubernetes)

// --- Export the Client ---
// Export the client instance so it can be required in other modules
module.exports = redisClient;
