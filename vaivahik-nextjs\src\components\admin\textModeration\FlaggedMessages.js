import { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Pagination,
  IconButton,
  Tooltip
} from '@mui/material';
import ViewIcon from '@mui/icons-material/Visibility';
import ApproveIcon from '@mui/icons-material/CheckCircle';
import RejectIcon from '@mui/icons-material/Cancel';
import FlagIcon from '@mui/icons-material/Flag';
import { toast } from 'react-toastify';
import axiosInstance from '@/utils/axiosConfig';

export default function FlaggedMessages() {
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Pagination
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [limit, setLimit] = useState(10);

  // Filtering
  const [statusFilter, setStatusFilter] = useState('');

  // Message review dialog
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [reviewDecision, setReviewDecision] = useState('');
  const [adminNotes, setAdminNotes] = useState('');
  const [reviewing, setReviewing] = useState(false);

  // Message details dialog
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);

  // Fetch flagged messages on component mount and when filters change
  useEffect(() => {
    fetchFlaggedMessages();
  }, [page, limit, statusFilter]);

  // Fetch flagged messages from API
  const fetchFlaggedMessages = async () => {
    try {
      setLoading(true);
      setError(null);

      let url = `/api/admin/text-moderation/flagged-messages?page=${page}&limit=${limit}`;

      if (statusFilter) {
        url += `&status=${statusFilter}`;
      }

      const response = await axiosInstance.get(url);

      if (response.data.success) {
        setMessages(response.data.messages);
        setTotalPages(response.data.pagination.totalPages);
        setTotalItems(response.data.pagination.totalItems);
      } else {
        throw new Error(response.data.message || 'Failed to fetch flagged messages');
      }
    } catch (error) {
      console.error('Error fetching flagged messages:', error);
      setError(error.message || 'Failed to fetch flagged messages');

      // For development, use mock data if API fails
      if (process.env.NODE_ENV === 'development') {
        const mockMessages = Array(10).fill(null).map((_, index) => ({
          id: index + 1,
          content: `This is a test message ${index + 1} that might contain flagged content.`,
          moderationFlags: index % 3 === 0 ? 'profanity' : index % 3 === 1 ? 'contact_info' : 'spam',
          moderationStatus: index % 3 === 0 ? 'PENDING' : index % 3 === 1 ? 'APPROVED' : 'REJECTED',
          sentAt: new Date(Date.now() - index * 86400000).toISOString(),
          sender: {
            id: 100 + index,
            profile: {
              fullName: `Sender ${index + 1}`
            },
            email: `sender${index + 1}@example.com`
          },
          receiver: {
            id: 200 + index,
            profile: {
              fullName: `Receiver ${index + 1}`
            },
            email: `receiver${index + 1}@example.com`
          }
        }));

        setMessages(mockMessages);
        setTotalPages(3);
        setTotalItems(30);
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle page change
  const handlePageChange = (event, value) => {
    setPage(value);
  };

  // Handle status filter change
  const handleStatusFilterChange = (event) => {
    setStatusFilter(event.target.value);
    setPage(1); // Reset to first page when filter changes
  };

  // Open message details dialog
  const handleViewDetails = (message) => {
    setSelectedMessage(message);
    setDetailsDialogOpen(true);
  };

  // Open message review dialog
  const handleOpenReview = (message, initialDecision = '') => {
    setSelectedMessage(message);
    setReviewDecision(initialDecision);
    setAdminNotes('');
    setReviewDialogOpen(true);
  };

  // Close message review dialog
  const handleCloseReview = () => {
    setReviewDialogOpen(false);
    setSelectedMessage(null);
    setReviewDecision('');
    setAdminNotes('');
  };

  // Submit message review
  const handleSubmitReview = async () => {
    if (!reviewDecision) {
      toast.error('Please select a decision');
      return;
    }

    try {
      setReviewing(true);

      const response = await axiosInstance.put(`/api/admin/text-moderation/review/${selectedMessage.id}`, {
        decision: reviewDecision,
        adminNotes
      });

      if (response.data.success) {
        toast.success(`Message ${reviewDecision.toLowerCase()} successfully`);
        handleCloseReview();
        fetchFlaggedMessages(); // Refresh the list
      } else {
        throw new Error(response.data.message || 'Failed to review message');
      }
    } catch (error) {
      console.error('Error reviewing message:', error);
      toast.error(error.message || 'Failed to review message');

      // For development, simulate success
      if (process.env.NODE_ENV === 'development') {
        toast.success(`Message ${reviewDecision.toLowerCase()} successfully (simulated)`);
        handleCloseReview();

        // Update the message in the local state
        setMessages(prev => prev.map(msg =>
          msg.id === selectedMessage.id
            ? { ...msg, moderationStatus: reviewDecision }
            : msg
        ));
      }
    } finally {
      setReviewing(false);
    }
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Get flag chip color
  const getFlagColor = (flag) => {
    switch (flag) {
      case 'profanity':
        return 'error';
      case 'contact_info':
        return 'warning';
      case 'spam':
        return 'info';
      default:
        return 'default';
    }
  };

  // Get status chip color
  const getStatusColor = (status) => {
    switch (status) {
      case 'APPROVED':
        return 'success';
      case 'REJECTED':
        return 'error';
      case 'PENDING':
        return 'warning';
      default:
        return 'default';
    }
  };

  if (loading && messages.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Flagged Messages
            </Typography>

            <FormControl sx={{ minWidth: 200 }}>
              <InputLabel>Filter by Status</InputLabel>
              <Select
                value={statusFilter}
                onChange={handleStatusFilterChange}
                label="Filter by Status"
              >
                <MenuItem value="">All Statuses</MenuItem>
                <MenuItem value="APPROVED">Approved</MenuItem>
                <MenuItem value="REJECTED">Rejected</MenuItem>
                <MenuItem value="PENDING">Pending</MenuItem>
              </Select>
            </FormControl>
          </Box>

          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Sender</TableCell>
                  <TableCell>Message</TableCell>
                  <TableCell>Flags</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {messages.length > 0 ? (
                  messages.map((message) => (
                    <TableRow key={message.id}>
                      <TableCell>
                        {message.sender.profile?.fullName || 'Unknown'}
                      </TableCell>
                      <TableCell>
                        <Typography noWrap sx={{ maxWidth: 200 }}>
                          {message.content}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {message.moderationFlags ? (
                          message.moderationFlags.split(',').map((flag) => (
                            <Chip
                              key={flag}
                              label={flag}
                              color={getFlagColor(flag)}
                              size="small"
                              sx={{ mr: 0.5, mb: 0.5 }}
                            />
                          ))
                        ) : (
                          <Chip label="None" size="small" />
                        )}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={message.moderationStatus || 'PENDING'}
                          color={getStatusColor(message.moderationStatus)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {formatDate(message.sentAt)}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex' }}>
                          <Tooltip title="View Details">
                            <IconButton
                              color="primary"
                              onClick={() => handleViewDetails(message)}
                              size="small"
                            >
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>

                          <Tooltip title="Approve">
                            <IconButton
                              color="success"
                              onClick={() => handleOpenReview(message, 'APPROVED')}
                              size="small"
                              disabled={message.moderationStatus === 'APPROVED'}
                            >
                              <ApproveIcon />
                            </IconButton>
                          </Tooltip>

                          <Tooltip title="Reject">
                            <IconButton
                              color="error"
                              onClick={() => handleOpenReview(message, 'REJECTED')}
                              size="small"
                              disabled={message.moderationStatus === 'REJECTED'}
                            >
                              <RejectIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <Typography variant="body1">
                        No flagged messages found
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={handlePageChange}
                color="primary"
              />
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Message Details Dialog */}
      <Dialog
        open={detailsDialogOpen}
        onClose={() => setDetailsDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Message Details
        </DialogTitle>
        <DialogContent dividers>
          {selectedMessage && (
            <Box>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1">Sender</Typography>
                  <Typography variant="body1">
                    {selectedMessage.sender.profile?.fullName || 'Unknown'}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {selectedMessage.sender.email || selectedMessage.sender.phone}
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1">Receiver</Typography>
                  <Typography variant="body1">
                    {selectedMessage.receiver.profile?.fullName || 'Unknown'}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {selectedMessage.receiver.email || selectedMessage.receiver.phone}
                  </Typography>
                </Grid>

                <Grid item xs={12}>
                  <Divider sx={{ my: 1 }} />
                  <Typography variant="subtitle1">Message Content</Typography>
                  <Paper variant="outlined" sx={{ p: 2, mt: 1, bgcolor: 'background.default' }}>
                    <Typography variant="body1">
                      {selectedMessage.content}
                    </Typography>
                  </Paper>
                </Grid>

                {selectedMessage.moderatedContent && selectedMessage.moderatedContent !== selectedMessage.content && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle1">Moderated Content</Typography>
                    <Paper variant="outlined" sx={{ p: 2, mt: 1, bgcolor: 'background.default' }}>
                      <Typography variant="body1">
                        {selectedMessage.moderatedContent}
                      </Typography>
                    </Paper>
                  </Grid>
                )}

                <Grid item xs={12}>
                  <Divider sx={{ my: 1 }} />
                  <Typography variant="subtitle1">Moderation Details</Typography>
                  <Box sx={{ mt: 1 }}>
                    <Typography variant="body2">
                      <strong>Status:</strong>{' '}
                      <Chip
                        label={selectedMessage.moderationStatus || 'PENDING'}
                        color={getStatusColor(selectedMessage.moderationStatus)}
                        size="small"
                      />
                    </Typography>
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      <strong>Flags:</strong>{' '}
                      {selectedMessage.moderationFlags ? (
                        selectedMessage.moderationFlags.split(',').map((flag) => (
                          <Chip
                            key={flag}
                            label={flag}
                            color={getFlagColor(flag)}
                            size="small"
                            sx={{ mr: 0.5, mb: 0.5 }}
                          />
                        ))
                      ) : (
                        'None'
                      )}
                    </Typography>
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      <strong>Sent At:</strong> {formatDate(selectedMessage.sentAt)}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsDialogOpen(false)}>
            Close
          </Button>
          {selectedMessage && selectedMessage.moderationStatus !== 'APPROVED' && (
            <Button
              color="success"
              variant="contained"
              onClick={() => {
                setDetailsDialogOpen(false);
                handleOpenReview(selectedMessage, 'APPROVED');
              }}
            >
              Approve
            </Button>
          )}
          {selectedMessage && selectedMessage.moderationStatus !== 'REJECTED' && (
            <Button
              color="error"
              variant="contained"
              onClick={() => {
                setDetailsDialogOpen(false);
                handleOpenReview(selectedMessage, 'REJECTED');
              }}
            >
              Reject
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* Message Review Dialog */}
      <Dialog
        open={reviewDialogOpen}
        onClose={handleCloseReview}
      >
        <DialogTitle>
          Review Message
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            Please confirm your decision for this message:
          </DialogContentText>

          {selectedMessage && (
            <Paper variant="outlined" sx={{ p: 2, my: 2, bgcolor: 'background.default' }}>
              <Typography variant="body2" color="textSecondary">
                From: {selectedMessage.sender.profile?.fullName || 'Unknown'}
              </Typography>
              <Typography variant="body1" sx={{ mt: 1 }}>
                {selectedMessage.content}
              </Typography>
            </Paper>
          )}

          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>Decision</InputLabel>
            <Select
              value={reviewDecision}
              onChange={(e) => setReviewDecision(e.target.value)}
              label="Decision"
            >
              <MenuItem value="APPROVED">Approve</MenuItem>
              <MenuItem value="REJECTED">Reject</MenuItem>
            </Select>
          </FormControl>

          <TextField
            label="Admin Notes"
            multiline
            rows={3}
            value={adminNotes}
            onChange={(e) => setAdminNotes(e.target.value)}
            fullWidth
            margin="normal"
            placeholder="Optional notes about this decision"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseReview}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmitReview}
            variant="contained"
            color={reviewDecision === 'APPROVED' ? 'success' : 'error'}
            disabled={!reviewDecision || reviewing}
          >
            {reviewing ? 'Submitting...' : 'Submit Decision'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
