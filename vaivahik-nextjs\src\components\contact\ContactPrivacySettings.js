/**
 * Contact Privacy Settings Component
 * Allows users to control their contact reveal preferences
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  FormControl,
  FormControlLabel,
  FormLabel,
  RadioGroup,
  Radio,
  Switch,
  Typography,
  Box,
  Button,
  Alert,
  Chip,
  Divider,
  CircularProgress
} from '@mui/material';
import {
  Phone as PhoneIcon,
  Security as SecurityIcon,
  AccessTime as AccessTimeIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import { contactApi, contactUtils } from '@/services/contactApiService';

const ContactPrivacySettings = () => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState({
    allowDirectCalls: true,
    contactRevealPreference: 'PREMIUM_ONLY',
    requireMutualInterest: true,
    callAvailability: 'ANYTIME'
  });
  const [options, setOptions] = useState({
    contactRevealPreferences: [],
    callAvailability: []
  });
  const [message, setMessage] = useState(null);

  useEffect(() => {
    loadSettings();
    loadOptions();
  }, []);

  const loadSettings = async () => {
    try {
      const result = await contactApi.getPrivacySettings();
      if (result.success) {
        setSettings(result.settings);
      }
    } catch (error) {
      console.error('Error loading contact settings:', error);
      setMessage({
        type: 'error',
        text: 'Failed to load contact settings'
      });
    } finally {
      setLoading(false);
    }
  };

  const loadOptions = async () => {
    try {
      const result = await contactApi.getContactOptions();
      if (result.success) {
        setOptions(result.options);
      }
    } catch (error) {
      console.error('Error loading contact options:', error);
    }
  };

  const handleSettingChange = (field, value) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    setSaving(true);
    setMessage(null);

    try {
      const result = await contactApi.updatePrivacySettings(settings);
      
      if (result.success) {
        setMessage({
          type: 'success',
          text: 'Contact privacy settings updated successfully!'
        });
      } else {
        throw new Error(result.message || 'Failed to update settings');
      }
    } catch (error) {
      console.error('Error saving contact settings:', error);
      setMessage({
        type: 'error',
        text: error.message || 'Failed to update contact settings'
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader
        avatar={<PhoneIcon />}
        title="Contact & Calling Privacy"
        subheader="Control who can access your contact details and call you"
      />
      
      <CardContent>
        {message && (
          <Alert 
            severity={message.type} 
            sx={{ mb: 3 }}
            onClose={() => setMessage(null)}
          >
            {message.text}
          </Alert>
        )}

        {/* Allow Direct Calls */}
        <Box sx={{ mb: 3 }}>
          <FormControlLabel
            control={
              <Switch
                checked={settings.allowDirectCalls}
                onChange={(e) => handleSettingChange('allowDirectCalls', e.target.checked)}
              />
            }
            label={
              <Box>
                <Typography variant="subtitle1">Allow Direct Calls</Typography>
                <Typography variant="body2" color="text.secondary">
                  Allow other users to access your contact details for calling
                </Typography>
              </Box>
            }
          />
        </Box>

        <Divider sx={{ my: 3 }} />

        {/* Contact Reveal Preference */}
        <Box sx={{ mb: 3 }}>
          <FormControl component="fieldset" fullWidth>
            <FormLabel component="legend" sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <SecurityIcon />
                <Typography variant="subtitle1">Who Can Access Your Contact</Typography>
              </Box>
            </FormLabel>
            
            <RadioGroup
              value={settings.contactRevealPreference}
              onChange={(e) => handleSettingChange('contactRevealPreference', e.target.value)}
            >
              {options.contactRevealPreferences.map((option) => (
                <FormControlLabel
                  key={option.value}
                  value={option.value}
                  control={<Radio />}
                  label={
                    <Box sx={{ ml: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <span>{option.icon}</span>
                        <Typography variant="body1">{option.label}</Typography>
                        {option.value === 'PREMIUM_ONLY' && (
                          <Chip label="Recommended" size="small" color="primary" />
                        )}
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {option.description}
                      </Typography>
                    </Box>
                  }
                  sx={{ 
                    alignItems: 'flex-start',
                    mb: 1,
                    '& .MuiRadio-root': { mt: 0.5 }
                  }}
                />
              ))}
            </RadioGroup>
          </FormControl>
        </Box>

        <Divider sx={{ my: 3 }} />

        {/* Call Availability */}
        <Box sx={{ mb: 3 }}>
          <FormControl component="fieldset" fullWidth>
            <FormLabel component="legend" sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <AccessTimeIcon />
                <Typography variant="subtitle1">When You're Available for Calls</Typography>
              </Box>
            </FormLabel>
            
            <RadioGroup
              value={settings.callAvailability}
              onChange={(e) => handleSettingChange('callAvailability', e.target.value)}
            >
              {options.callAvailability.map((option) => (
                <FormControlLabel
                  key={option.value}
                  value={option.value}
                  control={<Radio />}
                  label={
                    <Box sx={{ ml: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <span>{option.icon}</span>
                        <Typography variant="body1">{option.label}</Typography>
                        {contactUtils.isCurrentlyAvailable(option.value) && (
                          <Chip label="Available Now" size="small" color="success" />
                        )}
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {option.description}
                      </Typography>
                    </Box>
                  }
                  sx={{ 
                    alignItems: 'flex-start',
                    mb: 1,
                    '& .MuiRadio-root': { mt: 0.5 }
                  }}
                />
              ))}
            </RadioGroup>
          </FormControl>
        </Box>

        {/* Current Status Summary */}
        <Box sx={{ mb: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="subtitle2" gutterBottom>
            Current Settings Summary:
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            <Chip
              label={settings.allowDirectCalls ? 'Calls Enabled' : 'Calls Disabled'}
              color={settings.allowDirectCalls ? 'success' : 'error'}
              size="small"
            />
            <Chip
              label={contactUtils.getRevealPreferenceText(settings.contactRevealPreference)}
              color="primary"
              size="small"
            />
            <Chip
              label={`Available: ${contactUtils.getAvailabilityText(settings.callAvailability)}`}
              color="info"
              size="small"
            />
          </Box>
        </Box>

        {/* Save Button */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            startIcon={saving ? <CircularProgress size={16} /> : <SaveIcon />}
            onClick={handleSave}
            disabled={saving}
          >
            {saving ? 'Saving...' : 'Save Settings'}
          </Button>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ContactPrivacySettings;
