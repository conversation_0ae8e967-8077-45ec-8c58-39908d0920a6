// API endpoint for algorithm settings
import { withAuth } from '@/utils/authHandler';
import { handleApiError } from '@/utils/errorHandler';
import axios from 'axios';

// Backend API URL - adjust this to your actual backend URL
const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:8000/api';

// Main handler function
async function handler(req, res) {
  // Handle different HTTP methods
  try {
    switch (req.method) {
      case 'GET':
        return await getAlgorithmSettings(req, res);
      case 'PUT':
        return await updateAlgorithmSettings(req, res);
      case 'POST':
        return await createAlgorithmModel(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    return handleApiError(error, res, 'Algorithm settings API');
  }
}

// Export the handler with authentication middleware
export default withAuth(handler, 'ADMIN');

// GET /api/admin/algorithm-settings
async function getAlgorithmSettings(req, res) {
  try {
    // Construct the API URL with query parameters
    let apiUrl = `${BACKEND_API_URL}/admin/algorithm-settings`;

    // Add query parameters if they exist
    const queryParams = [];
    if (req.query.includeABTestResults === 'true') {
      queryParams.push('includeABTestResults=true');
    }
    if (req.query.includeMetrics === 'true') {
      queryParams.push('includeMetrics=true');
    }

    if (queryParams.length > 0) {
      apiUrl += '?' + queryParams.join('&');
    }

    try {
      // Fetch data from the backend API
      const response = await axios.get(apiUrl);

      // Return the response directly from the backend
      return res.status(200).json(response.data);
    } catch (apiError) {
      // Log the error
      console.error('Error fetching algorithm settings from backend API:', apiError.message);

      // Return mock data for development
      if (process.env.NODE_ENV === 'development') {
        return res.status(200).json(getMockAlgorithmSettings(req.query));
      }

      // Return a meaningful error message for production
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch algorithm settings from backend API.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available',
        note: "Please ensure the backend server is running and accessible."
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Get algorithm settings');
  }
}

// PUT /api/admin/algorithm-settings
async function updateAlgorithmSettings(req, res) {
  try {
    const { settings, modelId } = req.body;

    // Validate the request
    if (!settings && !modelId) {
      return res.status(400).json({
        success: false,
        message: 'No settings or model provided'
      });
    }

    try {
      // Send the update to the backend API
      const response = await axios.put(`${BACKEND_API_URL}/admin/algorithm-settings`, {
        settings,
        modelId
      });

      // Return the response from the backend
      return res.status(200).json(response.data);
    } catch (apiError) {
      // Log the error
      console.error('Error updating algorithm settings via backend API:', apiError.message);

      // Return mock success response for development
      if (process.env.NODE_ENV === 'development') {
        return res.status(200).json({
          success: true,
          message: 'Algorithm settings updated successfully (mock mode)',
          settings: settings
        });
      }

      // Return a meaningful error message for production
      return res.status(500).json({
        success: false,
        message: 'Failed to update algorithm settings via backend API.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available',
        note: "Please ensure the backend server is running and accessible."
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Update algorithm settings');
  }
}

// POST /api/admin/algorithm-settings
async function createAlgorithmModel(req, res) {
  try {
    const { model } = req.body;

    // Validate the request
    if (!model) {
      return res.status(400).json({
        success: false,
        message: 'No model data provided'
      });
    }

    // Validate required fields
    if (!model.name || !model.version || !model.type) {
      return res.status(400).json({
        success: false,
        message: 'Name, version, and type are required'
      });
    }

    try {
      // Send the create request to the backend API
      const response = await axios.post(`${BACKEND_API_URL}/admin/algorithm-settings`, {
        model
      });

      // Return the response from the backend
      return res.status(201).json(response.data);
    } catch (apiError) {
      // Log the error
      console.error('Error creating algorithm model via backend API:', apiError.message);

      // Return a meaningful error message
      return res.status(500).json({
        success: false,
        message: 'Failed to create algorithm model via backend API.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available',
        note: "Please ensure the backend server is running and accessible."
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Create algorithm model');
  }
}

// Helper function to generate mock algorithm settings
function getMockAlgorithmSettings(query = {}) {
  const includeABTestResults = query.includeABTestResults === 'true';
  const includeMetrics = query.includeMetrics === 'true';

  const mockData = {
    success: true,
    settings: {
      matchingAlgorithmVersion: 'v1.0',
      enableAIMatching: true,
      matchingModel: 'TWO_TOWER',
      weights: {
        ageWeight: 8,
        heightWeight: 6,
        educationWeight: 7,
        occupationWeight: 7,
        locationWeight: 8,
        casteWeight: 9,
        subCasteWeight: 5,
        gotraWeight: 6,
        incomeWeight: 5,
        lifestyleWeight: 4
      },
      minimumMatchScore: 65,
      highQualityMatchThreshold: 80,
      abTestingEnabled: true,
      abTestingVariant: 'B',
      abTestingDistribution: 50,
      maxDistanceKm: 100,
      maxAgeDifference: 10,
      considerUserActivity: true,
      boostNewProfiles: true,
      boostNewProfilesDays: 7,
      boostVerifiedProfiles: true,
      boostVerifiedProfilesAmount: 10,
      boostPremiumProfiles: true,
      boostPremiumProfilesAmount: 15,
      twoTower: {
        embeddingSize: 128,
        learningRate: 0.001,
        batchSize: 64,
        epochs: 10,
        userTowerLayers: [128, 64],
        matchTowerLayers: [128, 64],
        dropoutRate: 0.2,
        similarityMetric: 'cosine'
      }
    },
    message: 'Algorithm settings retrieved successfully (mock data)'
  };

  if (includeABTestResults) {
    mockData.abTestResults = {
      variantA: {
        users: 1250,
        matches: 187,
        successRate: 14.96,
        userSatisfaction: 4.1
      },
      variantB: {
        users: 1180,
        matches: 201,
        successRate: 17.03,
        userSatisfaction: 4.3
      }
    };
  }

  if (includeMetrics) {
    mockData.metrics = {
      totalMatches: 5842,
      successfulMatches: 2156,
      averageMatchScore: 72.4,
      matchDistribution: [12, 18, 25, 30, 15],
      monthlyTrend: [120, 145, 160, 178, 195, 210],
      performanceMetrics: {
        accuracy: 87.3,
        precision: 84.6,
        recall: 89.1,
        f1Score: 86.8
      }
    };
  }

  return mockData;
}
