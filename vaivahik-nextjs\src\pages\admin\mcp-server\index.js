import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  Alert,
  CircularProgress,
  Switch,
  FormControlLabel,
  Tooltip,
  Stack,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  LinearProgress
} from '@mui/material';
import {
  SmartToy as AIIcon,
  PlayArrow as StartIcon,
  Stop as StopIcon,
  Refresh as RestartIcon,
  Settings as SettingsIcon,
  Assessment as AnalyticsIcon,
  Build as ToolsIcon,
  Storage as ResourcesIcon,
  Psychology as PromptsIcon,
  People as ClientsIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Speed as SpeedIcon,
  Memory as MemoryIcon,
  NetworkCheck as NetworkIcon,
  Timeline as TimelineIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { adminGet, adminPost, adminPut } from '@/utils/api';

// Dynamic import for EnhancedAdminLayout to avoid SSR issues
const EnhancedAdminLayout = dynamic(() => import('@/components/admin/EnhancedAdminLayout'), {
  ssr: false
});

export default function MCPServerManagement() {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [serverStatus, setServerStatus] = useState({});
  const [analytics, setAnalytics] = useState({});
  const [tools, setTools] = useState([]);
  const [resources, setResources] = useState([]);
  const [prompts, setPrompts] = useState([]);
  const [clients, setClients] = useState([]);
  const [testDialogOpen, setTestDialogOpen] = useState(false);
  const [selectedTool, setSelectedTool] = useState('');
  const [testArgs, setTestArgs] = useState('{}');
  const [testResult, setTestResult] = useState(null);

  useEffect(() => {
    fetchData();
    const interval = setInterval(fetchData, 10000); // Refresh every 10 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        fetchServerStatus(),
        fetchAnalytics(),
        fetchTools(),
        fetchResources(),
        fetchPrompts(),
        fetchClients()
      ]);
    } catch (error) {
      console.error('Error fetching MCP data:', error);
      toast.error('Error fetching MCP data');
    } finally {
      setLoading(false);
    }
  };

  const fetchServerStatus = async () => {
    try {
      const response = await adminGet('/mcp/status');
      if (response.success) {
        setServerStatus(response.data);
      }
    } catch (error) {
      console.error('Error fetching server status:', error);
    }
  };

  const fetchAnalytics = async () => {
    try {
      const response = await adminGet('/mcp/analytics');
      if (response.success) {
        setAnalytics(response.data);
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
    }
  };

  const fetchTools = async () => {
    try {
      const response = await adminGet('/mcp/tools');
      if (response.success) {
        setTools(response.data);
      }
    } catch (error) {
      console.error('Error fetching tools:', error);
    }
  };

  const fetchResources = async () => {
    try {
      const response = await adminGet('/mcp/resources');
      if (response.success) {
        setResources(response.data);
      }
    } catch (error) {
      console.error('Error fetching resources:', error);
    }
  };

  const fetchPrompts = async () => {
    try {
      const response = await adminGet('/mcp/prompts');
      if (response.success) {
        setPrompts(response.data);
      }
    } catch (error) {
      console.error('Error fetching prompts:', error);
    }
  };

  const fetchClients = async () => {
    try {
      const response = await adminGet('/mcp/clients');
      if (response.success) {
        setClients(response.data);
      }
    } catch (error) {
      console.error('Error fetching clients:', error);
    }
  };

  const handleServerAction = async (action) => {
    try {
      const response = await adminPost(`/mcp/${action}`);
      if (response.success) {
        toast.success(response.message);
        await fetchServerStatus();
      } else {
        toast.error(response.message);
      }
    } catch (error) {
      toast.error(`Error ${action}ing server: ${error.message}`);
    }
  };

  const handleTestTool = async () => {
    try {
      let args = {};
      try {
        args = JSON.parse(testArgs);
      } catch (e) {
        toast.error('Invalid JSON in test arguments');
        return;
      }

      const response = await adminPost('/mcp/tools/test', {
        toolName: selectedTool,
        arguments: args
      });

      if (response.success) {
        setTestResult(response.data);
        toast.success('Tool test completed');
      } else {
        toast.error(response.message);
      }
    } catch (error) {
      toast.error(`Error testing tool: ${error.message}`);
    }
  };

  const getStatusChip = (isRunning, health) => {
    if (isRunning && health?.healthy) {
      return <Chip icon={<CheckCircleIcon />} label="Running" color="success" />;
    } else if (isRunning && !health?.healthy) {
      return <Chip icon={<WarningIcon />} label="Unhealthy" color="warning" />;
    } else {
      return <Chip icon={<ErrorIcon />} label="Stopped" color="error" />;
    }
  };

  const formatUptime = (uptime) => {
    if (!uptime) return 'N/A';
    const seconds = Math.floor(uptime / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  };

  const TabPanel = ({ children, value, index, ...other }) => (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`mcp-tabpanel-${index}`}
      aria-labelledby={`mcp-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );

  return (
    <EnhancedAdminLayout title="MCP Server Management">
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            MCP Server Management
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<StartIcon />}
              onClick={() => handleServerAction('start')}
              disabled={serverStatus.isRunning}
            >
              Start
            </Button>
            <Button
              variant="outlined"
              startIcon={<StopIcon />}
              onClick={() => handleServerAction('stop')}
              disabled={!serverStatus.isRunning}
              color="error"
            >
              Stop
            </Button>
            <Button
              variant="contained"
              startIcon={<RestartIcon />}
              onClick={() => handleServerAction('restart')}
            >
              Restart
            </Button>
          </Box>
        </Box>

        {/* Status Overview */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Server Status
                    </Typography>
                    {getStatusChip(serverStatus.isRunning, serverStatus.health)}
                  </Box>
                  <AIIcon color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Connected Clients
                    </Typography>
                    <Typography variant="h4" component="div">
                      {serverStatus.serverStatus?.connectedClients || 0}
                    </Typography>
                  </Box>
                  <ClientsIcon color="success" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Uptime
                    </Typography>
                    <Typography variant="h6" component="div">
                      {formatUptime(serverStatus.serverStatus?.uptime)}
                    </Typography>
                  </Box>
                  <TimelineIcon color="info" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Port
                    </Typography>
                    <Typography variant="h4" component="div">
                      {serverStatus.port || 8001}
                    </Typography>
                  </Box>
                  <NetworkIcon color="secondary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Performance Metrics */}
        {analytics.performanceMetrics && (
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    CPU Usage
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <LinearProgress
                      variant="determinate"
                      value={analytics.performanceMetrics.cpuUsage}
                      sx={{ flexGrow: 1, height: 8, borderRadius: 4 }}
                      color={analytics.performanceMetrics.cpuUsage > 80 ? 'error' : 'primary'}
                    />
                    <Typography variant="body2">
                      {analytics.performanceMetrics.cpuUsage?.toFixed(1)}%
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Memory Usage
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <LinearProgress
                      variant="determinate"
                      value={analytics.performanceMetrics.memoryUsage}
                      sx={{ flexGrow: 1, height: 8, borderRadius: 4 }}
                      color={analytics.performanceMetrics.memoryUsage > 80 ? 'error' : 'primary'}
                    />
                    <Typography variant="body2">
                      {analytics.performanceMetrics.memoryUsage?.toFixed(1)}%
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Active Connections
                  </Typography>
                  <Typography variant="h4" component="div">
                    {analytics.performanceMetrics.activeConnections || 0}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {/* Tabs */}
        <Paper sx={{ mb: 3 }}>
          <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
            <Tab label="Tools" icon={<ToolsIcon />} />
            <Tab label="Resources" icon={<ResourcesIcon />} />
            <Tab label="Prompts" icon={<PromptsIcon />} />
            <Tab label="Analytics" icon={<AnalyticsIcon />} />
            <Tab label="Settings" icon={<SettingsIcon />} />
          </Tabs>
        </Paper>

        {/* Tab Panels */}
        <TabPanel value={activeTab} index={0}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Registered Tools ({tools.length})</Typography>
            <Button
              variant="contained"
              onClick={() => setTestDialogOpen(true)}
              disabled={tools.length === 0}
            >
              Test Tool
            </Button>
          </Box>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>Input Schema</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {tools.map((tool, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Typography variant="body1" fontWeight="600">
                        {tool.name}
                      </Typography>
                    </TableCell>
                    <TableCell>{tool.description}</TableCell>
                    <TableCell>
                      <Typography variant="caption" fontFamily="monospace">
                        {tool.inputSchema?.properties ? Object.keys(tool.inputSchema.properties).join(', ') : 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Button
                        size="small"
                        onClick={() => {
                          setSelectedTool(tool.name);
                          setTestDialogOpen(true);
                        }}
                      >
                        Test
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          <Typography variant="h6" gutterBottom>
            Registered Resources ({resources.length})
          </Typography>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>URI</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>MIME Type</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {resources.map((resource, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Typography variant="body1" fontWeight="600">
                        {resource.name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontFamily="monospace">
                        {resource.uri}
                      </Typography>
                    </TableCell>
                    <TableCell>{resource.description}</TableCell>
                    <TableCell>
                      <Chip label={resource.mimeType} size="small" variant="outlined" />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={activeTab} index={2}>
          <Typography variant="h6" gutterBottom>
            Registered Prompts ({prompts.length})
          </Typography>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>Arguments</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {prompts.map((prompt, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Typography variant="body1" fontWeight="600">
                        {prompt.name}
                      </Typography>
                    </TableCell>
                    <TableCell>{prompt.description}</TableCell>
                    <TableCell>
                      {prompt.arguments?.map((arg, i) => (
                        <Chip key={i} label={arg.name} size="small" variant="outlined" sx={{ mr: 0.5 }} />
                      ))}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={activeTab} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Request Statistics
                  </Typography>
                  <Stack spacing={2}>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Total Requests
                      </Typography>
                      <Typography variant="h4">
                        {analytics.totalRequests?.toLocaleString() || 0}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Success Rate
                      </Typography>
                      <Typography variant="h5">
                        {analytics.totalRequests ? 
                          ((analytics.successfulRequests / analytics.totalRequests) * 100).toFixed(1) : 0}%
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Average Response Time
                      </Typography>
                      <Typography variant="h5">
                        {analytics.averageResponseTime || 0}ms
                      </Typography>
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Tool Usage
                  </Typography>
                  <Stack spacing={1}>
                    {analytics.toolUsage && Object.entries(analytics.toolUsage).map(([tool, count]) => (
                      <Box key={tool} sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2">{tool}</Typography>
                        <Typography variant="body2" fontWeight="600">{count}</Typography>
                      </Box>
                    ))}
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={activeTab} index={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                MCP Server Configuration
              </Typography>
              <Alert severity="info" sx={{ mb: 2 }}>
                Server configuration settings and advanced options.
              </Alert>
              <Typography variant="body2" color="text.secondary">
                Configuration options for port, auto-restart, and other server settings would be displayed here.
              </Typography>
            </CardContent>
          </Card>
        </TabPanel>

        {/* Test Tool Dialog */}
        <Dialog open={testDialogOpen} onClose={() => setTestDialogOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>Test MCP Tool</DialogTitle>
          <DialogContent>
            <Stack spacing={3} sx={{ mt: 1 }}>
              <FormControl fullWidth>
                <InputLabel>Select Tool</InputLabel>
                <Select
                  value={selectedTool}
                  label="Select Tool"
                  onChange={(e) => setSelectedTool(e.target.value)}
                >
                  {tools.map((tool) => (
                    <MenuItem key={tool.name} value={tool.name}>
                      {tool.name} - {tool.description}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <TextField
                fullWidth
                multiline
                rows={4}
                label="Test Arguments (JSON)"
                value={testArgs}
                onChange={(e) => setTestArgs(e.target.value)}
                placeholder='{"userId": "test123", "limit": 5}'
                helperText="Enter test arguments as JSON"
              />
              {testResult && (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Test Result:
                  </Typography>
                  <Paper sx={{ p: 2, bgcolor: 'grey.100' }}>
                    <Typography variant="body2" fontFamily="monospace" component="pre">
                      {JSON.stringify(testResult, null, 2)}
                    </Typography>
                  </Paper>
                </Box>
              )}
            </Stack>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setTestDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleTestTool} variant="contained" disabled={!selectedTool}>
              Test Tool
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </EnhancedAdminLayout>
  );
}
