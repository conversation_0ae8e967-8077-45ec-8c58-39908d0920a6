import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Tabs,
  Tab,
  TextField,
  Button,
  Grid,
  FormControl,
  FormLabel,
  Divider,
  Chip,
  Select,
  MenuItem,
  InputAdornment,
  IconButton,
  Collapse,
  Tooltip,
  Card,
  CardContent,
  Badge,
  Autocomplete,
  Slider,
  Stack,
  useMediaQuery,
  useTheme,
  Fade,
  Zoom,
  Popper,
  ClickAwayListener,
  Grow
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import PersonIcon from '@mui/icons-material/Person';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import TuneIcon from '@mui/icons-material/Tune';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import WorkIcon from '@mui/icons-material/Work';
import SchoolIcon from '@mui/icons-material/School';
import CakeIcon from '@mui/icons-material/Cake';
import HeightIcon from '@mui/icons-material/Height';
import PaidIcon from '@mui/icons-material/Paid';
import CloseIcon from '@mui/icons-material/Close';
import HistoryIcon from '@mui/icons-material/History';
import FavoriteIcon from '@mui/icons-material/Favorite';
import HeightRangeSelector from '@/components/search/HeightRangeSelector';
import { formatHeight } from '@/utils/heightUtils';

/**
 * Search Bar Component
 *
 * A comprehensive search interface for matrimony applications
 * with elegant animations, intuitive controls, and comprehensive filtering
 */
const SearchBar = ({
  onSearch,
  savedSearches = [],
  onSaveSearch,
  userGender = 'MALE', // The current user's gender
  recentSearches = []
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));

  // Search mode tabs
  const [searchMode, setSearchMode] = useState(0); // 0: Regular Search, 1: ID Search

  // Active filter section
  const [activeFilter, setActiveFilter] = useState(null);

  // Search parameters
  const [searchParams, setSearchParams] = useState({
    // Basic search params (always visible)
    ageFrom: userGender === 'MALE' ? 18 : 21,
    ageTo: userGender === 'MALE' ? 35 : 40,
    heightFrom: 53, // 4'5"
    heightTo: 77, // 6'5"
    location: '',
    occupation: [],
    incomeRange: '',

    // Religious & Community
    religion: 'HINDU',
    caste: 'MARATHA',
    subCaste: '',
    gotra: '',
    manglik: 'DOESNT_MATTER',

    // Education & Career
    education: [],

    // Lifestyle
    diet: '',
    smoking: 'DOESNT_MATTER',
    drinking: 'DOESNT_MATTER',

    // Appearance & Status
    maritalStatus: [],
    withPhoto: true,
    profileCreatedWithin: '',
    profileType: [], // VERIFIED, PREMIUM, etc.

    // ID search params
    userId: ''
  });

  // Count of active filters
  const [activeFilterCount, setActiveFilterCount] = useState(0);

  // Calculate active filter count on parameter changes
  useEffect(() => {
    let count = 0;

    if (searchParams.location) count++;
    if (searchParams.occupation.length > 0) count++;
    if (searchParams.incomeRange) count++;
    if (searchParams.religion !== 'HINDU') count++;
    if (searchParams.caste !== 'MARATHA') count++;
    if (searchParams.subCaste) count++;
    if (searchParams.gotra) count++;
    if (searchParams.manglik !== 'DOESNT_MATTER') count++;
    if (searchParams.education.length > 0) count++;
    if (searchParams.diet) count++;
    if (searchParams.smoking !== 'DOESNT_MATTER') count++;
    if (searchParams.drinking !== 'DOESNT_MATTER') count++;
    if (searchParams.maritalStatus.length > 0) count++;
    if (searchParams.profileCreatedWithin) count++;
    if (searchParams.profileType.length > 0) count++;

    setActiveFilterCount(count);
  }, [searchParams]);

  // Handle search mode change
  const handleSearchModeChange = (_, newValue) => {
    setSearchMode(newValue);
    setActiveFilter(null);
  };

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams({ ...searchParams, [name]: value });
  };

  // Handle multi-select change
  const handleMultiSelectChange = (name, value) => {
    setSearchParams({ ...searchParams, [name]: value });
  };

  // Handle height range change
  const handleHeightChange = (min, max) => {
    setSearchParams({
      ...searchParams,
      heightFrom: min,
      heightTo: max
    });
  };

  // Toggle filter section
  const handleFilterClick = (filter) => {
    setActiveFilter(activeFilter === filter ? null : filter);
  };

  // Handle search submission
  const handleSearch = () => {
    // Prepare search data based on active tab
    let searchData = {};

    if (searchMode === 0) { // Regular Search
      searchData = {
        searchType: 'REGULAR',
        targetGender: userGender === 'MALE' ? 'FEMALE' : 'MALE',
        ...searchParams
      };
    } else { // ID Search
      searchData = {
        searchType: 'ID',
        userId: searchParams.userId
      };
    }

    // Close any open filter
    setActiveFilter(null);

    // Call the search handler
    onSearch(searchData);
  };

  // Save current search
  const handleSaveSearch = () => {
    if (onSaveSearch) {
      onSaveSearch({
        ...searchParams,
        searchMode
      });
    }
  };

  // Clear all filters
  const handleClearFilters = () => {
    setSearchParams({
      ...searchParams,
      location: '',
      occupation: [],
      incomeRange: '',
      religion: 'HINDU',
      caste: 'MARATHA',
      subCaste: '',
      gotra: '',
      manglik: 'DOESNT_MATTER',
      education: [],
      diet: '',
      smoking: 'DOESNT_MATTER',
      drinking: 'DOESNT_MATTER',
      maritalStatus: [],
      profileCreatedWithin: '',
      profileType: []
    });
  };

  // Generate age options
  const generateAgeOptions = (min, max) => {
    const options = [];
    for (let i = min; i <= max; i++) {
      options.push(i);
    }
    return options;
  };

  // Age options based on target gender (opposite of user)
  const targetGender = userGender === 'MALE' ? 'FEMALE' : 'MALE';
  const minAge = targetGender === 'FEMALE' ? 18 : 21;
  const ageFromOptions = generateAgeOptions(minAge, 60);
  const ageToOptions = generateAgeOptions(
    Math.max(minAge, searchParams.ageFrom),
    70
  );

  // Occupation options
  const occupationOptions = [
    { value: 'IT_SOFTWARE', label: 'IT/Software' },
    { value: 'ENGINEERING', label: 'Engineering' },
    { value: 'MEDICAL', label: 'Medical' },
    { value: 'FINANCE', label: 'Finance' },
    { value: 'EDUCATION', label: 'Education' },
    { value: 'BUSINESS', label: 'Business' },
    { value: 'GOVERNMENT', label: 'Government' },
    { value: 'OTHER', label: 'Other' }
  ];

  // Income range options
  const incomeOptions = [
    { value: '', label: 'Any Income' },
    { value: 'UPTO_3L', label: 'Upto 3 Lakhs' },
    { value: '3L_5L', label: '3 - 5 Lakhs' },
    { value: '5L_10L', label: '5 - 10 Lakhs' },
    { value: '10L_20L', label: '10 - 20 Lakhs' },
    { value: 'ABOVE_20L', label: 'Above 20 Lakhs' }
  ];

  // Religion options
  const religionOptions = [
    { value: 'HINDU', label: 'Hindu' },
    { value: 'MUSLIM', label: 'Muslim' },
    { value: 'CHRISTIAN', label: 'Christian' },
    { value: 'SIKH', label: 'Sikh' },
    { value: 'JAIN', label: 'Jain' },
    { value: 'BUDDHIST', label: 'Buddhist' },
    { value: 'OTHER', label: 'Other' }
  ];

  // State for advanced filters
  const [advancedFilterTab, setAdvancedFilterTab] = useState(0);

  // Handle advanced filter tab change
  const handleAdvancedFilterTabChange = (_, newValue) => {
    setAdvancedFilterTab(newValue);
  };

  // Render filter popper content
  const renderFilterContent = () => {
    // Filter content implementation
    // (This is a placeholder - the actual implementation is quite long)
    return <Box>Filter content</Box>;
  };

  return (
    <Paper
      elevation={3}
      sx={{
        borderRadius: 2,
        overflow: 'hidden',
        transition: 'all 0.3s ease'
      }}
    >
      {/* Search Tabs */}
      <Tabs
        value={searchMode}
        onChange={handleSearchModeChange}
        variant="fullWidth"
        sx={{
          bgcolor: theme.palette.primary.main,
          color: 'white',
          '& .MuiTab-root': {
            color: 'rgba(255,255,255,0.7)',
            fontWeight: 500,
            py: 1.5
          },
          '& .Mui-selected': {
            color: 'white',
            fontWeight: 600
          },
          '& .MuiTabs-indicator': {
            height: 3,
            bgcolor: 'white'
          }
        }}
      >
        <Tab
          label="Regular Search"
          icon={<SearchIcon />}
          iconPosition="start"
        />
        <Tab
          label="Search by ID"
          icon={<PersonIcon />}
          iconPosition="start"
        />
      </Tabs>

      {/* Search Form */}
      <Box sx={{ p: 2 }}>
        {/* Regular Search Form */}
        {searchMode === 0 && (
          <>
            {/* Basic Search Fields */}
            <Grid container spacing={2} alignItems="center">
              {/* Age Range */}
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  variant={activeFilter === 'age' ? 'contained' : 'outlined'}
                  color="primary"
                  fullWidth
                  onClick={() => handleFilterClick('age')}
                  endIcon={activeFilter === 'age' ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  sx={{ justifyContent: 'space-between', py: 1 }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <CakeIcon sx={{ mr: 1 }} />
                    <Typography>
                      {searchParams.ageFrom}-{searchParams.ageTo} yrs
                    </Typography>
                  </Box>
                </Button>
              </Grid>

              {/* Height Range */}
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  variant={activeFilter === 'height' ? 'contained' : 'outlined'}
                  color="primary"
                  fullWidth
                  onClick={() => handleFilterClick('height')}
                  endIcon={activeFilter === 'height' ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  sx={{ justifyContent: 'space-between', py: 1 }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <HeightIcon sx={{ mr: 1 }} />
                    <Typography>
                      {formatHeight(Math.floor(searchParams.heightFrom / 12), searchParams.heightFrom % 12)} - 
                      {formatHeight(Math.floor(searchParams.heightTo / 12), searchParams.heightTo % 12)}
                    </Typography>
                  </Box>
                </Button>
              </Grid>

              {/* Search Button */}
              <Grid item xs={12} sm={12} md={3}>
                <Button
                  variant="contained"
                  color="primary"
                  fullWidth
                  onClick={handleSearch}
                  startIcon={<SearchIcon />}
                  sx={{ py: 1 }}
                >
                  Search
                </Button>
              </Grid>
            </Grid>
          </>
        )}

        {/* ID Search Form */}
        {searchMode === 1 && (
          <Box sx={{ py: 1 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={9}>
                <TextField
                  name="userId"
                  value={searchParams.userId}
                  onChange={handleInputChange}
                  placeholder="Enter Profile ID (e.g., VAI12345)"
                  fullWidth
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <PersonIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={3}>
                <Button
                  variant="contained"
                  color="primary"
                  fullWidth
                  onClick={handleSearch}
                  startIcon={<SearchIcon />}
                  sx={{ py: 1 }}
                >
                  Search
                </Button>
              </Grid>
            </Grid>
          </Box>
        )}
      </Box>
    </Paper>
  );
};

export default SearchBar;
