// src/routes/docs.js

const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');

/**
 * @route   GET /api/docs/openapi.json
 * @desc    Get OpenAPI specification
 * @access  Public
 */
router.get('/openapi.json', (req, res) => {
  try {
    // Define a basic OpenAPI specification
    const spec = {
      openapi: '3.0.0',
      info: {
        title: 'Vaivahik AI API',
        version: '1.0.0',
        description: 'API documentation for Vaivahik AI Platform',
        contact: {
          name: 'Vaivahik Support',
          email: '<EMAIL>',
          url: 'https://vaivahik.com/contact'
        }
      },
      servers: [
        {
          url: process.env.API_URL || 'http://localhost:8000',
          description: 'Development server'
        },
        {
          url: 'https://api.vaivahik.com',
          description: 'Production server'
        }
      ],
      paths: {
        '/api/users': {
          get: {
            summary: 'Get all users',
            description: 'Retrieve a list of all users',
            tags: ['Users'],
            responses: {
              '200': {
                description: 'Successful operation',
                content: {
                  'application/json': {
                    schema: {
                      type: 'object',
                      properties: {
                        success: {
                          type: 'boolean',
                          example: true
                        },
                        users: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              id: {
                                type: 'string',
                                example: '1'
                              },
                              name: {
                                type: 'string',
                                example: 'John Doe'
                              },
                              email: {
                                type: 'string',
                                example: '<EMAIL>'
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        '/api/admin/dashboard': {
          get: {
            summary: 'Get dashboard data',
            description: 'Retrieve dashboard statistics and data',
            tags: ['Admin'],
            responses: {
              '200': {
                description: 'Successful operation',
                content: {
                  'application/json': {
                    schema: {
                      type: 'object',
                      properties: {
                        success: {
                          type: 'boolean',
                          example: true
                        },
                        stats: {
                          type: 'object',
                          properties: {
                            totalUsers: {
                              type: 'integer',
                              example: 1000
                            },
                            activeUsers: {
                              type: 'integer',
                              example: 500
                            },
                            premiumUsers: {
                              type: 'integer',
                              example: 200
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    };
    
    // Return specification
    return res.status(200).json(spec);
  } catch (error) {
    console.error('Error generating OpenAPI specification', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to generate OpenAPI specification'
    });
  }
});

/**
 * @route   GET /api/docs/endpoints
 * @desc    Get API endpoints
 * @access  Public
 */
router.get('/endpoints', (req, res) => {
  try {
    // Define endpoints
    const endpoints = [
      {
        path: '/api/users',
        method: 'GET',
        summary: 'Get all users',
        description: 'Retrieve a list of all users'
      },
      {
        path: '/api/admin/dashboard',
        method: 'GET',
        summary: 'Get dashboard data',
        description: 'Retrieve dashboard statistics and data'
      }
    ];
    
    // Return endpoints
    return res.status(200).json({
      success: true,
      message: 'API endpoints retrieved successfully',
      endpoints
    });
  } catch (error) {
    console.error('Error retrieving API endpoints', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve API endpoints'
    });
  }
});

module.exports = router;
