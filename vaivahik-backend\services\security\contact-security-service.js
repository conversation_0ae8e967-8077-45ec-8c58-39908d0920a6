/**
 * Contact Security Service
 * Advanced security measures to prevent contact number theft
 * Protects against fake profiles and marriage bureau abuse
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * Advanced security checks before revealing contact
 */
const performSecurityChecks = async (accessorId, contactOwnerId, platform) => {
  try {
    const securityFlags = [];
    let riskScore = 0;
    let blockAccess = false;

    // Get accessor profile with detailed information
    const accessor = await prisma.user.findUnique({
      where: { id: accessorId },
      include: {
        profile: true,
        verificationDocuments: {
          where: { status: 'APPROVED' }
        },
        contactAccessGiven: {
          where: {
            accessedAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
            }
          }
        },
        interactions: {
          where: {
            timestamp: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
            }
          }
        }
      }
    });

    if (!accessor) {
      return {
        allowed: false,
        reason: 'USER_NOT_FOUND',
        riskScore: 100
      };
    }

    // 1. Profile Completeness Check
    const profileCompleteness = calculateProfileCompleteness(accessor.profile);
    if (profileCompleteness < 70) {
      securityFlags.push('INCOMPLETE_PROFILE');
      riskScore += 25;
    }

    // 2. Document Verification Check
    if (accessor.verificationDocuments.length === 0) {
      securityFlags.push('NO_DOCUMENT_VERIFICATION');
      riskScore += 30;
    }

    // 3. Account Age Check (minimum 7 days)
    const accountAge = Date.now() - new Date(accessor.createdAt).getTime();
    const minAccountAge = 7 * 24 * 60 * 60 * 1000; // 7 days
    if (accountAge < minAccountAge) {
      securityFlags.push('NEW_ACCOUNT');
      riskScore += 20;
    }

    // 4. Contact Access Frequency Check
    const dailyContactAccess = accessor.contactAccessGiven.length;
    if (dailyContactAccess > 10) {
      securityFlags.push('HIGH_CONTACT_ACCESS_FREQUENCY');
      riskScore += 40;
      if (dailyContactAccess > 20) {
        blockAccess = true;
      }
    }

    // 5. Interaction Pattern Analysis
    const interactionTypes = accessor.interactions.map(i => i.interactionType);
    const uniqueTargets = new Set(accessor.interactions.map(i => i.targetUserId)).size;
    const totalInteractions = accessor.interactions.length;
    
    if (totalInteractions > 100 && uniqueTargets < 10) {
      securityFlags.push('SUSPICIOUS_INTERACTION_PATTERN');
      riskScore += 35;
    }

    // 6. Profile Photo Analysis
    if (!accessor.profile?.profilePic) {
      securityFlags.push('NO_PROFILE_PHOTO');
      riskScore += 15;
    }

    // 7. Phone Number Pattern Check
    if (isPhoneNumberSuspicious(accessor.phone)) {
      securityFlags.push('SUSPICIOUS_PHONE_PATTERN');
      riskScore += 25;
    }

    // 8. Email Domain Check
    if (accessor.email && isSuspiciousEmailDomain(accessor.email)) {
      securityFlags.push('SUSPICIOUS_EMAIL_DOMAIN');
      riskScore += 20;
    }

    // 9. Geographic Consistency Check
    const geoConsistency = await checkGeographicConsistency(accessor);
    if (!geoConsistency.consistent) {
      securityFlags.push('GEOGRAPHIC_INCONSISTENCY');
      riskScore += 15;
    }

    // 10. Behavioral Analysis
    const behaviorAnalysis = await analyzeBehaviorPattern(accessorId);
    if (behaviorAnalysis.suspicious) {
      securityFlags.push('SUSPICIOUS_BEHAVIOR');
      riskScore += behaviorAnalysis.riskScore;
    }

    // Final Decision
    if (riskScore >= 80 || blockAccess) {
      return {
        allowed: false,
        reason: 'HIGH_RISK_PROFILE',
        riskScore,
        securityFlags,
        requiresManualReview: true
      };
    }

    if (riskScore >= 50) {
      return {
        allowed: true,
        reason: 'MEDIUM_RISK_APPROVED',
        riskScore,
        securityFlags,
        requiresAdditionalVerification: true
      };
    }

    return {
      allowed: true,
      reason: 'LOW_RISK_APPROVED',
      riskScore,
      securityFlags
    };

  } catch (error) {
    console.error('Security check error:', error);
    return {
      allowed: false,
      reason: 'SECURITY_CHECK_FAILED',
      riskScore: 100
    };
  }
};

/**
 * Calculate profile completeness percentage
 */
const calculateProfileCompleteness = (profile) => {
  if (!profile) return 0;

  const requiredFields = [
    'fullName', 'gender', 'dateOfBirth', 'religion', 'caste',
    'motherTongue', 'height', 'city', 'state', 'country'
  ];

  const optionalFields = [
    'occupation', 'education', 'income', 'familyType',
    'fatherOccupation', 'motherOccupation', 'siblings'
  ];

  let score = 0;
  let maxScore = 0;

  // Required fields (70% weight)
  requiredFields.forEach(field => {
    maxScore += 7;
    if (profile[field]) score += 7;
  });

  // Optional fields (30% weight)
  optionalFields.forEach(field => {
    maxScore += 3;
    if (profile[field]) score += 3;
  });

  return Math.round((score / maxScore) * 100);
};

/**
 * Check if phone number follows suspicious patterns
 */
const isPhoneNumberSuspicious = (phone) => {
  if (!phone) return true;

  // Check for common fake number patterns
  const suspiciousPatterns = [
    /^(\+91)?[0-9]{10}$/, // Too simple patterns
    /^(\+91)?1234567890$/, // Sequential numbers
    /^(\+91)?9999999999$/, // Repeated digits
    /^(\+91)?0000000000$/, // All zeros
  ];

  // Check for repeated digits (more than 6 same digits)
  const digits = phone.replace(/[^\d]/g, '');
  for (let i = 0; i <= 9; i++) {
    const repeated = i.toString().repeat(7);
    if (digits.includes(repeated)) return true;
  }

  return false;
};

/**
 * Check for suspicious email domains
 */
const isSuspiciousEmailDomain = (email) => {
  const suspiciousDomains = [
    'tempmail.org', '10minutemail.com', 'guerrillamail.com',
    'mailinator.com', 'throwaway.email', 'temp-mail.org'
  ];

  const domain = email.split('@')[1]?.toLowerCase();
  return suspiciousDomains.includes(domain);
};

/**
 * Check geographic consistency
 */
const checkGeographicConsistency = async (user) => {
  // Check if profile location matches phone number area code
  // Check if IP location history is consistent
  // This is a simplified version - implement based on your needs
  
  return {
    consistent: true, // Placeholder
    confidence: 0.8
  };
};

/**
 * Analyze behavior patterns for suspicious activity
 */
const analyzeBehaviorPattern = async (userId) => {
  const recentActivity = await prisma.userInteraction.findMany({
    where: {
      userId,
      timestamp: {
        gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      }
    },
    orderBy: { timestamp: 'desc' }
  });

  let suspiciousScore = 0;

  // Check for rapid-fire interactions
  for (let i = 1; i < recentActivity.length; i++) {
    const timeDiff = recentActivity[i-1].timestamp - recentActivity[i].timestamp;
    if (timeDiff < 5000) { // Less than 5 seconds between interactions
      suspiciousScore += 5;
    }
  }

  // Check for bot-like patterns
  const interactionTypes = recentActivity.map(a => a.interactionType);
  const uniqueTypes = new Set(interactionTypes).size;
  if (uniqueTypes === 1 && recentActivity.length > 20) {
    suspiciousScore += 20; // Only one type of interaction repeatedly
  }

  return {
    suspicious: suspiciousScore > 30,
    riskScore: Math.min(suspiciousScore, 50)
  };
};

/**
 * Log security events for monitoring
 */
const logSecurityEvent = async (eventData) => {
  try {
    await prisma.securityLog.create({
      data: {
        userId: eventData.userId,
        eventType: eventData.eventType,
        riskScore: eventData.riskScore,
        securityFlags: eventData.securityFlags?.join(','),
        details: JSON.stringify(eventData.details),
        ipAddress: eventData.ipAddress,
        userAgent: eventData.userAgent,
        platform: eventData.platform
      }
    });
  } catch (error) {
    console.error('Failed to log security event:', error);
  }
};

module.exports = {
  performSecurityChecks,
  calculateProfileCompleteness,
  isPhoneNumberSuspicious,
  isSuspiciousEmailDomain,
  checkGeographicConsistency,
  analyzeBehaviorPattern,
  logSecurityEvent
};
