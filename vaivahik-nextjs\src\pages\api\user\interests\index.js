import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  if (req.method === 'GET') {
    return await getInterests(req, res);
  } else if (req.method === 'POST') {
    return await sendInterest(req, res);
  } else {
    return res.status(405).json({ message: 'Method not allowed' });
  }
}

async function getInterests(req, res) {
  try {
    // Verify JWT token
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ success: false, message: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const userId = decoded.userId;

    // Get received interests
    const receivedInterests = await prisma.interest.findMany({
      where: {
        targetUserId: userId
      },
      include: {
        user: {
          include: {
            profile: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Get sent interests
    const sentInterests = await prisma.interest.findMany({
      where: {
        userId: userId
      },
      include: {
        targetUser: {
          include: {
            profile: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Format the data
    const formattedReceived = receivedInterests.map(interest => ({
      id: interest.id,
      status: interest.status,
      message: interest.message,
      createdAt: interest.createdAt,
      user: {
        id: interest.user.id,
        firstName: interest.user.profile?.firstName || 'Unknown',
        lastName: interest.user.profile?.lastName || '',
        age: interest.user.profile?.age,
        location: `${interest.user.profile?.city || ''}, ${interest.user.profile?.state || ''}`.trim().replace(/^,|,$/, ''),
        occupation: interest.user.profile?.occupation,
        profilePicture: interest.user.profile?.profilePicUrl
      }
    }));

    const formattedSent = sentInterests.map(interest => ({
      id: interest.id,
      status: interest.status,
      message: interest.message,
      createdAt: interest.createdAt,
      user: {
        id: interest.targetUser.id,
        firstName: interest.targetUser.profile?.firstName || 'Unknown',
        lastName: interest.targetUser.profile?.lastName || '',
        age: interest.targetUser.profile?.age,
        location: `${interest.targetUser.profile?.city || ''}, ${interest.targetUser.profile?.state || ''}`.trim().replace(/^,|,$/, ''),
        occupation: interest.targetUser.profile?.occupation,
        profilePicture: interest.targetUser.profile?.profilePicUrl
      }
    }));

    return res.status(200).json({
      success: true,
      data: {
        received: formattedReceived,
        sent: formattedSent
      }
    });

  } catch (error) {
    console.error('Error fetching interests:', error);
    return res.status(500).json({
      success: false,
      message: 'Error fetching interests',
      error: error.message
    });
  }
}

async function sendInterest(req, res) {
  try {
    // Verify JWT token
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ success: false, message: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const userId = decoded.userId;

    const { targetUserId, message } = req.body;

    if (!targetUserId) {
      return res.status(400).json({
        success: false,
        message: 'Target user ID is required'
      });
    }

    // Check if interest already exists
    const existingInterest = await prisma.interest.findFirst({
      where: {
        userId: userId,
        targetUserId: targetUserId
      }
    });

    if (existingInterest) {
      return res.status(400).json({
        success: false,
        message: 'Interest already sent to this user'
      });
    }

    // Check if target user exists
    const targetUser = await prisma.user.findUnique({
      where: { id: targetUserId }
    });

    if (!targetUser) {
      return res.status(404).json({
        success: false,
        message: 'Target user not found'
      });
    }

    // Create the interest
    const interest = await prisma.interest.create({
      data: {
        userId: userId,
        targetUserId: targetUserId,
        message: message || 'I found your profile interesting and would like to connect.',
        status: 'PENDING'
      },
      include: {
        targetUser: {
          include: {
            profile: true
          }
        }
      }
    });

    // Create notification for target user
    await prisma.notification.create({
      data: {
        userId: targetUserId,
        type: 'INTEREST_RECEIVED',
        title: 'New Interest Received',
        message: `Someone has expressed interest in your profile`,
        data: JSON.stringify({
          interestId: interest.id,
          fromUserId: userId
        })
      }
    });

    // Track user interaction
    await prisma.userInteraction.create({
      data: {
        userId: userId,
        targetUserId: targetUserId,
        interactionType: 'INTEREST_SENT',
        metadata: JSON.stringify({
          message: message
        })
      }
    });

    return res.status(201).json({
      success: true,
      message: 'Interest sent successfully',
      data: {
        interestId: interest.id,
        status: interest.status,
        targetUser: {
          id: interest.targetUser.id,
          firstName: interest.targetUser.profile?.firstName,
          lastName: interest.targetUser.profile?.lastName
        }
      }
    });

  } catch (error) {
    console.error('Error sending interest:', error);
    return res.status(500).json({
      success: false,
      message: 'Error sending interest',
      error: error.message
    });
  }
}
