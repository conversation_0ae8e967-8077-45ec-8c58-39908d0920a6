<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Minimalist Biodata</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Open+Sans:wght@300;400;600&display=swap');
        
        :root {
            --primary-color: #2c3e50;
            --accent-color: #3498db;
            --text-color: #333;
            --light-text: #777;
            --border-color: #eee;
            --light-bg: #f8f9fa;
            --header-font: 'Montserrat', sans-serif;
            --body-font: 'Open Sans', sans-serif;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--body-font);
            color: var(--text-color);
            line-height: 1.6;
            background-color: white;
            padding: 0;
            margin: 0;
        }
        
        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 30px;
            background-color: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.05);
            position: relative;
        }
        
        /* Invocation */
        .invocation {
            text-align: center;
            font-family: var(--header-font);
            color: var(--primary-color);
            padding: 10px 0;
            font-weight: 500;
            font-size: 16px;
            letter-spacing: 1px;
            margin-bottom: 30px;
        }
        
        /* Header Section */
        .header {
            display: flex;
            margin-bottom: 40px;
            position: relative;
        }
        
        .profile-photo-container {
            width: 40%;
            padding-right: 30px;
        }
        
        .profile-photo {
            width: 100%;
            height: auto;
            object-fit: cover;
            border-radius: 4px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .header-content {
            width: 60%;
            padding-left: 30px;
            border-left: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .name {
            font-family: var(--header-font);
            font-size: 36px;
            color: var(--primary-color);
            margin-bottom: 10px;
            font-weight: 600;
            letter-spacing: -0.5px;
        }
        
        .tagline {
            font-size: 16px;
            color: var(--light-text);
            margin-bottom: 20px;
            font-weight: 300;
            letter-spacing: 0.5px;
        }
        
        .quick-info {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 20px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            font-size: 14px;
        }
        
        .info-icon {
            width: 24px;
            height: 24px;
            background-color: var(--accent-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-size: 12px;
        }
        
        .info-label {
            font-weight: 600;
            margin-right: 5px;
            color: var(--primary-color);
        }
        
        /* Section Styling */
        .section {
            margin-bottom: 35px;
        }
        
        .section-title {
            font-family: var(--header-font);
            color: var(--primary-color);
            font-size: 18px;
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-bottom: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        
        .section-title:after {
            content: '';
            flex: 1;
            height: 1px;
            background-color: var(--border-color);
            margin-left: 15px;
        }
        
        .section-content {
            padding: 0 5px;
        }
        
        /* Grid Layout */
        .grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
        }
        
        /* Details Cards */
        .details-card {
            background-color: var(--light-bg);
            border-radius: 4px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .details-list {
            list-style: none;
        }
        
        .details-list li {
            padding: 8px 0;
            display: flex;
            border-bottom: 1px solid var(--border-color);
        }
        
        .details-list li:last-child {
            border-bottom: none;
        }
        
        .details-label {
            width: 40%;
            font-weight: 600;
            color: var(--primary-color);
            font-size: 14px;
        }
        
        .details-value {
            width: 60%;
            font-size: 14px;
        }
        
        /* Timeline */
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline:before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 1px;
            background-color: var(--accent-color);
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 25px;
        }
        
        .timeline-item:before {
            content: '';
            position: absolute;
            left: -30px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: white;
            border: 2px solid var(--accent-color);
        }
        
        .timeline-title {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 5px;
            font-size: 16px;
        }
        
        .timeline-subtitle {
            font-size: 14px;
            color: var(--accent-color);
            margin-bottom: 5px;
        }
        
        .timeline-content {
            font-size: 14px;
            color: var(--light-text);
        }
        
        /* About & Expectations */
        .about-section {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            margin-bottom: 35px;
        }
        
        .about-card {
            padding: 25px;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .about-me {
            background-color: var(--light-bg);
            border-left: 3px solid var(--primary-color);
        }
        
        .expectations {
            background-color: white;
            border: 1px solid var(--border-color);
            border-left: 3px solid var(--accent-color);
        }
        
        .about-title {
            font-family: var(--header-font);
            font-size: 18px;
            color: var(--primary-color);
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        /* Footer */
        .footer {
            margin-top: 40px;
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
            font-size: 14px;
            color: var(--light-text);
        }
        
        .branding {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 10px;
        }
        
        .brand-logo {
            height: 30px;
            margin-right: 10px;
        }
        
        .brand-name {
            font-weight: 500;
            color: var(--primary-color);
            letter-spacing: 1px;
        }
        
        /* Print Styles */
        @media print {
            body {
                background-color: white;
            }
            
            .container {
                box-shadow: none;
                padding: 0;
                max-width: 100%;
            }
            
            @page {
                size: A4;
                margin: 1cm;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Invocation -->
        <div class="invocation">
            ॥ श्री गणेशाय नमः ॥
        </div>
        
        <!-- Header Section -->
        <div class="header">
            <div class="profile-photo-container">
                <img src="{{profilePicture}}" alt="Profile Photo" class="profile-photo">
            </div>
            <div class="header-content">
                <h1 class="name">{{name}}</h1>
                <p class="tagline">{{tagline}}</p>
                <div class="quick-info">
                    <div class="info-item">
                        <div class="info-icon">A</div>
                        <span class="info-label">Age:</span> {{age}} years
                    </div>
                    <div class="info-item">
                        <div class="info-icon">H</div>
                        <span class="info-label">Height:</span> {{height}}
                    </div>
                    <div class="info-item">
                        <div class="info-icon">E</div>
                        <span class="info-label">Education:</span> {{education}}
                    </div>
                    <div class="info-item">
                        <div class="info-icon">P</div>
                        <span class="info-label">Profession:</span> {{occupation}}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Personal & Family Details -->
        <div class="section">
            <h2 class="section-title">Personal Information</h2>
            <div class="section-content">
                <div class="grid">
                    <!-- Personal Details -->
                    <div class="details-card">
                        <ul class="details-list">
                            <li>
                                <div class="details-label">Date of Birth</div>
                                <div class="details-value">{{dateOfBirth}}</div>
                            </li>
                            <li>
                                <div class="details-label">Birth Place</div>
                                <div class="details-value">{{birthPlace}}</div>
                            </li>
                            <li>
                                <div class="details-label">Religion</div>
                                <div class="details-value">{{religion}}</div>
                            </li>
                            <li>
                                <div class="details-label">Caste</div>
                                <div class="details-value">{{caste}}</div>
                            </li>
                            <li>
                                <div class="details-label">Gotra</div>
                                <div class="details-value">{{gotra}}</div>
                            </li>
                            <li>
                                <div class="details-label">Marital Status</div>
                                <div class="details-value">{{maritalStatus}}</div>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- Family Details -->
                    <div class="details-card">
                        <ul class="details-list">
                            <li>
                                <div class="details-label">Father</div>
                                <div class="details-value">{{fatherName}}</div>
                            </li>
                            <li>
                                <div class="details-label">Occupation</div>
                                <div class="details-value">{{fatherOccupation}}</div>
                            </li>
                            <li>
                                <div class="details-label">Mother</div>
                                <div class="details-value">{{motherName}}</div>
                            </li>
                            <li>
                                <div class="details-label">Occupation</div>
                                <div class="details-value">{{motherOccupation}}</div>
                            </li>
                            <li>
                                <div class="details-label">Family Type</div>
                                <div class="details-value">{{familyType}}</div>
                            </li>
                            <li>
                                <div class="details-label">Siblings</div>
                                <div class="details-value">{{siblings}}</div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Education & Career -->
        <div class="section">
            <h2 class="section-title">Education & Career</h2>
            <div class="section-content">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-title">{{education}}</div>
                        <div class="timeline-subtitle">{{educationDetails}}</div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-title">{{occupation}}</div>
                        <div class="timeline-subtitle">{{company}}</div>
                        <div class="timeline-content">{{occupationDetails}}</div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-title">Annual Income</div>
                        <div class="timeline-content">{{annualIncome}}</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- About Me & Expectations -->
        <div class="about-section">
            <!-- About Me -->
            <div class="about-card about-me">
                <h3 class="about-title">About Me</h3>
                <p>{{aboutMe}}</p>
                
                <div style="margin-top: 15px;">
                    <h4 style="font-size: 16px; margin-bottom: 5px; color: var(--primary-color);">Hobbies & Interests</h4>
                    <p>{{hobbies}}</p>
                </div>
            </div>
            
            <!-- Partner Expectations -->
            <div class="about-card expectations">
                <h3 class="about-title">Partner Expectations</h3>
                <p>{{partnerPreferences}}</p>
            </div>
        </div>
        
        <!-- Contact Information -->
        <div class="section">
            <h2 class="section-title">Contact Information</h2>
            <div class="section-content">
                <div class="details-card">
                    <ul class="details-list">
                        <li>
                            <div class="details-label">Current Location</div>
                            <div class="details-value">{{city}}, {{state}}, {{country}}</div>
                        </li>
                        <li>
                            <div class="details-label">Email</div>
                            <div class="details-value">{{email}}</div>
                        </li>
                        <li>
                            <div class="details-label">Phone</div>
                            <div class="details-value">{{phone}}</div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Footer with Branding -->
        <div class="footer">
            <div class="branding">
                <img src="{{brandLogo}}" alt="Brand Logo" class="brand-logo">
                <span class="brand-name">{{brandName}}</span>
            </div>
            <p>{{brandTagline}}</p>
            <p>Created on {{createdAt}}</p>
        </div>
    </div>
</body>
</html>
