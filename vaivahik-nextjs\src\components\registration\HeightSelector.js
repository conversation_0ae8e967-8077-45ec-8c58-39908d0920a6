import React, { useState, useEffect } from 'react';
import {
  FormControl,
  FormLabel,
  FormHelperText,
  Grid,
  Select,
  MenuItem,
  Box,
  Typography,
  Slider,
  Paper,
  Tooltip,
  IconButton,
  Popover,
  Tabs,
  Tab,
  InputAdornment,
  TextField
} from '@mui/material';
import HeightIcon from '@mui/icons-material/Height';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import SwapVertIcon from '@mui/icons-material/SwapVert';
import { formatHeightToCm } from '@/utils/heightUtils';

/**
 * Advanced Height Selector Component
 *
 * A highly user-friendly component for selecting height in feet and inches
 * with visual representation, proper validation, and multiple input methods
 */
const HeightSelector = ({
  feet,
  inches,
  onChange,
  error,
  touched,
  gender,
  showVisualization = true
}) => {
  // State for height visualization
  const [anchorEl, setAnchorEl] = useState(null);
  const [activeTab, setActiveTab] = useState(0);
  const [cmHeight, setCmHeight] = useState('');

  // Calculate total height in inches and cm
  const totalHeightInInches = feet && inches ? (parseInt(feet) * 12) + parseInt(inches) : 0;
  const heightInCm = totalHeightInInches ? formatHeightToCm(totalHeightInInches) : 0;

  // Update cm height when feet/inches change
  useEffect(() => {
    if (totalHeightInInches) {
      setCmHeight(heightInCm.toString());
    }
  }, [feet, inches, totalHeightInInches, heightInCm]);

  // Generate options for feet (4' to 6')
  const feetOptions = [];
  for (let i = 4; i <= 6; i++) {
    feetOptions.push(i);
  }

  // Generate options for inches (0 to 11)
  const inchesOptions = [];
  for (let i = 0; i <= 11; i++) {
    inchesOptions.push(i);
  }

  // Handle change in feet
  const handleFeetChange = (e) => {
    onChange('feet', parseInt(e.target.value));
  };

  // Handle change in inches
  const handleInchesChange = (e) => {
    onChange('inches', parseInt(e.target.value));
  };

  // Handle slider change
  const handleSliderChange = (_, newValue) => {
    // Convert slider value (total inches) to feet and inches
    const newFeet = Math.floor(newValue / 12);
    const newInches = newValue % 12;

    onChange('feet', newFeet);
    onChange('inches', newInches);
  };

  // Handle cm input change
  const handleCmChange = (e) => {
    const value = e.target.value;
    setCmHeight(value);

    // Only update feet/inches if we have a valid number
    if (value && !isNaN(value) && value > 0) {
      const totalInches = Math.round(parseFloat(value) / 2.54);
      const newFeet = Math.floor(totalInches / 12);
      const newInches = totalInches % 12;

      if (newFeet >= 4 && newFeet <= 6) {
        onChange('feet', newFeet);
        onChange('inches', newInches);
      }
    }
  };

  // Open height visualization
  const handleVisualizationClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  // Close height visualization
  const handleVisualizationClose = () => {
    setAnchorEl(null);
  };

  // Handle tab change
  const handleTabChange = (_, newValue) => {
    setActiveTab(newValue);
  };

  // Determine if height is within recommended range based on gender
  const getHeightRecommendation = () => {
    if (!feet || !inches) {
      return { message: '', color: 'text.secondary' };
    }

    // Universal height range: 4'5" (53") to 6'5" (77")
    if (totalHeightInInches < 53) {
      return {
        message: `Height must be at least 4'5" (${formatHeightToCm(53)} cm)`,
        color: 'error.main'
      };
    }

    if (totalHeightInInches > 77) {
      return {
        message: `Height must be at most 6'5" (${formatHeightToCm(77)} cm)`,
        color: 'error.main'
      };
    }

    // Gender-specific recommendations
    if (gender === 'FEMALE') {
      if (totalHeightInInches < 58) { // Less than 4'10"
        return { message: 'Below average height for females', color: 'warning.main' };
      } else if (totalHeightInInches > 67) { // More than 5'7"
        return { message: 'Above average height for females', color: 'info.main' };
      } else {
        return { message: 'Average height for females', color: 'success.main' };
      }
    } else { // MALE
      if (totalHeightInInches < 65) { // Less than 5'5"
        return { message: 'Below average height for males', color: 'warning.main' };
      } else if (totalHeightInInches > 73) { // More than 6'1"
        return { message: 'Above average height for males', color: 'info.main' };
      } else {
        return { message: 'Average height for males', color: 'success.main' };
      }
    }
  };

  const heightRecommendation = getHeightRecommendation();
  const visualizationOpen = Boolean(anchorEl);

  // Calculate slider marks and values
  const sliderMarks = [
    { value: 53, label: "4'5\"" },
    { value: 60, label: "5'0\"" },
    { value: 72, label: "6'0\"" },
    { value: 77, label: "6'5\"" }
  ];

  // Calculate slider color based on height recommendation
  const getSliderColor = () => {
    if (!feet || !inches) return 'primary';
    if (totalHeightInInches < 53 || totalHeightInInches > 77) return 'error';
    return 'primary';
  };

  return (
    <FormControl fullWidth error={!!error && touched}>
      <FormLabel
        htmlFor="height"
        sx={{ mb: 1, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <HeightIcon fontSize="small" sx={{ mr: 1 }} />
          Height*
        </Box>

        {showVisualization && (
          <Tooltip title="Height visualization and conversion">
            <IconButton size="small" onClick={handleVisualizationClick}>
              <SwapVertIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}
      </FormLabel>

      <Grid container spacing={2} alignItems="center">
        <Grid item xs={5}>
          <Select
            id="height-feet"
            value={feet}
            onChange={handleFeetChange}
            displayEmpty
            fullWidth
            error={!!error && touched}
          >
            <MenuItem value="" disabled>Feet</MenuItem>
            {feetOptions.map(option => (
              <MenuItem key={`feet-${option}`} value={option}>
                {option} ft
              </MenuItem>
            ))}
          </Select>
        </Grid>

        <Grid item xs={5}>
          <Select
            id="height-inches"
            value={inches}
            onChange={handleInchesChange}
            displayEmpty
            fullWidth
            error={!!error && touched}
          >
            <MenuItem value="" disabled>Inches</MenuItem>
            {inchesOptions.map(option => (
              <MenuItem key={`inches-${option}`} value={option}>
                {option} in
              </MenuItem>
            ))}
          </Select>
        </Grid>

        <Grid item xs={2}>
          <Box
            sx={{
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: '1px solid',
              borderColor: error && touched ? 'error.main' : 'divider',
              borderRadius: 1,
              py: 1
            }}
          >
            <Typography
              variant="body2"
              sx={{
                fontWeight: 'bold',
                color: error && touched ? 'error.main' : 'text.primary'
              }}
            >
              {feet && inches ? `${feet}'${inches}"` : '--'}
            </Typography>
          </Box>
        </Grid>
      </Grid>

      {/* Height visualization popover */}
      <Popover
        open={visualizationOpen}
        anchorEl={anchorEl}
        onClose={handleVisualizationClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
      >
        <Paper sx={{ p: 3, width: 300, maxWidth: '100%' }}>
          <Typography variant="subtitle1" gutterBottom>
            Height Details
          </Typography>

          <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
            <Tab label="Slider" />
            <Tab label="Metric" />
          </Tabs>

          {activeTab === 0 && (
            <Box sx={{ px: 1 }}>
              <Slider
                value={totalHeightInInches || 53}
                onChange={handleSliderChange}
                min={53}
                max={77}
                step={1}
                marks={sliderMarks}
                color={getSliderColor()}
                sx={{ mt: 4 }}
              />

              <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2" color="text.secondary">
                  Min: 4'5"
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Max: 6'5"
                </Typography>
              </Box>

              <Box sx={{ mt: 3, textAlign: 'center' }}>
                <Typography variant="h6">
                  {feet && inches ? `${feet}'${inches}"` : '--'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {heightInCm ? `${heightInCm} cm` : ''}
                </Typography>
              </Box>
            </Box>
          )}

          {activeTab === 1 && (
            <Box sx={{ px: 1 }}>
              <TextField
                fullWidth
                label="Height in centimeters"
                value={cmHeight}
                onChange={handleCmChange}
                type="number"
                InputProps={{
                  endAdornment: <InputAdornment position="end">cm</InputAdornment>,
                }}
                sx={{ mb: 2 }}
              />

              <Typography variant="body2" color="text.secondary" gutterBottom>
                Valid range: 135 cm - 196 cm
              </Typography>

              <Box sx={{ mt: 3, textAlign: 'center' }}>
                <Typography variant="h6">
                  {feet && inches ? `${feet}'${inches}"` : '--'}
                </Typography>
              </Box>
            </Box>
          )}
        </Paper>
      </Popover>

      {error && touched ? (
        <FormHelperText error>{error}</FormHelperText>
      ) : (
        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
          <FormHelperText sx={{ color: heightRecommendation.color, m: 0 }}>
            {heightRecommendation.message}
          </FormHelperText>
          {heightInCm > 0 && (
            <Typography variant="caption" color="text.secondary" sx={{ ml: 'auto' }}>
              {heightInCm} cm
            </Typography>
          )}
        </Box>
      )}
    </FormControl>
  );
};

// Default props
HeightSelector.defaultProps = {
  feet: '',
  inches: '',
  error: '',
  touched: false,
  gender: 'MALE',
  showVisualization: true
};

export default HeightSelector;
