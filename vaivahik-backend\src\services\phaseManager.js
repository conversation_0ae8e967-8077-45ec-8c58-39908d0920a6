/**
 * Phase Manager Service
 * Handles switching between different matching algorithm phases
 */

const { PrismaClient } = require('@prisma/client');
const FlexibilityService = require('./flexibilityService');
const logger = require('../utils/logger');

class PhaseManager {
  constructor() {
    this.prisma = new PrismaClient();
    this.flexibilityService = new FlexibilityService();
    
    this.phases = {
      'v1.0': {
        name: 'Current Rule-Based',
        description: 'Traditional rule-based matching with fixed preferences',
        features: ['Basic compatibility scoring', 'Fixed preference weights', 'Simple filtering'],
        status: 'ACTIVE',
        implementation: 'COMPLETE'
      },
      'v1.5': {
        name: 'Flexible Matching',
        description: 'Enhanced matching with flexible preferences and compatibility groups',
        features: ['Flexible age ranges', 'Religion compatibility groups', 'Caste flexibility', 'Gradual scoring'],
        status: 'READY',
        implementation: 'COMPLETE'
      },
      'v2.0': {
        name: 'Personalized AI',
        description: 'AI-powered personalization based on user behavior',
        features: ['Behavioral learning', 'Dynamic preferences', 'User adaptation', 'Interaction analysis'],
        status: 'PLANNED',
        implementation: 'PENDING'
      },
      'v2.5': {
        name: 'Intelligent Features',
        description: 'Advanced AI features for enhanced user experience',
        features: ['Smart explanations', 'Predictive scoring', 'AI recommendations', 'Conversation analysis'],
        status: 'PLANNED',
        implementation: 'PENDING'
      },
      'v3.0': {
        name: 'Advanced AI',
        description: 'Cutting-edge AI algorithms for superior matching',
        features: ['Multi-modal learning', 'Graph networks', 'Reinforcement learning', 'Real-time adaptation'],
        status: 'PLANNED',
        implementation: 'PENDING'
      }
    };
  }

  /**
   * Get all available phases
   */
  async getAvailablePhases() {
    return Object.entries(this.phases).map(([version, phase]) => ({
      version,
      ...phase,
      isImplemented: phase.implementation === 'COMPLETE',
      canActivate: phase.implementation === 'COMPLETE'
    }));
  }

  /**
   * Get current active phase
   */
  async getCurrentPhase() {
    try {
      const settings = await this.prisma.systemSettings.findFirst({
        where: { key: 'matching_algorithm_version' }
      });
      
      const currentVersion = settings?.value || 'v1.0';
      return {
        version: currentVersion,
        ...this.phases[currentVersion]
      };
    } catch (error) {
      console.error('Error getting current phase:', error);
      return {
        version: 'v1.0',
        ...this.phases['v1.0']
      };
    }
  }

  /**
   * Switch to a different phase
   */
  async switchPhase(targetVersion, adminUserId) {
    try {
      // Validate target version
      if (!this.phases[targetVersion]) {
        throw new Error(`Invalid phase version: ${targetVersion}`);
      }

      // Check if phase is implemented
      if (this.phases[targetVersion].implementation !== 'COMPLETE') {
        throw new Error(`Phase ${targetVersion} is not yet implemented`);
      }

      // Get current phase
      const currentPhase = await this.getCurrentPhase();
      
      if (currentPhase.version === targetVersion) {
        return {
          success: true,
          message: `Already using phase ${targetVersion}`,
          currentPhase: currentPhase
        };
      }

      // Perform phase switch
      await this.performPhaseSwitch(currentPhase.version, targetVersion, adminUserId);

      // Update system settings
      await this.prisma.systemSettings.upsert({
        where: { key: 'matching_algorithm_version' },
        update: { 
          value: targetVersion,
          updatedAt: new Date(),
          updatedBy: adminUserId
        },
        create: {
          key: 'matching_algorithm_version',
          value: targetVersion,
          createdBy: adminUserId
        }
      });

      // Log phase change
      await this.logPhaseChange(currentPhase.version, targetVersion, adminUserId);

      return {
        success: true,
        message: `Successfully switched from ${currentPhase.version} to ${targetVersion}`,
        previousPhase: currentPhase,
        newPhase: {
          version: targetVersion,
          ...this.phases[targetVersion]
        }
      };

    } catch (error) {
      console.error('Error switching phase:', error);
      throw error;
    }
  }

  /**
   * Perform the actual phase switch operations
   */
  async performPhaseSwitch(fromVersion, toVersion, adminUserId) {
    console.log(`Switching from ${fromVersion} to ${toVersion}`);

    // Phase-specific switch logic
    switch (toVersion) {
      case 'v1.5':
        await this.enableFlexibilityFeatures();
        break;
      case 'v2.0':
        await this.enablePersonalizationFeatures();
        break;
      case 'v2.5':
        await this.enableIntelligentFeatures();
        break;
      case 'v3.0':
        await this.enableAdvancedAIFeatures();
        break;
      default:
        // v1.0 or unknown - disable all advanced features
        await this.disableAdvancedFeatures();
    }

    // Clear caches that might be affected by the phase change
    await this.clearRelevantCaches(toVersion);
  }

  /**
   * Enable flexibility features for Phase 1
   */
  async enableFlexibilityFeatures() {
    console.log('Enabling flexibility features...');
    
    // Update system configuration
    await this.prisma.systemSettings.upsert({
      where: { key: 'flexibility_enabled' },
      update: { value: 'true' },
      create: { key: 'flexibility_enabled', value: 'true' }
    });

    // Initialize default flexibility settings for existing users
    const usersWithoutFlexibility = await this.prisma.user.findMany({
      where: {
        flexibilitySettings: null
      },
      select: { id: true }
    });

    for (const user of usersWithoutFlexibility) {
      await this.flexibilityService.createDefaultSettings(user.id);
    }

    console.log(`Initialized flexibility settings for ${usersWithoutFlexibility.length} users`);
  }

  /**
   * Enable personalization features for Phase 2
   */
  async enablePersonalizationFeatures() {
    console.log('Enabling personalization features...');
    
    await this.prisma.systemSettings.upsert({
      where: { key: 'personalization_enabled' },
      update: { value: 'true' },
      create: { key: 'personalization_enabled', value: 'true' }
    });

    // Initialize behavioral tracking
    await this.initializeBehavioralTracking();
  }

  /**
   * Enable intelligent features for Phase 3
   */
  async enableIntelligentFeatures() {
    console.log('Enabling intelligent features...');
    
    await this.prisma.systemSettings.upsert({
      where: { key: 'intelligent_features_enabled' },
      update: { value: 'true' },
      create: { key: 'intelligent_features_enabled', value: 'true' }
    });
  }

  /**
   * Enable advanced AI features for Phase 4
   */
  async enableAdvancedAIFeatures() {
    console.log('Enabling advanced AI features...');
    
    await this.prisma.systemSettings.upsert({
      where: { key: 'advanced_ai_enabled' },
      update: { value: 'true' },
      create: { key: 'advanced_ai_enabled', value: 'true' }
    });
  }

  /**
   * Disable all advanced features (revert to v1.0)
   */
  async disableAdvancedFeatures() {
    console.log('Disabling advanced features...');
    
    const featureKeys = [
      'flexibility_enabled',
      'personalization_enabled', 
      'intelligent_features_enabled',
      'advanced_ai_enabled'
    ];

    for (const key of featureKeys) {
      await this.prisma.systemSettings.upsert({
        where: { key },
        update: { value: 'false' },
        create: { key, value: 'false' }
      });
    }
  }

  /**
   * Clear caches affected by phase change
   */
  async clearRelevantCaches(newVersion) {
    console.log(`Clearing caches for phase ${newVersion}...`);
    
    // Clear compatibility scores cache
    await this.prisma.compatibilityScores.deleteMany({});
    
    // Clear user embeddings cache if switching to/from AI phases
    if (['v2.0', 'v2.5', 'v3.0'].includes(newVersion)) {
      // Clear ML-related caches
      console.log('Clearing ML caches...');
    }
  }

  /**
   * Log phase change for audit trail
   */
  async logPhaseChange(fromVersion, toVersion, adminUserId) {
    await this.prisma.systemAuditLog.create({
      data: {
        action: 'PHASE_CHANGE',
        details: {
          fromVersion,
          toVersion,
          timestamp: new Date(),
          adminUserId
        },
        performedBy: adminUserId,
        timestamp: new Date()
      }
    });
  }

  /**
   * Initialize behavioral tracking for personalization
   */
  async initializeBehavioralTracking() {
    // Create behavioral tracking tables if they don't exist
    // This would typically be done through migrations
    console.log('Initializing behavioral tracking...');
  }

  /**
   * Get phase-specific configuration
   */
  async getPhaseConfiguration(version) {
    const phase = this.phases[version];
    if (!phase) {
      throw new Error(`Unknown phase version: ${version}`);
    }

    // Get phase-specific settings from database
    const settings = await this.prisma.systemSettings.findMany({
      where: {
        key: {
          startsWith: version.replace('.', '_') + '_'
        }
      }
    });

    const config = {};
    settings.forEach(setting => {
      config[setting.key] = setting.value;
    });

    return {
      phase,
      configuration: config
    };
  }

  /**
   * Validate phase readiness
   */
  async validatePhaseReadiness(version) {
    const phase = this.phases[version];
    
    const readiness = {
      isImplemented: phase.implementation === 'COMPLETE',
      hasRequiredData: true,
      hasRequiredInfrastructure: true,
      estimatedUsers: 0,
      warnings: [],
      errors: []
    };

    // Check user count for personalization phases
    if (['v2.0', 'v2.5', 'v3.0'].includes(version)) {
      const userCount = await this.prisma.user.count();
      readiness.estimatedUsers = userCount;
      
      if (userCount < 1000) {
        readiness.warnings.push('Personalization works better with more users (recommended: 1000+)');
      }
    }

    // Check for required infrastructure
    if (version === 'v3.0') {
      readiness.warnings.push('Advanced AI features require additional GPU resources');
    }

    return readiness;
  }

  /**
   * Get MCP server integration status
   */
  async getMCPIntegrationStatus() {
    try {
      // Dynamically import to avoid circular dependency
      const mcpServerManager = require('./mcp/mcpServerManager');
      const status = mcpServerManager.getStatus();

      return {
        available: true,
        running: status.isRunning,
        connectedClients: status.serverStatus?.connectedClients || 0,
        registeredTools: status.serverStatus?.registeredTools || 0,
        uptime: status.serverStatus?.uptime || 0
      };
    } catch (error) {
      logger.warn('MCP server not available:', error.message);
      return {
        available: false,
        running: false,
        error: error.message
      };
    }
  }

  /**
   * Test AI algorithm through MCP server
   */
  async testAIAlgorithm(algorithmName, testData = {}) {
    try {
      const mcpStatus = await this.getMCPIntegrationStatus();

      if (!mcpStatus.available || !mcpStatus.running) {
        throw new Error('MCP server is not available or running');
      }

      // Dynamically import to avoid circular dependency
      const mcpServerManager = require('./mcp/mcpServerManager');
      const server = mcpServerManager.server;

      if (!server) {
        throw new Error('MCP server instance not found');
      }

      // Execute the AI algorithm
      const result = await server.handleToolCall({
        name: algorithmName,
        arguments: testData
      });

      logger.info(`AI algorithm ${algorithmName} tested successfully`);

      return {
        success: true,
        algorithm: algorithmName,
        result: result,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logger.error(`Error testing AI algorithm ${algorithmName}:`, error);
      throw error;
    }
  }

  /**
   * Get AI capabilities based on current phase
   */
  getAICapabilities(version) {
    const phase = this.phases[version];
    if (!phase) {
      return { available: false, features: [] };
    }

    const aiFeatures = {
      'v1.0': ['basic_matching', 'profile_scoring'],
      'v1.5': ['basic_matching', 'profile_scoring', 'preference_learning'],
      'v2.0': ['advanced_matching', 'compatibility_prediction', 'smart_recommendations'],
      'v2.5': ['advanced_matching', 'compatibility_prediction', 'smart_recommendations', 'fraud_detection'],
      'v3.0': ['neural_matching', 'behavioral_analysis', 'success_prediction', 'real_time_optimization']
    };

    return {
      available: true,
      phase: version,
      phaseName: phase.name,
      features: aiFeatures[version] || [],
      mcpIntegration: version >= 'v2.0' // MCP integration available from v2.0+
    };
  }

  /**
   * Enhanced phase transition with MCP integration
   */
  async transitionPhaseWithAI(fromVersion, toVersion, options = {}) {
    try {
      logger.info(`Starting AI-enhanced phase transition from ${fromVersion} to ${toVersion}`);

      // Perform standard phase transition
      const transitionResult = await this.transitionPhase(fromVersion, toVersion, options);

      // If MCP is available and target version supports it, initialize AI features
      const aiCapabilities = this.getAICapabilities(toVersion);

      if (aiCapabilities.mcpIntegration) {
        const mcpStatus = await this.getMCPIntegrationStatus();

        if (mcpStatus.available && mcpStatus.running) {
          logger.info(`Initializing AI features for phase ${toVersion}`);

          // Test each AI feature to ensure it's working
          for (const feature of aiCapabilities.features) {
            try {
              await this.testAIAlgorithm(feature, { test: true });
              logger.info(`AI feature ${feature} initialized successfully`);
            } catch (error) {
              logger.warn(`AI feature ${feature} initialization failed:`, error.message);
            }
          }
        } else {
          logger.warn('MCP server not available for AI feature initialization');
        }
      }

      return {
        ...transitionResult,
        aiIntegration: {
          enabled: aiCapabilities.mcpIntegration,
          features: aiCapabilities.features,
          mcpStatus: await this.getMCPIntegrationStatus()
        }
      };

    } catch (error) {
      logger.error('Error in AI-enhanced phase transition:', error);
      throw error;
    }
  }

  /**
   * Intelligent phase selection based on data availability and system readiness
   */
  async selectOptimalPhase(userCount, dataQuality, systemLoad = 'normal') {
    try {
      // Get current phase
      const currentPhase = await this.getCurrentPhase();

      // Check MCP server availability for advanced phases
      const mcpStatus = await this.getMCPIntegrationStatus();

      // Phase selection logic
      let recommendedPhase = 'v1.0'; // Default fallback

      if (userCount >= 100000 && dataQuality.score >= 90 && mcpStatus.available) {
        recommendedPhase = 'v3.0'; // Advanced AI
      } else if (userCount >= 10000 && dataQuality.score >= 75 && mcpStatus.available) {
        recommendedPhase = 'v2.5'; // Intelligent features
      } else if (userCount >= 1000 && dataQuality.score >= 60 && mcpStatus.available) {
        recommendedPhase = 'v2.0'; // Personalized AI
      } else if (userCount >= 100 && dataQuality.score >= 40) {
        recommendedPhase = 'v1.5'; // Flexible matching
      }

      // Consider system load
      if (systemLoad === 'high' && recommendedPhase !== currentPhase.version) {
        logger.warn(`High system load detected, staying with current phase ${currentPhase.version}`);
        return currentPhase.version;
      }

      logger.info(`Optimal phase selected: ${recommendedPhase} (users: ${userCount}, data quality: ${dataQuality.score}%, MCP: ${mcpStatus.available})`);

      return recommendedPhase;
    } catch (error) {
      logger.error('Error selecting optimal phase:', error);
      return 'v1.0'; // Safe fallback
    }
  }

  /**
   * Seamless algorithm coordination between traditional and MCP
   */
  async executeMatchingWithCoordination(userId, options = {}) {
    try {
      const currentPhase = await this.getCurrentPhase();
      const mcpStatus = await this.getMCPIntegrationStatus();

      // Determine execution strategy
      const shouldUseMCP = this.shouldUseMCPForPhase(currentPhase.version, mcpStatus);

      if (shouldUseMCP) {
        logger.info(`Using MCP server for matching (phase: ${currentPhase.version})`);
        return await this.executeMCPMatching(userId, options, currentPhase.version);
      } else {
        logger.info(`Using traditional algorithms for matching (phase: ${currentPhase.version})`);
        return await this.executeTraditionalMatching(userId, options, currentPhase.version);
      }

    } catch (error) {
      logger.error('Error in coordinated matching execution:', error);

      // Fallback to traditional algorithms
      logger.info('Falling back to traditional algorithms');
      return await this.executeTraditionalMatching(userId, options, 'v1.5');
    }
  }

  /**
   * Determine if MCP should be used for a given phase
   */
  shouldUseMCPForPhase(phaseVersion, mcpStatus) {
    // MCP is used for v2.0+ phases when available
    const mcpPhases = ['v2.0', 'v2.5', 'v3.0'];
    return mcpPhases.includes(phaseVersion) && mcpStatus.available && mcpStatus.running;
  }

  /**
   * Execute matching via MCP server
   */
  async executeMCPMatching(userId, options, phaseVersion) {
    try {
      // Dynamically import to avoid circular dependency
      const mcpServerManager = require('./mcp/mcpServerManager');
      const server = mcpServerManager.server;

      if (!server) {
        throw new Error('MCP server not available');
      }

      // Execute matching via MCP
      const result = await server.handleToolCall({
        name: 'user_matching',
        arguments: {
          userId: userId,
          limit: options.limit || 10,
          filters: options.filters || {},
          algorithm: phaseVersion
        }
      });

      return {
        success: true,
        matches: result.content?.[0]?.text ? JSON.parse(result.content[0].text) : result,
        source: 'mcp_server',
        phase: phaseVersion
      };

    } catch (error) {
      logger.error('Error executing MCP matching:', error);
      throw error;
    }
  }

  /**
   * Execute matching via traditional algorithms
   */
  async executeTraditionalMatching(userId, options, phaseVersion) {
    try {
      // Use ML matching service directly
      const mlMatchingService = require('./mlMatchingService');

      const result = await mlMatchingService.getMatches(
        userId,
        options.limit || 10,
        options.offset || 0,
        options.minScore || 60
      );

      return {
        success: true,
        matches: result,
        source: 'traditional_algorithms',
        phase: phaseVersion
      };

    } catch (error) {
      logger.error('Error executing traditional matching:', error);
      throw error;
    }
  }

  /**
   * Monitor and auto-adjust phase based on performance
   */
  async monitorAndAutoAdjust() {
    try {
      const currentPhase = await this.getCurrentPhase();
      const userCount = await this.getUserCount();
      const dataQuality = await this.assessDataQuality();
      const systemMetrics = await this.getSystemMetrics();

      // Check if phase adjustment is needed
      const optimalPhase = await this.selectOptimalPhase(userCount, dataQuality, systemMetrics.load);

      if (optimalPhase !== currentPhase.version) {
        logger.info(`Auto-adjusting phase from ${currentPhase.version} to ${optimalPhase}`);

        // Perform gradual transition
        await this.transitionPhaseWithAI(currentPhase.version, optimalPhase, {
          gradual: true,
          rollbackOnError: true
        });

        return {
          adjusted: true,
          fromPhase: currentPhase.version,
          toPhase: optimalPhase,
          reason: 'automatic_optimization'
        };
      }

      return {
        adjusted: false,
        currentPhase: currentPhase.version,
        reason: 'no_adjustment_needed'
      };

    } catch (error) {
      logger.error('Error in auto-adjustment monitoring:', error);
      return {
        adjusted: false,
        error: error.message
      };
    }
  }

  /**
   * Get system performance metrics
   */
  async getSystemMetrics() {
    try {
      // This would integrate with actual system monitoring
      // For now, return simulated metrics
      return {
        load: 'normal', // normal, high, critical
        responseTime: 250, // milliseconds
        errorRate: 0.01, // 1%
        throughput: 100 // requests per minute
      };
    } catch (error) {
      logger.error('Error getting system metrics:', error);
      return {
        load: 'unknown',
        responseTime: 0,
        errorRate: 0,
        throughput: 0
      };
    }
  }

  /**
   * Get current user count
   */
  async getUserCount() {
    try {
      const count = await this.prisma.user.count({
        where: {
          profileStatus: 'ACTIVE'
        }
      });
      return count;
    } catch (error) {
      logger.error('Error getting user count:', error);
      return 0;
    }
  }
}

module.exports = PhaseManager;
