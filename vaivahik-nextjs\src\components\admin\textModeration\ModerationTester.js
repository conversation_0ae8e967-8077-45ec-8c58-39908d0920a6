import { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  TextField,
  Button,
  Typography,
  CircularProgress,
  Alert,
  Paper,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';
import FlagIcon from '@mui/icons-material/Flag';
import WarningIcon from '@mui/icons-material/Warning';
import InfoIcon from '@mui/icons-material/Info';
import { toast } from 'react-toastify';
import axiosInstance from '@/utils/axiosConfig';

export default function ModerationTester() {
  const [text, setText] = useState('');
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Test text moderation
  const testModeration = async () => {
    if (!text.trim()) {
      toast.error('Please enter some text to test');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setResult(null);

      const response = await axiosInstance.post('/api/admin/text-moderation/test', { text });

      if (response.data.success) {
        setResult(response.data.result);
      } else {
        throw new Error(response.data.message || 'Failed to test moderation');
      }
    } catch (error) {
      console.error('Error testing moderation:', error);
      setError(error.message || 'Failed to test moderation');
      toast.error(error.message || 'Failed to test moderation');

      // For development, use mock data if API fails
      if (process.env.NODE_ENV === 'development') {
        // Create mock result based on the input text
        const mockResult = {
          isApproved: !text.includes('bad') && !text.includes('phone') && !text.includes('email'),
          flags: [],
          moderatedText: text,
          details: {}
        };

        // Check for profanity
        if (text.toLowerCase().includes('bad')) {
          mockResult.flags.push('profanity');
          mockResult.details.profanity = {
            words: ['bad']
          };
          mockResult.moderatedText = text.replace(/bad/gi, '***');
        }

        // Check for contact info
        if (text.includes('phone') || text.includes('email')) {
          mockResult.flags.push('contact_info');
          mockResult.details.contactInfo = {
            hasContactInfo: true,
            items: []
          };

          if (text.includes('phone')) {
            mockResult.details.contactInfo.items.push({
              type: 'phone',
              value: '1234567890'
            });
          }

          if (text.includes('email')) {
            mockResult.details.contactInfo.items.push({
              type: 'email',
              value: '<EMAIL>'
            });
          }
        }

        // Check for spam
        if (text.toUpperCase() === text && text.length > 10) {
          mockResult.flags.push('spam');
          mockResult.details.spam = {
            isSpam: true,
            score: 0.85,
            reasons: ['ALL_CAPS', 'EXCESSIVE_PUNCTUATION']
          };
        }

        setResult(mockResult);
      }
    } finally {
      setLoading(false);
    }
  };

  // Get flag chip color
  const getFlagColor = (flag) => {
    switch (flag) {
      case 'profanity':
        return 'error';
      case 'contact_info':
        return 'warning';
      case 'spam':
        return 'info';
      default:
        return 'default';
    }
  };

  // Get flag icon
  const getFlagIcon = (flag) => {
    switch (flag) {
      case 'profanity':
        return <WarningIcon color="error" />;
      case 'contact_info':
        return <InfoIcon color="warning" />;
      case 'spam':
        return <FlagIcon color="info" />;
      default:
        return <FlagIcon />;
    }
  };

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Test Text Moderation" />
            <Divider />
            <CardContent>
              <TextField
                label="Enter text to test"
                multiline
                rows={6}
                value={text}
                onChange={(e) => setText(e.target.value)}
                fullWidth
                placeholder="Type or paste text here to test the moderation system"
                sx={{ mb: 2 }}
              />

              <Button
                variant="contained"
                color="primary"
                startIcon={<SendIcon />}
                onClick={testModeration}
                disabled={loading || !text.trim()}
              >
                {loading ? 'Testing...' : 'Test Moderation'}
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Moderation Results" />
            <Divider />
            <CardContent>
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : result ? (
                <Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6" sx={{ mr: 1 }}>
                      Decision:
                    </Typography>
                    <Chip
                      icon={result.isApproved ? <CheckIcon /> : <CloseIcon />}
                      label={result.isApproved ? 'APPROVED' : 'REJECTED'}
                      color={result.isApproved ? 'success' : 'error'}
                    />
                  </Box>

                  {result.flags.length > 0 && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="h6" gutterBottom>
                        Flags:
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {result.flags.map((flag) => (
                          <Chip
                            key={flag}
                            label={flag}
                            color={getFlagColor(flag)}
                            icon={getFlagIcon(flag)}
                          />
                        ))}
                      </Box>
                    </Box>
                  )}

                  {result.moderatedText && result.moderatedText !== text && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="h6" gutterBottom>
                        Moderated Text:
                      </Typography>
                      <Paper variant="outlined" sx={{ p: 2, bgcolor: 'background.default' }}>
                        <Typography variant="body1">
                          {result.moderatedText}
                        </Typography>
                      </Paper>
                    </Box>
                  )}

                  {result.details && (
                    <Box>
                      <Typography variant="h6" gutterBottom>
                        Details:
                      </Typography>

                      {result.details.profanity && (
                        <Paper variant="outlined" sx={{ p: 2, mb: 2, bgcolor: 'background.default' }}>
                          <Typography variant="subtitle1" color="error" gutterBottom>
                            Profanity Detected
                          </Typography>
                          <List dense>
                            {result.details.profanity.words.map((word) => (
                              <ListItem key={word}>
                                <ListItemIcon>
                                  <WarningIcon color="error" />
                                </ListItemIcon>
                                <ListItemText primary={word} />
                              </ListItem>
                            ))}
                          </List>
                        </Paper>
                      )}

                      {result.details.contactInfo && result.details.contactInfo.hasContactInfo && (
                        <Paper variant="outlined" sx={{ p: 2, mb: 2, bgcolor: 'background.default' }}>
                          <Typography variant="subtitle1" color="warning.main" gutterBottom>
                            Contact Information Detected
                          </Typography>
                          <List dense>
                            {result.details.contactInfo.items.map((item, index) => (
                              <ListItem key={index}>
                                <ListItemIcon>
                                  <InfoIcon color="warning" />
                                </ListItemIcon>
                                <ListItemText
                                  primary={item.value}
                                  secondary={`Type: ${item.type}`}
                                />
                              </ListItem>
                            ))}
                          </List>
                        </Paper>
                      )}

                      {result.details.spam && result.details.spam.isSpam && (
                        <Paper variant="outlined" sx={{ p: 2, mb: 2, bgcolor: 'background.default' }}>
                          <Typography variant="subtitle1" color="info.main" gutterBottom>
                            Spam Detected (Score: {result.details.spam.score.toFixed(2)})
                          </Typography>
                          <List dense>
                            {result.details.spam.reasons.map((reason, index) => (
                              <ListItem key={index}>
                                <ListItemIcon>
                                  <FlagIcon color="info" />
                                </ListItemIcon>
                                <ListItemText
                                  primary={reason.replace(/_/g, ' ')}
                                />
                              </ListItem>
                            ))}
                          </List>
                        </Paper>
                      )}
                    </Box>
                  )}
                </Box>
              ) : (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
                  <Typography variant="body1" color="textSecondary">
                    Enter text and click "Test Moderation" to see results
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
