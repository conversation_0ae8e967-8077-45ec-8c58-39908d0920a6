import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Slider,
  FormControl,
  FormLabel,
  Paper,
  Grid,
  Chip,
  Tooltip,
  IconButton,
  Popover,
  Tabs,
  Tab,
  TextField,
  InputAdornment
} from '@mui/material';
import HeightIcon from '@mui/icons-material/Height';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { formatHeightToCm } from '@/utils/heightUtils';

/**
 * Height Range Selector Component for Search
 * 
 * A user-friendly component for selecting height range in search forms
 * Features:
 * - Slider for visual range selection
 * - Feet/inches and cm display
 * - Preset common ranges
 * - Detailed visualization
 */
const HeightRangeSelector = ({
  minHeight,
  maxHeight,
  onChange,
  gender
}) => {
  // State for height visualization
  const [anchorEl, setAnchorEl] = useState(null);
  const [activeTab, setActiveTab] = useState(0);
  const [minHeightCm, setMinHeightCm] = useState('');
  const [maxHeightCm, setMaxHeightCm] = useState('');
  
  // Convert height values to cm
  useEffect(() => {
    if (minHeight) {
      setMinHeightCm(formatHeightToCm(minHeight).toString());
    }
    if (maxHeight) {
      setMaxHeightCm(formatHeightToCm(maxHeight).toString());
    }
  }, [minHeight, maxHeight]);
  
  // Handle slider change
  const handleSliderChange = (_, newValue) => {
    onChange(newValue[0], newValue[1]);
  };
  
  // Handle min cm input change
  const handleMinCmChange = (e) => {
    const value = e.target.value;
    setMinHeightCm(value);
    
    if (value && !isNaN(value) && value > 0) {
      const totalInches = Math.round(parseFloat(value) / 2.54);
      if (totalInches >= 53 && totalInches <= maxHeight) {
        onChange(totalInches, maxHeight);
      }
    }
  };
  
  // Handle max cm input change
  const handleMaxCmChange = (e) => {
    const value = e.target.value;
    setMaxHeightCm(value);
    
    if (value && !isNaN(value) && value > 0) {
      const totalInches = Math.round(parseFloat(value) / 2.54);
      if (totalInches <= 77 && totalInches >= minHeight) {
        onChange(minHeight, totalInches);
      }
    }
  };
  
  // Open height visualization
  const handleVisualizationClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  
  // Close height visualization
  const handleVisualizationClose = () => {
    setAnchorEl(null);
  };
  
  // Handle tab change
  const handleTabChange = (_, newValue) => {
    setActiveTab(newValue);
  };
  
  // Handle preset selection
  const handlePresetClick = (min, max) => {
    onChange(min, max);
  };
  
  // Format height in inches to feet and inches string
  const formatHeight = (inches) => {
    if (!inches) return '--';
    const feet = Math.floor(inches / 12);
    const remainingInches = inches % 12;
    return `${feet}'${remainingInches}"`;
  };
  
  // Generate preset options based on gender
  const getPresets = () => {
    if (gender === 'FEMALE') {
      return [
        { label: 'Short (4\'5" - 5\'0")', min: 53, max: 60 },
        { label: 'Average (5\'0" - 5\'6")', min: 60, max: 66 },
        { label: 'Tall (5\'6" - 6\'0")', min: 66, max: 72 },
        { label: 'Very Tall (6\'0" - 6\'5")', min: 72, max: 77 }
      ];
    } else {
      return [
        { label: 'Short (5\'0" - 5\'6")', min: 60, max: 66 },
        { label: 'Average (5\'6" - 5\'10")', min: 66, max: 70 },
        { label: 'Tall (5\'10" - 6\'2")', min: 70, max: 74 },
        { label: 'Very Tall (6\'2" - 6\'5")', min: 74, max: 77 }
      ];
    }
  };
  
  const presets = getPresets();
  const visualizationOpen = Boolean(anchorEl);
  
  // Calculate slider marks
  const sliderMarks = [
    { value: 53, label: "4'5\"" },
    { value: 60, label: "5'0\"" },
    { value: 66, label: "5'6\"" },
    { value: 72, label: "6'0\"" },
    { value: 77, label: "6'5\"" }
  ];
  
  return (
    <FormControl fullWidth>
      <FormLabel 
        sx={{ 
          mb: 1, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between' 
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <HeightIcon fontSize="small" sx={{ mr: 1 }} />
          Height Range
        </Box>
        
        <Tooltip title="Height range details">
          <IconButton size="small" onClick={handleVisualizationClick}>
            <InfoOutlinedIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </FormLabel>
      
      <Box sx={{ px: 2, mt: 3, mb: 1 }}>
        <Slider
          value={[minHeight, maxHeight]}
          onChange={handleSliderChange}
          min={53}
          max={77}
          step={1}
          marks={sliderMarks}
          valueLabelDisplay="off"
        />
      </Box>
      
      <Box sx={{ display: 'flex', justifyContent: 'space-between', px: 2, mb: 2 }}>
        <Typography variant="body2">
          {formatHeight(minHeight)}
        </Typography>
        <Typography variant="body2">
          to
        </Typography>
        <Typography variant="body2">
          {formatHeight(maxHeight)}
        </Typography>
      </Box>
      
      <Box sx={{ mt: 2 }}>
        <Typography variant="caption" color="text.secondary" gutterBottom>
          Quick Select:
        </Typography>
        <Grid container spacing={1} sx={{ mt: 0.5 }}>
          {presets.map((preset, index) => (
            <Grid item key={index}>
              <Chip
                label={preset.label}
                onClick={() => handlePresetClick(preset.min, preset.max)}
                variant={
                  minHeight === preset.min && maxHeight === preset.max 
                    ? 'filled' 
                    : 'outlined'
                }
                size="small"
              />
            </Grid>
          ))}
        </Grid>
      </Box>
      
      {/* Height visualization popover */}
      <Popover
        open={visualizationOpen}
        anchorEl={anchorEl}
        onClose={handleVisualizationClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
      >
        <Paper sx={{ p: 3, width: 300, maxWidth: '100%' }}>
          <Typography variant="subtitle1" gutterBottom>
            Height Range Details
          </Typography>
          
          <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
            <Tab label="Imperial" />
            <Tab label="Metric" />
          </Tabs>
          
          {activeTab === 0 && (
            <Box>
              <Typography variant="body2" gutterBottom>
                Selected Range:
              </Typography>
              <Typography variant="h6" align="center" gutterBottom>
                {formatHeight(minHeight)} - {formatHeight(maxHeight)}
              </Typography>
              
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                This range is commonly used for {gender === 'FEMALE' ? 'female' : 'male'} height preferences.
              </Typography>
            </Box>
          )}
          
          {activeTab === 1 && (
            <Box>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Min (cm)"
                    value={minHeightCm}
                    onChange={handleMinCmChange}
                    type="number"
                    size="small"
                    InputProps={{
                      endAdornment: <InputAdornment position="end">cm</InputAdornment>,
                    }}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Max (cm)"
                    value={maxHeightCm}
                    onChange={handleMaxCmChange}
                    type="number"
                    size="small"
                    InputProps={{
                      endAdornment: <InputAdornment position="end">cm</InputAdornment>,
                    }}
                  />
                </Grid>
              </Grid>
              
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                Valid range: 135 cm - 196 cm
              </Typography>
            </Box>
          )}
        </Paper>
      </Popover>
    </FormControl>
  );
};

// Default props
HeightRangeSelector.defaultProps = {
  minHeight: 53, // 4'5"
  maxHeight: 77, // 6'5"
  gender: 'MALE'
};

export default HeightRangeSelector;
