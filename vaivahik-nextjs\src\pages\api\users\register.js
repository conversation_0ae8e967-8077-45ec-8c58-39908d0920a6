import { validateBirthDetails } from '@/utils/birthDetailsValidation';
import { isUsingRealBackend } from '@/utils/featureFlags';
import { BACKEND_API_URL } from '@/config';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const {
      // Phone Verification (Step 1)
      phone,
      otp,

      // Personal Details (Step 2)
      fullName,
      email,
      gender,
      profileFor,
      dateOfBirth,
      maritalStatus,
      heightFeet,
      heightInches,
      height,
      bloodGroup,
      religion,
      caste,
      subCaste,

      // Education & Career (Step 3)
      education,
      educationField,
      occupation,
      workingWith,
      incomeRange,

      // Location & Birth Details (Step 4)
      city,
      state,
      pincode,
      birthTime,
      birthPlace,
      birthPlaceCoordinates,
      gotra,

      // Profile Photo & About Me (Step 5)
      profilePhoto,
      photoPrivacy,
      aboutMe
    } = req.body;

    // Validate required fields
    const requiredFields = {
      'Phone Verification': { phone, otp },
      'Personal Details': { fullName, email, gender, dateOfBirth, maritalStatus, height, bloodGroup, religion, caste },
      'Education & Career': { education, occupation, incomeRange },
      'Location & Birth Details': { city, state, pincode, birthPlace }
    };

    // Check for missing required fields
    const missingFields = [];

    Object.entries(requiredFields).forEach(([section, fields]) => {
      Object.entries(fields).forEach(([field, value]) => {
        if (!value) {
          missingFields.push({ section, field });
        }
      });
    });

    if (missingFields.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        missingFields
      });
    }

    // Validate phone number
    if (!/^[0-9]{10}$/.test(phone)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid phone number format'
      });
    }

    // Validate email
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email format'
      });
    }

    // Validate height
    const heightValue = parseFloat(height);
    if (isNaN(heightValue) || heightValue < 4.5 || heightValue > 6.5) {
      return res.status(400).json({
        success: false,
        message: 'Height must be between 4.5 and 6.5 feet'
      });
    }

    // Validate birth details
    const birthValidation = validateBirthDetails({
      dateOfBirth,
      birthTime,
      birthPlace,
      gender
    });

    if (!birthValidation.isValid) {
      return res.status(400).json({
        success: false,
        message: 'Invalid birth details',
        errors: birthValidation.errors
      });
    }

    // Check if we should use real backend or mock data
    if (isUsingRealBackend()) {
      try {
        // Prepare data for backend API
        const userData = {
          phone,
          otp,
          fullName,
          email,
          gender,
          profileFor,
          dateOfBirth,
          maritalStatus,
          height,
          bloodGroup,
          religion,
          caste,
          subCaste,
          education,
          educationField,
          occupation,
          workingWith,
          incomeRange,
          city,
          state,
          pincode,
          birthTime,
          birthPlace,
          gotra,
          aboutMe
        };

        // Handle profile photo separately if needed
        if (profilePhoto) {
          // In a real implementation, you would upload the photo to cloud storage
          // and include the URL in userData
          userData.profilePhotoUrl = 'https://example.com/placeholder.jpg';
        }

        // Call the real backend API
        const apiUrl = `${BACKEND_API_URL}/users/register`;
        console.log(`[API] Registering user with real backend: ${apiUrl}`);

        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(userData)
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `Backend API error: ${response.status}`);
        }

        const data = await response.json();
        return res.status(201).json({
          success: true,
          message: 'Registration successful',
          user: data.user,
          source: 'real'
        });
      } catch (error) {
        console.error(`[API] Real backend error, falling back to mock data:`, error);
        // Fall back to mock implementation if real backend fails
        return res.status(201).json({
          success: true,
          message: 'Registration successful (mock)',
          user: {
            id: 'user_' + Math.random().toString(36).substr(2, 9),
            phone,
            email,
            fullName,
            gender,
            profileStatus: 'PENDING_APPROVAL'
          },
          source: 'mock',
          fallbackReason: error.message
        });
      }
    } else {
      // Use mock implementation
      console.log(`[API] Using mock implementation (feature flag set to mock)`);
      return res.status(201).json({
        success: true,
        message: 'Registration successful (mock)',
        user: {
          id: 'user_' + Math.random().toString(36).substr(2, 9),
          phone,
          email,
          fullName,
          gender,
          profileStatus: 'PENDING_APPROVAL'
        },
        source: 'mock'
      });
    }
  } catch (error) {
    console.error('Registration error:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred during registration',
      error: error.message || 'Unknown error'
    });
  }
}
