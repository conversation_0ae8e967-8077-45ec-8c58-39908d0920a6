// src/controllers/admin/promotions.controller.js

const PromotionSettings = require('../../models/PromotionSettings');
const ChatSettings = require('../../models/ChatSettings');

/**
 * @description Get all promotions
 * @route GET /api/admin/promotions
 */
exports.getPromotions = async (req, res, next) => {
    try {
        // Get all promotions from the settings
        const promotions = PromotionSettings.activePromotions;
        
        // Format promotions for response
        const formattedPromotions = Object.entries(promotions).map(([name, promotion]) => ({
            name,
            isActive: promotion.isActive,
            startDate: promotion.startDate,
            endDate: promotion.endDate,
            durationDays: promotion.durationDays,
            featureOverrides: promotion.featureOverrides,
            display: promotion.display,
            isCurrentlyActive: PromotionSettings.isPromotionActive(name)
        }));
        
        res.status(200).json({
            success: true,
            promotions: formattedPromotions
        });
    } catch (error) {
        console.error('Error fetching promotions:', error);
        next(error);
    }
};

/**
 * @description Get a specific promotion
 * @route GET /api/admin/promotions/:name
 */
exports.getPromotion = async (req, res, next) => {
    try {
        const { name } = req.params;
        
        // Get the promotion from settings
        const promotion = PromotionSettings.activePromotions[name];
        
        if (!promotion) {
            return res.status(404).json({
                success: false,
                message: `Promotion "${name}" not found.`
            });
        }
        
        // Format promotion for response
        const formattedPromotion = {
            name,
            isActive: promotion.isActive,
            startDate: promotion.startDate,
            endDate: promotion.endDate,
            durationDays: promotion.durationDays,
            featureOverrides: promotion.featureOverrides,
            display: promotion.display,
            isCurrentlyActive: PromotionSettings.isPromotionActive(name)
        };
        
        res.status(200).json({
            success: true,
            promotion: formattedPromotion
        });
    } catch (error) {
        console.error('Error fetching promotion:', error);
        next(error);
    }
};

/**
 * @description Activate a promotion
 * @route POST /api/admin/promotions/:name/activate
 */
exports.activatePromotion = async (req, res, next) => {
    try {
        const { name } = req.params;
        const { durationDays } = req.body;
        
        // Check if promotion exists
        if (!PromotionSettings.activePromotions[name]) {
            return res.status(404).json({
                success: false,
                message: `Promotion "${name}" not found.`
            });
        }
        
        // Activate the promotion
        const success = PromotionSettings.activatePromotion(name, durationDays);
        
        if (!success) {
            return res.status(400).json({
                success: false,
                message: `Failed to activate promotion "${name}".`
            });
        }
        
        // Get the updated promotion
        const promotion = PromotionSettings.activePromotions[name];
        
        res.status(200).json({
            success: true,
            message: `Promotion "${name}" activated successfully.`,
            promotion: {
                name,
                isActive: promotion.isActive,
                startDate: promotion.startDate,
                endDate: promotion.endDate,
                durationDays: promotion.durationDays || durationDays
            }
        });
    } catch (error) {
        console.error('Error activating promotion:', error);
        next(error);
    }
};

/**
 * @description Deactivate a promotion
 * @route POST /api/admin/promotions/:name/deactivate
 */
exports.deactivatePromotion = async (req, res, next) => {
    try {
        const { name } = req.params;
        
        // Check if promotion exists
        if (!PromotionSettings.activePromotions[name]) {
            return res.status(404).json({
                success: false,
                message: `Promotion "${name}" not found.`
            });
        }
        
        // Deactivate the promotion
        const success = PromotionSettings.deactivatePromotion(name);
        
        if (!success) {
            return res.status(400).json({
                success: false,
                message: `Failed to deactivate promotion "${name}".`
            });
        }
        
        res.status(200).json({
            success: true,
            message: `Promotion "${name}" deactivated successfully.`
        });
    } catch (error) {
        console.error('Error deactivating promotion:', error);
        next(error);
    }
};

/**
 * @description Update a promotion
 * @route PUT /api/admin/promotions/:name
 */
exports.updatePromotion = async (req, res, next) => {
    try {
        const { name } = req.params;
        const { durationDays, featureOverrides, display } = req.body;
        
        // Check if promotion exists
        if (!PromotionSettings.activePromotions[name]) {
            return res.status(404).json({
                success: false,
                message: `Promotion "${name}" not found.`
            });
        }
        
        // Update the promotion
        const promotion = PromotionSettings.activePromotions[name];
        
        if (durationDays !== undefined) {
            promotion.durationDays = durationDays;
        }
        
        if (featureOverrides) {
            // Update feature overrides for each tier
            for (const [tier, overrides] of Object.entries(featureOverrides)) {
                if (promotion.featureOverrides[tier]) {
                    promotion.featureOverrides[tier] = {
                        ...promotion.featureOverrides[tier],
                        ...overrides
                    };
                }
            }
        }
        
        if (display) {
            promotion.display = {
                ...promotion.display,
                ...display
            };
        }
        
        res.status(200).json({
            success: true,
            message: `Promotion "${name}" updated successfully.`,
            promotion: {
                name,
                isActive: promotion.isActive,
                startDate: promotion.startDate,
                endDate: promotion.endDate,
                durationDays: promotion.durationDays,
                featureOverrides: promotion.featureOverrides,
                display: promotion.display
            }
        });
    } catch (error) {
        console.error('Error updating promotion:', error);
        next(error);
    }
};

/**
 * @description Create a custom promotion
 * @route POST /api/admin/promotions
 */
exports.createPromotion = async (req, res, next) => {
    try {
        const { name, durationDays, featureOverrides, display } = req.body;
        
        // Validate required fields
        if (!name || !durationDays || !featureOverrides || !display) {
            return res.status(400).json({
                success: false,
                message: 'Missing required fields: name, durationDays, featureOverrides, display'
            });
        }
        
        // Check if promotion already exists
        if (PromotionSettings.activePromotions[name]) {
            return res.status(400).json({
                success: false,
                message: `Promotion "${name}" already exists.`
            });
        }
        
        // Create the new promotion
        PromotionSettings.activePromotions[name] = {
            isActive: false,
            startDate: null,
            endDate: null,
            durationDays,
            featureOverrides,
            display
        };
        
        res.status(201).json({
            success: true,
            message: `Promotion "${name}" created successfully.`,
            promotion: {
                name,
                isActive: false,
                durationDays,
                featureOverrides,
                display
            }
        });
    } catch (error) {
        console.error('Error creating promotion:', error);
        next(error);
    }
};
