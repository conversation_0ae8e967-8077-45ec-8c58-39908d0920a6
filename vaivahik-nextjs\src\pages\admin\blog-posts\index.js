import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);

// Dynamic import for rich text editor
const ReactQuill = dynamic(() => import('react-quill'), {
  ssr: false,
  loading: () => <p>Loading editor...</p>
});

import { adminGet, adminPost, adminPut, adminDelete } from '@/services/adminApiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';
import { toast } from 'react-toastify';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Pagination,
  Select,
  TextField,
  Typography,
  Alert,
  CircularProgress,
  Switch,
  FormControlLabel,
  Tooltip,
  Avatar,
  Stack,
  Divider,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  CardMedia
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Article as ArticleIcon,
  TrendingUp as TrendingUpIcon,
  Comment as CommentIcon,
  Schedule as ScheduleIcon,
  Publish as PublishIcon,
  Draft as DraftIcon,
  FilterList as FilterListIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

// Import CSS for ReactQuill
import 'react-quill/dist/quill.snow.css';

export default function BlogPostsManagement() {
  const router = useRouter();
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [currentPost, setCurrentPost] = useState(null);
  const [postToDelete, setPostToDelete] = useState(null);
  const [stats, setStats] = useState({});
  const [filters, setFilters] = useState({
    status: '',
    category: '',
    search: ''
  });
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    featuredImage: '',
    category: 'UNCATEGORIZED',
    tags: [],
    status: 'DRAFT',
    publishedAt: null,
    metaTitle: '',
    metaDescription: '',
    metaKeywords: ''
  });

  // Category options
  const categoryOptions = [
    { value: 'UNCATEGORIZED', label: 'Uncategorized' },
    { value: 'PROFILE_TIPS', label: 'Profile Tips' },
    { value: 'RELATIONSHIP_ADVICE', label: 'Relationship Advice' },
    { value: 'SUCCESS_STORIES', label: 'Success Stories' },
    { value: 'MATRIMONY_TIPS', label: 'Matrimony Tips' },
    { value: 'CULTURAL_INSIGHTS', label: 'Cultural Insights' },
    { value: 'WEDDING_PLANNING', label: 'Wedding Planning' },
    { value: 'LIFESTYLE', label: 'Lifestyle' },
    { value: 'NEWS_UPDATES', label: 'News & Updates' }
  ];

  // Status options
  const statusOptions = [
    { value: 'DRAFT', label: 'Draft', color: 'default' },
    { value: 'PUBLISHED', label: 'Published', color: 'success' },
    { value: 'SCHEDULED', label: 'Scheduled', color: 'warning' },
    { value: 'ARCHIVED', label: 'Archived', color: 'secondary' }
  ];

  useEffect(() => {
    fetchPosts();
    fetchStats();
  }, [page, rowsPerPage, filters]);

  const fetchPosts = async () => {
    setLoading(true);
    try {
      const response = await adminGet(ADMIN_ENDPOINTS.BLOG_POSTS, {
        page,
        limit,
        search,
        category,
        status
      });

      if (response.success) {
        setPosts(response.posts);
        setTotal(response.pagination.total);
        setTotalPages(response.pagination.totalPages);
        setCategories(response.categories);
      } else {
        toast.error(response.message || 'Failed to fetch blog posts');
      }
    } catch (error) {
      console.error('Error fetching blog posts:', error);
      toast.error('Error fetching blog posts');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await adminGet(`${ADMIN_ENDPOINTS.BLOG_POSTS}/stats/overview`);
      if (response.success) {
        setStats(response.stats || {});
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleCreatePost = () => {
    setCurrentPost(null);
    setFormData({
      title: '',
      slug: '',
      excerpt: '',
      content: '',
      featuredImage: '',
      category: 'UNCATEGORIZED',
      tags: [],
      status: 'DRAFT',
      publishedAt: null,
      metaTitle: '',
      metaDescription: '',
      metaKeywords: ''
    });
    setDialogOpen(true);
  };

  const handleEditPost = (post) => {
    setCurrentPost(post);
    setFormData({
      title: post.title || '',
      slug: post.slug || '',
      excerpt: post.excerpt || '',
      content: post.content || '',
      featuredImage: post.featuredImage || '',
      category: post.category || 'UNCATEGORIZED',
      tags: post.tags || [],
      status: post.status || 'DRAFT',
      publishedAt: post.publishedAt ? new Date(post.publishedAt) : null,
      metaTitle: post.metaTitle || '',
      metaDescription: post.metaDescription || '',
      metaKeywords: post.metaKeywords || ''
    });
    setDialogOpen(true);
  };

  const generateSlug = (title) => {
    return title
      .toLowerCase()
      .replace(/[^\w\s]/gi, '')
      .replace(/\s+/g, '-')
      .substring(0, 50);
  };

  const handleTitleChange = (title) => {
    setFormData({
      ...formData,
      title,
      slug: generateSlug(title),
      metaTitle: title.substring(0, 60) // SEO best practice: 60 chars max
    });
  };

  const handleSavePost = async () => {
    try {
      // Validate required fields
      if (!formData.title || !formData.content) {
        toast.error('Title and content are required');
        return;
      }

      const postData = {
        ...formData,
        publishedAt: formData.publishedAt ? formData.publishedAt.toISOString() : null,
        tags: Array.isArray(formData.tags) ? formData.tags : formData.tags.split(',').map(tag => tag.trim())
      };

      let response;
      if (currentPost) {
        response = await adminPut(`${ADMIN_ENDPOINTS.BLOG_POSTS}/${currentPost.id}`, postData);
      } else {
        response = await adminPost(ADMIN_ENDPOINTS.BLOG_POSTS, postData);
      }

      if (response.success) {
        toast.success(`Blog post ${currentPost ? 'updated' : 'created'} successfully`);
        setDialogOpen(false);
        fetchPosts();
        fetchStats();
      } else {
        toast.error(response.message || 'Failed to save blog post');
      }
    } catch (error) {
      console.error('Error saving blog post:', error);
      toast.error('Error saving blog post');
    }
  };

  const handleDeleteClick = (post) => {
    setPostToDelete(post);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      const response = await adminDelete(`${ADMIN_ENDPOINTS.BLOG_POSTS}/${postToDelete.id}`);

      if (response.success) {
        toast.success('Blog post deleted successfully');
        setDeleteDialogOpen(false);
        setPostToDelete(null);
        fetchPosts();
        fetchStats();
      } else {
        toast.error(response.message || 'Failed to delete blog post');
      }
    } catch (error) {
      console.error('Error deleting blog post:', error);
      toast.error('Error deleting blog post');
    }
  };

  const getStatusChip = (status) => {
    const statusConfig = statusOptions.find(opt => opt.value === status) || statusOptions[0];
    return (
      <Chip
        label={statusConfig.label}
        color={statusConfig.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Rich text editor modules
  const quillModules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      ['blockquote', 'code-block'],
      ['link', 'image'],
      ['clean']
    ],
  };

  const quillFormats = [
    'header', 'bold', 'italic', 'underline', 'strike',
    'list', 'bullet', 'blockquote', 'code-block',
    'link', 'image'
  ];

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setPostToDelete(null);
  };

  return (
    <EnhancedAdminLayout title="Blog Posts">
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Blog Posts Management
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleCreatePost}
          >
            Create New Post
          </Button>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Total Posts
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.totalPosts || 0}
                    </Typography>
                  </Box>
                  <ArticleIcon color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Published
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.publishedPosts || 0}
                    </Typography>
                  </Box>
                  <PublishIcon color="success" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Total Views
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.totalViews || 0}
                    </Typography>
                  </Box>
                  <TrendingUpIcon color="info" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Comments
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.totalComments || 0}
                    </Typography>
                  </Box>
                  <CommentIcon color="warning" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Filters */}
        <Box sx={{ mb: 3, p: { xs: 1.5, sm: 2 }, bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>
          <Grid container spacing={{ xs: 1, sm: 2 }} alignItems="center">
            <Grid item xs={12} sm={12} md={4}>
              <form onSubmit={handleSearchSubmit}>
                <TextField
                  fullWidth
                  label="Search Posts"
                  value={search}
                  onChange={handleSearchChange}
                  size="small"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton type="submit" edge="end" size="small">
                          <SearchIcon />
                        </IconButton>
                      </InputAdornment>
                    )
                  }}
                />
              </form>
            </Grid>
            <Grid item xs={6} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Category</InputLabel>
                <Select
                  value={category}
                  onChange={handleCategoryChange}
                  label="Category"
                >
                  <MenuItem value="">All Categories</MenuItem>
                  {categories.map((cat) => (
                    <MenuItem key={cat} value={cat}>{cat}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={status}
                  onChange={handleStatusChange}
                  label="Status"
                >
                  <MenuItem value="">All Status</MenuItem>
                  <MenuItem value="published">Published</MenuItem>
                  <MenuItem value="draft">Draft</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Show</InputLabel>
                <Select
                  value={limit}
                  onChange={handleLimitChange}
                  label="Show"
                >
                  <MenuItem value={5}>5</MenuItem>
                  <MenuItem value={10}>10</MenuItem>
                  <MenuItem value={25}>25</MenuItem>
                  <MenuItem value={50}>50</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Box>

        {/* Posts List */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 5 }}>
            <div className="loading-spinner"></div>
          </Box>
        ) : posts.length === 0 ? (
          <Box sx={{ textAlign: 'center', my: 5, p: 3, bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>
            <Typography variant="h6" gutterBottom>
              No blog posts found
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Try changing your search criteria or create a new post.
            </Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddPost}
              sx={{ mt: 2 }}
            >
              Add New Post
            </Button>
          </Box>
        ) : (
          <Grid container spacing={2}>
            {posts.map((post) => (
              <Grid item xs={12} key={post.id}>
                <Card sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, height: '100%' }}>
                  <Box
                    sx={{
                      width: { xs: '100%', sm: 200 },
                      height: { xs: 160, sm: 'auto' },
                      display: 'block'
                    }}
                  >
                    <Box
                      component="img"
                      sx={{
                        height: '100%',
                        width: '100%',
                        objectFit: 'cover'
                      }}
                      src={post.featuredImage || '/images/blog/default.jpg'}
                      alt={post.title}
                    />
                  </Box>
                  <Box sx={{ display: 'flex', flexDirection: 'column', flexGrow: 1 }}>
                    <CardContent sx={{ flex: '1 0 auto', p: { xs: 2, sm: 3 } }}>
                      <Box sx={{
                        display: 'flex',
                        flexDirection: { xs: 'column', sm: 'row' },
                        justifyContent: 'space-between',
                        alignItems: { xs: 'flex-start', sm: 'center' },
                        gap: { xs: 1, sm: 0 }
                      }}>
                        <Typography
                          component="div"
                          variant="h5"
                          sx={{
                            fontSize: { xs: '1.1rem', sm: '1.25rem' },
                            wordBreak: 'break-word'
                          }}
                        >
                          {post.title}
                        </Typography>
                        <Chip
                          label={post.status}
                          color={post.status === 'published' ? 'success' : 'default'}
                          size="small"
                          sx={{ alignSelf: { xs: 'flex-start', sm: 'center' } }}
                        />
                      </Box>
                      <Typography variant="subtitle1" color="text.secondary" component="div">
                        {post.category}
                      </Typography>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          mt: 1,
                          display: '-webkit-box',
                          overflow: 'hidden',
                          WebkitBoxOrient: 'vertical',
                          WebkitLineClamp: { xs: 2, sm: 3 },
                        }}
                      >
                        {post.excerpt}
                      </Typography>
                      <Box sx={{ display: 'flex', mt: 2, flexWrap: 'wrap', gap: 0.5 }}>
                        {post.tags.slice(0, 3).map((tag) => (
                          <Chip key={tag} label={tag} size="small" variant="outlined" />
                        ))}
                        {post.tags.length > 3 && (
                          <Chip label={`+${post.tags.length - 3} more`} size="small" variant="outlined" />
                        )}
                      </Box>
                    </CardContent>
                    <Box sx={{
                      display: 'flex',
                      flexDirection: { xs: 'column', sm: 'row' },
                      alignItems: { xs: 'flex-start', sm: 'center' },
                      justifyContent: 'space-between',
                      p: 2,
                      pt: 0,
                      gap: { xs: 1, sm: 0 }
                    }}>
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          By {post.author} • {formatDate(post.publishedAt || post.createdAt)}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                          <Typography variant="caption" color="text.secondary" sx={{ mr: 2 }}>
                            👁️ {post.viewCount} views
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            💬 {post.commentCount} comments
                          </Typography>
                        </Box>
                      </Box>
                      <Box sx={{
                        display: 'flex',
                        width: { xs: '100%', sm: 'auto' },
                        justifyContent: { xs: 'space-between', sm: 'flex-end' },
                        mt: { xs: 1, sm: 0 }
                      }}>
                        <IconButton onClick={() => handleViewPost(post.id)} color="primary" size="small">
                          <VisibilityIcon />
                        </IconButton>
                        <IconButton onClick={() => handleEditPost(post.id)} color="primary" size="small">
                          <EditIcon />
                        </IconButton>
                        <IconButton onClick={() => handleDeleteClick(post)} color="error" size="small">
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </Box>
                  </Box>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <Pagination
              count={totalPages}
              page={page}
              onChange={handlePageChange}
              color="primary"
              showFirstButton
              showLastButton
            />
          </Box>
        )}

        {/* Create/Edit Blog Post Dialog */}
        <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="lg" fullWidth>
          <DialogTitle>
            {currentPost ? 'Edit Blog Post' : 'Create New Blog Post'}
          </DialogTitle>
          <DialogContent>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={12} md={8}>
                  <TextField
                    fullWidth
                    label="Title"
                    value={formData.title}
                    onChange={(e) => handleTitleChange(e.target.value)}
                    required
                    sx={{ mb: 2 }}
                  />
                  <TextField
                    fullWidth
                    label="Slug"
                    value={formData.slug}
                    onChange={(e) => setFormData({ ...formData, slug: e.target.value })}
                    helperText="URL-friendly version of the title"
                    sx={{ mb: 2 }}
                  />
                  <TextField
                    fullWidth
                    multiline
                    rows={3}
                    label="Excerpt"
                    value={formData.excerpt}
                    onChange={(e) => setFormData({ ...formData, excerpt: e.target.value })}
                    helperText="Brief description for previews"
                    sx={{ mb: 2 }}
                  />
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    Content
                  </Typography>
                  <Box sx={{ mb: 2, '& .ql-editor': { minHeight: '200px' } }}>
                    <ReactQuill
                      theme="snow"
                      value={formData.content}
                      onChange={(content) => setFormData({ ...formData, content })}
                      modules={quillModules}
                      formats={quillFormats}
                    />
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Status</InputLabel>
                    <Select
                      value={formData.status}
                      label="Status"
                      onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                    >
                      {statusOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Category</InputLabel>
                    <Select
                      value={formData.category}
                      label="Category"
                      onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                    >
                      {categoryOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  <TextField
                    fullWidth
                    label="Featured Image URL"
                    value={formData.featuredImage}
                    onChange={(e) => setFormData({ ...formData, featuredImage: e.target.value })}
                    sx={{ mb: 2 }}
                  />
                  <TextField
                    fullWidth
                    label="Tags (comma-separated)"
                    value={Array.isArray(formData.tags) ? formData.tags.join(', ') : formData.tags}
                    onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
                    sx={{ mb: 2 }}
                  />
                  {formData.status === 'SCHEDULED' && (
                    <DateTimePicker
                      label="Publish Date"
                      value={formData.publishedAt}
                      onChange={(newValue) => setFormData({ ...formData, publishedAt: newValue })}
                      renderInput={(params) => <TextField {...params} fullWidth sx={{ mb: 2 }} />}
                    />
                  )}
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    SEO Settings
                  </Typography>
                  <TextField
                    fullWidth
                    label="Meta Title"
                    value={formData.metaTitle}
                    onChange={(e) => setFormData({ ...formData, metaTitle: e.target.value })}
                    helperText={`${formData.metaTitle.length}/60 characters`}
                    sx={{ mb: 2 }}
                  />
                  <TextField
                    fullWidth
                    multiline
                    rows={3}
                    label="Meta Description"
                    value={formData.metaDescription}
                    onChange={(e) => setFormData({ ...formData, metaDescription: e.target.value })}
                    helperText={`${formData.metaDescription.length}/160 characters`}
                    sx={{ mb: 2 }}
                  />
                  <TextField
                    fullWidth
                    label="Meta Keywords"
                    value={formData.metaKeywords}
                    onChange={(e) => setFormData({ ...formData, metaKeywords: e.target.value })}
                    helperText="Comma-separated keywords"
                  />
                </Grid>
              </Grid>
            </LocalizationProvider>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSavePost} variant="contained">
              {currentPost ? 'Update' : 'Create'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={deleteDialogOpen}
          onClose={handleDeleteCancel}
        >
          <DialogTitle>Delete Blog Post</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete the blog post "{postToDelete?.title}"? This action cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleDeleteCancel}>Cancel</Button>
            <Button onClick={handleDeleteConfirm} color="error" autoFocus>
              Delete
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </EnhancedAdminLayout>
  );
}
