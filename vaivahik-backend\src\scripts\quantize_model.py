"""
Model Quantization Script

This script quantizes and optimizes the matrimony matching model for production deployment.
"""

import os
import sys
import json
import argparse
import logging
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.services.model_quantizer import ModelQuantizer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Quantize and optimize the matrimony matching model')
    
    parser.add_argument(
        '--input',
        type=str,
        required=True,
        help='Path to the input model'
    )
    
    parser.add_argument(
        '--output',
        type=str,
        help='Path to save the quantized model'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        default='../../config/production_settings.json',
        help='Path to the configuration file'
    )
    
    parser.add_argument(
        '--method',
        type=str,
        choices=['dynamic', 'static'],
        default='dynamic',
        help='Quantization method'
    )
    
    parser.add_argument(
        '--dtype',
        type=str,
        choices=['qint8', 'float16'],
        default='qint8',
        help='Quantization data type'
    )
    
    parser.add_argument(
        '--mobile',
        action='store_true',
        help='Optimize for mobile deployment'
    )
    
    parser.add_argument(
        '--fusion',
        action='store_true',
        help='Enable operator fusion'
    )
    
    return parser.parse_args()

def load_config(config_path):
    """Load configuration from file"""
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Get quantization config
        quantization_config = config.get('quantization', {})
        return quantization_config
    except Exception as e:
        logger.error(f"Error loading configuration: {str(e)}")
        return {}

def main():
    """Main function"""
    # Parse arguments
    args = parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Update configuration with command line arguments
    if args.method:
        config['quantization_method'] = args.method
    
    if args.dtype:
        config['quantization_dtype'] = args.dtype
    
    if args.mobile:
        config['optimize_for_mobile'] = True
    
    if args.fusion:
        config['enable_fusion'] = True
    
    # Generate output path if not provided
    if not args.output:
        timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
        model_dir = config.get('model_dir', 'models')
        args.output = os.path.join(model_dir, f"quantized_model_{timestamp}.pt")
    
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(args.output), exist_ok=True)
    
    # Initialize quantizer
    logger.info(f"Initializing quantizer with configuration: {config}")
    quantizer = ModelQuantizer(args.input, config)
    
    # Quantize and optimize model
    logger.info("Quantizing and optimizing model...")
    output_path = quantizer.quantize_and_optimize(args.output)
    
    if output_path:
        logger.info(f"Model quantized and optimized successfully: {output_path}")
    else:
        logger.error("Failed to quantize and optimize model")
        sys.exit(1)
    
    # Print summary
    logger.info("Quantization summary:")
    logger.info(f"  Input model: {args.input}")
    logger.info(f"  Output model: {output_path}")
    logger.info(f"  Quantization method: {config.get('quantization_method', 'dynamic')}")
    logger.info(f"  Quantization dtype: {config.get('quantization_dtype', 'qint8')}")
    logger.info(f"  Optimized for mobile: {config.get('optimize_for_mobile', False)}")
    logger.info(f"  Operator fusion enabled: {config.get('enable_fusion', True)}")

if __name__ == '__main__':
    main()
