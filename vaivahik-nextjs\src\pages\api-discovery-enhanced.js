import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';

export default function ApiDiscoveryEnhanced() {
  const [loading, setLoading] = useState(true);
  const [discovering, setDiscovering] = useState(false);
  const [testing, setTesting] = useState(false);
  const [endpoints, setEndpoints] = useState([]);
  const [categorizedEndpoints, setCategorizedEndpoints] = useState({});
  const [baseUrl, setBaseUrl] = useState('');
  const [selectedEndpoint, setSelectedEndpoint] = useState(null);
  const [selectedMethod, setSelectedMethod] = useState('GET');
  const [testPayload, setTestPayload] = useState('');
  const [testResult, setTestResult] = useState(null);
  const [authToken, setAuthToken] = useState('');
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [activeCategory, setActiveCategory] = useState('All');
  const router = useRouter();

  // Available HTTP methods for testing
  const availableMethods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'];

  // Initialize component
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Set base URL
      const host = window.location.host;
      const protocol = window.location.protocol;
      setBaseUrl(`${protocol}//${host}`);
      
      // Load auth token from localStorage
      const savedToken = localStorage.getItem('api_auth_token');
      if (savedToken) {
        setAuthToken(savedToken);
      }
      
      setLoading(false);
    }
  }, []);

  // Function to discover API endpoints
  const discoverEndpoints = async () => {
    setDiscovering(true);
    try {
      const response = await fetch(`/api/api-discovery?baseUrl=${encodeURIComponent(baseUrl)}`);
      const data = await response.json();
      
      if (data.success) {
        setEndpoints(data.endpoints || []);
        setCategorizedEndpoints(data.categorizedEndpoints || {});
      } else {
        alert(`Error: ${data.message}`);
      }
    } catch (error) {
      console.error('Error discovering endpoints:', error);
      alert(`Error discovering endpoints: ${error.message}`);
    } finally {
      setDiscovering(false);
    }
  };

  // Function to test an endpoint
  const testEndpoint = async () => {
    if (!selectedEndpoint) return;
    
    setTesting(true);
    setTestResult(null);
    
    try {
      // Prepare headers
      const headers = {
        'Content-Type': 'application/json'
      };
      
      // Add auth token if available
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
      }
      
      // Parse payload if provided
      let parsedPayload = null;
      if (testPayload) {
        try {
          parsedPayload = JSON.parse(testPayload);
        } catch (e) {
          alert('Invalid JSON payload. Please check your syntax.');
          setTesting(false);
          return;
        }
      }
      
      // Make the request to the API discovery endpoint
      const response = await fetch('/api/api-discovery', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          baseUrl,
          path: selectedEndpoint.path,
          method: selectedMethod,
          payload: parsedPayload
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        setTestResult(data.result);
      } else {
        alert(`Error: ${data.message}`);
      }
    } catch (error) {
      console.error('Error testing endpoint:', error);
      alert(`Error testing endpoint: ${error.message}`);
    } finally {
      setTesting(false);
    }
  };

  // Function to handle authentication
  const handleAuth = (e) => {
    e.preventDefault();
    localStorage.setItem('api_auth_token', authToken);
    setShowAuthModal(false);
  };

  // Function to clear authentication
  const clearAuth = () => {
    localStorage.removeItem('api_auth_token');
    setAuthToken('');
    alert('Authentication token cleared');
  };

  // Function to generate sample payload based on the endpoint
  const generateSamplePayload = () => {
    if (!selectedEndpoint) return;
    
    // Simple logic to generate sample payloads based on the endpoint path
    let samplePayload = {};
    
    if (selectedEndpoint.path.includes('/auth/login')) {
      samplePayload = {
        email: '<EMAIL>',
        password: 'password123'
      };
    } else if (selectedEndpoint.path.includes('/auth/register')) {
      samplePayload = {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+91 9876543210',
        password: 'password123'
      };
    } else if (selectedEndpoint.path.includes('/users/profile')) {
      samplePayload = {
        name: 'John Doe',
        age: 28,
        gender: 'Male',
        height: '5\'10"',
        location: 'Mumbai',
        occupation: 'Software Engineer'
      };
    } else if (selectedEndpoint.path.includes('/admin/premium-plans')) {
      samplePayload = {
        name: 'Gold Plan',
        price: 2999,
        currency: 'INR',
        duration: 90,
        features: ['Feature 1', 'Feature 2', 'Feature 3']
      };
    } else {
      // Generic payload
      samplePayload = {
        id: 'sample_id',
        name: 'Sample Name',
        isActive: true
      };
    }
    
    setTestPayload(JSON.stringify(samplePayload, null, 2));
  };

  // Filter endpoints based on active category
  const filteredEndpoints = activeCategory === 'All' 
    ? endpoints 
    : (categorizedEndpoints[activeCategory] || []);

  // Get all categories
  const categories = ['All', ...Object.keys(categorizedEndpoints)];

  return (
    <div className="container">
      <Head>
        <title>Enhanced API Discovery - Vaivahik Admin</title>
        <meta name="description" content="Discover and test API endpoints" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="main">
        <div className="header">
          <h1 className="title">API Discovery Tool</h1>
          
          <div className="actions">
            <button 
              className="auth-button"
              onClick={() => setShowAuthModal(true)}
            >
              {authToken ? 'Update Auth Token' : 'Set Auth Token'}
            </button>
            
            {authToken && (
              <button 
                className="clear-auth-button"
                onClick={clearAuth}
              >
                Clear Auth Token
              </button>
            )}
            
            <Link href="/api-viewer-enhanced" className="tool-link">
              API Documentation
            </Link>
            
            <Link href="/admin/dashboard" className="tool-link">
              Back to Dashboard
            </Link>
          </div>
        </div>

        {loading ? (
          <div className="loading">Loading...</div>
        ) : (
          <div className="content">
            <div className="discovery-section">
              <div className="discovery-header">
                <h2>Discover API Endpoints</h2>
                <div className="base-url-container">
                  <input
                    type="text"
                    value={baseUrl}
                    onChange={(e) => setBaseUrl(e.target.value)}
                    placeholder="Base URL (e.g., http://localhost:3000)"
                    className="base-url-input"
                  />
                  <button 
                    onClick={discoverEndpoints} 
                    disabled={discovering}
                    className="discover-button"
                  >
                    {discovering ? 'Discovering...' : 'Discover Endpoints'}
                  </button>
                </div>
              </div>

              {endpoints.length > 0 && (
                <div className="endpoints-container">
                  <div className="categories-tabs">
                    {categories.map(category => (
                      <button
                        key={category}
                        className={`category-tab ${activeCategory === category ? 'active' : ''}`}
                        onClick={() => setActiveCategory(category)}
                      >
                        {category}
                      </button>
                    ))}
                  </div>
                  
                  <div className="endpoints-list">
                    <table>
                      <thead>
                        <tr>
                          <th>Path</th>
                          <th>Method</th>
                          <th>Status</th>
                          <th>Auth Required</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {filteredEndpoints.map((endpoint, index) => (
                          <tr 
                            key={`${endpoint.path}-${endpoint.method}-${index}`}
                            className={selectedEndpoint === endpoint ? 'selected' : ''}
                          >
                            <td>{endpoint.path}</td>
                            <td>{endpoint.method}</td>
                            <td>
                              <span className={`status-badge status-${endpoint.status < 300 ? 'success' : endpoint.status < 400 ? 'redirect' : endpoint.status < 500 ? 'client-error' : 'server-error'}`}>
                                {endpoint.status} {endpoint.statusText}
                              </span>
                            </td>
                            <td>{endpoint.requiresAuth ? 'Yes' : 'No'}</td>
                            <td>
                              <button 
                                onClick={() => {
                                  setSelectedEndpoint(endpoint);
                                  setSelectedMethod(endpoint.method);
                                  setTestResult(null);
                                  // Generate sample payload if needed
                                  if (['POST', 'PUT', 'PATCH'].includes(endpoint.method)) {
                                    generateSamplePayload();
                                  } else {
                                    setTestPayload('');
                                  }
                                }}
                                className="select-button"
                              >
                                Select
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>

            {selectedEndpoint && (
              <div className="testing-section">
                <h2>Test Endpoint</h2>
                
                <div className="endpoint-details">
                  <div className="endpoint-path">
                    <strong>Path:</strong> {selectedEndpoint.path}
                  </div>
                  
                  <div className="method-selector">
                    <label>
                      <strong>Method:</strong>
                      <select 
                        value={selectedMethod}
                        onChange={(e) => setSelectedMethod(e.target.value)}
                      >
                        {availableMethods.map(method => (
                          <option key={method} value={method}>{method}</option>
                        ))}
                      </select>
                    </label>
                  </div>
                </div>

                {['POST', 'PUT', 'PATCH'].includes(selectedMethod) && (
                  <div className="payload-container">
                    <div className="payload-header">
                      <label>
                        <strong>Request Payload (JSON):</strong>
                      </label>
                      <button 
                        onClick={generateSamplePayload}
                        className="generate-payload-button"
                      >
                        Generate Sample
                      </button>
                    </div>
                    <textarea
                      value={testPayload}
                      onChange={(e) => setTestPayload(e.target.value)}
                      placeholder="Enter JSON payload"
                      rows={10}
                      className="payload-textarea"
                    />
                  </div>
                )}

                <div className="test-actions">
                  <button 
                    onClick={testEndpoint}
                    disabled={testing}
                    className="test-button"
                  >
                    {testing ? 'Testing...' : 'Test Endpoint'}
                  </button>
                </div>

                {testResult && (
                  <div className="test-result">
                    <h3>Test Result</h3>
                    
                    <div className="result-details">
                      <div className="result-item">
                        <strong>Status:</strong> 
                        <span className={`status-badge status-${testResult.status < 300 ? 'success' : testResult.status < 400 ? 'redirect' : testResult.status < 500 ? 'client-error' : 'server-error'}`}>
                          {testResult.status} {testResult.statusText}
                        </span>
                      </div>
                      
                      <div className="result-item">
                        <strong>Method:</strong> {testResult.method}
                      </div>
                      
                      <div className="result-item">
                        <strong>Path:</strong> {testResult.path}
                      </div>
                      
                      <div className="result-item">
                        <strong>Requires Auth:</strong> {testResult.requiresAuth ? 'Yes' : 'No'}
                      </div>
                      
                      <div className="result-item">
                        <strong>Response Type:</strong> {testResult.responseType || 'N/A'}
                      </div>
                    </div>
                    
                    {testResult.responseData && (
                      <div className="response-data">
                        <h4>Response Data:</h4>
                        <pre>{JSON.stringify(testResult.responseData, null, 2)}</pre>
                      </div>
                    )}
                    
                    {testResult.error && (
                      <div className="response-error">
                        <h4>Error:</h4>
                        <pre>{testResult.error}</pre>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Authentication Modal */}
        {showAuthModal && (
          <div className="modal-overlay">
            <div className="auth-modal">
              <h2>Set Authentication Token</h2>
              <p>Enter your JWT token to authenticate API requests:</p>
              
              <form onSubmit={handleAuth}>
                <textarea
                  value={authToken}
                  onChange={(e) => setAuthToken(e.target.value)}
                  placeholder="Enter your JWT token here"
                  rows={4}
                />
                
                <div className="modal-actions">
                  <button type="button" onClick={() => setShowAuthModal(false)} className="cancel-button">
                    Cancel
                  </button>
                  <button type="submit" className="save-button">
                    Save Token
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </main>

      <style jsx>{`
        .container {
          min-height: 100vh;
          padding: 0;
          display: flex;
          flex-direction: column;
          background-color: #f5f5f5;
        }

        .main {
          flex: 1;
          display: flex;
          flex-direction: column;
          width: 100%;
        }

        .header {
          background-color: #5e35b1;
          color: white;
          padding: 1rem 2rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: wrap;
          gap: 1rem;
        }

        .title {
          margin: 0;
          font-size: 1.5rem;
          font-weight: 600;
        }

        .actions {
          display: flex;
          gap: 0.75rem;
          flex-wrap: wrap;
        }

        .auth-button, .clear-auth-button {
          padding: 0.5rem 1rem;
          border: none;
          border-radius: 4px;
          font-size: 0.9rem;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .auth-button {
          background-color: white;
          color: #5e35b1;
        }

        .auth-button:hover {
          background-color: #f0f0f0;
        }

        .clear-auth-button {
          background-color: rgba(255, 255, 255, 0.2);
          color: white;
        }

        .clear-auth-button:hover {
          background-color: rgba(255, 255, 255, 0.3);
        }

        .tool-link {
          padding: 0.5rem 1rem;
          background-color: rgba(255, 255, 255, 0.2);
          color: white;
          border-radius: 4px;
          text-decoration: none;
          font-size: 0.9rem;
          transition: background-color 0.2s;
        }

        .tool-link:hover {
          background-color: rgba(255, 255, 255, 0.3);
        }

        .loading {
          padding: 2rem;
          text-align: center;
          color: #666;
        }

        .content {
          flex: 1;
          padding: 1.5rem;
          display: flex;
          flex-direction: column;
          gap: 2rem;
        }

        .discovery-section, .testing-section {
          background-color: white;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          padding: 1.5rem;
        }

        .discovery-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1.5rem;
          flex-wrap: wrap;
          gap: 1rem;
        }

        .discovery-header h2 {
          margin: 0;
          color: #333;
        }

        .base-url-container {
          display: flex;
          gap: 0.5rem;
          flex-wrap: wrap;
        }

        .base-url-input {
          padding: 0.5rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          min-width: 300px;
        }

        .discover-button {
          padding: 0.5rem 1rem;
          background-color: #5e35b1;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        }

        .discover-button:disabled {
          background-color: #9e9e9e;
          cursor: not-allowed;
        }

        .endpoints-container {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .categories-tabs {
          display: flex;
          gap: 0.5rem;
          flex-wrap: wrap;
          margin-bottom: 1rem;
        }

        .category-tab {
          padding: 0.5rem 1rem;
          background-color: #f0f0f0;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .category-tab.active {
          background-color: #5e35b1;
          color: white;
        }

        .endpoints-list {
          overflow-x: auto;
        }

        table {
          width: 100%;
          border-collapse: collapse;
        }

        th, td {
          padding: 0.75rem;
          text-align: left;
          border-bottom: 1px solid #eee;
        }

        th {
          background-color: #f9f9f9;
          font-weight: 600;
        }

        tr.selected {
          background-color: #f0f7ff;
        }

        .status-badge {
          display: inline-block;
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
          font-size: 0.8rem;
          font-weight: 500;
        }

        .status-success {
          background-color: #e6f7e6;
          color: #2e7d32;
        }

        .status-redirect {
          background-color: #fff8e1;
          color: #ff8f00;
        }

        .status-client-error {
          background-color: #ffebee;
          color: #c62828;
        }

        .status-server-error {
          background-color: #fce4ec;
          color: #ad1457;
        }

        .select-button {
          padding: 0.25rem 0.5rem;
          background-color: #5e35b1;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 0.8rem;
        }

        .testing-section h2 {
          margin-top: 0;
          margin-bottom: 1.5rem;
          color: #333;
        }

        .endpoint-details {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1.5rem;
          padding: 1rem;
          background-color: #f9f9f9;
          border-radius: 4px;
          flex-wrap: wrap;
          gap: 1rem;
        }

        .method-selector select {
          margin-left: 0.5rem;
          padding: 0.25rem 0.5rem;
          border: 1px solid #ddd;
          border-radius: 4px;
        }

        .payload-container {
          margin-bottom: 1.5rem;
        }

        .payload-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 0.5rem;
        }

        .generate-payload-button {
          padding: 0.25rem 0.5rem;
          background-color: #f0f0f0;
          border: 1px solid #ddd;
          border-radius: 4px;
          cursor: pointer;
          font-size: 0.8rem;
        }

        .payload-textarea {
          width: 100%;
          padding: 0.75rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-family: monospace;
          resize: vertical;
        }

        .test-actions {
          margin-bottom: 1.5rem;
        }

        .test-button {
          padding: 0.5rem 1rem;
          background-color: #5e35b1;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        }

        .test-button:disabled {
          background-color: #9e9e9e;
          cursor: not-allowed;
        }

        .test-result {
          padding: 1rem;
          background-color: #f9f9f9;
          border-radius: 4px;
        }

        .test-result h3 {
          margin-top: 0;
          margin-bottom: 1rem;
          color: #333;
        }

        .result-details {
          display: flex;
          flex-wrap: wrap;
          gap: 1rem;
          margin-bottom: 1.5rem;
        }

        .result-item {
          flex: 1;
          min-width: 200px;
        }

        .response-data, .response-error {
          margin-top: 1.5rem;
        }

        .response-data h4, .response-error h4 {
          margin-top: 0;
          margin-bottom: 0.5rem;
          color: #333;
        }

        .response-data pre, .response-error pre {
          padding: 1rem;
          background-color: #f0f0f0;
          border-radius: 4px;
          overflow-x: auto;
          font-family: monospace;
          font-size: 0.9rem;
        }

        .response-error pre {
          background-color: #ffebee;
          color: #c62828;
        }

        /* Modal styles */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 1000;
        }

        .auth-modal {
          background-color: white;
          border-radius: 8px;
          padding: 1.5rem;
          width: 90%;
          max-width: 500px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .auth-modal h2 {
          margin-top: 0;
          color: #333;
        }

        .auth-modal p {
          color: #666;
          margin-bottom: 1rem;
        }

        .auth-modal textarea {
          width: 100%;
          padding: 0.75rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-family: monospace;
          margin-bottom: 1rem;
          resize: vertical;
        }

        .modal-actions {
          display: flex;
          justify-content: flex-end;
          gap: 0.75rem;
        }

        .cancel-button, .save-button {
          padding: 0.5rem 1rem;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        }

        .cancel-button {
          background-color: #f0f0f0;
          color: #333;
        }

        .save-button {
          background-color: #5e35b1;
          color: white;
        }

        @media (max-width: 768px) {
          .header {
            flex-direction: column;
            align-items: flex-start;
          }

          .actions {
            width: 100%;
            justify-content: flex-start;
          }

          .discovery-header {
            flex-direction: column;
            align-items: flex-start;
          }

          .base-url-container {
            width: 100%;
          }

          .base-url-input {
            flex: 1;
            min-width: 0;
          }

          .endpoint-details {
            flex-direction: column;
            align-items: flex-start;
          }
        }
      `}</style>
    </div>
  );
}
