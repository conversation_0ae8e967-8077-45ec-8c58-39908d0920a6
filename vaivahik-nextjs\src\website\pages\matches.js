import React, { useEffect } from 'react';
import { Container, Typography, Box, Paper } from '@mui/material';
import { useRouter } from 'next/router';
import { useAuth } from '@/contexts/AuthContext';
import MatchList from '@/components/matching/MatchList';
import MainLayout from '@/components/layouts/MainLayout';

export default function MatchesPage() {
  const { isAuthenticated, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      router.push('/login?callbackUrl=/matches');
    }
  }, [isAuthenticated, loading, router]);

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <MainLayout title="Your Matches">
      <Container maxWidth="lg">
        <Paper sx={{ p: 3, mt: 3 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Your Matches
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Discover profiles that match your preferences. Our AI-powered matching algorithm finds the most compatible profiles for you.
          </Typography>

          <Box sx={{ mt: 4 }}>
            <MatchList />
          </Box>
        </Paper>
      </Container>
    </MainLayout>
  );
}

// No server-side props needed with client-side auth
