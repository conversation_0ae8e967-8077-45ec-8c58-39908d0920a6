-- CreateTable
CREATE TABLE "interests" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "target_user_id" TEXT NOT NULL,
    "message" TEXT,
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "response_message" TEXT,
    "responded_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "interests_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "shortlists" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "target_user_id" TEXT NOT NULL,
    "note" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "shortlists_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "contact_access" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "target_user_id" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "reason" TEXT,
    "granted_at" TIMESTAMP(3),
    "expires_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "contact_access_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "profile_likes" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "target_user_id" TEXT NOT NULL,
    "likeType" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "profile_likes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "profile_views_detailed" (
    "id" TEXT NOT NULL,
    "viewer_id" TEXT NOT NULL,
    "viewed_user_id" TEXT NOT NULL,
    "view_duration" INTEGER,
    "device_type" TEXT,
    "ip_address" TEXT,
    "user_agent" TEXT,
    "referrer" TEXT,
    "sections_viewed" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "profile_views_detailed_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "interests_user_id_idx" ON "interests"("user_id");

-- CreateIndex
CREATE INDEX "interests_target_user_id_idx" ON "interests"("target_user_id");

-- CreateIndex
CREATE INDEX "interests_status_idx" ON "interests"("status");

-- CreateIndex
CREATE UNIQUE INDEX "interests_user_id_target_user_id_key" ON "interests"("user_id", "target_user_id");

-- CreateIndex
CREATE INDEX "shortlists_user_id_idx" ON "shortlists"("user_id");

-- CreateIndex
CREATE INDEX "shortlists_target_user_id_idx" ON "shortlists"("target_user_id");

-- CreateIndex
CREATE UNIQUE INDEX "shortlists_user_id_target_user_id_key" ON "shortlists"("user_id", "target_user_id");

-- CreateIndex
CREATE INDEX "contact_access_user_id_idx" ON "contact_access"("user_id");

-- CreateIndex
CREATE INDEX "contact_access_target_user_id_idx" ON "contact_access"("target_user_id");

-- CreateIndex
CREATE INDEX "contact_access_status_idx" ON "contact_access"("status");

-- CreateIndex
CREATE UNIQUE INDEX "contact_access_user_id_target_user_id_key" ON "contact_access"("user_id", "target_user_id");

-- CreateIndex
CREATE INDEX "profile_likes_user_id_idx" ON "profile_likes"("user_id");

-- CreateIndex
CREATE INDEX "profile_likes_target_user_id_idx" ON "profile_likes"("target_user_id");

-- CreateIndex
CREATE INDEX "profile_likes_likeType_idx" ON "profile_likes"("likeType");

-- CreateIndex
CREATE UNIQUE INDEX "profile_likes_user_id_target_user_id_key" ON "profile_likes"("user_id", "target_user_id");

-- CreateIndex
CREATE INDEX "profile_views_detailed_viewer_id_idx" ON "profile_views_detailed"("viewer_id");

-- CreateIndex
CREATE INDEX "profile_views_detailed_viewed_user_id_idx" ON "profile_views_detailed"("viewed_user_id");

-- CreateIndex
CREATE INDEX "profile_views_detailed_created_at_idx" ON "profile_views_detailed"("created_at");

-- AddForeignKey
ALTER TABLE "interests" ADD CONSTRAINT "interests_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "interests" ADD CONSTRAINT "interests_target_user_id_fkey" FOREIGN KEY ("target_user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shortlists" ADD CONSTRAINT "shortlists_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shortlists" ADD CONSTRAINT "shortlists_target_user_id_fkey" FOREIGN KEY ("target_user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "contact_access" ADD CONSTRAINT "contact_access_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "contact_access" ADD CONSTRAINT "contact_access_target_user_id_fkey" FOREIGN KEY ("target_user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "profile_likes" ADD CONSTRAINT "profile_likes_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "profile_likes" ADD CONSTRAINT "profile_likes_target_user_id_fkey" FOREIGN KEY ("target_user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "profile_views_detailed" ADD CONSTRAINT "profile_views_detailed_viewer_id_fkey" FOREIGN KEY ("viewer_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "profile_views_detailed" ADD CONSTRAINT "profile_views_detailed_viewed_user_id_fkey" FOREIGN KEY ("viewed_user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
