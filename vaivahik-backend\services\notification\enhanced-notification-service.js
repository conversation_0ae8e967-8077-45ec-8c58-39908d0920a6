/**
 * Enhanced Notification Service
 *
 * This service combines Redis and FCM for a comprehensive notification system.
 * - Redis for storage, retrieval, and real-time delivery
 * - FCM for push notifications to mobile devices
 */
const fcmService = require('./notification-service');
const redisService = require('./redis-notification-service');
const notificationScheduler = require('./notification-scheduler');
const templates = require('./templates');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * Send a notification to a single user
 *
 * @param {string} userId - ID of the user to send the notification to
 * @param {Object} notification - Notification content
 * @param {Object} options - Options for the notification
 * @param {boolean} options.storeInRedis - Whether to store the notification in Redis (default: true)
 * @param {boolean} options.storeInDatabase - Whether to store the notification in the database (default: true)
 * @param {boolean} options.sendPush - Whether to send a push notification via FCM (default: true)
 * @param {number} options.ttl - Time-to-live in seconds for Redis storage (default: 30 days)
 * @returns {Promise<Object>} Result of the send operation
 */
const sendToUser = async (userId, notification, options = {}) => {
  try {
    const {
      storeInRedis = true,
      storeInDatabase = true,
      sendPush = true,
      ttl = 30 * 24 * 60 * 60 // 30 days in seconds
    } = options;

    // Add user ID to notification
    notification.userId = userId;

    // Add TTL if storing in Redis
    if (storeInRedis) {
      notification.ttl = ttl;
    }

    // Store in Redis if enabled
    let notificationId;
    if (storeInRedis) {
      notificationId = await redisService.storeNotification(notification);
      await redisService.addNotificationToUser(userId, notificationId);

      // Publish to user's channel for real-time updates
      await redisService.publishNotification(`user:${userId}`, notification);
    }

    // Store in database if enabled
    if (storeInDatabase) {
      await prisma.notification.create({
        data: {
          userId,
          title: notification.title,
          body: notification.body,
          imageUrl: notification.imageUrl,
          data: notification.data || {},
          isRead: false,
          sentViaFCM: sendPush
        }
      });
    }

    // Send push notification via FCM if enabled
    let fcmResult = { success: true };
    if (sendPush) {
      fcmResult = await fcmService.sendToUser(userId, notification);
    }

    return {
      success: true,
      notificationId,
      fcmResult
    };
  } catch (error) {
    console.error('Error sending notification to user:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Send a notification to multiple users
 *
 * @param {string[]} userIds - IDs of users to send the notification to
 * @param {Object} notification - Notification content
 * @param {Object} options - Options for the notification
 * @returns {Promise<Object>} Result of the send operation
 */
const sendToUsers = async (userIds, notification, options = {}) => {
  try {
    const results = await Promise.all(
      userIds.map(userId => sendToUser(userId, notification, options))
    );

    return {
      success: true,
      results
    };
  } catch (error) {
    console.error('Error sending notification to multiple users:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Send a notification to a topic
 *
 * @param {string} topic - Topic to send the notification to
 * @param {Object} notification - Notification content
 * @param {Object} options - Options for the notification
 * @param {boolean} options.storeInRedis - Whether to store the notification in Redis (default: true)
 * @param {boolean} options.storeInDatabase - Whether to store the notification in the database (default: true)
 * @param {boolean} options.sendPush - Whether to send a push notification via FCM (default: true)
 * @param {number} options.ttl - Time-to-live in seconds for Redis storage (default: 7 days)
 * @returns {Promise<Object>} Result of the send operation
 */
const sendToTopic = async (topic, notification, options = {}) => {
  try {
    const {
      storeInRedis = true,
      storeInDatabase = true,
      sendPush = true,
      ttl = 7 * 24 * 60 * 60 // 7 days in seconds
    } = options;

    // Add topic to notification
    notification.topic = topic;

    // Add TTL if storing in Redis
    if (storeInRedis) {
      notification.ttl = ttl;
    }

    // Store in Redis if enabled
    let notificationId;
    if (storeInRedis) {
      notificationId = await redisService.storeNotification(notification);
      await redisService.addNotificationToTopic(topic, notificationId);

      // Publish to topic's channel for real-time updates
      await redisService.publishNotification(`topic:${topic}`, notification);
    }

    // Store in database if enabled
    if (storeInDatabase) {
      await prisma.topicNotification.create({
        data: {
          topic,
          title: notification.title,
          body: notification.body,
          imageUrl: notification.imageUrl,
          data: notification.data || {}
        }
      });
    }

    // Send push notification via FCM if enabled
    let fcmResult = { success: true };
    if (sendPush) {
      fcmResult = await fcmService.sendToTopic(topic, notification);
    }

    return {
      success: true,
      notificationId,
      fcmResult
    };
  } catch (error) {
    console.error(`Error sending notification to topic ${topic}:`, error);
    return { success: false, error: error.message };
  }
};

/**
 * Get notifications for a user
 *
 * @param {string} userId - ID of the user
 * @param {Object} options - Options for retrieving notifications
 * @param {number} options.limit - Maximum number of notifications to retrieve
 * @param {number} options.offset - Number of notifications to skip
 * @param {boolean} options.unreadOnly - Whether to retrieve only unread notifications
 * @param {string} options.source - Source to retrieve notifications from ('redis', 'database', or 'both')
 * @returns {Promise<Object>} Notifications and metadata
 */
const getUserNotifications = async (userId, options = {}) => {
  try {
    const {
      limit = 20,
      offset = 0,
      unreadOnly = false,
      source = 'both' // 'redis', 'database', or 'both'
    } = options;

    let redisNotifications = [];
    let databaseNotifications = [];

    // Get notifications from Redis if source is 'redis' or 'both'
    if (source === 'redis' || source === 'both') {
      redisNotifications = await redisService.getUserNotifications(userId, {
        limit,
        offset,
        unreadOnly
      });
    }

    // Get notifications from database if source is 'database' or 'both'
    if (source === 'database' || source === 'both') {
      const whereClause = {
        userId
      };

      if (unreadOnly) {
        whereClause.isRead = false;
      }

      databaseNotifications = await prisma.notification.findMany({
        where: whereClause,
        orderBy: {
          createdAt: 'desc'
        },
        skip: offset,
        take: limit
      });
    }

    // Combine and deduplicate notifications if getting from both sources
    let notifications = [];
    if (source === 'both') {
      // Create a map of notifications by ID
      const notificationMap = new Map();

      // Add Redis notifications to map
      redisNotifications.forEach(notification => {
        notificationMap.set(notification.id, notification);
      });

      // Add database notifications to map (will overwrite Redis notifications with same ID)
      databaseNotifications.forEach(notification => {
        notificationMap.set(notification.id, {
          ...notification,
          data: notification.data || {}
        });
      });

      // Convert map values to array
      notifications = Array.from(notificationMap.values());

      // Sort by createdAt (newest first)
      notifications.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      // Apply limit
      notifications = notifications.slice(0, limit);
    } else if (source === 'redis') {
      notifications = redisNotifications;
    } else {
      notifications = databaseNotifications;
    }

    // Get unread count
    const unreadCount = await getUnreadCount(userId);

    return {
      notifications,
      unreadCount,
      hasMore: notifications.length === limit
    };
  } catch (error) {
    console.error(`Error getting notifications for user ${userId}:`, error);
    throw error;
  }
};

/**
 * Get the count of unread notifications for a user
 *
 * @param {string} userId - ID of the user
 * @returns {Promise<number>} Count of unread notifications
 */
const getUnreadCount = async (userId) => {
  try {
    // Get unread count from database (more reliable than Redis for this)
    return await prisma.notification.count({
      where: {
        userId,
        isRead: false
      }
    });
  } catch (error) {
    console.error(`Error getting unread count for user ${userId}:`, error);
    throw error;
  }
};

/**
 * Mark a notification as read
 *
 * @param {string} notificationId - ID of the notification
 * @param {string} userId - ID of the user
 * @returns {Promise<boolean>} Whether the operation was successful
 */
const markNotificationAsRead = async (notificationId, userId) => {
  try {
    // Mark as read in Redis
    await redisService.markNotificationAsRead(notificationId, userId);

    // Mark as read in database
    const notification = await prisma.notification.findFirst({
      where: {
        id: notificationId,
        userId
      }
    });

    if (notification) {
      await prisma.notification.update({
        where: { id: notificationId },
        data: {
          isRead: true,
          readAt: new Date()
        }
      });
      return true;
    }

    return false;
  } catch (error) {
    console.error(`Error marking notification ${notificationId} as read:`, error);
    throw error;
  }
};

/**
 * Mark all notifications as read for a user
 *
 * @param {string} userId - ID of the user
 * @returns {Promise<number>} Number of notifications marked as read
 */
const markAllNotificationsAsRead = async (userId) => {
  try {
    // Mark all as read in Redis
    await redisService.markAllNotificationsAsRead(userId);

    // Mark all as read in database
    const result = await prisma.notification.updateMany({
      where: {
        userId,
        isRead: false
      },
      data: {
        isRead: true,
        readAt: new Date()
      }
    });

    return result.count;
  } catch (error) {
    console.error(`Error marking all notifications as read for user ${userId}:`, error);
    throw error;
  }
};

/**
 * Schedule a notification for future delivery
 *
 * @param {string} notificationType - Type of notification (e.g., 'promotional', 'newMatch')
 * @param {string} targetType - Type of target (USER, TOPIC, ALL_USERS, PREMIUM_USERS, etc.)
 * @param {string} targetId - ID of the target (user ID or topic name)
 * @param {Date} scheduledFor - When to send the notification
 * @param {Object} data - Data for the notification template
 * @returns {Promise<Object>} The created scheduled notification
 */
const scheduleNotification = async (notificationType, targetType, targetId, scheduledFor, data) => {
  try {
    // Create the scheduled notification
    return await notificationScheduler.createScheduledNotification({
      notificationType,
      targetType,
      targetId,
      scheduledFor,
      data
    });
  } catch (error) {
    console.error('Error scheduling notification:', error);
    throw error;
  }
};

/**
 * Cancel a scheduled notification
 *
 * @param {string} id - ID of the scheduled notification
 * @returns {Promise<Object>} The cancelled notification
 */
const cancelScheduledNotification = async (id) => {
  try {
    return await notificationScheduler.cancelScheduledNotification(id);
  } catch (error) {
    console.error(`Error cancelling scheduled notification ${id}:`, error);
    throw error;
  }
};

/**
 * Get all scheduled notifications
 *
 * @param {Object} options - Options for retrieving scheduled notifications
 * @param {string} options.status - Filter by status (SCHEDULED, SENT, FAILED, CANCELLED)
 * @returns {Promise<Array>} Array of scheduled notifications
 */
const getScheduledNotifications = async (options = {}) => {
  try {
    const { status } = options;

    const whereClause = {};
    if (status) {
      whereClause.status = status;
    }

    return await prisma.scheduledNotification.findMany({
      where: whereClause,
      orderBy: {
        scheduledFor: 'asc'
      }
    });
  } catch (error) {
    console.error('Error getting scheduled notifications:', error);
    throw error;
  }
};

module.exports = {
  sendToUser,
  sendToUsers,
  sendToTopic,
  getUserNotifications,
  getUnreadCount,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  scheduleNotification,
  cancelScheduledNotification,
  getScheduledNotifications,
  templates: fcmService.templates
};
