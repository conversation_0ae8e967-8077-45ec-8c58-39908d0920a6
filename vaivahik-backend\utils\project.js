// utils/project.js

// Define standard income range options
const incomeOptions = [
    'Below ₹2L', '₹2L–5L', '₹5L–10L',
    '₹10L–25L', '₹25L–50L',
    '₹50L–1Cr', 'Above ₹1Cr'
  ];
  
  // Define photo visibility options (mapping internal keys to display text if needed)
  // Note: The Prisma enum uses PUBLIC, PAID, INTEREST_BASED
  const visibilityOptions = {
    PUBLIC: 'Visible to All',
    PAID: 'Paid Members Only', // Corresponds to PAID enum
    INTEREST_BASED: 'Only After Interest Exchange' // Corresponds to INTEREST_BASED enum
  };
  
  /**
   * Validates if a file size is within the specified limit.
   * @param {object} file - The file object (e.g., from Multer req.file). Expected to have a 'size' property in bytes.
   * @param {number} [maxSizeMB=2] - Maximum allowed file size in megabytes.
   * @returns {boolean} True if the file size is valid, false otherwise.
   */
  function validateImageSize(file, maxSizeMB = 2) {
    if (!file || typeof file.size !== 'number') {
      console.error('Invalid file object passed to validateImageSize');
      return false; // Or throw an error
    }
    const maxSizeInBytes = maxSizeMB * 1024 * 1024;
    return file.size <= maxSizeInBytes;
  }
  
  // Export the constants and functions to be used elsewhere
  module.exports = {
    incomeOptions,
    visibilityOptions, // You might not need to export this if only used on frontend
    validateImageSize,
  };
  