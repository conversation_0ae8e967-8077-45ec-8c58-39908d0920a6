/**
 * User Profile Action Buttons Component
 * Comprehensive action buttons for user profiles including <PERSON>, Shortlist, <PERSON><PERSON>, Call, <PERSON>nd<PERSON>
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  IconButton,
  Tooltip,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Typography,
  Grid,
  Alert,
  CircularProgress,
  styled
} from '@mui/material';

// Icons
import {
  Favorite as HeartIcon,
  FavoriteBorder as HeartBorderIcon,
  BookmarkBorder as BookmarkIcon,
  Bookmark as BookmarkFilledIcon,
  Message as MessageIcon,
  Phone as PhoneIcon,
  Psychology as KundliIcon,
  Visibility as ViewIcon,
  Send as SendIcon,
  Block as BlockIcon,
  Report as ReportIcon,
  Share as ShareIcon,
  WorkspacePremium as PremiumIcon
} from '@mui/icons-material';

// Import existing components
import SmartCallButton from '@/components/contact/SmartCallButton';
import { PrivacyAwareName } from '@/components/dashboard/PrivacyAwareDisplay';

// Styled components
const ActionButton = styled(Button)(({ theme, variant: buttonVariant }) => ({
  borderRadius: 12,
  textTransform: 'none',
  fontWeight: 600,
  minWidth: 'auto',
  ...(buttonVariant === 'interest' && {
    background: 'linear-gradient(135deg, #E91E63, #F06292)',
    color: 'white',
    '&:hover': {
      background: 'linear-gradient(135deg, #C2185B, #E91E63)'
    }
  }),
  ...(buttonVariant === 'shortlist' && {
    background: 'linear-gradient(135deg, #FF9800, #FFB74D)',
    color: 'white',
    '&:hover': {
      background: 'linear-gradient(135deg, #F57C00, #FF9800)'
    }
  }),
  ...(buttonVariant === 'message' && {
    background: 'linear-gradient(135deg, #4CAF50, #81C784)',
    color: 'white',
    '&:hover': {
      background: 'linear-gradient(135deg, #388E3C, #4CAF50)'
    }
  }),
  ...(buttonVariant === 'kundli' && {
    background: 'linear-gradient(135deg, #9C27B0, #BA68C8)',
    color: 'white',
    '&:hover': {
      background: 'linear-gradient(135deg, #7B1FA2, #9C27B0)'
    }
  })
}));

const CompactButton = styled(IconButton)(({ theme, active }) => ({
  borderRadius: 8,
  border: '2px solid',
  borderColor: active ? theme.palette.primary.main : theme.palette.divider,
  backgroundColor: active ? theme.palette.primary.main : 'transparent',
  color: active ? 'white' : theme.palette.text.primary,
  '&:hover': {
    borderColor: theme.palette.primary.main,
    backgroundColor: active ? theme.palette.primary.dark : theme.palette.primary.light
  }
}));

export default function UserProfileActionButtons({
  targetUser,
  currentUser,
  compact = false,
  showLabels = true,
  isPremium = false,
  onPremiumRequired,
  onActionComplete
}) {
  const [states, setStates] = useState({
    interested: false,
    shortlisted: false,
    blocked: false,
    loading: {}
  });
  
  const [dialogs, setDialogs] = useState({
    interest: false,
    message: false,
    report: false,
    kundli: false
  });
  
  const [forms, setForms] = useState({
    interestMessage: '',
    chatMessage: '',
    reportReason: ''
  });

  useEffect(() => {
    fetchUserInteractionStatus();
  }, [targetUser?.id, currentUser?.id]);

  const fetchUserInteractionStatus = async () => {
    if (!targetUser?.id || !currentUser?.id) return;

    try {
      // Fetch current interaction status
      const response = await fetch(`/api/user/interactions/${targetUser.id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('userToken')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStates(prev => ({
          ...prev,
          interested: data.hasInterest,
          shortlisted: data.isShortlisted,
          blocked: data.isBlocked
        }));
      }
    } catch (error) {
      console.error('Error fetching interaction status:', error);
    }
  };

  const handleAction = async (action, payload = {}) => {
    if (!currentUser?.id || !targetUser?.id) return;

    setStates(prev => ({
      ...prev,
      loading: { ...prev.loading, [action]: true }
    }));

    try {
      let endpoint = '';
      let method = 'POST';
      let body = { targetUserId: targetUser.id, ...payload };

      switch (action) {
        case 'interest':
          endpoint = '/api/user/interests/send';
          body.message = forms.interestMessage;
          break;
        case 'shortlist':
          endpoint = '/api/user/shortlist';
          method = states.shortlisted ? 'DELETE' : 'POST';
          break;
        case 'message':
          endpoint = '/api/messages/send';
          body.message = forms.chatMessage;
          break;
        case 'block':
          endpoint = '/api/user/block';
          method = states.blocked ? 'DELETE' : 'POST';
          break;
        case 'report':
          endpoint = '/api/user/report';
          body.reason = forms.reportReason;
          break;
        default:
          throw new Error('Unknown action');
      }

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('userToken')}`
        },
        body: JSON.stringify(body)
      });

      if (response.ok) {
        const result = await response.json();
        
        // Update states based on action
        setStates(prev => ({
          ...prev,
          interested: action === 'interest' ? true : prev.interested,
          shortlisted: action === 'shortlist' ? !prev.shortlisted : prev.shortlisted,
          blocked: action === 'block' ? !prev.blocked : prev.blocked
        }));

        // Close dialogs and reset forms
        setDialogs({ interest: false, message: false, report: false, kundli: false });
        setForms({ interestMessage: '', chatMessage: '', reportReason: '' });

        onActionComplete?.(action, result);
      } else {
        const error = await response.json();
        throw new Error(error.message || 'Action failed');
      }
    } catch (error) {
      console.error(`Error performing ${action}:`, error);
      // Handle error (show toast, etc.)
    } finally {
      setStates(prev => ({
        ...prev,
        loading: { ...prev.loading, [action]: false }
      }));
    }
  };

  const handlePremiumAction = (action) => {
    if (!isPremium) {
      onPremiumRequired?.(action);
      return;
    }
    
    switch (action) {
      case 'message':
        setDialogs(prev => ({ ...prev, message: true }));
        break;
      case 'kundli':
        setDialogs(prev => ({ ...prev, kundli: true }));
        break;
      default:
        break;
    }
  };

  const renderCompactButtons = () => (
    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
      {/* Interest Button */}
      <Tooltip title={states.interested ? 'Interest Sent' : 'Send Interest'}>
        <CompactButton
          active={states.interested}
          onClick={() => states.interested ? null : setDialogs(prev => ({ ...prev, interest: true }))}
          disabled={states.interested || states.loading.interest}
        >
          {states.loading.interest ? (
            <CircularProgress size={20} />
          ) : states.interested ? (
            <HeartIcon />
          ) : (
            <HeartBorderIcon />
          )}
        </CompactButton>
      </Tooltip>

      {/* Shortlist Button */}
      <Tooltip title={states.shortlisted ? 'Remove from Shortlist' : 'Add to Shortlist'}>
        <CompactButton
          active={states.shortlisted}
          onClick={() => handleAction('shortlist')}
          disabled={states.loading.shortlist}
        >
          {states.loading.shortlist ? (
            <CircularProgress size={20} />
          ) : states.shortlisted ? (
            <BookmarkFilledIcon />
          ) : (
            <BookmarkIcon />
          )}
        </CompactButton>
      </Tooltip>

      {/* Message Button */}
      <Tooltip title={isPremium ? 'Send Message' : 'Send Message (Premium)'}>
        <CompactButton onClick={() => handlePremiumAction('message')}>
          <MessageIcon />
          {!isPremium && <PremiumIcon sx={{ fontSize: 12, position: 'absolute', top: -2, right: -2 }} />}
        </CompactButton>
      </Tooltip>

      {/* Call Button */}
      <SmartCallButton
        targetUserId={targetUser?.id}
        targetUserName={targetUser?.fullName}
        variant="icon"
        size="small"
      />

      {/* Kundli Button */}
      <Tooltip title={isPremium ? 'View Kundli Match' : 'Kundli Match (Premium)'}>
        <CompactButton onClick={() => handlePremiumAction('kundli')}>
          <KundliIcon />
          {!isPremium && <PremiumIcon sx={{ fontSize: 12, position: 'absolute', top: -2, right: -2 }} />}
        </CompactButton>
      </Tooltip>
    </Box>
  );

  const renderFullButtons = () => (
    <Grid container spacing={2}>
      {/* Interest Button */}
      <Grid item xs={12} sm={6} md={4}>
        <ActionButton
          fullWidth
          variant="interest"
          startIcon={states.interested ? <HeartIcon /> : <HeartBorderIcon />}
          onClick={() => states.interested ? null : setDialogs(prev => ({ ...prev, interest: true }))}
          disabled={states.interested || states.loading.interest}
        >
          {states.loading.interest ? (
            <CircularProgress size={20} color="inherit" />
          ) : states.interested ? (
            'Interest Sent'
          ) : (
            showLabels ? 'Send Interest' : <HeartBorderIcon />
          )}
        </ActionButton>
      </Grid>

      {/* Shortlist Button */}
      <Grid item xs={12} sm={6} md={4}>
        <ActionButton
          fullWidth
          variant="shortlist"
          startIcon={states.shortlisted ? <BookmarkFilledIcon /> : <BookmarkIcon />}
          onClick={() => handleAction('shortlist')}
          disabled={states.loading.shortlist}
        >
          {states.loading.shortlist ? (
            <CircularProgress size={20} color="inherit" />
          ) : states.shortlisted ? (
            'Shortlisted'
          ) : (
            showLabels ? 'Add to Shortlist' : <BookmarkIcon />
          )}
        </ActionButton>
      </Grid>

      {/* Message Button */}
      <Grid item xs={12} sm={6} md={4}>
        <ActionButton
          fullWidth
          variant="message"
          startIcon={<MessageIcon />}
          onClick={() => handlePremiumAction('message')}
          endIcon={!isPremium ? <PremiumIcon /> : null}
        >
          {showLabels ? (isPremium ? 'Send Message' : 'Message (Premium)') : <MessageIcon />}
        </ActionButton>
      </Grid>

      {/* Call Button */}
      <Grid item xs={12} sm={6} md={4}>
        <SmartCallButton
          targetUserId={targetUser?.id}
          targetUserName={targetUser?.fullName}
          variant="contained"
          size="large"
          fullWidth
          showLabel={showLabels}
        />
      </Grid>

      {/* Kundli Button */}
      <Grid item xs={12} sm={6} md={4}>
        <ActionButton
          fullWidth
          variant="kundli"
          startIcon={<KundliIcon />}
          onClick={() => handlePremiumAction('kundli')}
          endIcon={!isPremium ? <PremiumIcon /> : null}
        >
          {showLabels ? (isPremium ? 'Kundli Match' : 'Kundli (Premium)') : <KundliIcon />}
        </ActionButton>
      </Grid>

      {/* View Profile Button */}
      <Grid item xs={12} sm={6} md={4}>
        <Button
          fullWidth
          variant="outlined"
          startIcon={<ViewIcon />}
          onClick={() => window.open(`/profile/${targetUser?.id}`, '_blank')}
        >
          {showLabels ? 'View Full Profile' : <ViewIcon />}
        </Button>
      </Grid>
    </Grid>
  );

  return (
    <Box>
      {compact ? renderCompactButtons() : renderFullButtons()}

      {/* Interest Dialog */}
      <Dialog open={dialogs.interest} onClose={() => setDialogs(prev => ({ ...prev, interest: false }))}>
        <DialogTitle>
          Send Interest to <PrivacyAwareName user={targetUser} viewerUser={currentUser} />
        </DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            rows={3}
            label="Personal Message (Optional)"
            value={forms.interestMessage}
            onChange={(e) => setForms(prev => ({ ...prev, interestMessage: e.target.value }))}
            placeholder="Write a thoughtful message to introduce yourself..."
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogs(prev => ({ ...prev, interest: false }))}>
            Cancel
          </Button>
          <Button
            onClick={() => handleAction('interest')}
            variant="contained"
            startIcon={<SendIcon />}
            disabled={states.loading.interest}
          >
            Send Interest
          </Button>
        </DialogActions>
      </Dialog>

      {/* Message Dialog */}
      <Dialog open={dialogs.message} onClose={() => setDialogs(prev => ({ ...prev, message: false }))}>
        <DialogTitle>
          Send Message to <PrivacyAwareName user={targetUser} viewerUser={currentUser} />
        </DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            rows={4}
            label="Your Message"
            value={forms.chatMessage}
            onChange={(e) => setForms(prev => ({ ...prev, chatMessage: e.target.value }))}
            placeholder="Type your message here..."
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogs(prev => ({ ...prev, message: false }))}>
            Cancel
          </Button>
          <Button
            onClick={() => handleAction('message')}
            variant="contained"
            startIcon={<SendIcon />}
            disabled={!forms.chatMessage.trim() || states.loading.message}
          >
            Send Message
          </Button>
        </DialogActions>
      </Dialog>

      {/* Kundli Dialog */}
      <Dialog open={dialogs.kundli} onClose={() => setDialogs(prev => ({ ...prev, kundli: false }))} maxWidth="md" fullWidth>
        <DialogTitle>
          Kundli Matching with <PrivacyAwareName user={targetUser} viewerUser={currentUser} />
        </DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            Kundli matching is available for premium users. This feature provides detailed astrological compatibility analysis.
          </Alert>
          <Typography variant="body2" color="text.secondary">
            Premium kundli matching includes:
            • Guna Milan (36 points system)
            • Mangal Dosha analysis
            • Detailed compatibility report
            • Astrological recommendations
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogs(prev => ({ ...prev, kundli: false }))}>
            Close
          </Button>
          {isPremium && (
            <Button variant="contained" startIcon={<KundliIcon />}>
              View Kundli Match
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
}
