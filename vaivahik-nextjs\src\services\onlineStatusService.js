import { io } from 'socket.io-client';
import { API_BASE_URL } from '@/config';

let socket = null;
let statusListeners = new Map(); // userId -> callback

/**
 * Initialize the Socket.IO connection
 * @param {String} token - JWT token for authentication
 */
export const initializeSocket = (token) => {
  if (socket) {
    // If socket exists but disconnected, reconnect
    if (socket.disconnected) {
      socket.connect();
    }
    return;
  }
  
  // Create new socket connection
  socket = io(API_BASE_URL.replace('/api', ''), {
    autoConnect: true,
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000
  });
  
  // Set up event listeners
  socket.on('connect', () => {
    console.log('Socket connected');
    
    // Authenticate with the server
    socket.emit('authenticate', token);
  });
  
  socket.on('disconnect', () => {
    console.log('Socket disconnected');
  });
  
  socket.on('connect_error', (error) => {
    console.error('Socket connection error:', error);
  });
  
  socket.on('user_status_change', (data) => {
    const { userId, isOnline, lastActive } = data;
    
    // Notify all listeners for this user
    if (statusListeners.has(userId)) {
      statusListeners.get(userId).forEach(callback => {
        callback(isOnline, new Date(lastActive));
      });
    }
  });
};

/**
 * Disconnect the Socket.IO connection
 */
export const disconnectSocket = () => {
  if (socket) {
    socket.disconnect();
  }
};

/**
 * Subscribe to a user's online status changes
 * @param {String} userId - The user ID to monitor
 * @param {Function} callback - Function to call when status changes
 * @returns {Function} Unsubscribe function
 */
export const subscribeToUserStatus = (userId, callback) => {
  if (!statusListeners.has(userId)) {
    statusListeners.set(userId, new Set());
  }
  
  statusListeners.get(userId).add(callback);
  
  // Return unsubscribe function
  return () => {
    if (statusListeners.has(userId)) {
      statusListeners.get(userId).delete(callback);
      
      // Clean up if no more listeners
      if (statusListeners.get(userId).size === 0) {
        statusListeners.delete(userId);
      }
    }
  };
};

/**
 * Get a user's current online status
 * @param {String} userId - The user ID to check
 * @returns {Promise<Object>} Object with isOnline status
 */
export const getUserOnlineStatus = async (userId) => {
  try {
    const token = localStorage.getItem('token');
    
    const response = await fetch(`${API_BASE_URL}/users/${userId}/status`, {
      headers: {
        'x-auth-token': token
      }
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch user status');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching user online status:', error);
    return { isOnline: false };
  }
};

/**
 * Format the last active time in a human-readable format
 * @param {Date} lastActive - The last active timestamp
 * @returns {String} Formatted last active time
 */
export const formatLastActive = (lastActive) => {
  if (!lastActive) return 'Unknown';
  
  const now = new Date();
  const lastActiveDate = new Date(lastActive);
  const diffMs = now - lastActiveDate;
  
  // Convert to seconds
  const diffSec = Math.floor(diffMs / 1000);
  
  if (diffSec < 60) {
    return 'Just now';
  }
  
  // Convert to minutes
  const diffMin = Math.floor(diffSec / 60);
  
  if (diffMin < 60) {
    return `${diffMin} minute${diffMin !== 1 ? 's' : ''} ago`;
  }
  
  // Convert to hours
  const diffHour = Math.floor(diffMin / 60);
  
  if (diffHour < 24) {
    return `${diffHour} hour${diffHour !== 1 ? 's' : ''} ago`;
  }
  
  // Convert to days
  const diffDay = Math.floor(diffHour / 24);
  
  if (diffDay < 7) {
    return `${diffDay} day${diffDay !== 1 ? 's' : ''} ago`;
  }
  
  // Format as date for older timestamps
  return lastActiveDate.toLocaleDateString();
};
