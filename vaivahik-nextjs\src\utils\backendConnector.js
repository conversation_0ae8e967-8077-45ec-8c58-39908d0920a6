/**
 * Backend Connector
 * 
 * This utility provides a seamless way to switch between mock data and real backend.
 * It handles the transition from development to production by using feature flags.
 */

import axios from 'axios';
import { 
  BACKEND_CONFIG, 
  AUTH_CONFIG, 
  FEATURE_FLAGS, 
  MOCK_CONFIG 
} from '@/config/backendConfig';
import { getToken, getAdminToken } from '@/services/authService';

// Create axios instances
const createApiClient = (baseURL, getAuthToken) => {
  const client = axios.create({
    baseURL,
    timeout: BACKEND_CONFIG.timeout,
    headers: { ...BACKEND_CONFIG.headers }
  });

  // Request interceptor
  client.interceptors.request.use(
    (config) => {
      // Add auth token if available
      const token = getAuthToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor
  client.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalRequest = error.config;
      
      // Handle token refresh for 401 errors
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;
        
        try {
          // Attempt to refresh token
          const refreshToken = localStorage.getItem(AUTH_CONFIG.refreshTokenStorageKey);
          if (refreshToken) {
            const response = await axios.post(
              `${BACKEND_CONFIG.baseUrl}${AUTH_CONFIG.refreshEndpoint}`,
              { refreshToken }
            );
            
            if (response.data.token) {
              // Save new token
              localStorage.setItem(AUTH_CONFIG.tokenStorageKey, response.data.token);
              localStorage.setItem(AUTH_CONFIG.tokenExpiryKey, response.data.expiresAt);
              
              // Update authorization header
              originalRequest.headers.Authorization = `Bearer ${response.data.token}`;
              return axios(originalRequest);
            }
          }
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError);
          // Redirect to login page or dispatch logout action
          window.dispatchEvent(new CustomEvent('auth:logout'));
        }
      }
      
      return Promise.reject(error);
    }
  );

  return client;
};

// Create API clients
const realUserApi = createApiClient(
  `${BACKEND_CONFIG.baseUrl}/${BACKEND_CONFIG.apiVersion}`,
  getToken
);

const realAdminApi = createApiClient(
  `${BACKEND_CONFIG.baseUrl}/${BACKEND_CONFIG.apiVersion}/admin`,
  getAdminToken
);

// Mock data handler
const getMockData = async (endpoint, params = {}) => {
  // Simulate network delay
  if (MOCK_CONFIG.mockDelay > 0) {
    await new Promise(resolve => setTimeout(resolve, MOCK_CONFIG.mockDelay));
  }
  
  try {
    // Attempt to load mock data from public folder
    const mockPath = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    const response = await fetch(`${MOCK_CONFIG.mockDataPath}${mockPath}.json`);
    
    if (!response.ok) {
      throw new Error(`Mock data not found for ${endpoint}`);
    }
    
    const data = await response.json();
    
    // Apply filters if params are provided
    if (params && Object.keys(params).length > 0) {
      // Handle pagination
      if (params.page && params.limit && data.items) {
        const page = parseInt(params.page);
        const limit = parseInt(params.limit);
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        
        return {
          ...data,
          items: data.items.slice(startIndex, endIndex),
          pagination: {
            total: data.items.length,
            page,
            limit,
            totalPages: Math.ceil(data.items.length / limit)
          }
        };
      }
      
      // Handle search
      if (params.search && data.items) {
        const searchTerm = params.search.toLowerCase();
        const filteredItems = data.items.filter(item => {
          return Object.values(item).some(value => {
            if (typeof value === 'string') {
              return value.toLowerCase().includes(searchTerm);
            }
            return false;
          });
        });
        
        return {
          ...data,
          items: filteredItems,
          filtered: true
        };
      }
    }
    
    return data;
  } catch (error) {
    console.error(`Error loading mock data for ${endpoint}:`, error);
    return {
      success: false,
      message: `Mock data error: ${error.message}`
    };
  }
};

// Backend connector functions
const backendConnector = {
  // User API
  userApi: FEATURE_FLAGS.useRealBackend ? realUserApi : {
    get: async (url, config) => {
      const response = await getMockData(url, config?.params);
      return { data: response };
    },
    post: async (url, data) => {
      // For mock data, we'll just return a success response
      return {
        data: {
          success: true,
          message: 'Operation successful (mock)',
          data
        }
      };
    },
    put: async (url, data) => {
      return {
        data: {
          success: true,
          message: 'Update successful (mock)',
          data
        }
      };
    },
    delete: async (url) => {
      return {
        data: {
          success: true,
          message: 'Delete successful (mock)'
        }
      };
    }
  },
  
  // Admin API
  adminApi: FEATURE_FLAGS.useRealBackend ? realAdminApi : {
    get: async (url, config) => {
      const response = await getMockData(`/admin${url}`, config?.params);
      return { data: response };
    },
    post: async (url, data) => {
      return {
        data: {
          success: true,
          message: 'Admin operation successful (mock)',
          data
        }
      };
    },
    put: async (url, data) => {
      return {
        data: {
          success: true,
          message: 'Admin update successful (mock)',
          data
        }
      };
    },
    delete: async (url) => {
      return {
        data: {
          success: true,
          message: 'Admin delete successful (mock)'
        }
      };
    }
  },
  
  // Helper to check if using real backend
  isUsingRealBackend: () => FEATURE_FLAGS.useRealBackend,
  
  // Helper to toggle between real and mock backend (for development)
  toggleBackendMode: () => {
    if (typeof window !== 'undefined') {
      const currentMode = localStorage.getItem('useRealBackend');
      const newMode = currentMode === 'true' ? 'false' : 'true';
      localStorage.setItem('useRealBackend', newMode);
      window.location.reload();
    }
  }
};

export default backendConnector;
