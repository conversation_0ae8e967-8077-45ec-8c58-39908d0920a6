import React, { useState, useEffect, useRef } from "react";
import Head from "next/head";
import Link from "next/link";
import Script from "next/script";
// Import styles directly in the component
// Global CSS variables are imported in _app.js
import styles from "../styles/LandingPageNew.module.css";
// Import Swiper React components
import { Swiper, SwiperSlide } from 'swiper/react';
// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/effect-fade';
// Import required modules
import { Navigation, Pagination, Autoplay, EffectFade } from 'swiper/modules';

export default function LandingPage() {
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);
  const [activeNavLink, setActiveNavLink] = useState('#hero');
  const [activePricingPlan, setActivePricingPlan] = useState('monthly');
  const headerRef = useRef(null);
  const floatingHeartsRef = useRef(null);

  // Testimonials data
  const testimonials = [
    {
      image: "https://randomuser.me/api/portraits/women/65.jpg",
      author: "Priya & Sameer K.",
      date: "United: January 2024",
      text: "Vaivahik's AI truly understood what we were looking for, beyond just horoscopes. It connected us on a level of shared values that felt incredibly rare. We are now happily married, a testament to this platform's depth."
    },
    {
      image: "https://randomuser.me/api/portraits/men/32.jpg",
      author: "Aarav & Meera S.",
      date: "Connected: March 2024",
      text: "As busy professionals, time was precious. Vaivahik's verified profiles and AI-curated matches were a game-changer. We found a genuine, profound connection without the endless searching. Highly recommended!"
    },
    {
      image: "https://randomuser.me/api/portraits/women/28.jpg",
      author: "Neha & Vikram P.",
      date: "Married: December 2023",
      text: "Our families were involved from day one, and Vaivahik made that experience seamless. The platform's respect for tradition while embracing modern technology created the perfect environment for us to find each other."
    }
  ];

  // Success stories data
  const successStories = [
    {
      names: "Anika & Sameer",
      image: "https://picsum.photos/seed/successA/400/260",
      story: "Despite living miles apart, Vaivahik's platform brought us together. The detailed profiles and compatibility insights were key...",
      connected: "Aug 2024",
      married: "Feb 2025"
    },
    {
      names: "Vikram & Meera",
      image: "https://picsum.photos/seed/successB/400/260",
      story: "Finding someone who shared not just interests but core life values felt serendipitous. Vaivahik facilitated our journey beautifully.",
      connected: "Sep 2024",
      married: "Families Met: Dec 2024"
    },
    {
      names: "Deepa & Arjun",
      image: "https://picsum.photos/seed/successC/400/260",
      story: "The platform's respect for family involvement and privacy controls allowed us to navigate the process comfortably and confidently.",
      connected: "Jul 2024",
      married: "Mar 2025"
    }
  ];

  // Blog posts data
  const blogPosts = [
    {
      title: "Navigating Family Introductions: A Vaivahik Guide",
      excerpt: "Meeting the family is a significant step. Here are some tips to make it a smooth and positive experience...",
      image: "https://picsum.photos/seed/blogA/400/200",
      author: "Vaivahik Team",
      date: "May 3, 2024"
    },
    {
      title: "The Science of AI in Modern Matchmaking",
      excerpt: "How can algorithms help find love? We delve into the technology behind Vaivahik's smart matching...",
      image: "https://picsum.photos/seed/blogB/400/200",
      author: "Dr. Relationship Expert",
      date: "April 25, 2024"
    },
    {
      title: "From AI Match to \"I Do\": Anika & Sameer's Journey",
      excerpt: "Read the in-depth story of how Anika and Sameer found their happily ever after through Vaivahik...",
      image: "https://picsum.photos/seed/blogC/400/200",
      author: "Success Story",
      date: "April 18, 2024"
    }
  ];

  // Header Scroll & Active Link Logic
  useEffect(() => {
    const handleScroll = () => {
      if (headerRef.current) {
        headerRef.current.classList.toggle(styles.scrolled, window.scrollY > 50);
      }

      let currentSectionId = '#hero'; // Default to hero
      const sections = document.querySelectorAll('section[id]');
      const headerHeight = headerRef.current ? headerRef.current.offsetHeight : 70;

      sections.forEach(section => {
        const sectionTop = section.offsetTop;
        if (window.pageYOffset >= sectionTop - headerHeight - 70) {
          currentSectionId = `#${section.getAttribute('id')}`;
        }
      });
      setActiveNavLink(currentSectionId);
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial check
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Initialize floating hearts
  useEffect(() => {
    if (floatingHeartsRef.current) {
      const numHearts = 15;
      for (let i = 0; i < numHearts; i++) {
        createHeart();
      }
    }
  }, []);

  // Initialize animations
  useEffect(() => {
    const animatedElements = document.querySelectorAll('[data-animation]');

    const observer = new IntersectionObserver((entries, obs) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const animation = entry.target.getAttribute('data-animation') || 'animate__fadeIn';
          const animationClasses = animation.split(' ');

          // Check if the first animation class is already applied
          if (!entry.target.classList.contains(animationClasses[0])) {
            // Add all animation classes separately
            animationClasses.forEach(cls => {
              if (cls.trim()) {
                entry.target.classList.add(cls.trim());
              }
            });
          }
          obs.unobserve(entry.target);
        }
      });
    }, { threshold: 0.1 });

    animatedElements.forEach(el => {
      const animation = el.getAttribute('data-animation');
      if (animation) {
        // Remove all animation classes
        const animationClasses = animation.split(' ');
        animationClasses.forEach(cls => {
          if (cls.trim()) {
            el.classList.remove(cls.trim());
          }
        });
      }
      observer.observe(el);
    });
  }, []);

  // Mobile Nav Toggle
  const toggleMobileNav = () => {
    setIsMobileNavOpen(!isMobileNavOpen);
    document.body.classList.toggle('noScroll', !isMobileNavOpen);
  };

  const closeMobileNav = () => {
    setIsMobileNavOpen(false);
    document.body.classList.remove('noScroll');
  };

  // Switch pricing tabs
  const switchPricingTab = (tab) => {
    setActivePricingPlan(tab);
  };

  // Create floating hearts
  const createHeart = () => {
    if (!floatingHeartsRef.current) return;

    const heart = document.createElement('div');
    heart.classList.add(styles.heart);
    heart.style.left = `${Math.random() * 100}vw`;
    heart.style.animationDuration = `${Math.random() * 10 + 10}s`;
    heart.style.animationDelay = `${Math.random() * 5}s`;

    const scale = Math.random() * 0.5 + 0.5;
    heart.style.width = `${30 * scale}px`;
    heart.style.height = `${30 * scale}px`;
    heart.style.opacity = `${Math.random() * 0.4 + 0.3}`;

    floatingHeartsRef.current.appendChild(heart);

    heart.addEventListener('animationend', () => {
      heart.remove();
      if (floatingHeartsRef.current && floatingHeartsRef.current.children.length < 20) {
        createHeart();
      }
    }, { once: true });
  };

  return (
    <>
      <Head>
        <title>Vaivahik - AI-Powered Matrimony for Indian Families</title>
        <meta name="description" content="Discover meaningful connections with Vaivahik, where modern AI meets timeless Indian traditions to help families find the perfect match." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400&display=swap" rel="stylesheet" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
      </Head>

      <Script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" strategy="afterInteractive" />

      {/* Main Content */}
      <div className={styles.page}>
        {/* Header */}
        <header id="main-header" ref={headerRef} className={styles.mainHeader}>
          <div className={`${styles.container} ${styles.headerContainer}`}>
            <a href="#hero" className={styles.logo}>
              <i className={`fas fa-heartbeat ${styles.logoSymbol}`}></i>
              <span className={styles.logoText}>Vaiva<span>hik</span></span>
            </a>
            <nav id="main-nav" className={`${styles.mainNav} ${isMobileNavOpen ? styles.active : ''}`}>
              <ul>
                <li><a href="#hero" className={activeNavLink === '#hero' ? styles.activeLink : ''} onClick={closeMobileNav}>Home</a></li>
                <li><a href="#features" className={activeNavLink === '#features' ? styles.activeLink : ''} onClick={closeMobileNav}>Why Us</a></li>
                <li><a href="#how-it-works" className={activeNavLink === '#how-it-works' ? styles.activeLink : ''} onClick={closeMobileNav}>How It Works</a></li>
                <li><a href="#success-stories" className={activeNavLink === '#success-stories' ? styles.activeLink : ''} onClick={closeMobileNav}>Success Stories</a></li>
                <li><a href="#pricing" className={activeNavLink === '#pricing' ? styles.activeLink : ''} onClick={closeMobileNav}>Plans</a></li>
                <li><a href="#blog-preview" className={activeNavLink === '#blog-preview' ? styles.activeLink : ''} onClick={closeMobileNav}>Insights</a></li>
                <li><Link href="/register" className={`${styles.btn} ${styles.btnPrimary} ${styles.btnSmall}`} onClick={closeMobileNav}>Join Vaivahik</Link></li>
              </ul>
            </nav>
            <button className={`${styles.hamburger} ${isMobileNavOpen ? styles.active : ''}`} id="hamburger-icon" aria-label="Toggle Menu" onClick={toggleMobileNav}>
              <div className={styles.line1}></div>
              <div className={styles.line2}></div>
              <div className={styles.line3}></div>
            </button>
          </div>
        </header>

        {/* Hero Section */}
        <section id="hero" className={styles.hero}>
          <div className={styles.floatingHearts} ref={floatingHeartsRef}></div>
          <div className={styles.container}>
            <div className={styles.heroContent} data-animation="animate__fadeInUp">
              <h1>Begin Your Journey to Forever, Together.</h1>
              <p>Discover meaningful connections with Vaivahik, where modern AI meets timeless Indian traditions to help families find the perfect match.</p>
              <div className={styles.heroButtons}>
                <a href="#pricing" className={`${styles.btn} ${styles.btnPrimary}`}>Explore Membership Plans</a>
                <a href="#how-it-works" className={`${styles.btn} ${styles.btnOutline}`}>Discover Your Path to Partnership</a>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className={styles.features}>
          <div className={styles.container}>
            <div className={styles.sectionTitle}>
              <h2>The Vaivahik Advantage</h2>
              <p className={styles.subtitle}>Blending technology and tradition to create compatible, lasting relationships with trust and understanding.</p>
            </div>
            <div className={styles.featuresGrid}>
              <div className={styles.featureCard}>
                <div className={styles.featureVisualWrapper}>
                  <i className={`fas fa-brain ${styles.featureVisual}`}></i>
                </div>
                <h3>AI-Powered Compatibility</h3>
                <p>Our intelligent algorithms go beyond surface details, analyzing core values, aspirations, and lifestyle for deeply compatible matches.</p>
              </div>
              <div className={styles.featureCard}>
                <div className={styles.featureVisualWrapper}>
                  <i className={`fas fa-user-check ${styles.featureVisual}`}></i>
                </div>
                <h3>Verified & Secure Profiles</h3>
                <p>Your safety is our priority. We employ rigorous verification and robust security for a trustworthy matchmaking experience.</p>
                <div className={styles.securityPrivacyInfo}>
                  <i className="fas fa-shield-alt"></i>
                  <span>100% Secure & Private</span>
                </div>
              </div>
              <div className={styles.featureCard}>
                <div className={styles.featureVisualWrapper}>
                  <i className={`fas fa-users ${styles.featureVisual}`}></i>
                </div>
                <h3>Family-Centric Approach</h3>
                <p>We understand family's vital role, offering tools for respectful involvement and shared decision-making in your journey.</p>
              </div>
              <div className={styles.featureCard}>
                <div className={styles.featureVisualWrapper}>
                  <i className={`fas fa-user-secret ${styles.featureVisual}`}></i>
                </div>
                <h3>Granular Privacy Control</h3>
                <p>You command your visibility. Decide who sees your details and when to connect, ensuring a comfortable search.</p>
              </div>
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section id="how-it-works" className={`${styles.section} ${styles.howItWorks}`}>
          {/* Shape Divider for top of How It Works */}
          <div className={`${styles.shapeDivider} ${styles.top}`}>
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
              <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" className={styles.shapeFill}></path>
            </svg>
          </div>
          <div className={styles.sectionContentWrapper}>
            <div className={styles.container}>
              <div className={styles.sectionTitle}>
                <h2>Your Path to Partnership</h2>
                <p className={styles.subtitle}>Follow these easy steps, supported by our technology and team, to find your ideal life partner.</p>
              </div>
              <div className={styles.processTimeline}>
                <div className={styles.processStep}>
                  <div className={styles.stepVisual}>
                    <div className={styles.stepIconWrapper}>
                      <i className={`fas fa-user-plus ${styles.stepIcon}`}></i>
                    </div>
                    <div className={styles.stepNumberWrapper}>
                      <div className={styles.stepNumber}>1</div>
                    </div>
                  </div>
                  <h3>Create Your Profile</h3>
                  <p>Register and build a rich, authentic profile reflecting your personality, values, and partner preferences.</p>
                </div>
                <div className={styles.processStep}>
                  <div className={styles.stepVisual}>
                    <div className={styles.stepIconWrapper}>
                      <i className={`fas fa-robot ${styles.stepIcon}`}></i>
                    </div>
                    <div className={styles.stepNumberWrapper}>
                      <div className={styles.stepNumber}>2</div>
                    </div>
                  </div>
                  <h3>AI-Driven Discovery</h3>
                  <p>Receive highly relevant match suggestions, powered by our advanced AI's deep compatibility analysis.</p>
                </div>
                <div className={styles.processStep}>
                  <div className={styles.stepVisual}>
                    <div className={styles.stepIconWrapper}>
                      <i className={`fas fa-comments-dollar ${styles.stepIcon}`}></i>
                    </div>
                    <div className={styles.stepNumberWrapper}>
                      <div className={styles.stepNumber}>3</div>
                    </div>
                  </div>
                  <h3>Connect with Confidence</h3>
                  <p>Express interest and initiate meaningful conversations through our secure and private messaging platform.</p>
                </div>
                <div className={styles.processStep}>
                  <div className={styles.stepVisual}>
                    <div className={styles.stepIconWrapper}>
                      <i className={`fas fa-hands-helping ${styles.stepIcon}`}></i>
                    </div>
                    <div className={styles.stepNumberWrapper}>
                      <div className={styles.stepNumber}>4</div>
                    </div>
                  </div>
                  <h3>Build Your Future</h3>
                  <p>Nurture connections, involve families respectfully, and make informed decisions towards a shared future.</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section id="testimonials" className={styles.testimonials}>
          <div className={styles.shapeDivider + ' ' + styles.top}>
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
              <path d="M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.39-16.72-168.19-17.73-250.45-.39-55.85,11.57-111.91,31.79-172.44,42.59A271.42,271.42,0,0,1,122.47,53.37C58.64,53.37,0,0,0,0V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z" className={styles.shapeFill}></path>
            </svg>
          </div>
          <div className={styles.sectionContentWrapper}>
            <div className={styles.container}>
              <div className={styles.sectionTitle}>
                <h2>Real Couples, Real Connections</h2>
                <p className={styles.subtitle}>Don't just take our word for it. Hear from members who found happiness with Vaivahik.</p>
              </div>
              <Swiper
                className={styles.testimonialSlider + ' animate__animated'}
                data-animation="animate__zoomIn"
                modules={[Navigation, Pagination, Autoplay, EffectFade]}
                spaceBetween={30}
                slidesPerView={1}
                navigation={{
                  nextEl: '.swiper-button-next',
                  prevEl: '.swiper-button-prev',
                }}
                pagination={{
                  clickable: true,
                  el: '.swiper-pagination'
                }}
                loop={true}
                autoplay={{
                  delay: 6500,
                  disableOnInteraction: false,
                }}
                effect="fade"
                fadeEffect={{
                  crossFade: true
                }}
                grabCursor={true}
              >
                {testimonials.map((testimonial, index) => (
                  <SwiperSlide key={index}>
                    <div className={styles.testimonialCard}>
                      <p className={styles.testimonialText}>{testimonial.text}</p>
                      <div className={styles.testimonialAuthor}>
                        <div className={styles.authorImage} style={{ backgroundImage: `url('${testimonial.image}')` }}></div>
                        <div className={styles.authorInfo}>
                          <h4>{testimonial.author}</h4>
                          <p>{testimonial.date}</p>
                        </div>
                      </div>
                    </div>
                  </SwiperSlide>
                ))}
                <div className="swiper-pagination"></div>
                <div className="swiper-button-prev"></div>
                <div className="swiper-button-next"></div>
              </Swiper>
            </div>
          </div>
          <div className={styles.shapeDivider + ' ' + styles.bottom}>
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
              <path d="M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.39-16.72-168.19-17.73-250.45-.39-55.85,11.57-111.91,31.79-172.44,42.59A271.42,271.42,0,0,1,122.47,53.37C58.64,53.37,0,0,0,0V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z" className={styles.shapeFill}></path>
            </svg>
          </div>
        </section>

        {/* Success Stories Section */}
        <section id="success-stories" className={styles.successStories}>
          <div className={styles.sectionContentWrapper}>
            <div className={styles.container}>
              <div className={styles.sectionTitle}>
                <h2>Celebrating New Beginnings</h2>
                <p className={styles.subtitle}>Inspiring stories of love and partnership fostered through the Vaivahik platform.</p>
              </div>
              <div className={styles.successGrid}>
                {successStories.map((story, index) => (
                  <div key={index} className={`${styles.successCard} animate__animated`} data-animation="animate__fadeInUp" data-wow-delay={`${index * 0.1}s`}>
                    <div className={styles.successImage} style={{ backgroundImage: `url('${story.image}')` }}>
                      <div className={styles.successOverlay}>
                        <h3 className={styles.successNames}>{story.names}</h3>
                      </div>
                    </div>
                    <div className={styles.successContent}>
                      <div className={styles.successMeta}>
                        <span className={styles.metaItem}>
                          <i className="fas fa-calendar-check"></i> Connected: {story.connected}
                        </span>
                        <span className={styles.metaItem}>
                          <i className="fas fa-ring"></i> Married: {story.married}
                        </span>
                      </div>
                      <p className={styles.successStory}>{story.story}</p>
                      <a href="#" className={styles.readMore}>
                        Read Their Full Story <i className="fas fa-arrow-right"></i>
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          {/* Shape Divider for bottom of Success Stories */}
          <div className={`${styles.shapeDivider} ${styles.bottom}`}>
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
              <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.3-35.83,69.26-2.52,138.3,13.18,207.4,35.83s136.33,33.31,206.3,35.83c70.05,2.52,138.3-13.18,207.4-35.83s136.33-33.31,206.3-35.83L1200,0V120H0Z" className={styles.shapeFill}></path>
            </svg>
          </div>
        </section>

        {/* As Seen On Section */}
        <section id="as-seen-on" className={styles.asSeenOn}>
          {/* Top shape divider handled by Success Stories bottom shape */}
          <div className={styles.sectionContentWrapper}>
            <div className={styles.container}>
              <h3>Trusted by Families & Featured In</h3>
              <div className={styles.mediaLogos}>
                {/* TODO: Replace with actual media SVG logos */}
                <img src="https://placehold.co/150x45/cccccc/999999/svg?text=Media+1&font=montserrat" alt="Media Partner 1 Logo" />
                <img src="https://placehold.co/150x45/cccccc/999999/svg?text=Media+2&font=montserrat" alt="Media Partner 2 Logo" />
                <img src="https://placehold.co/150x45/cccccc/999999/svg?text=Media+3&font=montserrat" alt="Media Partner 3 Logo" />
                <img src="https://placehold.co/150x45/cccccc/999999/svg?text=Media+4&font=montserrat" alt="Media Partner 4 Logo" />
                <img src="https://placehold.co/150x45/cccccc/999999/svg?text=Media+5&font=montserrat" alt="Media Partner 5 Logo" />
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className={styles.pricing}>
          {/* TODO: Customize this SVG. Fill color should match BG of previous section (As Seen On). */}
          <div className={`${styles.shapeDivider} ${styles.top}`}>
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
              <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.3-35.83,69.26-2.52,138.3,13.18,207.4,35.83s136.33,33.31,206.3,35.83c70.05,2.52,138.3-13.18,207.4-35.83s136.33-33.31,206.3-35.83L1200,0V120H0Z" className={styles.shapeFill}></path>
            </svg>
          </div>
          <div className={styles.sectionContentWrapper}>
            <div className={styles.container}>
              <div className={styles.sectionTitle}>
                <h2>Invest in Your Future Happiness</h2>
                <p className={styles.subtitle}>Choose a plan that empowers your search for a compatible life partner on Vaivahik.</p>
              </div>
              <div className={styles.pricingTabs}>
                <span className={`${styles.pricingTab} ${activePricingPlan === 'monthly' ? styles.active : ''}`} data-plan="monthly" onClick={() => switchPricingTab('monthly')}>Monthly Billing</span>
                <span className={`${styles.pricingTab} ${activePricingPlan === 'yearly' ? styles.active : ''}`} data-plan="yearly" onClick={() => switchPricingTab('yearly')}>Annual Billing (Save 25%)</span>
              </div>
              <div className={styles.pricingGrid}>
                {/* Pricing cards HTML remains the same */}
                <div className={`${styles.pricingCard} animate__animated`} data-animation="animate__fadeInUp">
                  <div className={styles.pricingHeader}>
                    <h3>Discover</h3>
                    <p className={styles.planDescription}>Start exploring and see potential matches.</p>
                    <p className={styles.price}>Free</p>
                  </div>
                  <ul className={styles.pricingFeatures}>
                    <li><i className="fas fa-check"></i> Create Your Profile</li>
                    <li><i className="fas fa-check"></i> Receive AI Match Suggestions</li>
                    <li><i className="fas fa-check"></i> Browse Limited Profiles</li>
                    <li className={styles.disabled}><i className="fas fa-times"></i> Send Unlimited Interests</li>
                    <li className={styles.disabled}><i className="fas fa-times"></i> View Contact Information</li>
                    <li className={styles.disabled}><i className="fas fa-times"></i> Enhanced Privacy Controls</li>
                  </ul>
                  <Link href="/register" className={`${styles.btn} ${styles.btnOutline}`}>Register for Free</Link>
                </div>
                <div className={`${styles.pricingCard} ${styles.recommended} animate__animated`} data-animation="animate__fadeInUp" data-wow-delay="0.1s">
                  <div className={styles.popularBadge}>Most Popular</div>
                  <div className={styles.pricingHeader}>
                    <h3>Connect</h3>
                    <p className={styles.planDescription}>Ideal for actively searching and making connections.</p>
                    <p className={styles.price}>$49<span>/mo*</span></p>
                  </div>
                  <ul className={styles.pricingFeatures}>
                    <li><i className="fas fa-check"></i> All Discover Features</li>
                    <li><i className="fas fa-check"></i> Send Unlimited Interests</li>
                    <li><i className="fas fa-check"></i> Initiate Secure Chats</li>
                    <li><i className="fas fa-check"></i> View Verified Contact Details</li>
                    <li><i className="fas fa-check"></i> Enhanced Privacy Options</li>
                    <li><i className="fas fa-check"></i> Profile Performance Insights</li>
                  </ul>
                  <Link href="/register" className={`${styles.btn} ${styles.btnPrimary}`}>Choose Connect Plan</Link>
                </div>
                <div className={`${styles.pricingCard} animate__animated`} data-animation="animate__fadeInUp" data-wow-delay="0.2s">
                  <div className={styles.pricingHeader}>
                    <h3>Thrive</h3>
                    <p className={styles.planDescription}>For personalized guidance and maximum visibility.</p>
                    <p className={styles.price}>$99<span>/mo*</span></p>
                  </div>
                  <ul className={styles.pricingFeatures}>
                    <li><i className="fas fa-check"></i> All Connect Features</li>
                    <li><i className="fas fa-star"></i> Dedicated Relationship Advisor</li>
                    <li><i className="fas fa-star"></i> Profile Highlighting & Boost</li>
                    <li><i className="fas fa-star"></i> Advanced Compatibility Reports</li>
                    <li><i className="fas fa-star"></i> Priority Customer Support</li>
                    <li><i className="fas fa-star"></i> Early Access to New Features</li>
                  </ul>
                  <Link href="/register" className={`${styles.btn} ${styles.btnSecondary}`}>Choose Thrive Plan</Link>
                </div>
              </div>
              <p className={styles.pricingCardFooterNote}> *Prices shown for monthly billing. Choose Annual Billing tab for discounted rates. </p>
            </div>
          </div>
        </section>

        {/* Blog Preview Section */}
        <section id="blog-preview" className={styles.blogPreview}>
          {/* Shape Divider for top of Blog Preview */}
          <div className={`${styles.shapeDivider} ${styles.top}`}>
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
              <path d="M602.45,3.86h0S572.9,116.24,281.94,120H923C632,116.24,602.45,3.86,602.45,3.86Z" className={styles.shapeFill}></path>
            </svg>
          </div>
          <div className={styles.sectionContentWrapper}>
            <div className={styles.container}>
              <div className={styles.sectionTitle}>
                <h2>Insights & Stories</h2>
                <p className={styles.subtitle}>Explore our latest articles on relationships, matchmaking, and Vaivahik journeys.</p>
              </div>
              <div className={styles.blogGrid}>
                {blogPosts.map((post, index) => (
                  <div className={`${styles.blogCard} animate__animated`} key={index} data-animation="animate__fadeInUp" data-wow-delay={`${index * 0.1}s`}>
                    <div className={styles.blogCardImage} style={{ backgroundImage: `url('${post.image}')` }}></div>
                    <div className={styles.blogCardContent}>
                      <div className={styles.blogMeta}>
                        <span><i className="fas fa-calendar-alt"></i> {post.date}</span>
                        <span><i className="fas fa-user"></i> By {post.author}</span>
                      </div>
                      <h3><a href="#">{post.title}</a></h3>
                      <p className={styles.blogExcerpt}>{post.excerpt}</p>
                      <a href="#" className={`${styles.readMore} ${styles.blogReadMore}`}>Read More →</a>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className={styles.cta}>
          {/* Shape Divider for top of CTA */}
          <div className={`${styles.shapeDivider} ${styles.top}`}>
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
              <path d="M1200 120L0 16.48 0 0 1200 0 1200 120z" className={styles.shapeFill}></path>
            </svg>
          </div>
          <div className={styles.sectionContentWrapper}>
            <div className={styles.container}>
              <h2 className="animate__animated" data-animation="animate__pulse">Your Search for a Life Partner Starts Here.</h2>
              <p className="animate__animated" data-animation="animate__fadeInUp">Join the Vaivahik community today. Create your profile for free and discover how our unique blend of technology and tradition can help you find lasting happiness.</p>
              <Link href="/register" className={`${styles.btn} ${styles.btnSecondary} animate__animated`} data-animation="animate__swing">Create Your Free Profile Now</Link>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer id="footer" className={styles.footer}>
          {/* Shape Divider for top of Footer */}
          <div className={`${styles.shapeDivider} ${styles.top}`}>
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
              <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" className={styles.shapeFill}></path>
            </svg>
          </div>
          <div className={styles.sectionContentWrapper}>
            <div className={styles.container}>
              <div className={styles.footerContent}>
                <a href="#hero" className={`${styles.logo} ${styles.footerLogo}`}>
                  <i className={`fas fa-heartbeat ${styles.logoSymbol}`}></i>
                  <span className={styles.logoText}>Vaiva<span>hik</span></span>
                </a>
                <div className={styles.footerLinks}>
                  <ul>
                    <li><a href="/about">About Vaivahik</a></li>
                    <li><a href="/contact">Contact Us</a></li>
                    <li><Link href="/legal/privacy-policy">Privacy Policy</Link></li>
                    <li><Link href="/legal/terms-of-service">Terms of Service</Link></li>
                    <li><Link href="/legal/refund-policy">Refund Policy</Link></li>
                    <li><a href="#blog-preview">Blog</a></li>
                  </ul>
                </div>
                <div className={styles.socialLinks}>
                  <a href="#" aria-label="Facebook"><i className="fab fa-facebook-f"></i></a>
                  <a href="#" aria-label="Twitter"><i className="fab fa-twitter"></i></a>
                  <a href="#" aria-label="Instagram"><i className="fab fa-instagram"></i></a>
                  <a href="#" aria-label="LinkedIn"><i className="fab fa-linkedin-in"></i></a>
                </div>
              </div>
              <div className={styles.copyright}>
                © <span id="current-year">{new Date().getFullYear()}</span> Vaivahik Matrimony Services. All Rights Reserved. <br className="d-block d-sm-none" /> Built with <i className="fas fa-heart" style={{ color: 'var(--primary-color)' }}></i> for meaningful connections.
              </div>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}




