// API endpoint for specific user
import { generateMockUsers } from '@/utils/mockData';

export default function handler(req, res) {
  // Set proper content type header
  res.setHeader('Content-Type', 'application/json');

  try {
    // Get the user ID from the URL
    const { id } = req.query;

    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return getUserById(req, res, id);
      case 'PUT':
        return updateUser(req, res, id);
      case 'DELETE':
        return deleteUser(req, res, id);
      default:
        return res.status(405).json({
          success: false,
          message: 'Method not allowed'
        });
    }
  } catch (error) {
    console.error('Error in user API:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}

// Get user by ID
function getUserById(req, res, id) {
  // Get mock users data
  const users = generateMockUsers();

  // Find user by ID
  const user = users.find(user => user.id.toString() === id);

  // Return 404 if user not found
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  // Return user
  return res.status(200).json({
    success: true,
    user
  });
}

// Update user
function updateUser(req, res, id) {
  // Get mock users data
  const users = generateMockUsers();

  // Find user by ID
  const userIndex = users.findIndex(user => user.id.toString() === id);

  // Return 404 if user not found
  if (userIndex === -1) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  // In a real implementation, we would update the user in the database
  // For now, we'll just return a success response
  return res.status(200).json({
    success: true,
    message: 'User updated successfully',
    user: {
      ...users[userIndex],
      ...req.body
    }
  });
}

// Delete user
function deleteUser(req, res, id) {
  // Get mock users data
  const users = generateMockUsers();

  // Find user by ID
  const userIndex = users.findIndex(user => user.id.toString() === id);

  // Return 404 if user not found
  if (userIndex === -1) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  // In a real implementation, we would delete the user from the database
  // For now, we'll just return a success response
  return res.status(200).json({
    success: true,
    message: 'User deleted successfully'
  });
}
