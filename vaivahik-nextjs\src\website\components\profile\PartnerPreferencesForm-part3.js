        <Card elevation={3} sx={{ mb: 3, borderRadius: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              Location Preferences
              <Tooltip title="Specify preferred locations for your partner">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <LocationIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" fontWeight="medium">
                    Preferred Cities
                  </Typography>
                </Box>
                <FilterChips
                  label="Cities"
                  helperText="Select cities where you'd prefer your partner to be located"
                  options={CITIES}
                  selectedOptions={formData.preferredCities}
                  onChange={(newValue) => handleArrayChange('preferredCities', newValue)}
                  allowCustom={true}
                  customPlaceholder="Add another city..."
                  maxSelections={10}
                  showSelectedCount={true}
                />
              </Grid>
              
              <Grid item xs={12} sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <LocationIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" fontWeight="medium">
                    Preferred States
                  </Typography>
                </Box>
                <FilterChips
                  label="States"
                  helperText="Select states where you'd prefer your partner to be located"
                  options={STATES}
                  selectedOptions={formData.preferredStates}
                  onChange={(newValue) => handleArrayChange('preferredStates', newValue)}
                  allowCustom={true}
                  customPlaceholder="Add another state..."
                  maxSelections={5}
                  showSelectedCount={true}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
        
        <Card elevation={3} sx={{ mb: 3, borderRadius: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              Community Preferences
              <Tooltip title="Specify community preferences for your partner">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <CommunityIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" fontWeight="medium">
                    Sub-Caste Preferences
                  </Typography>
                </Box>
                <FilterChips
                  label="Acceptable Sub-Castes"
                  helperText="Select the Maratha sub-castes you're open to"
                  options={SUB_CASTES}
                  selectedOptions={formData.acceptSubCastes}
                  onChange={(newValue) => handleArrayChange('acceptSubCastes', newValue)}
                  allowCustom={false}
                  maxSelections={SUB_CASTES.length}
                  showSelectedCount={true}
                />
              </Grid>
              
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <FormLabel htmlFor="gotraPreference">Gotra Preference</FormLabel>
                  <TextField
                    id="gotraPreference"
                    name="gotraPreference"
                    value={formData.gotraPreference}
                    onChange={handleInputChange}
                    placeholder="Specify any gotra preferences"
                    size="small"
                  />
                  <FormHelperText>Leave blank if no specific preference</FormHelperText>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
        
        <Card elevation={3} sx={{ mb: 3, borderRadius: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              Lifestyle Preferences
              <Tooltip title="Specify lifestyle preferences for your partner">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <RestaurantIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" fontWeight="medium">
                    Diet Preference
                  </Typography>
                </Box>
                <FormControl fullWidth>
                  <Select
                    id="dietPreference"
                    name="dietPreference"
                    value={formData.dietPreference}
                    onChange={handleInputChange}
                    size="small"
                    displayEmpty
                  >
                    <MenuItem value="">No Preference</MenuItem>
                    <MenuItem value="ANY">Any Diet</MenuItem>
                    <MenuItem value="VEGETARIAN">Vegetarian Only</MenuItem>
                    <MenuItem value="NON_VEGETARIAN">Non-Vegetarian Acceptable</MenuItem>
                    <MenuItem value="EGGETARIAN">Eggetarian Acceptable</MenuItem>
                    <MenuItem value="VEGAN">Vegan Only</MenuItem>
                    <MenuItem value="JAIN">Jain Diet Only</MenuItem>
                  </Select>
                  <FormHelperText>Select your partner's diet preference</FormHelperText>
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <FormLabel htmlFor="hobbiesPreference">Hobbies Preference</FormLabel>
                  <TextField
                    id="hobbiesPreference"
                    name="hobbiesPreference"
                    value={formData.hobbiesPreference}
                    onChange={handleInputChange}
                    placeholder="Describe hobbies you'd like your partner to have"
                    multiline
                    rows={2}
                    size="small"
                  />
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <FormLabel htmlFor="interestsPreference">Interests Preference</FormLabel>
                  <TextField
                    id="interestsPreference"
                    name="interestsPreference"
                    value={formData.interestsPreference}
                    onChange={handleInputChange}
                    placeholder="Describe interests you'd like your partner to have"
                    multiline
                    rows={2}
                    size="small"
                  />
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
        
        <Card elevation={3} sx={{ mb: 3, borderRadius: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              Other Preferences
              <Tooltip title="Any other preferences not covered above">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <TextField
                    id="otherPreferences"
                    name="otherPreferences"
                    value={formData.otherPreferences}
                    onChange={handleInputChange}
                    placeholder="Describe any other preferences you have for your partner"
                    multiline
                    rows={4}
                    size="small"
                  />
                  <FormHelperText>
                    Include any other important preferences not covered in the sections above
                  </FormHelperText>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={handleBack}
          >
            Back to Profile
          </Button>
          
          <Button
            type="submit"
            variant="contained"
            color="primary"
            startIcon={isLoading ? null : <SaveIcon />}
            disabled={isLoading}
          >
            {isLoading ? 'Saving...' : 'Save Preferences'}
          </Button>
        </Box>
      </form>
      
      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess('')}
        message={success}
      />
    </Box>
  );
};

export default PartnerPreferencesForm;
