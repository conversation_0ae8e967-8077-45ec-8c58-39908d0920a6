/**
 * Flexible Matching Service
 * Provides configurable matching flexibility for users
 */

class FlexibleMatchingService {
  constructor() {
    this.flexibilityLevels = {
      STRICT: {
        name: 'Strict',
        description: 'Only exact matches for all preferences',
        ageFlexibility: 2,
        religionFlexibility: false,
        casteFlexibility: false,
        educationFlexibility: false,
        locationFlexibility: false
      },
      MODERATE: {
        name: 'Moderate',
        description: 'Some flexibility in preferences',
        ageFlexibility: 5,
        religionFlexibility: true,
        casteFlexibility: false,
        educationFlexibility: true,
        locationFlexibility: true
      },
      FLEXIBLE: {
        name: 'Flexible',
        description: 'Open to diverse matches',
        ageFlexibility: 10,
        religionFlexibility: true,
        casteFlexibility: true,
        educationFlexibility: true,
        locationFlexibility: true
      },
      VERY_FLEXIBLE: {
        name: 'Very Flexible',
        description: 'Focus on personality and compatibility',
        ageFlexibility: 15,
        religionFlexibility: true,
        casteFlexibility: true,
        educationFlexibility: true,
        locationFlexibility: true
      }
    };
  }

  /**
   * Apply flexibility settings to matching criteria
   */
  applyFlexibility(userPreferences, flexibilityLevel = 'MODERATE') {
    const flexibility = this.flexibilityLevels[flexibilityLevel];
    const flexiblePreferences = { ...userPreferences };

    // Age flexibility
    if (flexiblePreferences.ageMin && flexiblePreferences.ageMax) {
      const ageRange = flexiblePreferences.ageMax - flexiblePreferences.ageMin;
      const expansion = Math.min(flexibility.ageFlexibility, ageRange * 0.5);
      
      flexiblePreferences.ageMin = Math.max(18, flexiblePreferences.ageMin - expansion);
      flexiblePreferences.ageMax = Math.min(60, flexiblePreferences.ageMax + expansion);
    }

    // Height flexibility
    if (flexiblePreferences.heightMin && flexiblePreferences.heightMax) {
      const heightExpansion = flexibility.ageFlexibility * 0.5; // 0.5 feet per age year
      flexiblePreferences.heightMin = Math.max(4.0, flexiblePreferences.heightMin - heightExpansion);
      flexiblePreferences.heightMax = Math.min(7.0, flexiblePreferences.heightMax + heightExpansion);
    }

    // Religion flexibility
    if (flexibility.religionFlexibility && flexiblePreferences.religion) {
      flexiblePreferences.religionOptions = this.getCompatibleReligions(flexiblePreferences.religion);
    }

    // Caste flexibility
    if (flexibility.casteFlexibility && flexiblePreferences.caste) {
      flexiblePreferences.casteOptions = this.getCompatibleCastes(flexiblePreferences.caste);
    }

    // Education flexibility
    if (flexibility.educationFlexibility && flexiblePreferences.education) {
      flexiblePreferences.educationOptions = this.getCompatibleEducation(flexiblePreferences.education);
    }

    // Location flexibility
    if (flexibility.locationFlexibility && flexiblePreferences.location) {
      flexiblePreferences.locationOptions = this.getCompatibleLocations(flexiblePreferences.location);
    }

    return {
      ...flexiblePreferences,
      flexibilityLevel,
      appliedFlexibility: flexibility
    };
  }

  /**
   * Get compatible religions based on flexibility
   */
  getCompatibleReligions(primaryReligion) {
    const religionGroups = {
      'Hindu': ['Hindu', 'Jain', 'Buddhist', 'Sikh'],
      'Muslim': ['Muslim', 'Sufi'],
      'Christian': ['Christian', 'Catholic', 'Protestant'],
      'Sikh': ['Sikh', 'Hindu'],
      'Jain': ['Jain', 'Hindu'],
      'Buddhist': ['Buddhist', 'Hindu']
    };

    return religionGroups[primaryReligion] || [primaryReligion];
  }

  /**
   * Get compatible castes based on flexibility
   */
  getCompatibleCastes(primaryCaste) {
    const casteGroups = {
      'Maratha': ['Maratha', 'Kunbi', 'Mali', 'Dhangar'],
      'Brahmin': ['Brahmin', 'Deshastha', 'Chitpavan', 'Karhade'],
      'Kunbi': ['Kunbi', 'Maratha', 'Mali'],
      'Mali': ['Mali', 'Maratha', 'Kunbi']
    };

    return casteGroups[primaryCaste] || [primaryCaste];
  }

  /**
   * Get compatible education levels
   */
  getCompatibleEducation(primaryEducation) {
    const educationLevels = {
      'PhD': ['PhD', 'Masters', 'Post Graduate'],
      'Masters': ['Masters', 'PhD', 'Post Graduate', 'Graduate'],
      'Post Graduate': ['Post Graduate', 'Masters', 'PhD', 'Graduate'],
      'Graduate': ['Graduate', 'Post Graduate', 'Masters', 'Diploma'],
      'Diploma': ['Diploma', 'Graduate', 'Certificate'],
      'HSC': ['HSC', 'Diploma', 'Certificate']
    };

    return educationLevels[primaryEducation] || [primaryEducation];
  }

  /**
   * Get compatible locations (nearby cities/states)
   */
  getCompatibleLocations(primaryLocation) {
    const locationGroups = {
      'Mumbai': ['Mumbai', 'Pune', 'Nashik', 'Thane', 'Navi Mumbai'],
      'Pune': ['Pune', 'Mumbai', 'Nashik', 'Satara', 'Kolhapur'],
      'Nashik': ['Nashik', 'Mumbai', 'Pune', 'Aurangabad'],
      'Nagpur': ['Nagpur', 'Wardha', 'Amravati', 'Akola'],
      'Aurangabad': ['Aurangabad', 'Nashik', 'Pune', 'Jalna']
    };

    return locationGroups[primaryLocation] || [primaryLocation];
  }

  /**
   * Calculate flexibility score for a match
   */
  calculateFlexibilityScore(userPreferences, matchProfile, flexibilityLevel = 'MODERATE') {
    const flexibility = this.flexibilityLevels[flexibilityLevel];
    let score = 0;
    let maxScore = 0;

    // Age flexibility score
    if (userPreferences.ageMin && userPreferences.ageMax && matchProfile.age) {
      maxScore += 25;
      const ageInRange = matchProfile.age >= userPreferences.ageMin && matchProfile.age <= userPreferences.ageMax;
      
      if (ageInRange) {
        score += 25;
      } else {
        const ageDiff = Math.min(
          Math.abs(matchProfile.age - userPreferences.ageMin),
          Math.abs(matchProfile.age - userPreferences.ageMax)
        );
        
        if (ageDiff <= flexibility.ageFlexibility) {
          score += Math.max(0, 25 - (ageDiff * 2));
        }
      }
    }

    // Religion flexibility score
    if (userPreferences.religion && matchProfile.religion) {
      maxScore += 20;
      if (userPreferences.religion === matchProfile.religion) {
        score += 20;
      } else if (flexibility.religionFlexibility) {
        const compatibleReligions = this.getCompatibleReligions(userPreferences.religion);
        if (compatibleReligions.includes(matchProfile.religion)) {
          score += 15;
        }
      }
    }

    // Caste flexibility score
    if (userPreferences.caste && matchProfile.caste) {
      maxScore += 15;
      if (userPreferences.caste === matchProfile.caste) {
        score += 15;
      } else if (flexibility.casteFlexibility) {
        const compatibleCastes = this.getCompatibleCastes(userPreferences.caste);
        if (compatibleCastes.includes(matchProfile.caste)) {
          score += 10;
        }
      }
    }

    // Education flexibility score
    if (userPreferences.education && matchProfile.education) {
      maxScore += 15;
      if (userPreferences.education === matchProfile.education) {
        score += 15;
      } else if (flexibility.educationFlexibility) {
        const compatibleEducation = this.getCompatibleEducation(userPreferences.education);
        if (compatibleEducation.includes(matchProfile.education)) {
          score += 12;
        }
      }
    }

    // Location flexibility score
    if (userPreferences.location && matchProfile.location) {
      maxScore += 10;
      if (userPreferences.location === matchProfile.location) {
        score += 10;
      } else if (flexibility.locationFlexibility) {
        const compatibleLocations = this.getCompatibleLocations(userPreferences.location);
        if (compatibleLocations.includes(matchProfile.location)) {
          score += 8;
        }
      }
    }

    return maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;
  }

  /**
   * Get flexibility level recommendations based on user activity
   */
  getFlexibilityRecommendation(userStats) {
    const { totalMatches, interactions, successfulConnections } = userStats;

    // If user has very few matches, recommend more flexibility
    if (totalMatches < 5) {
      return 'VERY_FLEXIBLE';
    }

    // If user has low interaction rate, recommend more flexibility
    const interactionRate = interactions / totalMatches;
    if (interactionRate < 0.1) {
      return 'FLEXIBLE';
    }

    // If user has good success rate, can be more selective
    const successRate = successfulConnections / interactions;
    if (successRate > 0.3) {
      return 'STRICT';
    }

    return 'MODERATE';
  }
}

module.exports = new FlexibleMatchingService();
