import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Card,
  CardContent,
  CardActions,
  Avatar,
  Typography,
  Button,
  IconButton,
  Chip,
  Box,
  Grid,
  Tooltip,
  Badge,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Snackbar,
  Alert
} from '@mui/material';
import {
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  ThumbDown as DislikeIcon,
  Star as SuperLikeIcon,
  Visibility as ViewIcon,
  Message as MessageIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  Person as PersonIcon,
  LocationOn as LocationIcon,
  Work as WorkIcon,
  School as SchoolIcon,
  Verified as VerifiedIcon
} from '@mui/icons-material';

export default function ProfileCard({ 
  profile, 
  onLike, 
  onDislike, 
  onSuperLike, 
  onInterest, 
  onShortlist, 
  showActions = true,
  compact = false 
}) {
  const router = useRouter();
  const [liked, setLiked] = useState(false);
  const [disliked, setDisliked] = useState(false);
  const [superLiked, setSuperLiked] = useState(false);
  const [shortlisted, setShortlisted] = useState(profile?.isShortlisted || false);
  const [interestSent, setInterestSent] = useState(profile?.interestSent || false);
  const [loading, setLoading] = useState(false);
  const [interestDialog, setInterestDialog] = useState(false);
  const [interestMessage, setInterestMessage] = useState('');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  useEffect(() => {
    // Track profile view when component mounts
    if (profile?.id) {
      trackProfileView();
    }
  }, [profile?.id]);

  const trackProfileView = async () => {
    try {
      await fetch(`/api/user/profile-actions/${profile.id}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'view',
          data: {
            deviceType: /Mobile|Android|iPhone|iPad/.test(navigator.userAgent) ? 'mobile' : 'desktop',
            referrer: document.referrer || 'direct',
            sectionsViewed: ['basic_info'] // Track which sections were viewed
          }
        })
      });
    } catch (error) {
      console.error('Error tracking profile view:', error);
    }
  };

  const handleLike = async () => {
    if (loading) return;
    setLoading(true);

    try {
      const response = await fetch(`/api/user/profile-actions/${profile.id}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'like'
        })
      });

      if (response.ok) {
        const data = await response.json();
        setLiked(true);
        setDisliked(false);
        
        if (data.data?.isMatch) {
          setSnackbar({
            open: true,
            message: "It's a Match! 🎉 You both liked each other!",
            severity: 'success'
          });
        } else {
          setSnackbar({
            open: true,
            message: 'Profile liked successfully!',
            severity: 'success'
          });
        }

        if (onLike) onLike(profile.id, data.data);
      }
    } catch (error) {
      console.error('Error liking profile:', error);
      setSnackbar({
        open: true,
        message: 'Error liking profile',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDislike = async () => {
    if (loading) return;
    setLoading(true);

    try {
      const response = await fetch(`/api/user/profile-actions/${profile.id}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'dislike'
        })
      });

      if (response.ok) {
        setDisliked(true);
        setLiked(false);
        setSnackbar({
          open: true,
          message: 'Profile disliked',
          severity: 'info'
        });

        if (onDislike) onDislike(profile.id);
      }
    } catch (error) {
      console.error('Error disliking profile:', error);
      setSnackbar({
        open: true,
        message: 'Error disliking profile',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSuperLike = async () => {
    if (loading) return;
    setLoading(true);

    try {
      const response = await fetch(`/api/user/profile-actions/${profile.id}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'super_like'
        })
      });

      if (response.ok) {
        const data = await response.json();
        setSuperLiked(true);
        setLiked(false);
        setDisliked(false);
        
        setSnackbar({
          open: true,
          message: data.data?.isMatch ? "It's a Super Match! ⭐" : 'Super Like sent!',
          severity: 'success'
        });

        if (onSuperLike) onSuperLike(profile.id, data.data);
      }
    } catch (error) {
      console.error('Error super liking profile:', error);
      setSnackbar({
        open: true,
        message: 'Error sending super like',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSendInterest = async () => {
    if (loading) return;
    setLoading(true);

    try {
      const response = await fetch('/api/user/interests', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          targetUserId: profile.id,
          message: interestMessage || 'I found your profile interesting and would like to connect.'
        })
      });

      if (response.ok) {
        setInterestSent(true);
        setInterestDialog(false);
        setInterestMessage('');
        setSnackbar({
          open: true,
          message: 'Interest sent successfully!',
          severity: 'success'
        });

        if (onInterest) onInterest(profile.id);
      }
    } catch (error) {
      console.error('Error sending interest:', error);
      setSnackbar({
        open: true,
        message: 'Error sending interest',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleShortlist = async () => {
    if (loading) return;
    setLoading(true);

    try {
      const method = shortlisted ? 'DELETE' : 'POST';
      const url = shortlisted 
        ? `/api/user/shortlist/${profile.id}` 
        : '/api/user/shortlist';

      const response = await fetch(url, {
        method: method,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        ...(method === 'POST' && {
          body: JSON.stringify({ profileId: profile.id })
        })
      });

      if (response.ok) {
        setShortlisted(!shortlisted);
        setSnackbar({
          open: true,
          message: shortlisted ? 'Removed from shortlist' : 'Added to shortlist',
          severity: 'success'
        });

        if (onShortlist) onShortlist(profile.id, !shortlisted);
      }
    } catch (error) {
      console.error('Error updating shortlist:', error);
      setSnackbar({
        open: true,
        message: 'Error updating shortlist',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleViewProfile = () => {
    router.push(`/profile/${profile.id}`);
  };

  return (
    <>
      <Card 
        sx={{ 
          height: '100%', 
          display: 'flex', 
          flexDirection: 'column',
          '&:hover': { boxShadow: 6 },
          position: 'relative'
        }}
      >
        <CardContent sx={{ flexGrow: 1, pb: 1 }}>
          {/* Header with Avatar and Basic Info */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Badge
              overlap="circular"
              anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
              badgeContent={
                profile.isOnline ? (
                  <Box
                    sx={{
                      width: 12,
                      height: 12,
                      borderRadius: '50%',
                      bgcolor: 'success.main',
                      border: '2px solid white'
                    }}
                  />
                ) : null
              }
            >
              <Avatar
                src={profile.profilePicture}
                sx={{ width: compact ? 50 : 60, height: compact ? 50 : 60, mr: 2 }}
              >
                <PersonIcon />
              </Avatar>
            </Badge>
            
            <Box sx={{ flexGrow: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant={compact ? "subtitle1" : "h6"} noWrap>
                  {profile.firstName} {profile.lastName}
                </Typography>
                {profile.isVerified && (
                  <VerifiedIcon color="primary" sx={{ fontSize: 16 }} />
                )}
              </Box>
              <Typography variant="body2" color="text.secondary">
                {profile.age} years • {profile.height && `${profile.height}cm`}
              </Typography>
            </Box>

            {/* Action Icons */}
            {showActions && (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                <Tooltip title={shortlisted ? "Remove from Shortlist" : "Add to Shortlist"}>
                  <IconButton
                    size="small"
                    onClick={handleShortlist}
                    disabled={loading}
                    color={shortlisted ? "primary" : "default"}
                  >
                    {shortlisted ? <BookmarkIcon /> : <BookmarkBorderIcon />}
                  </IconButton>
                </Tooltip>
              </Box>
            )}
          </Box>

          {/* Profile Details */}
          {!compact && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                <LocationIcon sx={{ fontSize: 14, mr: 0.5, verticalAlign: 'middle' }} />
                {profile.city}, {profile.state}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                <SchoolIcon sx={{ fontSize: 14, mr: 0.5, verticalAlign: 'middle' }} />
                {profile.education}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                <WorkIcon sx={{ fontSize: 14, mr: 0.5, verticalAlign: 'middle' }} />
                {profile.occupation}
              </Typography>
            </Box>
          )}

          {/* Status Chips */}
          <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', mb: 1 }}>
            {profile.compatibility && (
              <Chip
                label={`${profile.compatibility}% Match`}
                color="primary"
                size="small"
              />
            )}
            {interestSent && (
              <Chip label="Interest Sent" color="success" size="small" />
            )}
            {liked && (
              <Chip label="Liked" color="error" size="small" icon={<FavoriteIcon />} />
            )}
            {superLiked && (
              <Chip label="Super Liked" color="warning" size="small" icon={<SuperLikeIcon />} />
            )}
          </Box>
        </CardContent>

        {/* Action Buttons */}
        {showActions && (
          <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
            <Button
              size="small"
              variant="outlined"
              startIcon={<ViewIcon />}
              onClick={handleViewProfile}
            >
              View
            </Button>

            <Box sx={{ display: 'flex', gap: 0.5 }}>
              {/* Like/Dislike Actions */}
              <Tooltip title="Dislike">
                <IconButton
                  size="small"
                  onClick={handleDislike}
                  disabled={loading}
                  color={disliked ? "error" : "default"}
                >
                  <DislikeIcon />
                </IconButton>
              </Tooltip>

              <Tooltip title="Like">
                <IconButton
                  size="small"
                  onClick={handleLike}
                  disabled={loading}
                  color={liked ? "error" : "default"}
                >
                  {liked ? <FavoriteIcon /> : <FavoriteBorderIcon />}
                </IconButton>
              </Tooltip>

              <Tooltip title="Super Like">
                <IconButton
                  size="small"
                  onClick={handleSuperLike}
                  disabled={loading}
                  color={superLiked ? "warning" : "default"}
                >
                  <SuperLikeIcon />
                </IconButton>
              </Tooltip>

              {/* Interest Button */}
              {!interestSent ? (
                <Button
                  size="small"
                  variant="contained"
                  onClick={() => setInterestDialog(true)}
                  disabled={loading}
                >
                  Interest
                </Button>
              ) : (
                <Button
                  size="small"
                  variant="outlined"
                  startIcon={<MessageIcon />}
                  onClick={() => router.push(`/messages?user=${profile.id}`)}
                >
                  Message
                </Button>
              )}
            </Box>
          </CardActions>
        )}
      </Card>

      {/* Interest Dialog */}
      <Dialog
        open={interestDialog}
        onClose={() => setInterestDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Send Interest to {profile.firstName}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Send a personal message with your interest:
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={3}
            placeholder="I found your profile interesting and would like to connect..."
            value={interestMessage}
            onChange={(e) => setInterestMessage(e.target.value)}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setInterestDialog(false)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleSendInterest}
            disabled={loading}
          >
            Send Interest
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert 
          severity={snackbar.severity} 
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
}
