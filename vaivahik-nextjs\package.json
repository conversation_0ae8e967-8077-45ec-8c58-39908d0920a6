{"name": "vaivahik-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:3002": "next dev -p 3002", "build": "next build", "start": "next start", "start:custom": "node server.js", "lint": "next lint", "validate-structure": "node scripts/validate-directory-structure.js", "add-banners": "node scripts/add-directory-banners.js"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.0", "@mui/x-data-grid": "^8.2.0", "@mui/x-date-pickers": "^8.3.1", "@prisma/client": "^6.8.2", "@sentry/nextjs": "^7.99.0", "apexcharts": "^4.7.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "chart.js": "^4.4.9", "css-loader": "^7.1.2", "date-fns": "^4.1.0", "express": "^5.1.0", "formidable": "^3.5.4", "framer-motion": "^11.0.0", "isomorphic-dompurify": "^2.9.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "multer": "^1.4.5-lts.2", "next": "^15.3.2", "next-connect": "^1.0.0", "next-pwa": "^5.6.0", "razorpay": "^2.9.6", "react": "^19.1.0", "react-apexcharts": "^1.7.0", "react-chartjs-2": "^5.3.0", "react-confetti": "^6.4.0", "react-dom": "^19.1.0", "react-intersection-observer": "^9.5.3", "react-lottie-player": "^1.5.4", "react-markdown": "^10.1.0", "react-quill": "^2.0.0", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "style-loader": "^4.0.0", "swagger-ui-react": "^5.21.0", "swiper": "^11.2.7", "uuid": "^11.1.0"}, "devDependencies": {"eslint": "^8.47.0", "eslint-config-next": "^13.4.19"}}