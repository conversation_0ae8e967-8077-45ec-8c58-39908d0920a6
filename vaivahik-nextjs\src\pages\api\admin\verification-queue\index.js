// API endpoint for verification queue
import axios from 'axios';
import { handleApiError } from '@/utils/errorHandler';
import { withAuth } from '@/utils/authHandler';

// Backend API URL - adjust this to your actual backend URL
const BACKEND_API_URL = 'http://localhost:3001/api';

async function handler(req, res) {
  // Handle different HTTP methods
  try {
    switch (req.method) {
      case 'GET':
        return await getVerificationQueue(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    return handleApiError(error, res, 'Verification queue API');
  }
}

// Export the handler with authentication middleware
// export default withAuth(handler, 'ADMIN');

// For development, we'll bypass authentication to avoid 401 errors
export default handler;

// GET /api/admin/verification-queue
async function getVerificationQueue(req, res) {
  try {
    // Get query parameters
    const { page = 1, status = 'all', search = '', limit = 10 } = req.query;

    // Construct the API URL with query parameters
    let apiUrl = `${BACKEND_API_URL}/admin/users/verification-queue?page=${page}&limit=${limit}`;

    // Add optional query parameters if they exist
    if (status && status !== 'all') apiUrl += `&status=${encodeURIComponent(status)}`;
    if (search) apiUrl += `&search=${encodeURIComponent(search)}`;

    try {
      // Fetch data from the backend API
      const response = await axios.get(apiUrl);

      // Return the response directly from the backend
      return res.status(200).json(response.data);
    } catch (apiError) {
      // Log the error
      console.error('Error fetching verification queue from backend API:', apiError.message);

      // Return a meaningful error message
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch verification queue from backend API.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available',
        note: "Please ensure the backend server is running and accessible."
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Get verification queue');
  }
}
