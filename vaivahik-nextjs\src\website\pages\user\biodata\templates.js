import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import UserLayout from '@/components/user/UserLayout';
import PaymentForm from '@/components/payment/PaymentForm';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardMedia,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  Grid,
  IconButton,
  Typography,
  Chip,
  Tooltip,
  Tabs,
  Tab,
  Alert,
  Snackbar
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  ShoppingCart as ShoppingCartIcon,
  Download as DownloadIcon,
  Lock as LockIcon,
  Male as MaleIcon,
  Female as FemaleIcon
} from '@mui/icons-material';
import axios from 'axios';
import { toast } from 'react-toastify';

export default function BiodataTemplatesPage() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [templates, setTemplates] = useState([]);
  const [purchasedTemplates, setPurchasedTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);
  const [openPreviewModal, setOpenPreviewModal] = useState(false);
  const [openPurchaseModal, setOpenPurchaseModal] = useState(false);
  const [currentTemplate, setCurrentTemplate] = useState(null);
  const [watermarkActive, setWatermarkActive] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [showPaymentForm, setShowPaymentForm] = useState(false);

  useEffect(() => {
    if (status === 'authenticated') {
      fetchTemplates();
      fetchPurchasedTemplates();
    }
  }, [status]);

  const fetchTemplates = async () => {
    try {
      const response = await axios.get('/api/user/biodata/templates');
      if (response.data.success) {
        setTemplates(response.data.templates);
      }
      setLoading(false);
    } catch (error) {
      console.error('Error fetching templates:', error);
      toast.error('Failed to fetch templates');
      setLoading(false);
    }
  };

  const fetchPurchasedTemplates = async () => {
    try {
      const response = await axios.get('/api/user/biodata/purchased');
      if (response.data.success) {
        setPurchasedTemplates(response.data.purchasedTemplates);
      }
    } catch (error) {
      console.error('Error fetching purchased templates:', error);
      toast.error('Failed to fetch your purchased templates');
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handlePreview = (template) => {
    setCurrentTemplate(template);
    setOpenPreviewModal(true);
  };

  const handlePurchase = (template) => {
    setCurrentTemplate(template);
    setOpenPurchaseModal(true);
  };

  const handleConfirmPurchase = () => {
    // Show payment form
    setShowPaymentForm(true);
  };

  const handlePaymentSuccess = (result) => {
    toast.success('Template purchased successfully!');
    fetchPurchasedTemplates();
    setOpenPurchaseModal(false);
    setShowPaymentForm(false);
    setTabValue(1); // Switch to "My Templates" tab
  };

  const handlePaymentCancel = () => {
    setShowPaymentForm(false);
  };

  const handlePaymentError = (errorMessage) => {
    toast.error(errorMessage || 'Payment failed');
    setShowPaymentForm(false);
  };

  const handleDownload = async (purchasedTemplate) => {
    try {
      const response = await axios.post('/api/user/biodata/generate', {
        templateId: purchasedTemplate.templateId
      }, {
        responseType: 'blob'
      });

      // Create a blob URL and trigger download
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `biodata_${session.user.name || 'user'}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();

      toast.success('Biodata downloaded successfully!');

      // Refresh purchased templates to update download count
      fetchPurchasedTemplates();
    } catch (error) {
      console.error('Error downloading biodata:', error);
      if (error.response?.status === 403) {
        toast.error('You need to purchase this template first');
      } else {
        toast.error('Failed to download biodata');
      }
    }
  };

  // Anti-screenshot measures for preview
  useEffect(() => {
    if (!openPreviewModal) return;

    // Add watermark when print screen is pressed
    const handleKeyDown = (e) => {
      // Check for Print Screen key (it's not consistently detectable across browsers)
      if (e.key === 'PrintScreen' || e.keyCode === 44) {
        activateWatermark();
      }

      // Check for Ctrl+P (print)
      if ((e.ctrlKey || e.metaKey) && (e.key === 'p' || e.keyCode === 80)) {
        e.preventDefault();
        setOpenSnackbar(true);
        return false;
      }

      // Check for Ctrl+Shift+I (developer tools)
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && (e.key === 'i' || e.keyCode === 73)) {
        e.preventDefault();
        setOpenSnackbar(true);
        return false;
      }
    };

    // Detect right-click
    const handleContextMenu = (e) => {
      if (openPreviewModal) {
        e.preventDefault();
        setOpenSnackbar(true);
        return false;
      }
    };

    // Detect when user switches tabs/windows
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden' && openPreviewModal) {
        activateWatermark();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('contextmenu', handleContextMenu);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('contextmenu', handleContextMenu);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [openPreviewModal]);

  const activateWatermark = () => {
    setWatermarkActive(true);
    setOpenSnackbar(true);

    // Remove watermark after a few seconds
    setTimeout(() => {
      setWatermarkActive(false);
    }, 3000);
  };

  // Filter templates by gender orientation
  const maleTemplates = templates.filter(template => template.name.toLowerCase().includes('professional') ||
                                                    template.name.toLowerCase().includes('minimalist') ||
                                                    template.name.toLowerCase().includes('heritage') ||
                                                    template.name.toLowerCase().includes('executive'));

  const femaleTemplates = templates.filter(template => template.name.toLowerCase().includes('floral') ||
                                                      template.name.toLowerCase().includes('chic') ||
                                                      template.name.toLowerCase().includes('grace') ||
                                                      template.name.toLowerCase().includes('artistic'));

  return (
    <UserLayout title="Biodata Templates">
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Biodata Templates
          </Typography>
        </Box>

        <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 3 }}>
          <Tab label="Available Templates" />
          <Tab label="My Templates" />
        </Tabs>

        {tabValue === 0 && (
          <>
            <Alert severity="info" sx={{ mb: 3 }}>
              Choose from our beautiful biodata templates to create a professional matrimonial profile.
              Purchase once and download anytime.
            </Alert>

            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                <MaleIcon sx={{ mr: 1 }} /> Male-Oriented Templates
              </Typography>

              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <Grid container spacing={{ xs: 2, sm: 3 }}>
                  {maleTemplates.map(template => (
                    <Grid item xs={6} sm={6} md={3} key={template.id}>
                      <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                        <CardMedia
                          component="img"
                          height={{ xs: 140, sm: 180 }}
                          image={template.previewImage}
                          alt={template.name}
                          sx={{ objectFit: 'cover' }}
                        />
                        <CardContent sx={{
                          flexGrow: 1,
                          p: { xs: 1.5, sm: 2 }
                        }}>
                          <Typography
                            gutterBottom
                            variant="h6"
                            component="h2"
                            sx={{
                              fontSize: { xs: '0.9rem', sm: '1.25rem' },
                              lineHeight: { xs: 1.2, sm: 1.6 }
                            }}
                          >
                            {template.name}
                          </Typography>
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{
                              mb: 2,
                              display: { xs: 'none', sm: 'block' }
                            }}
                          >
                            {template.description || 'No description provided'}
                          </Typography>

                          <Box sx={{
                            display: 'flex',
                            alignItems: 'center',
                            mb: 1,
                            flexWrap: { xs: 'wrap', sm: 'nowrap' }
                          }}>
                            {template.discountedPrice ? (
                              <>
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                  sx={{
                                    textDecoration: 'line-through',
                                    mr: 1,
                                    fontSize: { xs: '0.7rem', sm: '0.875rem' }
                                  }}
                                >
                                  ₹{template.price}
                                </Typography>
                                <Typography
                                  variant="body1"
                                  color="primary"
                                  fontWeight="bold"
                                  sx={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
                                >
                                  ₹{template.discountedPrice}
                                </Typography>
                                <Chip
                                  label={`-${template.discountPercent}%`}
                                  size="small"
                                  color="secondary"
                                  sx={{
                                    ml: 1,
                                    height: { xs: 20, sm: 24 },
                                    '& .MuiChip-label': {
                                      fontSize: { xs: '0.6rem', sm: '0.75rem' },
                                      px: { xs: 0.5, sm: 1 }
                                    }
                                  }}
                                />
                              </>
                            ) : (
                              <Typography
                                variant="body1"
                                fontWeight="bold"
                                sx={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
                              >
                                ₹{template.price}
                              </Typography>
                            )}
                          </Box>
                        </CardContent>
                        <Divider />
                        <Box sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          p: { xs: 1, sm: 1.5 },
                          flexDirection: { xs: 'column', sm: 'row' },
                          gap: { xs: 0.5, sm: 0 }
                        }}>
                          <Button
                            startIcon={<VisibilityIcon />}
                            onClick={() => handlePreview(template)}
                            size="small"
                            sx={{
                              fontSize: { xs: '0.7rem', sm: '0.8125rem' },
                              py: { xs: 0.5, sm: 1 }
                            }}
                          >
                            Preview
                          </Button>
                          <Button
                            variant="contained"
                            color="primary"
                            startIcon={<ShoppingCartIcon />}
                            onClick={() => handlePurchase(template)}
                            size="small"
                            disabled={purchasedTemplates.some(pt => pt.templateId === template.id)}
                            sx={{
                              fontSize: { xs: '0.7rem', sm: '0.8125rem' },
                              py: { xs: 0.5, sm: 1 }
                            }}
                          >
                            {purchasedTemplates.some(pt => pt.templateId === template.id) ? 'Purchased' : 'Purchase'}
                          </Button>
                        </Box>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              )}
            </Box>

            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                <FemaleIcon sx={{ mr: 1 }} /> Female-Oriented Templates
              </Typography>

              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <Grid container spacing={{ xs: 2, sm: 3 }}>
                  {femaleTemplates.map(template => (
                    <Grid item xs={6} sm={6} md={3} key={template.id}>
                      <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                        <CardMedia
                          component="img"
                          height="180"
                          image={template.previewImage}
                          alt={template.name}
                          sx={{ objectFit: 'cover' }}
                        />
                        <CardContent sx={{ flexGrow: 1 }}>
                          <Typography gutterBottom variant="h6" component="h2">
                            {template.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            {template.description || 'No description provided'}
                          </Typography>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            {template.discountedPrice ? (
                              <>
                                <Typography variant="body2" color="text.secondary" sx={{ textDecoration: 'line-through', mr: 1 }}>
                                  ₹{template.price}
                                </Typography>
                                <Typography variant="body1" color="primary" fontWeight="bold">
                                  ₹{template.discountedPrice}
                                </Typography>
                                <Chip
                                  label={`-${template.discountPercent}%`}
                                  size="small"
                                  color="secondary"
                                  sx={{ ml: 1 }}
                                />
                              </>
                            ) : (
                              <Typography variant="body1" fontWeight="bold">
                                ₹{template.price}
                              </Typography>
                            )}
                          </Box>
                        </CardContent>
                        <Divider />
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                          <Button
                            startIcon={<VisibilityIcon />}
                            onClick={() => handlePreview(template)}
                            size="small"
                          >
                            Preview
                          </Button>
                          <Button
                            variant="contained"
                            color="primary"
                            startIcon={<ShoppingCartIcon />}
                            onClick={() => handlePurchase(template)}
                            size="small"
                            disabled={purchasedTemplates.some(pt => pt.templateId === template.id)}
                          >
                            {purchasedTemplates.some(pt => pt.templateId === template.id) ? 'Purchased' : 'Purchase'}
                          </Button>
                        </Box>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              )}
            </Box>
          </>
        )}

        {tabValue === 1 && (
          <>
            {purchasedTemplates.length > 0 ? (
              <Grid container spacing={{ xs: 2, sm: 3 }}>
                {purchasedTemplates.map(purchasedTemplate => {
                  const template = templates.find(t => t.id === purchasedTemplate.templateId);
                  if (!template) return null;

                  return (
                    <Grid item xs={12} sm={6} md={4} key={purchasedTemplate.id}>
                      <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                        <CardMedia
                          component="img"
                          height="200"
                          image={template.previewImage}
                          alt={template.name}
                          sx={{ objectFit: 'cover' }}
                        />
                        <CardContent sx={{ flexGrow: 1 }}>
                          <Typography gutterBottom variant="h5" component="h2">
                            {template.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            {template.description || 'No description provided'}
                          </Typography>

                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                            <Typography variant="body2">
                              Purchased on: {new Date(purchasedTemplate.purchaseDate).toLocaleDateString()}
                            </Typography>
                            <Chip
                              label={`${purchasedTemplate.downloadCount} downloads`}
                              size="small"
                              color="primary"
                            />
                          </Box>
                        </CardContent>
                        <Divider />
                        <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                          <Button
                            variant="contained"
                            color="primary"
                            startIcon={<DownloadIcon />}
                            onClick={() => handleDownload(purchasedTemplate)}
                            fullWidth
                          >
                            Download Biodata
                          </Button>
                        </Box>
                      </Card>
                    </Grid>
                  );
                })}
              </Grid>
            ) : (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="h6" gutterBottom>
                  You haven't purchased any templates yet
                </Typography>
                <Typography variant="body1" color="text.secondary" paragraph>
                  Purchase a template to create and download your professional biodata
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => setTabValue(0)}
                >
                  Browse Templates
                </Button>
              </Box>
            )}
          </>
        )}
      </Box>

      {/* Preview Modal */}
      <Dialog
        open={openPreviewModal}
        onClose={() => setOpenPreviewModal(false)}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            height: { xs: '95vh', sm: '90vh' },
            maxWidth: { xs: '95vw', sm: '90vw' },
            margin: { xs: '10px', sm: 'auto' }
          }
        }}
      >
        <DialogTitle>
          Preview: {currentTemplate?.name}
          <IconButton
            aria-label="close"
            onClick={() => setOpenPreviewModal(false)}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
            }}
          >
            &times;
          </IconButton>
        </DialogTitle>
        <DialogContent dividers sx={{ position: 'relative', p: { xs: 1, sm: 2 } }}>
          {currentTemplate && (
            <Box sx={{ height: '100%', position: 'relative' }}>
              <iframe
                src={currentTemplate.designFile}
                style={{
                  width: '100%',
                  height: '100%',
                  border: 'none',
                  pointerEvents: 'none', // Prevent interactions with the iframe
                  overflow: 'auto',
                  WebkitOverflowScrolling: 'touch' // Smooth scrolling on iOS
                }}
                title="Template Preview"
              />

              {/* Semi-transparent overlay to prevent interactions */}
              <Box sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                background: 'transparent',
                zIndex: 1
              }} />

              {/* Watermark - responsive size */}
              <Box sx={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%) rotate(-45deg)',
                color: 'rgba(0, 0, 0, 0.1)',
                fontSize: { xs: '3rem', sm: '4rem', md: '5rem' },
                fontWeight: 'bold',
                whiteSpace: 'nowrap',
                pointerEvents: 'none',
                zIndex: 2
              }}>
                PREVIEW ONLY
              </Box>

              {/* Mobile zoom hint */}
              <Box sx={{
                position: 'absolute',
                bottom: 10,
                left: 0,
                right: 0,
                textAlign: 'center',
                color: 'rgba(0, 0, 0, 0.5)',
                fontSize: '0.75rem',
                display: { xs: 'block', sm: 'none' },
                zIndex: 3,
                backgroundColor: 'rgba(255, 255, 255, 0.7)',
                py: 0.5
              }}>
                Pinch to zoom
              </Box>

              {/* Watermark overlay (shown when screenshot attempt is detected) */}
              {watermarkActive && (
                <Box sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  zIndex: 9999,
                  flexDirection: 'column',
                  gap: { xs: 1, sm: 2 },
                  p: 2
                }}>
                  <LockIcon sx={{ fontSize: { xs: 40, sm: 60 }, color: 'error.main' }} />
                  <Typography variant="h4" color="error" align="center" sx={{ fontSize: { xs: '1.5rem', sm: '2.125rem' } }}>
                    Screenshot Detected
                  </Typography>
                  <Typography variant="body1" align="center">
                    This is a preview only. Please purchase the template to use it.
                  </Typography>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{
          flexDirection: { xs: 'column', sm: 'row' },
          alignItems: 'stretch',
          '& > button': {
            m: { xs: 0.5, sm: 0 },
            width: { xs: '100%', sm: 'auto' }
          }
        }}>
          <Button
            onClick={() => setOpenPreviewModal(false)}
            fullWidth={false}
            sx={{ order: { xs: 2, sm: 1 } }}
          >
            Close
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              setOpenPreviewModal(false);
              handlePurchase(currentTemplate);
            }}
            disabled={purchasedTemplates.some(pt => pt.templateId === currentTemplate?.id)}
            fullWidth={false}
            sx={{ order: { xs: 1, sm: 2 } }}
          >
            {purchasedTemplates.some(pt => pt.templateId === currentTemplate?.id) ? 'Already Purchased' : 'Purchase Template'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Purchase Confirmation Modal */}
      <Dialog
        open={openPurchaseModal}
        onClose={() => !showPaymentForm && setOpenPurchaseModal(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            width: { xs: '95%', sm: '100%' },
            maxHeight: { xs: '95vh', sm: '90vh' },
            margin: { xs: '10px auto', sm: 'auto' }
          }
        }}
      >
        <DialogTitle>Purchase Template</DialogTitle>
        <DialogContent>
          {!showPaymentForm ? (
            <>
              <DialogContentText sx={{ mb: 2 }}>
                You are about to purchase the "{currentTemplate?.name}" biodata template for
                <strong> ₹{currentTemplate?.discountedPrice || currentTemplate?.price}</strong>.
              </DialogContentText>

              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  This is a one-time purchase. You can download your biodata as many times as you need.
                </Typography>

                <Alert severity="info">
                  After purchase, you'll be able to generate a professional PDF biodata using this template.
                </Alert>
              </Box>
            </>
          ) : (
            <PaymentForm
              productType="BIODATA"
              productId={currentTemplate?.id}
              onSuccess={handlePaymentSuccess}
              onCancel={handlePaymentCancel}
              onError={handlePaymentError}
            />
          )}
        </DialogContent>
        {!showPaymentForm && (
          <DialogActions sx={{
            flexDirection: { xs: 'column', sm: 'row' },
            alignItems: 'stretch',
            p: { xs: 2, sm: 1.5 },
            '& > button': {
              m: { xs: 0.5, sm: 0.5 },
              width: { xs: '100%', sm: 'auto' }
            }
          }}>
            <Button
              onClick={() => setOpenPurchaseModal(false)}
              sx={{ order: { xs: 2, sm: 1 } }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirmPurchase}
              color="primary"
              variant="contained"
              sx={{ order: { xs: 1, sm: 2 } }}
            >
              Proceed to Payment
            </Button>
          </DialogActions>
        )}
      </Dialog>

      <Snackbar
        open={openSnackbar}
        autoHideDuration={6000}
        onClose={() => setOpenSnackbar(false)}
        message="Screenshots and printing are disabled for preview templates"
        action={
          <Button color="secondary" size="small" onClick={() => setOpenSnackbar(false)}>
            OK
          </Button>
        }
      />
    </UserLayout>
  );
}
