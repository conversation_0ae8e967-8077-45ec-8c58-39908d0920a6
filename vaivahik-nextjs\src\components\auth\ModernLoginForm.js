import React, { useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  CircularProgress,
  Alert,
  styled,
  InputAdornment,
  IconButton,
  Divider,
  Chip,
  Tooltip,
  Grid
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Login as LoginIcon,
  Phone as PhoneIcon,
  ContentCopy as ContentCopyIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import Link from 'next/link';
import DataSourceIndicator from '@/components/DataSourceIndicator';

// Styled components
const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  borderRadius: theme.shape.borderRadius * 2,
  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
  background: 'linear-gradient(to bottom, #ffffff, #f8f9fa)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '5px',
    background: 'var(--primary-gradient)',
  }
}));

const FloatingElement = styled(Box)(({ theme, position }) => ({
  position: 'absolute',
  width: '200px',
  height: '200px',
  borderRadius: '50%',
  background: 'linear-gradient(135deg, rgba(255, 95, 109, 0.05) 0%, rgba(255, 195, 113, 0.07) 100%)',
  zIndex: 0,
  ...(position === 'top-right' && {
    top: '-100px',
    right: '-100px',
  }),
  ...(position === 'bottom-left' && {
    bottom: '-100px',
    left: '-100px',
  }),
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: 12,
    transition: 'all 0.3s ease',
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: 'var(--primary-color)',
    },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      borderColor: 'var(--primary-color)',
      borderWidth: 2,
      boxShadow: '0 0 0 3px rgba(255, 95, 109, 0.2)',
    },
  },
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: 50,
  padding: '12px 30px',
  textTransform: 'none',
  fontWeight: 600,
  boxShadow: '0 4px 14px rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-3px)',
    boxShadow: '0 6px 20px rgba(0, 0, 0, 0.15)',
  },
  '&.MuiButton-contained': {
    background: 'var(--primary-gradient)',
  },
  '&.MuiButton-outlined': {
    borderWidth: 2,
    '&:hover': {
      borderWidth: 2,
    },
  },
}));

const TestCredentialCard = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  border: '1px solid var(--light-color-alt)',
  marginBottom: theme.spacing(2),
  transition: 'all 0.3s ease',
  background: 'var(--white)',
  '&:hover': {
    boxShadow: 'var(--shadow-soft)',
    transform: 'translateY(-2px)',
  },
}));

const ModernLoginForm = ({
  onLogin,
  loading = false,
  error = '',
  success = '',
  dataSource = 'mock'
}) => {
  const [credentials, setCredentials] = useState({
    email: '',
    password: ''
  });
  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [copiedCredential, setCopiedCredential] = useState(null);

  // Test credentials for easy login
  const testCredentials = [
    { type: 'Regular User', email: '<EMAIL>', password: 'user123' },
    { type: 'Admin', email: '<EMAIL>', password: 'admin123' },
    { type: 'Moderator', email: '<EMAIL>', password: 'moderator123' }
  ];

  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setCredentials({
      ...credentials,
      [name]: value
    });
    
    // Clear error when user types
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };

  // Toggle password visibility
  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};
    
    if (!credentials.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(credentials.email)) {
      newErrors.email = 'Email is invalid';
    }
    
    if (!credentials.password) {
      newErrors.password = 'Password is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      onLogin(credentials);
    }
  };

  // Copy test credential to clipboard
  const copyCredential = (credential) => {
    navigator.clipboard.writeText(`Email: ${credential.email} | Password: ${credential.password}`);
    setCopiedCredential(credential.type);
    
    // Reset copied state after 2 seconds
    setTimeout(() => {
      setCopiedCredential(null);
    }, 2000);
  };

  // Fill form with test credential
  const fillCredential = (credential) => {
    setCredentials({
      email: credential.email,
      password: credential.password
    });
    setErrors({});
  };

  return (
    <StyledPaper>
      <FloatingElement position="top-right" />
      <FloatingElement position="bottom-left" />
      
      <Box position="relative" zIndex={1}>
        <Typography 
          variant="h4" 
          align="center" 
          gutterBottom 
          sx={{ 
            fontFamily: 'var(--font-secondary)',
            background: 'var(--primary-gradient)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            fontWeight: 700,
            mb: 1
          }}
        >
          Welcome Back
        </Typography>
        
        <Typography variant="body1" align="center" paragraph sx={{ mb: 3, color: 'var(--text-color-medium)' }}>
          Sign in to continue to Vaivahik
        </Typography>
        
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
          <DataSourceIndicator dataSource={dataSource} />
        </Box>
        
        {error && <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>}
        {success && <Alert severity="success" sx={{ mb: 3 }}>{success}</Alert>}
        
        <form onSubmit={handleSubmit}>
          <StyledTextField
            fullWidth
            label="Email Address"
            name="email"
            type="email"
            value={credentials.email}
            onChange={handleChange}
            error={!!errors.email}
            helperText={errors.email}
            margin="normal"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <i className="fas fa-envelope" style={{ color: 'var(--text-color-medium)' }}></i>
                </InputAdornment>
              ),
            }}
          />
          
          <StyledTextField
            fullWidth
            label="Password"
            name="password"
            type={showPassword ? 'text' : 'password'}
            value={credentials.password}
            onChange={handleChange}
            error={!!errors.password}
            helperText={errors.password}
            margin="normal"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <i className="fas fa-lock" style={{ color: 'var(--text-color-medium)' }}></i>
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={handleTogglePassword}
                    edge="end"
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
          
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1, mb: 3 }}>
            <Link href="/forgot-password" passHref>
              <Typography 
                component="a" 
                variant="body2"
                sx={{ 
                  color: 'var(--primary-color)',
                  textDecoration: 'none',
                  fontWeight: 500,
                  '&:hover': {
                    textDecoration: 'underline'
                  }
                }}
              >
                Forgot Password?
              </Typography>
            </Link>
          </Box>
          
          <StyledButton
            type="submit"
            fullWidth
            variant="contained"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <LoginIcon />}
          >
            {loading ? 'Signing In...' : 'Sign In'}
          </StyledButton>
        </form>
        
        <Divider sx={{ my: 3 }}>
          <Typography variant="body2" color="text.secondary">
            OR
          </Typography>
        </Divider>
        
        <StyledButton
          fullWidth
          variant="outlined"
          startIcon={<PhoneIcon />}
          component={Link}
          href="/register"
          sx={{ mb: 3 }}
        >
          Sign Up with Phone
        </StyledButton>
        
        <Box sx={{ mt: 4 }}>
          <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, display: 'flex', alignItems: 'center' }}>
            <InfoIcon fontSize="small" sx={{ mr: 1, color: 'var(--info-color)' }} />
            Test Credentials
          </Typography>
          
          <Grid container spacing={2}>
            {testCredentials.map((credential) => (
              <Grid item xs={12} key={credential.type}>
                <TestCredentialCard>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="body2" fontWeight={500}>
                      {credential.type}
                    </Typography>
                    <Box>
                      <Tooltip title="Copy credentials">
                        <IconButton 
                          size="small" 
                          onClick={() => copyCredential(credential)}
                          color={copiedCredential === credential.type ? "success" : "default"}
                        >
                          <ContentCopyIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box>
                  <Typography variant="body2" sx={{ mb: 0.5, color: 'var(--text-color-medium)' }}>
                    Email: {credential.email}
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'var(--text-color-medium)' }}>
                    Password: {credential.password}
                  </Typography>
                  <Button 
                    size="small" 
                    onClick={() => fillCredential(credential)}
                    sx={{ 
                      mt: 1, 
                      color: 'var(--primary-color)',
                      '&:hover': {
                        background: 'rgba(255, 95, 109, 0.05)'
                      }
                    }}
                  >
                    Use these credentials
                  </Button>
                </TestCredentialCard>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Box>
    </StyledPaper>
  );
};

export default ModernLoginForm;
