/**
 * Contact Reveal Widget - Implements security system for contact information
 * Leverages existing fraud detection and premium subscription requirements
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Avatar,
  Button,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Checkbox,
  LinearProgress,
  IconButton,
  Tooltip,
  Divider,
  styled,
  CircularProgress
} from '@mui/material';

// Icons
import {
  ContactPhone as ContactIcon,
  Security as SecurityIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  WhatsApp as WhatsAppIcon,
  Shield as ShieldIcon,
  Visibility as ViewIcon,
  Block as BlockIcon,
  Report as ReportIcon,
  WorkspacePremium as PremiumIcon
} from '@mui/icons-material';

// Styled components
const SecurityCard = styled(Card)(({ theme }) => ({
  borderRadius: 16,
  boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
  border: '2px solid #4CAF50',
  background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.05), rgba(139, 195, 74, 0.05))'
}));

const ContactCard = styled(Card)(({ theme }) => ({
  borderRadius: 12,
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 12px 32px rgba(0,0,0,0.15)'
  }
}));

const RiskChip = styled(Chip)(({ risk }) => ({
  fontWeight: 600,
  background: risk === 'low' ? 'linear-gradient(135deg, #4CAF50, #8BC34A)' :
             risk === 'medium' ? 'linear-gradient(135deg, #FF9800, #FFC107)' :
             'linear-gradient(135deg, #F44336, #FF5722)',
  color: 'white'
}));

const PremiumSection = styled(Box)(({ theme }) => ({
  background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 160, 0, 0.1))',
  border: '2px dashed #FFD700',
  borderRadius: 12,
  padding: theme.spacing(3),
  textAlign: 'center'
}));

export default function ContactRevealWidget({ userId, isPremium, onPremiumFeatureClick }) {
  const [contactedUsers, setContactedUsers] = useState([]);
  const [availableContacts, setAvailableContacts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [revealDialog, setRevealDialog] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [securityCheck, setSecurityCheck] = useState({});
  const [reason, setReason] = useState('');
  const [agreeTerms, setAgreeTerms] = useState(false);

  useEffect(() => {
    fetchContactData();
  }, []);

  const fetchContactData = async () => {
    setLoading(true);
    try {
      // Simulate API call to fetch contact reveal data
      setTimeout(() => {
        setContactedUsers([
          {
            id: 1,
            name: 'Priya Sharma',
            age: 26,
            location: 'Mumbai',
            photo: '/api/placeholder/80/80',
            contactedAt: '2024-01-15',
            phone: '+91 98765 43210',
            email: '<EMAIL>',
            status: 'active',
            riskScore: 15
          },
          {
            id: 2,
            name: 'Anita Patil',
            age: 24,
            location: 'Pune',
            photo: '/api/placeholder/80/80',
            contactedAt: '2024-01-10',
            phone: '+91 87654 32109',
            email: '<EMAIL>',
            status: 'responded',
            riskScore: 8
          }
        ]);

        setAvailableContacts([
          {
            id: 3,
            name: 'Kavya Desai',
            age: 27,
            location: 'Nashik',
            photo: '/api/placeholder/80/80',
            compatibility: 87,
            riskScore: 12,
            isVerified: true,
            lastActive: '2 hours ago'
          },
          {
            id: 4,
            name: 'Sneha Joshi',
            age: 25,
            location: 'Kolhapur',
            photo: '/api/placeholder/80/80',
            compatibility: 85,
            riskScore: 25,
            isVerified: false,
            lastActive: '1 day ago'
          }
        ]);

        setLoading(false);
      }, 1500);
    } catch (error) {
      console.error('Error fetching contact data:', error);
      setLoading(false);
    }
  };

  const handleRevealContact = (user) => {
    if (!isPremium) {
      onPremiumFeatureClick('contact-reveal');
      return;
    }

    setSelectedUser(user);
    
    // Perform security check (based on your fraud detection system)
    const securityResult = performSecurityCheck(user);
    setSecurityCheck(securityResult);
    setRevealDialog(true);
  };

  const performSecurityCheck = (user) => {
    // Simulate your existing security check logic
    const riskFactors = [];
    let riskScore = user.riskScore || 0;

    if (riskScore > 80) {
      riskFactors.push('High risk profile detected');
    }
    if (riskScore > 50) {
      riskFactors.push('Moderate risk indicators present');
    }
    if (!user.isVerified) {
      riskFactors.push('Profile not verified');
      riskScore += 10;
    }
    if (user.lastActive && user.lastActive.includes('day')) {
      riskFactors.push('User not recently active');
      riskScore += 5;
    }

    return {
      riskScore,
      riskLevel: riskScore > 80 ? 'high' : riskScore > 50 ? 'medium' : 'low',
      riskFactors,
      canReveal: riskScore < 80,
      requiresApproval: riskScore > 50
    };
  };

  const confirmRevealContact = async () => {
    if (!reason.trim() || !agreeTerms) {
      return;
    }

    try {
      // Call your contact reveal API with security checks
      const revealData = {
        userId: selectedUser.id,
        reason: reason,
        securityCheck: securityCheck,
        timestamp: new Date().toISOString()
      };

      // Simulate API call
      console.log('Revealing contact with security data:', revealData);

      // Add to contacted users
      const newContact = {
        ...selectedUser,
        contactedAt: new Date().toISOString().split('T')[0],
        phone: '+91 98765 43210', // This would come from API
        email: '<EMAIL>', // This would come from API
        status: 'contacted'
      };

      setContactedUsers([newContact, ...contactedUsers]);
      setRevealDialog(false);
      setReason('');
      setAgreeTerms(false);
      setSelectedUser(null);
    } catch (error) {
      console.error('Error revealing contact:', error);
    }
  };

  const getRiskColor = (riskScore) => {
    if (riskScore < 30) return '#4CAF50';
    if (riskScore < 70) return '#FF9800';
    return '#F44336';
  };

  const renderContactedUsers = () => (
    <Box>
      <Typography variant="h6" fontWeight="600" gutterBottom>
        Contacted Users ({contactedUsers.length})
      </Typography>
      
      {contactedUsers.length === 0 ? (
        <Alert severity="info">
          No contacts revealed yet. Start connecting with your matches!
        </Alert>
      ) : (
        <Grid container spacing={2}>
          {contactedUsers.map((user) => (
            <Grid item xs={12} md={6} key={user.id}>
              <ContactCard>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar src={user.photo} sx={{ width: 50, height: 50, mr: 2 }} />
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="subtitle1" fontWeight="600">
                        {user.name}, {user.age}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {user.location}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Contacted: {user.contactedAt}
                      </Typography>
                    </Box>
                    <RiskChip
                      label={`Risk: ${user.riskScore}`}
                      size="small"
                      risk={user.riskScore < 30 ? 'low' : user.riskScore < 70 ? 'medium' : 'high'}
                    />
                  </Box>

                  <Divider sx={{ mb: 2 }} />

                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <PhoneIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                      <Typography variant="body2">{user.phone}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <EmailIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                      <Typography variant="body2">{user.email}</Typography>
                    </Box>
                  </Box>

                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      size="small"
                      startIcon={<PhoneIcon />}
                      href={`tel:${user.phone}`}
                      sx={{ flex: 1 }}
                    >
                      Call
                    </Button>
                    <Button
                      size="small"
                      startIcon={<WhatsAppIcon />}
                      href={`https://wa.me/${user.phone.replace(/\s/g, '')}`}
                      target="_blank"
                      color="success"
                      sx={{ flex: 1 }}
                    >
                      WhatsApp
                    </Button>
                  </Box>
                </CardContent>
              </ContactCard>
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );

  const renderAvailableContacts = () => (
    <Box>
      <Typography variant="h6" fontWeight="600" gutterBottom>
        Available Contacts ({availableContacts.length})
      </Typography>
      
      <Grid container spacing={2}>
        {availableContacts.map((user) => (
          <Grid item xs={12} md={6} key={user.id}>
            <ContactCard>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar src={user.photo} sx={{ width: 50, height: 50, mr: 2 }} />
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="subtitle1" fontWeight="600">
                      {user.name}, {user.age}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {user.location}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      <Chip
                        label={`${user.compatibility}% Match`}
                        size="small"
                        sx={{ mr: 1 }}
                      />
                      {user.isVerified && (
                        <Chip
                          icon={<CheckIcon />}
                          label="Verified"
                          size="small"
                          sx={{ backgroundColor: '#4CAF50', color: 'white' }}
                        />
                      )}
                    </Box>
                  </Box>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Security Score:
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={100 - user.riskScore}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      backgroundColor: 'rgba(0,0,0,0.1)',
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: getRiskColor(user.riskScore),
                        borderRadius: 4
                      }
                    }}
                  />
                  <Typography variant="caption" color="text.secondary">
                    Last active: {user.lastActive}
                  </Typography>
                </Box>

                <Button
                  fullWidth
                  variant="contained"
                  startIcon={<ContactIcon />}
                  onClick={() => handleRevealContact(user)}
                  sx={{
                    background: 'linear-gradient(135deg, #4CAF50, #8BC34A)',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #388E3C, #689F38)'
                    }
                  }}
                >
                  {isPremium ? 'Reveal Contact' : 'Reveal Contact (Premium)'}
                </Button>
              </CardContent>
            </ContactCard>
          </Grid>
        ))}
      </Grid>
    </Box>
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box>
      {/* Security Header */}
      <SecurityCard sx={{ mb: 4 }}>
        <CardContent sx={{ p: 4 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <SecurityIcon sx={{ fontSize: 40, color: '#4CAF50', mr: 2 }} />
            <Box>
              <Typography variant="h5" fontWeight="700">
                Secure Contact Reveal
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Advanced fraud detection protects against fake profiles and marriage bureaus
              </Typography>
            </Box>
          </Box>

          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <ShieldIcon sx={{ fontSize: 48, color: '#4CAF50', mb: 1 }} />
                <Typography variant="h6" fontWeight="600">
                  Fraud Detection
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  AI-powered security screening
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <CheckIcon sx={{ fontSize: 48, color: '#4CAF50', mb: 1 }} />
                <Typography variant="h6" fontWeight="600">
                  Verified Profiles
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Priority for verified users
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <ReportIcon sx={{ fontSize: 48, color: '#4CAF50', mb: 1 }} />
                <Typography variant="h6" fontWeight="600">
                  Audit Trail
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Complete contact history
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </SecurityCard>

      {/* Premium Check */}
      {!isPremium ? (
        <PremiumSection sx={{ mb: 4 }}>
          <PremiumIcon sx={{ fontSize: 48, color: '#FFD700', mb: 2 }} />
          <Typography variant="h6" fontWeight="600" gutterBottom>
            Premium Contact Reveal
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Upgrade to Premium to securely reveal contact information with advanced fraud protection
          </Typography>
          <Button
            variant="contained"
            startIcon={<PremiumIcon />}
            onClick={() => onPremiumFeatureClick('contact-reveal')}
            sx={{
              background: 'linear-gradient(135deg, #FFD700, #FFA000)',
              color: '#000',
              fontWeight: 600
            }}
          >
            Upgrade to Premium
          </Button>
        </PremiumSection>
      ) : (
        <>
          {/* Contacted Users */}
          <Box sx={{ mb: 4 }}>
            {renderContactedUsers()}
          </Box>

          <Divider sx={{ mb: 4 }} />

          {/* Available Contacts */}
          <Box>
            {renderAvailableContacts()}
          </Box>
        </>
      )}

      {/* Contact Reveal Dialog */}
      <Dialog open={revealDialog} onClose={() => setRevealDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SecurityIcon sx={{ mr: 2, color: '#4CAF50' }} />
            Security Check - Contact Reveal
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedUser && (
            <Box>
              {/* User Info */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, p: 2, backgroundColor: '#f5f5f5', borderRadius: 2 }}>
                <Avatar src={selectedUser.photo} sx={{ width: 60, height: 60, mr: 2 }} />
                <Box>
                  <Typography variant="h6" fontWeight="600">
                    {selectedUser.name}, {selectedUser.age}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedUser.location}
                  </Typography>
                </Box>
              </Box>

              {/* Security Assessment */}
              <Alert
                severity={securityCheck.riskLevel === 'low' ? 'success' : securityCheck.riskLevel === 'medium' ? 'warning' : 'error'}
                sx={{ mb: 3 }}
              >
                <Typography variant="subtitle2" fontWeight="600">
                  Security Assessment: {securityCheck.riskLevel?.toUpperCase()} RISK
                </Typography>
                <Typography variant="body2">
                  Risk Score: {securityCheck.riskScore}/100
                </Typography>
                {securityCheck.riskFactors?.length > 0 && (
                  <Box sx={{ mt: 1 }}>
                    {securityCheck.riskFactors.map((factor, index) => (
                      <Typography key={index} variant="body2">
                        • {factor}
                      </Typography>
                    ))}
                  </Box>
                )}
              </Alert>

              {securityCheck.canReveal ? (
                <Box>
                  <TextField
                    fullWidth
                    multiline
                    rows={3}
                    label="Reason for Contact Reveal"
                    placeholder="Please provide a genuine reason for revealing contact information..."
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    sx={{ mb: 2 }}
                  />
                  
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={agreeTerms}
                        onChange={(e) => setAgreeTerms(e.target.checked)}
                      />
                    }
                    label="I agree to use this contact information responsibly and not for commercial purposes"
                  />
                </Box>
              ) : (
                <Alert severity="error">
                  Contact reveal blocked due to high security risk. Please contact support if you believe this is an error.
                </Alert>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRevealDialog(false)}>
            Cancel
          </Button>
          {securityCheck.canReveal && (
            <Button
              onClick={confirmRevealContact}
              variant="contained"
              disabled={!reason.trim() || !agreeTerms}
              sx={{
                background: 'linear-gradient(135deg, #4CAF50, #8BC34A)'
              }}
            >
              Reveal Contact
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
}
