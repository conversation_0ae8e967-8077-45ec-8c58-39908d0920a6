import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Card,
  CardContent,
  Grid,
  Avatar,
  Button,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Paper,
  Divider,
  Tooltip,
  Link
} from '@mui/material';
import {
  ContactPhone as ContactIcon,
  Visibility as ViewIcon,
  Message as MessageIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  WhatsApp as WhatsAppIcon,
  Security as SecurityIcon,
  Lock as LockIcon,
  LockOpen as LockOpenIcon
} from '@mui/icons-material';
import { format } from 'date-fns';

export default function ContactsPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [contacts, setContacts] = useState({
    revealed: [],
    requested: []
  });
  const [selectedContact, setSelectedContact] = useState(null);
  const [contactDialog, setContactDialog] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    fetchContacts();
  }, []);

  const fetchContacts = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/user/contacts', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setContacts(data.data || { revealed: [], requested: [] });
      }
    } catch (error) {
      console.error('Error fetching contacts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRequestContact = async (profileId) => {
    setActionLoading(true);
    try {
      const response = await fetch('/api/user/contacts/request', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ targetUserId: profileId })
      });

      if (response.ok) {
        await fetchContacts(); // Refresh the list
      }
    } catch (error) {
      console.error('Error requesting contact:', error);
    } finally {
      setActionLoading(false);
    }
  };

  const handleViewProfile = (userId) => {
    router.push(`/profile/${userId}`);
  };

  const handleSendMessage = (userId) => {
    router.push(`/messages?user=${userId}`);
  };

  const handleCallContact = (phoneNumber) => {
    window.open(`tel:${phoneNumber}`, '_self');
  };

  const handleWhatsAppContact = (phoneNumber) => {
    window.open(`https://wa.me/${phoneNumber.replace(/[^0-9]/g, '')}`, '_blank');
  };

  const handleEmailContact = (email) => {
    window.open(`mailto:${email}`, '_self');
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'REVEALED': return 'success';
      case 'PENDING': return 'warning';
      case 'REJECTED': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'REVEALED': return 'Contact Revealed';
      case 'PENDING': return 'Request Pending';
      case 'REJECTED': return 'Request Rejected';
      default: return 'Unknown';
    }
  };

  const ContactCard = ({ contact, type }) => (
    <Card sx={{ mb: 2, '&:hover': { boxShadow: 4 } }}>
      <CardContent>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={2}>
            <Avatar
              src={contact.user?.profilePicture}
              sx={{ width: 60, height: 60, mx: 'auto' }}
            >
              <PersonIcon />
            </Avatar>
          </Grid>
          
          <Grid item xs={12} sm={4}>
            <Typography variant="h6" gutterBottom>
              {contact.user?.firstName} {contact.user?.lastName}
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {contact.user?.age} years • {contact.user?.location}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {contact.user?.occupation}
            </Typography>
          </Grid>

          <Grid item xs={12} sm={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Chip
                label={getStatusText(contact.status)}
                color={getStatusColor(contact.status)}
                size="small"
                sx={{ mb: 1 }}
              />
              <Typography variant="caption" display="block" color="text.secondary">
                <ScheduleIcon sx={{ fontSize: 12, mr: 0.5 }} />
                {format(new Date(contact.createdAt), 'MMM dd, yyyy')}
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={12} sm={3}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Button
                size="small"
                variant="outlined"
                startIcon={<ViewIcon />}
                onClick={() => handleViewProfile(contact.user?.id)}
                fullWidth
              >
                View Profile
              </Button>
              
              {contact.status === 'REVEALED' && (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    startIcon={<ContactIcon />}
                    onClick={() => {
                      setSelectedContact(contact);
                      setContactDialog(true);
                    }}
                    fullWidth
                  >
                    View Contact
                  </Button>
                  <Button
                    size="small"
                    variant="outlined"
                    startIcon={<MessageIcon />}
                    onClick={() => handleSendMessage(contact.user?.id)}
                    fullWidth
                  >
                    Message
                  </Button>
                </>
              )}
              
              {contact.status === 'PENDING' && type === 'requested' && (
                <Button
                  size="small"
                  variant="outlined"
                  disabled
                  startIcon={<ScheduleIcon />}
                  fullWidth
                >
                  Waiting for Response
                </Button>
              )}
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
        <CircularProgress />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading your contacts...
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        <ContactIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
        Contact Management
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        <SecurityIcon sx={{ mr: 1 }} />
        Your contact information is protected by our advanced security system. 
        Only verified users can request contact details.
      </Alert>
      
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          variant="fullWidth"
        >
          <Tab 
            label={`Revealed Contacts (${contacts.revealed?.length || 0})`}
            icon={<LockOpenIcon />}
          />
          <Tab 
            label={`Requested Contacts (${contacts.requested?.length || 0})`}
            icon={<LockIcon />}
          />
        </Tabs>
      </Paper>

      {/* Revealed Contacts Tab */}
      {activeTab === 0 && (
        <Box>
          {contacts.revealed?.length > 0 ? (
            contacts.revealed.map((contact) => (
              <ContactCard
                key={contact.id}
                contact={contact}
                type="revealed"
              />
            ))
          ) : (
            <Alert severity="info">
              No contact details revealed yet. Send interests and wait for acceptance to get contact details.
            </Alert>
          )}
        </Box>
      )}

      {/* Requested Contacts Tab */}
      {activeTab === 1 && (
        <Box>
          {contacts.requested?.length > 0 ? (
            contacts.requested.map((contact) => (
              <ContactCard
                key={contact.id}
                contact={contact}
                type="requested"
              />
            ))
          ) : (
            <Alert severity="info">
              You haven't requested any contact details yet. Browse profiles and request contact information from interesting matches.
            </Alert>
          )}
        </Box>
      )}

      {/* Contact Details Dialog */}
      <Dialog
        open={contactDialog}
        onClose={() => setContactDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Contact Details - {selectedContact?.user?.firstName} {selectedContact?.user?.lastName}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ py: 2 }}>
            <Alert severity="warning" sx={{ mb: 3 }}>
              <SecurityIcon sx={{ mr: 1 }} />
              Please use this contact information responsibly. Misuse may result in account suspension.
            </Alert>

            <Grid container spacing={3}>
              {selectedContact?.contactDetails?.phone && (
                <Grid item xs={12}>
                  <Paper sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <PhoneIcon sx={{ mr: 2, color: 'primary.main' }} />
                      <Box>
                        <Typography variant="subtitle2">Phone Number</Typography>
                        <Typography variant="body1">{selectedContact.contactDetails.phone}</Typography>
                      </Box>
                    </Box>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="Call">
                        <IconButton
                          color="primary"
                          onClick={() => handleCallContact(selectedContact.contactDetails.phone)}
                        >
                          <PhoneIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="WhatsApp">
                        <IconButton
                          color="success"
                          onClick={() => handleWhatsAppContact(selectedContact.contactDetails.phone)}
                        >
                          <WhatsAppIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Paper>
                </Grid>
              )}

              {selectedContact?.contactDetails?.email && (
                <Grid item xs={12}>
                  <Paper sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <EmailIcon sx={{ mr: 2, color: 'primary.main' }} />
                      <Box>
                        <Typography variant="subtitle2">Email Address</Typography>
                        <Typography variant="body1">{selectedContact.contactDetails.email}</Typography>
                      </Box>
                    </Box>
                    <Tooltip title="Send Email">
                      <IconButton
                        color="primary"
                        onClick={() => handleEmailContact(selectedContact.contactDetails.email)}
                      >
                        <EmailIcon />
                      </IconButton>
                    </Tooltip>
                  </Paper>
                </Grid>
              )}

              {selectedContact?.contactDetails?.alternatePhone && (
                <Grid item xs={12}>
                  <Paper sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <PhoneIcon sx={{ mr: 2, color: 'secondary.main' }} />
                      <Box>
                        <Typography variant="subtitle2">Alternate Phone</Typography>
                        <Typography variant="body1">{selectedContact.contactDetails.alternatePhone}</Typography>
                      </Box>
                    </Box>
                    <Tooltip title="Call">
                      <IconButton
                        color="secondary"
                        onClick={() => handleCallContact(selectedContact.contactDetails.alternatePhone)}
                      >
                        <PhoneIcon />
                      </IconButton>
                    </Tooltip>
                  </Paper>
                </Grid>
              )}
            </Grid>

            <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block' }}>
              Contact revealed on {format(new Date(selectedContact?.revealedAt || selectedContact?.createdAt), 'PPP')}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setContactDialog(false)}>
            Close
          </Button>
          <Button
            variant="contained"
            startIcon={<MessageIcon />}
            onClick={() => {
              setContactDialog(false);
              handleSendMessage(selectedContact?.user?.id);
            }}
          >
            Send Message
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
