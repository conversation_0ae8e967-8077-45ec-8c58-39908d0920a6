import { useState } from 'react';
import Head from 'next/head';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  Button,
  TextField,
  Chip
} from '@mui/material';
import InterestsDisplay from '@/website/components/profile/InterestsDisplay';
import FilterChips from '@/website/components/common/FilterChips';

// Sample user data
const SAMPLE_USERS = [
  {
    name: '<PERSON><PERSON>',
    hobbies: ['Cricket', 'Reading', 'Traveling', 'Photography', 'Music'],
    interests: 'I enjoy watching documentaries about history and science. I also like to explore new cuisines and restaurants.'
  },
  {
    name: '<PERSON><PERSON>',
    hobbies: ['Dancing', 'Yoga', 'Cooking', 'Painting', 'Meditation', 'Volunteering'],
    interests: 'I am passionate about Indian classical dance forms and teaching dance to underprivileged children.'
  },
  {
    name: '<PERSON><PERSON>',
    hobbies: ['Hiking', 'Swimming', 'Chess', 'Technology', 'Coding', 'Movies'],
    interests: 'I love exploring new hiking trails and mountains. I am also interested in latest technology trends and AI.'
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    hobbies: ['Singing', 'Gardening', 'Baking', 'Fashion', 'Shopping', 'Theatre'],
    interests: 'I enjoy singing classical music and participating in local cultural events. I also have a small kitchen garden where I grow herbs and vegetables.'
  }
];

// Hobby options for the filter chips
const HOBBY_OPTIONS = [
  // Sports & Fitness
  'Sports', 'Fitness', 'Yoga', 'Cricket', 'Swimming', 'Hiking', 'Trekking',
  
  // Arts & Creativity
  'Painting', 'Drawing', 'Photography', 'Dancing', 'Singing', 'Writing',
  
  // Entertainment
  'Movies', 'Theatre', 'Music', 'Gaming', 'Chess',
  
  // Knowledge & Learning
  'Reading', 'Learning Languages', 'Technology', 'Coding',
  
  // Lifestyle
  'Cooking', 'Baking', 'Gardening', 'Traveling', 'Shopping',
  
  // Spiritual & Wellness
  'Meditation', 'Spirituality', 'Volunteering'
];

// Interest categories for coloring
const INTEREST_CATEGORIES = {
  'Sports': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'Fitness': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'Yoga': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'Cricket': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'Swimming': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'Hiking': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  'Trekking': { bgcolor: '#e3f2fd', color: '#0d47a1' },
  
  'Painting': { bgcolor: '#f3e5f5', color: '#6a1b9a' },
  'Drawing': { bgcolor: '#f3e5f5', color: '#6a1b9a' },
  'Photography': { bgcolor: '#f3e5f5', color: '#6a1b9a' },
  'Dancing': { bgcolor: '#f3e5f5', color: '#6a1b9a' },
  'Singing': { bgcolor: '#f3e5f5', color: '#6a1b9a' },
  'Writing': { bgcolor: '#f3e5f5', color: '#6a1b9a' },
  
  'Movies': { bgcolor: '#fff3e0', color: '#e65100' },
  'Theatre': { bgcolor: '#fff3e0', color: '#e65100' },
  'Music': { bgcolor: '#fff3e0', color: '#e65100' },
  'Gaming': { bgcolor: '#fff3e0', color: '#e65100' },
  'Chess': { bgcolor: '#fff3e0', color: '#e65100' },
  
  'Reading': { bgcolor: '#e8f5e9', color: '#1b5e20' },
  'Learning': { bgcolor: '#e8f5e9', color: '#1b5e20' },
  'Technology': { bgcolor: '#e8f5e9', color: '#1b5e20' },
  'Coding': { bgcolor: '#e8f5e9', color: '#1b5e20' },
  
  'Cooking': { bgcolor: '#fbe9e7', color: '#bf360c' },
  'Baking': { bgcolor: '#fbe9e7', color: '#bf360c' },
  'Gardening': { bgcolor: '#fbe9e7', color: '#bf360c' },
  'Traveling': { bgcolor: '#fbe9e7', color: '#bf360c' },
  'Shopping': { bgcolor: '#fbe9e7', color: '#bf360c' },
  
  'Meditation': { bgcolor: '#e0f7fa', color: '#006064' },
  'Spirituality': { bgcolor: '#e0f7fa', color: '#006064' },
  'Volunteering': { bgcolor: '#e0f7fa', color: '#006064' }
};

export default function InterestsDisplayExample() {
  const [selectedHobbies, setSelectedHobbies] = useState([]);
  const [otherInterests, setOtherInterests] = useState('');
  
  return (
    <>
      <Head>
        <title>Interests Display Example | Vaivahik</title>
        <meta name="description" content="Example of interests display component" />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography variant="h4" gutterBottom>
          Interests Display Example
        </Typography>
        <Typography variant="body1" paragraph>
          This page demonstrates how to use the InterestsDisplay component to show user hobbies and interests.
        </Typography>
        
        <Divider sx={{ my: 3 }} />
        
        {/* Interactive Demo */}
        <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2 }}>
          <Typography variant="h5" gutterBottom>
            Interactive Demo
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Select Your Interests
              </Typography>
              
              <FilterChips
                label="Hobbies"
                helperText="Select hobbies that you enjoy in your free time"
                options={HOBBY_OPTIONS}
                selectedOptions={selectedHobbies}
                onChange={setSelectedHobbies}
                allowCustom={true}
                customPlaceholder="Add a hobby not in the list..."
                maxSelections={10}
                categoryColors={INTEREST_CATEGORIES}
                showSelectedCount={true}
              />
              
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Other Interests
                </Typography>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  placeholder="Describe your other interests and passions..."
                  value={otherInterests}
                  onChange={(e) => setOtherInterests(e.target.value)}
                />
              </Box>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Preview
              </Typography>
              
              <Paper elevation={1} sx={{ p: 2, borderRadius: 2, bgcolor: '#f9f9f9' }}>
                <InterestsDisplay
                  hobbies={selectedHobbies}
                  interests={otherInterests}
                  showTitle={true}
                />
              </Paper>
              
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Compact View (as shown in profile cards)
                </Typography>
                <Paper elevation={1} sx={{ p: 2, borderRadius: 2, bgcolor: '#f9f9f9' }}>
                  <InterestsDisplay
                    hobbies={selectedHobbies}
                    interests={otherInterests}
                    showTitle={false}
                    compact={true}
                  />
                </Paper>
              </Box>
            </Grid>
          </Grid>
        </Paper>
        
        {/* Sample Profiles */}
        <Typography variant="h5" gutterBottom>
          Sample Profiles
        </Typography>
        
        <Grid container spacing={3}>
          {SAMPLE_USERS.map((user, index) => (
            <Grid item xs={12} md={6} key={index}>
              <Card elevation={2} sx={{ height: '100%', borderRadius: 2 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {user.name}
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  
                  <InterestsDisplay
                    hobbies={user.hobbies}
                    interests={user.interests}
                    showTitle={true}
                  />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>
    </>
  );
}
