/**
 * Example of integrating event notifications with profile views
 * This is a sample implementation to demonstrate how to integrate notifications
 */
const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { authenticateUser } = require('../middleware/auth');
const eventNotifications = require('../services/notification/event-notifications');
const autoTriggers = require('../services/notification/auto-notification-triggers');

/**
 * @route POST /api/profiles/:id/view
 * @desc Record a profile view and send notification
 * @access Private
 */
router.post('/:id/view', authenticateUser, async (req, res) => {
  try {
    const { id: profileId } = req.params;
    const viewerId = req.user.id;

    // Don't record if user is viewing their own profile
    if (profileId === viewerId) {
      return res.status(400).json({ message: 'Cannot view your own profile' });
    }

    // Get profile owner's user ID
    const profile = await prisma.profile.findUnique({
      where: { id: profileId },
      select: { userId: true }
    });

    if (!profile) {
      return res.status(404).json({ message: 'Profile not found' });
    }

    // Record the profile view
    const profileView = await prisma.profileView.create({
      data: {
        viewerId,
        profileId,
        viewedAt: new Date()
      }
    });

    // Get viewer's information for the notification
    const viewer = await prisma.user.findUnique({
      where: { id: viewerId },
      select: {
        profile: {
          select: {
            firstName: true,
            lastName: true,
            photos: {
              where: { isMain: true },
              select: { url: true }
            }
          }
        }
      }
    });

    if (viewer && viewer.profile) {
      // Send notification to profile owner
      const viewerData = {
        viewerId,
        viewerName: `${viewer.profile.firstName} ${viewer.profile.lastName || ''}`.trim(),
        viewerPhotoUrl: viewer.profile.photos[0]?.url || null
      };

      // Use auto-trigger for comprehensive notifications
      await autoTriggers.onProfileViewed(profile.userId, viewerData);
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error recording profile view:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
