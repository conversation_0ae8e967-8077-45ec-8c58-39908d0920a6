/**
 * Admin User Management Routes
 *
 * These routes allow administrators to manage user accounts,
 * including updating locked fields that regular users cannot change.
 */

const express = require('express');
const router = express.Router();
const { authenticateAdmin, adminOverrideLockedFields } = require('../../middleware/adminAuth.middleware');
const { validateForm } = require('../../middleware/validation.middleware');
const userManagementController = require('../../controllers/admin/userManagement.controller');

// All routes in this file require admin authentication
router.use(authenticateAdmin);

// Get all users (with pagination and filtering)
router.get('/users', userManagementController.getAllUsers);

// Get a specific user by ID
router.get('/users/:userId', userManagementController.getUserById);

// Update a user's account details (email, phone, etc.)
router.put('/users/:userId/account', userManagementController.updateUserAccount);

// Update a user's basic details (including locked fields)
router.put(
  '/users/:userId/basic-details',
  adminOverrideLockedFields(),
  validateForm('basicDetails'),
  userManagementController.updateUserBasicDetails
);

// Update a user's location details (including locked fields)
router.put(
  '/users/:userId/location-details',
  adminOverrideLockedFields(),
  validateForm('locationDetails'),
  userManagementController.updateUserLocationDetails
);

// Update a user's family details
router.put(
  '/users/:userId/family-details',
  adminOverrideLockedFields(),
  validateForm('familyDetails'),
  userManagementController.updateUserFamilyDetails
);

// Update a user's education and career details
router.put(
  '/users/:userId/education-career',
  adminOverrideLockedFields(),
  validateForm('educationCareer'),
  userManagementController.updateUserEducationCareer
);

// Update a user's lifestyle and interests
router.put(
  '/users/:userId/lifestyle-interests',
  adminOverrideLockedFields(),
  userManagementController.updateUserLifestyleInterests
);

// Update a user's partner preferences
router.put(
  '/users/:userId/partner-preferences',
  adminOverrideLockedFields(),
  userManagementController.updateUserPartnerPreferences
);

// Update a user's critical fields (specifically for locked fields)
router.put(
  '/users/:userId/critical-fields',
  adminOverrideLockedFields(),
  userManagementController.updateUserCriticalFields
);

// Update a user's account status
router.put(
  '/users/:userId/status',
  userManagementController.updateUserStatus
);

// Get audit logs for all admin actions
router.get(
  '/audit-logs',
  userManagementController.getAuditLogs
);

module.exports = router;
