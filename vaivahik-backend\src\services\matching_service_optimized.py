"""
Optimized Matching Service for Matrimony App

This module provides an optimized matching service that uses Redis caching
to reduce database load and improve performance.
"""

import os
import json
import asyncio
import logging
import torch
import numpy as np
from prisma.client import PrismaClient
from datetime import datetime, timedelta

from .enhanced_tower_model_pytorch import EnhancedMatrimonyMatchingModel
from .feature_processor import FeatureProcessor
from .redis_cache import RedisCache
from .model_interpreter import ModelInterpreter
from .ab_testing import ABTestingFramework

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OptimizedMatchingService:
    """Optimized matching service for matrimony app with Redis caching"""

    def __init__(self):
        """Initialize the matching service"""
        # Initialize Prisma client
        self.prisma = PrismaClient()

        # Load settings
        self.settings = self._load_settings()

        # Initialize Redis cache
        redis_config = self.settings.get('redis', {})
        self.redis_cache = RedisCache(redis_config)

        # Initialize feature processor
        stats_path = os.path.join(os.path.dirname(__file__), '../../models/feature_stats.json')
        self.feature_processor = FeatureProcessor(stats_path)

        # Initialize A/B testing framework
        ab_config = self.settings.get('ab_testing', {})
        self.ab_testing = ABTestingFramework(ab_config, self.redis_cache)

        # Initialize models
        self.models = {}
        self.default_model_id = None

        # Initialize model interpreters
        self.interpreters = {}

        # Load models
        asyncio.create_task(self._initialize_models())

    def _load_settings(self):
        """Load matching service settings"""
        try:
            settings_path = os.path.join(os.path.dirname(__file__), '../../config/matching_settings.json')
            if os.path.exists(settings_path):
                with open(settings_path, 'r') as f:
                    return json.load(f)

            # Default settings
            return {
                'general': {
                    'matchingModel': 'TWO_TOWER',
                    'defaultModelId': 'default',
                    'minMatchScore': 50,
                    'maxMatchesPerUser': 100,
                    'cacheEnabled': True,
                    'cacheTTL': 3600  # 1 hour in seconds
                },
                'models': {
                    'default': {
                        'user_tower_layers': [256, 128],
                        'match_tower_layers': [256, 128],
                        'embedding_size': 128,
                        'dropout_rate': 0.2,
                        'similarity_metrics': ['cosine', 'euclidean', 'dot'],
                        'similarity_weights': [0.6, 0.2, 0.2]
                    }
                },
                'redis': {
                    'host': 'localhost',
                    'port': 6379,
                    'db': 0,
                    'enabled': True
                },
                'ab_testing': {
                    'enabled': True,
                    'experiments': {
                        'matching_model': {
                            'variants': {
                                'A': {
                                    'name': 'Base Model',
                                    'model_id': 'default',
                                    'traffic_allocation': 50
                                },
                                'B': {
                                    'name': 'Enhanced Model',
                                    'model_id': 'enhanced',
                                    'traffic_allocation': 50
                                }
                            }
                        }
                    }
                }
            }
        except Exception as e:
            logger.error(f"Error loading settings: {str(e)}")
            return {
                'general': {
                    'matchingModel': 'TWO_TOWER',
                    'defaultModelId': 'default',
                    'minMatchScore': 50,
                    'maxMatchesPerUser': 100,
                    'cacheEnabled': False
                },
                'models': {}
            }

    async def _initialize_models(self):
        """Initialize matching models"""
        try:
            # Get models from database
            db_models = await self.prisma.algorithm_model.find_many(
                where={
                    'type': 'TWO_TOWER',
                    'isActive': True
                }
            )

            # If no models in database, use default from settings
            if not db_models:
                model_id = 'default'
                model_config = self.settings['models'].get('default', {})
                self.models[model_id] = EnhancedMatrimonyMatchingModel(model_config)
                self.models[model_id].build_model()
                self.interpreters[model_id] = ModelInterpreter(self.models[model_id], self.feature_processor)
                self.default_model_id = model_id
                logger.info(f"Initialized default model: {model_id}")
                return

            # Initialize models from database
            for db_model in db_models:
                model_id = db_model.id
                try:
                    model_config = json.loads(db_model.config) if db_model.config else {}
                    self.models[model_id] = EnhancedMatrimonyMatchingModel(model_config)
                    self.models[model_id].build_model()
                    self.interpreters[model_id] = ModelInterpreter(self.models[model_id], self.feature_processor)

                    # Set as default if marked as default
                    if db_model.isDefault:
                        self.default_model_id = model_id

                    logger.info(f"Initialized model: {model_id}")
                except Exception as e:
                    logger.error(f"Error initializing model {model_id}: {str(e)}")

            # If no default model set, use the first one
            if not self.default_model_id and self.models:
                self.default_model_id = list(self.models.keys())[0]
                logger.info(f"Set default model to: {self.default_model_id}")

            # Compute feature statistics if needed
            await self._compute_feature_statistics()

        except Exception as e:
            logger.error(f"Error initializing models: {str(e)}")

    async def _compute_feature_statistics(self):
        """Compute feature statistics from user profiles"""
        try:
            # Check if we already have statistics
            if self.feature_processor.feature_stats:
                return

            # Get a sample of user profiles
            profiles = await self.prisma.profile.find_many(
                take=1000,  # Limit to 1000 profiles for efficiency
                include={
                    'user': True
                }
            )

            # Convert to list of dictionaries
            profile_dicts = []
            for profile in profiles:
                profile_dict = {
                    'age': profile.age,
                    'gender': profile.gender,
                    'height': profile.height,
                    'religion': profile.religion,
                    'caste': profile.caste,
                    'subCaste': profile.subCaste,
                    'gotra': profile.gotra,
                    'education': profile.education,
                    'occupation': profile.occupation,
                    'income': profile.income,
                    'city': profile.city,
                    'state': profile.state,
                    'maritalStatus': profile.maritalStatus
                }
                profile_dicts.append(profile_dict)

            # Compute statistics
            self.feature_processor.compute_feature_stats(profile_dicts)

            # Save statistics
            stats_path = os.path.join(os.path.dirname(__file__), '../../models/feature_stats.json')
            self.feature_processor.save_feature_stats(stats_path)

            # Update models with feature statistics
            for model in self.models.values():
                model.config['feature_stats'] = self.feature_processor.feature_stats

            logger.info("Computed and saved feature statistics")
        except Exception as e:
            logger.error(f"Error computing feature statistics: {str(e)}")

    async def get_matches(self, user_id, limit=10, offset=0, min_score=None, include_explanation=False):
        """
        Get matches for a user with Redis caching

        Args:
            user_id (str): User ID
            limit (int): Maximum number of matches to return
            offset (int): Offset for pagination
            min_score (int): Minimum match score
            include_explanation (bool): Whether to include match explanations

        Returns:
            list: List of matches
        """
        try:
            # Check cache if enabled
            cache_enabled = self.settings.get('general', {}).get('cacheEnabled', False)

            if cache_enabled and self.redis_cache.is_connected():
                # Create cache key based on parameters
                cache_params = {
                    'limit': limit,
                    'offset': offset,
                    'min_score': min_score,
                    'include_explanation': include_explanation
                }

                # Check cache
                cached_matches = await self.redis_cache.get_match_results(user_id, cache_params)
                if cached_matches:
                    logger.info(f"Using cached matches for user {user_id}")
                    return cached_matches

            # Get user profile and preferences
            user = await self.prisma.user.find_unique(
                where={'id': user_id},
                include={
                    'profile': True,
                    'preference': True
                }
            )

            if not user or not user.profile:
                logger.error(f"User {user_id} not found or has no profile")
                return []

            # Cache user profile
            if cache_enabled and self.redis_cache.is_connected():
                user_profile = {
                    'id': user.id,
                    'name': user.name,
                    'age': user.profile.age,
                    'gender': user.profile.gender,
                    'height': user.profile.height,
                    'religion': user.profile.religion,
                    'caste': user.profile.caste,
                    'subCaste': user.profile.subCaste,
                    'gotra': user.profile.gotra,
                    'education': user.profile.education,
                    'occupation': user.profile.occupation,
                    'income': user.profile.income,
                    'city': user.profile.city,
                    'state': user.profile.state,
                    'maritalStatus': user.profile.maritalStatus
                }
                await self.redis_cache.cache_user_profile(user_id, user_profile)

            # Get potential matches
            potential_matches = await self.prisma.user.find_many(
                where={
                    'id': {'not': user_id},
                    'profileStatus': 'ACTIVE',
                    'gender': user.preference.gender if user.preference else ('FEMALE' if user.gender == 'MALE' else 'MALE')
                },
                include={
                    'profile': True
                },
                take=100  # Get more than needed for scoring
            )

            if not potential_matches:
                logger.info(f"No potential matches found for user {user_id}")
                return []

            # Determine which model to use based on A/B testing
            variant = await self.ab_testing.assign_variant(user_id, 'matching_model')
            variant_config = self.ab_testing.get_variant_config(variant, 'matching_model')
            model_id = variant_config.get('model_id', self.default_model_id)

            # Score matches
            scored_matches = await self._score_matches(user, potential_matches, model_id, include_explanation)

            # Filter by minimum score if provided
            if min_score is not None:
                min_score_value = int(min_score)
                scored_matches = [m for m in scored_matches if m['score'] >= min_score_value]

            # Sort by score (descending)
            scored_matches.sort(key=lambda m: m['score'], reverse=True)

            # Apply pagination
            paginated_matches = scored_matches[offset:offset + limit]

            # Get detailed match data
            detailed_matches = []
            for match in paginated_matches:
                match_user = await self.prisma.user.find_unique(
                    where={'id': match['userId']},
                    include={
                        'profile': True,
                        'photos': {
                            'where': {'status': 'APPROVED'},
                            'take': 1
                        }
                    }
                )

                if match_user:
                    detailed_match = {
                        'userId': match_user.id,
                        'name': match_user.name,
                        'age': match_user.profile.age if match_user.profile else None,
                        'city': match_user.profile.city if match_user.profile else None,
                        'occupation': match_user.profile.occupation if match_user.profile else None,
                        'photo': match_user.photos[0].url if match_user.photos else None,
                        'score': match['score'],
                        'analysis': match.get('analysis', {})
                    }
                    detailed_matches.append(detailed_match)

            # Cache results if enabled
            if cache_enabled and self.redis_cache.is_connected():
                cache_ttl = self.settings.get('general', {}).get('cacheTTL', 3600)
                await self.redis_cache.cache_match_results(user_id, detailed_matches, cache_params)

            # Record A/B testing event
            await self.ab_testing.record_event(user_id, 'matches_viewed', len(detailed_matches), 'matching_model')

            return detailed_matches
        except Exception as e:
            logger.error(f"Error getting matches: {str(e)}")
            return []

    async def _score_matches(self, user, potential_matches, model_id=None, include_explanation=False):
        """
        Score potential matches for a user

        Args:
            user (dict): User profile and preferences
            potential_matches (list): List of potential matches
            model_id (str): Model ID to use
            include_explanation (bool): Whether to include match explanations

        Returns:
            list: List of matches with scores
        """
        # Use default model if not specified
        if model_id is None or model_id not in self.models:
            model_id = self.default_model_id

        # Get model
        model = self.models.get(model_id)
        if not model:
            logger.error(f"Model {model_id} not found")
            return []

        # Get interpreter
        interpreter = self.interpreters.get(model_id)

        # Convert user profile and preferences to dictionaries
        user_profile = {
            'id': user.id,
            'name': user.name,
            'age': user.profile.age,
            'gender': user.profile.gender,
            'height': user.profile.height,
            'religion': user.profile.religion,
            'caste': user.profile.caste,
            'subCaste': user.profile.subCaste,
            'gotra': user.profile.gotra,
            'education': user.profile.education,
            'occupation': user.profile.occupation,
            'income': user.profile.income,
            'city': user.profile.city,
            'state': user.profile.state,
            'maritalStatus': user.profile.maritalStatus
        }

        user_preferences = {}
        if user.preference:
            user_preferences = {
                'minAge': user.preference.minAge,
                'maxAge': user.preference.maxAge,
                'minHeight': user.preference.minHeight,
                'maxHeight': user.preference.maxHeight,
                'religion': user.preference.religion,
                'caste': user.preference.caste,
                'subCaste': user.preference.subCaste,
                'education': user.preference.education,
                'occupation': user.preference.occupation,
                'minIncome': user.preference.minIncome,
                'city': user.preference.city,
                'state': user.preference.state,
                'maritalStatus': user.preference.maritalStatus
            }

        # Process matches
        match_profiles = []
        for match in potential_matches:
            if match.profile:
                match_profile = {
                    'id': match.id,
                    'name': match.name,
                    'age': match.profile.age,
                    'gender': match.profile.gender,
                    'height': match.profile.height,
                    'religion': match.profile.religion,
                    'caste': match.profile.caste,
                    'subCaste': match.profile.subCaste,
                    'gotra': match.profile.gotra,
                    'education': match.profile.education,
                    'occupation': match.profile.occupation,
                    'income': match.profile.income,
                    'city': match.profile.city,
                    'state': match.profile.state,
                    'maritalStatus': match.profile.maritalStatus
                }
                match_profiles.append(match_profile)

        # Process features
        user_features_list = []
        match_features_list = []

        for match_profile in match_profiles:
            user_features, match_features = self.feature_processor.process_match_pair(
                user_profile, user_preferences, match_profile
            )
            user_features_list.append(user_features)
            match_features_list.append(match_features)

        # Convert to tensors
        user_features_tensor = self._features_to_tensor(user_features_list)
        match_features_tensor = self._features_to_tensor(match_features_list)

        # Get predictions
        with torch.no_grad():
            model.model.eval()
            scores = model.model(user_features_tensor, match_features_tensor)
            scores = scores.cpu().numpy().flatten()

        # Create match results
        results = []
        for i, score in enumerate(scores):
            # Convert score to percentage (0-100)
            percentage_score = int(round(score * 100))

            # Generate match analysis if requested
            analysis = {}
            if include_explanation and interpreter:
                analysis = interpreter.explain_match(
                    user_profile, user_preferences, match_profiles[i], score
                )

            results.append({
                'userId': potential_matches[i].id,
                'score': percentage_score,
                'analysis': analysis
            })

        return results

    def _features_to_tensor(self, features_list):
        """
        Convert feature dictionaries to tensor

        Args:
            features_list (list): List of feature dictionaries

        Returns:
            torch.Tensor: Feature tensor
        """
        # Get all feature keys
        all_keys = set()
        for features in features_list:
            all_keys.update(features.keys())

        # Sort keys for consistent ordering
        sorted_keys = sorted(all_keys)

        # Create tensor
        tensor_data = []
        for features in features_list:
            feature_vector = [features.get(key, 0.0) for key in sorted_keys]
            tensor_data.append(feature_vector)

        return torch.tensor(tensor_data, dtype=torch.float32)

    async def get_match_explanation(self, user_id, match_id):
        """
        Get explanation for a match

        Args:
            user_id (str): User ID
            match_id (str): Match ID

        Returns:
            dict: Match explanation
        """
        try:
            # Get user and match profiles
            user = await self.prisma.user.find_unique(
                where={'id': user_id},
                include={
                    'profile': True,
                    'preference': True
                }
            )

            match_user = await self.prisma.user.find_unique(
                where={'id': match_id},
                include={
                    'profile': True
                }
            )

            if not user or not user.profile or not match_user or not match_user.profile:
                logger.error(f"User {user_id} or match {match_id} not found or has no profile")
                return {}

            # Determine which model to use based on A/B testing
            variant = await self.ab_testing.assign_variant(user_id, 'matching_model')
            variant_config = self.ab_testing.get_variant_config(variant, 'matching_model')
            model_id = variant_config.get('model_id', self.default_model_id)

            # Get interpreter
            interpreter = self.interpreters.get(model_id)
            if not interpreter:
                logger.error(f"Interpreter for model {model_id} not found")
                return {}

            # Convert profiles to dictionaries
            user_profile = {
                'id': user.id,
                'name': user.name,
                'age': user.profile.age,
                'gender': user.profile.gender,
                'height': user.profile.height,
                'religion': user.profile.religion,
                'caste': user.profile.caste,
                'subCaste': user.profile.subCaste,
                'gotra': user.profile.gotra,
                'education': user.profile.education,
                'occupation': user.profile.occupation,
                'income': user.profile.income,
                'city': user.profile.city,
                'state': user.profile.state,
                'maritalStatus': user.profile.maritalStatus
            }

            user_preferences = {}
            if user.preference:
                user_preferences = {
                    'minAge': user.preference.minAge,
                    'maxAge': user.preference.maxAge,
                    'minHeight': user.preference.minHeight,
                    'maxHeight': user.preference.maxHeight,
                    'religion': user.preference.religion,
                    'caste': user.preference.caste,
                    'subCaste': user.preference.subCaste,
                    'education': user.preference.education,
                    'occupation': user.preference.occupation,
                    'minIncome': user.preference.minIncome,
                    'city': user.preference.city,
                    'state': user.preference.state,
                    'maritalStatus': user.preference.maritalStatus
                }

            match_profile = {
                'id': match_user.id,
                'name': match_user.name,
                'age': match_user.profile.age,
                'gender': match_user.profile.gender,
                'height': match_user.profile.height,
                'religion': match_user.profile.religion,
                'caste': match_user.profile.caste,
                'subCaste': match_user.profile.subCaste,
                'gotra': match_user.profile.gotra,
                'education': match_user.profile.education,
                'occupation': match_user.profile.occupation,
                'income': match_user.profile.income,
                'city': match_user.profile.city,
                'state': match_user.profile.state,
                'maritalStatus': match_user.profile.maritalStatus
            }

            # Generate explanation
            explanation = interpreter.explain_match(
                user_profile, user_preferences, match_profile
            )

            # Record A/B testing event
            await self.ab_testing.record_event(user_id, 'explanation_viewed', 1, 'matching_model')

            return explanation
        except Exception as e:
            logger.error(f"Error getting match explanation: {str(e)}")
            return {}
