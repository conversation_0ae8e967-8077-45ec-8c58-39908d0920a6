/**
 * WebSocket Server for Real-Time Notifications
 * 
 * This file sets up a WebSocket server that connects to Redis PubSub
 * to deliver real-time notifications to connected clients.
 */
const WebSocket = require('ws');
const http = require('http');
const jwt = require('jsonwebtoken');
const { redisPubSub } = require('./redis-config');
const redisService = require('./redis-notification-service');
require('dotenv').config();

// Map to store connected clients
const clients = new Map();

/**
 * Initialize WebSocket server
 * 
 * @param {http.Server} server - HTTP server to attach WebSocket server to
 */
const initWebSocketServer = (server) => {
  const wss = new WebSocket.Server({ server });
  
  wss.on('connection', async (ws, req) => {
    try {
      // Extract token from query parameters
      const url = new URL(req.url, `http://${req.headers.host}`);
      const token = url.searchParams.get('token');
      
      if (!token) {
        ws.close(4001, 'Authentication token required');
        return;
      }
      
      // Verify token
      let decoded;
      try {
        decoded = jwt.verify(token, process.env.JWT_SECRET);
      } catch (error) {
        ws.close(4002, 'Invalid authentication token');
        return;
      }
      
      const userId = decoded.id;
      
      // Store client connection
      if (!clients.has(userId)) {
        clients.set(userId, new Set());
      }
      clients.get(userId).add(ws);
      
      console.log(`WebSocket client connected: User ${userId}`);
      
      // Subscribe to user's notification channel
      const userChannel = `user:${userId}`;
      await redisService.subscribeToChannel(userChannel, (notification) => {
        sendToUser(userId, notification);
      });
      
      // Handle client messages
      ws.on('message', async (message) => {
        try {
          const data = JSON.parse(message);
          
          // Handle different message types
          switch (data.type) {
            case 'MARK_READ':
              if (data.notificationId) {
                await redisService.markNotificationAsRead(data.notificationId, userId);
                // Acknowledge the read status
                ws.send(JSON.stringify({
                  type: 'NOTIFICATION_READ',
                  notificationId: data.notificationId
                }));
              }
              break;
              
            case 'MARK_ALL_READ':
              await redisService.markAllNotificationsAsRead(userId);
              // Acknowledge all read
              ws.send(JSON.stringify({
                type: 'ALL_NOTIFICATIONS_READ'
              }));
              break;
              
            case 'GET_NOTIFICATIONS':
              const notifications = await redisService.getUserNotifications(userId, {
                limit: data.limit || 20,
                offset: data.offset || 0,
                unreadOnly: data.unreadOnly || false
              });
              
              ws.send(JSON.stringify({
                type: 'NOTIFICATIONS',
                notifications
              }));
              break;
              
            case 'PING':
              ws.send(JSON.stringify({ type: 'PONG' }));
              break;
              
            default:
              console.log(`Unknown message type: ${data.type}`);
          }
        } catch (error) {
          console.error('Error handling WebSocket message:', error);
          ws.send(JSON.stringify({
            type: 'ERROR',
            message: 'Error processing your request'
          }));
        }
      });
      
      // Handle client disconnect
      ws.on('close', async () => {
        console.log(`WebSocket client disconnected: User ${userId}`);
        
        // Remove client from map
        if (clients.has(userId)) {
          clients.get(userId).delete(ws);
          
          // If no more connections for this user, unsubscribe from channel
          if (clients.get(userId).size === 0) {
            clients.delete(userId);
            await redisService.unsubscribeFromChannel(`user:${userId}`);
          }
        }
      });
      
      // Send initial unread count
      const unreadCount = await redisService.getUnreadCount(userId);
      ws.send(JSON.stringify({
        type: 'UNREAD_COUNT',
        count: unreadCount
      }));
      
    } catch (error) {
      console.error('Error handling WebSocket connection:', error);
      ws.close(4000, 'Internal server error');
    }
  });
  
  console.log('WebSocket server initialized');
  
  return wss;
};

/**
 * Send a notification to a specific user
 * 
 * @param {string} userId - ID of the user to send the notification to
 * @param {Object} notification - Notification to send
 */
const sendToUser = (userId, notification) => {
  if (clients.has(userId)) {
    const userClients = clients.get(userId);
    
    userClients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify({
          type: 'NOTIFICATION',
          notification
        }));
      }
    });
  }
};

/**
 * Broadcast a notification to all connected clients
 * 
 * @param {Object} notification - Notification to broadcast
 */
const broadcastNotification = (notification) => {
  clients.forEach((userClients, userId) => {
    userClients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify({
          type: 'BROADCAST',
          notification
        }));
      }
    });
  });
};

module.exports = {
  initWebSocketServer,
  sendToUser,
  broadcastNotification
};
