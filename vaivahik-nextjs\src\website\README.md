# Vaivahik Website Module

This directory contains website-specific functionality for the Vaivahik matrimony application. The code is organized by feature rather than by technical function to improve maintainability and make future enhancements easier.

## Directory Structure

```
website/
├── components/            # Shared website components
│   ├── common/            # Common components used across features
│   └── profile/           # Profile-specific components
├── gamification/          # Gamification functionality
│   ├── components/        # Gamification-specific components
│   └── index.js           # Main exports
├── home/                  # Home page features
├── matching/              # Matching algorithm features
├── messaging/             # Messaging features
├── pages/                 # Website pages
│   ├── examples/          # Example pages
│   └── profile/           # Profile pages
│       └── edit/          # Profile editing pages
├── payments/              # Payment functionality
├── profile/               # Profile viewing/editing
├── search/                # Search functionality
│   ├── components/        # Search-specific components
│   └── index.js           # Main exports
├── shortlist/             # Shortlist functionality
│   ├── components/        # Shortlist-specific components
│   └── index.js           # Main exports
└── utils/                 # Website-specific utilities
```

## Approach

We've taken a gradual approach to reorganizing the codebase:

1. **Move, Don't Change**: Files are moved to the new structure without changing their functionality or logic
2. **Backward Compatibility**: The new structure maintains compatibility with existing code
3. **Gradual Migration**: Components are migrated one by one to avoid disrupting the application

## Features

### Search Module

The search module now includes:

- **Components**:
  - `FilterChips`: Display active search filters as removable chips
  - `SearchBar`: Main search interface with filters
  - `HeightRangeSelector`: Component for selecting height range

- **Utilities**:
  - Height utility functions for formatting and converting heights
  - Mock data generator for testing and development

- **API Integration**:
  - Functions for interacting with the search API
  - Support for both real and mock data

- **Hooks**:
  - `useSearch`: Custom hook for managing search state and operations

These components maintain the same functionality as their original versions but are now organized in a more maintainable structure.

### Profile Module

The profile module includes:

- **Privacy Control Structure**: Complete privacy settings management
  - `PrivacySettings`: UI component for managing privacy settings
  - `PrivacyContext`: Context provider for privacy settings
  - `privacyService`: Service for interacting with privacy API
  - Privacy-related constants and types

- **Profile Components**:
  - `InterestsDisplay`: Component for displaying user's hobbies and interests
  - `LifestyleForm`: Form for editing lifestyle preferences
  - `PartnerPreferencesForm`: Form for editing partner preferences

### Gamification Module

The gamification module includes:

- **Components**:
  - `ProfileGamificationSystem`: Comprehensive gamification system for user profiles

- **Features**:
  - Profile completion tracking
  - Badge system
  - Feature unlocking based on profile completion
  - Confetti animations for achievements

### Shortlist Module

The shortlist module includes:

- **Components**:
  - `ShortlistedProfiles`: Advanced UI for displaying and managing shortlisted profiles

- **Features**:
  - Shortlist management
  - Profile notes
  - Sorting and filtering
  - Responsive design

## Usage

### Search Module

You can import components from the new structure while maintaining backward compatibility:

```jsx
// Import from the new structure
import { FilterChips, SearchBar } from '@/website/search';

// Or continue using the original imports
import FilterChips from '@/components/search/FilterChips';
import PremiumSearchBar from '@/components/search/PremiumSearchBar';

function SearchPage() {
  // Your component logic here

  return (
    <div>
      {/* Using components from the new structure */}
      <SearchBar onSearch={handleSearch} />

      <FilterChips
        searchParams={searchParams}
        onRemoveFilter={handleRemoveFilter}
        onClearAllFilters={handleClearAllFilters}
      />
    </div>
  );
}
```

See the example page at `src/pages/search-example.js` for a complete example of using the new structure.

### Profile Module

You can use the privacy control structure from the new website folder:

```jsx
// Import from the new structure
import {
  PrivacySettings,
  PrivacyProvider,
  usePrivacy,
  PRIVACY_OPTIONS
} from '@/website/profile';

// Use the privacy context provider
function App() {
  return (
    <PrivacyProvider>
      <YourApp />
    </PrivacyProvider>
  );
}

// Use the privacy settings component
function SettingsPage() {
  const { savePrivacySettings } = usePrivacy();

  const handleSave = async (settings) => {
    return await savePrivacySettings(settings);
  };

  return (
    <PrivacySettings
      user={currentUser}
      onSave={handleSave}
    />
  );
}
```

See the example page at `src/pages/privacy-example.js` for a complete example of using the privacy settings component.

### Gamification Module

You can use the gamification components from the new website folder:

```jsx
// Import from the new structure
import { ProfileGamificationSystem } from '@/website/gamification';

function ProfilePage() {
  // Your component logic here
  const userData = {
    profileCompletionPercentage: 75,
    photoCount: 3,
    preferencesCompletion: 80,
    familyCompletion: 60
  };

  return (
    <div>
      <ProfileGamificationSystem
        userData={userData}
        profileCompletion={userData.profileCompletionPercentage}
        photoCount={userData.photoCount}
        preferencesCompletion={userData.preferencesCompletion}
        familyCompletion={userData.familyCompletion}
      />
    </div>
  );
}
```

### Shortlist Module

You can use the shortlist components from the new website folder:

```jsx
// Import from the new structure
import { ShortlistedProfiles } from '@/website/shortlist';

function ShortlistPage() {
  return (
    <div>
      <ShortlistedProfiles />
    </div>
  );
}
```

See the example page at `src/website/pages/examples/interests-display.js` for a complete example of using the interests display component.

## Migration Plan

Our plan for migrating the entire codebase to the new structure:

1. **Phase 1: Initial Structure Setup** ✅
   - Create the website folder structure
   - Move initial components without changing functionality
   - Create example page to demonstrate usage

2. **Phase 2: Component Migration** ✅
   - Move profile components
     - InterestsDisplay ✅
     - LifestyleForm ✅
     - PartnerPreferencesForm ✅
   - Move gamification components
     - ProfileGamificationSystem ✅
   - Move shortlist components
     - ShortlistedProfiles ✅

3. **Phase 3: Continue Component Migration**
   - Move remaining search components
   - Move notification components
   - Move matching components
   - Move messaging components
   - Move payment components

4. **Phase 4: Page Migration**
   - Update imports in existing pages to use the new structure
   - Test thoroughly to ensure no functionality is broken

4. **Phase 4: Cleanup**
   - Once all components are migrated and tested, remove duplicate files
   - Update documentation

## Benefits of the New Structure

1. **Better Organization**: Code is organized by feature rather than by technical function
2. **Easier Maintenance**: Related code is kept together, making it easier to understand and maintain
3. **Improved Scalability**: New features can be added without affecting existing code
4. **Better Collaboration**: Team members can work on different features without conflicts
5. **Clearer Dependencies**: Dependencies between features are more explicit

## Next Steps

1. Continue migrating components to the new structure
2. Update imports in existing pages to use the new structure
3. Add more features to the website module
