import React from 'react';
import dynamic from 'next/dynamic';
import ErrorMonitoringDashboard from '@/components/admin/ErrorMonitoring/ErrorMonitoringDashboard';
import { Typography, Box, Paper } from '@mui/material';
import Head from 'next/head';
import withAuth from '@/hoc/withAuth';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);

const ErrorMonitoringPage = () => {
  return (
    <>
      <Head>
        <title>Error Monitoring | Vaivahik Admin</title>
      </Head>
      <EnhancedAdminLayout title="Error Monitoring">
        <Box sx={{ p: 3 }}>
          <Paper sx={{ p: 3 }}>
            <ErrorMonitoringDashboard />
          </Paper>
        </Box>
      </EnhancedAdminLayout>
    </>
  );
};

export default withAuth(ErrorMonitoringPage, ['ADMIN', 'SUPER_ADMIN']);
