{"success": true, "message": "Algorithm settings retrieved successfully", "settings": {"matchingAlgorithmVersion": "v1.5", "enableAIMatching": true, "matchingModel": "TWO_TOWER", "weights": {"ageWeight": 8, "heightWeight": 6, "educationWeight": 7, "occupationWeight": 7, "locationWeight": 8, "casteWeight": 9, "subCasteWeight": 5, "gotraWeight": 6, "incomeWeight": 5, "lifestyleWeight": 4}, "minimumMatchScore": 65, "highQualityMatchThreshold": 80, "abTestingEnabled": true, "abTestingVariant": "B", "abTestingDistribution": 50, "maxDistanceKm": 100, "maxAgeDifference": 10, "considerUserActivity": true, "boostNewProfiles": true, "boostNewProfilesDays": 7, "boostVerifiedProfiles": true, "boostVerifiedProfilesAmount": 10, "boostPremiumProfiles": true, "boostPremiumProfilesAmount": 15, "twoTower": {"embeddingSize": 128, "learningRate": 0.001, "batchSize": 64, "epochs": 10, "userTowerLayers": [128, 64], "matchTowerLayers": [128, 64], "dropoutRate": 0.2, "similarityMetric": "cosine"}, "phases": {"flexibility": {"enabled": true, "allowUserFlexibilityControl": true, "religionFlexibilityEnabled": true, "casteFlexibilityEnabled": true, "ageFlexibilityRange": 5, "heightFlexibilityRange": 0.5}, "personalization": {"enabled": false, "behavioralLearningEnabled": false, "dynamicPreferencesEnabled": false, "userAdaptationEnabled": false, "minimumInteractionsForLearning": 10}, "intelligentFeatures": {"enabled": false, "smartExplanationsEnabled": false, "predictiveScoringEnabled": false, "aiRecommendationsEnabled": false, "conversationAnalysisEnabled": false}, "advancedAI": {"enabled": false, "multiModalLearningEnabled": false, "graphNetworksEnabled": false, "reinforcementLearningEnabled": false, "realTimeAdaptationEnabled": false}}}, "abTestResults": {"variantA": {"matches": 245, "conversations": 156, "successRate": 63.7, "averageMatchScore": 71.2, "userSatisfaction": 4.2}, "variantB": {"matches": 267, "conversations": 182, "successRate": 68.2, "averageMatchScore": 74.8, "userSatisfaction": 4.5}, "improvement": {"matchesIncrease": 8.98, "conversationsIncrease": 16.67, "successRateIncrease": 4.5, "scoreIncrease": 3.6, "satisfactionIncrease": 0.3}}, "metrics": {"totalMatches": 5842, "successfulMatches": 2156, "averageMatchScore": 72.4, "matchDistribution": [12, 18, 25, 30, 15], "monthlyTrend": [120, 145, 160, 178, 195, 210], "performanceMetrics": {"accuracy": 87.3, "precision": 84.6, "recall": 89.1, "f1Score": 86.8}, "userEngagement": {"averageSessionTime": 18.5, "profileViewsPerSession": 12.3, "likesPerSession": 3.7, "messagesPerSession": 1.2}, "conversionRates": {"profileToLike": 0.31, "likeToMessage": 0.42, "messageToContact": 0.18, "contactToMeeting": 0.25}}, "phaseInfo": {"v1.0": {"name": "Current Rule-Based", "description": "Traditional rule-based matching with fixed preferences", "features": ["Basic compatibility scoring", "Fixed preference weights", "Simple filtering"], "status": "ACTIVE", "color": "success", "implementation": "COMPLETE"}, "v1.5": {"name": "Flexible Matching", "description": "Enhanced matching with flexible preferences and compatibility groups", "features": ["Flexible age ranges", "Religion compatibility groups", "Caste flexibility", "Gradual scoring"], "status": "READY", "color": "info", "implementation": "COMPLETE"}, "v2.0": {"name": "Personalized AI", "description": "AI-powered personalization based on user behavior", "features": ["Behavioral learning", "Dynamic preferences", "User adaptation", "Interaction analysis"], "status": "PLANNED", "color": "warning", "implementation": "IN_PROGRESS"}, "v2.5": {"name": "Intelligent Features", "description": "Advanced AI features for enhanced user experience", "features": ["Smart explanations", "Predictive scoring", "AI recommendations", "Conversation analysis"], "status": "PLANNED", "color": "warning", "implementation": "PLANNED"}, "v3.0": {"name": "Advanced AI", "description": "Cutting-edge AI algorithms for superior matching", "features": ["Multi-modal learning", "Graph networks", "Reinforcement learning", "Real-time adaptation"], "status": "PLANNED", "color": "error", "implementation": "PLANNED"}}, "modelTraining": {"lastTrainingDate": "2024-01-15T10:30:00Z", "trainingDataSize": 15420, "modelAccuracy": 87.3, "trainingDuration": "2h 45m", "nextScheduledTraining": "2024-01-22T02:00:00Z", "autoRetrainingEnabled": true, "retrainingThreshold": 85.0}}