// src/pages/api/admin/biodata/templates/index.js
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../auth/[...nextauth]';
import formidable from 'formidable';
import fs from 'fs';
import path from 'path';
import { isUsingRealBackend, getApiEndpoint } from '@/utils/featureFlags';
import { BACKEND_API_URL } from '@/config';

// Disable the default body parser to handle form data
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  // Check if user is authenticated and is an admin
  const session = await getServerSession(req, res, authOptions);

  if (!session || !session.user || !session.user.isAdmin) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  // Handle GET request - fetch biodata templates
  if (req.method === 'GET') {
    try {
      // Extract query parameters
      const {
        page = 1,
        limit = 10,
        search = '',
        sortBy = 'createdAt',
        order = 'desc',
        isActive = ''
      } = req.query;

      // Build query parameters
      const queryParams = new URLSearchParams({
        page,
        limit,
        ...(search && { search }),
        ...(sortBy && { sortBy }),
        ...(order && { order }),
        ...(isActive !== '' && { isActive })
      }).toString();

      // Check if we should use real backend or mock data
      if (isUsingRealBackend()) {
        try {
          // Fetch templates from backend API
          const apiUrl = `${BACKEND_API_URL}/api/admin/biodata/templates?${queryParams}`;
          console.log(`[API] Fetching from real backend: ${apiUrl}`);

          const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${session.accessToken}`
            }
          });

          if (!response.ok) {
            throw new Error(`Backend API error: ${response.status}`);
          }

          const data = await response.json();
          return res.status(200).json({
            success: true,
            templates: data.templates,
            pagination: data.pagination,
            source: 'real'
          });
        } catch (error) {
          console.error(`[API] Real backend error, falling back to mock data:`, error);
          // Fall back to mock data if real backend fails
          return fetchMockData(res, page, limit, search, sortBy, order, isActive);
        }
      } else {
        // Use mock data
        console.log(`[API] Using mock data (feature flag set to mock)`);
        return fetchMockData(res, page, limit, search, sortBy, order, isActive);
      }
    } catch (error) {
      console.error('Error fetching biodata templates:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch biodata templates',
        error: error.message
      });
    }
  }

  // Handle POST request - create a new biodata template
  if (req.method === 'POST') {
    try {
      // Parse form data
      const { fields, files } = await parseFormData(req);

      // Validate required fields
      if (!fields.name || !files.previewImage || !files.designFile || !fields.price) {
        return res.status(400).json({
          success: false,
          message: 'Name, preview image, design file, and price are required'
        });
      }

      // Upload files to backend or cloud storage
      const previewImageUrl = await uploadFile(files.previewImage[0]);
      const designFileUrl = await uploadFile(files.designFile[0]);

      // Prepare data for backend API
      const templateData = {
        name: fields.name[0],
        description: fields.description ? fields.description[0] : '',
        previewImage: previewImageUrl,
        designFile: designFileUrl,
        price: parseFloat(fields.price[0]),
        discountPercent: fields.discountPercent ? parseInt(fields.discountPercent[0]) : null,
        isActive: fields.isActive === 'true'
      };

      // Check if we should use real backend or mock data
      if (isUsingRealBackend()) {
        try {
          // Create template in backend API
          const apiUrl = `${BACKEND_API_URL}/api/admin/biodata/templates`;
          console.log(`[API] Creating template in real backend: ${apiUrl}`);

          const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${session.accessToken}`
            },
            body: JSON.stringify(templateData)
          });

          if (!response.ok) {
            throw new Error(`Backend API error: ${response.status}`);
          }

          const data = await response.json();
          return res.status(201).json({
            success: true,
            message: 'Biodata template created successfully',
            template: data.template,
            source: 'real'
          });
        } catch (error) {
          console.error(`[API] Real backend error, using mock implementation:`, error);
          // Fall back to mock implementation if real backend fails
          return res.status(201).json({
            success: true,
            message: 'Biodata template created successfully (mock)',
            template: {
              id: `template-${Date.now()}`,
              ...templateData,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            },
            source: 'mock',
            fallbackReason: error.message
          });
        }
      } else {
        // Use mock implementation
        console.log(`[API] Using mock implementation (feature flag set to mock)`);
        return res.status(201).json({
          success: true,
          message: 'Biodata template created successfully (mock)',
          template: {
            id: `template-${Date.now()}`,
            ...templateData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          source: 'mock'
        });
      }
    } catch (error) {
      console.error('Error creating biodata template:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to create biodata template',
        error: error.message
      });
    }
  }

  // Handle unsupported methods
  return res.status(405).json({ success: false, message: 'Method not allowed' });
}

// Helper function to parse form data
const parseFormData = (req) => {
  return new Promise((resolve, reject) => {
    const form = new formidable.IncomingForm({
      multiples: true,
      keepExtensions: true
    });

    form.parse(req, (err, fields, files) => {
      if (err) return reject(err);
      resolve({ fields, files });
    });
  });
};

// Helper function to upload file (mock implementation)
const uploadFile = async (file) => {
  // In a real implementation, this would upload to cloud storage
  // For now, we'll just return a mock URL
  const fileName = path.basename(file.originalFilename || file.newFilename);
  const fileType = fileName.split('.').pop();

  return `https://example.com/uploads/${Date.now()}-${fileName}`;
};

// Function to get template files from the public directory
function getMockTemplates() {
  try {
    // Get the list of HTML files in the public/templates/biodata directory
    const templatesDir = path.join(process.cwd(), 'public', 'templates', 'biodata');
    console.log(`[API] Reading template files from: ${templatesDir}`);

    const files = fs.readdirSync(templatesDir);
    const htmlFiles = files.filter(file => file.endsWith('.html'));

    console.log(`[API] Found ${htmlFiles.length} template files`);

    // Create mock template objects from the files
    return htmlFiles.map((file, index) => {
      const name = file.replace('.html', '').split('-').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1)
      ).join(' ');

      const genderOrientation = file.includes('female') ? 'female' :
                               file.includes('male') ? 'male' : 'neutral';

      const category = file.includes('traditional') ? 'traditional' :
                      file.includes('modern') ? 'modern' :
                      file.includes('professional') ? 'professional' :
                      file.includes('premium') ? 'premium' : 'standard';

      return {
        id: index + 1,
        name: name,
        description: `A ${category} biodata template with ${genderOrientation === 'neutral' ? 'gender-neutral' : genderOrientation + '-oriented'} design`,
        previewImage: `/images/biodata-templates/${file.replace('.html', '')}-preview.jpg`,
        thumbnail: `/images/biodata-templates/${file.replace('.html', '')}-thumb.jpg`,
        designFile: `/templates/biodata/${file}`,
        genderOrientation: genderOrientation,
        category: category,
        isActive: true,
        isPremium: category === 'premium',
        price: category === 'premium' ? 299 : 0,
        currency: "INR",
        headerText: "Shree Ganeshay Namah",
        footerText: "Powered by Vaivahik - The Premier Maratha Matrimony Platform",
        createdAt: new Date(Date.now() - (30 - index) * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString(),
        purchaseCount: Math.floor(Math.random() * 50),
        downloadCount: Math.floor(Math.random() * 100),
        revenue: Math.floor(Math.random() * 10000)
      };
    });
  } catch (error) {
    console.error('Error reading template files:', error);
    return [];
  }
}

// Helper function to fetch mock data
async function fetchMockData(res, page, limit, search, sortBy, order, isActive) {
  try {
    // Read mock data file
    const filePath = path.join(process.cwd(), 'public', 'mock-data', 'admin', 'biodata-templates.json');
    console.log(`[API] Reading mock data from: ${filePath}`);

    let mockData;
    let filteredTemplates = [];

    try {
      const fileData = fs.readFileSync(filePath, 'utf8');
      mockData = JSON.parse(fileData);
      filteredTemplates = mockData.templates ? [...mockData.templates] : [];
      console.log(`[API] Found ${filteredTemplates.length} templates in mock data`);
    } catch (error) {
      console.error('Error reading mock data file:', error);
      console.log('[API] Using template files instead');
      filteredTemplates = getMockTemplates();
      mockData = { templates: filteredTemplates };
    }

    // If no templates are found in the mock data, use template files
    if (filteredTemplates.length === 0) {
      console.log('[API] No templates found in mock data, using template files');
      filteredTemplates = getMockTemplates();
    }

    if (search) {
      const searchLower = search.toLowerCase();
      filteredTemplates = filteredTemplates.filter(t =>
        t.name.toLowerCase().includes(searchLower) ||
        (t.description && t.description.toLowerCase().includes(searchLower))
      );
    }

    if (isActive !== '') {
      const isActiveValue = isActive === 'true';
      filteredTemplates = filteredTemplates.filter(t => t.isActive === isActiveValue);
    }

    // Apply sorting
    if (sortBy) {
      filteredTemplates.sort((a, b) => {
        const aValue = a[sortBy];
        const bValue = b[sortBy];

        if (typeof aValue === 'string') {
          return order === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
        } else {
          return order === 'asc' ? aValue - bValue : bValue - aValue;
        }
      });
    }

    // Apply pagination
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedTemplates = filteredTemplates.slice(startIndex, endIndex);

    // Return the mock data
    return res.status(200).json({
      success: true,
      message: 'Biodata templates fetched successfully',
      templates: paginatedTemplates,
      pagination: {
        currentPage: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(filteredTemplates.length / limitNum),
        totalTemplates: filteredTemplates.length
      },
      categories: mockData.categories,
      genderOrientations: mockData.genderOrientations,
      stats: mockData.stats,
      source: 'mock'
    });
  } catch (error) {
    console.error('Error reading mock data:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch mock biodata templates',
      error: error.message
    });
  }
}
