// API endpoint for success analytics
import axios from 'axios';
import { handleApiError } from '@/utils/errorHandler';
import { withAuth } from '@/utils/authHandler';

// Backend API URL - adjust this to your actual backend URL
const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:8000/api';

async function handler(req, res) {
  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getSuccessAnalytics(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    return handleApiError(error, res, 'Success Analytics API');
  }
}

// GET /api/admin/success-analytics
async function getSuccessAnalytics(req, res) {
  try {
    // Get the auth token from the request cookies or headers
    const token = req.cookies?.adminToken || req.headers.authorization?.split(' ')[1];

    if (!token) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    // Get query parameters
    const { period = 'month', metric = 'all' } = req.query;

    try {
      // Fetch success analytics from the backend API
      const response = await axios({
        method: 'GET',
        url: `${BACKEND_API_URL}/admin/success-analytics`,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        params: {
          period,
          metric
        },
        timeout: 10000 // 10 second timeout
      });

      // Return the response from the backend
      return res.status(200).json(response.data);
    } catch (apiError) {
      // Log the error
      console.error('Error fetching success analytics from backend API:', apiError.message);

      // Return mock data for development
      if (process.env.NODE_ENV === 'development') {
        return res.status(200).json({
          success: true,
          analytics: {
            totalMatches: 1250,
            successfulMatches: 187,
            successRate: 14.96,
            averageMatchingTime: 12.5,
            userSatisfactionScore: 4.2,
            monthlyGrowth: 8.5,
            topMatchingFactors: [
              { factor: 'Education Level', weight: 0.25 },
              { factor: 'Location Proximity', weight: 0.20 },
              { factor: 'Age Compatibility', weight: 0.18 },
              { factor: 'Caste Preference', weight: 0.15 },
              { factor: 'Income Range', weight: 0.12 },
              { factor: 'Height Preference', weight: 0.10 }
            ],
            recentSuccessStories: [
              {
                id: 1,
                couple: 'Priya & Rahul',
                matchDate: '2024-01-15',
                marriageDate: '2024-03-20',
                location: 'Mumbai'
              },
              {
                id: 2,
                couple: 'Sneha & Arjun',
                matchDate: '2024-02-10',
                marriageDate: '2024-04-15',
                location: 'Pune'
              }
            ]
          },
          message: 'Mock analytics data for development'
        });
      }

      // Return a meaningful error message for production
      return res.status(503).json({
        success: false,
        message: 'Analytics data is not available yet. Please check back after users have started using the platform.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available'
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Get success analytics');
  }
}

// Export the handler with authentication middleware
// In development mode, skip authentication for easier testing
export default process.env.NODE_ENV === 'development' ? handler : withAuth(handler, 'ADMIN');
