/**
 * Dashboard Navigation Component
 * Provides navigation between different dashboard versions
 */

import React from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Button,
  Card,
  CardContent,
  Grid,
  Typography,
  Chip,
  styled
} from '@mui/material';

// Icons
import {
  Dashboard as DashboardIcon,
  Psychology as AIIcon,
  WorkspacePremium as PremiumIcon,
  Star as StarIcon,
  Security as SecurityIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';

// Styled components
const NavigationCard = styled(Card)(({ theme }) => ({
  borderRadius: 16,
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 16px 48px rgba(0,0,0,0.12)'
  }
}));

const FeatureChip = styled(Chip)(({ theme }) => ({
  margin: theme.spacing(0.5),
  fontSize: '0.75rem'
}));

export default function DashboardNavigation() {
  const router = useRouter();

  const dashboardOptions = [
    {
      title: 'Current Dashboard',
      description: 'Your existing dashboard with basic features',
      path: '/website/dashboard',
      icon: <DashboardIcon sx={{ fontSize: 48, color: '#2196F3' }} />,
      features: ['Basic Stats', 'Profile Completion', 'Simple Search', 'Basic Matching'],
      type: 'current'
    },
    {
      title: 'Professional Dashboard',
      description: 'Advanced AI-powered matrimony platform',
      path: '/website/professional-dashboard',
      icon: <AIIcon sx={{ fontSize: 48, color: '#667eea' }} />,
      features: [
        'AI-Powered Matching',
        'Advanced Search & Filters',
        'Secure Contact Reveal',
        'User Analytics',
        'Premium Features',
        'Professional UI'
      ],
      type: 'professional',
      badge: 'RECOMMENDED'
    }
  ];

  const handleNavigate = (path) => {
    router.push(path);
  };

  return (
    <Box sx={{ p: 4 }}>
      <Box sx={{ textAlign: 'center', mb: 6 }}>
        <Typography variant="h3" fontWeight="700" gutterBottom>
          Choose Your Dashboard Experience
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Select the dashboard that best fits your needs
        </Typography>
      </Box>

      <Grid container spacing={4} justifyContent="center">
        {dashboardOptions.map((option, index) => (
          <Grid item xs={12} md={6} key={index}>
            <NavigationCard
              onClick={() => handleNavigate(option.path)}
              sx={{
                position: 'relative',
                border: option.type === 'professional' ? '2px solid #667eea' : '1px solid rgba(0,0,0,0.1)',
                background: option.type === 'professional' 
                  ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05))'
                  : 'white'
              }}
            >
              {option.badge && (
                <Chip
                  label={option.badge}
                  sx={{
                    position: 'absolute',
                    top: -12,
                    right: 20,
                    background: 'linear-gradient(135deg, #667eea, #764ba2)',
                    color: 'white',
                    fontWeight: 700,
                    fontSize: '0.75rem'
                  }}
                />
              )}
              
              <CardContent sx={{ p: 4, textAlign: 'center' }}>
                <Box sx={{ mb: 3 }}>
                  {option.icon}
                </Box>
                
                <Typography variant="h5" fontWeight="700" gutterBottom>
                  {option.title}
                </Typography>
                
                <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                  {option.description}
                </Typography>

                <Box sx={{ mb: 4 }}>
                  <Typography variant="subtitle2" fontWeight="600" gutterBottom>
                    Features:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'center' }}>
                    {option.features.map((feature, featureIndex) => (
                      <FeatureChip
                        key={featureIndex}
                        label={feature}
                        variant="outlined"
                        size="small"
                        sx={{
                          borderColor: option.type === 'professional' ? '#667eea' : '#2196F3',
                          color: option.type === 'professional' ? '#667eea' : '#2196F3'
                        }}
                      />
                    ))}
                  </Box>
                </Box>

                <Button
                  variant="contained"
                  size="large"
                  fullWidth
                  sx={{
                    background: option.type === 'professional' 
                      ? 'linear-gradient(135deg, #667eea, #764ba2)'
                      : 'linear-gradient(135deg, #2196F3, #1976D2)',
                    py: 1.5,
                    fontSize: '1.1rem',
                    fontWeight: 600
                  }}
                >
                  {option.type === 'professional' ? 'Try Professional Dashboard' : 'Continue with Current'}
                </Button>
              </CardContent>
            </NavigationCard>
          </Grid>
        ))}
      </Grid>

      {/* Professional Dashboard Benefits */}
      <Box sx={{ mt: 8, textAlign: 'center' }}>
        <Typography variant="h5" fontWeight="700" gutterBottom>
          Why Choose Professional Dashboard?
        </Typography>
        
        <Grid container spacing={3} sx={{ mt: 2 }}>
          <Grid item xs={12} md={4}>
            <Box>
              <AIIcon sx={{ fontSize: 48, color: '#667eea', mb: 2 }} />
              <Typography variant="h6" fontWeight="600" gutterBottom>
                AI-Powered Matching
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Advanced machine learning algorithms find your perfect match with high accuracy
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Box>
              <SecurityIcon sx={{ fontSize: 48, color: '#4CAF50', mb: 2 }} />
              <Typography variant="h6" fontWeight="600" gutterBottom>
                Enhanced Security
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Advanced fraud detection and secure contact reveal system for your safety
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Box>
              <AnalyticsIcon sx={{ fontSize: 48, color: '#FF9800', mb: 2 }} />
              <Typography variant="h6" fontWeight="600" gutterBottom>
                Detailed Analytics
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Comprehensive insights into your profile performance and match success
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Admin Integration Note */}
      <Box sx={{ mt: 6, p: 3, backgroundColor: '#f5f5f5', borderRadius: 2 }}>
        <Typography variant="h6" fontWeight="600" gutterBottom>
          🔧 Admin Integration
        </Typography>
        <Typography variant="body2" color="text.secondary">
          The Professional Dashboard leverages all your existing admin functionality including:
          ML matching service, user management, verification system, analytics, search functionality, 
          and premium management. All features are managed through your existing admin panel.
        </Typography>
      </Box>
    </Box>
  );
}
