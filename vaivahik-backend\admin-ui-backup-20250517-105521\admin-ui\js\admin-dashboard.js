// DOM Elements
const navLinks = document.querySelectorAll('.nav-item');
const darkModeToggle = document.getElementById('darkModeToggle');
const notificationBtn = document.querySelector('.notification-btn');
const sidebarToggle = document.getElementById('sidebarToggle');

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    // Initialize components
    initializeDashboard();
    setupEventListeners();
});

// Initialize dashboard components
function initializeDashboard() {
    loadDashboardData();
    loadCharts();
    loadRecentActivity();
}

// Event Listeners
function setupEventListeners() {
    // Navigation
    console.log("Setting up navigation event listeners...");

    // Get all navigation links
    console.log("Found navigation links:", navLinks.length);

    // Function to handle navigation
    function handleNavigation(e) {
        e.preventDefault(); // Prevent default link behavior
        const linkText = this.querySelector('.nav-text')?.textContent.trim();
        
        console.log("Navigation clicked:", linkText);
        
        switch(linkText) {
            case "Dashboard":
                window.location.href = "admin-dashboard.html";
                break;
            case "All Users":
                window.location.href = "all-users.html";
                break;
            case "Verification Queue":
                window.location.href = "verification-queue.html";
                break;
            case "Reported Profiles":
                window.location.href = "reported-profiles.html";
                break;
            default:
                console.log("Unknown navigation option:", linkText);
        }
    }

    // Add click event listeners to all navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', handleNavigation);
    });

    // Dark mode toggle
    darkModeToggle.addEventListener('click', toggleDarkMode);

    // Notifications
    notificationBtn.addEventListener('click', handleNotifications);

    // Sidebar toggle
    sidebarToggle.addEventListener('click', toggleSidebar);
}

// Update active navigation item
function updateActiveNavItem() {
    const currentPath = window.location.pathname;
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (currentPath.includes(href)) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
}

// Toggle dark mode
function toggleDarkMode() {
    document.body.classList.toggle('dark-mode');
    const isDarkMode = document.body.classList.contains('dark-mode');
    localStorage.setItem('darkMode', isDarkMode);
}

// Toggle sidebar
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');
    sidebar.classList.toggle('collapsed');
    mainContent.classList.toggle('collapsed');
}

// Handle notifications
function handleNotifications() {
    // Implement notification handling logic
    console.log('Notifications clicked');
}

// Update table for all users view
function updateTableForAllUsers() {
    const tableTitle = document.querySelector('.table-title');
    if (tableTitle) tableTitle.textContent = "User Registrations";

    const tableHeaders = document.querySelector('table thead tr');
    if (tableHeaders) {
        tableHeaders.innerHTML = `
            <th>User</th>
            <th>Registration Date</th>
            <th>Location</th>
            <th>Verification Status</th>
            <th>Account Status</th>
            <th>Actions</th>
        `;
    }
}

// Load dashboard data
async function loadDashboardData() {
    try {
        const response = await fetch('/api/admin/dashboard/stats');
        if (!response.ok) throw new Error('Failed to fetch dashboard data');
        
        const data = await response.json();
        updateDashboardWidgets(data);
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        showError('Failed to load dashboard statistics');
    }
}

// Update dashboard widgets with data
function updateDashboardWidgets(data) {
    // Update total users widget
    const totalUsers = document.getElementById('widgetTotalUsers');
    const totalUsersChange = document.getElementById('widgetTotalUsersChange');
    if (totalUsers && data.totalUsers) {
        totalUsers.textContent = data.totalUsers.toLocaleString();
        totalUsersChange.textContent = `${data.totalUsersGrowth}% from last month`;
        totalUsersChange.className = `widget-change ${data.totalUsersGrowth >= 0 ? 'positive' : 'negative'}`;
    }

    // Update new registrations widget
    const newReg = document.getElementById('widgetNewReg');
    const newRegChange = document.getElementById('widgetNewRegChange');
    if (newReg && data.newRegistrations) {
        newReg.textContent = data.newRegistrations.toLocaleString();
        newRegChange.textContent = `${data.newRegistrationsGrowth}% from last week`;
        newRegChange.className = `widget-change ${data.newRegistrationsGrowth >= 0 ? 'positive' : 'negative'}`;
    }

    // Update successful matches widget
    const successMatches = document.getElementById('widgetSuccessMatches');
    const successMatchesChange = document.getElementById('widgetSuccessMatchesChange');
    if (successMatches && data.successfulMatches) {
        successMatches.textContent = data.successfulMatches.toLocaleString();
        successMatchesChange.textContent = `${data.successfulMatchesGrowth}% from last month`;
        successMatchesChange.className = `widget-change ${data.successfulMatchesGrowth >= 0 ? 'positive' : 'negative'}`;
    }

    // Update revenue widget
    const revenue = document.getElementById('widgetRevenue');
    const revenueChange = document.getElementById('widgetRevenueChange');
    if (revenue && data.revenue) {
        revenue.textContent = `₹${data.revenue.toLocaleString()}`;
        revenueChange.textContent = `${data.revenueGrowth}% from last month`;
        revenueChange.className = `widget-change ${data.revenueGrowth >= 0 ? 'positive' : 'negative'}`;
    }
}

// Load charts
async function loadCharts() {
    try {
        const [userGrowthData, matchRateData] = await Promise.all([
            fetch('/api/admin/dashboard/user-growth').then(res => res.json()),
            fetch('/api/admin/dashboard/match-rate').then(res => res.json())
        ]);

        renderUserGrowthChart(userGrowthData);
        renderMatchRateChart(matchRateData);
    } catch (error) {
        console.error('Error loading charts:', error);
        showError('Failed to load chart data');
    }
}

// Render user growth chart
function renderUserGrowthChart(data) {
    const ctx = document.getElementById('userGrowthChart');
    if (!ctx) return;

    // Implement chart rendering using your preferred charting library
    // Example: Chart.js, ApexCharts, etc.
}

// Render match rate chart
function renderMatchRateChart(data) {
    const ctx = document.getElementById('matchRateChart');
    if (!ctx) return;

    // Implement chart rendering using your preferred charting library
    // Example: Chart.js, ApexCharts, etc.
}

// Load recent activity
async function loadRecentActivity() {
    try {
        const response = await fetch('/api/admin/dashboard/recent-activity');
        if (!response.ok) throw new Error('Failed to fetch recent activity');
        
        const activities = await response.json();
        renderActivityFeed(activities);
    } catch (error) {
        console.error('Error loading recent activity:', error);
        showError('Failed to load recent activity');
    }
}

// Render activity feed
function renderActivityFeed(activities) {
    const activityList = document.getElementById('activityList');
    if (!activityList || !activities.length) return;

    activityList.innerHTML = activities.map(activity => `
        <li class="activity-item">
            <div class="activity-content">
                <div class="activity-icon">${getActivityIcon(activity.type)}</div>
                <div class="activity-details">
                    <h4>${activity.title}</h4>
                    <p>${activity.description}</p>
                    <span class="activity-time">${formatActivityTime(activity.timestamp)}</span>
                </div>
            </div>
        </li>
    `).join('');
}

// Helper function to get activity icon
function getActivityIcon(type) {
    const icons = {
        'user_registration': '👤',
        'profile_verification': '✅',
        'match_success': '💑',
        'report': '⚠️',
        'payment': '💰'
    };
    return icons[type] || '📝';
}

// Helper function to format activity time
function formatActivityTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;

    // Convert milliseconds to minutes/hours/days
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 60) return `${minutes} minutes ago`;
    if (hours < 24) return `${hours} hours ago`;
    if (days < 30) return `${days} days ago`;
    return date.toLocaleDateString();
}

// Show error message
function showError(message) {
    // Implement error notification
    console.error(message);
} 