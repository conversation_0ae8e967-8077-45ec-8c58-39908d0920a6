import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Avatar,
  Button,
  IconButton,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Paper,
  Divider,
  Alert,
  CircularProgress,
  Tooltip,
  Badge
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  ExpandMore as ExpandMoreIcon,
  Visibility as ViewIcon,
  Favorite as FavoriteIcon,
  Bookmark as BookmarkIcon,
  Clear as ClearIcon,
  Save as SaveIcon,
  Person as PersonIcon,
  LocationOn as LocationIcon,
  Work as WorkIcon,
  School as SchoolIcon
} from '@mui/icons-material';

const AGE_RANGES = [18, 60];
const HEIGHT_RANGES = [140, 200]; // in cm

const EDUCATION_OPTIONS = [
  'High School', 'Diploma', 'Bachelor\'s Degree', 'Master\'s Degree', 
  'PhD', 'Professional Degree', 'Other'
];

const OCCUPATION_OPTIONS = [
  'Software Engineer', 'Doctor', 'Teacher', 'Business Owner', 'Lawyer',
  'Engineer', 'Accountant', 'Consultant', 'Government Employee', 'Other'
];

const MARATHA_SUBCASTES = [
  'Maratha', '96 Kuli Maratha', 'Kunbi Maratha', 'Dhangar', 'Koli',
  'Agri', 'Mahadeo Koli', 'Other'
];

const LOCATION_OPTIONS = [
  'Mumbai', 'Pune', 'Nagpur', 'Nashik', 'Aurangabad', 'Solapur',
  'Kolhapur', 'Sangli', 'Satara', 'Other'
];

export default function SearchPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [totalResults, setTotalResults] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [filtersExpanded, setFiltersExpanded] = useState(true);

  // Search filters
  const [filters, setFilters] = useState({
    ageRange: [22, 35],
    heightRange: [150, 180],
    education: '',
    occupation: '',
    location: '',
    subcaste: '',
    maritalStatus: '',
    motherTongue: '',
    minIncome: '',
    maxIncome: '',
    hasPhoto: false,
    isVerified: false,
    isOnline: false
  });

  const [savedSearches, setSavedSearches] = useState([]);
  const [saveSearchDialog, setSaveSearchDialog] = useState(false);

  useEffect(() => {
    // Load saved searches
    loadSavedSearches();
    // Perform initial search
    handleSearch();
  }, []);

  const loadSavedSearches = async () => {
    try {
      const response = await fetch('/api/user/saved-searches', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        setSavedSearches(data.data || []);
      }
    } catch (error) {
      console.error('Error loading saved searches:', error);
    }
  };

  const handleSearch = async (page = 1) => {
    setLoading(true);
    try {
      const searchParams = new URLSearchParams({
        page: page.toString(),
        limit: '12',
        ...filters,
        ageMin: filters.ageRange[0].toString(),
        ageMax: filters.ageRange[1].toString(),
        heightMin: filters.heightRange[0].toString(),
        heightMax: filters.heightRange[1].toString()
      });

      const response = await fetch(`/api/search/profiles?${searchParams}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSearchResults(data.data?.profiles || []);
        setTotalResults(data.data?.total || 0);
        setCurrentPage(page);
      }
    } catch (error) {
      console.error('Error searching profiles:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      ageRange: [22, 35],
      heightRange: [150, 180],
      education: '',
      occupation: '',
      location: '',
      subcaste: '',
      maritalStatus: '',
      motherTongue: '',
      minIncome: '',
      maxIncome: '',
      hasPhoto: false,
      isVerified: false,
      isOnline: false
    });
  };

  const handleSendInterest = async (profileId) => {
    try {
      const response = await fetch('/api/user/interests/send', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          targetUserId: profileId,
          message: 'I found your profile interesting and would like to connect.'
        })
      });

      if (response.ok) {
        // Update the profile in search results
        setSearchResults(results =>
          results.map(profile =>
            profile.id === profileId
              ? { ...profile, interestSent: true }
              : profile
          )
        );
      }
    } catch (error) {
      console.error('Error sending interest:', error);
    }
  };

  const handleAddToShortlist = async (profileId) => {
    try {
      const response = await fetch('/api/user/shortlist', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ profileId })
      });

      if (response.ok) {
        // Update the profile in search results
        setSearchResults(results =>
          results.map(profile =>
            profile.id === profileId
              ? { ...profile, isShortlisted: true }
              : profile
          )
        );
      }
    } catch (error) {
      console.error('Error adding to shortlist:', error);
    }
  };

  const ProfileCard = ({ profile }) => {
    const [liked, setLiked] = useState(false);
    const [shortlisted, setShortlisted] = useState(profile?.isShortlisted || false);

    return (
      <Card sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: 3,
        overflow: 'hidden',
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: '0 12px 24px rgba(0,0,0,0.15)'
        }
      }}>
        {/* Profile Image */}
        <Box sx={{ position: 'relative', height: 200 }}>
          <CardMedia
            component="img"
            height="200"
            image={profile.profilePicture || '/default-avatar.png'}
            alt={profile.firstName}
            sx={{ objectFit: 'cover' }}
          />

          {/* Online Status */}
          {profile.isOnline && (
            <Box sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              background: '#4CAF50',
              color: 'white',
              padding: '2px 6px',
              borderRadius: 1,
              fontSize: '0.7rem',
              fontWeight: 600
            }}>
              Online
            </Box>
          )}

          {/* Verification Badge */}
          {profile.isVerified && (
            <Box sx={{
              position: 'absolute',
              top: 8,
              left: 8,
              background: 'rgba(255,255,255,0.9)',
              borderRadius: '50%',
              p: 0.5
            }}>
              <VerifiedIcon sx={{ color: '#4CAF50', fontSize: 20 }} />
            </Box>
          )}

          {/* Quick Actions */}
          <Box sx={{
            position: 'absolute',
            bottom: 8,
            right: 8,
            display: 'flex',
            gap: 1
          }}>
            <IconButton
              size="small"
              onClick={() => setShortlisted(!shortlisted)}
              sx={{
                background: 'rgba(255,255,255,0.9)',
                '&:hover': { background: 'white' }
              }}
            >
              {shortlisted ?
                <BookmarkIcon sx={{ color: '#1976d2' }} /> :
                <BookmarkBorderIcon />
              }
            </IconButton>
            <IconButton
              size="small"
              onClick={() => setLiked(!liked)}
              sx={{
                background: 'rgba(255,255,255,0.9)',
                '&:hover': { background: 'white' }
              }}
            >
              {liked ?
                <FavoriteIcon sx={{ color: '#f44336' }} /> :
                <FavoriteBorderIcon />
              }
            </IconButton>
          </Box>
        </Box>

        <CardContent sx={{ flexGrow: 1, p: 2 }}>
          {/* Basic Info */}
          <Typography variant="h6" fontWeight="600" gutterBottom>
            {profile.firstName} {profile.lastName}, {profile.age}
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            <LocationIcon sx={{ fontSize: 16, color: '#666' }} />
            <Typography variant="body2" color="text.secondary">
              {profile.city}, {profile.state}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            <WorkIcon sx={{ fontSize: 16, color: '#666' }} />
            <Typography variant="body2" color="text.secondary" noWrap>
              {profile.occupation}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
            <SchoolIcon sx={{ fontSize: 16, color: '#666' }} />
            <Typography variant="body2" color="text.secondary" noWrap>
              {profile.education}
            </Typography>
          </Box>

          {/* Compatibility Score */}
          {profile.compatibility && (
            <Box sx={{
              background: `linear-gradient(90deg,
                ${profile.compatibility >= 90 ? '#4CAF50' :
                  profile.compatibility >= 80 ? '#FF9800' : '#FF5722'} 0%,
                rgba(255,255,255,0.1) 100%)`,
              borderRadius: 2,
              p: 1,
              mb: 2,
              textAlign: 'center'
            }}>
              <Typography variant="body2" fontWeight="600" color="white">
                {profile.compatibility}% Compatible
              </Typography>
            </Box>
          )}

          {/* Status Chips */}
          <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
            {profile.interestSent && (
              <Chip label="Interest Sent" color="success" size="small" />
            )}
            {shortlisted && (
              <Chip label="Shortlisted" color="info" size="small" />
            )}
            {liked && (
              <Chip label="Liked" color="error" size="small" />
            )}
          </Box>
        </CardContent>

        <CardActions sx={{ p: 2, pt: 0, gap: 1 }}>
          <Button
            size="small"
            variant="outlined"
            startIcon={<ViewIcon />}
            onClick={() => router.push(`/profile/${profile.id}`)}
            fullWidth
          >
            View Profile
          </Button>

          {!profile.interestSent ? (
            <Button
              size="small"
              variant="contained"
              startIcon={<FavoriteIcon />}
              onClick={() => handleSendInterest(profile.id)}
              fullWidth
            >
              Send Interest
            </Button>
          ) : (
            <Button
              size="small"
              variant="contained"
              startIcon={<MessageIcon />}
              onClick={() => router.push(`/messages?user=${profile.id}`)}
              fullWidth
            >
              Message
            </Button>
          )}
        </CardActions>
      </Card>
    );
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        <SearchIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
        Find Your Perfect Match
      </Typography>

      {/* Search Filters */}
      <Paper sx={{ mb: 3 }}>
        <Accordion expanded={filtersExpanded} onChange={() => setFiltersExpanded(!filtersExpanded)}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">
              <FilterIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Search Filters
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={3}>
              {/* Age Range */}
              <Grid item xs={12} sm={6} md={3}>
                <Typography gutterBottom>Age Range</Typography>
                <Slider
                  value={filters.ageRange}
                  onChange={(e, value) => handleFilterChange('ageRange', value)}
                  valueLabelDisplay="auto"
                  min={AGE_RANGES[0]}
                  max={AGE_RANGES[1]}
                  marks={[
                    { value: AGE_RANGES[0], label: `${AGE_RANGES[0]}` },
                    { value: AGE_RANGES[1], label: `${AGE_RANGES[1]}` }
                  ]}
                />
              </Grid>

              {/* Height Range */}
              <Grid item xs={12} sm={6} md={3}>
                <Typography gutterBottom>Height Range (cm)</Typography>
                <Slider
                  value={filters.heightRange}
                  onChange={(e, value) => handleFilterChange('heightRange', value)}
                  valueLabelDisplay="auto"
                  min={HEIGHT_RANGES[0]}
                  max={HEIGHT_RANGES[1]}
                  marks={[
                    { value: HEIGHT_RANGES[0], label: `${HEIGHT_RANGES[0]}` },
                    { value: HEIGHT_RANGES[1], label: `${HEIGHT_RANGES[1]}` }
                  ]}
                />
              </Grid>

              {/* Education */}
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Education</InputLabel>
                  <Select
                    value={filters.education}
                    onChange={(e) => handleFilterChange('education', e.target.value)}
                    label="Education"
                  >
                    <MenuItem value="">Any</MenuItem>
                    {EDUCATION_OPTIONS.map(option => (
                      <MenuItem key={option} value={option}>{option}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Occupation */}
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Occupation</InputLabel>
                  <Select
                    value={filters.occupation}
                    onChange={(e) => handleFilterChange('occupation', e.target.value)}
                    label="Occupation"
                  >
                    <MenuItem value="">Any</MenuItem>
                    {OCCUPATION_OPTIONS.map(option => (
                      <MenuItem key={option} value={option}>{option}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Location */}
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Location</InputLabel>
                  <Select
                    value={filters.location}
                    onChange={(e) => handleFilterChange('location', e.target.value)}
                    label="Location"
                  >
                    <MenuItem value="">Any</MenuItem>
                    {LOCATION_OPTIONS.map(option => (
                      <MenuItem key={option} value={option}>{option}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Subcaste */}
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Subcaste</InputLabel>
                  <Select
                    value={filters.subcaste}
                    onChange={(e) => handleFilterChange('subcaste', e.target.value)}
                    label="Subcaste"
                  >
                    <MenuItem value="">Any</MenuItem>
                    {MARATHA_SUBCASTES.map(option => (
                      <MenuItem key={option} value={option}>{option}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
              <Button
                variant="contained"
                startIcon={<SearchIcon />}
                onClick={() => handleSearch(1)}
                disabled={loading}
              >
                {loading ? <CircularProgress size={20} /> : 'Search'}
              </Button>
              <Button
                variant="outlined"
                startIcon={<ClearIcon />}
                onClick={clearFilters}
              >
                Clear Filters
              </Button>
              <Button
                variant="outlined"
                startIcon={<SaveIcon />}
                onClick={() => setSaveSearchDialog(true)}
              >
                Save Search
              </Button>
            </Box>
          </AccordionDetails>
        </Accordion>
      </Paper>

      {/* Search Results */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Search Results ({totalResults} profiles found)
        </Typography>
      </Box>

      {loading ? (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <CircularProgress />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Searching for your perfect match...
          </Typography>
        </Box>
      ) : searchResults.length > 0 ? (
        <Grid container spacing={3}>
          {searchResults.map((profile) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={profile.id}>
              <ProfileCard profile={profile} />
            </Grid>
          ))}
        </Grid>
      ) : (
        <Alert severity="info">
          No profiles found matching your criteria. Try adjusting your filters.
        </Alert>
      )}

      {/* Pagination */}
      {totalResults > 12 && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <Button
            disabled={currentPage === 1}
            onClick={() => handleSearch(currentPage - 1)}
          >
            Previous
          </Button>
          <Typography sx={{ mx: 2, alignSelf: 'center' }}>
            Page {currentPage} of {Math.ceil(totalResults / 12)}
          </Typography>
          <Button
            disabled={currentPage >= Math.ceil(totalResults / 12)}
            onClick={() => handleSearch(currentPage + 1)}
          >
            Next
          </Button>
        </Box>
      )}
    </Container>
  );
}
