/* Dashboard Styles */

/* Content Header */
.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn-primary {
  background-color: var(--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-icon {
  margin-right: 8px;
}

/* Loading Container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid var(--primary);
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Widget Container */
.widget-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.widget {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  padding: 20px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.widget:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.widget-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  font-size: 0.9rem;
  color: var(--text-muted);
  margin: 0 0 5px 0;
  font-weight: 500;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 5px;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.8rem;
}

.change-up {
  color: var(--success);
}

.change-down {
  color: var(--danger);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.bg-purple {
  background-color: rgba(94, 53, 177, 0.1);
  color: var(--primary);
}

.bg-purple-light {
  background-color: rgba(126, 87, 194, 0.1);
  color: var(--primary-light);
}

.bg-orange {
  background-color: rgba(255, 152, 0, 0.1);
  color: var(--warning);
}

.bg-green {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success);
}

.bg-blue {
  background-color: rgba(33, 150, 243, 0.1);
  color: var(--info);
}

.bg-red {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--danger);
}

/* Charts Grid */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.chart-card {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  padding: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0;
}

.chart-actions {
  display: flex;
  gap: 5px;
}

.chart-action {
  background: none;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chart-action.active {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

.chart {
  height: 300px;
  width: 100%;
}

/* Chart Placeholders */
.chart-placeholder {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-bars {
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  height: 80%;
  width: 80%;
}

.chart-bar {
  width: 40px;
  background-color: var(--primary);
  border-radius: 4px 4px 0 0;
  transition: height 0.5s ease;
}

.chart-donut {
  position: relative;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background-color: #f0f0f0;
}

.donut-segment {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  clip: rect(0, 100px, 200px, 0);
}

.donut-hole {
  position: absolute;
  width: 70%;
  height: 70%;
  border-radius: 50%;
  background-color: var(--bg-white);
  top: 15%;
  left: 15%;
}

.donut-center {
  position: absolute;
  width: 70%;
  height: 70%;
  border-radius: 50%;
  top: 15%;
  left: 15%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary);
}

/* Dashboard Bottom Section */
.dashboard-bottom {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
}

.recent-users-section, .recent-activity-section {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  padding: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0;
}

.filter-controls {
  display: flex;
  gap: 10px;
}

.view-all-link {
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  margin-left: 15px;
}

/* User Cell Styling */
.user-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--primary-light);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 500;
  color: var(--text-dark);
}

.user-age {
  font-size: 0.8rem;
  color: var(--text-muted);
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-bottom {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .widget-container {
    grid-template-columns: 1fr;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .filter-controls {
    width: 100%;
    flex-wrap: wrap;
  }
  
  .view-all-link {
    margin-left: 0;
  }
}

@media (max-width: 480px) {
  .stat-value {
    font-size: 1.5rem;
  }
  
  .chart-action {
    padding: 3px 6px;
    font-size: 0.7rem;
  }
  
  .chart {
    height: 250px;
  }
}
