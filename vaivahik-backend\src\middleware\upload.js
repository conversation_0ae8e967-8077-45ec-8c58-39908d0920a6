// src/middleware/upload.js

const multer = require('multer');
const path = require('path');
// No longer need fs or validateImageSize here as we process in controller
// const fs = require('fs');
// const { validateImageSize } = require('../../utils/project.js');

// Use memory storage to handle files as buffers for sharp processing
const storage = multer.memoryStorage();

// Configure Multer upload options
const uploadPhotos = multer({
  storage: storage, // Store file in memory as a buffer
  limits: {
    fileSize: 2 * 1024 * 1024 // 2MB limit per file
  },
  fileFilter: function (req, file, cb) {
    // Check file type (allow jpeg, jpg, png, webp)
    const filetypes = /jpeg|jpg|png|webp/; // Added webp
    const mimetype = filetypes.test(file.mimetype);
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

    if (mimetype && extname) {
      cb(null, true); // Accept the file
    } else {
      // Reject the file - create an error object for better handling
      const err = new Error('Error: Only JPEG, JPG, PNG, or WEBP images are allowed!');
      err.code = 'INVALID_FILE_TYPE'; // Custom error code
      cb(err, false);
    }
  }
// Use .array() to accept multiple files (up to 3) under the field name 'profilePhotos'
}).array('profilePhotos', 3); // Max 3 files, field name 'profilePhotos'

// Middleware function to handle potential Multer errors gracefully
const handleUploadMiddleware = (req, res, next) => {
    uploadPhotos(req, res, function (err) {
        if (err instanceof multer.MulterError) {
            // A Multer error occurred (e.g., file size limit exceeded)
            console.error('Multer Error:', err);
            let message = 'File upload error.';
            if (err.code === 'LIMIT_FILE_SIZE') {
                message = 'File size exceeds the 2MB limit.';
            } else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
                message = 'Too many files uploaded (max 3 allowed) or incorrect field name.';
            }
             // Send a user-friendly error response
             return res.status(400).json({ message });
        } else if (err) {
            // A custom error occurred (e.g., file type validation)
             console.error('File Filter Error:', err);
             // Use the message from the custom error object
             return res.status(400).json({ message: err.message || 'Invalid file type.' });
        }
        // No errors, proceed to the next middleware/controller
        next();
    });
};


module.exports = handleUploadMiddleware; // Export the wrapper middleware

