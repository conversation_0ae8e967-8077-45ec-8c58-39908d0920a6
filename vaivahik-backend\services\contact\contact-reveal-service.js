/**
 * Contact Reveal Service
 * Handles smart contact number reveal for calling feature
 * Works across Web, Android, and iOS platforms
 */

const { PrismaClient } = require('@prisma/client');
const securityService = require('../security/contact-security-service');
const prisma = new PrismaClient();

/**
 * Check if user can access contact details of another user
 * @param {string} accessorId - User requesting contact access
 * @param {string} contactOwnerId - User whose contact is being requested
 * @param {string} platform - WEB, ANDROID, IOS
 * @returns {Object} Access result with contact details or error
 */
const checkContactAccess = async (accessorId, contactOwnerId, platform = 'WEB', ipAddress = null, userAgent = null) => {
  try {
    // Don't allow self-access
    if (accessorId === contactOwnerId) {
      return {
        success: false,
        error: 'SELF_ACCESS_DENIED',
        message: 'Cannot access your own contact details'
      };
    }

    // Perform advanced security checks
    const securityCheck = await securityService.performSecurityChecks(accessorId, contactOwnerId, platform);

    if (!securityCheck.allowed) {
      // Log security event
      await securityService.logSecurityEvent({
        userId: accessorId,
        eventType: 'CONTACT_ACCESS_BLOCKED',
        riskScore: securityCheck.riskScore,
        securityFlags: securityCheck.securityFlags,
        details: {
          targetUserId: contactOwnerId,
          reason: securityCheck.reason,
          requiresManualReview: securityCheck.requiresManualReview
        },
        ipAddress,
        userAgent,
        platform
      });

      return {
        success: false,
        error: securityCheck.reason,
        message: getSecurityMessage(securityCheck.reason),
        riskScore: securityCheck.riskScore,
        requiresManualReview: securityCheck.requiresManualReview
      };
    }

    // Get both users with their profiles and subscriptions
    const [accessor, contactOwner] = await Promise.all([
      prisma.user.findUnique({
        where: { id: accessorId },
        include: {
          profile: true,
          subscriptions: {
            where: {
              isActive: true,
              endDate: { gte: new Date() }
            }
          }
        }
      }),
      prisma.user.findUnique({
        where: { id: contactOwnerId },
        include: {
          profile: true,
          targetInteractions: {
            where: {
              userId: accessorId,
              interactionType: 'LIKE'
            }
          }
        }
      })
    ]);

    if (!accessor || !contactOwner) {
      return {
        success: false,
        error: 'USER_NOT_FOUND',
        message: 'One or both users not found'
      };
    }

    // Check if contact owner allows direct calls
    if (!contactOwner.profile?.allowDirectCalls) {
      return {
        success: false,
        error: 'CALLS_DISABLED',
        message: 'This user has disabled direct calls'
      };
    }

    // Check contact reveal preference
    const revealPreference = contactOwner.profile?.contactRevealPreference || 'PREMIUM_ONLY';

    switch (revealPreference) {
      case 'NEVER':
        return {
          success: false,
          error: 'CONTACT_PRIVATE',
          message: 'This user keeps their contact information private'
        };

      case 'PREMIUM_ONLY':
        // Check if accessor has premium subscription
        const hasPremium = accessor.subscriptions.length > 0;
        if (!hasPremium) {
          return {
            success: false,
            error: 'PREMIUM_REQUIRED',
            message: 'Premium subscription required to access contact details',
            upgradeRequired: true
          };
        }
        break;

      case 'MUTUAL_INTEREST':
        // Check if there's mutual interest
        const accessorInterest = await prisma.userInteraction.findFirst({
          where: {
            userId: accessorId,
            targetUserId: contactOwnerId,
            interactionType: 'LIKE'
          }
        });

        const ownerInterest = contactOwner.targetInteractions.length > 0;

        if (!accessorInterest || !ownerInterest) {
          return {
            success: false,
            error: 'MUTUAL_INTEREST_REQUIRED',
            message: 'Mutual interest required to access contact details'
          };
        }
        break;

      case 'ACCEPTED_INTEREST':
        // Check if interest was accepted (using CONTACT_ACCEPTED)
        const acceptedInterest = await prisma.userInteraction.findFirst({
          where: {
            userId: accessorId,
            targetUserId: contactOwnerId,
            interactionType: 'CONTACT_ACCEPTED'
          }
        });

        if (!acceptedInterest) {
          return {
            success: false,
            error: 'ACCEPTED_INTEREST_REQUIRED',
            message: 'Interest must be accepted to access contact details'
          };
        }
        break;
    }

    // Get contact number (prefer family contact, fallback to user phone)
    const contactNumber = contactOwner.profile?.familyContact || contactOwner.phone;

    if (!contactNumber) {
      return {
        success: false,
        error: 'NO_CONTACT_AVAILABLE',
        message: 'No contact number available for this user'
      };
    }

    // Log the contact access
    await logContactAccess({
      accessorId,
      contactOwnerId,
      contactNumber,
      accessReason: revealPreference,
      isPremiumAccess: accessor.subscriptions.length > 0,
      platform
    });

    return {
      success: true,
      contactNumber,
      contactOwnerName: contactOwner.profile?.fullName || 'User',
      accessReason: revealPreference,
      callAvailability: contactOwner.profile?.callAvailability || 'ANYTIME'
    };

  } catch (error) {
    console.error('Error checking contact access:', error);
    return {
      success: false,
      error: 'SERVER_ERROR',
      message: 'Unable to process contact access request'
    };
  }
};

/**
 * Log contact access for security and analytics
 */
const logContactAccess = async ({
  accessorId,
  contactOwnerId,
  contactNumber,
  accessReason,
  isPremiumAccess,
  platform,
  accessType = 'CONTACT_REVEAL'
}) => {
  try {
    await prisma.contactAccessLog.create({
      data: {
        accessorId,
        contactOwnerId,
        contactNumber,
        accessType,
        accessReason,
        isPremiumAccess,
        platform
      }
    });
  } catch (error) {
    console.error('Error logging contact access:', error);
  }
};

/**
 * Get contact access history for a user
 */
const getContactAccessHistory = async (userId, limit = 50) => {
  try {
    const accessHistory = await prisma.contactAccessLog.findMany({
      where: {
        OR: [
          { accessorId: userId },
          { contactOwnerId: userId }
        ]
      },
      include: {
        accessor: {
          include: { profile: true }
        },
        contactOwner: {
          include: { profile: true }
        }
      },
      orderBy: { accessedAt: 'desc' },
      take: limit
    });

    return {
      success: true,
      history: accessHistory
    };
  } catch (error) {
    console.error('Error fetching contact access history:', error);
    return {
      success: false,
      error: 'SERVER_ERROR',
      message: 'Unable to fetch contact access history'
    };
  }
};

/**
 * Update contact privacy settings
 */
const updateContactPrivacySettings = async (userId, settings) => {
  try {
    const updatedProfile = await prisma.profile.update({
      where: { userId },
      data: {
        allowDirectCalls: settings.allowDirectCalls,
        contactRevealPreference: settings.contactRevealPreference,
        requireMutualInterest: settings.requireMutualInterest,
        callAvailability: settings.callAvailability
      }
    });

    return {
      success: true,
      settings: {
        allowDirectCalls: updatedProfile.allowDirectCalls,
        contactRevealPreference: updatedProfile.contactRevealPreference,
        requireMutualInterest: updatedProfile.requireMutualInterest,
        callAvailability: updatedProfile.callAvailability
      }
    };
  } catch (error) {
    console.error('Error updating contact privacy settings:', error);
    return {
      success: false,
      error: 'SERVER_ERROR',
      message: 'Unable to update privacy settings'
    };
  }
};

/**
 * Generate native dialer URL for different platforms
 */
const generateDialerUrl = (contactNumber, platform) => {
  // Clean the contact number
  const cleanNumber = contactNumber.replace(/[^\d+]/g, '');

  switch (platform.toUpperCase()) {
    case 'ANDROID':
    case 'IOS':
      return `tel:${cleanNumber}`;
    case 'WEB':
      return `tel:${cleanNumber}`;
    default:
      return `tel:${cleanNumber}`;
  }
};

/**
 * Get security message for blocked access
 */
const getSecurityMessage = (reason) => {
  switch (reason) {
    case 'HIGH_RISK_PROFILE':
      return 'Access denied due to security concerns. Please contact support if you believe this is an error.';
    case 'INCOMPLETE_PROFILE':
      return 'Please complete your profile and verify your documents to access contact details.';
    case 'NO_DOCUMENT_VERIFICATION':
      return 'Document verification required to access contact information.';
    case 'NEW_ACCOUNT':
      return 'Account must be at least 7 days old to access contact details.';
    case 'HIGH_CONTACT_ACCESS_FREQUENCY':
      return 'Daily contact access limit exceeded. Please try again tomorrow.';
    case 'SUSPICIOUS_INTERACTION_PATTERN':
      return 'Unusual activity detected. Please contact support for assistance.';
    case 'SUSPICIOUS_PHONE_PATTERN':
      return 'Please verify your phone number to access contact details.';
    case 'SUSPICIOUS_EMAIL_DOMAIN':
      return 'Please use a valid email address to access contact details.';
    case 'GEOGRAPHIC_INCONSISTENCY':
      return 'Location verification required to access contact information.';
    case 'SUSPICIOUS_BEHAVIOR':
      return 'Account under review for suspicious activity. Contact support for assistance.';
    case 'SECURITY_CHECK_FAILED':
      return 'Security verification failed. Please try again later.';
    default:
      return 'Access denied for security reasons. Please contact support.';
  }
};

/**
 * Get contact reveal statistics for admin
 */
const getContactRevealStats = async (startDate, endDate) => {
  try {
    const stats = await prisma.contactAccessLog.groupBy({
      by: ['platform', 'accessReason'],
      where: {
        accessedAt: {
          gte: startDate,
          lte: endDate
        }
      },
      _count: {
        id: true
      }
    });

    return {
      success: true,
      stats
    };
  } catch (error) {
    console.error('Error fetching contact reveal stats:', error);
    return {
      success: false,
      error: 'SERVER_ERROR',
      message: 'Unable to fetch statistics'
    };
  }
};

module.exports = {
  checkContactAccess,
  logContactAccess,
  getContactAccessHistory,
  updateContactPrivacySettings,
  generateDialerUrl,
  getContactRevealStats,
  getSecurityMessage
};
