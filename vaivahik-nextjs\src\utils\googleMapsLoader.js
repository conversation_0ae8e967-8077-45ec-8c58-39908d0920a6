/**
 * Google Maps API Loader
 *
 * This utility loads the Google Maps API script dynamically and provides
 * a promise-based interface to know when the API is ready to use.
 * It also provides helper functions for working with Places Autocomplete.
 */

let googleMapsPromise = null;

/**
 * Loads the Google Maps API script
 * @returns {Promise} A promise that resolves when the API is loaded
 */
export const loadGoogleMapsApi = () => {
  if (googleMapsPromise) {
    return googleMapsPromise;
  }

  googleMapsPromise = new Promise((resolve, reject) => {
    // Check if the API is already loaded
    if (window.google && window.google.maps) {
      resolve(window.google.maps);
      return;
    }

    // Create a callback function that will be called when the script loads
    const callbackName = `googleMapsApiCallback_${Math.round(Math.random() * 1000000)}`;
    window[callbackName] = () => {
      resolve(window.google.maps);
      delete window[callbackName];
    };

    // Create the script element
    const script = document.createElement('script');
    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=${callbackName}`;
    script.async = true;
    script.defer = true;
    script.onerror = (error) => {
      reject(new Error('Failed to load Google Maps API: ' + error.message));
      delete window[callbackName];
    };

    // Add the script to the document
    document.head.appendChild(script);
  });

  return googleMapsPromise;
};

/**
 * Initialize Google Places Autocomplete on an input element
 * @param {HTMLElement} inputElement - The input element to attach autocomplete to
 * @param {Object} options - Options for the autocomplete
 * @param {Function} onPlaceSelected - Callback when a place is selected
 * @returns {Promise<google.maps.places.Autocomplete>} The autocomplete instance
 */
export const initPlacesAutocomplete = async (inputElement, options = {}, onPlaceSelected) => {
  if (!inputElement) {
    return null;
  }

  try {
    const maps = await loadGoogleMapsApi();

    // Default options for India
    const defaultOptions = {
      types: ['(cities)'],
      componentRestrictions: { country: 'in' }
    };

    const autocomplete = new maps.places.Autocomplete(
      inputElement,
      { ...defaultOptions, ...options }
    );

    if (onPlaceSelected) {
      autocomplete.addListener('place_changed', () => {
        const place = autocomplete.getPlace();
        onPlaceSelected(place);
      });
    }

    return autocomplete;
  } catch (error) {
    console.error('Error initializing Places Autocomplete:', error);
    // Return a mock autocomplete object in case of error
    // This allows the app to continue functioning even if Google Maps fails to load
    return {
      addListener: (event, callback) => {
        // Mock listener that does nothing
        return;
      },
      getPlace: () => {
        // Return empty place object
        return { name: inputElement.value, formatted_address: inputElement.value };
      }
    };
  }
};

/**
 * Get place details from Google Places API
 * @param {string} placeId - The place ID to get details for
 * @returns {Promise<Object>} The place details
 */
export const getPlaceDetails = async (placeId) => {
  if (!placeId) {
    throw new Error('Place ID is required');
  }

  try {
    const maps = await loadGoogleMapsApi();

    return new Promise((resolve, reject) => {
      const placesService = new maps.places.PlacesService(
        document.createElement('div')
      );

      placesService.getDetails(
        {
          placeId,
          fields: ['address_components', 'formatted_address', 'geometry', 'name']
        },
        (place, status) => {
          if (status === maps.places.PlacesServiceStatus.OK) {
            resolve(place);
          } else {
            reject(new Error(`Place details request failed: ${status}`));
          }
        }
      );
    });
  } catch (error) {
    console.error('Error getting place details:', error);
    throw error;
  }
};

/**
 * Extract components from place details
 * @param {Object} place - The place details object
 * @returns {Object} Extracted components (city, state, country, etc.)
 */
export const extractPlaceComponents = (place) => {
  if (!place || !place.address_components) {
    return {};
  }

  const components = {};

  // Map address component types to our component names
  const componentMap = {
    locality: 'city',
    administrative_area_level_1: 'state',
    country: 'country',
    postal_code: 'pincode'
  };

  // Extract components
  place.address_components.forEach(component => {
    component.types.forEach(type => {
      if (componentMap[type]) {
        components[componentMap[type]] = component.long_name;
      }
    });
  });

  return components;
};

export default {
  loadGoogleMapsApi,
  initPlacesAutocomplete,
  getPlaceDetails,
  extractPlaceComponents
};