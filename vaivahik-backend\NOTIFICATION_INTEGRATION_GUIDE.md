# Notification System Integration Guide

This guide explains how to integrate the notification system with your existing Vaivahik Matrimony application.

## 1. Server Initialization

Update your main server file to initialize the WebSocket server and notification services:

```javascript
const express = require('express');
const { initNotificationServices } = require('./services/notification/init-websocket');

// Create Express app
const app = express();

// Set up middleware, routes, etc.
// ...

// Initialize notification services (this creates an HTTP server with WebSocket support)
const server = initNotificationServices(app);

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
```

## 2. Integrating with Existing Routes

### Profile Views

```javascript
// In your profile view route
router.post('/profiles/:id/view', authenticateUser, async (req, res) => {
  try {
    // Your existing logic...
    
    // After recording the profile view, trigger a notification
    const notificationHandler = require('../services/notification/notification-handler');
    
    notificationHandler.triggerNotification('PROFILE_VIEW', {
      profileOwnerId: profile.userId,
      viewerId,
      viewerName: `${viewer.profile.firstName} ${viewer.profile.lastName || ''}`.trim(),
      viewerPhotoUrl: viewer.profile.photos[0]?.url || null
    });
    
    res.json({ success: true });
  } catch (error) {
    console.error('Error recording profile view:', error);
    res.status(500).json({ message: 'Server error' });
  }
});
```

### New Matches

```javascript
// In your matching service
const notificationHandler = require('../services/notification/notification-handler');

// After finding a match
notificationHandler.triggerNotification('NEW_MATCH', {
  userId: user.id,
  matchId: match.id,
  matchName: `${match.profile.firstName} ${match.profile.lastName || ''}`.trim(),
  matchPhotoUrl: match.profile.photos[0]?.url || null,
  matchPercentage: Math.round(matchScore * 100)
});
```

### Interest Received/Accepted

```javascript
// In your interest routes
const notificationHandler = require('../services/notification/notification-handler');

// When someone sends an interest
notificationHandler.triggerNotification('INTEREST_RECEIVED', {
  recipientId: interest.recipientId,
  senderId: interest.senderId,
  senderName: `${sender.profile.firstName} ${sender.profile.lastName || ''}`.trim(),
  senderPhotoUrl: sender.profile.photos[0]?.url || null,
  interestId: interest.id
});

// When someone accepts an interest
notificationHandler.triggerNotification('INTEREST_ACCEPTED', {
  senderId: interest.senderId,
  acceptorId: interest.recipientId,
  acceptorName: `${acceptor.profile.firstName} ${acceptor.profile.lastName || ''}`.trim(),
  acceptorPhotoUrl: acceptor.profile.photos[0]?.url || null
});
```

### New Messages

```javascript
// In your messaging routes
const notificationHandler = require('../services/notification/notification-handler');

// After sending a message
notificationHandler.triggerNotification('NEW_MESSAGE', {
  recipientId: message.recipientId,
  senderId: message.senderId,
  senderName: `${sender.profile.firstName} ${sender.profile.lastName || ''}`.trim(),
  senderPhotoUrl: sender.profile.photos[0]?.url || null,
  messagePreview: messagePreview,
  conversationId: message.conversationId
});
```

### Verification Status

```javascript
// In your verification routes
const notificationHandler = require('../services/notification/notification-handler');

// After approving verification
notificationHandler.triggerNotification('VERIFICATION_STATUS', {
  userId: verificationDoc.userId,
  status: 'APPROVED'
});

// After rejecting verification
notificationHandler.triggerNotification('VERIFICATION_STATUS', {
  userId: verificationDoc.userId,
  status: 'REJECTED',
  reason: rejectionReason
});
```

## 3. Sending Promotional Notifications

### To All Users

```javascript
const notificationHandler = require('../services/notification/notification-handler');

// Send to all users
notificationHandler.triggerSegmentNotification('ALL_USERS', {
  title: 'Special Offer!',
  body: 'Get 50% off on premium membership today!',
  imageUrl: 'https://example.com/offer.jpg',
  targetUrl: '/offers/premium'
});
```

### To Premium Users Only

```javascript
notificationHandler.triggerSegmentNotification('PREMIUM_USERS', {
  title: 'Premium Exclusive',
  body: 'New premium-only feature now available!',
  imageUrl: 'https://example.com/premium-feature.jpg',
  targetUrl: '/features/new'
});
```

### To Free Users Only

```javascript
notificationHandler.triggerSegmentNotification('FREE_USERS', {
  title: 'Upgrade Now',
  body: 'See who viewed your profile with premium!',
  imageUrl: 'https://example.com/upgrade.jpg',
  targetUrl: '/upgrade'
});
```

## 4. Scheduling Notifications

```javascript
const notificationHandler = require('../services/notification/notification-handler');

// Schedule a notification for tomorrow
const tomorrow = new Date();
tomorrow.setDate(tomorrow.getDate() + 1);
tomorrow.setHours(10, 0, 0, 0); // 10:00 AM

notificationHandler.scheduleNotification(
  'SEGMENT',
  {
    title: 'Weekend Special',
    body: 'Limited time offer this weekend!',
    segment: 'ALL_USERS',
    targetUrl: '/weekend-offer'
  },
  tomorrow
);
```

## 5. Frontend Integration

Add the NotificationCenter component to your layout:

```jsx
import NotificationCenter from '@/components/notification/NotificationCenter';

function Layout({ children }) {
  return (
    <>
      <header>
        <nav>
          {/* Other nav items */}
          <NotificationCenter />
        </nav>
      </header>
      <main>{children}</main>
    </>
  );
}
```

## 6. Testing the Integration

1. **Test Profile Views**: View another user's profile and check if they receive a notification
2. **Test Interests**: Send an interest to another user and check if they receive a notification
3. **Test Messages**: Send a message to another user and check if they receive a notification
4. **Test Verification**: Approve/reject a verification request and check if the user receives a notification
5. **Test Promotional Notifications**: Send a promotional notification to a user segment

## 7. Troubleshooting

- **WebSocket Connection Issues**: Check browser console for connection errors
- **Missing Notifications**: Check Redis logs and ensure the pub/sub client is connected
- **FCM Not Working**: Verify Firebase configuration and check FCM logs
- **Database Errors**: Check Prisma logs for any database-related issues

For more detailed information, refer to the documentation in the `services/notification` directory.
