/**
 * Utility functions for authentication and session management
 */
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../pages/api/auth/[...nextauth]';
import { logError } from './errorHandler';

/**
 * Check if the user is authenticated and has the required role
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {string|string[]} requiredRole - Required role(s) for access
 * @returns {Promise<object|boolean>} - Session object if authenticated, false otherwise
 */
export const checkAuth = async (req, res, requiredRole = 'ADMIN') => {
  try {
    // Check for development mode with mock auth header
    if (process.env.NODE_ENV === 'development') {
      // Import mock auth utilities (only in development)
      const { verifyMockToken } = await import('./mockAuth');

      // Check for mock auth header or cookie
      const mockToken = req.headers['x-mock-auth'] ||
                        req.cookies?.mockAuthToken ||
                        req.headers.authorization?.replace('Bearer ', '');

      if (mockToken) {
        // Verify mock token
        const mockUser = verifyMockToken(mockToken);

        if (mockUser) {
          // Check if user has the required role
          if (requiredRole) {
            const userRole = mockUser.role;

            // If requiredRole is an array, check if user's role is in the array
            if (Array.isArray(requiredRole)) {
              if (!requiredRole.includes(userRole)) {
                res.status(403).json({ success: false, message: 'Not authorized' });
                return false;
              }
            }
            // If requiredRole is a string, check if user's role matches
            else if (userRole !== requiredRole) {
              res.status(403).json({ success: false, message: 'Not authorized' });
              return false;
            }
          }

          // Create a session-like object with the mock user
          return {
            user: {
              id: mockUser.sub,
              name: mockUser.name,
              email: mockUser.email,
              role: mockUser.role
            },
            expires: new Date(Date.now() + 3600000).toISOString() // 1 hour from now
          };
        }
      }

      // For development without token, create a default admin session
      // This makes development easier while still having proper auth structure
      console.log('Development mode: Using default admin session');
      return {
        user: {
          id: 'dev-admin',
          name: 'Development Admin',
          email: '<EMAIL>',
          role: 'ADMIN'
        },
        expires: new Date(Date.now() + 3600000).toISOString() // 1 hour from now
      };
    }

    // Production mode: Use real authentication
    // Get the session
    const session = await getServerSession(req, res, authOptions);

    // Check if session exists
    if (!session) {
      res.status(401).json({ success: false, message: 'Not authenticated' });
      return false;
    }

    // Check if session is expired
    const sessionExpiry = new Date(session.expires);
    if (sessionExpiry < new Date()) {
      res.status(401).json({ success: false, message: 'Session expired' });
      return false;
    }

    // Check if user has the required role
    if (requiredRole) {
      const userRole = session.user.role;

      // If requiredRole is an array, check if user's role is in the array
      if (Array.isArray(requiredRole)) {
        if (!requiredRole.includes(userRole)) {
          res.status(403).json({ success: false, message: 'Not authorized' });
          return false;
        }
      }
      // If requiredRole is a string, check if user's role matches
      else if (userRole !== requiredRole) {
        res.status(403).json({ success: false, message: 'Not authorized' });
        return false;
      }
    }

    // User is authenticated and authorized
    return session;
  } catch (error) {
    logError(error, 'Authentication check');
    res.status(500).json({ success: false, message: 'Authentication error' });
    return false;
  }
};

/**
 * Middleware to protect API routes
 * @param {Function} handler - API route handler
 * @param {string|string[]} requiredRole - Required role(s) for access
 * @returns {Function} - Protected handler
 */
export const withAuth = (handler, requiredRole = 'ADMIN') => {
  return async (req, res) => {
    const session = await checkAuth(req, res, requiredRole);

    if (!session) {
      return; // Response already sent by checkAuth
    }

    // Add session to request for use in handler
    req.session = session;

    // Call the original handler
    return handler(req, res);
  };
};

/**
 * Validate JWT token
 * @param {string} token - JWT token
 * @returns {Promise<object|null>} - Decoded token payload or null if invalid
 */
export const validateToken = async (token) => {
  try {
    if (!token) return null;

    // Check for development mode
    if (process.env.NODE_ENV === 'development') {
      // Import mock auth utilities (only in development)
      const { verifyMockToken } = await import('./mockAuth');

      // Verify mock token
      const mockUser = verifyMockToken(token);

      if (mockUser) {
        return {
          userId: mockUser.sub,
          role: mockUser.role,
          exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
        };
      }
    }

    // In production, use real token validation
    // For now, we'll just return a mock payload
    return {
      userId: 'user123',
      role: 'ADMIN',
      exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
    };
  } catch (error) {
    logError(error, 'Token validation');
    return null;
  }
};

/**
 * Refresh JWT token
 * @param {string} refreshToken - Refresh token
 * @returns {Promise<object|null>} - New tokens or null if invalid
 */
export const refreshToken = async (refreshToken) => {
  try {
    if (!refreshToken) return null;

    // In development mode, we'll generate a new mock token
    if (process.env.NODE_ENV === 'development') {
      // For mock auth, we'll just create a new token with the same user info
      // In a real implementation, you would verify the refresh token

      // The refresh token format is 'refresh-{userId}'
      const userId = refreshToken.startsWith('refresh-')
        ? refreshToken.substring(8)
        : null;

      if (userId) {
        // Import mock auth utilities (only in development)
        const { getMockUserById } = await import('./mockAuth');

        // Get user by ID
        const user = getMockUserById(userId);

        if (user) {
          // Generate a new token
          const { authenticateMockUser } = await import('./mockAuth');
          const authResult = authenticateMockUser(user.email, 'admin123'); // Password doesn't matter here

          if (authResult) {
            return {
              accessToken: authResult.token,
              refreshToken: authResult.refreshToken,
              expiresIn: authResult.expiresIn,
            };
          }
        }
      }
    }

    // In production, use real token refresh
    // For now, we'll just return mock tokens
    return {
      accessToken: 'new-access-token',
      refreshToken: 'new-refresh-token',
      expiresIn: 3600, // 1 hour
    };
  } catch (error) {
    logError(error, 'Token refresh');
    return null;
  }
};

/**
 * Get user permissions based on role
 * @param {string} role - User role
 * @returns {object} - User permissions
 */
export const getUserPermissions = (role) => {
  // Define permissions for each role
  const permissions = {
    ADMIN: {
      users: ['create', 'read', 'update', 'delete'],
      preferences: ['create', 'read', 'update', 'delete'],
      settings: ['create', 'read', 'update', 'delete'],
      reports: ['create', 'read', 'update', 'delete'],
    },
    MODERATOR: {
      users: ['read'],
      preferences: ['read'],
      settings: ['read'],
      reports: ['read', 'update'],
    },
    USER: {
      users: ['read'],
      preferences: ['read'],
      settings: ['read'],
      reports: ['read'],
    },
  };

  return permissions[role] || permissions.USER;
};
