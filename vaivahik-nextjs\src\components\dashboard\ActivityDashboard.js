import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Avatar,
  IconButton,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Button,
  Chip,
  Badge,
  Tooltip,
  CircularProgress,
  Snackbar,
  Alert,
  useTheme,
  alpha
} from '@mui/material';
import FavoriteIcon from '@mui/icons-material/Favorite';
import VisibilityIcon from '@mui/icons-material/Visibility';
import MessageIcon from '@mui/icons-material/Message';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';
import DeleteIcon from '@mui/icons-material/Delete';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import NotificationsIcon from '@mui/icons-material/Notifications';
import { getUserActivities, formatActivityTime, getActivityCounts } from '@/services/activityService';
import { getUnreadCount } from '@/services/notificationService';
import { getShortlistedProfiles, removeFromShortlist, addToShortlist } from '@/services/shortlistService';
import ContactedUsersList from '@/components/contact/ContactedUsersList';
import { useRouter } from 'next/router';

/**
 * Activity Dashboard Component
 *
 * Advanced UI for displaying user activity summary
 */
const ActivityDashboard = () => {
  const theme = useTheme();
  const router = useRouter();

  const [loading, setLoading] = useState(true);
  const [activities, setActivities] = useState({
    interests: [],
    visitors: [],
    messages: [],
    matches: [],
    shortlisted: []
  });

  const [actionLoading, setActionLoading] = useState(null);

  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    action: null
  });

  // Store removed profile for undo functionality
  const [removedProfile, setRemovedProfile] = useState(null);
  const [counts, setCounts] = useState({
    INTEREST_RECEIVED: 0,
    INTEREST_SENT: 0,
    INTEREST_ACCEPTED: 0,
    PROFILE_VISITOR: 0,
    PROFILE_VISITED: 0,
    MESSAGE_RECEIVED: 0,
    MESSAGE_SENT: 0,
    NEW_MATCH: 0,
    SHORTLISTED: 0
  });
  const [notificationCount, setNotificationCount] = useState(0);

  // Load activities on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Load recent interests
        const interestsData = await getUserActivities({
          type: ['INTEREST_RECEIVED', 'INTEREST_ACCEPTED'],
          limit: 5
        });

        // Load recent visitors
        const visitorsData = await getUserActivities({
          type: 'PROFILE_VISITOR',
          limit: 5
        });

        // Load recent messages
        const messagesData = await getUserActivities({
          type: 'MESSAGE_RECEIVED',
          limit: 5
        });

        // Load recent matches
        const matchesData = await getUserActivities({
          type: 'NEW_MATCH',
          limit: 5
        });

        // Load shortlisted profiles
        const shortlistedData = await getShortlistedProfiles({
          limit: 5,
          sortBy: 'createdAt',
          sortOrder: 'desc'
        });

        // Load activity counts
        const countsData = await getActivityCounts();

        // Load notification count
        const notifCount = await getUnreadCount();

        setActivities({
          interests: interestsData.activities || [],
          visitors: visitorsData.activities || [],
          messages: messagesData.activities || [],
          matches: matchesData.activities || [],
          shortlisted: shortlistedData.shortlisted || []
        });

        // Update shortlist count in counts
        if (shortlistedData.pagination) {
          setCounts({
            ...countsData,
            SHORTLISTED: shortlistedData.pagination.totalCount || 0
          });
        } else {
          setCounts(countsData);
        }

        setNotificationCount(notifCount);
      } catch (error) {
        console.error('Error loading activity data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Handle remove from shortlist
  const handleRemoveFromShortlist = async (userId, userName) => {
    try {
      setActionLoading(userId);

      // Store the profile for potential undo
      const profileToRemove = activities.shortlisted.find(profile => profile._id === userId);
      setRemovedProfile(profileToRemove);

      // Update local state immediately (optimistic update)
      setActivities(prev => ({
        ...prev,
        shortlisted: prev.shortlisted.filter(profile => profile._id !== userId)
      }));

      // Update count
      setCounts(prev => ({
        ...prev,
        SHORTLISTED: Math.max(0, (prev.SHORTLISTED || 0) - 1)
      }));

      // Call API to remove from shortlist
      await removeFromShortlist(userId);

      // Show snackbar with undo option
      setSnackbar({
        open: true,
        message: `${userName || 'Profile'} removed from shortlist`,
        action: () => handleUndoRemove(profileToRemove)
      });

    } catch (error) {
      console.error('Error removing profile from shortlist:', error);

      // Show error message
      setSnackbar({
        open: true,
        message: 'Failed to remove profile from shortlist',
        action: null
      });

      // Revert the optimistic update
      if (removedProfile) {
        setActivities(prev => ({
          ...prev,
          shortlisted: [...prev.shortlisted, removedProfile]
        }));

        setCounts(prev => ({
          ...prev,
          SHORTLISTED: prev.SHORTLISTED + 1
        }));
      }
    } finally {
      setActionLoading(null);
    }
  };

  // Handle undo remove
  const handleUndoRemove = async (profile) => {
    if (!profile) return;

    try {
      // Add back to shortlist in API
      await addToShortlist(profile._id, profile.notes || '');

      // Update local state
      setActivities(prev => ({
        ...prev,
        shortlisted: [...prev.shortlisted, profile]
      }));

      // Update count
      setCounts(prev => ({
        ...prev,
        SHORTLISTED: prev.SHORTLISTED + 1
      }));

      // Clear removed profile
      setRemovedProfile(null);

      // Show success message
      setSnackbar({
        open: true,
        message: 'Profile added back to shortlist',
        action: null
      });
    } catch (error) {
      console.error('Error adding profile back to shortlist:', error);

      // Show error message
      setSnackbar({
        open: true,
        message: 'Failed to add profile back to shortlist',
        action: null
      });
    }
  };

  // Handle close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({
      ...prev,
      open: false
    }));
  };

  // Activity summary cards
  const activitySummaryCards = [
    {
      title: 'Interests',
      icon: <FavoriteIcon fontSize="large" sx={{ color: theme.palette.error.main }} />,
      received: counts.INTEREST_RECEIVED || 0,
      sent: counts.INTEREST_SENT || 0,
      accepted: counts.INTEREST_ACCEPTED || 0,
      color: theme.palette.error.main,
      route: '/interests'
    },
    {
      title: 'Profile Visits',
      icon: <VisibilityIcon fontSize="large" sx={{ color: theme.palette.primary.main }} />,
      received: counts.PROFILE_VISITOR || 0,
      sent: counts.PROFILE_VISITED || 0,
      color: theme.palette.primary.main,
      route: '/visitors'
    },
    {
      title: 'Messages',
      icon: <MessageIcon fontSize="large" sx={{ color: theme.palette.info.main }} />,
      received: counts.MESSAGE_RECEIVED || 0,
      sent: counts.MESSAGE_SENT || 0,
      color: theme.palette.info.main,
      route: '/messages'
    },
    {
      title: 'Matches',
      icon: <PersonAddIcon fontSize="large" sx={{ color: theme.palette.secondary.main }} />,
      count: counts.NEW_MATCH || 0,
      color: theme.palette.secondary.main,
      route: '/matches'
    },
    {
      title: 'Shortlisted',
      icon: <BookmarkIcon fontSize="large" sx={{ color: theme.palette.warning.main }} />,
      count: counts.SHORTLISTED || 0,
      color: theme.palette.warning.main,
      route: '/shortlisted'
    }
  ];

  // Render activity summary card
  const renderActivitySummaryCard = (card) => {
    return (
      <Card
        elevation={2}
        sx={{
          height: '100%',
          borderRadius: 2,
          transition: 'transform 0.3s ease, box-shadow 0.3s ease',
          '&:hover': {
            transform: 'translateY(-5px)',
            boxShadow: 6
          }
        }}
      >
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar
              sx={{
                bgcolor: alpha(card.color, 0.1),
                color: card.color,
                width: 56,
                height: 56,
                mr: 2
              }}
            >
              {card.icon}
            </Avatar>
            <Box>
              <Typography variant="h6" component="div">
                {card.title}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Recent Activity
              </Typography>
            </Box>
          </Box>

          <Box sx={{ mb: 2 }}>
            {card.title === 'Matches' ? (
              <Typography variant="h4" component="div" sx={{ color: card.color }}>
                {card.count}
              </Typography>
            ) : (
              <Grid container spacing={2}>
                <Grid item xs={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h5" component="div" sx={{ color: card.color }}>
                      {card.received}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Received
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h5" component="div" sx={{ color: card.color }}>
                      {card.sent}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Sent
                    </Typography>
                  </Box>
                </Grid>
                {card.accepted !== undefined && (
                  <Grid item xs={4}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h5" component="div" sx={{ color: card.color }}>
                        {card.accepted}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Accepted
                      </Typography>
                    </Box>
                  </Grid>
                )}
              </Grid>
            )}
          </Box>

          <Button
            variant="outlined"
            color="inherit"
            endIcon={<ArrowForwardIcon />}
            fullWidth
            onClick={() => router.push(card.route)}
            sx={{
              borderColor: card.color,
              color: card.color,
              '&:hover': {
                borderColor: card.color,
                backgroundColor: alpha(card.color, 0.1)
              }
            }}
          >
            View Details
          </Button>
        </CardContent>
      </Card>
    );
  };

  // Render shortlisted profiles card
  const renderShortlistedProfilesCard = () => {
    return (
      <Card
        elevation={2}
        sx={{
          height: '100%',
          borderRadius: 2,
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <CardHeader
          avatar={
            <Avatar sx={{ bgcolor: theme.palette.warning.main }}>
              <BookmarkIcon />
            </Avatar>
          }
          action={
            <IconButton aria-label="settings" onClick={() => router.push('/shortlisted')}>
              <MoreVertIcon />
            </IconButton>
          }
          title="Shortlisted Profiles"
          titleTypographyProps={{ variant: 'h6' }}
          subheader="Saved for Later"
        />

        <Divider />

        <CardContent sx={{ p: 0, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : activities.shortlisted.length === 0 ? (
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', py: 4, flexGrow: 1 }}>
              <Avatar sx={{ bgcolor: alpha(theme.palette.warning.main, 0.1), color: theme.palette.warning.main, width: 60, height: 60, mb: 2 }}>
                <BookmarkIcon />
              </Avatar>
              <Typography variant="body1" color="text.secondary" align="center">
                No shortlisted profiles yet. When you shortlist profiles, they will appear here.
              </Typography>
            </Box>
          ) : (
            <List sx={{ width: '100%', p: 0 }}>
              {activities.shortlisted.map((profile) => (
                <React.Fragment key={profile._id}>
                  <ListItem
                    alignItems="flex-start"
                    button
                    onClick={() => router.push(`/profile/${profile._id}`)}
                  >
                    <ListItemAvatar>
                      <Avatar
                        src={profile.profilePhoto}
                        alt={profile.name}
                      />
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="subtitle2" component="span">
                            {profile.name}
                          </Typography>
                          {profile.isPremium && (
                            <Chip
                              label="Premium"
                              size="small"
                              color="primary"
                              sx={{ ml: 1, height: 20, fontSize: '0.7rem' }}
                            />
                          )}
                        </Box>
                      }
                      secondary={
                        <>
                          <Typography
                            variant="body2"
                            color="text.primary"
                            component="span"
                          >
                            {profile.age} yrs, {profile.occupation}
                          </Typography>
                          <Typography variant="caption" color="text.secondary" component="div">
                            {profile.city}, {profile.state}
                          </Typography>
                          {profile.notes && (
                            <Typography variant="caption" color="text.secondary" component="div" sx={{ fontStyle: 'italic' }}>
                              Note: {profile.notes.substring(0, 50)}{profile.notes.length > 50 ? '...' : ''}
                            </Typography>
                          )}
                        </>
                      }
                    />
                    <ListItemSecondaryAction>
                      <Tooltip title="Remove from Shortlist">
                        <IconButton
                          edge="end"
                          aria-label="delete"
                          size="small"
                          color="error"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemoveFromShortlist(profile._id, profile.name);
                          }}
                          disabled={actionLoading === profile._id}
                        >
                          {actionLoading === profile._id ?
                            <CircularProgress size={20} /> :
                            <DeleteIcon fontSize="small" />
                          }
                        </IconButton>
                      </Tooltip>
                    </ListItemSecondaryAction>
                  </ListItem>
                  <Divider variant="inset" component="li" />
                </React.Fragment>
              ))}
            </List>
          )}
        </CardContent>

        <Box sx={{ p: 2, mt: 'auto' }}>
          <Button
            variant="text"
            color="warning"
            endIcon={<ArrowForwardIcon />}
            fullWidth
            onClick={() => router.push('/shortlisted')}
          >
            View All Shortlisted
          </Button>
        </Box>
      </Card>
    );
  };

  // Render activity list card
  const renderActivityListCard = (title, icon, color, activities, emptyMessage, route) => {
    return (
      <Card
        elevation={2}
        sx={{
          height: '100%',
          borderRadius: 2,
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <CardHeader
          avatar={
            <Avatar sx={{ bgcolor: color }}>
              {icon}
            </Avatar>
          }
          action={
            <IconButton aria-label="settings" onClick={() => router.push(route)}>
              <MoreVertIcon />
            </IconButton>
          }
          title={title}
          titleTypographyProps={{ variant: 'h6' }}
          subheader="Recent Activity"
        />

        <Divider />

        <CardContent sx={{ p: 0, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : activities.length === 0 ? (
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', py: 4, flexGrow: 1 }}>
              <Avatar sx={{ bgcolor: alpha(color, 0.1), color: color, width: 60, height: 60, mb: 2 }}>
                {icon}
              </Avatar>
              <Typography variant="body1" color="text.secondary" align="center">
                {emptyMessage}
              </Typography>
            </Box>
          ) : (
            <List sx={{ width: '100%', p: 0 }}>
              {activities.map((activity) => {
                const user = activity.targetUser || {};

                return (
                  <React.Fragment key={activity._id}>
                    <ListItem
                      alignItems="flex-start"
                      button
                      onClick={() => router.push(`/profile/${user._id}`)}
                    >
                      <ListItemAvatar>
                        <Avatar
                          src={user.profilePhoto}
                          alt={user.name}
                        />
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Typography variant="subtitle2" component="span">
                              {user.name}
                            </Typography>
                            {user.isPremium && (
                              <Chip
                                label="Premium"
                                size="small"
                                color="primary"
                                sx={{ ml: 1, height: 20, fontSize: '0.7rem' }}
                              />
                            )}
                          </Box>
                        }
                        secondary={
                          <>
                            <Typography
                              variant="body2"
                              color="text.primary"
                              component="span"
                            >
                              {title === 'Interests' && activity.activityType === 'INTEREST_RECEIVED' && 'Sent you an interest'}
                              {title === 'Interests' && activity.activityType === 'INTEREST_ACCEPTED' && 'Accepted your interest'}
                              {title === 'Profile Visitors' && 'Viewed your profile'}
                              {title === 'Messages' && 'Sent you a message'}
                              {title === 'Matches' && 'New match based on your preferences'}
                            </Typography>
                            <Typography variant="caption" color="text.secondary" component="div">
                              {formatActivityTime(activity.createdAt)}
                            </Typography>
                          </>
                        }
                      />
                      <ListItemSecondaryAction>
                        <IconButton edge="end" aria-label="view" size="small">
                          <ArrowForwardIcon fontSize="small" />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                    <Divider variant="inset" component="li" />
                  </React.Fragment>
                );
              })}
            </List>
          )}
        </CardContent>

        <Box sx={{ p: 2, mt: 'auto' }}>
          <Button
            variant="text"
            color="inherit"
            endIcon={<ArrowForwardIcon />}
            fullWidth
            onClick={() => router.push(route)}
            sx={{ color }}
          >
            View All
          </Button>
        </Box>
      </Card>
    );
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" gutterBottom>
          Activity Dashboard
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Track your interactions and stay updated with your activity
        </Typography>
      </Box>

      {/* Notification Summary */}
      <Card
        elevation={2}
        sx={{
          mb: 4,
          borderRadius: 2,
          background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.secondary.main} 90%)`,
          color: 'white'
        }}
      >
        <CardContent sx={{ p: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={8}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Badge badgeContent={notificationCount} color="error" max={99}>
                  <NotificationsIcon sx={{ fontSize: 40, mr: 2 }} />
                </Badge>
                <Box>
                  <Typography variant="h6" component="div">
                    You have {notificationCount} unread notifications
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.8 }}>
                    Stay updated with your latest activities and interactions
                  </Typography>
                </Box>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4} sx={{ textAlign: { xs: 'left', sm: 'right' } }}>
              <Button
                variant="contained"
                color="inherit"
                sx={{
                  bgcolor: 'white',
                  color: theme.palette.primary.main,
                  '&:hover': {
                    bgcolor: alpha('white', 0.9)
                  }
                }}
                onClick={() => router.push('/notifications')}
              >
                View Notifications
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Activity Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {activitySummaryCards.map((card, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            {renderActivitySummaryCard(card)}
          </Grid>
        ))}
      </Grid>

      {/* Activity Lists */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          {renderActivityListCard(
            'Interests',
            <FavoriteIcon />,
            theme.palette.error.main,
            activities.interests,
            'No interests yet. When someone expresses interest in your profile, it will appear here.',
            '/interests'
          )}
        </Grid>
        <Grid item xs={12} md={6}>
          {renderActivityListCard(
            'Profile Visitors',
            <VisibilityIcon />,
            theme.palette.primary.main,
            activities.visitors,
            'No profile visitors yet. When someone views your profile, it will appear here.',
            '/visitors'
          )}
        </Grid>
        <Grid item xs={12} md={6}>
          {renderActivityListCard(
            'Messages',
            <MessageIcon />,
            theme.palette.info.main,
            activities.messages,
            'No messages yet. When someone sends you a message, it will appear here.',
            '/messages'
          )}
        </Grid>
        <Grid item xs={12} md={6}>
          {renderActivityListCard(
            'Matches',
            <PersonAddIcon />,
            theme.palette.secondary.main,
            activities.matches,
            'No matches yet. When we find profiles matching your preferences, they will appear here.',
            '/matches'
          )}
        </Grid>
        <Grid item xs={12} md={6}>
          {renderShortlistedProfilesCard()}
        </Grid>

        {/* Contact History Section */}
        <Grid item xs={12}>
          <ContactedUsersList
            userId={null} // Will use current user from context
            maxItems={10}
            showTitle={true}
          />
        </Grid>
      </Grid>

      {/* Snackbar for notifications with undo */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        message={snackbar.message}
        action={
          snackbar.action ? (
            <Button
              color="secondary"
              size="small"
              onClick={() => {
                snackbar.action();
                handleCloseSnackbar();
              }}
            >
              UNDO
            </Button>
          ) : undefined
        }
        sx={{
          '& .MuiSnackbarContent-root': {
            bgcolor: theme.palette.background.paper,
            color: theme.palette.text.primary,
            boxShadow: theme.shadows[3]
          }
        }}
      />
    </Box>
  );
};

export default ActivityDashboard;
