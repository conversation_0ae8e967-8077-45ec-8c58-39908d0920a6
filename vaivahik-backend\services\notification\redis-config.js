/**
 * Redis Configuration for Notification System
 *
 * This file adapts the notification system to use the existing Redis setup.
 */
const redisClient = require('../../src/config/redisClient'); // Import your existing Redis client
require('dotenv').config();

// Create a separate client for pub/sub (recommended practice to avoid blocking)
// We clone the existing client to ensure we use the same configuration
const redisPubSub = redisClient.duplicate();

// Log when the pub/sub client connects
redisPubSub.on('connect', () => {
  console.log('Redis PubSub client connected for notifications');
});

redisPubSub.on('error', (err) => {
  console.error('Redis PubSub client error:', err);
});

// Export both clients
module.exports = {
  redisClient,
  redisPubSub
};
