import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  // Check if user is authenticated and is an admin
  const session = await getServerSession(req, res, authOptions);
  
  if (!session || !session.user || session.user.role !== 'ADMIN') {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getReferralPrograms(req, res);
      case 'POST':
        return await createReferralProgram(req, res, session.user.id);
      case 'PUT':
        return await updateReferralProgram(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Error in referral-programs API:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
}

// Get all referral programs
async function getReferralPrograms(req, res) {
  try {
    const programs = await prisma.referralProgram.findMany({
      orderBy: [
        { status: 'asc' },
        { startDate: 'desc' }
      ],
      include: {
        _count: {
          select: { referrals: true }
        }
      }
    });

    // Enhance programs with additional stats
    const enhancedPrograms = await Promise.all(programs.map(async (program) => {
      // Get total referrals
      const totalReferrals = program._count.referrals;
      
      // Get conversion rate
      const completedReferrals = await prisma.referral.count({
        where: {
          referralProgramId: program.id,
          status: 'completed'
        }
      });
      
      const conversionRate = totalReferrals > 0 
        ? Math.round((completedReferrals / totalReferrals) * 100) 
        : 0;
      
      // Get total rewards given
      const totalRewards = await prisma.referralReward.aggregate({
        where: {
          referral: {
            referralProgramId: program.id
          },
          rewardType: 'cash'
        },
        _sum: {
          rewardAmount: true
        }
      });
      
      return {
        ...program,
        totalReferrals,
        conversionRate,
        totalRewardsGiven: totalRewards._sum?.rewardAmount || 0
      };
    }));

    return res.status(200).json({ success: true, programs: enhancedPrograms });
  } catch (error) {
    console.error('Error fetching referral programs:', error);
    return res.status(500).json({ success: false, message: 'Failed to fetch referral programs' });
  }
}

// Create a new referral program
async function createReferralProgram(req, res, adminId) {
  try {
    const {
      name,
      description,
      status,
      startDate,
      endDate,
      referrerRewardType,
      referrerRewardAmount,
      refereeRewardType,
      refereeRewardAmount,
      maxReferralsPerUser,
      conversionRequirement,
      termsAndConditions
    } = req.body;

    // Validate required fields
    if (!name || !startDate || !referrerRewardType || !referrerRewardAmount || !refereeRewardType || !refereeRewardAmount) {
      return res.status(400).json({ success: false, message: 'Missing required fields' });
    }

    // Create the program
    const program = await prisma.referralProgram.create({
      data: {
        name,
        description,
        status: status || 'active',
        startDate: new Date(startDate),
        endDate: endDate ? new Date(endDate) : null,
        referrerRewardType,
        referrerRewardAmount: parseFloat(referrerRewardAmount),
        refereeRewardType,
        refereeRewardAmount: parseFloat(refereeRewardAmount),
        maxReferralsPerUser: maxReferralsPerUser ? parseInt(maxReferralsPerUser) : null,
        conversionRequirement: conversionRequirement || 'none',
        termsAndConditions,
        createdBy: adminId
      }
    });

    return res.status(201).json({ success: true, program });
  } catch (error) {
    console.error('Error creating referral program:', error);
    return res.status(500).json({ success: false, message: 'Failed to create referral program' });
  }
}

// Update an existing referral program
async function updateReferralProgram(req, res) {
  try {
    const { id, ...updateData } = req.body;

    if (!id) {
      return res.status(400).json({ success: false, message: 'Program ID is required' });
    }

    // Prepare date fields
    if (updateData.startDate) {
      updateData.startDate = new Date(updateData.startDate);
    }
    
    if (updateData.endDate) {
      updateData.endDate = new Date(updateData.endDate);
    } else if (updateData.endDate === '') {
      updateData.endDate = null;
    }

    // Convert numeric fields
    if (updateData.referrerRewardAmount) {
      updateData.referrerRewardAmount = parseFloat(updateData.referrerRewardAmount);
    }
    
    if (updateData.refereeRewardAmount) {
      updateData.refereeRewardAmount = parseFloat(updateData.refereeRewardAmount);
    }
    
    if (updateData.maxReferralsPerUser) {
      updateData.maxReferralsPerUser = parseInt(updateData.maxReferralsPerUser);
    } else if (updateData.maxReferralsPerUser === '') {
      updateData.maxReferralsPerUser = null;
    }

    // Update the program
    const program = await prisma.referralProgram.update({
      where: { id },
      data: {
        ...updateData,
        updatedAt: new Date()
      }
    });

    return res.status(200).json({ success: true, program });
  } catch (error) {
    console.error('Error updating referral program:', error);
    return res.status(500).json({ success: false, message: 'Failed to update referral program' });
  }
}
