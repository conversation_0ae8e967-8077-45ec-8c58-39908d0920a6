const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { authenticateToken } = require('../../middleware/auth.middleware');

/**
 * @route POST /api/analytics/feedback
 * @desc Submit user feedback after interactions
 * @access Private
 *
 * Request body:
 * {
 *   targetUserId: string,
 *   feedbackType: "AFTER_VIEW" | "AFTER_CONTACT" | "AFTER_CHAT" | etc.,
 *   rating: number,
 *   comments?: string,
 *   continueInterest: boolean,
 *   factorsLiked?: string[],
 *   factorsDisliked?: string[]
 * }
 */
router.post('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get request data
    const {
      targetUserId,
      feedbackType,
      rating,
      comments,
      continueInterest,
      factorsLiked,
      factorsDisliked
    } = req.body;

    // Validate required fields
    if (!targetUserId || !feedbackType || rating === undefined) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: targetUserId, feedbackType, and rating are required'
      });
    }

    // Validate rating
    if (rating < 1 || rating > 5) {
      return res.status(400).json({
        success: false,
        message: 'Rating must be between 1 and 5'
      });
    }

    // Create the feedback record
    const feedback = await prisma.userFeedback.create({
      data: {
        userId,
        targetUserId,
        feedbackType,
        rating,
        comments: comments || null,
        continueInterest: continueInterest || false,
        factorsLiked: factorsLiked ? factorsLiked : null,
        factorsDisliked: factorsDisliked ? factorsDisliked : null,
      },
    });

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Feedback submitted successfully',
      feedbackId: feedback.id
    });

  } catch (error) {
    console.error('Error submitting feedback:', error);
    return res.status(500).json({
      success: false,
      message: 'Error submitting feedback',
      error: error.message
    });
  }
});

/**
 * @route GET /api/analytics/feedback/summary/:userId
 * @desc Get summary of feedback for a user
 * @access Private (Admin or Self)
 */
router.get('/summary/:userId', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;

    // Check if user is admin or requesting their own data
    if (req.user.id !== userId && req.user.role !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Unauthorized to access this data'
      });
    }

    // Get feedback summary
    const feedbackCount = await prisma.userFeedback.count({
      where: { targetUserId: userId }
    });

    const averageRating = await prisma.userFeedback.aggregate({
      where: { targetUserId: userId },
      _avg: { rating: true }
    });

    // Return summary
    return res.status(200).json({
      success: true,
      data: {
        feedbackCount,
        averageRating: averageRating._avg.rating || 0,
      }
    });

  } catch (error) {
    console.error('Error getting feedback summary:', error);
    return res.status(500).json({
      success: false,
      message: 'Error getting feedback summary',
      error: error.message
    });
  }
});

module.exports = router;
