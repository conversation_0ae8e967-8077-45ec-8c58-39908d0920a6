/**
 * Authentication Context
 *
 * This context provides authentication state and functions throughout the application.
 * It handles:
 * - User authentication state
 * - Login/logout functionality
 * - Token refresh
 * - Protected routes
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import authService from '@/services/authService';
import { isUsingRealBackend } from '@/utils/featureFlags';

// Create context
const AuthContext = createContext();

// Auth provider component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const router = useRouter();

  // Initialize auth state
  useEffect(() => {
    // Set loading to false immediately on the server to prevent hydration issues
    if (typeof window === 'undefined') {
      setLoading(false);
      return;
    }

    const initAuth = async () => {
      try {
        // Check if user is already logged in
        const currentUser = authService.getCurrentUser ? authService.getCurrentUser() : null;
        const isAuthenticated = authService.isAuthenticated ? authService.isAuthenticated() : false;

        if (currentUser && isAuthenticated) {
          setUser(currentUser);
        }
      } catch (err) {
        console.error('Auth initialization error:', err);
        setError('Failed to initialize authentication');
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  // Login function
  const login = async (credentials) => {
    try {
      setLoading(true);
      setError(null);

      const result = await authService.login(credentials);

      if (result.success) {
        setUser(result.user);
        return { success: true, user: result.user, source: result.source };
      } else {
        throw new Error(result.message || 'Login failed');
      }
    } catch (err) {
      console.error('Login error:', err);
      setError(err.message || 'Login failed');
      return { success: false, error: err.message || 'Login failed' };
    } finally {
      setLoading(false);
    }
  };

  // Register function
  const register = async (userData) => {
    try {
      setLoading(true);
      setError(null);

      const result = await authService.register(userData);

      if (result.success) {
        return { success: true, user: result.user, source: result.source };
      } else {
        throw new Error(result.message || 'Registration failed');
      }
    } catch (err) {
      console.error('Registration error:', err);
      setError(err.message || 'Registration failed');
      return { success: false, error: err.message || 'Registration failed' };
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      setLoading(true);

      await authService.logout();

      setUser(null);

      // Redirect to home page
      router.push('/');

      return { success: true };
    } catch (err) {
      console.error('Logout error:', err);
      return { success: false, error: err.message || 'Logout failed' };
    } finally {
      setLoading(false);
    }
  };

  // Update user data
  const updateUser = (userData) => {
    if (userData) {
      setUser(prevUser => ({ ...prevUser, ...userData }));

      // Check if we're in a browser environment
      if (typeof window !== 'undefined' && authService.getCurrentUser) {
        try {
          // Update user data in storage
          const currentUser = authService.getCurrentUser();
          if (currentUser) {
            const updatedUser = { ...currentUser, ...userData };
            const userStorageKey = authService.USER_STORAGE_KEY || 'vaivahik_user';
            localStorage.setItem(userStorageKey, JSON.stringify(updatedUser));
            localStorage.setItem('user', JSON.stringify(updatedUser)); // For backward compatibility
          }
        } catch (err) {
          console.error('Error updating user data in storage:', err);
        }
      }
    }
  };

  // Check if user is authenticated
  const isAuthenticated = () => {
    if (typeof window === 'undefined' || !authService.isAuthenticated) {
      return false;
    }
    return authService.isAuthenticated();
  };

  // Get data source (mock or real)
  const getDataSource = () => {
    return isUsingRealBackend() ? 'real' : 'mock';
  };

  // Context value
  const value = {
    user,
    loading,
    error,
    login,
    register,
    logout,
    updateUser,
    isAuthenticated,
    dataSource: getDataSource()
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
