/**
 * Error Resolution Database
 * 
 * This module contains a database of common errors and their resolution steps.
 * It provides functions to get resolution steps for specific error codes or messages.
 */

/**
 * Database of common errors and their resolution steps
 * Each entry contains:
 * - code: Error code (optional)
 * - message: Error message pattern (used for matching)
 * - type: Error type (error, warning, info)
 * - steps: Array of steps to resolve the error
 * - helpUrl: URL to more detailed help (optional)
 */
const errorDatabase = [
  // Authentication Errors
  {
    code: 'AUTH_001',
    message: 'Invalid credentials',
    type: 'error',
    steps: [
      'Check if you entered the correct email/phone and password',
      'Make sure Caps Lock is not enabled',
      'Try resetting your password using the "Forgot Password" link',
      'If you recently changed your password, try using the new password'
    ],
    helpUrl: '/help/authentication'
  },
  {
    code: 'AUTH_002',
    message: 'Session expired',
    type: 'warning',
    steps: [
      'Your login session has expired for security reasons',
      'Please log in again to continue',
      'We recommend not leaving the app inactive for extended periods'
    ]
  },
  {
    code: 'AUTH_003',
    message: 'Account locked',
    type: 'error',
    steps: [
      'Your account has been temporarily locked due to multiple failed login attempts',
      'Wait 30 minutes before trying again',
      'Use the "Forgot Password" option to reset your password',
      'Contact support if you believe this is an error'
    ],
    helpUrl: '/help/account-locked'
  },
  
  // Profile Errors
  {
    code: 'PROF_001',
    message: 'Profile update failed',
    type: 'error',
    steps: [
      'Check your internet connection',
      'Ensure all required fields are filled correctly',
      'Try refreshing the page and submitting again',
      'If the problem persists, try again later'
    ]
  },
  {
    code: 'PROF_002',
    message: 'Photo upload failed',
    type: 'error',
    steps: [
      'Ensure your photo is in JPG, PNG, or HEIC format',
      'Check that the file size is under 5MB',
      'Try a different photo if the problem persists',
      'Check your internet connection'
    ]
  },
  {
    code: 'PROF_003',
    message: 'Cannot change locked field',
    type: 'warning',
    steps: [
      'Some profile fields (gender, date of birth, birth time, birth place) cannot be changed once set',
      'These fields are critical for matching and kundali generation',
      'If you need to correct this information, please contact support'
    ],
    helpUrl: '/help/locked-fields'
  },
  
  // Payment Errors
  {
    code: 'PAY_001',
    message: 'Payment failed',
    type: 'error',
    steps: [
      'Check if your card has sufficient balance',
      'Verify that your card details are entered correctly',
      'Ensure your card is not expired or blocked for online transactions',
      'Try a different payment method',
      'Contact your bank if the problem persists'
    ]
  },
  {
    code: 'PAY_002',
    message: 'Subscription activation failed',
    type: 'error',
    steps: [
      'Your payment was successful, but subscription activation failed',
      'This is usually a temporary issue',
      'Please wait a few minutes and refresh the page',
      'If your subscription is not activated within 15 minutes, contact support'
    ],
    helpUrl: '/help/subscription-issues'
  },
  
  // Network Errors
  {
    code: 'NET_001',
    message: 'Network error',
    type: 'error',
    steps: [
      'Check your internet connection',
      'Try refreshing the page',
      'If you\'re on mobile data, try switching to Wi-Fi',
      'If the problem persists, the server might be temporarily down'
    ]
  },
  {
    code: 'NET_002',
    message: 'Request timeout',
    type: 'warning',
    steps: [
      'The server is taking too long to respond',
      'Check your internet connection',
      'Try again later when the server load might be lower',
      'If this happens repeatedly, please report the issue'
    ]
  },
  
  // Validation Errors
  {
    code: 'VAL_001',
    message: 'Invalid date format',
    type: 'warning',
    steps: [
      'Please enter the date in DD/MM/YYYY format',
      'Ensure the date is valid (e.g., not 30/02/2023)',
      'For birth dates, you must be at least 18 years old (females) or 21 years old (males)'
    ]
  },
  {
    code: 'VAL_002',
    message: 'Invalid phone number',
    type: 'warning',
    steps: [
      'Enter a 10-digit mobile number without country code',
      'Do not include spaces or special characters',
      'Ensure you\'re using a valid Indian mobile number'
    ]
  },
  {
    code: 'VAL_003',
    message: 'Invalid email format',
    type: 'warning',
    steps: [
      'Enter a valid email address (e.g., <EMAIL>)',
      'Check for typos or missing characters',
      'Ensure there are no spaces in the email address'
    ]
  },
  
  // Permission Errors
  {
    code: 'PERM_001',
    message: 'Access denied',
    type: 'error',
    steps: [
      'You don\'t have permission to access this feature',
      'This feature might require a premium subscription',
      'If you believe this is an error, please contact support'
    ],
    helpUrl: '/help/premium-features'
  },
  
  // Generic Errors
  {
    code: 'GEN_001',
    message: 'Something went wrong',
    type: 'error',
    steps: [
      'This is an unexpected error',
      'Try refreshing the page',
      'Clear your browser cache and cookies',
      'If the problem persists, please contact support with the error code'
    ]
  }
];

/**
 * Get resolution steps for a specific error code
 * @param {string} errorCode - The error code
 * @returns {Object|null} Error resolution object or null if not found
 */
export const getResolutionByCode = (errorCode) => {
  return errorDatabase.find(error => error.code === errorCode) || null;
};

/**
 * Get resolution steps by matching error message
 * @param {string} errorMessage - The error message
 * @returns {Object|null} Error resolution object or null if not found
 */
export const getResolutionByMessage = (errorMessage) => {
  // Try to find an exact match first
  let resolution = errorDatabase.find(error => 
    error.message.toLowerCase() === errorMessage.toLowerCase()
  );
  
  // If no exact match, try to find a partial match
  if (!resolution) {
    resolution = errorDatabase.find(error => 
      errorMessage.toLowerCase().includes(error.message.toLowerCase())
    );
  }
  
  return resolution || null;
};

/**
 * Get resolution steps for a specific error
 * @param {string} errorCodeOrMessage - Error code or message
 * @returns {Object|null} Error resolution object or null if not found
 */
export const getErrorResolution = (errorCodeOrMessage) => {
  // Try to find by code first
  let resolution = getResolutionByCode(errorCodeOrMessage);
  
  // If not found by code, try to find by message
  if (!resolution) {
    resolution = getResolutionByMessage(errorCodeOrMessage);
  }
  
  // If still not found, return the generic error
  if (!resolution) {
    resolution = getResolutionByCode('GEN_001');
  }
  
  return resolution;
};

export default errorDatabase;
