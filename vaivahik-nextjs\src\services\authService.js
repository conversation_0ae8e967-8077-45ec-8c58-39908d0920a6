/**
 * Authentication Service
 *
 * This service handles all authentication-related functionality including:
 * - Login/logout
 * - Token management
 * - Session persistence
 * - Mock/real data switching
 */

import axios from 'axios';
import Cookies from 'js-cookie';
import { AUTH_ENDPOINTS, API_TIMEOUT } from '@/config/apiConfig';
import { isUsingRealBackend } from '@/utils/featureFlags';

// Constants
const TOKEN_COOKIE_NAME = 'auth_token';
const REFRESH_TOKEN_COOKIE_NAME = 'refresh_token';
const USER_STORAGE_KEY = 'vaivahik_user';
const TOKEN_EXPIRY = 7; // days

// Create an axios instance for auth requests
const authApi = axios.create({
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * Register a new user
 * @param {Object} userData - User registration data
 * @returns {Promise} Promise with registration result
 */
export const register = async (userData) => {
  try {
    const response = await authApi.post(AUTH_ENDPOINTS.REGISTER, userData);

    return response.data;
  } catch (error) {
    console.error('Registration error:', error.response?.data || error.message);
    throw error;
  }
};

/**
 * Login user
 * @param {Object} credentials - Login credentials
 * @param {string} credentials.emailOrPhone - User email or phone
 * @param {string} credentials.password - User password
 * @returns {Promise} Promise with login result including token
 */
export const login = async (credentials) => {
  try {
    let response;

    // Determine if we should use real backend or mock data
    if (isUsingRealBackend()) {
      // Call real backend API
      response = await authApi.post(AUTH_ENDPOINTS.LOGIN, credentials);
    } else {
      // Use mock authentication
      response = await axios.post('/api/auth/mock-login', credentials);
    }

    if (response.data.success) {
      // Store tokens in cookies for better security and persistence
      storeAuthData(
        response.data.token,
        response.data.refreshToken,
        response.data.user
      );

      return {
        success: true,
        user: response.data.user,
        source: isUsingRealBackend() ? 'real' : 'mock'
      };
    } else {
      throw new Error(response.data.message || 'Login failed');
    }
  } catch (error) {
    console.error('Login error:', error.response?.data || error.message);
    throw error;
  }
};

// Admin login function is now implemented below with mock data support

/**
 * Logout user
 * @returns {Promise} Promise with logout result
 */
export const logout = async () => {
  try {
    // Get the token
    const token = getAuthToken();

    if (token && isUsingRealBackend()) {
      // Call the logout endpoint for real backend
      try {
        await authApi.post(AUTH_ENDPOINTS.LOGOUT, {}, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
      } catch (apiError) {
        console.error('API logout error:', apiError);
        // Continue with local logout even if API call fails
      }
    }

    // Clear all auth data
    clearAuthData();

    return { success: true };
  } catch (error) {
    // Still clear auth data even if there's an error
    clearAuthData();

    console.error('Logout error:', error.response?.data || error.message);
    return {
      success: true,
      message: 'Logged out locally, but server logout failed.'
    };
  }
};

/**
 * Store authentication data
 * @param {string} token - Auth token
 * @param {string} refreshToken - Refresh token
 * @param {Object} user - User data
 */
export const storeAuthData = (token, refreshToken, user) => {
  // Check if we're in a browser environment
  const isBrowser = typeof window !== 'undefined';

  if (isBrowser) {
    try {
      // Store tokens in cookies for better security and persistence
      Cookies.set(TOKEN_COOKIE_NAME, token, {
        expires: TOKEN_EXPIRY,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict'
      });

      if (refreshToken) {
        Cookies.set(REFRESH_TOKEN_COOKIE_NAME, refreshToken, {
          expires: TOKEN_EXPIRY * 2,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict'
        });
      }

      // Store user data in localStorage
      if (user) {
        localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(user));
      }

      // For backward compatibility
      localStorage.setItem('accessToken', token);
      if (user) localStorage.setItem('user', JSON.stringify(user));
    } catch (storageError) {
      console.warn('Could not store auth data:', storageError);
      // Continue without storing in localStorage
    }
  }
};

/**
 * Clear all authentication data
 */
export const clearAuthData = () => {
  // Check if we're in a browser environment
  if (typeof window !== 'undefined') {
    try {
      // Clear cookies
      Cookies.remove(TOKEN_COOKIE_NAME);
      Cookies.remove(REFRESH_TOKEN_COOKIE_NAME);

      // Clear localStorage
      localStorage.removeItem(USER_STORAGE_KEY);
      localStorage.removeItem('accessToken');
      localStorage.removeItem('user');

      // For mock auth, also clear mock auth data
      Cookies.remove('mockAuthToken');
      localStorage.removeItem('mockAuthUser');
      localStorage.removeItem('mockAuthToken');
    } catch (storageError) {
      console.warn('Could not clear auth data:', storageError);
      // Continue even if localStorage fails
    }
  }
};

// Admin logout function is now implemented below with better error handling

/**
 * Check if user is logged in
 * @returns {boolean} True if user is logged in
 */
export const isLoggedIn = () => {
  if (typeof window === 'undefined') {
    return false;
  }
  // Check both cookie and localStorage for backward compatibility
  return !!(getAuthToken() && getCurrentUser());
};

/**
 * Check if user is authenticated (alias for isLoggedIn for backward compatibility)
 * @returns {boolean} True if user is authenticated
 */
export const isAuthenticated = () => {
  return isLoggedIn();
};

/**
 * Check if admin is logged in
 * @returns {boolean} True if admin is logged in
 */
export const isAdminLoggedIn = () => {
  if (typeof window === 'undefined') {
    return false;
  }
  return !!localStorage.getItem('adminAccessToken');
};

/**
 * Get current user
 * @returns {Object|null} Current user data or null if not logged in
 */
export const getCurrentUser = () => {
  if (typeof window === 'undefined') {
    return null;
  }

  // Try to get user from our preferred storage first
  const userFromPreferred = localStorage.getItem(USER_STORAGE_KEY);
  if (userFromPreferred) {
    try {
      return JSON.parse(userFromPreferred);
    } catch (e) {
      console.error('Error parsing user data:', e);
    }
  }

  // Fall back to legacy storage
  const userFromLegacy = localStorage.getItem('user');
  if (userFromLegacy) {
    try {
      return JSON.parse(userFromLegacy);
    } catch (e) {
      console.error('Error parsing legacy user data:', e);
    }
  }

  return null;
};

/**
 * Get authentication token
 * @returns {string|null} - Auth token or null if not found
 */
export const getAuthToken = () => {
  if (typeof window === 'undefined') {
    return null;
  }

  // Try to get token from cookie first (more secure)
  const tokenFromCookie = Cookies.get(TOKEN_COOKIE_NAME);
  if (tokenFromCookie) {
    return tokenFromCookie;
  }

  // Fall back to localStorage for backward compatibility
  return localStorage.getItem('accessToken');
};

/**
 * Get refresh token
 * @returns {string|null} - Refresh token or null if not found
 */
export const getRefreshToken = () => {
  if (typeof window === 'undefined') {
    return null;
  }

  return Cookies.get(REFRESH_TOKEN_COOKIE_NAME);
};

/**
 * Get current admin
 * @returns {Object} Current admin data
 */
export const getCurrentAdmin = () => {
  if (typeof window === 'undefined') {
    return { name: 'Admin User', role: 'Admin' };
  }

  return {
    name: localStorage.getItem('adminName') || 'Admin User',
    role: localStorage.getItem('adminRole') || 'Admin',
  };
};

/**
 * Verify email
 * @param {string} token - Verification token
 * @returns {Promise} Promise with verification result
 */
export const verifyEmail = async (token) => {
  try {
    const response = await axios.get(`${API_URL}/verify-email/${token}`);
    return response.data;
  } catch (error) {
    console.error('Email verification error:', error.response?.data || error.message);
    throw error;
  }
};

/**
 * Request password reset
 * @param {string} email - User email
 * @returns {Promise} Promise with request result
 */
export const requestPasswordReset = async (email) => {
  try {
    const response = await axios.post(`${API_URL}/forgot-password`, { email }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    return response.data;
  } catch (error) {
    console.error('Password reset request error:', error.response?.data || error.message);
    throw error;
  }
};

/**
 * Reset password
 * @param {string} token - Reset token
 * @param {string} password - New password
 * @returns {Promise} Promise with reset result
 */
export const resetPassword = async (token, password) => {
  try {
    const response = await axios.post(`${API_URL}/reset-password/${token}`, { password }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    return response.data;
  } catch (error) {
    console.error('Password reset error:', error.response?.data || error.message);
    throw error;
  }
};

/**
 * Update user profile
 * @param {Object} userData - Updated user data
 * @returns {Promise} Promise with update result
 */
export const updateProfile = async (userData) => {
  try {
    const token = localStorage.getItem('token');

    const response = await axios.put(`${API_URL}/profile`, userData, {
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      }
    });

    // Update stored user data
    if (response.data.user) {
      localStorage.setItem('user', JSON.stringify(response.data.user));
    }

    return response.data;
  } catch (error) {
    console.error('Profile update error:', error.response?.data || error.message);
    throw error;
  }
};

/**
 * Change password
 * @param {string} currentPassword - Current password
 * @param {string} newPassword - New password
 * @returns {Promise} Promise with change result
 */
export const changePassword = async (currentPassword, newPassword) => {
  try {
    const token = getToken();

    const response = await authApi.put(
      AUTH_ENDPOINTS.CHANGE_PASSWORD,
      { currentPassword, newPassword },
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );

    return response.data;
  } catch (error) {
    console.error('Password change error:', error.response?.data || error.message);
    throw error;
  }
};

/**
 * Get the authentication token (alias for getAuthToken for backward compatibility)
 * @returns {string|null} - Token or null if not authenticated
 */
export const getToken = () => {
  return getAuthToken();
};

/**
 * Get the admin authentication token
 * @returns {string|null} - Token or null if not authenticated
 */
export const getAdminToken = () => {
  if (typeof window === 'undefined') {
    return null;
  }

  return localStorage.getItem('adminAccessToken');
};

/**
 * Refresh authentication token
 * @returns {Promise<Object>} - Refresh result with new tokens
 */
export const refreshAuthToken = async () => {
  try {
    const refreshToken = getRefreshToken();

    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    // Determine if we should use real backend or mock data
    let response;
    if (isUsingRealBackend()) {
      // Call real backend API
      response = await authApi.post(AUTH_ENDPOINTS.REFRESH_TOKEN, { refreshToken });
    } else {
      // Use mock token refresh
      response = await axios.post('/api/auth/mock-refresh-token', { refreshToken });
    }

    if (response.data.success) {
      // Store new tokens
      storeAuthData(
        response.data.token,
        response.data.refreshToken,
        null // Don't update user data
      );

      return {
        success: true,
        token: response.data.token,
        refreshToken: response.data.refreshToken,
        source: isUsingRealBackend() ? 'real' : 'mock'
      };
    } else {
      throw new Error(response.data.message || 'Token refresh failed');
    }
  } catch (error) {
    console.error('Token refresh error:', error);
    // Clear auth data on refresh failure
    clearAuthData();
    throw error;
  }
};

/**
 * Admin login
 * @param {string} email - Admin email
 * @param {string} password - Admin password
 * @returns {Promise} Promise with login result including token
 */
export const adminLogin = async (email, password) => {
  try {
    // Check if we're in a browser environment
    const isBrowser = typeof window !== 'undefined';
    let response;

    // Determine if we should use real backend or mock data
    if (isUsingRealBackend()) {
      // Call real backend API
      response = await authApi.post(AUTH_ENDPOINTS.ADMIN_LOGIN, { email, password });

      // Store token in localStorage (only in browser environment)
      if (response.data.token && isBrowser) {
        try {
          localStorage.setItem('adminAccessToken', response.data.token);
          localStorage.setItem('adminUser', JSON.stringify(response.data.user));

          // Also store in cookies for better security
          Cookies.set('adminToken', response.data.token, {
            expires: TOKEN_EXPIRY,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict'
          });
        } catch (storageError) {
          console.warn('Could not store admin token in localStorage:', storageError);
          // Continue without storing in localStorage
        }
      }

      return response.data;
    } else {
      // Use mock authentication
      // For development, we'll accept predefined credentials
      if ((email === '<EMAIL>' && password === 'admin123') ||
          (email === '<EMAIL>' && password === 'moderator123')) {

        // Create mock user based on email
        const isAdmin = email === '<EMAIL>';
        const mockUser = {
          id: isAdmin ? 'admin-1' : 'moderator-1',
          email: email,
          name: isAdmin ? 'Admin User' : 'Moderator User',
          role: isAdmin ? 'ADMIN' : 'MODERATOR'
        };

        // Create mock token
        const mockToken = `mock-token-${Math.random().toString(36).substring(2)}`;

        // Store in localStorage (only in browser environment)
        if (isBrowser) {
          try {
            localStorage.setItem('adminAccessToken', mockToken);
            localStorage.setItem('adminUser', JSON.stringify(mockUser));

            // Also store in cookies for better security
            Cookies.set('adminToken', mockToken, {
              expires: TOKEN_EXPIRY,
              secure: process.env.NODE_ENV === 'production',
              sameSite: 'strict'
            });
          } catch (storageError) {
            console.warn('Could not store admin token in localStorage:', storageError);
            // Continue without storing in localStorage
          }
        }

        // Return mock response
        return {
          success: true,
          message: 'Login successful',
          token: mockToken,
          user: mockUser,
          source: 'mock'
        };
      } else {
        // Invalid credentials
        return {
          success: false,
          message: 'Invalid email or password',
          source: 'mock'
        };
      }
    }
  } catch (error) {
    console.error('Admin login error:', error.response?.data || error.message);
    return {
      success: false,
      message: error.response?.data?.message || 'Login failed',
      error: error
    };
  }
};

/**
 * Admin logout
 * @returns {Promise} Promise with logout result
 */
export const adminLogout = async () => {
  try {
    // Check if we're in a browser environment
    if (typeof window !== 'undefined') {
      try {
        // Clear admin auth data
        localStorage.removeItem('adminAccessToken');
        localStorage.removeItem('adminUser');
        localStorage.removeItem('adminName');
        localStorage.removeItem('adminRole');
        Cookies.remove('adminToken');
      } catch (storageError) {
        console.warn('Could not clear admin data from localStorage:', storageError);
        // Continue with logout even if localStorage fails
      }
    }

    return { success: true };
  }
  catch (error) {
    console.error('Admin logout error:', error);
    return { success: false, error: error.message };
  }
};

// Export all functions as a default object for easier importing
const authService = {
  register,
  login,
  logout,
  storeAuthData,
  clearAuthData,
  isLoggedIn,
  isAuthenticated,
  isAdminLoggedIn,
  getCurrentUser,
  getAuthToken,
  getRefreshToken,
  getCurrentAdmin,
  verifyEmail,
  requestPasswordReset,
  resetPassword,
  updateProfile,
  changePassword,
  getToken,
  getAdminToken,
  refreshAuthToken,
  adminLogin,
  adminLogout,
  USER_STORAGE_KEY
};

export default authService;
