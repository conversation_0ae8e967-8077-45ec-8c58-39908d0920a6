import axios from 'axios';
import { API_BASE_URL } from '@/config';

const API_URL = `${API_BASE_URL}/privacy-settings`;

/**
 * Get the current user's privacy settings
 * @returns {Promise} Promise with privacy settings data
 */
export const getPrivacySettings = async () => {
  try {
    const token = localStorage.getItem('token');
    
    const response = await axios.get(API_URL, {
      headers: {
        'x-auth-token': token
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error fetching privacy settings:', error);
    throw error;
  }
};

/**
 * Update the current user's privacy settings
 * @param {Object} settings - The updated privacy settings
 * @returns {Promise} Promise with updated privacy settings data
 */
export const updatePrivacySettings = async (settings) => {
  try {
    const token = localStorage.getItem('token');
    
    const response = await axios.put(API_URL, settings, {
      headers: {
        'x-auth-token': token
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error updating privacy settings:', error);
    throw error;
  }
};

/**
 * Check if a user can view another user's content based on privacy settings
 * @param {Object} viewerUser - The user trying to view content
 * @param {Object} targetUser - The user whose content is being viewed
 * @param {Object} targetPrivacySettings - The privacy settings of the target user
 * @param {String} contentType - The type of content being viewed (photo, phone, email, etc.)
 * @returns {Boolean} Whether the viewer can access the content
 */
export const canViewContent = (viewerUser, targetUser, targetPrivacySettings, contentType) => {
  // If users are the same, they can always view their own content
  if (viewerUser.id === targetUser.id) {
    return true;
  }
  
  const privacySetting = targetPrivacySettings[`${contentType}Privacy`];
  
  switch (privacySetting) {
    case 'ALL_USERS':
      return true;
      
    case 'PREMIUM_USERS':
      return viewerUser.isPremium;
      
    case 'ACCEPTED_INTEREST':
      // Check if there's an accepted interest between the users
      const hasAcceptedInterest = viewerUser.acceptedInterests?.includes(targetUser.id) ||
                                 targetUser.acceptedInterests?.includes(viewerUser.id);
      return viewerUser.isPremium && hasAcceptedInterest;
      
    case 'HIDDEN':
      return false;
      
    default:
      return false;
  }
};
