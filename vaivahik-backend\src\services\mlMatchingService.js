/**
 * JavaScript wrapper for the Python ML Matching Service
 * This service calls the Python Flask API for ML-based matching
 */

const axios = require('axios');
const { PrismaClient } = require('@prisma/client');

class MLMatchingService {
  constructor() {
    this.pythonApiUrl = process.env.PYTHON_MATCHING_API_URL || 'http://localhost:5000';
    this.prisma = new PrismaClient();
    this.isServiceAvailable = false;
    this.checkServiceHealth();

    // Import phase manager and flexibility service
    this.PhaseManager = require('./phaseManager');
    this.FlexibilityService = require('./flexibilityService');
    this.phaseManager = new this.PhaseManager();
    this.flexibilityService = new this.FlexibilityService();
  }

  /**
   * Check if the Python ML service is available
   */
  async checkServiceHealth() {
    try {
      console.log(`Checking ML service health at: ${this.pythonApiUrl}/health`);
      const response = await axios.get(`${this.pythonApiUrl}/health`, { timeout: 5000 });
      this.isServiceAvailable = response.status === 200 && response.data.success;
      console.log('ML Matching Service:', this.isServiceAvailable ? 'Available' : 'Unavailable');
      if (this.isServiceAvailable) {
        console.log('ML Service Response:', response.data);
      }
    } catch (error) {
      this.isServiceAvailable = false;
      console.log('ML Matching Service: Unavailable (using fallback)');
      console.log('Error details:', error.code || error.message);
    }
  }

  /**
   * Get matches for a user using ML algorithm
   * @param {string} userId - User ID
   * @param {number} limit - Number of matches to return
   * @param {number} offset - Offset for pagination
   * @param {number} minScore - Minimum score threshold
   * @returns {Array} Array of matches with scores
   */
  async getMatches(userId, limit = 10, offset = 0, minScore = null) {
    try {
      // Get current user's profile and preferences
      const currentUser = await this.prisma.user.findUnique({
        where: { id: userId },
        include: { profile: true, preference: true }
      });

      if (!currentUser || !currentUser.profile) {
        return [];
      }

      // Get potential matches from database
      const targetGender = currentUser.profile.gender === 'Male' ? 'Female' : 'Male';
      const potentialMatches = await this.prisma.user.findMany({
        where: {
          id: { not: userId },
          isVerified: true,
          profile: {
            gender: targetGender
          }
        },
        include: {
          profile: true
        },
        take: Math.max(limit * 3, 50) // Get more candidates for ML to score
      });

      if (potentialMatches.length === 0) {
        return [];
      }

      // Get current phase and route to appropriate matching algorithm
      const currentPhase = await this.phaseManager.getCurrentPhase();
      console.log(`Using matching phase: ${currentPhase.version}`);

      // Route to appropriate matching algorithm based on phase
      switch (currentPhase.version) {
        case 'v1.5':
          return await this.getFlexibleMatches(currentUser, potentialMatches, limit, offset, minScore);
        case 'v2.0':
          return await this.getPersonalizedMatches(currentUser, potentialMatches, limit, offset, minScore);
        case 'v2.5':
          return await this.getIntelligentMatches(currentUser, potentialMatches, limit, offset, minScore);
        case 'v3.0':
          return await this.getAdvancedAIMatches(currentUser, potentialMatches, limit, offset, minScore);
        default:
          // v1.0 - Current rule-based matching
          return await this.getCurrentMatches(currentUser, potentialMatches, limit, offset, minScore);
      }

    } catch (error) {
      console.error('Error in getMatches:', error);
      return [];
    }
  }

  /**
   * Get matches using ML service
   */
  async getMLMatches(currentUser, potentialMatches, limit, offset, minScore) {
    const requestData = {
      user: {
        id: currentUser.id,
        profile: this.formatProfileForML(currentUser.profile)
      },
      preferences: currentUser.preference ? this.formatPreferencesForML(currentUser.preference) : {},
      potentialMatches: potentialMatches.map(match => ({
        id: match.id,
        profile: this.formatProfileForML(match.profile)
      }))
    };

    const response = await axios.post(`${this.pythonApiUrl}/api/match`, requestData, {
      timeout: 10000,
      headers: { 'Content-Type': 'application/json' }
    });

    if (!response.data.success) {
      throw new Error(response.data.message || 'ML service returned error');
    }

    let matches = response.data.matches;

    // Filter by minimum score if provided
    if (minScore) {
      matches = matches.filter(match => match.score >= minScore);
    }

    // Apply pagination
    matches = matches.slice(offset, offset + limit);

    // Enhance matches with profile data
    return await this.enhanceMatchesWithProfileData(matches, potentialMatches);
  }

  /**
   * Phase v1.0 - Current rule-based matching
   */
  async getCurrentMatches(currentUser, potentialMatches, limit, offset, minScore) {
    // If ML service is available, use it
    if (this.isServiceAvailable) {
      try {
        return await this.getMLMatches(currentUser, potentialMatches, limit, offset, minScore);
      } catch (error) {
        console.error('ML service error, falling back to rule-based:', error.message);
        this.isServiceAvailable = false;
      }
    }

    // Fallback to rule-based matching
    return await this.getRuleBasedMatches(currentUser, potentialMatches, limit, offset, minScore);
  }

  /**
   * Phase v1.5 - Flexible matching with compatibility groups
   */
  async getFlexibleMatches(currentUser, potentialMatches, limit, offset, minScore) {
    // Get user's flexibility settings
    const flexibilitySettings = await this.flexibilityService.getUserFlexibilitySettings(currentUser.id);

    const matches = potentialMatches.map(match => {
      let score = 50; // Base score

      // Flexible religion compatibility
      const religionScore = this.calculateReligionCompatibility(
        currentUser.profile.religion,
        match.profile.religion,
        flexibilitySettings.religionFlexible
      );
      score += religionScore * 0.3; // 30% weight

      // Flexible caste compatibility
      const casteScore = this.calculateCasteCompatibility(
        currentUser.profile.caste,
        match.profile.caste,
        flexibilitySettings.casteFlexible
      );
      score += casteScore * 0.25; // 25% weight

      // Flexible age compatibility
      const ageScore = this.calculateFlexibleAgeCompatibility(
        currentUser.profile.dateOfBirth,
        match.profile.dateOfBirth,
        flexibilitySettings.ageFlexibility || 5
      );
      score += ageScore * 0.2; // 20% weight

      // Education compatibility
      const educationScore = this.calculateEducationCompatibility(
        currentUser.profile.highestEducation,
        match.profile.highestEducation
      );
      score += educationScore * 0.15; // 15% weight

      // Location compatibility
      const locationScore = this.calculateLocationCompatibility(
        currentUser.profile.city,
        match.profile.city,
        currentUser.profile.state,
        match.profile.state
      );
      score += locationScore * 0.1; // 10% weight

      // Ensure score is between 0 and 100
      score = Math.min(100, Math.max(0, score));

      return {
        userId: match.id,
        score: Math.round(score),
        matchingPhase: 'v1.5',
        flexibilityApplied: flexibilitySettings.flexibilityLevel
      };
    });

    return this.processAndReturnMatches(matches, potentialMatches, limit, offset, minScore);
  }

  /**
   * Phase v2.0 - Personalized AI matching using behavior data
   */
  async getPersonalizedMatches(currentUser, potentialMatches, limit, offset, minScore) {
    try {
      console.log(`Phase v2.0: Personalized AI matching for user ${currentUser.id}`);

      // Check if sufficient behavior data exists for v2.0
      const dataReadiness = await this.checkDataReadinessForPhase('v2.0');
      if (!dataReadiness.ready) {
        console.log(`Insufficient data for v2.0, falling back to v1.5: ${dataReadiness.reason}`);
        return await this.getFlexibleMatches(currentUser, potentialMatches, limit, offset, minScore);
      }

      // Get user behavior insights
      const behaviorInsights = await this.getUserBehaviorInsights(currentUser.id);

      // Enhanced scoring with behavior-based personalization
      const scoredMatches = await Promise.all(
        potentialMatches.map(async (match) => {
          // Base compatibility score
          const baseScore = await this.calculateCompatibilityScore(currentUser, match);

          // Behavior-based personalization boost
          const personalizedScore = await this.applyPersonalizationBoost(
            currentUser,
            match,
            baseScore,
            behaviorInsights
          );

          return {
            ...match,
            compatibilityScore: personalizedScore,
            personalizedFactors: await this.getPersonalizedFactors(currentUser, match, behaviorInsights)
          };
        })
      );

      // Filter by minimum score and sort
      const filteredMatches = scoredMatches
        .filter(match => match.compatibilityScore >= minScore)
        .sort((a, b) => b.compatibilityScore - a.compatibilityScore);

      // Apply pagination
      const paginatedMatches = filteredMatches.slice(offset, offset + limit);

      console.log(`Phase v2.0: Found ${paginatedMatches.length} personalized matches`);

      return {
        matches: paginatedMatches,
        total: filteredMatches.length,
        algorithm: 'personalized_ai_v2.0',
        personalizedInsights: {
          behaviorFactors: behaviorInsights.topPreferences || [],
          matchingStrategy: 'behavior_enhanced_compatibility'
        }
      };

    } catch (error) {
      console.error('Error in personalized matching:', error);
      // Fallback to flexible matching
      return await this.getFlexibleMatches(currentUser, potentialMatches, limit, offset, minScore);
    }
  }

  /**
   * Phase v2.5 - Intelligent features with ML predictions
   */
  async getIntelligentMatches(currentUser, potentialMatches, limit, offset, minScore) {
    try {
      console.log(`Phase v2.5: Intelligent matching for user ${currentUser.id}`);

      // Check if sufficient data exists for v2.5
      const dataReadiness = await this.checkDataReadinessForPhase('v2.5');
      if (!dataReadiness.ready) {
        console.log(`Insufficient data for v2.5, falling back to v2.0: ${dataReadiness.reason}`);
        return await this.getPersonalizedMatches(currentUser, potentialMatches, limit, offset, minScore);
      }

      // Get comprehensive user insights
      const behaviorInsights = await this.getUserBehaviorInsights(currentUser.id);
      const interactionPatterns = await this.getInteractionPatterns(currentUser.id);

      // Enhanced scoring with intelligent predictions
      const scoredMatches = await Promise.all(
        potentialMatches.map(async (match) => {
          // Base personalized score from v2.0
          const personalizedScore = await this.applyPersonalizationBoost(
            currentUser,
            match,
            await this.calculateCompatibilityScore(currentUser, match),
            behaviorInsights
          );

          // Intelligent feature enhancements
          const intelligentScore = await this.applyIntelligentFeatures(
            currentUser,
            match,
            personalizedScore,
            behaviorInsights,
            interactionPatterns
          );

          // Success prediction
          const successProbability = await this.predictRelationshipSuccess(
            currentUser,
            match,
            behaviorInsights
          );

          return {
            ...match,
            compatibilityScore: intelligentScore,
            successProbability: successProbability,
            intelligentFactors: await this.getIntelligentFactors(currentUser, match, behaviorInsights),
            recommendationReason: await this.generateRecommendationReason(currentUser, match, behaviorInsights)
          };
        })
      );

      // Advanced filtering with success probability
      const filteredMatches = scoredMatches
        .filter(match =>
          match.compatibilityScore >= minScore &&
          match.successProbability >= 0.3 // Minimum 30% success probability
        )
        .sort((a, b) => {
          // Sort by weighted combination of compatibility and success probability
          const scoreA = (a.compatibilityScore * 0.7) + (a.successProbability * 0.3);
          const scoreB = (b.compatibilityScore * 0.7) + (b.successProbability * 0.3);
          return scoreB - scoreA;
        });

      // Apply pagination
      const paginatedMatches = filteredMatches.slice(offset, offset + limit);

      console.log(`Phase v2.5: Found ${paginatedMatches.length} intelligent matches`);

      return {
        matches: paginatedMatches,
        total: filteredMatches.length,
        algorithm: 'intelligent_features_v2.5',
        intelligentInsights: {
          behaviorPatterns: interactionPatterns,
          successFactors: await this.getTopSuccessFactors(currentUser.id),
          matchingStrategy: 'ml_enhanced_prediction'
        }
      };

    } catch (error) {
      console.error('Error in intelligent matching:', error);
      // Fallback to personalized matching
      return await this.getPersonalizedMatches(currentUser, potentialMatches, limit, offset, minScore);
    }
  }

  /**
   * Phase v3.0 - Advanced AI with 2-Tower model integration
   */
  async getAdvancedAIMatches(currentUser, potentialMatches, limit, offset, minScore) {
    try {
      console.log(`Phase v3.0: Advanced AI matching for user ${currentUser.id}`);

      // Check if sufficient data exists for v3.0
      const dataReadiness = await this.checkDataReadinessForPhase('v3.0');
      if (!dataReadiness.ready) {
        console.log(`Insufficient data for v3.0, falling back to v2.5: ${dataReadiness.reason}`);
        return await this.getIntelligentMatches(currentUser, potentialMatches, limit, offset, minScore);
      }

      // Get comprehensive insights
      const behaviorInsights = await this.getUserBehaviorInsights(currentUser.id);
      const interactionPatterns = await this.getInteractionPatterns(currentUser.id);
      const communityInsights = await this.getCommunityInsights(currentUser.id);

      // Use 2-Tower model for deep learning predictions
      const towerModelPredictions = await this.get2TowerModelPredictions(
        currentUser,
        potentialMatches,
        behaviorInsights
      );

      // Enhanced scoring with advanced AI
      const scoredMatches = await Promise.all(
        potentialMatches.map(async (match, index) => {
          // Get 2-Tower model score
          const towerScore = towerModelPredictions[index] || 0;

          // Intelligent score from v2.5
          const intelligentScore = await this.applyIntelligentFeatures(
            currentUser,
            match,
            await this.applyPersonalizationBoost(
              currentUser,
              match,
              await this.calculateCompatibilityScore(currentUser, match),
              behaviorInsights
            ),
            behaviorInsights,
            interactionPatterns
          );

          // Advanced AI enhancements
          const advancedScore = await this.applyAdvancedAIFeatures(
            currentUser,
            match,
            intelligentScore,
            towerScore,
            behaviorInsights,
            communityInsights
          );

          // Real-time optimization
          const optimizedScore = await this.applyRealTimeOptimization(
            currentUser,
            match,
            advancedScore,
            behaviorInsights
          );

          // Comprehensive success prediction
          const successProbability = await this.predictAdvancedRelationshipSuccess(
            currentUser,
            match,
            behaviorInsights,
            towerScore
          );

          return {
            ...match,
            compatibilityScore: optimizedScore,
            successProbability: successProbability,
            towerModelScore: towerScore,
            advancedFactors: await this.getAdvancedAIFactors(currentUser, match, behaviorInsights),
            aiInsights: await this.generateAIInsights(currentUser, match, behaviorInsights, towerScore),
            recommendationConfidence: await this.calculateRecommendationConfidence(
              optimizedScore,
              successProbability,
              towerScore
            )
          };
        })
      );

      // Advanced filtering with multiple criteria
      const filteredMatches = scoredMatches
        .filter(match =>
          match.compatibilityScore >= minScore &&
          match.successProbability >= 0.4 && // Higher success threshold
          match.recommendationConfidence >= 0.6 // Minimum confidence
        )
        .sort((a, b) => {
          // Advanced weighted scoring
          const scoreA = (a.compatibilityScore * 0.4) +
                        (a.successProbability * 0.3) +
                        (a.towerModelScore * 0.2) +
                        (a.recommendationConfidence * 0.1);
          const scoreB = (b.compatibilityScore * 0.4) +
                        (b.successProbability * 0.3) +
                        (b.towerModelScore * 0.2) +
                        (b.recommendationConfidence * 0.1);
          return scoreB - scoreA;
        });

      // Apply pagination
      const paginatedMatches = filteredMatches.slice(offset, offset + limit);

      console.log(`Phase v3.0: Found ${paginatedMatches.length} advanced AI matches`);

      return {
        matches: paginatedMatches,
        total: filteredMatches.length,
        algorithm: 'advanced_ai_v3.0',
        advancedInsights: {
          towerModelEnabled: true,
          behaviorPatterns: interactionPatterns,
          communityTrends: communityInsights,
          aiConfidence: paginatedMatches.length > 0 ?
            paginatedMatches.reduce((sum, m) => sum + m.recommendationConfidence, 0) / paginatedMatches.length : 0,
          matchingStrategy: 'neural_network_enhanced'
        }
      };

    } catch (error) {
      console.error('Error in advanced AI matching:', error);
      // Fallback to intelligent matching
      return await this.getIntelligentMatches(currentUser, potentialMatches, limit, offset, minScore);
    }
  }

  /**
   * Original rule-based matching logic
   */
  async getRuleBasedMatches(currentUser, potentialMatches, limit, offset, minScore) {
    const matches = potentialMatches.map(match => {
      let score = 50; // Base score

      // Same religion bonus
      if (match.profile.religion === currentUser.profile.religion) {
        score += 20;
      }

      // Same caste bonus
      if (match.profile.caste === currentUser.profile.caste) {
        score += 15;
      }

      // Same sub-caste bonus
      if (match.profile.subCaste === currentUser.profile.subCaste) {
        score += 10;
      }

      // Education compatibility
      if (match.profile.highestEducation && currentUser.profile.highestEducation) {
        score += 8;
      }

      // Age compatibility (more flexible)
      if (match.profile.dateOfBirth && currentUser.profile.dateOfBirth) {
        const matchAge = new Date().getFullYear() - new Date(match.profile.dateOfBirth).getFullYear();
        const currentAge = new Date().getFullYear() - new Date(currentUser.profile.dateOfBirth).getFullYear();
        const ageDiff = Math.abs(matchAge - currentAge);

        // More flexible age scoring
        if (ageDiff <= 2) {
          score += 15; // Perfect age match
        } else if (ageDiff <= 4) {
          score += 12; // Very good match
        } else if (ageDiff <= 6) {
          score += 8;  // Good match
        } else if (ageDiff <= 10) {
          score += 4;  // Acceptable match
        } else if (ageDiff <= 15) {
          score += 1;  // Still possible match
        }
        // No penalty for larger age gaps - just no bonus
      }

      // Premium user bonus
      if (match.isPremium) {
        score += 5;
      }

      // Ensure score is between 0 and 100
      score = Math.min(100, Math.max(0, score));

      return {
        userId: match.id,
        score: score
      };
    });

    // Sort by score (descending)
    matches.sort((a, b) => b.score - a.score);

    // Filter by minimum score if provided
    let filteredMatches = matches;
    if (minScore) {
      filteredMatches = matches.filter(match => match.score >= minScore);
    }

    // Apply pagination
    const paginatedMatches = filteredMatches.slice(offset, offset + limit);

    // Enhance matches with profile data
    return await this.enhanceMatchesWithProfileData(paginatedMatches, potentialMatches);
  }

  /**
   * Enhance matches with full profile data
   */
  async enhanceMatchesWithProfileData(matches, potentialMatches) {
    return matches.map(match => {
      const fullProfile = potentialMatches.find(p => p.id === match.userId);
      if (!fullProfile) return match;

      return {
        ...match,
        profile: {
          fullName: fullProfile.profile.fullName,
          age: fullProfile.profile.dateOfBirth ?
            new Date().getFullYear() - new Date(fullProfile.profile.dateOfBirth).getFullYear() : null,
          height: fullProfile.profile.height,
          education: fullProfile.profile.highestEducation,
          occupation: fullProfile.profile.occupation,
          city: fullProfile.profile.city,
          religion: fullProfile.profile.religion,
          caste: fullProfile.profile.caste,
          subCaste: fullProfile.profile.subCaste
        },
        isPremium: fullProfile.isPremium,
        isVerified: fullProfile.isVerified
      };
    });
  }

  /**
   * Format profile data for ML service
   */
  formatProfileForML(profile) {
    return {
      gender: profile.gender,
      age: profile.dateOfBirth ?
        new Date().getFullYear() - new Date(profile.dateOfBirth).getFullYear() : null,
      height: profile.height ? parseFloat(profile.height) : null,
      religion: profile.religion,
      caste: profile.caste,
      subCaste: profile.subCaste,
      education: profile.highestEducation,
      occupation: profile.occupation,
      income: profile.annualIncome,
      maritalStatus: profile.maritalStatus,
      city: profile.city,
      state: profile.state
    };
  }

  /**
   * Format preferences for ML service
   */
  formatPreferencesForML(preferences) {
    return {
      ageMin: preferences.ageMin,
      ageMax: preferences.ageMax,
      heightMin: preferences.heightMin,
      heightMax: preferences.heightMax,
      education: preferences.education,
      occupation: preferences.occupation,
      income: preferences.income,
      location: preferences.location
    };
  }

  /**
   * Calculate religion compatibility with flexibility
   */
  calculateReligionCompatibility(userReligion, matchReligion, isFlexible) {
    if (userReligion === matchReligion) {
      return 50; // Perfect match
    }

    if (!isFlexible) {
      return 5; // Low score for different religions when not flexible
    }

    // Religion compatibility groups
    const religionGroups = {
      'Hindu': ['Hindu', 'Jain', 'Buddhist', 'Sikh'],
      'Muslim': ['Muslim', 'Sufi'],
      'Christian': ['Christian', 'Catholic', 'Protestant']
    };

    for (const group of Object.values(religionGroups)) {
      if (group.includes(userReligion) && group.includes(matchReligion)) {
        return 35; // Good compatibility within group
      }
    }

    return 15; // Some compatibility when flexible
  }

  /**
   * Calculate caste compatibility with flexibility
   */
  calculateCasteCompatibility(userCaste, matchCaste, isFlexible) {
    if (userCaste === matchCaste) {
      return 40; // Perfect match
    }

    if (!isFlexible) {
      return 5; // Low score for different castes when not flexible
    }

    // Caste compatibility groups (Maratha community focused)
    const casteGroups = {
      'Maratha': ['Maratha', 'Kunbi', 'Mali', 'Dhangar'],
      'Brahmin': ['Brahmin', 'Deshastha', 'Chitpavan', 'Karhade']
    };

    for (const group of Object.values(casteGroups)) {
      if (group.includes(userCaste) && group.includes(matchCaste)) {
        return 25; // Good compatibility within group
      }
    }

    return 10; // Some compatibility when flexible
  }

  /**
   * Calculate flexible age compatibility
   */
  calculateFlexibleAgeCompatibility(userDOB, matchDOB, ageFlexibility) {
    if (!userDOB || !matchDOB) {
      return 10; // Default score when age unknown
    }

    const userAge = new Date().getFullYear() - new Date(userDOB).getFullYear();
    const matchAge = new Date().getFullYear() - new Date(matchDOB).getFullYear();
    const ageDiff = Math.abs(userAge - matchAge);

    if (ageDiff === 0) {
      return 30; // Perfect age match
    } else if (ageDiff <= 2) {
      return 25; // Excellent match
    } else if (ageDiff <= ageFlexibility) {
      return 20 - (ageDiff * 2); // Good match within flexibility range
    } else if (ageDiff <= ageFlexibility + 5) {
      return 10 - ageDiff; // Acceptable match slightly outside range
    } else {
      return 2; // Poor match outside flexibility range
    }
  }

  /**
   * Calculate education compatibility
   */
  calculateEducationCompatibility(userEducation, matchEducation) {
    if (!userEducation || !matchEducation) {
      return 8; // Default score when education unknown
    }

    if (userEducation === matchEducation) {
      return 20; // Perfect match
    }

    // Education level hierarchy
    const educationLevels = {
      'PhD': 7,
      'Masters': 6,
      'Post Graduate': 5,
      'Graduate': 4,
      'Diploma': 3,
      'Higher Secondary': 2,
      'Secondary': 1
    };

    const userLevel = educationLevels[userEducation] || 0;
    const matchLevel = educationLevels[matchEducation] || 0;
    const levelDiff = Math.abs(userLevel - matchLevel);

    if (levelDiff <= 1) {
      return 15; // Very compatible
    } else if (levelDiff <= 2) {
      return 10; // Compatible
    } else {
      return 5; // Less compatible
    }
  }

  /**
   * Calculate location compatibility
   */
  calculateLocationCompatibility(userCity, matchCity, userState, matchState) {
    if (userCity === matchCity) {
      return 15; // Same city
    } else if (userState === matchState) {
      return 10; // Same state
    } else {
      return 5; // Different state
    }
  }

  /**
   * Process and return matches with common logic
   */
  processAndReturnMatches(matches, potentialMatches, limit, offset, minScore) {
    // Sort by score (descending)
    matches.sort((a, b) => b.score - a.score);

    // Filter by minimum score if provided
    let filteredMatches = matches;
    if (minScore) {
      filteredMatches = matches.filter(match => match.score >= minScore);
    }

    // Apply pagination
    const paginatedMatches = filteredMatches.slice(offset, offset + limit);

    // Enhance matches with profile data
    return this.enhanceMatchesWithProfileData(paginatedMatches, potentialMatches);
  }

  /**
   * Get match explanation
   */
  async getMatchExplanation(userId, matchId) {
    try {
      if (!this.isServiceAvailable) {
        // Return simplified explanation
        return {
          matchId,
          overallScore: 75,
          factors: [
            { name: 'Religion', score: 100, weight: 30 },
            { name: 'Caste', score: 100, weight: 25 },
            { name: 'Education', score: 80, weight: 20 },
            { name: 'Age', score: 70, weight: 15 },
            { name: 'Location', score: 60, weight: 10 }
          ]
        };
      }

      // Get user and match profiles
      const [user, match] = await Promise.all([
        this.prisma.user.findUnique({
          where: { id: userId },
          include: { profile: true, preference: true }
        }),
        this.prisma.user.findUnique({
          where: { id: matchId },
          include: { profile: true }
        })
      ]);

      if (!user || !match) {
        throw new Error('User or match not found');
      }

      const requestData = {
        user: {
          id: user.id,
          profile: this.formatProfileForML(user.profile)
        },
        preferences: user.preference ? this.formatPreferencesForML(user.preference) : {},
        match: {
          id: match.id,
          profile: this.formatProfileForML(match.profile)
        }
      };

      const response = await axios.post(`${this.pythonApiUrl}/api/match-analysis`, requestData, {
        timeout: 5000,
        headers: { 'Content-Type': 'application/json' }
      });

      if (!response.data.success) {
        throw new Error(response.data.message || 'ML service returned error');
      }

      return response.data;

    } catch (error) {
      console.error('Error getting match explanation:', error);
      // Return fallback explanation
      return {
        matchId,
        overallScore: 75,
        factors: [
          { name: 'Religion', score: 100, weight: 30 },
          { name: 'Caste', score: 100, weight: 25 },
          { name: 'Education', score: 80, weight: 20 },
          { name: 'Age', score: 70, weight: 15 },
          { name: 'Location', score: 60, weight: 10 }
        ]
      };
    }
  }

  // ==================== BEHAVIOR-BASED METHODS (v2.0+) ====================

  /**
   * Get user behavior insights for personalization
   */
  async getUserBehaviorInsights(userId) {
    try {
      // Get recent interactions
      const recentInteractions = await this.prisma.userInteraction.findMany({
        where: {
          userId: userId,
          timestamp: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        },
        include: {
          targetUser: {
            include: { profile: true }
          }
        },
        orderBy: { timestamp: 'desc' },
        take: 100
      });

      // Get user feedback
      const userFeedback = await this.prisma.userFeedback.findMany({
        where: {
          userId: userId,
          timestamp: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          }
        },
        include: {
          targetUser: {
            include: { profile: true }
          }
        },
        orderBy: { timestamp: 'desc' }
      });

      // Analyze preferences from interactions
      const preferences = this.analyzeUserPreferences(recentInteractions, userFeedback);

      return {
        totalInteractions: recentInteractions.length,
        totalFeedback: userFeedback.length,
        topPreferences: preferences,
        interactionPatterns: this.analyzeInteractionPatterns(recentInteractions),
        feedbackPatterns: this.analyzeFeedbackPatterns(userFeedback),
        lastActive: recentInteractions[0]?.timestamp || null
      };
    } catch (error) {
      console.error('Error getting behavior insights:', error);
      return {
        totalInteractions: 0,
        totalFeedback: 0,
        topPreferences: {},
        interactionPatterns: {},
        feedbackPatterns: {},
        lastActive: null
      };
    }
  }

  /**
   * Analyze user preferences from interactions and feedback
   */
  analyzeUserPreferences(interactions, feedback) {
    const preferences = {
      age: {},
      education: {},
      occupation: {},
      location: {},
      height: {},
      caste: {}
    };

    // Analyze positive interactions (LIKE, SHORTLIST, CONTACT_REQUESTED)
    const positiveInteractions = interactions.filter(i =>
      ['LIKE', 'SHORTLIST', 'CONTACT_REQUESTED', 'CONTACT_ACCEPTED'].includes(i.interactionType)
    );

    positiveInteractions.forEach(interaction => {
      const profile = interaction.targetUser?.profile;
      if (profile) {
        // Age preference
        if (profile.age) {
          const ageGroup = Math.floor(profile.age / 5) * 5; // Group by 5-year ranges
          preferences.age[ageGroup] = (preferences.age[ageGroup] || 0) + 1;
        }

        // Other preferences
        if (profile.education) preferences.education[profile.education] = (preferences.education[profile.education] || 0) + 1;
        if (profile.occupation) preferences.occupation[profile.occupation] = (preferences.occupation[profile.occupation] || 0) + 1;
        if (profile.location) preferences.location[profile.location] = (preferences.location[profile.location] || 0) + 1;
        if (profile.height) {
          const heightGroup = Math.floor(profile.height / 5) * 5; // Group by 5cm ranges
          preferences.height[heightGroup] = (preferences.height[heightGroup] || 0) + 1;
        }
        if (profile.caste) preferences.caste[profile.caste] = (preferences.caste[profile.caste] || 0) + 1;
      }
    });

    // Analyze positive feedback
    const positiveFeedback = feedback.filter(f => f.rating >= 4);
    positiveFeedback.forEach(fb => {
      const profile = fb.targetUser?.profile;
      if (profile) {
        // Similar analysis for feedback
        if (profile.age) {
          const ageGroup = Math.floor(profile.age / 5) * 5;
          preferences.age[ageGroup] = (preferences.age[ageGroup] || 0) + 2; // Weight feedback higher
        }
        if (profile.education) preferences.education[profile.education] = (preferences.education[profile.education] || 0) + 2;
        if (profile.occupation) preferences.occupation[profile.occupation] = (preferences.occupation[profile.occupation] || 0) + 2;
        if (profile.location) preferences.location[profile.location] = (preferences.location[profile.location] || 0) + 2;
        if (profile.height) {
          const heightGroup = Math.floor(profile.height / 5) * 5;
          preferences.height[heightGroup] = (preferences.height[heightGroup] || 0) + 2;
        }
        if (profile.caste) preferences.caste[profile.caste] = (preferences.caste[profile.caste] || 0) + 2;
      }
    });

    // Convert to sorted preferences
    const sortedPreferences = {};
    Object.keys(preferences).forEach(key => {
      sortedPreferences[key] = Object.entries(preferences[key])
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3) // Top 3 preferences
        .map(([value, count]) => ({ value, count }));
    });

    return sortedPreferences;
  }

  /**
   * Apply personalization boost based on behavior data
   */
  async applyPersonalizationBoost(currentUser, targetUser, baseScore, behaviorInsights) {
    let personalizedScore = baseScore;
    const profile = targetUser.profile;

    if (!profile || !behaviorInsights.topPreferences) {
      return personalizedScore;
    }

    // Apply preference boosts
    const preferences = behaviorInsights.topPreferences;

    // Age preference boost
    if (preferences.age && preferences.age.length > 0 && profile.age) {
      const ageGroup = Math.floor(profile.age / 5) * 5;
      const agePreference = preferences.age.find(p => p.value == ageGroup);
      if (agePreference) {
        personalizedScore += (agePreference.count / Math.max(behaviorInsights.totalInteractions, 1)) * 10;
      }
    }

    // Education preference boost
    if (preferences.education && preferences.education.length > 0 && profile.education) {
      const eduPreference = preferences.education.find(p => p.value === profile.education);
      if (eduPreference) {
        personalizedScore += (eduPreference.count / Math.max(behaviorInsights.totalInteractions, 1)) * 8;
      }
    }

    // Occupation preference boost
    if (preferences.occupation && preferences.occupation.length > 0 && profile.occupation) {
      const occPreference = preferences.occupation.find(p => p.value === profile.occupation);
      if (occPreference) {
        personalizedScore += (occPreference.count / Math.max(behaviorInsights.totalInteractions, 1)) * 6;
      }
    }

    // Location preference boost
    if (preferences.location && preferences.location.length > 0 && profile.location) {
      const locPreference = preferences.location.find(p => p.value === profile.location);
      if (locPreference) {
        personalizedScore += (locPreference.count / Math.max(behaviorInsights.totalInteractions, 1)) * 5;
      }
    }

    return Math.min(personalizedScore, 100); // Cap at 100
  }

  /**
   * Check if sufficient data exists for a given algorithm phase
   */
  async checkDataReadinessForPhase(phase) {
    try {
      const userCount = await this.prisma.user.count({
        where: { profileStatus: 'ACTIVE' }
      });

      const interactionCount = await this.prisma.userInteraction.count();
      const feedbackCount = await this.prisma.userFeedback.count();

      // Define minimum requirements for each phase
      const requirements = {
        'v2.0': {
          minUsers: 100,
          minInteractions: 500,
          minFeedback: 50,
          description: 'Personalized AI matching'
        },
        'v2.5': {
          minUsers: 1000,
          minInteractions: 5000,
          minFeedback: 500,
          description: 'Intelligent features with ML predictions'
        },
        'v3.0': {
          minUsers: 10000,
          minInteractions: 50000,
          minFeedback: 5000,
          description: 'Advanced AI with 2-Tower model'
        }
      };

      const requirement = requirements[phase];
      if (!requirement) {
        return { ready: false, reason: 'Unknown phase' };
      }

      // Check each requirement
      const checks = {
        users: userCount >= requirement.minUsers,
        interactions: interactionCount >= requirement.minInteractions,
        feedback: feedbackCount >= requirement.minFeedback
      };

      const allReady = Object.values(checks).every(check => check);

      return {
        ready: allReady,
        phase: phase,
        description: requirement.description,
        currentData: {
          users: userCount,
          interactions: interactionCount,
          feedback: feedbackCount
        },
        requirements: requirement,
        checks: checks,
        reason: allReady ? 'Data requirements met' :
          `Insufficient data: Users(${userCount}/${requirement.minUsers}), Interactions(${interactionCount}/${requirement.minInteractions}), Feedback(${feedbackCount}/${requirement.minFeedback})`
      };

    } catch (error) {
      console.error('Error checking data readiness:', error);
      return {
        ready: false,
        reason: 'Error checking data availability',
        error: error.message
      };
    }
  }

  // ==================== INTELLIGENT FEATURES METHODS (v2.5+) ====================

  /**
   * Get interaction patterns for intelligent matching
   */
  async getInteractionPatterns(userId) {
    try {
      const interactions = await this.prisma.userInteraction.findMany({
        where: { userId: userId },
        orderBy: { timestamp: 'desc' },
        take: 200
      });

      return {
        totalInteractions: interactions.length,
        interactionTypes: this.groupBy(interactions, 'interactionType'),
        timePatterns: this.analyzeTimePatterns(interactions),
        responsePatterns: this.analyzeResponsePatterns(interactions)
      };
    } catch (error) {
      console.error('Error getting interaction patterns:', error);
      return { totalInteractions: 0, interactionTypes: {}, timePatterns: {}, responsePatterns: {} };
    }
  }

  /**
   * Apply intelligent features for v2.5
   */
  async applyIntelligentFeatures(currentUser, targetUser, baseScore, behaviorInsights, interactionPatterns) {
    let intelligentScore = baseScore;

    // Time-based intelligence
    if (interactionPatterns.timePatterns) {
      const timeBoost = this.calculateTimeBasedBoost(interactionPatterns.timePatterns);
      intelligentScore += timeBoost;
    }

    // Response pattern intelligence
    if (interactionPatterns.responsePatterns) {
      const responseBoost = this.calculateResponsePatternBoost(interactionPatterns.responsePatterns);
      intelligentScore += responseBoost;
    }

    // Behavioral consistency boost
    const consistencyBoost = this.calculateConsistencyBoost(behaviorInsights);
    intelligentScore += consistencyBoost;

    return Math.min(intelligentScore, 100);
  }

  /**
   * Predict relationship success probability
   */
  async predictRelationshipSuccess(currentUser, targetUser, behaviorInsights) {
    try {
      let successScore = 0.5; // Base 50% probability

      // Factor 1: User engagement level
      if (behaviorInsights.totalInteractions > 50) {
        successScore += 0.1; // Active users have higher success
      }

      // Factor 2: Positive feedback ratio
      if (behaviorInsights.totalFeedback > 0) {
        const positiveFeedback = await this.getPositiveFeedbackRatio(currentUser.id);
        successScore += positiveFeedback * 0.2;
      }

      // Factor 3: Profile completeness
      const profileCompleteness = this.calculateProfileCompleteness(targetUser.profile);
      successScore += (profileCompleteness / 100) * 0.1;

      // Factor 4: Mutual interests (if available)
      const mutualInterests = this.calculateMutualInterests(currentUser, targetUser);
      successScore += mutualInterests * 0.1;

      return Math.min(Math.max(successScore, 0), 1); // Clamp between 0 and 1
    } catch (error) {
      console.error('Error predicting relationship success:', error);
      return 0.5; // Default 50% probability
    }
  }

  // ==================== ADVANCED AI METHODS (v3.0+) ====================

  /**
   * Get community insights for advanced matching
   */
  async getCommunityInsights(userId) {
    try {
      // Get community trends from successful matches
      const communityData = await this.prisma.userInteraction.groupBy({
        by: ['interactionType'],
        where: {
          timestamp: {
            gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) // Last 90 days
          }
        },
        _count: {
          interactionType: true
        }
      });

      return {
        communityTrends: communityData,
        totalCommunityInteractions: communityData.reduce((sum, item) => sum + item._count.interactionType, 0)
      };
    } catch (error) {
      console.error('Error getting community insights:', error);
      return { communityTrends: [], totalCommunityInteractions: 0 };
    }
  }

  /**
   * Get 2-Tower model predictions (placeholder for actual ML model)
   */
  async get2TowerModelPredictions(currentUser, potentialMatches, behaviorInsights) {
    try {
      // This would integrate with the actual 2-Tower PyTorch model
      // For now, return simulated predictions based on behavior data

      return potentialMatches.map((match, index) => {
        // Simulate 2-Tower model score based on available data
        let towerScore = 0.5; // Base score

        // Factor in behavior insights
        if (behaviorInsights.totalInteractions > 20) {
          towerScore += 0.1;
        }

        // Factor in profile similarity
        const similarity = this.calculateProfileSimilarity(currentUser, match);
        towerScore += similarity * 0.3;

        // Add some randomness to simulate ML model variance
        towerScore += (Math.random() - 0.5) * 0.2;

        return Math.min(Math.max(towerScore, 0), 1);
      });
    } catch (error) {
      console.error('Error getting 2-Tower predictions:', error);
      return potentialMatches.map(() => 0.5); // Default scores
    }
  }

  /**
   * Apply advanced AI features for v3.0
   */
  async applyAdvancedAIFeatures(currentUser, targetUser, baseScore, towerScore, behaviorInsights, communityInsights) {
    let advancedScore = baseScore;

    // 2-Tower model integration
    advancedScore += towerScore * 20; // Scale tower score to match compatibility score range

    // Community trend alignment
    const communityAlignment = this.calculateCommunityAlignment(behaviorInsights, communityInsights);
    advancedScore += communityAlignment * 5;

    // Advanced behavioral analysis
    const behavioralComplexity = this.calculateBehavioralComplexity(behaviorInsights);
    advancedScore += behavioralComplexity * 3;

    return Math.min(advancedScore, 100);
  }

  /**
   * Apply real-time optimization
   */
  async applyRealTimeOptimization(currentUser, targetUser, baseScore, behaviorInsights) {
    let optimizedScore = baseScore;

    // Recent activity boost
    if (behaviorInsights.lastActive) {
      const hoursSinceActive = (Date.now() - new Date(behaviorInsights.lastActive).getTime()) / (1000 * 60 * 60);
      if (hoursSinceActive < 24) {
        optimizedScore += 2; // Recent activity boost
      }
    }

    // Peak usage time alignment
    const currentHour = new Date().getHours();
    if (currentHour >= 19 && currentHour <= 22) { // Peak matrimony browsing hours
      optimizedScore += 1;
    }

    return Math.min(optimizedScore, 100);
  }

  // ==================== HELPER METHODS ====================

  analyzeInteractionPatterns(interactions) {
    return {
      averageSessionLength: interactions.length > 0 ? interactions.reduce((sum, i) => sum + (i.duration || 0), 0) / interactions.length : 0,
      mostActiveHour: this.getMostActiveHour(interactions),
      interactionFrequency: interactions.length / 30 // Per day over 30 days
    };
  }

  analyzeFeedbackPatterns(feedback) {
    return {
      averageRating: feedback.length > 0 ? feedback.reduce((sum, f) => sum + f.rating, 0) / feedback.length : 0,
      positiveRatio: feedback.length > 0 ? feedback.filter(f => f.rating >= 4).length / feedback.length : 0,
      feedbackFrequency: feedback.length / 30
    };
  }

  groupBy(array, key) {
    return array.reduce((result, item) => {
      const group = item[key];
      result[group] = (result[group] || 0) + 1;
      return result;
    }, {});
  }

  calculateTimeBasedBoost(timePatterns) {
    // Boost for consistent usage patterns
    return timePatterns.interactionFrequency > 1 ? 2 : 0;
  }

  calculateResponsePatternBoost(responsePatterns) {
    // Boost for good response patterns
    return responsePatterns.averageSessionLength > 60 ? 1 : 0; // 60 seconds
  }

  calculateConsistencyBoost(behaviorInsights) {
    // Boost for consistent behavior
    return behaviorInsights.totalInteractions > 10 ? 1 : 0;
  }

  async getPositiveFeedbackRatio(userId) {
    try {
      const feedback = await this.prisma.userFeedback.findMany({
        where: { userId: userId }
      });

      if (feedback.length === 0) return 0;

      const positiveFeedback = feedback.filter(f => f.rating >= 4);
      return positiveFeedback.length / feedback.length;
    } catch (error) {
      return 0;
    }
  }

  calculateProfileCompleteness(profile) {
    if (!profile) return 0;

    const fields = ['age', 'height', 'education', 'occupation', 'location', 'bio'];
    const completedFields = fields.filter(field => profile[field]).length;
    return (completedFields / fields.length) * 100;
  }

  calculateMutualInterests(currentUser, targetUser) {
    // Placeholder for mutual interests calculation
    // Would compare hobbies, interests, etc.
    return 0.1; // Default small boost
  }

  calculateProfileSimilarity(currentUser, targetUser) {
    // Calculate basic profile similarity
    let similarity = 0;

    if (currentUser.profile && targetUser.profile) {
      const currentProfile = currentUser.profile;
      const targetProfile = targetUser.profile;

      // Age similarity (closer ages get higher scores)
      if (currentProfile.age && targetProfile.age) {
        const ageDiff = Math.abs(currentProfile.age - targetProfile.age);
        similarity += Math.max(0, (10 - ageDiff) / 10) * 0.2;
      }

      // Education similarity
      if (currentProfile.education === targetProfile.education) {
        similarity += 0.2;
      }

      // Location similarity
      if (currentProfile.location === targetProfile.location) {
        similarity += 0.3;
      }
    }

    return Math.min(similarity, 1);
  }

  calculateCommunityAlignment(behaviorInsights, communityInsights) {
    // Calculate how user's behavior aligns with community trends
    return 0.5; // Placeholder
  }

  calculateBehavioralComplexity(behaviorInsights) {
    // Calculate complexity of user's behavioral patterns
    const complexity = (behaviorInsights.totalInteractions / 100) + (behaviorInsights.totalFeedback / 50);
    return Math.min(complexity, 1);
  }

  getMostActiveHour(interactions) {
    const hourCounts = {};
    interactions.forEach(interaction => {
      const hour = new Date(interaction.timestamp).getHours();
      hourCounts[hour] = (hourCounts[hour] || 0) + 1;
    });

    return Object.entries(hourCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 20; // Default to 8 PM
  }

  // Placeholder methods for advanced features
  async getPersonalizedFactors(currentUser, targetUser, behaviorInsights) {
    return {
      behaviorMatch: 'High',
      preferenceAlignment: 'Strong',
      activityPattern: 'Compatible'
    };
  }

  async getIntelligentFactors(currentUser, targetUser, behaviorInsights) {
    return {
      successPrediction: 'High',
      compatibilityTrend: 'Positive',
      communityFit: 'Excellent'
    };
  }

  async getAdvancedAIFactors(currentUser, targetUser, behaviorInsights) {
    return {
      neuralNetworkScore: 'Optimal',
      deepLearningInsights: 'Highly Compatible',
      aiConfidence: 'Very High'
    };
  }

  async generateRecommendationReason(currentUser, targetUser, behaviorInsights) {
    return 'Based on your interaction patterns and preferences, this match shows high compatibility potential.';
  }

  async generateAIInsights(currentUser, targetUser, behaviorInsights, towerScore) {
    return {
      primaryReason: 'Strong behavioral compatibility',
      secondaryFactors: ['Similar activity patterns', 'Aligned preferences'],
      confidence: towerScore > 0.7 ? 'High' : 'Medium'
    };
  }

  async calculateRecommendationConfidence(compatibilityScore, successProbability, towerScore) {
    return (compatibilityScore / 100 * 0.4) + (successProbability * 0.3) + (towerScore * 0.3);
  }

  async getTopSuccessFactors(userId) {
    return [
      { factor: 'Communication Style', importance: 0.8 },
      { factor: 'Shared Values', importance: 0.7 },
      { factor: 'Lifestyle Compatibility', importance: 0.6 }
    ];
  }

  async predictAdvancedRelationshipSuccess(currentUser, targetUser, behaviorInsights, towerScore) {
    // Enhanced success prediction with 2-Tower model
    const baseSuccess = await this.predictRelationshipSuccess(currentUser, targetUser, behaviorInsights);
    const towerBoost = towerScore * 0.2;
    return Math.min(baseSuccess + towerBoost, 1);
  }

  // ==================== ADVANCED v2.0 ALGORITHM METHODS ====================

  /**
   * Analyze user interaction patterns for advanced personalization
   */
  async analyzeUserInteractionPatterns(userId) {
    try {
      const interactions = await this.prisma.userInteraction.findMany({
        where: { userId: userId },
        include: { targetUser: { include: { profile: true } } },
        orderBy: { timestamp: 'desc' },
        take: 500 // Last 500 interactions
      });

      if (interactions.length === 0) {
        return { confidence: 0, patterns: {}, summary: 'No interaction data available' };
      }

      const patterns = {
        preferredAgeRange: this.analyzeAgePreferences(interactions),
        preferredEducation: this.analyzeEducationPreferences(interactions),
        preferredOccupations: this.analyzeOccupationPreferences(interactions),
        preferredLocations: this.analyzeLocationPreferences(interactions),
        interactionTiming: this.analyzeInteractionTiming(interactions),
        responsePatterns: this.analyzeResponsePatterns(interactions),
        rejectionPatterns: this.analyzeRejectionPatterns(interactions)
      };

      return {
        patterns,
        confidence: Math.min(interactions.length / 50, 1),
        totalInteractions: interactions.length,
        summary: this.generatePatternSummary(patterns)
      };
    } catch (error) {
      console.error('Error analyzing interaction patterns:', error);
      return { confidence: 0, patterns: {}, summary: 'Error analyzing patterns' };
    }
  }

  /**
   * Extract learned preferences from user behavior
   */
  async extractLearnedPreferences(userId) {
    try {
      const [profileLikes, interests, shortlists] = await Promise.all([
        this.prisma.profileLike.findMany({
          where: { userId: userId },
          include: { targetUser: { include: { profile: true } } }
        }),
        this.prisma.interest.findMany({
          where: { userId: userId },
          include: { targetUser: { include: { profile: true } } }
        }),
        this.prisma.shortlist.findMany({
          where: { userId: userId },
          include: { targetUser: { include: { profile: true } } }
        })
      ]);

      const preferences = {
        physicalPreferences: this.extractPhysicalPreferences(profileLikes),
        personalityPreferences: this.extractPersonalityPreferences(interests),
        lifestylePreferences: this.extractLifestylePreferences(shortlists),
        careerPreferences: this.extractCareerPreferences([...profileLikes, ...interests]),
        familyPreferences: this.extractFamilyPreferences([...profileLikes, ...interests])
      };

      const totalActions = profileLikes.length + interests.length + shortlists.length;

      return {
        preferences,
        confidence: Math.min(totalActions / 30, 1),
        summary: {
          totalLikes: profileLikes.length,
          totalInterests: interests.length,
          totalShortlisted: shortlists.length,
          learningProgress: this.calculateLearningProgress(totalActions)
        }
      };
    } catch (error) {
      console.error('Error extracting learned preferences:', error);
      return { preferences: {}, confidence: 0, summary: {} };
    }
  }

  /**
   * Calculate behavior-based score for v2.0
   */
  async calculateBehaviorBasedScore(currentUser, match, behaviorInsights, interactionPatterns) {
    let behaviorScore = 0;

    // Age preference alignment
    if (interactionPatterns.patterns?.preferredAgeRange && match.profile?.age) {
      const ageRange = interactionPatterns.patterns.preferredAgeRange;
      if (match.profile.age >= ageRange.min && match.profile.age <= ageRange.max) {
        behaviorScore += 15;
      }
    }

    // Education preference alignment
    if (interactionPatterns.patterns?.preferredEducation?.length > 0 && match.profile?.education) {
      if (interactionPatterns.patterns.preferredEducation.includes(match.profile.education)) {
        behaviorScore += 12;
      }
    }

    // Occupation preference alignment
    if (interactionPatterns.patterns?.preferredOccupations?.length > 0 && match.profile?.occupation) {
      if (interactionPatterns.patterns.preferredOccupations.includes(match.profile.occupation)) {
        behaviorScore += 10;
      }
    }

    // Location preference alignment
    if (interactionPatterns.patterns?.preferredLocations?.length > 0 && match.profile?.city) {
      if (interactionPatterns.patterns.preferredLocations.includes(match.profile.city)) {
        behaviorScore += 8;
      }
    }

    // Response pattern boost
    if (interactionPatterns.patterns?.responsePatterns?.averageResponseTime) {
      // Users who respond quickly to similar profiles
      behaviorScore += 5;
    }

    return Math.min(behaviorScore, 50); // Cap at 50 points
  }

  /**
   * Calculate preference-based score
   */
  async calculatePreferenceScore(currentUser, match, learnedPreferences) {
    let preferenceScore = 0;

    // Physical preferences
    if (learnedPreferences.preferences?.physicalPreferences && match.profile) {
      const physicalMatch = this.calculatePhysicalPreferenceMatch(
        learnedPreferences.preferences.physicalPreferences,
        match.profile
      );
      preferenceScore += physicalMatch * 0.3;
    }

    // Career preferences
    if (learnedPreferences.preferences?.careerPreferences && match.profile?.occupation) {
      const careerMatch = this.calculateCareerPreferenceMatch(
        learnedPreferences.preferences.careerPreferences,
        match.profile.occupation
      );
      preferenceScore += careerMatch * 0.4;
    }

    // Lifestyle preferences
    if (learnedPreferences.preferences?.lifestylePreferences) {
      const lifestyleMatch = this.calculateLifestylePreferenceMatch(
        learnedPreferences.preferences.lifestylePreferences,
        match.profile
      );
      preferenceScore += lifestyleMatch * 0.3;
    }

    return Math.min(preferenceScore, 30); // Cap at 30 points
  }

  /**
   * Calculate temporal score based on user activity patterns
   */
  async calculateTemporalScore(currentUser, match, interactionPatterns) {
    // Simple temporal scoring - can be enhanced with more sophisticated timing analysis
    let temporalScore = 5; // Base temporal score

    // If user is more active during certain times, boost matches who are also active then
    if (interactionPatterns.patterns?.interactionTiming) {
      temporalScore += 3;
    }

    return temporalScore;
  }

  /**
   * Combine personalized scores with weights
   */
  combinePersonalizedScores(scores) {
    const weights = {
      base: 0.4,      // 40% base compatibility
      behavior: 0.3,   // 30% behavior patterns
      preference: 0.2, // 20% learned preferences
      temporal: 0.1    // 10% temporal patterns
    };

    return (
      scores.base * weights.base +
      scores.behavior * weights.behavior +
      scores.preference * weights.preference +
      scores.temporal * weights.temporal
    );
  }

  /**
   * Generate AI reasoning for matches
   */
  generateAIReasoning(currentUser, match, scores) {
    const reasons = [];

    if (scores.behavior > 15) {
      reasons.push("Strong alignment with your interaction patterns");
    }
    if (scores.preference > 20) {
      reasons.push("Matches your learned preferences from past likes");
    }
    if (scores.temporal > 5) {
      reasons.push("Active during similar times as you");
    }

    return reasons.length > 0 ? reasons.join(". ") : "Good overall compatibility based on AI analysis";
  }

  // Helper methods for preference analysis
  analyzeAgePreferences(interactions) {
    const likedAges = interactions
      .filter(i => i.interactionType === 'PROFILE_LIKE' || i.interactionType === 'INTEREST_SENT')
      .map(i => i.targetUser?.profile?.age)
      .filter(Boolean);

    if (likedAges.length === 0) return null;

    return {
      min: Math.min(...likedAges),
      max: Math.max(...likedAges),
      average: likedAges.reduce((a, b) => a + b, 0) / likedAges.length
    };
  }

  analyzeEducationPreferences(interactions) {
    const educationCounts = {};
    interactions
      .filter(i => i.interactionType === 'PROFILE_LIKE' || i.interactionType === 'INTEREST_SENT')
      .forEach(i => {
        const education = i.targetUser?.profile?.education;
        if (education) {
          educationCounts[education] = (educationCounts[education] || 0) + 1;
        }
      });

    return Object.entries(educationCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([education]) => education);
  }

  analyzeOccupationPreferences(interactions) {
    const occupationCounts = {};
    interactions
      .filter(i => i.interactionType === 'PROFILE_LIKE' || i.interactionType === 'INTEREST_SENT')
      .forEach(i => {
        const occupation = i.targetUser?.profile?.occupation;
        if (occupation) {
          occupationCounts[occupation] = (occupationCounts[occupation] || 0) + 1;
        }
      });

    return Object.entries(occupationCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([occupation]) => occupation);
  }

  analyzeLocationPreferences(interactions) {
    const locationCounts = {};
    interactions
      .filter(i => i.interactionType === 'PROFILE_LIKE' || i.interactionType === 'INTEREST_SENT')
      .forEach(i => {
        const city = i.targetUser?.profile?.city;
        if (city) {
          locationCounts[city] = (locationCounts[city] || 0) + 1;
        }
      });

    return Object.entries(locationCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([city]) => city);
  }

  analyzeInteractionTiming(interactions) {
    // Analyze when user is most active
    const hourCounts = {};
    interactions.forEach(i => {
      const hour = new Date(i.timestamp).getHours();
      hourCounts[hour] = (hourCounts[hour] || 0) + 1;
    });

    const mostActiveHour = Object.entries(hourCounts)
      .sort(([,a], [,b]) => b - a)[0];

    return {
      mostActiveHour: mostActiveHour ? parseInt(mostActiveHour[0]) : null,
      activityDistribution: hourCounts
    };
  }

  analyzeResponsePatterns(interactions) {
    const responses = interactions.filter(i =>
      i.interactionType === 'INTEREST_ACCEPTED' || i.interactionType === 'INTEREST_DECLINED'
    );

    return {
      totalResponses: responses.length,
      acceptanceRate: responses.length > 0 ?
        responses.filter(r => r.interactionType === 'INTEREST_ACCEPTED').length / responses.length : 0
    };
  }

  analyzeRejectionPatterns(interactions) {
    const rejections = interactions.filter(i =>
      i.interactionType === 'PROFILE_DISLIKE' || i.interactionType === 'INTEREST_DECLINED'
    );

    // Analyze common characteristics of rejected profiles
    const rejectedAges = rejections.map(r => r.targetUser?.profile?.age).filter(Boolean);
    const rejectedEducations = rejections.map(r => r.targetUser?.profile?.education).filter(Boolean);

    return {
      totalRejections: rejections.length,
      commonRejectedAges: rejectedAges.length > 0 ? {
        min: Math.min(...rejectedAges),
        max: Math.max(...rejectedAges)
      } : null,
      commonRejectedEducations: [...new Set(rejectedEducations)]
    };
  }

  generatePatternSummary(patterns) {
    const summary = [];

    if (patterns.preferredAgeRange) {
      summary.push(`Prefers ages ${patterns.preferredAgeRange.min}-${patterns.preferredAgeRange.max}`);
    }

    if (patterns.preferredEducation?.length > 0) {
      summary.push(`Interested in ${patterns.preferredEducation.slice(0, 2).join(', ')} education`);
    }

    if (patterns.preferredOccupations?.length > 0) {
      summary.push(`Attracted to ${patterns.preferredOccupations.slice(0, 2).join(', ')} professionals`);
    }

    return summary.join('. ');
  }

  extractPhysicalPreferences(profileLikes) {
    const likedProfiles = profileLikes.filter(like => like.likeType === 'LIKE' || like.likeType === 'SUPER_LIKE');

    if (likedProfiles.length === 0) return {};

    const heights = likedProfiles.map(p => p.targetUser?.profile?.height).filter(Boolean);

    return {
      preferredHeightRange: heights.length > 0 ? {
        min: Math.min(...heights),
        max: Math.max(...heights),
        average: heights.reduce((a, b) => a + b, 0) / heights.length
      } : null
    };
  }

  extractPersonalityPreferences(interests) {
    // Analyze personality traits from interest interactions
    return {
      communicationStyle: 'analyzed_from_messages', // Placeholder
      personalityTraits: 'extracted_from_interactions' // Placeholder
    };
  }

  extractLifestylePreferences(shortlists) {
    // Analyze lifestyle preferences from shortlisted profiles
    return {
      lifestyleType: 'analyzed_from_shortlists', // Placeholder
      interests: 'common_interests_identified' // Placeholder
    };
  }

  extractCareerPreferences(allInteractions) {
    const occupations = allInteractions
      .map(i => i.targetUser?.profile?.occupation)
      .filter(Boolean);

    const occupationCounts = {};
    occupations.forEach(occ => {
      occupationCounts[occ] = (occupationCounts[occ] || 0) + 1;
    });

    return {
      preferredOccupations: Object.entries(occupationCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3)
        .map(([occ]) => occ)
    };
  }

  extractFamilyPreferences(allInteractions) {
    // Placeholder for family preference analysis
    return {
      familyType: 'analyzed_from_profiles',
      familyValues: 'extracted_from_interactions'
    };
  }

  calculatePhysicalPreferenceMatch(physicalPrefs, profile) {
    let score = 0;

    if (physicalPrefs.preferredHeightRange && profile.height) {
      const heightRange = physicalPrefs.preferredHeightRange;
      if (profile.height >= heightRange.min && profile.height <= heightRange.max) {
        score += 20;
      }
    }

    return score;
  }

  calculateCareerPreferenceMatch(careerPrefs, occupation) {
    if (careerPrefs.preferredOccupations?.includes(occupation)) {
      return 25;
    }
    return 0;
  }

  calculateLifestylePreferenceMatch(lifestylePrefs, profile) {
    // Placeholder for lifestyle matching logic
    return 15; // Base lifestyle score
  }

  calculateLearningProgress(totalActions) {
    // Calculate how much the AI has learned about user preferences
    if (totalActions < 10) return 'beginner';
    if (totalActions < 30) return 'learning';
    if (totalActions < 50) return 'intermediate';
    return 'advanced';
  }

  calculatePersonalizationLevel(behaviorInsights) {
    const confidence = behaviorInsights.confidence || 0;
    if (confidence < 0.3) return 'low';
    if (confidence < 0.7) return 'medium';
    return 'high';
  }

  // ==================== ADVANCED v2.5 ALGORITHM METHODS ====================

  /**
   * Apply intelligent features for v2.5 matching
   */
  async applyIntelligentFeatures(currentUser, match, baseScore, behaviorInsights, interactionPatterns) {
    let intelligentScore = baseScore;

    // 1. Conversation Starter Prediction
    const conversationScore = await this.predictConversationSuccess(currentUser, match);
    intelligentScore += conversationScore * 0.15;

    // 2. Response Likelihood Prediction
    const responseScore = await this.predictResponseLikelihood(currentUser, match, interactionPatterns);
    intelligentScore += responseScore * 0.20;

    // 3. Long-term Compatibility Prediction
    const longTermScore = await this.predictLongTermCompatibility(currentUser, match, behaviorInsights);
    intelligentScore += longTermScore * 0.25;

    // 4. Mutual Interest Prediction
    const mutualScore = await this.predictMutualInterest(currentUser, match, behaviorInsights);
    intelligentScore += mutualScore * 0.20;

    // 5. Timing Optimization
    const timingScore = await this.calculateOptimalTimingScore(currentUser, match, interactionPatterns);
    intelligentScore += timingScore * 0.10;

    // 6. Communication Style Compatibility
    const communicationScore = await this.calculateCommunicationCompatibility(currentUser, match);
    intelligentScore += communicationScore * 0.10;

    return Math.min(intelligentScore, 100);
  }

  /**
   * Predict conversation success likelihood
   */
  async predictConversationSuccess(currentUser, match) {
    // Analyze common interests and topics
    const commonInterests = await this.findCommonInterests(currentUser, match);
    const personalityCompatibility = await this.calculatePersonalityCompatibility(currentUser, match);

    let score = 0;

    // Common interests boost
    if (commonInterests.length > 0) {
      score += Math.min(commonInterests.length * 5, 20);
    }

    // Personality compatibility boost
    score += personalityCompatibility * 0.3;

    // Education level compatibility
    if (currentUser.profile?.education === match.profile?.education) {
      score += 10;
    }

    return Math.min(score, 30);
  }

  /**
   * Predict response likelihood based on user patterns
   */
  async predictResponseLikelihood(currentUser, match, interactionPatterns) {
    let score = 0;

    // Base response rate from patterns
    const baseResponseRate = interactionPatterns.patterns?.responsePatterns?.acceptanceRate || 0;
    score += baseResponseRate * 20;

    // Profile completeness factor
    const profileCompleteness = this.calculateProfileCompleteness(match.profile);
    score += profileCompleteness * 0.15;

    // Verification status boost
    if (match.isVerified) {
      score += 8;
    }

    // Premium status consideration
    if (match.isPremium) {
      score += 5;
    }

    // Recent activity boost
    const daysSinceActive = match.lastActiveAt ?
      (Date.now() - new Date(match.lastActiveAt).getTime()) / (1000 * 60 * 60 * 24) : 30;

    if (daysSinceActive < 1) score += 10;
    else if (daysSinceActive < 7) score += 5;

    return Math.min(score, 35);
  }

  /**
   * Predict long-term relationship compatibility
   */
  async predictLongTermCompatibility(currentUser, match, behaviorInsights) {
    let score = 0;

    // Life goals alignment
    const lifeGoalsScore = await this.calculateLifeGoalsAlignment(currentUser, match);
    score += lifeGoalsScore * 0.4;

    // Family values compatibility
    const familyValuesScore = await this.calculateFamilyValuesCompatibility(currentUser, match);
    score += familyValuesScore * 0.3;

    // Lifestyle compatibility
    const lifestyleScore = await this.calculateLifestyleCompatibility(currentUser, match);
    score += lifestyleScore * 0.2;

    // Financial compatibility
    const financialScore = await this.calculateFinancialCompatibility(currentUser, match);
    score += financialScore * 0.1;

    return Math.min(score, 40);
  }

  /**
   * Predict mutual interest likelihood
   */
  async predictMutualInterest(currentUser, match, behaviorInsights) {
    let score = 0;

    // Analyze if match fits user's historical preferences
    const preferenceFit = await this.calculatePreferenceFit(currentUser, match, behaviorInsights);
    score += preferenceFit * 0.6;

    // Analyze if user fits match's likely preferences (reverse prediction)
    const reversePreferenceFit = await this.calculateReversePreferenceFit(currentUser, match);
    score += reversePreferenceFit * 0.4;

    return Math.min(score, 25);
  }

  /**
   * Calculate optimal timing score
   */
  async calculateOptimalTimingScore(currentUser, match, interactionPatterns) {
    let score = 5; // Base score

    // User activity pattern alignment
    if (interactionPatterns.patterns?.interactionTiming) {
      const userActiveHour = interactionPatterns.patterns.interactionTiming.mostActiveHour;

      // If we have match's activity data, compare
      // For now, give a moderate boost
      score += 3;
    }

    // Day of week optimization
    const currentDay = new Date().getDay();
    if (currentDay >= 1 && currentDay <= 5) { // Weekdays
      score += 2;
    }

    return score;
  }

  /**
   * Calculate communication style compatibility
   */
  async calculateCommunicationCompatibility(currentUser, match) {
    // Analyze communication patterns from messages/interests
    let score = 10; // Base compatibility

    // Profile writing style analysis (placeholder)
    if (currentUser.profile?.bio && match.profile?.bio) {
      // Simple length comparison as proxy for communication style
      const userBioLength = currentUser.profile.bio.length;
      const matchBioLength = match.profile.bio.length;

      const lengthDiff = Math.abs(userBioLength - matchBioLength);
      if (lengthDiff < 50) {
        score += 5; // Similar communication styles
      }
    }

    return score;
  }

  // Helper methods for intelligent features
  async findCommonInterests(currentUser, match) {
    // Placeholder - would analyze hobbies, interests, etc.
    return ['travel', 'movies']; // Mock common interests
  }

  async calculatePersonalityCompatibility(currentUser, match) {
    // Placeholder for personality analysis
    return 15; // Mock personality compatibility score
  }

  calculateProfileCompleteness(profile) {
    if (!profile) return 0;

    const fields = ['firstName', 'lastName', 'age', 'height', 'education', 'occupation', 'city', 'bio'];
    const completedFields = fields.filter(field => profile[field] && profile[field].toString().trim().length > 0);

    return (completedFields.length / fields.length) * 100;
  }

  async calculateLifeGoalsAlignment(currentUser, match) {
    // Placeholder for life goals analysis
    return 20; // Mock life goals alignment score
  }

  async calculateFamilyValuesCompatibility(currentUser, match) {
    // Placeholder for family values analysis
    return 18; // Mock family values compatibility
  }

  async calculateLifestyleCompatibility(currentUser, match) {
    // Placeholder for lifestyle analysis
    return 16; // Mock lifestyle compatibility
  }

  async calculateFinancialCompatibility(currentUser, match) {
    // Basic financial compatibility based on occupation/education
    let score = 10;

    if (currentUser.profile?.occupation && match.profile?.occupation) {
      // Simple occupation-based compatibility
      if (currentUser.profile.occupation === match.profile.occupation) {
        score += 5;
      }
    }

    return score;
  }

  async calculatePreferenceFit(currentUser, match, behaviorInsights) {
    // Calculate how well match fits user's learned preferences
    let score = 0;

    if (behaviorInsights.topPreferences) {
      // Age preference
      if (behaviorInsights.topPreferences.age && match.profile?.age) {
        const ageRange = behaviorInsights.topPreferences.age;
        if (match.profile.age >= ageRange.min && match.profile.age <= ageRange.max) {
          score += 8;
        }
      }

      // Education preference
      if (behaviorInsights.topPreferences.education?.includes(match.profile?.education)) {
        score += 6;
      }

      // Occupation preference
      if (behaviorInsights.topPreferences.occupation?.includes(match.profile?.occupation)) {
        score += 5;
      }
    }

    return score;
  }

  async calculateReversePreferenceFit(currentUser, match) {
    // Predict if user fits match's likely preferences
    // This is a simplified reverse prediction
    let score = 10; // Base assumption of moderate fit

    // Age compatibility from match's perspective
    if (currentUser.profile?.age && match.profile?.age) {
      const ageDiff = Math.abs(currentUser.profile.age - match.profile.age);
      if (ageDiff <= 3) score += 5;
      else if (ageDiff <= 5) score += 3;
    }

    // Education level compatibility
    if (currentUser.profile?.education === match.profile?.education) {
      score += 3;
    }

    return score;
  }
}

module.exports = new MLMatchingService();
