/**
 * Vaivahik API Server
 *
 * This is the main entry point for the Vaivahik API server.
 * It sets up the Express application, middleware, routes, and starts the server.
 */

// Load environment variables
require('dotenv').config();

// Import dependencies
const express = require('express');
const cors = require('cors');
const path = require('path');
const http = require('http');
const socketio = require('socket.io');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');

// Import custom modules
const connectDB = require('./config/db');
const routes = require('./src/routes');
const { errorHandler, notFoundHandler } = require('./src/middleware/errorHandler');
const requestLogger = require('./src/middleware/requestLogger');
const activityTracker = require('./middleware/activityTracker');
const OnlineStatusManager = require('./services/onlineStatusManager');
const logger = require('./src/utils/logger');

// Initialize Express app
const app = express();

// Create HTTP server with Express
const server = http.createServer(app);

// Initialize Socket.IO with the server
const io = socketio(server, {
  cors: {
    origin: process.env.NODE_ENV === 'production'
      ? process.env.FRONTEND_URL || 'https://vaivahik.com'
      : 'http://localhost:3000',
    methods: ['GET', 'POST'],
    credentials: true
  }
});

// Initialize Online Status Manager
const onlineStatusManager = new OnlineStatusManager(io);

// Connect to MongoDB
connectDB();

// Security middleware
app.use(helmet());

// Enable CORS
app.use(cors({
  origin: process.env.NODE_ENV === 'production'
    ? process.env.FRONTEND_URL || 'https://vaivahik.com'
    : 'http://localhost:3000',
  credentials: true
}));

// Parse JSON request body
app.use(express.json({ limit: '10mb' }));

// Parse URL-encoded request body
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Compress responses
app.use(compression());

// HTTP request logging
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// Custom request logger
app.use(requestLogger);

// Performance monitoring
const { performanceMonitor } = require('./src/middleware/performanceMonitor');
app.use(performanceMonitor);

// Rate limiting
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    success: false,
    message: 'Too many requests, please try again later.',
    errorCode: 'TOO_MANY_REQUESTS'
  }
});

// Apply rate limiting to all API routes
app.use('/api', apiLimiter);

// Apply activity tracking middleware to all authenticated routes
const authMiddleware = require('./middleware/auth');
const trackActivity = (req, res, next) => {
  // Skip activity tracking for certain routes
  const skipRoutes = ['/api/auth/login', '/api/auth/register', '/api/auth/verify'];
  if (skipRoutes.includes(req.path)) {
    return next();
  }

  // Apply auth middleware first, then activity tracker
  authMiddleware(req, res, (err) => {
    if (err) return next(err);
    activityTracker(req, res, next);
  });
};

// Mount API routes
app.use('/api', routes);

// API endpoint to check user online status
app.get('/api/users/:userId/status', authMiddleware, (req, res) => {
  const { userId } = req.params;
  const isOnline = onlineStatusManager.isUserOnline(userId);

  res.json({
    success: true,
    isOnline,
    timestamp: new Date().toISOString()
  });
});

// Serve static assets in production
if (process.env.NODE_ENV === 'production') {
  // Set static folder
  app.use(express.static('client/build'));

  app.get('*', (req, res) => {
    res.sendFile(path.resolve(__dirname, 'client', 'build', 'index.html'));
  });
}

// 404 handler for undefined routes
app.use(notFoundHandler);

// Global error handler
app.use(errorHandler);

// Define PORT
const PORT = process.env.PORT || 5000;

// Start server
server.listen(PORT, () => {
  logger.info(`Server started on port ${PORT} in ${process.env.NODE_ENV || 'development'} mode`);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  logger.error('Unhandled Promise Rejection:', err);
  // Don't crash the server in production
  if (process.env.NODE_ENV === 'development') {
    process.exit(1);
  }
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception:', err);
  // Don't crash the server in production
  if (process.env.NODE_ENV === 'development') {
    process.exit(1);
  }
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received. Shutting down gracefully...');
  server.close(() => {
    logger.info('Process terminated.');
  });
});
