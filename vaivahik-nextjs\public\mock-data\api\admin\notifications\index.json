{"success": true, "notifications": [{"id": 1, "title": "New User Registration", "message": "<PERSON><PERSON> has registered and completed her profile", "type": "user_registration", "priority": "medium", "timestamp": "2024-01-15T10:30:00Z", "read": false, "userId": 1001, "userDetails": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "profileCompletion": 85}}, {"id": 2, "title": "Profile Verification Required", "message": "<PERSON><PERSON> has submitted documents for verification", "type": "verification_pending", "priority": "high", "timestamp": "2024-01-15T09:45:00Z", "read": false, "userId": 1002, "userDetails": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "documentType": "<PERSON><PERSON><PERSON><PERSON>"}}, {"id": 3, "title": "Premium Subscription", "message": "<PERSON> has upgraded to Premium plan", "type": "premium_upgrade", "priority": "medium", "timestamp": "2024-01-15T08:20:00Z", "read": true, "userId": 1003, "userDetails": {"name": "<PERSON>", "email": "<EMAIL>", "plan": "Premium Monthly", "amount": 999}}, {"id": 4, "title": "Profile Reported", "message": "Profile ID #1004 has been reported for inappropriate content", "type": "profile_reported", "priority": "high", "timestamp": "2024-01-15T07:15:00Z", "read": false, "userId": 1004, "reportDetails": {"reportedBy": "User #1005", "reason": "Inappropriate photos", "description": "Profile contains photos that violate community guidelines"}}, {"id": 5, "title": "Successful Match", "message": "<PERSON><PERSON><PERSON> and <PERSON><PERSON> have connected successfully", "type": "successful_match", "priority": "low", "timestamp": "2024-01-15T06:30:00Z", "read": true, "matchDetails": {"user1": {"id": 1006, "name": "<PERSON><PERSON><PERSON>"}, "user2": {"id": 1007, "name": "<PERSON><PERSON>"}, "compatibility": 92}}, {"id": 6, "title": "Payment Failed", "message": "Payment failed for <PERSON><PERSON>'s premium subscription", "type": "payment_failed", "priority": "high", "timestamp": "2024-01-15T05:45:00Z", "read": false, "userId": 1008, "paymentDetails": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "amount": 2499, "plan": "Premium Quarterly", "failureReason": "Insufficient funds"}}, {"id": 7, "title": "System Alert", "message": "High server load detected - monitoring required", "type": "system_alert", "priority": "high", "timestamp": "2024-01-15T04:20:00Z", "read": true, "systemDetails": {"serverLoad": "85%", "activeUsers": 1247, "responseTime": "2.3s"}}, {"id": 8, "title": "New Feature Request", "message": "Multiple users have requested video calling feature", "type": "feature_request", "priority": "medium", "timestamp": "2024-01-15T03:10:00Z", "read": false, "featureDetails": {"feature": "Video Calling", "requestCount": 23, "priority": "High", "estimatedDevelopmentTime": "4 weeks"}}], "pagination": {"currentPage": 1, "totalPages": 3, "totalItems": 24, "itemsPerPage": 8}, "summary": {"total": 24, "unread": 12, "highPriority": 6, "mediumPriority": 10, "lowPriority": 8}, "filters": {"types": ["user_registration", "verification_pending", "premium_upgrade", "profile_reported", "successful_match", "payment_failed", "system_alert", "feature_request"], "priorities": ["high", "medium", "low"], "dateRange": {"from": "2024-01-01T00:00:00Z", "to": "2024-01-15T23:59:59Z"}}}