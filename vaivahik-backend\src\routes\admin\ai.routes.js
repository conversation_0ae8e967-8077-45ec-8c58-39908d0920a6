// src/routes/admin/ai.routes.js
const express = require('express');
const router = express.Router();
const aiController = require('../../controllers/admin/ai.controller');
const authenticateAdmin = require('../../middleware/adminAuth.middleware.js');

// AI & Matching Routes
router.get('/settings', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        aiController.getAiSettings(req, res, next);
    });
});

router.put('/settings', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        aiController.updateAiSettings(req, res, next);
    });
});

router.get('/ab-tests', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        aiController.getABTests(req, res, next);
    });
});

router.get('/analytics', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        aiController.getSuccessAnalytics(req, res, next);
    });
});

module.exports = router;
