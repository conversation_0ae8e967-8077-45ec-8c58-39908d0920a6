<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Artistic Biodata</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&family=Raleway:wght@300;400;500;600;700&display=swap');
        
        :root {
            --primary-color: #9c27b0; /* Purple */
            --secondary-color: #e91e63; /* Pink */
            --accent-color: #00bcd4; /* Cyan */
            --text-color: #333;
            --light-text: #666;
            --border-color: #eee;
            --light-bg: #f9f9f9;
            --header-font: 'Dancing Script', cursive;
            --body-font: 'Raleway', sans-serif;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--body-font);
            color: var(--text-color);
            line-height: 1.6;
            background-color: white;
            padding: 0;
            margin: 0;
        }
        
        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 30px;
            background-color: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }
        
        /* Watercolor Background */
        .watercolor-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('data:image/svg+xml;utf8,<svg width="500" height="500" viewBox="0 0 500 500" xmlns="http://www.w3.org/2000/svg"><defs><filter id="watercolor" x="-50%" y="-50%" width="200%" height="200%"><feTurbulence type="fractalNoise" baseFrequency="0.01" numOctaves="3" result="noise"/><feDisplacementMap in="SourceGraphic" in2="noise" scale="50" xChannelSelector="R" yChannelSelector="G" result="displaced"/><feGaussianBlur in="displaced" stdDeviation="10" result="blurred"/><feColorMatrix in="blurred" type="matrix" values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0.7 0" result="colored"/></filter></defs><rect width="100%" height="100%" fill="%23ffffff" filter="url(%23watercolor)"/><rect width="100%" height="100%" fill="%239c27b0" opacity="0.05" filter="url(%23watercolor)"/><rect width="100%" height="100%" fill="%23e91e63" opacity="0.05" filter="url(%23watercolor)"/><rect width="100%" height="100%" fill="%2300bcd4" opacity="0.05" filter="url(%23watercolor)"/></svg>');
            background-size: cover;
            opacity: 0.2;
            z-index: 0;
        }
        
        .content-wrapper {
            position: relative;
            z-index: 1;
            padding: 20px;
        }
        
        /* Invocation */
        .invocation {
            text-align: center;
            font-family: var(--header-font);
            color: var(--primary-color);
            padding: 10px 0;
            font-weight: 600;
            font-size: 24px;
            margin-bottom: 30px;
        }
        
        /* Header Section */
        .header {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 40px;
            position: relative;
        }
        
        .profile-photo-container {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            overflow: hidden;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 5px solid white;
            position: relative;
        }
        
        .profile-photo {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .name {
            font-family: var(--header-font);
            font-size: 40px;
            color: var(--primary-color);
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .tagline {
            font-size: 18px;
            color: var(--light-text);
            margin-bottom: 20px;
            font-style: italic;
            text-align: center;
            max-width: 80%;
        }
        
        .quick-info {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            font-size: 14px;
            background-color: white;
            padding: 8px 15px;
            border-radius: 30px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
        }
        
        .info-label {
            font-weight: 600;
            margin-right: 5px;
            color: var(--secondary-color);
        }
        
        /* Section Styling */
        .section {
            margin-bottom: 35px;
            position: relative;
        }
        
        .section-title {
            font-family: var(--header-font);
            color: var(--primary-color);
            font-size: 28px;
            margin-bottom: 20px;
            font-weight: 600;
            text-align: center;
            position: relative;
        }
        
        .section-title:after {
            content: '';
            position: absolute;
            left: 50%;
            bottom: -5px;
            transform: translateX(-50%);
            width: 80px;
            height: 2px;
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color), var(--accent-color));
        }
        
        .section-content {
            padding: 0 10px;
        }
        
        /* Artistic Cards */
        .artistic-card {
            background-color: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.05);
            margin-bottom: 25px;
            position: relative;
            overflow: hidden;
        }
        
        .artistic-card:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
        }
        
        .card-title {
            font-family: var(--header-font);
            color: var(--primary-color);
            font-size: 22px;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        /* Two Column Layout */
        .two-column {
            display: flex;
            gap: 30px;
            margin-bottom: 35px;
        }
        
        .column {
            flex: 1;
        }
        
        /* Details List */
        .details-list {
            list-style: none;
        }
        
        .details-list li {
            padding: 10px 0;
            display: flex;
            border-bottom: 1px dashed var(--border-color);
        }
        
        .details-list li:last-child {
            border-bottom: none;
        }
        
        .details-label {
            width: 40%;
            font-weight: 600;
            color: var(--secondary-color);
        }
        
        .details-value {
            width: 60%;
        }
        
        /* About & Interests */
        .about-section {
            background-color: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }
        
        .about-section:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color), var(--accent-color));
        }
        
        /* Expectations */
        .expectations {
            background-color: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }
        
        .expectations:before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, var(--secondary-color), var(--accent-color));
        }
        
        /* Photo Gallery */
        .photo-gallery {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            justify-content: center;
        }
        
        .gallery-photo {
            width: calc(33.33% - 10px);
            height: 150px;
            object-fit: cover;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .gallery-photo:hover {
            transform: scale(1.05);
        }
        
        /* Footer */
        .footer {
            margin-top: 40px;
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
            font-size: 14px;
            color: var(--light-text);
        }
        
        .branding {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 15px;
        }
        
        .brand-logo {
            height: 30px;
            margin-right: 10px;
        }
        
        .brand-name {
            font-weight: 500;
            color: var(--primary-color);
        }
        
        /* Print Styles */
        @media print {
            body {
                background-color: white;
            }
            
            .container {
                box-shadow: none;
                padding: 0;
                max-width: 100%;
            }
            
            @page {
                size: A4;
                margin: 1cm;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="watercolor-bg"></div>
        
        <div class="content-wrapper">
            <!-- Invocation -->
            <div class="invocation">
                ॥ श्री गणेशाय नमः ॥
            </div>
            
            <!-- Header Section -->
            <div class="header">
                <div class="profile-photo-container">
                    <img src="{{profilePicture}}" alt="Profile Photo" class="profile-photo">
                </div>
                <h1 class="name">{{name}}</h1>
                <p class="tagline">{{tagline}}</p>
                <div class="quick-info">
                    <div class="info-item">
                        <span class="info-label">Age:</span> {{age}} years
                    </div>
                    <div class="info-item">
                        <span class="info-label">Height:</span> {{height}}
                    </div>
                    <div class="info-item">
                        <span class="info-label">Education:</span> {{education}}
                    </div>
                    <div class="info-item">
                        <span class="info-label">Profession:</span> {{occupation}}
                    </div>
                </div>
            </div>
            
            <!-- Photo Gallery -->
            <div class="section">
                <div class="photo-gallery">
                    <img src="{{additionalPhotos.0}}" alt="Additional Photo" class="gallery-photo">
                    <img src="{{additionalPhotos.1}}" alt="Additional Photo" class="gallery-photo">
                    <img src="{{additionalPhotos.2}}" alt="Additional Photo" class="gallery-photo">
                </div>
            </div>
            
            <!-- About Me -->
            <div class="section">
                <h2 class="section-title">About Me</h2>
                <div class="section-content">
                    <div class="about-section">
                        <p style="font-size: 16px; line-height: 1.8;">{{aboutMe}}</p>
                        
                        <div style="margin-top: 20px;">
                            <div style="font-weight: 600; color: var(--primary-color); margin-bottom: 10px; font-size: 18px;">Hobbies & Interests</div>
                            <p style="font-size: 16px;">{{hobbies}}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Two Column Layout for Personal Details and Family Background -->
            <div class="section">
                <h2 class="section-title">Personal Information</h2>
                <div class="section-content">
                    <div class="two-column">
                        <!-- Personal Details -->
                        <div class="column">
                            <div class="artistic-card">
                                <h3 class="card-title">Personal Details</h3>
                                <ul class="details-list">
                                    <li>
                                        <div class="details-label">Date of Birth</div>
                                        <div class="details-value">{{dateOfBirth}}</div>
                                    </li>
                                    <li>
                                        <div class="details-label">Birth Place</div>
                                        <div class="details-value">{{birthPlace}}</div>
                                    </li>
                                    <li>
                                        <div class="details-label">Religion</div>
                                        <div class="details-value">{{religion}}</div>
                                    </li>
                                    <li>
                                        <div class="details-label">Caste</div>
                                        <div class="details-value">{{caste}}</div>
                                    </li>
                                    <li>
                                        <div class="details-label">Gotra</div>
                                        <div class="details-value">{{gotra}}</div>
                                    </li>
                                    <li>
                                        <div class="details-label">Marital Status</div>
                                        <div class="details-value">{{maritalStatus}}</div>
                                    </li>
                                    <li>
                                        <div class="details-label">Diet</div>
                                        <div class="details-value">{{diet}}</div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        
                        <!-- Family Background -->
                        <div class="column">
                            <div class="artistic-card">
                                <h3 class="card-title">Family Background</h3>
                                <ul class="details-list">
                                    <li>
                                        <div class="details-label">Father</div>
                                        <div class="details-value">{{fatherName}}</div>
                                    </li>
                                    <li>
                                        <div class="details-label">Occupation</div>
                                        <div class="details-value">{{fatherOccupation}}</div>
                                    </li>
                                    <li>
                                        <div class="details-label">Mother</div>
                                        <div class="details-value">{{motherName}}</div>
                                    </li>
                                    <li>
                                        <div class="details-label">Occupation</div>
                                        <div class="details-value">{{motherOccupation}}</div>
                                    </li>
                                    <li>
                                        <div class="details-label">Family Type</div>
                                        <div class="details-value">{{familyType}}</div>
                                    </li>
                                    <li>
                                        <div class="details-label">Siblings</div>
                                        <div class="details-value">{{siblings}}</div>
                                    </li>
                                    <li>
                                        <div class="details-label">Native Place</div>
                                        <div class="details-value">{{nativePlace}}</div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Education & Career -->
            <div class="section">
                <h2 class="section-title">Education & Career</h2>
                <div class="section-content">
                    <div class="artistic-card">
                        <div style="margin-bottom: 20px;">
                            <div style="font-weight: 600; color: var(--primary-color); margin-bottom: 5px; font-size: 18px;">Education</div>
                            <p style="font-size: 16px;">{{education}}</p>
                            <p style="font-size: 14px; color: var(--light-text); margin-top: 5px;">{{educationDetails}}</p>
                        </div>
                        
                        <div style="margin-bottom: 20px;">
                            <div style="font-weight: 600; color: var(--primary-color); margin-bottom: 5px; font-size: 18px;">Career</div>
                            <p style="font-size: 16px;">{{occupation}} at {{company}}</p>
                            <p style="font-size: 14px; color: var(--light-text); margin-top: 5px;">{{occupationDetails}}</p>
                        </div>
                        
                        <ul class="details-list" style="margin-top: 15px;">
                            <li>
                                <div class="details-label">Annual Income</div>
                                <div class="details-value">{{annualIncome}}</div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Partner Expectations -->
            <div class="section">
                <h2 class="section-title">Partner Expectations</h2>
                <div class="section-content">
                    <div class="expectations">
                        <p style="font-size: 16px; line-height: 1.8;">{{partnerPreferences}}</p>
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="section">
                <h2 class="section-title">Contact Information</h2>
                <div class="section-content">
                    <div class="artistic-card">
                        <ul class="details-list">
                            <li>
                                <div class="details-label">Current Location</div>
                                <div class="details-value">{{city}}, {{state}}, {{country}}</div>
                            </li>
                            <li>
                                <div class="details-label">Email</div>
                                <div class="details-value">{{email}}</div>
                            </li>
                            <li>
                                <div class="details-label">Phone</div>
                                <div class="details-value">{{phone}}</div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Footer with Branding -->
            <div class="footer">
                <div class="branding">
                    <img src="{{brandLogo}}" alt="Brand Logo" class="brand-logo">
                    <span class="brand-name">{{brandName}}</span>
                </div>
                <p>{{brandTagline}}</p>
                <p>Created on {{createdAt}}</p>
            </div>
        </div>
    </div>
</body>
</html>
