import { useState, useEffect } from 'react';
import { adminPost, adminPut } from '@/services/apiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';
import { toast } from 'react-toastify';
import {
  Box,
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormLabel,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Switch,
  TextField,
  Typography
} from '@mui/material';

const AdminUserDialog = ({ open, onClose, onSave, user, mode, roles }) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    role: '',
    permissions: [],
    status: 'active',
    password: '',
    confirmPassword: ''
  });
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name || '',
        email: user.email || '',
        role: user.role || '',
        permissions: user.permissions || [],
        status: user.status || 'active',
        password: '',
        confirmPassword: ''
      });
    } else {
      // Reset form for new user
      setFormData({
        name: '',
        email: '',
        role: '',
        permissions: [],
        status: 'active',
        password: '',
        confirmPassword: ''
      });
    }
    setErrors({});
  }, [user, open]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when field is updated
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const handleStatusChange = (e) => {
    setFormData(prev => ({ ...prev, status: e.target.checked ? 'active' : 'inactive' }));
  };

  const handlePermissionChange = (permission) => {
    setFormData(prev => {
      const newPermissions = prev.permissions.includes(permission)
        ? prev.permissions.filter(p => p !== permission)
        : [...prev.permissions, permission];
      
      return { ...prev, permissions: newPermissions };
    });
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        newErrors.email = 'Invalid email format';
      }
    }
    
    if (!formData.role) {
      newErrors.role = 'Role is required';
    }
    
    if (!user) { // New user requires password
      if (!formData.password) {
        newErrors.password = 'Password is required';
      } else if (formData.password.length < 8) {
        newErrors.password = 'Password must be at least 8 characters long';
      }
      
      if (!formData.confirmPassword) {
        newErrors.confirmPassword = 'Please confirm your password';
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    } else if (formData.password) { // Existing user with password change
      if (formData.password.length < 8) {
        newErrors.password = 'Password must be at least 8 characters long';
      }
      
      if (!formData.confirmPassword) {
        newErrors.confirmPassword = 'Please confirm your password';
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (mode === 'view') {
      onClose();
      return;
    }
    
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }
    
    setLoading(true);
    try {
      // Remove confirmPassword from the data sent to the API
      const { confirmPassword, ...dataToSend } = formData;
      
      // Only include password if it's provided
      if (!dataToSend.password) {
        delete dataToSend.password;
      }
      
      let response;
      if (user) {
        // Update existing user
        response = await adminPut(ADMIN_ENDPOINTS.ADMIN_USER_DETAILS(user.id), dataToSend);
      } else {
        // Create new user
        response = await adminPost(ADMIN_ENDPOINTS.ADMIN_USERS, dataToSend);
      }
      
      if (response.success) {
        toast.success(user ? 'Admin user updated successfully' : 'Admin user created successfully');
        onSave();
      } else {
        toast.error(response.message || 'Failed to save admin user');
      }
    } catch (error) {
      console.error('Error saving admin user:', error);
      toast.error('Error saving admin user');
    } finally {
      setLoading(false);
    }
  };

  // Available permissions based on role
  const getAvailablePermissions = () => {
    const allPermissions = {
      'SUPER_ADMIN': ['all'],
      'CONTENT_MANAGER': ['blog_posts', 'success_stories', 'biodata_templates', 'email_templates'],
      'USER_MANAGER': ['users', 'verification', 'reports', 'notifications'],
      'FINANCE_ADMIN': ['subscriptions', 'transactions', 'revenue_reports', 'referral_programs'],
      'SUPPORT_STAFF': ['users', 'verification', 'reports', 'notifications', 'success_stories']
    };
    
    return formData.role ? (allPermissions[formData.role] || []) : [];
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {mode === 'create' ? 'Add New Admin User' : mode === 'edit' ? 'Edit Admin User' : 'View Admin User'}
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              disabled={mode === 'view'}
              error={!!errors.name}
              helperText={errors.name}
              required
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              disabled={mode === 'view'}
              error={!!errors.email}
              helperText={errors.email}
              required
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth error={!!errors.role}>
              <InputLabel>Role</InputLabel>
              <Select
                name="role"
                value={formData.role}
                onChange={handleChange}
                label="Role"
                disabled={mode === 'view' || (user && user.role === 'SUPER_ADMIN')}
                required
              >
                {roles.map((role) => (
                  <MenuItem key={role} value={role}>
                    {role.replace('_', ' ')}
                  </MenuItem>
                ))}
              </Select>
              {errors.role && (
                <Typography color="error" variant="caption">
                  {errors.role}
                </Typography>
              )}
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.status === 'active'}
                  onChange={handleStatusChange}
                  disabled={mode === 'view' || (user && user.role === 'SUPER_ADMIN')}
                />
              }
              label={`Status: ${formData.status === 'active' ? 'Active' : 'Inactive'}`}
            />
          </Grid>
          
          {mode !== 'view' && (
            <>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={user ? 'New Password (leave blank to keep current)' : 'Password'}
                  name="password"
                  type="password"
                  value={formData.password}
                  onChange={handleChange}
                  error={!!errors.password}
                  helperText={errors.password}
                  required={!user}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Confirm Password"
                  name="confirmPassword"
                  type="password"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  error={!!errors.confirmPassword}
                  helperText={errors.confirmPassword}
                  required={!user || !!formData.password}
                />
              </Grid>
            </>
          )}
          
          <Grid item xs={12}>
            <FormControl component="fieldset" sx={{ mt: 2 }}>
              <FormLabel component="legend">Permissions</FormLabel>
              <FormGroup>
                <Grid container spacing={2}>
                  {getAvailablePermissions().map((permission) => (
                    <Grid item xs={12} sm={6} md={4} key={permission}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={formData.permissions.includes(permission)}
                            onChange={() => handlePermissionChange(permission)}
                            disabled={mode === 'view' || (user && user.role === 'SUPER_ADMIN')}
                          />
                        }
                        label={permission.replace('_', ' ')}
                      />
                    </Grid>
                  ))}
                </Grid>
              </FormGroup>
            </FormControl>
          </Grid>
          
          {user && (
            <Grid item xs={12}>
              <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                <Typography variant="subtitle2" color="textSecondary">
                  Last Login: {user.lastLogin ? new Date(user.lastLogin).toLocaleString() : 'Never'}
                </Typography>
                <Typography variant="subtitle2" color="textSecondary">
                  Created: {new Date(user.createdAt).toLocaleString()}
                </Typography>
              </Box>
            </Grid>
          )}
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>
          {mode === 'view' ? 'Close' : 'Cancel'}
        </Button>
        {mode !== 'view' && (
          <Button 
            onClick={handleSubmit} 
            color="primary" 
            variant="contained"
            disabled={loading}
          >
            {loading ? 'Saving...' : user ? 'Update' : 'Create'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default AdminUserDialog;
