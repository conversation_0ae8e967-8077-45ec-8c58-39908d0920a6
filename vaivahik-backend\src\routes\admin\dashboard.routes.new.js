/**
 * Admin Dashboard Routes
 *
 * Routes for the admin dashboard data, recent activity, and recent users.
 */
const express = require('express');
const router = express.Router();
const dashboardController = require('../../controllers/admin/dashboard.controller.new');
const authenticateAdmin = require('../../middleware/adminAuth.middleware');

// Get dashboard data
router.get('/', function(req, res, next) {
  authenticateAdmin(req, res, function() {
    dashboardController.getDashboardData(req, res, next);
  });
});

// Get recent activity
router.get('/recent-activity', function(req, res, next) {
  authenticateAdmin(req, res, function() {
    dashboardController.getRecentActivity(req, res, next);
  });
});

// Get recent users
router.get('/recent-users', function(req, res, next) {
  authenticateAdmin(req, res, function() {
    dashboardController.getRecentUsers(req, res, next);
  });
});

module.exports = router;
