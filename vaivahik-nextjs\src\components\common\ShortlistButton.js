import React, { useState, useEffect } from 'react';
import {
  IconButton,
  Tooltip,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Snackbar,
  Alert
} from '@mui/material';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';
import { 
  isProfileShortlisted, 
  addToShortlist, 
  removeFromShortlist, 
  updateShortlistNotes 
} from '@/services/shortlistService';

/**
 * Shortlist Button Component
 * 
 * Reusable button for shortlisting/un-shortlisting profiles
 * @param {Object} props - Component props
 * @param {String} props.userId - User ID to shortlist/un-shortlist
 * @param {String} props.size - Button size (small, medium, large)
 * @param {Boolean} props.showTooltip - Whether to show tooltip
 * @param {Function} props.onShortlistChange - Callback when shortlist status changes
 */
const ShortlistButton = ({ 
  userId, 
  size = 'medium', 
  showTooltip = true,
  onShortlistChange = null
}) => {
  const [isShortlisted, setIsShortlisted] = useState(false);
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [notesDialog, setNotesDialog] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });
  
  // Check if profile is shortlisted on component mount
  useEffect(() => {
    const checkShortlistStatus = async () => {
      try {
        setInitialLoading(true);
        const result = await isProfileShortlisted(userId);
        setIsShortlisted(result.isShortlisted);
        setNotes(result.notes || '');
      } catch (error) {
        console.error('Error checking shortlist status:', error);
      } finally {
        setInitialLoading(false);
      }
    };
    
    if (userId) {
      checkShortlistStatus();
    }
  }, [userId]);
  
  // Handle shortlist toggle
  const handleShortlistToggle = async () => {
    if (isShortlisted) {
      // Remove from shortlist
      try {
        setLoading(true);
        await removeFromShortlist(userId);
        
        setIsShortlisted(false);
        setNotes('');
        
        showSnackbar('Profile removed from shortlist', 'success');
        
        if (onShortlistChange) {
          onShortlistChange(false);
        }
      } catch (error) {
        console.error('Error removing from shortlist:', error);
        showSnackbar('Failed to remove from shortlist', 'error');
      } finally {
        setLoading(false);
      }
    } else {
      // Open notes dialog for adding to shortlist
      setNotesDialog(true);
    }
  };
  
  // Handle add to shortlist
  const handleAddToShortlist = async () => {
    try {
      setLoading(true);
      await addToShortlist(userId, notes);
      
      setIsShortlisted(true);
      setNotesDialog(false);
      
      showSnackbar('Profile added to shortlist', 'success');
      
      if (onShortlistChange) {
        onShortlistChange(true, notes);
      }
    } catch (error) {
      console.error('Error adding to shortlist:', error);
      showSnackbar('Failed to add to shortlist', 'error');
    } finally {
      setLoading(false);
    }
  };
  
  // Show snackbar
  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  };
  
  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };
  
  // Render button with tooltip if needed
  const renderButton = () => {
    const button = (
      <IconButton
        color={isShortlisted ? 'warning' : 'default'}
        onClick={handleShortlistToggle}
        disabled={loading || initialLoading}
        size={size}
      >
        {loading || initialLoading ? (
          <CircularProgress size={size === 'small' ? 16 : 24} />
        ) : isShortlisted ? (
          <BookmarkIcon />
        ) : (
          <BookmarkBorderIcon />
        )}
      </IconButton>
    );
    
    if (showTooltip) {
      return (
        <Tooltip title={isShortlisted ? 'Remove from Shortlist' : 'Add to Shortlist'}>
          {button}
        </Tooltip>
      );
    }
    
    return button;
  };
  
  return (
    <>
      {renderButton()}
      
      {/* Notes Dialog */}
      <Dialog
        open={notesDialog}
        onClose={() => setNotesDialog(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>Add to Shortlist</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Notes (Optional)"
            fullWidth
            multiline
            rows={4}
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Add notes about this profile (e.g., good family background, similar interests)"
            variant="outlined"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNotesDialog(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleAddToShortlist} 
            color="primary"
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Add to Shortlist'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default ShortlistButton;
