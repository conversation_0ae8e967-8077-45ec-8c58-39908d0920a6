import React, { useEffect, useRef } from 'react';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import { Doughnut } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(ArcElement, Tooltip, Legend);

/**
 * MatchRateChart Component
 * 
 * @param {Object} props - Component props
 * @param {string} props.period - Time period for the chart (week, month, year)
 * @param {Object} props.data - Chart data with accepted and total matches
 * @param {boolean} props.loading - Loading state
 * @param {function} props.onRefresh - Function to refresh data
 * @returns {JSX.Element} - Chart component
 */
const MatchRateChart = ({ period = 'month', data = {}, loading = false, onRefresh }) => {
  const chartRef = useRef(null);
  
  // Calculate success rate
  const calculateSuccessRate = () => {
    if (data && data.accepted && data.total && data.total > 0) {
      return (data.accepted / data.total * 100).toFixed(1);
    }
    
    // Return mock data based on period if real data is not available
    switch (period) {
      case 'week':
        return 82.5;
      case 'month':
        return 78.3;
      case 'year':
        return 75.8;
      default:
        return 78.3;
    }
  };
  
  const successRate = calculateSuccessRate();
  
  // Generate chart data
  const chartData = {
    labels: ['Successful Matches', 'Pending Matches'],
    datasets: [
      {
        data: [successRate, 100 - successRate],
        backgroundColor: [
          'rgba(76, 175, 80, 0.8)',
          'rgba(224, 224, 224, 0.8)',
        ],
        borderColor: [
          'rgba(76, 175, 80, 1)',
          'rgba(224, 224, 224, 1)',
        ],
        borderWidth: 1,
        hoverOffset: 4,
      },
    ],
  };

  // Chart options
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: '75%',
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          boxWidth: 15,
          usePointStyle: true,
          pointStyle: 'circle',
          padding: 20,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#333',
        bodyColor: '#666',
        borderColor: '#ddd',
        borderWidth: 1,
        padding: 10,
        boxPadding: 5,
        usePointStyle: true,
        callbacks: {
          label: (context) => `${context.label}: ${context.parsed}%`,
        },
      },
    },
    animation: {
      animateRotate: true,
      animateScale: true,
      duration: 1000,
    },
  };

  // Set up auto-refresh interval
  useEffect(() => {
    // Refresh data every 5 minutes if onRefresh function is provided
    if (onRefresh) {
      const interval = setInterval(() => {
        onRefresh();
      }, 5 * 60 * 1000); // 5 minutes
      
      return () => clearInterval(interval);
    }
  }, [onRefresh]);

  return (
    <div style={{ position: 'relative', height: '100%', width: '100%', minHeight: '250px' }}>
      {loading ? (
        <div style={{ 
          position: 'absolute', 
          top: 0, 
          left: 0, 
          right: 0, 
          bottom: 0, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
          zIndex: 10
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ 
              width: '40px', 
              height: '40px', 
              border: '3px solid #f3f3f3', 
              borderTop: '3px solid #5e35b1', 
              borderRadius: '50%', 
              animation: 'spin 1s linear infinite', 
              margin: '0 auto 15px' 
            }}></div>
            <p style={{ color: '#666' }}>Loading chart data...</p>
          </div>
        </div>
      ) : null}
      <Doughnut ref={chartRef} data={chartData} options={options} />
      
      {/* Center text */}
      <div style={{ 
        position: 'absolute', 
        top: '50%', 
        left: '50%', 
        transform: 'translate(-50%, -50%)',
        textAlign: 'center',
        pointerEvents: 'none'
      }}>
        <div style={{ 
          fontSize: '2rem', 
          fontWeight: 'bold', 
          color: '#333' 
        }}>
          {successRate}%
        </div>
        <div style={{ 
          fontSize: '0.8rem', 
          color: '#666' 
        }}>
          Success Rate
        </div>
      </div>
    </div>
  );
};

export default MatchRateChart;
