/**
 * Mock Data Generator for Search
 * 
 * This utility generates realistic mock data for search results
 * when the application is not connected to the real backend.
 */

/**
 * Generate mock search results based on search parameters
 * @param {Object} searchParams - Search parameters
 * @param {boolean} isAdvanced - Whether this is an advanced search with more filters
 * @returns {Array} Array of mock profile results
 */
export const generateMockResults = (searchParams, isAdvanced = false) => {
  // Extract search parameters
  const {
    searchType = 'REGULAR',
    userId = '',
    targetGender = 'FEMALE',
    ageFrom = 18,
    ageTo = 35,
    heightFrom = 53, // 4'5"
    heightTo = 77, // 6'5"
    location = '',
    religion = '',
    caste = '',
    education = [],
    occupation = [],
    incomeRange = '',
    maritalStatus = [],
    diet = '',
    withPhoto = false,
    profileCreatedWithin = ''
  } = searchParams;

  // If searching by ID, return a single result
  if (searchType === 'ID' && userId) {
    return [generateMockProfile(userId, targetGender)];
  }

  // Generate between 15-50 results
  const count = Math.floor(Math.random() * 35) + 15;
  const results = [];

  for (let i = 0; i < count; i++) {
    // Generate a profile ID
    const profileId = `VAI${10000 + i}`;
    
    // Generate age within the specified range or default range
    const age = Math.floor(Math.random() * (ageTo - ageFrom + 1)) + ageFrom;
    
    // Generate height within the specified range
    const heightRange = heightTo - heightFrom;
    const height = (Math.random() * heightRange + heightFrom) / 12; // Convert to feet
    const heightFormatted = height.toFixed(1);
    
    // Generate random location from common Indian cities
    const cities = ['Mumbai', 'Pune', 'Nagpur', 'Nashik', 'Aurangabad', 'Kolhapur', 'Solapur', 'Thane', 'Navi Mumbai'];
    const city = location || cities[Math.floor(Math.random() * cities.length)];
    
    // Generate education level
    const educationLevels = ['HIGH_SCHOOL', 'DIPLOMA', 'BACHELORS', 'MASTERS', 'DOCTORATE'];
    const educationLevel = education && education.length > 0 
      ? education[Math.floor(Math.random() * education.length)]
      : educationLevels[Math.floor(Math.random() * educationLevels.length)];
    
    // Generate occupation
    const occupations = [
      'Software Engineer', 'Doctor', 'Teacher', 'Business Owner', 'Lawyer',
      'Accountant', 'Engineer', 'Government Employee', 'Bank Employee', 'Professor'
    ];
    const selectedOccupation = occupation && occupation.length > 0
      ? occupation[Math.floor(Math.random() * occupation.length)]
      : occupations[Math.floor(Math.random() * occupations.length)];
    
    // Generate religion (default to Hindu for Maratha community)
    const selectedReligion = religion || 'HINDU';
    
    // Generate caste (default to Maratha)
    const selectedCaste = caste || 'MARATHA';
    
    // Generate marital status
    const maritalStatuses = ['NEVER_MARRIED', 'DIVORCED', 'WIDOWED'];
    const selectedMaritalStatus = maritalStatus && maritalStatus.length > 0
      ? maritalStatus[Math.floor(Math.random() * maritalStatus.length)]
      : maritalStatuses[Math.floor(Math.random() * maritalStatuses.length)];
    
    // Generate diet preference
    const diets = ['VEGETARIAN', 'NON_VEGETARIAN', 'VEGAN', 'JAIN'];
    const selectedDiet = diet || diets[Math.floor(Math.random() * diets.length)];
    
    // Generate income range
    const incomeRanges = ['BELOW_5_LAKHS', '5_10_LAKHS', '10_15_LAKHS', '15_20_LAKHS', '20_30_LAKHS', 'ABOVE_30_LAKHS'];
    const selectedIncomeRange = incomeRange || incomeRanges[Math.floor(Math.random() * incomeRanges.length)];
    
    // Determine if profile is verified (30% chance)
    const isVerified = Math.random() < 0.3;
    
    // Determine if profile is premium (20% chance)
    const isPremium = Math.random() < 0.2;
    
    // Determine if profile is spotlighted (10% chance)
    const isSpotlighted = Math.random() < 0.1;
    
    // Create the profile object
    const profile = {
      id: profileId,
      name: `${targetGender === 'FEMALE' ? 'Priya' : 'Raj'} ${i + 1}`,
      age: age,
      height: heightFormatted,
      location: city,
      education: educationLevel,
      occupation: selectedOccupation,
      religion: selectedReligion,
      caste: selectedCaste,
      maritalStatus: selectedMaritalStatus,
      diet: selectedDiet,
      incomeRange: selectedIncomeRange,
      isVerified: isVerified,
      isPremium: isPremium,
      isSpotlighted: isSpotlighted,
      photoUrl: `/mock-profiles/${targetGender.toLowerCase()}${(i % 5) + 1}.jpg`
    };
    
    // Apply advanced filters if this is an advanced search
    if (isAdvanced) {
      // Filter by location if specified
      if (location && profile.location !== location) {
        continue;
      }
      
      // Filter by religion if specified
      if (religion && religion !== 'ANY' && profile.religion !== religion) {
        continue;
      }
      
      // Filter by caste if specified
      if (caste && caste !== 'ANY' && profile.caste !== caste) {
        continue;
      }
      
      // Filter by education if specified
      if (education && education.length > 0 && !education.includes(profile.education)) {
        continue;
      }
      
      // Filter by occupation if specified
      if (occupation && occupation.length > 0 && !occupation.includes(profile.occupation)) {
        continue;
      }
      
      // Filter by income range if specified
      if (incomeRange && incomeRange !== 'ANY' && profile.incomeRange !== incomeRange) {
        continue;
      }
      
      // Filter by marital status if specified
      if (maritalStatus && maritalStatus.length > 0 && !maritalStatus.includes(profile.maritalStatus)) {
        continue;
      }
      
      // Filter by diet if specified
      if (diet && diet !== 'ANY' && profile.diet !== diet) {
        continue;
      }
      
      // Filter by photo if specified
      if (withPhoto && !profile.photoUrl) {
        continue;
      }
    }
    
    results.push(profile);
  }

  return results;
};

/**
 * Generate a single mock profile by ID
 * @param {string} userId - User ID
 * @param {string} gender - Gender of the profile
 * @returns {Object} Mock profile
 */
export const generateMockProfile = (userId, gender = 'FEMALE') => {
  // Extract numeric part of ID for deterministic generation
  const idNumber = parseInt(userId.replace(/\D/g, '')) || 12345;
  
  // Use the ID number as a seed for "random" values
  const seed = idNumber % 100;
  
  return {
    id: userId,
    name: gender === 'FEMALE' ? 'Priya Sharma' : 'Raj Patil',
    age: 25 + (seed % 10),
    height: (5 + (seed % 10) / 10).toFixed(1),
    location: ['Mumbai', 'Pune', 'Nagpur', 'Nashik', 'Aurangabad'][seed % 5],
    education: ['BACHELORS', 'MASTERS', 'DOCTORATE'][seed % 3],
    occupation: ['Software Engineer', 'Doctor', 'Teacher', 'Business Owner', 'Lawyer'][seed % 5],
    religion: 'HINDU',
    caste: 'MARATHA',
    maritalStatus: 'NEVER_MARRIED',
    diet: ['VEGETARIAN', 'NON_VEGETARIAN'][seed % 2],
    incomeRange: ['5_10_LAKHS', '10_15_LAKHS', '15_20_LAKHS'][seed % 3],
    isVerified: seed % 3 === 0,
    isPremium: seed % 4 === 0,
    isSpotlighted: seed % 10 === 0,
    photoUrl: `/mock-profiles/${gender.toLowerCase()}${(seed % 5) + 1}.jpg`
  };
};
