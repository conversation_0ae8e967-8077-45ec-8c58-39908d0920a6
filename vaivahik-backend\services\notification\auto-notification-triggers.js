/**
 * Automatic Notification Triggers
 * This service automatically triggers notifications based on app events
 */

const notificationHandler = require('./notification-handler');
const { sendEmail } = require('../../src/services/email.service');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * Get privacy-safe display name based on user's privacy preferences
 * Respects user choice: Full Name, First Name, Profile ID, Anonymous
 */
const getPrivacySafeName = (user, context = 'notification') => {
  if (!user || !user.profile) {
    return 'User';
  }

  const profile = user.profile;

  // Check if user allows name display in this context
  const contextPermissions = {
    'notification': profile.showNameInNotifications,
    'search': profile.showNameInSearch,
    'matches': profile.showNameInMatches,
    'messages': profile.showNameInMessages,
    'public': true // Always respect user preference for public contexts
  };

  // If user doesn't allow name display in this context, use anonymous
  if (!contextPermissions[context]) {
    return getAnonymousName(user);
  }

  // Use user's display preference
  const displayPreference = profile.displayNamePreference || 'FIRST_NAME';

  switch (displayPreference) {
    case 'FULL_NAME':
      return profile.fullName || getProfileId(user);

    case 'FIRST_NAME':
      if (profile.fullName) {
        const firstName = profile.fullName.split(' ')[0];
        return firstName || getProfileId(user);
      }
      return getProfileId(user);

    case 'PROFILE_ID':
      return getProfileId(user);

    case 'ANONYMOUS':
      return getAnonymousName(user);

    default:
      // Default to first name for safety
      if (profile.fullName) {
        const firstName = profile.fullName.split(' ')[0];
        return firstName || getProfileId(user);
      }
      return getProfileId(user);
  }
};

/**
 * Generate profile ID based on gender and user ID
 */
const getProfileId = (user) => {
  if (!user || !user.profile) {
    return `Profile ${user?.id?.slice(-4) || 'XXXX'}`;
  }

  const gender = user.profile.gender?.toLowerCase();
  const idSuffix = user.id.slice(-4);

  switch (gender) {
    case 'female':
      return `Profile F${idSuffix}`;
    case 'male':
      return `Profile M${idSuffix}`;
    default:
      return `Profile ${idSuffix}`;
  }
};

/**
 * Generate anonymous name based on gender
 */
const getAnonymousName = (user) => {
  if (!user || !user.profile) {
    return 'Someone';
  }

  const gender = user.profile.gender?.toLowerCase();

  switch (gender) {
    case 'female':
      return 'Someone'; // Maximum privacy for female users
    case 'male':
      return 'Someone';
    default:
      return 'Someone';
  }
};

/**
 * Get display name for different contexts with privacy respect
 */
const getContextualDisplayName = (user, context, viewerUser = null) => {
  // If viewer is the same user, show full name
  if (viewerUser && user.id === viewerUser.id) {
    return user.profile?.fullName || 'You';
  }

  // Use privacy-safe name based on context
  return getPrivacySafeName(user, context);
};

/**
 * Auto-trigger notifications when new match is found
 * Call this from your matching algorithm
 */
const onNewMatchFound = async (userId, matchData) => {
  try {
    console.log(`🎯 Auto-triggering NEW_MATCH notification for user ${userId}`);

    // 1. Send push notification
    const pushResult = await notificationHandler.triggerNotification('NEW_MATCH', {
      userId,
      matchId: matchData.matchId,
      matchName: matchData.name,
      matchPhotoUrl: matchData.photoUrl,
      matchPercentage: matchData.compatibilityScore
    });

    // 2. Send email notification
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { profile: true }
    });

    if (user?.email) {
      const emailResult = await sendEmail({
        to: user.email,
        subject: '💕 New Match Found - Vaivahik',
        template: 'default',
        data: {
          name: getPrivacySafeName(user, 'notification'),
          message: `
            <h3>🎉 Great News! We found a ${matchData.compatibilityScore}% match for you!</h3>
            <div style="border: 2px solid #7e57c2; padding: 20px; margin: 20px 0; border-radius: 10px;">
              <h4>${matchData.name}</h4>
              <p><strong>Age:</strong> ${matchData.age} years</p>
              <p><strong>Location:</strong> ${matchData.location}</p>
              <p><strong>Education:</strong> ${matchData.education}</p>
              <p><strong>Profession:</strong> ${matchData.profession}</p>
            </div>
            <a href="${process.env.FRONTEND_URL}/profile/${matchData.matchId}"
               style="background: #7e57c2; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px;">
               View Profile
            </a>
          `
        }
      });
      console.log(`📧 Match email sent to ${user.email}`);
    }

    return { pushResult, emailSent: !!user?.email };
  } catch (error) {
    console.error('Error in onNewMatchFound:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Auto-trigger when someone views user's profile
 */
const onProfileViewed = async (profileOwnerId, viewerData) => {
  try {
    console.log(`👀 Auto-triggering PROFILE_VIEW notification for user ${profileOwnerId}`);

    return await notificationHandler.triggerNotification('PROFILE_VIEW', {
      profileOwnerId,
      viewerId: viewerData.viewerId,
      viewerName: viewerData.name,
      viewerPhotoUrl: viewerData.photoUrl
    });
  } catch (error) {
    console.error('Error in onProfileViewed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Auto-trigger when interest is received
 */
const onInterestReceived = async (recipientId, senderData) => {
  try {
    console.log(`💝 Auto-triggering INTEREST_RECEIVED notification for user ${recipientId}`);

    // Send push notification
    const pushResult = await notificationHandler.triggerNotification('INTEREST_RECEIVED', {
      recipientId,
      senderId: senderData.senderId,
      senderName: senderData.name,
      senderPhotoUrl: senderData.photoUrl,
      interestId: senderData.interestId
    });

    // Send email notification
    const user = await prisma.user.findUnique({
      where: { id: recipientId },
      include: { profile: true }
    });

    if (user?.email) {
      await sendEmail({
        to: user.email,
        subject: '💝 Someone is Interested in You - Vaivahik',
        template: 'default',
        data: {
          name: getPrivacySafeName(user, 'notification'),
          message: `
            <h3>💝 ${senderData.name} is interested in your profile!</h3>
            <div style="text-align: center; margin: 20px 0;">
              <img src="${senderData.photoUrl}" style="width: 100px; height: 100px; border-radius: 50%; border: 3px solid #7e57c2;">
              <h4>${senderData.name}</h4>
            </div>
            <p>Someone special has shown interest in your profile. Check out their profile and respond!</p>
            <a href="${process.env.FRONTEND_URL}/interests"
               style="background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px;">
               View Interest
            </a>
          `
        }
      });
    }

    return pushResult;
  } catch (error) {
    console.error('Error in onInterestReceived:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Auto-trigger when interest is accepted
 */
const onInterestAccepted = async (senderId, acceptorData) => {
  try {
    console.log(`✅ Auto-triggering INTEREST_ACCEPTED notification for user ${senderId}`);

    return await notificationHandler.triggerNotification('INTEREST_ACCEPTED', {
      senderId,
      acceptorId: acceptorData.acceptorId,
      acceptorName: acceptorData.name,
      acceptorPhotoUrl: acceptorData.photoUrl
    });
  } catch (error) {
    console.error('Error in onInterestAccepted:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Auto-trigger when new message is received
 */
const onNewMessage = async (recipientId, messageData) => {
  try {
    console.log(`💬 Auto-triggering NEW_MESSAGE notification for user ${recipientId}`);

    return await notificationHandler.triggerNotification('NEW_MESSAGE', {
      recipientId,
      senderId: messageData.senderId,
      senderName: messageData.senderName,
      senderPhotoUrl: messageData.senderPhotoUrl,
      messagePreview: messageData.messagePreview,
      conversationId: messageData.conversationId
    });
  } catch (error) {
    console.error('Error in onNewMessage:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Auto-trigger verification status updates
 */
const onVerificationStatusChange = async (userId, statusData) => {
  try {
    console.log(`🔐 Auto-triggering VERIFICATION_STATUS notification for user ${userId}`);

    return await notificationHandler.triggerNotification('VERIFICATION_STATUS', {
      userId,
      status: statusData.status,
      reason: statusData.reason
    });
  } catch (error) {
    console.error('Error in onVerificationStatusChange:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Auto-trigger premium subscription notifications
 */
const onSubscriptionChange = async (userId, subscriptionData) => {
  try {
    console.log(`💎 Auto-triggering subscription notification for user ${userId}`);

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { profile: true }
    });

    if (user?.email) {
      let subject, message;

      if (subscriptionData.status === 'ACTIVE') {
        subject = '🎉 Welcome to Premium - Vaivahik';
        message = `
          <h3>🎉 Welcome to Vaivahik Premium!</h3>
          <p>Hi ${user.profile?.fullName || 'User'},</p>
          <p>Your premium subscription is now active! Enjoy these exclusive features:</p>
          <ul>
            <li>✅ Unlimited profile views</li>
            <li>✅ Priority matching</li>
            <li>✅ Direct contact details</li>
            <li>✅ Advanced search filters</li>
            <li>✅ Profile boost</li>
          </ul>
          <a href="${process.env.FRONTEND_URL}/dashboard"
             style="background: #7e57c2; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px;">
             Explore Premium Features
          </a>
        `;
      } else if (subscriptionData.status === 'EXPIRED') {
        subject = '⏰ Premium Subscription Expired - Vaivahik';
        message = `
          <h3>⏰ Your Premium subscription has expired</h3>
          <p>Hi ${user.profile?.fullName || 'User'},</p>
          <p>Your premium features are no longer active. Renew now to continue enjoying:</p>
          <ul>
            <li>Unlimited profile views</li>
            <li>Priority matching</li>
            <li>Direct contact details</li>
          </ul>
          <a href="${process.env.FRONTEND_URL}/upgrade"
             style="background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px;">
             Renew Premium
          </a>
        `;
      }

      if (subject && message) {
        await sendEmail({
          to: user.email,
          subject,
          template: 'default',
          data: { name: user.profile?.fullName || 'User', message }
        });
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error in onSubscriptionChange:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Schedule daily match digest
 */
const scheduleDailyMatchDigest = async () => {
  try {
    console.log('📊 Sending daily match digest...');

    // Get users who want daily digest
    const users = await prisma.user.findMany({
      where: {
        profile: {
          notificationPreferences: {
            path: ['dailyDigest'],
            equals: true
          }
        }
      },
      include: { profile: true }
    });

    for (const user of users) {
      // Get new matches for this user from last 24 hours
      const newMatches = await prisma.match.findMany({
        where: {
          userId: user.id,
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
          }
        },
        take: 5,
        include: {
          matchedUser: {
            include: { profile: true }
          }
        }
      });

      if (newMatches.length > 0 && user.email) {
        const matchesHtml = newMatches.map(match => `
          <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px;">
            <h4>${match.matchedUser.profile?.firstName} ${match.matchedUser.profile?.lastName}</h4>
            <p>Age: ${match.matchedUser.profile?.age} | Location: ${match.matchedUser.profile?.city}</p>
            <p>Compatibility: ${match.compatibilityScore}%</p>
          </div>
        `).join('');

        await sendEmail({
          to: user.email,
          subject: `📊 Your Daily Match Digest - ${newMatches.length} New Matches`,
          template: 'default',
          data: {
            name: user.profile?.firstName || 'User',
            message: `
              <h3>📊 Your Daily Match Digest</h3>
              <p>Here are your ${newMatches.length} new matches from the last 24 hours:</p>
              ${matchesHtml}
              <a href="${process.env.FRONTEND_URL}/matches"
                 style="background: #7e57c2; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px;">
                 View All Matches
              </a>
            `
          }
        });
      }
    }

    return { success: true, digestsSent: users.length };
  } catch (error) {
    console.error('Error in scheduleDailyMatchDigest:', error);
    return { success: false, error: error.message };
  }
};

module.exports = {
  onNewMatchFound,
  onProfileViewed,
  onInterestReceived,
  onInterestAccepted,
  onNewMessage,
  onVerificationStatusChange,
  onSubscriptionChange,
  scheduleDailyMatchDigest
};
