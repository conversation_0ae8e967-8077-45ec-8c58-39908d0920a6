/**
 * User Login Page
 *
 * This page handles user authentication with both email/phone and password.
 * It supports both mock and real authentication based on feature flags.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Box,
  Container,
  Grid,
  Typography,
  styled,
  ToggleButton,
  ToggleButtonGroup,
  Paper
} from '@mui/material';
import { useAuth } from '@/contexts/AuthContext';
import ModernLoginForm from '@/components/auth/ModernLoginForm';
import ModernOtpLogin from '@/components/auth/ModernOtpLogin';
import { determineUserState } from '@/utils/authFlowHandler';

export default function Login() {
  const [loading, setLoading] = useState(false);
  const [loginError, setLoginError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [loginMethod, setLoginMethod] = useState('otp'); // 'otp' or 'password'

  const router = useRouter();
  const { login, user, dataSource } = useAuth();

  // Check if user is already logged in
  useEffect(() => {
    if (user) {
      // Redirect to dashboard if already logged in
      router.push('/dashboard');
    }
  }, [user, router]);

  // Handle URL query parameters separately to avoid hydration issues
  useEffect(() => {
    if (router.isReady && router.query.registered === 'true') {
      setSuccessMessage('Registration successful! Please log in with your credentials.');
    }
  }, [router.isReady, router.query]);

  // Handle form submission
  const handleSubmit = async (credentials) => {
    setLoading(true);
    setLoginError('');

    try {
      // Call login function from auth context
      const result = await login(credentials);

      if (result.success) {
        // Show success message
        setSuccessMessage('Login successful! Redirecting...');

        // Redirect to dashboard or return URL
        const returnUrl = router.query.returnUrl || '/dashboard';
        router.push(returnUrl);
      } else {
        setLoginError(result.error || 'Login failed. Please check your credentials.');
      }
    } catch (error) {
      console.error('Login error:', error);
      setLoginError(error.message || 'An error occurred during login. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle OTP login submission with smart routing
  const handleOtpLogin = async (otpData) => {
    setLoading(true);
    setLoginError('');
    setSuccessMessage('');

    try {
      // Handle successful OTP verification
      if (otpData.success) {
        // Fetch user profile data to determine state
        const userResponse = await fetch('/api/users/profile', {
          headers: {
            'Authorization': `Bearer ${otpData.accessToken}`
          }
        });

        let userData = null;
        if (userResponse.ok) {
          userData = await userResponse.json();
        }

        // Determine where to redirect user based on their state
        const userState = determineUserState(userData);

        setSuccessMessage(`${userState.message} Redirecting...`);

        // Use smart routing instead of default dashboard
        const redirectTo = router.query.returnUrl || userState.redirectTo;

        setTimeout(() => {
          if (userState.step !== undefined) {
            // If user has incomplete registration, go to specific step
            router.push(`${userState.redirectTo}?step=${userState.step}`);
          } else {
            router.push(redirectTo);
          }
        }, 1500);
      } else {
        setLoginError(otpData.message || 'OTP verification failed.');
      }
    } catch (error) {
      console.error('OTP Login error:', error);
      setLoginError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Styled components for modern UI
  const PageContainer = styled(Box)(({ theme }) => ({
    minHeight: '100vh',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    padding: theme.spacing(4, 2),
    background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.9) 100%)',
    backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23FF5F6D' fill-opacity='0.03'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
    position: 'relative',
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      background: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'%23FF5F6D\' opacity=\'0.03\'%3E%3Cpath d=\'M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\'/%3E%3C/svg%3E") center/50px repeat',
      opacity: 0.05,
      zIndex: 0,
    }
  }));

  return (
    <>
      <Head>
        <title>Login - Vaivahik</title>
        <meta name="description" content="Login to your Vaivahik account" />
      </Head>

      <PageContainer>
        <Container maxWidth="lg" sx={{ display: 'flex', justifyContent: 'center', position: 'relative', zIndex: 1 }}>
          <Grid container spacing={3} alignItems="center" justifyContent="center">
            {/* Login Method Toggle */}
            <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
              <Paper
                elevation={0}
                sx={{
                  p: 1,
                  borderRadius: 3,
                  background: 'rgba(255, 255, 255, 0.9)',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255, 182, 193, 0.2)'
                }}
              >
                <ToggleButtonGroup
                  value={loginMethod}
                  exclusive
                  onChange={(e, newMethod) => newMethod && setLoginMethod(newMethod)}
                  sx={{
                    '& .MuiToggleButton-root': {
                      borderRadius: 2,
                      px: 3,
                      py: 1,
                      textTransform: 'none',
                      fontWeight: 500,
                      border: 'none',
                      '&.Mui-selected': {
                        background: 'linear-gradient(135deg, #FF69B4 0%, #FFB6C1 100%)',
                        color: 'white',
                        '&:hover': {
                          background: 'linear-gradient(135deg, #FF1493 0%, #FF69B4 100%)',
                        }
                      },
                      '&:not(.Mui-selected)': {
                        color: '#666',
                        '&:hover': {
                          backgroundColor: 'rgba(255, 105, 180, 0.1)'
                        }
                      }
                    }
                  }}
                >
                  <ToggleButton value="otp">
                    📱 Login with OTP
                  </ToggleButton>
                  <ToggleButton value="password">
                    🔐 Login with Password
                  </ToggleButton>
                </ToggleButtonGroup>
              </Paper>
            </Grid>

            {/* Login Form */}
            <Grid item xs={12} md={6}>
              {loginMethod === 'otp' ? (
                <ModernOtpLogin
                  onLogin={handleOtpLogin}
                  loading={loading}
                  error={loginError}
                  success={successMessage}
                />
              ) : (
                <ModernLoginForm
                  onLogin={handleSubmit}
                  loading={loading}
                  error={loginError}
                  success={successMessage}
                  dataSource={dataSource}
                />
              )}
            </Grid>

            {/* Welcome Image/Content */}
            <Grid item xs={12} md={6} sx={{ display: { xs: 'none', md: 'block' } }}>
              <Box
                sx={{
                  textAlign: 'center',
                  position: 'relative',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: '10%',
                    left: '10%',
                    width: '80%',
                    height: '80%',
                    borderRadius: '50%',
                    background: 'var(--primary-gradient)',
                    opacity: 0.1,
                    filter: 'blur(40px)',
                    zIndex: 0,
                  }
                }}
              >
                <img
                  src="/images/couple-illustration.svg"
                  alt="Vaivahik Matrimony"
                  style={{
                    maxWidth: '100%',
                    height: 'auto',
                    position: 'relative',
                    zIndex: 1
                  }}
                />

                <Typography
                  variant="h5"
                  sx={{
                    mt: 4,
                    fontFamily: 'var(--font-secondary)',
                    fontWeight: 600,
                    color: 'var(--text-color-dark)'
                  }}
                >
                  Find Your Perfect Match
                </Typography>

                <Typography
                  variant="body1"
                  sx={{
                    mt: 2,
                    color: 'var(--text-color-medium)',
                    maxWidth: '80%',
                    mx: 'auto'
                  }}
                >
                  Join thousands of Maratha community members who have found their life partners through Vaivahik.
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </PageContainer>
    </>
  );
}
