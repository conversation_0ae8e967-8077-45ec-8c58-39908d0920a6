import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import EnhancedAdminLayout from '@/components/admin/EnhancedAdminLayout';
import {
  Box,
  Button,
  CircularProgress,
  Paper,
  Typography,
  Alert,
  Snackbar,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Lock as LockIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';


export default function TemplatePreviewPage() {
  const router = useRouter();
  const { id } = router.query;
  const [template, setTemplate] = useState(null);
  const [loading, setLoading] = useState(true);
  const [sampleData, setSampleData] = useState(null);
  const [watermarkActive, setWatermarkActive] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const iframeRef = useRef(null);

  useEffect(() => {
    if (id) {
      fetchTemplate();
      fetchSampleData();
    }
  }, [id]);

  // Anti-screenshot measures
  useEffect(() => {
    // Add watermark when print screen is pressed
    const handleKeyDown = (e) => {
      // Check for Print Screen key (it's not consistently detectable across browsers)
      if (e.key === 'PrintScreen' || e.keyCode === 44) {
        activateWatermark();
      }

      // Check for Ctrl+P (print)
      if ((e.ctrlKey || e.metaKey) && (e.key === 'p' || e.keyCode === 80)) {
        e.preventDefault();
        setOpenSnackbar(true);
        return false;
      }

      // Check for Ctrl+Shift+I (developer tools)
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && (e.key === 'i' || e.keyCode === 73)) {
        e.preventDefault();
        setOpenSnackbar(true);
        return false;
      }
    };

    // Detect right-click
    const handleContextMenu = (e) => {
      e.preventDefault();
      setOpenSnackbar(true);
      return false;
    };

    // Detect when user switches tabs/windows
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        activateWatermark();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('contextmenu', handleContextMenu);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Detect when user tries to take a screenshot
    if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
      navigator.mediaDevices.getDisplayMedia = function() {
        activateWatermark();
        return Promise.reject(new Error('Screenshot not allowed'));
      };
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('contextmenu', handleContextMenu);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  const activateWatermark = () => {
    setWatermarkActive(true);
    setOpenSnackbar(true);

    // Remove watermark after a few seconds
    setTimeout(() => {
      setWatermarkActive(false);
    }, 3000);
  };

  const fetchTemplate = async () => {
    try {
      const response = await fetch(`/api/admin/biodata/templates/${id}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (data.success) {
        setTemplate(data.template);
      } else {
        toast.error('Failed to fetch template');
      }
      setLoading(false);
    } catch (error) {
      console.error('Error fetching template:', error);
      toast.error('Failed to fetch template');
      setLoading(false);
    }
  };

  const fetchSampleData = async () => {
    try {
      // In a real implementation, you would fetch sample user data
      // For now, we'll use mock data
      setSampleData({
        name: "Rahul Sharma",
        tagline: "Software Engineer seeking a life partner",
        age: 28,
        height: "5'10\"",
        education: "M.Tech in Computer Science",
        occupation: "Senior Software Engineer",
        dateOfBirth: "15 August 1995",
        birthTime: "10:30 AM",
        birthPlace: "Mumbai, Maharashtra",
        religion: "Hindu",
        caste: "Maratha",
        subCaste: "Deshmukh",
        gotra: "Kashyap",
        kul: "Surya",
        maritalStatus: "Never Married",
        diet: "Vegetarian",
        fatherName: "Rajesh Sharma",
        fatherOccupation: "Retired Government Officer",
        motherName: "Sunita Sharma",
        motherOccupation: "Homemaker",
        familyType: "Nuclear Family",
        familyStatus: "Middle Class",
        siblings: "1 Brother (Married), 1 Sister (Married)",
        nativePlace: "Pune, Maharashtra",
        nativeDistrict: "Pune",
        company: "Tech Innovations Pvt Ltd",
        annualIncome: "₹18,00,000",
        educationDetails: "IIT Bombay (2018)",
        occupationDetails: "Working as a Senior Software Engineer with 5 years of experience",
        city: "Bangalore",
        state: "Karnataka",
        country: "India",
        email: "<EMAIL>",
        phone: "+91 9876543210",
        aboutMe: "I am a dedicated professional with a passion for technology and innovation. I enjoy reading books, traveling, and exploring new cultures. I value honesty, respect, and understanding in relationships.",
        hobbies: "Reading, Traveling, Photography, Playing Guitar, Cooking",
        partnerPreferences: "Looking for an educated, understanding, and family-oriented partner. Preferably working in IT/Software field with similar values and interests.",
        profilePicture: "https://randomuser.me/api/portraits/men/32.jpg",
        additionalPhotos: [
          "https://randomuser.me/api/portraits/men/33.jpg",
          "https://randomuser.me/api/portraits/men/34.jpg",
          "https://randomuser.me/api/portraits/men/35.jpg"
        ],
        rashi: "Simha (Leo)",
        nakshatra: "Magha",
        mangal: "No",
        assets: "Own apartment in Bangalore",
        brandLogo: "/logo.png",
        brandName: "Vaivahik",
        brandTagline: "Find your perfect match",
        createdAt: new Date().toLocaleDateString('en-IN')
      });
    } catch (error) {
      console.error('Error fetching sample data:', error);
      toast.error('Failed to fetch sample data');
    }
  };

  const loadTemplateWithData = () => {
    if (!template || !sampleData || !iframeRef.current) return;

    const iframe = iframeRef.current;
    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

    // Fetch the template HTML
    fetch(template.designFile)
      .then(response => response.text())
      .then(html => {
        // Replace placeholders with sample data
        let processedHtml = html;

        // Replace all placeholders with sample data
        Object.keys(sampleData).forEach(key => {
          const value = sampleData[key];
          if (typeof value === 'string' || typeof value === 'number') {
            const regex = new RegExp(`{{${key}}}`, 'g');
            processedHtml = processedHtml.replace(regex, value);
          } else if (Array.isArray(value)) {
            // Handle arrays (like additionalPhotos)
            value.forEach((item, index) => {
              const regex = new RegExp(`{{${key}.${index}}}`, 'g');
              processedHtml = processedHtml.replace(regex, item);
            });
          }
        });

        // Add anti-screenshot CSS
        const antiScreenshotCSS = `
          body::before {
            content: "";
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.01);
            pointer-events: none;
            z-index: 9999;
          }

          @media print {
            body {
              display: none;
            }
          }
        `;

        // Write to iframe
        iframeDoc.open();
        iframeDoc.write(processedHtml);

        // Add anti-screenshot style
        const style = iframeDoc.createElement('style');
        style.textContent = antiScreenshotCSS;
        iframeDoc.head.appendChild(style);

        iframeDoc.close();

        // Disable right-click in iframe
        iframeDoc.addEventListener('contextmenu', e => {
          e.preventDefault();
          return false;
        });
      })
      .catch(error => {
        console.error('Error loading template:', error);
        toast.error('Failed to load template');
      });
  };

  useEffect(() => {
    if (template && sampleData && iframeRef.current) {
      loadTemplateWithData();
    }
  }, [template, sampleData]);

  const handleGoBack = () => {
    router.push('/admin/biodata-templates');
  };

  const handleEditTemplate = () => {
    router.push(`/admin/biodata-templates?edit=${id}`);
  };

  return (
    <EnhancedAdminLayout title={`Preview Template - ${template?.name || ''}`}>
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={handleGoBack}
          >
            Back to Templates
          </Button>

          {template && (
            <Box>
              <Tooltip title="Edit Template">
                <IconButton onClick={handleEditTemplate} color="primary">
                  <EditIcon />
                </IconButton>
              </Tooltip>
            </Box>
          )}
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <CircularProgress />
          </Box>
        ) : template ? (
          <>
            <Paper sx={{ p: 2, mb: 3 }}>
              <Typography variant="h5" gutterBottom>
                {template.name}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {template.description || 'No description provided'}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Typography variant="subtitle1" sx={{ mr: 2 }}>
                  Price: {template.discountedPrice ? (
                    <>
                      <span style={{ textDecoration: 'line-through', marginRight: '8px' }}>
                        ₹{template.price}
                      </span>
                      <span style={{ fontWeight: 'bold', color: '#1976d2' }}>
                        ₹{template.discountedPrice}
                      </span>
                      <span style={{
                        backgroundColor: '#f50057',
                        color: 'white',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        fontSize: '0.75rem',
                        marginLeft: '8px'
                      }}>
                        -{template.discountPercent}%
                      </span>
                    </>
                  ) : (
                    `₹${template.price}`
                  )}
                </Typography>
                <Typography variant="subtitle1" sx={{ mr: 2 }}>
                  Status: <span style={{
                    color: template.isActive ? '#4caf50' : '#f44336',
                    fontWeight: 'bold'
                  }}>
                    {template.isActive ? 'Active' : 'Inactive'}
                  </span>
                </Typography>
              </Box>
            </Paper>

            <Alert severity="info" sx={{ mb: 3 }}>
              This is a preview with sample data. The actual template will be filled with user's information.
            </Alert>

            <Box sx={{ position: 'relative', height: 'calc(100vh - 300px)', minHeight: '500px' }}>
              <iframe
                ref={iframeRef}
                style={{
                  width: '100%',
                  height: '100%',
                  border: '1px solid #ddd',
                  borderRadius: '4px'
                }}
                title="Template Preview"
              />

              {/* Watermark overlay (shown when screenshot attempt is detected) */}
              {watermarkActive && (
                <Box sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                  zIndex: 9999,
                  flexDirection: 'column',
                  gap: 2
                }}>
                  <LockIcon sx={{ fontSize: 60, color: 'error.main' }} />
                  <Typography variant="h4" color="error" align="center">
                    Screenshot Detected
                  </Typography>
                  <Typography variant="body1" align="center">
                    This is a preview only. Templates must be purchased for use.
                  </Typography>
                </Box>
              )}
            </Box>
          </>
        ) : (
          <Alert severity="error">
            Template not found
          </Alert>
        )}
      </Box>

      <Snackbar
        open={openSnackbar}
        autoHideDuration={6000}
        onClose={() => setOpenSnackbar(false)}
        message="Screenshots and printing are disabled for preview templates"
        action={
          <Button color="secondary" size="small" onClick={() => setOpenSnackbar(false)}>
            OK
          </Button>
        }
      />
    </EnhancedAdminLayout>
  );
}
