/**
 * Jest Test Setup
 * 
 * This file sets up the test environment for Jest.
 */

// Load environment variables
require('dotenv').config({ path: '.env.test' });

// Set test environment
process.env.NODE_ENV = 'test';

// Mock Redis client
jest.mock('redis', () => {
  const redisMock = {
    createClient: jest.fn().mockReturnValue({
      on: jest.fn(),
      connect: jest.fn().mockResolvedValue(true),
      isReady: true,
      get: jest.fn().mockResolvedValue(null),
      set: jest.fn().mockResolvedValue('OK'),
      del: jest.fn().mockResolvedValue(1),
      keys: jest.fn().mockResolvedValue([]),
      quit: jest.fn().mockResolvedValue(true),
      expire: jest.fn().mockResolvedValue(1),
      duplicate: jest.fn().mockReturnThis()
    })
  };
  return redisMock;
});

// Global test setup
beforeAll(async () => {
  console.log('Setting up test environment...');
  
  // Add global test setup here
  
  console.log('Test environment setup complete');
});

// Global test teardown
afterAll(async () => {
  console.log('Tearing down test environment...');
  
  // Add global test teardown here
  
  console.log('Test environment teardown complete');
});
