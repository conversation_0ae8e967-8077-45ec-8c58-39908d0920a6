// src/middleware/mockAuth.middleware.js

/**
 * Mock authentication middleware that always authenticates the request
 * This is used for development and testing purposes only
 */
const mockAuthenticateAdmin = (req, res, next) => {
    // Add admin user info to the request
    req.admin = {
        id: 'admin-1',
        email: '<EMAIL>',
        role: 'ADMIN'
    };

    // Add a mock prisma client to the request
    // This is needed because our controller functions expect it
    req.prisma = {
        // Add any mock methods that might be needed
        user: {
            findUnique: () => Promise.resolve(null),
            findMany: () => Promise.resolve([]),
            count: () => Promise.resolve(0)
        },
        report: {
            findUnique: () => Promise.resolve(null),
            findMany: () => Promise.resolve([]),
            count: () => Promise.resolve(0)
        },
        verificationDocument: {
            findUnique: () => Promise.resolve(null),
            findMany: () => Promise.resolve([]),
            count: () => Promise.resolve(0)
        }
    };

    // Continue to the next middleware or route handler
    next();
};

module.exports = mockAuthenticateAdmin;
