import React, { useState } from 'react';
import { Container, Box, Paper, Typography, TextField, But<PERSON>, <PERSON>per, <PERSON>, StepLabel, CircularProgress } from '@mui/material';
import ModernOtpWidget from '../components/auth/ModernOtpWidget';
import axios from 'axios';
import { useRouter } from 'next/router';

/**
 * Example login page with 6-digit OTP verification
 * This is a reference implementation that you can adapt to your existing login page
 */
const LoginExample = () => {
  const router = useRouter();
  const [activeStep, setActiveStep] = useState(0);
  const [phone, setPhone] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Handle phone submission and OTP request
  const handleRequestOtp = async (e) => {
    e.preventDefault();
    
    if (!phone || phone.length < 10) {
      setError('Please enter a valid phone number');
      return;
    }
    
    setLoading(true);
    setError('');
    
    try {
      const response = await axios.post('/api/users/request-otp', { phone });
      
      setSuccess('OTP sent successfully');
      setActiveStep(1);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to send OTP');
    } finally {
      setLoading(false);
    }
  };

  // Handle OTP verification
  const handleVerifyOtp = async (otp) => {
    setLoading(true);
    setError('');
    
    try {
      const response = await axios.post('/api/users/verify-otp', { phone, otp });
      
      setSuccess('OTP verified successfully');
      
      // Store auth token
      localStorage.setItem('authToken', response.data.accessToken);
      
      // Redirect based on user status
      if (response.data.isNewUser || response.data.profileStatus === 'INCOMPLETE') {
        router.push('/profile/complete');
      } else {
        router.push('/dashboard');
      }
    } catch (err) {
      setError(err.response?.data?.message || 'Invalid OTP');
    } finally {
      setLoading(false);
    }
  };

  // Handle OTP resend
  const handleResendOtp = async () => {
    setLoading(true);
    setError('');
    
    try {
      const response = await axios.post('/api/users/resend-otp', { phone });
      
      setSuccess('OTP resent successfully');
      return true;
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to resend OTP');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Go back to phone input
  const handleChangePhone = () => {
    setActiveStep(0);
    setError('');
    setSuccess('');
  };

  return (
    <Container maxWidth="sm">
      <Box sx={{ my: 4 }}>
        <Paper elevation={3} sx={{ p: 4, borderRadius: 2 }}>
          <Typography variant="h4" component="h1" gutterBottom textAlign="center">
            Login to Vaivahik
          </Typography>
          
          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            <Step>
              <StepLabel>Enter Phone</StepLabel>
            </Step>
            <Step>
              <StepLabel>Verify OTP</StepLabel>
            </Step>
          </Stepper>
          
          {activeStep === 0 ? (
            <Box component="form" onSubmit={handleRequestOtp}>
              <Typography variant="body1" gutterBottom>
                Enter your phone number to receive a 6-digit OTP
              </Typography>
              
              <TextField
                label="Phone Number"
                variant="outlined"
                fullWidth
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                placeholder="e.g., 9123456789"
                margin="normal"
                required
                inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
              />
              
              {error && (
                <Typography color="error" variant="body2" sx={{ mt: 1 }}>
                  {error}
                </Typography>
              )}
              
              {success && (
                <Typography color="success.main" variant="body2" sx={{ mt: 1 }}>
                  {success}
                </Typography>
              )}
              
              <Button
                type="submit"
                variant="contained"
                fullWidth
                size="large"
                disabled={loading || !phone}
                sx={{ mt: 3, mb: 2, py: 1.5, borderRadius: 2 }}
              >
                {loading ? <CircularProgress size={24} color="inherit" /> : 'Send OTP'}
              </Button>
              
              <Typography variant="caption" color="text.secondary" textAlign="center" sx={{ display: 'block', mt: 2 }}>
                By continuing, you agree to our Terms of Service and Privacy Policy
              </Typography>
            </Box>
          ) : (
            <ModernOtpWidget
              phone={phone}
              onVerify={handleVerifyOtp}
              onResendOtp={handleResendOtp}
              onChangePhone={handleChangePhone}
              loading={loading}
              error={error}
              success={success}
            />
          )}
        </Paper>
      </Box>
    </Container>
  );
};

export default LoginExample;
