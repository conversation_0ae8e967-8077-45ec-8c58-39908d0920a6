/**
 * Enhanced Search Page
 * Comprehensive search functionality with premium features and ML integration
 */

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import Head from 'next/head';
import {
  Box,
  Container,
  Typography,
  TextField,
  InputAdornment,
  Paper,
  Grid,
  Button,
  Chip,
  Alert,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Divider
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Sort as SortIcon,
  ViewModule as GridViewIcon,
  ViewList as ListViewIcon,
  Tune as AdvancedIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import axios from 'axios';

// Import custom components
import AdvancedSearchWidget from '@/components/dashboard/AdvancedSearchWidget';
import EnhancedSearchResults from '@/components/search/EnhancedSearchResults';
import SmartSearchSuggestions from '@/components/search/SmartSearchSuggestions';

export default function SearchPage() {
  const { data: session } = useSession();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [totalResults, setTotalResults] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [resultsPerPage] = useState(12);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [viewMode, setViewMode] = useState('grid');
  const [sortBy, setSortBy] = useState('relevance');
  const [userTier, setUserTier] = useState('free');
  const [searchFilters, setSearchFilters] = useState({});
  const [searchHistory, setSearchHistory] = useState([]);

  useEffect(() => {
    if (session?.user) {
      checkUserTier();
      loadSearchHistory();
    }
  }, [session]);

  const checkUserTier = async () => {
    try {
      const response = await axios.get('/api/user/subscription-status');
      setUserTier(response.data.tier || 'free');
    } catch (error) {
      console.error('Error checking user tier:', error);
    }
  };

  const loadSearchHistory = async () => {
    try {
      const response = await axios.get('/api/search/history');
      setSearchHistory(response.data.history || []);
    } catch (error) {
      console.error('Error loading search history:', error);
    }
  };

  const handleSearch = async (filters = {}, page = 1) => {
    if (!searchQuery.trim() && Object.keys(filters).length === 0) {
      toast.warning('Please enter a search query or apply filters');
      return;
    }

    setLoading(true);
    try {
      const searchParams = {
        query: searchQuery,
        page,
        limit: resultsPerPage,
        sortBy,
        ...filters
      };

      const endpoint = userTier === 'premium' && Object.keys(filters).length > 0
        ? '/api/users/advanced-search'
        : '/api/users/search';

      const response = await axios.get(endpoint, { params: searchParams });

      if (response.data.success) {
        setSearchResults(response.data.profiles || []);
        setTotalResults(response.data.total || 0);
        setCurrentPage(page);
        
        // Save to search history
        if (searchQuery.trim()) {
          saveToSearchHistory(searchQuery, response.data.total || 0);
        }
      } else {
        throw new Error(response.data.message || 'Search failed');
      }
    } catch (error) {
      console.error('Search error:', error);
      
      if (error.response?.status === 403) {
        toast.error('This search feature requires a premium subscription');
      } else {
        toast.error('Search failed. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const saveToSearchHistory = async (query, resultCount) => {
    try {
      await axios.post('/api/search/history', {
        query,
        resultCount,
        filters: searchFilters
      });
      
      // Update local history
      setSearchHistory(prev => [
        { query, resultCount, timestamp: new Date() },
        ...prev.slice(0, 9) // Keep only last 10 searches
      ]);
    } catch (error) {
      console.error('Error saving search history:', error);
    }
  };

  const handleSuggestionSelect = (suggestion) => {
    setSearchQuery(suggestion.text);
    setShowSuggestions(false);
    
    // Auto-execute search for certain suggestion types
    if (suggestion.type === 'history' || suggestion.type === 'trending') {
      handleSearch();
    }
  };

  const handleProfileView = (profile) => {
    // Navigate to profile view or open modal
    window.open(`/profile/${profile.id}`, '_blank');
  };

  const handleSendInterest = async (profile) => {
    try {
      const response = await axios.post('/api/interests/send', {
        targetUserId: profile.id
      });

      if (response.data.success) {
        toast.success(`Interest sent to ${profile.name}`);
      } else {
        throw new Error(response.data.message);
      }
    } catch (error) {
      console.error('Error sending interest:', error);
      
      if (error.response?.status === 403) {
        toast.error('Sending interests requires a premium subscription');
        handleUpgradeClick('send-interest');
      } else {
        toast.error('Failed to send interest');
      }
    }
  };

  const handleUpgradeClick = (feature) => {
    // Navigate to upgrade page with feature context
    window.open(`/upgrade?feature=${feature}`, '_blank');
  };

  const handlePageChange = (event, page) => {
    handleSearch(searchFilters, page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const totalPages = Math.ceil(totalResults / resultsPerPage);

  return (
    <>
      <Head>
        <title>Search Profiles - Vaivahik Matrimony</title>
        <meta name="description" content="Search for compatible life partners on Vaivahik matrimony platform" />
      </Head>

      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            Find Your Perfect Match
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Search through thousands of verified profiles to find your ideal life partner
          </Typography>
        </Box>

        {/* Search Bar */}
        <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
          <Box sx={{ position: 'relative' }}>
            <TextField
              fullWidth
              placeholder="Search by name, profession, location..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onFocus={() => setShowSuggestions(true)}
              onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <Button
                      variant="contained"
                      onClick={() => handleSearch(searchFilters)}
                      disabled={loading}
                      sx={{
                        background: 'linear-gradient(135deg, #D9534F 0%, #4A00E0 100%)',
                        color: 'white'
                      }}
                    >
                      Search
                    </Button>
                  </InputAdornment>
                )
              }}
              sx={{ mb: 2 }}
            />

            {/* Smart Suggestions */}
            <SmartSearchSuggestions
              searchQuery={searchQuery}
              onSuggestionSelect={handleSuggestionSelect}
              userTier={userTier}
              visible={showSuggestions}
            />
          </Box>

          {/* Search Controls */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <Button
                variant={showAdvancedFilters ? 'contained' : 'outlined'}
                startIcon={<AdvancedIcon />}
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              >
                Advanced Filters
              </Button>
              
              {userTier !== 'premium' && (
                <Chip
                  label="Upgrade for more filters"
                  color="warning"
                  size="small"
                  onClick={() => handleUpgradeClick('advanced-search')}
                  clickable
                />
              )}
            </Box>

            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Sort by</InputLabel>
                <Select
                  value={sortBy}
                  label="Sort by"
                  onChange={(e) => setSortBy(e.target.value)}
                >
                  <MenuItem value="relevance">Relevance</MenuItem>
                  <MenuItem value="lastActive">Last Active</MenuItem>
                  <MenuItem value="newest">Newest First</MenuItem>
                  {userTier === 'premium' && (
                    <>
                      <MenuItem value="compatibility">AI Compatibility</MenuItem>
                      <MenuItem value="responseRate">Response Rate</MenuItem>
                    </>
                  )}
                </Select>
              </FormControl>

              <Box sx={{ display: 'flex', border: 1, borderColor: 'divider', borderRadius: 1 }}>
                <Button
                  variant={viewMode === 'grid' ? 'contained' : 'text'}
                  size="small"
                  onClick={() => setViewMode('grid')}
                >
                  <GridViewIcon />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'contained' : 'text'}
                  size="small"
                  onClick={() => setViewMode('list')}
                >
                  <ListViewIcon />
                </Button>
              </Box>
            </Box>
          </Box>
        </Paper>

        {/* Advanced Filters */}
        {showAdvancedFilters && (
          <Box sx={{ mb: 4 }}>
            <AdvancedSearchWidget
              isPremium={userTier === 'premium'}
              onPremiumFeatureClick={handleUpgradeClick}
              onFiltersChange={setSearchFilters}
              onSearch={(filters) => handleSearch(filters)}
            />
          </Box>
        )}

        {/* Search Results Summary */}
        {totalResults > 0 && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              {totalResults.toLocaleString()} profiles found
            </Typography>
            {searchHistory.length > 0 && (
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mt: 1 }}>
                <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                  Recent searches:
                </Typography>
                {searchHistory.slice(0, 3).map((search, index) => (
                  <Chip
                    key={index}
                    label={search.query}
                    size="small"
                    variant="outlined"
                    onClick={() => {
                      setSearchQuery(search.query);
                      handleSearch();
                    }}
                    clickable
                  />
                ))}
              </Box>
            )}
          </Box>
        )}

        {/* Search Results */}
        <EnhancedSearchResults
          results={searchResults}
          loading={loading}
          userTier={userTier}
          onProfileView={handleProfileView}
          onSendInterest={handleSendInterest}
          onUpgradeClick={handleUpgradeClick}
        />

        {/* Pagination */}
        {totalPages > 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <Pagination
              count={totalPages}
              page={currentPage}
              onChange={handlePageChange}
              color="primary"
              size="large"
              showFirstButton
              showLastButton
            />
          </Box>
        )}

        {/* No Results */}
        {!loading && searchResults.length === 0 && searchQuery && (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No profiles found for "{searchQuery}"
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Try adjusting your search criteria or use different keywords
            </Typography>
            <Button
              variant="outlined"
              onClick={() => {
                setSearchQuery('');
                setSearchFilters({});
                setShowAdvancedFilters(true);
              }}
            >
              Browse All Profiles
            </Button>
          </Box>
        )}
      </Container>
    </>
  );
}
