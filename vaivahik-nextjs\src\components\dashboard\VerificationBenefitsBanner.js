import React from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  LinearProgress,
  styled
} from '@mui/material';
import {
  Verified as VerifiedIcon,
  Security as SecurityIcon,
  TrendingUp as TrendingIcon,
  Star as StarIcon,
  CheckCircle as CheckIcon
} from '@mui/icons-material';

const BenefitsBanner = styled(Card)(({ theme }) => ({
  background: 'linear-gradient(135deg, rgba(255, 95, 109, 0.1) 0%, rgba(255, 195, 113, 0.1) 100%)',
  border: '2px solid rgba(255, 95, 109, 0.2)',
  borderRadius: 20,
  overflow: 'hidden',
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
    background: 'linear-gradient(90deg, #FF5F6D, #FFC371)',
  }
}));

const BenefitChip = styled(Chip)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.9)',
  color: '#FF5F6D',
  fontWeight: 600,
  '& .MuiChip-icon': {
    color: '#4CAF50'
  }
}));

const VerificationBenefitsBanner = ({ 
  isVerified = false, 
  verificationStatus = 'PENDING',
  onVerifyClick 
}) => {
  const benefits = [
    { icon: <TrendingIcon />, text: '300% more profile views' },
    { icon: <StarIcon />, text: 'Priority in search results' },
    { icon: <SecurityIcon />, text: 'Trusted profile badge' },
    { icon: <CheckIcon />, text: 'Access to verified users only' }
  ];

  if (isVerified) {
    return (
      <BenefitsBanner>
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <VerifiedIcon sx={{ color: '#4CAF50', fontSize: 32, mr: 2 }} />
            <Box>
              <Typography variant="h5" fontWeight="700" color="#4CAF50">
                🎉 Congratulations! Your profile is verified
              </Typography>
              <Typography variant="body2" color="text.secondary">
                You now enjoy all verification benefits and increased visibility
              </Typography>
            </Box>
          </Box>
          
          <Grid container spacing={2}>
            {benefits.map((benefit, index) => (
              <Grid item xs={6} md={3} key={index}>
                <BenefitChip
                  icon={benefit.icon}
                  label={benefit.text}
                  size="small"
                  sx={{ width: '100%', justifyContent: 'flex-start' }}
                />
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </BenefitsBanner>
    );
  }

  return (
    <BenefitsBanner>
      <CardContent sx={{ p: 4 }}>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={8}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <SecurityIcon sx={{ color: '#FF5F6D', fontSize: 32, mr: 2 }} />
              <Box>
                <Typography variant="h5" fontWeight="700" color="#FF5F6D">
                  🔒 Verify Your Profile & Get 300% More Views!
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ mt: 1 }}>
                  Join thousands of verified members and boost your chances of finding the perfect match
                </Typography>
              </Box>
            </Box>

            {verificationStatus === 'PENDING' && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Verification in progress...
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={65} 
                  sx={{ 
                    height: 8, 
                    borderRadius: 4,
                    backgroundColor: 'rgba(255, 95, 109, 0.1)',
                    '& .MuiLinearProgress-bar': {
                      background: 'linear-gradient(90deg, #FF5F6D, #FFC371)',
                      borderRadius: 4
                    }
                  }} 
                />
                <Typography variant="caption" color="text.secondary">
                  65% complete - Upload remaining documents
                </Typography>
              </Box>
            )}

            <Grid container spacing={2} sx={{ mb: 3 }}>
              {benefits.map((benefit, index) => (
                <Grid item xs={6} md={3} key={index}>
                  <BenefitChip
                    icon={benefit.icon}
                    label={benefit.text}
                    size="small"
                    sx={{ width: '100%', justifyContent: 'flex-start' }}
                  />
                </Grid>
              ))}
            </Grid>

            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                size="large"
                startIcon={<VerifiedIcon />}
                onClick={onVerifyClick}
                sx={{
                  background: 'linear-gradient(135deg, #FF5F6D, #FFC371)',
                  borderRadius: 3,
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  boxShadow: '0 8px 24px rgba(255, 95, 109, 0.3)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #FF1493, #DC143C)',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 12px 32px rgba(255, 95, 109, 0.4)'
                  }
                }}
              >
                {verificationStatus === 'PENDING' ? 'Complete Verification' : 'Start Verification'}
              </Button>
              
              <Button
                variant="outlined"
                size="large"
                sx={{
                  borderColor: '#FF5F6D',
                  color: '#FF5F6D',
                  borderRadius: 3,
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  '&:hover': {
                    borderColor: '#FF1493',
                    backgroundColor: 'rgba(255, 95, 109, 0.1)',
                    transform: 'translateY(-2px)'
                  }
                }}
              >
                Learn More
              </Button>
            </Box>
          </Grid>

          <Grid item xs={12} md={4} sx={{ textAlign: 'center' }}>
            <Box sx={{ 
              position: 'relative',
              display: 'inline-block',
              p: 3
            }}>
              <Box sx={{
                width: 120,
                height: 120,
                borderRadius: '50%',
                background: 'linear-gradient(135deg, #FF5F6D, #FFC371)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto',
                boxShadow: '0 8px 32px rgba(255, 95, 109, 0.3)'
              }}>
                <SecurityIcon sx={{ fontSize: 48, color: 'white' }} />
              </Box>
              
              <Typography variant="h3" fontWeight="700" color="#FF5F6D" sx={{ mt: 2 }}>
                300%
              </Typography>
              <Typography variant="body1" color="text.secondary">
                More Profile Views
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </BenefitsBanner>
  );
};

export default VerificationBenefitsBanner;
