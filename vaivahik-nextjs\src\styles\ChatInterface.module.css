/* ChatInterface.module.css */

.chatInterface {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 80vh;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.chatHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background-color: #7e3af2;
  color: white;
  border-bottom: 1px solid #6c2ce9;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.userAvatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #5e2abe;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  position: relative;
}

.userAvatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.onlineIndicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #4caf50;
  border: 2px solid white;
}

.userName {
  display: flex;
  flex-direction: column;
}

.userName h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.userStatus {
  font-size: 12px;
  opacity: 0.8;
}

.chatActions {
  display: flex;
  gap: 10px;
}

.backButton {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.backButton:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.messageList {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.messageContainer {
  display: flex;
  margin-bottom: 10px;
  max-width: 80%;
}

.incoming {
  align-self: flex-start;
}

.outgoing {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  margin-right: 8px;
}

.messageContent {
  display: flex;
  flex-direction: column;
}

.message {
  padding: 10px 15px;
  border-radius: 18px;
  position: relative;
  word-break: break-word;
}

.incoming .message {
  background-color: white;
  border: 1px solid #e0e0e0;
  border-top-left-radius: 4px;
}

.outgoing .message {
  background-color: #7e3af2;
  color: white;
  border-top-right-radius: 4px;
}

.messageTime {
  font-size: 10px;
  margin-top: 4px;
  display: block;
  text-align: right;
  opacity: 0.7;
}

.readStatus {
  margin-left: 4px;
}

.typingIndicator {
  display: flex;
  align-items: center;
  margin-top: 8px;
  margin-bottom: 8px;
}

.typingDots {
  display: flex;
  gap: 3px;
  margin-right: 8px;
}

.typingDots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #bbb;
  display: inline-block;
  animation: typingAnimation 1.4s infinite ease-in-out both;
}

.typingDots span:nth-child(1) {
  animation-delay: 0s;
}

.typingDots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typingDots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingAnimation {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.6;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.typingText {
  font-size: 12px;
  color: #888;
}

.messageForm {
  display: flex;
  padding: 15px;
  background-color: white;
  border-top: 1px solid #e0e0e0;
}

.messageInput {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 24px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.messageInput:focus {
  border-color: #7e3af2;
}

.sendButton {
  margin-left: 10px;
  padding: 0 20px;
  background-color: #7e3af2;
  color: white;
  border: none;
  border-radius: 24px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.sendButton:hover {
  background-color: #6c2ce9;
}

.sendButton:disabled {
  background-color: #d1d5db;
  cursor: not-allowed;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #666;
}

.loadingSpinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #7e3af2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
  text-align: center;
}

.errorContainer {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 10px 15px;
  margin: 10px;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.errorContainer button {
  background: none;
  border: none;
  color: #b91c1c;
  font-weight: 600;
  cursor: pointer;
  padding: 5px 10px;
}

.loadMoreContainer {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
}

.loadMoreButton {
  background-color: transparent;
  border: 1px solid #d1d5db;
  color: #6b7280;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.loadMoreButton:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.imageContainer {
  margin-bottom: 8px;
  max-width: 200px;
}

.messageImage {
  width: 100%;
  border-radius: 8px;
  cursor: pointer;
}

/* Responsive styles */
@media (max-width: 768px) {
  .chatInterface {
    max-height: calc(100vh - 120px);
    border-radius: 0;
    box-shadow: none;
  }
  
  .messageContainer {
    max-width: 90%;
  }
}
