import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  Button,
  Avatar,
  Chip,
  Tabs,
  Tab,
  AppBar,
  Toolbar,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Badge,
  useTheme,
  useMediaQuery,
  Fab,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Dashboard as DashboardIcon,
  Search as SearchIcon,
  Person as PersonIcon,
  Message as MessageIcon,
  Favorite as FavoriteIcon,
  Star as StarIcon,
  Verified as VerifiedIcon,
  Premium as PremiumIcon,
  Menu as MenuIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  Logout as LogoutIcon,
  Home as HomeIcon,
  TrendingUp as TrendingUpIcon,
  Chat as ChatIcon,
  Description as BiodataIcon,
  Spotlight as SpotlightIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';

// Import components
import MockDataToggle from '@/components/common/MockDataToggle';
import { useAuth } from '@/contexts/AuthContext';

// Styled Components
const DashboardContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'url("/images/pattern.png") repeat',
    opacity: 0.1,
    zIndex: 0
  }
}));

const MainContent = styled(Box)(({ theme }) => ({
  marginLeft: 0,
  transition: theme.transitions.create(['margin'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  [theme.breakpoints.up('md')]: {
    marginLeft: 280,
  },
}));

const SidebarDrawer = styled(Drawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    width: 280,
    background: 'linear-gradient(180deg, #1a1a2e 0%, #16213e 100%)',
    color: 'white',
    border: 'none',
    boxShadow: '4px 0 20px rgba(0,0,0,0.1)'
  }
}));

const StatsCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  borderRadius: 20,
  border: '1px solid rgba(255, 255, 255, 0.2)',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 16px 48px rgba(0, 0, 0, 0.2)',
    '& .stats-icon': {
      transform: 'scale(1.2) rotate(10deg)'
    }
  }
}));

const MatchCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  borderRadius: 20,
  border: '1px solid rgba(255, 255, 255, 0.2)',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  overflow: 'hidden',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 16px 48px rgba(0, 0, 0, 0.2)',
    '& .match-avatar': {
      transform: 'scale(1.1)'
    }
  }
}));

const TopBar = styled(AppBar)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  color: '#333',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
  borderBottom: '1px solid rgba(255, 255, 255, 0.2)'
}));

export default function NewWebsiteDashboard() {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, logout } = useAuth();

  // State management
  const [activeTab, setActiveTab] = useState(0);
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    profileViews: 0,
    interests: 0,
    messages: 0,
    matches: 0
  });
  const [recentMatches, setRecentMatches] = useState([]);
  const [notifications, setNotifications] = useState([]);

  // Navigation items
  const navigationItems = [
    { label: 'Overview', icon: DashboardIcon, value: 0 },
    { label: 'Search Matches', icon: SearchIcon, value: 1 },
    { label: 'My Profile', icon: PersonIcon, value: 2 },
    { label: 'Messages', icon: MessageIcon, value: 3, premium: true },
    { label: 'Interests', icon: FavoriteIcon, value: 4 },
    { label: 'Verification', icon: SecurityIcon, value: 5 },
    { label: 'Biodata', icon: BiodataIcon, value: 6 },
    { label: 'Spotlight', icon: SpotlightIcon, value: 7, premium: true },
  ];

  // Speed dial actions
  const speedDialActions = [
    { icon: <SearchIcon />, name: 'Search', onClick: () => setActiveTab(1) },
    { icon: <MessageIcon />, name: 'Messages', onClick: () => setActiveTab(3) },
    { icon: <FavoriteIcon />, name: 'Interests', onClick: () => setActiveTab(4) },
    { icon: <SettingsIcon />, name: 'Settings', onClick: () => router.push('/settings') },
  ];

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Mock data for demonstration
      setStats({
        profileViews: 127,
        interests: 23,
        messages: 8,
        matches: 45
      });

      setRecentMatches([
        {
          id: 1,
          name: 'Priya Sharma',
          age: 26,
          location: 'Mumbai',
          photo: '/api/placeholder/150/150',
          compatibility: 94,
          isOnline: true,
          verified: true,
          premium: false
        },
        {
          id: 2,
          name: 'Anita Patil',
          age: 24,
          location: 'Pune',
          photo: '/api/placeholder/150/150',
          compatibility: 89,
          isOnline: false,
          verified: true,
          premium: true
        },
        {
          id: 3,
          name: 'Kavya Desai',
          age: 27,
          location: 'Nashik',
          photo: '/api/placeholder/150/150',
          compatibility: 87,
          isOnline: true,
          verified: false,
          premium: false
        }
      ]);

      setNotifications([
        { id: 1, message: 'New match found!', time: '2 min ago', type: 'match' },
        { id: 2, message: 'Profile view from Priya', time: '1 hour ago', type: 'view' },
        { id: 3, message: 'Message from Anita', time: '3 hours ago', type: 'message' }
      ]);

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (newValue) => {
    setActiveTab(newValue);
  };

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/login');
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Failed to logout');
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 0:
        return renderOverview();
      case 1:
        return renderSearch();
      case 2:
        return renderProfile();
      case 3:
        return renderMessages();
      case 4:
        return renderInterests();
      case 5:
        return renderVerification();
      case 6:
        return renderBiodata();
      case 7:
        return renderSpotlight();
      default:
        return renderOverview();
    }
  };

  const renderOverview = () => (
    <Grid container spacing={3}>
      {/* Stats Cards */}
      <Grid item xs={12}>
        <Grid container spacing={3}>
          {[
            { icon: TrendingUpIcon, label: 'Profile Views', value: stats.profileViews, color: '#2196F3' },
            { icon: FavoriteIcon, label: 'Interests', value: stats.interests, color: '#E91E63' },
            { icon: MessageIcon, label: 'Messages', value: stats.messages, color: '#4CAF50' },
            { icon: StarIcon, label: 'Matches', value: stats.matches, color: '#FF9800' }
          ].map((stat, index) => (
            <Grid item xs={6} md={3} key={index}>
              <StatsCard>
                <CardContent sx={{ textAlign: 'center', p: 3 }}>
                  <Box sx={{
                    width: 64,
                    height: 64,
                    borderRadius: '50%',
                    background: `${stat.color}20`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto 16px'
                  }}>
                    <stat.icon className="stats-icon" sx={{ fontSize: 32, color: stat.color }} />
                  </Box>
                  <Typography variant="h4" fontWeight="700" color={stat.color} gutterBottom>
                    {stat.value}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" fontWeight="500">
                    {stat.label}
                  </Typography>
                </CardContent>
              </StatsCard>
            </Grid>
          ))}
        </Grid>
      </Grid>

      {/* Recent Matches */}
      <Grid item xs={12}>
        <Typography variant="h5" fontWeight="700" gutterBottom sx={{ color: 'white', mb: 3 }}>
          🔥 Your Perfect Matches
        </Typography>
        <Grid container spacing={3}>
          {recentMatches.map((match) => (
            <Grid item xs={12} sm={6} md={4} key={match.id}>
              <MatchCard onClick={() => router.push(`/profile/${match.id}`)}>
                <CardContent sx={{ textAlign: 'center', p: 3 }}>
                  <Box sx={{ position: 'relative', mb: 2 }}>
                    <Avatar
                      src={match.photo}
                      className="match-avatar"
                      sx={{
                        width: 100,
                        height: 100,
                        margin: '0 auto',
                        border: '3px solid #FFB6C1'
                      }}
                    />
                    {match.isOnline && (
                      <Box sx={{
                        position: 'absolute',
                        bottom: 8,
                        right: '50%',
                        transform: 'translateX(50%)',
                        width: 16,
                        height: 16,
                        borderRadius: '50%',
                        background: '#4CAF50',
                        border: '2px solid white'
                      }} />
                    )}
                  </Box>
                  <Typography variant="h6" fontWeight="600" gutterBottom>
                    {match.name}, {match.age}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {match.location}
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 1 }}>
                    {match.verified && (
                      <Chip
                        icon={<VerifiedIcon sx={{ fontSize: 16 }} />}
                        label="Verified"
                        size="small"
                        color="success"
                      />
                    )}
                    {match.premium && (
                      <Chip
                        icon={<PremiumIcon sx={{ fontSize: 16 }} />}
                        label="Premium"
                        size="small"
                        sx={{ background: '#FFD700', color: '#000' }}
                      />
                    )}
                  </Box>
                  <Chip
                    label={`${match.compatibility}% Match`}
                    color="primary"
                    size="small"
                    sx={{ mt: 1 }}
                  />
                </CardContent>
              </MatchCard>
            </Grid>
          ))}
        </Grid>
      </Grid>
    </Grid>
  );

  const renderSearch = () => (
    <Box sx={{ color: 'white', textAlign: 'center', py: 8 }}>
      <SearchIcon sx={{ fontSize: 80, mb: 2, opacity: 0.7 }} />
      <Typography variant="h4" gutterBottom>Search Matches</Typography>
      <Typography variant="body1" sx={{ mb: 4, opacity: 0.8 }}>
        Find your perfect match with advanced search filters
      </Typography>
      <Button variant="contained" size="large" onClick={() => router.push('/search')}>
        Start Searching
      </Button>
    </Box>
  );

  const renderProfile = () => (
    <Box sx={{ color: 'white', textAlign: 'center', py: 8 }}>
      <PersonIcon sx={{ fontSize: 80, mb: 2, opacity: 0.7 }} />
      <Typography variant="h4" gutterBottom>My Profile</Typography>
      <Typography variant="body1" sx={{ mb: 4, opacity: 0.8 }}>
        Manage your profile information and preferences
      </Typography>
      <Button variant="contained" size="large" onClick={() => router.push('/profile/edit')}>
        Edit Profile
      </Button>
    </Box>
  );

  const renderMessages = () => (
    <Box sx={{ color: 'white', textAlign: 'center', py: 8 }}>
      <MessageIcon sx={{ fontSize: 80, mb: 2, opacity: 0.7 }} />
      <Typography variant="h4" gutterBottom>Messages</Typography>
      <Typography variant="body1" sx={{ mb: 4, opacity: 0.8 }}>
        Connect with your matches through secure messaging
      </Typography>
      <Button variant="contained" size="large" onClick={() => router.push('/messages')}>
        View Messages
      </Button>
    </Box>
  );

  const renderInterests = () => (
    <Box sx={{ color: 'white', textAlign: 'center', py: 8 }}>
      <FavoriteIcon sx={{ fontSize: 80, mb: 2, opacity: 0.7 }} />
      <Typography variant="h4" gutterBottom>Interests</Typography>
      <Typography variant="body1" sx={{ mb: 4, opacity: 0.8 }}>
        Manage your interests and see who's interested in you
      </Typography>
      <Button variant="contained" size="large" onClick={() => router.push('/interests')}>
        View Interests
      </Button>
    </Box>
  );

  const renderVerification = () => (
    <Box sx={{ color: 'white', textAlign: 'center', py: 8 }}>
      <SecurityIcon sx={{ fontSize: 80, mb: 2, opacity: 0.7 }} />
      <Typography variant="h4" gutterBottom>Verification</Typography>
      <Typography variant="body1" sx={{ mb: 4, opacity: 0.8 }}>
        Verify your profile to build trust and get more matches
      </Typography>
      <Button variant="contained" size="large" onClick={() => router.push('/verification')}>
        Get Verified
      </Button>
    </Box>
  );

  const renderBiodata = () => (
    <Box sx={{ color: 'white', textAlign: 'center', py: 8 }}>
      <BiodataIcon sx={{ fontSize: 80, mb: 2, opacity: 0.7 }} />
      <Typography variant="h4" gutterBottom>Biodata</Typography>
      <Typography variant="body1" sx={{ mb: 4, opacity: 0.8 }}>
        Create and download professional biodata templates
      </Typography>
      <Button variant="contained" size="large" onClick={() => router.push('/biodata')}>
        Create Biodata
      </Button>
    </Box>
  );

  const renderSpotlight = () => (
    <Box sx={{ color: 'white', textAlign: 'center', py: 8 }}>
      <SpotlightIcon sx={{ fontSize: 80, mb: 2, opacity: 0.7 }} />
      <Typography variant="h4" gutterBottom>Spotlight</Typography>
      <Typography variant="body1" sx={{ mb: 4, opacity: 0.8 }}>
        Boost your profile visibility with spotlight features
      </Typography>
      <Button variant="contained" size="large" onClick={() => router.push('/spotlight')}>
        Get Spotlight
      </Button>
    </Box>
  );

  return (
    <>
      <Head>
        <title>Dashboard - Find Your Perfect Match | Vaivahik</title>
        <meta name="description" content="Your personalized matrimony dashboard with matches, messages, and profile insights" />
      </Head>

      <DashboardContainer>
        {/* Top Navigation Bar */}
        <TopBar position="fixed" sx={{ zIndex: theme.zIndex.drawer + 1 }}>
          <Toolbar>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              onClick={handleSidebarToggle}
              edge="start"
              sx={{ mr: 2 }}
            >
              <MenuIcon />
            </IconButton>
            
            <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1, fontWeight: 700 }}>
              Vaivahik Dashboard
            </Typography>

            <IconButton color="inherit" sx={{ mr: 1 }}>
              <Badge badgeContent={notifications.length} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>

            <IconButton color="inherit" onClick={handleLogout}>
              <LogoutIcon />
            </IconButton>
          </Toolbar>
        </TopBar>

        {/* Sidebar Navigation */}
        <SidebarDrawer
          variant={isMobile ? "temporary" : "persistent"}
          open={sidebarOpen}
          onClose={handleSidebarToggle}
          ModalProps={{ keepMounted: true }}
        >
          <Toolbar />
          <Box sx={{ p: 3, textAlign: 'center', borderBottom: '1px solid rgba(255,255,255,0.1)' }}>
            <Avatar
              src={user?.profilePhoto || '/api/placeholder/80/80'}
              sx={{ width: 80, height: 80, margin: '0 auto 16px', border: '3px solid #4CAF50' }}
            />
            <Typography variant="h6" fontWeight="600" gutterBottom>
              {user?.name || 'Beautiful Soul'}
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.7 }}>
              {user?.location || 'Mumbai, India'}
            </Typography>
          </Box>

          <List sx={{ px: 2, py: 2 }}>
            {navigationItems.map((item) => (
              <ListItem
                button
                key={item.value}
                onClick={() => handleTabChange(item.value)}
                sx={{
                  borderRadius: 2,
                  mb: 1,
                  backgroundColor: activeTab === item.value ? 'rgba(255,255,255,0.1)' : 'transparent',
                  '&:hover': {
                    backgroundColor: 'rgba(255,255,255,0.05)'
                  }
                }}
              >
                <ListItemIcon sx={{ color: 'white', minWidth: 40 }}>
                  <item.icon />
                </ListItemIcon>
                <ListItemText 
                  primary={item.label}
                  sx={{ 
                    '& .MuiListItemText-primary': { 
                      fontWeight: activeTab === item.value ? 600 : 400 
                    }
                  }}
                />
                {item.premium && (
                  <Chip
                    label="Premium"
                    size="small"
                    sx={{
                      background: '#FFD700',
                      color: '#000',
                      fontSize: '0.7rem',
                      height: 20
                    }}
                  />
                )}
              </ListItem>
            ))}
          </List>

          {/* Mock Data Toggle */}
          <Box sx={{ p: 2, mt: 'auto' }}>
            <MockDataToggle showInProduction={false} />
          </Box>
        </SidebarDrawer>

        {/* Main Content Area */}
        <MainContent>
          <Toolbar />
          <Container maxWidth="xl" sx={{ py: 4, position: 'relative', zIndex: 1 }}>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
                <Typography variant="h6" sx={{ color: 'white' }}>Loading...</Typography>
              </Box>
            ) : (
              renderTabContent()
            )}
          </Container>
        </MainContent>

        {/* Mobile Speed Dial */}
        {isMobile && (
          <SpeedDial
            ariaLabel="Quick Actions"
            sx={{ position: 'fixed', bottom: 16, right: 16 }}
            icon={<SpeedDialIcon />}
          >
            {speedDialActions.map((action) => (
              <SpeedDialAction
                key={action.name}
                icon={action.icon}
                tooltipTitle={action.name}
                onClick={action.onClick}
              />
            ))}
          </SpeedDial>
        )}
      </DashboardContainer>
    </>
  );
}
