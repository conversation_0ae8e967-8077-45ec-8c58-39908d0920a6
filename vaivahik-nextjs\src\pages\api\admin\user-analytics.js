/**
 * User Analytics API Endpoint
 * Collects and analyzes user behavior data
 */

// Simple in-memory storage for demo (replace with database in production)
let userEvents = [];
const MAX_EVENTS = 5000;

export default async function handler(req, res) {
  if (req.method === 'POST') {
    try {
      const eventData = req.body;
      
      // Add metadata
      const eventEntry = {
        id: Date.now() + Math.random(),
        ...eventData,
        ip: req.ip || req.connection.remoteAddress,
        userAgent: req.headers['user-agent'],
        receivedAt: new Date().toISOString(),
        sessionId: req.headers['x-session-id'] || 'anonymous'
      };

      // Add to events
      userEvents.unshift(eventEntry);
      
      // Keep only recent events
      if (userEvents.length > MAX_EVENTS) {
        userEvents = userEvents.slice(0, MAX_EVENTS);
      }

      res.status(200).json({
        success: true,
        message: 'User event logged successfully'
      });

    } catch (error) {
      console.error('User analytics logging failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to log user event'
      });
    }
  } else if (req.method === 'GET') {
    try {
      const { timeRange = '24h' } = req.query;
      
      // Calculate time filter
      const now = new Date();
      let startTime;
      
      switch (timeRange) {
        case '1h':
          startTime = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case '24h':
          startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        default:
          startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      }

      // Filter events by time range
      const filteredEvents = userEvents.filter(event => 
        new Date(event.timestamp || event.receivedAt) >= startTime
      );

      // Generate analytics
      const analytics = generateUserAnalytics(filteredEvents);

      res.status(200).json({
        success: true,
        data: analytics,
        timeRange,
        totalEvents: filteredEvents.length
      });

    } catch (error) {
      console.error('User analytics failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get user analytics'
      });
    }
  } else {
    res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }
}

function generateUserAnalytics(events) {
  // Event counts by type
  const eventsByType = events.reduce((acc, event) => {
    const type = event.event || 'unknown';
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {});

  // Page views analysis
  const pageViews = events.filter(e => e.event === 'page_view');
  const pagesByUrl = pageViews.reduce((acc, event) => {
    const page = event.page || 'unknown';
    acc[page] = (acc[page] || 0) + 1;
    return acc;
  }, {});

  const topPages = Object.entries(pagesByUrl)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .map(([page, views]) => ({ page, views }));

  // User actions analysis
  const userActions = events.filter(e => e.event === 'user_action');
  const actionsByType = userActions.reduce((acc, event) => {
    const action = event.action || 'unknown';
    acc[action] = (acc[action] || 0) + 1;
    return acc;
  }, {});

  const topActions = Object.entries(actionsByType)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .map(([action, count]) => ({ action, count }));

  // Conversion analysis
  const conversions = events.filter(e => e.event === 'conversion');
  const conversionsByType = conversions.reduce((acc, event) => {
    const type = event.type || 'unknown';
    if (!acc[type]) acc[type] = { count: 0, totalValue: 0 };
    acc[type].count += 1;
    acc[type].totalValue += event.value || 0;
    return acc;
  }, {});

  // Hourly activity
  const hourlyActivity = events.reduce((acc, event) => {
    const hour = new Date(event.timestamp || event.receivedAt).getHours();
    acc[hour] = (acc[hour] || 0) + 1;
    return acc;
  }, {});

  const hourlyData = Array.from({ length: 24 }, (_, hour) => ({
    hour,
    activity: hourlyActivity[hour] || 0
  }));

  // Browser/Device analysis
  const browsers = events.reduce((acc, event) => {
    if (event.userAgent) {
      let browser = 'Unknown';
      if (event.userAgent.includes('Chrome')) browser = 'Chrome';
      else if (event.userAgent.includes('Firefox')) browser = 'Firefox';
      else if (event.userAgent.includes('Safari')) browser = 'Safari';
      else if (event.userAgent.includes('Edge')) browser = 'Edge';
      
      acc[browser] = (acc[browser] || 0) + 1;
    }
    return acc;
  }, {});

  // Session analysis
  const uniqueSessions = new Set(events.map(e => e.sessionId)).size;
  const avgEventsPerSession = events.length / uniqueSessions;

  // Referrer analysis
  const referrers = events
    .filter(e => e.referrer && e.referrer !== '')
    .reduce((acc, event) => {
      let referrer = 'Direct';
      if (event.referrer.includes('google')) referrer = 'Google';
      else if (event.referrer.includes('facebook')) referrer = 'Facebook';
      else if (event.referrer.includes('instagram')) referrer = 'Instagram';
      else if (event.referrer !== document.location.origin) referrer = 'Other';
      
      acc[referrer] = (acc[referrer] || 0) + 1;
      return acc;
    }, {});

  // Real-time metrics
  const last5Minutes = new Date(Date.now() - 5 * 60 * 1000);
  const recentEvents = events.filter(e => 
    new Date(e.timestamp || e.receivedAt) >= last5Minutes
  );

  return {
    summary: {
      totalEvents: events.length,
      uniqueSessions,
      avgEventsPerSession: Math.round(avgEventsPerSession * 100) / 100,
      totalPageViews: pageViews.length,
      totalUserActions: userActions.length,
      totalConversions: conversions.length,
      recentActivity: recentEvents.length
    },
    eventsByType,
    topPages,
    topActions,
    conversionsByType,
    hourlyData,
    browsers,
    referrers,
    recentEvents: events.slice(0, 50).map(event => ({
      id: event.id,
      event: event.event,
      action: event.action,
      page: event.page,
      timestamp: event.timestamp || event.receivedAt,
      sessionId: event.sessionId
    }))
  };
}
