import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  // Check if user is authenticated and is an admin
  const session = await getServerSession(req, res, authOptions);

  if (!session || !session.user || session.user.role !== 'ADMIN') {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ success: false, message: 'Feature ID is required' });
  }

  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getSpotlightFeature(req, res, id);
      case 'PUT':
        return await updateSpotlightFeature(req, res, id);
      case 'DELETE':
        return await deleteSpotlightFeature(req, res, id);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    console.error(`Error in spotlight-features/${id} API:`, error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
}

// Get a specific spotlight feature
async function getSpotlightFeature(req, res, id) {
  try {
    const feature = await prisma.spotlightFeature.findUnique({
      where: { id },
      include: {
        _count: {
          select: { userSpotlights: true }
        }
      }
    });

    if (!feature) {
      return res.status(404).json({ success: false, message: 'Spotlight feature not found' });
    }

    // Get additional stats
    const now = new Date();
    const activeCount = await prisma.userSpotlight.count({
      where: {
        spotlightId: id,
        isActive: true,
        endTime: {
          gt: now
        }
      }
    });

    const revenue = await prisma.userSpotlight.aggregate({
      where: {
        spotlightId: id
      },
      _sum: {
        pricePaid: true
      }
    });

    const enhancedFeature = {
      ...feature,
      purchaseCount: feature._count.userSpotlights,
      activeCount,
      revenue: revenue._sum?.pricePaid || 0
    };

    return res.status(200).json({ success: true, feature: enhancedFeature });
  } catch (error) {
    console.error('Error fetching spotlight feature:', error);
    return res.status(500).json({ success: false, message: 'Failed to fetch spotlight feature' });
  }
}

// Update a spotlight feature
async function updateSpotlightFeature(req, res, id) {
  try {
    const {
      name,
      description,
      durationHours,
      price,
      discountPercent,
      defaultCount,
      isActive
    } = req.body;

    // Check if feature exists
    const existingFeature = await prisma.spotlightFeature.findUnique({
      where: { id }
    });

    if (!existingFeature) {
      return res.status(404).json({ success: false, message: 'Spotlight feature not found' });
    }

    // Prepare update data
    const updateData = {};

    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (durationHours !== undefined) updateData.durationHours = parseInt(durationHours);
    if (defaultCount !== undefined) updateData.defaultCount = parseInt(defaultCount);
    if (isActive !== undefined) updateData.isActive = isActive === true || isActive === 'true';

    // Update price and discount if provided
    if (price !== undefined) {
      updateData.price = parseFloat(price);

      // Calculate discounted price if discount is provided
      if (discountPercent !== undefined) {
        if (parseInt(discountPercent) > 0) {
          const discount = parseInt(discountPercent);
          updateData.discountPercent = discount;
          updateData.discountedPrice = updateData.price - (updateData.price * (discount / 100));
        } else {
          updateData.discountPercent = null;
          updateData.discountedPrice = null;
        }
      }
    }

    // Update the feature
    const updatedFeature = await prisma.spotlightFeature.update({
      where: { id },
      data: updateData
    });

    return res.status(200).json({ success: true, feature: updatedFeature });
  } catch (error) {
    console.error('Error updating spotlight feature:', error);
    return res.status(500).json({ success: false, message: 'Failed to update spotlight feature' });
  }
}

// Delete a spotlight feature
async function deleteSpotlightFeature(req, res, id) {
  try {
    // Check if feature exists
    const existingFeature = await prisma.spotlightFeature.findUnique({
      where: { id },
      include: {
        _count: {
          select: { userSpotlights: true }
        }
      }
    });

    if (!existingFeature) {
      return res.status(404).json({ success: false, message: 'Spotlight feature not found' });
    }

    // Check if feature has active users
    const now = new Date();
    const activeSpotlights = await prisma.userSpotlight.count({
      where: {
        spotlightId: id,
        isActive: true,
        endTime: {
          gt: now
        }
      }
    });

    if (activeSpotlights > 0) {
      // Instead of deleting, mark as inactive
      await prisma.spotlightFeature.update({
        where: { id },
        data: {
          isActive: false
        }
      });

      return res.status(200).json({
        success: true,
        message: 'Feature has active users and has been marked as inactive instead of deleted'
      });
    }

    // Delete the feature if no active users
    await prisma.spotlightFeature.delete({
      where: { id }
    });

    return res.status(200).json({ success: true, message: 'Spotlight feature deleted successfully' });
  } catch (error) {
    console.error('Error deleting spotlight feature:', error);
    return res.status(500).json({ success: false, message: 'Failed to delete spotlight feature' });
  }
}
