/* Feature Management Styles */

/* Category Header */
.category-header {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary);
  margin: 25px 0 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border);
}

.category-header:first-of-type {
  margin-top: 0;
}

/* Feature Table */
.feature-access-table {
  margin-bottom: 30px;
}

.feature-access-table th {
  white-space: nowrap;
}

.feature-description {
  font-size: 0.85rem;
  color: #666;
  margin-top: 5px;
}

/* Access Badges */
.access-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
  min-width: 80px;
}

.access-badge.enabled {
  background-color: rgba(76, 175, 80, 0.15);
  color: #2e7d32;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.access-badge.disabled {
  background-color: rgba(158, 158, 158, 0.15);
  color: #616161;
  border: 1px solid rgba(158, 158, 158, 0.3);
}

.access-badge.limited {
  background-color: rgba(255, 152, 0, 0.15);
  color: #ef6c00;
  border: 1px solid rgba(255, 152, 0, 0.3);
}

/* Tab Container */
.tab-container {
  border: 1px solid var(--border);
  border-radius: 8px;
  overflow: hidden;
}

.tab-nav {
  display: flex;
  background-color: #f5f5f5;
  border-bottom: 1px solid var(--border);
}

.tab-nav-item {
  padding: 12px 20px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  border-bottom: 2px solid transparent;
}

.tab-nav-item.active {
  background-color: white;
  border-bottom-color: var(--secondary);
  color: var(--primary);
}

.tab-nav-item:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.tab-content {
  display: none;
  padding: 20px;
}

.tab-content.active {
  display: block;
}

.limit-type-container {
  display: flex;
  gap: 20px;
  margin-top: 10px;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.action-btn {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.access-btn:hover {
  border-color: var(--primary);
  background-color: rgba(94, 53, 177, 0.05);
}

.edit-btn:hover {
  border-color: var(--info);
  background-color: rgba(33, 150, 243, 0.05);
}

.delete-btn:hover {
  border-color: var(--danger);
  background-color: rgba(244, 67, 54, 0.05);
}
