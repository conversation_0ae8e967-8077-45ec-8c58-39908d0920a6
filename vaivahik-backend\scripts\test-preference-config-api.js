/**
 * Test script for Preference Configuration API
 * Tests GET, PUT, and DELETE endpoints for preference configuration
 */

const axios = require('axios');

// Configuration
const BASE_URL = process.env.API_BASE_URL || 'http://localhost:8080';
const ADMIN_EMAIL = process.env.ADMIN_EMAIL || '<EMAIL>';
const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'admin123';

let authToken = '';

// Colors for console output
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
};

const log = {
    success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
    error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
    warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
    info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`)
};

/**
 * Authenticate as admin to get auth token
 */
async function authenticateAdmin() {
    try {
        log.info('Authenticating admin...');
        
        const response = await axios.post(`${BASE_URL}/api/admin/login`, {
            email: ADMIN_EMAIL,
            password: ADMIN_PASSWORD
        });

        if (response.data.success && response.data.token) {
            authToken = response.data.token;
            log.success('Admin authentication successful');
            return true;
        } else {
            log.error('Admin authentication failed: Invalid credentials');
            return false;
        }
    } catch (error) {
        log.error(`Admin authentication failed: ${error.response?.data?.message || error.message}`);
        return false;
    }
}

/**
 * Test GET /api/admin/preference-config
 */
async function testGetPreferenceConfig() {
    try {
        log.info('Testing GET /api/admin/preference-config...');
        
        const response = await axios.get(`${BASE_URL}/api/admin/preference-config`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.data.success) {
            log.success('GET preference configuration successful');
            log.info(`Categories: ${response.data.data.categories.length}`);
            log.info(`Fields: ${response.data.data.fields.length}`);
            log.info(`Options: ${response.data.data.options.length}`);
            log.info(`Importance Settings: ${response.data.data.importanceSettings.length}`);
            return response.data.data;
        } else {
            log.error(`GET preference configuration failed: ${response.data.message}`);
            return null;
        }
    } catch (error) {
        log.error(`GET preference configuration failed: ${error.response?.data?.message || error.message}`);
        return null;
    }
}

/**
 * Test PUT /api/admin/preference-config (Update categories)
 */
async function testUpdateCategories() {
    try {
        log.info('Testing PUT /api/admin/preference-config (categories)...');
        
        const updatedCategories = [
            {
                id: 'cat1',
                name: 'physical_attributes',
                displayName: 'Physical Attributes (Updated)',
                description: 'Updated physical characteristics preferences',
                displayOrder: 1,
                icon: 'person',
                isActive: true,
                isRequired: true
            }
        ];

        const response = await axios.put(`${BASE_URL}/api/admin/preference-config`, {
            type: 'categories',
            data: updatedCategories
        }, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.data.success) {
            log.success('PUT categories successful');
            return true;
        } else {
            log.error(`PUT categories failed: ${response.data.message}`);
            return false;
        }
    } catch (error) {
        log.error(`PUT categories failed: ${error.response?.data?.message || error.message}`);
        return false;
    }
}

/**
 * Test PUT /api/admin/preference-config (Update importance settings)
 */
async function testUpdateImportance() {
    try {
        log.info('Testing PUT /api/admin/preference-config (importance)...');
        
        const updatedImportance = [
            {
                id: 'imp1',
                importanceLevel: 9.0, // Updated from 8.0
                description: 'Age is very highly important for males',
                isActive: true,
                fieldId: 'field1',
                gender: 'MALE'
            }
        ];

        const response = await axios.put(`${BASE_URL}/api/admin/preference-config`, {
            type: 'importance',
            data: updatedImportance
        }, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.data.success) {
            log.success('PUT importance settings successful');
            return true;
        } else {
            log.error(`PUT importance settings failed: ${response.data.message}`);
            return false;
        }
    } catch (error) {
        log.error(`PUT importance settings failed: ${error.response?.data?.message || error.message}`);
        return false;
    }
}

/**
 * Test DELETE /api/admin/preference-config
 */
async function testDeletePreferenceItem() {
    try {
        log.info('Testing DELETE /api/admin/preference-config...');
        
        const response = await axios.delete(`${BASE_URL}/api/admin/preference-config?type=category&id=cat_test`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.data.success) {
            log.success('DELETE preference item successful');
            return true;
        } else {
            log.error(`DELETE preference item failed: ${response.data.message}`);
            return false;
        }
    } catch (error) {
        log.error(`DELETE preference item failed: ${error.response?.data?.message || error.message}`);
        return false;
    }
}

/**
 * Test invalid request scenarios
 */
async function testErrorHandling() {
    try {
        log.info('Testing error handling...');
        
        // Test invalid update type
        const response = await axios.put(`${BASE_URL}/api/admin/preference-config`, {
            type: 'invalid_type',
            data: []
        }, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.data.success) {
            log.success('Error handling test passed - invalid type rejected');
        } else {
            log.warning('Error handling test failed - invalid type accepted');
        }
    } catch (error) {
        if (error.response?.status === 400) {
            log.success('Error handling test passed - 400 Bad Request for invalid type');
        } else {
            log.error(`Unexpected error in error handling test: ${error.message}`);
        }
    }

    try {
        // Test delete without required parameters
        const response = await axios.delete(`${BASE_URL}/api/admin/preference-config`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.data.success) {
            log.success('Error handling test passed - missing parameters rejected');
        } else {
            log.warning('Error handling test failed - missing parameters accepted');
        }
    } catch (error) {
        if (error.response?.status === 400) {
            log.success('Error handling test passed - 400 Bad Request for missing parameters');
        } else {
            log.error(`Unexpected error in error handling test: ${error.message}`);
        }
    }
}

/**
 * Main test runner
 */
async function runTests() {
    console.log('\n🧪 Testing Preference Configuration API\n');
    
    // Authenticate first
    const authSuccess = await authenticateAdmin();
    if (!authSuccess) {
        log.error('Cannot proceed without authentication');
        process.exit(1);
    }

    console.log('\n--- Running API Tests ---\n');

    // Test GET endpoint
    const configData = await testGetPreferenceConfig();
    if (!configData) {
        log.error('GET test failed, skipping remaining tests');
        process.exit(1);
    }

    console.log('');

    // Test PUT endpoints
    await testUpdateCategories();
    console.log('');
    
    await testUpdateImportance();
    console.log('');

    // Test DELETE endpoint
    await testDeletePreferenceItem();
    console.log('');

    // Test error handling
    await testErrorHandling();

    console.log('\n--- Test Summary ---');
    log.success('All preference configuration API tests completed');
    log.info('Check the logs above for individual test results');
}

// Handle uncaught errors
process.on('unhandledRejection', (reason, promise) => {
    log.error(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
    process.exit(1);
});

// Run the tests
runTests().catch((error) => {
    log.error(`Test execution failed: ${error.message}`);
    process.exit(1);
});
