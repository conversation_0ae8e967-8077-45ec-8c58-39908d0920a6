/*
 * Standardized UI Components for Admin Panel
 * This file contains all the standardized UI components used across the admin panel
 */

/* ===== Variables ===== */
:root {
  /* Primary Colors */
  --primary: #5e35b1; /* Deep Purple */
  --primary-light: #7e57c2;
  --primary-dark: #4527a0;
  --secondary: #ff5722; /* Deep Orange */
  --secondary-light: #ff8a50;
  --secondary-dark: #c41c00;

  /* Status Colors */
  --success: #4caf50; /* Green */
  --warning: #ff9800; /* Amber */
  --danger: #f44336; /* Red */
  --info: #2196f3; /* Blue */

  /* Text Colors */
  --text-dark: #333;
  --text-light: #f5f5f5;
  --text-muted: #6c757d;

  /* Background Colors */
  --bg-light: #f9f9f9;
  --bg-white: #ffffff;
  --bg-dark: #121212;
  --bg-dark-light: #1e1e1e;

  /* Border & Shadow */
  --border-color: #e0e0e0;
  --border-radius: 8px;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);

  /* Layout */
  --sidebar-width: 260px;
  --sidebar-collapsed-width: 70px;
  --topbar-height: 60px;
  --content-padding: 20px;

  /* Transitions */
  --transition-speed: 0.3s;
}

/* ===== Dark Mode Variables ===== */
body.dark-mode {
  --primary: #7c4dff;
  --primary-light: #a387e9;
  --primary-dark: #4527a0;
  --secondary: #ff9800;
  --secondary-light: #ffb74d;
  --secondary-dark: #f57c00;

  --text-dark: #e4e4e4;
  --text-light: #23272f;
  --text-muted: #a0aec0;

  --bg-light: #23272f;
  --bg-white: #1a1d24;
  --bg-dark: #121212;
  --bg-dark-light: #1e1e1e;

  --border-color: #33384a;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* ===== Global Styles ===== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-dark);
  background-color: var(--bg-light);
}

/* ===== Admin Container ===== */
.admin-container {
  display: flex;
  min-height: 100vh;
}

/* ===== Sidebar ===== */
.sidebar {
  width: var(--sidebar-width);
  background: linear-gradient(to bottom, var(--primary), var(--primary-dark));
  color: var(--text-light);
  padding: 20px 0;
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  transition: width var(--transition-speed) ease;
  z-index: 1000;
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px 15px 15px;
}

.sidebar-logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: white;
}

.logo-icon {
  width: 30px;
  height: 30px;
  background-color: white;
  color: var(--primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 10px;
}

.logo-text {
  font-size: 1.2rem;
  font-weight: bold;
}

.sidebar.collapsed .logo-text {
  display: none;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
}

.sidebar-user {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 15px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-weight: bold;
}

.user-info {
  display: block;
}

.sidebar.collapsed .user-info {
  display: none;
}

.user-name {
  font-weight: bold;
}

.user-role {
  font-size: 0.8rem;
  opacity: 0.8;
}

.sidebar-nav {
  padding: 0 15px;
}

.nav-category {
  font-size: 0.8rem;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 5px;
  margin-top: 15px;
}

.sidebar.collapsed .nav-category {
  display: none;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-radius: 8px;
  margin-bottom: 5px;
  text-decoration: none;
  color: white;
  background-color: transparent;
  border-left: 4px solid transparent;
  transition: all 0.2s ease;
}

.nav-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-item.active {
  background-color: rgba(255, 255, 255, 0.1);
  border-left: 4px solid var(--secondary);
}

.nav-icon {
  margin-right: 10px;
  font-size: 1.2rem;
}

.sidebar.collapsed .nav-text {
  display: none;
}

.logout-btn {
  margin-top: 15px;
  background: none;
  border: none;
  cursor: pointer;
  width: 100%;
  text-align: left;
}

/* ===== Main Content ===== */
.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  transition: margin-left var(--transition-speed) ease, width var(--transition-speed) ease;
  width: calc(100% - var(--sidebar-width));
  min-height: 100vh;
  background-color: var(--bg-light);
}

.main-content.collapsed {
  margin-left: var(--sidebar-collapsed-width);
  width: calc(100% - var(--sidebar-collapsed-width));
}

/* ===== Top Bar ===== */
.topbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: var(--topbar-height);
  background-color: var(--bg-white);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 900;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--text-dark);
  font-size: 0.9rem;
}

.breadcrumb a {
  color: var(--primary);
  text-decoration: none;
}

.topbar-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.dark-mode-toggle {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.light-icon {
  display: block;
}

.dark-icon {
  display: none;
}

body.dark-mode .light-icon {
  display: none;
}

body.dark-mode .dark-icon {
  display: block;
}

.notifications {
  position: relative;
}

.notification-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: var(--secondary);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ===== Page Content ===== */
.page-content {
  padding: var(--content-padding);
}

/* ===== Mobile Menu ===== */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  margin-right: 10px;
}

.mobile-sidebar-close {
  display: none;
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  position: absolute;
  right: 15px;
  top: 15px;
}

.sidebar-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.sidebar-overlay.active {
  display: block;
  opacity: 1;
}

/* ===== Responsive Styles ===== */
@media (max-width: 1024px) {
  .main-content {
    margin-left: 0;
    width: 100%;
  }

  .main-content.collapsed {
    margin-left: 0;
    width: 100%;
  }

  .sidebar {
    transform: translateX(-100%);
    width: 280px;
  }

  .sidebar.collapsed {
    transform: translateX(-100%);
    width: 280px;
  }

  .sidebar.mobile-open {
    transform: translateX(0);
  }

  .mobile-menu-toggle {
    display: block;
  }

  .mobile-sidebar-close {
    display: block;
  }

  .sidebar-toggle {
    display: none;
  }
}

@media (max-width: 768px) {
  :root {
    --content-padding: 15px;
  }

  .topbar {
    padding: 0 15px;
  }

  .page-content {
    padding: 15px;
  }

  .breadcrumb span {
    display: none;
  }
}

@media (max-width: 480px) {
  .topbar {
    height: 50px;
  }

  .page-content {
    padding: 10px;
  }

  .breadcrumb a {
    font-size: 0.8rem;
  }
}

/* ===== Responsive Table Styles ===== */
@media (max-width: 768px) {
  .responsive-table {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .responsive-card-grid {
    grid-template-columns: 1fr !important;
  }
}

/* ===== Responsive Form Styles ===== */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr !important;
  }

  .form-actions {
    flex-direction: column;
    gap: 10px;
  }

  .form-actions button {
    width: 100%;
  }
}

/* ===== Responsive Card Styles ===== */
@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: 1fr !important;
  }

  .card-actions {
    flex-wrap: wrap;
  }
}

/* ===== Responsive Dialog Styles ===== */
@media (max-width: 768px) {
  .dialog-content {
    padding: 15px;
  }

  .dialog-actions {
    flex-direction: column;
    gap: 10px;
  }

  .dialog-actions button {
    width: 100%;
  }
}
