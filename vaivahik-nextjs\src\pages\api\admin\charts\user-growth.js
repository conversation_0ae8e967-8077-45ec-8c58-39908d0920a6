import axios from 'axios';
import { handleApiError } from '@/utils/errorHandler';
import { withAuth } from '@/utils/authHandler';
import { getCache, setCache, CACHE_TTL } from '@/utils/redisClient';

// Backend API URL - adjust this to your actual backend URL
const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3001/api';

// Cache keys
const CACHE_KEYS = {
  USER_GROWTH_DATA: (period) => `dashboard:charts:user-growth:${period}`
};

async function handler(req, res) {
  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getUserGrowthData(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    return handleApiError(error, res, 'User Growth Chart API');
  }
}

// GET /api/admin/charts/user-growth
async function getUserGrowthData(req, res) {
  try {
    const { period = 'month', refresh = 'false' } = req.query;
    const forceRefresh = refresh === 'true';

    // Get the auth token from the request cookies or headers
    const token = req.cookies?.adminToken || req.headers.authorization?.split(' ')[1];

    // Generate cache key for this specific period
    const cacheKey = CACHE_KEYS.USER_GROWTH_DATA(period);

    // If not forcing refresh, try to get data from cache
    if (!forceRefresh) {
      const cachedData = await getCache(cacheKey);

      if (cachedData) {
        console.log(`Returning user growth data for period ${period} from Redis cache`);

        // Add cache metadata to response
        return res.status(200).json({
          ...cachedData,
          _cache: {
            hit: true,
            period
          }
        });
      }
    } else {
      console.log(`Force refreshing user growth data for period ${period}`);
    }

    // In production, try to fetch from the backend API
    if (process.env.NODE_ENV === 'production') {
      try {
        // Fetch user growth data from the backend API
        const response = await axios({
          method: 'GET',
          url: `${BACKEND_API_URL}/admin/charts/user-growth`,
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          params: { period },
          timeout: 10000 // 10 second timeout
        });

        // Cache the response data
        const responseData = response.data;
        if (responseData.success) {
          await setCache(
            cacheKey,
            responseData,
            CACHE_TTL.DASHBOARD_CHARTS
          );

          // Add cache metadata to response
          responseData._cache = {
            hit: false,
            period
          };
        }

        // Return the response from the backend
        return res.status(200).json(responseData);
      } catch (apiError) {
        console.error('Error fetching user growth data from backend API:', apiError.message);

        // Try to get stale data from cache if available
        const cachedData = await getCache(cacheKey);
        if (cachedData) {
          console.log(`Returning stale user growth data for period ${period} from cache due to API error`);

          return res.status(200).json({
            ...cachedData,
            _cache: {
              hit: true,
              stale: true,
              period
            }
          });
        }

        // In production, return the error if no cache is available
        return res.status(500).json({
          success: false,
          message: 'Failed to fetch user growth data from backend API.',
          error: apiError.message
        });
      }
    }

    // Generate mock data based on the period
    let data = [];

    // Use consistent mock data for the same period
    const seed = period === 'week' ? 123 : period === 'month' ? 456 : 789;
    const random = (min, max, seed) => {
      const x = Math.sin(seed) * 10000;
      const rand = x - Math.floor(x);
      return Math.floor(rand * (max - min + 1)) + min;
    };

    switch (period) {
      case 'week':
        data = [12, 19, 15, 8, 22, 14, 11];
        break;
      case 'month':
        data = Array.from({ length: 30 }, (_, i) => random(5, 25, seed + i));
        break;
      case 'year':
        data = [45, 52, 38, 65, 74, 56, 48, 62, 58, 70, 65, 48];
        break;
      default:
        data = Array.from({ length: 30 }, (_, i) => random(5, 25, seed + i));
    }

    // Create response data
    const mockData = {
      success: true,
      data,
      period
    };

    // Cache the mock data
    await setCache(
      cacheKey,
      mockData,
      CACHE_TTL.DASHBOARD_CHARTS
    );

    // Add cache metadata to response
    mockData._cache = {
      hit: false,
      environment: 'development',
      period
    };

    // In development or if backend call fails in production, return mock data
    return res.status(200).json(mockData);
  } catch (error) {
    return handleApiError(error, res, 'Get user growth data');
  }
}

// Export the handler with authentication middleware
export default withAuth(handler, 'ADMIN');
