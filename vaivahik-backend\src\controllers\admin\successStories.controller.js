// src/controllers/admin/successStories.controller.js

const logger = require('../../utils/logger');

// Mock data for success stories
const mockSuccessStories = [
    {
        id: "story1",
        userId1: "user1",
        userId2: "user2",
        user1Name: "<PERSON><PERSON>",
        user2Name: "<PERSON>hul <PERSON>",
        status: "MARRIED",
        storyText: "We met through Vaivahik and got married within 6 months! The AI matching was incredibly accurate and helped us find our perfect match.",
        testimonyText: "The AI matching was spot on! We couldn't be happier with our choice.",
        rating: 5,
        marriageDate: "2023-11-15T00:00:00Z",
        photos: ["https://placehold.co/600x400/e91e63/ffffff?text=Wedding+Photo"],
        isPublic: true,
        createdAt: "2023-05-15T00:00:00Z",
        updatedAt: "2023-11-15T00:00:00Z"
    },
    {
        id: "story2",
        userId1: "user3",
        userId2: "user4",
        user1Name: "<PERSON><PERSON><PERSON>",
        user2Name: "<PERSON><PERSON><PERSON>",
        status: "ENGAGED",
        storyText: "We are so happy we found each other on this platform. The verification process made us feel safe and secure.",
        testimonyText: "The verification process made us feel safe and the matching algorithm is excellent.",
        rating: 4.5,
        engagementDate: "2023-12-25T00:00:00Z",
        photos: ["https://placehold.co/600x400/9c27b0/ffffff?text=Engagement+Photo"],
        isPublic: true,
        createdAt: "2023-06-20T00:00:00Z",
        updatedAt: "2023-12-25T00:00:00Z"
    },
    {
        id: "story3",
        userId1: "user5",
        userId2: "user6",
        user1Name: "Kavya Joshi",
        user2Name: "Vikram Pawar",
        status: "TALKING",
        storyText: "We've been talking for 3 months now and things are going great! Thank you Vaivahik for bringing us together.",
        testimonyText: "Great platform with genuine profiles. Highly recommended!",
        rating: 4,
        photos: ["https://placehold.co/600x400/4caf50/ffffff?text=Happy+Couple"],
        isPublic: true,
        createdAt: "2023-09-10T00:00:00Z",
        updatedAt: "2023-12-10T00:00:00Z"
    }
];

// Check if mock data should be used
const shouldUseMockData = () => {
    return process.env.NODE_ENV === 'development' || process.env.USE_MOCK_DATA === 'true';
};

/**
 * @description Get all success stories
 * @route GET /api/admin/success-stories
 */
exports.getSuccessStories = async (req, res, next) => {
    try {
        const { 
            page = 1, 
            limit = 10, 
            search = '',
            status = '',
            sortBy = 'createdAt', 
            order = 'desc'
        } = req.query;

        if (shouldUseMockData()) {
            // Return mock data
            let filteredStories = [...mockSuccessStories];

            // Apply search filter
            if (search) {
                filteredStories = filteredStories.filter(story => 
                    story.user1Name.toLowerCase().includes(search.toLowerCase()) ||
                    story.user2Name.toLowerCase().includes(search.toLowerCase()) ||
                    story.storyText.toLowerCase().includes(search.toLowerCase())
                );
            }

            // Apply status filter
            if (status) {
                filteredStories = filteredStories.filter(story => story.status === status);
            }

            // Apply sorting
            filteredStories.sort((a, b) => {
                const aValue = a[sortBy];
                const bValue = b[sortBy];
                if (order === 'asc') {
                    return aValue > bValue ? 1 : -1;
                } else {
                    return aValue < bValue ? 1 : -1;
                }
            });

            // Apply pagination
            const pageNum = parseInt(page);
            const limitNum = parseInt(limit);
            const startIndex = (pageNum - 1) * limitNum;
            const endIndex = startIndex + limitNum;
            const paginatedStories = filteredStories.slice(startIndex, endIndex);

            return res.status(200).json({
                success: true,
                message: "Success stories fetched successfully (Mock Data)",
                stories: paginatedStories,
                pagination: {
                    currentPage: pageNum,
                    limit: limitNum,
                    totalPages: Math.ceil(filteredStories.length / limitNum),
                    totalStories: filteredStories.length
                },
                useMockData: true
            });
        }

        // Real database implementation would go here
        const prisma = req.prisma;
        
        // For now, return empty array if real data is requested but not implemented
        return res.status(200).json({
            success: true,
            message: "Success stories fetched successfully (Real Data - Not Implemented Yet)",
            stories: [],
            pagination: {
                currentPage: 1,
                limit: parseInt(limit),
                totalPages: 0,
                totalStories: 0
            },
            useMockData: false
        });

    } catch (error) {
        logger.error('Error fetching success stories:', error);
        next(error);
    }
};

/**
 * @description Get success story by ID
 * @route GET /api/admin/success-stories/:id
 */
exports.getSuccessStoryById = async (req, res, next) => {
    try {
        const { id } = req.params;

        if (shouldUseMockData()) {
            const story = mockSuccessStories.find(s => s.id === id);
            if (!story) {
                return res.status(404).json({
                    success: false,
                    message: "Success story not found"
                });
            }

            return res.status(200).json({
                success: true,
                message: "Success story fetched successfully",
                story,
                useMockData: true
            });
        }

        // Real database implementation would go here
        return res.status(404).json({
            success: false,
            message: "Success story not found (Real Data - Not Implemented Yet)"
        });

    } catch (error) {
        logger.error('Error fetching success story:', error);
        next(error);
    }
};

/**
 * @description Create new success story
 * @route POST /api/admin/success-stories
 */
exports.createSuccessStory = async (req, res, next) => {
    try {
        const storyData = req.body;

        if (shouldUseMockData()) {
            const newStory = {
                id: `story${Date.now()}`,
                ...storyData,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            mockSuccessStories.push(newStory);

            return res.status(201).json({
                success: true,
                message: "Success story created successfully (Mock Data)",
                story: newStory,
                useMockData: true
            });
        }

        // Real database implementation would go here
        return res.status(501).json({
            success: false,
            message: "Create success story not implemented for real data yet"
        });

    } catch (error) {
        logger.error('Error creating success story:', error);
        next(error);
    }
};

/**
 * @description Update success story
 * @route PUT /api/admin/success-stories/:id
 */
exports.updateSuccessStory = async (req, res, next) => {
    try {
        const { id } = req.params;
        const updateData = req.body;

        if (shouldUseMockData()) {
            const storyIndex = mockSuccessStories.findIndex(s => s.id === id);
            if (storyIndex === -1) {
                return res.status(404).json({
                    success: false,
                    message: "Success story not found"
                });
            }

            mockSuccessStories[storyIndex] = {
                ...mockSuccessStories[storyIndex],
                ...updateData,
                updatedAt: new Date().toISOString()
            };

            return res.status(200).json({
                success: true,
                message: "Success story updated successfully (Mock Data)",
                story: mockSuccessStories[storyIndex],
                useMockData: true
            });
        }

        // Real database implementation would go here
        return res.status(501).json({
            success: false,
            message: "Update success story not implemented for real data yet"
        });

    } catch (error) {
        logger.error('Error updating success story:', error);
        next(error);
    }
};

/**
 * @description Delete success story
 * @route DELETE /api/admin/success-stories/:id
 */
exports.deleteSuccessStory = async (req, res, next) => {
    try {
        const { id } = req.params;

        if (shouldUseMockData()) {
            const storyIndex = mockSuccessStories.findIndex(s => s.id === id);
            if (storyIndex === -1) {
                return res.status(404).json({
                    success: false,
                    message: "Success story not found"
                });
            }

            mockSuccessStories.splice(storyIndex, 1);

            return res.status(200).json({
                success: true,
                message: "Success story deleted successfully (Mock Data)",
                useMockData: true
            });
        }

        // Real database implementation would go here
        return res.status(501).json({
            success: false,
            message: "Delete success story not implemented for real data yet"
        });

    } catch (error) {
        logger.error('Error deleting success story:', error);
        next(error);
    }
};

/**
 * @description Get success stories statistics
 * @route GET /api/admin/success-stories/stats/overview
 */
exports.getSuccessStoriesStats = async (req, res, next) => {
    try {
        if (shouldUseMockData()) {
            const stats = {
                totalStories: mockSuccessStories.length,
                publishedStories: mockSuccessStories.filter(s => s.isPublic).length,
                marriedCouples: mockSuccessStories.filter(s => s.status === 'MARRIED').length,
                engagedCouples: mockSuccessStories.filter(s => s.status === 'ENGAGED').length,
                averageRating: mockSuccessStories.reduce((sum, s) => sum + s.rating, 0) / mockSuccessStories.length,
                averageMatchTime: '4.2 months'
            };

            return res.status(200).json({
                success: true,
                message: "Success stories statistics fetched successfully (Mock Data)",
                stats,
                useMockData: true
            });
        }

        // Real database implementation would go here
        return res.status(200).json({
            success: true,
            message: "Success stories statistics fetched successfully (Real Data - Not Implemented Yet)",
            stats: {
                totalStories: 0,
                publishedStories: 0,
                marriedCouples: 0,
                engagedCouples: 0,
                averageRating: 0,
                averageMatchTime: '0 months'
            },
            useMockData: false
        });

    } catch (error) {
        logger.error('Error fetching success stories statistics:', error);
        next(error);
    }
};
