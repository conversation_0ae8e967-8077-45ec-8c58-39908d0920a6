/**
 * Performance Monitoring Middleware
 * 
 * This middleware tracks API performance metrics and logs them.
 * It can be used to identify slow endpoints and optimize them.
 */

const logger = require('../utils/logger');

// Store metrics in memory
const metrics = {
  requests: {},
  endpoints: {},
  slowRequests: []
};

// Configuration
const config = {
  slowThreshold: 500, // ms
  maxSlowRequests: 100,
  logInterval: 60000, // 1 minute
  enabled: process.env.ENABLE_PERFORMANCE_MONITORING === 'true'
};

// Start periodic logging
if (config.enabled) {
  setInterval(() => {
    logMetrics();
  }, config.logInterval);
}

/**
 * Log collected metrics
 */
function logMetrics() {
  try {
    // Get top 5 slowest endpoints
    const slowestEndpoints = Object.entries(metrics.endpoints)
      .map(([path, data]) => ({
        path,
        avgResponseTime: data.totalResponseTime / data.count,
        count: data.count,
        max: data.max
      }))
      .sort((a, b) => b.avgResponseTime - a.avgResponseTime)
      .slice(0, 5);
    
    // Log metrics
    logger.info('Performance metrics', {
      totalRequests: Object.values(metrics.requests).reduce((sum, count) => sum + count, 0),
      slowRequests: metrics.slowRequests.length,
      slowestEndpoints
    });
    
    // Log detailed slow requests if there are any
    if (metrics.slowRequests.length > 0) {
      logger.warn('Slow requests detected', {
        count: metrics.slowRequests.length,
        requests: metrics.slowRequests.slice(0, 5) // Log only the 5 slowest
      });
    }
  } catch (error) {
    logger.error('Error logging metrics', { error: error.message });
  }
}

/**
 * Reset metrics
 */
function resetMetrics() {
  metrics.requests = {};
  metrics.endpoints = {};
  metrics.slowRequests = [];
}

/**
 * Track request metrics
 * @param {string} method - HTTP method
 * @param {string} path - Request path
 * @param {number} responseTime - Response time in ms
 * @param {number} statusCode - HTTP status code
 */
function trackRequest(method, path, responseTime, statusCode) {
  try {
    // Track by method
    if (!metrics.requests[method]) {
      metrics.requests[method] = 0;
    }
    metrics.requests[method]++;
    
    // Track by endpoint
    const endpoint = `${method} ${path}`;
    if (!metrics.endpoints[endpoint]) {
      metrics.endpoints[endpoint] = {
        count: 0,
        totalResponseTime: 0,
        min: Number.MAX_SAFE_INTEGER,
        max: 0
      };
    }
    
    const endpointMetrics = metrics.endpoints[endpoint];
    endpointMetrics.count++;
    endpointMetrics.totalResponseTime += responseTime;
    endpointMetrics.min = Math.min(endpointMetrics.min, responseTime);
    endpointMetrics.max = Math.max(endpointMetrics.max, responseTime);
    
    // Track slow requests
    if (responseTime > config.slowThreshold) {
      metrics.slowRequests.push({
        method,
        path,
        responseTime,
        statusCode,
        timestamp: new Date().toISOString()
      });
      
      // Keep only the latest slow requests
      if (metrics.slowRequests.length > config.maxSlowRequests) {
        metrics.slowRequests.shift();
      }
    }
  } catch (error) {
    logger.error('Error tracking request metrics', { error: error.message });
  }
}

/**
 * Performance monitoring middleware
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 * @param {function} next - Express next function
 */
function performanceMonitor(req, res, next) {
  // Skip if monitoring is disabled
  if (!config.enabled) {
    return next();
  }
  
  // Record start time
  const start = Date.now();
  
  // Process request
  res.on('finish', () => {
    // Calculate response time
    const responseTime = Date.now() - start;
    
    // Get normalized path (replace IDs with placeholders)
    const path = req.route ? req.route.path : req.path;
    const normalizedPath = path.replace(/\/[0-9a-f]{24}/g, '/:id');
    
    // Track request metrics
    trackRequest(req.method, normalizedPath, responseTime, res.statusCode);
    
    // Log slow requests immediately
    if (responseTime > config.slowThreshold) {
      logger.warn('Slow request detected', {
        method: req.method,
        path: normalizedPath,
        responseTime,
        statusCode: res.statusCode
      });
    }
  });
  
  next();
}

/**
 * Get current metrics
 * @returns {object} Current metrics
 */
function getMetrics() {
  return {
    requests: { ...metrics.requests },
    endpoints: Object.entries(metrics.endpoints).map(([path, data]) => ({
      path,
      count: data.count,
      avgResponseTime: data.totalResponseTime / data.count,
      min: data.min,
      max: data.max
    })),
    slowRequests: [...metrics.slowRequests]
  };
}

/**
 * Update configuration
 * @param {object} newConfig - New configuration
 */
function updateConfig(newConfig) {
  Object.assign(config, newConfig);
}

module.exports = {
  performanceMonitor,
  getMetrics,
  resetMetrics,
  updateConfig
};
