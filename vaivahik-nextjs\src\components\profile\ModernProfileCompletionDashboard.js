/**
 * Modern Profile Completion Dashboard
 *
 * A modern UI dashboard showing profile completion status with gamification elements.
 * Displays progress for each section of the profile and provides quick links to complete them.
 */

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Grid,
  Typography,
  Paper,
  Button,
  CircularProgress,
  Chip,
  Card,
  CardContent,
  CardActions,
  Divider,
  LinearProgress,
  useTheme,
  alpha,
  Zoom,
  Fade,
  Grow,
  Tooltip
} from '@mui/material';
import {
  Person as PersonIcon,
  PhotoCamera as PhotoIcon,
  People as FamilyIcon,
  School as SchoolIcon,
  LocationOn as LocationIcon,
  Favorite as FavoriteIcon,
  Restaurant as DiningIcon,
  Interests as InterestsIcon,
  EmojiEvents as TrophyIcon,
  Lock as LockIcon,
  CheckCircle as CheckCircleIcon,
  Star as StarIcon,
  Lightbulb as TipIcon
} from '@mui/icons-material';
import Confetti from 'react-confetti';
import { motion } from 'framer-motion';
import { StyledButton } from '@/components/ui/ModernFormComponents';
import Lottie from 'react-lottie-player';

// Calculate completion percentage for each category
const calculateCategoryCompletion = (userData, category) => {
  if (!userData) return 0;

  switch (category) {
    case 'basic':
      return userData.fullName && userData.gender && userData.dateOfBirth ? 100 :
             userData.fullName && (userData.gender || userData.dateOfBirth) ? 66 :
             userData.fullName ? 33 : 0;
    case 'photos':
      return userData.profilePhoto ? 100 : 0;
    case 'family':
      return userData.familyDetails ?
             Object.keys(userData.familyDetails).length >= 5 ? 100 :
             Object.keys(userData.familyDetails).length >= 2 ? 50 : 0 : 0;
    case 'education':
      return userData.education && userData.occupation ? 100 :
             userData.education || userData.occupation ? 50 : 0;
    case 'location':
      return userData.city && userData.state ? 100 :
             userData.city || userData.state ? 50 : 0;
    case 'preferences':
      return userData.partnerPreferences ?
             Object.keys(userData.partnerPreferences).length >= 5 ? 100 :
             Object.keys(userData.partnerPreferences).length >= 2 ? 50 : 0 : 0;
    case 'lifestyle':
      return userData.lifestyle ?
             Object.keys(userData.lifestyle).length >= 3 ? 100 :
             Object.keys(userData.lifestyle).length >= 1 ? 50 : 0 : 0;
    case 'about':
      return userData.aboutMe && userData.aboutMe.length >= 50 ? 100 :
             userData.aboutMe ? 50 : 0;
    default:
      return 0;
  }
};

// Import animation data
import completeAnimation from '@/public/animations/complete.json';
import progressAnimation from '@/public/animations/progress.json';
import trophyAnimation from '@/public/animations/trophy.json';

const ModernProfileCompletionDashboard = ({ userData, onUpdateSection }) => {
  const theme = useTheme();
  const router = useRouter();
  const [overallCompletion, setOverallCompletion] = useState(0);
  const [earnedBadges, setEarnedBadges] = useState([]);
  const [lockedFeatures, setLockedFeatures] = useState([]);
  const [showConfetti, setShowConfetti] = useState(false);
  const [previousCompletion, setPreviousCompletion] = useState(0);
  const [showCompletionAnimation, setShowCompletionAnimation] = useState(false);
  const [animatingCategory, setAnimatingCategory] = useState(null);
  const [showTips, setShowTips] = useState({});

  // Refs for animations
  const completeAnimationRef = useRef(null);
  const progressAnimationRef = useRef(null);
  const trophyAnimationRef = useRef(null);

  // Categories for profile completion
  const categories = [
    {
      id: 'basic',
      name: 'Basic Details',
      icon: <PersonIcon />,
      route: '/profile/edit/basic',
      completionPercentage: calculateCategoryCompletion(userData, 'basic'),
      requiredForLevel: 1,
      benefitText: 'Required to appear in search results',
      timeToComplete: '2 min'
    },
    {
      id: 'photos',
      name: 'Profile Photos',
      icon: <PhotoIcon />,
      route: '/profile/edit/photos',
      completionPercentage: calculateCategoryCompletion(userData, 'photos'),
      requiredForLevel: 1,
      benefitText: 'Profiles with photos get 10x more responses',
      timeToComplete: '1 min'
    },
    {
      id: 'family',
      name: 'Family Details',
      icon: <FamilyIcon />,
      route: '/website/pages/profile/edit/modern-family',
      completionPercentage: calculateCategoryCompletion(userData, 'family'),
      requiredForLevel: 2,
      benefitText: 'Improves match quality by 40%',
      timeToComplete: '3 min'
    },
    {
      id: 'education',
      name: 'Education & Career',
      icon: <SchoolIcon />,
      route: '/profile/edit/education',
      completionPercentage: calculateCategoryCompletion(userData, 'education'),
      requiredForLevel: 1,
      benefitText: 'Helps match with compatible professionals',
      timeToComplete: '2 min'
    },
    {
      id: 'location',
      name: 'Location Details',
      icon: <LocationIcon />,
      route: '/profile/edit/location',
      completionPercentage: calculateCategoryCompletion(userData, 'location'),
      requiredForLevel: 1,
      benefitText: 'Find matches in your preferred locations',
      timeToComplete: '1 min'
    },
    {
      id: 'preferences',
      name: 'Partner Preferences',
      icon: <FavoriteIcon />,
      route: '/website/pages/profile/edit/modern-preferences',
      completionPercentage: calculateCategoryCompletion(userData, 'preferences'),
      requiredForLevel: 2,
      benefitText: 'Receive more relevant match suggestions',
      timeToComplete: '4 min'
    },
    {
      id: 'lifestyle',
      name: 'Lifestyle & Habits',
      icon: <DiningIcon />,
      route: '/website/pages/profile/edit/lifestyle',
      completionPercentage: calculateCategoryCompletion(userData, 'lifestyle'),
      requiredForLevel: 3,
      benefitText: 'Find partners with compatible lifestyles',
      timeToComplete: '2 min'
    },
    {
      id: 'about',
      name: 'About Me',
      icon: <InterestsIcon />,
      route: '/profile/edit/about',
      completionPercentage: calculateCategoryCompletion(userData, 'about'),
      requiredForLevel: 1,
      benefitText: 'Express yourself to attract better matches',
      timeToComplete: '3 min'
    }
  ];

  // Calculate overall completion percentage
  useEffect(() => {
    if (!userData) return;

    // Store previous completion for animation effects
    setPreviousCompletion(overallCompletion);

    const totalCategories = categories.length;
    const completedPercentage = categories.reduce((sum, category) => sum + category.completionPercentage, 0) / totalCategories;

    setOverallCompletion(Math.round(completedPercentage));

    // Determine earned badges based on completion
    const badges = [];
    if (completedPercentage >= 25) badges.push('bronze');
    if (completedPercentage >= 50) badges.push('silver');
    if (completedPercentage >= 75) badges.push('gold');
    if (completedPercentage === 100) badges.push('platinum');

    // Check for newly earned badges
    const newBadges = badges.filter(badge => !earnedBadges.includes(badge));
    if (newBadges.length > 0) {
      // Play trophy animation for new badges
      if (trophyAnimationRef.current) {
        trophyAnimationRef.current.play();
      }
    }

    setEarnedBadges(badges);

    // Determine locked features
    const locked = [];
    if (completedPercentage < 40) locked.push('advanced_search');
    if (completedPercentage < 60) locked.push('contact_info');
    if (completedPercentage < 80) locked.push('spotlight');
    setLockedFeatures(locked);

    // Show animations based on completion milestones
    if (completedPercentage - previousCompletion >= 10 ||
        (previousCompletion < 25 && completedPercentage >= 25) ||
        (previousCompletion < 50 && completedPercentage >= 50) ||
        (previousCompletion < 75 && completedPercentage >= 75)) {
      // Show confetti for significant progress
      setShowConfetti(true);
      setTimeout(() => setShowConfetti(false), 5000);

      // Play progress animation
      if (progressAnimationRef.current) {
        progressAnimationRef.current.play();
      }
    }

    // Show completion animation when profile is 100% complete
    if (previousCompletion < 100 && completedPercentage === 100) {
      setShowCompletionAnimation(true);
      if (completeAnimationRef.current) {
        completeAnimationRef.current.play();
      }
      setTimeout(() => setShowCompletionAnimation(false), 3000);
    }

    // Find categories that have been completed since last update
    const completedCategories = categories.filter(
      category => category.completionPercentage === 100 &&
      calculateCategoryCompletion(userData, category.id) > 0 &&
      calculateCategoryCompletion(userData, category.id) !== category.completionPercentage
    );

    if (completedCategories.length > 0) {
      setAnimatingCategory(completedCategories[0].id);
      setTimeout(() => setAnimatingCategory(null), 2000);
    }

  }, [categories, userData, earnedBadges, previousCompletion]);

  // Navigate to edit section
  const handleEditSection = (route) => {
    router.push(route);
  };

  // Toggle tip visibility for a category
  const toggleTip = (categoryId) => {
    setShowTips(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  // Get profile strength label
  const getProfileStrengthLabel = () => {
    if (overallCompletion < 25) return 'Weak';
    if (overallCompletion < 50) return 'Basic';
    if (overallCompletion < 75) return 'Good';
    if (overallCompletion < 100) return 'Strong';
    return 'Excellent';
  };

  // Get profile strength color
  const getProfileStrengthColor = () => {
    if (overallCompletion < 25) return '#ff5757';
    if (overallCompletion < 50) return '#ffa726';
    if (overallCompletion < 75) return '#66bb6a';
    if (overallCompletion < 100) return '#26a69a';
    return '#8e24aa';
  };

  // Get next recommended section to complete
  const getNextRecommendedSection = () => {
    const incompleteCategories = categories
      .filter(category => category.completionPercentage < 100)
      .sort((a, b) => a.requiredForLevel - b.requiredForLevel);

    return incompleteCategories.length > 0 ? incompleteCategories[0] : null;
  };

  // Get tips for a category
  const getCategoryTips = (categoryId) => {
    switch(categoryId) {
      case 'basic':
        return "Include your full name, accurate date of birth, and current photos for better matches.";
      case 'photos':
        return "Upload clear, recent photos. Include at least one close-up and one full-length photo.";
      case 'family':
        return "Share details about your family background to help find culturally compatible matches.";
      case 'education':
        return "Be specific about your education and career details to match with compatible professionals.";
      case 'location':
        return "Specify your current location and any preferred locations for potential matches.";
      case 'preferences':
        return "Be clear but not too restrictive with your preferences to get quality matches.";
      case 'lifestyle':
        return "Share your lifestyle, habits, and interests to find someone with compatible daily routines.";
      case 'about':
        return "Write a genuine, detailed description about yourself and what you're looking for in a partner.";
      default:
        return "Complete this section to improve your profile strength.";
    }
  };

  const nextRecommended = getNextRecommendedSection();

  return (
    <>
      {showConfetti && <Confetti recycle={false} numberOfPieces={200} />}

      {/* Completion Animation Overlay */}
      {showCompletionAnimation && (
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            zIndex: 9999,
          }}
        >
          <Box sx={{ textAlign: 'center' }}>
            <Lottie
              ref={completeAnimationRef}
              animationData={completeAnimation}
              style={{ width: 300, height: 300 }}
              loop={false}
              play
            />
            <Typography variant="h4" sx={{ color: 'white', mt: 2 }}>
              Congratulations!
            </Typography>
            <Typography variant="h6" sx={{ color: 'white', mt: 1 }}>
              Your profile is now 100% complete!
            </Typography>
          </Box>
        </Box>
      )}

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" gutterBottom fontWeight={600}>
            Complete Your Profile
          </Typography>
          <Typography variant="body1" color="text.secondary">
            A complete profile helps you find better matches and increases your visibility.
          </Typography>
        </Box>
      </motion.div>

      {/* Overall Progress */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Paper
          elevation={3}
          sx={{
            p: 3,
            mb: 4,
            borderRadius: 4,
            background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
            position: 'relative',
            overflow: 'hidden',
            boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
            transition: 'all 0.3s ease',
            '&:hover': {
              boxShadow: '0 15px 40px rgba(0, 0, 0, 0.15)',
              transform: 'translateY(-5px)'
            }
          }}
        >
          <Box sx={{
            position: 'absolute',
            top: 0,
            right: 0,
            width: '150px',
            height: '150px',
            borderRadius: '0 0 0 100%',
            background: 'linear-gradient(135deg, rgba(255, 95, 109, 0.05) 0%, rgba(255, 195, 113, 0.1) 100%)',
            zIndex: 0
          }} />

          <Grid container spacing={3} position="relative" zIndex={1}>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{ position: 'relative', display: 'inline-flex', mr: 3 }}>
                  {overallCompletion < 100 ? (
                    <Box sx={{ position: 'relative' }}>
                      <CircularProgress
                        variant="determinate"
                        value={overallCompletion}
                        size={100}
                        thickness={5}
                        sx={{ color: getProfileStrengthColor() }}
                      />
                      <Box
                        sx={{
                          top: 0,
                          left: 0,
                          bottom: 0,
                          right: 0,
                          position: 'absolute',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <Typography variant="h4" component="div" fontWeight={700}>
                          {`${overallCompletion}%`}
                        </Typography>
                      </Box>
                      <Box sx={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}>
                        <Lottie
                          ref={progressAnimationRef}
                          animationData={progressAnimation}
                          style={{ width: 100, height: 100 }}
                          loop={true}
                          speed={0.5}
                        />
                      </Box>
                    </Box>
                  ) : (
                    <Zoom in={true} timeout={1000}>
                      <Box sx={{ position: 'relative' }}>
                        <CircularProgress
                          variant="determinate"
                          value={100}
                          size={100}
                          thickness={5}
                          sx={{ color: getProfileStrengthColor() }}
                        />
                        <Box
                          sx={{
                            top: 0,
                            left: 0,
                            bottom: 0,
                            right: 0,
                            position: 'absolute',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <Typography variant="h4" component="div" fontWeight={700}>
                            {`${overallCompletion}%`}
                          </Typography>
                        </Box>
                        <Box sx={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}>
                          <Lottie
                            ref={completeAnimationRef}
                            animationData={completeAnimation}
                            style={{ width: 100, height: 100 }}
                            loop={false}
                          />
                        </Box>
                      </Box>
                    </Zoom>
                  )}
                </Box>

                <Box>
                  <Typography variant="h5" fontWeight={600}>
                    Profile Strength: <span style={{ color: getProfileStrengthColor() }}>{getProfileStrengthLabel()}</span>
                  </Typography>
                  <Typography variant="body1" color="text.secondary" paragraph>
                    {overallCompletion < 100
                      ? `Complete your profile to unlock all features and get better matches.`
                      : `Congratulations! Your profile is complete and will receive maximum visibility.`}
                  </Typography>

                  {/* Badges */}
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                    <Tooltip title="Bronze badge: 25% profile completion" arrow>
                      <Zoom in={earnedBadges.includes('bronze')} timeout={500}>
                        <Chip
                          icon={<TrophyIcon />}
                          label="Bronze"
                          sx={{
                            bgcolor: earnedBadges.includes('bronze') ? '#cd7f32' : alpha('#cd7f32', 0.3),
                            color: earnedBadges.includes('bronze') ? 'white' : 'text.secondary',
                            fontWeight: 600,
                            transition: 'all 0.3s ease',
                            '&:hover': {
                              transform: 'scale(1.05)',
                              boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                            }
                          }}
                        />
                      </Zoom>
                    </Tooltip>
                    <Tooltip title="Silver badge: 50% profile completion" arrow>
                      <Zoom in={earnedBadges.includes('silver')} timeout={500} style={{ transitionDelay: earnedBadges.includes('silver') ? '100ms' : '0ms' }}>
                        <Chip
                          icon={<TrophyIcon />}
                          label="Silver"
                          sx={{
                            bgcolor: earnedBadges.includes('silver') ? '#C0C0C0' : alpha('#C0C0C0', 0.3),
                            color: earnedBadges.includes('silver') ? 'white' : 'text.secondary',
                            fontWeight: 600,
                            transition: 'all 0.3s ease',
                            '&:hover': {
                              transform: 'scale(1.05)',
                              boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                            }
                          }}
                        />
                      </Zoom>
                    </Tooltip>
                    <Tooltip title="Gold badge: 75% profile completion" arrow>
                      <Zoom in={earnedBadges.includes('gold')} timeout={500} style={{ transitionDelay: earnedBadges.includes('gold') ? '200ms' : '0ms' }}>
                        <Chip
                          icon={<TrophyIcon />}
                          label="Gold"
                          sx={{
                            bgcolor: earnedBadges.includes('gold') ? '#FFD700' : alpha('#FFD700', 0.3),
                            color: earnedBadges.includes('gold') ? 'white' : 'text.secondary',
                            fontWeight: 600,
                            transition: 'all 0.3s ease',
                            '&:hover': {
                              transform: 'scale(1.05)',
                              boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                            }
                          }}
                        />
                      </Zoom>
                    </Tooltip>
                    <Tooltip title="Platinum badge: 100% profile completion" arrow>
                      <Zoom in={earnedBadges.includes('platinum')} timeout={500} style={{ transitionDelay: earnedBadges.includes('platinum') ? '300ms' : '0ms' }}>
                        <Box sx={{ display: 'inline-block', position: 'relative' }}>
                          <Chip
                            icon={<TrophyIcon />}
                            label="Platinum"
                            sx={{
                              bgcolor: earnedBadges.includes('platinum') ? '#8e24aa' : alpha('#8e24aa', 0.3),
                              color: earnedBadges.includes('platinum') ? 'white' : 'text.secondary',
                              fontWeight: 600,
                              transition: 'all 0.3s ease',
                              '&:hover': {
                                transform: 'scale(1.05)',
                                boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                              }
                            }}
                          />
                          {earnedBadges.includes('platinum') && (
                            <Box sx={{ position: 'absolute', top: -10, right: -10 }}>
                              <Lottie
                                ref={trophyAnimationRef}
                                animationData={trophyAnimation}
                                style={{ width: 40, height: 40 }}
                                loop={false}
                              />
                            </Box>
                          )}
                        </Box>
                      </Zoom>
                    </Tooltip>
                  </Box>
                </Box>
              </Box>
            </Grid>

          <Grid item xs={12} md={6}>
            <Box>
              <Typography variant="subtitle1" fontWeight={600} gutterBottom>
                Features Unlocked by Profile Completion
              </Typography>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Fade in={true} timeout={1000} style={{ transitionDelay: '100ms' }}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      p: 1.5,
                      borderRadius: 2,
                      bgcolor: lockedFeatures.includes('advanced_search') ? 'transparent' : alpha(theme.palette.success.main, 0.1),
                      transition: 'all 0.3s ease',
                      border: lockedFeatures.includes('advanced_search') ? '1px dashed #ddd' : '1px solid transparent',
                    }}
                  >
                    {lockedFeatures.includes('advanced_search') ? (
                      <LockIcon fontSize="small" sx={{ mr: 1, color: 'text.disabled' }} />
                    ) : (
                      <CheckCircleIcon fontSize="small" sx={{ mr: 1, color: 'success.main' }} />
                    )}
                    <Box>
                      <Typography
                        variant="body2"
                        fontWeight={600}
                        color={lockedFeatures.includes('advanced_search') ? 'text.disabled' : 'text.primary'}
                      >
                        Advanced Search Filters
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Requires 40% profile completion
                      </Typography>
                    </Box>
                  </Box>
                </Fade>

                <Fade in={true} timeout={1000} style={{ transitionDelay: '200ms' }}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      p: 1.5,
                      borderRadius: 2,
                      bgcolor: lockedFeatures.includes('contact_info') ? 'transparent' : alpha(theme.palette.success.main, 0.1),
                      transition: 'all 0.3s ease',
                      border: lockedFeatures.includes('contact_info') ? '1px dashed #ddd' : '1px solid transparent',
                    }}
                  >
                    {lockedFeatures.includes('contact_info') ? (
                      <LockIcon fontSize="small" sx={{ mr: 1, color: 'text.disabled' }} />
                    ) : (
                      <CheckCircleIcon fontSize="small" sx={{ mr: 1, color: 'success.main' }} />
                    )}
                    <Box>
                      <Typography
                        variant="body2"
                        fontWeight={600}
                        color={lockedFeatures.includes('contact_info') ? 'text.disabled' : 'text.primary'}
                      >
                        View Contact Information
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Requires 60% profile completion
                      </Typography>
                    </Box>
                  </Box>
                </Fade>

                <Fade in={true} timeout={1000} style={{ transitionDelay: '300ms' }}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      p: 1.5,
                      borderRadius: 2,
                      bgcolor: lockedFeatures.includes('spotlight') ? 'transparent' : alpha(theme.palette.success.main, 0.1),
                      transition: 'all 0.3s ease',
                      border: lockedFeatures.includes('spotlight') ? '1px dashed #ddd' : '1px solid transparent',
                    }}
                  >
                    {lockedFeatures.includes('spotlight') ? (
                      <LockIcon fontSize="small" sx={{ mr: 1, color: 'text.disabled' }} />
                    ) : (
                      <CheckCircleIcon fontSize="small" sx={{ mr: 1, color: 'success.main' }} />
                    )}
                    <Box>
                      <Typography
                        variant="body2"
                        fontWeight={600}
                        color={lockedFeatures.includes('spotlight') ? 'text.disabled' : 'text.primary'}
                      >
                        Profile Spotlight
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Requires 80% profile completion
                      </Typography>
                    </Box>
                  </Box>
                </Fade>
              </Box>

              {nextRecommended && (
                <Grow in={true} timeout={1000} style={{ transformOrigin: '0 0 0' }}>
                  <Box sx={{ mt: 3 }}>
                    <StyledButton
                      variant="contained"
                      onClick={() => handleEditSection(nextRecommended.route)}
                      startIcon={nextRecommended.icon}
                      sx={{
                        position: 'relative',
                        overflow: 'hidden',
                        '&::after': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: '-100%',
                          width: '100%',
                          height: '100%',
                          background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                          animation: 'shine 2s infinite',
                        },
                        '@keyframes shine': {
                          '0%': { left: '-100%' },
                          '100%': { left: '100%' }
                        }
                      }}
                    >
                      Complete {nextRecommended.name} Now
                    </StyledButton>
                    <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                      Takes only {nextRecommended.timeToComplete} to complete
                    </Typography>
                  </Box>
                </Grow>
              )}
            </Box>
          </Grid>
        </Grid>
      </Paper>
      </motion.div>

      {/* Category Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Typography variant="h5" gutterBottom fontWeight={600} sx={{ mb: 3 }}>
          Profile Sections
        </Typography>
      </motion.div>

      <Grid container spacing={3}>
        {categories.map((category, index) => (
          <Grid item xs={12} sm={6} md={4} key={category.id}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 * (index + 1) }}
            >
              <Card
                sx={{
                  borderRadius: 3,
                  height: '100%',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-5px)',
                    boxShadow: '0 8px 30px rgba(0, 0, 0, 0.1)',
                  },
                  position: 'relative',
                  overflow: 'hidden',
                  border: animatingCategory === category.id ? '2px solid var(--primary-color)' : 'none',
                  boxShadow: animatingCategory === category.id ? '0 0 15px rgba(255, 95, 109, 0.5)' : '0 4px 12px rgba(0, 0, 0, 0.05)'
                }}
              >
                {category.completionPercentage === 100 && (
                  <Zoom in={true} timeout={500} style={{ transitionDelay: '300ms' }}>
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 0,
                        right: 0,
                        bgcolor: 'success.main',
                        color: 'white',
                        px: 2,
                        py: 0.5,
                        borderRadius: '0 0 0 8px',
                        fontSize: '0.75rem',
                        fontWeight: 600,
                        zIndex: 1
                      }}
                    >
                      COMPLETED
                    </Box>
                  </Zoom>
                )}

                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: 40,
                        height: 40,
                        borderRadius: '50%',
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        color: 'primary.main',
                        mr: 2,
                        transition: 'all 0.3s ease',
                        animation: animatingCategory === category.id ? 'pulse 1.5s infinite' : 'none',
                        '@keyframes pulse': {
                          '0%': { transform: 'scale(1)', boxShadow: '0 0 0 0 rgba(255, 95, 109, 0.7)' },
                          '70%': { transform: 'scale(1.1)', boxShadow: '0 0 0 10px rgba(255, 95, 109, 0)' },
                          '100%': { transform: 'scale(1)', boxShadow: '0 0 0 0 rgba(255, 95, 109, 0)' }
                        }
                      }}
                    >
                      {category.icon}
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography variant="h6" fontWeight={600}>
                        {category.name}
                      </Typography>
                      <Tooltip title="View tips" arrow>
                        <IconButton
                          size="small"
                          onClick={() => toggleTip(category.id)}
                          sx={{ ml: 0.5, color: 'text.secondary' }}
                        >
                          <TipIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box>

                  {showTips[category.id] && (
                    <Fade in={showTips[category.id]} timeout={500}>
                      <Box
                        sx={{
                          p: 1.5,
                          mb: 2,
                          bgcolor: alpha(theme.palette.info.main, 0.1),
                          borderRadius: 2,
                          border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
                        }}
                      >
                        <Typography variant="body2" color="text.secondary">
                          <b>Tip:</b> {getCategoryTips(category.id)}
                        </Typography>
                      </Box>
                    </Fade>
                  )}

                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                      <Typography variant="body2" color="text.secondary">
                        Completion
                      </Typography>
                      <Typography variant="body2" fontWeight={600}>
                        {category.completionPercentage}%
                      </Typography>
                    </Box>
                    <Box sx={{ position: 'relative' }}>
                      <LinearProgress
                        variant="determinate"
                        value={category.completionPercentage}
                        sx={{
                          height: 8,
                          borderRadius: 4,
                          bgcolor: alpha(theme.palette.primary.main, 0.1),
                          '& .MuiLinearProgress-bar': {
                            bgcolor: category.completionPercentage === 100 ? 'success.main' : 'primary.main',
                            transition: 'transform 1.5s ease'
                          }
                        }}
                      />
                      {animatingCategory === category.id && (
                        <Box
                          sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            height: '100%',
                            borderRadius: 4,
                            background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent)',
                            animation: 'shimmer 1.5s infinite',
                            '@keyframes shimmer': {
                              '0%': { transform: 'translateX(-100%)' },
                              '100%': { transform: 'translateX(100%)' }
                            }
                          }}
                        />
                      )}
                    </Box>
                  </Box>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    {category.benefitText}
                  </Typography>

                  <Typography variant="caption" color="text.secondary">
                    Takes about {category.timeToComplete} to complete
                  </Typography>
                </CardContent>

                <CardActions sx={{ p: 2, pt: 0 }}>
                  <StyledButton
                    variant={category.completionPercentage === 100 ? "outlined" : "contained"}
                    size="small"
                    fullWidth
                    onClick={() => handleEditSection(category.route)}
                    sx={{
                      position: 'relative',
                      overflow: 'hidden',
                      ...(category.completionPercentage < 100 && {
                        '&::after': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: '-100%',
                          width: '100%',
                          height: '100%',
                          background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                          animation: 'shine 3s infinite',
                        },
                        '@keyframes shine': {
                          '0%': { left: '-100%' },
                          '100%': { left: '100%' }
                        }
                      })
                    }}
                  >
                    {category.completionPercentage === 100 ? "Edit" : "Complete Now"}
                  </StyledButton>
                </CardActions>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>
    </>
  );
};

export default ModernProfileCompletionDashboard;
