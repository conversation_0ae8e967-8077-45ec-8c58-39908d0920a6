// Mock data for API endpoints

// Generate mock verification queue data
export function generateMockVerifications() {
  return [
    {
      id: 1,
      user: {
        id: 101,
        name: '<PERSON><PERSON>',
        age: 28,
        location: 'Mumbai',
        registrationDate: '2023-01-15',
        photo: '/img/user-placeholder.jpg',
        email: '<EMAIL>',
        phone: '+91 9876543210',
        occupation: 'Software Engineer',
        education: 'B.Tech in Computer Science',
        maritalStatus: 'Never Married',
        // Additional details
        height: '5\'10"',
        weight: '75 kg',
        complexion: 'Fair',
        religion: 'Hindu',
        caste: '<PERSON><PERSON>',
        subcaste: '<PERSON><PERSON><PERSON><PERSON>',
        gotra: 'Ka<PERSON>ya<PERSON>',
        diet: 'Vegetarian',
        smoking: 'No',
        drinking: 'Occasionally',
        income: '12-15 LPA',
        family: {
          father: '<PERSON><PERSON>',
          fatherOccupation: 'Retired Government Officer',
          mother: '<PERSON><PERSON>',
          motherOccupation: 'Homemaker',
          brothers: 1,
          sisters: 1,
          familyType: 'Joint Family',
          familyValues: 'Traditional',
          familyStatus: 'Middle Class',
          familyLocation: 'Mumbai'
        },
        horoscope: {
          manglik: 'No',
          nakshatra: '<PERSON><PERSON><PERSON>',
          rashi: 'Taurus'
        },
        preferences: {
          ageRange: '25-30',
          heightRange: '5\'2" - 5\'8"',
          education: 'Graduate',
          occupation: 'Any',
          location: 'Mumbai, Pune',
          caste: 'No Preference',
          income: 'Any'
        }
      },
      documents: [
        { id: 1, type: 'ID Proof', url: '/img/document-placeholder.jpg', name: 'Aadhar Card' },
        { id: 2, type: 'Address Proof', url: '/img/document-placeholder.jpg', name: 'Passport' }
      ],
      status: 'pending',
      submittedOn: '2023-07-10'
    },
    {
      id: 2,
      user: {
        id: 102,
        name: 'Priya Patel',
        age: 26,
        location: 'Pune',
        registrationDate: '2023-02-20',
        photo: '/img/user-placeholder.jpg',
        email: '<EMAIL>',
        phone: '+91 9876543211',
        occupation: 'Doctor',
        education: 'MBBS',
        maritalStatus: 'Never Married'
      },
      documents: [
        { id: 3, type: 'ID Proof', url: '/img/document-placeholder.jpg', name: 'Aadhar Card' },
        { id: 4, type: 'Address Proof', url: '/img/document-placeholder.jpg', name: 'Voter ID' }
      ],
      status: 'pending',
      submittedOn: '2023-07-12'
    },
    {
      id: 3,
      user: {
        id: 103,
        name: 'Amit Desai',
        age: 30,
        location: 'Bangalore',
        registrationDate: '2023-03-05',
        photo: '/img/user-placeholder.jpg',
        email: '<EMAIL>',
        phone: '+91 **********',
        occupation: 'Business Analyst',
        education: 'MBA in Finance',
        maritalStatus: 'Never Married'
      },
      documents: [
        { id: 5, type: 'ID Proof', url: '/img/document-placeholder.jpg', name: 'PAN Card' },
        { id: 6, type: 'Address Proof', url: '/img/document-placeholder.jpg', name: 'Driving License' }
      ],
      status: 'approved',
      submittedOn: '2023-07-08',
      actionDate: '2023-07-09',
      approvedBy: 'Admin User'
    },
    {
      id: 4,
      user: {
        id: 104,
        name: 'Neha Singh',
        age: 27,
        location: 'Delhi',
        registrationDate: '2023-04-10',
        photo: '/img/user-placeholder.jpg',
        email: '<EMAIL>',
        phone: '+91 **********',
        occupation: 'Marketing Manager',
        education: 'MBA in Marketing',
        maritalStatus: 'Never Married'
      },
      documents: [
        { id: 7, type: 'ID Proof', url: '/img/document-placeholder.jpg', name: 'Aadhar Card' },
        { id: 8, type: 'Address Proof', url: '/img/document-placeholder.jpg', name: 'Passport' }
      ],
      status: 'rejected',
      submittedOn: '2023-07-05',
      actionDate: '2023-07-06',
      rejectionReason: 'Documents unclear',
      rejectedBy: 'Admin User'
    },
    {
      id: 5,
      user: {
        id: 105,
        name: 'Vikram Malhotra',
        age: 32,
        location: 'Chennai',
        registrationDate: '2023-05-15',
        photo: '/img/user-placeholder.jpg',
        email: '<EMAIL>',
        phone: '+91 9876543214',
        occupation: 'Architect',
        education: 'B.Arch',
        maritalStatus: 'Never Married'
      },
      documents: [
        { id: 9, type: 'ID Proof', url: '/img/document-placeholder.jpg', name: 'Aadhar Card' },
        { id: 10, type: 'Address Proof', url: '/img/document-placeholder.jpg', name: 'Electricity Bill' }
      ],
      status: 'pending',
      submittedOn: '2023-07-14'
    }
  ];
}

// Generate mock reported profiles data
export function generateMockReportedProfiles() {
  return [
    {
      id: 1,
      reportedUser: {
        id: 101,
        name: 'Rahul Sharma',
        age: 28,
        gender: 'Male',
        location: 'Mumbai',
        registrationDate: '2023-01-15',
        photo: '/img/user-placeholder.jpg',
        email: '<EMAIL>',
        phone: '+91 9876543210',
        occupation: 'Software Engineer',
        education: 'B.Tech in Computer Science',
        maritalStatus: 'Never Married',
        familyDetails: {
          fatherName: 'Rajesh Sharma',
          fatherOccupation: 'Retired Government Officer',
          motherName: 'Sunita Sharma',
          motherOccupation: 'Homemaker',
          siblings: '1 Brother, 1 Sister',
          familyType: 'Joint Family',
          familyValues: 'Traditional'
        },
        communityDetails: {
          subCaste: 'Kunbi',
          gotra: 'Kashyap',
          rashi: 'Mesh',
          nakshatra: 'Ashwini',
          manglik: false
        }
      },
      reporter: {
        id: 201,
        name: 'Ananya Gupta',
        age: 27,
        gender: 'Female',
        location: 'Delhi',
        email: '<EMAIL>',
        phone: '+91 **********',
        occupation: 'HR Manager',
        education: 'MBA in HR',
        maritalStatus: 'Never Married',
        registrationDate: '2023-01-20'
      },
      reason: 'Fake Profile',
      additionalInfo: 'This person is using someone else\'s photos',
      status: 'pending',
      reportDate: '2023-07-10',
      reportCount: 3,
      evidence: [
        { id: 1, type: 'Screenshot', url: '/img/document-placeholder.jpg', uploadedAt: '2023-07-10' },
        { id: 2, type: 'Chat Evidence', url: '/img/document-placeholder.jpg', uploadedAt: '2023-07-10' }
      ]
    },
    {
      id: 2,
      reportedUser: {
        id: 102,
        name: 'Priya Patel',
        age: 26,
        location: 'Pune',
        registrationDate: '2023-02-20',
        photo: '/img/user-placeholder.jpg',
        email: '<EMAIL>',
        phone: '+91 9876543211',
        occupation: 'Doctor',
        education: 'MBBS',
        maritalStatus: 'Never Married'
      },
      reporter: {
        id: 202,
        name: 'Vikram Singh',
        age: 30,
        location: 'Mumbai',
        email: '<EMAIL>',
        phone: '+91 **********'
      },
      reason: 'Inappropriate Content',
      additionalInfo: 'Profile contains inappropriate photos',
      status: 'pending',
      reportDate: '2023-07-12',
      reportCount: 1
    },
    {
      id: 3,
      reportedUser: {
        id: 103,
        name: 'Amit Desai',
        age: 30,
        location: 'Bangalore',
        registrationDate: '2023-03-05',
        photo: '/img/user-placeholder.jpg',
        email: '<EMAIL>',
        phone: '+91 **********',
        occupation: 'Business Analyst',
        education: 'MBA in Finance',
        maritalStatus: 'Never Married'
      },
      reporter: {
        id: 203,
        name: 'Neha Sharma',
        age: 28,
        location: 'Bangalore',
        email: '<EMAIL>',
        phone: '+91 **********'
      },
      reason: 'Harassment',
      additionalInfo: 'This person is sending inappropriate messages',
      status: 'resolved',
      reportDate: '2023-07-08',
      actionDate: '2023-07-09',
      actionTaken: 'User warned and content removed',
      actionBy: 'Admin User',
      reportCount: 2
    },
    {
      id: 4,
      reportedUser: {
        id: 104,
        name: 'Neha Singh',
        age: 27,
        location: 'Delhi',
        registrationDate: '2023-04-10',
        photo: '/img/user-placeholder.jpg',
        email: '<EMAIL>',
        phone: '+91 **********',
        occupation: 'Marketing Manager',
        education: 'MBA in Marketing',
        maritalStatus: 'Never Married'
      },
      reporter: {
        id: 204,
        name: 'Rajesh Kumar',
        age: 32,
        location: 'Delhi',
        email: '<EMAIL>',
        phone: '+91 9876543223'
      },
      reason: 'Spam',
      additionalInfo: 'This profile is sending spam messages to multiple users',
      status: 'dismissed',
      reportDate: '2023-07-05',
      actionDate: '2023-07-06',
      actionBy: 'Admin User',
      reportCount: 1
    },
    {
      id: 5,
      reportedUser: {
        id: 105,
        name: 'Vikram Malhotra',
        age: 32,
        location: 'Chennai',
        registrationDate: '2023-05-15',
        photo: '/img/user-placeholder.jpg',
        email: '<EMAIL>',
        phone: '+91 9876543214',
        occupation: 'Architect',
        education: 'B.Arch',
        maritalStatus: 'Never Married'
      },
      reporter: {
        id: 205,
        name: 'Pooja Verma',
        age: 29,
        location: 'Chennai',
        email: '<EMAIL>',
        phone: '+91 9876543224'
      },
      reason: 'Other',
      additionalInfo: 'This person is misrepresenting their age and occupation',
      status: 'pending',
      reportDate: '2023-07-14',
      reportCount: 4
    }
  ];
}

// Generate mock features data
export function generateMockFeatures() {
  return [
    {
      id: '1',
      name: 'view-contacts',
      displayName: 'View Contact Details',
      description: 'Allow users to view contact information of other profiles',
      category: 'COMMUNICATION',
      isActive: true,
      accessRules: {
        basic: { isEnabled: false, limitType: 'none', limitValue: 0, upgradeMessage: 'Upgrade to view contact details' },
        verified: { isEnabled: true, limitType: 'daily', limitValue: 5, upgradeMessage: 'Upgrade to premium for unlimited access' },
        premium: { isEnabled: true, limitType: 'none', limitValue: 0, upgradeMessage: '' }
      }
    },
    {
      id: '2',
      name: 'advanced-search',
      displayName: 'Advanced Search',
      description: 'Access to advanced search filters and options',
      category: 'SEARCH',
      isActive: true,
      accessRules: {
        basic: { isEnabled: true, limitType: 'total', limitValue: 3, upgradeMessage: 'Upgrade for more search options' },
        verified: { isEnabled: true, limitType: 'none', limitValue: 0, upgradeMessage: '' },
        premium: { isEnabled: true, limitType: 'none', limitValue: 0, upgradeMessage: '' }
      }
    },
    {
      id: '3',
      name: 'priority-matching',
      displayName: 'Priority Matching',
      description: 'Get priority in matching algorithm and appear higher in search results',
      category: 'MATCHING',
      isActive: true,
      accessRules: {
        basic: { isEnabled: false, limitType: 'none', limitValue: 0, upgradeMessage: 'Upgrade to get priority in matches' },
        verified: { isEnabled: false, limitType: 'none', limitValue: 0, upgradeMessage: 'Upgrade to premium for priority matching' },
        premium: { isEnabled: true, limitType: 'none', limitValue: 0, upgradeMessage: '' }
      }
    },
    {
      id: '4',
      name: 'horoscope-matching',
      displayName: 'Horoscope Matching',
      description: 'Match profiles based on horoscope compatibility',
      category: 'MATCHING',
      isActive: true,
      accessRules: {
        basic: { isEnabled: false, limitType: 'none', limitValue: 0, upgradeMessage: 'Upgrade to use horoscope matching' },
        verified: { isEnabled: true, limitType: 'daily', limitValue: 2, upgradeMessage: 'Upgrade to premium for unlimited horoscope matching' },
        premium: { isEnabled: true, limitType: 'none', limitValue: 0, upgradeMessage: '' }
      }
    }
  ];
}

// Generate mock users data
export function generateMockUsers() {
  return [
    {
      id: 1,
      name: 'Rahul Sharma',
      age: 28,
      gender: 'Male',
      location: 'Mumbai',
      occupation: 'Software Engineer',
      education: 'B.Tech in Computer Science',
      registeredOn: '2023-01-15T10:30:00',
      photo: '/img/user-placeholder.jpg',
      email: '<EMAIL>',
      phone: '+91 9876543210',
      verified: false,
      premium: false,
      status: 'ACTIVE',
      maritalStatus: 'Never Married',
      height: '5\'10"',
      religion: 'Hindu',
      caste: 'Maratha',
      subcaste: 'Deshmukh',
      gotra: 'Kashyap',
      income: '12-15 LPA',
      familyDetails: {
        fatherName: 'Rajesh Sharma',
        fatherOccupation: 'Retired Government Officer',
        motherName: 'Sunita Sharma',
        motherOccupation: 'Homemaker',
        brothers: 1,
        sisters: 1,
        familyType: 'Joint Family',
        familyValues: 'Traditional'
      },
      communityDetails: {
        subCaste: 'Kunbi',
        gotra: 'Kashyap',
        rashi: 'Mesh',
        nakshatra: 'Ashwini',
        manglik: false
      }
    },
    {
      id: 2,
      name: 'Priya Patel',
      age: 26,
      gender: 'Female',
      location: 'Pune',
      occupation: 'Doctor',
      education: 'MBBS',
      registeredOn: '2023-02-20T14:45:00',
      photo: '/img/user-placeholder.jpg',
      email: '<EMAIL>',
      phone: '+91 9876543211',
      verified: true,
      premium: true,
      status: 'ACTIVE',
      maritalStatus: 'Never Married',
      height: '5\'4"',
      religion: 'Hindu',
      caste: 'Maratha',
      subcaste: '96 Kuli',
      gotra: 'Vatsa',
      income: '20-25 LPA'
    },
    {
      id: 3,
      name: 'Amit Desai',
      age: 30,
      gender: 'Male',
      location: 'Bangalore',
      occupation: 'Business Analyst',
      education: 'MBA in Finance',
      registeredOn: '2023-03-05T09:15:00',
      photo: '/img/user-placeholder.jpg',
      email: '<EMAIL>',
      phone: '+91 **********',
      verified: true,
      premium: false,
      status: 'ACTIVE',
      maritalStatus: 'Never Married',
      height: '5\'9"',
      religion: 'Hindu',
      caste: 'Maratha',
      subcaste: 'Deshastha',
      gotra: 'Bharadwaj',
      income: '18-22 LPA'
    },
    {
      id: 4,
      name: 'Neha Singh',
      age: 27,
      gender: 'Female',
      location: 'Delhi',
      occupation: 'Marketing Manager',
      education: 'MBA in Marketing',
      registeredOn: '2023-04-10T16:20:00',
      photo: '/img/user-placeholder.jpg',
      email: '<EMAIL>',
      phone: '+91 **********',
      verified: false,
      premium: false,
      status: 'ACTIVE',
      maritalStatus: 'Never Married',
      height: '5\'6"',
      religion: 'Hindu',
      caste: 'Maratha',
      subcaste: 'Kunbi',
      gotra: 'Kashyap',
      income: '15-18 LPA'
    },
    {
      id: 5,
      name: 'Vikram Malhotra',
      age: 32,
      gender: 'Male',
      location: 'Chennai',
      occupation: 'Architect',
      education: 'B.Arch',
      registeredOn: '2023-05-15T11:10:00',
      photo: '/img/user-placeholder.jpg',
      email: '<EMAIL>',
      phone: '+91 9876543214',
      verified: false,
      premium: true,
      status: 'ACTIVE',
      maritalStatus: 'Never Married',
      height: '6\'0"',
      religion: 'Hindu',
      caste: 'Maratha',
      subcaste: 'Deshastha',
      gotra: 'Bharadwaj',
      income: '25-30 LPA'
    },
    {
      id: 6,
      name: 'Anjali Mehta',
      age: 25,
      gender: 'Female',
      location: 'Mumbai',
      occupation: 'HR Executive',
      education: 'MBA in HR',
      registeredOn: '2023-06-05T13:25:00',
      photo: '/img/user-placeholder.jpg',
      email: '<EMAIL>',
      phone: '+91 9876543215',
      verified: true,
      premium: true,
      status: 'ACTIVE',
      maritalStatus: 'Never Married',
      height: '5\'5"',
      religion: 'Hindu',
      caste: 'Maratha',
      subcaste: '96 Kuli',
      gotra: 'Vatsa',
      income: '12-15 LPA'
    },
    {
      id: 7,
      name: 'Rajesh Kumar',
      age: 31,
      gender: 'Male',
      location: 'Hyderabad',
      occupation: 'Software Developer',
      education: 'M.Tech in Computer Science',
      registeredOn: '2023-06-10T09:40:00',
      photo: '/img/user-placeholder.jpg',
      email: '<EMAIL>',
      phone: '+91 9876543216',
      verified: true,
      premium: false,
      status: 'INACTIVE',
      maritalStatus: 'Never Married',
      height: '5\'8"',
      religion: 'Hindu',
      caste: 'Maratha',
      subcaste: 'Kunbi',
      gotra: 'Kashyap',
      income: '15-20 LPA'
    },
    {
      id: 8,
      name: 'Pooja Verma',
      age: 29,
      gender: 'Female',
      location: 'Pune',
      occupation: 'Teacher',
      education: 'B.Ed',
      registeredOn: '2023-06-15T14:50:00',
      photo: '/img/user-placeholder.jpg',
      email: '<EMAIL>',
      phone: '+91 **********',
      verified: false,
      premium: false,
      status: 'ACTIVE',
      maritalStatus: 'Never Married',
      height: '5\'3"',
      religion: 'Hindu',
      caste: 'Maratha',
      subcaste: 'Deshastha',
      gotra: 'Bharadwaj',
      income: '8-10 LPA'
    },
    {
      id: 9,
      name: 'Sanjay Joshi',
      age: 33,
      gender: 'Male',
      location: 'Nagpur',
      occupation: 'Bank Manager',
      education: 'MBA in Finance',
      registeredOn: '2023-06-20T10:15:00',
      photo: '/img/user-placeholder.jpg',
      email: '<EMAIL>',
      phone: '+91 **********',
      verified: true,
      premium: true,
      status: 'ACTIVE',
      maritalStatus: 'Divorced',
      height: '5\'11"',
      religion: 'Hindu',
      caste: 'Maratha',
      subcaste: '96 Kuli',
      gotra: 'Vatsa',
      income: '20-25 LPA'
    },
    {
      id: 10,
      name: 'Meera Kapoor',
      age: 28,
      gender: 'Female',
      location: 'Delhi',
      occupation: 'Fashion Designer',
      education: 'Bachelor of Design',
      registeredOn: '2023-06-25T15:30:00',
      photo: '/img/user-placeholder.jpg',
      email: '<EMAIL>',
      phone: '+91 **********',
      verified: false,
      premium: false,
      status: 'PENDING_APPROVAL',
      maritalStatus: 'Never Married',
      height: '5\'7"',
      religion: 'Hindu',
      caste: 'Maratha',
      subcaste: 'Kunbi',
      gotra: 'Kashyap',
      income: '15-20 LPA'
    },
    {
      id: 11,
      name: 'Arjun Reddy',
      age: 30,
      gender: 'Male',
      location: 'Hyderabad',
      occupation: 'Doctor',
      education: 'MBBS, MD',
      registeredOn: '2023-07-01T11:45:00',
      photo: '/img/user-placeholder.jpg',
      email: '<EMAIL>',
      phone: '+91 **********',
      verified: true,
      premium: true,
      status: 'ACTIVE',
      maritalStatus: 'Never Married',
      height: '6\'1"',
      religion: 'Hindu',
      caste: 'Maratha',
      subcaste: 'Deshastha',
      gotra: 'Bharadwaj',
      income: '30-35 LPA'
    },
    {
      id: 12,
      name: 'Kavita Sharma',
      age: 26,
      gender: 'Female',
      location: 'Mumbai',
      occupation: 'CA',
      education: 'Chartered Accountant',
      registeredOn: '2023-07-05T13:20:00',
      photo: '/img/user-placeholder.jpg',
      email: '<EMAIL>',
      phone: '+91 **********',
      verified: false,
      premium: false,
      status: 'ACTIVE',
      maritalStatus: 'Never Married',
      height: '5\'4"',
      religion: 'Hindu',
      caste: 'Maratha',
      subcaste: '96 Kuli',
      gotra: 'Vatsa',
      income: '18-22 LPA'
    },
    {
      id: 13,
      name: 'Rahul Khanna',
      age: 34,
      gender: 'Male',
      location: 'Bangalore',
      occupation: 'IT Manager',
      education: 'B.Tech, MBA',
      registeredOn: '2023-07-10T09:30:00',
      photo: '/img/user-placeholder.jpg',
      email: '<EMAIL>',
      phone: '+91 **********',
      verified: true,
      premium: false,
      status: 'SUSPENDED',
      maritalStatus: 'Divorced',
      height: '5\'10"',
      religion: 'Hindu',
      caste: 'Maratha',
      subcaste: 'Kunbi',
      gotra: 'Kashyap',
      income: '25-30 LPA'
    },
    {
      id: 14,
      name: 'Nisha Patel',
      age: 27,
      gender: 'Female',
      location: 'Ahmedabad',
      occupation: 'Software Engineer',
      education: 'B.Tech in IT',
      registeredOn: '2023-07-15T14:15:00',
      photo: '/img/user-placeholder.jpg',
      email: '<EMAIL>',
      phone: '+91 9876543223',
      verified: false,
      premium: true,
      status: 'ACTIVE',
      maritalStatus: 'Never Married',
      height: '5\'6"',
      religion: 'Hindu',
      caste: 'Maratha',
      subcaste: 'Deshastha',
      gotra: 'Bharadwaj',
      income: '15-20 LPA'
    },
    {
      id: 15,
      name: 'Vivek Singh',
      age: 31,
      gender: 'Male',
      location: 'Delhi',
      occupation: 'Government Officer',
      education: 'B.Tech, Civil Services',
      registeredOn: '2023-07-20T10:45:00',
      photo: '/img/user-placeholder.jpg',
      email: '<EMAIL>',
      phone: '+91 9876543224',
      verified: true,
      premium: true,
      status: 'ACTIVE',
      maritalStatus: 'Never Married',
      height: '5\'11"',
      religion: 'Hindu',
      caste: 'Maratha',
      subcaste: '96 Kuli',
      gotra: 'Vatsa',
      income: '20-25 LPA'
    }
  ];
}

// Generate mock premium plans data
export function getMockPlans() {
  return [
    {
      id: 'plan-1',
      name: 'Premium Monthly',
      planType: 'MONTHLY',
      amount: 499,
      currency: 'INR',
      durationDays: 30,
      description: 'Access all premium features for one month.',
      isActive: true,
      isPopular: true,
      features: [
        { id: 'feature-1', name: 'View contact details' },
        { id: 'feature-2', name: 'Advanced search filters' },
        { id: 'feature-3', name: 'Priority in search results' },
        { id: 'feature-4', name: 'Unlimited messages' }
      ]
    },
    {
      id: 'plan-2',
      name: 'Premium Quarterly',
      planType: 'QUARTERLY',
      amount: 1299,
      currency: 'INR',
      durationDays: 90,
      description: 'Access all premium features for three months at a discounted rate.',
      isActive: true,
      isPopular: false,
      features: [
        { id: 'feature-1', name: 'View contact details' },
        { id: 'feature-2', name: 'Advanced search filters' },
        { id: 'feature-3', name: 'Priority in search results' },
        { id: 'feature-4', name: 'Unlimited messages' },
        { id: 'feature-5', name: 'Horoscope matching' }
      ]
    },
    {
      id: 'plan-3',
      name: 'Premium Annual',
      planType: 'ANNUAL',
      amount: 4999,
      currency: 'INR',
      durationDays: 365,
      description: 'Access all premium features for one year at our best value rate.',
      isActive: true,
      isPopular: false,
      features: [
        { id: 'feature-1', name: 'View contact details' },
        { id: 'feature-2', name: 'Advanced search filters' },
        { id: 'feature-3', name: 'Priority in search results' },
        { id: 'feature-4', name: 'Unlimited messages' },
        { id: 'feature-5', name: 'Horoscope matching' },
        { id: 'feature-6', name: 'Profile highlighting' }
      ]
    },
    {
      id: 'plan-4',
      name: 'Basic Monthly',
      planType: 'MONTHLY',
      amount: 299,
      currency: 'INR',
      durationDays: 30,
      description: 'Access essential features for one month.',
      isActive: false,
      isPopular: false,
      features: [
        { id: 'feature-1', name: 'View contact details' },
        { id: 'feature-2', name: 'Advanced search filters' }
      ]
    }
  ];
}
