/**
 * Profile Completion Dashboard
 *
 * A comprehensive dashboard showing profile completion status with gamification elements.
 * Displays progress for each section of the profile and provides quick links to complete them.
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useTheme } from '@mui/material/styles';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  LinearProgress,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Tooltip,
  CircularProgress
} from '@mui/material';
import {
  Person as PersonIcon,
  Photo as PhotoIcon,
  School as EducationIcon,
  Work as WorkIcon,
  LocationOn as LocationIcon,
  Favorite as FavoriteIcon,
  Restaurant as DiningIcon,
  SportsBasketball as HobbyIcon,
  EmojiEvents as EmojiEventsIcon,
  Star as StarIcon,
  Visibility as VisibilityIcon,
  Lock as LockIcon,
  LockOpen as LockOpenIcon,
  People as FamilyIcon,
  Interests as InterestsIcon,
  ArrowForward as ArrowForwardIcon
} from '@mui/icons-material';
import Confetti from 'react-confetti';

// Calculate completion percentage for each category
const calculateCategoryCompletion = (userData, category) => {
  if (!userData) return 0;

  switch (category) {
    case 'basic':
      return userData.fullName && userData.gender && userData.dateOfBirth ? 100 :
             userData.fullName && (userData.gender || userData.dateOfBirth) ? 66 :
             userData.fullName ? 33 : 0;
    case 'photos':
      return userData.profilePhoto ? 100 : 0;
    case 'family':
      if (!userData.familyDetails) return 0;

      // Required fields for family details
      const requiredFields = ['familyType', 'fatherName', 'motherName'];
      const optionalFields = [
        'familyStatus', 'fatherOccupation', 'motherOccupation',
        'siblings', 'motherTongue', 'kul', 'nativePlace'
      ];

      // Count completed fields
      let completedRequired = 0;
      let completedOptional = 0;

      requiredFields.forEach(field => {
        if (userData.familyDetails[field]) completedRequired++;
      });

      optionalFields.forEach(field => {
        if (userData.familyDetails[field]) completedOptional++;
      });

      // Calculate percentage
      const requiredPercentage = (completedRequired / requiredFields.length) * 70;
      const optionalPercentage = (completedOptional / optionalFields.length) * 30;

      return Math.round(requiredPercentage + optionalPercentage);

    case 'education':
      return userData.education && userData.educationField ? 100 :
             userData.education || userData.educationField ? 50 : 0;
    case 'career':
      return userData.occupation && userData.incomeRange ? 100 :
             userData.occupation || userData.incomeRange ? 50 : 0;
    case 'location':
      return userData.city && userData.state ? 100 :
             userData.city || userData.state ? 50 : 0;
    case 'preferences':
      if (!userData.preferences) return 0;

      // Required fields for preferences
      const prefRequiredFields = ['ageMin', 'ageMax', 'heightMin', 'heightMax'];
      const prefOptionalFields = [
        'educationLevel', 'occupations', 'incomeMin', 'preferredCities',
        'preferredStates', 'dietPreference'
      ];

      // Count completed fields
      let prefCompletedRequired = 0;
      let prefCompletedOptional = 0;

      prefRequiredFields.forEach(field => {
        if (userData.preferences[field]) prefCompletedRequired++;
      });

      prefOptionalFields.forEach(field => {
        if (Array.isArray(userData.preferences[field]) ?
            userData.preferences[field].length > 0 :
            userData.preferences[field]) {
          prefCompletedOptional++;
        }
      });

      // Calculate percentage
      const prefRequiredPercentage = (prefCompletedRequired / prefRequiredFields.length) * 70;
      const prefOptionalPercentage = (prefCompletedOptional / prefOptionalFields.length) * 30;

      return Math.round(prefRequiredPercentage + prefOptionalPercentage);

    case 'lifestyle':
      return userData.lifestyle ?
             Object.keys(userData.lifestyle).length >= 3 ? 100 :
             Object.keys(userData.lifestyle).length >= 1 ? 50 : 0 : 0;
    case 'about':
      return userData.aboutMe && userData.aboutMe.length >= 50 ? 100 :
             userData.aboutMe ? 50 : 0;
    default:
      return 0;
  }
};

const ProfileCompletionDashboard = ({ userData, onUpdateSection }) => {
  const theme = useTheme();
  const router = useRouter();
  const [overallCompletion, setOverallCompletion] = useState(0);
  const [earnedBadges, setEarnedBadges] = useState([]);
  const [lockedFeatures, setLockedFeatures] = useState([]);
  const [showConfetti, setShowConfetti] = useState(false);
  const [previousCompletion, setPreviousCompletion] = useState(0);

  // Categories for profile completion
  const categories = [
    {
      id: 'basic',
      name: 'Basic Details',
      icon: <PersonIcon />,
      route: '/profile/edit/basic',
      completionPercentage: calculateCategoryCompletion(userData, 'basic'),
      requiredForLevel: 1,
      benefitText: 'Required to appear in search results',
      timeToComplete: '2 min'
    },
    {
      id: 'photos',
      name: 'Profile Photos',
      icon: <PhotoIcon />,
      route: '/profile/edit/photos',
      completionPercentage: calculateCategoryCompletion(userData, 'photos'),
      requiredForLevel: 1,
      benefitText: 'Profiles with photos get 10x more responses',
      timeToComplete: '1 min'
    },
    {
      id: 'family',
      name: 'Family Details',
      icon: <FamilyIcon />,
      route: '/profile/edit/family',
      completionPercentage: calculateCategoryCompletion(userData, 'family'),
      requiredForLevel: 2,
      benefitText: 'Improves match quality by 40%',
      timeToComplete: '3 min'
    },
    {
      id: 'education',
      name: 'Education & Career',
      icon: <EducationIcon />,
      route: '/profile/edit/education',
      completionPercentage: calculateCategoryCompletion(userData, 'education'),
      requiredForLevel: 1,
      benefitText: 'Helps match with compatible professionals',
      timeToComplete: '2 min'
    },
    {
      id: 'location',
      name: 'Location Details',
      icon: <LocationIcon />,
      route: '/profile/edit/location',
      completionPercentage: calculateCategoryCompletion(userData, 'location'),
      requiredForLevel: 1,
      benefitText: 'Find matches in your preferred locations',
      timeToComplete: '1 min'
    },
    {
      id: 'preferences',
      name: 'Partner Preferences',
      icon: <FavoriteIcon />,
      route: '/website/pages/profile/edit/preferences',
      completionPercentage: calculateCategoryCompletion(userData, 'preferences'),
      requiredForLevel: 2,
      benefitText: 'Receive more relevant match suggestions',
      timeToComplete: '4 min'
    },
    {
      id: 'lifestyle',
      name: 'Lifestyle & Habits',
      icon: <DiningIcon />,
      route: '/website/pages/profile/edit/lifestyle',
      completionPercentage: calculateCategoryCompletion(userData, 'lifestyle'),
      requiredForLevel: 3,
      benefitText: 'Find partners with compatible lifestyles',
      timeToComplete: '2 min'
    },
    {
      id: 'about',
      name: 'About Me',
      icon: <InterestsIcon />,
      route: '/profile/edit/about',
      completionPercentage: calculateCategoryCompletion(userData, 'about'),
      requiredForLevel: 1,
      benefitText: 'Express yourself to attract better matches',
      timeToComplete: '3 min'
    }
  ];

  // Calculate overall completion percentage
  useEffect(() => {
    // Store previous completion for confetti effect
    setPreviousCompletion(overallCompletion);

    // Calculate weighted completion percentage
    const totalWeight = categories.reduce((sum, category) => sum + (category.requiredForLevel === 1 ? 2 : 1), 0);
    let weightedSum = 0;

    categories.forEach(category => {
      const weight = category.requiredForLevel === 1 ? 2 : 1;
      weightedSum += (category.completionPercentage * weight);
    });

    const completedPercentage = Math.round(weightedSum / totalWeight);
    setOverallCompletion(completedPercentage);

    // Determine earned badges based on completion
    const badges = [];
    if (completedPercentage >= 25) badges.push('bronze');
    if (completedPercentage >= 50) badges.push('silver');
    if (completedPercentage >= 75) badges.push('gold');
    if (completedPercentage === 100) badges.push('platinum');
    setEarnedBadges(badges);

    // Determine locked features
    const locked = [];
    if (completedPercentage < 40) locked.push('advanced_search');
    if (completedPercentage < 60) locked.push('contact_info');
    if (completedPercentage < 80) locked.push('spotlight');
    setLockedFeatures(locked);

    // Show confetti if completion percentage increased significantly
    if (completedPercentage >= 25 && previousCompletion < 25 ||
        completedPercentage >= 50 && previousCompletion < 50 ||
        completedPercentage >= 75 && previousCompletion < 75 ||
        completedPercentage === 100 && previousCompletion < 100) {
      setShowConfetti(true);
      setTimeout(() => setShowConfetti(false), 5000);
    }
  }, [categories, previousCompletion]);

  // Get profile level based on completion
  const getProfileLevel = () => {
    if (overallCompletion >= 80) return 'Premium';
    if (overallCompletion >= 60) return 'Advanced';
    if (overallCompletion >= 40) return 'Standard';
    return 'Basic';
  };

  // Get visibility percentage based on completion
  const getVisibilityPercentage = () => {
    return Math.min(100, overallCompletion + 20);
  };

  // Navigate to edit section
  const handleEditSection = (route) => {
    if (onUpdateSection) {
      onUpdateSection(route);
    } else {
      router.push(route);
    }
  };

  return (
    <>
      {showConfetti && (
        <Confetti
          width={window.innerWidth}
          height={window.innerHeight}
          recycle={false}
          numberOfPieces={200}
          gravity={0.1}
        />
      )}

      <Box
        sx={{
          p: 3,
          borderRadius: 2,
          mb: 3,
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
          color: 'white',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={8}>
            <Typography variant="h5" fontWeight="bold" gutterBottom>
              Complete Your Profile
            </Typography>
            <Typography variant="body1" sx={{ mb: 2 }}>
              Your profile is {overallCompletion}% complete. Profiles with higher completion rates receive up to 10x more interest!
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Box sx={{ flexGrow: 1, mr: 2 }}>
                <LinearProgress
                  variant="determinate"
                  value={overallCompletion}
                  sx={{
                    height: 10,
                    borderRadius: 5,
                    backgroundColor: 'rgba(255,255,255,0.3)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'white'
                    }
                  }}
                />
              </Box>
              <Typography variant="h6" fontWeight="bold">
                {overallCompletion}%
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={4} sx={{ textAlign: 'center' }}>
            <Paper
              elevation={4}
              sx={{
                p: 2,
                borderRadius: 2,
                display: 'inline-block',
                background: 'rgba(255,255,255,0.9)',
                color: theme.palette.text.primary
              }}
            >
              <EmojiEventsIcon sx={{ fontSize: 40, color: theme.palette.warning.main }} />
              <Typography variant="h6" fontWeight="bold">
                {getProfileLevel()} Profile
              </Typography>
              <Typography variant="body2">
                Profile Visibility: {getVisibilityPercentage()}%
              </Typography>
            </Paper>
          </Grid>
        </Grid>
      </Box>

      <Card elevation={1} sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ mt: 1 }}>
            Complete these sections to improve your profile
          </Typography>

          <Grid container spacing={3}>
            {categories.map((category) => (
              <Grid item xs={12} sm={6} md={4} key={category.id}>
                <Paper
                  elevation={2}
                  sx={{
                    p: 2,
                    borderRadius: 2,
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    position: 'relative',
                    border: '1px solid',
                    borderColor: category.completionPercentage === 100 ? 'success.light' : 'divider'
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Box
                      sx={{
                        mr: 1.5,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: 40,
                        height: 40,
                        borderRadius: '50%',
                        backgroundColor: category.completionPercentage === 100 ? 'success.light' : 'action.selected'
                      }}
                    >
                      {React.cloneElement(category.icon, {
                        sx: { color: category.completionPercentage === 100 ? 'white' : 'text.secondary' }
                      })}
                    </Box>
                    <Typography variant="subtitle1" fontWeight="medium">
                      {category.name}
                    </Typography>
                    <Box sx={{ ml: 'auto' }}>
                      <CircularProgress
                        variant="determinate"
                        value={category.completionPercentage}
                        size={32}
                        thickness={4}
                        sx={{
                          color: category.completionPercentage === 100 ? 'success.main' : 'primary.main',
                          '& .MuiCircularProgress-circle': {
                            strokeLinecap: 'round',
                          },
                        }}
                      />
                    </Box>
                  </Box>

                  <Box sx={{ mt: 1, mb: 2 }}>
                    <LinearProgress
                      variant="determinate"
                      value={category.completionPercentage}
                      sx={{
                        height: 6,
                        borderRadius: 3,
                        backgroundColor: 'rgba(0,0,0,0.05)',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: category.completionPercentage === 100 ? 'success.main' : 'primary.main'
                        }
                      }}
                    />
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 0.5 }}>
                      <Typography variant="caption" color="text.secondary">
                        {category.completionPercentage}% Complete
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {category.timeToComplete}
                      </Typography>
                    </Box>
                  </Box>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2, flexGrow: 1 }}>
                    {category.benefitText}
                  </Typography>

                  <Button
                    variant={category.completionPercentage === 100 ? "outlined" : "contained"}
                    color={category.completionPercentage === 100 ? "success" : "primary"}
                    size="small"
                    onClick={() => handleEditSection(category.route)}
                    endIcon={<ArrowForwardIcon />}
                  >
                    {category.completionPercentage === 100 ? "Update" : "Complete"}
                  </Button>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card elevation={2} sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <EmojiEventsIcon sx={{ mr: 1 }} /> Profile Badges
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Grid container spacing={2} justifyContent="center">
                <Grid item xs={6} sm={3}>
                  <Tooltip title={earnedBadges.includes('bronze') ? 'Earned: 25% Complete' : 'Complete 25% of your profile'}>
                    <Paper
                      elevation={earnedBadges.includes('bronze') ? 3 : 0}
                      sx={{
                        p: 2,
                        textAlign: 'center',
                        bgcolor: earnedBadges.includes('bronze') ? 'rgba(176, 141, 87, 0.1)' : 'action.disabledBackground',
                        border: '1px solid',
                        borderColor: earnedBadges.includes('bronze') ? 'warning.light' : 'divider',
                        borderRadius: 2,
                        opacity: earnedBadges.includes('bronze') ? 1 : 0.6
                      }}
                    >
                      <StarIcon sx={{ fontSize: 40, color: earnedBadges.includes('bronze') ? '#CD7F32' : 'text.disabled' }} />
                      <Typography variant="subtitle2">Bronze</Typography>
                    </Paper>
                  </Tooltip>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Tooltip title={earnedBadges.includes('silver') ? 'Earned: 50% Complete' : 'Complete 50% of your profile'}>
                    <Paper
                      elevation={earnedBadges.includes('silver') ? 3 : 0}
                      sx={{
                        p: 2,
                        textAlign: 'center',
                        bgcolor: earnedBadges.includes('silver') ? 'rgba(192, 192, 192, 0.1)' : 'action.disabledBackground',
                        border: '1px solid',
                        borderColor: earnedBadges.includes('silver') ? 'grey.400' : 'divider',
                        borderRadius: 2,
                        opacity: earnedBadges.includes('silver') ? 1 : 0.6
                      }}
                    >
                      <StarIcon sx={{ fontSize: 40, color: earnedBadges.includes('silver') ? '#C0C0C0' : 'text.disabled' }} />
                      <Typography variant="subtitle2">Silver</Typography>
                    </Paper>
                  </Tooltip>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Tooltip title={earnedBadges.includes('gold') ? 'Earned: 75% Complete' : 'Complete 75% of your profile'}>
                    <Paper
                      elevation={earnedBadges.includes('gold') ? 3 : 0}
                      sx={{
                        p: 2,
                        textAlign: 'center',
                        bgcolor: earnedBadges.includes('gold') ? 'rgba(255, 215, 0, 0.1)' : 'action.disabledBackground',
                        border: '1px solid',
                        borderColor: earnedBadges.includes('gold') ? 'warning.main' : 'divider',
                        borderRadius: 2,
                        opacity: earnedBadges.includes('gold') ? 1 : 0.6
                      }}
                    >
                      <StarIcon sx={{ fontSize: 40, color: earnedBadges.includes('gold') ? '#FFD700' : 'text.disabled' }} />
                      <Typography variant="subtitle2">Gold</Typography>
                    </Paper>
                  </Tooltip>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Tooltip title={earnedBadges.includes('platinum') ? 'Earned: 100% Complete' : 'Complete 100% of your profile'}>
                    <Paper
                      elevation={earnedBadges.includes('platinum') ? 3 : 0}
                      sx={{
                        p: 2,
                        textAlign: 'center',
                        bgcolor: earnedBadges.includes('platinum') ? 'rgba(229, 228, 226, 0.2)' : 'action.disabledBackground',
                        border: '1px solid',
                        borderColor: earnedBadges.includes('platinum') ? 'grey.300' : 'divider',
                        borderRadius: 2,
                        opacity: earnedBadges.includes('platinum') ? 1 : 0.6
                      }}
                    >
                      <StarIcon sx={{ fontSize: 40, color: earnedBadges.includes('platinum') ? '#E5E4E2' : 'text.disabled' }} />
                      <Typography variant="subtitle2">Platinum</Typography>
                    </Paper>
                  </Tooltip>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card elevation={2} sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <VisibilityIcon sx={{ mr: 1 }} /> Profile Visibility Benefits
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <List dense>
                <ListItem>
                  <ListItemIcon>
                    {overallCompletion >= 40 ? <LockOpenIcon color="success" /> : <LockIcon color="action" />}
                  </ListItemIcon>
                  <ListItemText
                    primary="Advanced Search Visibility"
                    secondary="Your profile appears in advanced search results"
                  />
                  <Chip
                    size="small"
                    label={overallCompletion >= 40 ? "Unlocked" : "40% Required"}
                    color={overallCompletion >= 40 ? "success" : "default"}
                    variant={overallCompletion >= 40 ? "filled" : "outlined"}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    {overallCompletion >= 60 ? <LockOpenIcon color="success" /> : <LockIcon color="action" />}
                  </ListItemIcon>
                  <ListItemText
                    primary="Contact Information Sharing"
                    secondary="Share contact details with interested matches"
                  />
                  <Chip
                    size="small"
                    label={overallCompletion >= 60 ? "Unlocked" : "60% Required"}
                    color={overallCompletion >= 60 ? "success" : "default"}
                    variant={overallCompletion >= 60 ? "filled" : "outlined"}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    {overallCompletion >= 80 ? <LockOpenIcon color="success" /> : <LockIcon color="action" />}
                  </ListItemIcon>
                  <ListItemText
                    primary="Spotlight Feature Eligibility"
                    secondary="Highlight your profile to get more attention"
                  />
                  <Chip
                    size="small"
                    label={overallCompletion >= 80 ? "Unlocked" : "80% Required"}
                    color={overallCompletion >= 80 ? "success" : "default"}
                    variant={overallCompletion >= 80 ? "filled" : "outlined"}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    {overallCompletion === 100 ? <LockOpenIcon color="success" /> : <LockIcon color="action" />}
                  </ListItemIcon>
                  <ListItemText
                    primary="Premium Match Algorithm"
                    secondary="Get highest quality matches based on compatibility"
                  />
                  <Chip
                    size="small"
                    label={overallCompletion === 100 ? "Unlocked" : "100% Required"}
                    color={overallCompletion === 100 ? "success" : "default"}
                    variant={overallCompletion === 100 ? "filled" : "outlined"}
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </>
  );
};

export default ProfileCompletionDashboard;
