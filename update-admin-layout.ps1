# PowerShell script to update admin pages to use EnhancedAdminLayout

# Get all admin pages
$adminPages = Get-ChildItem -Path "vaivahik-nextjs\src\pages\admin" -Filter "*.js" -Recurse

# Counter for tracking changes
$updatedCount = 0
$alreadyEnhancedCount = 0
$skippedCount = 0

foreach ($page in $adminPages) {
    $content = Get-Content -Path $page.FullName -Raw
    
    # Skip files that already use EnhancedAdminLayout
    if ($content -match "import EnhancedAdminLayout from") {
        Write-Host "Already using EnhancedAdminLayout: $($page.FullName)" -ForegroundColor Green
        $alreadyEnhancedCount++
        continue
    }
    
    # Skip files that don't use AdminLayout
    if (-not ($content -match "import AdminLayout from")) {
        Write-Host "Skipping (no AdminLayout import): $($page.FullName)" -ForegroundColor Yellow
        $skippedCount++
        continue
    }
    
    # Replace AdminLayout import with EnhancedAdminLayout
    $updatedContent = $content -replace "import AdminLayout from '@/components/admin/AdminLayout';", "import dynamic from 'next/dynamic';`n`n// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues`nconst EnhancedAdminLayout = dynamic(`n  () => import('@/components/admin/EnhancedAdminLayout'),`n  { ssr: false }`n);"
    
    # Replace AdminLayout usage with EnhancedAdminLayout
    $updatedContent = $updatedContent -replace "<AdminLayout([^>]*)>", "<EnhancedAdminLayout$1>"
    $updatedContent = $updatedContent -replace "</AdminLayout>", "</EnhancedAdminLayout>"
    
    # Save the updated content
    Set-Content -Path $page.FullName -Value $updatedContent
    
    Write-Host "Updated: $($page.FullName)" -ForegroundColor Cyan
    $updatedCount++
}

Write-Host "`nSummary:" -ForegroundColor White
Write-Host "- Updated: $updatedCount files" -ForegroundColor Cyan
Write-Host "- Already using EnhancedAdminLayout: $alreadyEnhancedCount files" -ForegroundColor Green
Write-Host "- Skipped: $skippedCount files" -ForegroundColor Yellow
