{"success": true, "verifications": [{"id": 1, "user": {"id": 101, "name": "<PERSON><PERSON>", "age": 28, "location": "Mumbai", "registrationDate": "2023-01-15", "photo": null, "email": "<EMAIL>", "phone": "+91 9876543210", "occupation": "Software Engineer", "education": "B.Tech in Computer Science", "maritalStatus": "Never Married", "height": "5'10\"", "weight": "75 kg", "complexion": "Fair", "religion": "Hindu", "caste": "<PERSON><PERSON>", "subcaste": "<PERSON><PERSON><PERSON><PERSON>", "gotra": "<PERSON><PERSON><PERSON><PERSON>", "diet": "Vegetarian", "smoking": "No", "drinking": "Occasionally", "income": "12-15 LPA", "family": {"father": "<PERSON><PERSON>", "fatherOccupation": "Retired Government Officer", "mother": "<PERSON><PERSON>", "motherOccupation": "Homemaker", "brothers": 1, "sisters": 1, "familyType": "Joint Family", "familyValues": "Traditional", "familyStatus": "Middle Class", "familyLocation": "Mumbai"}, "horoscope": {"manglik": "No", "nakshatra": "<PERSON><PERSON><PERSON>", "rashi": "<PERSON><PERSON>"}}, "documents": [{"id": 1, "type": "ID Proof", "url": "/img/document-placeholder.jpg", "name": "<PERSON><PERSON><PERSON>"}, {"id": 2, "type": "Address Proof", "url": "/img/document-placeholder.jpg", "name": "Passport"}], "status": "pending", "submittedOn": "2023-07-10"}, {"id": 2, "user": {"id": 102, "name": "<PERSON><PERSON>", "age": 26, "location": "Pune", "registrationDate": "2023-02-20", "photo": null, "email": "<EMAIL>", "phone": "+91 9876543211", "occupation": "Doctor", "education": "MBBS", "maritalStatus": "Never Married"}, "documents": [{"id": 3, "type": "ID Proof", "url": "/img/document-placeholder.jpg", "name": "<PERSON><PERSON><PERSON>"}, {"id": 4, "type": "Address Proof", "url": "/img/document-placeholder.jpg", "name": "Voter ID"}], "status": "pending", "submittedOn": "2023-07-12"}, {"id": 3, "user": {"id": 103, "name": "<PERSON><PERSON>", "age": 30, "location": "Bangalore", "registrationDate": "2023-03-05", "photo": null, "email": "<EMAIL>", "phone": "+91 **********", "occupation": "Business Analyst", "education": "MBA in Finance", "maritalStatus": "Never Married"}, "documents": [{"id": 5, "type": "ID Proof", "url": "/img/document-placeholder.jpg", "name": "PAN Card"}, {"id": 6, "type": "Address Proof", "url": "/img/document-placeholder.jpg", "name": "Driving License"}], "status": "approved", "submittedOn": "2023-07-08", "actionDate": "2023-07-09", "approvedBy": "Admin User"}, {"id": 4, "user": {"id": 104, "name": "<PERSON><PERSON><PERSON>", "age": 27, "location": "Delhi", "registrationDate": "2023-04-10", "photo": null, "email": "<EMAIL>", "phone": "+91 **********", "occupation": "Marketing Manager", "education": "MBA in Marketing", "maritalStatus": "Never Married"}, "documents": [{"id": 7, "type": "ID Proof", "url": "/img/document-placeholder.jpg", "name": "<PERSON><PERSON><PERSON>"}, {"id": 8, "type": "Address Proof", "url": "/img/document-placeholder.jpg", "name": "Passport"}], "status": "rejected", "submittedOn": "2023-07-05", "actionDate": "2023-07-06", "rejectionReason": "Documents unclear", "rejectedBy": "Admin User"}, {"id": 5, "user": {"id": 105, "name": "<PERSON><PERSON><PERSON>", "age": 32, "location": "Chennai", "registrationDate": "2023-05-15", "photo": null, "email": "<EMAIL>", "phone": "+91 **********", "occupation": "Architect", "education": "B.Arch", "maritalStatus": "Never Married"}, "documents": [{"id": 9, "type": "ID Proof", "url": "/img/document-placeholder.jpg", "name": "<PERSON><PERSON><PERSON>"}, {"id": 10, "type": "Address Proof", "url": "/img/document-placeholder.jpg", "name": "Electricity Bill"}], "status": "pending", "submittedOn": "2023-07-14"}], "pagination": {"totalVerifications": 5, "page": 1, "limit": 10, "totalPages": 1}, "message": "Verification queue retrieved successfully"}