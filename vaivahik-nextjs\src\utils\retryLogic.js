/**
 * Retry Logic Utilities
 * 
 * This module provides utilities for implementing retry logic for API calls
 * and other asynchronous operations that might fail temporarily.
 */

import { isNetworkError } from './errorHandling';

/**
 * Default retry configuration
 */
const DEFAULT_RETRY_CONFIG = {
  maxRetries: 3,
  initialDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffFactor: 2, // Exponential backoff
  retryCondition: isNetworkError // Only retry network errors by default
};

/**
 * Calculate delay for the next retry attempt using exponential backoff
 * @param {number} attempt - Current attempt number (0-based)
 * @param {Object} config - Retry configuration
 * @returns {number} Delay in milliseconds
 */
export const calculateBackoffDelay = (attempt, config) => {
  const { initialDelay, maxDelay, backoffFactor } = { ...DEFAULT_RETRY_CONFIG, ...config };
  
  // Calculate exponential backoff: initialDelay * (backoffFactor ^ attempt)
  const delay = initialDelay * Math.pow(backoffFactor, attempt);
  
  // Add some randomness to prevent all clients retrying simultaneously
  const jitter = Math.random() * 0.3 + 0.85; // Random factor between 0.85 and 1.15
  
  // Return the delay, but not more than maxDelay
  return Math.min(delay * jitter, maxDelay);
};

/**
 * Sleep for a specified duration
 * @param {number} ms - Milliseconds to sleep
 * @returns {Promise} Promise that resolves after the specified duration
 */
export const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Execute an async function with retry logic
 * @param {Function} fn - Async function to execute
 * @param {Object} config - Retry configuration
 * @returns {Promise} Promise that resolves with the function result or rejects after all retries
 */
export const withRetry = async (fn, config = {}) => {
  const retryConfig = { ...DEFAULT_RETRY_CONFIG, ...config };
  const { maxRetries, retryCondition } = retryConfig;
  
  let lastError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      // Execute the function
      return await fn();
    } catch (error) {
      lastError = error;
      
      // Check if we should retry
      const shouldRetry = attempt < maxRetries && 
                          (typeof retryCondition === 'function' ? retryCondition(error) : true);
      
      if (!shouldRetry) {
        break;
      }
      
      // Calculate delay for next retry
      const delay = calculateBackoffDelay(attempt, retryConfig);
      
      // Log retry attempt
      console.log(`Retry attempt ${attempt + 1}/${maxRetries} after ${delay}ms`);
      
      // Wait before next retry
      await sleep(delay);
    }
  }
  
  // If we get here, all retries failed
  throw lastError;
};

/**
 * Create a wrapped version of an async function with retry logic
 * @param {Function} fn - Async function to wrap
 * @param {Object} config - Retry configuration
 * @returns {Function} Wrapped function with retry logic
 */
export const createRetryWrapper = (fn, config = {}) => {
  return async (...args) => {
    return withRetry(() => fn(...args), config);
  };
};

export default {
  withRetry,
  createRetryWrapper,
  calculateBackoffDelay,
  sleep,
  DEFAULT_RETRY_CONFIG
};
