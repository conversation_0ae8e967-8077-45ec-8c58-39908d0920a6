// Commented out next-auth imports as they're not available
// import { getServerSession } from 'next-auth/next';
// import { authOptions } from '../../auth/[...nextauth]';
import { PrismaClient } from '@prisma/client';
import Razorpay from 'razorpay';

// Mock session for development
const mockSession = {
  user: {
    id: 'user-123',
    name: 'Test User',
    email: '<EMAIL>'
  }
};

const prisma = new PrismaClient();

// Initialize Razorpay with your key_id and key_secret
const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID || 'rzp_test_mock_key',
  key_secret: process.env.RAZORPAY_KEY_SECRET || 'mock_secret'
});

export default async function handler(req, res) {
  // Use mock session for development
  const session = mockSession;

  // In production, we would use next-auth
  // const session = await getServerSession(req, res, authOptions);

  if (!session || !session.user) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { productType, productId, quantity = 1 } = req.body;
    const userId = session.user.id;

    if (!productType || !productId) {
      return res.status(400).json({ success: false, message: 'Product type and ID are required' });
    }

    // Get user data for prefilling payment form
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true
      }
    });

    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    let orderData;

    // Handle different product types
    if (productType === 'BIODATA') {
      orderData = await createBiodataOrder(userId, productId, user);
    } else if (productType === 'SPOTLIGHT') {
      orderData = await createSpotlightOrder(userId, productId, quantity, user);
    } else {
      return res.status(400).json({ success: false, message: 'Invalid product type' });
    }

    return res.status(200).json({
      success: true,
      order: orderData
    });
  } catch (error) {
    console.error('Error creating payment order:', error);
    return res.status(500).json({ success: false, message: error.message || 'Failed to create payment order' });
  }
}

/**
 * Create a payment order for biodata template purchase
 * @param {string} userId - The user ID
 * @param {string} templateId - The biodata template ID
 * @param {Object} user - The user data
 * @returns {Promise<Object>} - The payment order details
 */
async function createBiodataOrder(userId, templateId, user) {
  // Get template
  const template = await prisma.biodataTemplate.findUnique({
    where: { id: templateId }
  });

  if (!template) {
    throw new Error('Template not found');
  }

  if (!template.isActive) {
    throw new Error('This template is currently unavailable');
  }

  // Check if user has already purchased this template
  const existingPurchase = await prisma.userBiodata.findFirst({
    where: {
      userId,
      templateId
    }
  });

  if (existingPurchase) {
    throw new Error('You have already purchased this template');
  }

  // Calculate amount (in paise)
  const amount = Math.round((template.discountedPrice || template.price) * 100);

  // Create Razorpay order
  const order = await razorpay.orders.create({
    amount,
    currency: 'INR',
    receipt: `biodata_${userId}_${templateId}_${Date.now()}`,
    notes: {
      userId,
      templateId,
      productType: 'BIODATA',
      templateName: template.name
    }
  });

  // Save order in database (if Payment model exists)
  try {
    await prisma.payment.create({
      data: {
        userId,
        orderId: order.id,
        amount: amount / 100, // Store in rupees
        currency: 'INR',
        status: 'CREATED',
        productType: 'BIODATA',
        productId: templateId,
        metadata: JSON.stringify({
          templateId,
          templateName: template.name,
          price: template.price,
          discountedPrice: template.discountedPrice
        })
      }
    });
  } catch (error) {
    console.error('Warning: Could not save payment to database:', error);
    // Continue anyway since the Razorpay order was created
  }

  return {
    orderId: order.id,
    amount: amount / 100,
    currency: 'INR',
    keyId: process.env.RAZORPAY_KEY_ID,
    prefillData: {
      name: user.name,
      email: user.email,
      contact: user.phone
    },
    notes: {
      userId,
      templateId,
      productType: 'BIODATA',
      templateName: template.name
    },
    productDetails: {
      name: template.name,
      description: template.description,
      price: template.price,
      discountedPrice: template.discountedPrice
    }
  };
}

/**
 * Create a payment order for spotlight purchase
 * @param {string} userId - The user ID
 * @param {string} spotlightId - The spotlight feature ID
 * @param {number} quantity - The quantity to purchase
 * @param {Object} user - The user data
 * @returns {Promise<Object>} - The payment order details
 */
async function createSpotlightOrder(userId, spotlightId, quantity, user) {
  // Get spotlight feature
  const spotlight = await prisma.spotlightFeature.findUnique({
    where: { id: spotlightId }
  });

  if (!spotlight) {
    throw new Error('Spotlight feature not found');
  }

  if (!spotlight.isActive) {
    throw new Error('This spotlight feature is currently unavailable');
  }

  // Calculate amount (in paise)
  const unitPrice = spotlight.discountedPrice || spotlight.price;
  const totalPrice = unitPrice * quantity;
  const amount = Math.round(totalPrice * 100);

  // Calculate total spotlight count
  const defaultCount = spotlight.defaultCount || 1;
  const totalCount = quantity * defaultCount;

  // Create Razorpay order
  const order = await razorpay.orders.create({
    amount,
    currency: 'INR',
    receipt: `spotlight_${userId}_${spotlightId}_${Date.now()}`,
    notes: {
      userId,
      spotlightId,
      productType: 'SPOTLIGHT',
      spotlightName: spotlight.name,
      quantity,
      totalCount
    }
  });

  // Save order in database (if Payment model exists)
  try {
    await prisma.payment.create({
      data: {
        userId,
        orderId: order.id,
        amount: totalPrice, // Store in rupees
        currency: 'INR',
        status: 'CREATED',
        productType: 'SPOTLIGHT',
        productId: spotlightId,
        metadata: JSON.stringify({
          spotlightId,
          spotlightName: spotlight.name,
          price: spotlight.price,
          discountedPrice: spotlight.discountedPrice,
          quantity,
          defaultCount,
          totalCount
        })
      }
    });
  } catch (error) {
    console.error('Warning: Could not save payment to database:', error);
    // Continue anyway since the Razorpay order was created
  }

  return {
    orderId: order.id,
    amount: totalPrice,
    currency: 'INR',
    keyId: process.env.RAZORPAY_KEY_ID,
    prefillData: {
      name: user.name,
      email: user.email,
      contact: user.phone
    },
    notes: {
      userId,
      spotlightId,
      productType: 'SPOTLIGHT',
      spotlightName: spotlight.name,
      quantity,
      totalCount
    },
    productDetails: {
      name: spotlight.name,
      description: spotlight.description,
      price: spotlight.price,
      discountedPrice: spotlight.discountedPrice,
      quantity,
      defaultCount,
      totalCount
    }
  };
}
