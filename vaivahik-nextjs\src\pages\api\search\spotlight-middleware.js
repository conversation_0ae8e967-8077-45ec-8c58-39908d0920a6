import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Middleware function to enhance search results with spotlighted profiles
 * This should be called after the main search query but before returning results to the client
 * 
 * @param {Array} searchResults - The original search results
 * @param {Object} options - Options for spotlight handling
 * @returns {Array} - Enhanced search results with spotlighted profiles
 */
export async function enhanceWithSpotlightProfiles(searchResults, options = {}) {
  try {
    const {
      maxSpotlightCount = 3, // Maximum number of spotlighted profiles to include
      spotlightPosition = 'top', // Where to place spotlighted profiles: 'top', 'distributed', or 'section'
      excludeUserIds = [] // User IDs to exclude (e.g., the current user)
    } = options;
    
    // Get active spotlighted profiles
    const now = new Date();
    const spotlightedUsers = await prisma.userSpotlight.findMany({
      where: {
        isActive: true,
        endTime: {
          gt: now
        },
        userId: {
          notIn: excludeUserIds
        }
      },
      include: {
        user: {
          include: {
            profile: true,
            photos: {
              where: {
                isProfilePic: true
              },
              take: 1
            }
          }
        }
      },
      orderBy: {
        startTime: 'desc'
      },
      take: maxSpotlightCount
    });
    
    // If no spotlighted profiles, return original results
    if (spotlightedUsers.length === 0) {
      return searchResults;
    }
    
    // Extract user data from spotlighted users
    const spotlightedProfiles = spotlightedUsers.map(spotlight => {
      const user = spotlight.user;
      
      // Format the user data to match search results format
      // This will depend on your search results structure
      return {
        ...user,
        isSpotlighted: true,
        spotlightEndTime: spotlight.endTime,
        // Add any other fields needed to match your search results structure
      };
    });
    
    // Filter out any spotlighted profiles that are already in search results
    const spotlightedUserIds = spotlightedUsers.map(s => s.userId);
    const filteredResults = searchResults.filter(result => !spotlightedUserIds.includes(result.id));
    
    // Combine results based on the specified position
    let enhancedResults = [];
    
    switch (spotlightPosition) {
      case 'top':
        // Place all spotlighted profiles at the top
        enhancedResults = [...spotlightedProfiles, ...filteredResults];
        break;
        
      case 'distributed':
        // Distribute spotlighted profiles throughout the results
        // For example, place one every 5 regular results
        enhancedResults = [...filteredResults];
        const spacing = Math.max(1, Math.floor(filteredResults.length / (spotlightedProfiles.length + 1)));
        
        spotlightedProfiles.forEach((profile, index) => {
          const position = Math.min((index + 1) * spacing, enhancedResults.length);
          enhancedResults.splice(position, 0, profile);
        });
        break;
        
      case 'section':
        // Create a separate section for spotlighted profiles
        // In this case, we'll return an object instead of an array
        return {
          spotlightedProfiles,
          regularProfiles: filteredResults
        };
        
      default:
        enhancedResults = [...spotlightedProfiles, ...filteredResults];
    }
    
    return enhancedResults;
  } catch (error) {
    console.error('Error enhancing search results with spotlighted profiles:', error);
    // In case of error, return the original results
    return searchResults;
  }
}
