# Vaivahik API

This is the API for the Vaivahik Matrimony Platform. It provides endpoints for user management, profile management, matching, messaging, and more.

## Table of Contents

- [Features](#features)
- [Getting Started](#getting-started)
- [API Standards](#api-standards)
- [API Documentation](#api-documentation)
- [Testing](#testing)
- [Performance Monitoring](#performance-monitoring)
- [Caching](#caching)
- [<PERSON>rro<PERSON>ling](#error-handling)
- [Validation](#validation)
- [Authentication](#authentication)
- [Mock Data](#mock-data)

## Features

- RESTful API with standardized endpoints
- Comprehensive documentation with OpenAPI/Swagger
- Robust error handling and validation
- Authentication with JWT
- Caching with Redis
- Performance monitoring
- Comprehensive test suite
- Mock data support for development

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- PostgreSQL
- Redis

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   cd vaivahik-express
   npm install
   ```
3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```
4. Run database migrations:
   ```bash
   npx prisma migrate dev
   ```
5. Start the server:
   ```bash
   npm run dev
   ```

## API Standards

The API follows a standardized structure:

- All endpoints are prefixed with `/api/v1`
- Consistent response format for success and error responses
- Proper HTTP status codes
- Comprehensive validation for all inputs
- Detailed error messages

For more details, see the [API Standards Documentation](docs/api-standards.md).

## API Documentation

The API is documented using OpenAPI/Swagger. You can access the documentation at:

- `/api-docs` - Swagger UI
- `/api/v1/docs/openapi.json` - OpenAPI specification

To generate the documentation:

```bash
npm run docs
```

## Testing

The API includes comprehensive tests for all endpoints. To run the tests:

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

To generate tests for new endpoints:

```bash
npm run generate-tests
```

## Performance Monitoring

The API includes performance monitoring to track response times and identify slow endpoints. You can access the performance dashboard at:

- `/performance-dashboard`

To set up performance monitoring:

```bash
npm run setup-monitoring
```

## Caching

The API uses Redis for caching frequently accessed data. Caching is configured for different types of endpoints:

- Long cache (1 hour) for rarely changing data
- Medium cache (5 minutes) for occasionally changing data
- Short cache (1 minute) for frequently changing data
- No cache for real-time data

To optimize caching for your endpoints:

```bash
npm run optimize-caching
```

## Error Handling

The API includes comprehensive error handling for:

- Validation errors
- Database errors
- Authentication errors
- Authorization errors
- Server errors

All errors are returned in a consistent format:

```json
{
  "success": false,
  "message": "Error message",
  "errors": { "field": "Error message" },
  "errorCode": "ERROR_CODE",
  "timestamp": "2023-07-25T12:34:56.789Z"
}
```

## Validation

All inputs are validated using express-validator. Validation rules are defined in the route files and are enforced before the request reaches the controller.

## Authentication

The API uses JWT for authentication. To authenticate:

1. Call `/api/v1/auth/login` with your credentials
2. Use the returned access token in the Authorization header:
   ```
   Authorization: Bearer <access_token>
   ```

## Mock Data

The API supports mock data for development. You can toggle between mock and real data using the feature flag system in the admin dashboard.

To use mock data:

1. Set `USE_REAL_BACKEND` to `false` in the feature flags
2. The API will use mock data instead of real data

## Enhancing the API

To apply all enhancements to the API:

```bash
npm run enhance-api
```

This will:

1. Update all routes to follow the new standards
2. Optimize caching for frequently accessed data
3. Set up performance monitoring
4. Enhance API documentation
5. Generate tests for all endpoints

You can also run individual enhancement scripts:

```bash
# Update routes to follow new standards
npm run update-routes

# Optimize caching
npm run optimize-caching

# Set up performance monitoring
npm run setup-monitoring

# Enhance API documentation
npm run enhance-docs

# Generate tests
npm run generate-tests
```

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
