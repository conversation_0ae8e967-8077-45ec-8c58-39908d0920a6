/**
 * Search Integration Test
 * 
 * This script tests the integration between the frontend and backend for search functionality.
 * It verifies that the search API is working correctly with Redis caching.
 * 
 * Run this script with Node.js:
 * node src/website/search/test-integration.js
 */

const fetch = require('node-fetch');
const { performance } = require('perf_hooks');

// Configuration
const config = {
  apiUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api',
  accessToken: process.env.TEST_ACCESS_TOKEN || '', // Add your test access token here
  testUserId: process.env.TEST_USER_ID || 'VAI12345', // Add your test user ID here
  useRealBackend: process.env.USE_REAL_BACKEND === 'true' || false
};

// Test search functionality
async function testSearch() {
  console.log('=== Testing Search Integration ===');
  console.log(`API URL: ${config.apiUrl}`);
  console.log(`Using real backend: ${config.useRealBackend ? 'Yes' : 'No'}`);
  console.log('');

  try {
    // Test basic search
    await testBasicSearch();
    
    // Test advanced search
    await testAdvancedSearch();
    
    // Test similarity search
    await testSimilaritySearch();
    
    // Test compatibility score
    await testCompatibilityScore();
    
    console.log('\n=== All tests completed successfully ===');
  } catch (error) {
    console.error('\n=== Test failed ===');
    console.error(error);
    process.exit(1);
  }
}

// Test basic search
async function testBasicSearch() {
  console.log('Testing basic search...');
  
  // Search parameters
  const searchParams = new URLSearchParams({
    gender: 'FEMALE',
    minAge: 18,
    maxAge: 35,
    city: 'Mumbai',
    page: 1,
    limit: 10
  });
  
  // First search (should be from database)
  console.log('Performing first search (should be from database)...');
  const startTime1 = performance.now();
  const response1 = await fetch(`${config.apiUrl}/users/search?${searchParams.toString()}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.accessToken}`
    }
  });
  
  if (!response1.ok) {
    throw new Error(`Search API error: ${response1.status}`);
  }
  
  const data1 = await response1.json();
  const endTime1 = performance.now();
  
  console.log(`First search completed in ${(endTime1 - startTime1).toFixed(2)}ms`);
  console.log(`Found ${data1.total} profiles`);
  console.log(`From cache: ${data1.fromCache ? 'Yes' : 'No'}`);
  console.log('');
  
  // Second search (should be from cache)
  console.log('Performing second search (should be from cache)...');
  const startTime2 = performance.now();
  const response2 = await fetch(`${config.apiUrl}/users/search?${searchParams.toString()}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.accessToken}`
    }
  });
  
  if (!response2.ok) {
    throw new Error(`Search API error: ${response2.status}`);
  }
  
  const data2 = await response2.json();
  const endTime2 = performance.now();
  
  console.log(`Second search completed in ${(endTime2 - startTime2).toFixed(2)}ms`);
  console.log(`Found ${data2.total} profiles`);
  console.log(`From cache: ${data2.fromCache ? 'Yes' : 'No'}`);
  console.log('');
  
  // Verify cache is working
  if (config.useRealBackend && !data2.fromCache) {
    console.warn('Warning: Second search was not from cache. Redis caching may not be working correctly.');
  }
  
  // Force fresh search (skip cache)
  console.log('Performing forced fresh search (skip cache)...');
  const startTime3 = performance.now();
  const response3 = await fetch(`${config.apiUrl}/users/search?${searchParams.toString()}&skipCache=true`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.accessToken}`
    }
  });
  
  if (!response3.ok) {
    throw new Error(`Search API error: ${response3.status}`);
  }
  
  const data3 = await response3.json();
  const endTime3 = performance.now();
  
  console.log(`Fresh search completed in ${(endTime3 - startTime3).toFixed(2)}ms`);
  console.log(`Found ${data3.total} profiles`);
  console.log(`From cache: ${data3.fromCache ? 'Yes' : 'No'}`);
  console.log('');
  
  console.log('Basic search test completed successfully');
}

// Test advanced search
async function testAdvancedSearch() {
  console.log('Testing advanced search...');
  
  // Advanced search parameters
  const searchParams = new URLSearchParams({
    gender: 'FEMALE',
    minAge: 18,
    maxAge: 35,
    city: 'Mumbai',
    religion: 'HINDU',
    caste: 'MARATHA',
    education: 'MASTERS',
    occupation: 'SOFTWARE_ENGINEER',
    incomeRange: '10_15_LAKHS',
    maritalStatus: 'NEVER_MARRIED',
    diet: 'VEGETARIAN',
    page: 1,
    limit: 10
  });
  
  // First advanced search
  console.log('Performing first advanced search...');
  const startTime = performance.now();
  const response = await fetch(`${config.apiUrl}/users/advanced-search?${searchParams.toString()}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.accessToken}`
    }
  });
  
  if (!response.ok) {
    throw new Error(`Advanced search API error: ${response.status}`);
  }
  
  const data = await response.json();
  const endTime = performance.now();
  
  console.log(`Advanced search completed in ${(endTime - startTime).toFixed(2)}ms`);
  console.log(`Found ${data.total} profiles`);
  console.log(`From cache: ${data.fromCache ? 'Yes' : 'No'}`);
  console.log('');
  
  console.log('Advanced search test completed successfully');
}

// Test similarity search
async function testSimilaritySearch() {
  console.log('Testing similarity search...');
  
  // Similarity search parameters
  const searchParams = new URLSearchParams({
    minScore: 50,
    maxResults: 10
  });
  
  // Perform similarity search
  console.log('Performing similarity search...');
  const startTime = performance.now();
  const response = await fetch(`${config.apiUrl}/similarity/similar-profiles/${config.testUserId}?${searchParams.toString()}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.accessToken}`
    }
  });
  
  if (!response.ok) {
    throw new Error(`Similarity search API error: ${response.status}`);
  }
  
  const data = await response.json();
  const endTime = performance.now();
  
  console.log(`Similarity search completed in ${(endTime - startTime).toFixed(2)}ms`);
  console.log(`Found ${data.profiles?.length || 0} similar profiles`);
  console.log(`From cache: ${data.fromCache ? 'Yes' : 'No'}`);
  console.log('');
  
  console.log('Similarity search test completed successfully');
}

// Test compatibility score
async function testCompatibilityScore() {
  console.log('Testing compatibility score...');
  
  // Perform compatibility score check
  console.log('Checking compatibility score...');
  const startTime = performance.now();
  const response = await fetch(`${config.apiUrl}/similarity/compatibility/${config.testUserId}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.accessToken}`
    }
  });
  
  if (!response.ok) {
    throw new Error(`Compatibility score API error: ${response.status}`);
  }
  
  const data = await response.json();
  const endTime = performance.now();
  
  console.log(`Compatibility score check completed in ${(endTime - startTime).toFixed(2)}ms`);
  console.log(`Overall compatibility score: ${data.compatibility?.overallScore || 'N/A'}`);
  console.log(`From cache: ${data.fromCache ? 'Yes' : 'No'}`);
  console.log('');
  
  console.log('Compatibility score test completed successfully');
}

// Run the tests
testSearch().catch(console.error);
