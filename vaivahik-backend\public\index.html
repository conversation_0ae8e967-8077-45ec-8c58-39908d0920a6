<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vaivahik - AI-Powered Matrimony for Indian Families</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css"/>

    <style>
        /* Global Styles */
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap');

        :root {
            /* Core Colors */
            --primary-color: #FF5F6D; /* Coral Pink */
            --primary-light: #FFC371; /* Light Orange */
            --secondary-color: #8A2BE2; /* Blue Violet */
            --secondary-light: #9370DB; /* Medium Purple */
            --accent-color: #FFD700; /* Gold */

            /* Backgrounds & Text */
            --dark-color: #2D3047; /* Deep Blue/Gray (Used for Footer, maybe dark text) */
            --light-color: #F8F9FA; /* Very Light Gray (Main Section BG) */
            --white: #FFFFFF;
            --text-color-dark: #333; /* Primary dark text */
            --text-color-medium: #555; /* Secondary dark text */
            --text-color-light: #F0F0F0; /* Primary light text (on dark bg) */
            --text-color-light-muted: #B0B0B0; /* Secondary light text */

            /* Gradients */
            --primary-gradient: linear-gradient(135deg, #FF5F6D 0%, #FFC371 100%);
            --secondary-gradient: linear-gradient(135deg, #8A2BE2 0%, #9370DB 100%);

             /* Shadows & Effects */
            --shadow-soft: 0 5px 15px rgba(0,0,0,0.05);
            --shadow-medium: 0 10px 25px rgba(0,0,0,0.1);
            --shadow-hard: 0 15px 35px rgba(0,0,0,0.15);
            --transition-smooth: all 0.3s ease;
            --border-radius-medium: 20px;
            --border-radius-large: 25px;
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }
        html { scroll-behavior: smooth; }

        body {
            font-family: 'Montserrat', sans-serif;
            color: var(--text-color-dark);
            line-height: 1.6;
            overflow-x: hidden;
            background-color: var(--white); /* Default body background */
        }

        h1, h2, h3, h4, h5 {
            font-family: 'Playfair Display', serif;
            font-weight: 700;
            color: var(--dark-color);
        }

        p {
           color: var(--text-color-medium);
           margin-bottom: 1rem; /* Add default bottom margin to paragraphs */
        }
        section p:last-child {
             margin-bottom: 0; /* Remove margin from last paragraph in a section */
        }


        .container { width: 90%; max-width: 1400px; margin: 0 auto; }

        /* Button Styles */
        .btn { display: inline-block; padding: 12px 30px; border-radius: 50px; text-decoration: none; font-weight: 600; cursor: pointer; transition: var(--transition-smooth), transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); border: none; font-size: 16px; position: relative; overflow: hidden; z-index: 1; }
        .btn-primary { background: var(--primary-gradient); color: var(--white); box-shadow: 0 5px 15px rgba(255, 95, 109, 0.4); }
        .btn-secondary { background: var(--secondary-gradient); color: var(--white); box-shadow: 0 5px 15px rgba(138, 43, 226, 0.4); }
        .btn-outline { background: transparent; color: var(--primary-color); border: 2px solid var(--primary-color); }
        .btn-outline-secondary { background: transparent; color: var(--secondary-color); border: 2px solid var(--secondary-color); }
        .btn:hover, .btn:focus { transform: translateY(-5px); box-shadow: var(--shadow-hard); filter: brightness(1.1); outline: none; }
        .btn-outline:hover, .btn-outline:focus { background: var(--primary-color); color: var(--white); }
        .btn-outline-secondary:hover, .btn-outline-secondary:focus { background: var(--secondary-color); color: var(--white); }
        .btn-primary:hover, .btn-primary:focus { box-shadow: 0 8px 25px rgba(255, 95, 109, 0.6); }
        .btn-secondary:hover, .btn-secondary:focus { box-shadow: 0 8px 25px rgba(138, 43, 226, 0.6); }
        .btn::after { content: ''; position: absolute; width: 0; height: 0; border-radius: 50%; background: rgba(255, 255, 255, 0.2); top: 50%; left: 50%; transform: translate(-50%, -50%); transition: width 0.6s ease-out, height 0.6s ease-out; z-index: -1; opacity: 0; }
        .btn:hover::after { width: 300px; height: 300px; opacity: 1; }

        /* Section Base Styles */
        section {
            padding: 100px 0;
            position: relative;
            overflow: hidden;
            background-color: var(--light-color); /* Default light background for sections */
        }
        section:first-of-type { /* Hero section offset */
             padding-top: 150px;
             background-color: transparent; /* Hero handles its own background */
        }
         /* Override section background for specific sections if needed */
        #hero, #testimonials, .cta, footer {
             background-color: transparent; /* Allow these to set their own */
        }

        /* Section Title Styles */
        .section-title { text-align: center; margin-bottom: 60px; position: relative; }
        .section-title h2 { font-size: 42px; margin-bottom: 20px; background: var(--primary-gradient); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; display: inline-block; }
        .section-title .subtitle { font-size: 18px; color: var(--text-color-medium); max-width: 700px; margin: 0 auto 20px; position: relative; opacity: 0.9; }
        .section-title::after { content: ''; width: 80px; height: 3px; background: var(--primary-gradient); position: absolute; bottom: 0px; left: 50%; transform: translateX(-50%); border-radius: 2px; }

        /* Header */
        header { background: rgba(255, 255, 255, 0.95); padding: 15px 0; position: fixed; width: 100%; z-index: 1000; box-shadow: 0 2px 20px rgba(0,0,0,0.05); transition: var(--transition-smooth); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); }
        header.scrolled { padding: 10px 0; background: rgba(255, 255, 255, 0.98); box-shadow: var(--shadow-medium); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); }
        .header-container { display: flex; justify-content: space-between; align-items: center; }
        .logo { font-family: 'Playfair Display', serif; font-size: 28px; font-weight: 700; display: flex; align-items: center; text-decoration: none; transition: transform 0.3s ease; }
        .logo:hover { transform: scale(1.05); }
        .logo-text { background: var(--primary-gradient); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; }
        .logo-symbol {
            width: 32px;
            height: 32px;
            margin-right: 10px;
            color: var(--primary-color);
            transition: transform 0.3s ease;
        }
        .logo:hover .logo-symbol {
            transform: rotate(360deg);
        }
        .logo span { color: var(--secondary-color); font-weight: 800; }
        nav ul { display: flex; list-style: none; }
        nav ul li { margin-left: 30px; }
        nav ul li a { color: var(--dark-color); text-decoration: none; font-weight: 600; transition: var(--transition-smooth); position: relative; padding: 5px 0; letter-spacing: 0.5px; }
        nav ul li a::after { content: ''; position: absolute; width: 0; height: 2px; background: var(--primary-gradient); bottom: -2px; left: 50%; transform: translateX(-50%); transition: width 0.3s ease; border-radius: 1px; }
        nav ul li a:hover, nav ul li a:focus { color: var(--primary-color); outline: none; }
        nav ul li a:hover::after, nav ul li a:focus::after { width: 100%; }
        .hamburger { display: none; cursor: pointer; z-index: 1001; padding: 5px; background: transparent; border: none; }
        .hamburger div { width: 25px; height: 3px; background: var(--primary-color); margin: 5px; transition: all 0.4s ease; border-radius: 3px; }
        .hamburger.active .line1 { transform: rotate(-45deg) translate(-5px, 6px); }
        .hamburger.active .line2 { opacity: 0; transform: translateX(-20px); }
        .hamburger.active .line3 { transform: rotate(45deg) translate(-5px, -6px); }

        /* Hero Section */
        .hero { 
            background: url('https://placehold.co/1920x1080/FFC371/2D3047/png?text=Joyful+Indian+Couple+Silhouette') center/cover no-repeat; 
            min-height: 100vh; 
            display: flex; 
            align-items: center; 
            position: relative; 
            color: var(--white); 
            padding: 0; 
        }
        .hero::before { content: ''; position: absolute; inset: 0; background: linear-gradient(135deg, rgba(45, 48, 71, 0.9) 0%, rgba(255, 95, 109, 0.85) 100%); z-index: 1; }
        .hero-content { position: relative; z-index: 2; max-width: 750px; animation: fadeInUp 1s ease; }
        .hero h1 { font-size: 58px; font-weight: 700; margin-bottom: 25px; line-height: 1.25; text-shadow: 0 3px 12px rgba(0,0,0,0.25); color: var(--white); }
        .hero p { font-size: 21px; margin-bottom: 35px; text-shadow: 0 2px 7px rgba(0,0,0,0.2); opacity: 0.95; color: rgba(255, 255, 255, 0.95); }
        .hero-buttons { display: flex; flex-wrap: wrap; gap: 20px; margin-top: 30px; }
        .floating-hearts { position: absolute; inset: 0; z-index: 1; overflow: hidden; pointer-events: none; }
        .heart { position: absolute; width: 30px; height: 30px; background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF5F6D'%3E%3Cpath d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/%3E%3C/svg%3E") no-repeat center center; background-size: contain; opacity: 0.6; animation: floating 15s linear infinite; will-change: transform, opacity; }
        @keyframes floating { 0% { transform: translateY(100vh) scale(0.5) rotate(0deg); opacity: 0; } 10% { opacity: 0.8; } 90% { opacity: 0.8; } 100% { transform: translateY(-100px) scale(1.2) rotate(360deg); opacity: 0; } }

        /* Features Section Update */
        .features {
            background-color: var(--white);
            padding: 80px 0;
        }

        .section-title {
            text-align: center;
            margin-bottom: 60px;
        }

        .section-title h2 {
            font-size: 42px;
            background: linear-gradient(135deg, var(--primary-color) 0%, #FFA07A 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 20px;
        }

        .section-title .subtitle {
            font-size: 18px;
            color: var(--text-color-medium);
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            padding-bottom: 20px;
        }

        .section-title .subtitle::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 2px;
            background: var(--primary-color);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            padding: 20px;
        }

        .feature-card {
            background: var(--white);
            padding: 40px 30px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .feature-visual {
            width: 80px;
            height: 80px;
            margin-bottom: 30px;
            transform: rotate(-10deg);
            transition: transform 0.3s ease;
        }

        .feature-card:hover .feature-visual {
            transform: rotate(0deg);
        }

        .feature-card:nth-child(1) .feature-visual {
            background-color: var(--primary-color);
        }

        .feature-card:nth-child(2) .feature-visual {
            background-color: #8A2BE2;
        }

        .feature-card:nth-child(3) .feature-visual {
            background-color: #FFC371;
        }

        .feature-card:nth-child(4) .feature-visual {
            background-color: #4CAF50;
        }

        .feature-card h3 {
            font-size: 24px;
            color: var(--dark-color);
            margin-bottom: 15px;
            line-height: 1.3;
        }

        .feature-card p {
            color: var(--text-color-medium);
            font-size: 16px;
            line-height: 1.6;
            margin: 0;
        }

        /* How It Works Section (Light Background) */
        .how-it-works { background-color: var(--light-color); } /* Use light gray */
        .how-it-works::before { /* Subtle pattern for light bg */
            content: ''; position: absolute; inset: 0;
            background: url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%232D3047' fill-opacity='0.02'%3E%3Cpath d='M50 50c0-1.1-.9-2-2-2s-2 .9-2 2v10h-10c-1.1 0-2 .9-2 2s.9 2 2 2h10v10c0 1.1.9 2 2 2s2-.9 2-2V64h10c1.1 0 2-.9 2-2s-.9-2-2-2H50V50zm-50 0c0-1.1-.9-2-2-2s-2 .9-2 2v10H-10c-1.1 0-2 .9-2 2s.9 2 2 2h10v10c0 1.1.9 2 2 2s2-.9 2-2V64h10c1.1 0 2-.9 2-2s-.9-2-2-2H0V50zm50-50c0-1.1-.9-2-2-2s-2 .9-2 2v10h-10c-1.1 0-2 .9-2 2s.9 2 2 2h10v10c0 1.1.9 2 2 2s2-.9 2-2V14h10c1.1 0 2-.9 2-2s-.9-2-2-2H50V0zM0 0c0-1.1-.9-2-2-2s-2 .9-2 2v10H-10c-1.1 0-2 .9-2 2s.9 2 2 2h10v10c0 1.1.9 2 2 2s2-.9 2-2V14h10c1.1 0 2-.9 2-2s-.9-2-2-2H0V0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            opacity: 1; z-index: 0;
        }
        .process-timeline { display: flex; flex-wrap: wrap; justify-content: space-around; position: relative; margin-top: 80px; z-index: 1; }
        .process-timeline::before { content: ''; position: absolute; width: calc(100% - 200px); max-width: 1000px; height: 4px; background: var(--primary-gradient); top: 40px; left: 50%; transform: translateX(-50%); z-index: -1; display: none; border-radius: 2px; }
        .process-step { flex: 1; min-width: 250px; text-align: center; padding: 0 20px; position: relative; margin-bottom: 40px; }
        .step-number { background: var(--primary-gradient); color: var(--white); width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 32px; font-weight: 700; margin: 0 auto 30px; position: relative; z-index: 2; box-shadow: 0 10px 25px rgba(255, 95, 109, 0.35); transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); border: 3px solid var(--white); }
        .process-step:hover .step-number { transform: scale(1.15) rotate(5deg); box-shadow: 0 18px 35px rgba(255, 95, 109, 0.45); }
        .process-step h3 { color: var(--dark-color); }
        .process-step p { color: var(--text-color-medium); }

        /* Testimonials Section (Remains Purple Gradient) */
        .testimonials { background: var(--secondary-gradient); color: var(--white); position: relative; }
        .testimonials::before { content: ''; position: absolute; inset: 0; background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23FFFFFF' fill-opacity='0.07' fill-rule='evenodd'/%3E%3C/svg%3E"); z-index: 0; }
        .testimonials .section-title h2 { background: none; -webkit-text-fill-color: var(--white); text-shadow: 0 2px 8px rgba(0,0,0,0.2); }
        .testimonials .section-title .subtitle { color: rgba(255,255,255,0.9); }
        .testimonials .section-title::after { background: var(--white); height: 4px; bottom: 5px; }
        /* Swiper styles remain the same */
        .testimonial-slider { position: relative; z-index: 1; padding: 40px 0; max-width: 900px; margin: 0 auto; overflow: visible; }
        .swiper-container { width: 100%; padding: 20px 0 50px; }
        .testimonial-card { background: rgba(255,255,255,0.1); backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px); padding: 45px; border-radius: var(--border-radius-large); border: 1px solid rgba(255,255,255,0.25); box-shadow: 0 15px 35px rgba(0,0,0,0.15); transition: var(--transition-smooth); position: relative; margin: 0 10px; height: auto; min-height: 300px; display: flex; flex-direction: column; }
        .testimonial-card::before { content: '\201C'; font-family: Georgia, serif; position: absolute; top: 25px; left: 25px; font-size: 130px; color: rgba(255,255,255,0.1); line-height: 1; z-index: 0; transition: transform 0.4s ease; }
        .swiper-slide-active .testimonial-card::before { transform: scale(1.1); }
        .testimonial-text { font-style: italic; margin-bottom: 35px; font-size: 19px; line-height: 1.85; position: relative; z-index: 1; flex-grow: 1; color: rgba(255,255,255,0.95); }
        .testimonial-author { display: flex; align-items: center; margin-top: auto; }
        .author-image { width: 75px; height: 75px; border-radius: 50%; margin-right: 20px; background-color: rgba(255,255,255,0.2); background-size: cover; background-position: center; border: 4px solid rgba(255,255,255,0.35); box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .author-info h4 { font-size: 21px; margin-bottom: 5px; color: var(--white); font-weight: 600; }
        .author-info p { font-size: 16px; opacity: 0.85; }
        .swiper-button-next, .swiper-button-prev { color: var(--white); background-color: rgba(0, 0, 0, 0.2); width: 50px; height: 50px; border-radius: 50%; transition: var(--transition-smooth); opacity: 0.7; top: 50%; transform: translateY(-50%); }
        .swiper-button-next:hover, .swiper-button-prev:hover { background-color: rgba(0, 0, 0, 0.4); opacity: 1; transform: translateY(-50%) scale(1.1); }
        .swiper-button-next::after, .swiper-button-prev::after { font-size: 20px; font-weight: bold; }
        .swiper-button-prev { left: -15px; } .swiper-button-next { right: -15px; }
        .swiper-pagination-bullet { background-color: rgba(255, 255, 255, 0.5); width: 12px; height: 12px; opacity: 0.8; transition: var(--transition-smooth); }
        .swiper-pagination-bullet-active { background-color: var(--white); transform: scale(1.3); opacity: 1; }
        .swiper-pagination { bottom: 10px !important; position: absolute; }


        /* Success Stories Section (Light Background) */
        .success-stories { background-color: var(--light-color); } /* Use light gray */
        .success-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 35px; }
        .success-card { background: var(--white); border-radius: var(--border-radius-medium); overflow: hidden; box-shadow: var(--shadow-medium); transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); display: flex; flex-direction: column; border: 1px solid rgba(0,0,0,0.05); }
        .success-card:hover { transform: translateY(-12px) scale(1.03); box-shadow: var(--shadow-hard); }
        .success-image { height: 260px; background-size: cover; background-position: center; position: relative; overflow: hidden; }
        .success-image::before { content: ''; position: absolute; inset: 0; background: inherit; background-size: inherit; background-position: inherit; transition: transform 0.5s ease; }
        .success-card:hover .success-image::before { transform: scale(1.1); }
        .success-card:nth-child(1) .success-image { background-image: url('https://placehold.co/400x260/FF5F6D/FFFFFF/png?text=Anika+%26+Sameer'); }
        .success-card:nth-child(2) .success-image { background-image: url('https://placehold.co/400x260/8A2BE2/FFFFFF/png?text=Vikram+%26+Meera'); }
        .success-card:nth-child(3) .success-image { background-image: url('https://placehold.co/400x260/FFC371/2D3047/png?text=Deepa+%26+Arjun'); }
        .success-overlay { position: absolute; bottom: 0; left: 0; width: 100%; height: 50%; background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%); display: flex; align-items: flex-end; padding: 20px; box-sizing: border-box; z-index: 1; transition: height 0.4s ease; }
        .success-card:hover .success-overlay { height: 60%; }
        .success-names { color: var(--white); font-size: 24px; font-weight: 700; font-family: 'Playfair Display', serif; text-shadow: 1px 1px 3px rgba(0,0,0,0.5); }
        .success-content { padding: 30px; flex-grow: 1; display: flex; flex-direction: column; }
        .success-meta { display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 20px; color: #666; font-size: 14px; }
        .meta-item { display: flex; align-items: center; }
        .meta-item i { margin-right: 7px; color: var(--primary-color); width: 16px; text-align: center; }
        .success-story { font-size: 16px; line-height: 1.75; margin-bottom: 25px; color: var(--text-color-medium); flex-grow: 1; }
        .read-more { color: var(--primary-color); font-weight: 600; text-decoration: none; display: inline-flex; align-items: center; transition: var(--transition-smooth); margin-top: auto; padding: 5px 0; }
        .read-more i { margin-left: 8px; transition: transform 0.3s ease; }
        .read-more:hover, .read-more:focus { color: var(--secondary-color); letter-spacing: 0.5px; outline: none; }
        .read-more:hover i, .read-more:focus i { transform: translateX(6px); }

        /* Pricing Section Styles */
        .pricing {
            background-color: var(--white);
            padding: 80px 0;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .pricing-card {
            background: var(--white);
            border-radius: 25px;
            padding: 40px;
            text-align: left;
            position: relative;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        }

        .pricing-card.recommended {
            border: 2px solid var(--primary-color);
        }

        .popular-badge {
            position: absolute;
            top: -12px;
            left: 50%;
            transform: translateX(-50%);
            background: #FFD700;
            color: var(--dark-color);
            padding: 8px 20px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
        }

        .pricing-header {
            margin-bottom: 30px;
        }

        .pricing-header h3 {
            font-size: 32px;
            color: var(--dark-color);
            margin-bottom: 20px;
            font-weight: 600;
        }

        .price {
            font-size: 48px;
            color: var(--primary-color);
            font-weight: 600;
        }

        .price.free {
            color: var(--primary-color);
        }

        .price span {
            font-size: 16px;
            color: #666;
            margin-left: 2px;
        }

        .pricing-features {
            list-style: none;
            padding: 0;
            margin: 0 0 30px 0;
        }

        .pricing-features li {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            color: var(--text-color-medium);
            font-size: 16px;
        }

        .pricing-features li::before {
            content: '✓';
            color: var(--primary-color);
            margin-right: 10px;
            font-weight: bold;
        }

        .pricing-features li.star::before {
            content: '★';
            color: #FFD700;
        }

        .pricing-features li.disabled {
            color: #999;
            text-decoration: line-through;
        }

        .pricing-features li.disabled::before {
            content: '×';
            color: #999;
        }

        /* Pricing Buttons */
        .btn-register {
            width: 100%;
            padding: 15px;
            border: 2px solid var(--primary-color);
            border-radius: 50px;
            color: var(--primary-color);
            background: transparent;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-connect {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 50px;
            color: white;
            background: linear-gradient(to right, var(--primary-color), #FF8C7F);
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-thrive {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 50px;
            color: white;
            background: linear-gradient(to right, #8A2BE2, #9370DB);
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        /* CTA Section */
        .cta { background: var(--primary-gradient); color: var(--white); text-align: center; padding: 80px 0; }
        .cta h2 { font-size: 40px; margin-bottom: 25px; text-shadow: 0 2px 8px rgba(0,0,0,0.15); color: var(--white); }
        .cta p { font-size: 19px; margin-bottom: 35px; max-width: 650px; margin-left: auto; margin-right: auto; opacity: 0.9; line-height: 1.7; color: rgba(255,255,255,0.9); }
        .cta .btn { background: var(--white); color: var(--primary-color); box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1); padding: 15px 35px; font-size: 17px; }
        .cta .btn:hover, .cta .btn:focus { background: var(--light-color); box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15); color: var(--dark-color); transform: translateY(-7px) scale(1.05); }

        /* Footer Styles Update */
        footer {
            background-color: #2D3047;
            padding: 80px 0 40px;
            text-align: center;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 40px;
        }

        .footer-logo {
            display: inline-flex;
            align-items: center;
            text-decoration: none;
        }

        .footer-logo .logo-symbol {
            color: #FFD700;
        }

        .footer-logo .logo-text {
            color: #FF5F6D;
            font-size: 28px;
            font-weight: 600;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 40px;
        }

        .footer-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-size: 16px;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: var(--primary-color);
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 20px 0;
        }

        .social-links a {
            color: rgba(255, 255, 255, 0.8);
            font-size: 20px;
            transition: color 0.3s ease;
        }

        .social-links a:hover {
            color: var(--primary-color);
        }

        .footer-divider {
            width: 100%;
            height: 1px;
            background: rgba(255, 255, 255, 0.1);
            margin: 20px 0;
        }

        .copyright {
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
            line-height: 1.6;
        }

        .copyright .heart {
            color: var(--primary-color);
            display: inline-block;
            margin: 0 4px;
            position: relative;
            top: 2px;
        }

        /* Responsive Design Adjustments */
        @media (max-width: 992px) { .container { width: 95%; } header nav ul { box-shadow: var(--shadow-hard); border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; } header nav.active ul { max-height: 60vh; } header nav ul li a { padding: 18px; } .hero h1 { font-size: 44px; } .hero p { font-size: 19px; } .process-timeline { margin-top: 50px; } .process-timeline::before { display: none; } .process-step { flex-basis: 100%; max-width: 450px; } .features-grid, .success-grid, .pricing-grid { grid-template-columns: 1fr; gap: 30px; } .pricing-card.recommended { transform: scale(1); } .pricing-card.recommended:hover { transform: translateY(-10px); } .swiper-button-next, .swiper-button-prev { width: 40px; height: 40px; } .swiper-button-next::after, .swiper-button-prev::after { font-size: 16px; } .swiper-button-prev { left: 5px; } .swiper-button-next { right: 5px; } }
        @media (min-width: 993px) { .process-timeline::before { display: block; } }
        @media (max-width: 768px) { h1 { font-size: 38px; } h2, .section-title h2 { font-size: 34px; } h3 { font-size: 22px; } .hero h1 { font-size: 38px; } .hero p { font-size: 17px; } .hero-buttons { flex-direction: column; align-items: center; gap: 15px; } .hero-buttons .btn { width: 90%; max-width: 300px; text-align: center; } .section-title .subtitle { font-size: 17px; } .testimonial-card { padding: 35px 25px; } .testimonial-author { flex-direction: column; align-items: center; text-align: center; } .author-image { margin-right: 0; margin-bottom: 20px; } .pricing-tabs { flex-direction: column; gap: 12px; align-items: stretch; width: 90%; padding: 10px; } .pricing-tab { border-radius: 50px !important; margin: 0; text-align: center; } .footer-content { gap: 25px; } .footer-links ul { flex-direction: column; gap: 12px; } .social-links a { font-size: 24px; margin: 0 10px; } .swiper-button-next, .swiper-button-prev { display: none; } .swiper-container { padding-bottom: 40px; } .testimonial-slider { overflow: hidden; } }

        @keyframes pulse-spin {
            0% { transform: scale(1) rotate(0deg); }
            25% { transform: scale(1.2) rotate(90deg); }
            50% { transform: scale(1) rotate(180deg); }
            75% { transform: scale(1.2) rotate(270deg); }
            100% { transform: scale(1) rotate(360deg); }
        }

        /* Update the nav styles to accommodate the button */
        nav ul li a.nav-cta {
            padding: 10px 20px;
            margin-left: 15px;
            border-radius: 50px;
        }
        
        nav ul li a.nav-cta::after {
            display: none;
        }
        
        nav ul li a.nav-cta:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 95, 109, 0.4);
        }

        @media (max-width: 768px) {
            nav ul {
                display: none;
                position: fixed;
                top: 0;
                right: 0;
                height: 100vh;
                width: 70%;
                max-width: 300px;
                background: var(--white);
                flex-direction: column;
                padding: 80px 40px;
                box-shadow: -5px 0 25px rgba(0,0,0,0.1);
                transform: translateX(100%);
                transition: transform 0.3s ease;
            }
            
            nav ul.active {
                transform: translateX(0);
                display: flex;
            }
            
            nav ul li {
                margin: 15px 0;
            }
            
            nav ul li a.nav-cta {
                margin: 10px 0;
                text-align: center;
                display: block;
            }
            
            .hamburger {
                display: block;
            }
        }
    </style>
</head>
<body>
    <header id="main-header">
        <div class="container header-container">
            <a href="/" class="logo">
                <i class="fas fa-heart logo-symbol"></i>
                <span class="logo-text">Vaivahik</span>
            </a>
            <nav>
                <ul>
                    <li><a href="#features">Features</a></li>
                    <li><a href="#how-it-works">How It Works</a></li>
                    <li><a href="#testimonials">Testimonials</a></li>
                    <li><a href="#pricing">Pricing</a></li>
                    <li><a href="/register.html" class="btn btn-primary nav-cta" onclick="window.location.href='/register.html'">Sign In / Sign Up</a></li>
                </ul>
            </nav>
            <button class="hamburger">
                <div class="line1"></div>
                <div class="line2"></div>
                <div class="line3"></div>
            </button>
        </div>
    </header>

    <section id="hero" class="hero">
        <div class="floating-hearts" id="floating-hearts-container"></div>
        <div class="container">
            <div class="hero-content animate__animated" data-animation="animate__fadeInUp">
                <h1>Begin Your Journey to Forever, Together.</h1>
                <p>Discover meaningful connections with Vaivahik, where modern AI meets timeless Indian traditions to help families find the perfect match.</p>
                <div class="hero-buttons">
                    <a href="#pricing" class="btn btn-primary">Explore Membership Plans</a>
                    <a href="#features" class="btn btn-outline">Learn How Vaivahik Works</a>
                </div>
            </div>
        </div>
    </section>

    <section id="features" class="features">
        <div class="container">
            <div class="section-title">
                <h2>The Vaivahik Advantage</h2>
                <p class="subtitle">Blending technology and tradition to create compatible, lasting relationships with trust and understanding.</p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-visual">Match AI</div>
                    <h3>Intelligent<br>Compatibility<br>Matching</h3>
                    <p>Our AI delves deeper than surface-level details, analyzing core values, lifestyle compatibility, and long-term goals for truly meaningful matches.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-visual">Verified Profile</div>
                    <h3>Verified Profiles,<br>Secure Platform</h3>
                    <p>Your safety is paramount. We ensure profile authenticity through verification processes within a secure, privacy-focused environment.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-visual">Family Focus</div>
                    <h3>Family-Centric<br>Approach</h3>
                    <p>Understanding the importance of family, our platform facilitates communication and provides tools for involving loved ones in the journey.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-visual">Privacy Lock</div>
                    <h3>Complete Privacy<br>Control</h3>
                    <p>Manage your visibility with granular privacy settings. You decide who sees your profile details and when you connect.</p>
                </div>
            </div>
        </div>
    </section>

    <section id="how-it-works" class="how-it-works">
         <div class="container">
            <div class="section-title">
                <h2>Your Path to Partnership: Simple & Clear</h2>
                <p class="subtitle">Follow these easy steps, supported by our technology and team, to find your ideal life partner.</p>
            </div>
            <div class="process-timeline">
                <div class="process-step animate__animated" data-animation="animate__fadeInUp"><div class="step-number">1</div><h3>Craft Your Story</h3><p>Register and create a comprehensive profile that truly reflects your personality, aspirations, and partner preferences.</p></div>
                <div class="process-step animate__animated" data-animation="animate__fadeInUp" data-wow-delay="0.1s"><div class="step-number">2</div><h3>Discover Matches</h3><p>Receive carefully curated, AI-driven match suggestions based on deep compatibility analysis.</p></div>
                <div class="process-step animate__animated" data-animation="animate__fadeInUp" data-wow-delay="0.2s"><div class="step-number">3</div><h3>Connect Confidently</h3><p>Express interest in profiles that resonate with you and initiate conversations through our secure messaging platform.</p></div>
                <div class="process-step animate__animated" data-animation="animate__fadeInUp" data-wow-delay="0.3s"><div class="step-number">4</div><h3>Build Your Future</h3><p>Nurture connections, involve family members respectfully when the time is right, and make informed decisions together.</p></div>
            </div>
         </div>
    </section>

    <section id="testimonials" class="testimonials">
        <div class="container">
            <div class="section-title">
                <h2>Real Couples, Real Connections</h2>
                <p class="subtitle">Don't just take our word for it. Hear from members who found happiness with Vaivahik.</p>
            </div>
            <div class="testimonial-slider swiper-container animate__animated" data-animation="animate__zoomIn">
                <div class="swiper-wrapper">
                    <div class="swiper-slide"><div class="testimonial-card"><p class="testimonial-text">"Finding someone who truly understood our family's values felt impossible online... Vaivahik's AI matching was incredibly insightful... Happily married now!"</p><div class="testimonial-author"><div class="author-image" style="background-image: url('https://placehold.co/75x75/FF5F6D/FFFFFF/png?text=P+%26+R')"></div><div class="author-info"><h4>Priya & Rohan S.</h4><p>Found Each Other: 2024</p></div></div></div></div>
                    <div class="swiper-slide"><div class="testimonial-card"><p class="testimonial-text">"As busy professionals... Vaivahik's verified profiles and focused matches saved us so much time and led us to a wonderful connection..."</p><div class="testimonial-author"><div class="author-image" style="background-image: url('https://placehold.co/75x75/8A2BE2/FFFFFF/png?text=A+%26+K')"></div><div class="author-info"><h4>Amit & Kavita M.</h4><p>Found Each Other: 2025</p></div></div></div></div>
                    <div class="swiper-slide"><div class="testimonial-card"><p class="testimonial-text">"The emphasis on privacy and the ability to involve family comfortably made all the difference... perfect blend of modern technology and traditional values..."</p><div class="testimonial-author"><div class="author-image" style="background-image: url('https://placehold.co/75x75/FFC371/2D3047/png?text=S+%26+N')"></div><div class="author-info"><h4>Sunita & Naveen P.</h4><p>Found Each Other: 2024</p></div></div></div></div>
                </div>
                <div class="swiper-pagination"></div>
                <div class="swiper-button-prev"></div>
                <div class="swiper-button-next"></div>
            </div>
        </div>
    </section>

    <section id="success-stories" class="success-stories">
         <div class="container">
            <div class="section-title">
                <h2>Celebrating New Beginnings</h2>
                <p class="subtitle">Inspiring stories of love and partnership fostered through the Vaivahik platform.</p>
            </div>
            <div class="success-grid">
                <div class="success-card animate__animated" data-animation="animate__fadeInUp"><div class="success-image"><div class="success-overlay"><h3 class="success-names">Anika & Sameer</h3></div></div><div class="success-content"><div class="success-meta"><span class="meta-item"><i class="fas fa-calendar-check"></i> Connected: Aug 2024</span><span class="meta-item"><i class="fas fa-ring"></i> Married: Feb 2025</span></div><p class="success-story">Despite living miles apart, Vaivahik's platform brought us together. The detailed profiles and compatibility insights were key...</p><a href="#" class="read-more">Read Their Full Story <i class="fas fa-arrow-right"></i></a></div></div>
                <div class="success-card animate__animated" data-animation="animate__fadeInUp" data-wow-delay="0.1s"><div class="success-image"><div class="success-overlay"><h3 class="success-names">Vikram & Meera</h3></div></div><div class="success-content"><div class="success-meta"><span class="meta-item"><i class="fas fa-calendar-check"></i> Connected: Sep 2024</span><span class="meta-item"><i class="fas fa-user-friends"></i> Families Met: Dec 2024</span></div><p class="success-story">Finding someone who shared not just interests but core life values felt serendipitous. Vaivahik facilitated our journey beautifully.</p><a href="#" class="read-more">Read Their Full Story <i class="fas fa-arrow-right"></i></a></div></div>
                <div class="success-card animate__animated" data-animation="animate__fadeInUp" data-wow-delay="0.2s"><div class="success-image"><div class="success-overlay"><h3 class="success-names">Deepa & Arjun</h3></div></div><div class="success-content"><div class="success-meta"><span class="meta-item"><i class="fas fa-calendar-check"></i> Connected: Jul 2024</span><span class="meta-item"><i class="fas fa-ring"></i> Married: Mar 2025</span></div><p class="success-story">The platform's respect for family involvement and privacy controls allowed us to navigate the process comfortably and confidently.</p><a href="#" class="read-more">Read Their Full Story <i class="fas fa-arrow-right"></i></a></div></div>
            </div>
         </div>
    </section>

    <section id="pricing" class="pricing">
        <div class="container">
            <div class="section-title">
                <h2>Invest in Your Future Happiness</h2>
                <p class="subtitle">Choose a plan that empowers your search for a compatible life partner on Vaivahik.</p>
            </div>
            <div class="pricing-tabs">
                <span class="pricing-tab active" data-plan="monthly">Monthly Billing</span>
                <span class="pricing-tab" data-plan="yearly">Annual Billing (Save 25%)</span>
            </div>
            <div class="pricing-grid">
                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3>Discover</h3>
                        <p class="price free">Free</p>
                    </div>
                    <ul class="pricing-features">
                        <li>Create Your Profile</li>
                        <li>Receive AI Match Suggestions</li>
                        <li>Browse Profiles</li>
                        <li class="disabled">Send Unlimited Interests</li>
                        <li class="disabled">View Contact Information</li>
                        <li class="disabled">Enhanced Privacy Controls</li>
                    </ul>
                    <button class="btn-register" onclick="window.location.href='/register.html'">Register for Free</button>
                </div>

                <div class="pricing-card recommended">
                    <div class="popular-badge">Most Popular</div>
                    <div class="pricing-header">
                        <h3>Connect</h3>
                        <p class="price">$49<span>/mo*</span></p>
                    </div>
                    <ul class="pricing-features">
                        <li>All Discover Features</li>
                        <li>Send Unlimited Interests</li>
                        <li>Initiate Secure Chats</li>
                        <li>View Verified Contact Details</li>
                        <li>Enhanced Privacy Options</li>
                        <li>Profile Performance Insights</li>
                    </ul>
                    <button class="btn-connect" onclick="window.location.href='/register.html?plan=connect'">Choose Connect Plan</button>
                </div>

                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3>Thrive</h3>
                        <p class="price">$99<span>/mo*</span></p>
                    </div>
                    <ul class="pricing-features">
                        <li>All Connect Features</li>
                        <li class="star">Dedicated Relationship Advisor</li>
                        <li class="star">Profile Highlighting & Boost</li>
                        <li class="star">Advanced Compatibility Reports</li>
                        <li class="star">Priority Customer Support</li>
                        <li class="star">Early Access to New Features</li>
                    </ul>
                    <button class="btn-thrive" onclick="window.location.href='/register.html?plan=thrive'">Choose Thrive Plan</button>
                </div>
            </div>
            <p class="pricing-card-footer-note"> *Prices shown for monthly billing. Choose Annual Billing tab for discounted rates. </p>
        </div>
    </section>

    <section class="cta">
        <div class="container"> 
            <h2 class="animate__animated" data-animation="animate__pulse">Your Search for a Life Partner Starts Here.</h2> 
            <p class="animate__animated" data-animation="animate__fadeInUp">Join the Vaivahik community today. Create your profile for free and discover how our unique blend of technology and tradition can help you find lasting happiness.</p> 
            <a href="/register.html" class="btn btn-secondary animate__animated" data-animation="animate__swing" onclick="window.location.href='/register.html'">Create Your Free Profile Now</a> 
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <a href="#" class="footer-logo">
                    <svg class="logo-symbol" viewBox="0 0 512 512">
                        <path d="M462.3 62.6C407.5 15.9 326 24.3 275.7 76.2L256 96.5l-19.7-20.3C186.1 24.3 104.5 15.9 49.7 62.6c-62.8 53.6-66.1 149.8-9.9 207.9l193.5 199.8c12.5 12.9 32.8 12.9 45.3 0l193.5-199.8c56.3-58.1 53-154.3-9.8-207.9z" fill="currentColor"/>
                    </svg>
                    <span class="logo-text">Vaivahik</span>
                </a>
                
                <div class="footer-links">
                    <a href="#">About Vaivahik</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Privacy Policy</a>
                    <a href="#">Terms of Service</a>
                    <a href="#">Help & FAQ</a>
                    <a href="#">Blog</a>
                </div>
                
                <div class="social-links">
                    <a href="#" aria-label="Facebook">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" aria-label="Twitter">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" aria-label="Instagram">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" aria-label="LinkedIn">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                </div>

                <div class="footer-divider"></div>
                
                <div class="copyright">
                    © 2025 Vaivahik Matrimony Services. All Rights Reserved.<br>
                    Built with <svg class="heart" viewBox="0 0 512 512" width="16" height="16">
                        <path d="M462.3 62.6C407.5 15.9 326 24.3 275.7 76.2L256 96.5l-19.7-20.3C186.1 24.3 104.5 15.9 49.7 62.6c-62.8 53.6-66.1 149.8-9.9 207.9l193.5 199.8c12.5 12.9 32.8 12.9 45.3 0l193.5-199.8c56.3-58.1 53-154.3-9.8-207.9z" fill="currentColor"/>
                    </svg> for meaningful connections.
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const header = document.getElementById('main-header');
            window.addEventListener('scroll', () => { header.classList.toggle('scrolled', window.scrollY > 50); });
            const hamburger = document.getElementById('hamburger-icon');
            const nav = document.getElementById('main-nav');
            if (hamburger && nav) { hamburger.addEventListener('click', () => { hamburger.classList.toggle('active'); nav.classList.toggle('active'); }); }
            const navLinks = nav?.querySelectorAll('ul li a');
            navLinks?.forEach(link => { link.addEventListener('click', (e) => { if (link.getAttribute('href').startsWith('#') && nav.classList.contains('active')) { hamburger?.classList.remove('active'); nav.classList.remove('active'); } }); });
            const heartsContainer = document.getElementById('floating-hearts-container');
            if (heartsContainer) { const n = 15; for (let i = 0; i < n; i++) createHeart(); }
            function createHeart() { const h = document.createElement('div'); h.classList.add('heart'); h.style.left = Math.random()*100+'vw'; h.style.animationDuration = (Math.random()*10+10)+'s'; h.style.animationDelay = Math.random()*5+'s'; const s=Math.random()*.5+.5; h.style.width=(30*s)+'px'; h.style.height=(30*s)+'px'; h.style.opacity=Math.random()*.4+.3; heartsContainer?.appendChild(h); h.addEventListener('animationend', () => { h.remove(); if (heartsContainer && heartsContainer.children.length<20) createHeart(); }, { once: true }); }
            const yearSpan = document.getElementById('current-year');
            if (yearSpan) yearSpan.textContent = new Date().getFullYear();
            const pricingTabs = document.querySelectorAll('.pricing-tab');
            const pricingCards = document.querySelectorAll('.pricing-card');
            const prices = {
                monthly: ['$0', '$49<span>/mo</span>', '$99<span>/mo</span>'],
                yearly: ['$0', '$449<span>/yr</span>', '$999<span>/yr</span>']
            };
            pricingTabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    pricingTabs.forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    
                    const plan = tab.getAttribute('data-plan');
                    pricingCards.forEach((card, index) => {
                        const priceEl = card.querySelector('.price');
                        if (priceEl && prices[plan]?.[index]) {
                            priceEl.innerHTML = prices[plan][index];
                        }
                    });
                });
            });
            if (typeof Swiper !== 'undefined') { const swiper = new Swiper('.swiper-container', { loop: true, slidesPerView: 1, spaceBetween: 30, autoplay: { delay: 5500, disableOnInteraction: false }, pagination: { el: '.swiper-pagination', clickable: true }, navigation: { nextEl: '.swiper-button-next', prevEl: '.swiper-button-prev' }, grabCursor: true, effect: 'fade', fadeEffect: { crossFade: true }, breakpoints: { 768: { spaceBetween: 40 }, 992: { spaceBetween: 50 } } }); } else console.error("Swiper library not loaded.");
            const animatedElements = document.querySelectorAll('.animate__animated');
            const observer = new IntersectionObserver((entries, obs) => { entries.forEach(entry => { if (entry.isIntersecting) { const a = entry.target.getAttribute('data-animation')||'animate__fadeIn'; entry.target.classList.add(a); obs.unobserve(entry.target); } }); }, { threshold: 0.15 });
            animatedElements.forEach(el => { const i=el.getAttribute('data-animation'); if(i) el.classList.remove(i); observer.observe(el); });
        });
    </script>
</body>
</html>
