import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Verify JWT token
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ success: false, message: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const userId = decoded.userId;

    // Get current user's profile for filtering
    const currentUser = await prisma.user.findUnique({
      where: { id: userId },
      include: { profile: true }
    });

    if (!currentUser || !currentUser.profile) {
      return res.status(400).json({
        success: false,
        message: 'User profile not found'
      });
    }

    // Extract search parameters
    const {
      page = 1,
      limit = 12,
      ageMin,
      ageMax,
      heightMin,
      heightMax,
      education,
      occupation,
      location,
      subcaste,
      maritalStatus,
      motherTongue,
      minIncome,
      maxIncome,
      hasPhoto,
      isVerified,
      isOnline
    } = req.query;

    // Build where clause
    const whereClause = {
      AND: [
        // Exclude current user
        { id: { not: userId } },
        // Only active profiles
        { profileStatus: 'ACTIVE' },
        // Opposite gender (for heterosexual matching)
        { profile: { gender: { not: currentUser.profile.gender } } }
      ]
    };

    // Age filter
    if (ageMin || ageMax) {
      whereClause.AND.push({
        profile: {
          age: {
            ...(ageMin && { gte: parseInt(ageMin) }),
            ...(ageMax && { lte: parseInt(ageMax) })
          }
        }
      });
    }

    // Height filter
    if (heightMin || heightMax) {
      whereClause.AND.push({
        profile: {
          height: {
            ...(heightMin && { gte: parseInt(heightMin) }),
            ...(heightMax && { lte: parseInt(heightMax) })
          }
        }
      });
    }

    // Education filter
    if (education && education !== '') {
      whereClause.AND.push({
        profile: { education: education }
      });
    }

    // Occupation filter
    if (occupation && occupation !== '') {
      whereClause.AND.push({
        profile: { occupation: occupation }
      });
    }

    // Location filter
    if (location && location !== '') {
      whereClause.AND.push({
        OR: [
          { profile: { city: { contains: location, mode: 'insensitive' } } },
          { profile: { state: { contains: location, mode: 'insensitive' } } }
        ]
      });
    }

    // Subcaste filter
    if (subcaste && subcaste !== '') {
      whereClause.AND.push({
        profile: { subCaste: subcaste }
      });
    }

    // Marital status filter
    if (maritalStatus && maritalStatus !== '') {
      whereClause.AND.push({
        profile: { maritalStatus: maritalStatus }
      });
    }

    // Mother tongue filter
    if (motherTongue && motherTongue !== '') {
      whereClause.AND.push({
        profile: { motherTongue: motherTongue }
      });
    }

    // Income filter
    if (minIncome || maxIncome) {
      whereClause.AND.push({
        profile: {
          annualIncome: {
            ...(minIncome && { gte: parseInt(minIncome) }),
            ...(maxIncome && { lte: parseInt(maxIncome) })
          }
        }
      });
    }

    // Has photo filter
    if (hasPhoto === 'true') {
      whereClause.AND.push({
        profile: {
          profilePicUrl: { not: null }
        }
      });
    }

    // Verified filter
    if (isVerified === 'true') {
      whereClause.AND.push({
        isVerified: true
      });
    }

    // Online filter (last active within 24 hours)
    if (isOnline === 'true') {
      const onlineThreshold = new Date(Date.now() - 24 * 60 * 60 * 1000);
      whereClause.AND.push({
        lastActiveAt: { gte: onlineThreshold }
      });
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get total count
    const totalCount = await prisma.user.count({ where: whereClause });

    // Get profiles
    const profiles = await prisma.user.findMany({
      where: whereClause,
      include: {
        profile: true
      },
      skip: skip,
      take: parseInt(limit),
      orderBy: [
        { isVerified: 'desc' },
        { lastActiveAt: 'desc' },
        { createdAt: 'desc' }
      ]
    });

    // Get user's sent interests and shortlist for status checking
    const profileIds = profiles.map(p => p.id);
    
    const sentInterests = await prisma.interest.findMany({
      where: {
        userId: userId,
        targetUserId: { in: profileIds }
      }
    });

    const shortlistedProfiles = await prisma.shortlist.findMany({
      where: {
        userId: userId,
        targetUserId: { in: profileIds }
      }
    });

    const interestMap = sentInterests.reduce((acc, interest) => {
      acc[interest.targetUserId] = interest.status;
      return acc;
    }, {});

    const shortlistMap = shortlistedProfiles.reduce((acc, item) => {
      acc[item.targetUserId] = true;
      return acc;
    }, {});

    // Format the response
    const formattedProfiles = profiles.map(user => ({
      id: user.id,
      firstName: user.profile?.firstName || 'Unknown',
      lastName: user.profile?.lastName || '',
      age: user.profile?.age,
      height: user.profile?.height,
      city: user.profile?.city,
      state: user.profile?.state,
      education: user.profile?.education,
      occupation: user.profile?.occupation,
      profilePicture: user.profile?.profilePicUrl,
      isVerified: user.isVerified,
      isOnline: user.lastActiveAt && (Date.now() - new Date(user.lastActiveAt).getTime()) < 24 * 60 * 60 * 1000,
      compatibility: Math.floor(Math.random() * 30) + 70, // Mock compatibility score
      interestSent: !!interestMap[user.id],
      interestStatus: interestMap[user.id] || null,
      isShortlisted: !!shortlistMap[user.id]
    }));

    return res.status(200).json({
      success: true,
      data: {
        profiles: formattedProfiles,
        total: totalCount,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(totalCount / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('Error searching profiles:', error);
    return res.status(500).json({
      success: false,
      message: 'Error searching profiles',
      error: error.message
    });
  }
}
