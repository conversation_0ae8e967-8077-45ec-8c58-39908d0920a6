import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Paper,
  CircularProgress,
  Alert,
  Snackbar
} from '@mui/material';
import LifestyleForm from '@/website/components/profile/LifestyleForm';
import { isUsingRealBackend } from '@/utils/featureFlags';

// Mock user data for development
const MOCK_USER_DATA = {
  id: 'user123',
  fullName: '<PERSON><PERSON> Sharma',
  diet: 'VEGETARIAN',
  smoking: 'NO',
  drinking: 'OCCASIONALLY',
  hobbies: ['Reading', 'Traveling', 'Music'],
  interests: 'I enjoy watching cricket and exploring new cuisines.'
};

export default function EditLifestyle() {
  const router = useRouter();
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [saving, setSaving] = useState(false);

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      setLoading(true);
      setError('');

      try {
        if (isUsingRealBackend()) {
          // Call real API
          const response = await fetch('/api/user/profile', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          });

          if (!response.ok) {
            throw new Error('Failed to fetch user data');
          }

          const data = await response.json();
          setUserData(data);
        } else {
          // Use mock data
          setTimeout(() => {
            setUserData(MOCK_USER_DATA);
            setLoading(false);
          }, 500); // Simulate API delay
        }
      } catch (err) {
        console.error('Error fetching user data:', err);
        setError('Failed to load your profile data. Please try again later.');
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  // Handle save
  const handleSave = async (formData) => {
    setSaving(true);
    setError('');

    try {
      if (isUsingRealBackend()) {
        // Call real API
        const response = await fetch('/api/user/lifestyle', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify(formData)
        });

        if (!response.ok) {
          throw new Error('Failed to update lifestyle details');
        }

        const data = await response.json();
        setSuccess('Lifestyle details updated successfully!');
        
        // Update local user data
        setUserData(prevData => ({
          ...prevData,
          ...formData
        }));
      } else {
        // Mock API call
        setTimeout(() => {
          // Update local user data
          setUserData(prevData => ({
            ...prevData,
            ...formData
          }));
          setSuccess('Lifestyle details updated successfully! (Mock)');
          setSaving(false);
        }, 1000); // Simulate API delay
      }
    } catch (err) {
      console.error('Error saving lifestyle details:', err);
      setError('Failed to save your lifestyle details. Please try again.');
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
        <CircularProgress />
        <Typography variant="body1" sx={{ mt: 2 }}>
          Loading your profile...
        </Typography>
      </Container>
    );
  }

  return (
    <>
      <Head>
        <title>Edit Lifestyle & Habits | Vaivahik</title>
        <meta name="description" content="Edit your lifestyle and habits on Vaivahik matrimony" />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <LifestyleForm 
          userData={userData} 
          onSave={handleSave} 
          isLoading={saving} 
        />
      </Container>

      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess('')}
        message={success}
      />
    </>
  );
}
