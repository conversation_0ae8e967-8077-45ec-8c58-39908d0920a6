/* ConversationList.module.css */

.conversationList {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 80vh;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background-color: #7e3af2;
  color: white;
}

.header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.newChatButton {
  background-color: white;
  color: #7e3af2;
  border: none;
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.newChatButton:hover {
  background-color: #f3f4f6;
}

.searchContainer {
  padding: 15px;
  border-bottom: 1px solid #e5e7eb;
}

.inlinePromotion {
  padding: 0 15px;
  margin-top: 5px;
}

.searchInput {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid #e5e7eb;
  border-radius: 20px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.searchInput:focus {
  border-color: #7e3af2;
}

.conversations {
  flex: 1;
  overflow-y: auto;
}

.conversationItem {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s;
}

.conversationItem:hover {
  background-color: #f9fafb;
}

.conversationItem.active {
  background-color: #f3f4ff;
  border-left: 3px solid #7e3af2;
}

.avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #666;
  margin-right: 15px;
  flex-shrink: 0;
}

.avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.conversationDetails {
  flex: 1;
  min-width: 0; /* Ensures text truncation works */
}

.conversationHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.userName {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.timestamp {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
}

.conversationPreview {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.lastMessage {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80%;
}

.unreadBadge {
  background-color: #7e3af2;
  color: white;
  font-size: 12px;
  font-weight: 600;
  min-width: 20px;
  height: 20px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6px;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #6b7280;
}

.loadingSpinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #7e3af2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 15px;
  margin: 15px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.errorContainer button {
  background-color: #b91c1c;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #6b7280;
  text-align: center;
}

.startChatButton {
  margin-top: 15px;
  background-color: #7e3af2;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.startChatButton:hover {
  background-color: #6c2ce9;
}

/* Responsive styles */
@media (max-width: 768px) {
  .conversationList {
    max-height: calc(100vh - 120px);
    border-radius: 0;
    box-shadow: none;
  }
}
