/**
 * Production Configuration
 * 
 * This file manages the transition between mock data and real backend services
 * for production deployment. It provides a centralized way to control feature flags
 * and service endpoints.
 */

// Environment detection
const isProduction = process.env.NODE_ENV === 'production';
const isDevelopment = process.env.NODE_ENV === 'development';

// Backend service URLs
const BACKEND_SERVICES = {
  // Main API server
  API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api',
  
  // ML Matching service
  ML_SERVICE_URL: process.env.NEXT_PUBLIC_ML_SERVICE_URL || 'http://localhost:8000/api/matches',
  
  // Chat service
  CHAT_SERVICE_URL: process.env.NEXT_PUBLIC_CHAT_SERVICE_URL || 'http://localhost:8000/api/chat',
  
  // Notification service
  NOTIFICATION_SERVICE_URL: process.env.NEXT_PUBLIC_NOTIFICATION_SERVICE_URL || 'http://localhost:8000/api/notifications',
  
  // Payment service
  PAYMENT_SERVICE_URL: process.env.NEXT_PUBLIC_PAYMENT_SERVICE_URL || 'http://localhost:8000/api/payments',
  
  // Socket.IO server
  SOCKET_URL: process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:8000'
};

// Feature flags for production readiness
const PRODUCTION_FEATURES = {
  // Core features
  useRealBackend: isProduction || process.env.NEXT_PUBLIC_USE_REAL_BACKEND === 'true',
  useRealAuth: isProduction || process.env.NEXT_PUBLIC_USE_REAL_AUTH === 'true',
  useRealDatabase: isProduction || process.env.NEXT_PUBLIC_USE_REAL_DATABASE === 'true',
  
  // AI/ML features
  useRealMatching: isProduction || process.env.NEXT_PUBLIC_USE_REAL_MATCHING === 'true',
  enableMLService: isProduction || process.env.NEXT_PUBLIC_ENABLE_ML_SERVICE === 'true',
  
  // Communication features
  useRealChat: isProduction || process.env.NEXT_PUBLIC_USE_REAL_CHAT === 'true',
  useRealNotifications: isProduction || process.env.NEXT_PUBLIC_USE_REAL_NOTIFICATIONS === 'true',
  enableSocketIO: isProduction || process.env.NEXT_PUBLIC_ENABLE_SOCKET_IO === 'true',
  
  // Payment features
  useRealPayments: isProduction || process.env.NEXT_PUBLIC_USE_REAL_PAYMENTS === 'true',
  enableRazorpay: isProduction || process.env.NEXT_PUBLIC_ENABLE_RAZORPAY === 'true',
  
  // Premium features
  enablePremiumFeatures: true,
  enableBiodataTemplates: true,
  enableSpotlightFeatures: true,
  enableAdvancedSearch: true,
  
  // Security features
  enableContactRevealSecurity: true,
  enableFraudDetection: true,
  enableVerificationQueue: true,
  
  // Analytics and monitoring
  enableAnalytics: isProduction,
  enableErrorTracking: isProduction,
  enablePerformanceMonitoring: isProduction
};

// Mock data configuration
const MOCK_DATA_CONFIG = {
  // Enable mock data when real backend is not available
  useMockData: !PRODUCTION_FEATURES.useRealBackend,
  
  // Mock data settings
  mockDelay: isDevelopment ? 1000 : 0, // Simulate network delay in development
  mockDataPath: '/mock-data',
  
  // Fallback to mock data on API failures
  enableMockFallback: isDevelopment,
  
  // Mock data endpoints
  mockEndpoints: {
    dashboard: '/api/admin/dashboard',
    users: '/api/admin/users',
    matches: '/api/admin/matches',
    analytics: '/api/admin/success-analytics',
    algorithmSettings: '/api/admin/algorithm-settings',
    charts: '/api/admin/charts',
    notifications: '/api/admin/notifications',
    payments: '/api/admin/payments'
  }
};

// Service health check configuration
const HEALTH_CHECK_CONFIG = {
  // Health check intervals (in milliseconds)
  checkInterval: 30000, // 30 seconds
  retryInterval: 5000,  // 5 seconds
  
  // Timeout settings
  healthCheckTimeout: 5000, // 5 seconds
  
  // Health check endpoints
  healthEndpoints: {
    api: `${BACKEND_SERVICES.API_BASE_URL}/health`,
    ml: `${BACKEND_SERVICES.ML_SERVICE_URL}/health`,
    chat: `${BACKEND_SERVICES.CHAT_SERVICE_URL}/health`,
    notifications: `${BACKEND_SERVICES.NOTIFICATION_SERVICE_URL}/health`
  }
};

// Production deployment configuration
const DEPLOYMENT_CONFIG = {
  // Environment
  environment: process.env.NODE_ENV || 'development',
  
  // Domain configuration
  domain: process.env.NEXT_PUBLIC_DOMAIN || 'localhost:3000',
  apiDomain: process.env.NEXT_PUBLIC_API_DOMAIN || 'localhost:8000',
  
  // SSL configuration
  useSSL: isProduction,
  
  // CDN configuration
  cdnUrl: process.env.NEXT_PUBLIC_CDN_URL || '',
  
  // Database configuration
  databaseUrl: process.env.DATABASE_URL || '',
  redisUrl: process.env.REDIS_URL || '',
  
  // External services
  razorpayKeyId: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID || '',
  googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '',
  
  // Email service
  emailService: process.env.EMAIL_SERVICE || 'brevo',
  
  // SMS service
  smsService: process.env.SMS_SERVICE || 'msg91'
};

// Utility functions for production readiness
const ProductionUtils = {
  // Check if a feature is enabled
  isFeatureEnabled: (featureName) => {
    return PRODUCTION_FEATURES[featureName] || false;
  },
  
  // Get service URL based on environment
  getServiceUrl: (serviceName) => {
    return BACKEND_SERVICES[serviceName] || '';
  },
  
  // Check if we should use mock data
  shouldUseMockData: () => {
    if (typeof window !== 'undefined') {
      const storedValue = localStorage.getItem('useMockData');
      return storedValue === 'true' || MOCK_DATA_CONFIG.useMockData;
    }
    return MOCK_DATA_CONFIG.useMockData;
  },
  
  // Toggle between mock and real data
  toggleDataSource: () => {
    if (typeof window !== 'undefined') {
      const currentValue = localStorage.getItem('useMockData') === 'true';
      const newValue = !currentValue;
      localStorage.setItem('useMockData', newValue.toString());
      window.location.reload();
    }
  },
  
  // Get current data source status
  getDataSourceStatus: () => {
    const useMockData = ProductionUtils.shouldUseMockData();
    return {
      useMockData,
      label: useMockData ? 'Mock Data' : 'Real Backend',
      color: useMockData ? 'warning' : 'success',
      description: useMockData 
        ? 'Using mock data for development/testing' 
        : 'Connected to real backend services'
    };
  },
  
  // Check if environment is production ready
  isProductionReady: () => {
    const requiredFeatures = [
      'useRealBackend',
      'useRealAuth',
      'useRealDatabase'
    ];
    
    return requiredFeatures.every(feature => 
      PRODUCTION_FEATURES[feature]
    );
  },
  
  // Get production readiness report
  getProductionReadinessReport: () => {
    const checks = {
      backend: PRODUCTION_FEATURES.useRealBackend,
      auth: PRODUCTION_FEATURES.useRealAuth,
      database: PRODUCTION_FEATURES.useRealDatabase,
      ml: PRODUCTION_FEATURES.useRealMatching,
      chat: PRODUCTION_FEATURES.useRealChat,
      notifications: PRODUCTION_FEATURES.useRealNotifications,
      payments: PRODUCTION_FEATURES.useRealPayments
    };
    
    const passed = Object.values(checks).filter(Boolean).length;
    const total = Object.keys(checks).length;
    const percentage = Math.round((passed / total) * 100);
    
    return {
      checks,
      passed,
      total,
      percentage,
      isReady: percentage >= 80 // 80% threshold for production readiness
    };
  }
};

export {
  BACKEND_SERVICES,
  PRODUCTION_FEATURES,
  MOCK_DATA_CONFIG,
  HEALTH_CHECK_CONFIG,
  DEPLOYMENT_CONFIG,
  ProductionUtils
};

export default {
  BACKEND_SERVICES,
  PRODUCTION_FEATURES,
  MOCK_DATA_CONFIG,
  HEALTH_CHECK_CONFIG,
  DEPLOYMENT_CONFIG,
  ProductionUtils
};
