/**
 * Utility functions for error handling and logging
 */

// Prisma error codes
const PRISMA_ERROR_CODES = {
  P2000: 'The provided value for the column is too long',
  P2001: 'The record searched for does not exist',
  P2002: 'Unique constraint failed',
  P2003: 'Foreign key constraint failed',
  P2004: 'A constraint failed',
  P2005: 'The value stored in the database is invalid for the field type',
  P2006: 'The provided value is not valid',
  P2007: 'Data validation error',
  P2008: 'Failed to parse the query',
  P2009: 'Failed to validate the query',
  P2010: 'Raw query failed',
  P2011: 'Null constraint violation',
  P2012: 'Missing required value',
  P2013: 'Missing required argument',
  P2014: 'The change you are trying to make would violate the required relation',
  P2015: 'A related record could not be found',
  P2016: 'Query interpretation error',
  P2017: 'The records for relation are not connected',
  P2018: 'The required connected records were not found',
  P2019: 'Input error',
  P2020: 'Value out of range for the type',
  P2021: 'The table does not exist in the current database',
  P2022: 'The column does not exist in the current database',
  P2023: 'Inconsistent column data',
  P2024: 'Timed out fetching a connection from the connection pool',
  P2025: 'Record not found',
  P2026: 'The current database provider doesn\'t support a feature',
  P2027: 'Multiple errors occurred during the operation',
  P2028: 'Transaction API error',
  P2030: 'Cannot find a fulltext index to use for the search',
  P2033: 'Number of nested queries exceeds the maximum allowed',
  P2034: 'Transaction failed due to a write conflict or a deadlock',
};

/**
 * Log error details to console
 * @param {Error} error - The error object
 * @param {string} context - Context where the error occurred
 */
export const logError = (error, context) => {
  console.error(`[ERROR] ${context}:`, {
    message: error.message,
    code: error.code,
    meta: error.meta,
    stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    timestamp: new Date().toISOString(),
  });
};

/**
 * Handle Prisma database errors
 * @param {Error} error - The error object
 * @param {object} res - Express response object
 * @returns {object} - Response with appropriate status and message
 */
export const handleDatabaseError = (error, res) => {
  logError(error, 'Database operation');

  // Handle specific Prisma error codes
  if (error.code && PRISMA_ERROR_CODES[error.code]) {
    switch (error.code) {
      case 'P2002': // Unique constraint failed
        return res.status(409).json({
          success: false,
          message: 'A record with this unique constraint already exists',
          error: {
            code: error.code,
            fields: error.meta?.target || [],
          },
        });

      case 'P2003': // Foreign key constraint failed
        return res.status(400).json({
          success: false,
          message: 'Foreign key constraint failed',
          error: {
            code: error.code,
            field: error.meta?.field_name || '',
          },
        });

      case 'P2025': // Record not found
        return res.status(404).json({
          success: false,
          message: 'Record not found',
          error: {
            code: error.code,
            details: error.meta?.cause || '',
          },
        });

      case 'P2014': // Relation violation
        return res.status(400).json({
          success: false,
          message: 'The change would violate a required relation',
          error: {
            code: error.code,
            details: error.meta?.cause || '',
          },
        });

      case 'P2024': // Connection pool timeout
        return res.status(503).json({
          success: false,
          message: 'Database connection timeout',
          error: {
            code: error.code,
          },
        });

      default:
        // Handle other Prisma errors with known codes
        return res.status(400).json({
          success: false,
          message: PRISMA_ERROR_CODES[error.code] || 'Database operation failed',
          error: {
            code: error.code,
          },
        });
    }
  }

  // Handle other database errors
  return res.status(500).json({
    success: false,
    message: 'Database operation failed',
    error: process.env.NODE_ENV === 'development' 
      ? { message: error.message } 
      : undefined,
  });
};

/**
 * Handle API errors
 * @param {Error} error - The error object
 * @param {object} res - Express response object
 * @param {string} context - Context where the error occurred
 * @returns {object} - Response with appropriate status and message
 */
export const handleApiError = (error, res, context) => {
  logError(error, context);

  // Check if it's a database error
  if (error.code && PRISMA_ERROR_CODES[error.code]) {
    return handleDatabaseError(error, res);
  }

  // Handle other types of errors
  return res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' 
      ? { message: error.message } 
      : undefined,
  });
};
