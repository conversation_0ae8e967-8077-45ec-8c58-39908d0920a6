/**
 * Generate API Documentation
 * 
 * This script generates OpenAPI documentation for the API.
 */

// Load environment variables
require('dotenv').config();

// Set environment
process.env.NODE_ENV = 'development';

// Import dependencies
const path = require('path');
const fs = require('fs');
const { generateOpenApiSpec, saveOpenApiSpec } = require('../src/utils/openApiGenerator');

// Output directory
const outputDir = path.join(__dirname, '../public/api-docs');

// Create output directory if it doesn't exist
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Generate OpenAPI specification
console.log('Generating OpenAPI specification...');
const spec = generateOpenApiSpec();

// Save specification to file
const outputPath = path.join(outputDir, 'openapi.json');
saveOpenApiSpec(spec, outputPath);

// Generate HTML documentation
console.log('Generating HTML documentation...');
const htmlOutputPath = path.join(outputDir, 'index.html');
const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vaivahik API Documentation</title>
  <link rel="stylesheet" href="https://unpkg.com/swagger-ui-dist@4.5.0/swagger-ui.css" />
  <style>
    body {
      margin: 0;
      padding: 0;
    }
    #swagger-ui {
      max-width: 1200px;
      margin: 0 auto;
    }
    .topbar {
      background-color: #6200ea !important;
    }
  </style>
</head>
<body>
  <div id="swagger-ui"></div>
  <script src="https://unpkg.com/swagger-ui-dist@4.5.0/swagger-ui-bundle.js"></script>
  <script src="https://unpkg.com/swagger-ui-dist@4.5.0/swagger-ui-standalone-preset.js"></script>
  <script>
    window.onload = function() {
      window.ui = SwaggerUIBundle({
        url: "./openapi.json",
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "BaseLayout",
        defaultModelsExpandDepth: 1,
        defaultModelExpandDepth: 1,
        defaultModelRendering: 'model',
        displayRequestDuration: true,
        docExpansion: 'list',
        filter: true,
        showExtensions: true,
        showCommonExtensions: true,
        syntaxHighlight: {
          activate: true,
          theme: "agate"
        }
      });
    };
  </script>
</body>
</html>
`;

fs.writeFileSync(htmlOutputPath, htmlContent);

console.log('Documentation generated successfully!');
console.log(`OpenAPI specification: ${outputPath}`);
console.log(`HTML documentation: ${htmlOutputPath}`);
console.log('You can access the documentation at http://localhost:5000/api-docs/');
