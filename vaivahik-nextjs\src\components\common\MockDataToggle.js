import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>ton, Box, Typography, Switch, FormControlLabel } from '@mui/material';
import { styled } from '@mui/material/styles';
import { mockDataUtils } from '@/config/apiConfig';

// Fallback functions if featureFlags utils don't exist
const isUsingRealBackend = () => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('useMockData') !== 'true';
  }
  return true;
};

const toggleBackendMode = () => {
  if (typeof window !== 'undefined') {
    const currentValue = localStorage.getItem('useMockData') === 'true';
    const newValue = !currentValue;
    localStorage.setItem('useMockData', newValue.toString());
    return !newValue; // Return isRealBackend value
  }
  return true;
};

const StyledAlert = styled(Alert)(({ theme }) => ({
  margin: theme.spacing(2),
  borderRadius: theme.spacing(1),
  '& .MuiAlert-message': {
    width: '100%'
  }
}));

const ToggleContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  marginTop: theme.spacing(1),
  padding: theme.spacing(1),
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.spacing(0.5),
  border: `1px solid ${theme.palette.divider}`
}));

const MockDataToggle = ({ showInProduction = false }) => {
  const [isRealBackend, setIsRealBackend] = useState(true);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setIsRealBackend(isUsingRealBackend());
  }, []);

  // Don't show in production unless explicitly allowed
  if (process.env.NODE_ENV === 'production' && !showInProduction) {
    return null;
  }

  const handleToggle = async () => {
    setLoading(true);
    try {
      const newValue = toggleBackendMode();
      setIsRealBackend(newValue);
      
      // Show a brief loading state before reload
      setTimeout(() => {
        window.location.reload();
      }, 500);
    } catch (error) {
      console.error('Error toggling backend mode:', error);
      setLoading(false);
    }
  };

  const getAlertSeverity = () => {
    if (process.env.NODE_ENV === 'production') {
      return 'warning';
    }
    return isRealBackend ? 'success' : 'info';
  };

  const getAlertMessage = () => {
    if (process.env.NODE_ENV === 'production') {
      return 'Production Mode: Using real backend API and database.';
    }
    
    if (isRealBackend) {
      return 'Development Mode: Using real backend API. Your data will be stored permanently.';
    } else {
      return 'Development Mode: Using mock data. Your information will not be stored permanently.';
    }
  };

  const getToggleLabel = () => {
    return isRealBackend ? 'Switch to Mock Data' : 'Switch to Real Data';
  };

  return (
    <StyledAlert severity={getAlertSeverity()}>
      <Box>
        <Typography variant="body2" sx={{ fontWeight: 'medium', mb: 1 }}>
          🔧 Data Source Configuration
        </Typography>
        
        <Typography variant="body2" sx={{ mb: 2 }}>
          {getAlertMessage()}
        </Typography>

        {process.env.NODE_ENV === 'development' && (
          <ToggleContainer>
            <Box>
              <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                Backend Mode:
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {isRealBackend ? 'Real API (localhost:8000)' : 'Mock Data (Static)'}
              </Typography>
            </Box>
            
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={isRealBackend}
                    onChange={handleToggle}
                    disabled={loading}
                    color="primary"
                  />
                }
                label={isRealBackend ? 'Real' : 'Mock'}
                labelPlacement="start"
              />
              
              <Button
                variant="outlined"
                size="small"
                onClick={handleToggle}
                disabled={loading}
                sx={{ minWidth: '140px' }}
              >
                {loading ? 'Switching...' : getToggleLabel()}
              </Button>
            </Box>
          </ToggleContainer>
        )}

        {process.env.NODE_ENV === 'development' && (
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            💡 Tip: Toggle between mock and real data to test different scenarios. 
            Mock data is perfect for UI testing without affecting the database.
          </Typography>
        )}

        {process.env.NODE_ENV === 'production' && (
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            ⚠️ If you see this message in production, please contact support immediately.
          </Typography>
        )}
      </Box>
    </StyledAlert>
  );
};

export default MockDataToggle;
