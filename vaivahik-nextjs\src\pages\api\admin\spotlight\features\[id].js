// src/pages/api/admin/spotlight/features/[id].js
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../auth/[...nextauth]';

export default async function handler(req, res) {
  // Check if user is authenticated and is an admin
  const session = await getServerSession(req, res, authOptions);
  
  if (!session || !session.user || !session.user.isAdmin) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  // Get feature ID from the URL
  const { id } = req.query;
  
  if (!id) {
    return res.status(400).json({ success: false, message: 'Feature ID is required' });
  }

  // Handle GET request - fetch a specific spotlight feature
  if (req.method === 'GET') {
    try {
      // Fetch feature from backend API
      const response = await fetch(`${process.env.BACKEND_API_URL}/api/admin/spotlight/features/${id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.accessToken}`
        }
      }).catch(() => null);
      
      if (response && response.ok) {
        const data = await response.json();
        return res.status(200).json({
          success: true,
          message: data.message,
          feature: data.feature
        });
      } else {
        // Fallback to mock data if backend API fails
        const mockFeature = getMockFeatureById(id);
        
        if (!mockFeature) {
          return res.status(404).json({
            success: false,
            message: 'Feature not found'
          });
        }
        
        return res.status(200).json({
          success: true,
          message: 'Spotlight feature fetched successfully',
          feature: mockFeature
        });
      }
    } catch (error) {
      console.error('Error fetching spotlight feature:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch spotlight feature',
        error: error.message
      });
    }
  }
  
  // Handle PUT request - update a spotlight feature
  if (req.method === 'PUT') {
    try {
      const featureData = req.body;
      
      // Update feature in backend API
      const response = await fetch(`${process.env.BACKEND_API_URL}/api/admin/spotlight/features/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.accessToken}`
        },
        body: JSON.stringify(featureData)
      }).catch(() => null);
      
      if (response && response.ok) {
        const data = await response.json();
        return res.status(200).json({
          success: true,
          message: 'Spotlight feature updated successfully',
          feature: data.feature
        });
      } else {
        // Fallback if backend API fails
        const mockFeature = getMockFeatureById(id);
        
        if (!mockFeature) {
          return res.status(404).json({
            success: false,
            message: 'Feature not found'
          });
        }
        
        const updatedFeature = {
          ...mockFeature,
          ...featureData,
          updatedAt: new Date().toISOString()
        };
        
        // Recalculate discounted price if price or discount percent changed
        if ((featureData.price !== undefined || featureData.discountPercent !== undefined) && updatedFeature.discountPercent) {
          updatedFeature.discountedPrice = updatedFeature.price * (1 - updatedFeature.discountPercent / 100);
        }
        
        return res.status(200).json({
          success: true,
          message: 'Spotlight feature updated successfully',
          feature: updatedFeature
        });
      }
    } catch (error) {
      console.error('Error updating spotlight feature:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to update spotlight feature',
        error: error.message
      });
    }
  }
  
  // Handle DELETE request - delete a spotlight feature
  if (req.method === 'DELETE') {
    try {
      // Delete feature from backend API
      const response = await fetch(`${process.env.BACKEND_API_URL}/api/admin/spotlight/features/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${session.accessToken}`
        }
      }).catch(() => null);
      
      if (response && response.ok) {
        const data = await response.json();
        return res.status(200).json({
          success: true,
          message: 'Spotlight feature deleted successfully'
        });
      } else {
        // Fallback if backend API fails
        const mockFeature = getMockFeatureById(id);
        
        if (!mockFeature) {
          return res.status(404).json({
            success: false,
            message: 'Feature not found'
          });
        }
        
        // Check if feature has active users
        if (mockFeature.activeCount > 0) {
          return res.status(400).json({
            success: false,
            message: 'Cannot delete feature as it is currently in use by users',
            activeCount: mockFeature.activeCount
          });
        }
        
        return res.status(200).json({
          success: true,
          message: 'Spotlight feature deleted successfully'
        });
      }
    } catch (error) {
      console.error('Error deleting spotlight feature:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to delete spotlight feature',
        error: error.message
      });
    }
  }
  
  // Handle unsupported methods
  return res.status(405).json({ success: false, message: 'Method not allowed' });
}

// Helper function to get mock feature by ID
function getMockFeatureById(id) {
  const features = [
    {
      id: 'feature-1',
      name: 'Premium Spotlight',
      description: 'Get your profile featured at the top of search results for 24 hours',
      price: 499,
      discountPercent: 10,
      discountedPrice: 449.1,
      durationHours: 24,
      defaultCount: 1,
      isActive: true,
      purchaseCount: 150,
      activeCount: 12,
      revenue: 67365,
      createdAt: '2023-01-15T10:30:00Z',
      updatedAt: '2023-02-20T14:15:00Z'
    },
    {
      id: 'feature-2',
      name: 'Super Spotlight',
      description: 'Get your profile featured in the spotlight section for 48 hours',
      price: 799,
      discountPercent: null,
      discountedPrice: null,
      durationHours: 48,
      defaultCount: 1,
      isActive: true,
      purchaseCount: 85,
      activeCount: 8,
      revenue: 67915,
      createdAt: '2023-02-10T09:45:00Z',
      updatedAt: '2023-02-10T09:45:00Z'
    },
    {
      id: 'feature-3',
      name: 'Weekend Spotlight',
      description: 'Get your profile featured throughout the weekend (Friday to Sunday)',
      price: 999,
      discountPercent: 15,
      discountedPrice: 849.15,
      durationHours: 72,
      defaultCount: 1,
      isActive: true,
      purchaseCount: 65,
      activeCount: 5,
      revenue: 55194.75,
      createdAt: '2023-03-05T11:20:00Z',
      updatedAt: '2023-04-10T16:30:00Z'
    },
    {
      id: 'feature-4',
      name: 'Quick Boost',
      description: 'A short 12-hour boost to get more visibility',
      price: 299,
      discountPercent: null,
      discountedPrice: null,
      durationHours: 12,
      defaultCount: 1,
      isActive: false,
      purchaseCount: 40,
      activeCount: 0,
      revenue: 11960,
      createdAt: '2023-04-20T13:10:00Z',
      updatedAt: '2023-05-15T10:05:00Z'
    },
    {
      id: 'feature-5',
      name: 'Spotlight Pack',
      description: 'Get 3 spotlight features to use whenever you want',
      price: 1199,
      discountPercent: 20,
      discountedPrice: 959.2,
      durationHours: 24,
      defaultCount: 3,
      isActive: true,
      purchaseCount: 55,
      activeCount: 10,
      revenue: 52756,
      createdAt: '2023-05-25T15:40:00Z',
      updatedAt: '2023-05-25T15:40:00Z'
    },
    {
      id: 'feature-6',
      name: 'Festival Special',
      description: 'Special spotlight during festival season with extended visibility',
      price: 1499,
      discountPercent: 25,
      discountedPrice: 1124.25,
      durationHours: 96,
      defaultCount: 1,
      isActive: true,
      purchaseCount: 30,
      activeCount: 6,
      revenue: 33727.5,
      createdAt: '2023-06-10T09:15:00Z',
      updatedAt: '2023-07-05T11:30:00Z'
    }
  ];
  
  return features.find(f => f.id === id);
}
