/**
 * Error Handling Utilities
 * 
 * This module provides utilities for handling errors consistently across the application.
 * It includes functions for formatting error messages, handling API errors, and validation.
 */

// Error types for categorizing different errors
export const ERROR_TYPES = {
  VALIDATION: 'validation_error',
  NETWORK: 'network_error',
  SERVER: 'server_error',
  AUTH: 'authentication_error',
  NOT_FOUND: 'not_found_error',
  PERMISSION: 'permission_error',
  UNKNOWN: 'unknown_error'
};

// Map HTTP status codes to error types
const statusToErrorType = {
  400: ERROR_TYPES.VALIDATION,
  401: ERROR_TYPES.AUTH,
  403: ERROR_TYPES.PERMISSION,
  404: ERROR_TYPES.NOT_FOUND,
  422: ERROR_TYPES.VALIDATION,
  500: ERROR_TYPES.SERVER,
  502: ERROR_TYPES.NETWORK,
  503: ERROR_TYPES.NETWORK,
  504: ERROR_TYPES.NETWORK
};

/**
 * Format an error object into a standardized structure
 * @param {Error|Object} error - The error object
 * @param {string} defaultMessage - Default message if none is provided
 * @returns {Object} Formatted error object
 */
export const formatError = (error, defaultMessage = 'An unexpected error occurred') => {
  // If it's already in our format, return as is
  if (error && error.type && error.message) {
    return error;
  }

  // Handle Axios errors
  if (error && error.isAxiosError) {
    const status = error.response?.status;
    const serverMessage = error.response?.data?.message || error.response?.data?.error;
    const type = statusToErrorType[status] || ERROR_TYPES.UNKNOWN;
    
    // Handle validation errors with field-specific messages
    if (type === ERROR_TYPES.VALIDATION && error.response?.data?.errors) {
      return {
        type,
        message: serverMessage || 'Validation failed. Please check your input.',
        errors: error.response.data.errors,
        status
      };
    }
    
    return {
      type,
      message: serverMessage || `Network error (${status}): ${error.message}`,
      status
    };
  }

  // Handle regular Error objects
  if (error instanceof Error) {
    return {
      type: ERROR_TYPES.UNKNOWN,
      message: error.message || defaultMessage
    };
  }

  // Handle string errors
  if (typeof error === 'string') {
    return {
      type: ERROR_TYPES.UNKNOWN,
      message: error
    };
  }

  // Default case
  return {
    type: ERROR_TYPES.UNKNOWN,
    message: defaultMessage,
    originalError: error
  };
};

/**
 * Get a user-friendly error message based on error type and details
 * @param {Object} error - Formatted error object
 * @returns {string} User-friendly error message
 */
export const getUserFriendlyMessage = (error) => {
  const formattedError = formatError(error);
  
  switch (formattedError.type) {
    case ERROR_TYPES.VALIDATION:
      if (formattedError.errors && Object.keys(formattedError.errors).length > 0) {
        // Return the first field-specific error message
        const firstField = Object.keys(formattedError.errors)[0];
        return `${firstField}: ${formattedError.errors[firstField]}`;
      }
      return formattedError.message || 'Please check your input and try again.';
      
    case ERROR_TYPES.NETWORK:
      return formattedError.message || 'Network connection issue. Please check your internet connection and try again.';
      
    case ERROR_TYPES.SERVER:
      return 'The server encountered an error. Our team has been notified.';
      
    case ERROR_TYPES.AUTH:
      return 'Authentication failed. Please log in again.';
      
    case ERROR_TYPES.NOT_FOUND:
      return 'The requested resource was not found.';
      
    case ERROR_TYPES.PERMISSION:
      return 'You do not have permission to perform this action.';
      
    default:
      return formattedError.message || 'An unexpected error occurred. Please try again later.';
  }
};

/**
 * Get field-specific validation error messages
 * @param {Object} error - Formatted error object
 * @returns {Object} Field-specific error messages
 */
export const getValidationErrors = (error) => {
  const formattedError = formatError(error);
  
  if (formattedError.type === ERROR_TYPES.VALIDATION && formattedError.errors) {
    return formattedError.errors;
  }
  
  return {};
};

/**
 * Check if an error is a network error
 * @param {Object} error - Error object
 * @returns {boolean} True if it's a network error
 */
export const isNetworkError = (error) => {
  const formattedError = formatError(error);
  return formattedError.type === ERROR_TYPES.NETWORK;
};

/**
 * Check if an error is a validation error
 * @param {Object} error - Error object
 * @returns {boolean} True if it's a validation error
 */
export const isValidationError = (error) => {
  const formattedError = formatError(error);
  return formattedError.type === ERROR_TYPES.VALIDATION;
};

/**
 * Log an error to the console and potentially to a monitoring service
 * @param {Object} error - Error object
 * @param {string} context - Context where the error occurred
 */
export const logError = (error, context = '') => {
  const formattedError = formatError(error);
  
  console.error(`[${context}] ${formattedError.type}: ${formattedError.message}`, {
    error: formattedError,
    originalError: error,
    context
  });
  
  // Here you could add integration with error monitoring services like Sentry
  // if (typeof window !== 'undefined' && window.Sentry) {
  //   window.Sentry.captureException(error, { extra: { context } });
  // }
};

export default {
  formatError,
  getUserFriendlyMessage,
  getValidationErrors,
  isNetworkError,
  isValidationError,
  logError,
  ERROR_TYPES
};
