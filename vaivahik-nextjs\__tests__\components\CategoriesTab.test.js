/**
 * Component tests for CategoriesTab
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CategoriesTab from '@/components/admin/preferenceConfig/CategoriesTab';

// Mock data
const mockCategories = [
  {
    id: 'cat1',
    name: 'physical_attributes',
    displayName: 'Physical Attributes',
    description: 'Physical characteristics preferences',
    displayOrder: 1,
    icon: 'person',
    isActive: true,
    isRequired: true
  },
  {
    id: 'cat2',
    name: 'education_career',
    displayName: 'Education & Career',
    description: 'Education and career preferences',
    displayOrder: 2,
    icon: 'school',
    isActive: true,
    isRequired: false
  }
];

// Mock functions
const mockOnUpdate = jest.fn();
const mockOnDelete = jest.fn();

describe('CategoriesTab Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders categories correctly', () => {
    render(
      <CategoriesTab 
        categories={mockCategories} 
        onUpdate={mockOnUpdate} 
        onDelete={mockOnDelete} 
      />
    );

    // Check if categories are rendered
    expect(screen.getByText('Physical Attributes')).toBeInTheDocument();
    expect(screen.getByText('Education & Career')).toBeInTheDocument();
    expect(screen.getByText('Physical characteristics preferences')).toBeInTheDocument();
    expect(screen.getByText('Education and career preferences')).toBeInTheDocument();
  });

  test('opens add category dialog when add button is clicked', () => {
    render(
      <CategoriesTab 
        categories={mockCategories} 
        onUpdate={mockOnUpdate} 
        onDelete={mockOnDelete} 
      />
    );

    // Click add button
    fireEvent.click(screen.getByText('Add Category'));

    // Check if dialog is open
    expect(screen.getByText('Add Category')).toBeInTheDocument();
    expect(screen.getByLabelText('Internal Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Display Name')).toBeInTheDocument();
  });

  test('opens edit category dialog when edit button is clicked', () => {
    render(
      <CategoriesTab 
        categories={mockCategories} 
        onUpdate={mockOnUpdate} 
        onDelete={mockOnDelete} 
      />
    );

    // Find and click edit button for first category
    const editButtons = screen.getAllByRole('button', { name: /edit/i });
    fireEvent.click(editButtons[0]);

    // Check if dialog is open with correct values
    expect(screen.getByText('Edit Category')).toBeInTheDocument();
    expect(screen.getByDisplayValue('physical_attributes')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Physical Attributes')).toBeInTheDocument();
  });

  test('opens delete confirmation dialog when delete button is clicked', () => {
    render(
      <CategoriesTab 
        categories={mockCategories} 
        onUpdate={mockOnUpdate} 
        onDelete={mockOnDelete} 
      />
    );

    // Find and click delete button for first category
    const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
    fireEvent.click(deleteButtons[0]);

    // Check if confirmation dialog is open
    expect(screen.getByText('Confirm Delete')).toBeInTheDocument();
    expect(screen.getByText(/Are you sure you want to delete the category/)).toBeInTheDocument();
  });

  test('calls onDelete when delete is confirmed', async () => {
    render(
      <CategoriesTab 
        categories={mockCategories} 
        onUpdate={mockOnUpdate} 
        onDelete={mockOnDelete} 
      />
    );

    // Find and click delete button for first category
    const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
    fireEvent.click(deleteButtons[0]);

    // Confirm deletion
    fireEvent.click(screen.getByRole('button', { name: /delete$/i }));

    // Check if onDelete was called with correct ID
    expect(mockOnDelete).toHaveBeenCalledWith('cat1');
  });

  test('calls onUpdate when category is added', async () => {
    render(
      <CategoriesTab 
        categories={mockCategories} 
        onUpdate={mockOnUpdate} 
        onDelete={mockOnDelete} 
      />
    );

    // Click add button
    fireEvent.click(screen.getByText('Add Category'));

    // Fill in form
    fireEvent.change(screen.getByLabelText('Internal Name'), {
      target: { value: 'new_category' }
    });
    fireEvent.change(screen.getByLabelText('Display Name'), {
      target: { value: 'New Category' }
    });
    fireEvent.change(screen.getByLabelText('Description'), {
      target: { value: 'New category description' }
    });

    // Save the category
    fireEvent.click(screen.getByRole('button', { name: /save$/i }));

    // Check if onUpdate was called with updated categories
    expect(mockOnUpdate).toHaveBeenCalled();
    const updatedCategories = mockOnUpdate.mock.calls[0][0];
    expect(updatedCategories).toHaveLength(3);
    expect(updatedCategories[2].name).toBe('new_category');
    expect(updatedCategories[2].displayName).toBe('New Category');
  });

  test('calls onUpdate when category is edited', async () => {
    render(
      <CategoriesTab 
        categories={mockCategories} 
        onUpdate={mockOnUpdate} 
        onDelete={mockOnDelete} 
      />
    );

    // Find and click edit button for first category
    const editButtons = screen.getAllByRole('button', { name: /edit/i });
    fireEvent.click(editButtons[0]);

    // Change display name
    fireEvent.change(screen.getByDisplayValue('Physical Attributes'), {
      target: { value: 'Updated Physical Attributes' }
    });

    // Save the category
    fireEvent.click(screen.getByRole('button', { name: /save$/i }));

    // Check if onUpdate was called with updated categories
    expect(mockOnUpdate).toHaveBeenCalled();
    const updatedCategories = mockOnUpdate.mock.calls[0][0];
    expect(updatedCategories).toHaveLength(2);
    expect(updatedCategories[0].displayName).toBe('Updated Physical Attributes');
  });
});
