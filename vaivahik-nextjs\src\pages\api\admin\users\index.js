// API endpoint for all users
import axios from 'axios';
import { handleApiError } from '@/utils/errorHandler';
import { generateMockUsers } from '@/utils/mockData';

// Backend API URL - adjust this to your actual backend URL
const BACKEND_API_URL = 'http://localhost:3001/api';

export default function handler(req, res) {
  // Set proper content type header
  res.setHeader('Content-Type', 'application/json');

  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return getUsers(req, res);
      default:
        return res.status(405).json({
          success: false,
          message: 'Method not allowed'
        });
    }
  } catch (error) {
    console.error('Error in users API:', error);
    return handleApiError(error, res, 'Users API');
  }
}

// Get all users with filtering, search, and pagination
async function getUsers(req, res) {
  try {
    // Get query parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const status = req.query.status || '';
    const gender = req.query.gender || '';
    const verified = req.query.verified || '';
    const premium = req.query.premium || '';
    const sortBy = req.query.sortBy || 'registeredOn';
    const sortOrder = req.query.sortOrder || 'desc';

    // Construct the API URL with query parameters
    let apiUrl = `${BACKEND_API_URL}/admin/users?page=${page}&limit=${limit}`;

    // Add optional query parameters if they exist
    if (search) apiUrl += `&search=${encodeURIComponent(search)}`;
    if (status) apiUrl += `&status=${encodeURIComponent(status)}`;
    if (gender) apiUrl += `&gender=${encodeURIComponent(gender)}`;
    if (verified) apiUrl += `&verified=${encodeURIComponent(verified)}`;
    if (premium) apiUrl += `&premium=${encodeURIComponent(premium)}`;
    if (sortBy) apiUrl += `&sortBy=${encodeURIComponent(sortBy)}`;
    if (sortOrder) apiUrl += `&sortOrder=${encodeURIComponent(sortOrder)}`;

    try {
      // Try to fetch data from the backend API
      const response = await axios.get(apiUrl);

      // Return the response directly from the backend
      return res.status(200).json(response.data);
    } catch (apiError) {
      // Log the error
      console.warn('Error fetching users from backend API:', apiError.message);
      console.log('Using mock data instead');

      // Generate mock data
      const mockUsers = generateMockUsers();

      // Apply filtering to mock data
      let filteredUsers = [...mockUsers];

      // Apply search filter if provided
      if (search) {
        const searchLower = search.toLowerCase();
        filteredUsers = filteredUsers.filter(user =>
          user.name.toLowerCase().includes(searchLower) ||
          user.email.toLowerCase().includes(searchLower) ||
          user.phone.toLowerCase().includes(searchLower) ||
          user.location.toLowerCase().includes(searchLower)
        );
      }

      // Apply status filter if provided
      if (status && status !== 'all') {
        filteredUsers = filteredUsers.filter(user => user.status === status);
      }

      // Apply gender filter if provided
      if (gender && gender !== 'all') {
        filteredUsers = filteredUsers.filter(user => user.gender === gender);
      }

      // Apply verified filter if provided
      if (verified && verified !== 'all') {
        const isVerified = verified === 'true';
        filteredUsers = filteredUsers.filter(user => user.verified === isVerified);
      }

      // Apply premium filter if provided
      if (premium && premium !== 'all') {
        const isPremium = premium === 'true';
        filteredUsers = filteredUsers.filter(user => user.premium === isPremium);
      }

      // Calculate pagination
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

      // Return mock data with success status
      return res.status(200).json({
        success: true,
        users: paginatedUsers,
        pagination: {
          totalUsers: filteredUsers.length,
          page: page,
          limit: limit,
          totalPages: Math.ceil(filteredUsers.length / limit)
        },
        message: "Users retrieved successfully (mock data)"
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Get users');
  }
}
