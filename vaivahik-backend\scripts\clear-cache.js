/**
 * Redis Cache Clear Script
 * 
 * This script clears Redis cache by pattern or all keys.
 * Run it with: node scripts/clear-cache.js [pattern]
 * 
 * Examples:
 * - Clear all cache: node scripts/clear-cache.js
 * - Clear user cache: node scripts/clear-cache.js user:*
 * - Clear dashboard stats: node scripts/clear-cache.js dashboard:stats:*
 */

const redis = require('../redis/redisClient');
const { CACHE_PREFIXES } = require('../redis/cacheService');
const logger = require('../src/utils/logger');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  fg: {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
  }
};

/**
 * Clear cache by pattern
 * @param {string} pattern - Key pattern to clear
 * @returns {Promise<number>} Number of keys cleared
 */
async function clearCache(pattern = '*') {
  try {
    const client = await redis.getClient();
    
    // Get keys matching the pattern
    const keys = await client.keys(pattern);
    
    if (keys.length === 0) {
      console.log(`${colors.fg.yellow}No keys found matching pattern: ${pattern}${colors.reset}`);
      return 0;
    }
    
    // Delete the keys
    await client.del(keys);
    
    console.log(`${colors.fg.green}Successfully cleared ${keys.length} keys matching pattern: ${pattern}${colors.reset}`);
    return keys.length;
  } catch (error) {
    logger.error(`Error clearing cache with pattern ${pattern}:`, error);
    throw error;
  }
}

/**
 * Display available patterns
 */
function displayAvailablePatterns() {
  console.log(`\n${colors.bright}Available cache patterns:${colors.reset}`);
  
  for (const [name, prefix] of Object.entries(CACHE_PREFIXES)) {
    console.log(`  ${colors.fg.cyan}${name}${colors.reset}: ${prefix}*`);
  }
  
  console.log(`\n${colors.bright}Examples:${colors.reset}`);
  console.log(`  ${colors.fg.yellow}node scripts/clear-cache.js${colors.reset} - Clear all cache`);
  console.log(`  ${colors.fg.yellow}node scripts/clear-cache.js user:*${colors.reset} - Clear user cache`);
  console.log(`  ${colors.fg.yellow}node scripts/clear-cache.js dashboard:stats:*${colors.reset} - Clear dashboard stats`);
  console.log('');
}

// Run the script
(async () => {
  try {
    // Get pattern from command line arguments
    const pattern = process.argv[2] || '*';
    
    if (pattern === '--help' || pattern === '-h') {
      displayAvailablePatterns();
      process.exit(0);
    }
    
    console.log(`\n${colors.bright}${colors.fg.cyan}===== Redis Cache Clear =====${colors.reset}\n`);
    
    if (pattern === '*') {
      // Confirm before clearing all cache
      const readline = require('readline').createInterface({
        input: process.stdin,
        output: process.stdout
      });
      
      const answer = await new Promise(resolve => {
        readline.question(`${colors.fg.red}Are you sure you want to clear ALL cache? (y/N): ${colors.reset}`, resolve);
      });
      
      readline.close();
      
      if (answer.toLowerCase() !== 'y') {
        console.log(`${colors.fg.yellow}Operation cancelled.${colors.reset}`);
        process.exit(0);
      }
    }
    
    // Clear cache
    await clearCache(pattern);
    
    process.exit(0);
  } catch (error) {
    console.error(`${colors.fg.red}Error clearing cache:${colors.reset}`, error);
    process.exit(1);
  }
})();
