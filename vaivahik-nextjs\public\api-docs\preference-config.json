{"openapi": "3.0.0", "info": {"title": "Preference Configuration API", "description": "API for managing preference configuration in the matrimony app", "version": "1.0.0"}, "servers": [{"url": "/api", "description": "API server"}], "paths": {"/admin/preference-config": {"get": {"summary": "Get preference configuration", "description": "Retrieve preference configuration data", "tags": ["Preference Configuration"], "security": [{"bearerAuth": []}], "parameters": [{"name": "type", "in": "query", "description": "Type of data to retrieve", "required": false, "schema": {"type": "string", "enum": ["all", "categories", "fields", "options", "importance", "defaults"]}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "categories": {"type": "array", "items": {"$ref": "#/components/schemas/Category"}}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/Field"}}, "options": {"type": "array", "items": {"$ref": "#/components/schemas/Option"}}, "importanceSettings": {"type": "array", "items": {"$ref": "#/components/schemas/ImportanceSetting"}}, "defaultPreferences": {"type": "object", "example": {"ageMin": 21, "ageMax": 35, "heightMin": "5'0\"", "heightMax": "6'0\""}}}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "put": {"summary": "Update preference configuration", "description": "Update preference configuration data", "tags": ["Preference Configuration"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"type": {"type": "string", "enum": ["categories", "fields", "options", "importance", "defaults"], "description": "Type of data to update"}, "data": {"type": "array", "description": "Data to update"}}, "required": ["type", "data"]}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Categories updated successfully"}, "data": {"type": "array", "description": "Updated data"}}}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "delete": {"summary": "Delete preference item", "description": "Delete a preference item (category, field, option, or importance setting)", "tags": ["Preference Configuration"], "security": [{"bearerAuth": []}], "parameters": [{"name": "type", "in": "query", "description": "Type of item to delete", "required": true, "schema": {"type": "string", "enum": ["category", "field", "option", "importance"]}}, {"name": "id", "in": "query", "description": "ID of the item to delete", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Category deleted successfully"}}}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}}, "components": {"schemas": {"Category": {"type": "object", "properties": {"id": {"type": "string", "example": "cat1"}, "name": {"type": "string", "example": "physical_attributes"}, "displayName": {"type": "string", "example": "Physical Attributes"}, "description": {"type": "string", "example": "Physical characteristics preferences"}, "displayOrder": {"type": "integer", "example": 1}, "icon": {"type": "string", "example": "person"}, "isActive": {"type": "boolean", "example": true}, "isRequired": {"type": "boolean", "example": true}}}, "Field": {"type": "object", "properties": {"id": {"type": "string", "example": "field1"}, "name": {"type": "string", "example": "age_range"}, "displayName": {"type": "string", "example": "Age Range"}, "description": {"type": "string", "example": "Preferred age range of partner"}, "fieldType": {"type": "string", "example": "RANGE", "enum": ["TEXT", "NUMBER", "RANGE", "SELECT", "MULTI_SELECT", "BOOLEAN"]}, "displayOrder": {"type": "integer", "example": 1}, "isActive": {"type": "boolean", "example": true}, "isRequired": {"type": "boolean", "example": true}, "isSearchable": {"type": "boolean", "example": true}, "isMatchCriteria": {"type": "boolean", "example": true}, "defaultValue": {"type": "string", "example": "{\"min\": 21, \"max\": 35}"}, "validationRules": {"type": "string", "example": "{\"minValue\": 18, \"maxValue\": 70}"}, "minValue": {"type": "number", "example": 18}, "maxValue": {"type": "number", "example": 70}, "stepValue": {"type": "number", "example": 1}, "categoryId": {"type": "string", "example": "cat1"}}}, "Option": {"type": "object", "properties": {"id": {"type": "string", "example": "opt1"}, "value": {"type": "string", "example": "GRADUATE"}, "displayText": {"type": "string", "example": "Graduate"}, "description": {"type": "string", "example": "Bachelor's degree"}, "displayOrder": {"type": "integer", "example": 1}, "isActive": {"type": "boolean", "example": true}, "fieldId": {"type": "string", "example": "field3"}}}, "ImportanceSetting": {"type": "object", "properties": {"id": {"type": "string", "example": "imp1"}, "importanceLevel": {"type": "number", "example": 8.0}, "description": {"type": "string", "example": "Age is highly important for males"}, "isActive": {"type": "boolean", "example": true}, "fieldId": {"type": "string", "example": "field1"}, "gender": {"type": "string", "example": "MALE", "enum": ["MALE", "FEMALE", "ALL"]}}}, "Error": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized"}, "error": {"type": "object", "properties": {"code": {"type": "string", "example": "P2025"}, "details": {"type": "string", "example": "Record not found"}}}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}