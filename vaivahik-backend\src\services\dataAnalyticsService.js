/**
 * Data Analytics Service
 * Analyzes behavioral data volume for ML readiness assessment
 */

const { PrismaClient } = require('@prisma/client');

class DataAnalyticsService {
  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * Analyze current behavioral data volume for ML readiness
   */
  async analyzeBehavioralDataVolume() {
    try {
      console.log('🔍 Analyzing behavioral data volume for ML readiness...');

      // Get user interaction data
      const interactionStats = await this.getInteractionStats();
      
      // Get user feedback data
      const feedbackStats = await this.getFeedbackStats();
      
      // Get user engagement patterns
      const engagementStats = await this.getEngagementStats();
      
      // Assess ML readiness
      const mlReadiness = this.assessMLReadiness(interactionStats, feedbackStats, engagementStats);

      return {
        interactionStats,
        feedbackStats,
        engagementStats,
        mlReadiness,
        summary: this.generateSummary(interactionStats, feedbackStats, engagementStats, mlReadiness)
      };

    } catch (error) {
      console.error('Error analyzing behavioral data:', error);
      throw error;
    }
  }

  /**
   * Get user interaction statistics
   */
  async getInteractionStats() {
    // Total interactions
    const totalInteractions = await this.prisma.userInteraction.count();
    
    // Interactions by type
    const interactionsByType = await this.prisma.userInteraction.groupBy({
      by: ['interactionType'],
      _count: { id: true }
    });

    // Users with interactions
    const usersWithInteractions = await this.prisma.userInteraction.groupBy({
      by: ['userId'],
      _count: { id: true }
    });

    // Average interactions per user
    const avgInteractionsPerUser = usersWithInteractions.length > 0 
      ? totalInteractions / usersWithInteractions.length 
      : 0;

    // Users with sufficient data (>10 interactions)
    const usersWithSufficientData = usersWithInteractions.filter(user => user._count.id >= 10).length;

    // Recent interactions (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentInteractions = await this.prisma.userInteraction.count({
      where: {
        timestamp: { gte: thirtyDaysAgo }
      }
    });

    return {
      totalInteractions,
      interactionsByType: interactionsByType.reduce((acc, item) => {
        acc[item.interactionType] = item._count.id;
        return acc;
      }, {}),
      totalUsersWithInteractions: usersWithInteractions.length,
      avgInteractionsPerUser: Math.round(avgInteractionsPerUser * 100) / 100,
      usersWithSufficientData,
      recentInteractions,
      dataCollectionPeriod: this.calculateDataCollectionPeriod()
    };
  }

  /**
   * Get user feedback statistics
   */
  async getFeedbackStats() {
    // Total feedback entries
    const totalFeedback = await this.prisma.userFeedback.count();
    
    // Feedback by type
    const feedbackByType = await this.prisma.userFeedback.groupBy({
      by: ['feedbackType'],
      _count: { id: true }
    });

    // Average rating
    const avgRating = await this.prisma.userFeedback.aggregate({
      _avg: { rating: true }
    });

    // Users who provided feedback
    const usersWithFeedback = await this.prisma.userFeedback.groupBy({
      by: ['userId'],
      _count: { id: true }
    });

    return {
      totalFeedback,
      feedbackByType: feedbackByType.reduce((acc, item) => {
        acc[item.feedbackType] = item._count.id;
        return acc;
      }, {}),
      avgRating: avgRating._avg.rating || 0,
      totalUsersWithFeedback: usersWithFeedback.length
    };
  }

  /**
   * Get user engagement statistics
   */
  async getEngagementStats() {
    // Total users
    const totalUsers = await this.prisma.user.count();
    
    // Active users (users with recent activity)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const activeUsers = await this.prisma.user.count({
      where: {
        lastLoginAt: { gte: sevenDaysAgo }
      }
    });

    // Users with complete profiles
    const usersWithCompleteProfiles = await this.prisma.user.count({
      where: {
        profile: {
          isNot: null
        }
      }
    });

    return {
      totalUsers,
      activeUsers,
      usersWithCompleteProfiles,
      engagementRate: totalUsers > 0 ? (activeUsers / totalUsers * 100) : 0
    };
  }

  /**
   * Calculate data collection period
   */
  async calculateDataCollectionPeriod() {
    const oldestInteraction = await this.prisma.userInteraction.findFirst({
      orderBy: { timestamp: 'asc' },
      select: { timestamp: true }
    });

    if (!oldestInteraction) {
      return 0;
    }

    const now = new Date();
    const diffTime = Math.abs(now - oldestInteraction.timestamp);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  }

  /**
   * Assess ML readiness based on data volume
   */
  assessMLReadiness(interactionStats, feedbackStats, engagementStats) {
    const requirements = {
      v2_0: {
        minInteractionsPerUser: 50,
        minUsersWithSufficientData: 100,
        minTotalInteractions: 5000,
        minDataCollectionDays: 30
      },
      v2_5: {
        minInteractionsPerUser: 100,
        minUsersWithSufficientData: 500,
        minTotalInteractions: 50000,
        minDataCollectionDays: 90
      },
      v3_0: {
        minInteractionsPerUser: 200,
        minUsersWithSufficientData: 1000,
        minTotalInteractions: 200000,
        minDataCollectionDays: 180
      }
    };

    const readiness = {};

    Object.keys(requirements).forEach(phase => {
      const req = requirements[phase];
      const checks = {
        interactionsPerUser: interactionStats.avgInteractionsPerUser >= req.minInteractionsPerUser,
        usersWithSufficientData: interactionStats.usersWithSufficientData >= req.minUsersWithSufficientData,
        totalInteractions: interactionStats.totalInteractions >= req.minTotalInteractions,
        dataCollectionPeriod: interactionStats.dataCollectionPeriod >= req.minDataCollectionDays
      };

      const passedChecks = Object.values(checks).filter(Boolean).length;
      const totalChecks = Object.keys(checks).length;
      const readinessPercentage = (passedChecks / totalChecks) * 100;

      readiness[phase] = {
        isReady: passedChecks === totalChecks,
        readinessPercentage: Math.round(readinessPercentage),
        checks,
        requirements: req,
        current: {
          avgInteractionsPerUser: interactionStats.avgInteractionsPerUser,
          usersWithSufficientData: interactionStats.usersWithSufficientData,
          totalInteractions: interactionStats.totalInteractions,
          dataCollectionDays: interactionStats.dataCollectionPeriod
        }
      };
    });

    return readiness;
  }

  /**
   * Generate summary report
   */
  generateSummary(interactionStats, feedbackStats, engagementStats, mlReadiness) {
    const summary = {
      currentStatus: 'COLLECTING_DATA',
      dataQuality: 'GOOD',
      recommendations: [],
      nextSteps: []
    };

    // Determine current status
    if (mlReadiness.v2_0.isReady) {
      summary.currentStatus = 'READY_FOR_V2_0';
    } else if (mlReadiness.v2_0.readinessPercentage >= 75) {
      summary.currentStatus = 'ALMOST_READY_FOR_V2_0';
    } else if (interactionStats.totalInteractions > 1000) {
      summary.currentStatus = 'GOOD_DATA_COLLECTION';
    }

    // Generate recommendations
    if (interactionStats.avgInteractionsPerUser < 10) {
      summary.recommendations.push('Encourage more user interactions through gamification');
    }

    if (feedbackStats.totalFeedback < interactionStats.totalInteractions * 0.1) {
      summary.recommendations.push('Implement more feedback collection mechanisms');
    }

    if (engagementStats.engagementRate < 30) {
      summary.recommendations.push('Focus on user retention and engagement strategies');
    }

    // Generate next steps
    if (mlReadiness.v2_0.isReady) {
      summary.nextSteps.push('✅ Ready to implement Phase v2.0 - Personalized AI');
      summary.nextSteps.push('🚀 Start developing behavioral learning models');
    } else {
      summary.nextSteps.push('📊 Continue collecting behavioral data');
      summary.nextSteps.push('🎯 Focus on user engagement to increase interactions');
      
      const daysNeeded = Math.max(0, 30 - interactionStats.dataCollectionPeriod);
      if (daysNeeded > 0) {
        summary.nextSteps.push(`⏱️ Collect data for ${daysNeeded} more days`);
      }
    }

    return summary;
  }

  /**
   * Get detailed user behavior analysis
   */
  async getUserBehaviorAnalysis(userId) {
    const userInteractions = await this.prisma.userInteraction.findMany({
      where: { userId },
      orderBy: { timestamp: 'desc' },
      take: 100
    });

    const userFeedback = await this.prisma.userFeedback.findMany({
      where: { userId },
      orderBy: { timestamp: 'desc' }
    });

    return {
      totalInteractions: userInteractions.length,
      interactionTypes: this.groupByType(userInteractions, 'interactionType'),
      totalFeedback: userFeedback.length,
      avgRating: userFeedback.length > 0 
        ? userFeedback.reduce((sum, f) => sum + f.rating, 0) / userFeedback.length 
        : 0,
      isReadyForPersonalization: userInteractions.length >= 50
    };
  }

  /**
   * Helper function to group array by property
   */
  groupByType(array, property) {
    return array.reduce((acc, item) => {
      const key = item[property];
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {});
  }
}

module.exports = DataAnalyticsService;
