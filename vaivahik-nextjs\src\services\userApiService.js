/**
 * User API Service
 *
 * This service provides methods for making API requests to the user endpoints.
 * It uses the apiService for authenticated requests.
 */

import { get, post, put, del } from './apiService';
import { USER_ENDPOINTS } from '@/config/apiConfig';

/**
 * Get user profile
 * @returns {Promise<Object>} User profile data
 */
export const getUserProfile = async () => {
  return await get(USER_ENDPOINTS.PROFILE);
};

/**
 * Update user profile
 * @param {Object} profileData - Profile data to update
 * @returns {Promise<Object>} Updated profile data
 */
export const updateUserProfile = async (profileData) => {
  return await put(USER_ENDPOINTS.PROFILE, profileData);
};

/**
 * Update basic details
 * @param {Object} basicDetails - Basic details data
 * @returns {Promise<Object>} Updated basic details
 */
export const updateBasicDetails = async (basicDetails) => {
  return await put(USER_ENDPOINTS.BASIC_DETAILS, basicDetails);
};

/**
 * Update education and career details
 * @param {Object} educationCareer - Education and career data
 * @returns {Promise<Object>} Updated education and career details
 */
export const updateEducationCareer = async (educationCareer) => {
  return await put(USER_ENDPOINTS.EDUCATION_CAREER, educationCareer);
};

/**
 * Update location details
 * @param {Object} locationDetails - Location details data
 * @returns {Promise<Object>} Updated location details
 */
export const updateLocationDetails = async (locationDetails) => {
  return await put(USER_ENDPOINTS.LOCATION_DETAILS, locationDetails);
};

/**
 * Update family details
 * @param {Object} familyDetails - Family details data
 * @returns {Promise<Object>} Updated family details
 */
export const updateFamilyDetails = async (familyDetails) => {
  return await put(USER_ENDPOINTS.FAMILY_DETAILS, familyDetails);
};

/**
 * Update partner preferences
 * @param {Object} preferences - Partner preferences data
 * @returns {Promise<Object>} Updated preferences
 */
export const updatePartnerPreferences = async (preferences) => {
  return await put(USER_ENDPOINTS.PARTNER_PREFERENCES, preferences);
};

/**
 * Update lifestyle and habits
 * @param {Object} lifestyleHabits - Lifestyle and habits data
 * @returns {Promise<Object>} Updated lifestyle and habits
 */
export const updateLifestyleHabits = async (lifestyleHabits) => {
  return await put(USER_ENDPOINTS.LIFESTYLE_HABITS, lifestyleHabits);
};

/**
 * Update about me
 * @param {Object} aboutMe - About me data
 * @returns {Promise<Object>} Updated about me
 */
export const updateAboutMe = async (aboutMe) => {
  return await put(USER_ENDPOINTS.ABOUT_ME, aboutMe);
};

/**
 * Upload user photos
 * @param {FormData} formData - Form data with photos
 * @returns {Promise<Object>} Uploaded photos data
 */
export const uploadPhotos = async (formData) => {
  return await post(USER_ENDPOINTS.PHOTOS, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

/**
 * Get user photos
 * @returns {Promise<Object>} User photos
 */
export const getUserPhotos = async () => {
  return await get(USER_ENDPOINTS.PHOTOS);
};

/**
 * Set primary photo
 * @param {string} photoId - Photo ID
 * @returns {Promise<Object>} Updated photo data
 */
export const setPrimaryPhoto = async (photoId) => {
  return await put(USER_ENDPOINTS.SET_PRIMARY_PHOTO(photoId));
};

/**
 * Update photo visibility
 * @param {string} photoId - Photo ID
 * @param {boolean} isVisible - Visibility status
 * @returns {Promise<Object>} Updated photo data
 */
export const updatePhotoVisibility = async (photoId, isVisible) => {
  return await put(USER_ENDPOINTS.UPDATE_PHOTO_VISIBILITY(photoId), { isVisible });
};

/**
 * Delete photo
 * @param {string} photoId - Photo ID
 * @returns {Promise<Object>} Response data
 */
export const deletePhoto = async (photoId) => {
  return await del(USER_ENDPOINTS.PHOTO_DETAILS(photoId));
};

/**
 * Get user matches
 * @param {Object} params - Query parameters
 * @returns {Promise<Object>} Matches data
 */
export const getUserMatches = async (params = {}) => {
  return await get(USER_ENDPOINTS.MATCHES, params);
};

/**
 * Get user notifications
 * @param {Object} params - Query parameters
 * @returns {Promise<Object>} Notifications data
 */
export const getUserNotifications = async (params = {}) => {
  return await get(USER_ENDPOINTS.NOTIFICATIONS, params);
};

/**
 * Get user messages
 * @param {Object} params - Query parameters
 * @returns {Promise<Object>} Messages data
 */
export const getUserMessages = async (params = {}) => {
  return await get(USER_ENDPOINTS.MESSAGES, params);
};

/**
 * Get user conversations
 * @param {Object} params - Query parameters
 * @returns {Promise<Object>} Conversations data
 */
export const getUserConversations = async (params = {}) => {
  return await get(USER_ENDPOINTS.CONVERSATIONS, params);
};

/**
 * Search profiles
 * @param {Object} params - Search parameters
 * @returns {Promise<Object>} Search results
 */
export const searchProfiles = async (params = {}) => {
  return await get(USER_ENDPOINTS.SEARCH, params);
};

export default {
  getUserProfile,
  updateUserProfile,
  updateBasicDetails,
  updateEducationCareer,
  updateLocationDetails,
  updateFamilyDetails,
  updatePartnerPreferences,
  updateLifestyleHabits,
  updateAboutMe,
  uploadPhotos,
  getUserPhotos,
  setPrimaryPhoto,
  updatePhotoVisibility,
  deletePhoto,
  getUserMatches,
  getUserNotifications,
  getUserMessages,
  getUserConversations,
  searchProfiles
};
