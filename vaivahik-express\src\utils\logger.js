/**
 * Logger Utility
 * 
 * This utility provides consistent logging across the application.
 * It uses winston for logging with different levels and formats.
 */

const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define log level based on environment
const level = () => {
  const env = process.env.NODE_ENV || 'development';
  return env === 'development' ? 'debug' : 'info';
};

// Define log colors
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Add colors to winston
winston.addColors(colors);

// Define log format
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}${info.splat !== undefined ? `${info.splat}` : ''}${info.meta ? ` ${JSON.stringify(info.meta)}` : ''}`
  )
);

// Define log transports
const transports = [
  // Console transport
  new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize({ all: true }),
      winston.format.printf(
        (info) => `${info.timestamp} ${info.level}: ${info.message}${info.splat !== undefined ? `${info.splat}` : ''}${info.meta ? ` ${JSON.stringify(info.meta)}` : ''}`
      )
    ),
  }),
  
  // Error log file transport
  new winston.transports.File({
    filename: path.join(logsDir, 'error.log'),
    level: 'error',
  }),
  
  // Combined log file transport
  new winston.transports.File({
    filename: path.join(logsDir, 'combined.log'),
  }),
];

// Create the logger
const logger = winston.createLogger({
  level: level(),
  levels,
  format,
  transports,
});

/**
 * Log an error message
 * @param {string} message - Error message
 * @param {object} meta - Additional metadata
 */
const error = (message, meta = null) => {
  logger.error(message, { meta });
};

/**
 * Log a warning message
 * @param {string} message - Warning message
 * @param {object} meta - Additional metadata
 */
const warn = (message, meta = null) => {
  logger.warn(message, { meta });
};

/**
 * Log an info message
 * @param {string} message - Info message
 * @param {object} meta - Additional metadata
 */
const info = (message, meta = null) => {
  logger.info(message, { meta });
};

/**
 * Log an HTTP request
 * @param {string} message - HTTP message
 * @param {object} meta - Additional metadata
 */
const http = (message, meta = null) => {
  logger.http(message, { meta });
};

/**
 * Log a debug message
 * @param {string} message - Debug message
 * @param {object} meta - Additional metadata
 */
const debug = (message, meta = null) => {
  logger.debug(message, { meta });
};

module.exports = {
  error,
  warn,
  info,
  http,
  debug
};
