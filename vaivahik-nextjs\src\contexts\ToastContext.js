import React, { createContext, useState, useContext } from 'react';
import UndoToast from '@/components/common/UndoToast';
import GuidedErrorToast from '@/components/common/GuidedErrorToast';

// Create context
const ToastContext = createContext();

/**
 * Toast Provider Component
 *
 * Provides global toast notification functionality with undo support.
 * Wrap your application with this provider to enable toast notifications.
 */
export const ToastProvider = ({ children }) => {
  // State for regular toast
  const [toast, setToast] = useState({
    open: false,
    message: '',
    undoAction: null,
    duration: 6000,
    position: { vertical: 'bottom', horizontal: 'center' }
  });

  // State for guided error toast
  const [guidedToast, setGuidedToast] = useState({
    open: false,
    message: '',
    type: 'error',
    errorCode: '',
    steps: [],
    duration: 10000,
    position: { vertical: 'bottom', horizontal: 'right' },
    onHelp: null
  });

  /**
   * Show a toast notification
   * @param {string} message - Message to display
   * @param {Object} options - Toast options
   * @param {Function} options.undoAction - Function to call when undo is clicked
   * @param {number} options.duration - Duration to show the toast
   * @param {Object} options.position - Position of the toast
   */
  const showToast = (message, options = {}) => {
    // Close guided toast if open
    if (guidedToast.open) {
      setGuidedToast(prev => ({ ...prev, open: false }));
    }

    setToast({
      open: true,
      message,
      undoAction: options.undoAction || null,
      duration: options.duration || 6000,
      position: options.position || { vertical: 'bottom', horizontal: 'center' }
    });
  };

  /**
   * Show a toast with undo functionality
   * @param {string} message - Message to display
   * @param {Function} undoAction - Function to call when undo is clicked
   * @param {Object} options - Additional toast options
   */
  const showUndoToast = (message, undoAction, options = {}) => {
    showToast(message, { ...options, undoAction });
  };

  /**
   * Show a success toast
   * @param {string} message - Message to display
   * @param {Object} options - Additional toast options
   */
  const showSuccess = (message, options = {}) => {
    showToast(`✅ ${message}`, options);
  };

  /**
   * Show an error toast
   * @param {string} message - Message to display
   * @param {Object} options - Additional toast options
   */
  const showError = (message, options = {}) => {
    showToast(`❌ ${message}`, options);
  };

  /**
   * Show a warning toast
   * @param {string} message - Message to display
   * @param {Object} options - Additional toast options
   */
  const showWarning = (message, options = {}) => {
    showToast(`⚠️ ${message}`, options);
  };

  /**
   * Show an info toast
   * @param {string} message - Message to display
   * @param {Object} options - Additional toast options
   */
  const showInfo = (message, options = {}) => {
    showToast(`ℹ️ ${message}`, options);
  };

  /**
   * Show a guided error toast with resolution steps
   * @param {string} message - Error message
   * @param {Object} options - Toast options
   * @param {string} options.errorCode - Error code
   * @param {Array} options.steps - Steps to resolve the error
   * @param {string} options.type - Toast type (error, warning, info, success)
   * @param {Function} options.onHelp - Function to call when help button is clicked
   * @param {number} options.duration - Duration to show the toast
   * @param {Object} options.position - Position of the toast
   */
  const showGuidedError = (message, options = {}) => {
    // Close regular toast if open
    if (toast.open) {
      setToast(prev => ({ ...prev, open: false }));
    }

    setGuidedToast({
      open: true,
      message,
      type: options.type || 'error',
      errorCode: options.errorCode || '',
      steps: options.steps || [],
      duration: options.duration || 10000,
      position: options.position || { vertical: 'bottom', horizontal: 'right' },
      onHelp: options.onHelp || null
    });
  };

  /**
   * Close the toast
   */
  const closeToast = () => {
    setToast(prev => ({
      ...prev,
      open: false
    }));
  };

  /**
   * Close the guided toast
   */
  const closeGuidedToast = () => {
    setGuidedToast(prev => ({
      ...prev,
      open: false
    }));
  };

  /**
   * Handle undo action
   */
  const handleUndo = () => {
    if (toast.undoAction) {
      toast.undoAction();
    }
  };

  return (
    <ToastContext.Provider
      value={{
        showToast,
        showUndoToast,
        showSuccess,
        showError,
        showWarning,
        showInfo,
        showGuidedError,
        closeToast,
        closeGuidedToast
      }}
    >
      {children}

      {/* Regular toast with undo functionality */}
      <UndoToast
        open={toast.open}
        message={toast.message}
        onClose={closeToast}
        onUndo={toast.undoAction ? handleUndo : null}
        duration={toast.duration}
        position={toast.position}
      />

      {/* Guided error toast with resolution steps */}
      <GuidedErrorToast
        open={guidedToast.open}
        message={guidedToast.message}
        type={guidedToast.type}
        errorCode={guidedToast.errorCode}
        steps={guidedToast.steps}
        onClose={closeGuidedToast}
        duration={guidedToast.duration}
        position={guidedToast.position}
        onHelp={guidedToast.onHelp}
      />
    </ToastContext.Provider>
  );
};

/**
 * Hook to use the toast context
 * @returns {Object} Toast context
 */
export const useToast = () => useContext(ToastContext);
