<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reported Profiles - <PERSON>ai<PERSON><PERSON><PERSON> Admin</title>
    <link rel="stylesheet" href="css/admin-styles.css">
    <link rel="icon" type="image/png" href="img/favicon.png">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>Admin Panel</h2>
                <button id="sidebarToggle" class="sidebar-toggle">☰</button>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item">
                        <a href="admin-dashboard.html" class="nav-link">
                            <span class="nav-icon">📊</span>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="all-users.html" class="nav-link">
                            <span class="nav-icon">👥</span>
                            <span class="nav-text">All Users</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="verification-queue.html" class="nav-link">
                            <span class="nav-icon">✅</span>
                            <span class="nav-text">Verification Queue</span>
                        </a>
                    </li>
                    <li class="nav-item active">
                        <a href="reported-profiles.html" class="nav-link">
                            <span class="nav-icon">⚠️</span>
                            <span class="nav-text">Reported Profiles</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Bar -->
            <div class="topbar">
                <div class="breadcrumb">
                    <a href="admin-dashboard.html">Dashboard</a> / Reported Profiles
                </div>
                <div class="topbar-actions">
                    <button id="darkModeToggle" class="dark-mode-toggle" aria-label="Toggle Dark Mode">
                        <span class="light-icon">☀️</span>
                        <span class="dark-icon">🌙</span>
                    </button>
                    <div class="notifications">
                        <button class="notification-btn" aria-label="Notifications">
                            🔔
                            <span class="notification-badge">3</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Content Header -->
            <div class="content-header">
                <h2>Reported Profiles</h2>
            </div>

            <!-- Table Controls -->
            <div class="table-controls">
                <div class="table-actions">
                    <select id="entriesSelect" class="entries-select">
                        <option value="10">10 entries</option>
                        <option value="25">25 entries</option>
                        <option value="50">50 entries</option>
                        <option value="100">100 entries</option>
                    </select>
                    <div class="search-box">
                        <input type="text" id="searchInput" placeholder="Search reports...">
                        <button id="searchBtn">🔍</button>
                    </div>
                </div>
                <div class="filter-actions">
                    <select id="statusFilter" class="status-filter">
                        <option value="all">All Reports</option>
                        <option value="pending">Pending</option>
                        <option value="resolved">Resolved</option>
                        <option value="dismissed">Dismissed</option>
                    </select>
                    <button id="refreshBtn" class="refresh-btn">↻ Refresh</button>
                </div>
            </div>

            <!-- Table Container -->
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Report Reason</th>
                            <th>Report Count</th>
                            <th>Reported By</th>
                            <th>Report Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="reportedProfilesTableBody">
                        <!-- Table content will be dynamically populated -->
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="table-footer">
                <div id="tableInfo" class="table-info">
                    Showing 0 to 0 of 0 entries
                </div>
                <div id="paginationControls" class="pagination">
                    <!-- Pagination will be dynamically populated -->
                </div>
            </div>
        </main>
    </div>

    <!-- Report Details Modal -->
    <div class="modal-overlay" id="reportDetailsModalOverlay">
        <div class="modal">
            <div class="modal-header">
                <h2 class="modal-title">Report Details</h2>
                <button class="modal-close-button">&times;</button>
            </div>
            <div class="modal-body" id="reportDetailsModalContent">
                <!-- Content will be dynamically loaded -->
            </div>
            <div class="modal-footer">
                <button class="filter-button" id="dismissBtn">Dismiss Report</button>
                <button class="filter-button danger" id="takeActionBtn">Take Action</button>
            </div>
        </div>
    </div>

    <!-- User View Modal -->
    <div class="modal-overlay" id="userViewModal">
        <div class="modal">
            <div class="modal-header">
                <h2 class="modal-title">User Details</h2>
                <button class="modal-close-button close-modal">&times;</button>
            </div>
            <div class="modal-body" id="userViewContent">
                <!-- Content will be dynamically loaded -->
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div class="modal-overlay" id="confirmationModal">
        <div class="modal">
            <div class="modal-header">
                <h2 class="modal-title">Confirm Action</h2>
                <button class="modal-close-button close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <p id="confirmationMessage">Are you sure you want to proceed with this action?</p>
            </div>
            <div class="modal-footer">
                <button class="filter-button" id="cancelActionBtn">Cancel</button>
                <button class="filter-button danger" id="confirmActionBtn">Confirm</button>
            </div>
        </div>
    </div>

    <script src="js/reported-profiles.js"></script>
</body>
</html>