/**
 * User Routes
 * 
 * This file contains all routes related to user management.
 * It follows RESTful principles and includes validation for all endpoints.
 */

const express = require('express');
const router = express.Router();
const { body, param, query } = require('express-validator');

// Import controllers
const userController = require('../controllers/userController');

// Import middleware
const { authenticateToken } = require('../middleware/auth');
const { validate } = require('../middleware/validator');
const { uploadProfilePhoto } = require('../middleware/upload');

/**
 * @route   GET /api/v1/users
 * @desc    Get all users (admin only)
 * @access  Private/Admin
 */
router.get(
  '/',
  authenticateToken,
  [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('search')
      .optional()
      .isString()
      .trim()
      .escape()
      .withMessage('Search must be a string'),
    query('sortBy')
      .optional()
      .isString()
      .isIn(['createdAt', 'updatedAt', 'name', 'email'])
      .withMessage('Sort by must be one of: createdAt, updatedAt, name, email'),
    query('sortOrder')
      .optional()
      .isString()
      .isIn(['asc', 'desc'])
      .withMessage('Sort order must be asc or desc')
  ],
  validate,
  userController.getAllUsers
);

/**
 * @route   GET /api/v1/users/:id
 * @desc    Get user by ID
 * @access  Private
 */
router.get(
  '/:id',
  authenticateToken,
  [
    param('id')
      .isString()
      .trim()
      .notEmpty()
      .withMessage('User ID is required')
  ],
  validate,
  userController.getUserById
);

/**
 * @route   POST /api/v1/users
 * @desc    Create a new user (admin only)
 * @access  Private/Admin
 */
router.post(
  '/',
  authenticateToken,
  [
    body('email')
      .isEmail()
      .withMessage('Please provide a valid email')
      .normalizeEmail(),
    body('password')
      .isLength({ min: 6 })
      .withMessage('Password must be at least 6 characters long')
      .matches(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).*$/)
      .withMessage('Password must contain at least one number, one lowercase and one uppercase letter'),
    body('fullName')
      .isString()
      .trim()
      .isLength({ min: 2, max: 100 })
      .withMessage('Full name must be between 2 and 100 characters'),
    body('phone')
      .optional()
      .isMobilePhone()
      .withMessage('Please provide a valid phone number'),
    body('role')
      .optional()
      .isIn(['USER', 'ADMIN', 'MODERATOR'])
      .withMessage('Role must be USER, ADMIN, or MODERATOR')
  ],
  validate,
  userController.createUser
);

/**
 * @route   PUT /api/v1/users/:id
 * @desc    Update user by ID
 * @access  Private
 */
router.put(
  '/:id',
  authenticateToken,
  [
    param('id')
      .isString()
      .trim()
      .notEmpty()
      .withMessage('User ID is required'),
    body('email')
      .optional()
      .isEmail()
      .withMessage('Please provide a valid email')
      .normalizeEmail(),
    body('fullName')
      .optional()
      .isString()
      .trim()
      .isLength({ min: 2, max: 100 })
      .withMessage('Full name must be between 2 and 100 characters'),
    body('phone')
      .optional()
      .isMobilePhone()
      .withMessage('Please provide a valid phone number'),
    body('gender')
      .optional()
      .isIn(['MALE', 'FEMALE', 'OTHER'])
      .withMessage('Gender must be MALE, FEMALE, or OTHER'),
    body('dateOfBirth')
      .optional()
      .isISO8601()
      .withMessage('Date of birth must be a valid date in ISO 8601 format'),
    body('address')
      .optional()
      .isObject()
      .withMessage('Address must be an object'),
    body('address.city')
      .optional()
      .isString()
      .trim()
      .withMessage('City must be a string'),
    body('address.state')
      .optional()
      .isString()
      .trim()
      .withMessage('State must be a string'),
    body('address.country')
      .optional()
      .isString()
      .trim()
      .withMessage('Country must be a string'),
    body('address.postalCode')
      .optional()
      .isString()
      .trim()
      .withMessage('Postal code must be a string')
  ],
  validate,
  userController.updateUser
);

/**
 * @route   DELETE /api/v1/users/:id
 * @desc    Delete user by ID
 * @access  Private/Admin
 */
router.delete(
  '/:id',
  authenticateToken,
  [
    param('id')
      .isString()
      .trim()
      .notEmpty()
      .withMessage('User ID is required')
  ],
  validate,
  userController.deleteUser
);

/**
 * @route   POST /api/v1/users/profile-photo
 * @desc    Upload profile photo
 * @access  Private
 */
router.post(
  '/profile-photo',
  authenticateToken,
  uploadProfilePhoto,
  userController.uploadProfilePhoto
);

/**
 * @route   GET /api/v1/users/profile
 * @desc    Get current user profile
 * @access  Private
 */
router.get(
  '/profile',
  authenticateToken,
  userController.getCurrentUserProfile
);

/**
 * @route   PUT /api/v1/users/profile
 * @desc    Update current user profile
 * @access  Private
 */
router.put(
  '/profile',
  authenticateToken,
  [
    body('fullName')
      .optional()
      .isString()
      .trim()
      .isLength({ min: 2, max: 100 })
      .withMessage('Full name must be between 2 and 100 characters'),
    body('phone')
      .optional()
      .isMobilePhone()
      .withMessage('Please provide a valid phone number'),
    body('gender')
      .optional()
      .isIn(['MALE', 'FEMALE', 'OTHER'])
      .withMessage('Gender must be MALE, FEMALE, or OTHER'),
    body('dateOfBirth')
      .optional()
      .isISO8601()
      .withMessage('Date of birth must be a valid date in ISO 8601 format'),
    body('bio')
      .optional()
      .isString()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Bio must be at most 500 characters'),
    body('occupation')
      .optional()
      .isString()
      .trim()
      .withMessage('Occupation must be a string'),
    body('education')
      .optional()
      .isString()
      .trim()
      .withMessage('Education must be a string'),
    body('height')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Height must be a positive number'),
    body('religion')
      .optional()
      .isString()
      .trim()
      .withMessage('Religion must be a string'),
    body('motherTongue')
      .optional()
      .isString()
      .trim()
      .withMessage('Mother tongue must be a string'),
    body('maritalStatus')
      .optional()
      .isIn(['NEVER_MARRIED', 'DIVORCED', 'WIDOWED', 'SEPARATED'])
      .withMessage('Marital status must be NEVER_MARRIED, DIVORCED, WIDOWED, or SEPARATED')
  ],
  validate,
  userController.updateCurrentUserProfile
);

/**
 * @route   PUT /api/v1/users/change-password
 * @desc    Change user password
 * @access  Private
 */
router.put(
  '/change-password',
  authenticateToken,
  [
    body('currentPassword')
      .isString()
      .trim()
      .notEmpty()
      .withMessage('Current password is required'),
    body('newPassword')
      .isLength({ min: 6 })
      .withMessage('New password must be at least 6 characters long')
      .matches(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).*$/)
      .withMessage('New password must contain at least one number, one lowercase and one uppercase letter'),
    body('confirmPassword')
      .isString()
      .trim()
      .notEmpty()
      .withMessage('Confirm password is required')
      .custom((value, { req }) => {
        if (value !== req.body.newPassword) {
          throw new Error('Passwords do not match');
        }
        return true;
      })
  ],
  validate,
  userController.changePassword
);

module.exports = router;
