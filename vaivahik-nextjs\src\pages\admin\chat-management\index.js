import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  Alert,
  CircularProgress,
  Switch,
  FormControlLabel,
  Tooltip,
  Avatar,
  Stack,
  Divider,
  Paper,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Badge,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar
} from '@mui/material';
import {
  Chat as ChatIcon,
  Security as SecurityIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Search as SearchIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  Message as MessageIcon,
  FilterList as FilterListIcon,
  Visibility as VisibilityIcon,
  Delete as DeleteIcon,
  Flag as FlagIcon,
  Schedule as ScheduleIcon,
  Assessment as AssessmentIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { adminGet, adminPost, adminPut, adminDelete } from '@/services/adminApiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';

// Dynamic import for EnhancedAdminLayout to avoid SSR issues
const EnhancedAdminLayout = dynamic(() => import('@/components/admin/EnhancedAdminLayout'), {
  ssr: false
});

export default function ChatManagementSystem() {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({});
  const [flaggedMessages, setFlaggedMessages] = useState([]);
  const [moderationLogs, setModerationLogs] = useState([]);
  const [chatSettings, setChatSettings] = useState({});
  const [bannedWords, setBannedWords] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [filters, setFilters] = useState({
    status: '',
    flagType: '',
    search: ''
  });
  const [settingsDialogOpen, setSettingsDialogOpen] = useState(false);
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [newBannedWord, setNewBannedWord] = useState('');

  // Moderation status options
  const statusOptions = [
    { value: 'PENDING', label: 'Pending Review', color: 'warning' },
    { value: 'APPROVED', label: 'Approved', color: 'success' },
    { value: 'REJECTED', label: 'Rejected', color: 'error' },
    { value: 'AUTO_APPROVED', label: 'Auto Approved', color: 'info' }
  ];

  // Flag type options
  const flagTypeOptions = [
    { value: 'profanity', label: 'Profanity', icon: '🤬' },
    { value: 'contact_info', label: 'Contact Info', icon: '📞' },
    { value: 'spam', label: 'Spam', icon: '🚫' },
    { value: 'inappropriate', label: 'Inappropriate', icon: '⚠️' },
    { value: 'harassment', label: 'Harassment', icon: '😡' },
    { value: 'scam', label: 'Scam', icon: '💰' }
  ];

  useEffect(() => {
    fetchStats();
    fetchFlaggedMessages();
    fetchModerationLogs();
    fetchChatSettings();
    fetchBannedWords();
  }, [page, rowsPerPage, filters]);

  const fetchStats = async () => {
    try {
      const response = await adminGet(`${ADMIN_ENDPOINTS.TEXT_MODERATION}/stats`);
      if (response.success) {
        setStats(response.stats || {});
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const fetchFlaggedMessages = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams();
      queryParams.append('page', page + 1);
      queryParams.append('limit', rowsPerPage);
      
      if (filters.search) queryParams.append('search', filters.search);
      if (filters.status) queryParams.append('status', filters.status);
      if (filters.flagType) queryParams.append('flagType', filters.flagType);

      const response = await adminGet(`${ADMIN_ENDPOINTS.TEXT_MODERATION}/flagged-messages?${queryParams}`);
      
      if (response.success) {
        setFlaggedMessages(response.messages || []);
        setTotalCount(response.pagination?.total || 0);
      }
    } catch (error) {
      console.error('Error fetching flagged messages:', error);
      toast.error('Error fetching flagged messages');
    } finally {
      setLoading(false);
    }
  };

  const fetchModerationLogs = async () => {
    try {
      const response = await adminGet(`${ADMIN_ENDPOINTS.TEXT_MODERATION}/logs`);
      if (response.success) {
        setModerationLogs(response.logs || []);
      }
    } catch (error) {
      console.error('Error fetching moderation logs:', error);
    }
  };

  const fetchChatSettings = async () => {
    try {
      const response = await adminGet(`${ADMIN_ENDPOINTS.CHAT_SETTINGS}`);
      if (response.success) {
        setChatSettings(response.settings || {});
      }
    } catch (error) {
      console.error('Error fetching chat settings:', error);
    }
  };

  const fetchBannedWords = async () => {
    try {
      const response = await adminGet(`${ADMIN_ENDPOINTS.TEXT_MODERATION}/banned-words`);
      if (response.success) {
        setBannedWords(response.bannedWords || []);
      }
    } catch (error) {
      console.error('Error fetching banned words:', error);
    }
  };

  const handleReviewMessage = async (messageId, action, reason = '') => {
    try {
      const response = await adminPut(`${ADMIN_ENDPOINTS.TEXT_MODERATION}/review/${messageId}`, {
        action,
        reason
      });

      if (response.success) {
        toast.success(`Message ${action.toLowerCase()} successfully`);
        setReviewDialogOpen(false);
        setSelectedMessage(null);
        fetchFlaggedMessages();
        fetchStats();
      } else {
        toast.error('Failed to review message');
      }
    } catch (error) {
      console.error('Error reviewing message:', error);
      toast.error('Error reviewing message');
    }
  };

  const handleAddBannedWord = async () => {
    if (!newBannedWord.trim()) return;

    try {
      const updatedWords = [...bannedWords, newBannedWord.trim()];
      const response = await adminPut(`${ADMIN_ENDPOINTS.TEXT_MODERATION}/banned-words`, {
        bannedWords: updatedWords
      });

      if (response.success) {
        setBannedWords(updatedWords);
        setNewBannedWord('');
        toast.success('Banned word added successfully');
      } else {
        toast.error('Failed to add banned word');
      }
    } catch (error) {
      console.error('Error adding banned word:', error);
      toast.error('Error adding banned word');
    }
  };

  const handleRemoveBannedWord = async (wordToRemove) => {
    try {
      const updatedWords = bannedWords.filter(word => word !== wordToRemove);
      const response = await adminPut(`${ADMIN_ENDPOINTS.TEXT_MODERATION}/banned-words`, {
        bannedWords: updatedWords
      });

      if (response.success) {
        setBannedWords(updatedWords);
        toast.success('Banned word removed successfully');
      } else {
        toast.error('Failed to remove banned word');
      }
    } catch (error) {
      console.error('Error removing banned word:', error);
      toast.error('Error removing banned word');
    }
  };

  const getStatusChip = (status) => {
    const statusConfig = statusOptions.find(opt => opt.value === status) || statusOptions[0];
    return (
      <Chip
        label={statusConfig.label}
        color={statusConfig.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const getFlagChip = (flagType) => {
    const flagConfig = flagTypeOptions.find(opt => opt.value === flagType) || flagTypeOptions[0];
    return (
      <Chip
        label={`${flagConfig.icon} ${flagConfig.label}`}
        size="small"
        variant="outlined"
        color="warning"
      />
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const TabPanel = ({ children, value, index, ...other }) => (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`chat-tabpanel-${index}`}
      aria-labelledby={`chat-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );

  return (
    <EnhancedAdminLayout title="Chat Management System">
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Chat Management System
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={() => {
                fetchStats();
                fetchFlaggedMessages();
                fetchModerationLogs();
              }}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={<SettingsIcon />}
              onClick={() => setSettingsDialogOpen(true)}
            >
              Settings
            </Button>
          </Box>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Total Messages
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.totalMessages || 0}
                    </Typography>
                  </Box>
                  <MessageIcon color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Flagged Messages
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.moderatedMessages || 0}
                    </Typography>
                  </Box>
                  <FlagIcon color="warning" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Rejected Messages
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.rejectedMessages || 0}
                    </Typography>
                  </Box>
                  <BlockIcon color="error" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Moderation Rate
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stats.moderationRate?.toFixed(1) || 0}%
                    </Typography>
                  </Box>
                  <AssessmentIcon color="info" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Tabs */}
        <Paper sx={{ mb: 3 }}>
          <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
            <Tab label="Flagged Messages" icon={<FlagIcon />} />
            <Tab label="Moderation Logs" icon={<ScheduleIcon />} />
            <Tab label="Banned Words" icon={<BlockIcon />} />
            <Tab label="Settings" icon={<SettingsIcon />} />
          </Tabs>
        </Paper>

        {/* Tab Panels */}
        <TabPanel value={activeTab} index={0}>
          {/* Filters for Flagged Messages */}
          <Paper sx={{ mb: 3, p: 2 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={3}>
                <TextField
                  label="Search Messages"
                  variant="outlined"
                  size="small"
                  fullWidth
                  value={filters.search}
                  onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={filters.status}
                    label="Status"
                    onChange={(e) => setFilters({ ...filters, status: e.target.value })}
                  >
                    <MenuItem value="">All Status</MenuItem>
                    {statusOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Flag Type</InputLabel>
                  <Select
                    value={filters.flagType}
                    label="Flag Type"
                    onChange={(e) => setFilters({ ...filters, flagType: e.target.value })}
                  >
                    <MenuItem value="">All Types</MenuItem>
                    {flagTypeOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.icon} {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <Button
                  variant="outlined"
                  fullWidth
                  onClick={() => setFilters({ status: '', flagType: '', search: '' })}
                >
                  Clear Filters
                </Button>
              </Grid>
            </Grid>
          </Paper>

          {/* Flagged Messages Table */}
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Message</TableCell>
                  <TableCell>Sender</TableCell>
                  <TableCell>Flag Type</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                ) : flaggedMessages.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <Typography variant="body2" color="text.secondary">
                        No flagged messages found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  flaggedMessages.map((message) => (
                    <TableRow key={message.id}>
                      <TableCell>
                        <Typography variant="body2" sx={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                          {message.content}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Avatar sx={{ width: 32, height: 32 }}>
                            {message.senderName?.charAt(0)}
                          </Avatar>
                          <Typography variant="body2">
                            {message.senderName || 'Unknown'}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        {message.moderationFlags?.split(',').map((flag, index) => (
                          <Box key={index} sx={{ mb: 0.5 }}>
                            {getFlagChip(flag.trim())}
                          </Box>
                        ))}
                      </TableCell>
                      <TableCell>
                        {getStatusChip(message.moderationStatus)}
                      </TableCell>
                      <TableCell>
                        {formatDate(message.sentAt)}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                          <Tooltip title="Review">
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => {
                                setSelectedMessage(message);
                                setReviewDialogOpen(true);
                              }}
                            >
                              <VisibilityIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={totalCount}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={(e, newPage) => setPage(newPage)}
              onRowsPerPageChange={(e) => {
                setRowsPerPage(parseInt(e.target.value, 10));
                setPage(0);
              }}
            />
          </TableContainer>
        </TabPanel>

        {/* Moderation Logs Tab */}
        <TabPanel value={activeTab} index={1}>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Action</TableCell>
                  <TableCell>Content</TableCell>
                  <TableCell>Admin</TableCell>
                  <TableCell>Reason</TableCell>
                  <TableCell>Date</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {moderationLogs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      <Typography variant="body2" color="text.secondary">
                        No moderation logs found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  moderationLogs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell>
                        <Chip
                          label={log.action}
                          color={log.action === 'APPROVED' ? 'success' : 'error'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" sx={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                          {log.content}
                        </Typography>
                      </TableCell>
                      <TableCell>{log.adminName || 'System'}</TableCell>
                      <TableCell>{log.reason || 'N/A'}</TableCell>
                      <TableCell>{formatDate(log.createdAt)}</TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        {/* Banned Words Tab */}
        <TabPanel value={activeTab} index={2}>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Manage Banned Words
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
              <TextField
                label="Add Banned Word"
                variant="outlined"
                size="small"
                value={newBannedWord}
                onChange={(e) => setNewBannedWord(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleAddBannedWord();
                  }
                }}
              />
              <Button
                variant="contained"
                onClick={handleAddBannedWord}
                disabled={!newBannedWord.trim()}
              >
                Add Word
              </Button>
            </Box>
            <Paper sx={{ p: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Current Banned Words ({bannedWords.length})
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {bannedWords.map((word, index) => (
                  <Chip
                    key={index}
                    label={word}
                    onDelete={() => handleRemoveBannedWord(word)}
                    color="error"
                    variant="outlined"
                  />
                ))}
                {bannedWords.length === 0 && (
                  <Typography variant="body2" color="text.secondary">
                    No banned words configured
                  </Typography>
                )}
              </Box>
            </Paper>
          </Box>
        </TabPanel>

        {/* Settings Tab */}
        <TabPanel value={activeTab} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Chat Features
                  </Typography>
                  <Stack spacing={2}>
                    <FormControlLabel
                      control={<Switch checked={chatSettings.enableReadReceipts || false} />}
                      label="Enable Read Receipts"
                    />
                    <FormControlLabel
                      control={<Switch checked={chatSettings.enableTypingIndicators || false} />}
                      label="Enable Typing Indicators"
                    />
                    <FormControlLabel
                      control={<Switch checked={chatSettings.enableFileSharing || false} />}
                      label="Enable File Sharing"
                    />
                    <FormControlLabel
                      control={<Switch checked={chatSettings.enableVoiceMessages || false} />}
                      label="Enable Voice Messages"
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Moderation Settings
                  </Typography>
                  <Stack spacing={2}>
                    <TextField
                      label="Max Message Length"
                      type="number"
                      value={chatSettings.maxMessageLength || 1000}
                      size="small"
                    />
                    <TextField
                      label="Message Expiry Days"
                      type="number"
                      value={chatSettings.messageExpiryDays || 180}
                      size="small"
                    />
                    <FormControlLabel
                      control={<Switch checked={chatSettings.autoModeration || false} />}
                      label="Auto Moderation"
                    />
                    <FormControlLabel
                      control={<Switch checked={chatSettings.strictMode || false} />}
                      label="Strict Mode"
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Review Message Dialog */}
        <Dialog open={reviewDialogOpen} onClose={() => setReviewDialogOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>Review Flagged Message</DialogTitle>
          <DialogContent>
            {selectedMessage && (
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Original Message:
                </Typography>
                <Paper sx={{ p: 2, mb: 2, bgcolor: 'grey.100' }}>
                  <Typography variant="body1">
                    {selectedMessage.content}
                  </Typography>
                </Paper>
                <Typography variant="subtitle2" gutterBottom>
                  Moderation Flags:
                </Typography>
                <Box sx={{ mb: 2 }}>
                  {selectedMessage.moderationFlags?.split(',').map((flag, index) => (
                    <Box key={index} sx={{ mb: 0.5 }}>
                      {getFlagChip(flag.trim())}
                    </Box>
                  ))}
                </Box>
                <Typography variant="subtitle2" gutterBottom>
                  Sender Information:
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <Avatar>{selectedMessage.senderName?.charAt(0)}</Avatar>
                  <Typography>{selectedMessage.senderName || 'Unknown'}</Typography>
                </Box>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setReviewDialogOpen(false)}>Cancel</Button>
            <Button
              onClick={() => handleReviewMessage(selectedMessage?.id, 'REJECTED')}
              color="error"
              variant="outlined"
            >
              Reject
            </Button>
            <Button
              onClick={() => handleReviewMessage(selectedMessage?.id, 'APPROVED')}
              color="success"
              variant="contained"
            >
              Approve
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </EnhancedAdminLayout>
  );
}
