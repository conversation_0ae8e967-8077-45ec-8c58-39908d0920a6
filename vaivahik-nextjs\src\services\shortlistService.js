import axios from 'axios';
import { API_BASE_URL } from '@/config';

const API_URL = `${API_BASE_URL}/shortlist`;

/**
 * Get shortlisted profiles
 * @param {Object} options - Query options
 * @returns {Promise} Promise with shortlisted profiles data
 */
export const getShortlistedProfiles = async (options = {}) => {
  try {
    const token = localStorage.getItem('token');
    
    const queryParams = new URLSearchParams();
    
    if (options.page) {
      queryParams.append('page', options.page);
    }
    
    if (options.limit) {
      queryParams.append('limit', options.limit);
    }
    
    if (options.sortBy) {
      queryParams.append('sortBy', options.sortBy);
    }
    
    if (options.sortOrder) {
      queryParams.append('sortOrder', options.sortOrder);
    }
    
    const response = await axios.get(`${API_URL}?${queryParams.toString()}`, {
      headers: {
        'x-auth-token': token
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error fetching shortlisted profiles:', error);
    throw error;
  }
};

/**
 * Add profile to shortlist
 * @param {String} userId - User ID to shortlist
 * @param {String} notes - Optional notes about the profile
 * @returns {Promise} Promise with result
 */
export const addToShortlist = async (userId, notes = '') => {
  try {
    const token = localStorage.getItem('token');
    
    const response = await axios.post(
      API_URL,
      { userId, notes },
      {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      }
    );
    
    return response.data;
  } catch (error) {
    console.error('Error adding profile to shortlist:', error);
    throw error;
  }
};

/**
 * Remove profile from shortlist
 * @param {String} userId - User ID to remove from shortlist
 * @returns {Promise} Promise with result
 */
export const removeFromShortlist = async (userId) => {
  try {
    const token = localStorage.getItem('token');
    
    const response = await axios.delete(
      `${API_URL}/${userId}`,
      {
        headers: {
          'x-auth-token': token
        }
      }
    );
    
    return response.data;
  } catch (error) {
    console.error('Error removing profile from shortlist:', error);
    throw error;
  }
};

/**
 * Update shortlist notes
 * @param {String} userId - User ID
 * @param {String} notes - Updated notes
 * @returns {Promise} Promise with result
 */
export const updateShortlistNotes = async (userId, notes) => {
  try {
    const token = localStorage.getItem('token');
    
    const response = await axios.put(
      `${API_URL}/${userId}/notes`,
      { notes },
      {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      }
    );
    
    return response.data;
  } catch (error) {
    console.error('Error updating shortlist notes:', error);
    throw error;
  }
};

/**
 * Get shortlist count
 * @returns {Promise} Promise with count
 */
export const getShortlistCount = async () => {
  try {
    const token = localStorage.getItem('token');
    
    const response = await axios.get(`${API_URL}/count`, {
      headers: {
        'x-auth-token': token
      }
    });
    
    return response.data.count;
  } catch (error) {
    console.error('Error fetching shortlist count:', error);
    throw error;
  }
};

/**
 * Check if a profile is shortlisted
 * @param {String} userId - User ID to check
 * @returns {Promise} Promise with result
 */
export const isProfileShortlisted = async (userId) => {
  try {
    const token = localStorage.getItem('token');
    
    const response = await axios.get(`${API_URL}/check/${userId}`, {
      headers: {
        'x-auth-token': token
      }
    });
    
    return response.data.isShortlisted;
  } catch (error) {
    console.error('Error checking if profile is shortlisted:', error);
    throw error;
  }
};
