import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  // Check if user is authenticated
  const session = await getServerSession(req, res, authOptions);
  
  if (!session || !session.user) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { templateId, paymentMethod, transactionId } = req.body;
    const userId = session.user.id;

    if (!templateId) {
      return res.status(400).json({ success: false, message: 'Template ID is required' });
    }

    // Check if template exists
    const template = await prisma.biodataTemplate.findUnique({
      where: { id: templateId }
    });

    if (!template) {
      return res.status(404).json({ success: false, message: 'Template not found' });
    }

    // Check if user has already purchased this template
    const existingPurchase = await prisma.userBiodata.findFirst({
      where: {
        userId,
        templateId
      }
    });

    if (existingPurchase) {
      return res.status(400).json({ 
        success: false, 
        message: 'You have already purchased this template',
        userBiodata: existingPurchase
      });
    }

    // Calculate price
    const price = template.discountedPrice || template.price;

    // Create purchase record
    const userBiodata = await prisma.userBiodata.create({
      data: {
        userId,
        templateId,
        pricePaid: price,
        transactionId: transactionId || `MANUAL-${Date.now()}`,
        downloadCount: 0
      }
    });

    // TODO: In a real implementation, you would integrate with a payment gateway
    // and only create the purchase record after successful payment

    return res.status(201).json({ 
      success: true, 
      message: 'Template purchased successfully',
      userBiodata
    });
  } catch (error) {
    console.error('Error purchasing biodata template:', error);
    return res.status(500).json({ success: false, message: 'Failed to purchase biodata template' });
  }
}
