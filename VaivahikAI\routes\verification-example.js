/**
 * Example of integrating event notifications with verification status updates
 * This is a sample implementation to demonstrate how to integrate notifications
 */
const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { authenticateAdmin } = require('../middleware/auth');
const eventNotifications = require('../services/notification/event-notifications');

/**
 * @route PUT /api/admin/verifications/:id/approve
 * @desc Approve a verification request
 * @access Admin
 */
router.put('/:id/approve', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    // Find the verification document
    const verificationDoc = await prisma.verificationDocument.findUnique({
      where: { id },
      select: { id: true, userId: true, status: true }
    });
    
    if (!verificationDoc) {
      return res.status(404).json({ message: 'Verification document not found' });
    }
    
    if (verificationDoc.status === 'APPROVED') {
      return res.status(400).json({ message: 'Document already approved' });
    }
    
    // Update the document status
    const updatedDoc = await prisma.verificationDocument.update({
      where: { id },
      data: {
        status: 'APPROVED',
        reviewedAt: new Date(),
        reviewedBy: req.admin.id
      }
    });
    
    // Update user's verification status
    await prisma.user.update({
      where: { id: verificationDoc.userId },
      data: {
        isVerified: true
      }
    });
    
    // Send notification to user
    const verificationData = {
      status: 'APPROVED'
    };
    
    eventNotifications.notifyVerificationStatus(verificationDoc.userId, verificationData);
    
    res.json({ success: true, document: updatedDoc });
  } catch (error) {
    console.error('Error approving verification:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route PUT /api/admin/verifications/:id/reject
 * @desc Reject a verification request
 * @access Admin
 */
router.put('/:id/reject', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    
    if (!reason) {
      return res.status(400).json({ message: 'Rejection reason is required' });
    }
    
    // Find the verification document
    const verificationDoc = await prisma.verificationDocument.findUnique({
      where: { id },
      select: { id: true, userId: true, status: true }
    });
    
    if (!verificationDoc) {
      return res.status(404).json({ message: 'Verification document not found' });
    }
    
    if (verificationDoc.status === 'REJECTED') {
      return res.status(400).json({ message: 'Document already rejected' });
    }
    
    // Update the document status
    const updatedDoc = await prisma.verificationDocument.update({
      where: { id },
      data: {
        status: 'REJECTED',
        rejectionReason: reason,
        reviewedAt: new Date(),
        reviewedBy: req.admin.id
      }
    });
    
    // Send notification to user
    const verificationData = {
      status: 'REJECTED',
      reason
    };
    
    eventNotifications.notifyVerificationStatus(verificationDoc.userId, verificationData);
    
    res.json({ success: true, document: updatedDoc });
  } catch (error) {
    console.error('Error rejecting verification:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
