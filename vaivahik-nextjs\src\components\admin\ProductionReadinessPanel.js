/**
 * Production Readiness Panel
 * 
 * This component provides a comprehensive overview of the system's readiness
 * for production deployment, including service status, feature flags, and
 * smooth transition controls between mock and real data.
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Typography,
  Switch,
  FormControlLabel,
  Chip,
  LinearProgress,
  Alert,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
  Launch as LaunchIcon,
  Storage as DataIcon,
  Cloud as CloudIcon,
  Security as SecurityIcon,
  Payment as PaymentIcon,
  Chat as ChatIcon,
  Psychology as AIIcon
} from '@mui/icons-material';
import { ProductionUtils, PRODUCTION_FEATURES } from '@/config/productionConfig';

const ProductionReadinessPanel = () => {
  const [readinessReport, setReadinessReport] = useState(null);
  const [dataSourceStatus, setDataSourceStatus] = useState(null);
  const [serviceHealth, setServiceHealth] = useState({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Service icons mapping
  const serviceIcons = {
    backend: CloudIcon,
    auth: SecurityIcon,
    database: DataIcon,
    ml: AIIcon,
    chat: ChatIcon,
    notifications: InfoIcon,
    payments: PaymentIcon
  };

  // Fetch production readiness data
  const fetchReadinessData = async () => {
    setRefreshing(true);
    try {
      // Get production readiness report
      const report = ProductionUtils.getProductionReadinessReport();
      setReadinessReport(report);

      // Get data source status
      const dataStatus = ProductionUtils.getDataSourceStatus();
      setDataSourceStatus(dataStatus);

      // Check service health (simplified for demo)
      const healthChecks = {
        api: Math.random() > 0.3, // 70% chance of being healthy
        ml: Math.random() > 0.4,  // 60% chance
        chat: Math.random() > 0.5, // 50% chance
        notifications: Math.random() > 0.3 // 70% chance
      };
      setServiceHealth(healthChecks);

    } catch (error) {
      console.error('Error fetching readiness data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchReadinessData();
    
    // Set up periodic refresh
    const interval = setInterval(fetchReadinessData, 30000); // Every 30 seconds
    return () => clearInterval(interval);
  }, []);

  // Handle data source toggle
  const handleDataSourceToggle = () => {
    ProductionUtils.toggleDataSource();
  };

  // Get status color based on readiness
  const getStatusColor = (isReady) => {
    if (isReady) return 'success';
    return 'error';
  };

  // Get service status color
  const getServiceStatusColor = (isHealthy) => {
    if (isHealthy) return 'success';
    return 'error';
  };

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Loading Production Readiness...</Typography>
          </Box>
          <LinearProgress />
        </CardContent>
      </Card>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          Production Readiness Dashboard
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={fetchReadinessData}
          disabled={refreshing}
        >
          {refreshing ? 'Refreshing...' : 'Refresh Status'}
        </Button>
      </Box>

      <Grid container spacing={3}>
        {/* Overall Readiness Status */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader 
              title="Overall Production Readiness"
              action={
                <Chip
                  label={`${readinessReport?.percentage || 0}%`}
                  color={readinessReport?.isReady ? 'success' : 'warning'}
                  size="large"
                />
              }
            />
            <CardContent>
              <Box sx={{ mb: 2 }}>
                <LinearProgress
                  variant="determinate"
                  value={readinessReport?.percentage || 0}
                  color={readinessReport?.isReady ? 'success' : 'warning'}
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
              <Typography variant="body2" color="text.secondary">
                {readinessReport?.passed || 0} of {readinessReport?.total || 0} services ready
              </Typography>
              
              {readinessReport?.isReady ? (
                <Alert severity="success" sx={{ mt: 2 }}>
                  System is ready for production deployment!
                </Alert>
              ) : (
                <Alert severity="warning" sx={{ mt: 2 }}>
                  Some services need configuration before production deployment.
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Data Source Control */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Data Source Control" />
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={!dataSourceStatus?.useMockData}
                      onChange={handleDataSourceToggle}
                      color="primary"
                    />
                  }
                  label="Use Real Backend"
                />
                <Chip
                  label={dataSourceStatus?.label || 'Unknown'}
                  color={dataSourceStatus?.color || 'default'}
                  size="small"
                  sx={{ ml: 2 }}
                />
              </Box>
              <Typography variant="body2" color="text.secondary">
                {dataSourceStatus?.description || 'Data source status unknown'}
              </Typography>
              
              <Alert severity="info" sx={{ mt: 2 }}>
                Toggle this switch to seamlessly transition between mock data and real backend services.
              </Alert>
            </CardContent>
          </Card>
        </Grid>

        {/* Service Status Details */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Service Status Details" />
            <CardContent>
              <List>
                {readinessReport?.checks && Object.entries(readinessReport.checks).map(([service, isReady], index) => {
                  const IconComponent = serviceIcons[service] || InfoIcon;
                  const isHealthy = serviceHealth[service];
                  
                  return (
                    <React.Fragment key={service}>
                      <ListItem>
                        <ListItemIcon>
                          <IconComponent color={getStatusColor(isReady)} />
                        </ListItemIcon>
                        <ListItemText
                          primary={service.charAt(0).toUpperCase() + service.slice(1)}
                          secondary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Chip
                                label={isReady ? 'Configured' : 'Not Configured'}
                                color={getStatusColor(isReady)}
                                size="small"
                              />
                              {isHealthy !== undefined && (
                                <Chip
                                  label={isHealthy ? 'Healthy' : 'Unhealthy'}
                                  color={getServiceStatusColor(isHealthy)}
                                  size="small"
                                  variant="outlined"
                                />
                              )}
                            </Box>
                          }
                        />
                        <ListItemSecondaryAction>
                          {isReady ? (
                            <CheckIcon color="success" />
                          ) : (
                            <Tooltip title="Needs configuration">
                              <WarningIcon color="warning" />
                            </Tooltip>
                          )}
                        </ListItemSecondaryAction>
                      </ListItem>
                      {index < Object.entries(readinessReport.checks).length - 1 && <Divider />}
                    </React.Fragment>
                  );
                })}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Quick Actions" />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<LaunchIcon />}
                    onClick={() => window.open('/admin/algorithm-settings', '_blank')}
                  >
                    Algorithm Settings
                  </Button>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<DataIcon />}
                    onClick={() => window.open('/admin/success-analytics', '_blank')}
                  >
                    Analytics
                  </Button>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<SecurityIcon />}
                    onClick={() => window.open('/admin/users', '_blank')}
                  >
                    User Management
                  </Button>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<PaymentIcon />}
                    onClick={() => window.open('/admin/payments', '_blank')}
                  >
                    Payment Settings
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ProductionReadinessPanel;
