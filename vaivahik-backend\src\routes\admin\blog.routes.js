// src/routes/admin/blog.routes.js
const express = require('express');
const router = express.Router();
const blogController = require('../../controllers/admin/blog.controller');
const { authenticateAdmin } = require('../../middleware/auth.middleware');

// Apply admin authentication middleware to all routes
router.use(authenticateAdmin);

// Blog Posts Routes
router.get('/', blogController.getBlogPosts);
router.post('/', blogController.createBlogPost);
router.get('/:id', blogController.getBlogPost);
router.put('/:id', blogController.updateBlogPost);
router.delete('/:id', blogController.deleteBlogPost);

// Get blog statistics
router.get('/stats/overview', blogController.getBlogStats);

module.exports = router;
