/**
 * Search API Integration
 * 
 * This file contains functions for interacting with the search-related API endpoints.
 * It handles both real backend and mock data scenarios based on feature flags.
 */

import { isUsingRealBackend, getApiBaseUrl } from '@/utils/featureFlags';
import { generateMockResults } from './utils/mockDataGenerator';

/**
 * Search profiles with the given parameters
 * @param {Object} searchParams - Search parameters
 * @param {Object} options - Additional options (pagination, etc.)
 * @returns {Promise<Object>} Search results with pagination info
 */
export const searchProfiles = async (searchParams, options = {}) => {
  const { page = 1, limit = 9, skipCache = false } = options;
  
  // Check if using real backend or mock data
  if (isUsingRealBackend()) {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      
      // Add search parameters
      if (searchParams.targetGender) queryParams.append('gender', searchParams.targetGender);
      if (searchParams.ageFrom) queryParams.append('minAge', searchParams.ageFrom);
      if (searchParams.ageTo) queryParams.append('maxAge', searchParams.ageTo);
      if (searchParams.location) queryParams.append('city', searchParams.location);
      
      // Add pagination parameters
      queryParams.append('page', page);
      queryParams.append('limit', limit);
      
      // Add cache control
      if (skipCache) queryParams.append('skipCache', 'true');
      
      // Make API request
      const response = await fetch(`${getApiBaseUrl()}/users/search?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`Search API error: ${response.status}`);
      }
      
      const data = await response.json();
      
      return {
        profiles: data.profiles || [],
        total: data.total || 0,
        pagination: data.pagination || {
          currentPage: page,
          totalPages: Math.ceil((data.total || 0) / limit),
          totalResults: data.total || 0,
          resultsPerPage: limit
        },
        fromCache: data.fromCache || false
      };
    } catch (error) {
      console.error('Error searching profiles:', error);
      throw error;
    }
  } else {
    // Use mock data
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Generate mock results
      const mockResults = generateMockResults(searchParams);
      
      // Apply pagination
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedResults = mockResults.slice(startIndex, endIndex);
      
      return {
        profiles: paginatedResults,
        total: mockResults.length,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(mockResults.length / limit),
          totalResults: mockResults.length,
          resultsPerPage: limit
        },
        fromCache: false
      };
    } catch (error) {
      console.error('Error generating mock search results:', error);
      throw error;
    }
  }
};

/**
 * Perform advanced search with more detailed filters
 * @param {Object} searchParams - Advanced search parameters
 * @param {Object} options - Additional options (pagination, etc.)
 * @returns {Promise<Object>} Search results with pagination info
 */
export const advancedSearch = async (searchParams, options = {}) => {
  const { page = 1, limit = 9, skipCache = false } = options;
  
  // Check if using real backend or mock data
  if (isUsingRealBackend()) {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      
      // Add all search parameters
      Object.entries(searchParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          // Handle arrays
          if (Array.isArray(value)) {
            value.forEach(item => queryParams.append(key, item));
          } else {
            queryParams.append(key, value);
          }
        }
      });
      
      // Add pagination parameters
      queryParams.append('page', page);
      queryParams.append('limit', limit);
      
      // Add cache control
      if (skipCache) queryParams.append('skipCache', 'true');
      
      // Make API request
      const response = await fetch(`${getApiBaseUrl()}/users/advanced-search?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`Advanced search API error: ${response.status}`);
      }
      
      const data = await response.json();
      
      return {
        profiles: data.profiles || [],
        total: data.total || 0,
        pagination: data.pagination || {
          currentPage: page,
          totalPages: Math.ceil((data.total || 0) / limit),
          totalResults: data.total || 0,
          resultsPerPage: limit
        },
        fromCache: data.fromCache || false,
        upgradeRequired: data.upgradeRequired || false
      };
    } catch (error) {
      console.error('Error performing advanced search:', error);
      throw error;
    }
  } else {
    // Use mock data with more sophisticated filtering
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Generate mock results with advanced filtering
      const mockResults = generateMockResults(searchParams, true);
      
      // Apply pagination
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedResults = mockResults.slice(startIndex, endIndex);
      
      return {
        profiles: paginatedResults,
        total: mockResults.length,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(mockResults.length / limit),
          totalResults: mockResults.length,
          resultsPerPage: limit
        },
        fromCache: false
      };
    } catch (error) {
      console.error('Error generating mock advanced search results:', error);
      throw error;
    }
  }
};

/**
 * Find similar profiles to a specific user
 * @param {string} userId - User ID to find similar profiles for
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Similar profiles with similarity scores
 */
export const findSimilarProfiles = async (userId, options = {}) => {
  const { minScore = 0, maxResults = 20, skipCache = false } = options;
  
  if (isUsingRealBackend()) {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      queryParams.append('minScore', minScore);
      queryParams.append('maxResults', maxResults);
      if (skipCache) queryParams.append('skipCache', 'true');
      
      // Make API request
      const response = await fetch(`${getApiBaseUrl()}/similarity/similar-profiles/${userId}?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`Similar profiles API error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error finding similar profiles:', error);
      throw error;
    }
  } else {
    // Use mock data
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1200));
      
      // Generate mock similar profiles
      const mockResults = generateMockSimilarProfiles(userId, { minScore, maxResults });
      
      return {
        success: true,
        profiles: mockResults,
        sourceProfile: {
          id: userId,
          fullName: 'John Doe',
          age: 28,
          city: 'Mumbai',
          education: 'MASTERS',
          occupation: 'Software Engineer'
        },
        fromCache: false
      };
    } catch (error) {
      console.error('Error generating mock similar profiles:', error);
      throw error;
    }
  }
};

/**
 * Get compatibility score with a specific user
 * @param {string} userId - User ID to check compatibility with
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Compatibility scores and details
 */
export const getCompatibilityScore = async (userId, options = {}) => {
  const { skipCache = false } = options;
  
  if (isUsingRealBackend()) {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (skipCache) queryParams.append('skipCache', 'true');
      
      // Make API request
      const response = await fetch(`${getApiBaseUrl()}/similarity/compatibility/${userId}?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`Compatibility score API error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error getting compatibility score:', error);
      throw error;
    }
  } else {
    // Use mock data
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Generate mock compatibility score
      return {
        success: true,
        compatibility: {
          overallScore: 78,
          preferenceMatch: {
            total: 82,
            age: 90,
            height: 85,
            education: 100,
            occupation: 70,
            location: 60,
            religion: 100,
            caste: 100,
            income: 70
          },
          lifestyleCompatibility: {
            total: 68,
            diet: 80,
            smoking: 60,
            drinking: 65
          }
        },
        userProfile: {
          id: 'VAI12345',
          fullName: 'John Doe',
          age: 28
        },
        matchProfile: {
          id: userId,
          fullName: 'Jane Smith',
          age: 26
        },
        fromCache: false
      };
    } catch (error) {
      console.error('Error generating mock compatibility score:', error);
      throw error;
    }
  }
};

// Helper function to generate mock similar profiles (for mock data mode)
const generateMockSimilarProfiles = (userId, options) => {
  const { minScore = 0, maxResults = 20 } = options;
  const results = [];
  
  for (let i = 0; i < 30; i++) {
    // Generate a similarity score between 40 and 95
    const similarityScore = Math.floor(Math.random() * 55) + 40;
    
    // Skip if below minimum score
    if (similarityScore < minScore) continue;
    
    results.push({
      id: `VAI${10000 + i}`,
      fullName: `Similar Profile ${i + 1}`,
      age: Math.floor(Math.random() * 15) + 23,
      height: (Math.random() * (5.8 - 5.0) + 5.0).toFixed(1),
      city: ['Mumbai', 'Pune', 'Nagpur', 'Nashik', 'Aurangabad'][i % 5],
      education: ['BACHELORS', 'MASTERS', 'DOCTORATE'][i % 3],
      occupation: ['Software Engineer', 'Doctor', 'Teacher', 'Business Owner', 'Lawyer'][i % 5],
      religion: 'HINDU',
      caste: 'MARATHA',
      profilePicUrl: `/mock-profiles/female${(i % 5) + 1}.jpg`,
      isVerified: i % 3 === 0,
      isPremium: i % 4 === 0,
      similarityScore
    });
  }
  
  // Sort by similarity score (descending)
  results.sort((a, b) => b.similarityScore - a.similarityScore);
  
  // Limit results
  return results.slice(0, maxResults);
};
