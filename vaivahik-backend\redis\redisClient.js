/**
 * Redis Client Configuration
 * 
 * This module initializes and manages the Redis client connection.
 * It provides a singleton Redis client instance for the application.
 */

const Redis = require('ioredis');
const logger = require('../src/utils/logger');

// Default Redis configuration
const DEFAULT_CONFIG = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD || '',
  db: parseInt(process.env.REDIS_DB || '0'),
  retryStrategy: (times) => {
    const delay = Math.min(times * 50, 2000);
    return delay;
  }
};

// Redis client instance (singleton)
let redisClient = null;

/**
 * Initialize the Redis client
 * @param {Object} config - Redis configuration options (optional)
 * @returns {Promise<Redis>} - Redis client instance
 */
const initializeClient = async (config = {}) => {
  try {
    // Merge default config with provided config
    const redisConfig = { ...DEFAULT_CONFIG, ...config };
    
    // Create new Redis client
    redisClient = new Redis(redisConfig);
    
    // Set up event handlers
    redisClient.on('connect', () => {
      logger.info(`Redis client connected to ${redisConfig.host}:${redisConfig.port}`);
    });
    
    redisClient.on('error', (err) => {
      logger.error('Redis client error:', err);
    });
    
    redisClient.on('reconnecting', () => {
      logger.warn('Redis client reconnecting...');
    });
    
    // Test connection
    await redisClient.ping();
    
    return redisClient;
  } catch (error) {
    logger.error('Failed to initialize Redis client:', error);
    
    // Create a mock Redis client for development if real connection fails
    if (process.env.NODE_ENV !== 'production') {
      logger.warn('Using mock Redis client for development');
      return createMockRedisClient();
    }
    
    throw error;
  }
};

/**
 * Get the Redis client instance
 * @returns {Promise<Redis>} - Redis client instance
 */
const getClient = async () => {
  // If client doesn't exist or is closed, initialize it
  if (!redisClient || redisClient.status === 'end') {
    return await initializeClient();
  }
  
  return redisClient;
};

/**
 * Close the Redis client connection
 * @returns {Promise<void>}
 */
const closeClient = async () => {
  if (redisClient) {
    await redisClient.quit();
    redisClient = null;
    logger.info('Redis client connection closed');
  }
};

/**
 * Create a mock Redis client for development
 * This is used when Redis is not available
 * @returns {Object} - Mock Redis client
 */
const createMockRedisClient = () => {
  // In-memory storage for mock Redis
  const storage = new Map();
  
  // Mock Redis client with basic functionality
  return {
    status: 'ready',
    
    async set(key, value, ...args) {
      // Handle EX (expiration) argument
      let ttl = null;
      if (args.length >= 2 && args[0] === 'EX') {
        ttl = args[1];
      }
      
      storage.set(key, {
        value,
        expireAt: ttl ? Date.now() + (ttl * 1000) : null
      });
      
      return 'OK';
    },
    
    async get(key) {
      const item = storage.get(key);
      
      // Check if item exists and is not expired
      if (!item) return null;
      if (item.expireAt && item.expireAt < Date.now()) {
        storage.delete(key);
        return null;
      }
      
      return item.value;
    },
    
    async del(key) {
      if (Array.isArray(key)) {
        let count = 0;
        for (const k of key) {
          if (storage.delete(k)) count++;
        }
        return count;
      }
      
      return storage.delete(key) ? 1 : 0;
    },
    
    async keys(pattern) {
      // Simple pattern matching (only supports *)
      const keys = Array.from(storage.keys());
      
      if (pattern === '*') return keys;
      
      const regex = new RegExp('^' + pattern.replace('*', '.*') + '$');
      return keys.filter(key => regex.test(key));
    },
    
    async ping() {
      return 'PONG';
    },
    
    on() {
      // No-op for event handlers
      return this;
    },
    
    async quit() {
      storage.clear();
      return 'OK';
    }
  };
};

module.exports = {
  initializeClient,
  getClient,
  closeClient
};
