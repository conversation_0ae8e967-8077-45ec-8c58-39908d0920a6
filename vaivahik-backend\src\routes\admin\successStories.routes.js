// src/routes/admin/successStories.routes.js

const express = require('express');
const router = express.Router();
const successStoriesController = require('../../controllers/admin/successStories.controller');
const { authenticateAdmin } = require('../../middleware/auth.middleware');

// Apply admin authentication middleware to all routes
router.use(authenticateAdmin);

// Get all success stories
router.get('/', successStoriesController.getSuccessStories);

// Get success story by ID
router.get('/:id', successStoriesController.getSuccessStoryById);

// Create new success story
router.post('/', successStoriesController.createSuccessStory);

// Update success story
router.put('/:id', successStoriesController.updateSuccessStory);

// Delete success story
router.delete('/:id', successStoriesController.deleteSuccessStory);

// Get success stories statistics
router.get('/stats/overview', successStoriesController.getSuccessStoriesStats);

module.exports = router;
