"""
Performance Monitor for Matrimony Matching

This module provides performance monitoring and logging functionality
for the matrimony matching system.
"""

import os
import json
import logging
import time
import asyncio
import psutil
import torch
from datetime import datetime, timedelta
from prisma.client import PrismaClient
from collections import defaultdict, deque

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(os.path.dirname(__file__), '../../logs/performance.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """Performance monitor for matrimony matching"""
    
    def __init__(self, redis_cache=None, config=None):
        """
        Initialize the performance monitor
        
        Args:
            redis_cache: Redis cache instance
            config (dict): Configuration parameters
        """
        # Default configuration
        self.default_config = {
            'monitoring_interval': 300,  # 5 minutes in seconds
            'metrics_ttl': 86400 * 7,    # 7 days in seconds
            'alert_thresholds': {
                'cpu_usage': 80,         # Percentage
                'memory_usage': 80,      # Percentage
                'response_time': 1000,   # Milliseconds
                'error_rate': 5          # Percentage
            },
            'log_dir': os.path.join(os.path.dirname(__file__), '../../logs')
        }
        
        # Use provided config or default
        self.config = config if config else self.default_config
        
        # Initialize Prisma client
        self.prisma = PrismaClient()
        
        # Initialize Redis cache
        self.redis_cache = redis_cache
        
        # Create log directory if it doesn't exist
        os.makedirs(self.config['log_dir'], exist_ok=True)
        
        # Initialize metrics
        self.metrics = {
            'system': {
                'cpu_usage': [],
                'memory_usage': [],
                'disk_usage': []
            },
            'api': {
                'response_times': defaultdict(list),
                'error_counts': defaultdict(int),
                'request_counts': defaultdict(int)
            },
            'model': {
                'inference_times': [],
                'batch_sizes': [],
                'cache_hits': 0,
                'cache_misses': 0
            },
            'database': {
                'query_times': defaultdict(list),
                'query_counts': defaultdict(int)
            }
        }
        
        # Initialize recent metrics (for real-time monitoring)
        self.recent_metrics = {
            'response_times': deque(maxlen=100),
            'error_counts': 0,
            'request_counts': 0,
            'inference_times': deque(maxlen=100)
        }
    
    def record_api_request(self, endpoint, response_time, status_code):
        """
        Record an API request
        
        Args:
            endpoint (str): API endpoint
            response_time (float): Response time in milliseconds
            status_code (int): HTTP status code
        """
        # Record in metrics
        self.metrics['api']['response_times'][endpoint].append(response_time)
        self.metrics['api']['request_counts'][endpoint] += 1
        
        if status_code >= 400:
            self.metrics['api']['error_counts'][endpoint] += 1
        
        # Record in recent metrics
        self.recent_metrics['response_times'].append(response_time)
        self.recent_metrics['request_counts'] += 1
        
        if status_code >= 400:
            self.recent_metrics['error_counts'] += 1
        
        # Check for alerts
        self._check_alerts(endpoint, response_time, status_code)
    
    def record_model_inference(self, inference_time, batch_size=1, cache_hit=False):
        """
        Record a model inference
        
        Args:
            inference_time (float): Inference time in milliseconds
            batch_size (int): Batch size
            cache_hit (bool): Whether the result was from cache
        """
        # Record in metrics
        self.metrics['model']['inference_times'].append(inference_time)
        self.metrics['model']['batch_sizes'].append(batch_size)
        
        if cache_hit:
            self.metrics['model']['cache_hits'] += 1
        else:
            self.metrics['model']['cache_misses'] += 1
        
        # Record in recent metrics
        self.recent_metrics['inference_times'].append(inference_time)
    
    def record_database_query(self, query_type, query_time):
        """
        Record a database query
        
        Args:
            query_type (str): Query type
            query_time (float): Query time in milliseconds
        """
        # Record in metrics
        self.metrics['database']['query_times'][query_type].append(query_time)
        self.metrics['database']['query_counts'][query_type] += 1
    
    def _check_alerts(self, endpoint, response_time, status_code):
        """
        Check for performance alerts
        
        Args:
            endpoint (str): API endpoint
            response_time (float): Response time in milliseconds
            status_code (int): HTTP status code
        """
        # Check response time
        if response_time > self.config['alert_thresholds']['response_time']:
            logger.warning(f"High response time for {endpoint}: {response_time}ms")
        
        # Check error rate
        if len(self.recent_metrics['response_times']) >= 10:
            error_rate = (self.recent_metrics['error_counts'] / self.recent_metrics['request_counts']) * 100
            if error_rate > self.config['alert_thresholds']['error_rate']:
                logger.warning(f"High error rate: {error_rate:.2f}%")
    
    async def collect_system_metrics(self):
        """Collect system metrics"""
        try:
            # Get CPU usage
            cpu_usage = psutil.cpu_percent(interval=1)
            self.metrics['system']['cpu_usage'].append(cpu_usage)
            
            # Get memory usage
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            self.metrics['system']['memory_usage'].append(memory_usage)
            
            # Get disk usage
            disk = psutil.disk_usage('/')
            disk_usage = disk.percent
            self.metrics['system']['disk_usage'].append(disk_usage)
            
            # Check for alerts
            if cpu_usage > self.config['alert_thresholds']['cpu_usage']:
                logger.warning(f"High CPU usage: {cpu_usage}%")
            
            if memory_usage > self.config['alert_thresholds']['memory_usage']:
                logger.warning(f"High memory usage: {memory_usage}%")
            
            # Log system metrics
            logger.info(f"System metrics - CPU: {cpu_usage}%, Memory: {memory_usage}%, Disk: {disk_usage}%")
            
            # Get GPU metrics if available
            if torch.cuda.is_available():
                for i in range(torch.cuda.device_count()):
                    gpu_usage = torch.cuda.utilization(i)
                    gpu_memory = torch.cuda.memory_allocated(i) / torch.cuda.max_memory_allocated(i) * 100 if torch.cuda.max_memory_allocated(i) > 0 else 0
                    logger.info(f"GPU {i} - Usage: {gpu_usage}%, Memory: {gpu_memory:.2f}%")
        except Exception as e:
            logger.error(f"Error collecting system metrics: {str(e)}")
    
    async def collect_database_metrics(self):
        """Collect database metrics"""
        try:
            # Get database statistics
            # This is a placeholder - in a real implementation, you would get metrics from your database
            
            # Log database metrics
            query_counts = sum(self.metrics['database']['query_counts'].values())
            avg_query_time = 0
            if query_counts > 0:
                total_query_time = sum([sum(times) for times in self.metrics['database']['query_times'].values()])
                avg_query_time = total_query_time / query_counts
            
            logger.info(f"Database metrics - Queries: {query_counts}, Avg time: {avg_query_time:.2f}ms")
        except Exception as e:
            logger.error(f"Error collecting database metrics: {str(e)}")
    
    async def collect_cache_metrics(self):
        """Collect cache metrics"""
        try:
            if self.redis_cache and self.redis_cache.is_connected():
                # Get cache statistics
                # This is a placeholder - in a real implementation, you would get metrics from Redis
                
                # Log cache metrics
                cache_hits = self.metrics['model']['cache_hits']
                cache_misses = self.metrics['model']['cache_misses']
                cache_hit_rate = 0
                if cache_hits + cache_misses > 0:
                    cache_hit_rate = cache_hits / (cache_hits + cache_misses) * 100
                
                logger.info(f"Cache metrics - Hits: {cache_hits}, Misses: {cache_misses}, Hit rate: {cache_hit_rate:.2f}%")
        except Exception as e:
            logger.error(f"Error collecting cache metrics: {str(e)}")
    
    async def collect_api_metrics(self):
        """Collect API metrics"""
        try:
            # Calculate API metrics
            total_requests = sum(self.metrics['api']['request_counts'].values())
            total_errors = sum(self.metrics['api']['error_counts'].values())
            error_rate = 0
            if total_requests > 0:
                error_rate = (total_errors / total_requests) * 100
            
            # Calculate average response time
            avg_response_time = 0
            if total_requests > 0:
                total_response_time = sum([sum(times) for times in self.metrics['api']['response_times'].values()])
                avg_response_time = total_response_time / total_requests
            
            # Log API metrics
            logger.info(f"API metrics - Requests: {total_requests}, Errors: {total_errors}, Error rate: {error_rate:.2f}%, Avg response time: {avg_response_time:.2f}ms")
            
            # Log per-endpoint metrics
            for endpoint, count in self.metrics['api']['request_counts'].items():
                errors = self.metrics['api']['error_counts'].get(endpoint, 0)
                endpoint_error_rate = 0
                if count > 0:
                    endpoint_error_rate = (errors / count) * 100
                
                avg_endpoint_response_time = 0
                if count > 0:
                    total_endpoint_response_time = sum(self.metrics['api']['response_times'].get(endpoint, []))
                    avg_endpoint_response_time = total_endpoint_response_time / count
                
                logger.info(f"Endpoint {endpoint} - Requests: {count}, Errors: {errors}, Error rate: {endpoint_error_rate:.2f}%, Avg response time: {avg_endpoint_response_time:.2f}ms")
        except Exception as e:
            logger.error(f"Error collecting API metrics: {str(e)}")
    
    async def collect_model_metrics(self):
        """Collect model metrics"""
        try:
            # Calculate model metrics
            inference_count = len(self.metrics['model']['inference_times'])
            avg_inference_time = 0
            if inference_count > 0:
                avg_inference_time = sum(self.metrics['model']['inference_times']) / inference_count
            
            avg_batch_size = 0
            if inference_count > 0:
                avg_batch_size = sum(self.metrics['model']['batch_sizes']) / inference_count
            
            # Log model metrics
            logger.info(f"Model metrics - Inferences: {inference_count}, Avg time: {avg_inference_time:.2f}ms, Avg batch size: {avg_batch_size:.2f}")
        except Exception as e:
            logger.error(f"Error collecting model metrics: {str(e)}")
    
    async def save_metrics(self):
        """Save metrics to file"""
        try:
            # Create timestamp
            timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
            
            # Create metrics file path
            metrics_path = os.path.join(self.config['log_dir'], f"metrics_{timestamp}.json")
            
            # Convert metrics to JSON-serializable format
            serializable_metrics = {
                'system': {
                    'cpu_usage': self.metrics['system']['cpu_usage'],
                    'memory_usage': self.metrics['system']['memory_usage'],
                    'disk_usage': self.metrics['system']['disk_usage']
                },
                'api': {
                    'response_times': {k: list(v) for k, v in self.metrics['api']['response_times'].items()},
                    'error_counts': dict(self.metrics['api']['error_counts']),
                    'request_counts': dict(self.metrics['api']['request_counts'])
                },
                'model': {
                    'inference_times': self.metrics['model']['inference_times'],
                    'batch_sizes': self.metrics['model']['batch_sizes'],
                    'cache_hits': self.metrics['model']['cache_hits'],
                    'cache_misses': self.metrics['model']['cache_misses']
                },
                'database': {
                    'query_times': {k: list(v) for k, v in self.metrics['database']['query_times'].items()},
                    'query_counts': dict(self.metrics['database']['query_counts'])
                },
                'timestamp': timestamp
            }
            
            # Save to file
            with open(metrics_path, 'w') as f:
                json.dump(serializable_metrics, f, indent=2)
            
            logger.info(f"Metrics saved to {metrics_path}")
            
            # Reset metrics
            self._reset_metrics()
        except Exception as e:
            logger.error(f"Error saving metrics: {str(e)}")
    
    def _reset_metrics(self):
        """Reset metrics"""
        self.metrics = {
            'system': {
                'cpu_usage': [],
                'memory_usage': [],
                'disk_usage': []
            },
            'api': {
                'response_times': defaultdict(list),
                'error_counts': defaultdict(int),
                'request_counts': defaultdict(int)
            },
            'model': {
                'inference_times': [],
                'batch_sizes': [],
                'cache_hits': 0,
                'cache_misses': 0
            },
            'database': {
                'query_times': defaultdict(list),
                'query_counts': defaultdict(int)
            }
        }
    
    async def start_monitoring(self):
        """Start the monitoring scheduler"""
        while True:
            try:
                # Collect metrics
                await self.collect_system_metrics()
                await self.collect_database_metrics()
                await self.collect_cache_metrics()
                await self.collect_api_metrics()
                await self.collect_model_metrics()
                
                # Save metrics
                await self.save_metrics()
                
                # Sleep until next collection
                await asyncio.sleep(self.config['monitoring_interval'])
            except Exception as e:
                logger.error(f"Error in monitoring scheduler: {str(e)}")
                await asyncio.sleep(60)  # Sleep for a minute before retrying
