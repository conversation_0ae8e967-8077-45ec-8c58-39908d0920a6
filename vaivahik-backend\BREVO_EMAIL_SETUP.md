# 📧 Brevo Email Service Setup (Free)

## Why Brevo?
- ✅ **300 emails/day FREE** (vs SendGrid's 100/day)
- ✅ **Advanced templates** for matching notifications
- ✅ **Email automation** for user engagement
- ✅ **Detailed analytics** for email performance

## Setup Steps

### 1. Create Brevo Account
1. Go to [Brevo.com](https://www.brevo.com/)
2. Sign up for free account
3. Verify your email

### 2. Get API Key
1. Go to **Account Settings** → **API Keys**
2. Create new API key
3. Copy the key

### 3. Update .env File
```env
# Replace your current SMTP settings with:
SMTP_HOST=smtp-relay.brevo.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-brevo-api-key
SMTP_SECURE=false
EMAIL_FROM="Vaivahik" <<EMAIL>>
```

### 4. Verify Domain (Optional but Recommended)
1. In Brevo dashboard, go to **Senders & IP**
2. Add your domain
3. Add DNS records they provide
4. This improves email deliverability

## Email Templates for Your App

### 1. Match Notification Email
```html
<h2>New Match Found! 💕</h2>
<p>Hi {{user_name}},</p>
<p>We found a great match for you:</p>
<div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0;">
    <img src="{{match_photo}}" style="width: 100px; height: 100px; border-radius: 50%;">
    <h3>{{match_name}}</h3>
    <p>Age: {{match_age}} | Location: {{match_location}}</p>
    <p>Education: {{match_education}}</p>
    <a href="{{profile_link}}" style="background: #7e57c2; color: white; padding: 10px 20px; text-decoration: none;">View Profile</a>
</div>
```

### 2. Promotional Email
```html
<h2>Special Offer - Premium Membership! 🎉</h2>
<p>Hi {{user_name}},</p>
<p>Upgrade to Premium and get:</p>
<ul>
    <li>Unlimited profile views</li>
    <li>Priority matching</li>
    <li>Direct contact details</li>
</ul>
<a href="{{upgrade_link}}">Upgrade Now - 50% Off!</a>
```

## Integration with Your App

### Update email.service.js
```javascript
// Add Brevo-specific templates
const templates = {
    'match_notification': (data) => `
        <h2>New Match Found! 💕</h2>
        <p>Hi ${data.userName},</p>
        <p>We found a great match: ${data.matchName}</p>
        <a href="${data.profileLink}">View Profile</a>
    `,
    'promotional': (data) => `
        <h2>Special Offer! 🎉</h2>
        <p>Hi ${data.userName},</p>
        <p>${data.offerText}</p>
        <a href="${data.offerLink}">Learn More</a>
    `
};
```

## Test Email Service
```bash
# After setup, test with:
node scripts/test-email.js
```

## Free Tier Limits
- **Daily:** 300 emails
- **Monthly:** 9,000 emails
- **Perfect for:** Matrimony app with moderate user base

## Upgrade Path
When you grow:
- **Lite Plan:** €25/month for 20,000 emails
- **Premium:** €65/month for 40,000 emails + advanced features
