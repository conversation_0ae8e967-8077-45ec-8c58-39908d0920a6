import { PrismaClient } from '@prisma/client';
// Commented out next-auth imports as they're not available
// import { getServerSession } from 'next-auth/next';
// import { authOptions } from '../../auth/[...nextauth]';
import fs from 'fs';
import path from 'path';

// Mock session for development
const mockSession = {
  user: {
    id: 'user-123',
    name: 'Test User',
    email: '<EMAIL>'
  }
};

// Mock PDF service
const pdfService = {
  generateBiodata: async (userId, templateId, preview) => {
    return {
      success: true,
      filePath: '/mock-biodata.pdf',
      fileName: 'mock-biodata.pdf'
    };
  }
};

const prisma = new PrismaClient();

export default async function handler(req, res) {
  // Use mock session for development
  const session = mockSession;

  // In production, we would use next-auth
  // const session = await getServerSession(req, res, authOptions);

  if (!session || !session.user) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { templateId, preview = false } = req.body;
    const userId = session.user.id;

    if (!templateId) {
      return res.status(400).json({ success: false, message: 'Template ID is required' });
    }

    // Check if template exists
    const template = await prisma.biodataTemplate.findUnique({
      where: { id: templateId }
    });

    if (!template) {
      return res.status(404).json({ success: false, message: 'Template not found' });
    }

    // If not a preview, check if user has purchased this template
    if (!preview) {
      const userBiodata = await prisma.userBiodata.findFirst({
        where: {
          userId,
          templateId
        }
      });

      if (!userBiodata) {
        return res.status(403).json({
          success: false,
          message: 'You need to purchase this template first',
          purchaseRequired: true
        });
      }
    }

    // Check if user profile exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { profile: true }
    });

    if (!user || !user.profile) {
      return res.status(404).json({
        success: false,
        message: 'User profile not found or incomplete. Please complete your profile first.'
      });
    }

    // Generate PDF using our service
    const result = await pdfService.generateBiodata(userId, templateId, preview);

    // If it's a preview, return the file path
    if (preview) {
      return res.status(200).json({
        success: true,
        previewUrl: result.filePath
      });
    }

    // For actual download, stream the file
    const filePath = path.join(process.cwd(), 'public', result.filePath);
    const stat = fs.statSync(filePath);

    res.setHeader('Content-Length', stat.size);
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=${result.fileName}`);

    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
  } catch (error) {
    console.error('Error generating biodata PDF:', error);
    return res.status(500).json({ success: false, message: 'Failed to generate biodata PDF' });
  }
}
