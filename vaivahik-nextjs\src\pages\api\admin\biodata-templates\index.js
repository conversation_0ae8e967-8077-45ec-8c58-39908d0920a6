import { withAuth } from '@/utils/authHandler';
import { handleApiError } from '@/utils/errorHandler';
import multer from 'multer';
import { createRouter } from 'next-connect';
import path from 'path';
import fs from 'fs';

// Mock data for development
const mockBiodataTemplates = [
  {
    id: 1,
    name: "Traditional Maratha Style",
    description: "Classic Maratha style biodata with traditional elements and design",
    previewImage: "/images/biodata-templates/traditional-maratha.jpg",
    designFile: "/templates/biodata/traditional-maratha.html",
    price: 499,
    discountPercent: 10,
    discountedPrice: 449.1,
    isActive: true,
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(),
    purchaseCount: 45,
    downloadCount: 38,
    revenue: 20205
  },
  {
    id: 2,
    name: "Modern Professional",
    description: "Clean and professional biodata template with modern design elements",
    previewImage: "/images/biodata-templates/modern-professional.jpg",
    designFile: "/templates/biodata/modern-professional.html",
    price: 599,
    discountPercent: null,
    discountedPrice: null,
    isActive: true,
    createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(),
    purchaseCount: 32,
    downloadCount: 28,
    revenue: 19168
  },
  {
    id: 3,
    name: "Elegant Floral",
    description: "Beautiful floral-themed biodata template with elegant typography",
    previewImage: "/images/biodata-templates/elegant-floral.jpg",
    designFile: "/templates/biodata/elegant-floral.html",
    price: 649,
    discountPercent: 15,
    discountedPrice: 551.65,
    isActive: true,
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(),
    purchaseCount: 28,
    downloadCount: 25,
    revenue: 15446.2
  },
  {
    id: 4,
    name: "Royal Heritage",
    description: "Premium template inspired by royal Maratha heritage and traditions",
    previewImage: "/images/biodata-templates/royal-heritage.jpg",
    designFile: "/templates/biodata/royal-heritage.html",
    price: 799,
    discountPercent: 5,
    discountedPrice: 759.05,
    isActive: true,
    createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(),
    purchaseCount: 18,
    downloadCount: 15,
    revenue: 13662.9
  }
];

// In production, we would import Prisma
let prisma;
if (process.env.NODE_ENV === 'production') {
  try {
    const { PrismaClient } = require('@prisma/client');
    prisma = new PrismaClient();
  } catch (error) {
    console.error('Failed to initialize Prisma client:', error);
  }
}

// Configure multer for file uploads (only in production)
let upload;
if (process.env.NODE_ENV === 'production') {
  try {
    upload = multer({
      storage: multer.diskStorage({
        destination: (req, file, cb) => {
          const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'biodata-templates');

          // Create directory if it doesn't exist
          if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
          }

          cb(null, uploadDir);
        },
        filename: (req, file, cb) => {
          const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
          const ext = path.extname(file.originalname);
          cb(null, file.fieldname + '-' + uniqueSuffix + ext);
        }
      }),
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
      },
      fileFilter: (req, file, cb) => {
        // Accept images and HTML/CSS files
        const filetypes = /jpeg|jpg|png|gif|html|css|zip/;
        const mimetype = filetypes.test(file.mimetype);
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

        if (mimetype && extname) {
          return cb(null, true);
        }

        cb(new Error('Only image, HTML, CSS, and ZIP files are allowed'));
      }
    });
  } catch (error) {
    console.error('Failed to initialize multer:', error);
  }
} else {
  // In development, create a mock upload middleware
  upload = {
    fields: () => (req, res, next) => {
      // Mock the req.files object
      req.files = {
        previewImage: req.body.previewImage ? [{ filename: 'mock-preview-image.jpg' }] : undefined,
        designFile: req.body.designFile ? [{ filename: 'mock-design-file.html' }] : undefined
      };
      next();
    }
  };
}

// Create a router
const router = createRouter();

// Middleware to check admin authentication using withAuth utility in production
// In development mode, we'll handle auth check in the route handlers
async function checkAdminAuth(req, res, next) {
  if (process.env.NODE_ENV === 'development') {
    // Skip auth check in development
    return next();
  }

  try {
    // In production, we would check the session
    // This code won't run in development mode
    const { getServerSession } = require('next-auth/next');
    const { authOptions } = require('../../auth/[...nextauth]');

    const session = await getServerSession(req, res, authOptions);

    if (!session || !session.user || session.user.role !== 'ADMIN') {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    next();
  } catch (error) {
    console.error('Auth check error:', error);
    return res.status(401).json({ success: false, message: 'Authentication error' });
  }
}

// Apply middleware
router.use(checkAdminAuth);

// Handle GET request to fetch all templates
router.get(async (req, res) => {
  try {
    // In development mode, return mock data
    if (process.env.NODE_ENV === 'development') {
      return res.status(200).json({ success: true, templates: mockBiodataTemplates });
    }

    // In production, use Prisma to fetch data from the database
    const templates = await prisma.biodataTemplate.findMany({
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        _count: {
          select: { userBiodatas: true }
        }
      }
    });

    // Enhance templates with additional stats
    const enhancedTemplates = await Promise.all(templates.map(async (template) => {
      // Get total downloads
      const downloadCount = await prisma.userBiodata.aggregate({
        where: {
          templateId: template.id
        },
        _sum: {
          downloadCount: true
        }
      });

      // Get total revenue
      const revenue = await prisma.userBiodata.aggregate({
        where: {
          templateId: template.id
        },
        _sum: {
          pricePaid: true
        }
      });

      return {
        ...template,
        purchaseCount: template._count.userBiodatas,
        downloadCount: downloadCount._sum?.downloadCount || 0,
        revenue: revenue._sum?.pricePaid || 0
      };
    }));

    return res.status(200).json({ success: true, templates: enhancedTemplates });
  } catch (error) {
    console.error('Error fetching biodata templates:', error);

    // If there's an error in production, return mock data in development mode
    if (process.env.NODE_ENV === 'development') {
      return res.status(200).json({ success: true, templates: mockBiodataTemplates });
    }

    return res.status(500).json({ success: false, message: 'Failed to fetch biodata templates' });
  }
});

// Handle POST request to create a new template
router.post(process.env.NODE_ENV === 'production'
  ? upload.fields([
      { name: 'previewImage', maxCount: 1 },
      { name: 'designFile', maxCount: 1 }
    ])
  : upload.fields(), async (req, res) => {
  try {
    const {
      name,
      description,
      price,
      discountPercent
    } = req.body;

    // Validate required fields
    if (!name || !price) {
      return res.status(400).json({ success: false, message: 'Name and price are required' });
    }

    // In development mode, create a mock template
    if (process.env.NODE_ENV === 'development') {
      // Calculate discounted price if discount is provided
      const parsedPrice = parseFloat(price);
      let discountedPrice = null;
      let parsedDiscountPercent = null;

      if (discountPercent && parseInt(discountPercent) > 0) {
        parsedDiscountPercent = parseInt(discountPercent);
        discountedPrice = parsedPrice - (parsedPrice * (parsedDiscountPercent / 100));
      }

      // Create a mock template
      const mockTemplate = {
        id: mockBiodataTemplates.length + 1,
        name,
        description: description || null,
        previewImage: "/images/biodata-templates/template-placeholder.jpg",
        designFile: "/templates/biodata/template-placeholder.html",
        price: parsedPrice,
        discountPercent: parsedDiscountPercent,
        discountedPrice,
        isActive: req.body.isActive === 'true',
        createdAt: new Date(),
        updatedAt: new Date(),
        purchaseCount: 0,
        downloadCount: 0,
        revenue: 0
      };

      // Add to mock data for future requests
      mockBiodataTemplates.push(mockTemplate);

      return res.status(201).json({ success: true, template: mockTemplate });
    }

    // In production, continue with file upload and database creation

    // Check if files were uploaded
    if (!req.files || !req.files.previewImage || !req.files.designFile) {
      return res.status(400).json({ success: false, message: 'Preview image and design file are required' });
    }

    // Get file paths
    const previewImagePath = `/uploads/biodata-templates/${req.files.previewImage[0].filename}`;
    const designFilePath = `/uploads/biodata-templates/${req.files.designFile[0].filename}`;

    // Calculate discounted price if discount is provided
    const parsedPrice = parseFloat(price);
    let discountedPrice = null;

    if (discountPercent && parseInt(discountPercent) > 0) {
      const discount = parseInt(discountPercent);
      discountedPrice = parsedPrice - (parsedPrice * (discount / 100));
    }

    // Create the template
    const template = await prisma.biodataTemplate.create({
      data: {
        name,
        description: description || null,
        previewImage: previewImagePath,
        designFile: designFilePath,
        price: parsedPrice,
        discountPercent: discountPercent ? parseInt(discountPercent) : null,
        discountedPrice,
        isActive: req.body.isActive === 'true'
      }
    });

    return res.status(201).json({ success: true, template });
  } catch (error) {
    console.error('Error creating biodata template:', error);

    // If there's an error in production, return a mock success in development mode
    if (process.env.NODE_ENV === 'development') {
      const parsedPrice = parseFloat(req.body.price) || 599;
      let discountedPrice = null;
      let parsedDiscountPercent = null;

      if (req.body.discountPercent && parseInt(req.body.discountPercent) > 0) {
        parsedDiscountPercent = parseInt(req.body.discountPercent);
        discountedPrice = parsedPrice - (parsedPrice * (parsedDiscountPercent / 100));
      }

      const mockTemplate = {
        id: mockBiodataTemplates.length + 1,
        name: req.body.name || 'New Biodata Template',
        description: req.body.description || null,
        previewImage: "/images/biodata-templates/template-placeholder.jpg",
        designFile: "/templates/biodata/template-placeholder.html",
        price: parsedPrice,
        discountPercent: parsedDiscountPercent,
        discountedPrice,
        isActive: req.body.isActive === 'true',
        createdAt: new Date(),
        updatedAt: new Date(),
        purchaseCount: 0,
        downloadCount: 0,
        revenue: 0
      };

      // Add to mock data for future requests
      mockBiodataTemplates.push(mockTemplate);

      return res.status(201).json({ success: true, template: mockTemplate });
    }

    return res.status(500).json({ success: false, message: 'Failed to create biodata template' });
  }
});

// Export the router
export default router.handler({
  onError: (err, req, res) => {
    console.error(err.stack);
    res.status(500).json({ success: false, message: err.message });
  },
  onNoMatch: (req, res) => {
    res.status(405).json({ success: false, message: `Method '${req.method}' not allowed` });
  },
});

// Configure Next.js to handle file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};
