// Import the auth utility
// Using relative path instead of alias to avoid import issues
import { verifyAdminToken } from '../../../../../utils/auth';

export default async function handler(req, res) {
  console.log('API: Received request to /api/admin/chat-settings/moderation');
  console.log('Request method:', req.method);

  // Check if the request method is PUT
  if (req.method !== 'PUT') {
    console.log('Method not allowed:', req.method);
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Log auth header for debugging
    console.log('Auth header exists:', !!req.headers.authorization);

    // Verify admin token
    const adminData = verifyAdminToken(req);
    if (!adminData) {
      console.log('Unauthorized: Invalid or missing token');
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    console.log('Admin authenticated:', adminData.name);

    // Get the moderation settings from the request body
    const moderationSettings = req.body;
    console.log('Received moderation settings:', moderationSettings);

    // Validate the moderation settings
    if (!moderationSettings) {
      console.log('Error: No moderation settings provided');
      return res.status(400).json({ success: false, message: 'Moderation settings are required' });
    }

    // In a real application, you would save these settings to your database
    console.log('Successfully processed moderation settings');

    // Return success response
    const response = {
      success: true,
      message: 'Moderation settings updated successfully',
      settings: moderationSettings
    };

    console.log('Sending response:', response);
    return res.status(200).json(response);
  } catch (error) {
    console.error('Error in moderation settings API:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
}
