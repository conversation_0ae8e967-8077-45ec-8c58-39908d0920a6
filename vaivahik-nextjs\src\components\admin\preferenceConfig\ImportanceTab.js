import { useState } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Switch,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControlLabel,
  Tooltip,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Slider,
  Alert
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import InfoIcon from '@mui/icons-material/Info';

export default function ImportanceTab({ importanceSettings, fields, categories, onUpdate, onDelete }) {
  const [editingImportance, setEditingImportance] = useState(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [importanceToDelete, setImportanceToDelete] = useState(null);

  const genderOptions = [
    { value: 'MALE', label: 'Male' },
    { value: 'FEMALE', label: 'Female' },
    { value: 'ALL', label: 'All Genders' }
  ];

  const handleAddImportance = () => {
    setEditingImportance({
      id: '',
      importanceLevel: 5.0,
      description: '',
      isActive: true,
      fieldId: '',
      gender: 'ALL'
    });
    setIsDialogOpen(true);
  };

  const handleEditImportance = (importance) => {
    setEditingImportance({ ...importance });
    setIsDialogOpen(true);
  };

  const handleDeleteImportance = (importance) => {
    setImportanceToDelete(importance);
    setDeleteConfirmOpen(true);
  };

  const confirmDeleteImportance = () => {
    if (onDelete) {
      // Use the API delete function
      onDelete(importanceToDelete.id);
    } else {
      // Fallback to client-side filtering
      // Filter out the importance to delete
      const updatedImportanceSettings = importanceSettings.filter(imp => imp.id !== importanceToDelete.id);
      onUpdate(updatedImportanceSettings);
    }

    setDeleteConfirmOpen(false);
    setImportanceToDelete(null);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditingImportance(null);
  };

  const handleSaveImportance = () => {
    if (!editingImportance.fieldId || !editingImportance.gender) {
      // Show validation error
      return;
    }

    // Check if there's already an importance setting for this field and gender
    const existingImportance = importanceSettings.find(
      imp => imp.fieldId === editingImportance.fieldId &&
             imp.gender === editingImportance.gender &&
             imp.id !== editingImportance.id
    );

    if (existingImportance) {
      alert(`An importance setting already exists for this field and gender. Please edit the existing one.`);
      return;
    }

    let updatedImportanceSettings;

    if (editingImportance.id) {
      // Update existing importance
      updatedImportanceSettings = importanceSettings.map(imp =>
        imp.id === editingImportance.id ? editingImportance : imp
      );
    } else {
      // Add new importance with a generated ID
      const newImportance = {
        ...editingImportance,
        id: `imp${Date.now()}` // Simple ID generation
      };
      updatedImportanceSettings = [...importanceSettings, newImportance];
    }

    onUpdate(updatedImportanceSettings);
    setIsDialogOpen(false);
    setEditingImportance(null);
  };

  const handleInputChange = (e) => {
    const { name, value, checked, type } = e.target;

    setEditingImportance(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSliderChange = (event, newValue) => {
    setEditingImportance(prev => ({
      ...prev,
      importanceLevel: newValue
    }));
  };

  // Group fields by category for better organization
  const fieldsByCategory = {};
  categories.forEach(category => {
    fieldsByCategory[category.id] = fields.filter(field => field.categoryId === category.id);
  });

  return (
    <Box>
      <Alert severity="info" sx={{ mb: 3 }}>
        Importance settings determine how much weight each preference has in the matching algorithm.
        Higher values (0-10) give more importance to that preference when calculating match scores.
      </Alert>

      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleAddImportance}
        >
          Add Importance Setting
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Field</TableCell>
              <TableCell>Category</TableCell>
              <TableCell>Gender</TableCell>
              <TableCell>Importance Level</TableCell>
              <TableCell>Description</TableCell>
              <TableCell>Active</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {importanceSettings.map((importance) => {
              const field = fields.find(f => f.id === importance.fieldId);
              const category = field ? categories.find(c => c.id === field.categoryId) : null;

              return (
                <TableRow key={importance.id}>
                  <TableCell>{field?.displayName || 'Unknown Field'}</TableCell>
                  <TableCell>{category?.displayName || 'Unknown Category'}</TableCell>
                  <TableCell>
                    {genderOptions.find(g => g.value === importance.gender)?.label || importance.gender}
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Box sx={{ width: 100, mr: 2 }}>
                        <Slider
                          value={importance.importanceLevel}
                          min={0}
                          max={10}
                          step={0.1}
                          disabled
                          size="small"
                        />
                      </Box>
                      <Typography variant="body2">
                        {importance.importanceLevel.toFixed(1)}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>{importance.description}</TableCell>
                  <TableCell>
                    <Switch
                      checked={importance.isActive}
                      disabled
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton
                      color="primary"
                      size="small"
                      onClick={() => handleEditImportance(importance)}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      color="error"
                      size="small"
                      onClick={() => handleDeleteImportance(importance)}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Edit/Add Importance Dialog */}
      <Dialog open={isDialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingImportance?.id ? 'Edit Importance Setting' : 'Add Importance Setting'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth required>
                <InputLabel>Field</InputLabel>
                <Select
                  name="fieldId"
                  value={editingImportance?.fieldId || ''}
                  onChange={handleInputChange}
                  label="Field"
                >
                  {categories.map((category) => (
                    <MenuItem key={category.id} disabled>
                      <Typography variant="subtitle2">
                        {category.displayName}
                      </Typography>
                    </MenuItem>
                  )).reduce((acc, categoryItem, index) => {
                    const categoryId = categories[index].id;
                    const categoryFields = fieldsByCategory[categoryId] || [];

                    return [
                      ...acc,
                      categoryItem,
                      ...categoryFields.map(field => (
                        <MenuItem key={field.id} value={field.id} sx={{ pl: 4 }}>
                          {field.displayName}
                        </MenuItem>
                      ))
                    ];
                  }, [])}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth required>
                <InputLabel>Gender</InputLabel>
                <Select
                  name="gender"
                  value={editingImportance?.gender || ''}
                  onChange={handleInputChange}
                  label="Gender"
                >
                  {genderOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <Typography gutterBottom>
                Importance Level (0-10)
                <Tooltip title="Higher values give more weight to this preference in the matching algorithm">
                  <IconButton size="small">
                    <InfoIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Slider
                  value={editingImportance?.importanceLevel || 5}
                  onChange={handleSliderChange}
                  min={0}
                  max={10}
                  step={0.1}
                  marks={[
                    { value: 0, label: '0' },
                    { value: 5, label: '5' },
                    { value: 10, label: '10' }
                  ]}
                  valueLabelDisplay="auto"
                  sx={{ flex: 1, mr: 2 }}
                />
                <Typography>
                  {editingImportance?.importanceLevel.toFixed(1) || '5.0'}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="description"
                label="Description"
                value={editingImportance?.description || ''}
                onChange={handleInputChange}
                fullWidth
                multiline
                rows={2}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    name="isActive"
                    checked={editingImportance?.isActive || false}
                    onChange={handleInputChange}
                  />
                }
                label="Active"
              />
              <Tooltip title="If inactive, this importance setting will not be used in the matching algorithm">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSaveImportance} variant="contained" color="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteConfirmOpen} onClose={() => setDeleteConfirmOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this importance setting?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button onClick={confirmDeleteImportance} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
