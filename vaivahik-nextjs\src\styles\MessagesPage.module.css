/* MessagesPage.module.css */

.messagesPage {
  display: flex;
  height: calc(100vh - 80px); /* Adjust based on your header height */
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  position: relative;
}

.conversationListContainer {
  width: 350px;
  margin-right: 20px;
  flex-shrink: 0;
}

.chatContainer {
  flex: 1;
}

.emptyStateContainer {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emptyState {
  text-align: center;
  padding: 40px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 400px;
}

.emptyStateIcon {
  font-size: 48px;
  margin-bottom: 20px;
}

.emptyState h2 {
  margin: 0 0 10px;
  color: #1f2937;
}

.emptyState p {
  margin: 0 0 20px;
  color: #6b7280;
}

.newChatButton {
  background-color: #7e3af2;
  color: white;
  border: none;
  border-radius: 24px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.newChatButton:hover {
  background-color: #6c2ce9;
}

.promotionContainer {
  width: 100%;
  padding: 0 20px;
  margin-bottom: 20px;
}

.promotionHighlight {
  background-color: rgba(126, 58, 242, 0.1);
  border-left: 3px solid #7e3af2;
  padding: 10px 15px;
  margin: 15px 0;
  border-radius: 0 4px 4px 0;
}

.promotionHighlight p {
  margin: 0;
  color: #4b5563;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 80px);
  color: #6b7280;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #7e3af2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 80px);
  text-align: center;
  padding: 20px;
}

.errorContainer h2 {
  color: #b91c1c;
  margin-bottom: 10px;
}

.errorContainer p {
  color: #6b7280;
  margin-bottom: 20px;
}

.errorContainer button {
  background-color: #7e3af2;
  color: white;
  border: none;
  border-radius: 24px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.errorContainer button:hover {
  background-color: #6c2ce9;
}

.mobileToggle {
  display: none;
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #7e3af2;
  color: white;
  border: none;
  font-size: 24px;
  cursor: pointer;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Responsive styles */
@media (max-width: 768px) {
  .messagesPage {
    padding: 0;
    height: calc(100vh - 60px); /* Adjust based on your mobile header height */
  }

  .conversationListContainer {
    position: fixed;
    top: 60px; /* Adjust based on your mobile header height */
    left: 0;
    width: 100%;
    height: calc(100vh - 60px);
    margin-right: 0;
    z-index: 100;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .conversationListContainer.showMobile {
    transform: translateX(0);
  }

  .chatContainer {
    width: 100%;
  }

  .mobileToggle {
    display: block;
  }
}
