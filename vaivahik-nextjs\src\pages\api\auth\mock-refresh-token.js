/**
 * Mock Token Refresh API Endpoint
 * 
 * This endpoint is only available in development mode and provides
 * a way to refresh authentication tokens with mock credentials.
 */

import { verifyMockToken } from '@/utils/mockAuth';

export default function handler(req, res) {
  // Only allow this endpoint in development mode
  if (process.env.NODE_ENV !== 'development') {
    return res.status(404).json({ 
      success: false, 
      message: 'This endpoint is only available in development mode' 
    });
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    });
  }

  try {
    // Get refresh token from request body
    const { refreshToken } = req.body;

    // Validate required fields
    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        message: 'Refresh token is required'
      });
    }

    // The refresh token format is 'refresh-{userId}'
    const userId = refreshToken.startsWith('refresh-') 
      ? refreshToken.substring(8) 
      : null;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'Invalid refresh token format'
      });
    }

    // Import mock auth utilities
    const { getMockUserById, authenticateMockUser } = require('@/utils/mockAuth');
    
    // Get user by ID
    const user = getMockUserById(userId);
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid refresh token'
      });
    }

    // Generate a new token
    const authResult = authenticateMockUser(user.email, 'admin123'); // Password doesn't matter for refresh
    
    if (!authResult) {
      return res.status(500).json({
        success: false,
        message: 'Failed to generate new token'
      });
    }

    // Return success response with new tokens
    return res.status(200).json({
      success: true,
      message: 'Token refreshed successfully',
      token: authResult.token,
      refreshToken: authResult.refreshToken,
      expiresIn: authResult.expiresIn
    });
  } catch (error) {
    console.error('Mock token refresh error:', error);
    return res.status(500).json({
      success: false,
      message: 'Authentication error',
      error: error.message
    });
  }
}
