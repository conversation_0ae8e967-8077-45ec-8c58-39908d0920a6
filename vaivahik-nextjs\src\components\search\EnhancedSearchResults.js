/**
 * Enhanced Search Results Component
 * Advanced search results with ML-powered insights and premium features
 */

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Typography,
  Chip,
  Button,
  IconButton,
  Avatar,
  LinearProgress,
  Tooltip,
  Badge,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Skeleton
} from '@mui/material';
import {
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  Visibility as ViewIcon,
  Message as MessageIcon,
  Phone as PhoneIcon,
  Star as StarIcon,
  Verified as VerifiedIcon,
  Psychology as KundliIcon,
  TrendingUp as TrendingIcon,
  Lock as LockIcon,
  WorkspacePremium as PremiumIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import axios from 'axios';

export default function EnhancedSearchResults({ 
  results = [], 
  loading = false, 
  userTier = 'free',
  onProfileView,
  onSendInterest,
  onUpgradeClick 
}) {
  const { data: session } = useSession();
  const [favorites, setFavorites] = useState(new Set());
  const [viewCounts, setViewCounts] = useState({});
  const [mlInsights, setMlInsights] = useState({});
  const [selectedProfile, setSelectedProfile] = useState(null);
  const [showKundliDialog, setShowKundliDialog] = useState(false);

  useEffect(() => {
    if (results.length > 0 && userTier === 'premium') {
      fetchMLInsights();
    }
  }, [results, userTier]);

  const fetchMLInsights = async () => {
    try {
      const profileIds = results.map(profile => profile.id);
      const response = await axios.post('/api/ml/search-insights', {
        profileIds,
        currentUserId: session?.user?.id
      });

      if (response.data.success) {
        setMlInsights(response.data.insights);
      }
    } catch (error) {
      console.error('Error fetching ML insights:', error);
    }
  };

  const handleFavorite = async (profileId) => {
    try {
      const isFavorited = favorites.has(profileId);
      
      if (userTier === 'free' && favorites.size >= 5 && !isFavorited) {
        toast.warning('Free users can save up to 5 favorites. Upgrade to premium for unlimited favorites!');
        onUpgradeClick('unlimited-favorites');
        return;
      }

      const response = await axios.post('/api/favorites/toggle', { profileId });
      
      if (response.data.success) {
        const newFavorites = new Set(favorites);
        if (isFavorited) {
          newFavorites.delete(profileId);
          toast.success('Removed from favorites');
        } else {
          newFavorites.add(profileId);
          toast.success('Added to favorites');
        }
        setFavorites(newFavorites);
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
      toast.error('Failed to update favorites');
    }
  };

  const handleViewProfile = async (profile) => {
    try {
      // Track view count
      setViewCounts(prev => ({
        ...prev,
        [profile.id]: (prev[profile.id] || 0) + 1
      }));

      // Check view limits for free users
      if (userTier === 'free') {
        const dailyViews = Object.values(viewCounts).reduce((sum, count) => sum + count, 0);
        if (dailyViews >= 10) {
          toast.warning('Free users can view 10 profiles per day. Upgrade to premium for unlimited views!');
          onUpgradeClick('unlimited-views');
          return;
        }
      }

      // Track the view
      await axios.post('/api/profile-views/track', { profileId: profile.id });
      
      if (onProfileView) {
        onProfileView(profile);
      }
    } catch (error) {
      console.error('Error tracking profile view:', error);
    }
  };

  const handleKundliMatch = (profile) => {
    if (userTier !== 'premium') {
      toast.warning('Kundli matching is a premium feature!');
      onUpgradeClick('kundli-matching');
      return;
    }
    
    setSelectedProfile(profile);
    setShowKundliDialog(true);
  };

  const getCompatibilityColor = (score) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'error';
  };

  const getMLInsight = (profileId) => {
    return mlInsights[profileId] || {};
  };

  const renderProfileCard = (profile) => {
    const insight = getMLInsight(profile.id);
    const isFavorited = favorites.has(profile.id);
    const viewCount = viewCounts[profile.id] || 0;

    return (
      <Card
        key={profile.id}
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
          }
        }}
      >
        {/* Premium Badge */}
        {profile.isPremium && (
          <Chip
            icon={<PremiumIcon />}
            label="Premium"
            size="small"
            color="warning"
            sx={{
              position: 'absolute',
              top: 8,
              left: 8,
              zIndex: 1,
              background: 'linear-gradient(135deg, #FFD700, #FFA000)',
              color: '#000',
              fontWeight: 600
            }}
          />
        )}

        {/* Verified Badge */}
        {profile.isVerified && (
          <VerifiedIcon
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              zIndex: 1,
              color: 'primary.main',
              bgcolor: 'white',
              borderRadius: '50%',
              p: 0.5
            }}
          />
        )}

        {/* Profile Image */}
        <CardMedia
          component="img"
          height="200"
          image={profile.photos?.[0] || '/default-avatar.png'}
          alt={profile.name}
          sx={{ objectFit: 'cover' }}
        />

        <CardContent sx={{ flexGrow: 1, p: 2 }}>
          {/* Basic Info */}
          <Box sx={{ mb: 2 }}>
            <Typography variant="h6" component="h3" gutterBottom>
              {profile.name}
              {profile.age && (
                <Typography component="span" variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                  ({profile.age} years)
                </Typography>
              )}
            </Typography>
            
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {profile.occupation} • {profile.location}
            </Typography>
            
            <Typography variant="body2" color="text.secondary">
              {profile.education}
            </Typography>
          </Box>

          {/* ML Compatibility Score (Premium) */}
          {userTier === 'premium' && insight.compatibilityScore && (
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="body2" fontWeight="600">
                  AI Compatibility
                </Typography>
                <Typography variant="body2" color={getCompatibilityColor(insight.compatibilityScore)}>
                  {insight.compatibilityScore}%
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={insight.compatibilityScore}
                color={getCompatibilityColor(insight.compatibilityScore)}
                sx={{ height: 6, borderRadius: 3 }}
              />
              {insight.topReasons && (
                <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                  {insight.topReasons.slice(0, 2).join(', ')}
                </Typography>
              )}
            </Box>
          )}

          {/* Basic Compatibility (Free) */}
          {userTier === 'free' && (
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="body2" fontWeight="600">
                  Basic Match
                </Typography>
                <Typography variant="body2" color="primary">
                  {Math.floor(Math.random() * 30) + 60}%
                </Typography>
              </Box>
              <Alert severity="info" sx={{ py: 0.5 }}>
                <Typography variant="caption">
                  Upgrade for AI-powered compatibility insights
                </Typography>
              </Alert>
            </Box>
          )}

          {/* Quick Stats */}
          <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
            <Chip
              label={profile.religion}
              size="small"
              variant="outlined"
              color="primary"
            />
            {profile.height && (
              <Chip
                label={`${profile.height} ft`}
                size="small"
                variant="outlined"
              />
            )}
            {userTier === 'premium' && insight.responseRate && (
              <Chip
                icon={<TrendingIcon />}
                label={`${insight.responseRate}% response`}
                size="small"
                color="success"
                variant="outlined"
              />
            )}
          </Box>

          {/* Action Buttons */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title={isFavorited ? 'Remove from favorites' : 'Add to favorites'}>
                <IconButton
                  size="small"
                  onClick={() => handleFavorite(profile.id)}
                  color={isFavorited ? 'error' : 'default'}
                >
                  {isFavorited ? <FavoriteIcon /> : <FavoriteBorderIcon />}
                </IconButton>
              </Tooltip>
              
              <Tooltip title="View full profile">
                <IconButton
                  size="small"
                  onClick={() => handleViewProfile(profile)}
                  color="primary"
                >
                  <ViewIcon />
                </IconButton>
              </Tooltip>
              
              <Tooltip title="Kundli matching">
                <IconButton
                  size="small"
                  onClick={() => handleKundliMatch(profile)}
                  color="secondary"
                >
                  {userTier === 'premium' ? <KundliIcon /> : <LockIcon />}
                </IconButton>
              </Tooltip>
            </Box>

            <Button
              variant="contained"
              size="small"
              startIcon={<MessageIcon />}
              onClick={() => onSendInterest(profile)}
              sx={{
                background: 'linear-gradient(135deg, #D9534F 0%, #4A00E0 100%)',
                color: 'white'
              }}
            >
              Interest
            </Button>
          </Box>

          {/* View Count (for tracking) */}
          {viewCount > 0 && (
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
              Viewed {viewCount} time{viewCount > 1 ? 's' : ''}
            </Typography>
          )}
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <Grid container spacing={3}>
        {[...Array(6)].map((_, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card sx={{ height: 400 }}>
              <Skeleton variant="rectangular" height={200} />
              <CardContent>
                <Skeleton variant="text" height={32} />
                <Skeleton variant="text" height={20} />
                <Skeleton variant="text" height={20} />
                <Box sx={{ mt: 2 }}>
                  <Skeleton variant="rectangular" height={36} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  }

  if (results.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No profiles found
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Try adjusting your search filters to find more matches
        </Typography>
      </Box>
    );
  }

  return (
    <>
      <Grid container spacing={3}>
        {results.map((profile) => (
          <Grid item xs={12} sm={6} md={4} key={profile.id}>
            {renderProfileCard(profile)}
          </Grid>
        ))}
      </Grid>

      {/* Kundli Matching Dialog */}
      <Dialog open={showKundliDialog} onClose={() => setShowKundliDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <KundliIcon sx={{ mr: 2, color: 'primary.main' }} />
            Kundli Matching
          </Box>
        </DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            Kundli matching requires birth details from both profiles. This feature provides detailed astrological compatibility analysis.
          </Alert>
          <Typography variant="body2">
            Would you like to perform detailed Kundli matching with {selectedProfile?.name}?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowKundliDialog(false)}>Cancel</Button>
          <Button variant="contained" color="primary">
            Start Kundli Analysis
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
