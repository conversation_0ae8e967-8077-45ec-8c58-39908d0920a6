/**
 * API Endpoint Detector
 * 
 * This utility helps detect and catalog API endpoints in the application.
 * It uses a combination of static analysis and runtime detection.
 */

/**
 * Detect API endpoints by testing common patterns
 * @param {string} baseUrl - The base URL to test against
 * @returns {Promise<Array>} - Array of detected endpoints with metadata
 */
export async function detectApiEndpoints(baseUrl) {
  // Common API patterns to test
  const patterns = [
    // Basic test endpoint
    '/api/hello',

    // Admin endpoints - Verification Queue
    '/api/admin/verification-queue',
    '/api/admin/verification-queue/1',
    '/api/admin/verification-queue/1/approve',
    '/api/admin/verification-queue/1/reject',

    // Admin endpoints - Reported Profiles
    '/api/admin/reported-profiles',
    '/api/admin/reported-profiles/1',
    '/api/admin/reported-profiles/1/resolve',
    '/api/admin/reported-profiles/1/dismiss',

    // Admin endpoints - Premium Plans
    '/api/admin/premium-plans',
    '/api/admin/premium-plans/1',
    '/api/admin/premium-plans/2',
    '/api/admin/premium-plans/3',

    // Admin endpoints - Features
    '/api/admin/features',
    '/api/admin/features/1',
    '/api/admin/features/2',
    '/api/admin/features/1/activate',
    '/api/admin/features/1/deactivate',
    '/api/admin/feature-management',

    // Admin endpoints - Other
    '/api/admin/users',
    '/api/admin/dashboard',
    '/api/admin/algorithm-settings',
    '/api/admin/preference-config',
    '/api/admin/success-analytics',
    '/api/admin/subscriptions',
    '/api/admin/transactions',
    '/api/admin/revenue-reports',
    '/api/admin/settings',
    '/api/admin/admin-users',

    // User endpoints
    '/api/users',
    '/api/users/profile',
    '/api/users/photos',
    '/api/auth/login',
    '/api/auth/register',
    '/api/auth/send-otp',
    '/api/auth/verify-otp',
    '/api/profiles',
    '/api/matches',
    '/api/preferences',
    '/api/documents',
    '/api/subscriptions',
    '/api/payments',
    '/api/notifications',
    '/api/messages',

    // System endpoints
    '/api/health',
    '/api/status',
    '/api/version'
  ];

  const detectedEndpoints = [];

  // Test each pattern
  for (const pattern of patterns) {
    try {
      const url = `${baseUrl}${pattern}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        mode: 'cors'
      });

      // If the endpoint exists (even if it requires auth), add it to the list
      if (response.status !== 404) {
        let responseData = null;
        let responseType = 'unknown';
        
        try {
          // Try to parse as JSON
          responseData = await response.json();
          responseType = 'json';
        } catch (e) {
          // Not JSON or empty
          try {
            responseData = await response.text();
            responseType = 'text';
          } catch (e2) {
            // Couldn't get text either
          }
        }

        detectedEndpoints.push({
          path: pattern,
          method: 'GET',
          status: response.status,
          statusText: response.statusText,
          requiresAuth: response.status === 401 || response.status === 403,
          responseType,
          responsePreview: responseType === 'json' ? 
            JSON.stringify(responseData).substring(0, 100) : 
            (responseType === 'text' ? responseData.substring(0, 100) : null),
          detectedAt: new Date().toISOString()
        });
      }
    } catch (error) {
      // Network error or other issue - skip this endpoint
      console.log(`Error testing ${pattern}:`, error.message);
    }

    // Small delay to avoid overwhelming the server
    await new Promise(resolve => setTimeout(resolve, 50));
  }

  return detectedEndpoints;
}

/**
 * Test if an endpoint supports a specific HTTP method
 * @param {string} baseUrl - The base URL
 * @param {string} path - The endpoint path
 * @param {string} method - The HTTP method to test
 * @param {Object} payload - Optional payload for POST/PUT/PATCH requests
 * @returns {Promise<Object>} - Result of the test
 */
export async function testEndpointMethod(baseUrl, path, method, payload = null) {
  try {
    const url = `${baseUrl}${path}`;
    
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      mode: 'cors'
    };

    // Add payload for methods that support it
    if (['POST', 'PUT', 'PATCH'].includes(method) && payload) {
      options.body = JSON.stringify(payload);
    }

    const response = await fetch(url, options);

    let responseData = null;
    let responseType = 'unknown';
    
    try {
      // Try to parse as JSON
      responseData = await response.json();
      responseType = 'json';
    } catch (e) {
      // Not JSON or empty
      try {
        responseData = await response.text();
        responseType = 'text';
      } catch (e2) {
        // Couldn't get text either
      }
    }

    return {
      path,
      method,
      status: response.status,
      statusText: response.statusText,
      supported: response.status !== 404 && response.status !== 405,
      requiresAuth: response.status === 401 || response.status === 403,
      responseType,
      responseData,
      responsePreview: responseType === 'json' ? 
        JSON.stringify(responseData).substring(0, 100) : 
        (responseType === 'text' ? responseData.substring(0, 100) : null),
      testedAt: new Date().toISOString()
    };
  } catch (error) {
    return {
      path,
      method,
      error: error.message,
      supported: false,
      testedAt: new Date().toISOString()
    };
  }
}

/**
 * Categorize endpoints by their path structure
 * @param {Array} endpoints - Array of endpoint objects
 * @returns {Object} - Categorized endpoints
 */
export function categorizeEndpoints(endpoints) {
  const categories = {};

  endpoints.forEach(endpoint => {
    // Extract category from path
    let category = 'Other';
    
    if (endpoint.path.includes('/api/admin/')) {
      const adminSection = endpoint.path.split('/api/admin/')[1]?.split('/')[0];
      category = `Admin - ${adminSection ? capitalizeFirstLetter(adminSection) : 'General'}`;
    } else if (endpoint.path.startsWith('/api/auth/')) {
      category = 'Authentication';
    } else if (endpoint.path.startsWith('/api/users/')) {
      category = 'Users';
    } else if (endpoint.path.startsWith('/api/')) {
      const section = endpoint.path.split('/api/')[1]?.split('/')[0];
      category = section ? capitalizeFirstLetter(section) : 'API';
    }

    // Create category if it doesn't exist
    if (!categories[category]) {
      categories[category] = [];
    }

    // Add endpoint to category
    categories[category].push(endpoint);
  });

  return categories;
}

/**
 * Capitalize the first letter of a string
 * @param {string} string - The string to capitalize
 * @returns {string} - The capitalized string
 */
function capitalizeFirstLetter(string) {
  return string.charAt(0).toUpperCase() + string.slice(1);
}

/**
 * Generate a sample request payload based on the endpoint path
 * @param {string} path - The endpoint path
 * @param {string} method - The HTTP method
 * @returns {Object|null} - Sample payload or null if not applicable
 */
export function generateSamplePayload(path, method) {
  if (!['POST', 'PUT', 'PATCH'].includes(method)) {
    return null;
  }

  // Authentication endpoints
  if (path.includes('/api/auth/login')) {
    return {
      email: '<EMAIL>',
      password: 'password123'
    };
  }

  if (path.includes('/api/auth/register')) {
    return {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+91 9876543210',
      password: 'password123'
    };
  }

  if (path.includes('/api/auth/send-otp')) {
    return {
      phone: '+91 9876543210'
    };
  }

  if (path.includes('/api/auth/verify-otp')) {
    return {
      phone: '+91 9876543210',
      otp: '123456'
    };
  }

  // User profile endpoints
  if (path.includes('/api/users/profile')) {
    return {
      name: 'John Doe',
      age: 28,
      gender: 'Male',
      height: '5\'10"',
      location: 'Mumbai',
      occupation: 'Software Engineer',
      education: 'B.Tech in Computer Science',
      maritalStatus: 'Never Married',
      religion: 'Hindu',
      caste: 'Maratha',
      subcaste: 'Deshmukh',
      gotra: 'Kashyap'
    };
  }

  // Admin endpoints
  if (path.includes('/api/admin/premium-plans')) {
    return {
      name: 'Gold Plan',
      price: 2999,
      currency: 'INR',
      duration: 90,
      features: ['Feature 1', 'Feature 2', 'Feature 3'],
      isActive: true
    };
  }

  if (path.includes('/api/admin/verification-queue') && path.includes('/approve')) {
    return {
      notes: 'Documents verified successfully'
    };
  }

  if (path.includes('/api/admin/verification-queue') && path.includes('/reject')) {
    return {
      reason: 'Documents unclear or invalid'
    };
  }

  if (path.includes('/api/admin/reported-profiles') && path.includes('/resolve')) {
    return {
      actionType: 'WARNING',
      actionDetails: 'User warned about inappropriate behavior',
      suspensionDays: null,
      restrictedFeatures: null
    };
  }

  if (path.includes('/api/admin/reported-profiles') && path.includes('/dismiss')) {
    return {
      dismissReason: 'Report found to be invalid'
    };
  }

  // Default payload for other endpoints
  return {
    // Add generic fields that might be useful
    id: 'sample_id',
    name: 'Sample Name',
    description: 'Sample description',
    isActive: true,
    // Add timestamp for good measure
    timestamp: new Date().toISOString()
  };
}
