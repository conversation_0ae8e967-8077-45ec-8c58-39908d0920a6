/**
 * User Login Page
 *
 * This page handles user authentication with both email/phone and password.
 * It supports both mock and real authentication based on feature flags.
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import {
  Box,
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  FormControl,
  FormHelperText,
  InputLabel,
  OutlinedInput,
  InputAdornment,
  IconButton,
  Divider,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Grid,
  useTheme,
  Chip,
  Tooltip,
  styled
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Login as LoginIcon,
  Person as PersonIcon,
  Info as InfoIcon,
  ContentCopy as ContentCopyIcon
} from '@mui/icons-material';
import { useAuth } from '@/contexts/AuthContext';
import { isUsingRealBackend } from '@/utils/featureFlags';
import DataSourceIndicator from '@/components/DataSourceIndicator';
import ModernLoginForm from '@/components/auth/ModernLoginForm';
