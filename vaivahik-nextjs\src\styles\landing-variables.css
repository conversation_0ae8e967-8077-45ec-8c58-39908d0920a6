/* Landing Page Variables */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400&display=swap');

:root {
  --primary-color: #D9534F;
  --primary-light: #EABFB9;
  --secondary-color: #4A00E0;
  --secondary-light: #8E2DE2;
  --accent-color: #FFD700;
  --accent-light: #FFF2BF;
  --dark-color: #1A1E2C;
  --medium-dark-color: #2D3047;
  --light-color: #F7F8FC;
  --light-color-alt: #FFFFFF;
  --white: #FFFFFF;
  --text-color-dark: #22252E;
  --text-color-medium: #585C6D;
  --text-color-light: #F0F0F0;
  --text-color-light-muted: #C0C2CC;
  --primary-gradient: linear-gradient(135deg, var(--primary-color) 0%, #E97571 100%);
  --secondary-gradient: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-light) 100%);
  --premium-gold-gradient: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-light) 100%);
  --shadow-subtle: 0 2px 8px rgba(45, 48, 71, 0.06);
  --shadow-medium: 0 8px 25px rgba(45, 48, 71, 0.1);
  --shadow-strong: 0 15px 40px rgba(45, 48, 71, 0.12);
  --transition-smooth: all 0.35s cubic-bezier(0.25, 0.8, 0.25, 1);
  --border-radius-standard: 12px;
  --border-radius-large: 20px;

  /* Additional variables for new landing page */
  --shadow-soft: 0 5px 15px rgba(0,0,0,0.05);
  --shadow-hard: 0 15px 35px rgba(0,0,0,0.15);
  --subtle-white-gradient: linear-gradient(180deg, var(--white) 0%, #fcfdff 100%);
  --subtle-light-gradient: linear-gradient(180deg, var(--light-color) 0%, #f0f2f5 100%);
  --border-radius-medium: 20px;
}

/* Global Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Montserrat', sans-serif;
    color: var(--text-color-dark);
    line-height: 1.6;
    overflow-x: hidden;
    background-color: var(--white);
}

h1, h2, h3, h4, h5 {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    color: var(--dark-color);
}

p {
    color: var(--text-color-medium);
    margin-bottom: 1rem;
}

section p:last-child {
    margin-bottom: 0;
}

/* Global Section Styles */
section {
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

section:first-of-type {
    padding-top: 150px;
    background-color: transparent;
}
