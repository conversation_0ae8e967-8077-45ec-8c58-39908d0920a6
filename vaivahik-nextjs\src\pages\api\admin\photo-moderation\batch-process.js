// src/pages/api/admin/photo-moderation/batch-process.js
import axios from 'axios';

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }
  
  // Get the auth token from the request cookies or headers
  const token = req.cookies?.adminToken || req.headers.authorization?.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }
  
  try {
    // Forward the request to the backend API
    const response = await axios({
      method: 'POST',
      url: `${process.env.BACKEND_API_URL}/admin/photo-moderation/batch-process`,
      data: req.body,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    // Return the response from the backend
    return res.status(response.status).json(response.data);
  } catch (error) {
    console.error('Error proxying request to backend:', error);
    
    // Return the error response from the backend if available
    if (error.response) {
      return res.status(error.response.status).json(error.response.data);
    }
    
    // Otherwise return a generic error
    return res.status(500).json({ 
      success: false, 
      message: 'Error connecting to backend service' 
    });
  }
}
