/**
 * Notification Scheduler
 *
 * This service handles scheduling notifications to be sent at specific times.
 */
const cron = require('node-cron');
const { PrismaClient } = require('@prisma/client');
const notificationService = require('./notification-service');
const prisma = new PrismaClient();

// Map to store scheduled jobs
const scheduledJobs = new Map();

/**
 * Initialize the notification scheduler
 * Loads all scheduled notifications from the database and schedules them
 */
const initScheduler = async () => {
  try {
    // Get all scheduled notifications that haven't been sent yet
    const scheduledNotifications = await prisma.scheduledNotification.findMany({
      where: {
        sentAt: null,
        scheduledFor: {
          gt: new Date()
        }
      }
    });

    console.log(`Initializing notification scheduler with ${scheduledNotifications.length} pending notifications`);

    // Schedule each notification
    scheduledNotifications.forEach(notification => {
      scheduleNotification(notification);
    });
  } catch (error) {
    console.error('Error initializing notification scheduler:', error);
  }
};

/**
 * Schedule a notification to be sent at a specific time
 *
 * @param {Object} notification - The notification to schedule
 */
const scheduleNotification = (notification) => {
  try {
    const { id, scheduledFor, notificationType, targetType, targetId, data } = notification;

    // Convert scheduledFor to cron schedule
    const date = new Date(scheduledFor);
    const cronSchedule = `${date.getMinutes()} ${date.getHours()} ${date.getDate()} ${date.getMonth() + 1} *`;

    // Create a cron job
    const job = cron.schedule(cronSchedule, async () => {
      try {
        // Get the notification template
        const template = notificationService.templates[`${notificationType}Template`];

        if (!template) {
          throw new Error(`Template not found for notification type: ${notificationType}`);
        }

        // Create the notification content
        const notificationContent = template(data);

        // Send the notification based on target type
        let result;
        if (targetType === 'USER') {
          result = await notificationService.sendToUser(targetId, notificationContent);
        } else if (targetType === 'TOPIC') {
          result = await notificationService.sendToTopic(targetId, notificationContent);
        } else if (['ALL_USERS', 'PREMIUM_USERS', 'FREE_USERS', 'VERIFIED_USERS'].includes(targetType)) {
          // Build the where clause based on target type
          const whereClause = {
            fcmTokens: {
              isEmpty: false
            }
          };

          // Add filters based on target type
          if (targetType === 'PREMIUM_USERS') {
            whereClause.isPremium = true;
          } else if (targetType === 'FREE_USERS') {
            whereClause.isPremium = false;
          } else if (targetType === 'VERIFIED_USERS') {
            whereClause.isVerified = true;
          }

          // Get filtered users
          const users = await prisma.user.findMany({
            where: whereClause,
            select: { id: true }
          });

          const userIds = users.map(user => user.id);
          result = await notificationService.sendToUsers(userIds, notificationContent);
        } else {
          throw new Error(`Invalid target type: ${targetType}`);
        }

        // Update the notification as sent
        await prisma.scheduledNotification.update({
          where: { id },
          data: {
            sentAt: new Date(),
            status: result.success ? 'SENT' : 'FAILED',
            statusDetails: result.success ? null : JSON.stringify(result)
          }
        });

        // Remove the job from the map
        scheduledJobs.delete(id);

        // Stop the cron job
        job.stop();
      } catch (error) {
        console.error(`Error sending scheduled notification ${id}:`, error);

        // Update the notification as failed
        await prisma.scheduledNotification.update({
          where: { id },
          data: {
            sentAt: new Date(),
            status: 'FAILED',
            statusDetails: error.message
          }
        });

        // Remove the job from the map
        scheduledJobs.delete(id);

        // Stop the cron job
        job.stop();
      }
    });

    // Store the job in the map
    scheduledJobs.set(id, job);

    console.log(`Scheduled notification ${id} for ${scheduledFor}`);
  } catch (error) {
    console.error(`Error scheduling notification ${notification.id}:`, error);
  }
};

/**
 * Create a new scheduled notification
 *
 * @param {Object} notificationData - Data for the scheduled notification
 * @param {string} notificationData.notificationType - Type of notification (e.g., 'promotional')
 * @param {string} notificationData.targetType - Type of target (USER, TOPIC, ALL_USERS)
 * @param {string} notificationData.targetId - ID of the target (user ID or topic name)
 * @param {Date} notificationData.scheduledFor - When to send the notification
 * @param {Object} notificationData.data - Data for the notification template
 * @returns {Promise<Object>} The created scheduled notification
 */
const createScheduledNotification = async (notificationData) => {
  try {
    // Create the scheduled notification in the database
    const scheduledNotification = await prisma.scheduledNotification.create({
      data: {
        notificationType: notificationData.notificationType,
        targetType: notificationData.targetType,
        targetId: notificationData.targetId,
        scheduledFor: notificationData.scheduledFor,
        data: notificationData.data,
        status: 'SCHEDULED'
      }
    });

    // Schedule the notification
    scheduleNotification(scheduledNotification);

    return scheduledNotification;
  } catch (error) {
    console.error('Error creating scheduled notification:', error);
    throw error;
  }
};

/**
 * Cancel a scheduled notification
 *
 * @param {string} id - ID of the scheduled notification
 * @returns {Promise<Object>} The cancelled notification
 */
const cancelScheduledNotification = async (id) => {
  try {
    // Get the job from the map
    const job = scheduledJobs.get(id);

    if (job) {
      // Stop the cron job
      job.stop();

      // Remove the job from the map
      scheduledJobs.delete(id);
    }

    // Update the notification as cancelled
    return await prisma.scheduledNotification.update({
      where: { id },
      data: {
        status: 'CANCELLED'
      }
    });
  } catch (error) {
    console.error(`Error cancelling scheduled notification ${id}:`, error);
    throw error;
  }
};

module.exports = {
  initScheduler,
  createScheduledNotification,
  cancelScheduledNotification
};
