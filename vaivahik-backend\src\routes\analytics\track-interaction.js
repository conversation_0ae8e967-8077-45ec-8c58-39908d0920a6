const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { authenticateToken } = require('../../middleware/auth.middleware');

/**
 * @route POST /api/analytics/track-interaction
 * @desc Track user interactions with profiles
 * @access Private
 *
 * Request body:
 * {
 *   targetUserId: string,
 *   interactionType: "VIEW" | "LIKE" | "SHORTLIST" | "CONTACT_REQUESTED" | etc.,
 *   duration?: number,
 *   viewedFeatures?: {
 *     photos: boolean,
 *     details: boolean,
 *     preferences: boolean,
 *     contact: boolean
 *   }
 * }
 */
router.post('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get request data
    const {
      targetUserId,
      interactionType,
      duration,
      viewedFeatures
    } = req.body;

    // Validate required fields
    if (!targetUserId || !interactionType) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: targetUserId and interactionType are required'
      });
    }

    // Get device and IP information
    const deviceInfo = {
      userAgent: req.headers['user-agent'],
      platform: req.headers['sec-ch-ua-platform'],
      mobile: req.headers['sec-ch-ua-mobile'],
    };

    const ipAddress = req.headers['x-forwarded-for'] ||
                      req.connection.remoteAddress;

    // Create the interaction record
    const interaction = await prisma.userInteraction.create({
      data: {
        userId,
        targetUserId,
        interactionType,
        duration: duration || null,
        deviceInfo,
        ipAddress,
        viewedPhotos: viewedFeatures?.photos || false,
        viewedDetails: viewedFeatures?.details || false,
        viewedPreferences: viewedFeatures?.preferences || false,
        viewedContact: viewedFeatures?.contact || false,
      },
    });

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Interaction tracked successfully',
      interactionId: interaction.id
    });

  } catch (error) {
    console.error('Error tracking interaction:', error);
    return res.status(500).json({
      success: false,
      message: 'Error tracking interaction',
      error: error.message
    });
  }
});

module.exports = router;
