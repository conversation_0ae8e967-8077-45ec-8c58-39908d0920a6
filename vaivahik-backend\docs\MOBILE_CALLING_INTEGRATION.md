# Mobile App Calling Integration Guide

## 📱 Cross-Platform Smart Contact Reveal System

This guide explains how to integrate the smart contact reveal system with your mobile apps (Flutter/React Native).

## 🎯 Overview

The system uses **native phone dialer** instead of WebRTC for maximum reliability:
- ✅ Works even when app is deleted
- ✅ No internet dependency for calls
- ✅ Familiar user experience
- ✅ No infrastructure costs

## 🔧 Flutter Integration

### 1. Add Dependencies

```yaml
# pubspec.yaml
dependencies:
  url_launcher: ^6.1.14
  permission_handler: ^10.4.3
  http: ^1.1.0
```

### 2. Contact Service

```dart
// lib/services/contact_service.dart
import 'package:http/http.dart' as http;
import 'package:url_launcher/url_launcher.dart';
import 'dart:convert';

class ContactService {
  static const String baseUrl = 'http://your-api-url.com/api';
  
  // Check if user can access contact
  static Future<Map<String, dynamic>> canAccessContact(String userId) async {
    final response = await http.get(
      Uri.parse('$baseUrl/contact/can-access/$userId'),
      headers: {
        'Authorization': 'Bearer ${await getAuthToken()}',
        'X-Platform': 'ANDROID', // or 'IOS'
        'Content-Type': 'application/json',
      },
    );
    
    return json.decode(response.body);
  }
  
  // Reveal contact details
  static Future<Map<String, dynamic>> revealContact(String userId) async {
    final response = await http.post(
      Uri.parse('$baseUrl/contact/reveal/$userId'),
      headers: {
        'Authorization': 'Bearer ${await getAuthToken()}',
        'X-Platform': Platform.isAndroid ? 'ANDROID' : 'IOS',
        'Content-Type': 'application/json',
      },
      body: json.encode({
        'platform': Platform.isAndroid ? 'ANDROID' : 'IOS'
      }),
    );
    
    return json.decode(response.body);
  }
  
  // Open native dialer
  static Future<void> openDialer(String phoneNumber) async {
    final Uri dialerUri = Uri(scheme: 'tel', path: phoneNumber);
    
    if (await canLaunchUrl(dialerUri)) {
      await launchUrl(dialerUri);
    } else {
      throw 'Could not launch dialer';
    }
  }
}
```

### 3. Smart Call Button Widget

```dart
// lib/widgets/smart_call_button.dart
import 'package:flutter/material.dart';
import '../services/contact_service.dart';

class SmartCallButton extends StatefulWidget {
  final String targetUserId;
  final String targetUserName;
  final VoidCallback? onCallInitiated;
  
  const SmartCallButton({
    Key? key,
    required this.targetUserId,
    required this.targetUserName,
    this.onCallInitiated,
  }) : super(key: key);

  @override
  State<SmartCallButton> createState() => _SmartCallButtonState();
}

class _SmartCallButtonState extends State<SmartCallButton> {
  bool _loading = false;
  bool? _canAccess;

  @override
  void initState() {
    super.initState();
    _checkAccess();
  }

  Future<void> _checkAccess() async {
    try {
      final result = await ContactService.canAccessContact(widget.targetUserId);
      setState(() {
        _canAccess = result['canAccess'] ?? false;
      });
    } catch (e) {
      setState(() {
        _canAccess = false;
      });
    }
  }

  Future<void> _handleCall() async {
    if (_canAccess != true) {
      _showUpgradeDialog();
      return;
    }

    setState(() {
      _loading = true;
    });

    try {
      final result = await ContactService.revealContact(widget.targetUserId);
      
      if (result['success'] == true) {
        final contactNumber = result['contactNumber'];
        
        // Show contact details dialog
        _showContactDialog(contactNumber, result);
        
        // Auto-open dialer after delay
        Future.delayed(const Duration(seconds: 1), () {
          ContactService.openDialer(contactNumber);
        });
        
        widget.onCallInitiated?.call();
      } else {
        _showErrorDialog(result['message'] ?? 'Failed to reveal contact');
      }
    } catch (e) {
      _showErrorDialog('Error: $e');
    } finally {
      setState(() {
        _loading = false;
      });
    }
  }

  void _showContactDialog(String contactNumber, Map<String, dynamic> result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.phone),
            SizedBox(width: 8),
            Text('Contact Details'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Calling: ${widget.targetUserName}'),
            const SizedBox(height: 8),
            SelectableText(
              contactNumber,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                fontFamily: 'monospace',
              ),
            ),
            const SizedBox(height: 8),
            Chip(
              label: Text('Available: ${result['callAvailability'] ?? 'Anytime'}'),
              backgroundColor: Colors.green.shade100,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              ContactService.openDialer(contactNumber);
            },
            icon: const Icon(Icons.phone),
            label: const Text('Call Now'),
          ),
        ],
      ),
    );
  }

  void _showUpgradeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Premium Feature'),
        content: const Text(
          'Upgrade to Premium to access contact details and call users directly.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to premium upgrade page
            },
            child: const Text('Upgrade'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: _loading ? null : _handleCall,
      icon: _loading 
        ? const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          )
        : const Icon(Icons.phone),
      label: Text(_loading ? 'Connecting...' : 'Call'),
      style: ElevatedButton.styleFrom(
        backgroundColor: _canAccess == true 
          ? Theme.of(context).primaryColor 
          : Colors.orange,
      ),
    );
  }
}
```

## 🔧 React Native Integration

### 1. Install Dependencies

```bash
npm install react-native-url-launcher
# For iOS
cd ios && pod install
```

### 2. Contact Service

```javascript
// services/ContactService.js
import { Linking, Platform } from 'react-native';

class ContactService {
  static baseUrl = 'http://your-api-url.com/api';
  
  static async canAccessContact(userId) {
    const response = await fetch(`${this.baseUrl}/contact/can-access/${userId}`, {
      headers: {
        'Authorization': `Bearer ${await this.getAuthToken()}`,
        'X-Platform': Platform.OS.toUpperCase(),
        'Content-Type': 'application/json',
      },
    });
    
    return response.json();
  }
  
  static async revealContact(userId) {
    const response = await fetch(`${this.baseUrl}/contact/reveal/${userId}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${await this.getAuthToken()}`,
        'X-Platform': Platform.OS.toUpperCase(),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        platform: Platform.OS.toUpperCase()
      }),
    });
    
    return response.json();
  }
  
  static async openDialer(phoneNumber) {
    const url = `tel:${phoneNumber}`;
    
    const supported = await Linking.canOpenURL(url);
    if (supported) {
      await Linking.openURL(url);
    } else {
      throw new Error('Phone dialer not available');
    }
  }
  
  static async getAuthToken() {
    // Implement your auth token retrieval logic
    return 'your-auth-token';
  }
}

export default ContactService;
```

### 3. Smart Call Button Component

```javascript
// components/SmartCallButton.js
import React, { useState, useEffect } from 'react';
import { TouchableOpacity, Text, Alert, ActivityIndicator } from 'react-native';
import ContactService from '../services/ContactService';

const SmartCallButton = ({ 
  targetUserId, 
  targetUserName, 
  onCallInitiated = () => {} 
}) => {
  const [loading, setLoading] = useState(false);
  const [canAccess, setCanAccess] = useState(null);

  useEffect(() => {
    checkAccess();
  }, [targetUserId]);

  const checkAccess = async () => {
    try {
      const result = await ContactService.canAccessContact(targetUserId);
      setCanAccess(result.canAccess);
    } catch (error) {
      setCanAccess(false);
    }
  };

  const handleCall = async () => {
    if (!canAccess) {
      showUpgradeAlert();
      return;
    }

    setLoading(true);
    try {
      const result = await ContactService.revealContact(targetUserId);
      
      if (result.success) {
        const { contactNumber, callAvailability } = result;
        
        Alert.alert(
          'Contact Details',
          `Calling: ${targetUserName}\nNumber: ${contactNumber}\nAvailable: ${callAvailability}`,
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Call Now', 
              onPress: () => ContactService.openDialer(contactNumber)
            }
          ]
        );
        
        onCallInitiated({ targetUserId, contactNumber });
      } else {
        Alert.alert('Error', result.message || 'Failed to reveal contact');
      }
    } catch (error) {
      Alert.alert('Error', error.message);
    } finally {
      setLoading(false);
    }
  };

  const showUpgradeAlert = () => {
    Alert.alert(
      'Premium Feature',
      'Upgrade to Premium to access contact details and call users directly.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Upgrade', onPress: () => {/* Navigate to upgrade */} }
      ]
    );
  };

  return (
    <TouchableOpacity
      style={{
        backgroundColor: canAccess ? '#007AFF' : '#FF9500',
        padding: 12,
        borderRadius: 8,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      }}
      onPress={handleCall}
      disabled={loading}
    >
      {loading ? (
        <ActivityIndicator color="white" size="small" />
      ) : (
        <Text style={{ color: 'white', fontWeight: 'bold' }}>
          📞 Call
        </Text>
      )}
    </TouchableOpacity>
  );
};

export default SmartCallButton;
```

## 🔐 Security Considerations

### 1. API Authentication
- Always include auth tokens in requests
- Validate user permissions on backend
- Log all contact access attempts

### 2. Platform Detection
- Send platform info in headers
- Handle platform-specific dialer URLs
- Track usage by platform

### 3. Privacy Controls
- Respect user privacy settings
- Implement proper access controls
- Provide clear upgrade paths

## 📊 Analytics Integration

Track calling feature usage:

```javascript
// Analytics tracking
const trackContactReveal = (data) => {
  analytics.track('contact_revealed', {
    target_user_id: data.targetUserId,
    platform: data.platform,
    access_reason: data.accessReason,
    is_premium_user: data.isPremium
  });
};

const trackCallInitiated = (data) => {
  analytics.track('call_initiated', {
    target_user_id: data.targetUserId,
    platform: data.platform,
    contact_number: data.contactNumber // Hash this for privacy
  });
};
```

## 🎯 Best Practices

1. **Always check permissions** before revealing contacts
2. **Provide clear upgrade paths** for non-premium users
3. **Log all access attempts** for security
4. **Handle errors gracefully** with user-friendly messages
5. **Test on real devices** to ensure dialer integration works
6. **Respect privacy settings** and call availability
7. **Provide fallback options** if dialer fails

## 🚀 Testing

Test the integration with:
- Different phone number formats
- Various privacy settings
- Premium and non-premium users
- Different platforms (Android/iOS)
- Network connectivity issues
- Permission denied scenarios

This system provides a robust, cross-platform calling solution that works reliably across all devices and platforms!
