// Frontend API endpoint - delegates to backend
import axios from 'axios';

// Mock session for development
const mockSession = {
  user: {
    id: 'user-123',
    name: 'Test User',
    email: '<EMAIL>'
  }
};

const BACKEND_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { productType, productId, quantity = 1 } = req.body;

    // Validate required fields
    if (!productType || !productId) {
      return res.status(400).json({
        error: 'Missing required fields: productType, productId'
      });
    }

    // Forward request to backend
    const response = await axios.post(`${BACKEND_URL}/payments/create-order`, {
      productType,
      productId,
      quantity,
      userId: mockSession.user.id
    });

    res.status(200).json(response.data);

  } catch (error) {
    console.error('Error creating payment order:', error);
    res.status(500).json({
      error: 'Failed to create payment order',
      details: error.response?.data?.message || error.message
    });
  }
}
