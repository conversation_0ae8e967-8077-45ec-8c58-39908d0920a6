import { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  Typography,
  CircularProgress,
  Alert,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip
} from '@mui/material';
import {
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  Legend
} from 'recharts';
import axiosInstance from '@/utils/axiosConfig';

export default function ModerationStats() {
  const [stats, setStats] = useState(null);
  const [recentActivity, setRecentActivity] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Time range for stats
  const [timeRange, setTimeRange] = useState(7); // days

  // Fetch stats on component mount and when time range changes
  useEffect(() => {
    fetchStats();
  }, [timeRange]);

  // Fetch stats from API
  const fetchStats = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(`/api/admin/text-moderation/stats?days=${timeRange}`);

      if (response.data.success) {
        setStats(response.data.stats);
        setRecentActivity(response.data.recentActivity || []);
      } else {
        throw new Error(response.data.message || 'Failed to fetch moderation statistics');
      }
    } catch (error) {
      console.error('Error fetching moderation statistics:', error);
      setError(error.message || 'Failed to fetch moderation statistics');

      // For development, use mock data if API fails
      if (process.env.NODE_ENV === 'development') {
        setStats({
          totalMessages: 1250,
          moderatedMessages: 320,
          moderationRate: 25.6,
          rejectedMessages: 85,
          rejectionRate: 26.5,
          flagBreakdown: {
            profanity: 45,
            contactInfo: 120,
            spam: 155
          }
        });

        setRecentActivity([
          {
            id: 1,
            contentType: 'Message',
            decision: 'REJECTED',
            flags: 'profanity,spam',
            createdAt: new Date().toISOString()
          },
          {
            id: 2,
            contentType: 'Profile',
            decision: 'APPROVED',
            flags: 'contact_info',
            createdAt: new Date().toISOString()
          },
          {
            id: 3,
            contentType: 'Message',
            decision: 'PENDING',
            flags: 'spam',
            createdAt: new Date().toISOString()
          }
        ]);
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle time range change
  const handleTimeRangeChange = (event) => {
    setTimeRange(event.target.value);
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Get flag chip color
  const getFlagColor = (flag) => {
    switch (flag) {
      case 'profanity':
        return 'error';
      case 'contact_info':
        return 'warning';
      case 'spam':
        return 'info';
      default:
        return 'default';
    }
  };

  // Get decision chip color
  const getDecisionColor = (decision) => {
    switch (decision) {
      case 'APPROVED':
        return 'success';
      case 'REJECTED':
        return 'error';
      case 'PENDING':
        return 'warning';
      default:
        return 'default';
    }
  };

  // Prepare data for pie chart
  const preparePieChartData = () => {
    if (!stats) return [];

    return [
      { name: 'Moderated', value: stats.moderatedMessages },
      { name: 'Not Moderated', value: stats.totalMessages - stats.moderatedMessages }
    ];
  };

  // Prepare data for flag breakdown chart
  const prepareFlagBreakdownData = () => {
    if (!stats || !stats.flagBreakdown) return [];

    return [
      { name: 'Profanity', value: stats.flagBreakdown.profanity },
      { name: 'Contact Info', value: stats.flagBreakdown.contactInfo },
      { name: 'Spam', value: stats.flagBreakdown.spam }
    ];
  };

  // Colors for charts
  const COLORS = ['#7e3af2', '#e2e8f0', '#f05252', '#ff9800', '#10b981'];

  if (loading && !stats) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel>Time Range</InputLabel>
          <Select
            value={timeRange}
            onChange={handleTimeRangeChange}
            label="Time Range"
          >
            <MenuItem value={1}>Last 24 Hours</MenuItem>
            <MenuItem value={7}>Last 7 Days</MenuItem>
            <MenuItem value={30}>Last 30 Days</MenuItem>
            <MenuItem value={90}>Last 3 Months</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {stats && (
        <Grid container spacing={3}>
          {/* Summary Cards */}
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Total Messages
                </Typography>
                <Typography variant="h3">
                  {stats.totalMessages.toLocaleString()}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Moderated Messages
                </Typography>
                <Typography variant="h3">
                  {stats.moderatedMessages.toLocaleString()}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  {stats.moderationRate.toFixed(1)}% of total
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Rejected Messages
                </Typography>
                <Typography variant="h3">
                  {stats.rejectedMessages.toLocaleString()}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  {stats.rejectionRate.toFixed(1)}% of moderated
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Flag Breakdown
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mt: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Chip label="Profanity" color="error" size="small" />
                    <Typography>{stats.flagBreakdown.profanity.toLocaleString()}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Chip label="Contact Info" color="warning" size="small" />
                    <Typography>{stats.flagBreakdown.contactInfo.toLocaleString()}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Chip label="Spam" color="info" size="small" />
                    <Typography>{stats.flagBreakdown.spam.toLocaleString()}</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Charts */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Moderation Overview" />
              <Divider />
              <CardContent sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={preparePieChartData()}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {preparePieChartData().map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => value.toLocaleString()} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Flag Distribution" />
              <Divider />
              <CardContent sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={prepareFlagBreakdownData()}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip formatter={(value) => value.toLocaleString()} />
                    <Legend />
                    <Bar dataKey="value" fill="#7e3af2" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Recent Activity */}
          <Grid item xs={12}>
            <Card>
              <CardHeader title="Recent Moderation Activity" />
              <Divider />
              <CardContent>
                <TableContainer component={Paper} variant="outlined">
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Content Type</TableCell>
                        <TableCell>Decision</TableCell>
                        <TableCell>Flags</TableCell>
                        <TableCell>Date</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {recentActivity.length > 0 ? (
                        recentActivity.map((activity) => (
                          <TableRow key={activity.id}>
                            <TableCell>
                              {activity.contentType}
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={activity.decision}
                                color={getDecisionColor(activity.decision)}
                                size="small"
                              />
                            </TableCell>
                            <TableCell>
                              {activity.flags ? (
                                activity.flags.split(',').map((flag) => (
                                  <Chip
                                    key={flag}
                                    label={flag}
                                    color={getFlagColor(flag)}
                                    size="small"
                                    sx={{ mr: 0.5, mb: 0.5 }}
                                  />
                                ))
                              ) : (
                                <Chip label="None" size="small" />
                              )}
                            </TableCell>
                            <TableCell>
                              {formatDate(activity.createdAt)}
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={4} align="center">
                            <Typography variant="body1">
                              No recent activity found
                            </Typography>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
}
