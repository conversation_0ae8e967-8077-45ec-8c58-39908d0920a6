import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Verify JWT token
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ success: false, message: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const userId = decoded.userId;

    const { filters, format = 'csv' } = req.body;

    // Get all user interactions (similar to index.js but for export)
    const allInteractions = await prisma.userInteraction.findMany({
      where: {
        userId: userId
      },
      include: {
        targetUser: {
          include: {
            profile: true
          }
        }
      },
      orderBy: {
        timestamp: 'desc'
      }
    });

    // Get profile likes
    const profileLikes = await prisma.profileLike.findMany({
      where: {
        userId: userId
      },
      include: {
        targetUser: {
          include: {
            profile: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Get profile views
    const profileViews = await prisma.profileViewDetailed.findMany({
      where: {
        viewerId: userId
      },
      include: {
        viewedUser: {
          include: {
            profile: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Format for export
    const exportData = [];

    // Add regular interactions
    allInteractions.forEach(interaction => {
      exportData.push({
        timestamp: interaction.timestamp.toISOString(),
        type: interaction.interactionType,
        targetUserId: interaction.targetUserId,
        targetUserName: `${interaction.targetUser?.profile?.firstName || 'Unknown'} ${interaction.targetUser?.profile?.lastName || ''}`.trim(),
        targetUserAge: interaction.targetUser?.profile?.age || '',
        targetUserLocation: `${interaction.targetUser?.profile?.city || ''}, ${interaction.targetUser?.profile?.state || ''}`.trim().replace(/^,|,$/, ''),
        duration: interaction.duration || '',
        metadata: interaction.metadata || '',
        source: 'interaction'
      });
    });

    // Add profile likes
    profileLikes.forEach(like => {
      exportData.push({
        timestamp: like.createdAt.toISOString(),
        type: `PROFILE_${like.likeType}`,
        targetUserId: like.targetUserId,
        targetUserName: `${like.targetUser?.profile?.firstName || 'Unknown'} ${like.targetUser?.profile?.lastName || ''}`.trim(),
        targetUserAge: like.targetUser?.profile?.age || '',
        targetUserLocation: `${like.targetUser?.profile?.city || ''}, ${like.targetUser?.profile?.state || ''}`.trim().replace(/^,|,$/, ''),
        duration: '',
        metadata: JSON.stringify({ likeType: like.likeType }),
        source: 'like'
      });
    });

    // Add profile views
    profileViews.forEach(view => {
      exportData.push({
        timestamp: view.createdAt.toISOString(),
        type: 'PROFILE_VIEW_DETAILED',
        targetUserId: view.viewedUserId,
        targetUserName: `${view.viewedUser?.profile?.firstName || 'Unknown'} ${view.viewedUser?.profile?.lastName || ''}`.trim(),
        targetUserAge: view.viewedUser?.profile?.age || '',
        targetUserLocation: `${view.viewedUser?.profile?.city || ''}, ${view.viewedUser?.profile?.state || ''}`.trim().replace(/^,|,$/, ''),
        duration: view.viewDuration || '',
        metadata: JSON.stringify({
          deviceType: view.deviceType,
          sectionsViewed: view.sectionsViewed,
          referrer: view.referrer
        }),
        source: 'view'
      });
    });

    // Sort by timestamp
    exportData.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Apply filters if provided
    let filteredData = exportData;
    
    if (filters?.type) {
      filteredData = filteredData.filter(item => item.type === filters.type);
    }

    if (filters?.dateRange && filters.dateRange !== 'custom') {
      const days = parseInt(filters.dateRange.replace('d', ''));
      const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
      filteredData = filteredData.filter(item => new Date(item.timestamp) >= cutoffDate);
    }

    if (filters?.startDate && filters?.endDate) {
      const startDate = new Date(filters.startDate);
      const endDate = new Date(filters.endDate);
      filteredData = filteredData.filter(item => {
        const itemDate = new Date(item.timestamp);
        return itemDate >= startDate && itemDate <= endDate;
      });
    }

    // Generate CSV
    if (format === 'csv') {
      const csvHeaders = [
        'Timestamp',
        'Interaction Type',
        'Target User ID',
        'Target User Name',
        'Target User Age',
        'Target User Location',
        'Duration (ms)',
        'Metadata',
        'Source'
      ];

      const csvRows = filteredData.map(item => [
        item.timestamp,
        item.type,
        item.targetUserId,
        `"${item.targetUserName}"`, // Wrap in quotes for CSV safety
        item.targetUserAge,
        `"${item.targetUserLocation}"`,
        item.duration,
        `"${item.metadata.replace(/"/g, '""')}"`, // Escape quotes in metadata
        item.source
      ]);

      const csvContent = [
        csvHeaders.join(','),
        ...csvRows.map(row => row.join(','))
      ].join('\n');

      // Set headers for file download
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="interaction-history-${new Date().toISOString().split('T')[0]}.csv"`);
      
      return res.status(200).send(csvContent);
    }

    // Default JSON response
    return res.status(200).json({
      success: true,
      data: filteredData,
      totalRecords: filteredData.length,
      exportedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error exporting interaction history:', error);
    return res.status(500).json({
      success: false,
      message: 'Error exporting interaction history',
      error: error.message
    });
  }
}
