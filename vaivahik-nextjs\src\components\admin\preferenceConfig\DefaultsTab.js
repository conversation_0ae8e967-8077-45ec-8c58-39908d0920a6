import { useState } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  TextField,
  Switch,
  FormControlLabel,
  Tooltip,
  Paper,
  Typography,
  Slider,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  OutlinedInput,
  ListItemText,
  Checkbox
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import InfoIcon from '@mui/icons-material/Info';

export default function DefaultsTab({ defaultPreferences, fields, categories, options, onUpdate }) {
  const [preferences, setPreferences] = useState({ ...defaultPreferences });
  const [expandedCategory, setExpandedCategory] = useState(null);

  const handleAccordionChange = (categoryId) => (event, isExpanded) => {
    setExpandedCategory(isExpanded ? categoryId : null);
  };

  const handleSaveDefaults = () => {
    onUpdate(preferences);
  };

  const handleResetDefaults = () => {
    setPreferences({ ...defaultPreferences });
  };

  const handleTextChange = (e, field) => {
    const { value } = e.target;
    setPreferences(prev => ({
      ...prev,
      [field.name]: value
    }));
  };

  const handleNumberChange = (e, field) => {
    const { value } = e.target;
    setPreferences(prev => ({
      ...prev,
      [field.name]: parseInt(value, 10)
    }));
  };

  const handleRangeChange = (field, values) => {
    setPreferences(prev => ({
      ...prev,
      [`${field.name}Min`]: values[0],
      [`${field.name}Max`]: values[1]
    }));
  };

  const handleSelectChange = (e, field) => {
    const { value } = e.target;
    setPreferences(prev => ({
      ...prev,
      [field.name]: value
    }));
  };

  const handleMultiSelectChange = (e, field) => {
    const { value } = e.target;
    setPreferences(prev => ({
      ...prev,
      [field.name]: typeof value === 'string' ? value.split(',') : value
    }));
  };

  const handleBooleanChange = (e, field) => {
    const { checked } = e.target;
    setPreferences(prev => ({
      ...prev,
      [field.name]: checked
    }));
  };

  const renderFieldInput = (field) => {
    const fieldOptions = options.filter(o => o.fieldId === field.id);
    
    switch (field.fieldType) {
      case 'TEXT':
        return (
          <TextField
            fullWidth
            label={field.displayName}
            value={preferences[field.name] || ''}
            onChange={(e) => handleTextChange(e, field)}
            helperText={field.description}
          />
        );
      
      case 'NUMBER':
        return (
          <TextField
            fullWidth
            type="number"
            label={field.displayName}
            value={preferences[field.name] || ''}
            onChange={(e) => handleNumberChange(e, field)}
            helperText={field.description}
            InputProps={{ 
              inputProps: { 
                min: field.minValue || undefined, 
                max: field.maxValue || undefined,
                step: field.stepValue || undefined
              } 
            }}
          />
        );
      
      case 'RANGE':
        // For range fields like age or height
        const minKey = `${field.name}Min`;
        const maxKey = `${field.name}Max`;
        const minValue = preferences[minKey] || field.minValue || 0;
        const maxValue = preferences[maxKey] || field.maxValue || 100;
        
        return (
          <Box>
            <Typography gutterBottom>
              {field.displayName}
              <Tooltip title={field.description || ''}>
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={8}>
                <Slider
                  value={[minValue, maxValue]}
                  onChange={(e, newValue) => handleRangeChange(field, newValue)}
                  valueLabelDisplay="auto"
                  min={field.minValue || 0}
                  max={field.maxValue || 100}
                  step={field.stepValue || 1}
                />
              </Grid>
              <Grid item xs={6} sm={2}>
                <TextField
                  label="Min"
                  type="number"
                  value={minValue}
                  onChange={(e) => handleRangeChange(field, [parseInt(e.target.value, 10), maxValue])}
                  size="small"
                  fullWidth
                  InputProps={{ 
                    inputProps: { 
                      min: field.minValue || undefined, 
                      max: field.maxValue || undefined,
                      step: field.stepValue || undefined
                    } 
                  }}
                />
              </Grid>
              <Grid item xs={6} sm={2}>
                <TextField
                  label="Max"
                  type="number"
                  value={maxValue}
                  onChange={(e) => handleRangeChange(field, [minValue, parseInt(e.target.value, 10)])}
                  size="small"
                  fullWidth
                  InputProps={{ 
                    inputProps: { 
                      min: field.minValue || undefined, 
                      max: field.maxValue || undefined,
                      step: field.stepValue || undefined
                    } 
                  }}
                />
              </Grid>
            </Grid>
          </Box>
        );
      
      case 'SELECT':
        return (
          <FormControl fullWidth>
            <InputLabel>{field.displayName}</InputLabel>
            <Select
              value={preferences[field.name] || ''}
              onChange={(e) => handleSelectChange(e, field)}
              label={field.displayName}
            >
              {fieldOptions.map((option) => (
                <MenuItem key={option.id} value={option.value}>
                  {option.displayText}
                </MenuItem>
              ))}
            </Select>
            <Typography variant="caption" color="textSecondary">
              {field.description}
            </Typography>
          </FormControl>
        );
      
      case 'MULTI_SELECT':
        return (
          <FormControl fullWidth>
            <InputLabel>{field.displayName}</InputLabel>
            <Select
              multiple
              value={preferences[field.name] || []}
              onChange={(e) => handleMultiSelectChange(e, field)}
              input={<OutlinedInput label={field.displayName} />}
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((value) => {
                    const option = fieldOptions.find(o => o.value === value);
                    return (
                      <Chip key={value} label={option ? option.displayText : value} />
                    );
                  })}
                </Box>
              )}
            >
              {fieldOptions.map((option) => (
                <MenuItem key={option.id} value={option.value}>
                  <Checkbox checked={(preferences[field.name] || []).indexOf(option.value) > -1} />
                  <ListItemText primary={option.displayText} />
                </MenuItem>
              ))}
            </Select>
            <Typography variant="caption" color="textSecondary">
              {field.description}
            </Typography>
          </FormControl>
        );
      
      case 'BOOLEAN':
        return (
          <FormControlLabel
            control={
              <Switch
                checked={preferences[field.name] || false}
                onChange={(e) => handleBooleanChange(e, field)}
              />
            }
            label={
              <Box>
                {field.displayName}
                <Typography variant="caption" display="block" color="textSecondary">
                  {field.description}
                </Typography>
              </Box>
            }
          />
        );
      
      default:
        return (
          <Typography color="error">
            Unknown field type: {field.fieldType}
          </Typography>
        );
    }
  };

  return (
    <Box>
      <Alert severity="info" sx={{ mb: 3 }}>
        Default preference values are applied to new users when they register. Users can override these defaults with their own preferences.
      </Alert>
      
      <Box sx={{ mb: 3 }}>
        {categories.map((category) => {
          const categoryFields = fields.filter(field => field.categoryId === category.id && field.isActive);
          
          if (categoryFields.length === 0) {
            return null;
          }
          
          return (
            <Accordion 
              key={category.id} 
              expanded={expandedCategory === category.id}
              onChange={handleAccordionChange(category.id)}
              sx={{ mb: 2 }}
            >
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">
                  {category.displayName}
                  {!category.isActive && (
                    <Chip 
                      label="Inactive" 
                      size="small" 
                      color="default" 
                      sx={{ ml: 1 }}
                    />
                  )}
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  {categoryFields.map((field) => (
                    <Grid item xs={12} sm={field.fieldType === 'RANGE' ? 12 : 6} key={field.id}>
                      {renderFieldInput(field)}
                    </Grid>
                  ))}
                </Grid>
              </AccordionDetails>
            </Accordion>
          );
        })}
      </Box>
      
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
        <Button
          variant="outlined"
          startIcon={<RestartAltIcon />}
          onClick={handleResetDefaults}
        >
          Reset to Current
        </Button>
        <Button
          variant="contained"
          color="primary"
          startIcon={<SaveIcon />}
          onClick={handleSaveDefaults}
        >
          Save Default Values
        </Button>
      </Box>
    </Box>
  );
}
