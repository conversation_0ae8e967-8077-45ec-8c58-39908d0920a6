/**
 * Health Check Routes
 * 
 * These routes provide health check endpoints for monitoring the application.
 */

const express = require('express');
const router = express.Router();
const os = require('os');
const { version } = require('../../package.json');

/**
 * @route   GET /api/health
 * @desc    Basic health check
 * @access  Public
 */
router.get('/', (req, res) => {
  return res.success({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  }, 'Service is healthy');
});

/**
 * @route   GET /api/health/details
 * @desc    Detailed health check with system information
 * @access  Public
 */
router.get('/details', async (req, res) => {
  try {
    // Get database status
    let dbStatus = 'unknown';
    try {
      // Try a simple query to check database connection
      await req.prisma.$queryRaw`SELECT 1`;
      dbStatus = 'connected';
    } catch (err) {
      dbStatus = 'disconnected';
    }

    // Get system information
    const systemInfo = {
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version,
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      uptime: process.uptime(),
      hostname: os.hostname(),
      loadAvg: os.loadavg(),
      totalMem: os.totalmem(),
      freeMem: os.freemem()
    };

    // Get application information
    const appInfo = {
      version,
      environment: process.env.NODE_ENV || 'development',
      startTime: new Date(Date.now() - process.uptime() * 1000).toISOString()
    };

    return res.success({
      status: 'ok',
      timestamp: new Date().toISOString(),
      database: {
        status: dbStatus
      },
      system: systemInfo,
      application: appInfo
    }, 'Detailed health check');
  } catch (err) {
    return res.error('Health check failed', 500, {
      error: err.message
    });
  }
});

/**
 * @route   GET /api/health/readiness
 * @desc    Readiness probe for Kubernetes
 * @access  Public
 */
router.get('/readiness', async (req, res) => {
  try {
    // Check database connection
    await req.prisma.$queryRaw`SELECT 1`;
    
    return res.success({
      status: 'ready',
      timestamp: new Date().toISOString()
    }, 'Service is ready');
  } catch (err) {
    return res.error('Service is not ready', 503, {
      error: err.message
    });
  }
});

/**
 * @route   GET /api/health/liveness
 * @desc    Liveness probe for Kubernetes
 * @access  Public
 */
router.get('/liveness', (req, res) => {
  return res.success({
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  }, 'Service is alive');
});

module.exports = router;
