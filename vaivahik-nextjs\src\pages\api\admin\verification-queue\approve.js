// API endpoint for approving verification requests
import { generateMockVerifications } from '@/utils/mockData';

export default function handler(req, res) {
  // Set proper content type header
  res.setHeader('Content-Type', 'application/json');

  try {
    // Only allow POST method
    if (req.method !== 'POST') {
      return res.status(405).json({
        success: false,
        message: 'Method not allowed'
      });
    }

    // Get data from request body
    const { userId, notes } = req.body;

    // Validate required fields
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }

    // In a real implementation, this would update the database
    // For now, we'll just return a success response
    return res.status(200).json({
      success: true,
      message: 'Verification approved successfully',
      data: {
        userId,
        notes: notes || '',
        status: 'APPROVED',
        approvedAt: new Date().toISOString(),
        approvedBy: 'Admin User'
      }
    });
  } catch (error) {
    console.error('Error approving verification:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to approve verification'
    });
  }
}
