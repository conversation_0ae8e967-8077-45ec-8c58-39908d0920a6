// Mock data for API endpoints in admin section

// Generate mock premium plans data
export function getMockPlans() {
  return [
    {
      id: '1',
      name: 'Basic',
      duration: '1 month',
      price: '₹499',
      features: ['View contacts', 'Basic matching']
    },
    {
      id: '2',
      name: 'Premium',
      duration: '3 months',
      price: '₹1299',
      features: ['View contacts', 'Advanced matching', 'Priority in search results']
    },
    {
      id: '3',
      name: 'Gold',
      duration: '6 months',
      price: '₹2499',
      features: ['View contacts', 'Advanced matching', 'Priority in search results', 'Horoscope matching']
    },
    {
      id: '4',
      name: 'Platinum',
      duration: '12 months',
      price: '₹4999',
      features: ['View contacts', 'Advanced matching', 'Priority in search results', 'Horoscope matching', 'Dedicated relationship manager']
    }
  ];
}
