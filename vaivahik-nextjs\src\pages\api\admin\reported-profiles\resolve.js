// API endpoint for resolving reported profiles
import { generateMockReportedProfiles } from '@/utils/mockData';

export default function handler(req, res) {
  // Set proper content type header
  res.setHeader('Content-Type', 'application/json');

  try {
    // Only allow POST method
    if (req.method !== 'POST') {
      return res.status(405).json({
        success: false,
        message: 'Method not allowed'
      });
    }

    // Get data from request body
    const { reportId, action, notes } = req.body;

    // Validate required fields
    if (!reportId) {
      return res.status(400).json({
        success: false,
        message: 'Report ID is required'
      });
    }

    if (!action) {
      return res.status(400).json({
        success: false,
        message: 'Action is required'
      });
    }

    // Validate action
    const validActions = ['WARNING', 'SUSPENSION', 'BAN', 'NO_ACTION'];
    if (!validActions.includes(action)) {
      return res.status(400).json({
        success: false,
        message: `Invalid action. Must be one of: ${validActions.join(', ')}`
      });
    }

    // In a real implementation, this would update the database
    // For now, we'll just return a success response
    return res.status(200).json({
      success: true,
      message: 'Report resolved successfully',
      data: {
        reportId,
        action,
        notes: notes || '',
        status: 'RESOLVED',
        resolvedAt: new Date().toISOString(),
        resolvedBy: 'Admin User'
      }
    });
  } catch (error) {
    console.error('Error resolving report:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to resolve report'
    });
  }
}
