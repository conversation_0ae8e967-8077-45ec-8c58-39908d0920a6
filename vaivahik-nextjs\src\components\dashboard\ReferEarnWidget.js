/**
 * <PERSON><PERSON> & <PERSON>arn Widget - Integrates with existing referral system
 * Leverages admin referral programs and tracking
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  LinearProgress,
  styled,
  CircularProgress,
  InputAdornment,
  IconButton
} from '@mui/material';

// Icons
import {
  Share as ShareIcon,
  ContentCopy as CopyIcon,
  WhatsApp as WhatsAppIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Person as PersonIcon,
  MonetizationOn as MoneyIcon,
  EmojiEvents as TrophyIcon,
  Link as LinkIcon,
  Check as CheckIcon,
  Send as SendIcon
} from '@mui/icons-material';

// Styled components
const ReferralCard = styled(Card)(({ theme }) => ({
  borderRadius: 16,
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white',
  boxShadow: '0 8px 32px rgba(102, 126, 234, 0.3)'
}));

const RewardCard = styled(Card)(({ theme }) => ({
  borderRadius: 12,
  textAlign: 'center',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 12px 32px rgba(0,0,0,0.15)'
  }
}));

const ReferralCodeBox = styled(Box)(({ theme }) => ({
  background: 'rgba(255,255,255,0.1)',
  borderRadius: 8,
  padding: theme.spacing(2),
  border: '2px dashed rgba(255,255,255,0.3)',
  textAlign: 'center'
}));

export default function ReferEarnWidget({ userId }) {
  const [referralData, setReferralData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [shareDialog, setShareDialog] = useState(false);
  const [inviteDialog, setInviteDialog] = useState(false);
  const [inviteForm, setInviteForm] = useState({ email: '', phone: '', message: '' });
  const [sending, setSending] = useState(false);
  const [copied, setCopied] = useState(false);
  const [message, setMessage] = useState(null);

  useEffect(() => {
    fetchReferralData();
  }, [userId]);

  const fetchReferralData = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/user/referral-code', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('userToken')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setReferralData(data);
      }
    } catch (error) {
      console.error('Error fetching referral data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCopyCode = async () => {
    if (referralData?.referralCode) {
      try {
        await navigator.clipboard.writeText(referralData.referralCode);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (error) {
        console.error('Failed to copy:', error);
      }
    }
  };

  const handleCopyLink = async () => {
    if (referralData?.referralLink) {
      try {
        await navigator.clipboard.writeText(referralData.referralLink);
        setMessage({ type: 'success', text: 'Referral link copied to clipboard!' });
        setTimeout(() => setMessage(null), 3000);
      } catch (error) {
        console.error('Failed to copy link:', error);
      }
    }
  };

  const handleSendInvite = async () => {
    setSending(true);
    try {
      const response = await fetch('/api/user/refer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('userToken')}`
        },
        body: JSON.stringify(inviteForm)
      });

      if (response.ok) {
        setMessage({ type: 'success', text: 'Invitation sent successfully!' });
        setInviteForm({ email: '', phone: '', message: '' });
        setInviteDialog(false);
        fetchReferralData(); // Refresh data
      } else {
        const error = await response.json();
        setMessage({ type: 'error', text: error.message || 'Failed to send invitation' });
      }
    } catch (error) {
      console.error('Error sending invite:', error);
      setMessage({ type: 'error', text: 'An error occurred while sending invitation' });
    } finally {
      setSending(false);
      setTimeout(() => setMessage(null), 3000);
    }
  };

  const handleWhatsAppShare = () => {
    const text = `Join Vaivahik Matrimony using my referral code: ${referralData?.referralCode}\n\nGet premium features and find your perfect match!\n\n${referralData?.referralLink}`;
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(text)}`;
    window.open(whatsappUrl, '_blank');
  };

  const handleEmailShare = () => {
    const subject = 'Join Vaivahik Matrimony - Special Referral Offer';
    const body = `Hi!\n\nI'm inviting you to join Vaivahik Matrimony, a trusted platform for finding your life partner.\n\nUse my referral code: ${referralData?.referralCode}\nOr click this link: ${referralData?.referralLink}\n\nYou'll get special benefits when you sign up!\n\nBest regards`;
    const mailtoUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.open(mailtoUrl);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
        <ShareIcon sx={{ fontSize: 32, color: '#667eea', mr: 2 }} />
        <Typography variant="h5" fontWeight="700">
          Refer & Earn
        </Typography>
      </Box>

      {/* Message Alert */}
      {message && (
        <Alert severity={message.type} sx={{ mb: 3 }}>
          {message.text}
        </Alert>
      )}

      {/* Main Referral Card */}
      <ReferralCard sx={{ mb: 4 }}>
        <CardContent sx={{ p: 4 }}>
          <Typography variant="h5" fontWeight="700" gutterBottom>
            Invite Friends & Earn Rewards
          </Typography>
          <Typography variant="body1" sx={{ opacity: 0.9, mb: 4 }}>
            Share Vaivahik with your friends and family. Both you and your friends get amazing rewards!
          </Typography>

          {/* Referral Code */}
          <ReferralCodeBox sx={{ mb: 4 }}>
            <Typography variant="subtitle2" sx={{ opacity: 0.8, mb: 1 }}>
              Your Referral Code
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 2 }}>
              <Typography variant="h4" fontWeight="700" letterSpacing={2}>
                {referralData?.referralCode || 'LOADING...'}
              </Typography>
              <IconButton
                onClick={handleCopyCode}
                sx={{ color: 'white' }}
                disabled={!referralData?.referralCode}
              >
                {copied ? <CheckIcon /> : <CopyIcon />}
              </IconButton>
            </Box>
          </ReferralCodeBox>

          {/* Action Buttons */}
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                fullWidth
                variant="contained"
                startIcon={<SendIcon />}
                onClick={() => setInviteDialog(true)}
                sx={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  '&:hover': { backgroundColor: 'rgba(255,255,255,0.3)' }
                }}
              >
                Send Invite
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                fullWidth
                variant="contained"
                startIcon={<WhatsAppIcon />}
                onClick={handleWhatsAppShare}
                sx={{
                  backgroundColor: '#25D366',
                  '&:hover': { backgroundColor: '#128C7E' }
                }}
              >
                WhatsApp
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                fullWidth
                variant="contained"
                startIcon={<EmailIcon />}
                onClick={handleEmailShare}
                sx={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  '&:hover': { backgroundColor: 'rgba(255,255,255,0.3)' }
                }}
              >
                Email
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                fullWidth
                variant="contained"
                startIcon={<LinkIcon />}
                onClick={handleCopyLink}
                sx={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  '&:hover': { backgroundColor: 'rgba(255,255,255,0.3)' }
                }}
              >
                Copy Link
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </ReferralCard>

      {/* Rewards & Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <RewardCard>
            <CardContent sx={{ p: 3 }}>
              <MoneyIcon sx={{ fontSize: 48, color: '#4CAF50', mb: 2 }} />
              <Typography variant="h4" fontWeight="700" color="#4CAF50">
                ₹{referralData?.totalRewards || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Rewards Earned
              </Typography>
            </CardContent>
          </RewardCard>
        </Grid>
        <Grid item xs={12} md={4}>
          <RewardCard>
            <CardContent sx={{ p: 3 }}>
              <PersonIcon sx={{ fontSize: 48, color: '#2196F3', mb: 2 }} />
              <Typography variant="h4" fontWeight="700" color="#2196F3">
                {referralData?.referralHistory?.length || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Successful Referrals
              </Typography>
            </CardContent>
          </RewardCard>
        </Grid>
        <Grid item xs={12} md={4}>
          <RewardCard>
            <CardContent sx={{ p: 3 }}>
              <TrophyIcon sx={{ fontSize: 48, color: '#FF9800', mb: 2 }} />
              <Typography variant="h4" fontWeight="700" color="#FF9800">
                {referralData?.referralProgram?.maxReferralsPerUser || '∞'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Referral Limit
              </Typography>
            </CardContent>
          </RewardCard>
        </Grid>
      </Grid>

      {/* Referral History */}
      {referralData?.referralHistory?.length > 0 && (
        <Card sx={{ borderRadius: 2 }}>
          <CardContent sx={{ p: 4 }}>
            <Typography variant="h6" fontWeight="600" gutterBottom>
              Referral History
            </Typography>
            <List>
              {referralData.referralHistory.map((referral, index) => (
                <React.Fragment key={referral.id || index}>
                  <ListItem>
                    <ListItemIcon>
                      <PersonIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary={`Referral ${index + 1}`}
                      secondary={`Reward: ₹${referral.referralProgram?.referrerRewardAmount || 0} • ${new Date(referral.createdAt).toLocaleDateString()}`}
                    />
                    <Chip
                      label="Completed"
                      color="success"
                      size="small"
                    />
                  </ListItem>
                  {index < referralData.referralHistory.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </CardContent>
        </Card>
      )}

      {/* Send Invite Dialog */}
      <Dialog open={inviteDialog} onClose={() => setInviteDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Send Referral Invitation</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="Email Address"
              type="email"
              value={inviteForm.email}
              onChange={(e) => setInviteForm({ ...inviteForm, email: e.target.value })}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Phone Number"
              value={inviteForm.phone}
              onChange={(e) => setInviteForm({ ...inviteForm, phone: e.target.value })}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              multiline
              rows={3}
              label="Personal Message (Optional)"
              value={inviteForm.message}
              onChange={(e) => setInviteForm({ ...inviteForm, message: e.target.value })}
              placeholder="Add a personal message to your invitation..."
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setInviteDialog(false)}>Cancel</Button>
          <Button
            onClick={handleSendInvite}
            variant="contained"
            disabled={(!inviteForm.email && !inviteForm.phone) || sending}
            sx={{ background: 'linear-gradient(135deg, #667eea, #764ba2)' }}
          >
            {sending ? <CircularProgress size={20} color="inherit" /> : 'Send Invitation'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
