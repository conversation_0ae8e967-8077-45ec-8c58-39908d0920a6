import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  // Check if user is authenticated
  const session = await getServerSession(req, res, authOptions);

  if (!session || !session.user) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  // Only allow GET method
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const userId = session.user.id;

    // Get user's active spotlight activation
    const now = new Date();
    const activeSpotlightActivation = await prisma.spotlightActivation.findFirst({
      where: {
        userId,
        isActive: true,
        endTime: {
          gt: now
        }
      },
      include: {
        userSpotlight: {
          include: {
            spotlight: true
          }
        }
      },
      orderBy: {
        endTime: 'desc'
      }
    });

    // Get user's spotlight activation history
    const spotlightActivationHistory = await prisma.spotlightActivation.findMany({
      where: {
        userId,
        OR: [
          { isActive: false },
          {
            isActive: true,
            endTime: {
              lte: now
            }
          }
        ]
      },
      include: {
        userSpotlight: {
          include: {
            spotlight: true
          }
        }
      },
      orderBy: {
        endTime: 'desc'
      },
      take: 5
    });

    // Get user's purchased spotlights with available uses
    const purchasedSpotlights = await prisma.userSpotlight.findMany({
      where: {
        userId,
        availableCount: {
          gt: { usedCount: true } // Where availableCount > usedCount
        }
      },
      include: {
        spotlight: true
      }
    });

    // Get available spotlight options
    const availableSpotlights = await prisma.spotlightFeature.findMany({
      where: {
        isActive: true
      },
      orderBy: [
        { durationHours: 'asc' },
        { price: 'asc' }
      ]
    });

    return res.status(200).json({
      success: true,
      // Currently active spotlight
      activeSpotlight: activeSpotlightActivation ? {
        id: activeSpotlightActivation.id,
        startTime: activeSpotlightActivation.startTime,
        endTime: activeSpotlightActivation.endTime,
        timeRemaining: calculateTimeRemaining(activeSpotlightActivation.endTime),
        spotlight: {
          id: activeSpotlightActivation.userSpotlight.spotlight.id,
          name: activeSpotlightActivation.userSpotlight.spotlight.name
        }
      } : null,

      // Spotlight activation history
      spotlightHistory: spotlightActivationHistory.map(activation => ({
        id: activation.id,
        startTime: activation.startTime,
        endTime: activation.endTime,
        spotlight: {
          name: activation.userSpotlight.spotlight.name
        }
      })),

      // Purchased spotlights with available uses
      purchasedSpotlights: purchasedSpotlights.map(purchase => ({
        id: purchase.id,
        availableCount: purchase.availableCount,
        usedCount: purchase.usedCount,
        remainingCount: purchase.availableCount - purchase.usedCount,
        purchaseDate: purchase.purchaseDate,
        spotlight: {
          id: purchase.spotlight.id,
          name: purchase.spotlight.name,
          description: purchase.spotlight.description
        }
      })),

      // Available spotlight options to purchase
      availableSpotlights: availableSpotlights.map(spotlight => ({
        id: spotlight.id,
        name: spotlight.name,
        description: spotlight.description,
        price: spotlight.price,
        discountedPrice: spotlight.discountedPrice,
        discountPercent: spotlight.discountPercent
      }))
    });
  } catch (error) {
    console.error('Error fetching spotlight status:', error);
    return res.status(500).json({ success: false, message: 'Failed to fetch spotlight status' });
  }
}

// Helper function to calculate time remaining
function calculateTimeRemaining(endTime) {
  const now = new Date();
  const end = new Date(endTime);
  const diffMs = end - now;

  if (diffMs <= 0) {
    return 'Expired';
  }

  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

  if (diffHours > 24) {
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} remaining`;
  }

  return `${diffHours} hour${diffHours !== 1 ? 's' : ''}, ${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} remaining`;
}
