# Enhanced Matrimony Matching System

This document explains the enhanced matching system for the Vaivahik matrimony app, which uses an improved PyTorch-based two-tower model.

## Overview

The enhanced matching system includes:

1. **Improved Neural Network Architecture**
   - Batch normalization for better training stability
   - Residual connections to prevent vanishing gradients
   - Deeper network with more capacity

2. **Advanced Similarity Calculation**
   - Multiple similarity metrics (cosine, Euclidean, dot product)
   - Weighted combination for better matching accuracy

3. **Enhanced Feature Processing**
   - Numerical feature normalization
   - Categorical feature encoding
   - Compatibility feature generation

## Files

- `enhanced_tower_model_pytorch.py`: The core model implementation
- `feature_processor.py`: Feature preprocessing and normalization
- `matching_service_enhanced.py`: Service that integrates with your application

## How to Use

### 1. Integration with Existing System

To use the enhanced matching system, update your API endpoint to use the new service:

```javascript
// In your API route handler
const enhancedMatchingService = require('../services/matching_service_enhanced');

app.get('/api/matches', async (req, res) => {
  const { userId, limit = 10, offset = 0, minScore } = req.query;

  try {
    const matches = await enhancedMatchingService.get_matches(
      userId,
      parseInt(limit),
      parseInt(offset),
      minScore ? parseInt(minScore) : null
    );

    res.json({
      success: true,
      matches
    });
  } catch (error) {
    console.error('Error getting matches:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting matches'
    });
  }
});
```

### 2. Configuration

The enhanced matching system can be configured through the database or a configuration file:

#### Database Configuration

The system reads configuration from the `AlgorithmModel` table:

```sql
INSERT INTO "AlgorithmModel" (
  "id", "name", "version", "type", "description", "config", "isActive", "isDefault"
)
VALUES (
  'enhanced-v1',
  'Enhanced Two-Tower Model',
  '1.0.0',
  'TWO_TOWER',
  'Enhanced two-tower model with batch normalization and residual connections',
  '{"user_tower_layers": [256, 128], "match_tower_layers": [256, 128], "embedding_size": 128, "dropout_rate": 0.2, "similarity_metrics": ["cosine", "euclidean", "dot"], "similarity_weights": [0.6, 0.2, 0.2]}',
  true,
  true
);
```

#### File Configuration

Alternatively, you can create a configuration file at `vaivahik-backend/config/matching_settings.json`:

```json
{
  "general": {
    "matchingModel": "TWO_TOWER",
    "defaultModelId": "enhanced-v1",
    "minMatchScore": 50,
    "maxMatchesPerUser": 100
  },
  "models": {
    "enhanced-v1": {
      "user_tower_layers": [256, 128],
      "match_tower_layers": [256, 128],
      "embedding_size": 128,
      "dropout_rate": 0.2,
      "similarity_metrics": ["cosine", "euclidean", "dot"],
      "similarity_weights": [0.6, 0.2, 0.2]
    }
  }
}
```

### 3. Training the Model

To train the model with your data:

1. Create a script that loads user profiles and preferences from your database
2. Process the data using the `FeatureProcessor`
3. Train the model using the processed data
4. Save the trained model

Example training script:

```python
import torch
from torch.utils.data import DataLoader, TensorDataset
from enhanced_tower_model_pytorch import EnhancedMatrimonyMatchingModel
from feature_processor import FeatureProcessor

# Load data from database
# ...

# Process features
feature_processor = FeatureProcessor()
feature_processor.compute_feature_stats(profiles)

user_features_list = []
match_features_list = []
labels_list = []

for user, match, is_match in training_data:
    user_features, match_features = feature_processor.process_match_pair(
        user, user_preferences, match
    )
    user_features_list.append(user_features)
    match_features_list.append(match_features)
    labels_list.append(1.0 if is_match else 0.0)

# Convert to tensors
user_tensor = torch.tensor(user_features_list, dtype=torch.float32)
match_tensor = torch.tensor(match_features_list, dtype=torch.float32)
labels_tensor = torch.tensor(labels_list, dtype=torch.float32).view(-1, 1)

# Create dataset and dataloader
dataset = TensorDataset(user_tensor, match_tensor, labels_tensor)
train_loader = DataLoader(dataset, batch_size=64, shuffle=True)

# Create and train model
model = EnhancedMatrimonyMatchingModel()
model.build_model()
model.train_model(train_loader, epochs=10)

# Save model
model.save_model('models/enhanced_model.pt')
```

## Match Analysis

The enhanced system provides detailed match analysis explaining why users were matched:

```json
{
  "overallScore": 85,
  "factors": [
    {
      "name": "Age",
      "score": 100,
      "details": "28 years"
    },
    {
      "name": "Education",
      "score": 90,
      "details": "Master's Degree"
    },
    {
      "name": "Occupation",
      "score": 80,
      "details": "Software Engineer"
    },
    {
      "name": "Location",
      "score": 70,
      "details": "Mumbai"
    }
  ],
  "suggestions": [
    "The match's career as a Software Engineer could complement your profession.",
    "You both have similar educational backgrounds which can lead to shared values."
  ]
}
```

## Performance Considerations

The enhanced model is designed to work efficiently on your Hostinger VPS:

1. **Batch Processing**: Processes matches in batches to reduce memory usage
2. **Model Caching**: Loads the model once and reuses it for all requests
3. **Feature Normalization**: Improves convergence and reduces training time
4. **Efficient Tensor Operations**: Uses PyTorch's optimized operations

## Next Steps

After implementing Phase 1, consider these future enhancements:

1. **Hyperparameter Optimization**: Fine-tune model parameters for better performance
2. **A/B Testing**: Compare different model variants with real users
3. **Continuous Learning**: Update the model based on user feedback and interactions
4. **Model Quantization**: Reduce model size and improve inference speed

## Troubleshooting

### Common Issues

1. **High Memory Usage**:
   - Reduce batch size in the configuration
   - Process fewer potential matches at once

2. **Slow Inference**:
   - Enable model caching
   - Consider quantizing the model
   - Pre-compute embeddings for active users

3. **Poor Match Quality**:
   - Check feature normalization
   - Ensure training data is representative
   - Adjust similarity metric weights
