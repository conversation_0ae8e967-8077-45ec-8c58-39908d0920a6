@echo off
echo Setting up husky pre-commit hooks...

cd vaivahik-nextjs

echo Installing husky...
npm install husky --save-dev

echo Creating .husky directory...
if not exist .husky mkdir .husky

echo Setting up pre-commit hook...
npx husky install
npx husky add .husky/pre-commit "node scripts/validate-directory-structure.js"

echo Making pre-commit hook executable...
icacls .husky\pre-commit /grant Everyone:RX

echo Husky setup complete!
