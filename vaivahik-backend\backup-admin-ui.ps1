# Backup script for admin UI files before removal
# This script creates a backup of the admin UI files that will be removed

# Create backup directory
$backupDir = "admin-ui-backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
New-Item -ItemType Directory -Path $backupDir

# Backup admin-ui directory
if (Test-Path -Path "admin-ui") {
    Write-Host "Backing up admin-ui directory..."
    Copy-Item -Path "admin-ui" -Destination "$backupDir/admin-ui" -Recurse
    Write-Host "admin-ui directory backed up successfully."
}

# Backup public/admin directory
if (Test-Path -Path "public/admin") {
    Write-Host "Backing up public/admin directory..."
    Copy-Item -Path "public/admin" -Destination "$backupDir/public-admin" -Recurse
    Write-Host "public/admin directory backed up successfully."
}

Write-Host "Backup completed. Files are saved in $backupDir"
Write-Host "You can now safely remove the original directories."
