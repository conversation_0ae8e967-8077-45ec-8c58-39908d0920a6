// API endpoint for API Discovery
import fs from 'fs';
import path from 'path';

export default function handler(req, res) {
  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getApiEndpoints(req, res);
    default:
      return res.status(405).json({
        success: false,
        message: 'Method not allowed'
      });
  }
}

// GET /api/admin/api-discovery
function getApiEndpoints(req, res) {
  try {
    // Check if we have OpenAPI documentation files
    const docsDir = path.join(process.cwd(), 'public', 'api-docs');
    let apiDocs = [];

    // Try to read API documentation files
    if (fs.existsSync(docsDir)) {
      try {
        const files = fs.readdirSync(docsDir);
        for (const file of files) {
          if (file.endsWith('.json')) {
            const filePath = path.join(docsDir, file);
            const fileContent = fs.readFileSync(filePath, 'utf8');
            const apiDoc = JSON.parse(fileContent);

            // Extract endpoints from OpenAPI documentation
            if (apiDoc.paths) {
              for (const [path, methods] of Object.entries(apiDoc.paths)) {
                for (const [method, details] of Object.entries(methods)) {
                  if (method !== 'parameters') { // Skip common parameters
                    apiDocs.push({
                      path: apiDoc.servers?.[0]?.url ? apiDoc.servers[0].url + path : path,
                      methods: [method.toUpperCase()],
                      description: details.summary || details.description || 'No description',
                      parameters: [
                        ...(details.parameters || []).map(param => ({
                          name: param.name,
                          in: param.in,
                          type: param.schema?.type || 'string',
                          description: param.description || '',
                          required: param.required || false
                        })),
                        ...(path.includes('{') ? path.match(/\{([^}]+)\}/g).map(param => ({
                          name: param.slice(1, -1),
                          in: 'path',
                          type: 'string',
                          description: `Path parameter: ${param.slice(1, -1)}`,
                          required: true
                        })) : [])
                      ],
                      responses: Object.entries(details.responses || {}).reduce((acc, [code, response]) => {
                        acc[code] = {
                          description: response.description || 'No description',
                          schema: response.content?.['application/json']?.schema || {}
                        };
                        return acc;
                      }, {})
                    });
                  }
                }
              }
            }
          }
        }
      } catch (error) {
        console.error('Error reading API documentation files:', error);
      }
    }

    // Combine with hardcoded endpoints
    const endpoints = [
      // Include API docs endpoints first
      ...apiDocs,
      {
        path: '/api/admin/dashboard',
        methods: ['GET'],
        description: 'Get dashboard statistics and recent activities',
        parameters: [],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                stats: { type: 'object' },
                recentUsers: { type: 'array' },
                recentActivities: { type: 'array' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/users',
        methods: ['GET', 'POST'],
        description: 'Get all users or create a new user',
        parameters: [
          { name: 'page', in: 'query', type: 'integer', description: 'Page number' },
          { name: 'limit', in: 'query', type: 'integer', description: 'Items per page' },
          { name: 'search', in: 'query', type: 'string', description: 'Search term' },
          { name: 'status', in: 'query', type: 'string', description: 'Filter by status' }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                users: { type: 'array' },
                pagination: { type: 'object' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/users/:id',
        methods: ['GET', 'PUT', 'DELETE'],
        description: 'Get, update, or delete a specific user',
        parameters: [
          { name: 'id', in: 'path', type: 'string', description: 'User ID', required: true }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                user: { type: 'object' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/verification-queue',
        methods: ['GET'],
        description: 'Get verification requests',
        parameters: [
          { name: 'page', in: 'query', type: 'integer', description: 'Page number' },
          { name: 'limit', in: 'query', type: 'integer', description: 'Items per page' },
          { name: 'search', in: 'query', type: 'string', description: 'Search term' },
          { name: 'status', in: 'query', type: 'string', description: 'Filter by status' }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                verifications: { type: 'array' },
                pagination: { type: 'object' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/verification-queue/:id',
        methods: ['GET', 'POST'],
        description: 'Get or update a verification request',
        parameters: [
          { name: 'id', in: 'path', type: 'string', description: 'Verification ID', required: true },
          { name: 'action', in: 'query', type: 'string', description: 'Action to take (approve/reject)', required: true }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                message: { type: 'string' },
                verification: { type: 'object' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/reported-profiles',
        methods: ['GET'],
        description: 'Get reported profiles',
        parameters: [
          { name: 'page', in: 'query', type: 'integer', description: 'Page number' },
          { name: 'limit', in: 'query', type: 'integer', description: 'Items per page' },
          { name: 'search', in: 'query', type: 'string', description: 'Search term' },
          { name: 'status', in: 'query', type: 'string', description: 'Filter by status' }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                reports: { type: 'array' },
                pagination: { type: 'object' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/reported-profiles/:id',
        methods: ['GET', 'POST'],
        description: 'Get or update a reported profile',
        parameters: [
          { name: 'id', in: 'path', type: 'string', description: 'Report ID', required: true },
          { name: 'action', in: 'query', type: 'string', description: 'Action to take (resolve/dismiss)', required: true }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                message: { type: 'string' },
                report: { type: 'object' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/premium-plans',
        methods: ['GET', 'POST', 'PUT'],
        description: 'Get all premium plans, create a new plan, or update an existing plan',
        parameters: [],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                plans: { type: 'array' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/premium-plans/:id',
        methods: ['GET', 'DELETE'],
        description: 'Get or delete a specific premium plan',
        parameters: [
          { name: 'id', in: 'path', type: 'string', description: 'Plan ID', required: true }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                plan: { type: 'object' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/features',
        methods: ['GET', 'POST', 'PUT'],
        description: 'Get all features, create a new feature, or update an existing feature',
        parameters: [],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                features: { type: 'array' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/preference-config',
        methods: ['GET', 'PUT', 'DELETE'],
        description: 'Manage preference configuration (categories, fields, options, importance settings, default values)',
        parameters: [
          { name: 'type', in: 'query', type: 'string', description: 'Type of data to retrieve (all, categories, fields, options, importance, defaults)', required: false },
          { name: 'id', in: 'query', type: 'string', description: 'ID of the item to delete (for DELETE requests)', required: false }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                message: { type: 'string' },
                categories: { type: 'array' },
                fields: { type: 'array' },
                options: { type: 'array' },
                importanceSettings: { type: 'array' },
                defaultPreferences: { type: 'object' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/algorithm-settings',
        methods: ['GET', 'PUT', 'POST'],
        description: 'Manage algorithm settings for matching and recommendations',
        parameters: [
          { name: 'includeABTestResults', in: 'query', type: 'boolean', description: 'Include A/B test results in the response', required: false },
          { name: 'includeMetrics', in: 'query', type: 'boolean', description: 'Include performance metrics in the response', required: false }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                settings: { type: 'object' },
                models: { type: 'array' },
                abTestResults: { type: 'object' },
                metrics: { type: 'object' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/promotions',
        methods: ['GET', 'POST'],
        description: 'Get all promotions or create a new promotion',
        parameters: [],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                promotions: { type: 'array' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/promotions/:name',
        methods: ['GET', 'PUT'],
        description: 'Get or update a specific promotion',
        parameters: [
          { name: 'name', in: 'path', type: 'string', description: 'Promotion name', required: true }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                promotion: { type: 'object' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/promotions/:name/activate',
        methods: ['POST'],
        description: 'Activate a promotion',
        parameters: [
          { name: 'name', in: 'path', type: 'string', description: 'Promotion name', required: true },
          { name: 'durationDays', in: 'body', type: 'integer', description: 'Duration in days', required: true }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                message: { type: 'string' },
                promotion: { type: 'object' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/promotions/:name/deactivate',
        methods: ['POST'],
        description: 'Deactivate a promotion',
        parameters: [
          { name: 'name', in: 'path', type: 'string', description: 'Promotion name', required: true }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                message: { type: 'string' },
                promotion: { type: 'object' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/photo-moderation/settings',
        methods: ['GET', 'PUT'],
        description: 'Get or update photo moderation settings',
        parameters: [],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                settings: { type: 'object' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/photo-moderation/photos',
        methods: ['GET'],
        description: 'Get photos for moderation',
        parameters: [
          { name: 'status', in: 'query', type: 'string', description: 'Filter by status (PENDING, APPROVED, REJECTED)', required: false },
          { name: 'page', in: 'query', type: 'integer', description: 'Page number', required: false },
          { name: 'limit', in: 'query', type: 'integer', description: 'Items per page', required: false }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                photos: { type: 'array' },
                pagination: { type: 'object' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/photo-moderation/photos/:photoId/status',
        methods: ['PUT'],
        description: 'Update a photo\'s moderation status',
        parameters: [
          { name: 'photoId', in: 'path', type: 'string', description: 'Photo ID', required: true },
          { name: 'status', in: 'body', type: 'string', description: 'New status (APPROVED, REJECTED)', required: true },
          { name: 'adminNotes', in: 'body', type: 'string', description: 'Admin notes', required: false }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                message: { type: 'string' },
                photo: { type: 'object' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/text-moderation/settings',
        methods: ['GET', 'PUT'],
        description: 'Get or update text moderation settings',
        parameters: [],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                settings: { type: 'object' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/text-moderation/banned-words',
        methods: ['GET', 'PUT'],
        description: 'Get or update banned words list',
        parameters: [],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                bannedWords: { type: 'array' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/text-moderation/test',
        methods: ['POST'],
        description: 'Test text moderation on a sample text',
        parameters: [
          { name: 'text', in: 'body', type: 'string', description: 'Text to test', required: true }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                result: { type: 'object' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/moderation-settings',
        methods: ['GET', 'PUT'],
        description: 'Get or update general moderation settings',
        parameters: [],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                settings: { type: 'object' }
              }
            }
          }
        }
      },
      // Notification endpoints
      {
        path: '/api/admin/notifications',
        methods: ['GET'],
        description: 'Get all notifications for admin management',
        parameters: [
          { name: 'page', in: 'query', type: 'integer', description: 'Page number' },
          { name: 'limit', in: 'query', type: 'integer', description: 'Items per page' }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                notifications: { type: 'array' },
                pagination: { type: 'object' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/notifications/send',
        methods: ['POST'],
        description: 'Send a notification to users',
        parameters: [
          { name: 'targetType', in: 'body', type: 'string', description: 'Target type (USER, TOPIC, ALL_USERS, PREMIUM_USERS, FREE_USERS, VERIFIED_USERS)', required: true },
          { name: 'targetId', in: 'body', type: 'string', description: 'Target ID (user ID or topic name)', required: false },
          { name: 'notificationType', in: 'body', type: 'string', description: 'Notification type', required: true },
          { name: 'data', in: 'body', type: 'object', description: 'Notification data', required: true }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                message: { type: 'string' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/notifications/schedule',
        methods: ['POST'],
        description: 'Schedule a notification to be sent at a specific time',
        parameters: [
          { name: 'targetType', in: 'body', type: 'string', description: 'Target type (USER, TOPIC, ALL_USERS, PREMIUM_USERS, FREE_USERS, VERIFIED_USERS)', required: true },
          { name: 'targetId', in: 'body', type: 'string', description: 'Target ID (user ID or topic name)', required: false },
          { name: 'notificationType', in: 'body', type: 'string', description: 'Notification type', required: true },
          { name: 'data', in: 'body', type: 'object', description: 'Notification data', required: true },
          { name: 'scheduledFor', in: 'body', type: 'string', description: 'Scheduled time (ISO format)', required: true }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                scheduledNotification: { type: 'object' }
              }
            }
          }
        }
      },
      // User notification endpoints
      {
        path: '/api/notifications',
        methods: ['GET'],
        description: 'Get user\'s notifications',
        parameters: [
          { name: 'type', in: 'query', type: 'string', description: 'Filter by notification type' },
          { name: 'page', in: 'query', type: 'integer', description: 'Page number' },
          { name: 'limit', in: 'query', type: 'integer', description: 'Items per page' },
          { name: 'isRead', in: 'query', type: 'boolean', description: 'Filter by read status' }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                notifications: { type: 'array' },
                pagination: { type: 'object' }
              }
            }
          }
        }
      },
      {
        path: '/api/notifications/unread-count',
        methods: ['GET'],
        description: 'Get unread notification count',
        parameters: [],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                count: { type: 'integer' }
              }
            }
          }
        }
      },
      {
        path: '/api/notifications/read',
        methods: ['PUT'],
        description: 'Mark notifications as read',
        parameters: [
          { name: 'notificationIds', in: 'body', type: 'array', description: 'Array of notification IDs to mark as read', required: true }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                message: { type: 'string' }
              }
            }
          }
        }
      },
      {
        path: '/api/notifications/fcm-token',
        methods: ['POST'],
        description: 'Register FCM token for the authenticated user',
        parameters: [
          { name: 'token', in: 'body', type: 'string', description: 'FCM token', required: true }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                message: { type: 'string' }
              }
            }
          }
        }
      },
      // Biodata Templates endpoints
      {
        path: '/api/admin/biodata-templates',
        methods: ['GET', 'POST'],
        description: 'Get all biodata templates or create a new template',
        parameters: [],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                templates: { type: 'array' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/biodata-templates/:id',
        methods: ['GET', 'PUT', 'DELETE'],
        description: 'Get, update, or delete a specific biodata template',
        parameters: [
          { name: 'id', in: 'path', type: 'string', description: 'Template ID', required: true }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                template: { type: 'object' }
              }
            }
          }
        }
      },
      // Spotlight Features endpoints
      {
        path: '/api/admin/spotlight-features',
        methods: ['GET', 'POST'],
        description: 'Get all spotlight features or create a new feature',
        parameters: [],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                features: { type: 'array' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/spotlight-features/:id',
        methods: ['GET', 'PUT', 'DELETE'],
        description: 'Get, update, or delete a specific spotlight feature',
        parameters: [
          { name: 'id', in: 'path', type: 'string', description: 'Feature ID', required: true }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                feature: { type: 'object' }
              }
            }
          }
        }
      },
      {
        path: '/api/admin/spotlight-features/:id/active-users',
        methods: ['GET'],
        description: 'Get users with active spotlight for a specific feature',
        parameters: [
          { name: 'id', in: 'path', type: 'string', description: 'Feature ID', required: true }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                users: { type: 'array' }
              }
            }
          }
        }
      },
      {
        path: '/api/user/spotlight/activate',
        methods: ['POST'],
        description: 'Activate a spotlight feature for the current user',
        parameters: [
          { name: 'featureId', in: 'body', type: 'string', description: 'Feature ID', required: true }
        ],
        responses: {
          '200': {
            description: 'Success',
            schema: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                spotlightActivation: { type: 'object' }
              }
            }
          }
        }
      }
    ];

    return res.status(200).json({
      success: true,
      endpoints
    });
  } catch (error) {
    console.error('Error fetching API endpoints:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch API endpoints'
    });
  }
}
