/**
 * Performance Optimization Utilities
 * Features: Lazy loading, caching, image optimization, code splitting, performance monitoring
 */

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useInView } from 'react-intersection-observer';

// Performance monitoring
export class PerformanceMonitor {
  static instance = null;
  
  constructor() {
    if (PerformanceMonitor.instance) {
      return PerformanceMonitor.instance;
    }
    
    this.metrics = new Map();
    this.observers = [];
    this.isEnabled = typeof window !== 'undefined' && 'performance' in window;
    
    if (this.isEnabled) {
      this.initializeObservers();
    }
    
    PerformanceMonitor.instance = this;
  }
  
  static getInstance() {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }
  
  initializeObservers() {
    // Core Web Vitals observer
    if ('PerformanceObserver' in window) {
      // Largest Contentful Paint (LCP)
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.recordMetric('LCP', lastEntry.startTime);
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      
      // First Input Delay (FID)
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          this.recordMetric('FID', entry.processingStart - entry.startTime);
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
      
      // Cumulative Layout Shift (CLS)
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
            this.recordMetric('CLS', clsValue);
          }
        });
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    }
    
    // Navigation timing
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation) {
          this.recordMetric('TTFB', navigation.responseStart - navigation.requestStart);
          this.recordMetric('DOMContentLoaded', navigation.domContentLoadedEventEnd - navigation.navigationStart);
          this.recordMetric('LoadComplete', navigation.loadEventEnd - navigation.navigationStart);
        }
      }, 0);
    });
  }
  
  recordMetric(name, value) {
    this.metrics.set(name, {
      value,
      timestamp: Date.now(),
      url: window.location.pathname
    });
    
    // Send to analytics if configured
    this.sendToAnalytics(name, value);
  }
  
  sendToAnalytics(name, value) {
    // Send to Google Analytics, Sentry, or custom analytics
    if (typeof gtag !== 'undefined') {
      gtag('event', 'performance_metric', {
        metric_name: name,
        metric_value: Math.round(value),
        page_path: window.location.pathname
      });
    }
  }
  
  getMetrics() {
    return Object.fromEntries(this.metrics);
  }
  
  getMetric(name) {
    return this.metrics.get(name);
  }
}

// Lazy loading hook
export const useLazyLoading = (threshold = 0.1) => {
  const { ref, inView, entry } = useInView({
    threshold,
    triggerOnce: true,
    rootMargin: '50px 0px'
  });
  
  return { ref, inView, entry };
};

// Image optimization hook
export const useOptimizedImage = (src, options = {}) => {
  const [optimizedSrc, setOptimizedSrc] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const {
    width,
    height,
    quality = 80,
    format = 'webp',
    fallbackFormat = 'jpg'
  } = options;
  
  useEffect(() => {
    if (!src) return;
    
    const img = new Image();
    
    // Create optimized URL (assuming you have an image optimization service)
    let optimizedUrl = src;
    
    // Add optimization parameters
    const params = new URLSearchParams();
    if (width) params.append('w', width);
    if (height) params.append('h', height);
    params.append('q', quality);
    params.append('f', format);
    
    // Check if URL already has parameters
    const separator = src.includes('?') ? '&' : '?';
    optimizedUrl = `${src}${separator}${params.toString()}`;
    
    img.onload = () => {
      setOptimizedSrc(optimizedUrl);
      setLoading(false);
    };
    
    img.onerror = () => {
      // Fallback to original or different format
      const fallbackParams = new URLSearchParams();
      if (width) fallbackParams.append('w', width);
      if (height) fallbackParams.append('h', height);
      fallbackParams.append('q', quality);
      fallbackParams.append('f', fallbackFormat);
      
      const fallbackUrl = `${src}${separator}${fallbackParams.toString()}`;
      
      const fallbackImg = new Image();
      fallbackImg.onload = () => {
        setOptimizedSrc(fallbackUrl);
        setLoading(false);
      };
      fallbackImg.onerror = () => {
        setOptimizedSrc(src);
        setError('Failed to optimize image');
        setLoading(false);
      };
      fallbackImg.src = fallbackUrl;
    };
    
    img.src = optimizedUrl;
  }, [src, width, height, quality, format, fallbackFormat]);
  
  return { optimizedSrc, loading, error };
};

// Debounce hook for performance
export const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  
  return debouncedValue;
};

// Throttle hook for performance
export const useThrottle = (value, limit) => {
  const [throttledValue, setThrottledValue] = useState(value);
  const lastRan = useRef(Date.now());
  
  useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() - lastRan.current >= limit) {
        setThrottledValue(value);
        lastRan.current = Date.now();
      }
    }, limit - (Date.now() - lastRan.current));
    
    return () => {
      clearTimeout(handler);
    };
  }, [value, limit]);
  
  return throttledValue;
};

// Memory-efficient list virtualization
export const useVirtualList = (items, itemHeight, containerHeight) => {
  const [scrollTop, setScrollTop] = useState(0);
  
  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    );
    
    return {
      startIndex,
      endIndex,
      items: items.slice(startIndex, endIndex),
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight
    };
  }, [items, itemHeight, containerHeight, scrollTop]);
  
  const handleScroll = useCallback((e) => {
    setScrollTop(e.target.scrollTop);
  }, []);
  
  return {
    visibleItems,
    handleScroll,
    totalHeight: visibleItems.totalHeight,
    offsetY: visibleItems.offsetY
  };
};

// Cache management
export class CacheManager {
  constructor(maxSize = 100, ttl = 5 * 60 * 1000) { // 5 minutes default TTL
    this.cache = new Map();
    this.maxSize = maxSize;
    this.ttl = ttl;
  }
  
  set(key, value, customTtl) {
    const expiresAt = Date.now() + (customTtl || this.ttl);
    
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      value,
      expiresAt
    });
  }
  
  get(key) {
    const item = this.cache.get(key);
    
    if (!item) return null;
    
    if (Date.now() > item.expiresAt) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value;
  }
  
  has(key) {
    return this.get(key) !== null;
  }
  
  delete(key) {
    return this.cache.delete(key);
  }
  
  clear() {
    this.cache.clear();
  }
  
  size() {
    return this.cache.size;
  }
}

// Global cache instance
export const globalCache = new CacheManager();

// API cache hook
export const useApiCache = (key, fetcher, options = {}) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const { ttl = 5 * 60 * 1000, enabled = true } = options;
  
  useEffect(() => {
    if (!enabled || !key) return;
    
    const fetchData = async () => {
      // Check cache first
      const cachedData = globalCache.get(key);
      if (cachedData) {
        setData(cachedData);
        setLoading(false);
        return;
      }
      
      try {
        setLoading(true);
        const result = await fetcher();
        globalCache.set(key, result, ttl);
        setData(result);
        setError(null);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [key, fetcher, ttl, enabled]);
  
  const invalidate = useCallback(() => {
    globalCache.delete(key);
  }, [key]);
  
  const refresh = useCallback(async () => {
    invalidate();
    try {
      setLoading(true);
      const result = await fetcher();
      globalCache.set(key, result, ttl);
      setData(result);
      setError(null);
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  }, [key, fetcher, ttl, invalidate]);
  
  return { data, loading, error, invalidate, refresh };
};

// Bundle size analyzer
export const analyzeBundleSize = () => {
  if (typeof window === 'undefined') return;
  
  const scripts = Array.from(document.querySelectorAll('script[src]'));
  const styles = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));
  
  const analysis = {
    scripts: scripts.map(script => ({
      src: script.src,
      async: script.async,
      defer: script.defer
    })),
    styles: styles.map(style => ({
      href: style.href,
      media: style.media
    })),
    totalScripts: scripts.length,
    totalStyles: styles.length
  };
  
  console.log('Bundle Analysis:', analysis);
  return analysis;
};

// Performance optimization recommendations
export const getPerformanceRecommendations = () => {
  const monitor = PerformanceMonitor.getInstance();
  const metrics = monitor.getMetrics();
  const recommendations = [];
  
  // LCP recommendations
  const lcp = metrics.LCP;
  if (lcp && lcp.value > 2500) {
    recommendations.push({
      type: 'LCP',
      severity: lcp.value > 4000 ? 'high' : 'medium',
      message: 'Largest Contentful Paint is slow. Consider optimizing images and reducing server response time.',
      value: lcp.value
    });
  }
  
  // FID recommendations
  const fid = metrics.FID;
  if (fid && fid.value > 100) {
    recommendations.push({
      type: 'FID',
      severity: fid.value > 300 ? 'high' : 'medium',
      message: 'First Input Delay is high. Consider reducing JavaScript execution time.',
      value: fid.value
    });
  }
  
  // CLS recommendations
  const cls = metrics.CLS;
  if (cls && cls.value > 0.1) {
    recommendations.push({
      type: 'CLS',
      severity: cls.value > 0.25 ? 'high' : 'medium',
      message: 'Cumulative Layout Shift is high. Ensure images and ads have dimensions.',
      value: cls.value
    });
  }
  
  return recommendations;
};

// Initialize performance monitoring
if (typeof window !== 'undefined') {
  PerformanceMonitor.getInstance();
}
