<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Luxury Royal Biodata - Premium Edition</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;900&family=Inter:wght@300;400;500;600;700&family=Dancing+Script:wght@400;500;600;700&display=swap');
        
        :root {
            --royal-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gold-gradient: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
            --luxury-gradient: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            --accent-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            --text-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            --gold-color: #ffd700;
            --royal-blue: #667eea;
            --deep-purple: #764ba2;
            --text-color: #2c3e50;
            --light-text: #7f8c8d;
            --luxury-bg: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --header-font: 'Playfair Display', serif;
            --body-font: 'Inter', sans-serif;
            --script-font: 'Dancing Script', cursive;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--body-font);
            color: var(--text-color);
            line-height: 1.6;
            background: var(--luxury-bg);
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
            border-radius: 20px;
            overflow: hidden;
            position: relative;
        }
        
        /* Luxury Border Animation */
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--royal-gradient);
            z-index: -1;
            border-radius: 20px;
            padding: 3px;
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
        }
        
        /* Header Section with Royal Design */
        .royal-header {
            background: var(--royal-gradient);
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .royal-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: sparkle 20s linear infinite;
        }
        
        @keyframes sparkle {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .invocation {
            font-family: var(--script-font);
            color: var(--gold-color);
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 2;
        }
        
        .profile-section {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 40px;
            margin-top: 30px;
            position: relative;
            z-index: 2;
        }
        
        .profile-photo-container {
            position: relative;
        }
        
        .profile-photo {
            width: 200px;
            height: 250px;
            object-fit: cover;
            border-radius: 20px;
            border: 5px solid var(--gold-color);
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
            transition: transform 0.3s ease;
        }
        
        .profile-photo:hover {
            transform: scale(1.05);
        }
        
        .profile-info {
            text-align: left;
            color: white;
        }
        
        .name {
            font-family: var(--header-font);
            font-size: 42px;
            font-weight: 700;
            color: var(--gold-color);
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .tagline {
            font-family: var(--script-font);
            font-size: 24px;
            color: rgba(255,255,255,0.9);
            margin-bottom: 20px;
            font-style: italic;
        }
        
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 20px;
        }
        
        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 12px 20px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            text-align: center;
        }
        
        .stat-label {
            font-size: 12px;
            color: rgba(255,255,255,0.7);
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .stat-value {
            font-size: 16px;
            font-weight: 600;
            color: var(--gold-color);
            margin-top: 2px;
        }
        
        /* Content Section */
        .content-wrapper {
            padding: 50px;
        }
        
        .section {
            margin-bottom: 40px;
            position: relative;
        }
        
        .section-title {
            font-family: var(--header-font);
            font-size: 28px;
            font-weight: 600;
            color: var(--royal-blue);
            margin-bottom: 25px;
            position: relative;
            padding-bottom: 10px;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 80px;
            height: 3px;
            background: var(--gold-gradient);
            border-radius: 2px;
        }
        
        /* Luxury Cards */
        .luxury-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .luxury-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(--royal-gradient);
        }
        
        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid rgba(102, 126, 234, 0.1);
        }
        
        .detail-icon {
            width: 40px;
            height: 40px;
            background: var(--royal-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-weight: 600;
        }
        
        .detail-content {
            flex: 1;
        }
        
        .detail-label {
            font-size: 12px;
            color: var(--light-text);
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 2px;
        }
        
        .detail-value {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-color);
        }
        
        /* Photo Gallery */
        .photo-gallery {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 30px;
        }
        
        .gallery-photo {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }
        
        .gallery-photo:hover {
            transform: translateY(-5px);
        }
        
        /* Footer */
        .luxury-footer {
            background: var(--luxury-gradient);
            padding: 30px;
            text-align: center;
            color: white;
        }
        
        .footer-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .brand-logo {
            width: 40px;
            height: 40px;
            background: var(--gold-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            color: white;
        }
        
        .brand-text {
            font-family: var(--header-font);
            font-size: 18px;
            font-weight: 600;
            color: var(--gold-color);
        }
        
        /* Print Styles */
        @media print {
            body { background: white; padding: 0; }
            .container { box-shadow: none; border-radius: 0; }
            @page { size: A4; margin: 1cm; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Royal Header -->
        <div class="royal-header">
            <div class="invocation">॥ श्री गणेशाय नमः ॥</div>
            <div class="profile-section">
                <div class="profile-photo-container">
                    <img src="{{profilePicture}}" alt="Profile Photo" class="profile-photo">
                </div>
                <div class="profile-info">
                    <h1 class="name">{{name}}</h1>
                    <p class="tagline">{{tagline}}</p>
                    <div class="quick-stats">
                        <div class="stat-item">
                            <div class="stat-label">Age</div>
                            <div class="stat-value">{{age}} years</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Height</div>
                            <div class="stat-value">{{height}}</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Education</div>
                            <div class="stat-value">{{education}}</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Profession</div>
                            <div class="stat-value">{{occupation}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Wrapper -->
        <div class="content-wrapper">
            <!-- Personal Details Section -->
            <div class="section">
                <h2 class="section-title">Personal Details</h2>
                <div class="luxury-card">
                    <div class="details-grid">
                        <div class="detail-item">
                            <div class="detail-icon">📅</div>
                            <div class="detail-content">
                                <div class="detail-label">Date of Birth</div>
                                <div class="detail-value">{{dateOfBirth}}</div>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-icon">📍</div>
                            <div class="detail-content">
                                <div class="detail-label">Birth Place</div>
                                <div class="detail-value">{{birthPlace}}</div>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-icon">🕉️</div>
                            <div class="detail-content">
                                <div class="detail-label">Religion</div>
                                <div class="detail-value">{{religion}}</div>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-icon">🏛️</div>
                            <div class="detail-content">
                                <div class="detail-label">Caste</div>
                                <div class="detail-value">{{caste}}</div>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-icon">🌿</div>
                            <div class="detail-content">
                                <div class="detail-label">Sub-caste</div>
                                <div class="detail-value">{{subCaste}}</div>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-icon">🔱</div>
                            <div class="detail-content">
                                <div class="detail-label">Gotra</div>
                                <div class="detail-value">{{gotra}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Family Background Section -->
            <div class="section">
                <h2 class="section-title">Family Background</h2>
                <div class="luxury-card">
                    <div class="details-grid">
                        <div class="detail-item">
                            <div class="detail-icon">👨</div>
                            <div class="detail-content">
                                <div class="detail-label">Father's Name</div>
                                <div class="detail-value">{{fatherName}}</div>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-icon">💼</div>
                            <div class="detail-content">
                                <div class="detail-label">Father's Occupation</div>
                                <div class="detail-value">{{fatherOccupation}}</div>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-icon">👩</div>
                            <div class="detail-content">
                                <div class="detail-label">Mother's Name</div>
                                <div class="detail-value">{{motherName}}</div>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-icon">🏢</div>
                            <div class="detail-content">
                                <div class="detail-label">Mother's Occupation</div>
                                <div class="detail-value">{{motherOccupation}}</div>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-icon">🏠</div>
                            <div class="detail-content">
                                <div class="detail-label">Family Type</div>
                                <div class="detail-value">{{familyType}}</div>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-icon">👥</div>
                            <div class="detail-content">
                                <div class="detail-label">Siblings</div>
                                <div class="detail-value">{{siblings}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Photo Gallery Section -->
            <div class="section">
                <h2 class="section-title">Photo Gallery</h2>
                <div class="photo-gallery">
                    <img src="{{additionalPhotos.0}}" alt="Additional Photo" class="gallery-photo">
                    <img src="{{additionalPhotos.1}}" alt="Additional Photo" class="gallery-photo">
                    <img src="{{additionalPhotos.2}}" alt="Additional Photo" class="gallery-photo">
                </div>
            </div>

            <!-- Education & Career Section -->
            <div class="section">
                <h2 class="section-title">Education & Career</h2>
                <div class="luxury-card">
                    <div class="details-grid">
                        <div class="detail-item">
                            <div class="detail-icon">🎓</div>
                            <div class="detail-content">
                                <div class="detail-label">Education</div>
                                <div class="detail-value">{{education}}</div>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-icon">💼</div>
                            <div class="detail-content">
                                <div class="detail-label">Occupation</div>
                                <div class="detail-value">{{occupation}}</div>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-icon">💰</div>
                            <div class="detail-content">
                                <div class="detail-label">Annual Income</div>
                                <div class="detail-value">{{annualIncome}}</div>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-icon">🏢</div>
                            <div class="detail-content">
                                <div class="detail-label">Company</div>
                                <div class="detail-value">{{company}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Partner Expectations Section -->
            <div class="section">
                <h2 class="section-title">Partner Expectations</h2>
                <div class="luxury-card">
                    <p style="font-size: 16px; line-height: 1.8; color: var(--text-color);">
                        {{partnerExpectations}}
                    </p>
                </div>
            </div>
        </div>

        <!-- Luxury Footer -->
        <div class="luxury-footer">
            <div class="footer-content">
                <div class="brand-logo">V</div>
                <div class="brand-text">Powered by Vaivahik - The Premier Maratha Matrimony Platform</div>
            </div>
        </div>
    </div>
</body>
</html>
