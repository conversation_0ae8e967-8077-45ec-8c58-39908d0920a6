/**
 * Mock Authentication System for Development
 *
 * This module provides mock authentication functionality for development environments.
 * It simulates a real authentication system with predefined users and roles.
 */

// Polyfill for btoa in Node.js environment
const safebtoa = (str) => {
  if (typeof btoa === 'function') {
    // Browser environment
    return btoa(str);
  } else {
    // Node.js environment
    return Buffer.from(str).toString('base64');
  }
};

// Mock users with credentials and roles
const MOCK_USERS = [
  // Admin users
  {
    id: 'admin-1',
    email: '<EMAIL>',
    password: 'admin123',
    name: 'Admin User',
    role: 'ADMIN',
    permissions: ['*'],
    isActive: true
  },
  {
    id: 'moderator-1',
    email: '<EMAIL>',
    password: 'moderator123',
    name: 'Moderator User',
    role: 'MODERATOR',
    permissions: ['read:users', 'update:users', 'read:reports', 'update:reports'],
    isActive: true
  },

  // Regular users with both email and phone
  {
    id: 'user-1',
    email: '<EMAIL>',
    phone: '9876543210',
    password: 'user123',
    name: 'Regular User',
    role: 'USER',
    permissions: ['read:own', 'update:own'],
    profileCompleted: true,
    isActive: true
  },
  {
    id: 'user-2',
    email: '<EMAIL>',
    phone: '9876543211',
    password: 'password123',
    name: 'John Doe',
    role: 'USER',
    permissions: ['read:own', 'update:own'],
    profileCompleted: false,
    isActive: true
  },
  {
    id: 'user-3',
    email: '<EMAIL>',
    phone: '9876543212',
    password: 'password123',
    name: 'Jane Smith',
    role: 'USER',
    permissions: ['read:own', 'update:own'],
    profileCompleted: true,
    isActive: true
  }
];

// Mock tokens with expiry
const MOCK_TOKENS = new Map();

/**
 * Generate a mock JWT token
 * @param {object} user - User object
 * @returns {string} - Mock JWT token
 */
const generateMockToken = (user) => {
  // Create a simple encoded string that looks like a JWT
  // In a real system, this would be a proper JWT
  const header = safebtoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
  const payload = safebtoa(JSON.stringify({
    sub: user.id,
    name: user.name,
    email: user.email,
    role: user.role,
    permissions: user.permissions,
    exp: Math.floor(Date.now() / 1000) + 3600 // 1 hour from now
  }));
  const signature = safebtoa('mock-signature');

  return `${header}.${payload}.${signature}`;
};

/**
 * Authenticate a user with email/phone and password
 * @param {string} emailOrPhone - User email or phone
 * @param {string} password - User password
 * @returns {object|null} - Authentication result or null if failed
 */
export const authenticateMockUser = (emailOrPhone, password) => {
  // Find user with matching credentials (either email or phone)
  const user = MOCK_USERS.find(u =>
    (u.email === emailOrPhone || u.phone === emailOrPhone) &&
    u.password === password &&
    u.isActive
  );

  if (!user) {
    return null;
  }

  // Generate token
  const token = generateMockToken(user);
  const refreshToken = `refresh-${Math.random().toString(36).substring(2)}`;

  // Store token with expiry
  MOCK_TOKENS.set(token, {
    userId: user.id,
    expires: new Date(Date.now() + 3600000) // 1 hour from now
  });

  // Return auth result with additional user properties
  return {
    user: {
      id: user.id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      role: user.role,
      profileCompleted: user.profileCompleted || false
    },
    token,
    refreshToken,
    expiresIn: 86400 // 24 hours
  };
};

/**
 * Verify a mock token
 * @param {string} token - Token to verify
 * @returns {object|null} - Decoded token payload or null if invalid
 */
export const verifyMockToken = (token) => {
  // Check if token exists and is not expired
  const tokenData = MOCK_TOKENS.get(token);

  if (!tokenData) {
    return null;
  }

  // Check if token is expired
  if (tokenData.expires < new Date()) {
    MOCK_TOKENS.delete(token);
    return null;
  }

  // Find user
  const user = MOCK_USERS.find(u => u.id === tokenData.userId);

  if (!user) {
    return null;
  }

  // Return decoded payload
  return {
    sub: user.id,
    name: user.name,
    email: user.email,
    role: user.role,
    permissions: user.permissions
  };
};

/**
 * Get all mock users (for admin purposes)
 * @returns {Array} - List of mock users (without passwords)
 */
export const getMockUsers = () => {
  return MOCK_USERS.map(({ password, ...user }) => user);
};

/**
 * Get mock user by ID
 * @param {string} id - User ID
 * @returns {object|null} - User object or null if not found
 */
export const getMockUserById = (id) => {
  const user = MOCK_USERS.find(u => u.id === id);

  if (!user) {
    return null;
  }

  // Return user without password
  const { password, ...userWithoutPassword } = user;
  return userWithoutPassword;
};

export default {
  authenticateMockUser,
  verifyMockToken,
  getMockUsers,
  getMockUserById
};
