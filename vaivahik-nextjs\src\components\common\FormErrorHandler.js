import React, { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON>ert<PERSON><PERSON>le, 
  <PERSON>, 
  ListItem, 
  ListItemIcon, 
  ListItemText, 
  Collapse, 
  IconButton, 
  Box, 
  Typography,
  Button
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { getErrorResolution } from '@/utils/errorResolutionDatabase';

/**
 * Form Error Handler Component
 * 
 * This component displays form validation errors with guided resolution steps.
 * It can be used in any form to provide helpful error messages and resolution steps.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.errors - Form errors object (e.g., from react-hook-form)
 * @param {string} props.title - Title for the error alert
 * @param {boolean} props.showGuide - Whether to show guided resolution steps
 * @param {Function} props.onHelp - Function to call when help button is clicked
 */
const FormErrorHandler = ({ 
  errors, 
  title = 'Please fix the following errors:',
  showGuide = true,
  onHelp
}) => {
  const [expanded, setExpanded] = useState(true);
  
  // If no errors, don't render anything
  if (!errors || Object.keys(errors).length === 0) {
    return null;
  }
  
  // Toggle expanded state
  const toggleExpanded = () => {
    setExpanded(!expanded);
  };
  
  // Get all error messages
  const errorMessages = Object.entries(errors).map(([field, error]) => ({
    field,
    message: error.message || `Invalid ${field.replace(/([A-Z])/g, ' $1').toLowerCase()}`
  }));
  
  // Get resolution steps for the first error
  const firstError = errorMessages[0];
  const resolution = showGuide && firstError ? getErrorResolution(firstError.message) : null;
  
  return (
    <Alert 
      severity="error" 
      sx={{ 
        mb: 3, 
        '& .MuiAlert-message': { width: '100%' } 
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <AlertTitle>{title}</AlertTitle>
        <IconButton
          size="small"
          onClick={toggleExpanded}
          aria-expanded={expanded}
          aria-label="toggle errors"
        >
          {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
        </IconButton>
      </Box>
      
      <Collapse in={expanded}>
        <List dense disablePadding>
          {errorMessages.map(({ field, message }) => (
            <ListItem key={field} disablePadding sx={{ py: 0.5 }}>
              <ListItemIcon sx={{ minWidth: 30 }}>
                <ErrorOutlineIcon color="error" fontSize="small" />
              </ListItemIcon>
              <ListItemText 
                primary={message} 
                secondary={field.replace(/([A-Z])/g, ' $1').toLowerCase()}
                primaryTypographyProps={{ variant: 'body2' }}
                secondaryTypographyProps={{ variant: 'caption' }}
              />
            </ListItem>
          ))}
        </List>
        
        {/* Resolution steps if available */}
        {resolution && resolution.steps && resolution.steps.length > 0 && (
          <Box sx={{ mt: 2, borderTop: '1px solid rgba(0, 0, 0, 0.1)', pt: 1 }}>
            <Typography variant="subtitle2" gutterBottom>
              How to fix this:
            </Typography>
            <List dense disablePadding>
              {resolution.steps.map((step, index) => (
                <ListItem key={index} disablePadding sx={{ py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 30 }}>
                    <ArrowRightIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText 
                    primary={step} 
                    primaryTypographyProps={{ variant: 'body2' }}
                  />
                </ListItem>
              ))}
            </List>
            
            {/* Help button */}
            {(onHelp || resolution.helpUrl) && (
              <Box sx={{ mt: 1, display: 'flex', justifyContent: 'flex-end' }}>
                <Button
                  size="small"
                  startIcon={<HelpOutlineIcon />}
                  onClick={() => {
                    if (resolution.helpUrl) {
                      window.open(resolution.helpUrl, '_blank');
                    }
                    if (onHelp) {
                      onHelp();
                    }
                  }}
                  color="primary"
                >
                  Get More Help
                </Button>
              </Box>
            )}
          </Box>
        )}
      </Collapse>
    </Alert>
  );
};

export default FormErrorHandler;
