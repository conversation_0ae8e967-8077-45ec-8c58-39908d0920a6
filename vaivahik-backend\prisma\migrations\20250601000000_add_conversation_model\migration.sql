-- CreateTable
CREATE TABLE "conversations" (
    "id" TEXT NOT NULL,
    "user1_id" TEXT NOT NULL,
    "user2_id" TEXT NOT NULL,
    "last_message_at" TIMESTAMP(3),
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "conversations_pkey" PRIMARY KEY ("id")
);

-- Add conversation_id to messages table
ALTER TABLE "messages" ADD COLUMN "conversation_id" TEXT;

-- Add message_type to messages table
ALTER TABLE "messages" ADD COLUMN "message_type" TEXT NOT NULL DEFAULT 'TEXT';

-- Add metadata to messages table
ALTER TABLE "messages" ADD COLUMN "metadata" TEXT;

-- <PERSON><PERSON> indexes
CREATE INDEX "conversations_user1_id_idx" ON "conversations"("user1_id");
CREATE INDEX "conversations_user2_id_idx" ON "conversations"("user2_id");
CREATE INDEX "messages_conversation_id_idx" ON "messages"("conversation_id");

-- Create unique constraint to prevent duplicate conversations
CREATE UNIQUE INDEX "conversations_user1_id_user2_id_unique" ON "conversations"("user1_id", "user2_id");

-- Add foreign key constraints
ALTER TABLE "messages" ADD CONSTRAINT "messages_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "conversations"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "conversations" ADD CONSTRAINT "conversations_user1_id_fkey" FOREIGN KEY ("user1_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "conversations" ADD CONSTRAINT "conversations_user2_id_fkey" FOREIGN KEY ("user2_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
