// src/routes/admin.routes.js

const express = require('express');
const router = express.Router();
const { body } = require('express-validator');

// Import controllers and middleware
const adminController = require('../controllers/admin.controller.js');
const adminVerificationController = require('../controllers/admin.verification.controller.js');
const adminMockController = require('../controllers/admin.mock.controller.js');
const dashboardController = require('../controllers/admin/dashboard.controller.js');
const authenticateAdmin = require('../middleware/adminAuth.middleware.js');
const mockAuthenticateAdmin = require('../middleware/mockAuth.middleware.js');
const { authenticateToken, isAdmin } = require('../middleware/auth.middleware.js');
// Assuming validate function handles validation errors and passes control if valid
const { validate } = require('../validators/user.validator.js'); // Adjust path if needed

// Define allowed statuses for validation
const ALLOWED_USER_STATUSES = ['ACTIVE', 'SUSPENDED', 'INACTIVE', 'PENDING_APPROVAL', 'INCOMPLETE'];
const ALLOWED_PHOTO_STATUSES = ['APPROVED', 'REJECTED'];

// --- Admin Authentication Routes ---
router.post('/login', adminController.login);
// TODO: Add refresh token route? router.post('/refresh-token', adminController.refreshToken);
// TODO: Add logout route? router.post('/logout', authenticateAdmin, adminController.logout);


// --- Dashboard Routes (Protected) ---
// Import dashboard routes
const dashboardRoutes = require('./admin/dashboard.routes');
// Use the dashboard routes
router.use('/dashboard', dashboardRoutes);


// --- Admin User Management Routes (Protected) ---

// GET /api/admin/users - Fetch all registered users
router.get(
    '/users',
    authenticateAdmin,
    adminController.getAllUsers // Implemented
);

// GET /api/admin/users/:id - Fetch details for a specific user
router.get(
    '/users/:id',
    authenticateAdmin,
    adminController.getUserDetails // Implemented (aligned with latest schema)
);

// PATCH /api/admin/users/:id - Update user details
router.patch(
    '/users/:id',
    authenticateAdmin,
    [ // Add validation rules for the fields you allow editing from the modal
      body('fullName').optional().trim().notEmpty().withMessage('Full name cannot be empty if provided.'),
      body('email').optional().isEmail().normalizeEmail().withMessage('Invalid email format if provided.'),
      body('phone').optional().trim().notEmpty().withMessage('Phone cannot be empty if provided.'),
      body('profileStatus').optional().isIn(ALLOWED_USER_STATUSES).withMessage('Invalid profile status.'),
      body('isVerified').optional().isBoolean().withMessage('Verified must be true or false.'),
      // Add validation for other editable fields from your NEW schema here
      // e.g., body('state').optional().trim()...
    ],
    validate, // Use your validation error handler middleware
    adminController.updateUserDetails // Implemented (aligned with latest schema)
);

// PUT /api/admin/users/:id/verify - Approve a user's profile (specific action)
router.put(
    '/users/:id/verify',
    authenticateAdmin,
    adminVerificationController.verifyUser
);

// PUT /api/admin/users/:id/reject-verification - Reject a user's verification request
router.put(
    '/users/:id/reject-verification',
    authenticateAdmin,
    [
        body('reason').optional().trim()
    ],
    validate,
    adminVerificationController.rejectVerification
);

// PUT /api/admin/users/:id/status - Update a user's account status (specific action)
router.put(
    '/users/:id/status',
    authenticateAdmin,
    [
        body('status')
            .trim()
            .notEmpty().withMessage('New status is required.')
            .isIn(ALLOWED_USER_STATUSES).withMessage(`Status must be one of: ${ALLOWED_USER_STATUSES.join(', ')}`)
    ],
    validate,
    adminController.updateUserStatus // Implemented
);

// DELETE /api/admin/users/:id - Delete a user account
router.delete(
    '/users/:id',
    authenticateAdmin,
    adminController.deleteUser // Implemented
);

// GET /api/admin/users/verification-queue - Fetch users needing verification
router.get(
    '/users/verification-queue',
    authenticateAdmin, // Using real authentication
    adminController.getVerificationQueue // Using real controller
);

// GET /api/admin/users/reported - Fetch reported profiles
router.get(
    '/users/reported',
    authenticateAdmin, // Using real authentication
    adminController.getReportedProfiles
);

// GET /api/admin/users/reported/:id - Get details for a specific report
router.get(
    '/users/reported/:id',
    authenticateAdmin, // Using real authentication
    adminController.getReportDetails
);

// PUT /api/admin/users/reported/:id/status - Update report status
router.put(
    '/users/reported/:id/status',
    authenticateAdmin, // Using real authentication
    [
        body('status')
            .trim()
            .notEmpty().withMessage('New report status is required.')
            .isIn(['PENDING', 'REVIEWED', 'DISMISSED', 'ACTIONED']).withMessage('Status must be one of: PENDING, REVIEWED, DISMISSED, ACTIONED')
    ],
    validate,
    adminController.updateReportStatus
);

// GET /api/admin/users/reported/export/csv - Export reported profiles as CSV
router.get(
    '/users/reported/export/csv',
    authenticateAdmin, // Using real authentication
    adminController.exportReportedProfilesCsv
);

// GET /api/admin/users/reported/export/xlsx - Export reported profiles as Excel
router.get(
    '/users/reported/export/xlsx',
    authenticateAdmin, // Using real authentication
    adminController.exportReportedProfilesXlsx
);


// --- Admin Photo Moderation Routes (Protected) ---

// GET /api/admin/photos/pending - Fetch photos awaiting moderation
router.get(
    '/photos/pending',
    authenticateAdmin,
    adminController.getPendingPhotos // Implemented
);

// PUT /api/admin/photos/:photoId/status - Approve or Reject a photo
router.put(
    '/photos/:photoId/status',
    authenticateAdmin,
    [
        body('status')
            .trim()
            .notEmpty().withMessage('New photo status is required.')
            .isIn(ALLOWED_PHOTO_STATUSES).withMessage(`Status must be one of: ${ALLOWED_PHOTO_STATUSES.join(', ')}`)
    ],
    validate,
    adminController.updatePhotoStatus // Implemented
);

// --- Admin Verification Document Routes (Protected) ---

// PUT /api/admin/verification/documents/:documentId/review - Review a verification document
router.put(
    '/verification/documents/:documentId/review',
    authenticateAdmin,
    [
        body('status')
            .trim()
            .notEmpty().withMessage('Status is required.')
            .isIn(['APPROVED', 'REJECTED']).withMessage('Status must be either APPROVED or REJECTED'),
        body('adminNotes').optional().trim()
    ],
    validate,
    adminVerificationController.reviewVerificationDocument
);


// --- AI & Matching Routes ---
// Import AI routes
const aiRoutes = require('./admin/ai.routes');
// Use the AI routes
router.use('/ai', aiRoutes);


// --- Content Management Routes ---
// Success Stories Routes (Placeholders)
router.get('/content/success-stories', authenticateAdmin, adminController.getSuccessStories);
router.post('/content/success-stories', authenticateAdmin, /* [validation], validate, */ adminController.createSuccessStory);
router.put('/content/success-stories/:storyId', authenticateAdmin, /* [validation], validate, */ adminController.updateSuccessStory);
router.delete('/content/success-stories/:storyId', authenticateAdmin, adminController.deleteSuccessStory);

// Import blog routes
const blogRoutes = require('./admin/blog.routes');
// Use the blog routes
router.use('/content/blog', blogRoutes);


// --- Financial Routes ---
// Import financial routes
const financialRoutes = require('./admin/financial.routes');
// Use the financial routes
router.use('/financial', financialRoutes);


// --- System Routes (Placeholders) ---
router.get('/system/settings', authenticateAdmin, adminController.getSystemSettings);
router.put('/system/settings', authenticateAdmin, /* [validation], validate, */ adminController.updateSystemSettings);
router.get('/system/admins', authenticateAdmin, adminController.getAdminUsers); // Manage other admins
router.post('/system/admins', authenticateAdmin, /* [validation], validate, */ adminController.createAdminUser);

router.put('/system/admins/:adminId', authenticateAdmin, /* [validation], validate, */ adminController.updateAdminUser);
router.delete('/system/admins/:adminId', authenticateAdmin, adminController.deleteAdminUser);
router.get('/system/logs/security', authenticateAdmin, adminController.getSecurityLogs);


// --- Feature Management Routes ---
// Import feature management routes
const featureManagementRoutes = require('./admin/featureManagement.routes');
// Use the feature management routes
router.use('/feature-management', featureManagementRoutes);


module.exports = router;
