# Vaivahik Backend Production Deployment Guide

This guide provides instructions for deploying the Vaivahik backend to a production environment.

## Prerequisites

- Node.js 16+ installed on the server
- PostgreSQL 13+ database server
- Redis server (optional but recommended for caching)
- Nginx or similar web server for reverse proxy (recommended)
- SSL certificate for HTTPS

## Environment Setup

1. Clone the repository to your server:
   ```bash
   git clone https://github.com/your-username/vaivahik-backend.git
   cd vaivahik-backend
   ```

2. Install dependencies:
   ```bash
   npm install --production
   ```

3. Create a `.env` file based on the `.env.example` template:
   ```bash
   cp .env.example .env
   ```

4. Configure the environment variables in the `.env` file:
   ```
   # Server Configuration
   PORT=8080
   NODE_ENV=production

   # Frontend URL (for CORS)
   FRONTEND_URL=https://vaivahik.com

   # Database Configuration
   DATABASE_URL=postgresql://username:password@localhost:5432/vaivahik?schema=public

   # JWT Authentication
   JWT_SECRET=your_secure_jwt_secret_here
   JWT_EXPIRY=1h
   REFRESH_TOKEN_SECRET=your_secure_refresh_token_secret_here
   REFRESH_TOKEN_EXPIRY=7d
   ADMIN_JWT_SECRET=your_secure_admin_jwt_secret_here

   # Redis Configuration (if using Redis)
   REDIS_URL=redis://localhost:6379

   # Logging
   LOG_LEVEL=info
   LOG_DIR=logs

   # Feature Flags
   USE_REAL_DATA=true
   ENABLE_REDIS_CACHE=true
   ENABLE_RATE_LIMITING=true
   ```

5. Generate secure secrets for JWT tokens:
   ```bash
   node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
   ```

## Database Setup

1. Create a PostgreSQL database:
   ```sql
   CREATE DATABASE vaivahik;
   CREATE USER vaivahik_user WITH ENCRYPTED PASSWORD 'your_secure_password';
   GRANT ALL PRIVILEGES ON DATABASE vaivahik TO vaivahik_user;
   ```

2. Run database migrations:
   ```bash
   npx prisma migrate deploy
   ```

3. Seed the database with initial data:
   ```bash
   SEED_FEATURES=true node prisma/seed.js
   ```

## Running the Application

### Using PM2 (Recommended)

1. Install PM2 globally:
   ```bash
   npm install -g pm2
   ```

2. Create a PM2 ecosystem file:
   ```bash
   touch ecosystem.config.js
   ```

3. Add the following configuration to the ecosystem file:
   ```javascript
   module.exports = {
     apps: [
       {
         name: 'vaivahik-backend',
         script: 'server.js',
         instances: 'max',
         exec_mode: 'cluster',
         autorestart: true,
         watch: false,
         max_memory_restart: '1G',
         env: {
           NODE_ENV: 'production',
         },
       },
     ],
   };
   ```

4. Start the application with PM2:
   ```bash
   pm2 start ecosystem.config.js
   ```

5. Set up PM2 to start on system boot:
   ```bash
   pm2 startup
   pm2 save
   ```

### Using Systemd

1. Create a systemd service file:
   ```bash
   sudo nano /etc/systemd/system/vaivahik-backend.service
   ```

2. Add the following configuration:
   ```
   [Unit]
   Description=Vaivahik Backend
   After=network.target

   [Service]
   Type=simple
   User=your_user
   WorkingDirectory=/path/to/vaivahik-backend
   ExecStart=/usr/bin/node server.js
   Restart=on-failure
   Environment=NODE_ENV=production

   [Install]
   WantedBy=multi-user.target
   ```

3. Start and enable the service:
   ```bash
   sudo systemctl start vaivahik-backend
   sudo systemctl enable vaivahik-backend
   ```

## Nginx Configuration

1. Install Nginx:
   ```bash
   sudo apt update
   sudo apt install nginx
   ```

2. Create an Nginx configuration file:
   ```bash
   sudo nano /etc/nginx/sites-available/vaivahik-backend
   ```

3. Add the following configuration:
   ```nginx
   server {
       listen 80;
       server_name api.vaivahik.com;

       # Redirect HTTP to HTTPS
       location / {
           return 301 https://$host$request_uri;
       }
   }

   server {
       listen 443 ssl;
       server_name api.vaivahik.com;

       ssl_certificate /path/to/ssl/certificate.crt;
       ssl_certificate_key /path/to/ssl/private.key;

       # SSL configuration
       ssl_protocols TLSv1.2 TLSv1.3;
       ssl_prefer_server_ciphers on;
       ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
       ssl_session_timeout 1d;
       ssl_session_cache shared:SSL:10m;
       ssl_session_tickets off;

       # HSTS (optional)
       add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

       # Proxy settings
       location / {
           proxy_pass http://localhost:8080;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

4. Enable the site and restart Nginx:
   ```bash
   sudo ln -s /etc/nginx/sites-available/vaivahik-backend /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl restart nginx
   ```

## Monitoring and Maintenance

1. Monitor logs:
   ```bash
   # If using PM2
   pm2 logs vaivahik-backend

   # If using systemd
   journalctl -u vaivahik-backend -f
   ```

2. Set up regular database backups:
   ```bash
   # Create a backup script
   mkdir -p /path/to/backups
   
   # Add to crontab
   crontab -e
   # Add the following line for daily backups at 2 AM
   0 2 * * * pg_dump -U vaivahik_user vaivahik > /path/to/backups/vaivahik_$(date +\%Y\%m\%d).sql
   ```

3. Set up log rotation:
   ```bash
   sudo nano /etc/logrotate.d/vaivahik-backend
   ```
   
   Add the following configuration:
   ```
   /path/to/vaivahik-backend/logs/*.log {
       daily
       rotate 14
       compress
       delaycompress
       notifempty
       create 0640 your_user your_group
       sharedscripts
       postrotate
           [ -f /var/run/nginx.pid ] && kill -USR1 `cat /var/run/nginx.pid`
       endscript
   }
   ```

## Security Considerations

1. Ensure all secrets and API keys are stored securely in environment variables
2. Regularly update dependencies to patch security vulnerabilities
3. Implement rate limiting to prevent abuse
4. Use HTTPS for all communications
5. Set up a firewall to restrict access to the server
6. Regularly audit user access and permissions

## Troubleshooting

- Check the application logs in the `logs` directory
- Verify database connectivity
- Ensure environment variables are correctly set
- Check Nginx error logs: `sudo tail -f /var/log/nginx/error.log`
- Verify that the server has sufficient resources (CPU, memory, disk space)

## Updating the Application

1. Pull the latest changes:
   ```bash
   git pull origin main
   ```

2. Install dependencies:
   ```bash
   npm install --production
   ```

3. Run database migrations:
   ```bash
   npx prisma migrate deploy
   ```

4. Restart the application:
   ```bash
   # If using PM2
   pm2 restart vaivahik-backend

   # If using systemd
   sudo systemctl restart vaivahik-backend
   ```
