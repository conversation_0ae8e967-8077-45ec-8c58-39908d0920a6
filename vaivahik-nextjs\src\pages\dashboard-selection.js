/**
 * Dashboard Selection Page
 * Allows users to choose between current and professional dashboard
 */

import React from 'react';
import Head from 'next/head';
import { Container } from '@mui/material';
import DashboardNavigation from '@/components/navigation/DashboardNavigation';

export default function DashboardSelection() {
  return (
    <>
      <Head>
        <title>Choose Your Dashboard - Vaivahik Matrimony</title>
        <meta name="description" content="Choose between current dashboard and professional AI-powered dashboard" />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <DashboardNavigation />
      </Container>
    </>
  );
}
