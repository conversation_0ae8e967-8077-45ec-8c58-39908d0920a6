import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON>,
  Typography,
  IconButton,
  Badge,
  Popover,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Divider,
  Button,
  Tabs,
  Tab,
  CircularProgress,
  Chip,
  Tooltip,
  Fade,
  Skeleton,
  useTheme
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  Favorite as FavoriteIcon,
  Visibility as VisibilityIcon,
  Message as MessageIcon,
  PersonAdd as PersonAddIcon,
  Verified as VerifiedIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import SettingsIcon from '@mui/icons-material/Settings';
import DoneAllIcon from '@mui/icons-material/DoneAll';
import { 
  getUserNotifications, 
  markNotificationsAsRead, 
  getUnreadCount,
  formatNotificationTime
} from '@/services/notificationService';
import { useRouter } from 'next/router';

/**
 * Notification Center Component
 * 
 * Advanced UI for displaying and managing notifications
 */
const NotificationCenter = () => {
  const theme = useTheme();
  const router = useRouter();
  const [anchorEl, setAnchorEl] = useState(null);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [markingAllAsRead, setMarkingAllAsRead] = useState(false);
  const notificationPollingRef = useRef(null);
  
  // Load notifications on component mount
  useEffect(() => {
    loadNotifications();
    
    // Set up polling for new notifications
    notificationPollingRef.current = setInterval(() => {
      fetchUnreadCount();
    }, 30000); // Poll every 30 seconds
    
    return () => {
      if (notificationPollingRef.current) {
        clearInterval(notificationPollingRef.current);
      }
    };
  }, []);
  
  // Fetch unread count
  const fetchUnreadCount = async () => {
    try {
      const count = await getUnreadCount();
      setUnreadCount(count);
    } catch (error) {
      console.error('Error fetching unread count:', error);
    }
  };
  
  // Load notifications
  const loadNotifications = async () => {
    try {
      setLoading(true);
      
      // Fetch notifications
      const result = await getUserNotifications({
        limit: 20,
        isRead: activeTab === 1 ? true : (activeTab === 2 ? false : undefined)
      });
      
      setNotifications(result.notifications || []);
      
      // Fetch unread count
      await fetchUnreadCount();
    } catch (error) {
      console.error('Error loading notifications:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // Handle notification click
  const handleNotificationClick = (event) => {
    setAnchorEl(event.currentTarget);
    loadNotifications();
  };
  
  // Handle close
  const handleClose = () => {
    setAnchorEl(null);
  };
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    loadNotifications();
  };
  
  // Mark all as read
  const handleMarkAllAsRead = async () => {
    try {
      setMarkingAllAsRead(true);
      
      // Mark all notifications as read
      await markNotificationsAsRead();
      
      // Refresh notifications
      await loadNotifications();
    } catch (error) {
      console.error('Error marking notifications as read:', error);
    } finally {
      setMarkingAllAsRead(false);
    }
  };
  
  // Handle notification item click
  const handleNotificationItemClick = async (notification) => {
    try {
      // Mark notification as read if it's unread
      if (!notification.isRead) {
        await markNotificationsAsRead([notification._id]);
        
        // Update local state
        setNotifications(prev => 
          prev.map(item => 
            item._id === notification._id 
              ? { ...item, isRead: true } 
              : item
          )
        );
        
        // Update unread count
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
      
      // Navigate based on notification type
      switch (notification.notificationType) {
        case 'INTEREST_RECEIVED':
        case 'INTEREST_ACCEPTED':
        case 'INTEREST_DECLINED':
          router.push('/interests');
          break;
          
        case 'PROFILE_VIEWED':
          router.push('/visitors');
          break;
          
        case 'NEW_MESSAGE':
          router.push('/messages');
          break;
          
        case 'NEW_MATCH':
        case 'PREMIUM_MATCH':
          router.push('/matches');
          break;
          
        case 'PROFILE_VERIFICATION':
          router.push('/verification');
          break;
          
        default:
          // If there's a sender, navigate to their profile
          if (notification.sender) {
            router.push(`/profile/${notification.sender._id}`);
          }
      }
      
      // Close popover
      handleClose();
    } catch (error) {
      console.error('Error handling notification click:', error);
    }
  };
  
  // Get notification icon based on type
  const getNotificationIcon = (type) => {
    switch (type) {
      case 'INTEREST_RECEIVED':
        return <FavoriteIcon color="error" />;
      case 'INTEREST_ACCEPTED':
        return <CheckCircleIcon color="success" />;
      case 'INTEREST_DECLINED':
        return <CancelIcon color="error" />;
      case 'PROFILE_VIEWED':
        return <VisibilityIcon color="primary" />;
      case 'NEW_MESSAGE':
        return <MessageIcon color="info" />;
      case 'NEW_MATCH':
      case 'PREMIUM_MATCH':
        return <PersonAddIcon color="secondary" />;
      case 'PROFILE_VERIFICATION':
        return <VerifiedIcon color="success" />;
      default:
        return <NotificationsIcon color="action" />;
    }
  };
  
  const open = Boolean(anchorEl);
  
  return (
    <>
      <IconButton
        color="inherit"
        onClick={handleNotificationClick}
        aria-label="notifications"
      >
        <Badge badgeContent={unreadCount} color="error" max={99}>
          <NotificationsIcon />
        </Badge>
      </IconButton>
      
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          sx: {
            width: 360,
            maxHeight: 500,
            borderRadius: 2,
            boxShadow: theme.shadows[8]
          }
        }}
      >
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">Notifications</Typography>
          <Box>
            <Tooltip title="Mark all as read">
              <IconButton 
                size="small" 
                onClick={handleMarkAllAsRead}
                disabled={markingAllAsRead || unreadCount === 0}
              >
                {markingAllAsRead ? (
                  <CircularProgress size={20} />
                ) : (
                  <DoneAllIcon fontSize="small" />
                )}
              </IconButton>
            </Tooltip>
            <Tooltip title="Notification settings">
              <IconButton 
                size="small"
                onClick={() => {
                  router.push('/settings?tab=notifications');
                  handleClose();
                }}
              >
                <SettingsIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
        
        <Divider />
        
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={activeTab} 
            onChange={handleTabChange} 
            variant="fullWidth"
            indicatorColor="primary"
            textColor="primary"
          >
            <Tab label="All" />
            <Tab label="Read" />
            <Tab label="Unread" />
          </Tabs>
        </Box>
        
        {loading ? (
          <List sx={{ p: 0 }}>
            {[1, 2, 3, 4].map((item) => (
              <React.Fragment key={item}>
                <ListItem alignItems="flex-start">
                  <ListItemAvatar>
                    <Skeleton variant="circular" width={40} height={40} />
                  </ListItemAvatar>
                  <ListItemText
                    primary={<Skeleton variant="text" width="60%" />}
                    secondary={
                      <>
                        <Skeleton variant="text" width="90%" />
                        <Skeleton variant="text" width="40%" />
                      </>
                    }
                  />
                </ListItem>
                <Divider variant="inset" component="li" />
              </React.Fragment>
            ))}
          </List>
        ) : notifications.length === 0 ? (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <NotificationsIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="body1" color="text.secondary" gutterBottom>
              No notifications
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {activeTab === 0 
                ? "You don't have any notifications yet" 
                : activeTab === 1 
                  ? "You don't have any read notifications" 
                  : "You don't have any unread notifications"}
            </Typography>
          </Box>
        ) : (
          <List sx={{ p: 0 }}>
            {notifications.map((notification) => (
              <React.Fragment key={notification._id}>
                <ListItem 
                  alignItems="flex-start" 
                  button 
                  onClick={() => handleNotificationItemClick(notification)}
                  sx={{
                    backgroundColor: notification.isRead ? 'inherit' : 'action.hover',
                    transition: 'background-color 0.3s ease',
                    '&:hover': {
                      backgroundColor: notification.isRead ? 'action.hover' : 'action.selected'
                    }
                  }}
                >
                  <ListItemAvatar>
                    <Avatar 
                      src={notification.sender?.profilePhoto} 
                      alt={notification.sender?.name}
                    >
                      {getNotificationIcon(notification.notificationType)}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography 
                          variant="subtitle2" 
                          component="span"
                          sx={{ fontWeight: notification.isRead ? 'normal' : 'bold' }}
                        >
                          {notification.title}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatNotificationTime(notification.createdAt)}
                        </Typography>
                      </Box>
                    }
                    secondary={
                      <>
                        <Typography
                          variant="body2"
                          color="text.primary"
                          component="span"
                          sx={{ 
                            display: 'inline',
                            fontWeight: notification.isRead ? 'normal' : 'medium'
                          }}
                        >
                          {notification.message}
                        </Typography>
                        {!notification.isRead && (
                          <Chip 
                            label="New" 
                            size="small" 
                            color="primary" 
                            sx={{ ml: 1, height: 20, fontSize: '0.7rem' }} 
                          />
                        )}
                      </>
                    }
                  />
                </ListItem>
                <Divider variant="inset" component="li" />
              </React.Fragment>
            ))}
          </List>
        )}
        
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Button 
            variant="outlined" 
            fullWidth
            onClick={() => {
              router.push('/notifications');
              handleClose();
            }}
          >
            View All Notifications
          </Button>
        </Box>
      </Popover>
    </>
  );
};

export default NotificationCenter;
