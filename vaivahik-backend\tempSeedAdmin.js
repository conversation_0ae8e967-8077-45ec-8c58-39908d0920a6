// tempSeedAdmin.js
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  const adminEmail = '<EMAIL>';
  const adminPassword = 'admin123'; // Choose a secure password

  // Check if admin already exists
  const existingAdmin = await prisma.admin.findUnique({
    where: { email: adminEmail },
  });

  if (existingAdmin) {
    console.log(`Admin with email ${adminEmail} already exists.`);
    return;
  }

  // Hash the password
  const saltRounds = 10;
  const hashedPassword = await bcrypt.hash(adminPassword, saltRounds);

  // Create the admin user
  const newAdmin = await prisma.admin.create({
    data: {
      email: adminEmail,
      password: hashedPassword,
      role: 'SUPER_ADMIN', // Or 'ADMIN'
    },
  });

  console.log(`Admin user created successfully:`);
  console.log(newAdmin);
}

main()
  .catch(async (e) => {
    console.error('Error seeding admin:', e);
    await prisma.$disconnect();
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });