/**
 * Search Module
 *
 * This file exports all search-related functionality for the website
 */

// Export components
export { default as FilterChips } from './components/FilterChips';
export { default as SearchBar } from './components/SearchBar';
export { default as HeightRangeSelector } from './components/HeightRangeSelector';

// Export hooks
export { default as useSearch } from './hooks/useSearch';

// Export API functions
export {
  searchProfiles,
  advancedSearch,
  findSimilarProfiles,
  getCompatibilityScore
} from './api';

// Export utilities
export * from './utils/heightUtils';
export { generateMockResults } from './utils/mockDataGenerator';

// Export components from the old structure (for backward compatibility)
export { default as PremiumSearchBar } from '@/components/search/PremiumSearchBar';
