// API endpoint for specific feature
import { generateMockFeatures } from '@/utils/mockData';

export default function handler(req, res) {
  // Get the feature ID from the URL
  const { id } = req.query;
  
  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getFeatureById(req, res, id);
    case 'DELETE':
      return deleteFeature(req, res, id);
    default:
      return res.status(405).json({ message: 'Method not allowed' });
  }
}

// GET /api/admin/features/[id]
function getFeatureById(req, res, id) {
  try {
    // In a real implementation, this would fetch data from a database
    // For now, we'll use mock data
    const mockFeatures = generateMockFeatures();
    
    // Find the feature by ID
    const feature = mockFeatures.find(f => f.id.toString() === id);
    
    // If feature not found, return 404
    if (!feature) {
      return res.status(404).json({ 
        success: false, 
        message: 'Feature not found' 
      });
    }
    
    // Return the feature
    return res.status(200).json({
      success: true,
      feature
    });
  } catch (error) {
    console.error('Error fetching feature:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch feature' 
    });
  }
}

// DELETE /api/admin/features/[id]
function deleteFeature(req, res, id) {
  try {
    // In a real implementation, this would delete the feature from the database
    // For now, we'll just return a success response
    
    return res.status(200).json({
      success: true,
      message: 'Feature deleted successfully',
      featureId: id
    });
  } catch (error) {
    console.error('Error deleting feature:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to delete feature' 
    });
  }
}
