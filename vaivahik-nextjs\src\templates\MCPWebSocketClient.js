/**
 * MCP WebSocket Client Template for Next.js
 * Complete template for integrating MCP server with authentication
 */

import { useState, useEffect, useRef, useCallback } from 'react';

class MCPWebSocketClient {
  constructor(serverUrl, options = {}) {
    this.serverUrl = serverUrl;
    this.options = {
      autoReconnect: true,
      maxReconnectAttempts: 5,
      reconnectDelay: 1000,
      heartbeatInterval: 30000,
      ...options
    };
    
    this.ws = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.messageId = 0;
    this.pendingRequests = new Map();
    this.eventListeners = new Map();
    this.heartbeatTimer = null;
    this.token = null;
  }

  /**
   * Connect to MCP server with authentication
   */
  async connect(token) {
    return new Promise((resolve, reject) => {
      try {
        this.token = token;
        const wsUrl = token 
          ? `${this.serverUrl}?token=${encodeURIComponent(token)}`
          : this.serverUrl;
        
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('Connected to MCP server');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.emit('connected');
          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(JSON.parse(event.data));
        };

        this.ws.onclose = (event) => {
          console.log('Disconnected from MCP server:', event.code, event.reason);
          this.isConnected = false;
          this.stopHeartbeat();
          this.emit('disconnected', { code: event.code, reason: event.reason });
          
          if (this.options.autoReconnect && this.reconnectAttempts < this.options.maxReconnectAttempts) {
            this.attemptReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('MCP WebSocket error:', error);
          this.emit('error', error);
          reject(error);
        };

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Disconnect from MCP server
   */
  disconnect() {
    this.options.autoReconnect = false;
    this.stopHeartbeat();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    
    this.isConnected = false;
    this.emit('disconnected', { code: 1000, reason: 'Client disconnect' });
  }

  /**
   * Send message to MCP server
   */
  send(message) {
    if (!this.isConnected || !this.ws) {
      throw new Error('Not connected to MCP server');
    }

    this.ws.send(JSON.stringify(message));
  }

  /**
   * Call MCP tool with promise-based response
   */
  async callTool(toolName, args = {}, options = {}) {
    return new Promise((resolve, reject) => {
      const messageId = ++this.messageId;
      const timeout = options.timeout || 30000;

      const message = {
        jsonrpc: '2.0',
        id: messageId,
        method: 'tools/call',
        params: {
          name: toolName,
          arguments: args
        }
      };

      // Store pending request
      const timeoutId = setTimeout(() => {
        this.pendingRequests.delete(messageId);
        reject(new Error(`Tool call timeout: ${toolName}`));
      }, timeout);

      this.pendingRequests.set(messageId, {
        resolve,
        reject,
        timeoutId,
        toolName,
        startTime: Date.now()
      });

      this.send(message);
    });
  }

  /**
   * Handle incoming messages
   */
  handleMessage(message) {
    // Handle responses to pending requests
    if (message.id && this.pendingRequests.has(message.id)) {
      const request = this.pendingRequests.get(message.id);
      clearTimeout(request.timeoutId);
      this.pendingRequests.delete(message.id);

      if (message.error) {
        request.reject(new Error(message.error.message || 'Tool call failed'));
      } else {
        const executionTime = Date.now() - request.startTime;
        console.log(`Tool ${request.toolName} executed in ${executionTime}ms`);
        request.resolve(message.result);
      }
      return;
    }

    // Handle notifications and other messages
    if (message.method) {
      this.emit('notification', message);
    }

    this.emit('message', message);
  }

  /**
   * Start heartbeat to keep connection alive
   */
  startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.send({ type: 'ping', timestamp: Date.now() });
      }
    }, this.options.heartbeatInterval);
  }

  /**
   * Stop heartbeat
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * Attempt to reconnect
   */
  attemptReconnect() {
    this.reconnectAttempts++;
    const delay = this.options.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.options.maxReconnectAttempts}) in ${delay}ms`);
    
    setTimeout(() => {
      if (this.reconnectAttempts <= this.options.maxReconnectAttempts) {
        this.connect(this.token).catch(() => {
          // Reconnection failed, will try again if attempts remain
        });
      }
    }, delay);
  }

  /**
   * Event emitter functionality
   */
  on(event, listener) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(listener);
  }

  off(event, listener) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error('Error in event listener:', error);
        }
      });
    }
  }
}

/**
 * React Hook for MCP WebSocket Client
 */
export const useMCPClient = (serverUrl, token, options = {}) => {
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState(null);
  const [lastMessage, setLastMessage] = useState(null);
  const clientRef = useRef(null);

  // Initialize client
  useEffect(() => {
    if (!serverUrl) return;

    const client = new MCPWebSocketClient(serverUrl, options);
    clientRef.current = client;

    // Set up event listeners
    client.on('connected', () => {
      setIsConnected(true);
      setError(null);
    });

    client.on('disconnected', () => {
      setIsConnected(false);
    });

    client.on('error', (err) => {
      setError(err.message || 'Connection error');
    });

    client.on('message', (message) => {
      setLastMessage(message);
    });

    return () => {
      client.disconnect();
    };
  }, [serverUrl]);

  // Connect when token is available
  useEffect(() => {
    if (clientRef.current && token && !isConnected) {
      clientRef.current.connect(token).catch((err) => {
        setError(err.message || 'Failed to connect');
      });
    }
  }, [token, isConnected]);

  // Memoized functions
  const callTool = useCallback(async (toolName, args, options) => {
    if (!clientRef.current) {
      throw new Error('Client not initialized');
    }
    return clientRef.current.callTool(toolName, args, options);
  }, []);

  const disconnect = useCallback(() => {
    if (clientRef.current) {
      clientRef.current.disconnect();
    }
  }, []);

  return {
    isConnected,
    error,
    lastMessage,
    callTool,
    disconnect,
    client: clientRef.current
  };
};

export default MCPWebSocketClient;
