/**
 * MCP Client for Frontend
 * Connects to the MCP server for AI-powered features
 */

class MCPClient {
  constructor(serverUrl = 'ws://localhost:8001') {
    this.serverUrl = serverUrl;
    this.ws = null;
    this.isConnected = false;
    this.messageId = 0;
    this.pendingRequests = new Map();
    this.capabilities = null;
    this.tools = [];
    this.resources = [];
    this.prompts = [];
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
  }

  /**
   * Connect to MCP server
   */
  async connect() {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.serverUrl);

        this.ws.onopen = () => {
          console.log('Connected to MCP server');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.initialize().then(resolve).catch(reject);
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(JSON.parse(event.data));
        };

        this.ws.onclose = () => {
          console.log('Disconnected from MCP server');
          this.isConnected = false;
          this.attemptReconnect();
        };

        this.ws.onerror = (error) => {
          console.error('MCP WebSocket error:', error);
          reject(error);
        };

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Disconnect from MCP server
   */
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
      this.isConnected = false;
    }
  }

  /**
   * Initialize connection with server
   */
  async initialize() {
    const response = await this.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {
        roots: { listChanged: true },
        sampling: {}
      },
      clientInfo: {
        name: 'Vaivahik Admin Panel',
        version: '1.0.0'
      }
    });

    this.capabilities = response.capabilities;
    
    // Load available tools, resources, and prompts
    await this.loadTools();
    await this.loadResources();
    await this.loadPrompts();

    return response;
  }

  /**
   * Load available tools from server
   */
  async loadTools() {
    try {
      const response = await this.sendRequest('tools/list');
      this.tools = response.tools || [];
      console.log(`Loaded ${this.tools.length} MCP tools`);
    } catch (error) {
      console.error('Error loading tools:', error);
    }
  }

  /**
   * Load available resources from server
   */
  async loadResources() {
    try {
      const response = await this.sendRequest('resources/list');
      this.resources = response.resources || [];
      console.log(`Loaded ${this.resources.length} MCP resources`);
    } catch (error) {
      console.error('Error loading resources:', error);
    }
  }

  /**
   * Load available prompts from server
   */
  async loadPrompts() {
    try {
      const response = await this.sendRequest('prompts/list');
      this.prompts = response.prompts || [];
      console.log(`Loaded ${this.prompts.length} MCP prompts`);
    } catch (error) {
      console.error('Error loading prompts:', error);
    }
  }

  /**
   * Call a tool on the MCP server
   */
  async callTool(name, args = {}) {
    if (!this.isConnected) {
      throw new Error('Not connected to MCP server');
    }

    const tool = this.tools.find(t => t.name === name);
    if (!tool) {
      throw new Error(`Tool not found: ${name}`);
    }

    try {
      const response = await this.sendRequest('tools/call', {
        name: name,
        arguments: args
      });

      return response;
    } catch (error) {
      console.error(`Error calling tool ${name}:`, error);
      throw error;
    }
  }

  /**
   * Read a resource from the MCP server
   */
  async readResource(uri) {
    if (!this.isConnected) {
      throw new Error('Not connected to MCP server');
    }

    try {
      const response = await this.sendRequest('resources/read', {
        uri: uri
      });

      return response;
    } catch (error) {
      console.error(`Error reading resource ${uri}:`, error);
      throw error;
    }
  }

  /**
   * Get a prompt from the MCP server
   */
  async getPrompt(name, args = {}) {
    if (!this.isConnected) {
      throw new Error('Not connected to MCP server');
    }

    const prompt = this.prompts.find(p => p.name === name);
    if (!prompt) {
      throw new Error(`Prompt not found: ${name}`);
    }

    try {
      const response = await this.sendRequest('prompts/get', {
        name: name,
        arguments: args
      });

      return response;
    } catch (error) {
      console.error(`Error getting prompt ${name}:`, error);
      throw error;
    }
  }

  /**
   * Send request to MCP server
   */
  async sendRequest(method, params = {}) {
    return new Promise((resolve, reject) => {
      if (!this.isConnected || !this.ws) {
        reject(new Error('Not connected to MCP server'));
        return;
      }

      const id = ++this.messageId;
      const message = {
        jsonrpc: '2.0',
        id: id,
        method: method,
        params: params
      };

      this.pendingRequests.set(id, { resolve, reject });

      try {
        this.ws.send(JSON.stringify(message));
      } catch (error) {
        this.pendingRequests.delete(id);
        reject(error);
      }

      // Set timeout for request
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error(`Request timeout for method: ${method}`));
        }
      }, 30000); // 30 second timeout
    });
  }

  /**
   * Handle incoming messages from server
   */
  handleMessage(message) {
    const { id, result, error, method } = message;

    if (id && this.pendingRequests.has(id)) {
      const { resolve, reject } = this.pendingRequests.get(id);
      this.pendingRequests.delete(id);

      if (error) {
        reject(new Error(error.message || 'Unknown error'));
      } else {
        resolve(result);
      }
    } else if (method) {
      // Handle server notifications
      this.handleNotification(method, message.params);
    }
  }

  /**
   * Handle server notifications
   */
  handleNotification(method, params) {
    switch (method) {
      case 'tools/list_changed':
        this.loadTools();
        break;
      case 'resources/list_changed':
        this.loadResources();
        break;
      case 'prompts/list_changed':
        this.loadPrompts();
        break;
      default:
        console.log(`Received notification: ${method}`, params);
    }
  }

  /**
   * Attempt to reconnect to server
   */
  attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);

    setTimeout(() => {
      this.connect().catch(error => {
        console.error('Reconnection failed:', error);
      });
    }, delay);
  }

  /**
   * Get connection status
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      serverUrl: this.serverUrl,
      toolsCount: this.tools.length,
      resourcesCount: this.resources.length,
      promptsCount: this.prompts.length,
      pendingRequests: this.pendingRequests.size
    };
  }

  /**
   * High-level AI methods for easy use
   */

  // Find matches for a user
  async findMatches(userId, options = {}) {
    const { limit = 10, filters = {} } = options;
    
    try {
      const response = await this.callTool('user_matching', {
        userId,
        limit,
        filters
      });

      const result = JSON.parse(response.content[0].text);
      return result.matches;
    } catch (error) {
      console.error('Error finding matches:', error);
      throw error;
    }
  }

  // Analyze user profile
  async analyzeProfile(userId, includeRecommendations = true) {
    try {
      const response = await this.callTool('profile_analysis', {
        userId,
        includeRecommendations
      });

      const result = JSON.parse(response.content[0].text);
      return result.analysis;
    } catch (error) {
      console.error('Error analyzing profile:', error);
      throw error;
    }
  }

  // Calculate compatibility between users
  async calculateCompatibility(userId1, userId2, detailed = false) {
    try {
      const response = await this.callTool('compatibility_score', {
        userId1,
        userId2,
        detailed
      });

      const result = JSON.parse(response.content[0].text);
      return result.compatibilityScore;
    } catch (error) {
      console.error('Error calculating compatibility:', error);
      throw error;
    }
  }

  // Get smart recommendations
  async getRecommendations(userId, type) {
    try {
      const response = await this.callTool('smart_recommendations', {
        userId,
        type
      });

      const result = JSON.parse(response.content[0].text);
      return result.recommendations;
    } catch (error) {
      console.error('Error getting recommendations:', error);
      throw error;
    }
  }

  // Detect fraud
  async detectFraud(userId, checkType = 'profile') {
    try {
      const response = await this.callTool('fraud_detection', {
        userId,
        checkType
      });

      const result = JSON.parse(response.content[0].text);
      return result.fraudAnalysis;
    } catch (error) {
      console.error('Error detecting fraud:', error);
      throw error;
    }
  }

  // Predict relationship success
  async predictSuccess(userId1, userId2, factors = []) {
    try {
      const response = await this.callTool('success_prediction', {
        userId1,
        userId2,
        factors
      });

      const result = JSON.parse(response.content[0].text);
      return result.prediction;
    } catch (error) {
      console.error('Error predicting success:', error);
      throw error;
    }
  }
}

// Create singleton instance
const mcpClient = new MCPClient();

export default mcpClient;
