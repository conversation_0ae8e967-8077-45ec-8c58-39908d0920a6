/**
 * This script adds banner comments to page files to indicate which directory structure they belong to.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readdir = promisify(fs.readdir);
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const stat = promisify(fs.stat);

// Get the root directory of the project
const rootDir = path.resolve(__dirname, '..');

// Banner comments
const pagesBanner = `/**
 * PRIMARY PAGES DIRECTORY
 * This file is in the /pages directory, which is the primary directory for Next.js pages.
 * New pages should be added to this directory.
 */
`;

const srcPagesBanner = `/**
 * LEGACY PAGES DIRECTORY
 * This file is in the /src/pages directory, which is maintained for backward compatibility.
 * New pages should be added to the /pages directory instead.
 */
`;

// Recursively get all JavaScript files in a directory
async function getJsFiles(dir) {
  const files = [];
  const entries = await readdir(dir, { withFileTypes: true });

  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    
    if (entry.isDirectory()) {
      const subFiles = await getJsFiles(fullPath);
      files.push(...subFiles);
    } else if (entry.name.endsWith('.js') || entry.name.endsWith('.jsx')) {
      files.push(fullPath);
    }
  }

  return files;
}

// Add banner comments to files
async function addBanners() {
  try {
    // Get all JavaScript files in the pages directory
    const pagesDir = path.join(rootDir, 'pages');
    const pagesFiles = await getJsFiles(pagesDir);

    // Get all JavaScript files in the src/pages directory
    const srcPagesDir = path.join(rootDir, 'src', 'pages');
    const srcPagesFiles = await getJsFiles(srcPagesDir);

    // Add banners to pages files
    for (const file of pagesFiles) {
      const content = await readFile(file, 'utf8');
      
      // Skip if the file already has a banner
      if (content.includes('PRIMARY PAGES DIRECTORY')) {
        continue;
      }
      
      await writeFile(file, pagesBanner + content);
      console.log(`Added banner to ${file}`);
    }

    // Add banners to src/pages files
    for (const file of srcPagesFiles) {
      const content = await readFile(file, 'utf8');
      
      // Skip if the file already has a banner
      if (content.includes('LEGACY PAGES DIRECTORY')) {
        continue;
      }
      
      await writeFile(file, srcPagesBanner + content);
      console.log(`Added banner to ${file}`);
    }

    console.log('✅ Added directory banners to all page files');
  } catch (error) {
    console.error('Error adding banners:', error);
  }
}

// Run the script
addBanners();
