import { useState } from 'react';
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  AlertTitle,
  Button,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  TextField,
  Chip
} from '@mui/material';
import {
  Code as CodeIcon,
  Storage as StorageIcon,
  CloudUpload as CloudUploadIcon,
  Settings as SettingsIcon,
  Security as SecurityIcon,
  Speed as SpeedIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Info as InfoIcon
} from '@mui/icons-material';

// Tab Panel Component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`deployment-tabpanel-${index}`}
      aria-labelledby={`deployment-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

export default function DeploymentGuide() {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  return (
    <EnhancedAdminLayout title="Deployment Guide">
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Deployment Guide
        </Typography>

        <Alert severity="info" sx={{ mb: 3 }}>
          <AlertTitle>Preparing for Deployment</AlertTitle>
          This guide provides step-by-step instructions for deploying the Vaivahik Admin Panel to production.
        </Alert>

        <Paper sx={{ width: '100%', mb: 3 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              aria-label="deployment tabs"
            >
              <Tab label="Prerequisites" icon={<CheckCircleIcon />} iconPosition="start" />
              <Tab label="Environment Setup" icon={<SettingsIcon />} iconPosition="start" />
              <Tab label="Build Process" icon={<CodeIcon />} iconPosition="start" />
              <Tab label="Hosting Options" icon={<CloudUploadIcon />} iconPosition="start" />
              <Tab label="Database Setup" icon={<StorageIcon />} iconPosition="start" />
              <Tab label="Performance" icon={<SpeedIcon />} iconPosition="start" />
              <Tab label="Security" icon={<SecurityIcon />} iconPosition="start" />
            </Tabs>
          </Box>

          {/* Prerequisites Tab */}
          <TabPanel value={tabValue} index={0}>
            <Typography variant="h6" gutterBottom>
              Prerequisites
            </Typography>
            <Typography paragraph>
              Before deploying the Vaivahik Admin Panel to production, ensure you have the following prerequisites:
            </Typography>

            <List>
              <ListItem>
                <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                <ListItemText 
                  primary="Node.js 14.x or later" 
                  secondary="Required for building and running the Next.js application" 
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                <ListItemText 
                  primary="npm 7.x or later" 
                  secondary="Package manager for installing dependencies" 
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                <ListItemText 
                  primary="Git" 
                  secondary="Version control system for managing code" 
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                <ListItemText 
                  primary="Hosting account" 
                  secondary="Hostinger VPS or similar hosting provider" 
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                <ListItemText 
                  primary="Domain name (optional)" 
                  secondary="For accessing the admin panel" 
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                <ListItemText 
                  primary="SSL certificate (recommended)" 
                  secondary="For secure HTTPS connections" 
                />
              </ListItem>
            </List>

            <Alert severity="warning" sx={{ mt: 3 }}>
              <AlertTitle>Important</AlertTitle>
              Make sure all mock data is replaced with real API connections before deploying to production.
            </Alert>
          </TabPanel>

          {/* Environment Setup Tab */}
          <TabPanel value={tabValue} index={1}>
            <Typography variant="h6" gutterBottom>
              Environment Setup
            </Typography>
            <Typography paragraph>
              Configure environment variables for production deployment:
            </Typography>

            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardHeader title="Environment Variables" />
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>
                  Create a <code>.env.production</code> file with the following variables:
                </Typography>
                <Box sx={{ bgcolor: 'grey.100', p: 2, borderRadius: 1, mb: 2 }}>
                  <Typography variant="body2" component="pre" sx={{ fontFamily: 'monospace' }}>
                    {`# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://api.vaivahik.com/api
NEXT_PUBLIC_USE_MOCK_DATA=false

# Authentication
NEXT_PUBLIC_AUTH_DOMAIN=vaivahik.com
NEXT_PUBLIC_AUTH_COOKIE_SECURE=true

# Analytics
NEXT_PUBLIC_ANALYTICS_ID=UA-XXXXXXXXX-X

# Other Settings
NEXT_PUBLIC_SITE_URL=https://admin.vaivahik.com`}
                  </Typography>
                </Box>
                <Typography variant="subtitle1" gutterBottom>
                  For Vercel deployment, add these environment variables in the Vercel dashboard.
                </Typography>
              </CardContent>
            </Card>

            <Alert severity="info">
              <AlertTitle>Environment Variables Security</AlertTitle>
              Never commit sensitive environment variables to your repository. Use environment variable management provided by your hosting platform.
            </Alert>
          </TabPanel>

          {/* Build Process Tab */}
          <TabPanel value={tabValue} index={2}>
            <Typography variant="h6" gutterBottom>
              Build Process
            </Typography>
            <Typography paragraph>
              Follow these steps to build the application for production:
            </Typography>

            <Stepper orientation="vertical" sx={{ mb: 3 }}>
              <Step active={true}>
                <StepLabel>Install Dependencies</StepLabel>
                <StepContent>
                  <Box sx={{ bgcolor: 'grey.100', p: 2, borderRadius: 1, mb: 2 }}>
                    <Typography variant="body2" component="pre" sx={{ fontFamily: 'monospace' }}>
                      npm install --production
                    </Typography>
                  </Box>
                  <Typography variant="body2">
                    This installs only the dependencies required for production, skipping development dependencies.
                  </Typography>
                </StepContent>
              </Step>
              <Step active={true}>
                <StepLabel>Build the Application</StepLabel>
                <StepContent>
                  <Box sx={{ bgcolor: 'grey.100', p: 2, borderRadius: 1, mb: 2 }}>
                    <Typography variant="body2" component="pre" sx={{ fontFamily: 'monospace' }}>
                      npm run build
                    </Typography>
                  </Box>
                  <Typography variant="body2">
                    This creates an optimized production build in the <code>.next</code> directory.
                  </Typography>
                </StepContent>
              </Step>
              <Step active={true}>
                <StepLabel>Test the Production Build</StepLabel>
                <StepContent>
                  <Box sx={{ bgcolor: 'grey.100', p: 2, borderRadius: 1, mb: 2 }}>
                    <Typography variant="body2" component="pre" sx={{ fontFamily: 'monospace' }}>
                      npm run start
                    </Typography>
                  </Box>
                  <Typography variant="body2">
                    This starts the application in production mode. Visit http://localhost:3000 to verify it works correctly.
                  </Typography>
                </StepContent>
              </Step>
            </Stepper>

            <Alert severity="warning">
              <AlertTitle>Build Verification</AlertTitle>
              Always test the production build locally before deploying to ensure everything works as expected.
            </Alert>
          </TabPanel>

          {/* Hosting Options Tab */}
          <TabPanel value={tabValue} index={3}>
            <Typography variant="h6" gutterBottom>
              Hosting Options
            </Typography>
            <Typography paragraph>
              There are several options for hosting your Next.js application:
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card variant="outlined" sx={{ height: '100%' }}>
                  <CardHeader 
                    title="Hostinger VPS" 
                    subheader="Recommended Option"
                    action={<Chip label="Recommended" color="primary" />}
                  />
                  <CardContent>
                    <Typography paragraph>
                      Deploy to your Hostinger VPS using the following steps:
                    </Typography>
                    <List dense>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                        <ListItemText primary="SSH into your VPS" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                        <ListItemText primary="Clone your repository" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                        <ListItemText primary="Install dependencies and build" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                        <ListItemText primary="Set up PM2 for process management" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                        <ListItemText primary="Configure Nginx as a reverse proxy" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                        <ListItemText primary="Set up SSL with Let's Encrypt" />
                      </ListItem>
                    </List>
                    <Button 
                      variant="contained" 
                      color="primary" 
                      fullWidth
                      sx={{ mt: 2 }}
                      href="https://www.hostinger.com/tutorials/vps/how-to-deploy-nodejs-application-with-nginx-and-pm2"
                      target="_blank"
                    >
                      View Hostinger Guide
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card variant="outlined" sx={{ height: '100%' }}>
                  <CardHeader 
                    title="Vercel" 
                    subheader="Alternative Option"
                  />
                  <CardContent>
                    <Typography paragraph>
                      Deploy to Vercel for simplified deployment:
                    </Typography>
                    <List dense>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                        <ListItemText primary="Connect your GitHub repository to Vercel" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                        <ListItemText primary="Configure environment variables in Vercel dashboard" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                        <ListItemText primary="Deploy with a single click" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                        <ListItemText primary="Set up custom domain and SSL" />
                      </ListItem>
                    </List>
                    <Button 
                      variant="contained" 
                      color="primary" 
                      fullWidth
                      sx={{ mt: 2 }}
                      href="https://vercel.com/docs/concepts/deployments/overview"
                      target="_blank"
                    >
                      View Vercel Guide
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Database Setup Tab */}
          <TabPanel value={tabValue} index={4}>
            <Typography variant="h6" gutterBottom>
              Database Setup
            </Typography>
            <Typography paragraph>
              Configure your database connections for production:
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardHeader title="PostgreSQL Setup" />
                  <CardContent>
                    <Typography paragraph>
                      Ensure your PostgreSQL database is properly configured:
                    </Typography>
                    <List dense>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                        <ListItemText primary="Create production database with proper credentials" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                        <ListItemText primary="Configure connection pooling for optimal performance" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                        <ListItemText primary="Set up regular backups" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                        <ListItemText primary="Configure database security (firewall, access controls)" />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardHeader title="Redis Setup" />
                  <CardContent>
                    <Typography paragraph>
                      Configure Redis for caching and session management:
                    </Typography>
                    <List dense>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                        <ListItemText primary="Install and configure Redis server" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                        <ListItemText primary="Set up password authentication" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                        <ListItemText primary="Configure persistence options" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
                        <ListItemText primary="Set appropriate memory limits" />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            <Alert severity="info" sx={{ mt: 3 }}>
              <AlertTitle>Database Connection</AlertTitle>
              Store database connection strings as environment variables, never in your code.
            </Alert>
          </TabPanel>

          {/* Performance Tab */}
          <TabPanel value={tabValue} index={5}>
            <Typography variant="h6" gutterBottom>
              Performance Optimization
            </Typography>
            <Typography paragraph>
              Ensure optimal performance in production:
            </Typography>

            <List>
              <ListItem>
                <ListItemIcon><SpeedIcon color="primary" /></ListItemIcon>
                <ListItemText 
                  primary="Enable Gzip Compression" 
                  secondary="Configure your server to compress responses for faster loading" 
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><SpeedIcon color="primary" /></ListItemIcon>
                <ListItemText 
                  primary="Configure Caching Headers" 
                  secondary="Set appropriate cache headers for static assets" 
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><SpeedIcon color="primary" /></ListItemIcon>
                <ListItemText 
                  primary="Use a CDN" 
                  secondary="Configure a CDN for serving static assets" 
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><SpeedIcon color="primary" /></ListItemIcon>
                <ListItemText 
                  primary="Optimize Images" 
                  secondary="Ensure all images are properly optimized" 
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><SpeedIcon color="primary" /></ListItemIcon>
                <ListItemText 
                  primary="Implement Server-Side Rendering" 
                  secondary="Use Next.js SSR features for faster initial page loads" 
                />
              </ListItem>
            </List>

            <Alert severity="info" sx={{ mt: 3 }}>
              <AlertTitle>Performance Monitoring</AlertTitle>
              Set up performance monitoring tools to track and improve application performance over time.
            </Alert>
          </TabPanel>

          {/* Security Tab */}
          <TabPanel value={tabValue} index={6}>
            <Typography variant="h6" gutterBottom>
              Security Measures
            </Typography>
            <Typography paragraph>
              Implement these security measures for your production deployment:
            </Typography>

            <List>
              <ListItem>
                <ListItemIcon><SecurityIcon color="primary" /></ListItemIcon>
                <ListItemText 
                  primary="Enable HTTPS" 
                  secondary="Use SSL certificates for secure connections" 
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><SecurityIcon color="primary" /></ListItemIcon>
                <ListItemText 
                  primary="Set Security Headers" 
                  secondary="Configure Content-Security-Policy, X-XSS-Protection, etc." 
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><SecurityIcon color="primary" /></ListItemIcon>
                <ListItemText 
                  primary="Implement Rate Limiting" 
                  secondary="Protect against brute force attacks" 
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><SecurityIcon color="primary" /></ListItemIcon>
                <ListItemText 
                  primary="Use CSRF Protection" 
                  secondary="Implement Cross-Site Request Forgery protection" 
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><SecurityIcon color="primary" /></ListItemIcon>
                <ListItemText 
                  primary="Regular Security Updates" 
                  secondary="Keep all dependencies updated" 
                />
              </ListItem>
            </List>

            <Alert severity="warning" sx={{ mt: 3 }}>
              <AlertTitle>Security Audit</AlertTitle>
              Perform a security audit before going live to identify and fix potential vulnerabilities.
            </Alert>
          </TabPanel>
        </Paper>

        <Box sx={{ mt: 4 }}>
          <Typography variant="h5" gutterBottom>
            Deployment Checklist
          </Typography>
          <Divider sx={{ mb: 2 }} />
          
          <List>
            <ListItem>
              <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
              <ListItemText primary="Switch from mock data to real API" />
            </ListItem>
            <ListItem>
              <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
              <ListItemText primary="Configure environment variables" />
            </ListItem>
            <ListItem>
              <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
              <ListItemText primary="Build and test the application locally" />
            </ListItem>
            <ListItem>
              <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
              <ListItemText primary="Set up hosting environment" />
            </ListItem>
            <ListItem>
              <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
              <ListItemText primary="Configure database connections" />
            </ListItem>
            <ListItem>
              <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
              <ListItemText primary="Implement security measures" />
            </ListItem>
            <ListItem>
              <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
              <ListItemText primary="Set up monitoring and logging" />
            </ListItem>
            <ListItem>
              <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
              <ListItemText primary="Configure backups" />
            </ListItem>
            <ListItem>
              <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
              <ListItemText primary="Deploy to production" />
            </ListItem>
            <ListItem>
              <ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon>
              <ListItemText primary="Verify deployment" />
            </ListItem>
          </List>

          <Button 
            variant="contained" 
            color="primary" 
            sx={{ mt: 3 }}
            href="/admin/production-checklist"
          >
            Go to Production Checklist
          </Button>
        </Box>
      </Box>
    </EnhancedAdminLayout>
  );
}
