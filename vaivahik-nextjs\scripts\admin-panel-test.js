/**
 * Comprehensive Admin Panel Test Suite
 * Tests all 35+ admin functions for functionality and integration
 */

const fs = require('fs');
const path = require('path');

// Admin panel routes and their expected functionality
const ADMIN_ROUTES = {
  // Core Admin Functions
  '/admin/dashboard': {
    name: 'Admin Dashboard',
    critical: true,
    features: ['User stats', 'Revenue metrics', 'System health', 'Recent activity']
  },
  '/admin/users': {
    name: 'User Management',
    critical: true,
    features: ['User list', 'User search', 'User actions', 'Profile management']
  },
  '/admin/verification-queue': {
    name: 'Verification Queue',
    critical: true,
    features: ['Pending verifications', 'Approve/reject', 'Document review']
  },
  
  // Algorithm & AI Management
  '/admin/algorithm-settings': {
    name: 'Algorithm Settings',
    critical: true,
    features: ['Phase management', 'ML parameters', 'Matching weights']
  },
  '/admin/ai-phase-management': {
    name: 'AI Phase Management',
    critical: true,
    features: ['Phase transitions', 'Data readiness', 'Performance metrics']
  },
  '/admin/mcp-server': {
    name: 'MCP Server Management',
    critical: true,
    features: ['Server status', 'AI algorithms', 'Resource monitoring']
  },
  '/admin/mcp-logs': {
    name: 'MCP Logs',
    critical: false,
    features: ['AI decisions', 'Algorithm performance', 'Error tracking']
  },

  // Content Management
  '/admin/photo-moderation': {
    name: 'Photo Moderation',
    critical: true,
    features: ['Photo review', 'Auto-moderation', 'Flagged content']
  },
  '/admin/text-moderation': {
    name: 'Text Moderation',
    critical: true,
    features: ['Message filtering', 'Banned words', 'Content flags']
  },
  '/admin/reported-profiles': {
    name: 'Reported Profiles',
    critical: true,
    features: ['Report review', 'User actions', 'Safety measures']
  },

  // Business Management
  '/admin/premium-plans': {
    name: 'Premium Plans',
    critical: true,
    features: ['Plan management', 'Pricing', 'Feature access']
  },
  '/admin/subscriptions': {
    name: 'Subscriptions',
    critical: true,
    features: ['Active subscriptions', 'Billing', 'Plan changes']
  },
  '/admin/transactions': {
    name: 'Transactions',
    critical: true,
    features: ['Payment history', 'Revenue tracking', 'Refunds']
  },
  '/admin/revenue-reports': {
    name: 'Revenue Reports',
    critical: false,
    features: ['Financial analytics', 'Growth metrics', 'Forecasting']
  },

  // Communication
  '/admin/notifications': {
    name: 'Notifications',
    critical: true,
    features: ['Push notifications', 'Email campaigns', 'SMS alerts']
  },
  '/admin/email-templates': {
    name: 'Email Templates',
    critical: false,
    features: ['Template management', 'Email design', 'Personalization']
  },
  '/admin/sms-configuration': {
    name: 'SMS Configuration',
    critical: true,
    features: ['SMS settings', 'Provider config', 'Message templates']
  },
  '/admin/chat-management': {
    name: 'Chat Management',
    critical: true,
    features: ['Chat monitoring', 'Message moderation', 'User safety']
  },

  // Analytics & Monitoring
  '/admin/system-monitoring': {
    name: 'System Monitoring',
    critical: true,
    features: ['Server health', 'Performance metrics', 'Alerts']
  },
  '/admin/error-monitoring': {
    name: 'Error Monitoring',
    critical: true,
    features: ['Error tracking', 'Bug reports', 'System issues']
  },
  '/admin/logs-monitoring': {
    name: 'Logs Monitoring',
    critical: false,
    features: ['System logs', 'User activity', 'Security events']
  },
  '/admin/advanced-analytics': {
    name: 'Advanced Analytics',
    critical: false,
    features: ['User behavior', 'Matching success', 'Platform insights']
  },
  '/admin/success-analytics': {
    name: 'Success Analytics',
    critical: false,
    features: ['Match success rates', 'User satisfaction', 'Platform effectiveness']
  },

  // Security & Privacy
  '/admin/security-settings': {
    name: 'Security Settings',
    critical: true,
    features: ['Security policies', 'Access controls', 'Threat protection']
  },
  '/admin/privacy-controls': {
    name: 'Privacy Controls',
    critical: true,
    features: ['Privacy settings', 'Data protection', 'User consent']
  },
  '/admin/contact-security': {
    name: 'Contact Security',
    critical: true,
    features: ['Contact reveal controls', 'Security measures', 'Fraud prevention']
  },

  // Feature Management
  '/admin/feature-flags': {
    name: 'Feature Flags',
    critical: false,
    features: ['Feature toggles', 'A/B testing', 'Rollout control']
  },
  '/admin/feature-management': {
    name: 'Feature Management',
    critical: false,
    features: ['Feature configuration', 'User access', 'Beta features']
  },
  '/admin/spotlight-features': {
    name: 'Spotlight Features',
    critical: false,
    features: ['Spotlight management', 'Featured profiles', 'Promotion tools']
  },

  // Content & Templates
  '/admin/biodata-templates': {
    name: 'Biodata Templates',
    critical: false,
    features: ['Template design', 'Customization', 'Preview system']
  },
  '/admin/success-stories': {
    name: 'Success Stories',
    critical: false,
    features: ['Story management', 'User testimonials', 'Content curation']
  },
  '/admin/blog-posts': {
    name: 'Blog Posts',
    critical: false,
    features: ['Content management', 'Blog publishing', 'SEO optimization']
  },

  // Marketing & Growth
  '/admin/promotions': {
    name: 'Promotions',
    critical: false,
    features: ['Campaign management', 'Discount codes', 'Marketing tools']
  },
  '/admin/referral-programs': {
    name: 'Referral Programs',
    critical: false,
    features: ['Referral tracking', 'Reward management', 'Growth incentives']
  },

  // System Management
  '/admin/settings': {
    name: 'System Settings',
    critical: true,
    features: ['Global settings', 'Configuration', 'System parameters']
  },
  '/admin/backup-recovery': {
    name: 'Backup & Recovery',
    critical: true,
    features: ['Data backup', 'System recovery', 'Disaster planning']
  },
  '/admin/api-management': {
    name: 'API Management',
    critical: false,
    features: ['API monitoring', 'Rate limiting', 'Access control']
  },

  // Development Tools
  '/admin/testing-tools': {
    name: 'Testing Tools',
    critical: false,
    features: ['System testing', 'Load testing', 'Quality assurance']
  },
  '/admin/documentation': {
    name: 'Documentation',
    critical: false,
    features: ['System docs', 'API documentation', 'User guides']
  },
  '/admin/production-checklist': {
    name: 'Production Checklist',
    critical: true,
    features: ['Launch readiness', 'System validation', 'Quality checks']
  }
};

// Test results storage
const testResults = {
  passed: [],
  failed: [],
  warnings: [],
  critical_issues: [],
  total_routes: Object.keys(ADMIN_ROUTES).length
};

/**
 * Check if admin route file exists and is properly structured
 */
function testAdminRoute(route, config) {
  const routePath = route.replace('/admin/', '');
  const filePath = path.join(__dirname, '../src/pages/admin', `${routePath}.js`);
  const indexFilePath = path.join(__dirname, '../src/pages/admin', routePath, 'index.js');
  
  let fileExists = false;
  let actualPath = '';
  
  // Check for direct file
  if (fs.existsSync(filePath)) {
    fileExists = true;
    actualPath = filePath;
  }
  // Check for index file in directory
  else if (fs.existsSync(indexFilePath)) {
    fileExists = true;
    actualPath = indexFilePath;
  }
  
  const result = {
    route: route,
    name: config.name,
    critical: config.critical,
    fileExists: fileExists,
    filePath: actualPath,
    features: config.features,
    issues: []
  };
  
  if (!fileExists) {
    result.issues.push('File does not exist');
    if (config.critical) {
      testResults.critical_issues.push(`Critical route missing: ${route}`);
    }
    testResults.failed.push(result);
    return result;
  }
  
  // Read and analyze file content
  try {
    const fileContent = fs.readFileSync(actualPath, 'utf8');
    
    // Basic structure checks
    if (!fileContent.includes('export default')) {
      result.issues.push('Missing default export');
    }
    
    if (!fileContent.includes('useState') && !fileContent.includes('useEffect')) {
      result.issues.push('No React hooks detected - may be static');
    }
    
    if (!fileContent.includes('Container') && !fileContent.includes('Box')) {
      result.issues.push('No Material-UI layout components detected');
    }
    
    // Check for API calls
    if (!fileContent.includes('fetch') && !fileContent.includes('axios')) {
      result.issues.push('No API calls detected - may not be connected to backend');
    }
    
    // Check for error handling
    if (!fileContent.includes('try') && !fileContent.includes('catch')) {
      result.issues.push('No error handling detected');
    }
    
    // Feature-specific checks
    config.features.forEach(feature => {
      const featureKeywords = {
        'User stats': ['user', 'count', 'total'],
        'User list': ['map', 'filter', 'users'],
        'Photo review': ['photo', 'image', 'moderation'],
        'Payment history': ['payment', 'transaction', 'billing'],
        'System health': ['health', 'status', 'monitoring']
      };
      
      const keywords = featureKeywords[feature];
      if (keywords && !keywords.some(keyword => 
        fileContent.toLowerCase().includes(keyword.toLowerCase())
      )) {
        result.issues.push(`Feature "${feature}" implementation not detected`);
      }
    });
    
    if (result.issues.length === 0) {
      testResults.passed.push(result);
    } else if (result.issues.length <= 2 && !config.critical) {
      testResults.warnings.push(result);
    } else {
      testResults.failed.push(result);
      if (config.critical) {
        testResults.critical_issues.push(`Critical route has issues: ${route}`);
      }
    }
    
  } catch (error) {
    result.issues.push(`Error reading file: ${error.message}`);
    testResults.failed.push(result);
  }
  
  return result;
}

/**
 * Run comprehensive admin panel tests
 */
function runAdminPanelTests() {
  console.log('🔍 Starting Comprehensive Admin Panel Audit...\n');
  
  const results = [];
  
  // Test each admin route
  Object.entries(ADMIN_ROUTES).forEach(([route, config]) => {
    const result = testAdminRoute(route, config);
    results.push(result);
    
    // Log progress
    const status = result.issues.length === 0 ? '✅' : 
                  result.issues.length <= 2 && !config.critical ? '⚠️' : '❌';
    console.log(`${status} ${config.name} (${route})`);
    
    if (result.issues.length > 0) {
      result.issues.forEach(issue => {
        console.log(`   - ${issue}`);
      });
    }
  });
  
  // Generate summary report
  console.log('\n📊 ADMIN PANEL AUDIT SUMMARY');
  console.log('=' .repeat(50));
  console.log(`Total Routes Tested: ${testResults.total_routes}`);
  console.log(`✅ Passed: ${testResults.passed.length}`);
  console.log(`⚠️  Warnings: ${testResults.warnings.length}`);
  console.log(`❌ Failed: ${testResults.failed.length}`);
  console.log(`🚨 Critical Issues: ${testResults.critical_issues.length}`);
  
  // Critical issues report
  if (testResults.critical_issues.length > 0) {
    console.log('\n🚨 CRITICAL ISSUES REQUIRING IMMEDIATE ATTENTION:');
    testResults.critical_issues.forEach(issue => {
      console.log(`   - ${issue}`);
    });
  }
  
  // Success rate calculation
  const successRate = ((testResults.passed.length + testResults.warnings.length) / testResults.total_routes * 100).toFixed(1);
  console.log(`\n📈 Overall Success Rate: ${successRate}%`);
  
  // Production readiness assessment
  if (testResults.critical_issues.length === 0 && successRate >= 90) {
    console.log('\n🎉 ADMIN PANEL IS PRODUCTION READY!');
  } else if (testResults.critical_issues.length === 0 && successRate >= 80) {
    console.log('\n✅ Admin panel is mostly ready with minor improvements needed');
  } else {
    console.log('\n⚠️  Admin panel needs attention before production deployment');
  }
  
  return {
    results: results,
    summary: testResults,
    successRate: successRate,
    productionReady: testResults.critical_issues.length === 0 && successRate >= 90
  };
}

// Run the tests if this script is executed directly
if (require.main === module) {
  const auditResults = runAdminPanelTests();
  
  // Save detailed results to file
  const reportPath = path.join(__dirname, '../admin-panel-audit-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(auditResults, null, 2));
  console.log(`\n📄 Detailed report saved to: ${reportPath}`);
}

module.exports = { runAdminPanelTests, ADMIN_ROUTES };
