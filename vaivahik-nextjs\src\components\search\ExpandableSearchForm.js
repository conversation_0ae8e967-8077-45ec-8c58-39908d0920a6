import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Tabs,
  Tab,
  TextField,
  Button,
  Grid,
  FormControl,
  FormLabel,
  Divider,
  Chip,
  Select,
  MenuItem,
  InputAdornment,
  IconButton,
  Collapse,
  FormControlLabel,
  Switch,
  Tooltip,
  Card,
  CardContent,
  Badge
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import PersonIcon from '@mui/icons-material/Person';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import TuneIcon from '@mui/icons-material/Tune';
import HeightRangeSelector from './HeightRangeSelector';
import { formatHeight } from '@/utils/heightUtils';

/**
 * Expandable Search Form Component
 *
 * Features:
 * - Basic search options shown by default
 * - Expandable sections for additional filters
 * - User ID search in a separate tab
 * - Automatic opposite gender matching
 * - Save search functionality
 */
const ExpandableSearchForm = ({
  onSearch,
  savedSearches = [],
  onSaveSearch,
  userGender = 'MALE' // The current user's gender
}) => {
  // Search mode tabs
  const [searchMode, setSearchMode] = useState(0); // 0: Regular Search, 1: ID Search

  // Expanded sections tracking
  const [expandedSections, setExpandedSections] = useState({
    religious: false,
    education: false,
    lifestyle: false,
    appearance: false
  });

  // Count of active advanced filters
  const [advancedFilterCount, setAdvancedFilterCount] = useState(0);

  // Search parameters
  const [searchParams, setSearchParams] = useState({
    // Basic search params (always visible)
    ageFrom: userGender === 'MALE' ? 18 : 21,
    ageTo: userGender === 'MALE' ? 35 : 40,
    heightFrom: 53, // 4'5"
    heightTo: 77, // 6'5"
    location: '',

    // Religious & Community (expandable)
    religion: 'HINDU',
    caste: 'MARATHA',
    subCaste: '',
    gotra: '',
    manglik: 'DOESNT_MATTER',

    // Education & Career (expandable)
    education: [],
    occupation: [],
    incomeRange: '',

    // Lifestyle (expandable)
    diet: '',
    smoking: 'DOESNT_MATTER',
    drinking: 'DOESNT_MATTER',

    // Appearance & Status (expandable)
    maritalStatus: [],
    withPhoto: true,
    profileCreatedWithin: '',
    profileType: [], // VERIFIED, PREMIUM, etc.

    // ID search params
    userId: ''
  });

  // Calculate active filter count on parameter changes
  useEffect(() => {
    let count = 0;

    // Religious & Community
    if (searchParams.subCaste) count++;
    if (searchParams.gotra) count++;
    if (searchParams.manglik !== 'DOESNT_MATTER') count++;

    // Education & Career
    if (searchParams.education.length > 0) count++;
    if (searchParams.occupation.length > 0) count++;
    if (searchParams.incomeRange) count++;

    // Lifestyle
    if (searchParams.diet) count++;
    if (searchParams.smoking !== 'DOESNT_MATTER') count++;
    if (searchParams.drinking !== 'DOESNT_MATTER') count++;

    // Appearance & Status
    if (searchParams.maritalStatus.length > 0) count++;
    if (searchParams.profileCreatedWithin) count++;
    if (searchParams.profileType.length > 0) count++;

    setAdvancedFilterCount(count);
  }, [searchParams]);

  // Handle search mode change
  const handleSearchModeChange = (_, newValue) => {
    setSearchMode(newValue);
  };

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchParams({ ...searchParams, [name]: value });
  };

  // Handle checkbox/switch change
  const handleSwitchChange = (e) => {
    const { name, checked } = e.target;
    setSearchParams({ ...searchParams, [name]: checked });
  };

  // Handle multi-select change
  const handleMultiSelectChange = (e) => {
    const { name, value } = e.target;
    setSearchParams({ ...searchParams, [name]: value });
  };

  // Handle height range change
  const handleHeightChange = (min, max) => {
    setSearchParams({
      ...searchParams,
      heightFrom: min,
      heightTo: max
    });
  };

  // Toggle expanded section
  const toggleSection = (section) => {
    setExpandedSections({
      ...expandedSections,
      [section]: !expandedSections[section]
    });
  };

  // Handle search submission
  const handleSearch = () => {
    // Prepare search data based on active tab
    let searchData = {};

    if (searchMode === 0) { // Regular Search
      searchData = {
        searchType: 'REGULAR',
        targetGender: userGender === 'MALE' ? 'FEMALE' : 'MALE',
        ...searchParams
      };
    } else { // ID Search
      searchData = {
        searchType: 'ID',
        userId: searchParams.userId
      };
    }

    // Call the search handler
    onSearch(searchData);
  };

  // Save current search
  const handleSaveSearch = () => {
    if (onSaveSearch) {
      onSaveSearch({
        ...searchParams,
        searchMode
      });
    }
  };

  // Generate age options
  const generateAgeOptions = (min, max) => {
    const options = [];
    for (let i = min; i <= max; i++) {
      options.push(i);
    }
    return options;
  };

  // Age options based on target gender (opposite of user)
  const targetGender = userGender === 'MALE' ? 'FEMALE' : 'MALE';
  const minAge = targetGender === 'FEMALE' ? 18 : 21;
  const ageFromOptions = generateAgeOptions(minAge, 60);
  const ageToOptions = generateAgeOptions(
    Math.max(minAge, searchParams.ageFrom),
    70
  );

  return (
    <Paper elevation={3} sx={{ p: { xs: 2, md: 3 }, borderRadius: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5" component="h2">
          Find Your Perfect {targetGender === 'FEMALE' ? 'Bride' : 'Groom'}
        </Typography>

        {/* Save search button */}
        <Tooltip title="Save this search">
          <IconButton
            onClick={handleSaveSearch}
            size="small"
          >
            <BookmarkBorderIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Search mode tabs */}
      <Tabs
        value={searchMode}
        onChange={handleSearchModeChange}
        sx={{ mb: 3 }}
        variant="fullWidth"
      >
        <Tab label="Regular Search" icon={<SearchIcon />} iconPosition="start" />
        <Tab label="Search by ID" icon={<PersonIcon />} iconPosition="start" />
      </Tabs>

      {/* Regular Search Form */}
      {searchMode === 0 && (
        <>
          {/* Basic Search Options - Always Visible */}
          <Card variant="outlined" sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Basic Search
              </Typography>

              <Grid container spacing={3}>
                {/* Age Range */}
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <FormLabel>Age</FormLabel>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Select
                        name="ageFrom"
                        value={searchParams.ageFrom}
                        onChange={handleInputChange}
                        size="small"
                        sx={{ width: '45%' }}
                      >
                        {ageFromOptions.map(age => (
                          <MenuItem key={`from-${age}`} value={age}>{age}</MenuItem>
                        ))}
                      </Select>
                      <Typography sx={{ mx: 1 }}>to</Typography>
                      <Select
                        name="ageTo"
                        value={searchParams.ageTo}
                        onChange={handleInputChange}
                        size="small"
                        sx={{ width: '45%' }}
                      >
                        {ageToOptions.map(age => (
                          <MenuItem key={`to-${age}`} value={age}>{age}</MenuItem>
                        ))}
                      </Select>
                    </Box>
                  </FormControl>
                </Grid>

                {/* Location */}
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <FormLabel>Location</FormLabel>
                    <TextField
                      name="location"
                      value={searchParams.location}
                      onChange={handleInputChange}
                      placeholder="City, State or Country"
                      size="small"
                    />
                  </FormControl>
                </Grid>

                {/* Height Range */}
                <Grid item xs={12}>
                  <HeightRangeSelector
                    minHeight={searchParams.heightFrom}
                    maxHeight={searchParams.heightTo}
                    onChange={handleHeightChange}
                    gender={targetGender}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Religious & Community Section */}
          <Card variant="outlined" sx={{ mb: 2 }}>
            <CardContent sx={{ pb: expandedSections.religious ? 2 : 1 }}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  cursor: 'pointer'
                }}
                onClick={() => toggleSection('religious')}
              >
                <Typography variant="subtitle1">
                  Religion & Community
                </Typography>
                <IconButton size="small">
                  {expandedSections.religious ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              </Box>

              {/* Always visible basic options */}
              <Grid container spacing={2} sx={{ mt: 0.5 }}>
                <Grid item xs={6}>
                  <FormControl fullWidth size="small">
                    <Select
                      name="religion"
                      value={searchParams.religion}
                      onChange={handleInputChange}
                      displayEmpty
                    >
                      <MenuItem value="HINDU">Hindu</MenuItem>
                      <MenuItem value="MUSLIM">Muslim</MenuItem>
                      <MenuItem value="CHRISTIAN">Christian</MenuItem>
                      <MenuItem value="SIKH">Sikh</MenuItem>
                      <MenuItem value="JAIN">Jain</MenuItem>
                      <MenuItem value="BUDDHIST">Buddhist</MenuItem>
                      <MenuItem value="OTHER">Other</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={6}>
                  <FormControl fullWidth size="small">
                    <TextField
                      name="caste"
                      value={searchParams.caste}
                      onChange={handleInputChange}
                      placeholder="Caste (e.g., Maratha)"
                      size="small"
                    />
                  </FormControl>
                </Grid>
              </Grid>

              {/* Expandable additional options */}
              <Collapse in={expandedSections.religious}>
                <Grid container spacing={2} sx={{ mt: 1 }}>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth size="small">
                      <FormLabel>Sub-caste</FormLabel>
                      <TextField
                        name="subCaste"
                        value={searchParams.subCaste}
                        onChange={handleInputChange}
                        placeholder="Enter sub-caste"
                        size="small"
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth size="small">
                      <FormLabel>Gotra</FormLabel>
                      <TextField
                        name="gotra"
                        value={searchParams.gotra}
                        onChange={handleInputChange}
                        placeholder="Enter gotra"
                        size="small"
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xs={12}>
                    <FormControl fullWidth size="small">
                      <FormLabel>Manglik</FormLabel>
                      <Select
                        name="manglik"
                        value={searchParams.manglik}
                        onChange={handleInputChange}
                      >
                        <MenuItem value="DOESNT_MATTER">Doesn't Matter</MenuItem>
                        <MenuItem value="YES">Yes</MenuItem>
                        <MenuItem value="NO">No</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </Collapse>
            </CardContent>
          </Card>

          {/* Education & Career Section */}
          <Card variant="outlined" sx={{ mb: 2 }}>
            <CardContent sx={{ pb: expandedSections.education ? 2 : 1 }}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  cursor: 'pointer'
                }}
                onClick={() => toggleSection('education')}
              >
                <Typography variant="subtitle1">
                  Education & Career
                </Typography>
                <IconButton size="small">
                  {expandedSections.education ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              </Box>

              {/* Expandable education options */}
              <Collapse in={expandedSections.education}>
                <Grid container spacing={2} sx={{ mt: 1 }}>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth size="small">
                      <FormLabel>Education</FormLabel>
                      <Select
                        name="education"
                        value={searchParams.education}
                        onChange={handleMultiSelectChange}
                        multiple
                        renderValue={(selected) => (
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {selected.map((value) => (
                              <Chip key={value} label={value.replace('_', ' ')} size="small" />
                            ))}
                          </Box>
                        )}
                      >
                        <MenuItem value="HIGH_SCHOOL">High School</MenuItem>
                        <MenuItem value="BACHELORS">Bachelor's Degree</MenuItem>
                        <MenuItem value="MASTERS">Master's Degree</MenuItem>
                        <MenuItem value="DOCTORATE">Doctorate</MenuItem>
                        <MenuItem value="DIPLOMA">Diploma</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth size="small">
                      <FormLabel>Occupation</FormLabel>
                      <Select
                        name="occupation"
                        value={searchParams.occupation}
                        onChange={handleMultiSelectChange}
                        multiple
                        renderValue={(selected) => (
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {selected.map((value) => (
                              <Chip key={value} label={value.replace('_', ' ')} size="small" />
                            ))}
                          </Box>
                        )}
                      >
                        <MenuItem value="IT_SOFTWARE">IT/Software</MenuItem>
                        <MenuItem value="ENGINEERING">Engineering</MenuItem>
                        <MenuItem value="MEDICAL">Medical</MenuItem>
                        <MenuItem value="FINANCE">Finance</MenuItem>
                        <MenuItem value="EDUCATION">Education</MenuItem>
                        <MenuItem value="BUSINESS">Business</MenuItem>
                        <MenuItem value="GOVERNMENT">Government</MenuItem>
                        <MenuItem value="OTHER">Other</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12}>
                    <FormControl fullWidth size="small">
                      <FormLabel>Annual Income</FormLabel>
                      <Select
                        name="incomeRange"
                        value={searchParams.incomeRange}
                        onChange={handleInputChange}
                      >
                        <MenuItem value="">Any</MenuItem>
                        <MenuItem value="UPTO_3L">Upto 3 Lakhs</MenuItem>
                        <MenuItem value="3L_5L">3 - 5 Lakhs</MenuItem>
                        <MenuItem value="5L_10L">5 - 10 Lakhs</MenuItem>
                        <MenuItem value="10L_20L">10 - 20 Lakhs</MenuItem>
                        <MenuItem value="ABOVE_20L">Above 20 Lakhs</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </Collapse>
            </CardContent>
          </Card>

          {/* Lifestyle Section */}
          <Card variant="outlined" sx={{ mb: 2 }}>
            <CardContent sx={{ pb: expandedSections.lifestyle ? 2 : 1 }}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  cursor: 'pointer'
                }}
                onClick={() => toggleSection('lifestyle')}
              >
                <Typography variant="subtitle1">
                  Lifestyle
                </Typography>
                <IconButton size="small">
                  {expandedSections.lifestyle ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              </Box>

              {/* Expandable lifestyle options */}
              <Collapse in={expandedSections.lifestyle}>
                <Grid container spacing={2} sx={{ mt: 1 }}>
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth size="small">
                      <FormLabel>Diet</FormLabel>
                      <Select
                        name="diet"
                        value={searchParams.diet}
                        onChange={handleInputChange}
                      >
                        <MenuItem value="">Any</MenuItem>
                        <MenuItem value="VEG">Vegetarian</MenuItem>
                        <MenuItem value="NON_VEG">Non-Vegetarian</MenuItem>
                        <MenuItem value="JAIN">Jain</MenuItem>
                        <MenuItem value="VEGAN">Vegan</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth size="small">
                      <FormLabel>Smoking</FormLabel>
                      <Select
                        name="smoking"
                        value={searchParams.smoking}
                        onChange={handleInputChange}
                      >
                        <MenuItem value="DOESNT_MATTER">Doesn't Matter</MenuItem>
                        <MenuItem value="NO">No</MenuItem>
                        <MenuItem value="YES">Yes</MenuItem>
                        <MenuItem value="OCCASIONALLY">Occasionally</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth size="small">
                      <FormLabel>Drinking</FormLabel>
                      <Select
                        name="drinking"
                        value={searchParams.drinking}
                        onChange={handleInputChange}
                      >
                        <MenuItem value="DOESNT_MATTER">Doesn't Matter</MenuItem>
                        <MenuItem value="NO">No</MenuItem>
                        <MenuItem value="YES">Yes</MenuItem>
                        <MenuItem value="OCCASIONALLY">Occasionally</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </Collapse>
            </CardContent>
          </Card>

          {/* Appearance & Status Section */}
          <Card variant="outlined" sx={{ mb: 2 }}>
            <CardContent sx={{ pb: expandedSections.appearance ? 2 : 1 }}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  cursor: 'pointer'
                }}
                onClick={() => toggleSection('appearance')}
              >
                <Typography variant="subtitle1">
                  Profile Status
                </Typography>
                <IconButton size="small">
                  {expandedSections.appearance ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              </Box>

              {/* Expandable appearance options */}
              <Collapse in={expandedSections.appearance}>
                <Grid container spacing={2} sx={{ mt: 1 }}>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth size="small">
                      <FormLabel>Marital Status</FormLabel>
                      <Select
                        name="maritalStatus"
                        value={searchParams.maritalStatus}
                        onChange={handleMultiSelectChange}
                        multiple
                        renderValue={(selected) => (
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {selected.map((value) => (
                              <Chip key={value} label={value.replace('_', ' ')} size="small" />
                            ))}
                          </Box>
                        )}
                      >
                        <MenuItem value="NEVER_MARRIED">Never Married</MenuItem>
                        <MenuItem value="DIVORCED">Divorced</MenuItem>
                        <MenuItem value="WIDOWED">Widowed</MenuItem>
                        <MenuItem value="AWAITING_DIVORCE">Awaiting Divorce</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth size="small">
                      <FormLabel>Profile Created</FormLabel>
                      <Select
                        name="profileCreatedWithin"
                        value={searchParams.profileCreatedWithin}
                        onChange={handleInputChange}
                      >
                        <MenuItem value="">Any Time</MenuItem>
                        <MenuItem value="1_WEEK">Last Week</MenuItem>
                        <MenuItem value="1_MONTH">Last Month</MenuItem>
                        <MenuItem value="3_MONTHS">Last 3 Months</MenuItem>
                        <MenuItem value="6_MONTHS">Last 6 Months</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth size="small">
                      <FormLabel>Profile Type</FormLabel>
                      <Select
                        name="profileType"
                        value={searchParams.profileType}
                        onChange={handleMultiSelectChange}
                        multiple
                        renderValue={(selected) => (
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {selected.map((value) => (
                              <Chip key={value} label={value} size="small" />
                            ))}
                          </Box>
                        )}
                      >
                        <MenuItem value="VERIFIED">Verified</MenuItem>
                        <MenuItem value="PREMIUM">Premium</MenuItem>
                        <MenuItem value="FEATURED">Featured</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth sx={{ mt: 2 }}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={searchParams.withPhoto}
                            onChange={handleSwitchChange}
                            name="withPhoto"
                          />
                        }
                        label="Profiles with Photo"
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              </Collapse>
            </CardContent>
          </Card>

          {/* Advanced Filters Button */}
          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
            <Badge badgeContent={advancedFilterCount} color="primary" sx={{ mr: 1 }}>
              <TuneIcon />
            </Badge>
            <Typography color="text.secondary">
              {advancedFilterCount} advanced filters applied
            </Typography>
          </Box>

          {/* Search Button */}
          <Button
            variant="contained"
            color="primary"
            size="large"
            fullWidth
            onClick={handleSearch}
            startIcon={<SearchIcon />}
          >
            Find Matches
          </Button>
        </>
      )}

      {/* ID Search Form */}
      {searchMode === 1 && (
        <Box>
          <Typography variant="body2" color="text.secondary" paragraph>
            Enter the User ID to find a specific profile. User IDs are typically displayed on profiles as "VAI12345".
          </Typography>

          <TextField
            fullWidth
            label="User ID"
            name="userId"
            value={searchParams.userId}
            onChange={handleInputChange}
            placeholder="e.g., VAI12345"
            sx={{ mb: 3 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <PersonIcon />
                </InputAdornment>
              ),
            }}
          />

          <Button
            variant="contained"
            color="primary"
            size="large"
            fullWidth
            onClick={handleSearch}
            startIcon={<SearchIcon />}
            disabled={!searchParams.userId}
          >
            Find Profile
          </Button>
        </Box>
      )}
    </Paper>
  );
};

export default ExpandableSearchForm;
