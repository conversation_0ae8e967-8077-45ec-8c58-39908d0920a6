import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Paper,
  CircularProgress,
  Alert,
  Snackbar
} from '@mui/material';
import PartnerPreferencesForm from '@/website/components/profile/PartnerPreferencesForm';
import { isUsingRealBackend } from '@/utils/featureFlags';

// Mock user data for development
const MOCK_USER_DATA = {
  id: 'user123',
  fullName: '<PERSON><PERSON>',
  preferences: {
    ageMin: 25,
    ageMax: 32,
    heightMin: '5.0',
    heightMax: '5.8',
    educationLevel: ['Bachelor\'s Degree', 'Master\'s Degree'],
    occupations: ['Software Engineer', 'Teacher', 'Doctor'],
    incomeMin: '5L_10L',
    preferredCities: ['Mumbai', 'Pune'],
    preferredStates: ['Maharashtra'],
    acceptSubCastes: ['96 Kuli <PERSON>tha', '<PERSON>hmukh'],
    gotraPreference: '',
    dietPreference: 'VEGETARIAN',
    hobbiesPreference: 'Reading, traveling, music',
    interestsPreference: '',
    otherPreferences: 'Looking for someone who values family traditions and has a good sense of humor.'
  }
};

export default function EditPartnerPreferences() {
  const router = useRouter();
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [saving, setSaving] = useState(false);

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      setLoading(true);
      setError('');

      try {
        if (isUsingRealBackend()) {
          // Call real API
          const response = await fetch('/api/user/profile', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          });

          if (!response.ok) {
            throw new Error('Failed to fetch user data');
          }

          const data = await response.json();
          setUserData(data);
        } else {
          // Use mock data
          setTimeout(() => {
            setUserData(MOCK_USER_DATA);
            setLoading(false);
          }, 500); // Simulate API delay
        }
      } catch (err) {
        console.error('Error fetching user data:', err);
        setError('Failed to load your profile data. Please try again later.');
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  // Handle save
  const handleSave = async (formData) => {
    setSaving(true);
    setError('');

    try {
      if (isUsingRealBackend()) {
        // Call real API
        const response = await fetch('/api/user/partner-preferences', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify(formData)
        });

        if (!response.ok) {
          throw new Error('Failed to update partner preferences');
        }

        const data = await response.json();
        setSuccess('Partner preferences updated successfully!');
        
        // Update local user data
        setUserData(prevData => ({
          ...prevData,
          preferences: formData
        }));
      } else {
        // Mock API call
        setTimeout(() => {
          // Update local user data
          setUserData(prevData => ({
            ...prevData,
            preferences: formData
          }));
          setSuccess('Partner preferences updated successfully! (Mock)');
          setSaving(false);
        }, 1000); // Simulate API delay
      }
    } catch (err) {
      console.error('Error saving partner preferences:', err);
      setError('Failed to save your partner preferences. Please try again.');
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
        <CircularProgress />
        <Typography variant="body1" sx={{ mt: 2 }}>
          Loading your preferences...
        </Typography>
      </Container>
    );
  }

  return (
    <>
      <Head>
        <title>Edit Partner Preferences | Vaivahik</title>
        <meta name="description" content="Edit your partner preferences on Vaivahik matrimony" />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <PartnerPreferencesForm 
          userData={userData} 
          onSave={handleSave} 
          isLoading={saving} 
        />
      </Container>

      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess('')}
        message={success}
      />
    </>
  );
}
