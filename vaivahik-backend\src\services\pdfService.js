const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * Service for generating PDF biodatas from HTML templates
 */
class PDFService {
  /**
   * Generate a PDF biodata for a user
   * @param {string} userId - The user ID
   * @param {string} templateId - The biodata template ID
   * @param {boolean} watermark - Whether to add a watermark (for previews)
   * @returns {Promise<{filePath: string, fileName: string}>} - The generated PDF file info
   */
  async generateBiodata(userId, templateId, watermark = false) {
    try {
      // Get user data
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          profile: true,
          photos: {
            where: {
              status: 'APPROVED',
              OR: [
                { isProfilePic: true },
                { visibility: 'PUBLIC' }
              ]
            },
            orderBy: {
              isProfilePic: 'desc'
            },
            take: 4
          }
        }
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Get template
      const template = await prisma.biodataTemplate.findUnique({
        where: { id: templateId }
      });

      if (!template) {
        throw new Error('Template not found');
      }

      // Check if user has purchased this template (unless it's a preview)
      if (!watermark) {
        const purchase = await prisma.userBiodata.findUnique({
          where: {
            userId_templateId: {
              userId: userId,
              templateId: templateId
            }
          }
        });

        if (!purchase) {
          throw new Error('User has not purchased this template');
        }

        // Update download count
        await prisma.userBiodata.update({
          where: {
            id: purchase.id
          },
          data: {
            downloadCount: { increment: 1 },
            lastDownload: new Date()
          }
        });
      }

      // Get branding settings
      const brandingSettings = await this.getBrandingSettings();

      // Prepare data for template
      const templateData = this.prepareTemplateData(user, brandingSettings);

      // Get the template HTML
      const templatePath = path.join(process.cwd(), 'public', template.designFile);
      let templateHtml = fs.readFileSync(templatePath, 'utf8');

      // Replace placeholders with actual data
      templateHtml = this.replacePlaceholders(templateHtml, templateData);

      // Add watermark if needed
      if (watermark) {
        templateHtml = this.addWatermark(templateHtml);
      } else {
        // Add security features for purchased PDFs
        templateHtml = this.addSecurityFeatures(templateHtml, user.id);
      }

      // Generate PDF
      const pdfBuffer = await this.generatePDF(templateHtml);

      // Save PDF to disk
      const fileName = `biodata_${user.profile?.fullName || user.id}_${new Date().getTime()}.pdf`;
      const outputDir = path.join(process.cwd(), 'public', 'generated', 'biodatas');
      
      // Create directory if it doesn't exist
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }
      
      const filePath = path.join(outputDir, fileName);
      fs.writeFileSync(filePath, pdfBuffer);

      return {
        filePath: `/generated/biodatas/${fileName}`,
        fileName: fileName
      };
    } catch (error) {
      console.error('Error generating biodata PDF:', error);
      throw error;
    }
  }

  /**
   * Get branding settings from the database
   * @returns {Promise<Object>} - The branding settings
   */
  async getBrandingSettings() {
    try {
      const settings = await prisma.appSettings.findFirst({
        where: {
          key: 'branding'
        }
      });

      if (!settings) {
        return {
          brandName: 'Vaivahik',
          brandTagline: 'Find your perfect match',
          brandLogo: '/logo.png'
        };
      }

      return JSON.parse(settings.value);
    } catch (error) {
      console.error('Error getting branding settings:', error);
      return {
        brandName: 'Vaivahik',
        brandTagline: 'Find your perfect match',
        brandLogo: '/logo.png'
      };
    }
  }

  /**
   * Prepare data for the template
   * @param {Object} user - The user data
   * @param {Object} brandingSettings - The branding settings
   * @returns {Object} - The prepared data
   */
  prepareTemplateData(user, brandingSettings) {
    const profile = user.profile || {};
    const photos = user.photos || [];
    
    // Calculate age from date of birth
    let age = '';
    if (profile.dateOfBirth) {
      const birthDate = new Date(profile.dateOfBirth);
      const today = new Date();
      age = today.getFullYear() - birthDate.getFullYear();
      const m = today.getMonth() - birthDate.getMonth();
      if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
    }

    // Format date of birth
    let formattedDOB = '';
    if (profile.dateOfBirth) {
      const options = { year: 'numeric', month: 'long', day: 'numeric' };
      formattedDOB = new Date(profile.dateOfBirth).toLocaleDateString('en-IN', options);
    }

    return {
      // Basic info
      name: profile.fullName || '',
      tagline: `${profile.occupation || 'Professional'} seeking a life partner`,
      age: age,
      dateOfBirth: formattedDOB,
      birthTime: profile.birthTime || '',
      birthPlace: profile.birthPlace || '',
      
      // Physical attributes
      height: profile.height || '',
      
      // Community details
      religion: profile.religion || 'Hindu',
      caste: profile.caste || 'Maratha',
      subCaste: profile.subCaste || '',
      gotra: profile.gotra || '',
      kul: profile.kul || '',
      maritalStatus: profile.maritalStatus || 'Never Married',
      diet: profile.diet || '',
      
      // Education & Career
      education: profile.highestEducation || '',
      educationDetails: '',
      occupation: profile.occupation || '',
      company: '',
      annualIncome: profile.annualIncome || '',
      
      // Location
      city: profile.city || '',
      state: profile.state || '',
      country: profile.country || 'India',
      nativePlace: profile.nativePlace || '',
      
      // Family details
      fatherName: profile.fatherName || '',
      fatherOccupation: profile.fatherOccupation || '',
      motherName: profile.motherName || '',
      motherOccupation: profile.motherOccupation || '',
      familyType: profile.familyType || '',
      familyStatus: profile.familyStatus || '',
      siblings: profile.siblings || '',
      
      // Contact
      email: user.email || '',
      phone: user.phone || '',
      
      // About & Preferences
      aboutMe: profile.aboutMe || '',
      hobbies: profile.hobbies || '',
      partnerPreferences: profile.partnerPreferences || '',
      
      // Photos
      profilePicture: photos.length > 0 ? photos[0].url : '/placeholder-profile.jpg',
      additionalPhotos: photos.slice(1).map(photo => photo.url),
      
      // Horoscope details
      rashi: '',
      nakshatra: '',
      mangal: 'No',
      
      // Branding
      brandLogo: brandingSettings.brandLogo,
      brandName: brandingSettings.brandName,
      brandTagline: brandingSettings.brandTagline,
      createdAt: new Date().toLocaleDateString('en-IN')
    };
  }

  /**
   * Replace placeholders in the template with actual data
   * @param {string} html - The template HTML
   * @param {Object} data - The data to replace placeholders with
   * @returns {string} - The HTML with placeholders replaced
   */
  replacePlaceholders(html, data) {
    let result = html;
    
    // Replace all placeholders with data
    Object.keys(data).forEach(key => {
      const value = data[key];
      if (typeof value === 'string' || typeof value === 'number') {
        const regex = new RegExp(`{{${key}}}`, 'g');
        result = result.replace(regex, value);
      } else if (Array.isArray(value)) {
        // Handle arrays (like additionalPhotos)
        value.forEach((item, index) => {
          const regex = new RegExp(`{{${key}.${index}}}`, 'g');
          result = result.replace(regex, item);
        });
      }
    });
    
    return result;
  }

  /**
   * Add watermark to the template HTML for previews
   * @param {string} html - The template HTML
   * @returns {string} - The HTML with watermark
   */
  addWatermark(html) {
    const watermarkStyle = `
      <style>
        body {
          position: relative;
        }
        body::before {
          content: "PREVIEW ONLY";
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 100px;
          color: rgba(200, 0, 0, 0.2);
          transform: rotate(-45deg);
          pointer-events: none;
          z-index: 1000;
        }
      </style>
    `;
    
    return html.replace('</head>', `${watermarkStyle}</head>`);
  }

  /**
   * Add security features to the template HTML for purchased PDFs
   * @param {string} html - The template HTML
   * @param {string} userId - The user ID
   * @returns {string} - The HTML with security features
   */
  addSecurityFeatures(html, userId) {
    const uniqueId = uuidv4().substring(0, 8);
    const timestamp = new Date().toISOString();
    
    const securityFooter = `
      <div style="position: fixed; bottom: 5px; left: 0; right: 0; text-align: center; font-size: 8px; color: #999; padding: 5px;">
        Generated for user ID: ${userId} | Document ID: ${uniqueId} | Generated on: ${timestamp}
      </div>
    `;
    
    return html.replace('</body>', `${securityFooter}</body>`);
  }

  /**
   * Generate PDF from HTML
   * @param {string} html - The HTML to convert to PDF
   * @returns {Promise<Buffer>} - The generated PDF as a buffer
   */
  async generatePDF(html) {
    const browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    try {
      const page = await browser.newPage();
      await page.setContent(html, { waitUntil: 'networkidle0' });
      
      // Set PDF options
      const pdfOptions = {
        format: 'A4',
        printBackground: true,
        margin: {
          top: '1cm',
          right: '1cm',
          bottom: '1cm',
          left: '1cm'
        }
      };
      
      // Generate PDF
      const pdfBuffer = await page.pdf(pdfOptions);
      return pdfBuffer;
    } finally {
      await browser.close();
    }
  }
}

module.exports = new PDFService();
