/**
 * Modern Education & Career Page
 *
 * This page allows users to update their education and career details using a modern UI form.
 */

import React, { useState } from 'react';
import { Box, Container, Typography, Alert, Breadcrumbs } from '@mui/material';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import ModernEducationCareerForm from '@/components/profile/ModernEducationCareerForm';
import { useAuth } from '@/contexts/AuthContext';
import { isUsingRealBackend } from '@/utils/apiUtils';
import { updateEducationCareer } from '@/services/userApiService';
import { formatError, getUserFriendlyMessage, isNetworkError } from '@/utils/errorHandling';
import { withRetry } from '@/utils/retryLogic';

const ModernEducationCareerPage = () => {
  const router = useRouter();
  const { userData, setUserData } = useAuth();
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Handle form submission
  const handleSave = async (formData) => {
    try {
      setSaving(true);
      setError(''); // Clear previous errors

      if (isUsingRealBackend()) {
        // Define the API call function
        const saveData = async () => {
          try {
            return await updateEducationCareer(formData);
          } catch (apiError) {
            // Format the error for better user feedback
            const formattedError = formatError(apiError);
            throw formattedError; // Rethrow for retry logic to catch
          }
        };

        // Call API with retry logic for network errors
        const response = await withRetry(saveData, {
          maxRetries: 3,
          retryCondition: isNetworkError
        });

        setSuccess('Education and career details saved successfully!');

        // Update local user data
        setUserData(prev => ({
          ...prev,
          educationCareer: response.data?.educationCareer || formData
        }));
      } else {
        // Simulate API call
        setTimeout(() => {
          setSuccess('Education and career details saved successfully!');

          // Update local user data
          setUserData(prev => ({
            ...prev,
            educationCareer: formData,
            profileCompletionPercentage: Math.min(75, (prev?.profileCompletionPercentage || 50) + 10)
          }));

          setSaving(false);
        }, 1000);
        return;
      }

      setSaving(false);
    } catch (err) {
      console.error('Error saving education and career details:', err);

      // Get user-friendly error message
      const errorMessage = getUserFriendlyMessage(err);
      setError(errorMessage);

      setSaving(false);
    }
  };

  return (
    <>
      <Head>
        <title>Education & Career | Vaivahik</title>
        <meta name="description" content="Update your education and career details" />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box sx={{ mb: 4 }}>
          <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
            <Link href="/website/pages/profile" passHref>
              <Typography color="inherit" sx={{ textDecoration: 'none', cursor: 'pointer' }}>
                Profile
              </Typography>
            </Link>
            <Typography color="text.primary">Education & Career</Typography>
          </Breadcrumbs>

          <Typography variant="h4" component="h1" gutterBottom>
            Education & Career
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Update your education and career information to help us find compatible matches.
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError('')}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess('')}>
              {success}
            </Alert>
          )}
        </Box>

        <ModernEducationCareerForm
          userData={userData}
          onSave={handleSave}
          isLoading={saving}
        />

        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Next: Update your <Link href="/website/pages/profile/edit/modern-location">Location Details</Link>
          </Typography>
        </Box>
      </Container>
    </>
  );
};

export default ModernEducationCareerPage;
