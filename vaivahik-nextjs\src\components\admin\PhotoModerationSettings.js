import { useState, useEffect } from 'react';
import axiosInstance from '@/utils/axiosConfig';

export default function PhotoModerationSettings() {
  const [settings, setSettings] = useState({
    operationMode: 0,
    automationPercentage: 0,
    autoApprovalConfidence: 95,
    autoRejectionConfidence: 98,
    requireFaceDetection: true,
    allowMultipleFaces: false,
    minFaceSize: 15,
    rejectExplicitContent: true,
    rejectViolentContent: true,
    flagSuggestiveContent: true,
    minResolution: 400
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [notification, setNotification] = useState({ show: false, message: '', type: 'success' });

  // Operation mode descriptions
  const operationModes = [
    {
      value: 0,
      label: 'Manual Only',
      description: 'All photos require manual review by admins. AI is disabled.'
    },
    {
      value: 1,
      label: 'Shadow Mode',
      description: 'AI analyzes photos but all decisions are still manual. Use this to test AI accuracy.'
    },
    {
      value: 2,
      label: 'Limited Automation',
      description: 'AI handles clear cases automatically. Control the percentage below.'
    },
    {
      value: 3,
      label: 'Full Automation',
      description: 'AI handles most cases automatically with customizable thresholds.'
    }
  ];

  // Fetch current settings
  const fetchSettings = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get('/api/admin/photo-moderation/settings');

      if (response.data.success) {
        setSettings(response.data.settings);
      } else {
        throw new Error('Failed to load settings');
      }
    } catch (error) {
      console.error('Error fetching moderation settings:', error);
      showNotification('Failed to load moderation settings', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Save settings
  const saveSettings = async () => {
    setSaving(true);
    try {
      const response = await axiosInstance.put('/api/admin/photo-moderation/settings', {
        settings
      });

      if (response.data.success) {
        showNotification('Settings saved successfully');
      } else {
        throw new Error('Failed to save settings');
      }
    } catch (error) {
      console.error('Error saving moderation settings:', error);
      showNotification('Failed to save settings', 'error');
    } finally {
      setSaving(false);
    }
  };

  // Handle input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked :
              type === 'number' ? parseFloat(value) :
              parseInt(value)
    }));
  };

  // Handle slider changes
  const handleSliderChange = (e) => {
    const { name, value } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: parseInt(value)
    }));
  };

  // Show notification
  const showNotification = (message, type = 'success') => {
    setNotification({ show: true, message, type });
    setTimeout(() => {
      setNotification({ ...notification, show: false });
    }, 3000);
  };

  // Load settings on component mount
  useEffect(() => {
    fetchSettings();
  }, []);

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading settings...</p>
      </div>
    );
  }

  return (
    <div className="settings-container">
      <div className="settings-header">
        <h2 className="section-title">AI Photo Moderation Settings</h2>
        <p className="section-description">
          Configure how the AI photo moderation system operates. Start with Manual or Shadow mode and gradually
          move to automation as you build confidence in the system.
        </p>
      </div>

      <div className="settings-section">
        <h3 className="subsection-title">Operation Mode</h3>
        <div className="operation-modes">
          {operationModes.map(mode => (
            <div
              key={mode.value}
              className={`mode-card ${settings.operationMode === mode.value ? 'selected' : ''}`}
              onClick={() => setSettings({...settings, operationMode: mode.value})}
            >
              <div className="mode-header">
                <input
                  type="radio"
                  name="operationMode"
                  value={mode.value}
                  checked={settings.operationMode === mode.value}
                  onChange={() => setSettings({...settings, operationMode: mode.value})}
                />
                <h4>{mode.label}</h4>
              </div>
              <p className="mode-description">{mode.description}</p>
            </div>
          ))}
        </div>
      </div>

      {settings.operationMode === 2 && (
        <div className="settings-section">
          <h3 className="subsection-title">Automation Percentage</h3>
          <p className="subsection-description">
            In Limited Automation mode, control what percentage of uploads are processed by AI.
          </p>

          <div className="slider-control">
            <label htmlFor="automationPercentage">
              Percentage of photos to process with AI: {settings.automationPercentage}%
            </label>
            <input
              type="range"
              id="automationPercentage"
              name="automationPercentage"
              min="0"
              max="100"
              step="10"
              value={settings.automationPercentage}
              onChange={handleSliderChange}
            />
            <div className="slider-labels">
              <span>0%</span>
              <span>50%</span>
              <span>100%</span>
            </div>
          </div>
        </div>
      )}

      {(settings.operationMode === 2 || settings.operationMode === 3) && (
        <>
          <div className="settings-section">
            <h3 className="subsection-title">Confidence Thresholds</h3>
            <p className="subsection-description">
              Set how confident the AI must be to automatically approve or reject photos.
            </p>

            <div className="slider-control">
              <label htmlFor="autoApprovalConfidence">
                Auto-Approval Threshold: {settings.autoApprovalConfidence}%
              </label>
              <input
                type="range"
                id="autoApprovalConfidence"
                name="autoApprovalConfidence"
                min="50"
                max="99"
                step="1"
                value={settings.autoApprovalConfidence}
                onChange={handleSliderChange}
              />
              <div className="slider-labels">
                <span>50%</span>
                <span>75%</span>
                <span>99%</span>
              </div>
            </div>

            <div className="slider-control">
              <label htmlFor="autoRejectionConfidence">
                Auto-Rejection Threshold: {settings.autoRejectionConfidence}%
              </label>
              <input
                type="range"
                id="autoRejectionConfidence"
                name="autoRejectionConfidence"
                min="50"
                max="99"
                step="1"
                value={settings.autoRejectionConfidence}
                onChange={handleSliderChange}
              />
              <div className="slider-labels">
                <span>50%</span>
                <span>75%</span>
                <span>99%</span>
              </div>
            </div>
          </div>

          <div className="settings-section">
            <h3 className="subsection-title">Content Detection Settings</h3>

            <div className="checkbox-group">
              <div className="checkbox-control">
                <input
                  type="checkbox"
                  id="requireFaceDetection"
                  name="requireFaceDetection"
                  checked={settings.requireFaceDetection}
                  onChange={handleChange}
                />
                <label htmlFor="requireFaceDetection">
                  Require face detection
                </label>
              </div>

              <div className="checkbox-control">
                <input
                  type="checkbox"
                  id="allowMultipleFaces"
                  name="allowMultipleFaces"
                  checked={settings.allowMultipleFaces}
                  onChange={handleChange}
                  disabled={!settings.requireFaceDetection}
                />
                <label htmlFor="allowMultipleFaces" className={!settings.requireFaceDetection ? 'disabled' : ''}>
                  Allow multiple faces in photos
                </label>
              </div>

              <div className="checkbox-control">
                <input
                  type="checkbox"
                  id="rejectExplicitContent"
                  name="rejectExplicitContent"
                  checked={settings.rejectExplicitContent}
                  onChange={handleChange}
                />
                <label htmlFor="rejectExplicitContent">
                  Reject explicit content
                </label>
              </div>

              <div className="checkbox-control">
                <input
                  type="checkbox"
                  id="rejectViolentContent"
                  name="rejectViolentContent"
                  checked={settings.rejectViolentContent}
                  onChange={handleChange}
                />
                <label htmlFor="rejectViolentContent">
                  Reject violent content
                </label>
              </div>

              <div className="checkbox-control">
                <input
                  type="checkbox"
                  id="flagSuggestiveContent"
                  name="flagSuggestiveContent"
                  checked={settings.flagSuggestiveContent}
                  onChange={handleChange}
                />
                <label htmlFor="flagSuggestiveContent">
                  Flag suggestive content for review
                </label>
              </div>
            </div>

            {settings.requireFaceDetection && (
              <div className="slider-control">
                <label htmlFor="minFaceSize">
                  Minimum face size: {settings.minFaceSize}% of image
                </label>
                <input
                  type="range"
                  id="minFaceSize"
                  name="minFaceSize"
                  min="5"
                  max="30"
                  step="1"
                  value={settings.minFaceSize}
                  onChange={handleSliderChange}
                />
                <div className="slider-labels">
                  <span>5%</span>
                  <span>15%</span>
                  <span>30%</span>
                </div>
              </div>
            )}

            <div className="number-control">
              <label htmlFor="minResolution">
                Minimum resolution (pixels):
              </label>
              <input
                type="number"
                id="minResolution"
                name="minResolution"
                min="200"
                max="1000"
                step="50"
                value={settings.minResolution}
                onChange={handleChange}
              />
            </div>
          </div>
        </>
      )}

      <div className="settings-actions">
        <button
          className="btn btn-outline-primary"
          onClick={fetchSettings}
          disabled={loading || saving}
        >
          Reset
        </button>

        <button
          className="btn btn-primary"
          onClick={saveSettings}
          disabled={saving}
        >
          {saving ? 'Saving...' : 'Save Settings'}
        </button>
      </div>

      {/* Notification Toast */}
      {notification.show && (
        <div className={`notification-toast show notification-${notification.type}`}>
          <div className="notification-content">
            <span>{notification.message}</span>
          </div>
        </div>
      )}

      <style jsx>{`
        .settings-container {
          background-color: white;
          border-radius: 12px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          padding: 20px;
          margin-bottom: 30px;
        }

        .settings-header {
          margin-bottom: 20px;
        }

        .section-title {
          font-size: 1.5rem;
          margin-bottom: 10px;
          color: var(--text-dark);
        }

        .section-description {
          color: #666;
          margin-bottom: 20px;
        }

        .settings-section {
          margin-bottom: 30px;
          padding-bottom: 20px;
          border-bottom: 1px solid #eee;
        }

        .subsection-title {
          font-size: 1.2rem;
          margin-bottom: 15px;
          color: var(--text-dark);
        }

        .subsection-description {
          color: #666;
          margin-bottom: 15px;
          font-size: 0.9rem;
        }

        .operation-modes {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
          gap: 15px;
          margin-bottom: 20px;
        }

        .mode-card {
          border: 2px solid #e0e0e0;
          border-radius: 8px;
          padding: 15px;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .mode-card.selected {
          border-color: var(--primary);
          background-color: rgba(var(--primary-rgb), 0.05);
        }

        .mode-card:hover {
          border-color: var(--primary-light);
        }

        .mode-header {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
        }

        .mode-header h4 {
          margin: 0 0 0 10px;
          font-size: 1rem;
        }

        .mode-description {
          font-size: 0.85rem;
          color: #666;
          margin: 0;
        }

        .slider-control {
          margin-bottom: 20px;
        }

        .slider-control label {
          display: block;
          margin-bottom: 10px;
          font-size: 0.9rem;
        }

        .slider-control input[type="range"] {
          width: 100%;
          margin-bottom: 5px;
        }

        .slider-labels {
          display: flex;
          justify-content: space-between;
          font-size: 0.8rem;
          color: #666;
        }

        .checkbox-group {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
          gap: 15px;
          margin-bottom: 20px;
        }

        .checkbox-control {
          display: flex;
          align-items: center;
        }

        .checkbox-control input {
          margin-right: 10px;
        }

        .checkbox-control label.disabled {
          color: #999;
        }

        .number-control {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .number-control input {
          width: 100px;
          padding: 8px;
          border: 1px solid #ccc;
          border-radius: 4px;
        }

        .settings-actions {
          display: flex;
          justify-content: flex-end;
          gap: 10px;
          margin-top: 20px;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
          .operation-modes {
            grid-template-columns: 1fr;
          }

          .checkbox-group {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </div>
  );
}
