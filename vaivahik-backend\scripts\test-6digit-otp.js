/**
 * Test script for 6-digit OTP implementation
 * 
 * This script tests the 6-digit OTP implementation by:
 * 1. Generating a 6-digit OTP
 * 2. Sending it to a phone number
 * 3. Verifying that the OTP is 6 digits
 * 
 * Usage:
 * node scripts/test-6digit-otp.js <phone_number>
 * 
 * Example:
 * node scripts/test-6digit-otp.js 919527159115
 */

// Load environment variables
require('dotenv').config();

// Import required modules
const msg91Service = require('../src/services/sms/msg91.service');
const redisOtpService = require('../redis/otpService');

// Check if phone number is provided
const phoneNumber = process.argv[2];
if (!phoneNumber) {
  console.error('Please provide a phone number as an argument.');
  console.error('Usage: node scripts/test-6digit-otp.js <phone_number>');
  process.exit(1);
}

// Initialize the MSG91 service
msg91Service.initialize({
  apiKey: process.env.MSG91_API_KEY,
  senderId: process.env.MSG91_SENDER_ID,
  dltTemplateId: process.env.MSG91_DLT_TEMPLATE_ID,
  dltPeId: process.env.MSG91_DLT_PE_ID,
  otpLength: 6,
  otpExpiry: 15,
  otpTemplate: process.env.MSG91_OTP_TEMPLATE
});

// Format the phone number
const formattedPhone = msg91Service.formatPhoneNumber(phoneNumber);

// Test the OTP generation
async function testOtpGeneration() {
  console.log('\n=== Testing OTP Generation ===');
  
  // Generate OTP using MSG91 service
  const msg91Otp = msg91Service.generateOtp();
  console.log(`MSG91 OTP: ${msg91Otp} (Length: ${msg91Otp.length})`);
  
  // Generate OTP using Redis OTP service
  const redisOtp = redisOtpService.generateOTP();
  console.log(`Redis OTP: ${redisOtp} (Length: ${redisOtp.length})`);
  
  // Verify that both OTPs are 6 digits
  if (msg91Otp.length !== 6) {
    console.error('❌ MSG91 OTP is not 6 digits!');
  } else {
    console.log('✅ MSG91 OTP is 6 digits');
  }
  
  if (redisOtp.length !== 6) {
    console.error('❌ Redis OTP is not 6 digits!');
  } else {
    console.log('✅ Redis OTP is 6 digits');
  }
  
  return msg91Otp;
}

// Test sending OTP
async function testSendOtp(otp) {
  console.log('\n=== Testing OTP Sending ===');
  console.log(`Sending OTP ${otp} to ${formattedPhone}...`);
  
  try {
    const result = await msg91Service.sendOtp(formattedPhone, otp);
    
    if (result.success) {
      console.log('✅ OTP sent successfully!');
      console.log('Response:', JSON.stringify(result.data, null, 2));
      
      // Store OTP in Redis
      await redisOtpService.setOTP(formattedPhone, otp, 900); // 15 minutes
      console.log('✅ OTP stored in Redis');
      
      return true;
    } else {
      console.error('❌ Failed to send OTP:', result.message);
      console.error('Response data:', JSON.stringify(result.data, null, 2));
      return false;
    }
  } catch (error) {
    console.error('❌ Error sending OTP:', error);
    return false;
  }
}

// Test verifying OTP
async function testVerifyOtp(otp) {
  console.log('\n=== Testing OTP Verification ===');
  console.log(`Verifying OTP ${otp} for ${formattedPhone}...`);
  
  try {
    // Verify OTP from Redis
    const redisResult = await redisOtpService.verifyOTP(formattedPhone, otp);
    
    if (redisResult) {
      console.log('✅ OTP verified successfully in Redis!');
    } else {
      console.error('❌ Failed to verify OTP in Redis!');
    }
    
    // Verify OTP from MSG91
    const msg91Result = await msg91Service.verifyOtp(formattedPhone, otp);
    
    if (msg91Result.success) {
      console.log('✅ OTP verified successfully with MSG91!');
      console.log('Response:', JSON.stringify(msg91Result.data, null, 2));
    } else {
      console.error('❌ Failed to verify OTP with MSG91:', msg91Result.message);
      console.error('Response data:', JSON.stringify(msg91Result.data, null, 2));
    }
    
    return redisResult || msg91Result.success;
  } catch (error) {
    console.error('❌ Error verifying OTP:', error);
    return false;
  }
}

// Run the tests
async function runTests() {
  try {
    console.log('=== 6-Digit OTP Test ===');
    console.log(`Testing with phone number: ${formattedPhone}`);
    
    // Test OTP generation
    const otp = await testOtpGeneration();
    
    // Test sending OTP
    const sendResult = await testSendOtp(otp);
    
    if (sendResult) {
      console.log('\n✅ OTP test completed successfully!');
      console.log(`The 6-digit OTP ${otp} has been sent to ${formattedPhone}`);
      console.log('Please check your phone to verify that you received a 6-digit OTP.');
    } else {
      console.error('\n❌ OTP test failed!');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error running tests:', error);
    process.exit(1);
  }
}

// Run the tests
runTests();
