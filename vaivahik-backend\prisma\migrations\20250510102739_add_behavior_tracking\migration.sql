/*
  Warnings:

  - The `message_type` column on the `messages` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - Made the column `content_type` on table `moderation_logs` required. This step will fail if there are existing NULL values in that column.
  - Made the column `decision` on table `moderation_logs` required. This step will fail if there are existing NULL values in that column.

*/
-- CreateEnum
CREATE TYPE "MessageType" AS ENUM ('TEXT', 'IMAGE', 'FILE', 'AUDIO', 'VIDEO', 'SYSTEM');

-- CreateEnum
CREATE TYPE "InteractionType" AS ENUM ('VIEW', 'LIKE', 'SHORTLIST', 'CONTACT_REQUESTED', 'CONTACT_VIEWED', 'CONTACT_ACCEPTED', 'CONTACT_REJECTED', 'CHAT_INITIATED', 'CHAT_RESPONDED', 'REPORT', 'BLOCK', 'IGNORE');

-- CreateEnum
CREATE TYPE "FeedbackType" AS ENUM ('AFTER_VIEW', 'AFTER_CONTACT', 'AFTER_CHAT', 'AFTER_MEETING', 'GENERAL', 'REASON_FOR_REJECT', 'REASON_FOR_ACCEPT');

-- CreateEnum
CREATE TYPE "SuccessStoryStatus" AS ENUM ('MATCHED', 'TALKING', 'MET', 'ENGAGED', 'MARRIED');

-- AlterTable
ALTER TABLE "messages" DROP COLUMN "message_type",
ADD COLUMN     "message_type" "MessageType" NOT NULL DEFAULT 'TEXT';

-- AlterTable
ALTER TABLE "moderation_logs" ALTER COLUMN "content_type" SET NOT NULL,
ALTER COLUMN "decision" SET NOT NULL;

-- CreateTable
CREATE TABLE "preference_categories" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "display_name" TEXT NOT NULL,
    "description" TEXT,
    "display_order" INTEGER NOT NULL DEFAULT 0,
    "icon" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "is_required" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT,
    "updated_by" TEXT,

    CONSTRAINT "preference_categories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "preference_fields" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "display_name" TEXT NOT NULL,
    "description" TEXT,
    "field_type" TEXT NOT NULL,
    "display_order" INTEGER NOT NULL DEFAULT 0,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "is_required" BOOLEAN NOT NULL DEFAULT false,
    "is_searchable" BOOLEAN NOT NULL DEFAULT true,
    "is_match_criteria" BOOLEAN NOT NULL DEFAULT true,
    "default_value" TEXT,
    "validation_rules" TEXT,
    "min_value" DOUBLE PRECISION,
    "max_value" DOUBLE PRECISION,
    "step_value" DOUBLE PRECISION,
    "category_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT,
    "updated_by" TEXT,

    CONSTRAINT "preference_fields_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "preference_options" (
    "id" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "display_text" TEXT NOT NULL,
    "description" TEXT,
    "display_order" INTEGER NOT NULL DEFAULT 0,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "field_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT,
    "updated_by" TEXT,

    CONSTRAINT "preference_options_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "preference_importance" (
    "id" TEXT NOT NULL,
    "importance_level" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "description" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "field_id" TEXT NOT NULL,
    "gender" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT,
    "updated_by" TEXT,

    CONSTRAINT "preference_importance_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_interactions" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "targetUserId" TEXT NOT NULL,
    "interactionType" "InteractionType" NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "duration" INTEGER,
    "deviceInfo" JSONB,
    "ipAddress" TEXT,
    "viewedPhotos" BOOLEAN NOT NULL DEFAULT false,
    "viewedDetails" BOOLEAN NOT NULL DEFAULT false,
    "viewedPreferences" BOOLEAN NOT NULL DEFAULT false,
    "viewedContact" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "user_interactions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_feedback" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "targetUserId" TEXT NOT NULL,
    "feedbackType" "FeedbackType" NOT NULL,
    "rating" DOUBLE PRECISION NOT NULL,
    "comments" TEXT,
    "continueInterest" BOOLEAN NOT NULL DEFAULT false,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "factorsLiked" JSONB,
    "factorsDisliked" JSONB,

    CONSTRAINT "user_feedback_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "success_stories" (
    "id" TEXT NOT NULL,
    "userId1" TEXT NOT NULL,
    "userId2" TEXT NOT NULL,
    "status" "SuccessStoryStatus" NOT NULL,
    "storyText" TEXT,
    "testimonyText" TEXT,
    "rating" DOUBLE PRECISION,
    "engagementDate" TIMESTAMP(3),
    "marriageDate" TIMESTAMP(3),
    "photos" JSONB,
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "successFactors" JSONB,

    CONSTRAINT "success_stories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_preference_history" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "preferenceType" TEXT NOT NULL,
    "oldValue" JSONB,
    "newValue" JSONB,
    "changeReason" TEXT,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_preference_history_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "match_scores" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "targetUserId" TEXT NOT NULL,
    "score" DOUBLE PRECISION NOT NULL,
    "factorScores" JSONB NOT NULL,
    "algorithm" TEXT NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "match_scores_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "preference_categories_name_key" ON "preference_categories"("name");

-- CreateIndex
CREATE UNIQUE INDEX "preference_fields_category_id_name_key" ON "preference_fields"("category_id", "name");

-- CreateIndex
CREATE UNIQUE INDEX "preference_options_field_id_value_key" ON "preference_options"("field_id", "value");

-- CreateIndex
CREATE UNIQUE INDEX "preference_importance_field_id_gender_key" ON "preference_importance"("field_id", "gender");

-- CreateIndex
CREATE INDEX "user_interactions_userId_idx" ON "user_interactions"("userId");

-- CreateIndex
CREATE INDEX "user_interactions_targetUserId_idx" ON "user_interactions"("targetUserId");

-- CreateIndex
CREATE INDEX "user_interactions_interactionType_idx" ON "user_interactions"("interactionType");

-- CreateIndex
CREATE INDEX "user_interactions_timestamp_idx" ON "user_interactions"("timestamp");

-- CreateIndex
CREATE INDEX "user_feedback_userId_idx" ON "user_feedback"("userId");

-- CreateIndex
CREATE INDEX "user_feedback_targetUserId_idx" ON "user_feedback"("targetUserId");

-- CreateIndex
CREATE INDEX "user_feedback_feedbackType_idx" ON "user_feedback"("feedbackType");

-- CreateIndex
CREATE INDEX "user_feedback_timestamp_idx" ON "user_feedback"("timestamp");

-- CreateIndex
CREATE INDEX "success_stories_userId1_idx" ON "success_stories"("userId1");

-- CreateIndex
CREATE INDEX "success_stories_userId2_idx" ON "success_stories"("userId2");

-- CreateIndex
CREATE INDEX "success_stories_status_idx" ON "success_stories"("status");

-- CreateIndex
CREATE INDEX "success_stories_isPublic_idx" ON "success_stories"("isPublic");

-- CreateIndex
CREATE INDEX "user_preference_history_userId_idx" ON "user_preference_history"("userId");

-- CreateIndex
CREATE INDEX "user_preference_history_preferenceType_idx" ON "user_preference_history"("preferenceType");

-- CreateIndex
CREATE INDEX "user_preference_history_timestamp_idx" ON "user_preference_history"("timestamp");

-- CreateIndex
CREATE INDEX "match_scores_userId_idx" ON "match_scores"("userId");

-- CreateIndex
CREATE INDEX "match_scores_targetUserId_idx" ON "match_scores"("targetUserId");

-- CreateIndex
CREATE INDEX "match_scores_score_idx" ON "match_scores"("score");

-- CreateIndex
CREATE INDEX "match_scores_timestamp_idx" ON "match_scores"("timestamp");

-- AddForeignKey
ALTER TABLE "preference_fields" ADD CONSTRAINT "preference_fields_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "preference_categories"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "preference_options" ADD CONSTRAINT "preference_options_field_id_fkey" FOREIGN KEY ("field_id") REFERENCES "preference_fields"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "preference_importance" ADD CONSTRAINT "preference_importance_field_id_fkey" FOREIGN KEY ("field_id") REFERENCES "preference_fields"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_interactions" ADD CONSTRAINT "user_interactions_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_interactions" ADD CONSTRAINT "user_interactions_targetUserId_fkey" FOREIGN KEY ("targetUserId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_feedback" ADD CONSTRAINT "user_feedback_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_feedback" ADD CONSTRAINT "user_feedback_targetUserId_fkey" FOREIGN KEY ("targetUserId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "success_stories" ADD CONSTRAINT "success_stories_userId1_fkey" FOREIGN KEY ("userId1") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "success_stories" ADD CONSTRAINT "success_stories_userId2_fkey" FOREIGN KEY ("userId2") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_preference_history" ADD CONSTRAINT "user_preference_history_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "match_scores" ADD CONSTRAINT "match_scores_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "match_scores" ADD CONSTRAINT "match_scores_targetUserId_fkey" FOREIGN KEY ("targetUserId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- RenameIndex
ALTER INDEX "conversations_user1_id_user2_id_unique" RENAME TO "conversations_user1_id_user2_id_key";
