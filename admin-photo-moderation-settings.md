## Photo Moderation Settings

### General Settings

- **Operation Mode**
  - [ ] Shadow Mode (AI analyzes but doesn't affect approval)
  - [x] Hybrid Mode (AI handles clear cases, humans handle edge cases)
  - [ ] Full AI Mode (AI handles all cases with manual override)

- **Auto-Approval Settings**
  - [x] Enable Auto-Approval
  - Confidence Threshold: [90%] (slider from 50-99%)
  - Maximum Processing Time: [5] seconds

- **Auto-Rejection Settings**
  - [x] Enable Auto-Rejection
  - Confidence Threshold: [95%] (slider from 50-99%)
  - [x] Send rejection reason to user

### Content Detection Settings

- **Face Detection Requirements**
  - [x] Require face detection
  - [x] Reject photos with no face
  - [ ] Allow multiple faces
  - Minimum face size: [15%] of image

- **Inappropriate Content Detection**
  - [x] Reject explicit content
  - [x] Reject violent content
  - [x] Flag suggestive content for review
  - [x] Flag text-heavy images for review

- **Quality Requirements**
  - [x] Reject blurry images
  - Minimum resolution: [400 x 400] pixels
  - Maximum file size: [5] MB

### User Experience Settings

- **Feedback Options**
  - [x] Show specific rejection reasons to users
  - [x] Provide improvement suggestions
  - [x] Allow appeals for rejected photos
  - Appeal review priority: [High]

- **Processing Queue**
  - Maximum queue time: [24] hours
  - [x] Auto-approve if queue time exceeded
  - [x] Notify admin if queue grows beyond [50] photos

### Advanced Settings

- **Model Configuration**
  - Primary NSFW Model: [NSFWJS v2.4.0]
  - Face Detection Model: [Face-API SSD MobileNet]
  - [x] Enable model caching
  - Cache duration: [24] hours

- **System Resources**
  - Maximum concurrent processes: [5]
  - Memory limit: [1000] MB
  - [x] Enable resource monitoring
  - [x] Auto-scale based on queue size
