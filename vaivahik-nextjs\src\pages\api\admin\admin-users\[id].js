// API endpoint for individual admin users
import { handleApiError } from '@/utils/errorHandler';
import { withAuth } from '@/utils/authHandler';

async function handler(req, res) {
  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getAdminUser(req, res);
      case 'PUT':
        return await updateAdminUser(req, res);
      case 'DELETE':
        return await deleteAdminUser(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    return handleApiError(error, res, 'Admin User API');
  }
}

// Mock data for admin users
const adminUsers = [
  {
    id: 1,
    name: 'Super Admin',
    email: '<EMAIL>',
    role: 'SUPER_ADMIN',
    permissions: ['all'],
    lastLogin: '2023-08-01T10:15:00Z',
    createdAt: '2023-01-01T00:00:00Z',
    status: 'active',
    avatar: null
  },
  {
    id: 2,
    name: 'Content Manager',
    email: '<EMAIL>',
    role: 'CONTENT_MANAGER',
    permissions: ['blog_posts', 'success_stories', 'biodata_templates'],
    lastLogin: '2023-07-28T14:30:00Z',
    createdAt: '2023-02-15T00:00:00Z',
    status: 'active',
    avatar: null
  },
  {
    id: 3,
    name: 'User Manager',
    email: '<EMAIL>',
    role: 'USER_MANAGER',
    permissions: ['users', 'verification', 'reports'],
    lastLogin: '2023-07-30T09:45:00Z',
    createdAt: '2023-03-10T00:00:00Z',
    status: 'active',
    avatar: null
  },
  {
    id: 4,
    name: 'Finance Admin',
    email: '<EMAIL>',
    role: 'FINANCE_ADMIN',
    permissions: ['subscriptions', 'transactions', 'revenue_reports'],
    lastLogin: '2023-07-25T16:20:00Z',
    createdAt: '2023-04-05T00:00:00Z',
    status: 'active',
    avatar: null
  },
  {
    id: 5,
    name: 'Support Staff',
    email: '<EMAIL>',
    role: 'SUPPORT_STAFF',
    permissions: ['users', 'verification', 'reports', 'notifications'],
    lastLogin: '2023-07-31T11:10:00Z',
    createdAt: '2023-05-20T00:00:00Z',
    status: 'active',
    avatar: null
  },
  {
    id: 6,
    name: 'Test Admin',
    email: '<EMAIL>',
    role: 'CONTENT_MANAGER',
    permissions: ['blog_posts', 'success_stories'],
    lastLogin: null,
    createdAt: '2023-06-15T00:00:00Z',
    status: 'inactive',
    avatar: null
  }
];

// GET /api/admin/admin-users/[id]
async function getAdminUser(req, res) {
  try {
    const { id } = req.query;
    
    // Find the admin user by ID
    const adminUser = adminUsers.find(user => user.id === parseInt(id));
    
    if (!adminUser) {
      return res.status(404).json({
        success: false,
        message: 'Admin user not found'
      });
    }
    
    return res.status(200).json({
      success: true,
      adminUser
    });
  } catch (error) {
    return handleApiError(error, res, 'Get admin user');
  }
}

// PUT /api/admin/admin-users/[id]
async function updateAdminUser(req, res) {
  try {
    const { id } = req.query;
    const { name, email, role, permissions, status, password } = req.body;
    
    // Find the admin user by ID
    const adminUserIndex = adminUsers.findIndex(user => user.id === parseInt(id));
    
    if (adminUserIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Admin user not found'
      });
    }
    
    // Check if email is valid
    if (email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid email format'
        });
      }
    }
    
    // Check if password is strong enough
    if (password && password.length < 8) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 8 characters long'
      });
    }
    
    // Update the admin user
    const updatedAdminUser = {
      ...adminUsers[adminUserIndex],
      name: name || adminUsers[adminUserIndex].name,
      email: email || adminUsers[adminUserIndex].email,
      role: role || adminUsers[adminUserIndex].role,
      permissions: permissions || adminUsers[adminUserIndex].permissions,
      status: status || adminUsers[adminUserIndex].status,
      // In a real implementation, we would hash the password
      // password: password ? hashPassword(password) : adminUsers[adminUserIndex].password
    };
    
    // In a real implementation, this would update the admin user in the database
    // For now, we'll just return a success response with the updated admin user
    
    return res.status(200).json({
      success: true,
      message: 'Admin user updated successfully',
      adminUser: updatedAdminUser
    });
  } catch (error) {
    return handleApiError(error, res, 'Update admin user');
  }
}

// DELETE /api/admin/admin-users/[id]
async function deleteAdminUser(req, res) {
  try {
    const { id } = req.query;
    
    // Find the admin user by ID
    const adminUserIndex = adminUsers.findIndex(user => user.id === parseInt(id));
    
    if (adminUserIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Admin user not found'
      });
    }
    
    // Check if trying to delete the super admin
    if (adminUsers[adminUserIndex].role === 'SUPER_ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Cannot delete the Super Admin user'
      });
    }
    
    // In a real implementation, this would delete the admin user from the database
    // For now, we'll just return a success response
    
    return res.status(200).json({
      success: true,
      message: 'Admin user deleted successfully'
    });
  } catch (error) {
    return handleApiError(error, res, 'Delete admin user');
  }
}

export default handler;
