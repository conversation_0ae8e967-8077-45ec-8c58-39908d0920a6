import { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  TextField,
  Button,
  Chip,
  Typography,
  CircularProgress,
  Alert,
  IconButton,
  Paper,
  InputAdornment
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import { toast } from 'react-toastify';
import axiosInstance from '@/utils/axiosConfig';

export default function BannedWordsList() {
  const [bannedWords, setBannedWords] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  // For adding new words
  const [newWord, setNewWord] = useState('');

  // For filtering words
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredWords, setFilteredWords] = useState([]);

  // Fetch banned words on component mount
  useEffect(() => {
    fetchBannedWords();
  }, []);

  // Filter words when search term or banned words change
  useEffect(() => {
    if (searchTerm) {
      setFilteredWords(bannedWords.filter(word =>
        word.toLowerCase().includes(searchTerm.toLowerCase())
      ));
    } else {
      setFilteredWords(bannedWords);
    }
  }, [searchTerm, bannedWords]);

  // Fetch banned words from API
  const fetchBannedWords = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get('/api/admin/text-moderation/banned-words');

      if (response.data.success) {
        setBannedWords(response.data.bannedWords);
      } else {
        throw new Error(response.data.message || 'Failed to fetch banned words');
      }
    } catch (error) {
      console.error('Error fetching banned words:', error);
      setError(error.message || 'Failed to fetch banned words');
    } finally {
      setLoading(false);
    }
  };

  // Save banned words to API
  const saveBannedWords = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(false);

      const response = await axiosInstance.put('/api/admin/text-moderation/banned-words', {
        words: bannedWords
      });

      if (response.data.success) {
        setSuccess(true);
        toast.success('Banned words updated successfully');
      } else {
        throw new Error(response.data.message || 'Failed to update banned words');
      }
    } catch (error) {
      console.error('Error updating banned words:', error);
      setError(error.message || 'Failed to update banned words');
      toast.error(error.message || 'Failed to update banned words');
    } finally {
      setSaving(false);
    }
  };

  // Add word to banned words
  const handleAddWord = () => {
    if (!newWord.trim()) {
      toast.error('Please enter a word');
      return;
    }

    if (bannedWords.includes(newWord.trim())) {
      toast.error('Word already exists in the list');
      return;
    }

    setBannedWords(prev => [...prev, newWord.trim()]);
    setNewWord('');
  };

  // Remove word from banned words
  const handleRemoveWord = (word) => {
    setBannedWords(prev => prev.filter(w => w !== word));
  };

  // Clear search term
  const handleClearSearch = () => {
    setSearchTerm('');
  };

  // Import words from text area
  const handleImportWords = (e) => {
    const text = e.target.value;
    if (!text.trim()) return;

    // Split by commas, newlines, or spaces
    const words = text.split(/[\s,]+/).filter(word => word.trim());

    // Add unique words
    const uniqueWords = [...new Set(words)];
    const newWords = uniqueWords.filter(word => !bannedWords.includes(word));

    if (newWords.length > 0) {
      setBannedWords(prev => [...prev, ...newWords]);
      toast.success(`Added ${newWords.length} new words`);
    } else {
      toast.info('No new words to add');
    }

    // Clear the textarea
    e.target.value = '';
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          Banned words updated successfully
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Add Banned Words" />
            <Divider />
            <CardContent>
              <Box sx={{ mb: 3 }}>
                <TextField
                  label="New Banned Word"
                  value={newWord}
                  onChange={(e) => setNewWord(e.target.value)}
                  fullWidth
                  placeholder="Enter a word to ban"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      handleAddWord();
                    }
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <Button
                          variant="contained"
                          color="primary"
                          onClick={handleAddWord}
                          disabled={!newWord.trim()}
                        >
                          <AddIcon />
                        </Button>
                      </InputAdornment>
                    )
                  }}
                />
              </Box>

              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Bulk Import
                </Typography>
                <TextField
                  multiline
                  rows={4}
                  fullWidth
                  placeholder="Paste multiple words separated by commas, spaces, or new lines"
                  onChange={handleImportWords}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Current Banned Words"
              subheader={`Total: ${bannedWords.length} words`}
              action={
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<SaveIcon />}
                  onClick={saveBannedWords}
                  disabled={saving}
                >
                  {saving ? 'Saving...' : 'Save Changes'}
                </Button>
              }
            />
            <Divider />
            <CardContent>
              <TextField
                label="Search Words"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                fullWidth
                sx={{ mb: 2 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                  endAdornment: searchTerm && (
                    <InputAdornment position="end">
                      <IconButton onClick={handleClearSearch} size="small">
                        <ClearIcon />
                      </IconButton>
                    </InputAdornment>
                  )
                }}
              />

              <Paper
                variant="outlined"
                sx={{
                  p: 2,
                  maxHeight: 300,
                  overflowY: 'auto',
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: 1
                }}
              >
                {filteredWords.length > 0 ? (
                  filteredWords.map((word) => (
                    <Chip
                      key={word}
                      label={word}
                      onDelete={() => handleRemoveWord(word)}
                      color="error"
                      variant="outlined"
                      sx={{ m: 0.5 }}
                    />
                  ))
                ) : (
                  <Typography variant="body2" color="textSecondary" sx={{ width: '100%', textAlign: 'center' }}>
                    {searchTerm ? 'No matching words found' : 'No banned words added yet'}
                  </Typography>
                )}
              </Paper>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}