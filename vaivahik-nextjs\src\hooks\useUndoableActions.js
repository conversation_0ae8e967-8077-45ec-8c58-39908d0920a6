import { useState } from 'react';
import { useToast } from '@/contexts/ToastContext';

/**
 * Hook for managing undoable actions
 * 
 * Provides standardized methods for performing actions with undo functionality
 * across the application.
 * 
 * @returns {Object} Methods for performing undoable actions
 */
const useUndoableActions = () => {
  const { showUndoToast } = useToast();
  const [actionLoading, setActionLoading] = useState(null);
  const [removedItems, setRemovedItems] = useState({});
  
  /**
   * Remove an item with undo functionality
   * 
   * @param {Object} options - Options for the remove action
   * @param {string} options.itemId - ID of the item to remove
   * @param {string} options.itemName - Name of the item (for toast message)
   * @param {string} options.itemType - Type of item (e.g., 'profile', 'interest')
   * @param {Function} options.removeFunction - API function to call for removal
   * @param {Function} options.addFunction - API function to call for restoration
   * @param {Function} options.onRemoveFromState - Function to update local state after removal
   * @param {Function} options.onAddToState - Function to update local state after restoration
   * @param {Object} options.itemData - Full item data (for restoration)
   * @param {Object} options.additionalData - Any additional data needed for API calls
   */
  const removeWithUndo = async ({
    itemId,
    itemName,
    itemType = 'item',
    removeFunction,
    addFunction,
    onRemoveFromState,
    onAddToState,
    itemData,
    additionalData = {}
  }) => {
    try {
      setActionLoading(itemId);
      
      // Store the item for potential undo
      setRemovedItems(prev => ({
        ...prev,
        [itemId]: { itemData, additionalData }
      }));
      
      // Update local state immediately (optimistic update)
      if (onRemoveFromState) {
        onRemoveFromState(itemId);
      }
      
      // Call API to remove the item
      await removeFunction(itemId, additionalData);
      
      // Show toast with undo option
      showUndoToast(
        `${itemName || itemType} removed`,
        () => handleUndo({
          itemId,
          itemType,
          addFunction,
          onAddToState
        })
      );
      
    } catch (error) {
      console.error(`Error removing ${itemType}:`, error);
      
      // Revert the optimistic update
      if (onAddToState && removedItems[itemId]) {
        onAddToState(removedItems[itemId].itemData);
      }
      
      // Show error toast
      showUndoToast(`Failed to remove ${itemType}`, null);
    } finally {
      setActionLoading(null);
    }
  };
  
  /**
   * Handle undo action
   * 
   * @param {Object} options - Options for the undo action
   * @param {string} options.itemId - ID of the item to restore
   * @param {string} options.itemType - Type of item
   * @param {Function} options.addFunction - API function to call for restoration
   * @param {Function} options.onAddToState - Function to update local state after restoration
   */
  const handleUndo = async ({
    itemId,
    itemType = 'item',
    addFunction,
    onAddToState
  }) => {
    const removedItem = removedItems[itemId];
    if (!removedItem) return;
    
    try {
      setActionLoading(itemId);
      
      // Call API to restore the item
      await addFunction(itemId, removedItem.additionalData);
      
      // Update local state
      if (onAddToState) {
        onAddToState(removedItem.itemData);
      }
      
      // Clear removed item
      setRemovedItems(prev => {
        const newState = { ...prev };
        delete newState[itemId];
        return newState;
      });
      
      // Show success toast
      showUndoToast(`${itemType} restored`, null);
    } catch (error) {
      console.error(`Error restoring ${itemType}:`, error);
      
      // Show error toast
      showUndoToast(`Failed to restore ${itemType}`, null);
    } finally {
      setActionLoading(null);
    }
  };
  
  return {
    removeWithUndo,
    actionLoading
  };
};

export default useUndoableActions;
