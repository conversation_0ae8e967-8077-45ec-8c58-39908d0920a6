import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import ChatInterface from '@/components/chat/ChatInterface';
import ConversationList from '@/components/chat/ConversationList';
import MainLayout from '@/components/layouts/MainLayout';
import styles from '@/styles/MessagesPage.module.css';

export default function ConversationPage() {
  const [userId, setUserId] = useState(null);
  const [token, setToken] = useState(null);
  const [conversation, setConversation] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showMobileList, setShowMobileList] = useState(false);
  
  const router = useRouter();
  const { id: conversationId } = router.query;
  const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
  
  // Check authentication
  useEffect(() => {
    const storedToken = localStorage.getItem('token');
    const storedUserId = localStorage.getItem('userId');
    
    if (!storedToken || !storedUserId) {
      router.push('/login?redirect=/messages');
      return;
    }
    
    setToken(storedToken);
    setUserId(storedUserId);
  }, [router]);
  
  // Fetch conversation details
  useEffect(() => {
    if (!token || !userId || !conversationId) return;
    
    const fetchConversation = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${apiBaseUrl}/api/users/conversations`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (!response.ok) {
          throw new Error('Failed to fetch conversations');
        }
        
        const data = await response.json();
        
        if (data.success) {
          const foundConversation = data.conversations.find(
            conv => conv.id === conversationId
          );
          
          if (foundConversation) {
            setConversation(foundConversation);
          } else {
            throw new Error('Conversation not found');
          }
        } else {
          throw new Error(data.message || 'Failed to fetch conversation');
        }
      } catch (error) {
        console.error('Error fetching conversation:', error);
        setError(error.message || 'Failed to fetch conversation');
      } finally {
        setLoading(false);
      }
    };
    
    fetchConversation();
  }, [token, userId, conversationId, apiBaseUrl]);
  
  // Toggle mobile conversation list
  const toggleMobileList = () => {
    setShowMobileList(!showMobileList);
  };
  
  if (loading) {
    return (
      <MainLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading conversation...</p>
        </div>
      </MainLayout>
    );
  }
  
  if (error) {
    return (
      <MainLayout>
        <div className={styles.errorContainer}>
          <h2>Error</h2>
          <p>{error}</p>
          <button onClick={() => router.push('/messages')}>Back to Messages</button>
        </div>
      </MainLayout>
    );
  }
  
  return (
    <MainLayout>
      <Head>
        <title>
          {conversation?.otherUser?.name 
            ? `Chat with ${conversation.otherUser.name} | Vaivahik` 
            : 'Messages | Vaivahik'}
        </title>
        <meta name="description" content="Your conversations on Vaivahik" />
      </Head>
      
      <div className={styles.messagesPage}>
        <div className={`${styles.conversationListContainer} ${showMobileList ? styles.showMobile : ''}`}>
          <ConversationList
            userId={userId}
            token={token}
            apiBaseUrl={apiBaseUrl}
            onSelectConversation={(id) => {
              router.push(`/messages/${id}`);
              setShowMobileList(false);
            }}
          />
        </div>
        
        <div className={styles.chatContainer}>
          {conversation && (
            <ChatInterface
              userId={userId}
              token={token}
              apiBaseUrl={apiBaseUrl}
              conversationId={conversationId}
              otherUser={conversation.otherUser}
            />
          )}
        </div>
        
        <button 
          className={styles.mobileToggle}
          onClick={toggleMobileList}
        >
          {showMobileList ? '✕' : '☰'}
        </button>
      </div>
    </MainLayout>
  );
}
