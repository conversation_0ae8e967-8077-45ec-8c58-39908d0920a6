const Razorpay = require('razorpay');
const crypto = require('crypto');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Initialize Razorpay with your key_id and key_secret
const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID,
  key_secret: process.env.RAZORPAY_KEY_SECRET
});

/**
 * Service for handling payments
 */
class PaymentService {
  /**
   * Create a new payment order for biodata template purchase
   * @param {string} userId - The user ID
   * @param {string} templateId - The biodata template ID
   * @returns {Promise<Object>} - The payment order details
   */
  async createBiodataPaymentOrder(userId, templateId) {
    try {
      // Get user
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          name: true,
          email: true,
          phone: true
        }
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Get template
      const template = await prisma.biodataTemplate.findUnique({
        where: { id: templateId }
      });

      if (!template) {
        throw new Error('Template not found');
      }

      // Check if user has already purchased this template
      const existingPurchase = await prisma.userBiodata.findFirst({
        where: {
          userId,
          templateId
        }
      });

      if (existingPurchase) {
        throw new Error('You have already purchased this template');
      }

      // Calculate amount (in paise)
      const amount = Math.round((template.discountedPrice || template.price) * 100);

      // Create Razorpay order
      const order = await razorpay.orders.create({
        amount,
        currency: 'INR',
        receipt: `biodata_${userId}_${templateId}_${Date.now()}`,
        notes: {
          userId,
          templateId,
          productType: 'biodata',
          templateName: template.name
        }
      });

      // Save order in database
      await prisma.payment.create({
        data: {
          userId,
          orderId: order.id,
          amount: amount / 100, // Store in rupees
          currency: 'INR',
          status: 'CREATED',
          productType: 'BIODATA',
          productId: templateId,
          metadata: JSON.stringify({
            templateId,
            templateName: template.name,
            price: template.price,
            discountedPrice: template.discountedPrice
          })
        }
      });

      return {
        orderId: order.id,
        amount: amount / 100,
        currency: 'INR',
        keyId: process.env.RAZORPAY_KEY_ID,
        prefillData: {
          name: user.name,
          email: user.email,
          contact: user.phone
        },
        notes: {
          userId,
          templateId,
          productType: 'biodata',
          templateName: template.name
        }
      };
    } catch (error) {
      console.error('Error creating biodata payment order:', error);
      throw error;
    }
  }

  /**
   * Create a new payment order for spotlight purchase
   * @param {string} userId - The user ID
   * @param {string} spotlightId - The spotlight feature ID
   * @param {number} quantity - The quantity to purchase
   * @returns {Promise<Object>} - The payment order details
   */
  async createSpotlightPaymentOrder(userId, spotlightId, quantity = 1) {
    try {
      // Get user
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          name: true,
          email: true,
          phone: true
        }
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Get spotlight feature
      const spotlight = await prisma.spotlightFeature.findUnique({
        where: { id: spotlightId }
      });

      if (!spotlight) {
        throw new Error('Spotlight feature not found');
      }

      if (!spotlight.isActive) {
        throw new Error('This spotlight feature is currently unavailable');
      }

      // Calculate amount (in paise)
      const unitPrice = spotlight.discountedPrice || spotlight.price;
      const totalPrice = unitPrice * quantity;
      const amount = Math.round(totalPrice * 100);

      // Create Razorpay order
      const order = await razorpay.orders.create({
        amount,
        currency: 'INR',
        receipt: `spotlight_${userId}_${spotlightId}_${Date.now()}`,
        notes: {
          userId,
          spotlightId,
          productType: 'spotlight',
          spotlightName: spotlight.name,
          quantity
        }
      });

      // Save order in database
      await prisma.payment.create({
        data: {
          userId,
          orderId: order.id,
          amount: totalPrice, // Store in rupees
          currency: 'INR',
          status: 'CREATED',
          productType: 'SPOTLIGHT',
          productId: spotlightId,
          metadata: JSON.stringify({
            spotlightId,
            spotlightName: spotlight.name,
            price: spotlight.price,
            discountedPrice: spotlight.discountedPrice,
            quantity,
            defaultCount: spotlight.defaultCount || 1
          })
        }
      });

      return {
        orderId: order.id,
        amount: totalPrice,
        currency: 'INR',
        keyId: process.env.RAZORPAY_KEY_ID,
        prefillData: {
          name: user.name,
          email: user.email,
          contact: user.phone
        },
        notes: {
          userId,
          spotlightId,
          productType: 'spotlight',
          spotlightName: spotlight.name,
          quantity
        }
      };
    } catch (error) {
      console.error('Error creating spotlight payment order:', error);
      throw error;
    }
  }

  /**
   * Verify payment signature
   * @param {string} orderId - The order ID
   * @param {string} paymentId - The payment ID
   * @param {string} signature - The signature from Razorpay
   * @returns {boolean} - Whether the signature is valid
   */
  verifyPaymentSignature(orderId, paymentId, signature) {
    try {
      const generatedSignature = crypto
        .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
        .update(`${orderId}|${paymentId}`)
        .digest('hex');
      
      return generatedSignature === signature;
    } catch (error) {
      console.error('Error verifying payment signature:', error);
      return false;
    }
  }

  /**
   * Process successful payment
   * @param {string} orderId - The order ID
   * @param {string} paymentId - The payment ID
   * @returns {Promise<Object>} - The processed payment details
   */
  async processSuccessfulPayment(orderId, paymentId) {
    try {
      // Get payment from database
      const payment = await prisma.payment.findFirst({
        where: { orderId }
      });

      if (!payment) {
        throw new Error('Payment not found');
      }

      if (payment.status === 'COMPLETED') {
        return { success: true, message: 'Payment already processed', payment };
      }

      // Update payment status
      const updatedPayment = await prisma.payment.update({
        where: { id: payment.id },
        data: {
          status: 'COMPLETED',
          paymentId,
          completedAt: new Date()
        }
      });

      // Process based on product type
      if (payment.productType === 'BIODATA') {
        await this.processBiodataPurchase(payment);
      } else if (payment.productType === 'SPOTLIGHT') {
        await this.processSpotlightPurchase(payment);
      }

      return { success: true, payment: updatedPayment };
    } catch (error) {
      console.error('Error processing successful payment:', error);
      throw error;
    }
  }

  /**
   * Process biodata purchase
   * @param {Object} payment - The payment object
   * @returns {Promise<void>}
   */
  async processBiodataPurchase(payment) {
    try {
      const metadata = JSON.parse(payment.metadata);
      const { templateId } = metadata;

      // Create user biodata purchase
      await prisma.userBiodata.create({
        data: {
          userId: payment.userId,
          templateId,
          purchaseDate: new Date(),
          pricePaid: payment.amount,
          downloadCount: 0,
          transactionId: payment.paymentId
        }
      });
    } catch (error) {
      console.error('Error processing biodata purchase:', error);
      throw error;
    }
  }

  /**
   * Process spotlight purchase
   * @param {Object} payment - The payment object
   * @returns {Promise<void>}
   */
  async processSpotlightPurchase(payment) {
    try {
      const metadata = JSON.parse(payment.metadata);
      const { spotlightId, quantity, defaultCount = 1 } = metadata;
      const totalCount = quantity * defaultCount;

      // Check if user already has this spotlight
      const existingSpotlight = await prisma.userSpotlight.findFirst({
        where: {
          userId: payment.userId,
          spotlightId,
          availableCount: { gt: 0 }
        }
      });

      if (existingSpotlight) {
        // Update existing spotlight
        await prisma.userSpotlight.update({
          where: { id: existingSpotlight.id },
          data: {
            availableCount: existingSpotlight.availableCount + totalCount,
            pricePaid: existingSpotlight.pricePaid + payment.amount
          }
        });
      } else {
        // Create new spotlight purchase
        await prisma.userSpotlight.create({
          data: {
            userId: payment.userId,
            spotlightId,
            purchaseDate: new Date(),
            pricePaid: payment.amount,
            availableCount: totalCount,
            usedCount: 0,
            transactionId: payment.paymentId
          }
        });
      }
    } catch (error) {
      console.error('Error processing spotlight purchase:', error);
      throw error;
    }
  }
}

module.exports = new PaymentService();
