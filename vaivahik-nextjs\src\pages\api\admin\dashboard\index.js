// API endpoint for admin dashboard
import axios from 'axios';
import { handleApiError } from '@/utils/errorHandler';
import { withAuth } from '@/utils/authHandler';
import { getCache, setCache, deleteCache, CACHE_TTL } from '@/utils/redisClient';

// Backend API URL - adjust this to your actual backend URL
const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3001/api';

// Cache keys
const CACHE_KEYS = {
  DASHBOARD_STATS: 'dashboard:stats',
  DASHBOARD_STATS_LAST_UPDATED: 'dashboard:stats:lastUpdated'
};

async function handler(req, res) {
  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getDashboardData(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    return handleApiError(error, res, 'Dashboard API');
  }
}

// GET /api/admin/dashboard
async function getDashboardData(req, res) {
  try {
    // Check for force refresh parameter
    const forceRefresh = req.query.refresh === 'true';

    // Get the auth token from the request cookies or headers
    const token = req.cookies?.adminToken || req.headers.authorization?.split(' ')[1];

    // If not forcing refresh, try to get data from cache
    if (!forceRefresh) {
      const cachedData = await getCache(CACHE_KEYS.DASHBOARD_STATS);
      const lastUpdated = await getCache(CACHE_KEYS.DASHBOARD_STATS_LAST_UPDATED);

      if (cachedData) {
        console.log('Returning dashboard stats from Redis cache');

        // Add last updated timestamp to the response
        return res.status(200).json({
          ...cachedData,
          _cache: {
            hit: true,
            lastUpdated: lastUpdated || new Date().toISOString()
          }
        });
      }
    } else {
      console.log('Force refreshing dashboard stats');
    }

    // In production, try to fetch from the backend API
    if (process.env.NODE_ENV === 'production') {
      try {
        // Fetch dashboard data from the backend API
        const response = await axios({
          method: 'GET',
          url: `${BACKEND_API_URL}/admin/dashboard`,
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          params: req.query,
          timeout: 10000 // 10 second timeout
        });

        // Cache the response data
        const responseData = response.data;
        if (responseData.success) {
          await setCache(
            CACHE_KEYS.DASHBOARD_STATS,
            responseData,
            CACHE_TTL.DASHBOARD_STATS
          );

          // Store last updated timestamp
          const now = new Date().toISOString();
          await setCache(
            CACHE_KEYS.DASHBOARD_STATS_LAST_UPDATED,
            now,
            CACHE_TTL.DASHBOARD_STATS
          );

          // Add cache metadata to response
          responseData._cache = {
            hit: false,
            lastUpdated: now
          };
        }

        // Return the response from the backend
        return res.status(200).json(responseData);
      } catch (apiError) {
        console.error('Error fetching dashboard data from backend API:', apiError.message);

        // Try to get stale data from cache if available
        const cachedData = await getCache(CACHE_KEYS.DASHBOARD_STATS);
        if (cachedData) {
          console.log('Returning stale dashboard stats from cache due to API error');
          const lastUpdated = await getCache(CACHE_KEYS.DASHBOARD_STATS_LAST_UPDATED) || 'unknown';

          return res.status(200).json({
            ...cachedData,
            _cache: {
              hit: true,
              stale: true,
              lastUpdated
            }
          });
        }

        // In production, return the error if no cache is available
        return res.status(500).json({
          success: false,
          message: 'Failed to fetch dashboard data from backend API.',
          error: apiError.message
        });
      }
    }

    // Generate mock data
    const mockData = {
      success: true,
      // Detailed stats for advanced usage
      userStats: {
        total: 1250,
        premium: 320,
        premiumPercentage: "25.6",
        verified: 980,
        verifiedPercentage: "78.4",
        newLast7Days: 48,
        genderDistribution: {
          male: 720,
          female: 510,
          other: 20
        }
      },
      matchStats: {
        total: 850,
        accepted: 32,
        successRate: "3.8"
      },
      reportStats: {
        total: 25,
        pending: 7
      },
      subscriptionStats: {
        total: 450,
        active: 320,
        activeRate: "71.1",
        totalRevenue: 125000
      },
      // Frontend-friendly format (matches the frontend structure)
      stats: {
        totalUsers: 1250,
        totalUsersGrowth: 15,
        newRegistrations: 48,
        newRegistrationsGrowth: 12,
        successfulMatches: 32,
        successfulMatchesGrowth: 8,
        pendingVerifications: 18,
        pendingVerificationsGrowth: -5,
        reportedProfiles: 7,
        premiumUsers: 320,
        premiumUsersGrowth: 22,
        revenue: 125000,
        revenueGrowth: 18
      }
    };

    // In development, cache the mock data
    await setCache(
      CACHE_KEYS.DASHBOARD_STATS,
      mockData,
      CACHE_TTL.DASHBOARD_STATS
    );

    // Store last updated timestamp
    const now = new Date().toISOString();
    await setCache(
      CACHE_KEYS.DASHBOARD_STATS_LAST_UPDATED,
      now,
      CACHE_TTL.DASHBOARD_STATS
    );

    // Add cache metadata to response
    mockData._cache = {
      hit: false,
      lastUpdated: now,
      environment: 'development'
    };

    // In development or if backend call fails in production, return mock data
    return res.status(200).json(mockData);
  } catch (error) {
    return handleApiError(error, res, 'Get dashboard data');
  }
}

// Export the handler with authentication middleware
export default withAuth(handler, 'ADMIN');
