<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Elegant Floral Biodata</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Quicksand:wght@300;400;500;600&display=swap');
        
        :root {
            --primary-color: #8e44ad; /* Purple */
            --secondary-color: #9b59b6; /* Light purple */
            --accent-color: #e84393; /* Pink */
            --text-color: #333;
            --light-text: #666;
            --border-color: #f0d3f7;
            --light-bg: #fcf5ff;
            --header-font: 'Playfair Display', serif;
            --body-font: 'Quicksand', sans-serif;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--body-font);
            color: var(--text-color);
            line-height: 1.6;
            background-color: white;
            padding: 0;
            margin: 0;
        }
        
        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 30px;
            background-color: white;
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
            position: relative;
            background-image: url('data:image/svg+xml;utf8,<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><path d="M30,10 Q50,0 70,10 Q90,20 90,40 Q90,60 70,70 Q50,80 30,70 Q10,60 10,40 Q10,20 30,10 Z" fill="none" stroke="%23f0d3f7" stroke-width="1" opacity="0.2"/></svg>');
            background-size: 100px 100px;
        }
        
        /* Floral Border */
        .floral-border {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            border: 1px solid var(--border-color);
            pointer-events: none;
            z-index: 1;
            border-radius: 10px;
        }
        
        .floral-corner {
            position: absolute;
            width: 50px;
            height: 50px;
            background-image: url('data:image/svg+xml;utf8,<svg width="50" height="50" viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg"><path d="M10,0 Q25,0 25,25 Q25,50 0,50 Q0,25 10,0 Z" fill="none" stroke="%23e84393" stroke-width="1"/><circle cx="15" cy="15" r="5" fill="%23e84393" opacity="0.3"/><circle cx="10" cy="25" r="3" fill="%23e84393" opacity="0.2"/><circle cx="25" cy="10" r="3" fill="%23e84393" opacity="0.2"/></svg>');
            background-repeat: no-repeat;
        }
        
        .top-left {
            top: 10px;
            left: 10px;
            transform: rotate(0deg);
        }
        
        .top-right {
            top: 10px;
            right: 10px;
            transform: rotate(90deg);
        }
        
        .bottom-left {
            bottom: 10px;
            left: 10px;
            transform: rotate(270deg);
        }
        
        .bottom-right {
            bottom: 10px;
            right: 10px;
            transform: rotate(180deg);
        }
        
        .content-wrapper {
            position: relative;
            z-index: 2;
            padding: 20px;
        }
        
        /* Invocation */
        .invocation {
            text-align: center;
            font-family: var(--header-font);
            color: var(--primary-color);
            padding: 10px 0;
            font-weight: 500;
            font-size: 18px;
            margin-bottom: 25px;
            position: relative;
        }
        
        .invocation:after {
            content: '';
            position: absolute;
            left: 50%;
            bottom: -5px;
            transform: translateX(-50%);
            width: 100px;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--accent-color), transparent);
        }
        
        /* Header Section */
        .header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }
        
        .profile-photo-frame {
            width: 180px;
            height: 220px;
            margin: 0 auto 20px;
            position: relative;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .profile-photo {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .photo-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: 3px solid white;
            box-shadow: 0 0 0 1px var(--accent-color);
            pointer-events: none;
            border-radius: 5px;
        }
        
        .name {
            font-family: var(--header-font);
            font-size: 32px;
            color: var(--primary-color);
            margin-bottom: 5px;
            font-weight: 600;
        }
        
        .tagline {
            font-size: 16px;
            color: var(--light-text);
            margin-bottom: 15px;
            font-style: italic;
        }
        
        .quick-info {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            font-size: 14px;
            background-color: var(--light-bg);
            padding: 5px 15px;
            border-radius: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .info-label {
            font-weight: 600;
            margin-right: 5px;
            color: var(--primary-color);
        }
        
        /* Section Styling */
        .section {
            margin-bottom: 30px;
            position: relative;
        }
        
        .section-title {
            font-family: var(--header-font);
            color: var(--primary-color);
            font-size: 22px;
            padding-bottom: 10px;
            margin-bottom: 15px;
            position: relative;
            text-align: center;
        }
        
        .section-title:after {
            content: '';
            position: absolute;
            left: 50%;
            bottom: 0;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: linear-gradient(to right, var(--secondary-color), var(--accent-color));
        }
        
        .section-content {
            padding: 0 10px;
        }
        
        /* Two Column Layout */
        .two-column {
            display: flex;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .column {
            flex: 1;
        }
        
        /* Details Card */
        .details-card {
            background-color: var(--light-bg);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.03);
        }
        
        /* Details Table */
        .details-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .details-table tr {
            border-bottom: 1px solid rgba(142, 68, 173, 0.1);
        }
        
        .details-table tr:last-child {
            border-bottom: none;
        }
        
        .details-table td {
            padding: 10px 5px;
            vertical-align: top;
        }
        
        .details-table td:first-child {
            width: 40%;
            font-weight: 600;
            color: var(--primary-color);
        }
        
        /* About & Interests */
        .about-section {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.03);
            border: 1px solid var(--border-color);
        }
        
        /* Expectations */
        .expectations {
            background-color: var(--light-bg);
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.03);
            position: relative;
        }
        
        .expectations:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, var(--secondary-color), var(--accent-color));
            border-radius: 10px 0 0 10px;
        }
        
        /* Footer */
        .footer {
            margin-top: 30px;
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
            font-size: 14px;
            color: var(--light-text);
        }
        
        .branding {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 10px;
        }
        
        .brand-logo {
            height: 30px;
            margin-right: 10px;
        }
        
        .brand-name {
            font-weight: 500;
            color: var(--primary-color);
        }
        
        /* Print Styles */
        @media print {
            body {
                background-color: white;
            }
            
            .container {
                box-shadow: none;
                padding: 0;
                max-width: 100%;
            }
            
            @page {
                size: A4;
                margin: 1cm;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="floral-border"></div>
        <div class="floral-corner top-left"></div>
        <div class="floral-corner top-right"></div>
        <div class="floral-corner bottom-left"></div>
        <div class="floral-corner bottom-right"></div>
        
        <div class="content-wrapper">
            <!-- Invocation -->
            <div class="invocation">
                ॥ श्री गणेशाय नमः ॥
            </div>
            
            <!-- Header Section -->
            <div class="header">
                <div class="profile-photo-frame">
                    <img src="{{profilePicture}}" alt="Profile Photo" class="profile-photo">
                    <div class="photo-overlay"></div>
                </div>
                <h1 class="name">{{name}}</h1>
                <p class="tagline">{{tagline}}</p>
                <div class="quick-info">
                    <div class="info-item">
                        <span class="info-label">Age:</span> {{age}} years
                    </div>
                    <div class="info-item">
                        <span class="info-label">Height:</span> {{height}}
                    </div>
                    <div class="info-item">
                        <span class="info-label">Education:</span> {{education}}
                    </div>
                    <div class="info-item">
                        <span class="info-label">Profession:</span> {{occupation}}
                    </div>
                </div>
            </div>
            
            <!-- Two Column Layout for Personal Details and Family Background -->
            <div class="two-column">
                <!-- Personal Details -->
                <div class="column">
                    <div class="section">
                        <h2 class="section-title">Personal Details</h2>
                        <div class="section-content">
                            <div class="details-card">
                                <table class="details-table">
                                    <tr>
                                        <td>Date of Birth</td>
                                        <td>{{dateOfBirth}}</td>
                                    </tr>
                                    <tr>
                                        <td>Birth Time</td>
                                        <td>{{birthTime}}</td>
                                    </tr>
                                    <tr>
                                        <td>Birth Place</td>
                                        <td>{{birthPlace}}</td>
                                    </tr>
                                    <tr>
                                        <td>Religion</td>
                                        <td>{{religion}}</td>
                                    </tr>
                                    <tr>
                                        <td>Caste</td>
                                        <td>{{caste}}</td>
                                    </tr>
                                    <tr>
                                        <td>Sub-caste</td>
                                        <td>{{subCaste}}</td>
                                    </tr>
                                    <tr>
                                        <td>Gotra</td>
                                        <td>{{gotra}}</td>
                                    </tr>
                                    <tr>
                                        <td>Marital Status</td>
                                        <td>{{maritalStatus}}</td>
                                    </tr>
                                    <tr>
                                        <td>Height</td>
                                        <td>{{height}}</td>
                                    </tr>
                                    <tr>
                                        <td>Diet</td>
                                        <td>{{diet}}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Family Background -->
                <div class="column">
                    <div class="section">
                        <h2 class="section-title">Family Background</h2>
                        <div class="section-content">
                            <div class="details-card">
                                <table class="details-table">
                                    <tr>
                                        <td>Father's Name</td>
                                        <td>{{fatherName}}</td>
                                    </tr>
                                    <tr>
                                        <td>Father's Occupation</td>
                                        <td>{{fatherOccupation}}</td>
                                    </tr>
                                    <tr>
                                        <td>Mother's Name</td>
                                        <td>{{motherName}}</td>
                                    </tr>
                                    <tr>
                                        <td>Mother's Occupation</td>
                                        <td>{{motherOccupation}}</td>
                                    </tr>
                                    <tr>
                                        <td>Family Type</td>
                                        <td>{{familyType}}</td>
                                    </tr>
                                    <tr>
                                        <td>Family Status</td>
                                        <td>{{familyStatus}}</td>
                                    </tr>
                                    <tr>
                                        <td>Siblings</td>
                                        <td>{{siblings}}</td>
                                    </tr>
                                    <tr>
                                        <td>Native Place</td>
                                        <td>{{nativePlace}}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Education & Career -->
            <div class="section">
                <h2 class="section-title">Education & Career</h2>
                <div class="section-content">
                    <div class="details-card">
                        <table class="details-table">
                            <tr>
                                <td>Education</td>
                                <td>{{education}}</td>
                            </tr>
                            <tr>
                                <td>Details</td>
                                <td>{{educationDetails}}</td>
                            </tr>
                            <tr>
                                <td>Occupation</td>
                                <td>{{occupation}}</td>
                            </tr>
                            <tr>
                                <td>Company</td>
                                <td>{{company}}</td>
                            </tr>
                            <tr>
                                <td>Annual Income</td>
                                <td>{{annualIncome}}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Two Column Layout for About Me and Expectations -->
            <div class="two-column">
                <!-- About Me -->
                <div class="column">
                    <div class="section">
                        <h2 class="section-title">About Me</h2>
                        <div class="section-content">
                            <div class="about-section">
                                <p>{{aboutMe}}</p>
                                
                                <div style="margin-top: 15px;">
                                    <div style="font-weight: 600; color: var(--primary-color); margin-bottom: 5px;">Hobbies & Interests:</div>
                                    <p>{{hobbies}}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Partner Expectations -->
                <div class="column">
                    <div class="section">
                        <h2 class="section-title">Partner Expectations</h2>
                        <div class="section-content">
                            <div class="expectations">
                                <p>{{partnerPreferences}}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="section">
                <h2 class="section-title">Contact Information</h2>
                <div class="section-content">
                    <div class="details-card">
                        <table class="details-table">
                            <tr>
                                <td>Current Location</td>
                                <td>{{city}}, {{state}}, {{country}}</td>
                            </tr>
                            <tr>
                                <td>Email</td>
                                <td>{{email}}</td>
                            </tr>
                            <tr>
                                <td>Phone</td>
                                <td>{{phone}}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Footer with Branding -->
            <div class="footer">
                <div class="branding">
                    <img src="{{brandLogo}}" alt="Brand Logo" class="brand-logo">
                    <span class="brand-name">{{brandName}}</span>
                </div>
                <p>{{brandTagline}}</p>
                <p>Created on {{createdAt}}</p>
            </div>
        </div>
    </div>
</body>
</html>
