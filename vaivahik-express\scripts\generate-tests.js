/**
 * Test Generation Script
 * 
 * This script generates test files for all API endpoints.
 * It creates both unit tests for controllers and integration tests for routes.
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Configuration
const config = {
  routesDir: path.join(__dirname, '../src/routes'),
  controllersDir: path.join(__dirname, '../src/controllers'),
  testsDir: path.join(__dirname, '../tests')
};

// Create tests directory if it doesn't exist
if (!fs.existsSync(config.testsDir)) {
  fs.mkdirSync(config.testsDir, { recursive: true });
}

// Create unit tests directory if it doesn't exist
const unitTestsDir = path.join(config.testsDir, 'unit');
if (!fs.existsSync(unitTestsDir)) {
  fs.mkdirSync(unitTestsDir, { recursive: true });
}

// Create integration tests directory if it doesn't exist
const integrationTestsDir = path.join(config.testsDir, 'integration');
if (!fs.existsSync(integrationTestsDir)) {
  fs.mkdirSync(integrationTestsDir, { recursive: true });
}

/**
 * Get all route files
 * @returns {Array<string>} Array of route file paths
 */
function getRouteFiles() {
  return glob.sync('**/*.js', { cwd: config.routesDir, absolute: true });
}

/**
 * Get all controller files
 * @returns {Array<string>} Array of controller file paths
 */
function getControllerFiles() {
  return glob.sync('**/*.js', { cwd: config.controllersDir, absolute: true });
}

/**
 * Extract routes from file content
 * @param {string} content - File content
 * @returns {Array<Object>} Array of route objects
 */
function extractRoutes(content) {
  const routes = [];
  const routeRegex = /router\.(get|post|put|patch|delete)\(\s*['"]([^'"]+)['"]\s*,\s*([^)]+)\)/g;
  
  let match;
  while ((match = routeRegex.exec(content)) !== null) {
    const method = match[1];
    const path = match[2];
    const handlers = match[3].split(',').map(h => h.trim());
    
    // Extract controller function name
    const controllerFn = handlers[handlers.length - 1];
    
    routes.push({
      method,
      path,
      handlers,
      controllerFn
    });
  }
  
  return routes;
}

/**
 * Extract controller functions from file content
 * @param {string} content - File content
 * @returns {Array<Object>} Array of controller function objects
 */
function extractControllerFunctions(content) {
  const functions = [];
  const functionRegex = /(?:const|function)\s+([a-zA-Z0-9_]+)\s*=\s*async\s*\(\s*req\s*,\s*res\s*,\s*next\s*\)\s*=>\s*{/g;
  
  let match;
  while ((match = functionRegex.exec(content)) !== null) {
    functions.push({
      name: match[1],
      signature: match[0]
    });
  }
  
  return functions;
}

/**
 * Generate unit test for a controller file
 * @param {string} filePath - Path to the controller file
 */
function generateControllerTest(filePath) {
  // Read file
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Extract controller functions
  const functions = extractControllerFunctions(content);
  
  // Skip if no functions found
  if (functions.length === 0) {
    console.log(`Skipping ${path.relative(path.join(__dirname, '..'), filePath)} (no controller functions found)`);
    return;
  }
  
  // Generate test file path
  const relativePath = path.relative(config.controllersDir, filePath);
  const testFilePath = path.join(unitTestsDir, relativePath.replace('.js', '.test.js'));
  
  // Create directory if it doesn't exist
  const testDir = path.dirname(testFilePath);
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
  }
  
  // Skip if test file already exists
  if (fs.existsSync(testFilePath)) {
    console.log(`Skipping ${path.relative(path.join(__dirname, '..'), testFilePath)} (already exists)`);
    return;
  }
  
  // Generate test content
  const testContent = generateControllerTestContent(filePath, functions);
  
  // Write test file
  fs.writeFileSync(testFilePath, testContent);
  
  console.log(`Generated ${path.relative(path.join(__dirname, '..'), testFilePath)}`);
}

/**
 * Generate integration test for a route file
 * @param {string} filePath - Path to the route file
 */
function generateRouteTest(filePath) {
  // Read file
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Extract routes
  const routes = extractRoutes(content);
  
  // Skip if no routes found
  if (routes.length === 0) {
    console.log(`Skipping ${path.relative(path.join(__dirname, '..'), filePath)} (no routes found)`);
    return;
  }
  
  // Generate test file path
  const relativePath = path.relative(config.routesDir, filePath);
  const testFilePath = path.join(integrationTestsDir, relativePath.replace('.js', '.test.js'));
  
  // Create directory if it doesn't exist
  const testDir = path.dirname(testFilePath);
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
  }
  
  // Skip if test file already exists
  if (fs.existsSync(testFilePath)) {
    console.log(`Skipping ${path.relative(path.join(__dirname, '..'), testFilePath)} (already exists)`);
    return;
  }
  
  // Generate test content
  const testContent = generateRouteTestContent(filePath, routes);
  
  // Write test file
  fs.writeFileSync(testFilePath, testContent);
  
  console.log(`Generated ${path.relative(path.join(__dirname, '..'), testFilePath)}`);
}

/**
 * Generate controller test content
 * @param {string} filePath - Path to the controller file
 * @param {Array<Object>} functions - Array of controller function objects
 * @returns {string} Test content
 */
function generateControllerTestContent(filePath, functions) {
  const controllerName = path.basename(filePath, '.js');
  const relativePath = path.relative(path.join(__dirname, '..'), filePath).replace(/\\/g, '/');
  
  let content = `/**
 * Unit Tests for ${controllerName}
 */

const { PrismaClient } = require('@prisma/client');
const ${controllerName} = require('../../${relativePath}');
const apiResponse = require('../../src/utils/apiResponse');

// Mock dependencies
jest.mock('@prisma/client', () => {
  const mockPrismaClient = {
    user: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn()
    },
    profile: {
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      upsert: jest.fn()
    },
    // Add other models as needed
    $disconnect: jest.fn()
  };
  
  return {
    PrismaClient: jest.fn(() => mockPrismaClient)
  };
});

jest.mock('../../src/utils/apiResponse', () => ({
  success: jest.fn().mockReturnValue({ json: 'success' }),
  error: jest.fn().mockReturnValue({ json: 'error' }),
  created: jest.fn().mockReturnValue({ json: 'created' }),
  noContent: jest.fn().mockReturnValue({ json: 'noContent' }),
  badRequest: jest.fn().mockReturnValue({ json: 'badRequest' }),
  unauthorized: jest.fn().mockReturnValue({ json: 'unauthorized' }),
  forbidden: jest.fn().mockReturnValue({ json: 'forbidden' }),
  notFound: jest.fn().mockReturnValue({ json: 'notFound' }),
  validationError: jest.fn().mockReturnValue({ json: 'validationError' }),
  conflict: jest.fn().mockReturnValue({ json: 'conflict' }),
  serverError: jest.fn().mockReturnValue({ json: 'serverError' })
}));

describe('${controllerName}', () => {
  let prisma;
  let req;
  let res;
  let next;
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Setup prisma mock
    prisma = new PrismaClient();
    
    // Setup request mock
    req = {
      params: {},
      query: {},
      body: {},
      user: { id: 'user-1', role: 'USER' }
    };
    
    // Setup response mock
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
      send: jest.fn(),
      end: jest.fn()
    };
    
    // Setup next mock
    next = jest.fn();
  });
  
`;
  
  // Generate tests for each function
  for (const fn of functions) {
    content += generateControllerFunctionTest(fn);
  }
  
  // Close describe block
  content += '});\n';
  
  return content;
}

/**
 * Generate controller function test
 * @param {Object} fn - Controller function object
 * @returns {string} Test content
 */
function generateControllerFunctionTest(fn) {
  return `  describe('${fn.name}', () => {
    it('should return success response with data', async () => {
      // Mock data
      const mockData = { id: 'data-1', name: 'Test Data' };
      
      // Mock prisma response
      prisma.user.findUnique.mockResolvedValue(mockData);
      
      // Call function
      await ${fn.name}(req, res, next);
      
      // Assert response
      expect(apiResponse.success).toHaveBeenCalledWith(res, expect.any(String), expect.anything());
      expect(next).not.toHaveBeenCalled();
    });
    
    it('should handle errors and call next', async () => {
      // Mock error
      const mockError = new Error('Test error');
      
      // Mock prisma response
      prisma.user.findUnique.mockRejectedValue(mockError);
      
      // Call function
      await ${fn.name}(req, res, next);
      
      // Assert response
      expect(next).toHaveBeenCalledWith(mockError);
    });
  });
  
`;
}

/**
 * Generate route test content
 * @param {string} filePath - Path to the route file
 * @param {Array<Object>} routes - Array of route objects
 * @returns {string} Test content
 */
function generateRouteTestContent(filePath, routes) {
  const routeName = path.basename(filePath, '.js');
  const relativePath = path.relative(path.join(__dirname, '..'), filePath).replace(/\\/g, '/');
  const apiPath = `/api/v1${getApiPath(filePath)}`;
  
  let content = `/**
 * Integration Tests for ${routeName} routes
 */

const request = require('supertest');
const express = require('express');
const router = require('../../${relativePath}');
const { authenticateToken } = require('../../src/middleware/auth');
const { validate } = require('../../src/middleware/validator');

// Mock dependencies
jest.mock('../../src/middleware/auth', () => ({
  authenticateToken: jest.fn((req, res, next) => {
    req.user = { id: 'user-1', role: 'USER' };
    next();
  })
}));

jest.mock('../../src/middleware/validator', () => ({
  validate: jest.fn((req, res, next) => next())
}));

// Create Express app for testing
const app = express();
app.use(express.json());
app.use('${apiPath}', router);

describe('${routeName} routes', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
  });
  
`;
  
  // Generate tests for each route
  for (const route of routes) {
    content += generateRouteTest(route, apiPath);
  }
  
  // Close describe block
  content += '});\n';
  
  return content;
}

/**
 * Generate route test
 * @param {Object} route - Route object
 * @param {string} apiPath - API path
 * @returns {string} Test content
 */
function generateRouteTest(route, apiPath) {
  const method = route.method.toLowerCase();
  const path = route.path;
  const controllerFn = route.controllerFn;
  
  // Replace path parameters with values
  const testPath = path.replace(/:([^/]+)/g, 'test-$1');
  
  return `  describe('${method.toUpperCase()} ${path}', () => {
    it('should return 200 OK with data', async () => {
      // Make request
      const response = await request(app)
        .${method}('${apiPath}${testPath}')
        .set('Authorization', 'Bearer test-token')
        .send({});
      
      // Assert response
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
    });
    
    it('should handle validation errors', async () => {
      // Mock validation error
      validate.mockImplementationOnce((req, res, next) => {
        res.status(422).json({
          success: false,
          message: 'Validation error',
          errors: { field: 'Error message' }
        });
      });
      
      // Make request
      const response = await request(app)
        .${method}('${apiPath}${testPath}')
        .set('Authorization', 'Bearer test-token')
        .send({});
      
      // Assert response
      expect(response.status).toBe(422);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('errors');
    });
    
    it('should handle authentication errors', async () => {
      // Mock authentication error
      authenticateToken.mockImplementationOnce((req, res, next) => {
        res.status(401).json({
          success: false,
          message: 'Unauthorized'
        });
      });
      
      // Make request
      const response = await request(app)
        .${method}('${apiPath}${testPath}')
        .send({});
      
      // Assert response
      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('success', false);
    });
  });
  
`;
}

/**
 * Get API path from file path
 * @param {string} filePath - Path to the route file
 * @returns {string} API path
 */
function getApiPath(filePath) {
  const relativePath = path.relative(config.routesDir, filePath);
  const parts = relativePath.split(path.sep);
  
  // Remove file extension
  parts[parts.length - 1] = parts[parts.length - 1].replace('.js', '');
  
  // Handle index.js files
  if (parts[parts.length - 1] === 'index') {
    parts.pop();
  }
  
  return '/' + parts.join('/');
}

/**
 * Main function
 */
function main() {
  console.log('Generating tests for all endpoints...');
  
  // Get all controller files
  const controllerFiles = getControllerFiles();
  
  // Generate unit tests for each controller
  for (const filePath of controllerFiles) {
    generateControllerTest(filePath);
  }
  
  // Get all route files
  const routeFiles = getRouteFiles();
  
  // Generate integration tests for each route
  for (const filePath of routeFiles) {
    generateRouteTest(filePath);
  }
  
  console.log('Done!');
}

// Run main function
main();
