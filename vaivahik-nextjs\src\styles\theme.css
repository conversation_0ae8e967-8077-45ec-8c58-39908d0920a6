/* Vaivahik Global Theme System
 * This file defines the global design system for the entire website
 * All components should use these variables for consistent styling
 */

:root {
  /* Core Colors */
  --primary-color: #FF5F6D; /* Coral Pink */
  --primary-light: #FFC371; /* Light Orange */
  --secondary-color: #8A2BE2; /* Blue Violet */
  --secondary-light: #9370DB; /* Medium Purple */
  --accent-color: #FFD700; /* Gold */

  /* Backgrounds & Text */
  --dark-color: #2D3047; /* Deep Blue/Gray */
  --light-color: #F8F9FA; /* Very Light Gray */
  --light-color-alt: #F0F2F5; /* Slightly different light gray for alternation */
  --white: #FFFFFF;
  --text-color-dark: #333;
  --text-color-medium: #555;
  --text-color-light: #F0F0F0;
  --text-color-light-muted: #B0B0B0;

  /* Gradients */
  --primary-gradient: linear-gradient(135deg, #FF5F6D 0%, #FFC371 100%);
  --secondary-gradient: linear-gradient(135deg, #8A2BE2 0%, #9370DB 100%);
  --subtle-white-gradient: linear-gradient(180deg, var(--white) 0%, #fcfdff 100%);
  --subtle-light-gradient: linear-gradient(180deg, var(--light-color) 0%, #f0f2f5 100%);

  /* Shadows & Effects */
  --shadow-soft: 0 5px 15px rgba(0,0,0,0.05);
  --shadow-medium: 0 10px 25px rgba(0,0,0,0.1);
  --shadow-hard: 0 15px 35px rgba(0,0,0,0.15);
  --transition-smooth: all 0.3s ease;
  --border-radius-small: 10px;
  --border-radius-medium: 20px;
  --border-radius-large: 25px;

  /* Typography */
  --font-primary: 'Montserrat', sans-serif;
  --font-secondary: 'Playfair Display', serif;
  --font-size-xs: 0.75rem;   /* 12px */
  --font-size-sm: 0.875rem;  /* 14px */
  --font-size-md: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;  /* 18px */
  --font-size-xl: 1.25rem;   /* 20px */
  --font-size-2xl: 1.5rem;   /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem;  /* 36px */
  --font-size-5xl: 3rem;     /* 48px */

  /* Spacing */
  --spacing-xs: 0.25rem;  /* 4px */
  --spacing-sm: 0.5rem;   /* 8px */
  --spacing-md: 1rem;     /* 16px */
  --spacing-lg: 1.5rem;   /* 24px */
  --spacing-xl: 2rem;     /* 32px */
  --spacing-2xl: 3rem;    /* 48px */
  --spacing-3xl: 4rem;    /* 64px */

  /* Status Colors */
  --success-color: #10B981;
  --warning-color: #F59E0B;
  --error-color: #EF4444;
  --info-color: #3B82F6;

  /* Form Elements */
  --input-bg: var(--white);
  --input-border: rgba(0,0,0,0.1);
  --input-border-focus: var(--primary-color);
  --input-shadow-focus: 0 0 0 3px rgba(255, 95, 109, 0.2);
  --input-text: var(--text-color-dark);
  --input-placeholder: #9CA3AF;
  --input-radius: 12px;
  --input-padding: 12px 16px;

  /* Z-index Layers */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;

  /* Animation Durations */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
}

/* Dark Mode Theme */
.dark-mode {
  --primary-color: #FF5F6D;
  --primary-light: #FFC371;
  --secondary-color: #9370DB;
  --secondary-light: #B19CD9;
  --accent-color: #FFD700;
  
  --dark-color: #1A1A2E;
  --light-color: #2A2A3C;
  --light-color-alt: #252538;
  --white: #1A1A2E;
  
  --text-color-dark: #F0F0F0;
  --text-color-medium: #D0D0D0;
  --text-color-light: #F0F0F0;
  --text-color-light-muted: #A0A0A0;
  
  --primary-gradient: linear-gradient(135deg, #FF5F6D 0%, #FFC371 100%);
  --secondary-gradient: linear-gradient(135deg, #8A2BE2 0%, #9370DB 100%);
  --subtle-white-gradient: linear-gradient(180deg, #1A1A2E 0%, #1E1E35 100%);
  --subtle-light-gradient: linear-gradient(180deg, #2A2A3C 0%, #252538 100%);
  
  --shadow-soft: 0 5px 15px rgba(0,0,0,0.2);
  --shadow-medium: 0 10px 25px rgba(0,0,0,0.25);
  --shadow-hard: 0 15px 35px rgba(0,0,0,0.3);
  
  --input-bg: #252538;
  --input-border: rgba(255,255,255,0.1);
  --input-text: var(--text-color-light);
  --input-placeholder: #8A8A9A;
}

/* Utility Classes */
.text-gradient-primary {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-secondary {
  background: var(--secondary-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.bg-gradient-primary {
  background: var(--primary-gradient);
}

.bg-gradient-secondary {
  background: var(--secondary-gradient);
}

/* Animation Classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}
