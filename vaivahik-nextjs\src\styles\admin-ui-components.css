/*
 * Standardized UI Components for Admin Panel
 * This file contains all the standardized UI components used across the admin panel
 */

/* ===== Variables ===== */
:root {
  /* Primary Colors */
  --primary: #5e35b1; /* Deep Purple */
  --primary-light: #7e57c2;
  --primary-dark: #4527a0;
  --secondary: #ff5722; /* Deep Orange */
  --secondary-light: #ff8a50;
  --secondary-dark: #c41c00;

  /* Status Colors */
  --success: #4caf50; /* Green */
  --warning: #ff9800; /* Amber */
  --danger: #f44336; /* Red */
  --info: #2196f3; /* Blue */

  /* Text Colors */
  --text-dark: #333;
  --text-light: #f5f5f5;
  --text-muted: #6c757d;

  /* Background Colors */
  --bg-light: #f9f9f9;
  --bg-white: #ffffff;
  --bg-dark: #121212;
  --bg-dark-light: #1e1e1e;

  /* Border & Shadow */
  --border-color: #e0e0e0;
  --border-radius: 8px;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);

  /* Layout */
  --sidebar-width: 260px;
  --sidebar-collapsed-width: 70px;
  --topbar-height: 60px;
  --content-padding: 20px;

  /* Transitions */
  --transition-speed: 0.3s;
}

/* ===== Dark Mode Variables ===== */
body.dark-mode {
  --primary: #7c4dff;
  --primary-light: #a387e9;
  --primary-dark: #4527a0;
  --secondary: #ff9800;
  --secondary-light: #ffb74d;
  --secondary-dark: #f57c00;

  --text-dark: #e4e4e4;
  --text-light: #23272f;
  --text-muted: #a0aec0;

  --bg-light: #23272f;
  --bg-white: #1a1d24;
  --bg-dark: #121212;
  --bg-dark-light: #1e1e1e;

  --border-color: #33384a;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* ===== Button Styles ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  border: 1px solid transparent;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
}

.btn:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

.btn-sm {
  padding: 5px 10px;
  font-size: 0.85rem;
}

.btn-lg {
  padding: 10px 20px;
  font-size: 1rem;
}

.btn-primary {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-secondary {
  background-color: #f5f5f5;
  color: var(--text-dark);
  border-color: #ddd;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #e0e0e0;
  border-color: #ccc;
}

.btn-success {
  background-color: var(--success);
  color: white;
  border-color: var(--success);
}

.btn-success:hover:not(:disabled) {
  background-color: #3d8b40;
  border-color: #3d8b40;
}

.btn-danger {
  background-color: var(--danger);
  color: white;
  border-color: var(--danger);
}

.btn-danger:hover:not(:disabled) {
  background-color: #d32f2f;
  border-color: #d32f2f;
}

.btn-warning {
  background-color: var(--warning);
  color: white;
  border-color: var(--warning);
}

.btn-warning:hover:not(:disabled) {
  background-color: #f57c00;
  border-color: #f57c00;
}

.btn-info {
  background-color: var(--info);
  color: white;
  border-color: var(--info);
}

.btn-info:hover:not(:disabled) {
  background-color: #1976d2;
  border-color: #1976d2;
}

.btn-outline-primary {
  background-color: transparent;
  color: var(--primary);
  border-color: var(--primary);
}

.btn-outline-primary:hover:not(:disabled) {
  background-color: var(--primary);
  color: white;
}

.btn-outline-secondary {
  background-color: transparent;
  color: var(--text-dark);
  border-color: #ddd;
}

.btn-outline-secondary:hover:not(:disabled) {
  background-color: #e0e0e0;
}

.btn-outline-success {
  background-color: transparent;
  color: var(--success);
  border-color: var(--success);
}

.btn-outline-success:hover:not(:disabled) {
  background-color: var(--success);
  color: white;
}

.btn-outline-danger {
  background-color: transparent;
  color: var(--danger);
  border-color: var(--danger);
}

.btn-outline-danger:hover:not(:disabled) {
  background-color: var(--danger);
  color: white;
}

.btn-link {
  background-color: transparent;
  color: var(--primary);
  border-color: transparent;
  padding: 0;
  text-decoration: none;
}

.btn-link:hover:not(:disabled) {
  text-decoration: underline;
  color: var(--primary-dark);
}

.btn-icon {
  padding: 8px;
  border-radius: 50%;
}

.btn-group {
  display: inline-flex;
  border-radius: var(--border-radius);
}

.btn-group .btn {
  border-radius: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
}

.btn-group .btn:last-child {
  border-top-right-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
}

/* Dark mode button styles */
body.dark-mode .btn-secondary {
  background-color: #2d3748;
  color: var(--text-dark);
  border-color: #4a5568;
}

body.dark-mode .btn-secondary:hover:not(:disabled) {
  background-color: #4a5568;
}

body.dark-mode .btn-outline-secondary {
  color: var(--text-dark);
  border-color: #4a5568;
}

body.dark-mode .btn-outline-secondary:hover:not(:disabled) {
  background-color: #4a5568;
}

/* ===== Form Controls ===== */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-dark);
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 0.9rem;
  line-height: 1.5;
  color: var(--text-dark);
  background-color: var(--bg-white);
  background-clip: padding-box;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: border-color var(--transition-speed) ease-in-out;
}

.form-control:focus {
  border-color: var(--primary);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(94, 53, 177, 0.25);
}

.form-control:disabled {
  background-color: #e9ecef;
  opacity: 1;
}

/* Dark mode form styles */
body.dark-mode .form-label {
  color: var(--text-dark);
}

body.dark-mode .form-control {
  color: var(--text-dark);
  background-color: #2d3748;
  border-color: #4a5568;
}

body.dark-mode .form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(124, 77, 255, 0.25);
}

body.dark-mode .form-control:disabled {
  background-color: #1a202c;
}

/* ===== Table Styles ===== */
.table-container {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid var(--border-color);
  overflow-x: auto;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
  flex-wrap: wrap;
  gap: 1rem;
}

.table-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 600px;
}

.data-table th,
.data-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  vertical-align: middle;
}

.data-table th {
  font-weight: 600;
  color: var(--text-dark);
  background-color: rgba(0, 0, 0, 0.02);
}

.data-table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.data-table .user-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.data-table .user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-light);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
}

.data-table .user-info {
  display: flex;
  flex-direction: column;
}

.data-table .user-name {
  font-weight: 600;
  color: var(--text-dark);
}

.data-table .user-details {
  font-size: 0.85rem;
  color: var(--text-muted);
}

.data-table .action-cell {
  display: flex;
  gap: 0.5rem;
}

/* Status badges */
.status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge-success {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success);
}

.status-badge-warning {
  background-color: rgba(255, 152, 0, 0.1);
  color: var(--warning);
}

.status-badge-danger {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--danger);
}

.status-badge-info {
  background-color: rgba(33, 150, 243, 0.1);
  color: var(--info);
}

.status-badge-primary {
  background-color: rgba(94, 53, 177, 0.1);
  color: var(--primary);
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.pagination {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pagination-info {
  font-size: 0.9rem;
  color: var(--text-muted);
}

.pagination-btn {
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  background-color: var(--bg-white);
  border: 1px solid var(--border-color);
  color: var(--text-dark);
  cursor: pointer;
  transition: all var(--transition-speed) ease;
}

.pagination-btn:hover:not(:disabled) {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-btn.active {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

/* Dark mode table styles */
body.dark-mode .table-container {
  background-color: var(--bg-white);
  border-color: var(--border-color);
}

body.dark-mode .table-title {
  color: var(--text-dark);
}

body.dark-mode .data-table th {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-dark);
}

body.dark-mode .data-table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

body.dark-mode .data-table th,
body.dark-mode .data-table td {
  border-color: var(--border-color);
}

body.dark-mode .pagination-btn {
  background-color: var(--bg-white);
  border-color: var(--border-color);
  color: var(--text-dark);
}

body.dark-mode .pagination-container {
  border-color: var(--border-color);
}

/* ===== Card Styles ===== */
.card {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.card-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background-color: rgba(0, 0, 0, 0.02);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-dark);
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  background-color: rgba(0, 0, 0, 0.02);
}

/* Dark mode card styles */
body.dark-mode .card {
  background-color: var(--bg-white);
  border-color: var(--border-color);
}

body.dark-mode .card-header,
body.dark-mode .card-footer {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: var(--border-color);
}

body.dark-mode .card-title {
  color: var(--text-dark);
}

/* ===== Modal Styles ===== */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1050;
}

.modal.show {
  display: flex;
}

.modal-content {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-dark);
}

.close-modal {
  background: none;
  border: none;
  font-size: 1.5rem;
  line-height: 1;
  color: var(--text-muted);
  cursor: pointer;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

/* Dark mode modal styles */
body.dark-mode .modal-content {
  background-color: var(--bg-white);
}

body.dark-mode .modal-header,
body.dark-mode .modal-footer {
  border-color: var(--border-color);
}

body.dark-mode .modal-title {
  color: var(--text-dark);
}

body.dark-mode .close-modal {
  color: var(--text-muted);
}

/* ===== Alert Styles ===== */
.alert {
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: var(--border-radius);
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.alert-icon {
  font-size: 1.25rem;
  line-height: 1;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.alert-message {
  margin: 0;
}

.alert-success {
  background-color: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.2);
  color: var(--success);
}

.alert-warning {
  background-color: rgba(255, 152, 0, 0.1);
  border-color: rgba(255, 152, 0, 0.2);
  color: var(--warning);
}

.alert-danger {
  background-color: rgba(244, 67, 54, 0.1);
  border-color: rgba(244, 67, 54, 0.2);
  color: var(--danger);
}

.alert-info {
  background-color: rgba(33, 150, 243, 0.1);
  border-color: rgba(33, 150, 243, 0.2);
  color: var(--info);
}

/* ===== Tabs Styles ===== */
.tab-container {
  margin-bottom: 1.5rem;
}

.tab-nav {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 1.5rem;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
}

.tab-nav::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

.tab-nav-item {
  padding: 0.75rem 1.25rem;
  border-bottom: 3px solid transparent;
  font-weight: 500;
  color: var(--text-muted);
  cursor: pointer;
  white-space: nowrap;
  transition: all var(--transition-speed) ease;
}

.tab-nav-item:hover {
  color: var(--primary);
}

.tab-nav-item.active {
  color: var(--primary);
  border-bottom-color: var(--primary);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Dark mode tabs styles */
body.dark-mode .tab-nav {
  border-color: var(--border-color);
}

body.dark-mode .tab-nav-item {
  color: var(--text-muted);
}

body.dark-mode .tab-nav-item:hover,
body.dark-mode .tab-nav-item.active {
  color: var(--primary);
}

/* ===== Loading Spinner ===== */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-weight: 500;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.loading-subtext {
  color: var(--text-muted);
  font-size: 0.9rem;
}

/* Dark mode loading styles */
body.dark-mode .loading-spinner {
  border-color: rgba(255, 255, 255, 0.1);
  border-top-color: var(--primary);
}

body.dark-mode .loading-text {
  color: var(--text-dark);
}

/* ===== Empty State ===== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1.5rem;
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
  color: var(--text-muted);
  margin-bottom: 1rem;
}

.empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.empty-message {
  color: var(--text-muted);
  margin-bottom: 1.5rem;
  max-width: 500px;
}

/* ===== Responsive Utilities ===== */
/* Mobile first approach */
.d-flex {
  display: flex !important;
}

.flex-column {
  flex-direction: column !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.align-items-center {
  align-items: center !important;
}

.gap-1 {
  gap: 0.25rem !important;
}

.gap-2 {
  gap: 0.5rem !important;
}

.gap-3 {
  gap: 1rem !important;
}

.mb-1 {
  margin-bottom: 0.25rem !important;
}

.mb-2 {
  margin-bottom: 0.5rem !important;
}

.mb-3 {
  margin-bottom: 1rem !important;
}

.mb-4 {
  margin-bottom: 1.5rem !important;
}

.mt-1 {
  margin-top: 0.25rem !important;
}

.mt-2 {
  margin-top: 0.5rem !important;
}

.mt-3 {
  margin-top: 1rem !important;
}

.mt-4 {
  margin-top: 1.5rem !important;
}

.p-0 {
  padding: 0 !important;
}

.p-1 {
  padding: 0.25rem !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.p-3 {
  padding: 1rem !important;
}

.p-4 {
  padding: 1.5rem !important;
}

.w-100 {
  width: 100% !important;
}

.h-100 {
  height: 100% !important;
}

.text-center {
  text-align: center !important;
}

.text-right {
  text-align: right !important;
}

.text-muted {
  color: var(--text-muted) !important;
}

.text-primary {
  color: var(--primary) !important;
}

.text-success {
  color: var(--success) !important;
}

.text-danger {
  color: var(--danger) !important;
}

.text-warning {
  color: var(--warning) !important;
}

.text-info {
  color: var(--info) !important;
}

.bg-white {
  background-color: var(--bg-white) !important;
}

.bg-light {
  background-color: var(--bg-light) !important;
}

.bg-primary {
  background-color: var(--primary) !important;
  color: white !important;
}

.bg-success {
  background-color: var(--success) !important;
  color: white !important;
}

.bg-danger {
  background-color: var(--danger) !important;
  color: white !important;
}

.bg-warning {
  background-color: var(--warning) !important;
  color: white !important;
}

.bg-info {
  background-color: var(--info) !important;
  color: white !important;
}

.rounded {
  border-radius: var(--border-radius) !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.border {
  border: 1px solid var(--border-color) !important;
}

.shadow-sm {
  box-shadow: var(--shadow-sm) !important;
}

.shadow-md {
  box-shadow: var(--shadow-md) !important;
}

.shadow-lg {
  box-shadow: var(--shadow-lg) !important;
}

/* Responsive breakpoints */
/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) {
  .d-sm-flex {
    display: flex !important;
  }

  .d-sm-none {
    display: none !important;
  }

  .d-sm-block {
    display: block !important;
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
  .d-md-flex {
    display: flex !important;
  }

  .d-md-none {
    display: none !important;
  }

  .d-md-block {
    display: block !important;
  }

  .flex-md-row {
    flex-direction: row !important;
  }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
  .d-lg-flex {
    display: flex !important;
  }

  .d-lg-none {
    display: none !important;
  }

  .d-lg-block {
    display: block !important;
  }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  .d-xl-flex {
    display: flex !important;
  }

  .d-xl-none {
    display: none !important;
  }

  .d-xl-block {
    display: block !important;
  }
}

/* Mobile-specific styles */
@media (max-width: 767px) {
  .table-container {
    padding: 1rem;
  }

  .card-header, .card-body, .card-footer {
    padding: 1rem;
  }

  .modal-content {
    width: 95%;
  }

  .tab-nav-item {
    padding: 0.5rem 1rem;
  }

  .btn-group {
    flex-direction: column;
  }

  .btn-group .btn {
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
  }

  .pagination-container {
    flex-direction: column;
    gap: 1rem;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .table-actions {
    width: 100%;
    justify-content: space-between;
  }

  .data-table .action-cell {
    flex-wrap: wrap;
  }
}
