<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cultural Grace Biodata</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Amita:wght@400;700&family=Lora:wght@400;500;600;700&display=swap');
        
        :root {
            --primary-color: #800020; /* Burgundy */
            --secondary-color: #008080; /* Teal */
            --accent-color: #b8860b; /* Dark goldenrod */
            --text-color: #333;
            --light-text: #666;
            --border-color: #ddd;
            --light-bg: #f9f9f9;
            --cream-color: #fff8e7;
            --header-font: 'Amita', cursive;
            --body-font: 'Lora', serif;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--body-font);
            color: var(--text-color);
            line-height: 1.6;
            background-color: white;
            padding: 0;
            margin: 0;
        }
        
        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20px;
            background-color: var(--cream-color);
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            position: relative;
            background-image: url('data:image/svg+xml;utf8,<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><path d="M0,0 L100,0 L100,100 L0,100 Z" fill="none" stroke="%23b8860b" stroke-width="0.5" opacity="0.1"/><path d="M25,25 L75,25 L75,75 L25,75 Z" fill="none" stroke="%23b8860b" stroke-width="0.5" opacity="0.1"/></svg>');
            background-size: 100px 100px;
        }
        
        /* Ornamental Border */
        .ornamental-border {
            position: absolute;
            top: 15px;
            left: 15px;
            right: 15px;
            bottom: 15px;
            border: 2px solid var(--accent-color);
            pointer-events: none;
            z-index: 1;
        }
        
        .ornamental-corner {
            position: absolute;
            width: 30px;
            height: 30px;
            z-index: 2;
        }
        
        .top-left {
            top: 10px;
            left: 10px;
            border-top: 3px solid var(--accent-color);
            border-left: 3px solid var(--accent-color);
        }
        
        .top-right {
            top: 10px;
            right: 10px;
            border-top: 3px solid var(--accent-color);
            border-right: 3px solid var(--accent-color);
        }
        
        .bottom-left {
            bottom: 10px;
            left: 10px;
            border-bottom: 3px solid var(--accent-color);
            border-left: 3px solid var(--accent-color);
        }
        
        .bottom-right {
            bottom: 10px;
            right: 10px;
            border-bottom: 3px solid var(--accent-color);
            border-right: 3px solid var(--accent-color);
        }
        
        .content-wrapper {
            position: relative;
            z-index: 2;
            padding: 20px;
        }
        
        /* Invocation */
        .invocation {
            text-align: center;
            font-family: var(--header-font);
            color: var(--primary-color);
            padding: 15px 0;
            font-weight: 700;
            font-size: 22px;
            margin-bottom: 25px;
            background-color: rgba(255, 248, 231, 0.8);
            border-bottom: 1px solid var(--accent-color);
            position: relative;
        }
        
        .invocation:after {
            content: '';
            position: absolute;
            left: 50%;
            bottom: -10px;
            transform: translateX(-50%);
            width: 100px;
            height: 1px;
            background-color: var(--accent-color);
        }
        
        /* Header Section */
        .header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid var(--primary-color);
            position: relative;
        }
        
        .header:after {
            content: '';
            position: absolute;
            left: 50%;
            bottom: -8px;
            transform: translateX(-50%);
            width: 50px;
            height: 15px;
            background-image: url('data:image/svg+xml;utf8,<svg width="50" height="15" viewBox="0 0 50 15" xmlns="http://www.w3.org/2000/svg"><path d="M0 7.5 L25 0 L50 7.5 L25 15 Z" fill="%23800020"/></svg>');
            background-repeat: no-repeat;
        }
        
        .profile-photo-container {
            width: 180px;
            margin-right: 30px;
            position: relative;
        }
        
        .profile-photo {
            width: 100%;
            height: auto;
            border: 5px solid white;
            box-shadow: 0 0 0 1px var(--accent-color);
        }
        
        .photo-frame {
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border: 1px solid var(--accent-color);
            pointer-events: none;
        }
        
        .header-content {
            flex: 1;
        }
        
        .name {
            font-family: var(--header-font);
            font-size: 32px;
            color: var(--primary-color);
            margin-bottom: 5px;
            font-weight: 700;
        }
        
        .tagline {
            font-size: 16px;
            color: var(--light-text);
            margin-bottom: 15px;
            font-style: italic;
        }
        
        .quick-info {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            font-size: 14px;
            background-color: rgba(255, 248, 231, 0.8);
            padding: 5px 15px;
            border-radius: 3px;
            border: 1px solid var(--accent-color);
        }
        
        .info-label {
            font-weight: 600;
            margin-right: 5px;
            color: var(--primary-color);
        }
        
        /* Section Styling */
        .section {
            margin-bottom: 30px;
            position: relative;
        }
        
        .section-title {
            font-family: var(--header-font);
            color: var(--primary-color);
            font-size: 22px;
            padding-bottom: 10px;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--accent-color);
            position: relative;
        }
        
        .section-title:after {
            content: '';
            position: absolute;
            left: 0;
            bottom: -1px;
            width: 60px;
            height: 3px;
            background-color: var(--primary-color);
        }
        
        .section-content {
            padding: 0 10px;
        }
        
        /* Two Column Layout */
        .two-column {
            display: flex;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .column {
            flex: 1;
        }
        
        /* Cultural Card */
        .cultural-card {
            background-color: rgba(255, 248, 231, 0.8);
            border: 1px solid var(--accent-color);
            padding: 20px;
            margin-bottom: 20px;
            position: relative;
        }
        
        .cultural-card:before {
            content: '';
            position: absolute;
            top: 5px;
            left: 5px;
            right: 5px;
            bottom: 5px;
            border: 1px dashed var(--accent-color);
            pointer-events: none;
        }
        
        /* Details Table */
        .details-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .details-table tr {
            border-bottom: 1px solid rgba(184, 134, 11, 0.3);
        }
        
        .details-table tr:last-child {
            border-bottom: none;
        }
        
        .details-table td {
            padding: 10px 5px;
            vertical-align: top;
        }
        
        .details-table td:first-child {
            width: 40%;
            font-weight: 600;
            color: var(--primary-color);
        }
        
        /* Horoscope */
        .horoscope {
            background-color: rgba(255, 248, 231, 0.9);
            padding: 15px;
            border: 1px solid var(--accent-color);
            margin-top: 20px;
        }
        
        .horoscope-title {
            font-family: var(--header-font);
            color: var(--primary-color);
            font-size: 18px;
            margin-bottom: 15px;
            text-align: center;
            font-weight: 600;
        }
        
        /* Expectations */
        .expectations {
            background-color: rgba(255, 248, 231, 0.9);
            padding: 20px;
            border: 1px solid var(--accent-color);
            position: relative;
        }
        
        .expectations:before {
            content: '';
            position: absolute;
            top: 5px;
            left: 5px;
            right: 5px;
            bottom: 5px;
            border: 1px dashed var(--accent-color);
            pointer-events: none;
        }
        
        /* Footer */
        .footer {
            margin-top: 30px;
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid var(--accent-color);
            font-size: 14px;
            color: var(--light-text);
            position: relative;
        }
        
        .footer:before {
            content: '';
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 20px;
            background-color: var(--cream-color);
            background-image: url('data:image/svg+xml;utf8,<svg width="100" height="20" viewBox="0 0 100 20" xmlns="http://www.w3.org/2000/svg"><path d="M0 10 L50 0 L100 10 L50 20 Z" fill="%23b8860b"/></svg>');
            background-repeat: no-repeat;
        }
        
        .branding {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 15px;
        }
        
        .brand-logo {
            height: 30px;
            margin-right: 10px;
        }
        
        .brand-name {
            font-weight: 600;
            color: var(--primary-color);
        }
        
        /* Print Styles */
        @media print {
            body {
                background-color: white;
            }
            
            .container {
                box-shadow: none;
                padding: 0;
                max-width: 100%;
            }
            
            @page {
                size: A4;
                margin: 1cm;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="ornamental-border"></div>
        <div class="ornamental-corner top-left"></div>
        <div class="ornamental-corner top-right"></div>
        <div class="ornamental-corner bottom-left"></div>
        <div class="ornamental-corner bottom-right"></div>
        
        <div class="content-wrapper">
            <!-- Invocation -->
            <div class="invocation">
                ॥ श्री गणेशाय नमः ॥
            </div>
            
            <!-- Header Section -->
            <div class="header">
                <div class="profile-photo-container">
                    <img src="{{profilePicture}}" alt="Profile Photo" class="profile-photo">
                    <div class="photo-frame"></div>
                </div>
                <div class="header-content">
                    <h1 class="name">{{name}}</h1>
                    <p class="tagline">{{tagline}}</p>
                    <div class="quick-info">
                        <div class="info-item">
                            <span class="info-label">Age:</span> {{age}} years
                        </div>
                        <div class="info-item">
                            <span class="info-label">Height:</span> {{height}}
                        </div>
                        <div class="info-item">
                            <span class="info-label">Education:</span> {{education}}
                        </div>
                        <div class="info-item">
                            <span class="info-label">Gotra:</span> {{gotra}}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Personal Details -->
            <div class="section">
                <h2 class="section-title">Personal Details</h2>
                <div class="section-content">
                    <div class="cultural-card">
                        <table class="details-table">
                            <tr>
                                <td>Full Name</td>
                                <td>{{name}}</td>
                            </tr>
                            <tr>
                                <td>Date of Birth</td>
                                <td>{{dateOfBirth}}</td>
                            </tr>
                            <tr>
                                <td>Birth Time</td>
                                <td>{{birthTime}}</td>
                            </tr>
                            <tr>
                                <td>Birth Place</td>
                                <td>{{birthPlace}}</td>
                            </tr>
                            <tr>
                                <td>Religion</td>
                                <td>{{religion}}</td>
                            </tr>
                            <tr>
                                <td>Caste</td>
                                <td>{{caste}}</td>
                            </tr>
                            <tr>
                                <td>Sub-caste</td>
                                <td>{{subCaste}}</td>
                            </tr>
                            <tr>
                                <td>Gotra</td>
                                <td>{{gotra}}</td>
                            </tr>
                            <tr>
                                <td>Kul</td>
                                <td>{{kul}}</td>
                            </tr>
                            <tr>
                                <td>Marital Status</td>
                                <td>{{maritalStatus}}</td>
                            </tr>
                            <tr>
                                <td>Height</td>
                                <td>{{height}}</td>
                            </tr>
                            <tr>
                                <td>Diet</td>
                                <td>{{diet}}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Two Column Layout for Family Background and Education/Career -->
            <div class="two-column">
                <!-- Family Background -->
                <div class="column">
                    <div class="section">
                        <h2 class="section-title">Family Background</h2>
                        <div class="section-content">
                            <div class="cultural-card">
                                <table class="details-table">
                                    <tr>
                                        <td>Father's Name</td>
                                        <td>{{fatherName}}</td>
                                    </tr>
                                    <tr>
                                        <td>Father's Occupation</td>
                                        <td>{{fatherOccupation}}</td>
                                    </tr>
                                    <tr>
                                        <td>Mother's Name</td>
                                        <td>{{motherName}}</td>
                                    </tr>
                                    <tr>
                                        <td>Mother's Occupation</td>
                                        <td>{{motherOccupation}}</td>
                                    </tr>
                                    <tr>
                                        <td>Family Type</td>
                                        <td>{{familyType}}</td>
                                    </tr>
                                    <tr>
                                        <td>Family Status</td>
                                        <td>{{familyStatus}}</td>
                                    </tr>
                                    <tr>
                                        <td>Siblings</td>
                                        <td>{{siblings}}</td>
                                    </tr>
                                    <tr>
                                        <td>Native Place</td>
                                        <td>{{nativePlace}}</td>
                                    </tr>
                                </table>
                            </div>
                            
                            <div class="horoscope">
                                <h3 class="horoscope-title">Horoscope Details</h3>
                                <table class="details-table">
                                    <tr>
                                        <td>Rashi</td>
                                        <td>{{rashi}}</td>
                                    </tr>
                                    <tr>
                                        <td>Nakshatra</td>
                                        <td>{{nakshatra}}</td>
                                    </tr>
                                    <tr>
                                        <td>Mangal</td>
                                        <td>{{mangal}}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Education & Career -->
                <div class="column">
                    <div class="section">
                        <h2 class="section-title">Education & Career</h2>
                        <div class="section-content">
                            <div class="cultural-card">
                                <table class="details-table">
                                    <tr>
                                        <td>Education</td>
                                        <td>{{education}}</td>
                                    </tr>
                                    <tr>
                                        <td>Details</td>
                                        <td>{{educationDetails}}</td>
                                    </tr>
                                    <tr>
                                        <td>Occupation</td>
                                        <td>{{occupation}}</td>
                                    </tr>
                                    <tr>
                                        <td>Company</td>
                                        <td>{{company}}</td>
                                    </tr>
                                    <tr>
                                        <td>Annual Income</td>
                                        <td>{{annualIncome}}</td>
                                    </tr>
                                </table>
                            </div>
                            
                            <div class="cultural-card" style="margin-top: 20px;">
                                <h3 class="horoscope-title">About Me</h3>
                                <p>{{aboutMe}}</p>
                                
                                <div style="margin-top: 15px;">
                                    <div style="font-weight: 600; color: var(--primary-color); margin-bottom: 5px;">Hobbies & Interests:</div>
                                    <p>{{hobbies}}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="section">
                <h2 class="section-title">Contact Information</h2>
                <div class="section-content">
                    <div class="cultural-card">
                        <table class="details-table">
                            <tr>
                                <td>Current Location</td>
                                <td>{{city}}, {{state}}, {{country}}</td>
                            </tr>
                            <tr>
                                <td>Email</td>
                                <td>{{email}}</td>
                            </tr>
                            <tr>
                                <td>Phone</td>
                                <td>{{phone}}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Partner Expectations -->
            <div class="section">
                <h2 class="section-title">Partner Expectations</h2>
                <div class="section-content">
                    <div class="expectations">
                        <p>{{partnerPreferences}}</p>
                    </div>
                </div>
            </div>
            
            <!-- Footer with Branding -->
            <div class="footer">
                <div class="branding">
                    <img src="{{brandLogo}}" alt="Brand Logo" class="brand-logo">
                    <span class="brand-name">{{brandName}}</span>
                </div>
                <p>{{brandTagline}}</p>
                <p>Created on {{createdAt}}</p>
            </div>
        </div>
    </div>
</body>
</html>
