import { useEffect, useState, createContext, useContext } from 'react';
import { useSession } from 'next-auth/react';
import axios from 'axios';

// Create a context for FCM
export const FCMContext = createContext(null);

/**
 * FCM Provider Component
 * This component handles Firebase Cloud Messaging setup and token management
 */
export function FCMProvider({ children }) {
  const { data: session } = useSession();
  const [fcmToken, setFcmToken] = useState(null);
  const [notificationPermission, setNotificationPermission] = useState('default');
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize Firebase Messaging
  useEffect(() => {
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator && session?.user) {
      initializeFirebaseMessaging();
    }
  }, [session]);

  // Initialize Firebase Messaging
  const initializeFirebaseMessaging = async () => {
    try {
      // Import Firebase modules dynamically
      const { initializeApp } = await import('firebase/app');
      const { getMessaging, getToken, onMessage } = await import('firebase/messaging');

      // Firebase configuration
      const firebaseConfig = {
        apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
        authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
        storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
        messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
        appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
      };

      // Initialize Firebase
      const app = initializeApp(firebaseConfig);
      const messaging = getMessaging(app);

      // Check if notification permission is granted
      if (Notification.permission === 'granted') {
        setNotificationPermission('granted');
        
        // Get FCM token
        const currentToken = await getToken(messaging, {
          vapidKey: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
        });

        if (currentToken) {
          setFcmToken(currentToken);
          registerTokenWithServer(currentToken);
        }
      } else {
        setNotificationPermission(Notification.permission);
      }

      // Set up message handler
      onMessage(messaging, (payload) => {
        console.log('Message received:', payload);
        
        // Display notification if not using service worker
        if (payload.notification) {
          const { title, body, image } = payload.notification;
          
          // Show notification
          if (Notification.permission === 'granted') {
            navigator.serviceWorker.ready.then((registration) => {
              registration.showNotification(title, {
                body,
                icon: '/logo.png',
                image,
                data: payload.data,
                badge: '/badge-icon.png',
                vibrate: [200, 100, 200],
                actions: [
                  {
                    action: 'view',
                    title: 'View',
                  },
                ],
              });
            });
          }
        }
      });

      setIsInitialized(true);
    } catch (error) {
      console.error('Error initializing Firebase Messaging:', error);
    }
  };

  // Register FCM token with server
  const registerTokenWithServer = async (token) => {
    try {
      await axios.post('/api/notifications/fcm-token', { token });
      console.log('FCM token registered with server');
    } catch (error) {
      console.error('Error registering FCM token with server:', error);
    }
  };

  // Request notification permission
  const requestPermission = async () => {
    try {
      if (!('Notification' in window)) {
        console.log('This browser does not support notifications');
        return;
      }

      const permission = await Notification.requestPermission();
      setNotificationPermission(permission);

      if (permission === 'granted') {
        // Re-initialize FCM
        initializeFirebaseMessaging();
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
    }
  };

  // Context value
  const contextValue = {
    fcmToken,
    notificationPermission,
    isInitialized,
    requestPermission,
  };

  return (
    <FCMContext.Provider value={contextValue}>
      {children}
    </FCMContext.Provider>
  );
}

// Custom hook to use FCM context
export function useFCM() {
  const context = useContext(FCMContext);
  if (!context) {
    throw new Error('useFCM must be used within an FCMProvider');
  }
  return context;
}

export default FCMProvider;
