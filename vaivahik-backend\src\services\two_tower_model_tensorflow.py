"""
Two-Tower Model for Matrimony Matching (TensorFlow Implementation)

NOTE: THIS FILE IS KEPT FOR REFERENCE ONLY. 
THE ACTIVE IMPLEMENTATION IS IN two_tower_model_pytorch.py.
DO NOT USE THIS FILE FOR NEW DEVELOPMENT.

This module implements a two-tower neural network model for matrimony matching using TensorFlow.
The model consists of two separate neural networks (towers):
1. User Tower: Processes user profile and preference data
2. Match Tower: Processes potential match profile data

The outputs of both towers are compared to calculate a match score.
"""

import os
import json
import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Model, load_model
from tensorflow.keras.layers import Input, Dense, Dropout, Concatenate
from tensorflow.keras.optimizers import <PERSON>
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint

class TwoTowerModel:
    """Two-Tower Model for matrimony matching"""
    
    def __init__(self, config=None):
        """
        Initialize the Two-Tower Model
        
        Args:
            config (dict): Configuration parameters for the model
        """
        # Default configuration
        self.default_config = {
            'user_tower_layers': [128, 64],
            'match_tower_layers': [128, 64],
            'embedding_size': 128,
            'dropout_rate': 0.2,
            'learning_rate': 0.001,
            'similarity_metric': 'cosine',
            'batch_size': 64,
            'epochs': 10
        }
        
        # Use provided config or default
        self.config = config if config else self.default_config
        
        # Initialize model
        self.model = None
        self.user_tower = None
        self.match_tower = None
        
        # Feature columns
        self.user_features = [
            'age', 'gender', 'height', 'religion', 'caste', 'subCaste', 'gotra',
            'education', 'occupation', 'income', 'city', 'state', 'maritalStatus'
        ]
        
        self.preference_features = [
            'ageMin', 'ageMax', 'heightMin', 'heightMax', 'religion', 'caste',
            'subCaste', 'gotra', 'education', 'occupation', 'incomeMin',
            'location', 'maritalStatus'
        ]
        
        self.match_features = [
            'age', 'gender', 'height', 'religion', 'caste', 'subCaste', 'gotra',
            'education', 'occupation', 'income', 'city', 'state', 'maritalStatus'
        ]
        
        # Feature dimensions (to be set during preprocessing)
        self.feature_dims = {}
    
    def build_model(self):
        """Build the two-tower model architecture"""
        # User tower input: user profile + preferences
        user_input = Input(shape=(len(self.user_features) + len(self.preference_features),), name='user_input')
        
        # Match tower input: potential match profile
        match_input = Input(shape=(len(self.match_features),), name='match_input')
        
        # Build user tower
        user_x = user_input
        for i, units in enumerate(self.config['user_tower_layers']):
            user_x = Dense(units, activation='relu', name=f'user_dense_{i}')(user_x)
            user_x = Dropout(self.config['dropout_rate'], name=f'user_dropout_{i}')(user_x)
        
        user_embedding = Dense(self.config['embedding_size'], activation='tanh', name='user_embedding')(user_x)
        
        # Build match tower
        match_x = match_input
        for i, units in enumerate(self.config['match_tower_layers']):
            match_x = Dense(units, activation='relu', name=f'match_dense_{i}')(match_x)
            match_x = Dropout(self.config['dropout_rate'], name=f'match_dropout_{i}')(match_x)
        
        match_embedding = Dense(self.config['embedding_size'], activation='tanh', name='match_embedding')(match_x)
        
        # Calculate similarity
        if self.config['similarity_metric'] == 'cosine':
            # Cosine similarity
            dot_product = tf.reduce_sum(user_embedding * match_embedding, axis=1)
            norm_user = tf.sqrt(tf.reduce_sum(tf.square(user_embedding), axis=1))
            norm_match = tf.sqrt(tf.reduce_sum(tf.square(match_embedding), axis=1))
            similarity = dot_product / (norm_user * norm_match)
            # Rescale from [-1, 1] to [0, 1]
            similarity = (similarity + 1) / 2
        elif self.config['similarity_metric'] == 'dot':
            # Dot product
            similarity = tf.reduce_sum(user_embedding * match_embedding, axis=1)
            # Apply sigmoid to get [0, 1]
            similarity = tf.sigmoid(similarity)
        else:
            # Euclidean distance (converted to similarity)
            distance = tf.sqrt(tf.reduce_sum(tf.square(user_embedding - match_embedding), axis=1))
            # Convert distance to similarity (1 when distance is 0, 0 when distance is large)
            similarity = tf.exp(-distance)
        
        # Reshape to match expected output shape
        output = tf.reshape(similarity, [-1, 1], name='match_score')
        
        # Create model
        self.model = Model(inputs=[user_input, match_input], outputs=output)
        
        # Compile model
        self.model.compile(
            optimizer=Adam(learning_rate=self.config['learning_rate']),
            loss='binary_crossentropy',
            metrics=['accuracy']
        )
        
        # Create separate models for inference
        self.user_tower = Model(inputs=user_input, outputs=user_embedding)
        self.match_tower = Model(inputs=match_input, outputs=match_embedding)
        
        return self.model
    
    def preprocess_data(self, users, preferences, matches, labels=None):
        """
        Preprocess data for training or inference
        
        Args:
            users (list): List of user profiles
            preferences (list): List of user preferences
            matches (list): List of potential matches
            labels (list): Match labels (1 for match, 0 for non-match)
            
        Returns:
            tuple: Processed inputs and outputs for the model
        """
        # TODO: Implement data preprocessing
        # This would include:
        # - One-hot encoding categorical features
        # - Normalizing numerical features
        # - Handling missing values
        # - Combining user profiles and preferences
        
        # For now, return dummy data
        user_data = np.random.random((len(users), len(self.user_features) + len(self.preference_features)))
        match_data = np.random.random((len(matches), len(self.match_features)))
        
        if labels is not None:
            return [user_data, match_data], np.array(labels)
        else:
            return [user_data, match_data]
    
    def train(self, users, preferences, matches, labels, validation_split=0.2):
        """
        Train the model
        
        Args:
            users (list): List of user profiles
            preferences (list): List of user preferences
            matches (list): List of potential matches
            labels (list): Match labels (1 for match, 0 for non-match)
            validation_split (float): Fraction of data to use for validation
            
        Returns:
            dict: Training history
        """
        # Build model if not already built
        if self.model is None:
            self.build_model()
        
        # Preprocess data
        inputs, outputs = self.preprocess_data(users, preferences, matches, labels)
        
        # Callbacks
        callbacks = [
            EarlyStopping(monitor='val_loss', patience=3, restore_best_weights=True),
            ModelCheckpoint('models/two_tower_model.h5', save_best_only=True)
        ]
        
        # Train model
        history = self.model.fit(
            inputs, outputs,
            batch_size=self.config['batch_size'],
            epochs=self.config['epochs'],
            validation_split=validation_split,
            callbacks=callbacks
        )
        
        return history.history
    
    def predict(self, users, preferences, matches):
        """
        Predict match scores
        
        Args:
            users (list): List of user profiles
            preferences (list): List of user preferences
            matches (list): List of potential matches
            
        Returns:
            list: Match scores
        """
        # Build model if not already built
        if self.model is None:
            self.build_model()
        
        # Preprocess data
        inputs = self.preprocess_data(users, preferences, matches)
        
        # Predict
        scores = self.model.predict(inputs)
        
        return scores.flatten().tolist()
    
    def get_user_embedding(self, user, preferences):
        """
        Get embedding for a user
        
        Args:
            user (dict): User profile
            preferences (dict): User preferences
            
        Returns:
            np.array: User embedding
        """
        # Preprocess data
        user_data = self.preprocess_data([user], [preferences], [{}])[0][0]
        
        # Get embedding
        embedding = self.user_tower.predict(user_data)
        
        return embedding[0]
    
    def get_match_embedding(self, match):
        """
        Get embedding for a potential match
        
        Args:
            match (dict): Potential match profile
            
        Returns:
            np.array: Match embedding
        """
        # Preprocess data
        match_data = self.preprocess_data([{}], [{}], [match])[0][1]
        
        # Get embedding
        embedding = self.match_tower.predict(match_data)
        
        return embedding[0]
    
    def save(self, path):
        """
        Save the model
        
        Args:
            path (str): Path to save the model
        """
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # Save model
        self.model.save(f"{path}/full_model.h5")
        self.user_tower.save(f"{path}/user_tower.h5")
        self.match_tower.save(f"{path}/match_tower.h5")
        
        # Save config
        with open(f"{path}/config.json", 'w') as f:
            json.dump(self.config, f)
    
    def load(self, path):
        """
        Load the model
        
        Args:
            path (str): Path to load the model from
        """
        # Load config
        with open(f"{path}/config.json", 'r') as f:
            self.config = json.load(f)
        
        # Load models
        self.model = load_model(f"{path}/full_model.h5")
        self.user_tower = load_model(f"{path}/user_tower.h5")
        self.match_tower = load_model(f"{path}/match_tower.h5")
