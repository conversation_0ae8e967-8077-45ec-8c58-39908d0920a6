import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  // Check if user is authenticated
  const session = await getServerSession(req, res, authOptions);
  
  if (!session || !session.user) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  const userId = session.user.id;

  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { email, phone, message } = req.body;

    if (!email && !phone) {
      return res.status(400).json({ 
        success: false, 
        message: 'Either email or phone is required' 
      });
    }

    // Get active referral program
    const activeProgram = await prisma.referralProgram.findFirst({
      where: {
        status: 'active',
        startDate: {
          lte: new Date()
        },
        OR: [
          { endDate: null },
          { endDate: { gte: new Date() } }
        ]
      },
      orderBy: {
        startDate: 'desc'
      }
    });

    if (!activeProgram) {
      return res.status(404).json({ 
        success: false, 
        message: 'No active referral program found' 
      });
    }

    // Check if user has reached max referrals limit
    if (activeProgram.maxReferralsPerUser) {
      const userReferralCount = await prisma.referral.count({
        where: {
          referrerId: userId,
          referralProgramId: activeProgram.id,
          refereeId: { not: null } // Only count completed referrals
        }
      });
      
      if (userReferralCount >= activeProgram.maxReferralsPerUser) {
        return res.status(400).json({ 
          success: false, 
          message: 'You have reached the maximum number of referrals for this program' 
        });
      }
    }

    // Get user's referral code
    let referral = await prisma.referral.findFirst({
      where: {
        referrerId: userId,
        referralProgramId: activeProgram.id
      }
    });

    if (!referral) {
      return res.status(404).json({ 
        success: false, 
        message: 'Referral code not found. Please generate a referral code first.' 
      });
    }

    // Check if this email/phone has already been referred
    if (email) {
      const existingReferral = await prisma.referral.findFirst({
        where: {
          referralEmail: email,
          referralProgramId: activeProgram.id
        }
      });

      if (existingReferral) {
        return res.status(400).json({ 
          success: false, 
          message: 'This email has already been referred' 
        });
      }
    }

    if (phone) {
      const existingReferral = await prisma.referral.findFirst({
        where: {
          referralPhone: phone,
          referralProgramId: activeProgram.id
        }
      });

      if (existingReferral) {
        return res.status(400).json({ 
          success: false, 
          message: 'This phone number has already been referred' 
        });
      }
    }

    // Update referral with contact info
    await prisma.referral.update({
      where: { id: referral.id },
      data: {
        referralEmail: email || null,
        referralPhone: phone || null
      }
    });

    // TODO: Send email or SMS invitation
    // This would typically involve calling an email service or SMS gateway
    // For now, we'll just simulate success

    return res.status(200).json({
      success: true,
      message: 'Referral invitation sent successfully',
      referralCode: referral.referralCode,
      referralLink: referral.referralLink
    });
  } catch (error) {
    console.error('Error sending referral invitation:', error);
    return res.status(500).json({ success: false, message: 'Failed to send referral invitation' });
  }
}
