// API endpoint for blog posts
import { handleApiError } from '@/utils/errorHandler';
import { withAuth } from '@/utils/authHandler';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';

async function handler(req, res) {
  // Check if user is authenticated and is an admin
  const session = await getServerSession(req, res, authOptions);

  if (!session || !session.user || !session.user.isAdmin) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }
  try {
    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await getBlogPosts(req, res);
      case 'POST':
        return await createBlogPost(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    return handleApiError(error, res, 'Blog Posts API');
  }
}

// GET /api/admin/blog-posts
async function getBlogPosts(req, res) {
  try {
    // Get query parameters
    const { page = 1, limit = 10, search = '', category = '', status = '' } = req.query;

    // Mock data for blog posts
    const allPosts = [
      {
        id: 1,
        title: 'Finding Your Perfect Match: Tips for Creating an Attractive Profile',
        slug: 'finding-your-perfect-match-tips',
        excerpt: 'Learn how to create a profile that stands out and attracts the right matches.',
        content: 'Creating an attractive profile is the first step towards finding your perfect match. Here are some tips to help you create a profile that stands out...',
        featuredImage: '/images/blog/profile-tips.jpg',
        category: 'Profile Tips',
        tags: ['profile', 'tips', 'matching'],
        author: 'Priya Sharma',
        authorId: 1,
        status: 'published',
        publishedAt: '2023-05-15T10:30:00Z',
        createdAt: '2023-05-10T08:15:00Z',
        updatedAt: '2023-05-15T10:30:00Z',
        viewCount: 1250,
        commentCount: 18
      },
      {
        id: 2,
        title: 'The Importance of Family Values in Maratha Marriages',
        slug: 'importance-of-family-values-maratha-marriages',
        excerpt: 'Discover why family values play a crucial role in successful Maratha marriages.',
        content: 'In Maratha culture, family values are the foundation of a successful marriage. This article explores the importance of these values...',
        featuredImage: '/images/blog/family-values.jpg',
        category: 'Culture',
        tags: ['family', 'values', 'culture', 'maratha'],
        author: 'Rajesh Patil',
        authorId: 2,
        status: 'published',
        publishedAt: '2023-06-02T14:45:00Z',
        createdAt: '2023-05-28T11:20:00Z',
        updatedAt: '2023-06-02T14:45:00Z',
        viewCount: 980,
        commentCount: 12
      },
      {
        id: 3,
        title: 'Modern Dating vs Traditional Matchmaking: Finding the Balance',
        slug: 'modern-dating-vs-traditional-matchmaking',
        excerpt: 'Explore the pros and cons of modern dating and traditional matchmaking approaches.',
        content: 'In today\'s world, many young Marathas are caught between modern dating approaches and traditional matchmaking. This article explores how to find the right balance...',
        featuredImage: '/images/blog/modern-traditional.jpg',
        category: 'Relationships',
        tags: ['dating', 'matchmaking', 'tradition', 'modern'],
        author: 'Ananya Desai',
        authorId: 3,
        status: 'published',
        publishedAt: '2023-06-20T09:15:00Z',
        createdAt: '2023-06-15T16:30:00Z',
        updatedAt: '2023-06-20T09:15:00Z',
        viewCount: 1560,
        commentCount: 25
      },
      {
        id: 4,
        title: 'Preparing for Your First Meeting: Do\'s and Don\'ts',
        slug: 'first-meeting-dos-and-donts',
        excerpt: 'Essential tips for making a great impression during your first meeting with a potential match.',
        content: 'The first meeting with a potential match can be nerve-wracking. Here are some do\'s and don\'ts to help you make a great impression...',
        featuredImage: '/images/blog/first-meeting.jpg',
        category: 'Dating Tips',
        tags: ['meeting', 'first impression', 'dating'],
        author: 'Vikram Singh',
        authorId: 4,
        status: 'draft',
        publishedAt: null,
        createdAt: '2023-07-05T13:45:00Z',
        updatedAt: '2023-07-05T13:45:00Z',
        viewCount: 0,
        commentCount: 0
      },
      {
        id: 5,
        title: 'Understanding Compatibility: Beyond Horoscopes',
        slug: 'understanding-compatibility-beyond-horoscopes',
        excerpt: 'Learn about the various factors that contribute to compatibility in a successful marriage.',
        content: 'While horoscopes are important in traditional matchmaking, there are many other factors that contribute to compatibility. This article explores these factors...',
        featuredImage: '/images/blog/compatibility.jpg',
        category: 'Compatibility',
        tags: ['compatibility', 'horoscope', 'matching'],
        author: 'Neha Gupta',
        authorId: 5,
        status: 'published',
        publishedAt: '2023-07-18T11:30:00Z',
        createdAt: '2023-07-10T09:20:00Z',
        updatedAt: '2023-07-18T11:30:00Z',
        viewCount: 850,
        commentCount: 9
      }
    ];

    // Filter posts based on search, category, and status
    let filteredPosts = [...allPosts];

    if (search) {
      const searchLower = search.toLowerCase();
      filteredPosts = filteredPosts.filter(post =>
        post.title.toLowerCase().includes(searchLower) ||
        post.excerpt.toLowerCase().includes(searchLower) ||
        post.content.toLowerCase().includes(searchLower) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    if (category) {
      filteredPosts = filteredPosts.filter(post => post.category === category);
    }

    if (status) {
      filteredPosts = filteredPosts.filter(post => post.status === status);
    }

    // Calculate pagination
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedPosts = filteredPosts.slice(startIndex, endIndex);

    // Get unique categories for filtering
    const categories = [...new Set(allPosts.map(post => post.category))];

    return res.status(200).json({
      success: true,
      posts: paginatedPosts,
      pagination: {
        total: filteredPosts.length,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(filteredPosts.length / parseInt(limit))
      },
      categories
    });
  } catch (error) {
    return handleApiError(error, res, 'Get blog posts');
  }
}

// POST /api/admin/blog-posts
async function createBlogPost(req, res) {
  try {
    const { title, excerpt, content, category, tags, status } = req.body;

    // Validate required fields
    if (!title || !content || !category) {
      return res.status(400).json({
        success: false,
        message: 'Title, content, and category are required'
      });
    }

    // In a real implementation, this would create a new post in the database
    // For now, we'll just return a success response with mock data

    return res.status(201).json({
      success: true,
      message: 'Blog post created successfully',
      post: {
        id: Math.floor(Math.random() * 1000) + 6,
        title,
        slug: title.toLowerCase().replace(/[^\w\s]/gi, '').replace(/\s+/g, '-'),
        excerpt: excerpt || title,
        content,
        featuredImage: '/images/blog/default.jpg',
        category,
        tags: tags || [],
        author: 'Admin User',
        authorId: 1,
        status: status || 'draft',
        publishedAt: status === 'published' ? new Date().toISOString() : null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        viewCount: 0,
        commentCount: 0
      }
    });
  } catch (error) {
    return handleApiError(error, res, 'Create blog post');
  }
}

export default handler;
