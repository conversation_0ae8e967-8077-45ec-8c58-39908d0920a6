/* Landing Page Styles */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap');

/* 6. Fluid Typography Implementation */
.page {
  font-family: 'Montserrat', sans-serif;
  color: var(--dark-gray);
  line-height: 1.6;
  overflow-x: hidden;
  font-size: clamp(
    calc(var(--fluid-min-size) * 1px),
    calc(var(--fluid-min-size) * 1px + (var(--fluid-max-size) - var(--fluid-min-size)) *
    ((100vw - (var(--fluid-min-width) * 1px)) / (var(--fluid-max-width) - var(--fluid-min-width)))),
    calc(var(--fluid-max-size) * 1px)
  );
}

/* Header Styles with Glassmorphism */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  padding: 20px 0;
  transition: var(--transition);
  background-color: transparent;
}

.headerScrolled {
  /* 2. Glassmorphism Effect */
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  padding: 15px 0;
}

.headerContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--primary-color);
  font-weight: 700;
  font-size: 1.8rem;
}

/* Enhanced Logo Symbol with Animations */
.logoSymbol {
  margin-right: 10px;
  color: var(--primary-color);
  display: inline-block;
  transition: transform 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  filter: drop-shadow(0 0 5px rgba(255, 95, 109, 0.5));
  transform-origin: center;
  position: relative;
}

/* Gradient overlay for the heart */
.logoSymbol::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 50%;
  z-index: -1;
}

/* Clockwise spin on hover */
.logo:hover .logoSymbol {
  transform: rotate(360deg) scale(1.2);
  color: var(--accent-color);
}

.logo:hover .logoSymbol::after {
  opacity: 0.3;
}

/* Anti-clockwise spin when hover ends */
.logo .logoSymbol.spinBack,
.footerLogo .logoSymbol.spinBack {
  transform: rotate(-360deg);
}

.nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav li {
  margin-left: 30px;
}

/* 9. Custom Animated Underlines for Links */
.nav a {
  position: relative;
  text-decoration: none;
  color: var(--dark-gray);
  font-weight: 500;
  transition: var(--transition);
  padding: 5px 0;
}

.nav a::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  bottom: 0;
  left: 0;
  background: var(--primary-gradient);
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.3s ease-out;
}

.nav a:hover {
  color: var(--primary-color);
}

.nav a:hover::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

.navCta {
  background: var(--primary-gradient);
  color: var(--white) !important;
  padding: 10px 20px;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.navCta:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(255, 95, 109, 0.4);
}

.hamburger {
  display: none;
  cursor: pointer;
  z-index: 1001;
  padding: 5px;
  background: transparent;
  border: none;
}

.hamburger div {
  width: 25px;
  height: 3px;
  background: var(--primary-color);
  margin: 5px;
  transition: all 0.4s ease;
  border-radius: 3px;
}

/* Hero Section */
/* Enhanced Hero Section with Gradient Background */
.hero {
  background: linear-gradient(135deg, rgba(94, 53, 177, 0.9) 0%, rgba(255, 95, 109, 0.9) 100%),
              url('/images/hero-bg.jpg') center/cover no-repeat;
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  color: var(--white);
  padding: 0;
  overflow: hidden;
}

.heroContent {
  max-width: 600px;
  padding: 0 20px;
}

/* 1. Gradient Text for Hero Heading */
.heroContent h1 {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 20px;
  line-height: 1.2;
  font-family: 'Playfair Display', serif;
  background: var(--text-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
}

.heroContent p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.heroButtons {
  display: flex;
  gap: 15px;
}

/* Button Styles */
.btn {
  display: inline-block;
  padding: 12px 25px;
  border-radius: var(--border-radius);
  font-weight: 600;
  text-decoration: none;
  transition: var(--transition);
  cursor: pointer;
  text-align: center;
}

.btnPrimary {
  background: var(--primary-gradient);
  color: var(--white);
  border: none;
}

.btnPrimary:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(255, 95, 109, 0.4);
}

.btnOutline {
  background: transparent;
  color: var(--white);
  border: 2px solid var(--white);
}

.btnOutline:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-3px);
}

.btnSecondary {
  background: var(--secondary-gradient);
  color: var(--white);
  border: none;
}

.btnSecondary:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(94, 53, 177, 0.4);
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Section Styles */
.section {
  padding: 100px 0;
}

.sectionTitle {
  text-align: center;
  margin-bottom: 60px;
}

.sectionTitle h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--dark-gray);
  font-family: 'Playfair Display', serif;
}

.subtitle {
  font-size: 1.1rem;
  color: var(--dark-gray);
  max-width: 700px;
  margin: 0 auto;
  opacity: 0.8;
}

/* Features Section */
.features {
  background-color: var(--white);
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

/* 4. 3D Effects & 7. Neumorphism for Feature Cards */
.featureCard {
  background: var(--white);
  border-radius: var(--border-radius);
  padding: 30px;
  box-shadow: 10px 10px 20px #d1d1d4, -10px -10px 20px #ffffff;
  transition: var(--transition);
  text-align: center;
  transform-style: preserve-3d;
  perspective: 1000px;
  position: relative;
  overflow: hidden;
}

.featureCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 50%, rgba(255,255,255,0) 100%);
  transform: translateZ(20px);
  pointer-events: none;
  z-index: 1;
}

.featureCard:hover {
  transform: translateY(-10px) rotateX(5deg) rotateY(5deg);
  box-shadow: 15px 15px 30px #d1d1d4, -15px -15px 30px #ffffff;
}

/* 3. Subtle Animations for Feature Visuals */
.featureVisual {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: var(--primary-gradient);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 1.5rem;
  font-weight: 700;
  position: relative;
  z-index: 2;
  animation: float 6s ease-in-out infinite;
  box-shadow: 0 10px 20px rgba(255, 95, 109, 0.3);
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

.featureVisual::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: var(--primary-gradient);
  opacity: 0.3;
  z-index: -1;
  animation: pulse-ring 3s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
}

@keyframes pulse-ring {
  0% { transform: scale(0.95); opacity: 0.7; }
  50% { transform: scale(1.2); opacity: 0.3; }
  100% { transform: scale(0.95); opacity: 0.7; }
}

.featureCard h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: var(--dark-gray);
}

.featureCard p {
  color: var(--dark-gray);
  opacity: 0.8;
}

/* How It Works Section with Parallax */
.howItWorks {
  background-color: var(--light-gray);
  position: relative;
  overflow: hidden;
}

/* 8. Parallax Scrolling Effect */
.howItWorks::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(255,255,255,0.8) 0%, rgba(245,245,245,0.5) 70%);
  transform: translateZ(-1px) scale(2);
  z-index: 0;
}

.processTimeline {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  position: relative;
  z-index: 1;
}

/* 2. Glassmorphism for Process Steps */
.processStep {
  text-align: center;
  padding: 30px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  transition: var(--transition);
}

.processStep:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
}

.stepNumber {
  width: 50px;
  height: 50px;
  background: var(--primary-gradient);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 auto 20px;
}

.processStep h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: var(--dark-gray);
}

.processStep p {
  color: var(--dark-gray);
  opacity: 0.8;
}

/* Pricing Section */
.pricing {
  background-color: var(--white);
}

.pricingTabs {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}

.pricingTab {
  padding: 10px 20px;
  cursor: pointer;
  border-radius: var(--border-radius);
  transition: var(--transition);
  margin: 0 10px;
  background: var(--light-gray);
}

.pricingTabActive {
  background: var(--primary-gradient);
  color: var(--white);
}

.pricingGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

/* 4. 3D Effects for Pricing Cards */
.pricingCard {
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  transition: var(--transition);
  transform-style: preserve-3d;
  perspective: 1000px;
}

.pricingCard:hover {
  transform: translateY(-10px) rotateY(5deg);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.pricingCard::before {
  content: '';
  position: absolute;
  top: -10px;
  right: -10px;
  width: 100px;
  height: 100px;
  background: var(--primary-gradient);
  transform: rotate(45deg) translateZ(10px);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.pricingCard:hover::before {
  opacity: 0.1;
}

.pricingHeader {
  padding: 30px;
  text-align: center;
  background: var(--light-gray);
}

.pricingHeader h3 {
  font-size: 1.8rem;
  margin-bottom: 15px;
  color: var(--dark-gray);
}

.price {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.free {
  color: var(--success-color);
}

.pricingFeatures {
  padding: 30px;
  list-style: none;
}

.pricingFeatures li {
  padding: 10px 0;
  border-bottom: 1px solid var(--medium-gray);
  position: relative;
  padding-left: 30px;
}

.pricingFeatures li:before {
  content: "✓";
  color: var(--success-color);
  position: absolute;
  left: 0;
}

.pricingFeatures li.disabled {
  opacity: 0.5;
}

.pricingFeatures li.disabled:before {
  content: "✕";
  color: var(--error-color);
}

.pricingFooter {
  padding: 20px 30px 30px;
  text-align: center;
}

/* CTA Section */
.cta {
  background: var(--primary-gradient);
  color: var(--white);
  text-align: center;
  padding: 80px 0;
}

.cta h2 {
  font-size: 2.5rem;
  margin-bottom: 20px;
  font-family: 'Playfair Display', serif;
}

.cta p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  opacity: 0.9;
}

/* Footer */
.footer {
  background: var(--dark-gray);
  color: var(--white);
  padding: 80px 0 40px;
}

.footerContent {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.footerLogo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--white);
  font-weight: 700;
  font-size: 1.8rem;
  margin-bottom: 30px;
}

/* Apply the same hover effects to footer logo */
.footerLogo:hover .logoSymbol {
  transform: rotate(360deg) scale(1.2);
  color: var(--accent-color);
}

.footerLogo:hover .logoSymbol::after {
  opacity: 0.3;
}

.logoSymbol {
  margin-right: 10px;
  color: var(--primary-color);
}

.footerLinks {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 30px;
}

.footerLinks a {
  margin: 0 15px 10px;
  color: var(--white);
  text-decoration: none;
  transition: var(--transition);
}

.footerLinks a:hover {
  color: var(--primary-color);
}

.footerDivider {
  width: 100%;
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin: 20px 0;
}

.copyright {
  text-align: center;
  font-size: 0.9rem;
  opacity: 0.7;
}

/* Enhanced heart in footer */
.heart {
  display: inline-block;
  margin: 0 5px;
  animation: pulse 1.5s infinite;
  filter: drop-shadow(0 0 3px rgba(255, 95, 109, 0.5));
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* 10. Enhanced Floating Hearts Animation */
.floatingHearts {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: 1;
}

.heart {
  position: absolute;
  width: 30px;
  height: 30px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF5F6D'%3E%3Cpath d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/%3E%3C/svg%3E") no-repeat center center;
  background-size: contain;
  opacity: 0.6;
  animation: floating-heart 15s linear infinite;
  will-change: transform, opacity;
  filter: drop-shadow(0 0 5px rgba(255, 95, 109, 0.5));
}

@keyframes floating-heart {
  0% {
    transform: translateY(100vh) scale(0.5) rotate(0deg);
    opacity: 0;
    filter: hue-rotate(0deg);
  }
  10% {
    opacity: 0.8;
  }
  50% {
    filter: hue-rotate(180deg);
  }
  90% {
    opacity: 0.8;
  }
  100% {
    transform: translateY(-100px) scale(1.2) rotate(360deg);
    opacity: 0;
    filter: hue-rotate(360deg);
  }
}

/* Create hearts of different sizes */
.heart:nth-child(3n) {
  width: 20px;
  height: 20px;
  animation-duration: 12s;
}

.heart:nth-child(3n+1) {
  width: 40px;
  height: 40px;
  animation-duration: 18s;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .heroContent h1 {
    font-size: 2.8rem;
  }

  .featuresGrid,
  .processTimeline,
  .pricingGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .nav {
    position: fixed;
    top: 0;
    right: -100%;
    width: 70%;
    height: 100vh;
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    z-index: 1000;
  }

  .navActive {
    right: 0;
  }

  .nav ul {
    flex-direction: column;
    align-items: center;
  }

  .nav li {
    margin: 15px 0;
  }

  .hamburger {
    display: block;
  }

  .hamburgerActive .line1 {
    transform: rotate(-45deg) translate(-5px, 6px);
  }

  .hamburgerActive .line2 {
    opacity: 0;
    transform: translateX(-20px);
  }

  .hamburgerActive .line3 {
    transform: rotate(45deg) translate(-5px, -6px);
  }

  .heroContent h1 {
    font-size: 2.2rem;
  }

  .featuresGrid,
  .processTimeline,
  .pricingGrid {
    grid-template-columns: 1fr;
  }

  .section {
    padding: 60px 0;
  }

  .sectionTitle h2 {
    font-size: 2rem;
  }
}

/* Admin Link Fixed Position */
.adminLinkFixed {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  transition: var(--transition);
}

.adminLinkFixed .btn {
  opacity: 0.7;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.adminLinkFixed .btn:hover {
  opacity: 1;
  transform: translateY(-5px);
}

@media (max-width: 576px) {
  .heroButtons {
    flex-direction: column;
    width: 100%;
  }

  .btn {
    width: 100%;
    margin-bottom: 10px;
  }

  .heroContent h1 {
    font-size: 1.8rem;
  }

  .pricingTabs {
    flex-direction: column;
    align-items: center;
  }

  .pricingTab {
    margin-bottom: 10px;
    width: 100%;
    text-align: center;
  }

  .adminLinkFixed {
    bottom: 10px;
    right: 10px;
  }

  .adminLinkFixed .btn {
    padding: 8px 15px;
    font-size: 0.9rem;
  }
}
